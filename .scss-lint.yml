# Default application configuration that all configurations inherit from.

scss_files: "**/*.scss"
plugin_directories: ['.scss-linters']

# List of gem names to load custom linters from (make sure they are already
# installed)
plugin_gems: []

# Default severity of all linters.
severity: warning

linters:
  BangFormat:
    enabled: true
    space_before_bang: true
    space_after_bang: false

  BemDepth:
    enabled: false
    max_elements: 1

  BorderZero:
    enabled: true
    convention: zero # or `none`

  ChainedClasses:
    enabled: false

  ColorKeyword:
    enabled: true

  ColorVariable:
    enabled: false

  Comment:
    enabled: true
    style: silent

  DebugStatement:
    enabled: true

  DeclarationOrder:
    enabled: true

  DisableLinterReason:
    enabled: false

  DuplicateProperty:
    enabled: true

  ElsePlacement:
    enabled: true
    style: same_line # or 'new_line'

  EmptyLineBetweenBlocks:
    enabled: false
    ignore_single_line_blocks: true

  EmptyRule:
    enabled: true

  ExtendDirective:
    enabled: false

  FinalNewline:
    enabled: true
    present: true

  HexLength:
    enabled: true
    style: short # or 'long'

  HexNotation:
    enabled: true
    style: lowercase # or 'uppercase'

  HexValidation:
    enabled: true

  IdSelector:
    enabled: true

  ImportantRule:
    enabled: false

  ImportPath:
    enabled: true
    leading_underscore: false
    filename_extension: false

  Indentation:
    enabled: true
    allow_non_nested_indentation: false
    character: space # or 'tab'
    width: 2

  LeadingZero:
    enabled: true
    style: exclude_zero # or 'include_zero'

  MergeableSelector:
    enabled: false
    force_nesting: true

  NameFormat:
    enabled: false
    allow_leading_underscore: true
    convention: hyphenated_lowercase # or 'camel_case', or 'snake_case', or a regex pattern

  NestingDepth:
    enabled: true
    max_depth: 4
    ignore_parent_selectors: false

  PlaceholderInExtend:
    enabled: true

  PropertyCount:
    enabled: false
    include_nested: false
    max_properties: 10

  PropertySortOrder:
    enabled: false
    ignore_unspecified: false
    min_properties: 2
    separate_groups: false

  PropertySpelling:
    enabled: true
    extra_properties: []
    disabled_properties: []

  PropertyUnits:
    enabled: true
    global: [
      'ch', 'em', 'ex', 'rem',                 # Font-relative lengths
      'cm', 'in', 'mm', 'pc', 'pt', 'px', 'q', # Absolute lengths
      'vh', 'vw', 'vmin', 'vmax',              # Viewport-percentage lengths
      'deg', 'grad', 'rad', 'turn',            # Angle
      'ms', 's',                               # Duration
      'Hz', 'kHz',                             # Frequency
      'dpi', 'dpcm', 'dppx',                   # Resolution
      '%']                                     # Other
    properties: {}

  PseudoElement:
    enabled: true

  QualifyingElement:
    enabled: true
    allow_element_with_attribute: true
    allow_element_with_class: true
    allow_element_with_id: true

  SelectorDepth:
    enabled: true
    max_depth: 5

  SelectorFormat:
    enabled: true
    convention: hyphenated_lowercase # or 'strict_BEM', or 'hyphenated_BEM', or 'snake_case', or 'camel_case', or a regex pattern

  Shorthand:
    enabled: true
    allowed_shorthands: [1, 2, 3, 4]

  SingleLinePerProperty:
    enabled: true
    allow_single_line_rule_sets: true

  SingleLinePerSelector:
    enabled: true

  SpaceAfterComma:
    enabled: false
    style: one_space # or 'no_space', or 'at_least_one_space'

  SpaceAfterPropertyColon:
    enabled: true
    style: one_space # or 'no_space', or 'at_least_one_space', or 'aligned'

  SpaceAfterPropertyName:
    enabled: true

  SpaceAfterVariableName:
    enabled: true

  SpaceAroundOperator:
    enabled: true
    style: one_space # or 'at_least_one_space', or 'no_space'

  SpaceBeforeBrace:
    enabled: true
    style: space # or 'new_line'
    allow_single_line_padding: false

  SpaceBetweenParens:
    enabled: true
    spaces: 0

  StringQuotes:
    enabled: true
    style: single_quotes # or double_quotes

  TrailingSemicolon:
    enabled: true

  TrailingWhitespace:
    enabled: true

  TrailingZero:
    enabled: false

  TransitionAll:
    enabled: false

  UnnecessaryMantissa:
    enabled: true

  UnnecessaryParentReference:
    enabled: true

  UrlFormat:
    enabled: true

  UrlQuotes:
    enabled: true

  VariableForProperty:
    enabled: false
    properties: []

  VendorPrefix:
    enabled: true
    identifier_list: base
    additional_identifiers: []
    excluded_identifiers: []

  ZeroUnit:
    enabled: true

  Compass::*:
    enabled: false
