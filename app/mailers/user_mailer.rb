require 'mail'

class UserMailer < BaseMailer
  def welcome(user_id)

    @user = User.find_by_id(user_id)
    return false if @user.blank?

    address = Mail::Address.new @user.email # ex: "<EMAIL>"
    address.display_name = @user.name # ex: "<PERSON>"
    # Set the From or Reply-To header to the following:
    address.format # returns "<PERSON> <<EMAIL>>"

    mail(to: address.format, subject: t('mail.welcome_subject', app_name: Setting.app_name).to_s)
  end
end
