require 'mail'

class Notify<PERSON><PERSON>er < BaseMailer

  def self.send_replacement_request(shift)
    # @recipients = 
    @recipients.each do |recipient|
      notify(recipient, shift).deliver_later
    end
  end

  def request_replacement(recipient, shift)

  end


  def notify(user_id)

    @user = User.find_by_id(user_id)
    return false if @user.blank?

    address = Mail::Address.new @user.email # ex: "<EMAIL>"
    address.display_name = @user.name # ex: "<PERSON>"
    # Set the From or Reply-To header to the following:
    address.format # returns "<PERSON> <<EMAIL>>"

    mail(to: address.format, subject: t('mail.welcome_subject', app_name: Setting.app_name).to_s)
  end
end
