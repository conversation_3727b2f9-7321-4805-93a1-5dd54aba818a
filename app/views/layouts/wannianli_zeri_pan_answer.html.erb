<!DOCTYPE html>
<html lang="<%= I18n.locale %>">
<%= render :partial => "/head" %>

<body id="<%= params[:controller].parameterize%>">
<div id="iwnl">
	<div id="iwnl_main">
		<div id="iwnl_content">
		    <%= yield :content%>
		</div>
		<div id="iwnl_rightbar">
		    <%= yield :rightbar %>
		</div>
	</div>
</div>
  <%= render :partial => "/ga" %>
  <span id="server_response_time" data-time="<%= sprintf('%.3f', (Time.now.usec - @start_time).abs / 1000000.0) %>"></span>
  <script>
    function downloadJSAtOnload() {
    var element = document.createElement("script");
    element.src = "<%= asset_path 'application.js' %>";
    document.body.appendChild(element);
    }
    if (window.addEventListener)
    window.addEventListener("load", downloadJSAtOnload, false);
    else if (window.attachEvent)
    window.attachEvent("onload", downloadJSAtOnload);
    else window.onload = downloadJSAtOnload;
  </script>
</body>
</html>
