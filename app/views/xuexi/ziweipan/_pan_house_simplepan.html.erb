<% 
  star_names = []
  star_types = []
  star_ids = []
  sStarInfos = []
  aAStarInfos[nEarthIndex].each do |sAStarInfo|
    star_names.push(Star.Get_AStarInfo_Name(sAStarInfo))
    star_types.push(Star.Get_AStarInfo_StarType(sAStarInfo))
    star_ids.push(Star.Get_AStarInfo_Star(sAStarInfo))
    sStarInfos.push(sAStarInfo)
  end 
  if (star_names.length < 9) then
    aBStarInfos[nEarthIndex][0..9-star_names.length-1].each do |sBStarInfo|
      star_names.push(Star.Get_BStarInfo_Name(sBStarInfo))
      star_types.push(Star.Get_BStarInfo_StarType(sBStarInfo))
      star_ids.push(Star.Get_BStarInfo_Star(sBStarInfo))
      sStarInfos.push(sBStarInfo)
    end
  end 
  # star_names += star_names
  # star_types += star_types
  # star_ids += star_ids
  # sStarInfos += sStarInfos
%>
        <% if (aPanBg34[nEarthIndex] != 99) then %>
        <% if !(@gong_dizhi != nil && aPanBg34[nEarthIndex] == 0) then %>
            <div class="bg34_<%= aPanBg34[nEarthIndex] %>_<%= nEarth - nEarth %>" id="bg34_<%= aPanBg34[nEarthIndex] %>"></div>
        <% else %>
            <div class="bg34_<%= aPanBg34[nEarthIndex] %>_<%= @gong_dizhi %>" id="bg34_<%= aPanBg34[nEarthIndex] %>"></div>
        <% end %>
        <% end %>
        <div id="earth<%=nEarth%>">
                <div id = "houseline1" class="houseline1" style="width:100%;height:30%">
                    <% i = 0 %>
                    <% star_names[0..6].each_with_index do |star_name,idx| %>
                        <% i += 1 %>
                        <div id="<%=star_types[idx]%>star<%=star_ids[idx]%>_<%=i%>_pos" class="astar_pos_<%=i%>" style="left:<%=100-100/7*(i)%>%;height: 100%;width:14%">
                            <div name="<%=star_types[idx]%>star<%=star_ids[idx]%>" id="<%=star_types[idx]%>star<%=star_ids[idx]%>" class="astar" onmouseover="<%=make_AStarMouseOver2(oStar,nPanType,i,aAStarFourHuaSky,nEarthIndex,hApFunc,sStarInfos[idx])%>" onmouseup="<%=make_AStarMouseUp(oStar,nPanType,i,aAStarFourHuaSky,nEarthIndex,hApFunc,sStarInfos[idx])%>"  style="font-size:130%;width:100%;height:100%;line-height:100%">
                              <%=star_name%>
                            </div>
                        </div>
                    <% end %>
                </div>

                <div id = "houseline2" class="houseline2" style="top:30%;width:100%;height:30%">
                    <div id = "houseline2_2" class="houseline2_2" style="top:0%;left:0%;height: 50%;width:100%">
                            <% i = 0 %>
                            <% aAStarOrgFourHua[nEarthIndex][0..3].each do |aOrgFourHua| %>
                            <% i += 1 %>
                                <div id="orgfourhua_<%=nEarth%>_pos_<%=i%>" class="orgfourhua_pos_<%=i%>" style="left:<%=100/7*(7-i)%>%;height: 100%;width:14%">
                                    <div id="orgfourhua<%=aOrgFourHua[0]%>" class="orgfourhua<%=aOrgFourHua[0]%>" style="top:5%;font-size:100%;width:100%;height:95%">
                                        <div id="orgfourhuadata_<%=nEarth%>_<%=i%>" class="orgfourhuadata" style="font-size:120%;width:100%;height:100%">
                                            <%=aOrgFourHua[1]%>
                                        </div>
                                    </div>
                                </div>
                            <% end %>
                        </div>

                        <div id = "houseline2_3" class="houseline2_3" style="font-size:100%;width:100%;height:50%;left:0%;top:50%">
                        <% if (ShowSelfFourHua(hApFunc["SelfFourHua"],bAStarSelfFourHuaDisplay)) then %>
                            <% nSelfFourhuaIndex = 0 %>
                            <% aAStarSelfFourHua[nEarthIndex][0..3].each do |aAStarSelfFourHua| %>
                            <% nSelfFourhuaIndex += 1 %>
                                <div id="selffourhua_<%=nEarth%>_pos_<%=nSelfFourhuaIndex%>" class="selffourhua_pos_<%=nSelfFourhuaIndex%>" style="left:<%=100/7*(7-nSelfFourhuaIndex)%>%;height:100%;width:14%">
                                    <div id="selffourhua<%=aAStarSelfFourHua[0]%>" class="selffourhua<%=aAStarSelfFourHua[0]%>" style="top:0%;left:0%;font-size:100%;width:100%;height:90%">
                                        <div id="selffourhuadata_<%=nEarth%>_<%=nSelfFourhuaIndex%>" class="selffourhuadata" style="position:absolute;top:15%;left:0%;font-size:120%;width:100%;height:100%">
                                            <%=aAStarSelfFourHua[1]%>
                                        </div>
                                    </div>
                                </div>
                            <% end %>
                        <% end %>
                        </div>
                </div>

                <div id ="houseline4" class="houseline4" style="top:60%;font-size:100%;line-height: 100%;width:100%;height: 40%">
                    <div id ="houseline4_1" class="houseline4_1" style="left:30%;top:0%;width:40%;height:30%">
                        <% if (nPanType == Cfate::PAN_NORMAL) then %>
                            <% if (bSmallDisplay) then %>
                                <div id="small_pos_<%=nEarth%>" class="small_pos" style="top:0%;left:20%;width:60%;height:100%">
                                <% 
                                  nSmallId = 0 
                                  nSmall = aSmall[nEarthIndex][0]
                                  nSmallId = aSmall[nEarthIndex].length / 2
                                %>
                                    <div id="pan_small_<%=nEarth%>_<%=nSmallId%>" class="pan_small_<%=nSmallId%>" style="top:0%;left:30%;width:50%;height:100%">
                                            <div id="pan_small_<%=nEarth%>_<%=nSmallId%>_col_1" class="pan_small_col_1" style="top:0%;left:0%;width:100%;height:100%;font-size:100%">
                                                <%="#{nSmall}"%>
                                            </div>
                                    </div>
                                </div>
                            <% end %>
                        <% elsif (nPanType == Cfate::PAN_TENYEAR) then %>
                            <% if (bSmallDisplay) then %>
                                <% 
                                  nSmallId = 0 
                                  nSmall = aSmall[nEarthIndex][0]
                                  nSmallId = aSmall[nEarthIndex].length / 2
                                %>
                                <div id="small_pos_<%=nEarth%>" class="small_pos" style="top:0%;left:20%;width:60%;height:100%">
                                    <div id="pan_small_<%=nEarth%>_<%=nSmallId%>" class="pan_small_<%=nSmallId%>" style="top:0%;left:30%;width:50%;height:100%">
                                        <div id="pan_small_<%=nEarth%>_<%=nSmallId%>_col_1" class="pan_small_col_1" style="top:0%;left:0%;width:100%;height:100%;font-size:100%">
                                            <%=nSmall%>
                                        </div>
                                    </div>
                                </div>
                            <% end %>
                        <% elsif (nPanType == Cfate::PAN_FLOWYEAR) then %>
                            <% if (aPanSmallSanYear[nEarthIndex] != nil) then %>
                                <div id="pan_smallsanyear_pos_<%=nEarth%>" class="pan_smallsanyear_pos" style="top:0%;left:0%;width:100%;height:100%">
                                    <div id="pan_smallsanyear_<%=nEarth%>" class="pan_smallsanyear" style="top:0%;left:0%;width:100%;height:100%;font-size:100%">
                                        <%=aPanSmallSanYear[nEarthIndex]%>
                                    </div>
                                </div>
                            <% end %>
                        <% else %>
                        <% end %>
                    </div>
                    <div id ="houseline4_2" class="houseline4_2" style="left:21%;top:30%;width:58%;height:30%">
                        <div id="pan_info_pos_<%=nEarth%>" class="pan_info_pos" style="top:0%;left:0%;width:100%;height:100%;font-size:100%">
                            <% if (nPanType == Cfate::PAN_NORMAL) then %>
                                <% if (bLargeDisplay) then %>
                                    <div id="large" class="large" onmouseover="Star_ButtonOver(this,<%=@bPanChange && hApFunc[Cfate.gGetPanTypeHashKey(Cfate::PAN_TENYEAR)] && ((aLarge[nEarthIndex] + nUi_EY - 1 + 9) <= nPanLastYear)%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange && hApFunc[Cfate.gGetPanTypeHashKey(Cfate::PAN_TENYEAR)] && ((aLarge[nEarthIndex] + nUi_EY - 1 + 9) <= nPanLastYear)%>)" onmouseup="PanNormalLargeUp(this,'<%=sMy_url%>',<%=Cfate::PAN_TENYEAR%>,<%=nUi_EY + aLarge[nEarthIndex]-1%>,<%=@bPanChange && hApFunc[Cfate.gGetPanTypeHashKey(Cfate::PAN_TENYEAR)] && ((aLarge[nEarthIndex] + nUi_EY - 1 + 9) <= nPanLastYear)%>)" style="font-size:100%">
                                            <%=aLarge[nEarthIndex]%> - <%=aLarge[nEarthIndex] + 9%>
                                    </div>
                                <% end %>
                            <% elsif (nPanType == Cfate::PAN_TENYEAR) then %>
                                <% if (bLargeDisplay) then %>
                                    <% if (nLargeSan == nEarth) then %>
                                        <div id="large_san" class="large_san" onmouseover="Star_ButtonOver(this,<%=@bPanChange && ((aLarge[nEarthIndex] + nUi_EY - 1 + 9) <= nPanLastYear)%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange && ((aLarge[nEarthIndex] + nUi_EY - 1 + 9) <= nPanLastYear)%>)" onmouseup="PanTenyearLargeUp(this,'<%=sMy_url%>',<%=Cfate::PAN_TENYEAR%>,<%=nUi_EY + aLarge[nEarthIndex]-1%>,<%=@bPanChange && ((aLarge[nEarthIndex] + nUi_EY - 1 + 9) <= nPanLastYear)%>)" style="font-size:100%">
                                            <%=aLarge[nEarthIndex]%> - <%=aLarge[nEarthIndex] + 9%>
                                        </div>
                                    <% else %>
                                        <div id="large" class="large" onmouseover="Star_ButtonOver(this,<%=@bPanChange && ((aLarge[nEarthIndex] + nUi_EY - 1 + 9) <= nPanLastYear)%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange && ((aLarge[nEarthIndex] + nUi_EY - 1 + 9) <= nPanLastYear)%>)" onmouseup="PanTenyearLargeUp(this,'<%=sMy_url%>',<%=Cfate::PAN_TENYEAR%>,<%=nUi_EY + aLarge[nEarthIndex]-1%>,<%=@bPanChange && ((aLarge[nEarthIndex] + nUi_EY - 1 + 9) <= nPanLastYear)%>)" style="font-size:100%">
                                            <%=aLarge[nEarthIndex]%> - <%=aLarge[nEarthIndex] + 9%>
                                        </div>
                                    <% end %>
                                <% end %>
                            <% elsif (nPanType == Cfate::PAN_FLOWYEAR) then %>
                                <% if (aPanFlowYear[nEarthIndex]) then %>
                                    <div id="pan_flowyear" class="pan_flowyear" onmouseover="Star_ButtonOver(this,<%=@bPanChange && (aPanYear[nEarthIndex] <= nPanLastYear)%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange && (aPanYear[nEarthIndex] <= nPanLastYear)%>)" onmouseup="PanFlowyearYearoldUp(this,'<%=sMy_url%>',<%=Cfate::PAN_FLOWYEAR%>,<%=aPanYear[nEarthIndex]%>,<%=@bPanChange && (aPanYear[nEarthIndex] <= nPanLastYear)%>)" style="font-size:100%">
                                        <%=aPanYearOld[nEarthIndex][2..10]%>
                                    </div>
                                <% else %>
                                    <div id="pan_year" class="pan_year" onmouseover="Star_ButtonOver(this,<%=@bPanChange && (aPanYear[nEarthIndex] <= nPanLastYear)%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange && (aPanYear[nEarthIndex] <= nPanLastYear)%>)" onmouseup="PanFlowyearYearoldUp(this,'<%=sMy_url%>',<%=Cfate::PAN_FLOWYEAR%>,<%=aPanYear[nEarthIndex]%>,<%=@bPanChange && (aPanYear[nEarthIndex] <= nPanLastYear)%>)" style="font-size:100%">
                                        <%=aPanYearOld[nEarthIndex]%>
                                    </div>
                                <% end %>
                            <% elsif (nPanType == Cfate::PAN_FLOWMONTH) then %>
                                <% if (nFlowMonthEarth == nEarth) then %>
                                    <div id="pan_flowmonth" class="pan_flowmonth" onmouseover="Star_ButtonOver(this,<%=@bPanChange%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange%>)" onmouseup="PanFlowmonthMonthUp(this,'<%=sMy_url%>',<%=Cfate::PAN_FLOWMONTH%>,<%=nFlowEYear%>,<%=aPanMonth[nEarthIndex]%>,<%=@bPanChange%>)" style="font-size:100%">
                                        <%=aPanMonthStr[nEarthIndex][2..10]%>
                                    </div>
                                <% else %>
                                    <div id="pan_month" class="pan_month" onmouseover="Star_ButtonOver(this,<%=@bPanChange%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange%>)" onmouseup="PanFlowmonthMonthUp(this,'<%=sMy_url%>',<%=Cfate::PAN_FLOWMONTH%>,<%=nFlowEYear%>,<%=aPanMonth[nEarthIndex]%>,<%=@bPanChange%>)" style="font-size:100%">
                                        <%=aPanMonthStr[nEarthIndex]%>
                                    </div>
                                <% end %>
                            <% elsif (nPanType == Cfate::PAN_FLOWDATE) then %>
                                <% if (nFlowDateEarth == nEarth) then %>
                                    <% nFlowDateStrLen = 2 + aPanDate[nEarthIndex].length + 1 %>
                                <% else %>
                                    <% nFlowDateStrLen = aPanDate[nEarthIndex].length + 1 %>
                                <% end %>
                                <% if (aPanDate[nEarthIndex].length == 2) then %>
                                    <% nfd_left = 30 %>
                                <% else %>
                                    <% nfd_left = 20 %>
                                <% end %>
                                <div id="pan_date_pos_<%=nEarth%>" class="pan_date_pos_<%=nFlowDateStrLen%>" style="top:0%;left:<%=nfd_left%>%;width:<%=100-nfd_left%>%;height:100%">
                                    <% if (nFlowDateEarth == nEarth && false) then %>
                                        <div id="pan_flowdate_flowstr" class="pan_flowdate_flowstr">
                                            <div id="pan_flowdate_flowstr_data" class="pan_flowdate_flowstr_data">
                                                <%=Pm.GetStr("IDS_S_FLOW_DATE")%>
                                            </div>
                                        </div>
                                    <% end %>
                                    <% (1..aPanDate[nEarthIndex].length).each do |nDateId| %>
                                    <% nDateIndex = nDateId - 1 %>
                                    <% nDate = aPanDate[nEarthIndex][nDateIndex] %>
                                        <% if nFlowDate == nDate then %>
                                            <div id="pan_flowdate_<%=nDateId%>" class="pan_flowdate_<%=nDateId%>" onmouseover="Star_ButtonOver(this,<%=@bPanChange%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange%>)" onmouseup="PanFlowdateDateUp(this,'<%=sMy_url%>',<%=Cfate::PAN_FLOWDATE%>,<%=nFlowEYear%>,<%=nFlowEMonth%>,<%=bFlowELeap%>,<%=nDate%>,<%=@bPanChange%>)">
                                                <div id="pan_flowdate_data" class="pan_flowdate_data" style="font-size:100%">
                                                    <%=nDate%>
                                                </div>
                                            </div>
                                        <% else %>
                                            <div id="pan_date_<%=nDateId%>" class="pan_date_<%=nDateId%>" onmouseover="Star_ButtonOver(this,<%=@bPanChange%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange%>)" onmouseup="PanFlowdateDateUp(this,'<%=sMy_url%>',<%=Cfate::PAN_FLOWDATE%>,<%=nFlowEYear%>,<%=nFlowEMonth%>,<%=bFlowELeap%>,<%=nDate%>,<%=@bPanChange%>)">
                                                <div id="pan_date_data" class="pan_date_data" style="font-size:100%">
                                                    <%=nDate%>
                                                </div>
                                            </div>
                                        <% end %>
                                    <% end %>
                                    <div id="pan_date_str" class="pan_date_str">
                                        <div id="pan_date_str_data" class="pan_date_str_data">
                                        </div>
                                    </div>
                                </div>
                            <% elsif (nPanType == Cfate::PAN_FLOWTIME) then %>
                                <% if (nFlowTimeEarth == nEarth) then %>
                                    <div id="pan_flowtime_pos_<%=nEarth%>" class="pan_flowtime_pos" style="left:0%;top:0%;width:100%;height:100%">
                                        <div id="pan_flowtime" class="pan_flowtime" onmouseover="Star_ButtonOver(this,<%=@bPanChange%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange%>)" onmouseup="PanFlowtimeTimeUp(this,'<%=sMy_url%>',<%=Cfate::PAN_FLOWTIME%>,<%=nFlowEYear%>,<%=nFlowEMonth%>,<%=bFlowELeap%>,<%=nFlowEDate%>,<%=aPanTime[nEarthIndex]%>,<%=@bPanChange%>)" style="left:0%;top:0%;width:100%;height:100%;font-size:100%">
                                            <%=aPanTimeStr[nEarthIndex][2..100]%>
                                        </div>
                                    </div>
                                <% else %>
                                    <div id="pan_time_pos_<%=nEarth%>" class="pan_time_pos" style="left:0%;top:0%;width:100%;height:100%">
                                        <div id="pan_time" class="pan_time" onmouseover="Star_ButtonOver(this,<%=@bPanChange%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange%>)" onmouseup="PanFlowtimeTimeUp(this,'<%=sMy_url%>',<%=Cfate::PAN_FLOWTIME%>,<%=nFlowEYear%>,<%=nFlowEMonth%>,<%=bFlowELeap%>,<%=nFlowEDate%>,<%=aPanTime[nEarthIndex]%>,<%=@bPanChange%>)" style="left:0%;top:0%;width:100%;height:100%;font-size:100%">
                                            <%=aPanTimeStr[nEarthIndex]%>
                                        </div>
                                    </div>
                                <% end %>
                            <% elsif (nPanType == Cfate::PAN_FLOWMIN) then %>
                                <% if (nFlowMinEarth == nEarth) then %>
                                    <div id="pan_flowmin_pos_<%=nEarth%>" class="pan_flowmin_pos" style="left:0%;top:0%;width:100%;height:100%">
                                        <div id="pan_flowmin" class="pan_flowmin" onmouseover="Star_ButtonOver(this,<%=@bPanChange%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange%>)" onmouseup="PanFlowminMinuteUp(this,'<%=sMy_url%>',<%=Cfate::PAN_FLOWMIN%>,<%=nFlowEYear%>,<%=nFlowEMonth%>,<%=bFlowELeap%>,<%=nFlowEDate%>,<%=aPanMinHour[nEarthIndex]%>,<%=aPanMin[nEarthIndex]%>,<%=@bPanChange%>)" style="left:0%;top:0%;width:100%;height:100%;font-size:100%">
                                            <%=aPanMinStr[nEarthIndex][2..6]%>
                                        </div>
                                    </div>
                                <% else %>
                                    <div id="pan_min_pos_<%=nEarth%>" class="pan_min_pos" style="left:0%;top:0%;width:100%;height:100%">
                                        <div id="pan_min" class="pan_min" onmouseover="Star_ButtonOver(this,<%=@bPanChange%>)" onmouseout="Star_ButtonOut(this,<%=@bPanChange%>)" onmouseup="PanFlowminMinuteUp(this,'<%=sMy_url%>',<%=Cfate::PAN_FLOWMIN%>,<%=nFlowEYear%>,<%=nFlowEMonth%>,<%=bFlowELeap%>,<%=nFlowEDate%>,<%=aPanMinHour[nEarthIndex]%>,<%=aPanMin[nEarthIndex]%>,<%=@bPanChange%>)" style="left:0%;top:0%;width:100%;height:100%;font-size:100%">
                                            <%=aPanMinStr[nEarthIndex][0..4]%>
                                        </div>
                                    </div>
                                <% end %>
                            <% end %>
                        </div>
                    </div>

                    <% bHasExplain = checkExplain(hApFunc,nPanType,aHouseId[nEarthIndex]) %>
                    <% if (nPanType == Cfate::PAN_NORMAL) then %>
                      <div id = "houseline4_3_<%=nEarth%>" class="houseline4_3" style="left:0%;top:0%;width:40%;height:100%">
                            <div id="pan_normal_houseinfo_pos_<%=nEarth%>" class="pan_normal_houseinfo_pos" style="left:0%;top:0%;width:100%;height:100%">
                                <% if (nBodyHouse == nEarth) then %>
                                    <div id="pan_normal_houseinfo_bodyname_pos" class="pan_normal_houseinfo_bodyname_pos" style="left:55%;top:50%;width:20%;height:30%">
                                        <div id="pan_normal_bodyname" class="pan_normal_bodyname" style="left:0%;top:0%;width:100%;height:100%">
                                            <%=Pm.GetStrWithQuote("IDS_S_BODY_STRING")[1..1]%>
                                        </div>
                                    </div>
                                <% end %>
                                <div id="pan_normal_houseinfo_housename_pos_<%=nEarth%>" class="pan_normal_houseinfo_housename_pos" style="left:10%;top:0%;width:45%;height:100%">
                                    <div id="pan_normal_houseinfo_housename_<%=nEarth%>" class="pan_normal_houseinfo_housename" onmouseover="Star_ButtonOver(this,<%=bHasExplain%>)" onmouseout="Star_ButtonOut(this,<%=bHasExplain%>)" onmouseup="PanHouseButtonUp(this,<%=bHasExplain%>,<%=nPanType%>,<%=nEarth%>,<%=aUrlKey%>,<%=aUrlValue%>)" style="cursor:pointer;left:0%;top:0%;width:100%;height:100%;font-size:150%;line-height:100%">
                                        <%=aHouseName[nEarthIndex][1..aHouseName[nEarthIndex].length-2]%>
                                    </div>
                                </div>
                            </div>
                        </div>
                      <% else %>
                        <div id = "houseline4_3_1" class="houseline4_3" style="left:0%;top:0%;width:40%;height:100%">
                            <div id="pan_flow_houseinfo_pos_<%=nEarth%>" class="pan_flow_houseinfo_pos" style="left:0%;top:0%;width:100%;height:100%">
                                <div id="pan_flow_houseinfo_housename_flow_pos_<%=nEarth%>" class="pan_flow_houseinfo_housename_flow_pos" style="left:10%;top:0%;width:45%;height:100%">
                            <% if (nEarth == nGiSuongEarth) then  %>
                                    <div id="pan_flow_houseinfo_housename_flow_<%=nEarth%>" class="pan_flow_houseinfo_housename_flow_gisuong"  onmouseover="Star_ButtonOver(this,<%=bHasExplain%>)" onmouseout="Star_ButtonOut(this,<%=bHasExplain%>)" onmouseup="PanHouseButtonUp(this,<%=bHasExplain%>,<%=nPanType%>,<%=nEarth%>,<%=aUrlKey%>,<%=aUrlValue%>)" style="cursor:pointer;left:0%;top:0%;width:100%;height:100%;font-size:150%;line-height:100%">
                            <% else  %>
                                    <div id="pan_flow_houseinfo_housename_flow_<%=nEarth%>" class="pan_flow_houseinfo_housename_flow"  onmouseover="Star_ButtonOver(this,<%=bHasExplain%>)" onmouseout="Star_ButtonOut(this,<%=bHasExplain%>)" onmouseup="PanHouseButtonUp(this,<%=bHasExplain%>,<%=nPanType%>,<%=nEarth%>,<%=aUrlKey%>,<%=aUrlValue%>)" style="cursor:pointer;left:0%;top:0%;width:100%;height:100%;font-size:150%;line-height:100%">
                            <% end  %>
                                        <%=aHouseName[nEarthIndex][1..aHouseName[nEarthIndex].length-2]%>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id = "houseline4_3_2" class="houseline4_3" style="left:21%;top:60%;width:58%;height:30%">
                                <div id="pan_flow_houseinfo_housename_normal_pos_<%=nEarth%>" class="pan_flow_houseinfo_housename_normal_pos" style="left:20%;top:0%;width:60%;height:100%">
                                    <div id="pan_flow_houseinfo_housename_normal_<%=nEarth%>" class="pan_flow_houseinfo_housename_normal" style="left:0%;top:0%;width:100%;height:100%;font-size:120%">
                                        <%=aHouseName_Normal[nEarthIndex][1..aHouseName_Normal[nEarthIndex].length-2]%>
                                    </div>
                                </div>
                        </div>
                      <% end %>

                    <div id = "houseline4_4" class="houseline4_4" style="left:75%;top:30%;width:25%;height:70%">
                        <div id="housesky_pos_<%=nEarth%>" class="housesky_pos" style="left:50%;top:0%;width:50%;height:50%">
                            <div name="housesky<%=aHouseSky[nEarthIndex]%>" id="housesky_<%=aHouseSky[nEarthIndex]%>_<%=nEarth%>" class="housesky" onmouseover="<%=make_HouseSkyMouseOver2(oStar,nPanType,aSkyAStarFourHua,nEarthIndex,hApFunc)%>" onmouseup="<%=make_HouseSkyMouseUp(oStar,nPanType,aSkyAStarFourHua,nEarthIndex,hApFunc)%>" style="left:0%;top:0%;width:100%;height:100%;font-size:120%">
                                <%=aHouseSkyStr[nEarthIndex]%>
                            </div>
                        </div>
                        <div id="housefive_pos_<%=nEarth%>" class="housefive_pos" style="left:0%;top:30%;width:50%;height:50%">
                            <div id="housefive" class="housefive" style="left:0%;top:0%;width:100%;height:100%;font-size:120%">
                                <%=aHouseFiveStr[nEarthIndex]%>
                            </div>
                        </div>
                    <% if ((hApFunc["GiSuongEnable"] == true) && (nPanType != Cfate::PAN_NORMAL)) then %>
                        <div id="houseearth_pos_<%=nEarth%>" class="houseearth_pos" onmouseover="px_MouseOver(this,true)" onmouseout="px_MouseOut(this,true)" onmouseup="HouseEarthMouseUp(this,'<%=sMy_url%>?GiSuongHouseEarth=<%=nEarth%>')" style="left:50%;top:50%;width:50%;height:50%">
                    <% else %>
                        <div id="houseearth_pos_<%=nEarth%>" class="houseearth_pos_noevent" style="left:50%;top:50%;width:50%;height:50%">
                    <% end %>
                            <div id="houseearth_<%=nEarth%>" class="houseearth" style="left:0%;top:0%;width:100%;height:100%;font-size:120%">
                                <%=aHouseEarthStr[nEarthIndex]%>
                            </div>
                        </div>
                    </div>

                </div>
        </div>
