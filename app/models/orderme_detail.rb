class OrdermeDetail < ActiveRecord::Base
  belongs_to :ordermes
  # attr_accessible :discount_amount, :orderme_id, :original_amount, :paid_amount, :product_description, :product_id, :product_name, :product_type, :product_version, :refund_amount

  def self.check_ids(orderme_ids)
    where(["id in (:ids)", :ids => orderme_ids ])
  end
  def self.check_ordermeid(orderme_id)
    where(["orderme_id = ?", orderme_id ])
  end
  def self.check_productid(product_id)
    where(["product_id = ?", product_id ])
  end
  def self.check_main()
    where(["product_type = ?", "main" ])
  end
  def self.check_additional()
    where(["product_type = ?", "additional" ])
  end

end
