class Testdb < ActiveRecord::Base
  # attr_accessible :key, :value

  def self.check_key(key)
    where(["key = ?", key ])
  end
  def self.check_key_like(key)
    where(["key like ?", key ])
  end

  def hValue
    if (self.value == nil || self.value == "null") then
      return Hash.new
    else
      return JSON.parse(self.value)
    end
  end

  def hValue=(value)
    self.value = value.to_json
  end

end
