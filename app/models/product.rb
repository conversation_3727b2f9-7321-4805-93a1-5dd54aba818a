class Product < ActiveRecord::Base
  	# default_scope { where("(startdate <= ? or startdate = ?) and (enddate >= ? or enddate = ?) and status = 0", nil,Xdate.GetNowRails(),Xdate.GetNowRails(),nil) }
  	default_scope { where("(start_date <= ? or start_date is null) and (end_date >= ? or end_date is null) and status = 0", Xdate.GetNowRails(),Xdate.GetNowRails()) }

    def free_amount
        (discount_amount == 0)
    end

    def free
        (product_type == "free")
    end

    def demo
        (product_type == "demo")
    end

    def main
        # (product_type != "free")
        (product_type == "main")
    end

    def forever
        (product_type == "forever")
    end

    def additional
        (product_type == "additional")
    end

    def self.check_main()
        # where("product_type != ?","demo")
        where("product_type = ?","main")
    end
    def self.check_additional()
        where("product_type = ?","additional")
    end
    def self.check_demo()
        where("product_type = ?","demo")
    end
    def self.check_show()
        where("product_type = ?","show")
    end
    def self.check_free()
        where("product_type = ?","free")
    end
    def self.check_guest()
        where("product_type = ?","guest")
    end
    def self.check_forever()
        where("product_type = ?","forever")
    end
    def self.check_status(status)
        where("status = ?", status)
    end
    def self.excludeId(product_id)
        where(["id != ?", product_id ])
    end

    def self.excludeVersion(versions)
        where(["product_version not in (:vers) ", :vers => versions ])
    end

    def self.available()
        where(["start_date <= ? and end_date >= ? and status = 0", Xdate.GetNowRails(),Xdate.GetNowRails() ])
    end
    def self.check_type(type)
  		where(["product_type = ?", type ])
	  end

    def self.check_name(name)
        where(["product_name like ?" , "%" + name + "%" ])
	  end
    def self.check_amount_great(amount)
        where(["discount_amount > ?", amount ])
    end
    def self.check_discountamount(discount_amount)
        where(["discount_amount = ?", discount_amount ])
    end

    def self.check_originalamount(original_amount)
        where(["original_amount = ?", original_amount ])
    end
    def self.check_version(version)
      where(["product_version = ?", version ])
    end

    def self.paid_products(amount=0)
        where(["original_amount > ? ", amount ])
    end
    def self.excludeIds(ids)
        where(["id not in (:ids) ", :ids => ids ])
    end
    def self.includeIds(ids)
        where(["id in (:ids) ", :ids => ids ])
    end

    def self.check_ws_dates(ws_dates)
        where(["product_content = ?", ws_dates ])
    end

    def self.check_code(code)
        where(["product_version = ?" , code ])
    end
end
