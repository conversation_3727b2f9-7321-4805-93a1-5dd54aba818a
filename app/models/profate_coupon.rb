class ProfateCoupon < ActiveRecord::Base
  default_scope { where("(start_date <= ? or start_date is null) and (end_date >= ? or end_date is null)", Xdate.GetNowRails(),Xdate.GetNowRails()) }

  def self.check_uuid(uuid)
     where(["uuid = ?",uuid])
  end
  # flag : 已發出 1，已使用 2，未發出 0
  def self.created()
     where(["flag = 0"])
  end
  def self.issued()
     where(["flag = 1"])
  end
  def self.used()
     where(["flag = 2"])
  end
  def self.valid()
     where(["flag = 1"])
  end
  def valid
    return (self.flag == 1)
  end
end
