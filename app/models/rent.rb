class Rent < ActiveRecord::Base
	has_one :ordermes

	def self.available()
		# where(["start_date <= ? and end_date >= ? and status = 0 and orderme_id > 0", Xdate.GetNowRails(),Xdate.GetNowRails() ])
		where(["end_date >= ? and status = 0 and orderme_id > 0", Xdate.GetNowRails() ])
	end

  def self.check_ordermeid(orderme_id)
    where(["orderme_id = ?", orderme_id])
  end
  def self.check_userid(user_id)
    where(["user_id = ?", user_id])
  end
  def self.check_status(status)
    where(["status = ?", status])
  end
  def self.check_flag(flag)
    where(["flag = ?", flag])
  end
  def self.check_date(dateIn)
    where(["start_date <= ? and end_date >= ?", dateIn,dateIn])
  end
end
