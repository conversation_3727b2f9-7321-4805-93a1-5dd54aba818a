class PickDays < ActiveRecord::Base
  # attr_accessible :all_info, :hour, :sex, :wday, :wmonth, :wyear

  # sex : female 女生； male 男生
  def self.c_y(y)
    where(["wyear = ?", y ])
  end
  def self.c_m(m)
    where(["wmonth = ?", m ])
  end
  def self.c_d(d)
    where(["wday = ?", d ])
  end
  def self.c_h(h)
    where(["hour = ?", h ])
  end
  def self.c_s(s)
    where(["sex = ?", s ])
  end
  def hAll
    if (self.all_info == nil) then
      return Hash.new
    else
      return JSON.parse(self.all_info)
    end
    # return Cfate.pan_par_dbfield2hash(self.all_info)
  end

  def hAll=(value)
    self.all_info = value.to_json
    # self.all_info = Cfate.pan_par_hash2db(value)
  end
end
