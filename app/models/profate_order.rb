class ProfateOrder < ActiveRecord::Base
  def hParam
    if self.client_param.nil?
      {}
    else
      JSON.parse(self.client_param)
    end
  end

  def hParam=(value)
    unless value.nil?
      self.client_param = value.to_json
    end
  end

  def self.check_profate_event_id(profate_event_id)
    where(['profate_event_id = ?', profate_event_id])
  end

  def self.check_buy_point
    where(["p_type = 'buy'"])
  end

  def self.check_buy_event
    where(["p_type != 'buy'"])
  end

  def self.check_complete
    where(['status = 0'])
  end
end
