class IfatePar < ActiveRecord::Base
  def self.check_userid_star(user_id)
    where(["user_id = ? and ap_name = 'star'", user_id])
  end
  def self.check_userid_eightword(user_id)
     # where(["(start_date is not null and end_date is not null) and (DATEDIFF(end_date,start_date) >= 3650) and status = 0"])
     where(["user_id = ? and ap_name = 'eightword'",user_id])
  end
  def self.check_userid_liuyaogua(user_id)
     # where(["(start_date is not null and end_date is not null) and (DATEDIFF(end_date,start_date) >= 3650) and status = 0"])
     where(["user_id = ? and ap_name = 'liuyaogua'",user_id])
  end
  def self.check_userid_xdate(user_id)
     # where(["(start_date is not null and end_date is not null) and (DATEDIFF(end_date,start_date) >= 3650) and status = 0"])
     where(["user_id = ? and ap_name = 'xdate'",user_id])
  end
  def self.check_userid_wannianli(user_id)
    where(["user_id = ? and ap_name = 'wannianli'", user_id])
  end
  def self.check_userid_customer(user_id)
     # where(["(start_date is not null and end_date is not null) and (DATEDIFF(end_date,start_date) >= 3650) and status = 0"])
     where(["user_id = ? and ap_name = 'customer'",user_id])
  end
  def self.check_userid(user_id)
     # where(["(start_date is not null and end_date is not null) and (DATEDIFF(end_date,start_date) >= 3650) and status = 0"])
     where(["user_id = ?",user_id])
  end
  def self.check_apname(ap_name)
     # where(["(start_date is not null and end_date is not null) and (DATEDIFF(end_date,start_date) >= 3650) and status = 0"])
     where(["ap_name = ?",ap_name])
  end
  def hPars
    if (self.parameter == nil) then
      return Hash.new
    else
      return JSON.parse(self.parameter)
    end
    # return Cfate.pan_par_dbfield2hash(self.parameter)
  end

  def hPars=(value)
    self.parameter = value.to_json
    # self.parameter = Cfate.pan_par_hash2db(value)
  end


end
