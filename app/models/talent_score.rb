class TalentScore < ApplicationRecord
  # rails generate model talent_score talent_key:string timestamp_user:string pan_type:integer timestamp_udt:string status:integer scores:text
  def self.available()
    where(["status = 0"])
  end
  def self.check_talent_key(talent_key)
    where(["talent_key = ?", talent_key])
  end
  def self.check_status(status)
    where(["status = ?", status])
  end
  def self.check_timestamp_user(timestamp_user)
    where(["timestamp_user = ?", timestamp_user])
  end
  def self.timestamp_user_between(timestamp_user_1,timestamp_user_2)
    where(["(timestamp_user >= ?) and (timestamp_user <= ?)", timestamp_user_1,timestamp_user_2])
  end
  def self.check_pan_type(pan_type)
    where(["pan_type = ?", pan_type])
  end
  def self.check_timestamp_udt(timestamp_udt)
    where(["timestamp_udt = ?", timestamp_udt])
  end
  def hScores
    if (self.scores == nil || self.scores == "null") then
      return Hash.new
    else
      return JSON.parse(self.scores)
    end
  end
  def hScores=(value)
    self.scores = value.to_json
  end
end
