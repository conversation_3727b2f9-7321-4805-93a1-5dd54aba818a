require 'securerandom'
require 'digest/md5'
require 'open-uri'
# require 'geoip'

class User < ApplicationRecord
  # include OmniauthCallbacks
  # include Searchable
  # include Redis::Search

  acts_as_cached version: 4, expires_in: 1.week

  ALLOW_LOGIN_CHARS_REGEXP = /\A[a-z0-9\-\_\.]+\z/

  devise :database_authenticatable, :registerable, :recoverable, :lockable,
         :rememberable, :trackable, :validatable

  has_many :user_customers

  attr_accessor :password_confirmation

  ACCESSABLE_ATTRS = %i[name email_public location company bio website github twitter
                        tagline avatar by current_password password password_confirmation
                        _rucaptcha]

  enum state: { deleted: -1, normal: 1, blocked: 2 }

  validates :login, format: { with: ALLOW_LOGIN_CHARS_REGEXP, message: '只允許[數字]、[小寫字母]和[下劃線]' },
                    length: { in: 2..20 },
                    presence: true,
                    uniqueness: { case_sensitive: false }

  validates_exclusion_of :login, in: %w[access account accounts activate activities activity address administration administrator advertising affiliate affiliates analysis analytics android anonymous archive archives article authentication avatar backup balancer-manager banner banners billing bookmark business cadastro calendar campaign cancel captcha career careers categories category cgi-bin changelog checking checkout client cliente clients codereview comercial comment comments communities community company compare compras config configuration connect contact contact-us contact_us contactus contest contribute create dashboard default delete design designer destroy developer developers diagram dictionary direct_messages directory documentation domain download downloads ecommerce editor education employment enterprise entries errors explore facebook favorite favorites feature features feedback fleets follow followers following forgot forums founder friend friends gadget gadgets github groups guests homepage hosting hostmaster hostname iamges images indice information inquiry intranet invitations invite iphone issues javascript knowledgebase language languages ldap-status license log-in log-out log_in log_out logout mailer mailing maintenance manager manual marketing master member members message messages messenger microblog microblogs mobile movies musicas navigation network newsletter nickname noticias notification notifications notify oauth_clients offers official online openid operator orders organization organizations overview owners password payment photoalbum photos plugin plugins policy popular portal postfix postmaster premium pricing privacy privacy-policy privacy_policy privacypolicy private product products profile project projects public purpose python random ranking readme recent recruit recruitment register registration release remove replies report reports repositories repository request requests sample samples school script scripts search secure security server server-info server-status service services session sessions setting settings sign-in sign-up sign_in sign_up signin signout signup sitemap smartphone soporte source special ssladmin ssladministrator sslwebmaster staging static status stores stories styleguide stylesheet stylesheets subdomain subscribe subscriptions superuser suporte support sysadmin sysadministrator system tablet tablets telnet terms-of-service terms_of_service termsofservice testing themes thread threads topics translations trends tutorial twitter unfollow unsubscribe update upload uploads username usuario vendas version videos visitor weather webmail webmaster website websites welcome widget widgets windows workshop yourdomain yourname yoursite yourusername], message: '無法使用系統保留字串作為 ID'

  validates :name, length: { maximum: 20 }

  # scope :hot, -> { order(replies_count: :desc).order(topics_count: :desc) }
  # scope :without_team, -> { where(type: nil) }
  scope :fields_for_list, lambda {
    select(:type, :id, :name, :login, :email, :email_md5, :email_public, :avatar, :verified, :state,
           :tagline, :github, :website, :location, :location_id, :twitter, :co, :team_users_count)
  }

  # include Blockable
  # include Likeable
  # include Followable

  # include TopicRead
  # include TopicFavorate
  # include GithubRepository

  # from meen.tw to comply with the database rules.
  before_validation(on: :create) do
    # self[:provider] = "email" if self[:provider].blank?
    # self[:uid] = self[:email] if self[:uid].blank? && self[:email].present?
    self.provider = 'email' if self.provider.blank?
    self.uid = self.email if self.uid.blank? && self.email.present?
  end

  # def self.set_geoinfo
  #   g = GeoIP.new("#{Rails.root}/lib/geodata/GeoIP.dat")
  #   User.find_each do |u|
  #     # puts g.country(u.current_sign_in_ip).country_code2
  #     u.country_code2 = g.country(u.current_sign_in_ip).country_code2 if u.country_code2.blank? && u.current_sign_in_ip.present?
  #     u.country_code3 = g.country(u.current_sign_in_ip).country_code3 if u.country_code3.blank? && u.current_sign_in_ip.present?
  #     u.country_name = g.country(u.current_sign_in_ip).country_name if u.country_name.blank? && u.current_sign_in_ip.present?
  #     u.save!
  #   end
  # end

  def index_score
    0
  end

  def user_type
    (self[:type] || 'User').underscore.to_sym
  end

  def to_param
    login
  end

  def email=(val)
    self.email_md5 = Digest::MD5.hexdigest(val || '')
    self[:email] = val
  end

  def password_required?
    !password.blank? && super
  end

  def profile_url
    "/#{login}"
  end

  def fullname
    return login if name.blank?
    "#{login} (#{name})"
  end

  # 是否是管理員
  def admin?
    Setting.admin_emails.include?(email)
  end

  # 是否有 Wiki 維護權限
  def wiki_editor?
    # self.admin? || verified == true
    self.admin?
  end

  # 回帖大於 150 的才有酷站的發佈權限
  def site_editor?
    # self.admin? || replies_count >= 100
    self.admin?
  end

  # 是否能發帖
  def newbie?
    return false if verified? || hr?
    # created_at > 1.minute.ago
  end

  def roles?(role)
    case role
    when :admin then admin?
    when :wiki_editor then wiki_editor?
    when :site_editor then site_editor?
    when :member then self.normal?
    else false
    end
  end

  # 註冊郵件提醒
  # after_create :send_welcome_mail
  # def send_welcome_mail
  #   UserMailer.welcome(id).deliver_later
  # end

  # 保存用戶所在城市
  before_save :store_location
  def store_location
    if self.location_changed?
      if location.present?
        old_location = Location.location_find_by_name(self.location_was)
        old_location.decrement!(:users_count) if old_location

        location = Location.location_find_or_create_by_name(self.location)
        unless location.new_record?
          location.increment!(:users_count)
          self.location_id = location.id
        end
      else
        self.location_id = nil
      end
    end
  end

  def update_with_password(params = {})
    if !params[:current_password].blank? || !params[:password].blank? || !params[:password_confirmation].blank?
      super
    else
      params.delete(:current_password)
      update_without_password(params)
    end
  end

  def self.find_by_email(email)
    fetch_by_uniq_keys(email: email)
  end

  def self.find_login!(slug)
    find_login(slug) || raise(ActiveRecord::RecordNotFound.new(slug: slug))
  end

  def self.find_login(slug)
    return nil unless slug =~ ALLOW_LOGIN_CHARS_REGEXP
    fetch_by_uniq_keys(login: slug) || where('lower(login) = ?', slug.downcase).take
  end

  def self.find_by_login_or_email(login_or_email)
    login_or_email = login_or_email.downcase
    find_login(login_or_email) || find_by_email(login_or_email)
  end

  def self.find_for_database_authentication(warden_conditions)
    conditions = warden_conditions.dup
    logger.info "-------------- #{conditions.inspect}"
    login = conditions.delete(:login)
    login.downcase!
    where(conditions.to_h).where(['lower(login) = :value OR lower(email) = :value', { value: login }]).first
  end

  # Override Devise to send mails with async
  def send_devise_notification(notification, *args)
    devise_mailer.send(notification, self, *args).deliver_later
  end

  def bind?(provider)
    authorizations.collect(&:provider).include?(provider)
  end

  def bind_service(response)
    provider = response['provider']
    uid = response['uid'].to_s
    authorizations.create(provider: provider, uid: uid)
  end

  # 軟刪除
  # 只是把用戶信息修改了
  def soft_delete
    # assuming you have deleted_at column added already
    self.bio = ''
    self.website = ''
    self.github = ''
    self.tagline = ''
    self.location = ''
    self.authorizations = []
    self.state = User.states[:deleted]
    save(validate: false)
  end

  # 用戶的帳號類型
  def level
    if admin?
      'admin'
    elsif verified?
      'vip'
    elsif hr?
      'hr'
    elsif blocked?
      'blocked'
    elsif newbie?
      'newbie'
    else
      'normal'
    end
  end

  def level_name
    I18n.t("common.#{level}_user")
  end

  # def letter_avatar_url(size)
  #   path = LetterAvatar.generate(self.login, size).sub('public/', '/')

  #   "#{Setting.protocol}://#{Setting.domain}#{path}"
  # end

  # def large_avatar_url
  #   if self[:avatar].present?
  #     self.avatar.url(:lg)
  #   else
  #     self.letter_avatar_url(192)
  #   end
  # end

  # def avatar?
  #   self[:avatar].present?
  # end

  # @example.com 的可以修改郵件地址
  def email_locked?
    self.email.exclude?('@example.com')
  end

  def calendar_data
    Rails.cache.fetch(['user', self.id, 'calendar_data', Date.today, 'by-months']) do
      date_from = 12.months.ago.beginning_of_month.to_date
      replies = self.replies.where('created_at > ?', date_from)
                    .group("date(created_at AT TIME ZONE 'CST')")
                    .select("date(created_at AT TIME ZONE 'CST') AS date, count(id) AS total_amount").all

      replies.each_with_object({}) do |reply, timestamps|
        timestamps[reply['date'].to_time.to_i.to_s] = reply['total_amount']
      end
    end
  end

  def self.current
    Thread.current[:current_user]
  end

  def self.current=(user)
    Thread.current[:current_user] = user
  end

  def team_collection
    return @team_collection if defined? @team_collection
    teams = self.admin? ? Team.all : self.teams
    @team_collection = teams.collect { |t| [t.name, t.id] }
  end

  def organization?
    self.user_type == :team
  end

  def as_indexed_json(_options = {})
    as_json(only: %i[login name])
  end

  # from meen.tw
  def self.check_sign_in_count(n)
    where(['sign_in_count >= ?', n])
  end

  # from meen.tw
  def self.check_email(email)
    where('email like ?', '%' + email + '%')
  end
end
