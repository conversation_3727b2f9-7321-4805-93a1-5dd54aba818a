class ProfateEvent < ActiveRecord::Base
  def hParam
    if (self.param == nil) then
      return Hash.new
    else
      return JSON.parse(self.param)
    end
  end

  def hParam=(value)
    self.param = value.to_json
  end
  def hResult
    if (self.result == nil) then
      return Hash.new
    else
      return JSON.parse(self.result)
    end
  end

  def hResult=(value)
    self.result = value.to_json
  end

  # 已付款 2，未付款 1，使用coupon 3，免費4
  def self.can_use()
     where(["(flag = 2 or flag = 3 or flag = 4)"])
  end
  def self.paid()
     where(["flag = 2"])
  end
  def self.unpaid()
     where(["flag = 1"])
  end
  def self.use_coupon()
     where(["flag = 3"])
  end
  def self.free()
     where(["flag = 4"])
  end
  def self.check_uuid(uuid)
     where(["uuid = ?",uuid])
  end
  def self.check_flag(flag)
     where(["flag = ?",flag])
  end
  def can_be_use
     return (self.flag == 2 || self.flag == 3 || self.flag = 4)
  end
  def unpaid
     return (self.flag == 1)
  end
end
