class PersonalEvent < ActiveRecord::Base
  # attr_accessible :pe_desc, :pe_main_key, :pe_main_value, :pe_sub_key, :pe_sub_self, :pe_sub_value, :pe_time, :status, :user_customer_id, :user_id
  has_one :user_customers
  has_one :users

  def self.check_status(status)
      where("status = ?", status)
  end

    def self.available()
        where(["status = 0"])
    end

  def self.check_user_id(user_id)
      where("user_id = ?", user_id)
  end
  def self.check_option_user_id(user_id)
    where(user_id == nil ? "(user_id = 0)" : "(user_id = ?)" , user_id)
  end

  def self.check_user_customer_id(user_customer_id)
      where("user_customer_id = ?", user_customer_id)
  end

  # def self.last3pes(user_id,user_customer_id)
  #     self.available.check_user_id(user_id).check_user_customer_id(user_customer_id).last(3)
  # end

  def self.check_caltype(caltype)
      where('caltype = ?' , caltype)
  end
  def self.check_year(year)
      where('year = ?' , year)
  end
  def self.check_leap(leap)
      where('leap = ?' , leap == true ? 1 : 0)
  end
  def self.check_month(month)
      where('month = ?' , month)
  end
  def self.check_day(day)
      where('day = ?' , day)
  end
  def self.check_hour(hour)
      where('hour = ?' , hour)
  end
  def self.check_minute(minute)
      where('minute = ?' , minute)
  end
  def self.check_main_key(pe_main_key)
      where("pe_main_key = ?", pe_main_key)
  end
  def self.check_main_key_pe()
      where("pe_main_key like 'pe_%'")
  end
  def self.check_main_key_liuyaogua()
      where("pe_main_key = 'liuyaogua'")
  end
  
  def self.check_sub_key(pe_sub_key)
      where("pe_sub_key = ?", pe_sub_key)
  end
  def self.check_sub_self(pe_sub_self)
      where("pe_sub_self = ?", pe_sub_self)
  end
  def self.check_pe_desc(pe_desc)
      where("pe_desc = ?", pe_desc)
  end

  def h_pe_desc
    if (self.pe_desc == nil || self.pe_desc == "null") then
      return Hash.new
    else
      return JSON.parse(self.pe_desc)
    end
  end

  def h_pe_desc=(value)
    self.pe_desc = value.to_json
  end

end
