class ApiKey < ActiveRecord::Base
  # attr_accessible :avail_count, :content, :end_date, :key, :partner_id, :password, :start_date, :used_count

  default_scope { where("(start_date <= ? or start_date is null) and (end_date >= ? or end_date is null)", Xdate.GetNowRails(),Xdate.GetNowRails()) }
  def self.check_key(key)
      where(["key = ?", key ])
  end
  def self.count_avail()
      where(["avail_count = 0 or avail_count > (used_count + 1)" ])
  end
end
