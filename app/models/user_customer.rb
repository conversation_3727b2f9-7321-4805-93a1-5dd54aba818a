class UserCustomer < ActiveRecord::Base
	has_many :personal_events
	belongs_to :user
	@LimitCount = 20

  def self.check_user_id(user_id)
    where([user_id == nil ? 'user_id = -1' : 'user_id = ? and flag = 1' , user_id])
  end

  def self.check_option_user_id(user_id)
    where([user_id == nil ? 'user_id = 0' : '((user_id = ? and flag = 1) or (user_id = 0 and flag = 1))' , user_id])
  end

  def self.check_id(customer_id)
    where(['id = ?' , customer_id])
  end

  def self.next(id)
    where(['id > ?', id]).order("id asc")
  end

  def self.prev(id)
    where(['id < ?', id]).order("id desc")
  end

  def self.check_year(nYear)
    where([nYear == nil ? 'wyear = wyear' : '(wyear = ? or eyear = ?)' , nYear,nYear])
  end

  def self.check_years(nYear)
    where([nYear == nil ? 'wyear = wyear' : '(wyear in (:nWYears) or eyear in (:nEYears))' , :nWYears => nYear, :nEYears => nYear])
  end

  def self.check_month(nMonth)
    where([nMonth == nil ? 'wmonth = wmonth' : '(wmonth = ? or emonth = ?)' , nMonth, nMonth])
  end

  def self.check_month_day(nMonth,nDay)
    wM = nMonth == nil ? 'wmonth' : nMonth
    eM = nMonth == nil ? 'emonth' : nMonth
    wD = nDay == nil ? 'wday' : nDay
    eD = nDay == nil ? 'eday' : nDay
    s = "((wmonth = #{wM} and wday = #{wD}) or (emonth = #{eM} and eday = #{eD}))"
    where(s)
    # where('((wmonth = ? and wday = ?) or (emonth = ? and eday = ?))' , wM, wD,eM,eD)
  end
  def self.check_year_month_day(nYear,nMonth,nDay)
    wY = nYear == nil ? 'wyear' : nYear
    eY = nYear == nil ? 'eyear' : nYear
    wM = nMonth == nil ? 'wmonth' : nMonth
    eM = nMonth == nil ? 'emonth' : nMonth
    wD = nDay == nil ? 'wday' : nDay
    eD = nDay == nil ? 'eday' : nDay
    s = "((wyear = #{wY} and wmonth = #{wM} and wday = #{wD}) or (eyear = #{eY} and emonth = #{eM} and eday = #{eD}))"
    where(s)
    # where('((wmonth = ? and wday = ?) or (emonth = ? and eday = ?))' , wM, wD,eM,eD)
  end
  def self.check_months(nMonth)
    where([nMonth == nil ? 'wmonth = wmonth' : '(wmonth in (:nWMonths) or emonth in (:nEMonths))' , :nWMonths => nMonth, :nEMonths => nMonth])
  end

  def self.check_ymd_or(nYs,nMs,nDs)
    where(['(wyear in (:nWYears) or eyear in (:nEYears)) or (wmonth in (:nWMonths) or emonth in (:nEMonths)) or (wday in (:nWDays) or eday in (:nEDays))' , :nWYears => nYs, :nEYears => nYs, :nWMonths => nMs, :nEMonths => nMs, :nWDays => nDs,:nEDays => nDs])
  end

    # def self.check_ymds(nYs,nMs,nDs)
  def self.check_ymd_and(nYs,nMs,nDs)
    where(['(wyear in (:nWYears) or eyear in (:nEYears)) and ((wmonth in (:nWMonths) or emonth in (:nEMonths)) and (wday in (:nWDays) or eday in (:nEDays)))' , :nWYears => nYs, :nEYears => nYs, :nWMonths => nMs, :nEMonths => nMs, :nWDays => nDs,:nEDays => nDs])
  end

  def self.check_years_months_days(nYs,nMs,nDs)
    # wY = nYs == nil || nYs == [] ? "wyear = wyear" : "wyear in (#{nYs.join(',')})"
    wY = nYs == nil || nYs == [] ? "wyear in ()" : "wyear in (#{nYs.join(',')})"
    wM = nMs == nil || nMs == [] ? "wmonth = wmonth" : "wmonth in (#{nMs.join(',')})"
    wD = nDs == nil || nDs == [] ? "wday = wday" : "wday in (#{nDs.join(',')})"
    # eY = nYs == nil || nYs == [] ? "eyear = eyear" : "eyear in (#{nYs.join(',')})"
    eY = nYs == nil || nYs == [] ? "eyear in ()" : "eyear in (#{nYs.join(',')})"
    eM = nMs == nil || nMs == [] ? "emonth = emonth" : "emonth in (#{nMs.join(',')})"
    eD = nDs == nil || nDs == [] ? "eday = eday" : "eday in (#{nDs.join(',')})"
    s = "((#{wY} or (#{wM} and #{wD})) or (#{eY} or (#{eM} and #{eD})))"
    # s = "((#{wY} and #{wM} and #{wD}) or (#{eY} and #{eM} and #{eD}))"
    where(s)
    # where('(wyear in (:nWYears) or eyear in (:nEYears)) and ((wmonth in (:nWMonths) or emonth in (:nEMonths)) and (wday in (:nWDays) or eday in (:nEDays)))' , :nWYears => nYs, :nEYears => nYs, :nWMonths => nMs, :nEMonths => nMs, :nWDays => nDs,:nEDays => nDs)
  end
  def self.make_ymd_sql(y,m,d)
    wY = y == nil ? "wyear = wyear" : "wyear = #{y}"
    wM = m == nil ? "wmonth = wmonth" : "wmonth = #{m}"
    wD = d == nil ? "wday = wday" : "wday = #{d}"

    eY = y == nil ? "eyear = eyear" : "eyear = #{y}"
    eM = m == nil ? "emonth = emonth" : "emonth = #{m}"
    eD = d == nil ? "eday = eday" : "eday = #{d}"

    a = Array.new
    b = Array.new
    if (y != nil) then
      a.push("#{wY}")
      b.push("#{eY}")
    end
    if (m != nil) then
      a.push("#{wM}")
      b.push("#{eM}")
    end
    if (d != nil) then
      a.push("#{wD}")
      b.push("#{eD}")
    end
    if (a.length == 0) then
      return ""
    end
    s1 = a.join(" and ")
    s2 = b.join(" and ")
    return "((#{s1}) or (#{s2}))"
  end
  def self.make_sp_sql(sp)
    y,m,d,h,s,n = sp[0],sp[1],sp[2],sp[3],sp[4],sp[5]

    wh = h == nil ? "(hour = hour)" : "(hour = #{h})"
    sex = s == nil ? "(sex = sex)" : "(sex = #{s})"
    name = n == nil ? "(name = name)" : "(name like '%#{n}%')"

    # s = "(((#{wY} and #{wM} and #{wD}) or (#{eY} and #{eM} and #{eD})) and #{wH} and #{sex} and #{name})"

    a = Array.new
    s1 = self.make_ymd_sql(y,m,d)
    if (s1.length != 0) then
      a.push(s1)
    end
    if (h != nil) then
      a.push("#{wh}")
    end
    if (s != nil) then
      a.push("#{sex}")
    end
    if (n != nil) then
      a.push("#{name}")
    end
    if (a.length == 0) then
      return ""
    end
    return "(#{a.join(" and ")})"
  end
  def self.check_sps(sps)
    s2 = ""
    sps.each_index do |i|
      s1 = self.make_sp_sql(sps[i])
      if (s2 == "") then
        s2 += s1
      else
        if (s1 != "") then
          s2 += " or "
          s2 += s1
        end
      end
    end
    if (s2 != "") then
      s = "(#{s2})"
      where(s)
    end
  end

  def self.check_day(nDay)
    where([nDay == nil ? 'wday = wday' : '(wday = ? or eday = ?)' , nDay,nDay])
  end

  def self.check_days(nDay)
    where([nDay == nil ? 'wday = wday' : '(wday in (:nWDays) or eday in (:nEDays))' , :nWDays => nDay,:nEDays => nDay])
  end

  def self.check_hour(nHour)
    where([nHour == nil ? 'hour = hour' : 'hour = ?' , nHour])
  end

  def self.check_name(sName)
    where([sName == nil ? 'name = name' : "name like ?" , "%" + sName + "%"])
  end

  def self.check_sex(nSex)
    where([nSex == nil ? 'sex = sex' : 'sex = ?' , nSex])
  end

  def hAll
  	if (self.all_info == nil) then
  		return Hash.new
  	else
  		return JSON.parse(self.all_info)
  	end
  	# return Cfate.pan_par_dbfield2hash(self.all_info)
  end

  def hAll=(value)
  	self.all_info = value.to_json
  	# self.all_info = Cfate.pan_par_hash2db(value)
  end

	# first_name:string last_name:string
  # Getter
  # def full_name
  #   [first_name, last_name].join(' ')
  # end

  # # Setter
  # def full_name=(name)
  #   split = name.split(' ', 2)
  #   self.first_name = split.first
  #   self.last_name = split.last
  # end
end
