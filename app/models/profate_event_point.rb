class ProfateEventPoint < ActiveRecord::Base
  def hParam
    if (self.param == nil) then
      return Hash.new
    else
      return JSON.parse(self.param)
    end
  end

  def hParam=(value)
    self.param = value.to_json
  end

  def self.need_amount()
     where(["(need_point > 0)"])
  end
  def self.check_event_name(event_name)
     where(["event_name = ?",event_name])
  end
  def self.like_event_name(event_name_like)
    where(["event_name like ?", "%" + event_name_like + "%"])
  end
end
