class Order < ActiveRecord::Base
  def self.check_userid(user_id)
    where(["user_id = ?", user_id])
  end
  def self.check_clientip(client_ip)
    where(["client_ip = ?", client_ip])
  end
  def self.check_status(status)
    where(["status = ?", status])
  end
  def self.check_servername(name)
      where(["server_name like ?" , "%" + name + "%" ])
  end
  def self.user_id_legal()
     where(["user_id is not null"])
  end
end
