class Orderme < ActiveRecord::Base
  has_many :orderme_details
  belongs_to :rents
  # attr_accessible :billing_order_id, :buy_type, :discount_amount, :old_userap_id, :original_amount, :paid_amount, :product_id, :refund_amount, :status, :user_id, :ws_dates
  def howlong
    # eval("#{self.ws_dates.split(//,2).join(".")}")
    # if (self.ws_dates == "forever") then
    #   return nil
    # else
      eval("#{self.ws_dates.split.join(".")}")
    # end
  end

  def howlong=(value)
	 self.ws_dates = "#{value}"
  end

end
