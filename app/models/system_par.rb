class SystemPar < ActiveRecord::Base
  # attr_accessible :key, :status, :value
  # default_scope { where("status = 0") }
  # SystemPar.unscoped.all
  def self.check_key(skey)
    where(["key = ?", skey])
  end
  def self.check_key1(skey)
    where(["key = ?", skey ])
  end
  def self.available()
    where(["status = ?", 0 ])
  end

  def hValue
    if (self.value == nil || self.value == "null" || self.value == "") then
      return Hash.new
    else
      return JSON.parse(self.value)
    end
  end

  def hValue=(value)
    self.value = value.to_json
  end
end
