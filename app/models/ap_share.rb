class ApShare < ActiveRecord::Base
  # attr_accessible :action_name, :ap_data, :ap_name, :count, :customer_id, :eday, :eleap, :emonth, :eyear, :hour, :minute, :pantype, :share_key, :status, :user_id, :sky_earth

  def hAll
    if (self.ap_data == nil) then
      return Hash.new
    else
      return JSON.parse(self.ap_data)
    end
    # return Cfate.pan_par_dbfield2hash(self.all_info)
  end

  def hAll=(value)
    self.ap_data = value.to_json
    # self.all_info = Cfate.pan_par_hash2db(value)
  end

  def self.check_key(key)
      where(["share_key = ?", key ])
  end
  def self.check_apname(ap_name)
      where(["ap_name = ?", ap_name ])
  end
  def self.check_actionname(action_name)
      where(["action_name = ?", action_name ])
  end
  def self.check_userid(user_id)
      where(["user_id = ?", user_id ])
  end
  def self.check_customerid(customer_id)
      where(["customer_id = ?", customer_id ])
  end
  def self.check_pantype(pantype)
      where(["pantype = ?", pantype ])
  end
  def self.check_eyear(eyear)
      where(["eyear = ?", eyear ])
  end
  def self.check_emonth(eleap,emonth)
      where(["eleap = ? and emonth = ?", eleap == true ? 1 : 0 ,emonth ])
  end
  def self.check_eday(eday)
      where(["eday = ?", eday ])
  end
  def self.check_hour(hour)
      where(["hour = ?", hour ])
  end
  def self.check_minute(minute)
      where(["minute = ?", minute ])
  end
  def self.check_sky_earth(sky_earth)
      where(["sky_earth = ?", sky_earth ])
  end
  def self.check_ap_data(ap_data)
      where(["ap_data = ?", ap_data ])
      # where(["ap_data like ?",  "%" + ap_data + "%" ])
  end

end
