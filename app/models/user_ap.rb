class UserAp < ActiveRecord::Base
  has_one :products

  # default_scope { where(["(DATE_ADD(end_date,INTERVAL gift_days DAY) >= ? or end_date is null) and status = 0", Xdate.GetNowRails() ]) }
  # 不要設default_scope，因為有時要全部判斷

  def self.current()
    # for sqlite
        # where(["(start_date <= ? or start_date is null) and status = 0", Xdate.GetNowRails() ])
        # For MySql
        # where(["(start_date <= ? or start_date is null) and (DATE_ADD(end_date,INTERVAL gift_days DAY) >= ? or end_date is null) and status = 0", Xdate.GetNowRails(),Xdate.GetNowRails() ])
        # For PostGres
        where(["(start_date <= ? or start_date is null) and (date_part('day', ? - end_date) <= gift_days or end_date is null) and status = 0", Xdate.GetNowRails(),Xdate.GetNowRails() ])
  end
  def self.available()
    # for sqlite
     # where(["(end_date is null or end_date >= ?) and status = 0", Xdate.GetNowRails() ])
      # For MySql
     # where(["(end_date is null or DATE_ADD(end_date,INTERVAL gift_days DAY) >= ?) and status = 0", Xdate.GetNowRails() ])
      # For PostGres
     where(["(end_date is null or date_part('day', ? - end_date) <= gift_days) and status = 0", Xdate.GetNowRails() ])
  end
  def self.not_available()
     where(["(date_part('day', ? - end_date) > gift_days) or status <> 0", Xdate.GetNowRails() ])
  end

  def self.date_legal()
     where(["start_date is not null and end_date is not null"])
  end

  def self.check_outofdate()
    # for sqlite
     # where(["(end_date < ?) and status = 0", Xdate.GetNowRails() ])
      # For MySql
     # where(["(DATE_ADD(end_date,INTERVAL gift_days DAY) < ?) and status = 0", Xdate.GetNowRails() ])
      # For PostGres
     where(["(date_part('day', ? - end_date) > gift_days) and status = 0", Xdate.GetNowRails() ])
  end

  def self.check_free()
     where(["product_type = ?","free"])
  end
  def self.check_demo()
     where(["product_type = ?","demo"])
  end
  def self.check_main()
     # where(["product_type != ?","demo"])
     where(["product_type = ?","main"])
  end
  # 3650 near 10 years
  def self.check_forever()
     # where(["(start_date is not null and end_date is not null) and (DATEDIFF(end_date,start_date) >= 3650) and status = 0"])
     where(["product_type = ?","forever"])
  end
  def self.check_not_forever()
     where(["product_type != ?","forever"])
  end

  def self.check_userid(user_id)
    where(["user_id = ?", user_id])
  end
  def self.check_product_name(ap_name)
    where(["product_name like ?", "%" + ap_name + "%"])
  end
  def self.check_product_version(version)
    where(["product_version = ?", version])
  end
  def self.check_rentid(rent_id)
    where(["rent_id = ?", rent_id])
  end
  def self.check_product_type(product_type)
     where(["product_type = ?",product_type])
  end

  def self.check_hallnumber()
    where("ap_additionalfunc like ?", "%" + "b_HallNumber" + "%")
  end
  def has_hallnumber
    return (self.ap_additionalfunc.indexOf("b_HallNumber") != -1)
  end

  def self.great_than(id)
    where("id > ?", id)
  end
  def self.less_than(id)
    where("id <= ?", id)
  end

  def free
    return (self.product_type == "free")
  end
  def demo
    return (self.product_type == "demo")
  end
  def show
    return (self.product_type == "show")
  end
  def guest
    return (self.product_type == "guest")
  end

  def can_forever
    return (free || demo)
  end

  def forever
    return (self.product_type == "forever")
  end
  def main
    # return (!free)
    return (self.product_type == "main")
  end
  def additional
    return (self.product_type == "additional")
  end
  def star
    return (self.product_name == "star")
  end
  def eightword
    return (self.product_name == "eightword")
  end
  def wannianli
    return (self.product_name == "wannianli")
  end

  def isAvailable
     return ((self.end_date is null or (self.end_date + self.gift_days.days >= Xdate.GetNowRails())) and self.status = 0)
  end

  def can_use_pars
    return (demo || show || forever || main)
  end

  def self.check_productid(product_id)
    where(["product_id = ?", product_id ])
  end

  def canBuyAdditional
     return ((self.end_date - Xdate.GetNowRails()) >= 10.days)
  end

  def custDisplayCheck
    # return (self.product_type != "free")
    return (self.product_type == "main" || self.product_type == "free" || self.product_type == "demo")
  end

  def firstShowAdvertise
    # return (self.product_type == "free")
    return !(self.product_type == "main" || self.product_type == "demo")
  end

  def can_zeri
    return (demo || main)
  end
  def can_chucunkerhu
    return (star || eightword)
  end

end
