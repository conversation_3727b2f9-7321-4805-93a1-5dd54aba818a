class ProfatePointProduct < ActiveRecord::Base
  default_scope { where("(start_date <= ? or start_date is null) and (end_date >= ? or end_date is null) and status = 0", Xdate.GetNowRails(),Xdate.GetNowRails()) }

  def self.available()
    # for sqlite
     # where(["(end_date is null or end_date >= ?) and status = 0", Xdate.GetNowRails() ])
      # For MySql
     # where(["(end_date is null or DATE_ADD(end_date,INTERVAL gift_days DAY) >= ?) and status = 0", Xdate.GetNowRails() ])
      # For PostGres
     where(["(end_date is null or end_date > ?) and status = 0", Xdate.GetNowRails() ])
  end
  def self.check_type(type)
      where(["p_type = ?", type ])
  end

  def self.check_amount(amount)
      where(["amount = ?", amount ])
  end
end
