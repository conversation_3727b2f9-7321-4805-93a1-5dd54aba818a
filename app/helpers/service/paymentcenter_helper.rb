module Service::PaymentcenterHelper
  @@NONCREDIT_PT = ["ATM","WEBATM","CS","MMK","ALIPAY"]
  @@CREDIT_PT = ["CREDIT"]
  def isNonCredit?(paymenttype)
    if (paymenttype == nil) then 
      return true
    end
    if (@@NONCREDIT_PT.index(paymenttype) == nil) then
      return false
    end
    return true
  end
  def isCredit?(paymenttype)
    if (@@CREDIT_PT.index(paymenttype) == nil) then
      return false
    end
    return true
  end
end
