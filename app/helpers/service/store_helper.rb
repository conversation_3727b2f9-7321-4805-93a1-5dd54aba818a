module Service::StoreHelper
  def getFinalBuyType(products,buyType)
    if (products != nil) then
      return buyType
    else
      return "BUY_WEB_ADD"
    end
  end

  def needAdditional(product_version,apFunc,buy_type)
    if (buy_type == "BUY") then
      return true
    end
    if (buy_type == "REBUY") then
      return true
    end
    if (apFunc[product_version] == nil) then
      return true
    end
    return !checkTrueFalse(apFunc[product_version])
  end

  def checkTrueFalse(value)
    if (value == 0) then
      return false
    end
    if (value == "0") then
      return false
    end
    if (value == false) then
      return false
    end
    if (value == "false") then
      return false
    end
    return true
  end

  def checkProductSelected(product_version,ap_version,buy_type,index)
    case (buy_type)
      when ("FOREVER") then
        return true
      when ("BUY") then
        if (index == 0) then
          return true
        end
        return false
      when ("REBUY") then
        if (ap_version == product_version) then
          return true
        end
        return false
      when ("BUY_WEB_ADD") then
        return false
      else
        return false
    end
  end

  def checkProductAdditionalSelected(product_id,aBuiedProductId,buy_type)
    case (buy_type)
      when ("BUY") then
        return false
      when ("REBUY") then
        if (aBuiedProductId.index(product_id) != nil) then
          return true
        end
        return false
      when ("BUY_WEB_ADD") then
        return false
      else
        return false
    end
  end

  def needBuiedProductList(buy_type)
    if (buy_type == "REBUY") then
      return true
    end
    if (buy_type == "BUY_WEB_ADD") then
      return true
    end
    return true
  end

  def getBuyTypeStr(buy_type)
    return Pm.GetStr("IDS_S_APP_#{buy_type}")
  end

  def getBuyTypeDescUrl(buy_type)
    return Pm.getSvUrl("#{buy_type}_DESC_URL")
  end

  def getBuyTypeInfo(buy_type)
    buy_type_str = getBuyTypeStr(buy_type)
    buy_type_url = getBuyTypeDescUrl(buy_type)
    return link_to(buy_type_str, buy_type_url, :target => "_blank")
  end

  def needProducts(buy_type,products)
    if (products == nil) then
      return false
    end
    if (buy_type == "BUY_WEB_ADD") then
      return false
    end
    return true
  end


  def needProductAdditionals(buy_type,product_additional)
    if (product_additional == nil) then
      return false
    end
    if (product_additional.length == 0) then
      return false
    end
    return true
  end

  def needDatesAndDiscount(buy_type,products)
    if (products == nil) then
      return false
    end
    if (buy_type == "UPGRADE") then
      return false
    end
    if (buy_type == "BUY_WEB_ADD") then
      return false
    end
    return true
  end

  def needApName()
    return false
  end

  def makeDatesAndDiscount(sDatesDiscount)
    sOut = ""
    hContent = Cfate.pan_par_dbfield2hash(sDatesDiscount)
    hContent.each_pair { |key,value|
      sOut += radio_button_tag("DatesDiscount", "#{key}",  false, :style => "vertical-align: left")
      sOut += makeDiscountText(key,value.to_i)
      sOut += "<BR/>"
    }
    return sOut
  end
  def makeDateAndDiscountText(key,discount)
    return "#{makeRentDates(key)}:#{discount}%"
  end
  def makeDiscountText(discount)
    return "#{100-discount.to_i}%"
  end

  def makeRentDates(key)
    # a = key.split(/\s*.\s*/)
    # a2 = a[1].split(//,2)
    # a = key.split(//,2)
    a = key.split
    s = a.join(".")
    # s = "#{a[0]}.#{a[1]}"
      oProduct = Product.select(:product_content).check_forever.last
      if (oProduct == nil) then
      sOut = "#{a[0]}#{getDateStr(a[1])}"
      else
      sf = oProduct.product_content.split.join(".")
      if (eval(s) >= eval(sf)) then
        sOut = Pm.GetStr("IDS_S_APP_FOREVER")
      else
        sOut = "#{a[0]}#{getDateStr(a[1])}"
      end
    end
    return sOut
  end
  def makeRentDates2(key)
    a = key.split(/\s*_\s*/)
    a2 = a[1].split(//,2)
    sOut = "#{a2[0]}#{getDateStr(a2[1])}"
    return sOut
  end

  def getDateStr(sDate)
    if (sDate.index("year") != nil) then
      return Pm.GetStr("IDS_X_YEAR")
    elsif (sDate.index("month") != nil) then
      return Pm.GetStr("IDS_S_MONTH_UNIT") + Pm.GetStr("IDS_X_MONTH")
    elsif (sDate.index("day") != nil) then
      return Pm.GetStr("IDS_S_DAY")
    end
    return ""
  end
  def getOnlyDateStr(sDate)
    if (sDate.index("year") != nil) then
      return Pm.GetStr("IDS_X_YEAR")
    elsif (sDate.index("month") != nil) then
      return Pm.GetStr("IDS_X_MONTH")
    elsif (sDate.index("day") != nil) then
      return Pm.GetStr("IDS_S_DAY")
    end
    return ""
  end

  def ap_title(ap_name)
    return Pm.GetStr("IDS_APNAME_#{ap_name.upcase}")
  end

  def free_ap_select(hAps,sAp)
    select_tag("ap_name", options_for_select(hAps,sAp), :multiple => false,:include_blank => false, :style =>"width:auto;height:auto", :onchange => "OnApSelected(this);")
  end

  def end_date_check(end_date,int_ws_dates)
    if (end_date == nil) then
      end_date = Xdate.GetNowRails() + int_ws_dates
    else
      end_date = end_date + int_ws_dates
    end
    return end_date.strftime("%Y-%m-%d")
  end
  def check_product_rent(product,aBuied)
    aBuied.each do |hBuied|
      if (product.id == hBuied["product_id"]) then
        return hBuied
      end
      if (hBuied["additionalfunc"][product.product_version] != nil) then
        return hBuied
      end
    end
    return nil
  end
end
