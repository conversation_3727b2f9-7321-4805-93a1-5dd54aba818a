module ApplicationHelper
  EMPTY_STRING = ''.freeze

  def notice_message
    flash_messages = []

    flash.each do |type, message|
      type = 'success' if type.to_sym == :notice
      type = 'error'   if type.to_sym == :alert
      text = "<script>toastr.#{type}('#{message}');</script>"
      flash_messages << text.html_safe if message
    end

    flash_messages.join("\n").html_safe
  end

  def admin?(user = nil)
    user ||= current_user
    user.try(:admin?)
  end

  def wiki_editor?(user = nil)
    user ||= current_user
    user.try(:wiki_editor?)
  end

  def owner?(item)
    return false if item.blank? || current_user.blank?
    if item.is_a?(User)
      item.id == current_user.id
    else
      item.user_id == current_user.id
    end
  end

  def title_tag(str)
    content_for :title, raw("#{str} · #{Setting.app_name}")
  end

  MOBILE_USER_AGENTS = 'palm|blackberry|nokia|phone|midp|mobi|symbian|chtml|ericsson|minimo|' \
                       'audiovox|motorola|samsung|telit|upg1|windows ce|ucweb|astel|plucker|' \
                       'x320|x240|j2me|sgh|portable|sprint|docomo|kddi|softbank|android|mmp|' \
                       'pdxgw|netfront|xiino|vodafone|portalmmm|sagem|mot-|sie-|ipod|up\\.b|' \
                       'webos|amoi|novarra|cdm|alcatel|pocket|iphone|mobileexplorer|mobile'
  def mobile?
    agent_str = request.user_agent.to_s.downcase
    return false if agent_str =~ /ipad/
    agent_str =~ Regexp.new(MOBILE_USER_AGENTS)
  end

  # Override cache helper for support multiple I18n locale
  def cache(name = {}, options = {}, &block)
    options ||= {}
    super([I18n.locale, name], options, &block)
  end

  def render_list(opts = {})
    list = []
    yield(list)
    items = []
    list.each do |link|
      item_class = EMPTY_STRING
      urls = link.match(/href=(["'])(.*?)(\1)/) || []
      url = urls.length > 2 ? urls[2] : nil
      if url && current_page?(url) || (@current && @current.include?(url))
        item_class = 'active'
      end
      items << content_tag('li', raw(link), class: item_class)
    end
    content_tag('ul', raw(items.join(EMPTY_STRING)), opts)
  end

  # def highlight(text)
  #   text = escape_once(text)
  #   text.gsub!('[h]', '<em>')
  #   text.gsub!('[/h]', '</em>')
  #   text.gsub!(/\\n|\\r/, '')
  #   raw text
  # end

  # from meen.tw
  def g_ap_namespace(ap_name)
    if (["star","eightword","wannianli","liuyaogua"].include?(ap_name)) then
      return "ifate"
    else
      if (["xuexi_ziwei","xuexi_ziwei_lunduan","xuexi_ziweipan","xuexi_ziweipars"].include?(ap_name)) then
        return "xuexi"
      end
    end
    return "ifate"
  end
end
