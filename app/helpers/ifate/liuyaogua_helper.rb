module Ifate::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def get_explain(result)
    return "" if result == {}
    explain = result["pugua_pan"]["explain"]
    return "" if explain == nil
    biao_tou = explain["biao_tou"]
    jie_guo = explain["jie_guo"]
    s = ""
    (1..15).each do |i|
      k = "xiang_mu_#{i}"
      s1 = biao_tou[k]

      if s1 != nil then
        if jie_guo[k] != "" then
          s += Pm.GetStrWithQuote3(s1)
          s += "\n"
          s += jie_guo[k]
          s += "\n\n"
        end
      end
    end
    return s
  end
  def get_explain2(result)
    explain = result["pugua_pan"]["explain"]
    biao_tou = explain["biao_tou"]
    jie_guo = explain["jie_guo"]
    s = ""
    (1..15).each do |i|
      k = "xiang_mu_#{i}"
      s1 = biao_tou[k]

      if s1 != nil then
        if jie_guo[k] != "" then
          s += Pm.GetStrWithQuote3(s1)
          s += jie_guo[k]
        end
      end
    end
    s.gsub!("\n","")
    return s
  end
  def qigua_check(wai,yao_1,qiguafanxi)
    return Liuyaogua::PAR_QIGUAFANXI_NO if wai != nil && wai.to_i > 0
    return Liuyaogua::PAR_QIGUAFANXI_YAO if yao_1 != nil && yao_1.to_i > 0
    return qiguafanxi
  end
  def displayCheck(sVisible)
    return "display:none;" if sVisible == "hidden"
    return "display:block;"
  end
  def get_question(h_pe_desc)
    gua_bie_desc = h_pe_desc["question"]
    if (gua_bie_desc == nil) then
      gua_bie = h_pe_desc["gua_bie"]
      gua_bie_desc = Divination.get_gua_bie_desc(gua_bie)
    end
    return gua_bie_desc
  end
  def makeLiuyaoguaDateStr_brief(personal_event)
    h_pe_desc = personal_event.h_pe_desc
    sDate = makeLiuyaoguaDateStr(h_pe_desc)
    gua_bie_desc = get_question(h_pe_desc)
    result = h_pe_desc["result"]
    a = result["pugua_pan"]["pan"]["data"]["ben_gua_info"]

    sData = "#{gua_bie_desc},#{sDate},#{a[0]}"
    return sData
  end
  def makeLiuyaoguaDateStr(h_pe_desc)
    sCal = "#{h_pe_desc["year"]}/#{h_pe_desc["month"]}/#{h_pe_desc["day"]}"

    return sCal
  end
  def Liuyaogua_Par_Parameter?(nPanPar)
    return Liuyaogua::PanPar_Type == nPanPar
  end
  def Liuyaogua_Par_HallNumber?(nPanPar)
    return Liuyaogua::PanPar_HallNumber == nPanPar
  end
  def Liuyaogua_Par_Print?(nPanPar)
    return (Liuyaogua::PanPar_Print == nPanPar)
  end

  def liuyaogua_pan_str(s)
    return Divination.liuyaogua_pan_str(s)
  end
  def ly_font_size()
    return 16
  end
  def ly_large_font_size()
    return 25
  end
  def ly_big_font_size()
    return 20
  end  
  def ly_small_font_size()
    return 12
  end
  def ly_x()
    # return 5
    return 0
  end
  def ly_y()
    return 5
  end
  def ly_width()
    return ly_param_tt_width(1) + ly_main_tt_width() + ly_data_tt_width() + 22 # 22 border line
  end
  def ly_height()
  	return ly_param_tt_height() + 10 + 50
  end
  # 參考資訊 旺相休囚死
  def ly_param_tt_width(j)
    return ly_param_width(j) * 6
  end
  def ly_param_width(j)
    # return 42 if j <=5
    return 36 if j <=5
    return ly_param_width(1) * 2 / 3
  end
  def ly_param_height_1()
    # return ly_param_width(1) / 2 * 3 - 6
  	return ly_param_width(1) / 2 * 3 - 6 + 30
  end
  def ly_param_height(j)
  	a1 = ly_param_height_1()
  	a2 = ly_param_width(1)
  	a = [a1,a2,a1,a1,a1,a1,a2 / 2 * 5,a1,a2 * 4]
    return a[j - 1]
  end
  def ly_param_tt_height(j = 9)
  	y = 0
  	(1..j).each do |i|
  		y += ly_param_height(i)
  	end
    return y
  end
  def ly_param_x(i,j)
  	x = ly_x() + ly_param_width(j) * (i - 1)
    return x
  end
  def ly_param_y(j)
    y = ly_y() + ly_param_tt_height(j - 1)
  end
  def ly_param_desc_key(i,j)
  	return Divination.liuyaogua_pan_str_key("param_#{i}_#{j}")
  end
  def ly_param_desc_exist(i,j)
  	key = ly_param_desc_key(i,j)
  	return I18n.exists?(key)
  end
  def ly_param_desc(i,j)
  	key = ly_param_desc_key(i,j)
  	return "" if !I18n.exists?(key)
  	return Pm.t(key)
  end
  def ly_param_text_x(i,j)
  	x = ly_param_x(i,j) + ly_param_width(j) / 2
  	return x
  end
  def ly_param_text_y(j)
  	return ly_param_y(j) + ly_font_size() / 2 if j == 7
  	return ly_param_y(j) + ly_font_size() / 2 if j == 9
  	y = ly_param_y(j) + ly_param_height(j) / 2
  	return y
  end
  def ly_param_text_height(j)
  	return ly_param_height(j) / 3 * 2
  end

  # main pan 六爻卦主盤
  def ly_main_width(i)
  	return 0 if i == 0
  	return ly_param_width(1) * 2 if i == 9
    return ly_param_width(1) / 2 * 3
  end
  def ly_main_tt_width(i=9)
    # x = 10 # 各block之間距
    x = 5 # 各block之間距
  	return x if i <= 0
  	(1..i).each do |k|
    	x += ly_main_width(k)
    end
    return x
  end
  def ly_main_height(j)
  	return ly_param_height(1) if j == 1
  	y = (ly_param_tt_height() - ly_main_height(1)) / 6
    return y
  end
  def ly_main_tt_height(j = 7)
  	y = 0
  	(1..j).each do |i|
  		y += ly_main_height(i)
  	end
    return y
  end
  def ly_main_x(i)
  	x = ly_param_x(1,1) + ly_param_tt_width(1) + ly_main_tt_width(i - 1)
    return x
  end
  def ly_main_y(j)
    y = ly_y() + ly_main_tt_height(j - 1)
  end
  def ly_main_desc(i)
  	return liuyaogua_pan_str("main_#{i}")
  end
  def ly_main_text_x(i,j)
  	return ly_main_x(i) + ly_small_font_size() / 2 if i == 8 && j > 1
    # return ly_main_x(i) + ly_small_font_size() if i == 9 && j > 1
  	return ly_main_x(i) + ly_small_font_size() / 2 if i == 9 && j > 1
  	x = ly_main_x(i) + ly_main_width(j) / 2
  	return x
  end
  def ly_main_text_y(i,j)
  	return ly_main_y(j) + ly_font_size() * 2 if i == 8 && j != 1
  	y = ly_main_y(j) + ly_main_height(j) / 2
  	return y
  end
  def ly_main_text_height(j)
  	return ly_main_height(j) / 3 * 2
  end

  # 個人及卦資訊
  def ly_data_width(i,j)
  	return 0 if i == 0
  	return ly_param_width(1) / 3 * 4 if i == 3 && j == 1
  	return 0 if i == 4 && j == 1
    return ly_param_width(1) / 3 * 2 if j == 2
    return ly_data_width(2,2) * 7 if i == 1 && j == 3
    # return ly_data_width(2,2) * 4 if i == 2 && j == 3
    return ly_data_width(2,2)
  end
  def ly_data_tt_width(i=7,j=1)
  	x = 0
  	(1..i).each do |k|
    	x += ly_data_width(k,j)
    end
    return x
  end
  def ly_data_height(j)
    return ly_param_height(1) if j == 1
  	return ly_main_height(2) * 5 if j == 2
  	y = (ly_param_tt_height() - ly_data_height(1) - ly_data_height(2))
    return y
  end
  def ly_data_tt_height(j = 3)
  	y = 0
  	(1..j).each do |i_j|
  		y += ly_data_height(i_j)
  	end
    return y
  end
  def ly_data_right()
    return ly_data_x(1,1) + ly_data_tt_width()
  end
  def ly_data_x(i,j)
  	x = ly_main_x(1) + ly_main_tt_width() + ly_data_tt_width(i - 1,j)
    return x
  end
  def ly_data_y(j)
    y = ly_y() + ly_data_tt_height(j - 1)
  end
  def ly_data_desc(i)
  	return liuyaogua_pan_str("data_#{i}")
  end
  def ly_data_text_x(i,j)
  	x = ly_data_x(i,j) + ly_data_width(i,j) / 2
  	return x
  end
  def ly_data_text_y(j)
  	return ly_data_y(j) + ly_small_font_size() if j == 2
    return ly_data_y(j) + ly_data_height(j) / 2 + ly_small_font_size() / 4 if j == 3
  	y = ly_data_y(j) + ly_data_height(j) / 2
  	return y
  end
  def ly_data_text_height(j)
  	return ly_data_height(j) / 3 * 2
  end

  # get liuyaogua data
  def ly_5_choose_1(a,i)
  	return a[5-i]
  end
  def ly_a_to_dot_sep(h)
    a = []
    (0...h["jieguo_count"]).each do |i|
    	a.push(h["jieguo_#{i + 1}"].join(""))
    end
    return a.join(Pm.GetStr("IDS_E_CHI_DOT"))
  end

  def ly_param_data(result,i,j)
    return "" if result == {}
  	return ly_5_choose_1(result["pugua_pan"]["pan"]["parameter"]["wang_xiang_xiu_qiu_si"],i) if j == 2 and i <= 5
  	return result["pugua_pan"]["pan"]["parameter"]["yue_ling"] if j == 2 and i == 6
  	return ly_5_choose_1(result["pugua_pan"]["pan"]["parameter"]["liu_qin"],i) if j == 3 and i <= 5
  	return ly_5_choose_1(result["pugua_pan"]["pan"]["parameter"]["shi"],i) + ly_5_choose_1(result["pugua_pan"]["pan"]["parameter"]["yin"],i) if j == 4 and i <= 5
  	# 日辰
  	return ly_5_choose_1(result["pugua_pan"]["pan"]["parameter"]["chang_sheng"],i) if j == 5 and i <= 5

  	# 卦身
    gua_shen = result["pugua_pan"]["pan"]["parameter"]["gua_shen"]
  	return gua_shen.join(" ") if j == 7 and i == 1

  	# 劫煞
  	return result["pugua_pan"]["pan"]["parameter"]["jie_sha"] if j == 7 and i == 2

  	# 桃花
  	return result["pugua_pan"]["pan"]["parameter"]["tao_hua"] if j == 7 and i == 3
  	# 天喜
  	return result["pugua_pan"]["pan"]["parameter"]["tian_xi"] if j == 7 and i == 4
  	# 驛馬
  	return result["pugua_pan"]["pan"]["parameter"]["yi_ma"] if j == 7 and i == 5
  	# 干祿
  	return result["pugua_pan"]["pan"]["parameter"]["gan_lu"] if j == 7 and i == 6
  	# 羊刃
  	return result["pugua_pan"]["pan"]["parameter"]["yang_ren"] if j == 7 and i == 7
  	# 貴人
  	return result["pugua_pan"]["pan"]["parameter"]["gui_ren"].join if j == 7 and i == 8
  	# 空亡
  	return result["pugua_pan"]["pan"]["parameter"]["kong_wang"].join if j == 7 and i == 9

  	# 日合
  	return result["pugua_pan"]["pan"]["parameter"]["ri_he"] if j == 9 and i == 1

  	# 月合
  	return result["pugua_pan"]["pan"]["parameter"]["yue_he"] if j == 9 and i == 2

  	# 年合
  	return result["pugua_pan"]["pan"]["parameter"]["nian_he"] if j == 9 and i == 3
  	# 日沖
  	return result["pugua_pan"]["pan"]["parameter"]["ri_chong"] if j == 9 and i == 4
  	# 月沖
  	return result["pugua_pan"]["pan"]["parameter"]["yue_chong"] if j == 9 and i == 5
  	# 年沖
  	return result["pugua_pan"]["pan"]["parameter"]["nian_chong"] if j == 9 and i == 6
  	# 互刑
  	hu_xing = result["pugua_pan"]["pan"]["parameter"]["hu_xing"]
  	return ly_a_to_dot_sep(hu_xing) if j == 9 and i == 7
  	# 三刑
  	san_xing = result["pugua_pan"]["pan"]["parameter"]["san_xing"]
  	return ly_a_to_dot_sep(san_xing) if j == 9 and i == 8
  	# 三合
  	san_he = result["pugua_pan"]["pan"]["parameter"]["san_he"]
  	return ly_a_to_dot_sep(san_he) if j == 9 and i == 9

  	return "a"
  end
  def ly_param_text_font_size(result,i,j)
  	s = ly_param_data(result,i,j)
    return ly_small_font_size() if s.length > 5
  	return ly_small_font_size() - 2 if s.length > 7
  	return ly_font_size()
  end

  # main
  def ly_main_writing_mode(i,j)
  	# lr-tb | rl-tb | tb-rl | lr | rl | tb | inherit
  	return "lr" if [1,2,3,4,7,9].include?(i)
  	return "tb"
  end
  def ly_6_choose_1(a,j)
  	# main j start from 7 to 2 for main data
  	return a[j - 2]
  end
  def ly_main_data(result,i,j)
    if result == {} then
      return ["","",""] if i == 5
      return ["","",""] if i == 6
      return ["","",""] if i == 8
      return ["",""] if i == 9
      return "" 
    end
  	# 六獸
  	return ly_6_choose_1(result["pugua_pan"]["pan"]["main_gua"]["liu_shou"],j) if i == 1
  	return ly_6_choose_1(result["pugua_pan"]["pan"]["main_gua"]["ben_gua_liu_qin"],j) if i == 2
  	if i == 3 then
  		a = ly_6_choose_1(result["pugua_pan"]["pan"]["main_gua"]["shi_yin"],j)
  		return a[0] if a != nil && a != [] && a != ""
  		return ""
  	end
  	return ly_6_choose_1(result["pugua_pan"]["pan"]["main_gua"]["liu_yao"],j) if i == 4

  	return result["pugua_pan"]["pan"]["main_gua"]["ben_gua_gan_zhi"]["yao_#{8 - j}"] if i == 5
  	if i == 6 then
  		a = result["pugua_pan"]["pan"]["main_gua"]["bian_gua_gan_zhi"]["yao_#{8 - j}"]
  		return a if a != "" && a != []
  		return ["","",""]
  	end
  	return ly_6_choose_1(result["pugua_pan"]["pan"]["main_gua"]["bian_gua_liu_qin"],j) if i == 7

  	if i == 8 then
		# http://127.0.0.1:3000/ifate/liuyaogua/pan_liuyaogua?locale=zh-TW&wai=3&nei=23&bian=0
		# 有伏神
  		# ["官鬼", "己", "亥", "水"] sample
  		a = ly_6_choose_1(result["pugua_pan"]["pan"]["main_gua"]["fu_shen"],j)
  		return ["","",""] if a == nil || a == [] || a == ""
  		b = ["","",""]
  		b[0] = a[0]
  		b[1] = a[1] + a[2]
  		b[2] = a[3]
  		return b
  	end
  	if i == 9 then
  		a = ly_6_choose_1(result["pugua_pan"]["pan"]["main_gua"]["bian_dong_jie_guo"],j).clone
  		return ["",""] if a == nil || a == [] || a == ""
  		return a.push("") if a.length == 1
  		return a[0] = "" if a[0] == nil
  		return a[1] = "" if a[1] == nil
  		return a 
  	end

  	return "a"
  end
  def ly_main_data_length(result,i,j)
  	r = ly_main_data(result,i,j)
  	return 0 if r == nil || r == "" || r == []
  	return r[0].length * ly_big_font_size() if i == 3
  	return (r[0] + r[1]).length * ly_big_font_size() * 3 / 2 if i == 5
  	return (r[0] + r[1]).length * ly_big_font_size() * 3 / 2 if i == 6
    return ly_large_font_size() if i == 4
  	return r.length * ly_big_font_size()
  end 

  # data data
  def ly_data_data(result,i,j)
    return "" if result == {}
  	if i == 1 then
  		# "bian_gua_info"=>["火地晉", "乾", "７", "金", "遊魂卦", ""]
  		a = result["pugua_pan"]["pan"]["data"]["bian_gua_info"]
  		b = [a[0],a[1..3].join,a[4],a[5]]
  		return b.join(" ")
  	end
  	if i == 2 then
  		# ben_gua_info"=>["火山旅", "離", "２", "火", "", "六合卦"]
  		a = result["pugua_pan"]["pan"]["data"]["ben_gua_info"]
  		b = [a[0],a[1..3].join,a[4],a[5]]
  		return b.join(" ")
  	end

  	if i == 3 then
  		# ganzhi_liuqin"=>["子孫", "兄弟", "子孫"]
  		a = result["pugua_pan"]["pan"]["data"]["ganzhi_liuqin"].clone
  		# a = []
  		a.push("#{liuyaogua_pan_str("zam")}") if a[0] != ""
  		return a.join(Pm.t("IDS_A_KEY_SPACE"))
  	end

  	if i == 4 then
  		# date_ganzhi"=>["辛丑", "甲午", "丙戌", "丁酉"]
  		a = result["pugua_pan"]["pan"]["data"]["date_ganzhi"].clone
  		return "#{a[0]}#{liuyaogua_pan_str("nian")}#{a[1]}#{liuyaogua_pan_str("yue")}#{a[2]}#{liuyaogua_pan_str("ri")}#{liuyaogua_pan_str("zam")}"
  	end

  	if i == 5 then
  		# "westen_date"=>["１１０", "６", "７", "１８"]
  		a = result["pugua_pan"]["pan"]["data"]["westen_date"].clone
  		return "#{a[0]}#{liuyaogua_pan_str("nian")}#{a[1]}#{liuyaogua_pan_str("yue")}#{a[2]}#{liuyaogua_pan_str("ri")}"
  	end

  	if i == 6 then
  		# "question"=>"健康"
  		question = result["pugua_pan"]["pan"]["data"]["question"].clone
      # return "#{liuyaogua_pan_str("name")}－#{question}"
  		return "#{question}"
  	end

  	return result["pugua_pan"]["pan"]["data"]["name"] if i == 7
  	return "a"
  end
  def ly_data_data_length(result,i,j)
  	r = ly_data_data(result,i,j)
  	return 0 if r == nil || r == "" || r == []
  	return r.length * ly_font_size() + ly_small_font_size() / 2
  end 
  def ly_data_text_x(i,j)
    return ly_data_x(i,j) + ly_small_font_size() if j == 3
  	x = ly_data_x(i,j) + ly_data_width(i,j) / 2
  	return x
  end
  def ly_data_text_height(j)
  	return ly_data_height(j) / 3 * 2
  end
end
