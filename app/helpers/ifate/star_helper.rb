module Ifate::<PERSON><PERSON><PERSON><PERSON>
  def Par_Header(nPanPar)
    if (Par_Parameter?(nPanPar)) then
      sId = "IDS_CAPTION_PAN_PAR_SET"
    elsif (Par_Interface?(nPanPar)) then
      sId = "IDS_CAPTION_PAN_INTERFACE_SET"
    elsif (Par_Display?(nPanPar)) then
      sId = "IDS_CAPTION_PAN_STAR_DISPLAY_SET"
    elsif (Par_PrivateFourHua?(nPanPar)) then
      sId = "IDS_CAPTION_PAN_STAR_PRIVATEFOURHUA_SET"
    elsif (Par_HallNumber?(nPanPar)) then
      sId = "IDS_S_SET_HALL_NUMBER"
    elsif (Par_Print?(nPanPar)) then
      sId = "IDS_PRINT_SET"
    elsif (Par_Copy?(nPanPar)) then
      sId = "IDS_PAR_SETUP_COPY"
    end
    return Pm.GetStrWithColon(sId)
  end
  def Par_Str(nPanPar)
    sId = Par_yml_key(nPanPar)

    return Pm.t(sId)
  end
  def Par_StrWithQuote(nPanPar)
    sId = Par_yml_key(nPanPar)

    return Pm.GetStrWithQuote(sId)
  end
  def Par_yml_key(nPanPar)
    if (Par_Parameter?(nPanPar)) then
      sId = "ifate_star_setup_pan_base"
    elsif (Par_Interface?(nPanPar)) then
      sId = "ifate_star_setup_pan_interface"
    elsif (Par_Display?(nPanPar)) then
      sId = "ifate_star_setup_pan_display"
    elsif (Par_PrivateFourHua?(nPanPar)) then
      sId = "ifate_star_setup_pan_privatefourhua"
    elsif (Par_HallNumber?(nPanPar)) then
      sId = "ifate_star_set_hall_number"
    elsif (Par_Print?(nPanPar)) then
      sId = "ifate_star_print_set"
    elsif (Par_Copy?(nPanPar)) then
      sId = "ifate_star_setup_copy"
    end
    return sId
  end

  def Par_Copy?(nPanPar)
    return Star::PanPar_Copy == nPanPar
  end
  def Par_Parameter?(nPanPar)
    return Star::PanPar_Type == nPanPar
  end
  def Par_Interface?(nPanPar)
    return Star::PanPar_Interface == nPanPar
  end
  def Par_Display?(nPanPar)
    return Star::PanPar_Display == nPanPar
  end
  def Par_PrivateFourHua?(nPanPar)
    return Star::PanPar_PrivateFourHua == nPanPar
  end
  def Par_HallNumber?(nPanPar)
    return Star::PanPar_HallNumber == nPanPar
  end
  def Par_Print?(nPanPar)
    return (Star::PanPar_Print == nPanPar)
  end
  def Par_Print_Visible(nPanPar)
    return Par_Print?(nPanPar) ? "visible" : "hidden"
  end

  def ShowSelfFourHua(bFuncShow,bOptionShow)
    return bFuncShow && bOptionShow
  end

  def CheckSmallRevert(bSmallYearRevertDisplay,nSmall,nAStarSelfFourHuaCount,nLarge)
    if (!bSmallYearRevertDisplay) then
      return false
    end
    if (nAStarSelfFourHuaCount == 0) then
      return false
    end
    if (nSmall < nLarge) then
      return false
    end
    if (nSmall > (nLarge + 9)) then
      return false
    end
    return true
  end

  def Star7_Style(bStar7Enable,nPiTop)
    if (bStar7Enable) then
      sOut = "style='top:#{nPiTop}em'"
    else
      sOut = "style='top:#{nPiTop}em;cursor:default'"
    end
    return sOut
  end

  def Star7_MouseEvent(bStar7Enable)
    sOut = ""
    if (bStar7Enable) then
      sOut = 'onmouseover="px_MouseOver(this,true)" onmouseout="px_MouseOut(this,true)" onmouseup="PiLifeUp(this)"'
    end
    return sOut
  end

  def CheckFlyoutFourHuaExist(aFourhuaStr)
    (Star::FH_LU..Star::FH_GI).each do |nFourHuaIndex|
      if (aFourhuaStr[nFourHuaIndex].length > 0) then
        return true
      end
    end
    return false
  end

  def pan_header(customer_user_id,szDefault,hApFunc,hParAll)
    if (customer_user_id == 0) then
      return szDefault
    end
    if (hApFunc != nil && hApFunc[Cfate::FUNC_HALL_NUMBER]) then
      if (hParAll == nil || hParAll[Cfate::PAR_PRINT_PAN_HEADER] == nil) then
        return szDefault
      else
        return hParAll[Cfate::PAR_PRINT_PAN_HEADER]
      end
    else
      return szDefault
    end
  end

  def tp_one_rows()
    return 27
  end

  def tp_one_cols()
    return 3
  end

  def tp_one_content_width(par_YearDisplay)
    if (par_YearDisplay == Cfate::PAN_DISPLAY_WEST) then
      return 17
    else
      return 20
    end
  end

  def tp_one_empty_width(par_YearDisplay)
    nConWidth = tp_one_content_width(par_YearDisplay)
    return (100 / tp_one_cols()) - nConWidth
  end

  def tp_one(par_YearDisplay,usercustomer,row,col)
    nBirthEYear = usercustomer.eyear
    nFlowEYear = nBirthEYear + (row - 1) + (col - 1) * tp_one_rows()

    return tp_body_info(nFlowEYear,nBirthEYear,par_YearDisplay)
  end

  def tp_body_info(nFlowEYear,nBirthEYear,par_YearDisplay)
    sYear = Xdate.YearDisplay(nFlowEYear,par_YearDisplay)
    sGanZhi = Xdate.GetLunarYearGanZhiStr(nFlowEYear)
    sYearOld = Xdate.GetEYearOldStr(nBirthEYear,nFlowEYear)
    return "#{sYear} #{sGanZhi} #{sYearOld}"
  end

  def tp_one_need_tr(row,col)
    if (col == 1) then
      return true
    end
    return false
  end
  def tp_one_need_tr_end(row,col)
    if (col == tp_one_cols()) then
      return true
    end
    return false
  end

  def tp_header1(usercustomer)
    return "#{usercustomer.name} #{tp_sex(usercustomer.sex)} #{Pm.GetStr("IDS_S_MINBOOK_DESC")}"
  end

  def tp_sex(sex)
    if (sex == 1) then
      sId = "IDS_S_MISTER"
    else
      sId = "IDS_S_MISS"
    end
    return Pm.GetStr(sId)
  end

  def tp_header2(usercustomer,par_YearDisplay)
    return tp_SolarDateStr(usercustomer,par_YearDisplay)
  end

  def tp_SolarDateStr(uc,par_YearDisplay)
    sData = Pm.GetStr("IDS_S_UI_SOLAR_CAL")
    sData += Xdate.GetSolarDateStr(uc.wyear,uc.wmonth,uc.wday,par_YearDisplay)
    sData += Xdate.GetHourStr(uc.hour)
    sData += Pm.GetStr("IDS_S_MINBOOK_GOODTIME")
    return sData
  end

  def tp_footer(hApFunc,hParAll)
    if (hApFunc[Cfate::FUNC_HALL_NUMBER]) then
      if (hParAll[Cfate::PAR_PRINT_SET_FOOTER] == nil) then
        return Pm.GetStr("IDS_S_MINBOOK_FOOTER")
      else
        return hParAll[Cfate::PAR_PRINT_SET_FOOTER]
      end
    else
      return Pm.GetStr("IDS_S_MINBOOK_FOOTER")
    end
  end

  def tp_three_rows()
    return 27
  end

  def tp_three(par_YearDisplay,usercustomer,row,nPage)
    nBirthEYear = usercustomer.eyear
    nFlowEYear = nBirthEYear + (row - 1) + (nPage - 1) * tp_three_rows()

    return tp_body_info(nFlowEYear,nBirthEYear,par_YearDisplay)
  end

  def star_house_select(hHouse,sHouse)
    select_tag("star_house", options_for_select(hHouse,sHouse), :multiple => false,:include_blank => true, :style =>"width:auto;height:auto")
  end
  def pan_type_select(hPanType,nPanType)
    select_tag("pan_type", options_for_select(hPanType,nPanType), :multiple => false,:include_blank => false, :style =>"width:auto;height:auto")
  end

  def checkExplain(hApFunc,nPanType,nHouse)
    # Pm.saveTestDb("checkExplain","#{nPanType},#{hApFunc["explain_pan"]}")
    nHouseKey = 1 << (nHouse - 1)
    bExist = ((nPanType <= hApFunc["n_explain_pan"])) && (hApFunc["n_explain_content"] > 0) && ((hApFunc["n_explain_house"] & nHouseKey) == nHouseKey)
    bExist = bExist || ((nPanType <= hApFunc["n_explain_free_pan"])) && (hApFunc["n_explain_free_content"] > 0) && ((hApFunc["n_explain_free_house"] & nHouseKey) == nHouseKey)
    return bExist
  end

  def makePantypeSelectCss(nPanType,nCurPanType)
    if (nPanType == nCurPanType) then
      return "pantype_select"
    else
      return "pantype"
    end
  end

  # AStarMouseOver2(obj,classId,index,AStarFourHuaSky,bWithEvent)
  def make_AStarMouseOver2(oStar,nPanType,nStar,aAStarFourHuaSky,nEarthIndex,hApFunc,sAStarInfo)
    sFunc = "AStarMouseOver2(this"
    sFunc += ",#{nStar},#{nStar - 1}"

    nHouseIndex = Star.EarthIndex2HouseIndex(nEarthIndex)
    nHouseIdIndex = oStar.g_House_HouseIndex2HouseIdIndex(nPanType,nHouseIndex)
    bWithEvent = checkStarFindSky(hApFunc,nHouseIdIndex) && Star.Check_AStarInfo_StarType_A(sAStarInfo)
    bWithEvent = bWithEvent && (aAStarFourHuaSky[nEarthIndex][nStar - 1] != [])

    if (bWithEvent) then
      sFunc += ",#{aAStarFourHuaSky[nEarthIndex]}"
    else
      sFunc += ",[[], []]"
    end

    sFunc += ",#{bWithEvent}"

    sFunc += ")"
    return sFunc
  end
  def make_AStarMouseUp(oStar,nPanType,nStar,aAStarFourHuaSky,nEarthIndex,hApFunc,sAStarInfo)
    sFunc = "AStarMouseUp(this"
    sFunc += ",#{nStar},#{nStar - 1}"

    nHouseIndex = Star.EarthIndex2HouseIndex(nEarthIndex)
    nHouseIdIndex = oStar.g_House_HouseIndex2HouseIdIndex(nPanType,nHouseIndex)
    bWithEvent = checkStarFindSky(hApFunc,nHouseIdIndex) && Star.Check_AStarInfo_StarType_A(sAStarInfo)
    bWithEvent = bWithEvent && (aAStarFourHuaSky[nEarthIndex][nStar - 1] != [])

    if (bWithEvent) then
      sFunc += ",#{aAStarFourHuaSky[nEarthIndex]}"
    else
      sFunc += ",[[], []]"
    end

    sFunc += ",#{bWithEvent}"

    sFunc += ")"
    return sFunc
  end
  def checkStarFindSky(hApFunc,nHouseIdIndex)
    nHouseKey = 1 << nHouseIdIndex
    bExist = ((hApFunc["n_StarFindSky_house"] & nHouseKey) == nHouseKey)
    return bExist
  end

  def make_HouseSkyMouseOver2(oStar,nPanType,aSkyAStarFourHua,nEarthIndex,hApFunc)
    sFunc = "HouseSkyMouseOver2(this"

    nHouseIndex = Star.EarthIndex2HouseIndex(nEarthIndex)
    nHouseIdIndex = oStar.g_House_HouseIndex2HouseIdIndex(nPanType,nHouseIndex)
    bWithEvent = checkSkyFindStar(hApFunc,nHouseIdIndex)

    if (bWithEvent) then
      sFunc += ",#{aSkyAStarFourHua[nEarthIndex]}"
    else
      sFunc += ",[[], [], [], []]"
    end

    sFunc += ",#{bWithEvent}"

    sFunc += ")"
    return sFunc
  end
  def make_HouseSkyMouseUp(oStar,nPanType,aSkyAStarFourHua,nEarthIndex,hApFunc)
    sFunc = "HouseSkyMouseUp(this"

    nHouseIndex = Star.EarthIndex2HouseIndex(nEarthIndex)
    nHouseIdIndex = oStar.g_House_HouseIndex2HouseIdIndex(nPanType,nHouseIndex)
    bWithEvent = checkSkyFindStar(hApFunc,nHouseIdIndex)

    if (bWithEvent) then
      sFunc += ",#{aSkyAStarFourHua[nEarthIndex]}"
    else
      sFunc += ",[[], [], [], []]"
    end

    sFunc += ",#{bWithEvent}"

    sFunc += ")"
    return sFunc
  end
  def checkSkyFindStar(hApFunc,nHouseIdIndex)
    nHouseKey = 1 << nHouseIdIndex
    if (hApFunc["n_SkyFindStar_house"] == nil) then
      bExist = false
    else
      bExist = ((hApFunc["n_SkyFindStar_house"] & nHouseKey) == nHouseKey)
    end
    return bExist
  end

  def b_c(v)
    return Cfate.ValueBoolCheck(v)
  end

  # for pan_star position 
  def ps_house_width()
    return 78
  end
  def ps_house_height()
    return ps_house_width()
  end
  def ps_x()
    return 20
  end
  def ps_y()
    return 0
  end
  def ps_width()
    return ps_house_width() * 4 + ps_x() + 10
  end
  def ps_height()
    return ps_house_width() * 4 + ps_y() + 20
  end
  def ps_font_size()
    return 14
  end
  def ps_small_font_size()
    return 12
  end
  # earth 1 to 12
  def ps_house_x(earth)
    a = [2,1,0,0,0,0,1,2,3,3,3,3]
    earth_idx = earth - 1
    pos = a[earth_idx]
    x = ps_x() + ps_house_width() * pos
    return x
  end
  def ps_house_y(earth)
    a = [3,3,3,2,1,0,0,0,0,1,2,3]
    earth_idx = earth - 1
    pos = a[earth_idx]
    y = ps_y() + ps_house_width() * pos
    return y
  end
  def ps_text_x()
    lt_x = ps_x() - 5 + ps_font_size()
    return lt_x
  end
  def ps_text_y()
    lt_y = ps_y() + ps_font_size() + 5
    return lt_y
  end
  def ps_star_count()
    return 5
  end
  def ps_astar_x(earth,i)
    a = [2,1,0,0,0,0,1,2,3,3,3,3]
    earth_idx = earth - 1
    pos = a[earth_idx]
    return ps_text_x() + ps_house_width() * pos + (ps_star_count() - i) * ps_font_size() + 4
  end
  def ps_astar_y(earth)
    a = [3,3,3,2,1,0,0,0,0,1,2,3]
    earth_idx = earth - 1
    pos = a[earth_idx]
    return ps_text_y() + ps_house_width() * pos
  end
  def ps_sihua_x(earth,i)
    x = ps_astar_x(earth,i)
    return x
  end
  def ps_sihua_y(earth)
    y = ps_astar_y(earth)
    y = y + ps_font_size() * 1.5
    return y
  end
  def ps_self_sihua_x(earth,i)
    x = ps_astar_x(earth,i)
    return x
  end
  def ps_self_sihua_y(earth)
    y = ps_sihua_y(earth)
    y = y + ps_small_font_size()
    return y
  end
  def ps_self_sihua_rect_x(earth,i)
    x = ps_self_sihua_x(earth,i)
    return x - ps_small_font_size() / 2
  end
  def ps_self_sihua_rect_y(earth)
    y = ps_self_sihua_y(earth)
    return y - ps_small_font_size() / 2
  end
  def ps_sihua_color(i)
    return "transparent" if i == 99 || i == nil
    fourHuaColor_Lu = "rgba(192,57,43,1.0)"
    return fourHuaColor_Lu if i == 0
    fourHuaColor_Chuan = "rgba(142,68,173,1.0)"
    return fourHuaColor_Chuan if i == 1
    fourHuaColor_Ker = "rgba(41,128,185,1.0)"
    return fourHuaColor_Ker if i == 2
    fourHuaColor_Gi = "rgba(243,156,18,1.0)"
    return fourHuaColor_Gi if i == 3
    return "transparent"
  end
  def ps_pt0_house_x(nPanType,earth)
    x = ps_house_x(earth)
    if (nPanType == 0) then
      return x + ps_house_width() / 2
    else
      return x + ps_house_width() / 4 * 2
    end
  end
  def ps_pt0_house_y(earth)
    y = ps_house_y(earth)
    return y + ps_house_height() - ps_font_size() / 2 + 2
  end
  # def ps_house_y(earth)
  #   a = [3,3,3,2,1,0,0,0,0,1,2,3]
  #   earth_idx = earth - 1
  #   pos = a[earth_idx]
  #   return ps_text_y() + ps_house_width() * pos
  # end
  def ps_pt0_housename(housename)
    return housename[1,1]
  end
  def ps_ptn_house_x(nPanType,earth)
    x = ps_house_x(earth)
    return x + ps_font_size() / 2 + 1
  end
  def ps_ptn_house_y(earth)
    return ps_pt0_house_y(earth)
  end
  def ps_small_x(earth)
    x = ps_pt0_house_x(0,earth)
    return x - 4
  end
  def ps_small_y(earth)
    y = ps_pt0_house_y(earth)
    return y - ps_small_font_size() / 3 - 2
  end
  def ps_large_x(earth)
    x = ps_small_x(earth)
    return x
  end
  def ps_large_y(earth)
    y = ps_small_y(earth)
    return y + ps_small_font_size() / 2 + 3
  end
  def ps_housename_hightlight_color()
    return "rgba(211,84,0,1.0)"
  end
  # 天干 x
  def ps_tiangan_x(earth)
    x = ps_house_x(earth)
    return x + ps_house_width() - ps_small_font_size() / 2
  end
  def ps_tiangan_y(earth)
    return ps_pt0_house_y(earth)
  end
  # 五行
  def ps_wuxing_x(earth)
    x = ps_tiangan_x(earth)
    return x - ps_small_font_size()
  end
  def ps_wuxing_y(earth)
    return ps_tiangan_y(earth)
  end

  def ps_house_bk_color(panbg34)
    if panbg34 == 99 then
      return "transparent"
    else
      return "rgba(231,76,60,.2)"
    end
  end
  def ps_34_house_x(earth)
    a = [1,1,2,2,2,2,1,1,0,0,0,0]
    earth_idx = earth - 1
    pos = a[earth_idx]
    x = ps_house_x(earth)
    return x + ps_house_width() * pos / 2
  end
  def ps_34_house_y(earth)
    a = [0,0,0,1,1,2,2,2,2,1,1,0]
    earth_idx = earth - 1
    pos = a[earth_idx]
    y = ps_house_y(earth)
    return y + ps_house_width() * pos / 2
  end

  def ps_34_house_check(hSp_star_pan)
    a34Pt = Array.new(4) {[0,0]}
    (1..12).each do |nEarth|
      n = hSp_star_pan["middle_house_in_middle_panbg34_#{nEarth}"]
      if (n != 99) then
        a34Pt[n] = Array.new(2)
        a34Pt[n][0] = ps_34_house_x(nEarth)
        a34Pt[n][1] = ps_34_house_y(nEarth)
      end
    end
    return a34Pt
  end

  def ps_middle_x()
    x = ps_house_x(7)
    return x + 2 * ps_small_font_size()
  end
  def ps_middle_y()
    y = ps_house_y(5)
    return y + 2 * ps_small_font_size()
  end
  def ps_name_x()
    return ps_middle_x() - 12
  end
  def ps_name_y()
    return ps_middle_y()
  end
  def ps_changepan_x()
    x = ps_house_x(2)
    return x + 5
  end
  def ps_changepan_y()
    y = ps_house_y(2)
    return y - ps_font_size() * 3 / 2
  end

end
