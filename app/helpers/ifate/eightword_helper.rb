module Ifate::<PERSON><PERSON><PERSON><PERSON><PERSON>
	def Ew_Pan_Title(index)
		sIds = ["IDS_E_UI_NAME","IDS_E_UI_MAINSTAR",
			"IDS_E_UI_SKY","IDS_E_UI_EARTH",
			"IDS_E_UI_CHUNGSKY","IDS_E_UI_SUBSTAR",
			"IDS_E_UI_12WIN","IDS_E_UI_GODKILL",
			"IDS_E_UI_NAIN"]
		return Pm.GetStr(sIds[index])
	end
	def Ew_Ptd_Class(index)
		if (index == 7) then
			return "ew_p4ct_data_#{index}"
		else
			return "ew_p4ct_data"
		end
	end

	def Ew_Pcb_Class(index)
		return "ew_pcb_#{index}"
	end
	def Ew_Pcbd_Pos_Class(index,subIdx)
		return "ew_pcbd_pos_#{index}_#{subIdx}"
	end
	def Ew_Pcbd_Pos_Id(byWhatIdx,index,subIdx=nil)
		# return "ew_pcbd_pos_#{byWhatIdx}_#{index}_#{subIdx}"
		if (subIdx == nil) then
			return "ew_pcb_#{byWhatIdx}_#{index}_pos"
		else
			return "ew_pcb_#{byWhatIdx}_#{index}_#{subIdx}_pos"
		end
	end
	def Ew_Pcbd_Data_Class(index,subIdx)
		return "ew_pcbd_data_#{index}_#{subIdx}"
	end
	def Ew_Pcbd_Data_Id(byWhatIdx,index,subIdx=nil)
			# return "ew_pcbd_data_#{byWhatIdx}_#{index}_#{subIdx}"
		if (subIdx == nil) then
			return "ew_pcb_#{byWhatIdx}_#{index}_data"
		else
			return "ew_pcb_#{byWhatIdx}_#{index}_#{subIdx}_data"
		end
	end
	def Ew_Pcbd_0(byWhatIdx)
		sIds = ["IDS_E_DESC_0","IDS_E_DESC_1",
			"IDS_E_DESC_2","IDS_E_DESC_3"]
		sId2s = ["IDS_X_YEAR","IDS_X_MONTH","IDS_X_DAY","IDS_X_HOUR"]
		return Pm.GetStr(sIds[byWhatIdx]) + Pm.GetStrWithQuote(sId2s[byWhatIdx])
	end

	def Ew_Pcbd_1_0(sFiveLevel)
		return sFiveLevel
	end
	def Ew_Pcbd_1_1(sMainStar)
		return sMainStar
	end

	def Ew_4C_Sky_Normal(byWhatIdx)
		if (byWhatIdx != 1) then
			return true
		else
			return true
		end
	end

	def Ew_Pcbd_2_0(sFive)
		return sFive
	end
	def Ew_Pcbd_2_1(sSky)
		# nSky = Sky.SkyIndex2Sky(byWhatIdx)
		# sId = "IDS_S_SKY_#{nSky}"
		# return Pm.GetStr(sId)
		return sSky
	end
	def Ew_Pcbd_2_2(byWhatIdx)
		nFive = byWhatIdx + 1
		return Eightword.getFiveName(nFive)
	end
	def Ew_Pcbd_2_3(byWhatIdx)
		nSky = Sky.SkyIndex2Sky(byWhatIdx)
		sId = "IDS_S_SKY_#{nSky}"
		return Pm.GetStr(sId)
	end
	def Ew_Pcbd_2_4(byWhatIdx)
		sId = "IDS_E_ARROW"
		return Pm.GetStr(sId)
	end
	def Ew_Pcbd_2_5(byWhatIdx)
		nFive = byWhatIdx + 1
		return Eightword.getFiveName(nFive)
	end
	def Ew_Pcbd_2_6(byWhatIdx)
		nSky = Sky.SkyIndex2Sky(byWhatIdx)
		sId = "IDS_S_SKY_#{nSky}"
		return Pm.GetStr(sId)
	end

	# 地支
	def Ew_4C_Earth_Normal(byWhatIdx)
		if (byWhatIdx != 1) then
			return true
		else
			return true
		end
	end
	def Ew_Pcbd_3_0(sFive)
		return sFive
	end
	def Ew_Pcbd_3_1(sEarth)
		# nEarth = Earth.EarthIndex2Earth(byWhatIdx)
		# sId = "IDS_S_EARTH_#{nEarth}"
		# return Pm.GetStr(sId)
		return sEarth
	end
	def Ew_Pcbd_3_2(byWhatIdx)
		nFive = byWhatIdx + 1
		return Eightword.getFiveName(nFive)
	end
	def Ew_Pcbd_3_3(byWhatIdx)
		nEarth = Earth.EarthIndex2Earth(byWhatIdx)
		sId = "IDS_S_EARTH_#{nEarth}"
		return Pm.GetStr(sId)
	end
	def Ew_Pcbd_3_4(byWhatIdx)
		sId = "IDS_E_ARROW"
		return Pm.GetStr(sId)
	end
	def Ew_Pcbd_3_5(byWhatIdx)
		nFive = byWhatIdx + 1
		return Eightword.getFiveName(nFive)
	end
	def Ew_Pcbd_3_6(byWhatIdx)
		nEarth = Earth.EarthIndex2Earth(byWhatIdx)
		sId = "IDS_S_EARTH_#{nEarth}"
		return Pm.GetStr(sId)
	end

	# 藏干
	def Ew_Pcbd_4_0(sSky)
		# nSky = Sky.SkyIndex2Sky(byWhatIdx+i)
		# sId = "IDS_S_SKY_#{nSky}"
		# return Pm.GetStr(sId)
		return sSky
	end

	def Ew_Pcbd_4_1(sFive)
		return sFive
	end

	# 副星
	def Ew_Pcbd_5_0(sStar)
		# nIndex = byWhatIdx + i
		# sId = "IDS_E_MS_#{nIndex}"
		# return Pm.GetStr(sId)
		return sStar
	end

	# 12運
	def Ew_Pcbd_6_0(s12Win)
		# nIndex = byWhatIdx + 1
		# sId = "IDS_S_GOD_#{nIndex}"
		# return Pm.GetStr(sId)
		return s12Win
	end

	# 神煞
	def Ew_Pcbd_7_0(sGodKill,i)
		# sOut = ""
		# (0..byWhatIdx).each do |nIndex|
		# 	sOut += Pm.GetStr("IDS_E_ST_GOD_#{nIndex+1}")
		# 	sOut += Pm.GetStr("IDS_E_CHI_DOT")
		# 	sOut += Pm.GetStr("IDS_E_BST_KILL_#{nIndex+1}")
		# 	if (nIndex != byWhatIdx) then
		# 		sOut += Pm.GetStr("IDS_E_CHI_DOT")
		# 	end
		# end
		sOut = sGodKill
		if (i < sOut.length) then
			return sOut[i]
		else
			return ""
		end
		# length = (i + 1) * 6 > sOut.length ? sOut.length - i * 6 : 6
		# return sOut[i*6,length]
	end

	# 納音
	def Ew_Pcbd_8_0(sNaIn)
		# nIndex = byWhatIdx + 1
		# sId = "IDS_E_NAING_#{nIndex}"
		# return Pm.GetStr(sId)
		return sNaIn
	end

	# 客戶資訊
	def Ew_Pib_Class(index)
		return "ew_pib_#{index}"
	end
	def Ew_Pibd_Pos_Id(index,subIdx,gsubIdx=nil)
		return Ew_gsubIdxCheck("ew_pibd_pos_#{index}_#{subIdx}",gsubIdx)
	end
	def Ew_Pibd_Pos_Class(index,subIdx,gsubIdx=nil)
		return Ew_gsubIdxCheck("ew_pibd_pos_#{index}_#{subIdx}",gsubIdx)
	end
	def Ew_Pibd_Data_Id(index,subIdx,gsubIdx=nil,gsubIdx2=nil)
		sOut = Ew_gsubIdxCheck("ew_pibd_data_#{index}_#{subIdx}",gsubIdx)
		return Ew_gsubIdxCheck(sOut,gsubIdx2)
	end
	def Ew_Pibd_Data_Class(index,subIdx=nil,gsubIdx=nil)
		sOut = Ew_gsubIdxCheck("ew_pibd_data_#{index}",subIdx)
		return Ew_gsubIdxCheck(sOut,gsubIdx)
	end
	def Ew_gsubIdxCheck(sPre,gsubIdx=nil)
		sOut = sPre
		if (gsubIdx != nil) then
			sOut += "_#{gsubIdx}"
		end
		return sOut
	end
	def Ew_Pibd_0(sName)
		return sName
	end
	def Ew_Pibd_1_0(sIn)
		return sIn
	end
	def Ew_Pibd_1_1(sIn)
		return sIn
	end
	def Ew_Pibd_2_0(aIn,index)
		return aIn[index]
	end
	def Ew_Pibd_2_1(aIn,index)
		return aIn[index]
	end
	def Ew_Pibd_3_0(sIn)
		return sIn
	end
	def Ew_Pibd_3_1(sIn)
		return sIn
	end

	# 命宮，胎元...等等
	# 命宮
	def Ew_Psgb_0_1(oEw,i)
		str = oEw.cp_mp_ui_GetLifeHouse()
		if (str == nil) then
			return ""
		end
		return str[i]
	end
	def Ew_Psgb_1_1(oEw)
		return oEw.cp_mp_ui_GetLifeStyle()
	end
	def Ew_Psgb_2_1(oEw,i)
		str = oEw.cp_mp_ui_GetTaiUan()
		if (str == nil) then
			return ""
		end
		return str[i]
	end
	def Ew_Psgb_3_1(oEw,i)
		str = oEw.cp_mp_ui_GetTaiHsi()
		if (str == nil) then
			return ""
		end
		return str[i]
	end
	def Ew_Psgb_4_1(oEw,i)
		str = oEw.cp_mp_ui_GetYearEmpty()
		if (str == nil) then
			return ""
		end
		return str[i]
	end
	def Ew_Psgb_5_1(oEw,i)
		str = oEw.cp_mp_ui_GetDayEmpty()
		if (str == nil) then
			return ""
		end
		return str[i]
	end
	def Ew_Psgb_6(oEw,i)
		if (i == 0) then
			return oEw.cp_mp_ui_UsePeople_Sky()
		else
			return oEw.cp_mp_ui_UsePeople_MainStar()
		end
	end
	# 疾病檢核表 7
	def Ew_Psgb_Title_7_1(i)
		return Pm.GetStr("IDS_E_SICK_#{i + 1}")
	end
	def Ew_Psgb_Data_7_1(oEw,i)
		return oEw.cp_fi_ui_GetnSick(i)
	end
	def Ew_Psgb_Data_7_1_highlight_check(oEw,i)
		if (oEw.cp_fi_ui_GetnSick_alert(i)) then
			return "_highlight"
		else
			return ""
		end
	end

	 # 日主旺度 8
	def Ew_Psgb_8_1(oEw,nPanType)
		return oEw.cp_fi_ui_GetNAvsNB(nPanType)
	end
	 # 陰陽氣合 9
	def Ew_Psgb_9_1(oEw)
		return oEw.cp_mp_ui_NInYangValue()
	end
	 # 中用神 10
	def Ew_Psgb_10_1(oEw)
		return oEw.cp_fi_ui_GetMidGod()
	end
	 # 格局用神 11
	def Ew_Psgb_11_1(oEw)
		return oEw.cp_fi_ui_GetSpecialGod()
	end
	 # 用喜閒忌仇神 12
	def Ew_Psgb_Title_12(oEw,i)
		Pm.GetStr("IDS_E_SG_12_#{i}")
	end
	def Ew_Psgb_Data_12(oEw,i)
		return oEw.cp_fi_ui_GetByUseGod(i)
	end
	 # 旺相休囚死 13
	def Ew_Psgb_Title_13(oEw,i)
		Pm.GetStr("IDS_E_SG_13_#{i}")
	end
	def Ew_Psgb_Data_13(oEw,i)
		return oEw.cp_mp_ui_GetFiveLevel(i)
	end

	# 十神分數
	def Ew_Pfwbs_Title_0(oEw,i)
		return oEw.pan_par_getTenGodStr(i)
	end
	def Ew_Pfwbs_Data_0(oEw,i)
		return oEw.cp_fi_ui_GetnGodValue(i)
	end
	# 五行分數
	def Ew_Pfwbs_Title_1(i)
		return Eightword.getFiveName(i)
	end
	def Ew_Pfwbs_Data_1(oEw,i)
		return oEw.cp_fi_ui_GetnFiveValue(i)
	end

	# 大運
	def Ew_Pfwbw_Title(oEw,nPanType,i)
		sFunc = "Ew_Pfwbw_Title_#{nPanType}(oEw,nPanType,i)"
		return eval(sFunc)
	end
	# 本命
	def Ew_Pfwbw_Title_0(oEw,nPanType,i)
		return Pm.GetStr("IDS_E_FW_#{2 + i}")
	end
	# 十年 hParAll[Cfate::PAR_PAN_YEAR_DISPLAY]
	def Ew_Pfwbw_Title_1(oEw,nPanType,i)
		sRet = case i
		when 0 then
			Pm.GetStr("IDS_S_PAN_NAME1") + Pm.GetStr("IDS_E_FW_3")
		when 1 then
			oEw.cp_fp_ty_getSEWin(nPanType)
		when 2 then
			Xdate.GetYearOldStr(oEw.cp_fp_ty_getDate1(nPanType))
		when 3 then
			oEw.cp_fp_ty_getDate1Year_Str(nPanType)
		when 4 then
			"|"
		when 5 then
			Xdate.GetYearOldStr(oEw.cp_fp_ty_getDate2(nPanType))
		when 6 then
			oEw.cp_fp_ty_getDate2Year_Str(nPanType)
		end
	end
	def Ew_Pfwbw_Title_2(oEw,nPanType,i)
		sRet = case i
		when 0 then
			Pm.GetStr("IDS_S_PAN_NAME2") + Pm.GetStr("IDS_E_FW_3")
		when 1 then
			oEw.cp_fp_fy_getSEWin(nPanType)
		when 2 then
			Xdate.GetYearOldStr(oEw.cp_fp_fy_getDate1(nPanType))
		when 3 then
			oEw.cp_fp_fy_getDate1Year(nPanType)
		end
	end
	def Ew_Pfwbw_Title_3(oEw,nPanType,i)
		sRet = case i
		when 0 then
			Pm.GetStr("IDS_S_PAN_NAME3") + Pm.GetStr("IDS_E_FW_3")
		when 1 then
			oEw.cp_fp_fm_getSEWinYear(nPanType)
		when 2 then
			oEw.cp_fp_fm_getSEWinMonth(nPanType)
		when 3 then
			oEw.cp_fp_fm_getDate1(nPanType)
		when 4 then
			oEw.cp_fp_fm_getDate2(nPanType)
		end
	end
	def Ew_Pfwbw_Title_4(oEw,nPanType,i)
		sRet = case i
		when 0 then
			Pm.GetStr("IDS_S_PAN_NAME4") + Pm.GetStr("IDS_E_FW_3")
		when 1 then
			oEw.cp_fp_fd_getSEWinYear(nPanType)
		when 2 then
			oEw.cp_fp_fd_getSEWinMonth(nPanType)
		when 3 then
			oEw.cp_fp_fd_getSEWinDate(nPanType)
		when 4 then
			oEw.cp_fp_fd_getDate1(nPanType)
		when 5 then
			oEw.cp_fp_fd_getDate2(nPanType)
		when 6 then
			oEw.cp_fp_fd_getDate3(nPanType)
		end
	end
	def Ew_Pfwbw_Title_5(oEw,nPanType,i)
		sRet = case i
		when 0 then
			Pm.GetStr("IDS_S_PAN_NAME5") + Pm.GetStr("IDS_E_FW_3")
		when 1 then
			oEw.cp_fp_ft_getSEWinYear(nPanType)
		when 2 then
			oEw.cp_fp_ft_getSEWinMonth(nPanType)
		when 3 then
			oEw.cp_fp_ft_getSEWinDate(nPanType)
		when 4 then
			oEw.cp_fp_ft_getDate1(nPanType)
		when 5 then
			oEw.cp_fp_ft_getDate2(nPanType)
		when 6 then
			oEw.cp_fp_ft_getDate3(nPanType)
		end
	end
	def Ew_Pfwbw_Title_old(nPanType,i)
		if ([0,4].include?(i)) then
			return Pm.GetStr("IDS_E_FW_#{i}_#{nPanType}")
		else
			return Pm.GetStr("IDS_E_FW_#{i}")
		end
	end
	def ew_pfwbw_col_no(oEw,nPanType)
		if (nPanType == Cfate::PAN_FLOWDATE) then
			return oEw.cp_fp_getMaxCol_FlowDate()
		else
			return 12
		end
	end
	def Ew_Pfwbw_Data(oEw,nPanType,nCol,i,j=nil)
		if (j == nil) then
			sFunc = "Ew_Pfwbw_Data_#{i}(oEw,nPanType,nCol)"
		else
			sFunc = "Ew_Pfwbw_Data_#{i}(oEw,nPanType,nCol,j)"
		end
		return eval(sFunc)
	end
	def Ew_Pfwbw_Data_0(oEw,nPanType,nCol)
		return oEw.cp_fp_4c_getByDateValue(nCol,nPanType)
	end
	def Ew_Pfwbw_Data_1(oEw,nPanType,nCol)
		return oEw.cp_fp_4c_getByMainStar_Name(nCol,nPanType)
	end
	def Ew_Pfwbw_Data_2(oEw,nPanType,nCol)
		return oEw.cp_fp_4c_getSky_Name(nCol,nPanType)
	end
	def Ew_Pfwbw_Data_3(oEw,nPanType,nCol)
		return oEw.cp_fp_4c_getEarth_Name(nCol,nPanType)
	end
	def Ew_Pfwbw_Data_4(oEw,nPanType,nCol)
		return oEw.cp_fp_4c_getByDateValue2(nCol,nPanType)
	end
	def Ew_Pfwbw_Data_5(oEw,nPanType,nCol,j)
		return oEw.cp_fp_4c_getChungSky_Name(nCol,nPanType,j)
	end
	def Ew_Pfwbw_Data_6(oEw,nPanType,nCol,j)
		return oEw.cp_fp_4c_getBySubStar_Name(nCol,nPanType,j)
	end
	def Ew_Pfwbw_Data_7(oEw,nPanType,nCol)
		return oEw.cp_fp_4c_getBy12Win_Name(nCol,nPanType)
	end
	def Ew_Pfwbw_Data_8(oEw,nPanType,nCol)
		return oEw.cp_fp_4c_getNaIn_Name(nCol,nPanType)
	end

	def Ew_Pfwbw_Data_Font(i,oEw,nCol,nPanType)
		nEYear,nEMonth,nEDate,nHour,bLeapMonth = oEw.cp_fp_4c_getEDates(nCol,nPanType)
		if (nPanType == Cfate::PAN_FLOWYEAR) then
			if (nEYear < oEw.uig_EY()) then
				return ""
			end
		end
		if ([0].include?(i)) then
			return "_large"
		elsif ([2,3].include?(i)) then
			return "_xlarge"
		elsif ([4].include?(i)) then
			return ""
		else
			return ""
		end
	end

	def Ew_pfwbw_Data_Highlight(oEw,nPanType,nCol,sSkyEarth)
		if (oEw.cp_fp_isHighlight(nPanType,nCol,sSkyEarth)) then
			return "_highlight"
		else
			return ""
		end
	end
	def Ew_pfwbw_check_cursor(oEw,hApFunc,sMy_url,nPanType,nCol,sSkyEarth,bPanChange)
    bWithEvent = bPanChange
		nNextPanType = Ew_nextPanType(nPanType)
		if (!hApFunc[Cfate.gGetPanTypeHashKey(nNextPanType)]) then
			bWithEvent = false
		end
		sDate = Ew_WinUp_Date(oEw,nPanType,nCol)
		if (sDate == "") then
			bWithEvent = false
		end

		sFunc = "Ew_CursorCheck(this"
		sFunc += ",#{bWithEvent}"
		sFunc += ")"
	end

	def Ew_WinMouseUpFunc(oEw,hApFunc,sMy_url,nPanType,nCol,sSkyEarth)
		nNextPanType = Ew_nextPanType(nPanType)
		if (!hApFunc[Cfate.gGetPanTypeHashKey(nNextPanType)]) then
			return "Ew_WinUp(this,'',-1,-1,-1,-1,-1,0,'')"
		end

		sFunc = "Ew_WinUp(this,'#{sMy_url}'"
		sFunc += ",#{nNextPanType}"

		sDate = Ew_WinUp_Date(oEw,nPanType,nCol)
		if (sDate == "") then
			return "Ew_WinUp(this,'',-1,-1,-1,-1,-1,0,'')"
		end
		sFunc += ",#{sDate}"

		sFunc += ",'#{sSkyEarth}'"

		sFunc += ")"
		return sFunc
	end

	def Ew_WinUp_Date(oEw,nPanType,nCol)
		nEYear,nEMonth,nEDate,nHour,bLeapMonth = oEw.cp_fp_4c_getEDates(nCol,nPanType)
		nLeapMonth = bLeapMonth ? 1 : 0
		if (nEYear >= Cfate::PAN_LAST_YEAR) then
			return ""
		end
		if (nPanType <= Cfate::PAN_FLOWYEAR) then
			if (nEYear < oEw.uig_EY()) then
				return ""
			else
				# if ((nCol == 0) && (oEw.fdg_EY() - 10 >= oEw.uig_EY())) then
				# 	nEYear = oEw.fdg_EY() - 10
				# elsif ((nCol == 11) && (oEw.fdg_EY() + 10 <= Cfate::PAN_LAST_YEAR)) then
				# 	nEYear = oEw.fdg_EY() + 10
				# end
				return "#{nEYear},-1,-1,-1,0"
			end
		else
			if (nPanType == Cfate::PAN_FLOWMONTH) then
				return "#{nEYear},#{nEMonth},#{nEDate},-1,#{nLeapMonth}"
			elsif (nPanType == Cfate::PAN_FLOWDATE) then
				# nEYear1,nEMonth1,nEDate1,bLeapMonth1 = oEw.fdg_E_YMDL()
				# if ((nCol == 0) && (oEw.fdg_SD() - 10 >= 1)) then
				# 	nEYear,nEMonth,nEDate,bLeapMonth = Xdate.PreNEDate(nEYear1,nEMonth1,nEDate1,bLeapMonth1,10)
				# elsif ((nCol == 11) && (oEw.fdg_SD() + 10 <= oEw.cp_fp_getMax_FlowDate())) then
				# 	nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextNEDate(nEYear1,nEMonth1,nEDate1,bLeapMonth1,10)
				# end
				return "#{nEYear},#{nEMonth},#{nEDate},-1,#{nLeapMonth}"
			else
				return "#{nEYear},#{nEMonth},#{nEDate},#{nHour},#{nLeapMonth}"
			end
		end
	end
	def Ew_WinUp_SkyEarth(nRow)
		if (nRow == 2) then
			return Sky::SKY
		elsif (nRow == 3) then
			return Earth::EARTH
		else
			return ""
		end
	end

	def Ew_nextPanType(nPanType)
		if (nPanType == Cfate::PAN_NORMAL) then
			return nPanType + 1
		else
			return nPanType
		end
	end

	# Mouse Over
	def Ew_MainSkyMouseOverFunc(oEw,byWhatIdx)
		isMouseOverEvent = oEw.cp_mp_isSkyMouseOverEvent()
		nSkyIndex = oEw.cp_mp_getBySkyIndex(byWhatIdx)
		return Ew_SkyMouseOverFunc(nSkyIndex,["main","win"],isMouseOverEvent)
	end
	def Ew_WinSkyMouseOverFunc(oEw,hApFunc,nPanType,nCol)
		isMouseOverEvent = oEw.cp_fp_isSkyMouseOverEvent()
		nEYear,nEMonth,nEDate,nHour,bLeapMonth = oEw.cp_fp_4c_getEDates(nCol,nPanType)
		if (nPanType == Cfate::PAN_FLOWYEAR) then
			if (nEYear < oEw.uig_EY()) then
				isMouseOverEvent = false
			end
		end
		nSkyIndex = oEw.cp_fp_4c_getSkyIndex(nCol,nPanType)
		return Ew_SkyMouseOverFunc(nSkyIndex,["main"],isMouseOverEvent)
	end
	def Ew_SkyMouseOverFunc(nSkyIndex,ohlArea,isMouseOverEvent)
		sFunc = "Ew_SkyOver(this"

		if (isMouseOverEvent) then
			sFunc += ",#{[nSkyIndex]}"
		else
			sFunc += ",#{[]}"
		end

		oSkyHerHua = Eightword.SkyHerHua(nSkyIndex,isMouseOverEvent)
		sFunc += ",#{oSkyHerHua}"

		sFunc += ",#{ohlArea}"

		sFunc += ",#{isMouseOverEvent}"

		sFunc += ")"
		return sFunc
	end
	def Ew_MainEarthMouseOverFunc(oEw,byWhatIdx)
		nEvent = oEw.cp_mp_EarthMouseOverEvent()
		nEarthIndex = oEw.cp_mp_getByEarthIndex(byWhatIdx)
		return Ew_EarthMouseOverFunc(nEarthIndex,["main","win"],nEvent)
	end
	def Ew_WinEarthMouseOverFunc(oEw,hApFunc,nPanType,nCol)
		nEvent = oEw.cp_fp_EarthMouseOverEvent()
		nEYear,nEMonth,nEDate,nHour,bLeapMonth = oEw.cp_fp_4c_getEDates(nCol,nPanType)
		if (nPanType == Cfate::PAN_FLOWYEAR) then
			if (nEYear < oEw.uig_EY()) then
				nEvent = 0
			end
		end
		nEarthIndex = oEw.cp_fp_4c_getEarthIndex(nCol,nPanType)
		return Ew_EarthMouseOverFunc(nEarthIndex,["main"],nEvent)
	end
	def Ew_EarthMouseOverFunc(nEarthIndex,ohlArea,nEvent)
		sFunc = "Ew_EarthOver(this"

		sFunc += ",#{[nEarthIndex]}"

		oEarthHer = Eightword.Earth3Her(nEarthIndex,nEvent)
		sFunc += ",#{oEarthHer}"

		oEarthIndex_6Her = Eightword.Earth6Her(nEarthIndex,nEvent)
		sFunc += ",#{oEarthIndex_6Her}"

		oEarthIndex_6Chuon = Eightword.Earth6Chuon(nEarthIndex,nEvent)
		sFunc += ",#{oEarthIndex_6Chuon}"

		oEarthIndex_Hsin = Eightword.EarthHsin(nEarthIndex,nEvent)
		sFunc += ",#{oEarthIndex_Hsin}"

		oEarthIndex_Hai = Eightword.EarthHai(nEarthIndex,nEvent)
		sFunc += ",#{oEarthIndex_Hai}"

		sFunc += ",#{ohlArea}"

		sFunc += ",#{nEvent}"

		sFunc += ")"
		return sFunc
	end

	# Mouse Up
	def Ew_MainSkyMouseUpFunc(oEw,byWhatIdx)
		isMouseOverEvent = oEw.cp_mp_isSkyMouseOverEvent()
		nSkyIndex = oEw.cp_mp_getBySkyIndex(byWhatIdx)
		return Ew_SkyMouseUpFunc(nSkyIndex,["main","win"],isMouseOverEvent)
	end
	def Ew_WinSkyMouseUpFunc(oEw,hApFunc,nPanType,nCol)
		isMouseOverEvent = oEw.cp_fp_isSkyMouseOverEvent()
		nEYear,nEMonth,nEDate,nHour,bLeapMonth = oEw.cp_fp_4c_getEDates(nCol,nPanType)
		if (nPanType == Cfate::PAN_FLOWYEAR) then
			if (nEYear < oEw.uig_EY()) then
				isMouseOverEvent = false
			end
		end
		nSkyIndex = oEw.cp_fp_4c_getSkyIndex(nCol,nPanType)
		return Ew_SkyMouseUpFunc(nSkyIndex,["main"],isMouseOverEvent)
	end
	def Ew_SkyMouseUpFunc(nSkyIndex,ohlArea,isMouseOverEvent)
		sFunc = "Ew_SkyUp(this"

		if (isMouseOverEvent) then
			sFunc += ",#{[nSkyIndex]}"
		else
			sFunc += ",#{[]}"
		end

		oSkyHerHua = Eightword.SkyHerHua(nSkyIndex,isMouseOverEvent)
		sFunc += ",#{oSkyHerHua}"

		sFunc += ",#{ohlArea}"

		sFunc += ",#{isMouseOverEvent}"

		sFunc += ")"
		return sFunc
	end
	def Ew_MainEarthMouseUpFunc(oEw,byWhatIdx)
		nEvent = oEw.cp_mp_EarthMouseOverEvent()
		nEarthIndex = oEw.cp_mp_getByEarthIndex(byWhatIdx)
		return Ew_EarthMouseUpFunc(nEarthIndex,["main","win"],nEvent)
	end
	def Ew_WinEarthMouseUpFunc(oEw,hApFunc,nPanType,nCol)
		nEvent = oEw.cp_fp_EarthMouseOverEvent()
		nEYear,nEMonth,nEDate,nHour,bLeapMonth = oEw.cp_fp_4c_getEDates(nCol,nPanType)
		if (nPanType == Cfate::PAN_FLOWYEAR) then
			if (nEYear < oEw.uig_EY()) then
				nEvent = 0
			end
		end
		nEarthIndex = oEw.cp_fp_4c_getEarthIndex(nCol,nPanType)
		return Ew_EarthMouseUpFunc(nEarthIndex,["main"],nEvent)
	end
	def Ew_EarthMouseUpFunc(nEarthIndex,ohlArea,nEvent)
		sFunc = "Ew_EarthUp(this"

		sFunc += ",#{[nEarthIndex]}"

		oEarthHer = Eightword.Earth3Her(nEarthIndex,nEvent)
		sFunc += ",#{oEarthHer}"

		oEarthIndex_6Her = Eightword.Earth6Her(nEarthIndex,nEvent)
		sFunc += ",#{oEarthIndex_6Her}"

		oEarthIndex_6Chuon = Eightword.Earth6Chuon(nEarthIndex,nEvent)
		sFunc += ",#{oEarthIndex_6Chuon}"

		oEarthIndex_Hsin = Eightword.EarthHsin(nEarthIndex,nEvent)
		sFunc += ",#{oEarthIndex_Hsin}"

		oEarthIndex_Hai = Eightword.EarthHai(nEarthIndex,nEvent)
		sFunc += ",#{oEarthIndex_Hai}"

		sFunc += ",#{ohlArea}"

		sFunc += ",#{nEvent}"

		sFunc += ")"
		return sFunc
	end
	def Ew_isFYRowHighlight(hXdate,nIndex,nDataIndex)
		aDate = hXdate["pan_xdate_flowyear_data"][nIndex][nDataIndex]
		nEYear = hXdate["CurEYear"]
		nEMonth = hXdate["CurEMonth"]
		nEDate = hXdate["CurEDate"]
		nWHour = hXdate["CurWHour"]
		nWMinute = hXdate["CurWMin"]
		bLeapMonth = hXdate["CurELeap"]
		nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
		# nWYear,nSegmentIndex = Xdate.GetSegmentFromWest(nWYear, nWMonth, nWDate,23, 59)
		nWYear, nWMonth, nWDate,nHour,nMinute,nSegmentIndex = Xdate.GetSegMonthFirstDate(nWYear, nWMonth, nWDate,nWHour,nWMinute)
		if ((aDate[4] == nSegmentIndex) || (aDate[4] + 1 == nSegmentIndex)) then
			segTime = Xdate.GetSegDateThisYear(nWYear,aDate[4])
			nWYear,nWMonth,nWDate = nWYear,segTime[Xdate::ST_MONTH],segTime[Xdate::ST_DAY]
			nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
		end

		if ((aDate[0] == nEYear) && (aDate[1] == nEMonth) && (aDate[2] == nEDate) && (aDate[3] == bLeapMonth)) then
			return true
		else
			return false
		end
	end

	def Ew_WinSkyName(oEw,nCol,nPanType)
		nEYear,nEMonth,nEDate,nHour,bLeapMonth = oEw.cp_fp_4c_getEDates(nCol,nPanType)
		if (nPanType == Cfate::PAN_FLOWYEAR) then
			if (nEYear < oEw.uig_EY()) then
				return ""
			end
		end
		return "win_sky_#{oEw.cp_fp_4c_getSkyIndex(nCol,nPanType)}"
	end
	def Ew_WinEarthName(oEw,nCol,nPanType)
		nEYear,nEMonth,nEDate,nHour,bLeapMonth = oEw.cp_fp_4c_getEDates(nCol,nPanType)
		if (nPanType == Cfate::PAN_FLOWYEAR) then
			if (nEYear < oEw.uig_EY()) then
				return ""
			end
		end
		return "win_earth_#{oEw.cp_fp_4c_getEarthIndex(nCol,nPanType)}"
	end

	def Par_Ew_Header(nPanPar)
		if (Par_Ew_Parameter?(nPanPar)) then
			sId = "IDS_E_CAPTION_PAN_PAR_SET"
		elsif (Par_Ew_TenGod?(nPanPar)) then
			sId = "IDS_E_CAPTION_TEN_GOD_SET"
		elsif (Par_Ew_Interface?(nPanPar)) then
			sId = "IDS_E_CAPTION_PAN_INTERFACE_SET"
		elsif (Par_Ew_Display?(nPanPar)) then
			sId = "IDS_E_CAPTION_PAN_DISPLAY_SET"
		elsif (Par_Ew_HallNumber?(nPanPar)) then
			sId = "IDS_E_SET_HALL_NUMBER"
		elsif (Par_Ew_Print?(nPanPar)) then
			sId = "IDS_E_PRINT_SET"
		else
			return ""
		end
		return Pm.GetStrWithColon(sId)
	end

	def Par_Ew_Parameter?(nPanPar)
		return Eightword::PanPar_Type == nPanPar
	end
	def Par_Ew_TenGod?(nPanPar)
		return Eightword::PanPar_TenGod == nPanPar
	end

	def Par_Ew_Interface?(nPanPar)
		return false
		# return Eightword::PanPar_Interface == nPanPar
	end
	def Par_Ew_Display?(nPanPar)
		return Eightword::PanPar_Display == nPanPar
	end
	def Par_Ew_HallNumber?(nPanPar)
		return Eightword::PanPar_HallNumber == nPanPar
	end
	def Par_Ew_Print?(nPanPar)
		return Eightword::PanPar_Print == nPanPar
	end

	def Par_Ew_ResetPos(nPanPar)
		if (Par_Ew_Parameter?(nPanPar)) then
			return 2
		elsif (Par_Ew_TenGod?(nPanPar)) then
			return 2
		elsif (Par_Ew_Interface?(nPanPar)) then
			return 2
		elsif (Par_Ew_Display?(nPanPar)) then
			return 2
		elsif (Par_Ew_HallNumber?(nPanPar)) then
			return 2
		elsif (Par_Ew_Print?(nPanPar)) then
			return 2
		end
		return 2

	end

	def Ew_GodCount()
		return Eightword.GodCount()
	end
	def Ew_KillCount()
		return Eightword.KillCount()
	end

	def Ew_pan_header(customer_user_id,szDefault,hApFunc,hParAll)
		if (customer_user_id == 0) then
			return szDefault
		end
		if (hApFunc != nil && hApFunc[Cfate::FUNC_HALL_NUMBER]) then
			if (hParAll == nil || hParAll[Eightword::PAR_PRINT_PAN_HEADER] == nil) then
				return szDefault
			else
				return hParAll[Eightword::PAR_PRINT_PAN_HEADER]
			end
		else
			return szDefault
		end
	end
	def Ew_pan_footer(customer_user_id,szDefault,hApFunc,hParAll)
		if (customer_user_id == 0) then
			return szDefault
		end
		if (hApFunc != nil && hApFunc[Cfate::FUNC_HALL_NUMBER]) then
			if (hParAll == nil || hParAll[Eightword::PAR_PRINT_SET_FOOTER] == nil) then
				return szDefault
			else
				return hParAll[Eightword::PAR_PRINT_SET_FOOTER]
			end
		else
			return szDefault
		end
	end

	def Ew_tp_footer(hApFunc,hParAll)
		if (hApFunc[Cfate::FUNC_HALL_NUMBER]) then
			if (hParAll[Eightword::PAR_PRINT_SET_FOOTER] == nil) then
				return Pm.GetStr("IDS_E_APP_EIGHTWORD_FOOTER")
			else
				return hParAll[Eightword::PAR_PRINT_SET_FOOTER]
			end
		else
			return Pm.GetStr("IDS_E_APP_EIGHTWORD_FOOTER")
		end
	end

	def ew_select_five(nUseGod,nFive)
		hFive = getFiveHash()
		return select_tag("ug_five_#{nUseGod}", options_for_select(hFive,nFive), :multiple => false,:include_blank => false, :style =>"width:auto;height:auto", :onchange => "OnUseGodFiveClick(#{nUseGod});")
	end
	def getFiveHash()
		hFive = Hash.new
		(0..4).each do |nFive|
			sFive = Eightword.getFiveName(nFive)
			hFive[sFive] = nFive
		end
		return hFive
	end


  # for pan_eightword position 
  def ew_zhu_yao_pan_width()
    return 71
  end
  def ew_x()
    return 31
  end
  def ew_y()
    return 0
  end
  def ew_zhu_yao_pan_height(i)
    return ew_zhu_yao_pan_width() / 3 if i == 1
    return ew_zhu_yao_pan_width() / 2 if i == 2
    return ew_zhu_yao_pan_width() / 2 if i == 5
    return ew_zhu_yao_pan_width() / 2 if i == 6
    return ew_zhu_yao_pan_width() / 3 if i == 7
    if (false) then
    	return ew_zhu_yao_pan_width() * 3 / 2 + 4 if i == 8
    	return ew_zhu_yao_pan_width() / 3 if i == 9
	end
    return ew_zhu_yao_pan_width() / 3 if i == 8
    return ew_zhu_yao_pan_width() / 3 if i == 9
    return ew_zhu_yao_pan_width() / 2 if i == 10

    return ew_zhu_yao_pan_width() / 3
  end
  def ew_width()
    return ew_zhu_yao_pan_width() * 4 + ew_x() + 30
  end
  def ew_height()
    return ew_zhu_yao_pan_height(1) * 15 + ew_y()
  end
  def ew_font_size()
    return 16
  end
  def ew_small_font_size()
    return 12
  end
  def ew_text_x()
    lt_x = ew_x() - 5 + ew_font_size()
    return lt_x
  end
  def ew_text_y()
    lt_y = ew_y() + ew_font_size() + 5
    return lt_y
  end
  def ew_date_words(d)
  	a = ["nian","yue","ri","shi"]
  	return a[d]
  end
  def ew_zhu_yao_pan_x(d)
    a = [3,2,1,0]
    d_idx = d
    pos = a[d_idx]
    x = ew_x() + ew_zhu_yao_pan_width() * pos
    return x
  end
  def ew_zhu_yao_pan_current_y(i)
  	y = 0
  	(1...i).each do |j|
  		y = y + ew_zhu_yao_pan_height(j)
  	end
  	return y
  end
  def ew_zhu_yao_pan_y(d,i)
  	y = ew_zhu_yao_pan_current_y(i)
  	return ew_y() + y
  end
  def ew_zhu_yao_pan_text_x(d,i,j)
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 2 if i == 1 && j == 1
  	# 主星
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 2 if i == 2 && j == 1
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 3 if i == 2 && j == 2

  	# 天干
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 2 if i == 3 && j == 1
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 3 if i == 3 && j == 2

  	# 地支
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 2 if i == 4 && j == 1
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 3 if i == 4 && j == 2

  	# 藏干
  	return ew_zhu_yao_pan_x(d) + 3 if i == 5 && j == 1
  	return ew_zhu_yao_pan_x(d) + 5 if i == 5 && j == 2

  	# 副星
  	return ew_zhu_yao_pan_x(d) + 3 if i == 6 && j == 1
  	return ew_zhu_yao_pan_x(d) + 3 if i == 6 && j == 2

  	# 十二運
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 2 if i == 7

  	# 神煞
  	# return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() - ew_font_size() / 2 * ((j - 1) * 2 + 1) if i == 8

  	# 納音
  	# return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 2 if i == 9

  	# 大運日期
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 4 * 3 if i == 8 && j == 1
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 4 if i == 8 && j == 2

  	# 主星
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 4 * 3 if i == 9 && j == 1
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 4 if i == 9 && j == 2

  	# 干支
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 4 * 3 if i == 10 && j == 1
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 4 if i == 10 && j == 2

  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 4 * 3 if i == 11 && j == 1
  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 4 if i == 11 && j == 2

  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 4 * 3 if i == 12

  	return ew_zhu_yao_pan_x(d) + ew_zhu_yao_pan_width() / 3
  end
  def ew_zhu_yao_pan_text_y(d,i,j)
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) * 2 / 3 if i == 1 && j == 1
  	# 主星
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) * 2 / 3 if i == 2 && j == 1
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 2 if i == 2 && j == 2

  	# 天干
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) * 4 / 5 if i == 3 && j == 1
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 2 if i == 3 && j == 2

  	# 地支
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) * 4 / 5 if i == 4 && j == 1
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 2 if i == 4 && j == 2

  	# 藏干
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + 5 if i == 5 && j == 1
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + 3 + ew_font_size() if i == 5 && j == 2

  	# 副星
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + 5 if i == 6 && j == 1
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + 5 + ew_font_size() if i == 6 && j == 2

  	# 十二運
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + ew_font_size() / 2 if i == 7 && j == 1

  	# 神煞
  	# return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + ew_font_size() + 3 if i == 8

  	# 納音
  	# return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + ew_font_size() / 2 if i == 9

  	# 大運日期
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + ew_font_size() / 2 if i == 8

  	# 主星
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + ew_font_size() / 2 if i == 9
  	
  	# 干支
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + ew_font_size() / 4 if i == 10

  	# 西元年
  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + ew_font_size() / 2 if i == 11

  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 3 + ew_font_size() / 2 if i == 12

  	return ew_zhu_yao_pan_y(d,i) + ew_zhu_yao_pan_height(i) / 2
  end
  def ew_cang_gan_words(sp_eightword_pan,d)
  	cg = ""
  	cg_wx = ""
  	(1..3).each do |i|
  		s = ew_get_word_or_empty(sp_eightword_pan["zhu_yao_pan_#{ew_date_words(d)}_cang_gan_#{4 - i}_tian_gan"])
  		cg += s
  		s = ew_get_word_or_empty(sp_eightword_pan["zhu_yao_pan_#{ew_date_words(d)}_cang_gan_#{4 - i}_tian_gan_wu_xing"])
  		cg_wx += s
  	end
  	return cg,cg_wx
  end
  def ew_get_word_or_empty(s)
  	return Pm.GetStr("IDS_A_KEY_SPACE") if s == nil || s == ""
  	return s
  end
  def ew_fu_xing_words(sp_eightword_pan,d)
  	s1 = ""
  	s2 = ""
  	(1..3).each do |i|
  		s = sp_eightword_pan["zhu_yao_pan_#{ew_date_words(d)}_fu_xing_#{4 - i}"]
  		s = [Pm.GetStr("IDS_A_KEY_SPACE"),Pm.GetStr("IDS_A_KEY_SPACE")] if s == nil || s == ""
  		s1 += ew_get_word_or_empty(s[0])
  		s2 += ew_get_word_or_empty(s[1])
  	end
  	return s1,s2
  end
  def ew_shen_sha_words(sp_eightword_pan,d)
  	a = []
  	(1..32).each do |i|
  		s = sp_eightword_pan["zhu_yao_pan_#{ew_date_words(d)}_shen_sha_#{i}"]
  		a.push(s) if s != nil
  	end
    s_out = a.join(Pm.GetStr("IDS_E_CHI_DOT"))
    # s_out = "天乙貴人、華蓋、孤辰、十惡大敗、驛馬、祿神、孤虛、孤辰、十惡大敗"
    a_out = Array.new()
    if s_out.length > 0 then
    	a_out.push(s_out[0,8]) 
    	a_out[0].concat(ew_create_empty(8-a_out[0].length))
    end
    if s_out.length > 8 then
	    a_out.push(s_out[8,8])
    	a_out[1].concat(ew_create_empty(8-a_out[1].length))
    end
    if s_out.length > 16 then
	    a_out.push(s_out[16,8])
    	a_out[2].concat(ew_create_empty(8-a_out[1].length))
    end
    if s_out.length > 24 then
	    a_out.push(s_out[24,8])
    	a_out[3].concat(ew_create_empty(8-a_out[1].length))
    end

    return a_out
  end
  def ew_create_empty(c)
  	a = Array.new(c,Pm.GetStr("IDS_A_KEY_SPACE"))
  	return a.join("")
  end

  def ew_name_x(d,i,j)
  	x = ew_zhu_yao_pan_text_x(d,i,j)
  	return x - ew_font_size() * 2
  end
  def ew_name_y(d,i,j)
  	y = ew_zhu_yao_pan_text_y(d,i,j)
  	return y
  end

  def ew_changepan_x()
  	x = ew_zhu_yao_pan_text_x(1,1,1)
    return x + 5
  end
  def ew_changepan_y()
    y = ew_name_y(3,12,1)
    return y + 1
  end
end
