module Ifate::<PERSON><PERSON><PERSON><PERSON><PERSON>
  def Need_Star?(bNeed,ap_name)
    return bNeed && (ap_name.upcase == "STAR")
  end
  def Need_Pan?(bNeed)
    return bNeed
  end

  def datesValue(date,dateType)
    if (date.class == Integer) then
      if (date == Xdate::DATE_NIL) then
        return Xdate::DATE_NIL
      else
        return date
      end
    elsif (date.class == String) then
      return date.to_i
    elsif (date.class == Time) then
      return eval("date.#{dateType}")
    else
      return date.to_i
    end
  end

  def getYearHash(start_year,end_year,nYearDisplay)
    hYear = Hash.new
    (start_year..end_year).each do |nYear|
      sYear = Xdate.YearDisplay(nYear,nYearDisplay)
      hYear[sYear] = nYear
    end
    return hYear
  end

  def me_select_year(date,par_YearDisplay, options = {}, html_options = {})
    start_year = options[:start_year]
    end_year = options[:end_year]
    bCanAdd = options[:bCanAdd]

    hYear = getYearHash(start_year,end_year,par_YearDisplay)
    year = datesValue(date,"year")
    return select_tag("year", options_for_select(hYear,year), :multiple => false,:include_blank => false, :style =>"width:auto;height:auto", :onchange => "OnMeDatesClick('year',#{bCanAdd});")
    # return select_year(year, :start_year => start_year, :end_year => end_year, :prefix => "dates")
    # return options
  end

  def me_select_month(calType,date_year,leap,date_month,bIncBlank,bCanAdd)
    hMonth,month = me_get_month(calType,date_year,leap,date_month,bIncBlank)
    return select_tag("month", options_for_select(hMonth,month), :multiple => false,:include_blank => false, :style =>"width:auto;height:auto", :onchange => "OnMeDatesClick('month',#{bCanAdd});")
  end
  def me_get_month(calType,date_year,leap,date_month,bIncBlank)
    year = datesValue(date_year,"year")
    month = datesValue(date_month,"month")
    hMonth = Hash.new
    if (bIncBlank) then
      hMonth[Pm.GetStr("IDS_S_DATE_EMPTY")] = Xdate::DATE_NIL
    end
    if (calType == Xdate::CT_SOLAR) then
      (1..12).each do |nMonth|
        hMonth[nMonth] = Xdate.makeMonthValue(calType,false,nMonth)
      end
    else
      aEMonth = Xdate.GetEastMonthArray(year)
      aEMonth.each { |aMItem| # [nMonth,30,true]
        sMonth = ""
        if (aMItem[2] == true) then
          sMonth = Pm.GetStr("IDS_X_LEAP")
        end
        sMonth += aMItem[0].to_s
        hMonth[sMonth] = Xdate.makeMonthValue(calType,aMItem[2],aMItem[0])
      }
      if (leap) then
        month += 100
      end
    end
    return hMonth,month
  end

  def me_select_day(calType,date_year,leap,date_month,date_day,bIncBlank)
    hDay,day = me_get_day(calType,date_year,leap,date_month,date_day,bIncBlank)
    return select_tag("day", options_for_select(hDay,day), :multiple => false,:include_blank => false, :style =>"width:auto;height:auto")
  end
  def me_get_day(calType,date_year,leap,date_month,date_day,bIncBlank)
    year = datesValue(date_year,"year")
    month = datesValue(date_month,"month")
    if (month == Xdate::DATE_NIL) then
      month = 1
    end
    day = datesValue(date_day,"day")
    if (calType == Xdate::CT_SOLAR) then
      nDays = Xdate.GetWMonthDays(year,month)
    else
      nDays = Xdate.GetEastMonthDays(year, month, leap)
    end
    if (day > nDays) then
      day = nDays
    end
    hDay = Hash.new
    if (bIncBlank) then
      hDay[Pm.GetStr("IDS_S_DATE_EMPTY")] = Xdate::DATE_NIL
    end
    (1..nDays).each do |nDay|
      hDay[nDay] = nDay
    end
    return hDay,day
  end

  def me_select_hour(date_hour,bIncBlank)
    hHour,hour = me_get_hour(date_hour,bIncBlank)
    return select_tag("hour", options_for_select(hHour,hour), :multiple => false,:include_blank => false, :style =>"width:auto;height:auto")
  end
  def me_get_hour(date_hour,bIncBlank)
    hour = datesValue(date_hour,"hour")
    hHour = Hash.new
    if (bIncBlank) then
      hHour[Pm.GetStr("IDS_S_DATE_EMPTY")] = Xdate::DATE_NIL
    end
    (0..23).each do |nHour|
      hHour[nHour] = nHour
    end
    return hHour,hour
  end

  def me_select_minute(date_minute,bIncBlank)
    hMinute,minute = me_get_minute(date_minute,bIncBlank)
    return select_tag("minute", options_for_select(hMinute,minute), :multiple => false,:include_blank => false, :style =>"width:auto;height:auto")
  end
  def me_get_minute(date_minute,bIncBlank)
    minute = datesValue(date_minute,"min")
    hMinute = Hash.new
    if (bIncBlank) then
      hMinute[Pm.GetStr("IDS_S_DATE_EMPTY")] = Xdate::DATE_NIL
    end
    (0..59).each do |nMinute|
      hMinute[nMinute] = nMinute
    end
    return hMinute,minute
  end

  def getUCYear(user_customer)
    if (user_customer.calType == Xdate::CT_SOLAR) then
      return user_customer.wyear
    else
      return user_customer.eyear
    end
  end

  def getUCMonth(user_customer)
    if (user_customer.calType == Xdate::CT_SOLAR) then
      return user_customer.wmonth
    else
      return user_customer.emonth
    end
  end

  def getUCLeap(user_customer)
    if (user_customer.calType == Xdate::CT_SOLAR) then
      return false
    else
      return Cfate.Int2Bool(user_customer.eleap)
    end
  end

  def getUCDay(user_customer)
    if (user_customer.calType == Xdate::CT_SOLAR) then
      return user_customer.wday
    else
      return user_customer.eday
    end
  end

  def getUCHour(user_customer)
    return user_customer.hour
  end

  def getUCMinute(user_customer)
    return user_customer.minute
  end

  def me_blood_select(bloodSelected)
    return select_tag("blood","<option value='0' #{bloodSelected[0]}>O</option><option value='1' #{bloodSelected[1]}>A</option><option value='2' #{bloodSelected[2]}>B</option><option value='3' #{bloodSelected[3]}>AB</option>".html_safe, :multiple => false,:include_blank => true, :style =>"width:auto;height:auto")
  end

  # make_search_func(@ap_name)
  def make_search_func(ap_name)
    func = "cus_search_data_st(this.value,'#{ap_name}');"
    return func
  end
  def make_search_func2(ap_name)
    func = "cus_search_data_st2(this.value,'#{ap_name}');"
    return func
  end

  def make_navbar_class(active,disable)
    if (disable) then
      return "disabled"
    end
    if (active) then
      return "active"
    end
    return ""
  end

  # health
  def make_health_search_func()
    # func = "health_search_data_st(this.value);"
    func = "health_search_key_st(this.value);"
    return func
    # return ""
  end

  def cust_display_check(hPars,key)
    if (["b_name_abbr","b_showsum"].include?(key)) then
      return Cfate.ParValueCheck(key,hPars[key],true)
    else
      return Cfate.ParValueCheck(key,hPars[key],true)
    end
  end

  def cust_display_solar(hCustDisplay,wyear,wmonth,wday,hour,minute)
    y = cust_display_check(hCustDisplay,"b_solar_year") ? wyear : -1
    m = cust_display_check(hCustDisplay,"b_solar_md") ? wmonth : -1
    d = cust_display_check(hCustDisplay,"b_solar_md") ? wday : -1
    h = cust_display_check(hCustDisplay,"b_solar_hm") ? hour : -1
    min = cust_display_check(hCustDisplay,"b_solar_hm") ? minute : -1
    return y,m,d,h,min
  end
  def cust_display_lunar(hCustDisplay,eyear,emonth,eday,hour,eleap)
    y = cust_display_check(hCustDisplay,"b_lunar_year") ? eyear : -1
    m = cust_display_check(hCustDisplay,"b_lunar_md") ? emonth : -1
    l = cust_display_check(hCustDisplay,"b_lunar_md") ? Cfate.BoolCheck(eleap,false) : false
    d = cust_display_check(hCustDisplay,"b_lunar_md") ? eday : -1
    t = cust_display_check(hCustDisplay,"b_lunar_hm") ? Xdate.Hour2ETime(hour) : -1
    return y,m,d,t,l
  end

  def cust_display_name(s)
    if (s.length == 0) then
      return s
    end
    i = 0
    s.each_byte {|c| i += 1 }
    if (i == s.length()) then
      return s[s.length - 2,2]
    else
      return s[s.length - 1,1]
    end
  end
end
