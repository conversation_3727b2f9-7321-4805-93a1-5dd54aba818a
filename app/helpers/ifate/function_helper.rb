require("Zeri.rb")
module Ifate::Fun<PERSON>Helper
  def zeri_simple(can_zeri,ap_name)
    return Zeri.zeri_simple(can_zeri,ap_name)
    # return is_main ? 2 : 1
  end
  def zeri_need_hour(ap_name,free=false)
    return Zeri.zeri_need_hour(ap_name,free)
  end

  # star
  def zeri_xing_checked?(xings,xing)
    if (xings == nil) then
      return false
    end
    return xings.include?(xing)
  end
  def zeri_gongwei(gongweis,idx)
    if (gongweis == nil) then
      return [1,5,9,7,0][idx]
    end
    return gongweis[idx]
  end
  def zeri_sihua_checked?(sihua,sihua_idx)
    if (sihua == nil) then
      return false
    end
    return sihua.include?(sihua_idx)
  end
  def zeri_gongweitiaojian(gongweitiaojian,gongweitiaojian_key=nil)
    if (gongweitiaojian == nil) then
      if (gongweitiaojian_key == "gongweitiaojian_xingyao2") then
        return 2
      else
        return 0
      end
    end
    return gongweitiaojian
  end

  # eightword

  # wann<PERSON><PERSON>
  def zeri_hechong_checked?(hechongs,hechong)
    if (hechongs == nil) then
      return false
    end
    return hechongs.include?(hechong)
  end
end
