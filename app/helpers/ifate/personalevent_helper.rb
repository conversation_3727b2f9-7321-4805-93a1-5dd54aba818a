module Ifate::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
	def makePeDateStr_brief(personal_event)
		sDate = makePeDateStr(personal_event)
		sData = "#{personal_event.pe_main_value},#{personal_event.pe_sub_value},#{sDate},#{personal_event.pe_desc}"
		return sData
	end
	
	def makePeDateTimeStr(personal_event)
		sDate = makePeDateStr(personal_event)
		sTime = makePeTimeStr(personal_event)
		return "#{sDate} #{sTime}"
	end

	def makePeDateStr(personal_event)
		sCal = personal_event.caltype == Xdate::CT_SOLAR ? "IDS_S_UI_SOLAR_CAL" : "IDS_S_UI_LUNAR_CAL"
		sCal = Pm.GetStr(sCal)[0]
		sLeap = personal_event.leap == 1 ? Pm.GetStr("IDS_X_LEAP") : ""
		sMonth = "#{sLeap}#{makePeDateItemStr(personal_event.month)}"
		sDay = "#{makePeDateItemStr(personal_event.day)}"
		sDate = "#{sCal}#{personal_event.year}/#{sMonth}/#{sDay}"
		return sDate
	end
	def makePeTimeStr(personal_event)
		sHour = "#{makePeDateItemStr(personal_event.hour)}"
		sMinute = "#{makePeDateItemStr(personal_event.minute)}"
		sTime = "#{sHour}:#{sMinute}"
		return sTime
	end
	def makePeDateItemStr(date)
		if (date == Xdate::DATE_NIL) then
			return ""
		else
			return date.to_s
		end
	end

	def makePeHeader(szHeaderId,customer)
		sBuf = Pm.GetStr(szHeaderId)
		sBuf += Pm.GetStr("IDS_A_LEFT_QUOTE")
		sBuf += customer.name
		sBuf += Pm.GetStr("IDS_A_RIGHT_QUOTE")
		return sBuf
	end
end
