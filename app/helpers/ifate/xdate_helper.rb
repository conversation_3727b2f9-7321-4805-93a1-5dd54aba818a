module Ifate::<PERSON><PERSON><PERSON><PERSON><PERSON>
  def makeSunDateStr(hDate)
    hDate["SunDate"]
  end

  def makeLunarDateStr(hDate)
    hDate["LunarDate"]
  end

  def makeLunarGanZhiStr(hDate,index)
    getGanZhiStrByIndex(hDate["LunarGanZhi"],index)
  end

  def getGanZhiStrByIndex(ganZhiStr,index)
    start = index * 3
    len = 2
    return ganZhiStr[start,len]
  end

  def makeSegmentGanZhiStr(hDate,index)
    getGanZhiStrByIndex(hDate["SegGanZhi"],index)
  end

  def checkTabVisibility(firstShowAdvertise,blockName)
    return (firstShowAdvertise == blockName) ? "visible" : "hidden"
  end

  def checkXdateVisibility(sVisible,curPanType,nPanType)
    # return (curPanType == nPanType) ? sVisible : "hidden"
    return (curPanType == nPanType) ? "visible" : "hidden"
  end

  def checkTabSelect(firstShowAdvertise,blockName)
    return (firstShowAdvertise == blockName) ? "_select" : ""
  end
end
