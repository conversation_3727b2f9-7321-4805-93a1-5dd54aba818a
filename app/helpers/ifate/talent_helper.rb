module Ifate::<PERSON><PERSON><PERSON><PERSON>
  def talent_find_key(h,v)
  	h.keys.each_with_index do |key,i|
  		if (v == h[key]) then
  			return key
  		end
  	end
  	return ""
  end
  def sw_b_c(v)
    return Cfate.ValueBoolCheck(v)
  end

  def trait_List_all_get_key(keys,trait_code)
    # "N71_L1:基本特質/職能天賦/業務型"=>"L1:category_1/item_7/trait_1",
    keys.each  do |key|
      a = key.split(":")
      b = a[0].split("_")
      if trait_code == b[0] then
        return key
      end
    end
    return ""
  end
  def trait_List_all_get_value(trait_List_all,trait_code)
    key = trait_List_all_get_key(trait_List_all.keys,trait_code)
    if (key == "") then 
      return ""
    end
    return trait_List_all[key]
  end
end
