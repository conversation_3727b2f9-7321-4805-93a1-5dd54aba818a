module Xuexi::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def zp_pantype_xdate(pantype)
    return Cfate::PAN_FLOWYEAR if [Cfate::PAN_NORMAL,Cfate::PAN_TENYEAR].include?(pantype)
    return [pantype + 1,Cfate::PAN_FLOWMIN].min
  end
  def zp_pantype_pan(pantype)
    return Cfate::PAN_TENYEAR if [Cfate::PAN_NORMAL].include?(pantype)
    return pantype
  end
  def zp_pantype_pan_gong(pantype)
    return 1 if [Cfate::PAN_NORMAL].include?(pantype)
    return 2
  end

end

