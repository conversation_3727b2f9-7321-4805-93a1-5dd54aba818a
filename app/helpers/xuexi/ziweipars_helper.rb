module Xuexi::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  def zw_pars_stylecolor(par_key,par_keys,xing)
    show = par_keys.include?(par_key)
    return "" if (!show)
    hl_xings = zw_pars_stylecolor_hl_xings(par_key)
    return "" if !hl_xings.include?(xing)
    return "color:white;background-color:rgba(233,30,99,1);animation: blinker 1s linear infinite;"
  end
  def zw_pars_stylecolor2(par_key,par_keys,xing)
    show = par_keys.include?(par_key)
    return "" if (!show)
    hl_xings = zw_pars_stylecolor_hl_xings(par_key)
    return "" if !hl_xings.include?(xing)
    return "color:white;background-color:rgba(233,30,99,1);animation: blinker 1s linear infinite;"
  end
  def zw_pars_stylecolor_astar(par_key,star_type,par_keys,xing)
    show = par_keys.include?(par_key)
    return "" if (!show)
    hl_xings = zw_pars_stylecolor_hl_xings_astar(par_key,star_type)
    return "" if !hl_xings.include?(xing)
    return "color:white;background-color:rgba(233,30,99,1);animation: blinker 1s linear infinite;"
  end
  def zw_pars_stylecolor_hl_xings(par_key)
    hl_xings = []
    hl_xings += zw_parkey_stylecolor_hl_xings(par_key)
    return hl_xings
  end
  def zw_pars_stylecolor_hl_xings_astar(par_key,star_type)
    if (star_type == "a") then
      return zw_parkey_stylecolor_hl_xings(par_key)
    elsif (star_type == "b") then
      return zw_parkey_stylecolor_hl_xings(par_key)
    elsif (star_type == "fb") then
      return [4] if par_key == Star::DIS_FLOWMA
      return [5] if par_key == Star::DIS_FLOWLUAN
      return []
    elsif (star_type == "fa") then
      return [1,2,3] if par_key == Star::DIS_FLOWLYT
      return [6] if par_key == Star::DIS_FLOWCHAN
      return []
    end
    return [1]
  end
  def zw_parkey_stylecolor_hl_xings(par_key)
    return [1,2,4,6,7,10,11] if par_key == Star::PAR_GS_NAME_TYPE
    return [19] if par_key == Star::PAR_LU_TYPE
    return [5] if par_key == Star::PAR_HORSE_TYPE
    return [30] if par_key == Star::PAR_GIAKUNG_TYPE
    return [1,4,7,10] if par_key == Star::PAR_GOD_TYPE12
    return [1,4,7,10] if par_key == Star::PAR_DOC_TYPE
    return [1,4,7,10] if par_key == Star::PAR_GS_TYPE
    return [1] if par_key == Star::PAR_FLOWSKY_TYPE
    return [31,32] if par_key == Star::PAR_B3132_TYPE
    return [30] if par_key == Star::PAR_B30_TYPE
    return [24,25] if par_key == Star::PAR_B2425_TYPE
    return [24,25] if par_key == Star::PAR_YUEKUI_TYPE
    return [1,2,3,4,5,6,7,8,9,10,11,12] if par_key == Star::INT_SMALL_DISPLAY
    return [19,20,21] if par_key == Star::DIS_FLOWLYT
    return [5] if par_key == Star::DIS_FLOWMA
    return [15] if par_key == Star::DIS_FLOWLUAN
    return [1,2,3,4,5,6,7,8,9,10,11,12] if par_key == Star::DIS_DOCTOR
    return [1,2,3,4,5,6,7,8,9,10,11,12] if par_key == Star::DIS_LIVE
    return [1,2,3,4,5,6,7,8,9,10,11,12] if par_key == Star::DIS_YEARSTAR
    return [1,2,3,4,5,6,7,8,9,10,11,12] if par_key == Star::DIS_YEARGOD
    return [15] if par_key == Star::DIS_FLOWCHAN

    return [1]
  end
  def zw_pars_desc_html(s)
    dot = Pm.t("xuexi.ziweipars.desc.dot")
    a = s.split(dot)
    so = ""
    a.each do |p|
      so += zw_pars_desc_html_each(p)
      # so += "<br/><br/>"
      so += "<br/>"
    end
    return so
  end
  def zw_pars_desc_html_each(p)
    colon = Pm.t("xuexi.ziweipars.desc.colon")
    a = p.split(colon)
    so = ""
    a.each_with_index do |s,i|
      so += "<br/>" if i > 0
      so += "<span style='background-color:rgba(233,30,99,1);color:white;font-weight:bold;'>" if i == 0
      so += s
      so += "</span>" if i == 0
    end
    return so
  end
end
