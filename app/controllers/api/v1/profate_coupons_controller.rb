require("Controller_Api.rb")

class Api::V1::ProfateCouponsController < ApplicationController
  include Controller_Api
  # before_action :authenticate_profate_clients!, :except => []
  before_action :set_coupon, only: [:show, :update, :destroy, :coupon_if_valid]
  protect_from_forgery with: :null_session, if: -> { request.format.json? }
  respond_to :json

  def index
    @coupons = ProfateCoupon.all
    if @coupons.nil?
      render json: [{status: "no data"}]
    else
      render json: @coupons if @coupons.present?
    end
  end

  # GET /api/v1/profate_coupons/1
  # GET /api/v1/profate_coupons/1.json
  def show
    if @coupon.nil?
      render nothing: true
    else
      render json: @coupon if @coupon.present?
    end
  end
  def coupon_if_valid
    if (@coupon.nil? || !@coupon.valid) then
      render json: {:status => "invalid"}
    else
      render json: {:status => "valid"}
    end
  end
  # POST /api/v1/profate_coupons
  # POST /api/v1/profate_coupons.json
  def create
    count = params["count"].to_i
    if (count == 0) then
      count = 1
    end
    coupon_save = true
    a = Array.new(count)
    (0..count).each do |i|
      @coupon = ProfateCoupon.new
      @coupon.uuid = get_profateevent_coupon()
      # @coupon.create_user_id = user_id
      # @coupon.flag = 0 # 未發出
      @coupon.flag = 1 # 已發出 1
      @coupon.start_date = params["start_date"]
      @coupon.end_date = params["end_date"]
      @coupon.save!
      a[i] = @coupon.uuid
    end

    if coupon_save then
      render json: {:coupons => a,:status => "created #{count} coupons"}, status: :created
    else
      render json: @coupon.errors, status: :unprocessable_entity
    end
  end
  # PATCH/PUT /api/v1/profate_coupons/1
  # PATCH/PUT /api/v1/profate_coupons/1.json
  def update
    # @coupon = ProfateCoupon.find(params["id"])

    if @coupon.update(coupon_params)
      head :no_content
    else
      render json: @coupon.errors, status: :unprocessable_entity
    end
  end

  # DELETE /api/v1/profate_coupons/1
  # DELETE /api/v1/profate_coupons/1.json
  def destroy
    @coupon.destroy

    head :no_content
  end

  private

  # 先判斷是否為event_name（因為event_name是英文，不會跟id重覆）
  # 若不是，再當成id來抓
  def set_coupon
    # event_name
    @coupon = ProfateCoupon.check_uuid(params["id"]).last
  end

  def coupon_params
    params.require(:profate_coupons).permit(:uuid, :start_date, :end_date, :use_date, :remote_ip, :buy_user_id, :profate_order_id, :create_user_id, :send_user_id, :use_user_id, :flag)
  end

end
