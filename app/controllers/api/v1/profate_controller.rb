class Api::V1::Profate<PERSON>ontroller < ApplicationController
  respond_to :json
  protect_from_forgery with: :null_session, if: -> { request.format.json? }

  public
  def members_test
    if member_signed_in?
      render json: {
       data: {
         message: "Welcome #{current_member.email}",
         user: current_member
       }
      }, status: 200
    else
      render json: {
       errors: ["#{I18n.locale}"]
      }, status: 200
    end
  end

end
