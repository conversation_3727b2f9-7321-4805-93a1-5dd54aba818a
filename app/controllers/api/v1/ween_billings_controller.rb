require("Xdate.rb")
require("WeenApi.rb")
require("Pm.rb")
require("Controller_Api.rb")
require("Controller_Api_Order.rb")
require("Controller_Api_Allpay.rb")
require("Controller_Api_Profate_Order.rb")

class Api::V1::WeenBillingsController < ApplicationController
  include Controller_Api
  include Controller_Api_Order
  include Controller_Api_Allpay
  include Controller_Api_Profate_Order
  protect_from_forgery with: :null_session, if: -> { request.format.json? }
  respond_to :json

  def supported_paymenttype
    cp = Allpay_ChoosePayment::Support_CP
    # ua = AgentOrange::UserAgent.new(request.headers["HTTP_USER_AGENT"])
    # if (!ua.device.is_mobile?) then
    #   cp = Allpay_ChoosePayment::Support_CP
    # else
    #   cp = Allpay_ChoosePayment::Support_CP_mobile
    # end
    a = Array.new
    cp.each do |key|
      a.push({ "Key" => key,"desc" => Controller_Api_Allpay.choosepayment_desc(key)})
    end
    h = {"supported_paymenttype" => a}
    render json: h
  end

  def allpay_checkout
    client_ip = remote_ip()
    h = ween_billing_get_allpay_checkout_data(params,client_ip)
    render json: h
  end
  def pan_par_hash2db(h)
    a = h.to_a#.flatten
    return pan_par_join(a)
  end

  def pan_par_join(a)
    s = a.join(",")
    return s
  end
  def allpay_return
    # Pm.saveTestDb("allpay_return",pan_par_hash2db(params.to_hash))
    # hPars = {"MerchantID"=>"2000132", "MerchantTradeNo"=>"we1900", "PaymentDate"=>"2015/10/28 16:15:20", "PaymentType"=>"Credit_CreditCard", "PaymentTypeChargeFee"=>"6", "RtnCode"=>"1", "RtnMsg"=>"交易成功", "SimulatePaid"=>"0", "TradeAmt"=>"200", "TradeDate"=>"2015/10/28 16:11:10", "TradeNo"=>"1510281611107105", "CheckMacValue"=>"D6E05ECB29950E10B5417DE033DF5922", "format"=>"json", "controller"=>"api/v1/profate_orders", "action"=>"allpay_return"}
    # hPars = {"MerchantID"=>"2000132", "MerchantTradeNo"=>"ween2278", "PaymentDate"=>"2015/10/29 21:35:53", "PaymentType"=>"Credit_CreditCard", "PaymentTypeChargeFee"=>"6", "RtnCode"=>"1", "RtnMsg"=>"交易成功", "SimulatePaid"=>"0", "TradeAmt"=>"200", "TradeDate"=>"2015/10/29 21:34:37", "TradeNo"=>"1510292134375956", "CheckMacValue"=>"763DA5CB125A8F4C0BF731552FBC690E", "format"=>"json", "controller"=>"api/v1/profate_orders", "action"=>"allpay_return"}
    # hPars = Cfate.pan_par_dbfield2hash("MerchantID,2000132,MerchantTradeNo,ween2509,RtnCode,1,RtnMsg,交易成功,TradeNo,1602081526498953,TradeAmt,200,PaymentDate,2016/02/08 15:28:07,PaymentType,Credit_CreditCard,PaymentTypeChargeFee,6,TradeDate,2016/02/08 15:26:49,SimulatePaid,0,CheckMacValue,388F24993514673086306F848DEDDCB3,ChoosePayment,Credit,controller,service/store,action,buy_products_complete,locale,zh-TW")
    # 將資料庫從meentw_stage加回來local,再測試程式，OK了再放上staging
    hPars = params.clone

    # 手動補救，不影響正常程式
    if (params["peter"] == "1") then
     # <EMAIL>
     # hPars = {"MerchantID"=>"1118704", "MerchantTradeNo"=>"ween2406", "PaymentDate"=>"2016/02/18 14:08:34", "PaymentType"=>"CVS_CVS", "PaymentTypeChargeFee"=>"26", "RtnCode"=>"1", "RtnMsg"=>"交易成功", "SimulatePaid"=>"0", "TradeAmt"=>"1800", "TradeDate"=>"2016/02/17 23:07:28", "TradeNo"=>"1602172307280082", "CheckMacValue"=>"2F7FB64E6E58E727BC0D7E6D8039BFCE", "format"=>"json", "controller"=>"api/v1/ween_billings", "action"=>"allpay_return"}
    end

    # hdefault,is_RtnCode_valid,is_macvalue_valid = allpay_returnurl_pars_parse(params)
    hdefault,is_RtnCode_valid,is_macvalue_valid = allpay_returnurl_pars_parse(hPars)
    # Pm.saveTestDb("hdefault",pan_par_hash2db(hdefault.to_hash))
    # Pm.saveTestDb("is_RtnCode_valid",is_RtnCode_valid)
    if (!is_macvalue_valid) then
      # Pm.saveTestDb("allpay_return",pan_par_hash2db(params.to_hash))
      Pm.saveTestDb("is_macvalue_valid",is_macvalue_valid)
    end
  # puts "hdefault => #{hdefault}"
    order_id = allpay_merchanttradeno_decode_hash(hdefault)
    # 1-模擬付款/0-非模擬付款
    if (is_macvalue_valid) then
      render_text = "1|OK"
      sData = Pm.getSystemValue("SimulatePaid")
      if (hdefault["SimulatePaid"] != "1" || sData == "Y") then
        bSuccess,h_order,feedback_url = finish_order(order_id,hdefault,is_RtnCode_valid)
      # Pm.saveTestDb("bSuccess",bSuccess)
      # Pm.saveTestDb("h_order",pan_par_hash2db(h_order.to_hash))
        if (bSuccess) then
          if (feedback_url == "profate_order") then
            finish_profate_order(order_id,is_RtnCode_valid,hdefault["RtnCode"])
          else
            # 其他主機使用我們的ween-api的金流，要通知他們交易成功。
            if (is_macvalue_valid) then
              t = send_back_to_feedback_url(order_id,is_RtnCode_valid,hdefault,feedback_url)
              if (!t) then
                update_order_status_when_finish_order(order_id,false,-2)
                delete_payment_when_order_failed(order_id)
                render_text = "0|error"
              end
            end
          end
        else
          render_text = "0|error"
        end
      end
    else
      update_order_status_when_finish_order(order_id,false,-3)
      render_text = "0|CheckMacValue error"
    end
    render :text => render_text
  end
  def allpay_paymentinfo
    # Pm.saveTestDb("allpay_paymentinfo",pan_par_hash2db(params.to_hash))
    hPar = params.clone
    # hPar = {"BankCode"=>"812", "ExpireDate"=>"2015/11/27", "MerchantID"=>"2000132", "MerchantTradeNo"=>"ween2464", "PaymentType"=>"ATM_TAISHIN", "RtnCode"=>"2", "RtnMsg"=>"Get VirtualAccount Succeeded", "TradeAmt"=>"100", "TradeDate"=>"2015/11/24 14:54:14", "TradeNo"=>"****************", "vAccount"=>"****************", "CheckMacValue"=>"3AF3C77E675C047D69F90CC75EB8E763", "format"=>"json", "controller"=>"api/v1/ween_billings", "action"=>"allpay_paymentinfo"}
    # hPar = {"Barcode1"=>"0411186EA", "Barcode2"=>"****************", "Barcode3"=>"***************", "ExpireDate"=>"2015/11/18 15:18:36", "MerchantID"=>"2000132", "MerchantTradeNo"=>"ween2390", "PaymentNo"=>"", "PaymentType"=>"BARCODE_BARCODE", "RtnCode"=>"********", "RtnMsg"=>"Get CVS Code Succeeded.", "TradeAmt"=>"180", "TradeDate"=>"2015/11/11 15:18:36", "TradeNo"=>"****************", "CheckMacValue"=>"2057E96B7900A690480E35EEEE99481C", "format"=>"json", "controller"=>"api/v1/ween_billings", "action"=>"allpay_paymentinfo"}

    hdefault,is_RtnCode_valid,is_macvalue_valid = allpay_paymentinfo_pars_parse(hPar)
    order_id = allpay_merchanttradeno_decode_hash(hdefault)
    bSuccess = update_order_when_server_created_order(order_id,is_RtnCode_valid,hdefault)
    if (is_macvalue_valid) then
      render_text = "1|OK"
    else
      render_text = "0|CheckMacValue error"
    end
    render :text => render_text
  end
  def allpay_periodreturn
    # Pm.saveTestDb("allpay_periodreturn",pan_par_hash2db(params.to_hash))
  end
  private
end
