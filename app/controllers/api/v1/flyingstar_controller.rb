require("Pm.rb")
require("Xdate.rb")
require("WeenApi.rb")
require("Controller_Api.rb")
require("Score.rb")
require("Jianggong.rb")
require("Talent.rb")
require("meen_forum.rb")
require("Divination.rb")
require("Controller_Api_Profate_Order.rb")

# redis-server /usr/local/etc/redis.conf
# bundle exec sidekiq

class Api::V1::FlyingstarController < ApplicationController
  include Controller_Api
  include Controller_Api_Profate_Order
  include MeenForum
  
  before_action :set_my_locale, :except => []
  protect_from_forgery with: :null_session, if: -> { request.format.json? }
  respond_to :json

  public
  # 姜公獻寶API
  def jianggong
    h_par_org = params.clone
    api_id = h_par_org["api_id"]
    @oJg = Jianggong.new
    @ApFunc = nil
    @ParAll = nil
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserInfo = Jianggong.get_user_info(h_par_org)
    hUserDefData["my_current_lang"] = my_current_lang()
    h_par_org["my_current_lang"] = my_current_lang()
    api_name = 1
    @result = {}

    if (api_id == "api_1") then
    # 1、輸出資料財運(財帛宮)、事業(官祿宮)、感情(夫妻宮)、運氣(命宮)、疾病(疾厄宮)共五項。吉凶分數
      @result = jianggong_api_1(h_par_org)
    elsif (api_id == "api_2") then
    # 2、農民曆資料輸出
      @result = jianggong_api_2(h_par_org)
      @result["final_success"] = false
    elsif (api_id == "api_3") then
    # 3、萬年曆查詢
      @result = jianggong_api_3(h_par_org)
    elsif (api_id == "api_4") then
      hUserDefData["CurrentDate"] = Cfate.ValueBoolCheck(h_par_org["CurrentDate"]) if h_par_org["CurrentDate"] != nil
      hUserDefData["days"] = h_par_org["days"].to_i if h_par_org["days"] != nil
      @result = jianggong_api_4(hUserInfo,hUserDefData)
    elsif (api_id == "api_5") then
      @result = jianggong_api_5(h_par_org)
    elsif (api_id == "api_b") then
      @result = jianggong_api_b(hUserInfo,hUserDefData)
    elsif (api_id == "api_2_days") then
      @result = gpc_api_2_days(h_par_org)
    end
    h_all = {}
    if (h_par_org["need_all"] == "1") then
      h_all = @oJg.jianggong_api_find_month2(h_par_org)
      m_udt = hUserDefData[Cfate::WMonth]
      if (h_all["m_#{m_udt}"] == {}) then
        MeenForum::BgWorker.perform_async("jianggong_api_all",hUserInfo,hUserDefData)
      else
        # 避免一直計算，呼叫5次後再算一次
        if (h_all["m_#{m_udt}"]["count"].to_i == 20) then
          h_y = @oJg.jianggong_api_find_year(hUserInfo,hUserDefData)
          h_y["m_#{m_udt}"] = {}
          @oJg.jianggong_api_save_year(hUserInfo,hUserDefData,h_y)
          h_all["m_#{m_udt}"] = {}
        elsif (h_all["m_#{m_udt}"]["count"].to_i > 0) then
          h_all["m_#{m_udt}"]["count"] = h_all["m_#{m_udt}"]["count"].to_i + 1
          h_y = @oJg.jianggong_api_find_year(hUserInfo,hUserDefData)
          h_y["m_#{m_udt}"] = h_all["m_#{m_udt}"]
          @oJg.jianggong_api_save_year(hUserInfo,hUserDefData,h_y)
          h_all = {}
        end
      end
    end
    if (h_par_org["need_all"] == "1") then
      @result["month_all"] = h_all
    end

    # 測字
    if (api_id == "api_11") then
      @result = gpc_cezi_simple(h_par_org)
    elsif (api_id == "api_12") then
      @result = gpc_xingmingxue_simple(h_par_org)
    elsif (api_id == "api_13") then
      @result = api_xingming_peidui_simple(h_par_org)
    elsif (api_id == "api_31") then
      # api_31 紫微相關api
      api_name = h_par_org["code"]
      h_result = {}
      func_name = "gpc_#{api_name}"
      # puts "#{"#{api_name}(h_par_org.clone,h_result.clone,remote_ip())"}"
      if (check_gpc_ziwei_api_legal(func_name)) then
        @result = eval("#{func_name}(h_par_org.clone,h_result.clone,remote_ip())")
        @result["final_success"] = true
      end
      # @result = gpc_ziwei_lunduan_benming_minggong(h_par_org.clone,h_result.clone,remote_ip())
    elsif (api_id == "api_41") then
      # api_41 交爻卦相關api
      api_name = h_par_org["code"]
      h_result = {}
      # puts "#{"#{api_name}(h_par_org.clone,h_result.clone,remote_ip())"}"
      if (Divination.check_liuyaogua_api_legal(api_name)) then
        @result = eval("gpc_liuyaogua_#{api_name}(h_par_org.clone,h_result.clone,remote_ip())")
        @result["final_success"] = true
      end
      # @result = gpc_ziwei_lunduan_benming_minggong(h_par_org.clone,h_result.clone,remote_ip())
    elsif (api_id == "api_51") then
      api_name = h_par_org["ask_no"]
      @result = gpc_pukwa(h_par_org)
    elsif (api_id == "api_52") then
      api_name = h_par_org["ask_no"]
      @result = gpc_flower(h_par_org)
    elsif (api_id == "api_5222") then
      @result = gpc_flower_gua(h_par_org)
      @result["final_success"] = false
    end

    if (api_id == "api_sti_1") then
      @result = gpc_sti_1(h_par_org)
    end

    @result["function_desc"] = gpc_function_name("jianggong",api_id,api_name)

    my_user_id = -101  # jianggong
    p_name = [api_id,api_name].join(":")
    p_type = @result["function_desc"]
    point = 1
    save_profate_payment2(my_user_id,p_name,p_type,point) if @result["final_success"]

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => h_par_org["callback"] }
    end
  end
  def sti_api
    h_par_org = params.clone
    api_id = h_par_org["api_id"]
    api_id = "api_1" if api_id == nil || api_id == ""
    @oTalent = Talent.new
    @ApFunc = nil
    @ParAll = nil
    @result = {}
    api_name = 1
    nPanType = h_par_org["pan_type"].to_i
    hUserInfo = Jianggong.get_user_info(h_par_org)
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserDefData["my_current_lang"] = my_current_lang()
    h_par_org["my_current_lang"] = hUserDefData["my_current_lang"]

    if (api_id == "api_1") then
      @result = sti_api_1(nPanType,hUserInfo,hUserDefData)
    elsif (api_id == "api_1_1") then
      nPanType = 2 if h_par_org["pan_type"] == nil
      hUserDefData["flow_rate"] = @oTalent.get_ts_rate(nPanType,h_par_org)
      @result = sti_api_1_1(nPanType,hUserInfo,hUserDefData)
    elsif (api_id == "api_1_2") then
      @result = sti_api_1_2(nPanType,hUserInfo,hUserDefData)
    elsif (api_id == "api_1_3") then
      @result = sti_api_1_3(nPanType,hUserInfo,hUserDefData)
    elsif (api_id == "api_1_4") then
      @result = sti_api_1_4(nPanType,hUserInfo,hUserDefData)
    elsif (api_id == "api_1_5") then
      @result = sti_api_1_5(nPanType,hUserInfo,hUserDefData)
    elsif (api_id == "api_1_6") then
      @result = sti_api_1_6(nPanType,hUserInfo,hUserDefData)
    end

    @result["function_desc"] = gpc_function_name("sti",api_id,api_name)

    if (api_id == "api_1_all") then
      nPanType = 2 if h_par_org["pan_type"] == nil
      hUserDefData["flow_rate"] = @oTalent.get_ts_rate(nPanType,h_par_org)
      hUserDefData["uuid"] = ""
      data = @oTalent.sti_api_all(nPanType,hUserInfo,hUserDefData)
      @result = @oTalent.sti_api_all_empty_result(hUserDefData["uuid"],h_par_org)
      @result["data"] = data
      @result["status"] = @oTalent.sti_api_remove_s(Talent::STI_UUID_FINISHED)
    elsif (api_id == "api_1_all_async") then
      # hUserDefData["flow_rate"] = @oTalent.get_ts_rate(nPanType,h_par_org)
      uuid = get_profateevent_unique_uuid()
      # MeenForum::BgWorker.perform_async("sti_api_all",nPanType,hUserInfo,hUserDefData)
      @result = @oTalent.sti_api_all_savedb_waiting(uuid,h_par_org)
      @result["status"] = @oTalent.sti_api_remove_s(Talent::STI_UUID_WAITING)
    elsif (api_id == "api_1_all_async_check") then
      uuid = h_par_org["uuid"]
      if uuid == nil then
        @result = {"status" => "NULL"}
      else
        @result = @oTalent.sti_api_all_check(uuid)
      end
    end
    if (api_id == "api_mp_1") then
      nPanType = 2 if h_par_org["pan_type"] == nil
      hUserDefData["flow_rate"] = @oTalent.get_ts_rate(nPanType,h_par_org)
      @result = sti_api_mp_1(nPanType,hUserInfo,hUserDefData)
    end

    my_user_id = -102  # sti_api
    p_name = [api_id,api_name].join(":")
    p_type = @result["function_desc"]
    point = 1
    save_profate_payment2(my_user_id,p_name,p_type,point) if @result["final_success"]

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => h_par_org["callback"] }
    end
  end

  # 廣藥集團api 九登
  def gpc_api
    h_par_org = params.clone
    api_id = h_par_org["api_id"]
    @oJg = Jianggong.new
    @ApFunc = nil
    @ParAll = nil
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserInfo = Jianggong.get_user_info(h_par_org)
    hUserDefData["my_current_lang"] = my_current_lang()
    h_par_org["my_current_lang"] = my_current_lang()
    @result = {}
    api_name = 1

    my_user_id = -103 # gpc_api for 春璽堂 姜公獻寶跟Sti

    # 姜公獻寶
    if (api_id == "api_1") then
    # 1、輸出資料財運(財帛宮)、事業(官祿宮)、感情(夫妻宮)、運氣(命宮)、疾病(疾厄宮)共五項。吉凶分數
      @result = jianggong_api_1(h_par_org)
    elsif (api_id == "api_2") then
    # 2、農民曆資料輸出
      @result = gpc_api_2(h_par_org)
      @result["final_success"] = false
    elsif (api_id == "api_3") then
    # 3、萬年曆查詢
      @result = jianggong_api_3(h_par_org)
    elsif (api_id == "api_4") then
      hUserDefData["CurrentDate"] = Cfate.ValueBoolCheck(h_par_org["CurrentDate"]) if h_par_org["CurrentDate"] != nil
      hUserDefData["days"] = h_par_org["days"].to_i if h_par_org["days"] != nil
      @result = gpc_api_4(hUserInfo,hUserDefData)
    elsif (api_id == "api_5") then
      @result = jianggong_api_5(h_par_org)
    elsif (api_id == "api_b") then
      @result = jianggong_api_b(hUserInfo,hUserDefData)
    elsif (api_id == "api_2_days") then
      @result = gpc_api_2_days(h_par_org)
    end

    # 測字
    if (api_id == "api_11") then
      my_user_id = -104 # gpc_api for 郁宏 測算
      @result = gpc_cezi_simple(h_par_org)
    elsif (api_id == "api_12") then
      my_user_id = -104 # gpc_api for 郁宏 測算
      @result = gpc_xingmingxue_simple(h_par_org)
    elsif (api_id == "api_13") then
      my_user_id = -104 # gpc_api for 郁宏 測算
      @result = api_xingming_peidui_simple(h_par_org)
    elsif (api_id == "api_31") then
      my_user_id = -104 # gpc_api for 郁宏 測算
      # api_31 紫微相關api
      api_name = h_par_org["code"]
      h_result = {}
      # puts "#{"#{api_name}(h_par_org.clone,h_result.clone,remote_ip())"}"
      if (check_gpc_ziwei_api_legal(api_name)) then
        @result = eval("#{api_name}(h_par_org.clone,h_result.clone,remote_ip())")
        @result["final_success"] = true
      end
      # @result = gpc_ziwei_lunduan_benming_minggong(h_par_org.clone,h_result.clone,remote_ip())
    elsif (api_id == "api_41") then
      my_user_id = -104 # gpc_api for 郁宏 測算
      # api_41 交爻卦相關api
      api_name = h_par_org["code"]
      h_result = {}
      # puts "#{"#{api_name}(h_par_org.clone,h_result.clone,remote_ip())"}"
      if (Divination.check_liuyaogua_api_legal(api_name)) then
        @result = eval("gpc_liuyaogua_#{api_name}(h_par_org.clone,h_result.clone,remote_ip())")
        @result["final_success"] = true
      end
      # @result = gpc_ziwei_lunduan_benming_minggong(h_par_org.clone,h_result.clone,remote_ip())
    elsif (api_id == "api_51") then
      my_user_id = -104 # gpc_api for 郁宏 測算
      api_name = h_par_org["ask_no"]
      @result = gpc_pukwa(h_par_org)
    elsif (api_id == "api_52") then
      my_user_id = -104 # gpc_api for 郁宏 測算
      api_name = h_par_org["ask_no"]
      @result = gpc_flower(h_par_org)
    elsif (api_id == "api_5222") then
      my_user_id = -104 # gpc_api for 郁宏 測算
      @result = gpc_flower_gua(h_par_org)
      @result["final_success"] = false
    end

    if (api_id == "api_sti_1_2") then
      @result = gpc_sti_api_1_2(h_par_org)
    elsif (api_id == "api_sti_1_3") then
      @result = gpc_sti_api_1_3(h_par_org)
    elsif (api_id == "api_sti_1_4") then
      @result = gpc_sti_api_1_4(h_par_org)
    end

    if (api_id == "api_sti_1") then
      my_user_id = -103 # gpc_api for 春璽堂 姜公獻寶跟Sti
      @result = gpc_sti_1(h_par_org)
    end

    @result["function_desc"] = gpc_function_name("gpc",api_id,api_name)

    p_name = [api_id,api_name].join(":")
    p_type = @result["function_desc"]
    point = 1
    save_profate_payment2(my_user_id,p_name,p_type,point) if @result["final_success"]

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => h_par_org["callback"] }
    end
  end

  protected
  # 才藝及學科API內容
  # 1.1.2  舞蹈律動等十二才藝指標
  def gpc_sti_api_1_2(h_par_org)
    trait_codes = ["MA01","MA02","MA03","MA04","MA05","MA06","MA07","MA08","MA09","MA11","MA12","MA13"]
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserInfo = Jianggong.get_user_info(h_par_org)
    hUserDefData["my_current_lang"] = my_current_lang()
    nPanType = h_par_org["pan_type"].to_i
    hUserDefData["trait_codes"] = trait_codes

    oTalent = Talent.new
    @Out = oTalent.gpc_sti_getdata_1_2(hUserInfo,hUserDefData)

    @Out["final_success"] = true

    return @Out
  end
  # 1.1.3 科系類別共有20項。
  def gpc_sti_api_1_3(h_par_org)
    trait_codes = ["MI01","MI02","MI03","MI04","MI05","MI06","MI07","MI08","MI09","MI10","MI11","MI12","MI13","MI14","MI15","MI16","MI17","MI18","MI19","MI20"]
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserInfo = Jianggong.get_user_info(h_par_org)
    hUserDefData["my_current_lang"] = my_current_lang()
    nPanType = h_par_org["pan_type"].to_i
    hUserDefData["trait_codes"] = trait_codes

    oTalent = Talent.new
    @Out = oTalent.gpc_sti_getdata_1_3(hUserInfo,hUserDefData)

    @Out["final_success"] = true

    return @Out
  end
  # 1.1.4  十二門課
  def gpc_sti_api_1_4(h_par_org)
    trait_codes = ["MS01","MS02","MS03","MS04","MS05","MS06","MS07","MS08","MS09","MS10","MS11","MS12"]
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserInfo = Jianggong.get_user_info(h_par_org)
    hUserDefData["my_current_lang"] = my_current_lang()
    nPanType = h_par_org["pan_type"].to_i
    hUserDefData["trait_codes"] = trait_codes

    oTalent = Talent.new
    @Out = oTalent.gpc_sti_getdata_1_4(hUserInfo,hUserDefData)

    @Out["final_success"] = true

    return @Out
  end

  def gpc_sti_1(h_par_org)
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserInfo = Jianggong.get_user_info(h_par_org)
    hUserDefData["my_current_lang"] = my_current_lang()
    nPanType = h_par_org["pan_type"].to_i
    # nPanType = 0 # 目前只提供本命

    # 親和力,適應力,注意力,求知慾,記憶能力,抽象概念,開拓嚐新,表達能力,領導才華
    # hUserDefData["trait_codes"] = ["A11","A20","A13","C31","C33","C36","D15","D51","D54"]
    # 親和力,耐心度,適應力,注意力,求知慾,學習效率,記憶能力,抽象概念,開拓嚐新,表達能力,領導才華
    hUserDefData["trait_codes"] = ["A11","A18","A20","A13","C31","C32","C33","C36","D15","D51","D54"]

    oTalent = Talent.new
    @Out = oTalent.gpc_getdata(nPanType,hUserInfo,hUserDefData)
  end
  def sti_api_1(nPanType,hUserInfo,hUserDefData)
    @Out = @oTalent.sti_api_1(nPanType,hUserInfo,hUserDefData)

    return @Out
  end
  # 主指標輸出
  def sti_api_1_1(nPanType,hUserInfo,hUserDefData)
    # trait_codes = ["A11","A12","A13","A16","A17","A18","A20","A22","A25","B21","B23","B24","B25","B26","B27","B28","C32","C33","C34","C36","C38","C39","C40","C41","D11","D13","D14","D15","D16","D51","D54","D55"]
    @Out = @oTalent.sti_api_1_1(nPanType,hUserInfo,hUserDefData)

    return @Out
  end
  # 大五人格
  def sti_api_1_2(nPanType,hUserInfo,hUserDefData)
    # trait_codes = ["MO11","MO12","MO13","MO14","MO15"]
    @Out = @oTalent.sti_api_1_2(nPanType,hUserInfo,hUserDefData)

    return @Out
  end
  # 1.3  C＋D霍蘭德六邊形
  def sti_api_1_3(nPanType,hUserInfo,hUserDefData)
    # trait_codes = ["MH01","MH02","MH03","MH04","MH05","MH06"]
    @Out = @oTalent.sti_api_1_3(nPanType,hUserInfo,hUserDefData)

    return @Out
  end
  # 1.4  E十二門課堂指標
  def sti_api_1_4(nPanType,hUserInfo,hUserDefData)
    # trait_codes = ["MS01","MS02","MS03","MS04","MS05","MS06","MS07","MS08","MS09","MS10","MS11","MS12"]
    @Out = @oTalent.sti_api_1_4(nPanType,hUserInfo,hUserDefData)

    return @Out
  end
  # 1.5  E十一門課堂指標
  def sti_api_1_5(nPanType,hUserInfo,hUserDefData)
    # trait_codes1 = ["L01","L02","L03","L04","L05","L06"]
    # trait_codes2 = ["MA01","MA02","MA03","MA04","MA05","MA06","MA07","MA08","MA09","MA11","MA12","MA13"]
    @Out = @oTalent.sti_api_1_5(nPanType,hUserInfo,hUserDefData)

    return @Out
  end
  # 1.6.1  G綜合應用指標
  # N71_業務型
  # N72_創業型
  # N73_幕僚型
  # N74_高管型
  # N75_研究型
  # N76_企劃型

  # 1.6.2  G綜合應用指標
  # MI01  心理、哲學
  # MI02  文學、史學
  # MI03  語言、文字
  # MI04  藝術、創作
  # MI05  教育、培訓
  # MI06  金融、經濟
  # MI07  經營、管理
  # MI08  司法、法學
  # MI09  科學、理學
  # MI10  製造、工業
  # MI11  畜牧、農林
  # MI12  行銷、商學
  # MI13  生物、醫學
  # MI14  營造、建築
  # MI15  社會、服務
  # MI16  休閒、觀光
  # MI17  影音、媒體
  # MI18  信息、通訊
  # MI19  政治、國際
  # MI20  物流、運輸  
  def sti_api_1_6(nPanType,hUserInfo,hUserDefData)
    # trait_codes1 = ["N71","N72","N73","N74","N75","N76"]
    # trait_codes2 = ["MI01","MI02","MI03","MI04","MI05","MI06","MI07","MI08","MI09","MI10","MI11","MI12","MI13","MI14","MI15","MI16","MI17","MI18","MI19","MI20"]
    @Out = @oTalent.sti_api_1_6(nPanType,hUserInfo,hUserDefData)

    return @Out
  end
  def sti_api_mp_1(nPanType,hUserInfo,hUserDefData)
    # nPanType = 2
    @Out = @oTalent.sti_api_mp_1(nPanType,hUserInfo,hUserDefData,true)

    return @Out
  end

  # 說明：
  # 1、輸出資料財運(財帛宮)、事業(官祿宮)、感情(夫妻宮)、運氣(命宮)、疾病(疾厄宮)共五項。
  # 2、營業規則：
      # 2.1 區分為生活版、專業版。
      # 2.2 生活版：
          # 2.2.1 財運、事業、感情以上三選一；運氣、疾病提供。
          # 2.2.2 提供到流時(十二時辰)分析。
      # 2.3 專業版：
          # 2.3.1 財運、事業、感情、運氣、疾病提供服務。
          # 2.3.2 提供到流分(十分鐘)分析
  # 
  # 3、客戶購買完成，將購買期間之資料存在姜公獻寶SERVER裡，流分盤不存。客戶要看流分盤(二小時)再訪問。
  # 4、資料呈現是國曆為主，以租用時間為週期。
  # 5、租用時間區分為月、半年、全年。
  # 6、吉凶分數最高+50 最低-50
  # 
  # 吉凶分析 需求說明：
  # 1、流月輸出內容 ---> 項目、國曆月日、星期、干支日、農曆月日、吉凶分數
  # 2、流日輸出內容 ---> 項目、時辰(03:00 ~ 05:00)、干支時、吉凶分數
  # 3、流時輸出內容 ---> 項目、時間(00:00 ~ 10:00)、吉凶分數
  def jianggong_api_1(h_par_org)
    # h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
    return @oJg.jianggong_api_1(h_par_org,@ApFunc,@ParAll)
  end


  public
  # ============================
  # 萬年曆查詢
  # 需求說明：
  # 2、農民曆資料輸出：
      # 國曆年月日、農曆年月日、(農曆)干支年(農曆年)干支月(節氣月)干支日、十二節氣、星期、沖煞、胎神、財神、喜神
      # sample :
      # 國曆年月日 : 
        # xili_year: 2020,
        # xili_month: 8,
        # xili_day: 8,
        # xili_date: "2020年8月8日",
        # xili_date_simple: "2020/8/8",

      # 農曆年月日 :
        # yinli_date: "庚子年六月十九日",
        # yinli_date_simple: "2020/6/19",

      # (農曆)干支年(農曆年)干支月(節氣月)干支日 : jieqili_date: "庚子年甲申月癸未日"
      # jieqi_ganzhi_year: "庚子",
      # jieqi_ganzhi_month: "甲申",
      # jieqi_ganzhi_day: "癸未",
      # 十二節氣 : jieqi: "立秋",
      # 星期 : week_day: "星期六"
      # 【沖煞】： 沖牛 (丁丑年24歲) 煞西 : chong_shi_er_sheng_xiao: "冲牛",chong_nian_ji: "(丁丑年24岁)",chong_zuo_xiang: "煞西",
      # 【胎神】： 房床廁外西北 : tai_shen: "房床厕外西北"
      # 【財神】： 正南 : cai_shen: "正南"
      # 【喜神】： 東南 : xi_shen: "东南"
  def jianggong_api_2(h_par_org)
    return @oJg.jianggong_api_farmercal(h_par_org)
  end
  def gpc_api_2(h_par_org)
    return @oJg.gpc_api_farmercal(h_par_org)
  end
  def gpc_api_2_days(h_par_org)
    if (h_par_org["start_year"] != nil) then
      start_year = h_par_org["start_year"].to_i
      stop_year = h_par_org["stop_year"].to_i
      return @oJg.gpc_api_farmercal_years(start_year,stop_year)
    end
    if (h_par_org["year"] != nil) then
      year = h_par_org["year"].to_i
      month = h_par_org["month"].to_i
      return @oJg.gpc_api_farmercal_month(year,month)
    end
    if h_par_org["days"] != nil && h_par_org["days"].to_i > 0 then
      return @oJg.gpc_api_farmercal_days(h_par_org)
    end
  return {}
  end
  protected

  public
  # 3、萬年曆查詢輸出：
      # 輸入 ==> 查詢國曆或農曆(輸入年月日時分)、出生地經緯度
      # 輸出 --> 國曆年月日時、農曆年月日時、真太陽時(年月日時分) 
      # 重慶 Xdate.api_rst_localtime_to_localsuntime(2020,3,5,0,35,106.97357,29.63438) [2020, 3, 4, 23, 31]
      # http://127.0.0.1:3000/api/v1/jianggong?api_id=api_3&longitude=106.97357&latitude=29.63438&timestamp=202003050035M
  # ============================
  def jianggong_api_3(h_par_org)
    return @oJg.jianggong_api_wannianli(h_par_org)
  end
  # 
  def jianggong_api_4(hUserInfo,hUserDefData)
    # h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
    return @oJg.jianggong_api_one_month_h(hUserInfo,hUserDefData,@ApFunc,@ParAll)
    # return @oJg.jianggong_api_one_month_all_house(hUserInfo,hUserDefData,@ApFunc,@ParAll)
  end
  def gpc_api_4(hUserInfo,hUserDefData)
    # h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
    return @oJg.gpc_api_one_month_h(hUserInfo,hUserDefData,@ApFunc,@ParAll)
    # return @oJg.jianggong_api_one_month_all_house(hUserInfo,hUserDefData,@ApFunc,@ParAll)
  end
  def jianggong_api_5(h_par_org)
    return @oJg.jianggong_api_oneday_allhosue_gixiong(h_par_org,@ApFunc,@ParAll)
  end
  def jianggong_api_b(hUserInfo,hUserDefData)
      return @oJg.jianggong_api_one_month_oneday_houses(hUserInfo,hUserDefData,@ApFunc,@ParAll)
  end    
  public
  # 各種api，計次收費
  def call
    if (api_id == "api_1") then
    # 1、輸出資料財運(財帛宮)、事業(官祿宮)、感情(夫妻宮)、運氣(命宮)、疾病(疾厄宮)共五項。吉凶分數
      call_api_1()
    elsif (api_id == "api_2") then
    # 2、萬年曆查詢
      call_api_2()
    end
  end
  protected
  def call_api_1()
  end
  def call_api_2()
  end
  def set_my_locale
    h_par_org = params.clone
    h_par_org["locale"] = Cfate::DEFAULT_LANG
    h_par_org["locale"] = params["locale"] if params["locale"]
    my_set_locale(h_par_org)
  end
end
