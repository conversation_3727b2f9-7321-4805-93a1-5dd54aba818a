require("Xdate.rb")
require("WeenApi.rb")
require("Pm.rb")

class Api::V1::ProfateOrdersController < ApplicationController
  protect_from_forgery with: :null_session, if: -> { request.format.json? }

  def finish_orders
    # buy points
    profate_orders = ProfateOrder.check_buy_point().check_complete().all.order('created_at DESC')
    a_buy_points = []
    profate_orders.each do |profate_order|
      h = Hash.new
      h["user_id"] = profate_order.user_id
      h["billing_id"] = profate_order.billing_order_id
      h["p_name"] = profate_order.p_name
      h["amount"] = profate_order.amount
      h["point"] = profate_order.point
      h["created_at"] = profate_order.created_at
      order = Order.find_by_id(profate_order.billing_order_id)
      h["payment_type"] = order.payment_type
      h["client_ip"] = order.client_ip
      h["reopen_url"] = ""

      a_buy_points.push(h)
    end

    # buy events
    profate_orders = ProfateOrder.check_buy_event().check_complete().all.order('created_at DESC')
    a_buy_events = []
    profate_orders.each do |profate_order|
      h = Hash.new
      h["user_id"] = profate_order.user_id
      h["billing_id"] = profate_order.billing_order_id
      h["p_name"] = profate_order.p_name
      h["amount"] = profate_order.amount
      h["point"] = profate_order.point
      h["created_at"] = profate_order.created_at
      order = Order.find_by_id(profate_order.billing_order_id)
      h["payment_type"] = order.payment_type
      h["client_ip"] = order.client_ip
      h["reopen_url"] = repoen_url(order.receive_url)

      a_buy_events.push(h)
    end

    render json: {"points" => a_buy_points, "events" => a_buy_events}
    # render nothing: true
  end

  def index
  end
  def show
  end
  def create
  end
  def update
  end
  def destroy
  end
  private
  def repoen_url(receive_url)
    # https://ween.tw:443/#/stars/3FD2690F8AE54AD89663265AD4FB9B02745C8756035B43F8AD47F82CA48BDD05
    n = receive_url.index("//")
    if (n == nil) then
      return receive_url
    end
    n = receive_url.index(":",n)
    if (n == nil) then
      return receive_url
    end
    m = receive_url.index("/",n)
    s = receive_url[n,m - n]
    s1 = receive_url.sub(s,"")
    return s1
  end
end
