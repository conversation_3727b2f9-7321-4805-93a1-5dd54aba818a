require("Xdate.rb")
require("WeenApi.rb")

class Api::V1::ProfateFreeController < ApplicationController
  include Controller_Api
  protect_from_forgery with: :null_session, if: -> { request.format.json? }
  respond_to :json

  def ziwei_shinian_dayun
    h,h,h_par = api_ziwei_shinian_dayun(params,{},remote_ip())
    render json: h
  end

  # 陳明德 八字
  def bazhi_1
    # 1.要判斷對方url ip
    user_ip = remote_ip()
    # if (user_ip != "**********") then
    #   h = {"data" : "no data"}
    #   render json: h
    # else
      # 2.要記錄使用次數 未做
      # if request.referer != nil && URI(request.referer).path == '/adsense' then
        # puts "refer: #{URI(request.referer).path}"
        h_par = params.clone
        # 先固定兩個盤，讓他們寫好之後，再開放完整功能，再加上referer判斷及記錄使用狀況
        # h_par["timestamp"] = ["2000020310M","L02000030512F"].sample
        h_par["timestamp"] = ["2000020310M"].sample
        par_all = {Eightword::PAR_FIRST_TIME_TYPE => Xdate::FIRST_CURRENT}
        h,h,h_par = api_bazhi_pan(h_par,{},remote_ip(),par_all)
        render json: h
      # else
      #   render json: {}
      # end
    # end
  end
end
