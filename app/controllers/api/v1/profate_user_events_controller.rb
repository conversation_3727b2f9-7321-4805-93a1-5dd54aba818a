require("Xdate.rb")
require("WeenApi.rb")
require("Controller_Api.rb")

class Api::V1::ProfateUserEventsController < ApplicationController
  include Controller_Api
  # devise_token_auth_group :member, contains: [:admin, :profate_client] #把四類人歸類成member
  # before_action :authenticate_member!, :except => [:members_test] #檢查是否為member
  before_action :set_api_v1_events, only: [:show, :update, :destroy]
  protect_from_forgery with: :null_session, if: -> { request.format.json? }
  respond_to :json

  def index
    # @api_v1_events = ProfateEvent.where(:user_id => current_user.id, :event_name => params[:event_name])
    event_name = params["event_name"]
    if (event_name == nil) then
      event_name = "ziwei"
    end
    @api_v1_events = ProfateEvent.where(:user_id => 1, :event_name => event_name)
    if @api_v1_events.nil?
      render nothing: true
    else
      render json: @api_v1_events
    end
  end

  # GET /api/v1/profate_events/1
  # GET /api/v1/profate_events/1.json
  def show
    if (@api_v1_events == nil) then
      render json: {:status => "error"} #, status: :unprocessable_entity
    else
      h = Hash.new
      h["params"] = @api_v1_events.hParam

      if (@api_v1_events.result == nil) then
        if (@api_v1_events.param != nil) then
          api_name = @api_v1_events.api_name
          h_par = @api_v1_events.hParam
          @api_v1_events.hResult = eval("#{api_name}(h_par,remote_ip())")
          @api_v1_events.save!
        end
      end
      h["result"] = @api_v1_events.hResult
      h["event"] = @api_v1_events.as_json
      # h["event"]["profate_event"]["result"] = ""
      render json: h
      # render json: @api_v1_events
      # render json: nil,status: :bad_request # 400 Bad Request for header status code in html
    end
  end

  # POST /api/v1/profate_events
  # POST /api/v1/profate_events.json
  def create
    user_id = 1
    @api_v1_events,user_point,coupon = api_v1_events_params_create_check(user_id,remote_ip(),api_v1_events_params)

    if (@api_v1_events != nil) then
      if @api_v1_events.save then
        if (user_point != nil) then
          user_point.save
        end
        if (coupon != nil) then
          coupon.save
        end
        if (@api_v1_events.flag == 1) then # 未付款
          # 找出以哪一筆 point_product來付款
          point_product = find_point_product(@api_v1_events.event_name,@api_v1_events.point_used)
          # 準備付款相關資料
          # 建立order的所有資料庫，回傳你背景的url,需要傳給歐付寶的參數
          h = profate_order_get_data_from_ween_billing(user_id,@api_v1_events,point_product,remote_ip(),params)
          h["status"] = "pending"
          render json: h
          # render :html => res
          # respond_to do |format|
            # format.html { render :html => res }
            # format.json { head :ok }
          # end
        else
          # render json: @api_v1_events, status: :created #, location: @api_v1_events
          h = Hash.new
          h["uuid"] = @api_v1_events.uuid
          h["allpay_aiocheckout_server"] = ""
          h["allpay"] = Hash.new
          # h["auto_post_html"] = ""
          h["status"] = "received"
          render json: h, status: :created #, location: @api_v1_events
        end
      else
        render json: {:status => "error"} #, status: :unprocessable_entity
        # render json: @api_v1_events.errors, status: :unprocessable_entity
      end
    else
      render json: {:status => "error"} #, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /api/v1/profate_events/1
  # PATCH/PUT /api/v1/profate_events/1.json
  def update
    if (@api_v1_events == nil) then
      render json: {:status => "error"} #, status: :unprocessable_entity
    else
      if (@api_v1_events.can_update_count > 0) then
        @api_v1_events.can_update_count -= 1
        h = api_v1_events_params
        h_par = combine_user_and_server_param(@api_v1_events.hParam,h["param"],@api_v1_events.event_point_id)
        @api_v1_events.hParam = h_par
        @api_v1_events.hResult = nil
        if @api_v1_events.save! then
          # head :no_content
          render json: {:status => "received"}
        else
          render json: @api_v1_events.errors, status: :unprocessable_entity
        end
      else
        render json: @api_v1_events.errors, status: :unprocessable_entity
      end
    end
  end

  # DELETE /api/v1/profate_events/1
  # DELETE /api/v1/profate_events/1.json
  def destroy
    if (@api_v1_events == nil) then
      render json: {:status => "error"}
    else
      @api_v1_events.destroy

      render json: {:status => "received"}
      # head :no_content
    end
  end

  private

    def set_api_v1_events
      h = params #api_v1_events_params
      # @api_v1_events = ProfateEvent.find(h[:id])
      # @api_v1_events = ProfateEvent.where(:id => h[:id], :user_id => h[:user_id])
      @api_v1_events = ProfateEvent.check_uuid(h["id"]).last
      # 等 profate_clients 好了再用底下的
      # if (@api_v1_events.user_id != 1) then
      # # if (@api_v1_events.user_id != current_user.id) then
      #   @api_v1_events = nil
      # end
    end

  # hash params 函 api_v1_events 為 key 之 hash 內有 ： id ,user_id, event_point_id, need_point, event_name param: 傳 hash 上來
  # api_name: 不傳上來，create時由 profate_event_points table 讀取
  # can_update_count ： 不傳create時由 profate_event_points table 讀取；每update一次param 減 1
  # can_update_count : n > 0: count ; 99999999 : always ; 0 : can't update ; 預設為1(可改一次，包含create)
  # point_used,free_point_used 根據傳入的 need_point 計算，原則上扣除客戶point_used,free_point_used各一半，若其一不足，才從另一個扣除其餘的
  def api_v1_events_params
    # params[:api_v1_events]
    params
  end

end
