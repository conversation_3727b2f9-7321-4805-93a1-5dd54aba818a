class Api::V1::ProfatePointProductsController < ApplicationController
  # 列出點數產品清單
  before_action :set_coupon, only: [:show, :update, :destroy]
  protect_from_forgery with: :null_session, if: -> { request.format.json? }
  respond_to :json

  # 購買點數
  def buy
    if (@api_v1_user_points == nil) then
      render json: nil, status: :unprocessable_entity
    else
      point_product = ProfatePointProduct.where(:id => h["id"], :amount => h["amount"].to_i)
      # 準備付款相關資料
      # 建立order的所有資料庫，回傳你背景的url,需要傳給歐付寶的參數
      h = profate_order_get_data_from_ween_billing(params["user_id"],nil,point_product,remote_ip(),params)
      render json: h
    end
  end
  def index
    @point_products = ProfatePointProduct.check_type("buy").all
    if @point_products.nil?
      render json: [{status: "no data"}]
    else
      render json: @point_products if @point_products.present?
    end
  end

  # GET /api/v1/profate_events/1
  # GET /api/v1/profate_events/1.json
  def show
    if @point_product.nil?
      render nothing: true
    else
      render json: @point_product if @point_product.present?
    end
  end

  # POST /api/v1/profate_events
  # POST /api/v1/profate_events.json
  def create
    @point_product = ProfatePointProduct.new(point_products_params)

    if @point_product.save
      render json: @point_product, status: :created, location: point_product_path(@point_product)
    else
      render json: @point_product.errors, status: :unprocessable_entity
    end
  end
  # PATCH/PUT /api/v1/profate_events/1
  # PATCH/PUT /api/v1/profate_events/1.json
  def update
    if @point_product.update(point_product_params)
      head :no_content
    else
      render json: @point_product.errors, status: :unprocessable_entity
    end
  end

  # DELETE /api/v1/profate_events/1
  # DELETE /api/v1/profate_events/1.json
  def destroy
    @point_product.destroy

    head :no_content
  end

  private

  # 先判斷是否為event_name（因為event_name是英文，不會跟id重覆）
  # 若不是，再當成id來抓
  def set_point_product
    # event_name
    @point_product = ProfatePointProduct.find(params["id"])
  end

  def point_product_params
    params.require(:profate_point_products).permit(:p_name, :p_type, :p_desc, :amount, :point, :free_coupon, :start_date, :end_date, :desc_url, :status)
  end

end
