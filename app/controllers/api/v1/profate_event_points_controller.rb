class Api::V1::ProfateEventPointsController < ApplicationController
  before_action :set_event_point, only: [:show, :update, :destroy]
  protect_from_forgery with: :null_session, if: -> { request.format.json? }
  respond_to :json

  def index
    event_name_like = params["event_name"]
    if (event_name_like == nil || event_name_like == "") then
      @event_points = ProfateEventPoint.need_amount.all
    else
      # @event_points = ProfateEventPoint.like_event_name(event_name_like).need_amount.all
      @event_points = ProfateEventPoint.like_event_name(event_name_like).all
    end
    if @event_points.nil?
      render json: [{status: "no data"}]
    else
      render json: @event_points if @event_points.present?
    end
  end

  # GET /api/v1/profate_events/1
  # GET /api/v1/profate_events/1.json
  def show
    if @event_point.nil?
      render nothing: true
    else
      @h_event_point = @event_point.serializable_hash
      @h_event_point.delete("param")
      @h_event_point["param"] = @event_point.hParam
      @h_event_point.delete("created_at")
      @h_event_point.delete("updated_at")
      render json: @h_event_point
      # render json: @event_point if @event_point.present?
    end
  end

  # POST /api/v1/profate_events
  # POST /api/v1/profate_events.json
  def create
    # @event_point = ProfateEventPoint.new(event_points_params)

    # if @event_point.save
    #   render json: @event_point, status: :created, location: event_point_path(@event_point)
    # else
    #   render json: @event_point.errors, status: :unprocessable_entity
    # end
  end
  # PATCH/PUT /api/v1/profate_events/1
  # PATCH/PUT /api/v1/profate_events/1.json
  def update
    # @event_point = ProfateEventPoint.find(params[:id])

    # if @event_point.update(event_point_params)
    #   head :no_content
    # else
    #   render json: @event_point.errors, status: :unprocessable_entity
    # end
  end

  # DELETE /api/v1/profate_events/1
  # DELETE /api/v1/profate_events/1.json
  def destroy
    # @event_point.destroy

    # head :no_content
  end

  private

  # 先判斷是否為event_name（因為event_name是英文，不會跟id重覆）
  # 若不是，再當成id來抓
  def set_event_point
    # event_name
    if ("#{params["id"].to_i}" == params["id"]) then
      @event_point = ProfateEventPoint.find(params["id"].to_i)
    else
      @event_point = ProfateEventPoint.check_event_name(params["id"]).last
    end
    # @event_point = ProfateEventPoint.check_event_name(params["id"]).last
    # if (@event_point == nil) then
    #   @event_point = ProfateEventPoint.find(params["id"].to_i)
    # end
  end

  def event_point_params
    params.require(:profate_event_points).permit(:event_name, :api_name, :need_point, :can_update_count, :desc)
  end

end
