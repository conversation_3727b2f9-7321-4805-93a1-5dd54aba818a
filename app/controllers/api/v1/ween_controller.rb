require("Pm.rb")
require("Xdate.rb")
require("WeenApi.rb")
require("Controller_Api.rb")
require("Health.rb")
require("Score.rb")

class Api::V1::WeenController < ApplicationController
  include Controller_Api
  # before_action :authenticate_user!
  before_action :set_my_locale, :only => [:sh_api_gixiongscore,:hsieh_api_ziwei_pan,:hsieh_api_ziwei_lunduan,:huochui_api_ziwei_lunduan,:huochui_api_ziwei_lunduan_12,:huochui_api_ziwei_pan,:huochui_api_gnum]
  protect_from_forgery with: :null_session, if: -> { request.format.json? }
  respond_to :json

  # def authenticate
  #   @api_key = params["key"]
  #   legal = true
  #   if (@api_key == nil) then
  #     legal = false
  #     # redirect_to "/", notice: 'invalid'
  #   else
  #     ak = ApiKey.check_key(@api_key).count_avail.first
  #     if (ak == nil) then
  #       legal = false
  #       # redirect_to "/", notice: 'invalid'
  #     else
  #       ak.used_count = ak.used_count + 1
  #       ak.save!
  #       a = ApiDetails.new
  #       a.api_key_id = ak.id
  #       a.controller = params[:controller]
  #       a.action = params[:action]
  #       a.caller_ip = remote_ip()
  #       a.hAll = params
  #       a.save!
  #     end
  #   end
  #   # api_key table : id, key, password, start_date,end_date,avail_count,used_count
  #   # if @api_id illegal in api_key table , return failed
  #   if (!legal) then
  #     # respond_with Hash.new
  #     # 未經授權存取，回覆正規http status
  #     respond_with do |format|
  #       format.text { render :text=> "unauthorized", status: 401 }
  #       # format.json { render :text=> "unauthorized", status: 401 }
  #       format.html { render :text=> "unauthorized", status: 401 }
  #       format.any(:js, :json){ render :text => "unauthorized", :callback => params[:callback], status: 401 }
  #       format.xml { render :text=> "<result>unauthorized</result>", status: 401 }
  #     end
  #   end
  # end

  # 臨時改出來給HRT用的
  def fourhua
    hUserDefData = ween_get_cur_date_info(params)
    hUserInfo = ween_get_user_info(params)
    oWeenApi = WeenApi.new
    nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"].to_i
    @ApFunc = nil
    @ParAll = nil
    @result = oWeenApi.g_GetFourHuaInfo(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end

  def panscore
    hUserDefData = ween_get_cur_date_info(params)
    hUserInfo = ween_get_user_info(params)
    # oWeenApi = WeenApi.new
    nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"].to_i
    # if (nPanType == Cfate::PAN_NORMAL) then
    #   nPanType = Cfate::PAN_TENYEAR
    # end
    @ApFunc = nil
    @ParAll = {}
    @ParAll[Star::PAR_FLOWYEAR_HUA] = Star::PAN_TYPE_FLOW
    @ParAll[Star::PAR_LU_TYPE] = Star::PAN_LU_CALD
    @ParAll[Star::DIS_FLOWLYT] = Star::STAR_DIS_FLOW_DIE
    hUserDefData["ParAll"] = @ParAll

    ps = Score.new
    # @Score = ps.ps_getScore(Pm::AP_STAR,Cfate::PAN_NORMAL,hUserInfo,hUserDefData,true)
    @Score = ps.ps_getScore(Pm::AP_STAR,nPanType,hUserInfo,hUserDefData,true)
    oStar = Star.new
    pan = oStar.wa_Pan_for_score(nPanType,hUserInfo,hUserDefData,@ParAll)
    # @Score = oStar.wa_Pan_for_score(nPanType,hUserInfo,hUserDefData,@ParAll)
    # @Score = oStar.wa_Pan(nPanType,hUserInfo,hUserDefData,@ParAll)
    @Score = ps.ps_Star_getScore_20180606(@Score,pan,oStar,nPanType)

    # JSONP format
    respond_with(@Score) do |format|
      format.any(:js, :json) { render :json => @Score, :callback => params["callback"] }
    end
  end


  def answer
    hUserDefData = ween_get_cur_date_info(params)
    hUserInfo = ween_get_user_info(params)
    oWeenApi = WeenApi.new
    nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"].to_i
    @ApFunc = nil
    @ParAll = nil
    @result = oWeenApi.g_GetInfo(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end
  def ziwei_pan
    hUserDefData = ween_get_cur_date_info(params)
    hUserInfo = ween_get_user_info(params)
    oWeenApi = WeenApi.new
    nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"].to_i
    @ApFunc = nil
    @ParAll = nil
    @result = oWeenApi.ziwei_pan(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end
  # 從十年開始
  def gixiongscore
    hUserDefData = ween_get_cur_date_info(params)
    hUserInfo = ween_get_user_info(params)
    oWeenApi = WeenApi.new
    nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"].to_i
    if (nPanType == Cfate::PAN_NORMAL) then
      nPanType = Cfate::PAN_TENYEAR
    end
    @ApFunc = nil
    @ParAll = nil
    @result = oWeenApi.g_fe_score(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end
  def gixiongtext
    hUserDefData = ween_get_cur_date_info(params)
    hUserInfo = ween_get_user_info(params)
    oWeenApi = WeenApi.new
    nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"].to_i
    if (nPanType == Cfate::PAN_NORMAL) then
      nPanType = Cfate::PAN_TENYEAR
    end
    @ApFunc = nil
    @ParAll = nil
    @result = oWeenApi.g_explode_text(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll,true)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end
  def feeling
    hUserDefData = ween_get_cur_date_info(params)
    hUserInfo = ween_get_user_info(params)
    oWeenApi = WeenApi.new
    nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"].to_i
    @ApFunc = nil
    @ParAll = nil
    @result = oWeenApi.g_feeling(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end
  # 從十年開始
  def explode
    hUserDefData = ween_get_cur_date_info(params)
    hUserInfo = ween_get_user_info(params)
    oWeenApi = WeenApi.new
    nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"].to_i
    if (nPanType == Cfate::PAN_NORMAL) then
      nPanType = Cfate::PAN_TENYEAR
    end
    @ApFunc = nil
    @ParAll = nil
    @result = oWeenApi.g_explode(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end
  def profate_lunduan
    hUserDefData = ween_get_cur_date_info(params)
    hUserInfo = ween_get_user_info(params)
    oWeenApi = WeenApi.new
    nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"].to_i
    if (nPanType == Cfate::PAN_NORMAL) then
      nPanType = Cfate::PAN_TENYEAR
    end
    @ApFunc = nil
    @ParAll = nil
    @result = oWeenApi.g_profate_lunduan(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end
  protected
  def remote_ip()
    return request.remote_ip.to_s
    # return "yahoo.com"
  end

  def ween_get_cur_date_info(h)
    y,m,d,hour,min,l,dt = h["uy"],h["um"],h["ud"],h["uh"],h["umin"],h["ul"],h["udt"]
    hUserDefData = Hash.new
    if (y != nil) then
      if (dt.to_i == 0) then # solar
        y,m,d,l = Xdate.West2East(y.to_i, m.to_i, d.to_i)
      else
        l = (l.to_i != 0)
      end
      hUserDefData[Cfate::EYear] = y.to_i
      hUserDefData[Cfate::EMonth] = m.to_i
      hUserDefData[Cfate::EDate] = d.to_i
      hUserDefData[Cfate::LeapMonth] = l
      hUserDefData[Cfate::WHour] = hour.to_i
      hUserDefData[Cfate::WMinute] = min.to_i
    else
      nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
      hUserDefData[Cfate::EYear] = nEYear
      hUserDefData[Cfate::EMonth] = nEMonth
      hUserDefData[Cfate::EDate] = nEDate
      hUserDefData[Cfate::LeapMonth] = bLeapMonth
      hUserDefData[Cfate::WHour] = nWHour
      hUserDefData[Cfate::WMinute] = nWMin
    end
    hUserDefData[Star::HOUSE_NAME] = params["house"] == nil ? Star::EX_HOUSE_ALL : params["house"].to_i

    return hUserDefData
  end

  def ween_get_user_info(h)
    y,m,d,hour,min,l,dt = h["y"],h["m"],h["d"],h["h"],h["min"],h["l"],h["dt"]
    hUserInfo = Hash.new
    if (y != nil) then
      if (dt.to_i == 0) then # solar
        hUserInfo[Cfate::WYear] = y.to_i
        hUserInfo[Cfate::WMonth] = m.to_i
        hUserInfo[Cfate::WDate] = d.to_i
      else # lunar
        nWYear,nWMonth,nWDate = Xdate.East2West(y.to_i, m.to_i, d.to_i, l.to_i != 0)
        hUserInfo[Cfate::WYear] = nWYear
        hUserInfo[Cfate::WMonth] = nWMonth
        hUserInfo[Cfate::WDate] = nWDate
      end
      hUserInfo[Cfate::WHour] = hour.to_i
      hUserInfo[Cfate::WMinute] = min.to_i
    # puts h["sex"]
      hUserInfo[Cfate::Sex] = h["sex"] == "1" ? false : true  # sex : 1 ±íÊ¾ÄÐÉú
      hUserInfo[Cfate::Name] = h["name"]
    else
      nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
      hUserInfo[Cfate::WYear] = nWYear
      hUserInfo[Cfate::WMonth] = nWMonth
      hUserInfo[Cfate::WDate] = nWDate
      hUserInfo[Cfate::WHour] = nWHour
      hUserInfo[Cfate::WMinute] = nWMin
      hUserInfo[Cfate::Sex] = false
      hUserInfo[Cfate::Name] = "name"
    end

    timestamp = h["timestamp"]
    if (timestamp != nil) then
      y,m,d,hour,sex,min,mb = Xdate.parse_timestamp(timestamp)
      hUserInfo[Cfate::WYear] = y
      hUserInfo[Cfate::WMonth] = m
      hUserInfo[Cfate::WDate] = d
      hUserInfo[Cfate::WHour] = hour
      hUserInfo[Cfate::WMinute] = min
      hUserInfo[Cfate::Sex] = sex == 1 ? false : true 
      hUserInfo[Cfate::Name] = h["name"] == nil ? timestamp : h["name"]
      hUserInfo[Cfate::Multiple_births] = mb
      # y,m,d = timestamp[0,4].to_i,timestamp[4,2].to_i,timestamp[6,2].to_i
      # if (timestamp.length == 10) then
        # hUserInfo[Cfate::WHour] = timestamp[8,2].to_i
      # end
      # if (dt == nil || dt.to_i == 0) then # solar
      #   hUserInfo[Cfate::WYear] = y.to_i
      #   hUserInfo[Cfate::WMonth] = m.to_i
      #   hUserInfo[Cfate::WDate] = d.to_i
      # else # lunar
      #   l = 0
      #   nWYear,nWMonth,nWDate = Xdate.East2West(y.to_i, m.to_i, d.to_i, l.to_i != 0)
      #   hUserInfo[Cfate::WYear] = nWYear
      #   hUserInfo[Cfate::WMonth] = nWMonth
      #   hUserInfo[Cfate::WDate] = nWDate
      # end
    end
    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(hUserInfo[Cfate::WYear],hUserInfo[Cfate::WMonth],hUserInfo[Cfate::WDate])
    hUserInfo[Cfate::EYear] = nEYear
    hUserInfo[Cfate::EMonth] = nEMonth
    hUserInfo[Cfate::EDate] = nEDate
    hUserInfo[Cfate::LeapMonth] = bLeapMonth

    return hUserInfo
  end

  public
  def gnum
    word = params["word"]
    land = params["land"]
    if (land == "-1") then
      land = nil
    end
    m = params["minute"]
    if (m == "-1") then
      m = nil
    end
    thing = params["thing"]

    oWeenApi = WeenApi.new
    @result = oWeenApi.g_gnum(word,land,m,thing)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end

  def name
    s = Pm.GetStrWithColon("explain.star.title.house")
    surname = s[0,2]
    txtname = s[2,2]
    sex = "M"
    surname = params["sname"]
    txtname = params["tname"]
    sex = params["sex"]
    s=nil
    t=nil
    lang = my_current_lang()

    oWeenApi = WeenApi.new
    @result = oWeenApi.g_name(surname,txtname,sex,s,t,lang)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end
  def namepair
    s = Pm.GetStrWithColon("explain.star.title.house")
    surname = s[0,1]
    txtname = s[2,1]
    s = Pm.GetStrWithColon("explain.star.title.main")
    psurname = s[0,1]
    ptxtname = s[2,1]
    sex = "man"
    surname = params["sname"]
    txtname = params["tname"]
    sex = params["sex"]
    psurname = params["psname"]
    ptxtname = params["ptname"]
    s=nil
    t=nil
    lang = my_current_lang()

    oWeenApi = WeenApi.new
    @result = oWeenApi.g_namepair(surname,txtname,sex,psurname,ptxtname,s,t,lang)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end

  def farmercal
    hData = ween_get_user_info(params)
    oWeenApi = WeenApi.new
    @result = oWeenApi.g_farmercal(hData)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
    # Http Cache-Control
    # expires_in Time.now.seconds_until_end_of_day, public: true
    # fresh_when etag: @result
  end

  def ganzhi
    hData = ween_get_user_info(params)
    oWeenApi = WeenApi.new
    @result = oWeenApi.g_ganzhi(hData)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end

    # Http Cache-Control
    expires_in (Time.now.end_of_hour - Time.now), public: true
    fresh_when etag: @result
  end

  def pickdays
    hUserDefData = ween_get_cur_date_info(params)
    hUserDefData[Cfate::WHour] = 0
    hUserDefData[Cfate::WMinute] = 0
    hUserInfo = ween_get_user_info(params)
    if (params["pickdays"] != nil) then
      hUserDefData[Cfate::PickDays] = params["pickdays"].to_i
    end
    oWeenApi = WeenApi.new
    @ApFunc = nil
    @ParAll = nil
    @result = oWeenApi.g_pick_days(hUserInfo,hUserDefData,@ApFunc,@ParAll,false)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end

  def pugua_pan
    gua_bie = params["gua_bie"]
    if (gua_bie == nil) then
      gua_bie = Divination::Jiankang
    end
    liuyaogua_pan(params,gua_bie)
  end
  def liuyaogua_pan(hPar,gua_bie)
    hData = ween_get_user_info(params)
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
    nWYear,nWMonth,nWDate,nWHour,nWMin = hData[Cfate::WYear],hData[Cfate::WMonth],hData[Cfate::WDate],hData[Cfate::WHour],hData[Cfate::WMinute]
    year = hPar["uy"] != nil ? hPar["uy"].to_i : nWYear
    month = hPar["um"] != nil ? hPar["um"].to_i : nWMonth
    day = hPar["ud"] != nil ? hPar["ud"].to_i : nWDate
    hour = hPar["uh"] != nil ? hPar["uh"].to_i : nWHour
    name = hPar["name"]
    question = hPar["question"]
    if (hPar["wai"] != nil) then
      wai = hPar["wai"].to_i
    else
      wai = hPar["up"].to_i
    end
    if (hPar["nei"] != nil) then
      nei = hPar["nei"].to_i
    else
      nei = hPar["down"].to_i
    end
    if (hPar["bian"] != nil) then
      bian = hPar["bian"].to_i
    else
      bian = hPar["change"].to_i
    end
    hoUserAp = g_getUserCurrentAp(nil,"liuyaogua")    
    hApFunc = g_ap_product_allfunc_hash(hoUserAp)
    hParAll = Liuyaogua.get_pan_pars(nil,g_can_use_pars(hoUserAp))
    oWeenApi = WeenApi.new
    @result = oWeenApi.g_pugua_pan(wai,nei,bian,year,month,day,hour,name,question,gua_bie,hParAll,hApFunc)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end

  def pugua
    gua_bie = params["gua_bie"]
    if (gua_bie == nil) then
      gua_bie = Divination::Jiankang
    end
    liuyaogua(params,gua_bie)
  end
  def liuyaogua(hPar,gua_bie)
    hData = ween_get_user_info(params)
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
    nWYear,nWMonth,nWDate,nWHour,nWMin = hData[Cfate::WYear],hData[Cfate::WMonth],hData[Cfate::WDate],hData[Cfate::WHour],hData[Cfate::WMinute]
    year = hPar["uy"] != nil ? hPar["uy"].to_i : nWYear
    month = hPar["um"] != nil ? hPar["um"].to_i : nWMonth
    day = hPar["ud"] != nil ? hPar["ud"].to_i : nWDate
    hour = hPar["uh"] != nil ? hPar["uh"].to_i : nWHour
    name = hPar["name"]
    question = hPar["question"]
    if (hPar["wai"] != nil) then
      wai = hPar["wai"].to_i
    else
      wai = hPar["up"].to_i
    end
    if (hPar["nei"] != nil) then
      nei = hPar["nei"].to_i
    else
      nei = hPar["down"].to_i
    end
    if (hPar["bian"] != nil) then
      bian = hPar["bian"].to_i
    else
      bian = hPar["change"].to_i
    end
    oWeenApi = WeenApi.new
    @result = oWeenApi.g_pugua(wai,nei,bian,year,month,day,hour,name,question,gua_bie)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end
  def liuyaogua_jiankang
    liuyaogua(params,Divination::Jiankang)
  end
  def liuyaogua_qiucai
    liuyaogua(params,Divination::Qiucai)
  end
  def liuyaogua_nuweihun
    liuyaogua(params,Divination::Nuweihun)
  end
  def liuyaogua_nuyihun
    liuyaogua(params,Divination::Nuyihun)
  end
  def liuyaogua_nanweihun
    liuyaogua(params,Divination::Nanweihun)
  end
  def liuyaogua_nanyihun
    liuyaogua(params,Divination::Nanyihun)
  end
  def liuyaogua_zhichang
    liuyaogua(params,Divination::ZhiChang)
  end

  # y,m,d,h : 搜尋干支日期的起始點；年(例1982)，月，日，時
  # l : 是否潤月，1表示潤月；0表示非潤月。陰曆時用(dt == 1)
  # dt : 日期格式，0是陽曆；1是陰曆
  # gan_zhis : 天干跟地支的字串組合 => [[t1,d1],[t2,d2],...,[tn,dn]]
  # t is tiangan : 想要搜尋的天干，例 0 代表不限天干，1 代表甲，2 代表乙,...，10代表癸
  # d is dizhi : 地支，0 代表不限地支，1 代表 子， 2 代表 丑，...，12 代表 亥
  # 例：只要地支 子 寅 辰 午 時，傳入 [[0,1],[0,3],[0,5],[0,7]]
  # duixiang : 搜尋的對象， y:年,m:月,d:日,h:時
  # count : 需回傳的日期數量
  # lifa : 曆法(年，月時沒差，日，時有差）； jieqi => 節氣曆； yinli => 陰曆
  # 回傳
  # [{干支年:"甲子",干支月:"丙寅",干支日:"戊辰",干支時:"庚午",年:"2015",月:"12",日:"31",時:"11~13",是否假日:"0"},{},...]
  def ganzhi_riqi
    hData = ween_get_user_info(params)
    if (params["gan_zhis"] != nil) then
      hData["gan_zhis"] = eval(params["gan_zhis"]).to_a
    end
    hData["duixiang"] = params["duixiang"]
    if (params["count"] != nil) then
      hData["count"] = params["count"].to_i
    end
    hData["lifa"] = "jieqi" #params["lifa"]

    oWeenApi = WeenApi.new
    @result = oWeenApi.g_ganzhi_riqi(hData)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end
  def wan_nian_li
    hData = ween_get_user_info(params)
    # hData["qian_ji_tian"] = params["qian_ji_tian"].to_i
    # hData["hou_ji_tian"] = params["hou_ji_tian"].to_i
    if (params["qian_hou_ji_tian"] != nil) then
      hData["qian_hou_ji_tian"] = params["qian_hou_ji_tian"].to_i
    end
    # hData["ji_tian_hou_kai_shi"] = params["ji_tian_hou_kai_shi"].to_i

    oWeenApi = WeenApi.new
    @result = oWeenApi.g_wan_nian_li(hData)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end
  def shi_ju
    hUserDefData = ween_get_cur_date_info(params)
    hData = ween_get_user_info(params)

    oWeenApi = WeenApi.new
    @result = oWeenApi.g_shi_ju(hData,hUserDefData)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json)  { render :json => @result, :callback => params["callback"] }
    end
  end
  # 八字盤
  def bazhi_pan
    hUserDefData = ween_get_cur_date_info(params)
    hUserInfo = ween_get_user_info(params)
    oWeenApi = WeenApi.new
    nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"].to_i
    @ApFunc = nil
    @ParAll = nil
    @result = oWeenApi.g_bazhi_info(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end

  def ziwei_liuri
    hResult,h_result,h_par = api_ziwei_lunduan_liuri_now(params.clone,remote_ip())

    # respond_with @result
    # JSONP format
    respond_with(h_result) do |format|
      format.any(:js, :json) { render :json => h_result, :callback => params["callback"] }
    end
  end
  def timestamps
    timestamp = params["timestamp"]
    y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp)
    ey,em,ed,bl = Xdate.West2East(y,m,d)
    ebl = bl ? 1 : 0
    s = sex == 1 ? "m" : "f"
    h_result = {
      "lunar" => {"timestamp" => Xdate.make_timestamp_lunar(ey,em,ed,h,ebl,sex),"y" => ey, "m" => em, "d" => ed, "l" => ebl, "dt" => 1, "h" => h, "gender" => s, "min" => min},
      "solar" => {"timestamp" => Xdate.make_timestamp_solar(y,m,d,h,sex),"y" => y, "m" => m, "d" => d, "dt" => 0, "h" => h, "gender" => s, "min" => min}
    }
    respond_with(h_result) do |format|
      format.any(:js, :json) { render :json => h_result, :callback => params["callback"] }
    end
  end

  def ziwei_lunduan_api
    oWeenApi = WeenApi.new
    @result = oWeenApi.ziwei_api_result(params)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end

  def bbbot_health_neirong
    s = Health.bbbot_neirong(params)
    @result = {"answer" => s}
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
    end
  end


  # 陳明德 八字
  def bazhi_1
    # 1.要判斷對方url ip
    user_ip = remote_ip()
    # if (user_ip != "**********") then
    #   h = {"data" : "no data"}
    #   render json: h
    # else
      # 2.要記錄使用次數 未做
      # if request.referer != nil && URI(request.referer).path == '/adsense' then
        # puts "refer: #{URI(request.referer).path}"
        h_par = params.clone
        # 先固定兩個盤，讓他們寫好之後，再開放完整功能，再加上referer判斷及記錄使用狀況
        # h_par["timestamp"] = ["2000020310M","L02000030512F"].sample
        # h_par["timestamp"] = ["2000020310M"].sample
        par_all = {Eightword::PAR_FIRST_TIME_TYPE => Xdate::FIRST_CURRENT}
        # par_all = nil
        h,h,h_par = api_bazhi_pan(h_par,{},remote_ip(),par_all)
        h_par["user_ip"] = user_ip
        h_par["user_url"] = request.referer
        Pm.saveTestDb2("bazhi_api_chenminde",h_par)
        render :json => h.to_json
        # respond_with(h_par) do |format|
        #   format.any(:js, :json) { render :json => h, :callback => params["callback"] }
        # end
      # else
      #   render json: {}
      # end
    # end
  end

  # 火錘
  # timestamp: YYYYMMDDhhmms   (s:M male, F female)
  # pt : 盤別 十年盤1 流年盤2 流月盤3 流日盤4  預設值 十年盤
  # house : 宮位 夫妻3 財帛5 官祿9 預設值夫妻
  # uy: 流年年份 yyyy 預設值 今年
  # um: 流月月份 mm   預設值 本月
  # ud: 流日日期 dd   預設值 本日
  # name: 姓名
  def huochui_api_ziwei_lunduan()
    if (!huochui_api_check_ip_exist?(remote_ip())) then
      render :json => {"fail" => "illegal ip"}.to_json
    else
      api_count = huochui_change_count("huochui_api_ziwei_lunduan",-1)
      if (api_count < 0) then
        render :json => {"fail" => "huochui_api_ziwei_lunduan out of count"}.to_json
      else
        # 儲存輸入參數
        huochui_save_api_parms("huochui_api_ziwei_lunduan",params)

        # 五行論斷法 目前只接受 夫妻3 財帛5 官祿9 三宮的論斷
        # 可接受 十年盤1 流年盤2 流月盤3 流日盤4
        houses = [3,5,9]
        h_par_org = params.clone
  
        h_par_org["house"] = 3 if h_par_org["house"] == nil
        if (!houses.include?(h_par_org["house"].to_i)) then
          h_par_org["house"] = 3
        end
        pts = [1,2,3,4]
        h_par_org["pt"] = 1 if h_par_org["pt"] == nil
        if (!pts.include?(h_par_org["pt"].to_i)) then
          h_par_org["pt"] = 1
        end
        # liunian_guanlu
        h_result = {}
        # h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
        h,h,h_par = huochui_ziwei_lunduan(h_par_org,h_result,remote_ip())
        h["api_count"] = api_count
        render :json => h.to_json
      end
    end
  end
  # 輸入參數
  # timestamp: YYYYMMDDhhmms   (s:M male, F female)
  # pt : 盤別 ： 本命盤0 十年盤1 流年盤2 流月盤3 流日盤4 預設值 本命盤
  # uy: 流年年份 yyyy 預設值 今年
  # um: 流月月份 mm   預設值 本月
  # ud: 流日日期 dd   預設值 本日
  # name: 姓名
  def huochui_api_ziwei_lunduan_12()
    if (!huochui_api_check_ip_exist?(remote_ip())) then
      render :json => {"fail" => "illegal ip"}.to_json
    else
      api_count = huochui_change_count("huochui_api_ziwei_lunduan_12",-1)
      if (api_count < 0) then
        render :json => {"fail" => "huochui_api_ziwei_lunduan_12 out of count"}.to_json
      else
        # 儲存輸入參數
        huochui_save_api_parms("huochui_api_ziwei_lunduan_12",params)

        # 生剋論斷法 目前只提供各流盤十二宮之論斷（house＝99)，不提供單一宮位之論斷
        # 可接受 本命盤0 十年盤1 流年盤2 流月盤3 流日盤4
        h_par_org = params.clone
  
        pts = [0,1,2,3,4]
        h_par_org["pt"] = 0 if h_par_org["pt"] == nil
        if (!pts.include?(h_par_org["pt"].to_i)) then
          h_par_org["pt"] = 0
        end
        h_par_org["house"] = 99 # 此api固定呈現十二宮之論斷，不提供單宮之論斷
        h_result = {}
        # h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
        h,h,h_par = huochui_ziwei_lunduan_meen(h_par_org,h_result,remote_ip())
        h["api_count"] = api_count
        render :json => h.to_json
      end
    end
  end

  # timestamp: YYYYMMDDhhmms   (s:M male, F female)
  # pt : 盤別 ： 本命盤0 十年盤1 流年盤2 流月盤3 流日盤4 預設值 本命盤
  # uy: 流年年份 yyyy 預設值 今年
  # um: 流月月份 mm   預設值 本月
  # ud: 流日日期 dd   預設值 本日
  # name: 姓名
  def huochui_api_ziwei_pan()
    if (!huochui_api_check_ip_exist?(remote_ip())) then
      render :json => {"fail" => "illegal ip"}.to_json
    else
      api_count = huochui_change_count("huochui_api_ziwei_pan",-1)
      if (api_count < 0) then
        render :json => {"fail" => "huochui_api_ziwei_pan out of count"}.to_json
      else
        # 儲存輸入參數
        huochui_save_api_parms("huochui_api_ziwei_pan",params)

        h_par_org = params.clone

        pts = [0,1,2,3,4]
        h_par_org["pt"] = 0 if h_par_org["pt"] == nil
        if (!pts.include?(h_par_org["pt"].to_i)) then
          h_par_org["pt"] = 0
        end
        h_result = {}
        # h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
        h,h,h_par = huochui_ziwei_pan(h_par_org,h_result,remote_ip())
        h["api_count"] = api_count
        render :json => h.to_json
      end
    end
  end

  # word : 所測的字，一個字
  # land : 地數,1 ~ 24
  # minute : 測字時的分鐘
  # thing : 問的事情，會原封不動回給呼叫者
  def huochui_api_gnum()
    if (!huochui_api_check_ip_exist?(remote_ip())) then
      render :json => {"fail" => "illegal ip"}.to_json
    else
      api_count = huochui_change_count("huochui_api_gnum",-1)
      if (api_count < 0) then
        render :json => {"fail" => "huochui_api_gnum out of count"}.to_json
      else
        # 儲存輸入參數
        huochui_save_api_parms("huochui_api_gnum",params)

        word = params["word"]
        land = params["land"]
        if (land == "-1") then
          land = nil
        end
        m = params["minute"]
        if (m == "-1") then
          m = nil
        end
        thing = params["thing"]
    
        oWeenApi = WeenApi.new
        @result = oWeenApi.g_gnum(word,land,m,thing)
    
        # @result["word"] = word
        # @result["land"] = land
        # @result["minute"] = m
        # @result["thing"] = thing
        @result["api_count"] = api_count
        # respond_with @result
        # JSONP format
        respond_with(@result) do |format|
          format.any(:js, :json) { render :json => @result, :callback => params["callback"] }
        end
      end
    end
  end
  def set_my_locale
    h_par_org = params.clone
    h_par_org["locale"] = Cfate::DEFAULT_LANG
    h_par_org["locale"] = params["locale"] if params["locale"]
    my_set_locale(h_par_org)
  end
  def huochui_api_count()
    h_par = params.clone
    hHuochui = Pm.getSystemValue_hash("huochui_api")
    hHuochui_count = hHuochui["count"]
    if (hHuochui_count == nil) then
      hHuochui_count = {}
      hHuochui_count["huochui_api_ziwei_lunduan"] = 0
      hHuochui_count["huochui_api_ziwei_lunduan_12"] = 0
      hHuochui_count["huochui_api_ziwei_pan"] = 0
      hHuochui_count["huochui_api_gnum"] = 0
    end
    if (h_par["key"] == "sw2293pj") then
      cmd = h_par["cmd"]
      if (cmd == "add") then
        hHuochui_count["huochui_api_ziwei_lunduan"] = hHuochui_count["huochui_api_ziwei_lunduan"].to_i + h_par["ziwei_lunduan"].to_i
        hHuochui_count["huochui_api_ziwei_lunduan_12"] = hHuochui_count["huochui_api_ziwei_lunduan_12"].to_i + h_par["ziwei_lunduan_12"].to_i
        hHuochui_count["huochui_api_ziwei_pan"] = hHuochui_count["huochui_api_ziwei_pan"].to_i + h_par["ziwei_pan"].to_i
        hHuochui_count["huochui_api_gnum"] = hHuochui_count["huochui_api_gnum"].to_i + h_par["gnum"].to_i
      elsif (cmd == "minus") then
        hHuochui_count["huochui_api_ziwei_lunduan"] = hHuochui_count["huochui_api_ziwei_lunduan"].to_i - h_par["ziwei_lunduan"].to_i
        hHuochui_count["huochui_api_ziwei_lunduan_12"] = hHuochui_count["huochui_api_ziwei_lunduan_12"].to_i - h_par["ziwei_lunduan_12"].to_i
        hHuochui_count["huochui_api_ziwei_pan"] = hHuochui_count["huochui_api_ziwei_pan"].to_i - h_par["ziwei_pan"].to_i
        hHuochui_count["huochui_api_gnum"] = hHuochui_count["huochui_api_gnum"].to_i - h_par["gnum"].to_i
      elsif (cmd == "reset") then
        hHuochui_count["huochui_api_ziwei_lunduan"] = 0
        hHuochui_count["huochui_api_ziwei_lunduan_12"] = 0
        hHuochui_count["huochui_api_ziwei_pan"] = 0
        hHuochui_count["huochui_api_gnum"] = 0
      end
      hHuochui["count"] = hHuochui_count
      Pm.setSystemValue_hash("huochui_api",hHuochui)
    end

    render :json => hHuochui.to_json
  end

  def huochui_api_par_set()
    func = params["func"]
    if (func == "count") then
      huochui_api_count()
    elsif (func == "ip") then
      huochui_api_ip()
    else
      huochui_api_par_list()
    end
  end
  def huochui_api_par_list()
    hHuochui = Pm.getSystemValue_hash("huochui_api")
    hHuochui_count = hHuochui["count"]
    if (hHuochui_count == nil) then
      hHuochui_count = {}
      hHuochui_count["huochui_api_ziwei_lunduan"] = 0
      hHuochui_count["huochui_api_ziwei_lunduan_12"] = 0
      hHuochui_count["huochui_api_ziwei_pan"] = 0
      hHuochui_count["huochui_api_gnum"] = 0
    end
    if (hHuochui["huochui_api_ziwei_lunduan"] != nil) then
      hHuochui_count["huochui_api_ziwei_lunduan"] = hHuochui_count["huochui_api_ziwei_lunduan"].to_i + hHuochui["huochui_api_ziwei_lunduan"].to_i
      hHuochui.delete("huochui_api_ziwei_lunduan")

      hHuochui_count["huochui_api_ziwei_lunduan_12"] = hHuochui_count["huochui_api_ziwei_lunduan_12"].to_i + hHuochui["huochui_api_ziwei_lunduan_12"].to_i
      hHuochui.delete("huochui_api_ziwei_lunduan_12")

      hHuochui_count["huochui_api_ziwei_pan"] = hHuochui_count["huochui_api_ziwei_pan"].to_i + hHuochui["huochui_api_ziwei_pan"].to_i
      hHuochui.delete("huochui_api_ziwei_pan")

      hHuochui_count["huochui_api_gnum"] = hHuochui_count["huochui_api_gnum"].to_i + hHuochui["huochui_api_gnum"].to_i
      hHuochui.delete("huochui_api_gnum")

      hHuochui["count"] = hHuochui_count
      Pm.setSystemValue_hash("huochui_api",hHuochui)
    end
    render :json => hHuochui.to_json
  end

  def huochui_api_ip()
    h_par = params.clone
    hHuochui = Pm.getSystemValue_hash("huochui_api")
    hHuochui_ip = hHuochui["ip"]
    if (hHuochui_ip == nil) then
      hHuochui_ip = []
    end
    cmd = h_par["cmd"]
    if (h_par["key"] == "sw2293pj") then
      if (cmd == "add") then
        hHuochui_ip.push(h_par["ip"])
      elsif (cmd == "delete") then
        hHuochui_ip.delete(h_par["ip"])
      elsif (cmd == "reset") then
        hHuochui_ip = []
      end
      hHuochui_ip.uniq!
      hHuochui["ip"] = hHuochui_ip
      Pm.setSystemValue_hash("huochui_api",hHuochui)
    end

    render :json => hHuochui_ip.to_json
  end
  def huochui_api_check_ip_exist?(ip)
    h = Pm.findTestDbHashValue_key("huochui_server_ip")
    if (h == nil || h == {}) then
      h = {}
      h["ip"] = []
    end
    h["ip"].push(ip)
    h["ip"].uniq!
    Pm.updateTestDbHashValue_key("huochui_server_ip",h)

    hHuochui = Pm.getSystemValue_hash("huochui_api")
    hHuochui_ip = hHuochui["ip"]
    if (hHuochui_ip == nil || hHuochui_ip == []) then
      return true
    else
      return hHuochui_ip.include?(ip)
    end
  end
  def huochui_change_count(key,count)
    hHuochui = Pm.getSystemValue_hash("huochui_api")
    hHuochui_count = hHuochui["count"]
    if (hHuochui_count == nil) then
      hHuochui_count = {}
      hHuochui_count["huochui_api_ziwei_lunduan"] = 0
      hHuochui_count["huochui_api_ziwei_lunduan_12"] = 0
      hHuochui_count["huochui_api_ziwei_pan"] = 0
      hHuochui_count["huochui_api_gnum"] = 0
    end
    hHuochui_count[key] = hHuochui_count[key].to_i + count
    n = hHuochui_count[key].to_i
    hHuochui_count[key] = 0 if hHuochui_count[key] < 0
    hHuochui["count"] = hHuochui_count
    Pm.setSystemValue_hash("huochui_api",hHuochui)

    return n
  end
  def huochui_save_api_parms(key,pars)
    h = Pm.findTestDbHashValue_key(key)
    if (["huochui_api_ziwei_lunduan","huochui_api_ziwei_lunduan_12","huochui_api_ziwei_pan"].include?(key)) then
      if h["timestamp"] == nil then h["timestamp"] = [] end
      h["timestamp"].push(huochui_modify_timestamp_for_save(pars["timestamp"])) if pars["timestamp"] != nil 
    elsif (key == "huochui_api_gnum") then
      if h["word"] == nil then h["word"] = [] end
      h["word"].push(pars["word"]) if pars["word"] != nil 
    end
    Pm.updateTestDbHashValue_key(key,h)
  end
  def huochui_modify_timestamp_for_save(ts)
    if (ts.length == 13) then
      return "#{ts[0,10]}#{ts[12]}"
    else
      return ts
    end
  end
  def huochui_api_show_par_input()
    keys = ["huochui_api_ziwei_lunduan","huochui_api_ziwei_lunduan_12","huochui_api_ziwei_pan","huochui_api_gnum","huochui_server_ip"]
    hOut = {}
    keys.each do |key|
      h = Pm.findTestDbHashValue_key(key)
      if (["huochui_api_ziwei_lunduan","huochui_api_ziwei_lunduan_12","huochui_api_ziwei_pan"].include?(key)) then
        h["timestamp"].uniq! if ((h["timestamp"] != nil) && (params["uniq"] == "1"))
        h["timestamp"].sort! if ((h["timestamp"] != nil) && (params["sort"] == "1"))
      elsif (key == "huochui_api_gnum") then
        h["word"].uniq! if ((h["word"] != nil) && (params["uniq"] == "1"))
        h["word"].sort! if ((h["word"] != nil) && (params["sort"] == "1"))
      elsif (key == "huochui_server_ip") then
        h["ip"].uniq! if ((h["ip"] != nil) && (params["uniq"] == "1"))
        h["ip"].sort! if ((h["ip"] != nil) && (params["sort"] == "1"))
      end
      hOut[key] = h
      if ([key,"all"].include?(params["api_name"]) && params["key"] == "sw2293pj") then
        if (params["cmd"] == "delete") then
          h[h.keys[0]].delete(params["item"]) if h[h.keys[0]] != nil
          Pm.updateTestDbHashValue_key(key,h)
          hOut[key] = h
        elsif (params["cmd"] == "save") then
          Pm.updateTestDbHashValue_key(key,h)
        elsif (params["cmd"] == "reset") then
          Pm.updateTestDbHashValue_key(key,{})
          hOut[key] = {}
        end
      end
    end
    render :json => hOut.to_json
  end

  # api for pan
  public
  def pan_iframe
    ap_name = "star"
    product_type = "pan2"
    email = params["email"]
    if (email == nil) then
      hOut = {"fail" => "need email"}
      render :json => hOut.to_json
    else
      oUser = User.find_by_email(email)
      if (oUser == nil) then
        hOut = {"fail" => "need web site email"}
        render :json => hOut.to_json
      else
        user_id = oUser.id
        hOut = pan_iframe_render(user_id,ap_name)
        render :json => hOut.to_json
      end
    end
  end
  protected
  def pan_iframe_render(user_id,ap_name)
    hUserDefData = ween_get_cur_date_info(params)
    hUserInfo = ween_get_user_info(params)
    customer_id = params["customer_id"].to_i
    if (customer_id != nil && customer_id > 0) then
      oCustomer = UserCustomer.check_option_user_id(user_id).find(customer_id)
      if (oCustomer != nil) then
        hUserInfo[Cfate::WYear] = oCustomer.wyear
        hUserInfo[Cfate::WMonth] = oCustomer.wmonth
        hUserInfo[Cfate::WDate] = oCustomer.wday
        hUserInfo[Cfate::WHour] = oCustomer.hour
        hUserInfo[Cfate::WMinute] = oCustomer.minute
        hUserInfo[Cfate::Sex] = oCustomer.sex == 1 ? false : true
        hUserInfo[Cfate::Name] = oCustomer.name
      else
        hOut = {"fail" => "need your customer"}
        return hOut
      end
    else
      a = UserCustomer.check_option_user_id(user_id).check_name(hUserInfo[Cfate::Name])
      a = a.check_wyear(hUserInfo[Cfate::WYear])
      a = a.check_wmonth(hUserInfo[Cfate::WMonth])
      a = a.check_wday(hUserInfo[Cfate::WDate])
      a = a.check_hour(hUserInfo[Cfate::WHour])
      oCustomer = a.check_sex(hUserInfo[Cfate::Sex]).last
      if (oCustomer == nil) then
        oCustomer = newUserCustomerNow(hUserInfo)
        oCustomer.user_id = user_id
        oCustomer.save!
      end
      customer_id = oCustomer.id
    end

    # 關閉吉凶
    hApFunc = {}
    hApFunc["GiSuongEnable"] = false
    # 關閉論斷
    hApFunc["n_explain_pan"] = -1
    hApFunc["n_explain_free_pan"] = -1

    (Cfate::PAN_NORMAL..Cfate::PAN_FLOWMIN).each do |nPanType|
      hApFunc[Cfate.gGetPanTypeHashKey(nPanType)] = false
    end
    nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"]
    hApFunc[Cfate.gGetPanTypeHashKey(nPanType)] = true
    hApFunc["LastXdatePanType"] = nPanType
    hApFunc[Cfate::LAST_XDATE_PAN_TYPE] = nPanType

    h = Hash.new
    hParAll = {}
    h["ParAll"] = hParAll
    h["ApFunc"] = hApFunc
    h["customer"] = hUserInfo

    # 需判斷重覆的狀況
    a = ApShare.check_userid(user_id).check_customerid(customer_id).check_apname(ap_name).check_pantype(nPanType).check_actionname("pan2").check_ap_data(h.to_json)
    if (nPanType == Cfate::PAN_NORMAL) then
      ap_share = a.last
    else
      a = a.check_eyear(hUserDefData[Cfate::EYear])
      if (nPanType <= Cfate::PAN_FLOWYEAR) then
        ap_share = a.last
      else
        a = a.check_emonth(hUserDefData[Cfate::ELeapMonth],hUserDefData[Cfate::EMonth])
        if (nPanType == Cfate::PAN_FLOWMONTH) then
          ap_share = a.last
        else
          a = a.check_eday(hUserDefData[Cfate::EDate])
          if (nPanType == Cfate::PAN_FLOWDATE) then
            ap_share = a.last
          else
            a = a.check_hour(hUserDefData[Cfate::WHour])
            if (nPanType == Cfate::PAN_FLOWTIME) then
              ap_share = a.last
            else
              # Cfate::PAN_FLOWMIN
              ap_share = a.check_minute(hUserDefData[Cfate::WMinute]).last
            end
          end
        end
      end
    end

    if (ap_share == nil) then
      ap_share = new_ap_share(h,user_id,customer_id,ap_name,nPanType,hUserDefData)
    end

    sHost = Pm.getSystemValue("MEEN_URL")
    if (params["locale"] == nil || params["locale"] == "") then
      url = "#{sHost}/ifate/#{ap_name.downcase}/pan2?k=#{ap_share.share_key}"
      url_iframe = "<iframe width='720' height='720' src='#{url}' frameborder='0' scrolling='yes'></iframe>"
    else
      url = "#{sHost}/#{params["locale"]}/ifate/#{ap_name.downcase}/pan2?k=#{ap_share.share_key}"
      url_iframe = "<iframe width='720' height='720' src='#{url}' frameborder='0' scrolling='yes'></iframe>"
    end
    hOut = {}
    hOut["pan_url"] = url
    hOut["pan_url_iframe"] = url_iframe
    return hOut
  end
  protected
  def new_ap_share(h,user_id,customer_id,ap_name,nPanType,hUserDefData)
    ap_share = ApShare.new
    ap_share.user_id = user_id
    ap_share.customer_id = customer_id
    ap_share.ap_name = ap_name
    ap_share.action_name = "pan2"
    ap_share.pantype = nPanType
    ap_share.share_key = SecureRandom.uuid
    ap_share.eyear = hUserDefData[Cfate::EYear]
    ap_share.eleap = hUserDefData[Cfate::ELeapMonth]
    ap_share.emonth = hUserDefData[Cfate::EMonth]
    ap_share.eday = hUserDefData[Cfate::EDate]
    ap_share.hour = hUserDefData[Cfate::WHour]
    ap_share.minute = hUserDefData[Cfate::WMinute]
    ap_share.sky_earth = ""
    ap_share.hAll = h
    ap_share.count = 0
    ap_share.status = 0
    ap_share.save!

    return ap_share
  end

  def newUserCustomerNow(hUserInfo)
    user_customer = UserCustomer.new
    user_customer.calType = Xdate::CT_SOLAR
    user_customer.wyear = hUserInfo[Cfate::WYear]
    user_customer.wmonth = hUserInfo[Cfate::WMonth]
    user_customer.wday = hUserInfo[Cfate::WDate]
    user_customer.hour = hUserInfo[Cfate::WHour]
    user_customer.minute = hUserInfo[Cfate::WMinute]
    user_customer.name = hUserInfo[Cfate::Name]
    user_customer.sex = hUserInfo[Cfate::Sex] ? 0 : 1
    user_customer.eyear = hUserInfo[Cfate::EYear]
    user_customer.eleap = hUserInfo[Cfate::ELeapMonth]
    user_customer.emonth = hUserInfo[Cfate::EMonth]
    user_customer.eday = hUserInfo[Cfate::EDate]
    return user_customer
  end


  public
  # 謝先生
  # timestamp: YYYYMMDDhhmms   (s:M male, F female)
  # pt : 盤別 十年盤1 流年盤2 流月盤3 流日盤4  預設值 十年盤
  # house : 宮位 夫妻3 財帛5 官祿9 預設值夫妻
  # uy: 流年年份 yyyy 預設值 今年
  # um: 流月月份 mm   預設值 本月
  # ud: 流日日期 dd   預設值 本日
  # name: 姓名
  def hsieh_api_ziwei_lunduan()
    # if (!huochui_api_check_ip_exist?(remote_ip())) then
    #   render :json => {"fail" => "illegal ip"}.to_json
    # else
      # api_count = huochui_change_count("huochui_api_ziwei_lunduan",-1)
      # if (api_count < 0) then
      #   render :json => {"fail" => "huochui_api_ziwei_lunduan out of count"}.to_json
      # else
        # 儲存輸入參數
        # huochui_save_api_parms("huochui_api_ziwei_lunduan",params)

        # 五行論斷法 目前只接受 夫妻3 財帛5 官祿9 三宮的論斷
        # 可接受 十年盤1 流年盤2 流月盤3 流日盤4
        houses = [3,5,9]
        h_par_org = params.clone
  
        h_par_org["house"] = 3 if h_par_org["house"] == nil
        if (!houses.include?(h_par_org["house"].to_i)) then
          h_par_org["house"] = 3
        end
        pts = [1,2,3,4]
        h_par_org["pt"] = 1 if h_par_org["pt"] == nil
        if (!pts.include?(h_par_org["pt"].to_i)) then
          h_par_org["pt"] = 1
        end
        # liunian_guanlu
        h_result = {}
        h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
        h,h,h_par = huochui_ziwei_lunduan(h_par_org,h_result,remote_ip())
        # h["api_count"] = api_count
        render :json => h.to_json
      # end
    # end
  end

  # timestamp: YYYYMMDDhhmms   (s:M male, F female)
  # pt : 盤別 ： 本命盤0 十年盤1 流年盤2 流月盤3 流日盤4 預設值 本命盤
  # uy: 流年年份 yyyy 預設值 今年
  # um: 流月月份 mm   預設值 本月
  # ud: 流日日期 dd   預設值 本日
  # name: 姓名
  def hsieh_api_ziwei_pan()
    # if (!huochui_api_check_ip_exist?(remote_ip())) then
    #   render :json => {"fail" => "illegal ip"}.to_json
    # else
    #   api_count = huochui_change_count("huochui_api_ziwei_pan",-1)
    #   if (api_count < 0) then
    #     render :json => {"fail" => "huochui_api_ziwei_pan out of count"}.to_json
    #   else
        # 儲存輸入參數
        # huochui_save_api_parms("huochui_api_ziwei_pan",params)

        h_par_org = params.clone

        pts = [0,1,2,3,4]
        h_par_org["pt"] = 0 if h_par_org["pt"] == nil
        if (!pts.include?(h_par_org["pt"].to_i)) then
          h_par_org["pt"] = 0
        end
        h_result = {}
        h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
        h,h,h_par = huochui_ziwei_pan(h_par_org,h_result,remote_ip())
        # h["api_count"] = api_count
        render :json => h.to_json
    #   end
    # end
  end


  # 1.小趣科技 要呼叫吉凶分數api
  def sh_api_gixiongscore
    h_par_org = params.clone

    h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
    
    hUserDefData = ween_get_cur_date_info(h_par_org)
    hUserInfo = ween_get_user_info(h_par_org)
    oWeenApi = WeenApi.new
    nPanType = h_par_org["pt"] == nil ? Cfate::PAN_NORMAL : h_par_org["pt"].to_i
    if (nPanType == Cfate::PAN_NORMAL) then
      nPanType = Cfate::PAN_TENYEAR
    end
    @ApFunc = nil
    @ParAll = nil
    @result = oWeenApi.g_fe_score(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll)

    # respond_with @result
    # JSONP format
    respond_with(@result) do |format|
      format.any(:js, :json) { render :json => @result, :callback => h_par_org["callback"] }
    end
  end

end
