require("Xdate.rb")
require("WeenApi.rb")
require("Controller_Api.rb")
require("Controller_Api_Order.rb")
require("Controller_Api_Allpay.rb")
require("Controller_Api_Profate_Order.rb")

# 非會員，單次交易的處理
class Api::V1::ProfateEventsController < ApplicationController
  include Controller_Api
  include Controller_Api_Order
  include Controller_Api_Allpay
  include Controller_Api_Profate_Order
  before_action :set_api_v1_events, only: [:show, :update, :destroy, :event_name]
  protect_from_forgery with: :null_session, if: -> { request.format.json? }
  respond_to :json

  def index
    flag = params["flag"].to_i

    if (flag > 0)
      profate_events = ProfateEvent.check_flag(flag).all.order('created_at DESC')
    else
      profate_events = ProfateEvent.all.order('created_at DESC')
    end
    render json: profate_events
    # render nothing: true
  end

  # GET /api/v1/profate_events/1
  # GET /api/v1/profate_events/1.json
  def show
    if (@api_v1_events == nil) then
      render json: {:status => "error"} #, status: :unprocessable_entity
    else
      h = Hash.new
      # h["params"] = @api_v1_events.hParam

      h_result = @api_v1_events.hResult.clone
      h_par = @api_v1_events.hParam.clone
      # 判斷是否需重算 result
      bNew,reduce_count = need_new_result(@api_v1_events,params)
      if (bNew) then
        @api_v1_events.can_update_count -= reduce_count
        api_name = @api_v1_events.api_name
        # h_par = @api_v1_events.hParam
        h_com = {}
        h_com["p"] = params["p"].to_i if params["p"] != nil
        h_com["timestamp_zeri"] = params["timestamp_zeri"] if params["timestamp_zeri"] != nil
        h_par = combine_user_and_server_param(h_par,h_com,@api_v1_events.event_point_id)
        # 回傳值是到目前為止計算的結果，第二個值是這一次的計算結果，第三個值是最後的參數值
        @api_v1_events.hResult,h_result,h_par = eval("#{api_name}(h_par.clone,h_result.clone,remote_ip())")

        h_par["need_new_result"] = false
        @api_v1_events.hParam = h_par
        @api_v1_events.save!
      end

      # 全部傳回。若沒有下面這一行，就是傳回此次算的部分
      # h_result = @api_v1_events.hResult

      h = @api_v1_events.serializable_hash
      if (@api_v1_events.unpaid) then # 非 未付款
        h["status"] = "pending"
        h["result"] = h_result
        h["result"].delete("qushitu")
        h["result"].delete("lunduan")
        h["result"].delete("jingyu")
        h["param"] = h_par
      else
        h["status"] = "received"
        h["result"] = h_result
        h["param"] = h_par
      end
      h.delete("api_name")
      render json: h
      # render json: @api_v1_events
      # render json: nil,status: :bad_request # 400 Bad Request for header status code in html
    end
  end
  def event_name
    render json: {:event_name => @api_v1_events.event_name}
  end
  # POST /api/v1/profate_events
  # POST /api/v1/profate_events.json
  def create
    user_id = nil
    @api_v1_events,user_point,coupon = api_v1_events_params_create_check(user_id,remote_ip(),api_v1_events_params)

    if (@api_v1_events != nil) then
      if @api_v1_events.save then
        if (user_point != nil) then
          user_point.save
        end
        if (coupon != nil) then
          coupon.save
        end
        if (@api_v1_events.flag == 1) then # 未付款
          # 找出以哪一筆 point_product來付款
          point_product = find_point_product(@api_v1_events.event_name,@api_v1_events.point_used)
          # 準備付款相關資料
          # 建立order的所有資料庫，回傳你背景的url,需要傳給歐付寶的參數
          h = profate_order_get_data_from_ween_billing(user_id,@api_v1_events,point_product,remote_ip(),params)
          h["status"] = "pending"
          render json: h
          # render :html => res
          # respond_to do |format|
            # format.html { render :html => res }
            # format.json { head :ok }
          # end
        else
          # render json: @api_v1_events, status: :created #, location: @api_v1_events
          h = Hash.new
          h["uuid"] = @api_v1_events.uuid
          h["allpay_aiocheckout_server"] = ""
          h["allpay"] = Hash.new
          # h["auto_post_html"] = ""
          h["status"] = "received"
          render json: h, status: :created #, location: @api_v1_events
        end
      else
        render json: {:status => "error1"} #, status: :unprocessable_entity
        # render json: @api_v1_events.errors, status: :unprocessable_entity
      end
    else
      render json: {:status => "error2"} #, status: :unprocessable_entity
    end
  end
  # PATCH/PUT /api/v1/profate_events/1
  # PATCH/PUT /api/v1/profate_events/1.json
  def update
    if (@api_v1_events == nil) then
      render json: {:status => "error"} #, status: :unprocessable_entity
    else
      if (@api_v1_events.can_update_count > 0) then
        @api_v1_events.can_update_count -= 1
        h = api_v1_events_params
        h_par = combine_user_and_server_param(@api_v1_events.hParam,h["param"],@api_v1_events.event_point_id)
        # 有新參數，需重算 result
        h_par["need_new_result"] = true
        # 如果有傳入 delete_result 參數，才歌掉 hResult 重算
        if (h_par["delete_result"] == "1") then
          @api_v1_events.hResult = nil
          h_par["delete_result"] = "0"
        end
        @api_v1_events.hParam = h_par
        if @api_v1_events.save! then
          render json: {:status => "received"}
          # head :no_content
        else
          render json: @api_v1_events.errors, status: :unprocessable_entity
        end
      else
        render json: @api_v1_events.errors, status: :unprocessable_entity
      end
    end
  end

  # DELETE /api/v1/profate_events/1
  # DELETE /api/v1/profate_events/1.json
  def destroy
    if (@api_v1_events == nil) then
      render json: {:status => "error"}
    else
      @api_v1_events.destroy

      render json: {:status => "received"}
      # head :no_content
    end
  end

  private

    def set_api_v1_events
      h = params #api_v1_events_params
      # @api_v1_events = ProfateEvent.find(h[:id])
      # @api_v1_events = ProfateEvent.where(:id => h[:id], :user_id => h[:user_id])
      @api_v1_events = ProfateEvent.check_uuid(h["id"]).last
      # 等 profate_clients 好了再用底下的
      # if (@api_v1_events.user_id != 1) then
      # # if (@api_v1_events.user_id != current_user.id) then
      #   @api_v1_events = nil
      # end
    end

  # hash params 函 api_v1_events 為 key 之 hash 內有 ： id ,user_id, event_point_id, need_point, event_name param: 傳 hash 上來
  # api_name: 不傳上來，create時由 profate_event_points table 讀取
  # can_update_count ： 不傳create時由 profate_event_points table 讀取；每update一次param 減 1
  # can_update_count : n > 0: count ; 99999999 : always ; 0 : can't update ; 預設為1(可改一次，包含create)
  # point_used,free_point_used 根據傳入的 need_point 計算，原則上扣除客戶point_used,free_point_used各一半，若其一不足，才從另一個扣除其餘的
  def api_v1_events_params
    # params[:api_v1_events]
    params
  end

  # 第一個參數回傳是否要算
  # 第二個參數回傳扣幾點 can_update_count
  def need_new_result(api_v1_events,hPars)
    if (api_v1_events.hParam["need_new_result"]) then
      return true,0
    end
    if (api_v1_events.param != nil && api_v1_events.result == nil) then
      return true,0
    end
    # 傳入參數 p 表示要取得下一筆資料，傳入每個 api 後，各 api 會根據自己的程式，去決定該處理的內容
    # p 會帶一個值，表示這次要處理第幾次；p 這個值每次算完後都由我們程式放入 param內，傳給 client;
    # 若 p 值 > 0 , 則 client 會繼續來跟 server 要資料
    if (api_v1_events.can_update_count > 0) then
      if (hPars["p"] != nil) then
        if (hPars["p"].to_i >= 0) then
          return true,0
        end
      end
    end
    if (api_v1_events.event_name == "ziwei_poufuzeri") then
      if !(hPars["timestamp_zeri"] == nil || hPars["timestamp_zeri"] == "") then
        return true,0
      end
    end
    return false,0
  end

end
