require("Xdate.rb")
require("WeenApi.rb")
require("Controller_Api.rb")

class Api::V1::ProfateUserPointsController < ApplicationController
  include Controller_Api
  # devise_token_auth_group :member, contains: [:admin, :profate_client] #把四類人歸類成member
  # before_action :authenticate_member!, :except => [:members_test] #檢查是否為member
  before_action :set_api_v1_user_points, only: [:buy]
  protect_from_forgery with: :null_session, if: -> { request.format.json? }
  # respond_to :json

  private
  def set_api_v1_user_points
    h = api_v1_events_params
    @api_v1_user_points = ProfateUserPoint.where(:id => h["user_id"])
  end
  def api_v1_events_params
    params["api_v1_user_points"]
  end

end
