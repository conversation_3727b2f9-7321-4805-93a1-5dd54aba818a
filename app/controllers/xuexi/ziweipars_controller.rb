require("Star.rb")
require("Explain.rb")
require("PanGiSuong.rb")
require("WeenApi.rb")
require("Xuexi_Ziwei.rb")
require("Eightword.rb")

class Xuexi::ZiweiparsController < ApplicationController
  before_action :authenticate_user!, :except => []
  before_action :authenticate, :except => [:set_par_ajax]
  layout "xuexi", :except => []
  # layout "xuexi_ziweipars", :except => []

  def xuexi
    # session.delete("ziweipars_par")
    set_parall_keyvalue_insession()
    hPars = xuexi_data(params.clone)
    # puts "hPars : #{hPars}"
    # puts "session[ziweipars_par] : #{session["ziweipars_par"]}"
    @ParAll = hPars
  end
  def pan_ajax
    hPars = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    # keys = hPars["keys"].split(",")
    # values = hPars["values"].split(",")
    h = {}
    # keys.each_with_index do |key,i|
    #   h[key] = values[i]
    # end
    nPanPar = hPars["PanPar"].to_i
    h.merge!(hPars) { |key, v1, v2| v1 }
    h.merge!(@ParAll) { |key, v1, v2| v1 }
    @ParAll = Star.pan_par_assign_each(nPanPar,@ParAll,h)
    hPars["PanType"] = session["ziweipars_par"]["PanType"]
    hPars["timestamp"] = session["ziweipars_par"]["timestamp"]

    hPars = xuexi_data(hPars.clone)
    puts "session[ziweipars_par] : #{session["ziweipars_par"]}"
    puts "@ParAll #{session["ziweipars_par"]["par_key"]} : #{@ParAll[session["ziweipars_par"]["par_key"]]}"

    render :partial => "xuexi/ziweipars/pan_data"
  end
  def set_par_ajax
    @user_id = current_user.id if logged_in?
    hPars = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    hParAll = Star.get_pan_pars(@user_id,true)
    hPars.merge!(hParAll) { |key, v1, v2| v1 }
    @ParAll = panparupdate(hPars,hParAll)
    render html: Pm.t("xuexi.ziweipars.msg.set_pars").html_safe
  end
  def fuwu_shuoming
    render :layout => 'xuexi'
  end

  protected
  def panparupdate(hPars,hParAll)
    nPanPar = hPars["PanPar"].to_i
    if (hPars["PanParReset"] == "1") then
      hParAll_Final = Star.pan_par_init_each(nPanPar,hParAll)
    else
      hParAll_Final = Star.pan_par_assign_each(nPanPar,hParAll,hPars)
    end
    panPar2Db(hParAll_Final)
    return hParAll_Final
  end
  def panPar2Db(hParAll)
    ap_name = "star"
    panParType = IfatePar.check_userid(@user_id).check_apname(ap_name).first
    if (panParType == nil) then
      panParType = IfatePar.new
      panParType.user_id = @user_id
      panParType.ap_name = ap_name
    end
    panParType.hPars = hParAll
    panParType.save!
  end
  def find_timestamp(hPars,par_key)
    hPars["timestamp"] = "19780903121"
    # hPars["timestamp"] = "20170724121" if par_key == Star::PAR_LEAP_TYPE
    hPars["timestamp"] = "19820606091" if par_key == Star::PAR_LEAP_TYPE
    hPars["timestamp"] = "20030122121" if par_key == Star::PAR_DOC_TYPE
    hPars["timestamp"] = "20130922121" if par_key == Star::PAR_LU_TYPE
    hPars["timestamp"] = "20120313180" if par_key == Star::PAR_GOD_TYPE12
    hPars["timestamp"] = "20110313180" if par_key == Star::PAR_YUEKUI_TYPE
    hPars["timestamp"] = "20160502131" if par_key == Star::INT_HUAOUT_DISPLAY
    hPars["timestamp"] = "20160502131" if par_key == Star::INT_SELHUA_DISPLAY
    hPars["timestamp"] = "20160502231" if par_key == Star::PAR_ZI_SHI_TYPE
    # hPars["timestamp"] = "20160502001" if par_key == Star::PAR_ZI_SHI_TYPE

    return hPars["timestamp"]
  end
  def set_parall_keyvalue_insession()
    session["ziweipars_par"] = {} if session["ziweipars_par"] == nil
    key = session["ziweipars_par"]["par_key"]
    @ParAll["#{key}"] = session["ziweipars_par"]["#{key}"] if key != nil
  end
  def set_ziweipars_session(hPars,hParAll)
    session["ziweipars_par"] = {}
    session["ziweipars_par"]["timestamp"] = hPars["timestamp"]
    session["ziweipars_par"]["PanPar"] = hPars["PanPar"]
    session["ziweipars_par"]["PanType"] = hPars["PanType"].to_i

    if (session["ziweipars_par"]["par_key"] != hPars["par_key"]) then
      session["ziweipars_par"]["USE_CUR_TIME"] = 1
      session["ziweipars_par"].delete("UD_EYear")
      session["ziweipars_par"].delete("UD_EMonth")
      session["ziweipars_par"].delete("UD_EDate")
      session["ziweipars_par"].delete("UD_ELeap")
      session["ziweipars_par"].delete("UD_WHour")
      session["ziweipars_par"].delete("UD_WMin")
    else
      if (hPars["USE_CUR_TIME"].to_i == 1) then
        session["ziweipars_par"]["USE_CUR_TIME"] = hPars["USE_CUR_TIME"].to_i
        session["ziweipars_par"].delete("UD_EYear")
        session["ziweipars_par"].delete("UD_EMonth")
        session["ziweipars_par"].delete("UD_EDate")
        session["ziweipars_par"].delete("UD_ELeap")
        session["ziweipars_par"].delete("UD_WHour")
        session["ziweipars_par"].delete("UD_WMin")
      else
        session["ziweipars_par"].delete("USE_CUR_TIME")
        session["ziweipars_par"]["UD_EYear"] = hPars["UD_EYear"] if hPars["UD_EYear"] != nil
        session["ziweipars_par"]["UD_EMonth"] = hPars["UD_EMonth"] if hPars["UD_EMonth"] != nil
        session["ziweipars_par"]["UD_EDate"] = hPars["UD_EDate"] if hPars["UD_EDate"] != nil
        session["ziweipars_par"]["UD_ELeap"] = hPars["UD_ELeap"] if hPars["UD_ELeap"] != nil
        session["ziweipars_par"]["UD_WHour"] = hPars["UD_WHour"] if hPars["UD_WHour"] != nil
        session["ziweipars_par"]["UD_WMin"] = hPars["UD_WMin"] if hPars["UD_WMin"] != nil
      end
    end
    session["ziweipars_par"].delete("#{session["ziweipars_par"]["par_key"]}")
    session["ziweipars_par"]["par_key"] = hPars["par_key"]
    # session["ziweipars_par"]["#{hPars["par_key"]}"] = hParAll["#{hPars["par_key"]}"] if hPars["par_key"] != nil
    if hPars["par_key"] != nil then
      hParAll.keys.each do |k|
        if (k.include?(hPars["par_key"])) then
          session["ziweipars_par"][k] = hParAll[k] if hPars["par_key"] != nil
        end
      end
    end
    hPars.merge!(session["ziweipars_par"]) { |key, v1, v2| v1 } if session["ziweipars_par"] != nil
    return hPars
  end
  def check_if_last_chuangguan()
    if (Xuexi_Ziwei.last_class_itemcount(@xuexi_class,@xuexi_item)) then
      redirect_to xuexi_ziwei_chuangguan_path()
    end
  end
  def star_pan_data(hPars)
    @m_nPanType = hPars["PanType"].to_i
    hPars["PanType"] = @m_nPanType
    hPars["sLang"] = @sLang
    xuexi_pan_data(hPars)
    # @Pan34Pt = Array.new(4,[0,0])
    @uig_timestamp = hPars["timestamp"]
  end
  def xuexi_pan_data(hPars)
    # 精實創業，用來分析使用狀況
    # Pm.saveTestDb("#{hPars[:controller]}-#{hPars[:action]}-#{@oUserAp.user_id}-#{@oUserAp.product_type}",hPars.to_json)

    # 處理使用者傳入之資料及相關物件之準備
    xuexi_pan_prepare_data(hPars)

    # @ParAll[Star::INT_34COLOR] = Star::INT_34_MONO
    # @ParAll[Star::INT_LIFELINE_DISPLAY] = Cfate::PAR_FALSE
    # @ApFunc["n_SkyFindStar_house"] = 0
    # if ([@xuexi_class,@xuexi_item] == [1,8]) then
    #   @ApFunc["n_SkyFindStar_house"] = 4095
    # end
    @oStar = Star.new
    @oStar.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll)

    # 排盤資料
    pan_pan_info()

    # 萬年曆
    pan_xdate_info()

    # 個人事件資料
    pan_prepare_personalevent_data()

    # 產品購買資訊
    pan_userap_info()

    @edit_customer_url = "/ifate/customer/edit"
    @select_customer_url = "/ifate/customer/index"
    @url_back = "/ifate/star/pan"

    @aUrlKey = ["timestamp"]
    @aUrlValue = [@uig_timestamp]
    @url_pars = "timestamp,#{@timestamp},,PanType,#{@m_nPanType}"
    g_userap_addone_usecount(hPars["action"],@m_nPanType,@oUserAp)
  end
  def xuexi_pan_prepare_data(hData=nil)
    # 排盤資訊
    @PanLastYear = Cfate::PAN_LAST_YEAR # 萬年曆最後一年

    # 設定客戶資訊
    hUserInfo = set_pan_customer_info(hData)
    @UserInfo = hUserInfo

    # 處理流盤時間
    hUserDefData = set_pan_user_def_data(hData)
    @UserDefData = hUserDefData

    # # 開始處理排盤相關資訊
    # @WYear = hUserInfo[Cfate::WYear]
    # @WMonth = hUserInfo[Cfate::WMonth]
    # @WDate = hUserInfo[Cfate::WDate]
    # @WHour = hUserInfo[Cfate::WHour]
    # @Sex = hUserInfo[Cfate::Sex]
    # @Name = hUserInfo[Cfate::Name]

    # @UA_EYear = hUserDefData[Cfate::EYear]
    # @UA_EMonth = hUserDefData[Cfate::EMonth]
    # @UA_EDate = hUserDefData[Cfate::EDate]
    # @UA_WHour = hUserDefData[Cfate::WHour]
    # @UA_WMinute = hUserDefData[Cfate::WMinute]
    # @UA_LeapMonth = hUserDefData[Cfate::LeapMonth]
    # @UA_HouseName = hUserDefData[Star::HOUSE_NAME]

    @bPanChange = true
    @bDeclare = false
  end
  def set_pan_customer_info(hData=nil)
    hUserInfo = Hash.new
    hUserInfo["remote_ip"] = remote_ip()

    # 預設用現在時間當客戶生日排盤,男生
    t = time_now_local()
    hUserInfo[Cfate::WYear] = t.year
    hUserInfo[Cfate::WMonth] = t.month
    hUserInfo[Cfate::WDate] = t.day
    hUserInfo[Cfate::WHour] = t.hour
    hUserInfo[Cfate::Sex] = false
    hUserInfo[Cfate::Name] = "Profate"

    @m_nPanType = hData["PanType"].to_i

    if ((hData != nil) && (hData["wyear"] != nil)) then
      hUserInfo[Cfate::WYear] = hData["wyear"]
      hUserInfo[Cfate::WMonth] = hData["wmonth"]
      hUserInfo[Cfate::WDate] = hData["wday"]
      hUserInfo[Cfate::WHour] = hData["hour"]
      hUserInfo[Cfate::WMinute] = hData["minute"]
      hUserInfo[Cfate::Sex] = hData["sex"].to_i == 1 ? false : true
      hUserInfo[Cfate::Name] = hData["name"]
    end
    if (hData["timestamp"] != nil) then
      timestamp = hData["timestamp"]
      y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp)
      hUserInfo[Cfate::WYear] = y
      hUserInfo[Cfate::WMonth] = m
      hUserInfo[Cfate::WDate] = d
      hUserInfo[Cfate::WHour] = h
      hUserInfo[Cfate::WMinute] = min
      hUserInfo[Cfate::Sex] = sex == 1 ? false : true
      hUserInfo[Cfate::Name] = timestamp
      hUserInfo[Cfate::Multiple_births] = mb
    end
    return hUserInfo
  end
  def get_cur_date_info()
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
    hUserDefData = Hash.new
    hUserDefData[Cfate::EYear] = nEYear
    hUserDefData[Cfate::EMonth] = nEMonth
    hUserDefData[Cfate::EDate] = nEDate
    hUserDefData[Cfate::LeapMonth] = bLeapMonth
    hUserDefData[Cfate::WHour] = nWHour
    hUserDefData[Cfate::WMinute] = nWMin
    return hUserDefData
  end
  def set_pan_user_def_data(hData)
    # 處理流盤時間
    if ((hData["UD_EYear"] == nil)) then
      hUserDefData = get_cur_date_info()
    else
      #若有傳入,以傳入為主
      if (hData["UD_EYear"] != nil) then
        hUserDefData = Hash.new
        hUserDefData[Cfate::EYear] = hData["UD_EYear"].to_i
        hUserDefData[Cfate::EMonth] = hData["UD_EMonth"].to_i
        hUserDefData[Cfate::EDate] = hData["UD_EDate"].to_i
        hUserDefData[Cfate::LeapMonth] = (hData["UD_ELeap"].to_i == 0) ? false : true
        hUserDefData[Cfate::WHour] = hData["UD_WHour"].to_i
        hUserDefData[Cfate::WMinute] = hData["UD_WMin"].to_i
      end
    end

    @nLeapMonth = hUserDefData[Cfate::LeapMonth] ? 1 : 0
    hUserDefData[Star::HOUSE_NAME] = hData["star_house"] == nil ? 99 : hData["star_house"].to_i

    # session["user_def_data"] = hUserDefData
#puts("session["user_def_data"]#{session["user_def_data"][Cfate::EYear]}")
    return hUserDefData
  end
  def pan_pan_info()
    nPanType = @m_nPanType
    @IsSelFourHuaStack = @oStar.IsSelFourHuaStack?()
    @IsMioWongNum = @oStar.IsMioWongNum?()

    pan_init_instant_var(@oStar)

    # 三方四正，用比例的方式
    ptEarth34 = [[75,100],[25,100],[0,100],[0,75],[0,25],[0,0],[25,0],[75,0],[100,0],[100,25],[100,75],[100,100]]

    nHouse1Earth = @oStar.gHouse_GetFirstHouseEarth(nPanType)
    (1..12).each do |nEarth|
      # 各宮共同資訊
      pan_house_general_info(@oStar,nPanType,nEarth)

      # 各流盤資訊
      pan_house_flow_info(@oStar,nPanType,nEarth)

      # 各宮在中宮的資訊 三方四正及 四化飛出
      pan_house_in_middle_info(@oStar,nPanType,nEarth,nHouse1Earth,ptEarth34)
    end
    # personal info
    pan_personal_info(@oStar)
  end

  def pan_xdate_info()
    # 萬年曆
    makeXdate_data(@oStar)


    # 吉凶垂象
    MakeGiSuongInfo(@oStar)
  end

  def pan_userap_info()
    oProduct = g_getProductByUserAp(@oUserAp)
    @buy_info = ""
    @ap_version_desc = oProduct.product_description if oProduct != nil
    if (oProduct.demo) then
      @ap_version_desc += "(#{Pm.GetStrWithColon("IDS_S_UNTILL")}#{@oUserAp.end_date.strftime("%Y-%m-%d")})"
    else
      if (oProduct.free) then
        if (g_isDemoExist(@user_id,@ap_name)) then
          @buy_info = "buy"
        else
          @buy_info = "demo"
        end
        @ap_version_desc = Pm.GetStr("IDS_S_APP_PAN_NOT_BUIED")
      else
        if ((@oUserAp.end_date < Xdate.GetNowRails() + 14.days) && getUserApCounts(@user_id,@ap_name) < 2) then
          @buy_info = "rebuy"
        end
        @ap_version_desc = Pm.GetStr("IDS_S_APP_PAN_BUIED")
      end
    end
      if (@customer_user_id == 0) then
        @ap_version_desc = Pm.GetStr("IDS_S_ALL_SHOW")
      end
  end
  def pan_init_instant_var(oStar)
    @Doctor = Array.new(12)
    @God = Array.new(12)
    @YearGod = Array.new(12)  # 歲建
    @YearStar = Array.new(12)  # 將星
    @AStarInfos = Array.new(12)
    @AStarMioWong = Array.new(12)
    @AStarOrgFourHua = Array.new(12)
    @AStarFourHuaSky = Array.new(12)
    @BStarInfos = Array.new(12)
    @BStarMioWong = Array.new(12)
    @Small = Array.new(12)
    @Large = Array.new(12)
    @LargeSan = Star.HouseIndex2Earth(oStar.gHouse_getLargeSan())
    @BodyHouse = oStar.gPanHouse_GetBodyHouse()
    @HouseId = Array.new(12)
    @HouseName = Array.new(12)
    @HouseName_Normal = Array.new(12)
    @HouseFiveStr = Array.new(12)
    @HouseFive = Array.new(12)
    @HouseSky = Array.new(12)
    @SkyAStarFourHua = Array.new(12)
    @HouseSkyStr = Array.new(12)
    @HouseEarthStr = Array.new(12)
    @PanSmallSanYear = Array.new(12)
    @PanFlowYear = Array.new(12)
    @PanYear = Array.new(12)
    @PanYearOld = Array.new(12)
    @PanMonth = Array.new(12)
    @PanMonthStr = Array.new(12)
    @PanDate = Array.new(12)
    @PanTime = Array.new(12)
    @PanTimeStr = Array.new(12)
    @PanMinHour = Array.new(12)
    @PanMin = Array.new(12)
    @PanMinStr = Array.new(12)
    @SStarInfos = Array.new(12)
    @Star7Infos = Array.new(12)

    @PanBg34 = Array.new(12)
    @HouseOutFourHua = Array.new(12) {Array.new(4,"")}
    @AStarSelfFourHua = Array.new(12)
    @AStarSelfFourHuaCount = Array.new(12)
    @Pan34Pt = Array.new(4,[0,0])

    @HasGiSuong = false
    @GiSuongEarth = nil

    @SmallDisplay = oStar.gHouse_GetSmallDisplay()
    @LargeDisplay = oStar.gHouse_GetLargeDisplay()
    @PI_GanZhiBirthDisplay = oStar.gHouse_Get8WordsDisplay() && @ApFunc["GanZhiBirthDisplay"]
    @SmallYearRevertDisplay = oStar.gHouse_GetSmallYearRevertDisplay() && @ApFunc["SmallYearRevertDisplay"]
    @DoctorDisplay = oStar.gHouse_GetDoctorDisplay()
    @GodDisplay = oStar.gHouse_GetGodDisplay()
    @YearStarDisplay = oStar.gHouse_GetYearStarDisplay()
    @YearGodDisplay = oStar.gHouse_GetYearGodDisplay()
    @LyygDisplay = oStar.gHouse_GetLyygDisplay() && @ApFunc["LYYGDisplay"]
    @Star7Display = @ApFunc["Star7Display"]
    @ShowStar7 = oStar.gHouse_Get7StarDisplay() && @ApFunc["Star7Display"]
  end

  def pan_house_general_info(oStar,nPanType,nEarth)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)

    @Doctor[nEarthIndex] = oStar.gHouse_GetDoctorName(nPanType,nEarth)
    @God[nEarthIndex] = oStar.gHouse_GetGodName(nPanType,nEarth)
    @AStarInfos[nEarthIndex] = oStar.gHouse_GetAStarInfos(nPanType,nEarth,false)

    @AStarMioWong[nEarthIndex] = oStar.gHouse_GetAStarsMioWongInfo(nPanType,nEarth,false,false)
    @AStarOrgFourHua[nEarthIndex] = oStar.gHouse_GetAStarsFourHua(nPanType,nEarth)
    @AStarSelfFourHua[nEarthIndex],@AStarSelfFourHuaCount[nEarthIndex] = oStar.gHouse_GetAStarsSelfFourHua(nPanType,nEarth)
    @AStarSelfFourHuaDisplay = oStar.gHouse_GetAStarsSelfFourHuaDisplay()

    fa_starInfos = oStar.get_HouseFAStarInfos(nPanType,nEarth)
    @AStarInfos[nEarthIndex] = @AStarInfos[nEarthIndex] + fa_starInfos

    aStar,bUseOpp = oStar.gHouse_GetAStars(nPanType,nEarth,false)
    @AStarFourHuaSky[nEarthIndex] = oStar.gNormal_FindAStarsFourHuaSky(aStar)

    @YearGod[nEarthIndex] = oStar.gHouse_GetYearGodName(nPanType,nEarth)
    @YearStar[nEarthIndex] = oStar.gHouse_GetYearStarName(nPanType,nEarth)
    @SStarInfos[nEarthIndex] = oStar.get_HouseSStarInfos(nPanType,nEarth)

    @BStarInfos[nEarthIndex] = oStar.gHouse_GetBStarInfos(nPanType,nEarth)
    @BStarInfos[nEarthIndex] = @BStarInfos[nEarthIndex] + oStar.get_HouseFBStarInfos(nPanType,nEarth)
    @BStarMioWong[nEarthIndex] = oStar.gHouse_GetBStarsMioWongInfo(nPanType,nEarth)

    @HouseId[nEarthIndex] = oStar.g_House_GetHouseId(nPanType,nEarth)
    @HouseName[nEarthIndex] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
    @HouseName_Normal[nEarthIndex] = oStar.gHouse_GetHouseNameWithQuota(Cfate::PAN_NORMAL,nEarth)
    @HouseFiveStr[nEarthIndex] = oStar.gHouse_FiveStr(nPanType,nEarth)
    @HouseFive[nEarthIndex] = oStar.gHouse_Five(nPanType,nEarth)
    @HouseSkyStr[nEarthIndex] = oStar.gHouse_GetSkyName(nPanType,nEarth)
    @HouseSky[nEarthIndex] = oStar.gHouse_GetSky(nPanType,nEarth)
    @SkyAStarFourHua[nEarthIndex] = oStar.gNormal_FindSkyFourHuaStar(@HouseSky[nEarthIndex])

    @HouseEarthStr[nEarthIndex] = oStar.gHouse_GetEarthName(nEarth)

    @Star7Infos[nEarthIndex] = oStar.gHouse_Get7Star(nEarth)

    # 祿存疊四層(不含生年祿存)
    # 紫微工具 新增功能 #3
    astarInfos = oStar.star_GetLuYangTuoInfos(nPanType,nEarth)
    @AStarInfos[nEarthIndex] = @AStarInfos[nEarthIndex] + astarInfos
    @AStarMioWong[nEarthIndex] = @AStarMioWong[nEarthIndex] + oStar.star_GetAStarsMioWongInfo(fa_starInfos)
    @AStarMioWong[nEarthIndex] = @AStarMioWong[nEarthIndex] + oStar.star_GetAStarsMioWongInfo(astarInfos)
end

  def pan_house_flow_info(oStar,nPanType,nEarth)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)

    # normal and ten year
    @Small[nEarthIndex] = oStar.gHouse_GetSmall(nEarth)
    @Large[nEarthIndex] = oStar.gHouse_GetLarge(nEarth)

    # flow year
    if (nPanType >= Cfate::PAN_FLOWYEAR) then
      @PanSmallSanYear[nEarthIndex] = oStar.gPanHouse_FlowYear_SmallSan(nEarth,nPanType)
      @PanFlowYear[nEarthIndex],@PanYearOld[nEarthIndex],@PanYear[nEarthIndex] = oStar.gPanHouse_FlowYear(nEarth,nPanType)
    end

    # flow month
    if (nPanType >= Cfate::PAN_FLOWMONTH) then
      bFlowHouse,@PanMonthStr[nEarthIndex],@PanMonth[nEarthIndex] = oStar.gPanHouse_FlowMonth(nEarth)
      if (bFlowHouse) then
        @nFlowMonthEarth = nEarth
      end
    end

    # flow date
    if (nPanType >= Cfate::PAN_FLOWDATE) then
      bFlowHouse,@nFlowDate,aPanFlowDate = oStar.gPanHouse_FlowDate(nEarth)
      nPanFlowDateLen = aPanFlowDate[2] == nil ? 2 : 3
      @PanDate[nEarthIndex] = aPanFlowDate[0..nPanFlowDateLen-1]
      if (bFlowHouse) then
        @nFlowDateEarth = nEarth
      end
    end

    # flow time
    if (nPanType >= Cfate::PAN_FLOWTIME) then
      bFlowHouse,@PanTimeStr[nEarthIndex],nfETime = oStar.gPanHouse_FlowTime(nEarth)
      @PanTime[nEarthIndex] = Xdate.ETime2Hour(nfETime)
      if (bFlowHouse) then
        @nFlowTimeEarth = nEarth
      end
    end

    # flow min
    if (nPanType >= Cfate::PAN_FLOWMIN) then
      bFlowHouse,@PanMinStr[nEarthIndex],nfEMin = oStar.gPanHouse_FlowMin2(nEarth,true)
      nWHourStart,nWHourStop = Xdate.GetEMinHour(oStar.fdg_ET(nPanType),nfEMin)
      @PanMinHour[nEarthIndex] = nWHourStart
      @PanMin[nEarthIndex] = Xdate.GetEMin(nfEMin)
      if (bFlowHouse) then
        @nFlowMinEarth = nEarth
      end
    end
  end

  def pan_house_in_middle_info(oStar,nPanType,nEarth,nHouse1Earth,ptEarth34)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)
    # 三方四正色彩顯示
    @PanBg34[nEarthIndex] = oStar.gHouse_Check34House(nHouse1Earth,nEarth)

    # 標示命宮三方四正
    nMiddle34 = oStar.gHouse_CheckMiddle34House(nHouse1Earth,nEarth)
    #bNeed34 = oStar.int_LifeLine
    if (nMiddle34 != Star::HOUSE_34_NONE) then
      @Pan34Pt[nMiddle34] = ptEarth34[nEarthIndex]
    end

    # four hua fly out
    @HouseOutFourHua[nEarthIndex] = oStar.gHouse_GetHouseOutFourHuaStr(nPanType,nEarth)
  end

  # personal info
  def pan_personal_info(oStar)
    @PI_Name = oStar.gPan_PI_Name()
    @PI_LunarYearOld = oStar.gPan_PI_LunarYearOld()
    @PI_IYMF = oStar.gPan_PI_IYMF()
    @PI_Animal = oStar.gPan_PI_Animal()
    @PI_SolarBirth = oStar.gPan_PI_SolarBirth2()
    @PI_LunarBirth = oStar.gPan_PI_LunarBirth2()
    @PI_GanZhiBirth = oStar.gPan_PI_GanZhiBirth()

    bSegment,nFirstTimeType,nFirstSegment,bSpace,nSegmentMove = g_eightword_SegmentGanZhi_pars(@user_id)
    @PI_Segment_GanZhiBirth = oStar.gPan_PI_SegmentGanZhiBirth(bSegment,nFirstTimeType,nFirstSegment,bSpace,nSegmentMove)

    @PI_FiveType = oStar.gPan_PI_FiveType()
    @PI_LifeHouse = oStar.gPan_PI_LifeHouse()
    @PI_BodyHouse = oStar.gPan_PI_BodyHouse()
    @PI_Life = oStar.gPan_PI_Life()
    @PI_Body = oStar.gPan_PI_Body()
    @PI_God = oStar.gPan_PI_God()
    @CI_Data1 = Star.gPan_CI_Data1(@ApFunc,@ParAll)
    @CI_Data2 = Star.gPan_CI_Data2(@ApFunc,@ParAll)
    @CI_Data3 = Star.gPan_CI_Data3(@ApFunc,@ParAll)

    @DI_Info = Array.new(3,"")
    @DI_Info[0],@DI_Info[1],@DI_Info[2] = oStar.gHouse_GetFlowTimeStr(@m_nPanType)
  end
  def makeXdate_data(oStar)
    hAppData = Hash.new
    nPanType = @m_nPanType

    hAppData["Ui_EY"] = oStar.uig_EY()
    hAppData["nSky"] = oStar.gNormal_GetSky()
    hAppData["nEarth"] = oStar.gNormal_GetEarth()
    hAppData["CurEYear"] = oStar.cdg_EY()
    hAppData["CurEMonth"] = oStar.cdg_EM()
    hAppData["CurEDate"] = oStar.cdg_ED()
    hAppData["CurELeap"] = oStar.cdg_EL()
    hAppData["CurETime"] = oStar.cdg_ET()
    hAppData["CurEMin"] = oStar.cdg_EMI()
    hAppData["CurWMin "] = oStar.cdg_WMI()
    hAppData["FlowEYear"] = oStar.fdg_EY(nPanType)
    hAppData["FlowEMonth"] = oStar.fdg_EM(nPanType)
    hAppData["FlowEDate"] = oStar.fdg_ED(nPanType)
    hAppData["FlowELeap"] = oStar.fdg_EL(nPanType)
    hAppData["FlowETime"] = oStar.fdg_ET(nPanType)
    hAppData["FlowWHour"] = oStar.fdg_WH(nPanType)
    hAppData["FlowEMin"] = oStar.fdg_EMI(nPanType)

    hAppData["ddg_EY"] = oStar.ddg_EY()
    hAppData["LargeSan"] = Star.HouseIndex2EarthIndex(oStar.gHouse_getLargeSan())
    hAppData["Large"] = oStar.gHouse_GetLarge()

    hParAll = @ParAll
    hParAll[Cfate::PAR_PAN_YEAR_DISPLAY] = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]

    hApFunc = @ApFunc.clone
    hApFunc[Cfate::LAST_XDATE_PAN_TYPE] = @ApFunc["LastXdatePanType"]

    @hXdate = Xdate.MakeXdateInfo_Star(hAppData,@m_nPanType,hApFunc,hParAll)

    @Ui_EY = oStar.uig_EY()
    @nSky = oStar.gNormal_GetSky()
    @nEarth = oStar.gNormal_GetEarth()
    @CurEYear = oStar.cdg_EY()
    @CurEMonth = oStar.cdg_EM()
    @CurEDate = oStar.cdg_ED()
    @CurELeap = oStar.cdg_EL()
    @CurETime = oStar.cdg_ET()
    @CurEMin = oStar.cdg_EMI()
    @CurWMin  = oStar.cdg_WMI()
    @FlowEYear = oStar.fdg_EY(nPanType)
    @FlowEMonth = oStar.fdg_EM(nPanType)
    @FlowEDate = oStar.fdg_ED(nPanType)
    @FlowELeap = oStar.fdg_EL(nPanType)
    @FlowETime = oStar.fdg_ET(nPanType)
    @FlowWHour = oStar.fdg_WH(nPanType)
    @FlowEMin = oStar.fdg_EMI(nPanType)

  end
  def MakeGiSuongInfo(oStar)
    if (@ApFunc["GiSuongEnable"] == false) then
      return
    end
    if (params["action"] == "panprint") then
      return
    end
    if (@m_nPanType == Cfate::PAN_NORMAL) then
      return
    end

    # 吉凶垂象
    if (params["GiSuongHouseEarth"] != nil) then
      nGiSuongHouseEarth = params["GiSuongHouseEarth"].to_i
      nHouseId = oStar.g_House_GetHouseId(@m_nPanType,nGiSuongHouseEarth)
      if (session["GiSuongHouseId_xuexiziweipan"] == nHouseId) then
        session["GiSuongHouseId_xuexiziweipan"] = nil
      else
        session["GiSuongHouseId_xuexiziweipan"] = nHouseId
      end
    end

    @aPanGiSuong = Array.new(7,nil) # NORMAL -- FLOWMIN
    if (session["GiSuongHouseId_xuexiziweipan"] != nil && session["GiSuongHouseId_xuexiziweipan"] != 99) then
      nGiSuongHouseId = session["GiSuongHouseId_xuexiziweipan"]
      @GiSuongEarth = oStar.g_House_GetEarth(@m_nPanType,nGiSuongHouseId)
      oPanGiSuong = PanGiSuong.new
      oPanGiSuong.ap_Star_Init(oStar,@m_nPanType,@UserInfo,@UserDefData,nGiSuongHouseId)

      MakeGiSuongInfo_AllPan(oStar,oPanGiSuong)
    end
  end

  def MakeGiSuongInfo_AllPan(oStar,oPanGiSuong)
    # 精實創業，用來分析使用狀況
    aaa = Pm.GetStr("IDS_S_HOUSE_NAME_#{session["GiSuongHouseId_xuexiziweipan"]}")
    # Pm.saveTestDb("ifate/star-GiSuong-#{@oUserAp.user_id}-#{@oUserAp.product_type}","GiSuongHouseId:#{session["GiSuongHouseId_xuexiziweipan"]},#{aaa}")
    nPanType = @m_nPanType

    @HasGiSuong = true
    @Ui_EY = oStar.uig_EY()
    @nSky = oStar.gNormal_GetSky()
    @nEarth = oStar.gNormal_GetEarth()
    @CurEYear = oStar.cdg_EY()
    @CurEMonth = oStar.cdg_EM()
    @CurEDate = oStar.cdg_ED()
    @CurELeap = oStar.cdg_EL()
    @CurETime = oStar.cdg_ET()
    @CurEMin = oStar.cdg_EMI()
    @CurWMin  = oStar.cdg_WMI()
    @FlowEYear = oStar.fdg_EY(nPanType)
    @FlowEMonth = oStar.fdg_EM(nPanType)
    @FlowEDate = oStar.fdg_ED(nPanType)
    @FlowELeap = oStar.fdg_EL(nPanType)
    @FlowETime = oStar.fdg_ET(nPanType)
    @FlowWHour = oStar.fdg_WH(nPanType)
    @FlowEMin = oStar.fdg_EMI(nPanType)

    if (NeedXdate?(Cfate::PAN_TENYEAR,@m_nPanType)) then
      MakeGiSuongInfo_Tenyear(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWYEAR,@m_nPanType)) then
      MakeGiSuongInfo_Flowyear(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWMONTH,@m_nPanType)) then
      MakeGiSuongInfo_Flowmonth(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWDATE,@m_nPanType)) then
      MakeGiSuongInfo_Flowdate(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWTIME,@m_nPanType)) then
      MakeGiSuongInfo_Flowtime(oStar,oPanGiSuong)
    end
  end


  def MakeGiSuongInfo_Tenyear(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId_xuexiziweipan"]

    #elsif (@m_nPanType == Cfate::PAN_TENYEAR) then
    nSky = @nSky
    nEarth = @nEarth
    if (Cfate::PAN_TENYEAR <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(30) {Array.new(2)}

      #取得十年大限開始歲數
      nLargeHouseIndex = oStar.gHouse_getLargeSan()
      nEarthIndex = Star.HouseIndex2EarthIndex(nLargeHouseIndex)
      nETenYearStartYearOld = @Large[nEarthIndex]

      # 十年大限開始年
      nHighLightStartEYear = @Ui_EY + nETenYearStartYearOld - 1

      # 萬年曆十年盤起始年
      if (nETenYearStartYearOld > 10) then
        nDisStartEYear = nHighLightStartEYear - 10
      else
        nDisStartEYear = @Ui_EY
      end
      #垂象
      nStartNum = nHighLightStartEYear - nDisStartEYear

      # 萬年曆十年盤起始年紀
      nStartEYearOld = oStar.gNormal_GetYearOld(@Ui_EY,nDisStartEYear)

      # 起始干支
      nSky = Sky.ModifySky(nSky + nStartEYearOld - 1)
      nEarth = Earth.ModifyEarth(nEarth + nStartEYearOld - 1)

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      a = [nDisStartEYear,nHighLightStartEYear,nHighLightStartEYear+10,nHighLightStartEYear+20]
      a.each_index do |i|
        hGisuongUserData[Cfate::EYear] = a[i]
        hGisuongUserData[Cfate::EMonth] = 6
        hGisuongUserData[Cfate::EDate] = 1
        hGisuongUserData[Cfate::LeapMonth] = false
        hGisuongUserData[Cfate::WHour] = 0
        hgs = oWeenApi.g_explode_text(Cfate::PAN_TENYEAR,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        h[a[i]] = hgs["explode_text"]
      end

      # new from WeenApi 1
      (1..30).each do |nRow|
        nRowIndex = nRow - 1
        nAge = nStartEYearOld + nRowIndex
        nStartEYear = nDisStartEYear + nRowIndex

        # new from WeenApi 0
        nGiSuongStartEYear = mgsi_ty_find_eyear(a,nStartEYear)
        szGi = h[nGiSuongStartEYear]["gi"][nStartEYear - nGiSuongStartEYear]
        szSuong = h[nGiSuongStartEYear]["xiong"][nStartEYear - nGiSuongStartEYear]
        # new from WeenApi 1

        # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_TenYear(nSky,nEarth,nAge,nRowIndex,nStartNum)
        aDataGiSuong[nRowIndex][0] = szGi
        aDataGiSuong[nRowIndex][1] = szSuong

        nSky = Sky.ModifySky(nSky + 1)
        nEarth = Earth.ModifyEarth(nEarth + 1)
      end
      @aPanGiSuong[Cfate::PAN_TENYEAR] = aDataGiSuong
    end
  end
  def mgsi_ty_find_eyear(a,nStartEYear)
    if (nStartEYear < a[1]) then
      return a[0]
    elsif (nStartEYear < a[2]) then
      return a[1]
    elsif (nStartEYear < a[3]) then
      return a[2]
    else
      return a[3]
    end
  end

  def MakeGiSuongInfo_Flowyear(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId_xuexiziweipan"]

#   elsif (@m_nPanType == Cfate::PAN_FLOWYEAR) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    if (Cfate::PAN_FLOWYEAR <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(2) {Array.new}
      nAge = 0

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      (0..1).each do |index|
        hGisuongUserData[Cfate::EYear] = @FlowEYear + index
        hGisuongUserData[Cfate::EMonth] = 6
        hGisuongUserData[Cfate::EDate] = 1
        hGisuongUserData[Cfate::LeapMonth] = false
        hGisuongUserData[Cfate::WHour] = 0
        hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWYEAR,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        h[@FlowEYear + index] = hgs["explode_text"]
      end
      # new from WeenApi 1

      (0..1).each do |index|
        nEYear = @FlowEYear + index

        # 取得該年月資訊
        aaMonth = Xdate.GetEastMonthArray(@hXdate["pan_xdate_flowyear_title"][index][0])
        aaMonth.each_index do |nMonthIndex|
          aData = Array.new

          aMonth = aaMonth[nMonthIndex]

          nEMonth = aMonth[0]
          nSky,nEarth = Xdate.GetLunarMonthGanZhiSkyEarth(nEYear,nEMonth)

          # new from WeenApi 0
          szGi = h[nEYear]["gi"][nMonthIndex]
          szSuong = h[nEYear]["xiong"][nMonthIndex]
          # new from WeenApi 1

          # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowYear(nSky,nEarth,nAge,nEYear)

            aData.push(szGi)
            aData.push(szSuong)

            aDataGiSuong[index].push(aData)
        end
      end
      @aPanGiSuong[Cfate::PAN_FLOWYEAR] = aDataGiSuong
    end
  end

  def MakeGiSuongInfo_Flowmonth(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId_xuexiziweipan"]

    #elsif (@m_nPanType == Cfate::PAN_FLOWMONTH) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    bLeapMonth = @FlowELeap
    if (Cfate::PAN_FLOWMONTH <= @ApFunc["LastXdatePanType"]) then
      # 取得該年月資訊
      aaMonth = Xdate.GetEastMonthArray(nEYear)
      nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,aaMonth)
      aDataGiSuong = Array.new
      nAge = 0
      nDays = aaMonth[nMonthIndex][1]

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      hGisuongUserData[Cfate::EYear] = nEYear
      hGisuongUserData[Cfate::EMonth] = nEMonth
      hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
      hGisuongUserData[Cfate::EDate] = 1
      hGisuongUserData[Cfate::WHour] = 0
      hGisuongUserData[Cfate::ETime] = 0
      hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWMONTH,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
      h[nEMonth] = hgs["explode_text"]
      # new from WeenApi 1
# nil[1]
      (1..aaMonth[nMonthIndex][1]).each do |nEDate|
        aData = Array.new

        # new from WeenApi 0
        szGi = h[nEMonth]["gi"][nEDate - 1]
        szSuong = h[nEMonth]["xiong"][nEDate - 1]
        # new from WeenApi 1

        # nSky,nEarth = Xdate.GetLunarDateGanZhiSkyEarth(nEYear,nEMonth,nEDate,6,bLeapMonth)
        # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowMonth(nEYear,nEMonth,bLeapMonth,nSky,nEarth,nAge)

        aData.push(szGi)
        aData.push(szSuong)

        aDataGiSuong.push(aData)
      end
      @aPanGiSuong[Cfate::PAN_FLOWMONTH] = aDataGiSuong
    end
  end

  def MakeGiSuongInfo_Flowdate(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId_xuexiziweipan"]

#   elsif (@m_nPanType == Cfate::PAN_FLOWDATE) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    bLeapMonth = @FlowELeap
    nEDate = @FlowEDate
    if (Cfate::PAN_FLOWDATE <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(2) {Array.new}

      # 年月表頭
      nEYear = @FlowEYear
      nEMonth = @FlowEMonth
      bLeapMonth = @FlowELeap

      # 日的訊息
      nEDate = @FlowEDate
      (0..1).each do |index|
        # new from WeenApi 0
        oWeenApi = WeenApi.new
        h = Hash.new
        hGisuongUserData[Cfate::EYear] = nEYear
        hGisuongUserData[Cfate::EMonth] = nEMonth
        hGisuongUserData[Cfate::EDate] = nEDate
        hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
        hGisuongUserData[Cfate::WHour] = 0
        hGisuongUserData[Cfate::ETime] = 0
        hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWDATE,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        h[nEDate] = hgs["explode_text"]
        # new from WeenApi 1

        # 日表頭
        (1..12).each do |nEarth|
          nETime = nEarth - 1
          aData = Array.new

          # new from WeenApi 0
          szGi = h[nEDate]["gi"][nEarth - 1]
          szSuong = h[nEDate]["xiong"][nEarth - 1]
          # new from WeenApi 1

          # nSky,nEarth = Xdate.GetLunarTimeGanZhiSkyEarth(nEYear,nEMonth,nEDate,nETime,bLeapMonth)
          # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowDate(nEYear,nEMonth,bLeapMonth,nEDate,nSky,nEarth,0)


          aData.push(szGi)
          aData.push(szSuong)

          aDataGiSuong[index].push(aData)
        end
        # 換到下一天
        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      end
      @aPanGiSuong[Cfate::PAN_FLOWDATE] = aDataGiSuong
    end
  end
  # 選流日時，萬年曆顯示的流月，流年，十年的吉凶錯誤；流日流時的吉凶未做
  def MakeGiSuongInfo_Flowtime(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId_xuexiziweipan"]

#   elsif (@m_nPanType == Cfate::PAN_FLOWDATE) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    bLeapMonth = @FlowELeap
    nEDate = @FlowEDate
    nETime = @FlowETime
    nWHour = @FlowWHour
    if (Cfate::PAN_FLOWTIME <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(2) {Array.new}

      # 年月表頭
      nEYear = @FlowEYear
      nEMonth = @FlowEMonth
      bLeapMonth = @FlowELeap

      # 日的訊息
      nEDate = @FlowEDate
      (0..1).each do |index|
        # new from WeenApi 0
        oWeenApi = WeenApi.new
        h = Hash.new
        hGisuongUserData[Cfate::EYear] = nEYear
        hGisuongUserData[Cfate::EMonth] = nEMonth
        hGisuongUserData[Cfate::EDate] = nEDate
        hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
        hGisuongUserData[Cfate::ETime] = Pm.TestNo12(nETime)
        hGisuongUserData[Cfate::WHour] = Xdate.ETimeFirstHour(nETime)
        hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWTIME,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        h[hGisuongUserData[Cfate::ETime]] = hgs["explode_text"]
        # new from WeenApi 1

        # 日表頭
        # (1..12).each do |nEarth|
        (1..12).each do |i|
          # nETime = nEarth - 1
          aData = Array.new

          # new from WeenApi 0
          szGi = h[hGisuongUserData[Cfate::ETime]]["gi"][i - 1]
          szSuong = h[hGisuongUserData[Cfate::ETime]]["xiong"][i - 1]
          # new from WeenApi 1

          # nSky,nEarth = Xdate.GetLunarTimeGanZhiSkyEarth(nEYear,nEMonth,nEDate,nETime,bLeapMonth)
          # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowDate(nEYear,nEMonth,bLeapMonth,nEDate,nSky,nEarth,0)


          aData.push(szGi)
          aData.push(szSuong)

          aDataGiSuong[index].push(aData)
        end
        # 換到下一天
        nETime += 1
        if (nETime == 1) then
          nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
        end
      end
      @aPanGiSuong[Cfate::PAN_FLOWTIME] = aDataGiSuong
    end
  end

  # 個人事件
  def pan_prepare_personalevent_data()
    tNow = time_now_local()
    oUserCustomer = UserCustomer.find_by_id(session["customer_id_xuexiziweipan"])
    if (oUserCustomer == nil) then
      @starYearSelect = 1901
    else
      @starYearSelect = oUserCustomer.wyear
    end
      if (oUserCustomer == nil || oUserCustomer.wyear <= tNow.year) then
        @stopYearSelect = tNow.year
      else
        @stopYearSelect = oUserCustomer.wyear + 10
      end

    @pe_title_data = Pm.GetStrWithQuote("IDS_PE_TITLE")
    # h.invert
    @pe_mains = Cfate.pan_par_dbfield2hash(Pm.GetPeStr("pe_mains"))
    @pe_main_key_default = @pe_mains.values[0]

    @pe_subs = Cfate.pan_par_dbfield2hash(Pm.GetPeStr(@pe_main_key_default))
    @pe_sub_key_default = @pe_subs.values[0]
    @pe_time = tNow

    @personal_events = PersonalEvent.available.check_option_user_id(@user_id).check_user_customer_id(session["customer_id_xuexiziweipan"]).check_main_key_pe().last(3).reverse
    @canAddPe = true #session["customer_id_xuexiziweipan"] != nil
  end


  def authenticate
    authenticate_xuexi_ziwei(Xuexi_Ziwei.pars_ap_name(),"xuexi")
    @sLang = my_current_lang()
    @nav_type = "xuexi-function"
  end
  def logged_in?
    return (current_user != nil)
  end
  def authenticate_xuexi_ziwei(new_ap_name,ap_namespace)
    if (Pm.getSystemValue("WebSpeedTest") == "1") then
      @user_name = "<EMAIL>" #current_user.email
      @user_id = 3
    else
      #@user_name = current_user.name
      @user_name = current_user.email if logged_in?
      @user_id = current_user.id if logged_in?
    end
    @user_level = ""
    @ap_name = g_user_ap_name(new_ap_name,@user_id,ap_namespace)

    # session["userlevel"] = @user_level

    getUserCurrentAp(@user_id)

    # session["ap_name"] = @ap_name
    # session["ap_version"] = @ap_version
    # session["user_name"] = { :value => @user_name, :expires => 1.minute.from_now }
    @ParAll = Star.get_pan_pars(@user_id,g_can_use_pars(@oUserAp))
    # @ParAll = Star.get_pan_pars(nil,false)
    star_version_check()

    @action_result = params["action_result"]

    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?

    @admin = current_user.roles? :admin
    @can_add_customer,@customer_max_count = g_can_add_customer(@user_id,@admin)
  end
  def getUserCurrentAp(user_id)
    # @oUserAp = g_getUserCurrentAp(user_id,@ap_name)
    @oUserAp = g_getAnyUserDemoUserAp(user_id,"star")
    @showAdvertise = true
    @firstShowAdvertise = "pan_xdate_block"
    @canUsePan = true #g_canUsePan(session["customer_id_xuexiziweipan"])
  end

  def star_version_check()
    # 權限控管
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    # oUserAp = g_getUserCurrentAp(@user_id,@ap_name)
    # hApFunc = g_ap_product_allfunc_hash(oUserAp)
    # @ApFunc[Cfate::FUNC_HALL_NUMBER] = hApFunc[Cfate::FUNC_HALL_NUMBER]
    @hHallNumberAdver = find_last_hallnumber_adver(@ap_name,@oUserAp,@ApFunc)
    @hCanDeal = canDealCheck(@user_id,@oUserAp,@ap_name)
    @hCanDeal = nil
    @main_products = g_getMainProducts(@ap_name)
    @canAddPe = true #session["customer_id_xuexiziweipan"] != nil
  end
  def xuexi_data(hPars)
    hPars = xuexi_pars(hPars)
    xuexi_pan_info(hPars)
    # puts "hPars 2: #{hPars}"
    return hPars
  end
  def class_item_name(taitou,xuexi_class,xuexi_item)
    s = Pm.t("xuexi.navbar.#{taitou}")
    s += Pm.t("xuexi.guan.dijigua",:xuexi_class => @xuexi_class, :xuexi_item => @xuexi_item)
    if (Xuexi_Ziwei.last_class_itemcount(@xuexi_class,@xuexi_item)) then
      s += Pm.t("xuexi.final_test")
    end
    return s
  end
  def xuexi_pars(hPars)
    if (hPars["PanPar"] == nil) then
      # 盤中換盤或Xdate換盤
      hPars["timestamp"] = session["ziweipars_par"]["timestamp"]
      hPars["PanPar"] = session["ziweipars_par"]["PanPar"]
      hPars["par_key"] = session["ziweipars_par"]["par_key"]
    else
      if (hPars["PanPar"].to_i == -1) then
        #  課程大綱
      else
        if (hPars["par_key"] == nil) then
          # 設定排盤參數 等列出該參數所有功能
        else
          # 某一個參數
          pantype = Cfate::PAN_NORMAL
          if (hPars["PanType"] == nil) then
            # 第一次進該參數，設定預設的pantype
            par_key = hPars["par_key"]
            pantype = find_pantype(par_key)
          else
            pantype = hPars["PanType"].to_i
          end
          hPars["PanType"] = pantype
          if (session["ziweipars_par"]["par_key"] != hPars["par_key"]) then
            # 換參數
            hPars["timestamp"] = find_timestamp(hPars,hPars["par_key"])
            hPars["PanPar"] = hPars["PanPar"].to_i
            hPars["PanType"] = hPars["PanType"].to_i
          else
            if hPars["pars"] != nil then
              # 從Pantype中來的，已不用
              pars = Cfate.pan_par_dbfield2hash(hPars["pars"])
              hPars["timestamp"] = pars["timestamp"]
              hPars["PanPar"] = pars["PanPar"].to_i
              hPars["par_key"] = pars["par_key"]
              hPars["PanType"] = pars["PanType"].to_i
            else
              # 更改參數的值
              hPars["timestamp"] = session["ziweipars_par"]["timestamp"]
              hPars["PanPar"] = session["ziweipars_par"]["PanPar"]
              hPars["par_key"] = session["ziweipars_par"]["par_key"]
              hPars["PanType"] = hPars["PanType"].to_i
            end
          end
        end
      end
    end
    hPars = set_ziweipars_session(hPars,@ParAll)
    return hPars
  end
  def find_pantype(par_key)
    # pantype = Cfate::PAN_FLOWYEAR if (par_key == Star::PAR_FLOWYEAR_HUA)
    # pantype = Cfate::PAN_FLOWYEAR if (par_key == Star::PAR_FLOWSKY_TYPE)
    return Cfate::PAN_FLOWYEAR if ([Star::DIS_FLOWCHAN,Star::DIS_FLOWLUAN,Star::DIS_FLOWMA,Star::DIS_FLOWLYT,Star::PAR_FLOWLIFE_HOUSETYPE,Star::PAR_SMALLSAN_TYPE,Star::PAR_FLOWSKY_TYPE_CALD_FLOWYEAR].include?(par_key))
    return Cfate::PAN_FLOWMONTH if (par_key == Star::PAR_GOD_TYPE)
    return Cfate::PAN_FLOWTIME if (par_key == Star::PAR_LIUSHI_MINGGONG)
    return Cfate::PAN_NORMAL
  end
  def xuexi_pan_info(hPars)
    @PanPar = -1
    @PanPar = hPars["PanPar"].to_i if hPars["PanPar"] != nil
    @par_key = hPars["par_key"]
    @par_keys = get_pan_par_key_strs(@PanPar)
    par_key_idx = @par_keys.index(@par_key)
    s = nil
    i = par_key_idx
    while i != nil && i + 1 < @par_keys.length && s == nil
      s = Star.pan_par_key_str(@par_keys[i + 1],@ParAll)
      if (s != nil) then
        @next_par_key = @par_keys[i + 1]
        @next_panpar = @PanPar
      end
      # Increment.
      i += 1
    end
    if (@next_par_key == nil) then
      @next_panpar = get_next_pan_par_with_keys(@PanPar,1)
      if (@next_panpar != nil) then
        parkeys = get_pan_par_key_strs(@next_panpar)
        if (parkeys != []) then
          @next_par_key = parkeys[0]
        end
      end
    end
    s = nil
    i = par_key_idx
    while i != nil && i - 1 >= 0 && s == nil
      s = Star.pan_par_key_str(@par_keys[i - 1],@ParAll)
      if (s != nil) then
        @pre_par_key = @par_keys[i - 1]
        @pre_panpar = @PanPar
      end
      # Increment.
      i -= 1
    end
    if (@pre_par_key == nil) then
      @pre_panpar = get_next_pan_par_with_keys(@PanPar,-1)
      if (@pre_panpar != nil) then
        parkeys = get_pan_par_key_strs(@pre_panpar)
        if (parkeys != []) then
          @pre_par_key = parkeys[parkeys.length - 1]
        end
      end
    end
    @timestamp = hPars["timestamp"]
    star_pan_data(hPars)
  end
  def get_next_pan_par_with_keys(panpar,n)
    a = [Star::PanPar_Type,Star::PanPar_Interface,Star::PanPar_Display,Star::PanPar_HallNumber]
    i = a.index(panpar)
    if i == nil || (i + n < 0) || (i + n >= a.length) then
      return nil
    end
    return a[i + n]
  end
  def get_pan_par_key_strs(panpar)
    par_keys = []
    if ([Star::PanPar_Type,Star::PanPar_Interface,Star::PanPar_Display,Star::PanPar_HallNumber].include?(panpar)) then
      par_keys = Star.pan_par_keys(panpar)
    end
    if (panpar == Star::PanPar_Display) then
      a = []
      a.push(Star::DIS_ASTAR)
      a.push(Star::DIS_BSTAR)
      (1..Star::A_STAR_COUNT).each do |nStar|
        par_keys.delete("#{Star::DIS_ASTAR}_#{nStar}")
      end
      (1..Star::B_STAR_COUNT).each do |nStar|
        par_keys.delete("#{Star::DIS_BSTAR}_#{nStar}")
      end
      a += par_keys
      par_keys = a.clone
    end
    if (panpar == Star::PanPar_HallNumber) then
      par_keys.delete(Cfate::ADV_HALL_NUMBER_1)
      par_keys.delete(Cfate::ADV_HALL_NUMBER_2)
      par_keys.delete(Cfate::ADV_HALL_NUMBER_3)
    end
    return par_keys
  end
  def NeedXdate?(nDisPanType,nCurPanType)
    if (@ApFunc["showXdate"] == false) then  # 免費版
      return false
    end
    if (nDisPanType > @ApFunc["LastXdatePanType"]) then
      return false
    end
    # if (Cfate::PAN_NORMAL == nCurPanType) then
    #   return false
    # end
    # if (Cfate::PAN_TENYEAR == nCurPanType) then
    #   return false
    # end
    # if (Cfate::PAN_FLOWTIME == nCurPanType) then
    #   return false
    # end
    # if (Cfate::PAN_FLOWMIN == nCurPanType) then
    #   return false
    # end
    if (nDisPanType <= nCurPanType) then
      return true
    end
    if (nDisPanType == nCurPanType) then
      return true
    end
    if (nDisPanType == (nCurPanType - 1)) then
      return true
    end
    return false
  end

end
