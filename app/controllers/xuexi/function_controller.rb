class Xuexi::FunctionController < Ifate::FunctionController
  before_action :authenticate_user!, :except => []
  before_action :authenticate, :except => []
  layout "xuexi", :except => []

  def kecheng
    redirect_to :controller => xuexi_ap_name_to_controller(@ap_name) ,:action => "kecheng"
  end

  def xuexi
    redirect_to :controller => xuexi_ap_name_to_controller(@ap_name) ,:action => "xuexi"
  end

  def lianxi
    redirect_to :controller => xuexi_ap_name_to_controller(@ap_name) ,:action => "lianxi"
  end

  def chuangguan
    redirect_to :controller => xuexi_ap_name_to_controller(@ap_name) ,:action => "chuangguan"
  end
  def fuwu_shuoming
    redirect_to :controller => xuexi_ap_name_to_controller(@ap_name) ,:action => "fuwu_shuoming"
  end
  protected
  def authenticate
    authenticate_function(params["ap_name"],"xuexi")
  end
end
