require("Xuexi_Ziwei.rb")
require("Eightword.rb")

class Xuexi::ZiweiController < ApplicationController
  before_action :authenticate_user!, :except => []
  before_action :authenticate, :except => []
  layout "xuexi", :except => [:pan_ajax]
  # layout "xuexi_ziwei", :except => [:pan_ajax]
  # layout "xuexi", :only => [:kecheng]

  # 學習內容分為大項跟小項，共兩階 例 1.1是第一級 第一項
  # 分為 xuexi_class 級 xuexi_item 項

  # 在盤裡面操作的說明
  def pan_event_ajax
    hPars = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    star_pan_data(hPars)
    @answer = Xuexi_Ziwei.xuexi_pan_event(hPars,@oStar,@m_nPanType)

    render :partial => "xuexi/ziwei/pan_event_ajax"
  end
  # 在rightbar中操作時，出現的說明
  def pan_ajax
    hPars = params["submit_data"]
    if (hPars == nil) then
      hPars = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
      hPars = Cfate.pan_par_dbfield2hash(hPars["xuexi_data"])
    end
    @gong_dizhi = hPars["gong_dizhi"].to_i if hPars["gong_dizhi"] != nil
    @xings = hPars["xings"].split("__") if hPars["xings"] != nil
    star_pan_data(hPars)
    render :partial => "xuexi/ziwei/pan_ajax"
  end
  def kecheng
    hPars = params.clone
    xuexi_pars(hPars,"muqian_class_item")
    @kechengs = Xuexi_Ziwei.kecheng_strs(@ap_name)
    render :layout => "xuexi"
  end

  def xuexi
    xuexi_data(params.clone)
    # temporary remove by myywar@gmail
    # check_if_last_chuangguan()

    # Xuexi_Ziwei.check_gongs_gong_jia_xings_timestamp(@m_nPanType,@UserDefData,@ParAll)
    # Xuexi_Ziwei.check_gongs_gong_xia_xings_timestamp(@m_nPanType,@UserDefData,@ParAll)
  # a = Xuexi_Ziwei.find_gongs_gong_jia_xings_timestamp()
  # (1..12).each do |gong|
  #   a = Xuexi_Ziwei.find_gong_gong_jia_xings_timestamp(gong)
  # end
  end
  def xuexi_ajax
    hPars = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    hPars = Cfate.pan_par_dbfield2hash(hPars["xuexi_data"])
    star_pan_data(hPars)
    if (hPars["mudi"] == "jieguo") then
      xuexi_pars(hPars,"db_class_item")
      @shuoming = Xuexi_Ziwei.xuexi_shuoming(hPars,@oStar,@m_nPanType)
      render :partial => "xuexi/ziwei/xuexi_shuoming"
    else
      xuexi_data(hPars.clone)
      @shuoming = Xuexi_Ziwei.xuexi_shuoming(hPars,@m_nPanType,@UserInfo,@UserDefData,@ParAll,@ApFunc)
      render :partial => "xuexi/ziwei/xuexi"
    end
  end

  def lianxi
    lianxi_timu(params.clone)
    if (Xuexi_Ziwei.itemcount_in_class(@xuexi_class) == @xuexi_item) then
      redirect_to :action => "chuangguan"
    end
    # temporary remove by myywar@gmail
    # check_if_last_chuangguan()
  end
  def lianxi_ajax
    hPars = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    if (hPars["mudi"] == "jieguo") then
      xuexi_pars(hPars,"db_class_item")
      @shuoming = Xuexi_Ziwei.lianxi_jieguo(hPars)
      render :partial => "xuexi/ziwei/lianxi_shuoming"
    else
      lianxi_timu(hPars.clone)
      render :partial => "xuexi/ziwei/lianxi"
    end
  end

  def chuangguan
    @shuoming = ""
    chuangguan_timu(params.clone,"new_class_item")
    @mudi = "timu"
  end
  def chuangguan_ajax
  puts "chuangguan_ajax : #{params}"
    hPars = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    hPars["user_id"] = @user_id
    @mudi = hPars["mudi"]
    @shuoming = ""
    if (hPars["mudi"] == "jieguo") then
      xuexi_pars(hPars,"db_class_item")
      @dijiti = hPars["dijiti"].to_i
      @jiti = Xuexi_Ziwei.chuangguan_timusu(@xuexi_class,@xuexi_item)
      @shuoming,@shuoming_all,@nextmsg = Xuexi_Ziwei.chuangguan_jieguo(hPars,@ap_name,@xuexi_class,@xuexi_item)
      @chuangguan = hPars.clone

      render :partial => "xuexi/ziwei/chuangguan_shuoming"
    elsif (hPars["mudi"] == "timu") then
      chuangguan_timu(hPars.clone,"db_class_item")
      render :partial => "xuexi/ziwei/chuangguan"
    end
  end

  def xpan
    hPars = params.clone
    hPars["timestamp"] = Xdate.make_now_timestamp()
    star_pan_data(hPars)
  end
  def xparedit
    @nav_type = "xuexi-ziwei_paredit"
    hPars = params["submit_data"]
    if (hPars == nil) then
      hPars = params
    end
    if (@ApFunc[Cfate::FUNC_SET_PARS]) then
      if (hPars["PanPar"] == nil) then
        @nPanPar = Star::PanPar_Type
      else
        @nPanPar = hPars["PanPar"].to_i
      end
      if (hPars["PanParReset"] == "1") then
        hParAll = Xuexi_Ziwei.pan_par_init_each(@nPanPar,@ParAll)
        panPar2Db(hParAll)
      elsif (hPars["PanParUpdate"] == "1") then
        hParAll = Xuexi_Ziwei.pan_par_assign_each(@nPanPar,@ParAll,hPars)
        panPar2Db(hParAll)
        @action_result = Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
      elsif (hPars["PanParCopyAction"] == "1") then
        panparcopy_action(hPars)
        @action_result = Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
      end
      @ParAll = Xuexi_Ziwei.get_pan_pars(@user_id,g_can_use_pars(@oUserAp))

      render :layout => 'xuexi'
    else
      redirect_to :action => "xuexi"
    end
  end
  # def fuwu_shuoming
  #   render :layout => 'xuexi'
  # end
  protected
  def check_if_last_chuangguan()
    if (Xuexi_Ziwei.last_class_itemcount(@xuexi_class,@xuexi_item)) then
      redirect_to xuexi_ziwei_chuangguan_path()
    end
  end
  def star_pan_data(hPars)
    @m_nPanType = Cfate::PAN_NORMAL
    hPars["sLang"] = @sLang
    xuexi_pan_data(hPars)
    @Pan34Pt = Array.new(4,[0,0])
    @uig_timestamp = hPars["timestamp"]
  end
  def xuexi_pan_data(hPars)
    # 精實創業，用來分析使用狀況
    # Pm.saveTestDb("#{hPars[:controller]}-#{hPars[:action]}-#{@oUserAp.user_id}-#{@oUserAp.product_type}",hPars.to_json)

    # 處理使用者傳入之資料及相關物件之準備
    xuexi_pan_prepare_data(hPars)

    # @ParAll[Star::INT_34COLOR] = Star::INT_34_MONO
    # @ParAll[Star::INT_LIFELINE_DISPLAY] = Cfate::PAR_FALSE
    @ApFunc["n_SkyFindStar_house"] = 0
    if ([@xuexi_class,@xuexi_item] == [1,8]) then
      @ApFunc["n_SkyFindStar_house"] = 4095
    end
    @oStar = Star.new
    @oStar.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll)

    # 排盤資料
    pan_pan_info()

    # 萬年曆
    pan_xdate_info()

    # 個人事件資料
    pan_prepare_personalevent_data()

    # 產品購買資訊
    pan_userap_info()

    @edit_customer_url = "/ifate/customer/edit"
    @select_customer_url = "/ifate/customer/index"
    @url_back = "/ifate/star/pan"

    @aUrlKey = []
    @aUrlValue = []

    g_userap_addone_usecount(hPars["action"],@m_nPanType,@oUserAp)
  end
  def xuexi_pan_prepare_data(hData=nil)
    # 排盤資訊
    @PanLastYear = Cfate::PAN_LAST_YEAR # 萬年曆最後一年

    # 設定客戶資訊
    hUserInfo = set_pan_customer_info(hData)
    @UserInfo = hUserInfo

    # 處理流盤時間
    hUserDefData = set_pan_user_def_data(hData)
    @UserDefData = hUserDefData

    # # 開始處理排盤相關資訊
    # @WYear = hUserInfo[Cfate::WYear]
    # @WMonth = hUserInfo[Cfate::WMonth]
    # @WDate = hUserInfo[Cfate::WDate]
    # @WHour = hUserInfo[Cfate::WHour]
    # @Sex = hUserInfo[Cfate::Sex]
    # @Name = hUserInfo[Cfate::Name]

    # @UA_EYear = hUserDefData[Cfate::EYear]
    # @UA_EMonth = hUserDefData[Cfate::EMonth]
    # @UA_EDate = hUserDefData[Cfate::EDate]
    # @UA_WHour = hUserDefData[Cfate::WHour]
    # @UA_WMinute = hUserDefData[Cfate::WMinute]
    # @UA_LeapMonth = hUserDefData[Cfate::LeapMonth]
    # @UA_HouseName = hUserDefData[Star::HOUSE_NAME]

    # @bPanChange = true
    # @bDeclare = false
  end
  def set_pan_customer_info(hData=nil)
    hUserInfo = Hash.new
    hUserInfo["remote_ip"] = remote_ip()

    # 預設用現在時間當客戶生日排盤,男生
    t = time_now_local()
    hUserInfo[Cfate::WYear] = t.year
    hUserInfo[Cfate::WMonth] = t.month
    hUserInfo[Cfate::WDate] = t.day
    hUserInfo[Cfate::WHour] = t.hour
    hUserInfo[Cfate::Sex] = false
    hUserInfo[Cfate::Name] = "Profate"

    @m_nPanType = Cfate::PAN_NORMAL

    if ((hData != nil) && (hData["wyear"] != nil)) then
      hUserInfo[Cfate::WYear] = hData["wyear"]
      hUserInfo[Cfate::WMonth] = hData["wmonth"]
      hUserInfo[Cfate::WDate] = hData["wday"]
      hUserInfo[Cfate::WHour] = hData["hour"]
      hUserInfo[Cfate::WMinute] = hData["minute"]
      hUserInfo[Cfate::Sex] = hData["sex"].to_i == 1 ? false : true
      hUserInfo[Cfate::Name] = hData["name"]
    end
    if (hData["timestamp"] != nil) then
      timestamp = hData["timestamp"]
      y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp)
      hUserInfo[Cfate::WYear] = y
      hUserInfo[Cfate::WMonth] = m
      hUserInfo[Cfate::WDate] = d
      hUserInfo[Cfate::WHour] = h
      hUserInfo[Cfate::WMinute] = min
      hUserInfo[Cfate::Sex] = sex == 1 ? false : true
      hUserInfo[Cfate::Name] = timestamp
      hUserInfo[Cfate::Multiple_births] = mb
    end
    return hUserInfo
  end
  def get_cur_date_info()
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
    hUserDefData = Hash.new
    hUserDefData[Cfate::EYear] = nEYear
    hUserDefData[Cfate::EMonth] = nEMonth
    hUserDefData[Cfate::EDate] = nEDate
    hUserDefData[Cfate::LeapMonth] = bLeapMonth
    hUserDefData[Cfate::WHour] = nWHour
    hUserDefData[Cfate::WMinute] = nWMin
    return hUserDefData
  end
  def set_pan_user_def_data(hData)
    # 處理流盤時間
    if ((hData["UD_EYear"] == nil)) then
      hUserDefData = get_cur_date_info()
    else
      #若有傳入,以傳入為主
      if (hData["UD_EYear"] != nil) then
        hUserDefData = Hash.new
        hUserDefData[Cfate::EYear] = hData["UD_EYear"].to_i
        hUserDefData[Cfate::EMonth] = hData["UD_EMonth"].to_i
        hUserDefData[Cfate::EDate] = hData["UD_EDate"].to_i
        hUserDefData[Cfate::LeapMonth] = (hData["UD_ELeap"].to_i == 0) ? false : true
        hUserDefData[Cfate::WHour] = hData["UD_WHour"].to_i
        hUserDefData[Cfate::WMinute] = hData["UD_WMin"].to_i
      end
    end

    @nLeapMonth = hUserDefData[Cfate::LeapMonth] ? 1 : 0
    hUserDefData[Star::HOUSE_NAME] = hData["star_house"] == nil ? 99 : hData["star_house"].to_i

    # session["user_def_data"] = hUserDefData
#puts("session["user_def_data"]#{session["user_def_data"][Cfate::EYear]}")
    return hUserDefData
  end
  def pan_pan_info()
    nPanType = @m_nPanType
    @IsSelFourHuaStack = @oStar.IsSelFourHuaStack?()
    @IsMioWongNum = @oStar.IsMioWongNum?()

    pan_init_instant_var(@oStar)

    # 三方四正，用比例的方式
    ptEarth34 = [[75,100],[25,100],[0,100],[0,75],[0,25],[0,0],[25,0],[75,0],[100,0],[100,25],[100,75],[100,100]]

    nHouse1Earth = @oStar.gHouse_GetFirstHouseEarth(nPanType)
    (1..12).each do |nEarth|
      # 各宮共同資訊
      pan_house_general_info(@oStar,nPanType,nEarth)

      # 各流盤資訊
      pan_house_flow_info(@oStar,nPanType,nEarth)

      # 各宮在中宮的資訊 三方四正及 四化飛出
      pan_house_in_middle_info(@oStar,nPanType,nEarth,nHouse1Earth,ptEarth34)
    end

    # personal info
    pan_personal_info(@oStar)
  end

  def pan_xdate_info()
    # 萬年曆
    makeXdate_data(@oStar)


    # 吉凶垂象
    MakeGiSuongInfo(@oStar)
  end

  def pan_userap_info()
    oProduct = g_getProductByUserAp(@oUserAp)
    @buy_info = ""
    @ap_version_desc = oProduct.product_description if oProduct != nil
    if (oProduct.demo) then
      @ap_version_desc += "(#{Pm.GetStrWithColon("IDS_S_UNTILL")}#{@oUserAp.end_date.strftime("%Y-%m-%d")})"
    else
      if (oProduct.free) then
        if (g_isDemoExist(@user_id,@ap_name)) then
          @buy_info = "buy"
        else
          @buy_info = "demo"
        end
        @ap_version_desc = Pm.GetStr("IDS_S_APP_PAN_NOT_BUIED")
      else
        if ((@oUserAp.end_date < Xdate.GetNowRails() + 14.days) && getUserApCounts(@user_id,@ap_name) < 2) then
          @buy_info = "rebuy"
        end
        @ap_version_desc = Pm.GetStr("IDS_S_APP_PAN_BUIED")
      end
    end
      if (@customer_user_id == 0) then
        @ap_version_desc = Pm.GetStr("IDS_S_ALL_SHOW")
      end
  end
  def pan_init_instant_var(oStar)
    @Doctor = Array.new(12)
    @God = Array.new(12)
    @YearGod = Array.new(12)  # 歲建
    @YearStar = Array.new(12)  # 將星
    @AStarInfos = Array.new(12)
    @AStarMioWong = Array.new(12)
    @AStarOrgFourHua = Array.new(12)
    @AStarFourHuaSky = Array.new(12)
    @BStarInfos = Array.new(12)
    @BStarMioWong = Array.new(12)
    @Small = Array.new(12)
    @Large = Array.new(12)
    @LargeSan = Star.HouseIndex2Earth(oStar.gHouse_getLargeSan())
    @BodyHouse = oStar.gPanHouse_GetBodyHouse()
    @HouseId = Array.new(12)
    @HouseName = Array.new(12)
    @HouseName_Normal = Array.new(12)
    @HouseFiveStr = Array.new(12)
    @HouseFive = Array.new(12)
    @HouseSky = Array.new(12)
    @SkyAStarFourHua = Array.new(12)
    @HouseSkyStr = Array.new(12)
    @HouseEarthStr = Array.new(12)
    @PanSmallSanYear = Array.new(12)
    @PanFlowYear = Array.new(12)
    @PanYear = Array.new(12)
    @PanYearOld = Array.new(12)
    @PanMonth = Array.new(12)
    @PanMonthStr = Array.new(12)
    @PanDate = Array.new(12)
    @PanTime = Array.new(12)
    @PanTimeStr = Array.new(12)
    @PanMinHour = Array.new(12)
    @PanMin = Array.new(12)
    @PanMinStr = Array.new(12)
    @SStarInfos = Array.new(12)
    @Star7Infos = Array.new(12)

    @PanBg34 = Array.new(12)
    @HouseOutFourHua = Array.new(12) {Array.new(4,"")}
    @AStarSelfFourHua = Array.new(12)
    @AStarSelfFourHuaCount = Array.new(12)
    @Pan34Pt = Array.new(4,[0,0])

    @HasGiSuong = false
    @GiSuongEarth = nil

    @SmallDisplay = oStar.gHouse_GetSmallDisplay()
    @LargeDisplay = oStar.gHouse_GetLargeDisplay()
    @PI_GanZhiBirthDisplay = oStar.gHouse_Get8WordsDisplay() && @ApFunc["GanZhiBirthDisplay"]
    @SmallYearRevertDisplay = oStar.gHouse_GetSmallYearRevertDisplay() && @ApFunc["SmallYearRevertDisplay"]
    @DoctorDisplay = oStar.gHouse_GetDoctorDisplay()
    @GodDisplay = oStar.gHouse_GetGodDisplay()
    @YearStarDisplay = oStar.gHouse_GetYearStarDisplay()
    @YearGodDisplay = oStar.gHouse_GetYearGodDisplay()
    @LyygDisplay = oStar.gHouse_GetLyygDisplay() && @ApFunc["LYYGDisplay"]
    @Star7Display = @ApFunc["Star7Display"]
    @ShowStar7 = oStar.gHouse_Get7StarDisplay() && @ApFunc["Star7Display"]
  end

  def pan_house_general_info(oStar,nPanType,nEarth)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)

    @Doctor[nEarthIndex] = oStar.gHouse_GetDoctorName(nPanType,nEarth)
    @God[nEarthIndex] = oStar.gHouse_GetGodName(nPanType,nEarth)
    @AStarInfos[nEarthIndex] = oStar.gHouse_GetAStarInfos(nPanType,nEarth,false)

    @AStarMioWong[nEarthIndex] = oStar.gHouse_GetAStarsMioWongInfo(nPanType,nEarth,false,false)
    @AStarOrgFourHua[nEarthIndex] = oStar.gHouse_GetAStarsFourHua(nPanType,nEarth)
    @AStarSelfFourHua[nEarthIndex],@AStarSelfFourHuaCount[nEarthIndex] = oStar.gHouse_GetAStarsSelfFourHua(nPanType,nEarth)
    @AStarSelfFourHuaDisplay = oStar.gHouse_GetAStarsSelfFourHuaDisplay()

    fa_starInfos = oStar.get_HouseFAStarInfos(nPanType,nEarth)
    @AStarInfos[nEarthIndex] = @AStarInfos[nEarthIndex] + fa_starInfos

    aStar,bUseOpp = oStar.gHouse_GetAStars(nPanType,nEarth,false)
    @AStarFourHuaSky[nEarthIndex] = oStar.gNormal_FindAStarsFourHuaSky(aStar)

    @YearGod[nEarthIndex] = oStar.gHouse_GetYearGodName(nPanType,nEarth)
    @YearStar[nEarthIndex] = oStar.gHouse_GetYearStarName(nPanType,nEarth)
    @SStarInfos[nEarthIndex] = oStar.get_HouseSStarInfos(nPanType,nEarth)

    @BStarInfos[nEarthIndex] = oStar.gHouse_GetBStarInfos(nPanType,nEarth)
    @BStarInfos[nEarthIndex] = @BStarInfos[nEarthIndex] + oStar.get_HouseFBStarInfos(nPanType,nEarth)
    @BStarMioWong[nEarthIndex] = oStar.gHouse_GetBStarsMioWongInfo(nPanType,nEarth)

    @HouseId[nEarthIndex] = oStar.g_House_GetHouseId(nPanType,nEarth)
    @HouseName[nEarthIndex] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
    @HouseName_Normal[nEarthIndex] = oStar.gHouse_GetHouseNameWithQuota(Cfate::PAN_NORMAL,nEarth)
    @HouseFiveStr[nEarthIndex] = oStar.gHouse_FiveStr(nPanType,nEarth)
    @HouseFive[nEarthIndex] = oStar.gHouse_Five(nPanType,nEarth)
    @HouseSkyStr[nEarthIndex] = oStar.gHouse_GetSkyName(nPanType,nEarth)
    @HouseSky[nEarthIndex] = oStar.gHouse_GetSky(nPanType,nEarth)
    @SkyAStarFourHua[nEarthIndex] = oStar.gNormal_FindSkyFourHuaStar(@HouseSky[nEarthIndex])

    @HouseEarthStr[nEarthIndex] = oStar.gHouse_GetEarthName(nEarth)

    @Star7Infos[nEarthIndex] = oStar.gHouse_Get7Star(nEarth)

    # 祿存疊四層(不含生年祿存)
    # 紫微工具 新增功能 #3
    astarInfos = oStar.star_GetLuYangTuoInfos(nPanType,nEarth)
    @AStarInfos[nEarthIndex] = @AStarInfos[nEarthIndex] + astarInfos
    @AStarMioWong[nEarthIndex] = @AStarMioWong[nEarthIndex] + oStar.star_GetAStarsMioWongInfo(fa_starInfos)
    @AStarMioWong[nEarthIndex] = @AStarMioWong[nEarthIndex] + oStar.star_GetAStarsMioWongInfo(astarInfos)
end

  def pan_house_flow_info(oStar,nPanType,nEarth)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)

    # normal and ten year
    @Small[nEarthIndex] = oStar.gHouse_GetSmall(nEarth)
    @Large[nEarthIndex] = oStar.gHouse_GetLarge(nEarth)

    # flow year
    if (nPanType >= Cfate::PAN_FLOWYEAR) then
      @PanSmallSanYear[nEarthIndex] = oStar.gPanHouse_FlowYear_SmallSan(nEarth,nPanType)
      @PanFlowYear[nEarthIndex],@PanYearOld[nEarthIndex],@PanYear[nEarthIndex] = oStar.gPanHouse_FlowYear(nEarth,nPanType)
    end

    # flow month
    if (nPanType >= Cfate::PAN_FLOWMONTH) then
      bFlowHouse,@PanMonthStr[nEarthIndex],@PanMonth[nEarthIndex] = oStar.gPanHouse_FlowMonth(nEarth)
      if (bFlowHouse) then
        @nFlowMonthEarth = nEarth
      end
    end

    # flow date
    if (nPanType >= Cfate::PAN_FLOWDATE) then
      bFlowHouse,@nFlowDate,aPanFlowDate = oStar.gPanHouse_FlowDate(nEarth)
      nPanFlowDateLen = aPanFlowDate[2] == nil ? 2 : 3
      @PanDate[nEarthIndex] = aPanFlowDate[0..nPanFlowDateLen-1]
      if (bFlowHouse) then
        @nFlowDateEarth = nEarth
      end
    end

    # flow time
    if (nPanType >= Cfate::PAN_FLOWTIME) then
      bFlowHouse,@PanTimeStr[nEarthIndex],nfETime = oStar.gPanHouse_FlowTime(nEarth)
      @PanTime[nEarthIndex] = Xdate.ETime2Hour(nfETime)
      if (bFlowHouse) then
        @nFlowTimeEarth = nEarth
      end
    end

    # flow min
    if (nPanType >= Cfate::PAN_FLOWMIN) then
      bFlowHouse,@PanMinStr[nEarthIndex],nfEMin = oStar.gPanHouse_FlowMin2(nEarth,true)
      nWHourStart,nWHourStop = Xdate.GetEMinHour(oStar.fdg_ET(nPanType),nfEMin)
      @PanMinHour[nEarthIndex] = nWHourStart
      @PanMin[nEarthIndex] = Xdate.GetEMin(nfEMin)
      if (bFlowHouse) then
        @nFlowMinEarth = nEarth
      end
    end
  end

  def pan_house_in_middle_info(oStar,nPanType,nEarth,nHouse1Earth,ptEarth34)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)
    # 三方四正色彩顯示
    @PanBg34[nEarthIndex] = oStar.gHouse_Check34House(nHouse1Earth,nEarth)

    # 標示命宮三方四正
    nMiddle34 = oStar.gHouse_CheckMiddle34House(nHouse1Earth,nEarth)
    #bNeed34 = oStar.int_LifeLine
    if (nMiddle34 != Star::HOUSE_34_NONE) then
      @Pan34Pt[nMiddle34] = ptEarth34[nEarthIndex]
    end

    # four hua fly out
    @HouseOutFourHua[nEarthIndex] = oStar.gHouse_GetHouseOutFourHuaStr(nPanType,nEarth)
  end

  # personal info
  def pan_personal_info(oStar)
    @PI_Name = oStar.gPan_PI_Name()
    @PI_LunarYearOld = oStar.gPan_PI_LunarYearOld()
    @PI_IYMF = oStar.gPan_PI_IYMF()
    @PI_Animal = oStar.gPan_PI_Animal()
    @PI_SolarBirth = oStar.gPan_PI_SolarBirth2()
    @PI_LunarBirth = oStar.gPan_PI_LunarBirth2()
    @PI_GanZhiBirth = oStar.gPan_PI_GanZhiBirth()

    bSegment,nFirstTimeType,nFirstSegment,bSpace,nSegmentMove = g_eightword_SegmentGanZhi_pars(@user_id)
    @PI_Segment_GanZhiBirth = oStar.gPan_PI_SegmentGanZhiBirth(bSegment,nFirstTimeType,nFirstSegment,bSpace,nSegmentMove)

    @PI_FiveType = oStar.gPan_PI_FiveType()
    @PI_LifeHouse = oStar.gPan_PI_LifeHouse()
    @PI_BodyHouse = oStar.gPan_PI_BodyHouse()
    @PI_Life = oStar.gPan_PI_Life()
    @PI_Body = oStar.gPan_PI_Body()
    @PI_God = oStar.gPan_PI_God()
    @CI_Data1 = Star.gPan_CI_Data1(@ApFunc,@ParAll)
    @CI_Data2 = Star.gPan_CI_Data2(@ApFunc,@ParAll)
    @CI_Data3 = Star.gPan_CI_Data3(@ApFunc,@ParAll)

    @DI_Info = Array.new(3,"")
    @DI_Info[0],@DI_Info[1],@DI_Info[2] = oStar.gHouse_GetFlowTimeStr(@m_nPanType)
  end
  def makeXdate_data(oStar)
    hAppData = Hash.new
    nPanType = @m_nPanType

    hAppData["Ui_EY"] = oStar.uig_EY()
    hAppData["nSky"] = oStar.gNormal_GetSky()
    hAppData["nEarth"] = oStar.gNormal_GetEarth()
    hAppData["CurEYear"] = oStar.cdg_EY()
    hAppData["CurEMonth"] = oStar.cdg_EM()
    hAppData["CurEDate"] = oStar.cdg_ED()
    hAppData["CurELeap"] = oStar.cdg_EL()
    hAppData["CurETime"] = oStar.cdg_ET()
    hAppData["CurEMin"] = oStar.cdg_EMI()
    hAppData["CurWMin "] = oStar.cdg_WMI()
    hAppData["FlowEYear"] = oStar.fdg_EY(nPanType)
    hAppData["FlowEMonth"] = oStar.fdg_EM(nPanType)
    hAppData["FlowEDate"] = oStar.fdg_ED(nPanType)
    hAppData["FlowELeap"] = oStar.fdg_EL(nPanType)
    hAppData["FlowETime"] = oStar.fdg_ET(nPanType)
    hAppData["FlowWHour"] = oStar.fdg_WH(nPanType)
    hAppData["FlowEMin"] = oStar.fdg_EMI(nPanType)

    hAppData["ddg_EY"] = oStar.ddg_EY()
    hAppData["LargeSan"] = Star.HouseIndex2EarthIndex(oStar.gHouse_getLargeSan())
    hAppData["Large"] = oStar.gHouse_GetLarge()

    hParAll = @ParAll
    hParAll[Cfate::PAR_PAN_YEAR_DISPLAY] = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]

    hApFunc = @ApFunc.clone
    hApFunc[Cfate::LAST_XDATE_PAN_TYPE] = @ApFunc["LastXdatePanType"]

    @hXdate = Xdate.MakeXdateInfo_Star(hAppData,@m_nPanType,hApFunc,hParAll)

    @Ui_EY = oStar.uig_EY()
    @nSky = oStar.gNormal_GetSky()
    @nEarth = oStar.gNormal_GetEarth()
    @CurEYear = oStar.cdg_EY()
    @CurEMonth = oStar.cdg_EM()
    @CurEDate = oStar.cdg_ED()
    @CurELeap = oStar.cdg_EL()
    @CurETime = oStar.cdg_ET()
    @CurEMin = oStar.cdg_EMI()
    @CurWMin  = oStar.cdg_WMI()
    @FlowEYear = oStar.fdg_EY(nPanType)
    @FlowEMonth = oStar.fdg_EM(nPanType)
    @FlowEDate = oStar.fdg_ED(nPanType)
    @FlowELeap = oStar.fdg_EL(nPanType)
    @FlowETime = oStar.fdg_ET(nPanType)
    @FlowWHour = oStar.fdg_WH(nPanType)
    @FlowEMin = oStar.fdg_EMI(nPanType)

  end
  def MakeGiSuongInfo(oStar)
    if (@ApFunc["GiSuongEnable"] == false) then
      return
    end
    if (params["action"] == "panprint") then
      return
    end
    if (@m_nPanType == Cfate::PAN_NORMAL) then
      return
    end

    # 吉凶垂象
    # if (params[:GiSuongHouseEarth] != nil) then
    #   nGiSuongHouseEarth = params[:GiSuongHouseEarth].to_i
    #   nHouseId = oStar.g_House_GetHouseId(@m_nPanType,nGiSuongHouseEarth)
    #   if (session["GiSuongHouseId"] == nHouseId) then
    #     session["GiSuongHouseId"] = nil
    #   else
    #     session["GiSuongHouseId"] = nHouseId
    #   end
    # end

    @aPanGiSuong = Array.new(7,nil) # NORMAL -- FLOWMIN
    # if (session["GiSuongHouseId"] != nil && session["GiSuongHouseId"] != 99) then
    #   nGiSuongHouseId = session["GiSuongHouseId"]
    #   @GiSuongEarth = oStar.g_House_GetEarth(@m_nPanType,nGiSuongHouseId)
    #   oPanGiSuong = PanGiSuong.new
    #   oPanGiSuong.ap_Star_Init(oStar,@m_nPanType,@UserInfo,@UserDefData,nGiSuongHouseId)

    #   MakeGiSuongInfo_AllPan(oStar,oPanGiSuong)
    # end
  end

  def MakeGiSuongInfo_AllPan(oStar,oPanGiSuong)
    # 精實創業，用來分析使用狀況
    aaa = Pm.GetStr("IDS_S_HOUSE_NAME_#{session["GiSuongHouseId"]}")
    # Pm.saveTestDb("ifate/star-GiSuong-#{@oUserAp.user_id}-#{@oUserAp.product_type}","GiSuongHouseId:#{session["GiSuongHouseId"]},#{aaa}")
    nPanType = @m_nPanType

    @HasGiSuong = true
    @Ui_EY = oStar.uig_EY()
    @nSky = oStar.gNormal_GetSky()
    @nEarth = oStar.gNormal_GetEarth()
    @CurEYear = oStar.cdg_EY()
    @CurEMonth = oStar.cdg_EM()
    @CurEDate = oStar.cdg_ED()
    @CurELeap = oStar.cdg_EL()
    @CurETime = oStar.cdg_ET()
    @CurEMin = oStar.cdg_EMI()
    @CurWMin  = oStar.cdg_WMI()
    @FlowEYear = oStar.fdg_EY(nPanType)
    @FlowEMonth = oStar.fdg_EM(nPanType)
    @FlowEDate = oStar.fdg_ED(nPanType)
    @FlowELeap = oStar.fdg_EL(nPanType)
    @FlowETime = oStar.fdg_ET(nPanType)
    @FlowWHour = oStar.fdg_WH(nPanType)
    @FlowEMin = oStar.fdg_EMI(nPanType)

    if (NeedXdate?(Cfate::PAN_TENYEAR,@m_nPanType)) then
      MakeGiSuongInfo_Tenyear(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWYEAR,@m_nPanType)) then
      MakeGiSuongInfo_Flowyear(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWMONTH,@m_nPanType)) then
      MakeGiSuongInfo_Flowmonth(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWDATE,@m_nPanType)) then
      MakeGiSuongInfo_Flowdate(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWTIME,@m_nPanType)) then
      MakeGiSuongInfo_Flowtime(oStar,oPanGiSuong)
    end
  end


  def MakeGiSuongInfo_Tenyear(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId"]

    #elsif (@m_nPanType == Cfate::PAN_TENYEAR) then
    nSky = @nSky
    nEarth = @nEarth
    if (Cfate::PAN_TENYEAR <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(30) {Array.new(2)}

      #取得十年大限開始歲數
      nLargeHouseIndex = oStar.gHouse_getLargeSan()
      nEarthIndex = Star.HouseIndex2EarthIndex(nLargeHouseIndex)
      nETenYearStartYearOld = @Large[nEarthIndex]

      # 十年大限開始年
      nHighLightStartEYear = @Ui_EY + nETenYearStartYearOld - 1

      # 萬年曆十年盤起始年
      if (nETenYearStartYearOld > 10) then
        nDisStartEYear = nHighLightStartEYear - 10
      else
        nDisStartEYear = @Ui_EY
      end
      #垂象
      nStartNum = nHighLightStartEYear - nDisStartEYear

      # 萬年曆十年盤起始年紀
      nStartEYearOld = oStar.gNormal_GetYearOld(@Ui_EY,nDisStartEYear)

      # 起始干支
      nSky = Sky.ModifySky(nSky + nStartEYearOld - 1)
      nEarth = Earth.ModifyEarth(nEarth + nStartEYearOld - 1)

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      a = [nDisStartEYear,nHighLightStartEYear,nHighLightStartEYear+10,nHighLightStartEYear+20]
      a.each_index do |i|
        hGisuongUserData[Cfate::EYear] = a[i]
        hGisuongUserData[Cfate::EMonth] = 6
        hGisuongUserData[Cfate::EDate] = 1
        hGisuongUserData[Cfate::LeapMonth] = false
        hGisuongUserData[Cfate::WHour] = 0
        hgs = oWeenApi.g_explode_text(Cfate::PAN_TENYEAR,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        h[a[i]] = hgs["explode_text"]
      end

      # new from WeenApi 1
      (1..30).each do |nRow|
        nRowIndex = nRow - 1
        nAge = nStartEYearOld + nRowIndex
        nStartEYear = nDisStartEYear + nRowIndex

        # new from WeenApi 0
        nGiSuongStartEYear = mgsi_ty_find_eyear(a,nStartEYear)
        szGi = h[nGiSuongStartEYear]["gi"][nStartEYear - nGiSuongStartEYear]
        szSuong = h[nGiSuongStartEYear]["xiong"][nStartEYear - nGiSuongStartEYear]
        # new from WeenApi 1

        # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_TenYear(nSky,nEarth,nAge,nRowIndex,nStartNum)
        aDataGiSuong[nRowIndex][0] = szGi
        aDataGiSuong[nRowIndex][1] = szSuong

        nSky = Sky.ModifySky(nSky + 1)
        nEarth = Earth.ModifyEarth(nEarth + 1)
      end
      @aPanGiSuong[Cfate::PAN_TENYEAR] = aDataGiSuong
    end
  end
  def mgsi_ty_find_eyear(a,nStartEYear)
    if (nStartEYear < a[1]) then
      return a[0]
    elsif (nStartEYear < a[2]) then
      return a[1]
    elsif (nStartEYear < a[3]) then
      return a[2]
    else
      return a[3]
    end
  end

  def MakeGiSuongInfo_Flowyear(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId"]

#   elsif (@m_nPanType == Cfate::PAN_FLOWYEAR) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    if (Cfate::PAN_FLOWYEAR <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(2) {Array.new}
      nAge = 0

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      (0..1).each do |index|
        hGisuongUserData[Cfate::EYear] = @FlowEYear + index
        hGisuongUserData[Cfate::EMonth] = 6
        hGisuongUserData[Cfate::EDate] = 1
        hGisuongUserData[Cfate::LeapMonth] = false
        hGisuongUserData[Cfate::WHour] = 0
        hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWYEAR,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        h[@FlowEYear + index] = hgs["explode_text"]
      end
      # new from WeenApi 1

      (0..1).each do |index|
        nEYear = @FlowEYear + index

        # 取得該年月資訊
        aaMonth = Xdate.GetEastMonthArray(@hXdate["pan_xdate_flowyear_title"][index][0])
        aaMonth.each_index do |nMonthIndex|
          aData = Array.new

          aMonth = aaMonth[nMonthIndex]

          nEMonth = aMonth[0]
          nSky,nEarth = Xdate.GetLunarMonthGanZhiSkyEarth(nEYear,nEMonth)

          # new from WeenApi 0
          szGi = h[nEYear]["gi"][nMonthIndex]
          szSuong = h[nEYear]["xiong"][nMonthIndex]
          # new from WeenApi 1

          # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowYear(nSky,nEarth,nAge,nEYear)

            aData.push(szGi)
            aData.push(szSuong)

            aDataGiSuong[index].push(aData)
        end
      end
      @aPanGiSuong[Cfate::PAN_FLOWYEAR] = aDataGiSuong
    end
  end

  def MakeGiSuongInfo_Flowmonth(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId"]

    #elsif (@m_nPanType == Cfate::PAN_FLOWMONTH) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    bLeapMonth = @FlowELeap
    if (Cfate::PAN_FLOWMONTH <= @ApFunc["LastXdatePanType"]) then
      # 取得該年月資訊
      aaMonth = Xdate.GetEastMonthArray(nEYear)
      nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,aaMonth)
      aDataGiSuong = Array.new
      nAge = 0
      nDays = aaMonth[nMonthIndex][1]

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      hGisuongUserData[Cfate::EYear] = nEYear
      hGisuongUserData[Cfate::EMonth] = nEMonth
      hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
      hGisuongUserData[Cfate::EDate] = 1
      hGisuongUserData[Cfate::WHour] = 0
      hGisuongUserData[Cfate::ETime] = 0
      hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWMONTH,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
      h[nEMonth] = hgs["explode_text"]
      # new from WeenApi 1
# nil[1]
      (1..aaMonth[nMonthIndex][1]).each do |nEDate|
        aData = Array.new

        # new from WeenApi 0
        szGi = h[nEMonth]["gi"][nEDate - 1]
        szSuong = h[nEMonth]["xiong"][nEDate - 1]
        # new from WeenApi 1

        # nSky,nEarth = Xdate.GetLunarDateGanZhiSkyEarth(nEYear,nEMonth,nEDate,6,bLeapMonth)
        # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowMonth(nEYear,nEMonth,bLeapMonth,nSky,nEarth,nAge)

        aData.push(szGi)
        aData.push(szSuong)

        aDataGiSuong.push(aData)
      end
      @aPanGiSuong[Cfate::PAN_FLOWMONTH] = aDataGiSuong
    end
  end

  def MakeGiSuongInfo_Flowdate(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId"]

#   elsif (@m_nPanType == Cfate::PAN_FLOWDATE) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    bLeapMonth = @FlowELeap
    nEDate = @FlowEDate
    if (Cfate::PAN_FLOWDATE <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(2) {Array.new}

      # 年月表頭
      nEYear = @FlowEYear
      nEMonth = @FlowEMonth
      bLeapMonth = @FlowELeap

      # 日的訊息
      nEDate = @FlowEDate
      (0..1).each do |index|
        # new from WeenApi 0
        oWeenApi = WeenApi.new
        h = Hash.new
        hGisuongUserData[Cfate::EYear] = nEYear
        hGisuongUserData[Cfate::EMonth] = nEMonth
        hGisuongUserData[Cfate::EDate] = nEDate
        hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
        hGisuongUserData[Cfate::WHour] = 0
        hGisuongUserData[Cfate::ETime] = 0
        hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWDATE,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        h[nEDate] = hgs["explode_text"]
        # new from WeenApi 1

        # 日表頭
        (1..12).each do |nEarth|
          nETime = nEarth - 1
          aData = Array.new

          # new from WeenApi 0
          szGi = h[nEDate]["gi"][nEarth - 1]
          szSuong = h[nEDate]["xiong"][nEarth - 1]
          # new from WeenApi 1

          # nSky,nEarth = Xdate.GetLunarTimeGanZhiSkyEarth(nEYear,nEMonth,nEDate,nETime,bLeapMonth)
          # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowDate(nEYear,nEMonth,bLeapMonth,nEDate,nSky,nEarth,0)


          aData.push(szGi)
          aData.push(szSuong)

          aDataGiSuong[index].push(aData)
        end
        # 換到下一天
        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      end
      @aPanGiSuong[Cfate::PAN_FLOWDATE] = aDataGiSuong
    end
  end
  # 選流日時，萬年曆顯示的流月，流年，十年的吉凶錯誤；流日流時的吉凶未做
  def MakeGiSuongInfo_Flowtime(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId"]

#   elsif (@m_nPanType == Cfate::PAN_FLOWDATE) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    bLeapMonth = @FlowELeap
    nEDate = @FlowEDate
    nETime = @FlowETime
    nWHour = @FlowWHour
    if (Cfate::PAN_FLOWTIME <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(2) {Array.new}

      # 年月表頭
      nEYear = @FlowEYear
      nEMonth = @FlowEMonth
      bLeapMonth = @FlowELeap

      # 日的訊息
      nEDate = @FlowEDate
      (0..1).each do |index|
        # new from WeenApi 0
        oWeenApi = WeenApi.new
        h = Hash.new
        hGisuongUserData[Cfate::EYear] = nEYear
        hGisuongUserData[Cfate::EMonth] = nEMonth
        hGisuongUserData[Cfate::EDate] = nEDate
        hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
        hGisuongUserData[Cfate::ETime] = Pm.TestNo12(nETime)
        hGisuongUserData[Cfate::WHour] = Xdate.ETimeFirstHour(nETime)
        hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWTIME,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        h[hGisuongUserData[Cfate::ETime]] = hgs["explode_text"]
        # new from WeenApi 1

        # 日表頭
        # (1..12).each do |nEarth|
        (1..12).each do |i|
          # nETime = nEarth - 1
          aData = Array.new

          # new from WeenApi 0
          szGi = h[hGisuongUserData[Cfate::ETime]]["gi"][i - 1]
          szSuong = h[hGisuongUserData[Cfate::ETime]]["xiong"][i - 1]
          # new from WeenApi 1

          # nSky,nEarth = Xdate.GetLunarTimeGanZhiSkyEarth(nEYear,nEMonth,nEDate,nETime,bLeapMonth)
          # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowDate(nEYear,nEMonth,bLeapMonth,nEDate,nSky,nEarth,0)


          aData.push(szGi)
          aData.push(szSuong)

          aDataGiSuong[index].push(aData)
        end
        # 換到下一天
        nETime += 1
        if (nETime == 1) then
          nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
        end
      end
      @aPanGiSuong[Cfate::PAN_FLOWTIME] = aDataGiSuong
    end
  end

  # 個人事件
  def pan_prepare_personalevent_data()
    tNow = time_now_local()
    oUserCustomer = UserCustomer.find_by_id(session["customer_id"])
    if (oUserCustomer == nil) then
      @starYearSelect = 1901
    else
      @starYearSelect = oUserCustomer.wyear
    end
      if (oUserCustomer == nil || oUserCustomer.wyear <= tNow.year) then
        @stopYearSelect = tNow.year
      else
        @stopYearSelect = oUserCustomer.wyear + 10
      end

    @pe_title_data = Pm.GetStrWithQuote("IDS_PE_TITLE")
    # h.invert
    @pe_mains = Cfate.pan_par_dbfield2hash(Pm.GetPeStr("pe_mains"))
    @pe_main_key_default = @pe_mains.values[0]

    @pe_subs = Cfate.pan_par_dbfield2hash(Pm.GetPeStr(@pe_main_key_default))
    @pe_sub_key_default = @pe_subs.values[0]
    @pe_time = tNow

    @personal_events = PersonalEvent.available.check_option_user_id(@user_id).check_user_customer_id(session["customer_id"]).check_main_key_pe().last(3).reverse
    @canAddPe = session["customer_id"] != nil
  end


  def authenticate
    authenticate_xuexi_ziwei(Xuexi_Ziwei.ap_name(),"xuexi")
    @sLang = my_current_lang()
    @nav_type = "xuexi-function"
  end
  def logged_in?
    return (current_user != nil)
  end
  def authenticate_xuexi_ziwei(new_ap_name,ap_namespace)
    if (Pm.getSystemValue("WebSpeedTest") == "1") then
      @user_name = "<EMAIL>" #current_user.email
      @user_id = 3
    else
      #@user_name = current_user.name
      @user_name = current_user.email if logged_in?
      @user_id = current_user.id if logged_in?
    end
    @user_level = ""
    @ap_name = g_user_ap_name(new_ap_name,@user_id,ap_namespace)

    # session["userlevel"] = @user_level

    getUserCurrentAp(@user_id)

    # session["ap_name"] = @ap_name
    # session["ap_version"] = @ap_version
    session["user_name"] = { :value => @user_name, :expires => 1.minute.from_now }
    bCanUseIfatePars = true
    @ParAll = Star.get_pan_pars(@user_id,bCanUseIfatePars)
    # @ParAll = Star.get_pan_pars(@user_id,g_can_use_pars(@oUserAp))
    star_version_check()

    @action_result = params["action_result"]

    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?

    @admin = current_user.roles? :admin
    @can_add_customer,@customer_max_count = g_can_add_customer(@user_id,@admin)
  end
  def getUserCurrentAp(user_id)
    @oUserAp = g_getUserCurrentAp(user_id,@ap_name)
    @showAdvertise = true
    @firstShowAdvertise = @oUserAp.firstShowAdvertise && @showAdvertise ? "pan_information_block" : "pan_xdate_block"
    @canUsePan = g_canUsePan(session["customer_id"])
  end

  def star_version_check()
    # 權限控管
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @hHallNumberAdver = find_last_hallnumber_adver(@ap_name,@oUserAp,@ApFunc)
    @hCanDeal = canDealCheck(@user_id,@oUserAp,@ap_name)
    @hCanDeal = nil    
    @main_products = g_getMainProducts(@ap_name)
    @canAddPe = session["customer_id"] != nil
  end
  def xuexi_data(hPars)
    xuexi_pars(hPars,"new_class_item")
    xuexi_pan_info(hPars)
    @const_data = Xuexi_Ziwei.xuexi_const_data(@xuexi_class,@xuexi_item,@oStar,@m_nPanType)

    # @xuexi = Xuexi_Ziwei.xuexi_data(hPars,@xuexi_class,@xuexi_item)
    @shuoming = {"name"=>"","value"=>[]}
    @class_item_name = class_item_name("xuexi",@xuexi_class,@xuexi_item)
  end
  def lianxi_timu(hPars)
    xuexi_pars(hPars,"new_class_item")
    if (Xuexi_Ziwei.itemcount_in_class(@xuexi_class) == @xuexi_item) then
      return
    end
    xuexi_pan_info(hPars)
    @const_data = Xuexi_Ziwei.lianxi_const_data(@xuexi_class,@xuexi_item,@oStar,@m_nPanType)
    @lianxi = Xuexi_Ziwei.lianxi_data(hPars,@xuexi_class,@xuexi_item,remote_ip(),@oStar,@m_nPanType)
    @shuoming = ""
    @class_item_name = class_item_name("lianxi",@xuexi_class,@xuexi_item)
    # 若題目要有自己的盤，會在題目中回timestamp，此時再重算oStar
    xuexi_pan_info(hPars)
  end
  def class_item_name(taitou,xuexi_class,xuexi_item)
    s = Pm.t("xuexi.navbar.#{taitou}")
    s += Pm.t("xuexi.guan.dijigua",:xuexi_class => @xuexi_class, :xuexi_item => @xuexi_item)
    if (Xuexi_Ziwei.last_class_itemcount(@xuexi_class,@xuexi_item)) then
      s += Pm.t("xuexi.final_test")
    end
    return s
  end
  def chuangguan_timu(hPars,where_class_item)
    xuexi_pars(hPars,where_class_item)
    xuexi_pan_info(hPars)
    @const_data = Xuexi_Ziwei.chuangguan_const_data(@xuexi_class,@xuexi_item)
    @chuangguan = Xuexi_Ziwei.chuangguan_data(hPars,@xuexi_class,@xuexi_item,remote_ip(),@oStar,@m_nPanType)
    @dijiti = @chuangguan["dijiti"]
    @jiti = @chuangguan["jiti"]
    @class_item_name = class_item_name("chuangguan",@xuexi_class,@xuexi_item)
    xuexi_pan_info(hPars)
  end
  def xuexi_pars(hPars,where_class_item)
    ifate_par = Xuexi_Ziwei.find_from_ifatepar(@user_id,@ap_name)
    if (where_class_item == "muqian_class_item") then
      @xuexi_class = ifate_par["xuexi_class_muqian"]
      @xuexi_item = ifate_par["xuexi_item_muqian"]
    else
      @xuexi_class = hPars["xuexi_class"].to_i
      @xuexi_item = hPars["xuexi_item"].to_i
      if (@xuexi_class == 0) then
        @xuexi_class = ifate_par["xuexi_class"]
      end
      if (@xuexi_item == 0) then
        @xuexi_item = ifate_par["xuexi_item"]
      end
      if (@xuexi_class > ifate_par["xuexi_class_muqian"]) then
        @xuexi_class = ifate_par["xuexi_class_muqian"]
      end
      if (@xuexi_class == ifate_par["xuexi_class_muqian"]) then
        if (@xuexi_item > ifate_par["xuexi_item_muqian"]) then
          @xuexi_item = ifate_par["xuexi_item_muqian"]
        end
      else
        if (@xuexi_item > Xuexi_Ziwei.itemcount_in_class(@xuexi_class)) then
          @xuexi_item = Xuexi_Ziwei.itemcount_in_class(@xuexi_class)
        end
      end
    end

    ifate_par["xuexi_class"] = @xuexi_class
    ifate_par["xuexi_item"] = @xuexi_item
    Xuexi_Ziwei.save_to_ifatepar(@user_id,@ap_name,ifate_par)
  end
  def xuexi_pan_info(hPars)
    hPars["timestamp"] = Xuexi_Ziwei.timu_data_timestamp(@xuexi_class,@xuexi_item) if hPars["timestamp"] == nil
    @timestamp = hPars["timestamp"]
    star_pan_data(hPars)
  end
end
