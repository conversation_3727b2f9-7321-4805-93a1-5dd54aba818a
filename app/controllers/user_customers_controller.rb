require("Xdate.rb")

class UserCustomersController < ApplicationController
  before_action :authenticate_user!
  # before_action :check_owner

  def index
    @user_customers = UserCustomer.where("user_id = ? and flag = 1", current_user.id).all.order(created_at: :desc)
    # @user_customers = UserCustomer.accessible_by(current_ability)
  end

  def show
    @user_customer = UserCustomer.where("user_id = ? and flag = 1", current_user.id).find(params[:id])
  end

  def new
    @user_customer = UserCustomer.new
  end

  def create
    @user_customer = UserCustomer.new(user_customer_params)
    if @user_customer.save
      redirect_to groups_path
    else
      render :new
    end
  end

  def edit
    @user_customer = UserCustomer.where("user_id = ? and flag = 1", current_user.id).find(params[:id]).where(flag: 1)
  end

  def update
    @user_customer = UserCustomer.where("user_id = ? and flag = 1", current_user.id).find(params[:id])

    if @user_customer.update(user_customer_params)
      redirect_to user_customer_path(@user_customer)
    else
      render :edit
    end
  end

  def destroy
    @user_customer = UserCustomer.where("user_id = ? and flag = 1", current_user.id).find(params[:id])

    @user_customer.destroy
    redirect_to user_customer_path
  end

  private
  def user_customer_params
    params.require(:user_customer).permit(:user_id, :name, :sex, :blood, :email)
  end

end
