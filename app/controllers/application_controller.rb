# ApplicationController
class ApplicationController < ActionController::Base
  protect_from_forgery
  # helper_method :unread_notify_count
  helper_method :turbolinks_app?


  # Addition contents for etag
  etag { current_user.try(:id) }
  # etag { unread_notify_count }
  etag { flash }
  etag { Setting.footer_html }
  etag { Rails.env.development? ? Time.now : Date.current }

  before_action do
    resource = controller_name.singularize.to_sym
    method = "#{resource}_params"
    params[resource] &&= send(method) if respond_to?(method, true)

    if devise_controller?
      devise_parameter_sanitizer.permit(:sign_in) { |u| u.permit(*User::ACCESSABLE_ATTRS) }
      devise_parameter_sanitizer.permit(:account_update) do |u|
        if current_user.email_locked?
          u.permit(*User::ACCESSABLE_ATTRS)
        else
          u.permit(:email, *User::ACCESSABLE_ATTRS)
        end
      end
      devise_parameter_sanitizer.permit(:sign_up) { |u| u.permit(*User::ACCESSABLE_ATTRS) }
    end

    User.current = current_user
    cookies.signed[:user_id] ||= current_user.try(:id)

  end

  before_action :set_active_menu
  def set_active_menu
    @current = ["/#{controller_name}"]
  end

  # from meen.tw
  before_action :set_start_time
  def set_start_time
    @start_time = Time.now.usec
  end

  before_action :set_locale
  def set_locale
    I18n.locale = user_locale

    # after store current locale
    cookies[:locale] = params[:locale] if params[:locale]
  rescue I18n::InvalidLocale
    I18n.locale = I18n.default_locale
  end

  def render_404
    # render_optional_error_file(404)
    raise ActionController::RoutingError.new('Not Found')
  end

  def render_403
    # render_optional_error_file(403)
    raise ActionController::RoutingError.new('Forbidden')
  end

  def render_optional_error_file(status_code)
    session.clear
    reset_session
    status = status_code.to_s
    fname = %w(404 403 422 500).include?(status) ? status : 'unknown'
    render template: "/errors/#{fname}", format: [:html],
           handler: [:erb], status: status, layout: 'application'
  end

  rescue_from CanCan::AccessDenied do |_exception|
    redirect_to topics_path, alert: t('common.access_denied')
  end

  def store_location
    session[:return_to] = request.request_uri
  end

  def redirect_back_or_default(default)
    redirect_to(session[:return_to] || default)
    session[:return_to] = nil
  end

  def redirect_referrer_or_default(default)
    redirect_to(request.referrer || default)
  end

  # def unread_notify_count
  #   return 0 if current_user.blank?
  #   @unread_notify_count ||= Notification.unread_count(current_user)
  # end

  def authenticate_user!(opts = {})
    if turbolinks_app?
      render plain: '401 Unauthorized', status: 401 if current_user.blank?
    else
      super(opts)
    end
  end

  def current_user
    super
  end

  def turbolinks_app?
    agent_str = request.user_agent.to_s
    agent_str.include?('turbolinks-app')
  end
  

  private

  def user_locale
    params[:locale] || cookies[:locale] || http_head_locale || I18n.default_locale
  end

  def http_head_locale
    http_accept_language.language_region_compatible_from(I18n.available_locales)
  end


  # from meen.tw
  def my_current_lang()
    # saveTestDb("I18N",I18n.locale)
    if (I18n.locale.to_s == "zh-TW") then
      return I18n.locale.to_s
    elsif (I18n.locale.to_s == "zh-CN") then
      return I18n.locale.to_s
    end
    #return "zh-TW"
    # return "en"
  end

  def getSystemValue(key)
    return Pm.getSystemValue(key)
  end
  def getSvUrl(key)
    sValue = Pm.getSystemValue(key)
    sHost = Pm.getSystemValue("MEEN_URL")
    return "#{sHost}#{sValue}"
  end

  def dateStrToIntDates(sDate)
    # return eval("#{sDate.split(//,2).join(".")}")
    return eval("#{sDate.split.join(".")}")
  end

  def getWebServer()
    return request.host
  end

  def g_getProductByUserAp(user_ap)
    if (user_ap == nil) then
      return g_getProduct(0)
    else
      return g_getProduct(user_ap.product_id,user_ap.product_type,user_ap.product_name)
    end
  end
  def g_getProduct(product_id,product_type="free",ap_name="star")
    if (product_id != 0) then
      oProduct = Product.find_by_id(product_id)
    else
      oProduct = eval("g_get#{product_type.capitalize}Product(ap_name)")
    end
    return oProduct
  end
  def getProductFuncParHash(product_id,product_type,ap_name)
    oProduct = g_getProduct(product_id,product_type,ap_name)
    return getProductFuncHash(oProduct)
  end
  def getProductFuncHash(oProduct)
    if (oProduct == nil) then
      return Hash.new
    end
    hProductionFunc = Cfate.pan_par_dbfield2hash(oProduct.func_par)
    return hProductionFunc
  end
  def g_product_par_hash(oProduct)
    if (oProduct == nil) then
      return Hash.new
    end
    hApFunc = getProductFuncHash(oProduct)
    hFreePar = g_GetFreeApFuncHash(oProduct.product_name)

    return g_product_func_par_assign(oProduct.product_name,hApFunc,hFreePar)
  end

  def getProductFuncParByUserAp(oUserAp)
    oProduct = g_getProductByUserAp(oUserAp)
    return oProduct.func_par
  end
  def getProductFuncPar(product_id,product_type,ap_name)
    oProduct = g_getProduct(product_id,product_type,ap_name)
    return oProduct.func_par
  end
  def g_getUserAvailAps(user_id)
    user_aps = UserAp.available.check_userid(user_id).all
    return user_aps
  end
  def g_getUserAllAps(user_id)
    user_aps = UserAp.check_userid(user_id).all
    return user_aps
  end
  def g_getUserNotAvailAps(user_id)
    user_aps = UserAp.not_available.check_userid(user_id).all
    return user_aps
  end

  def g_getUserAvailAp(user_id,ap_name)
    return UserAp.available.check_userid(user_id).check_product_name(ap_name).first
  end
  def g_getUserMainAps(user_id,ap_name)
    return UserAp.available.check_userid(user_id).check_main.check_product_name(ap_name).order("end_date ASC").all
  end

  def g_getDemoProduct(ap_name)
      oProduct = Product.check_demo.check_name(ap_name).last
      return oProduct
    # hPar = {"star" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,1,b_PAN_FLOWYEAR,1,b_PAN_FLOWMONTH,1,b_PAN_FLOWDATE,1,b_PAN_FLOWTIME,1,b_PAN_FLOWMIN,1,SelfFourHua,1,FlyoutFourHua,1,LastPanType,6,SetStarPars,1,GanZhiBirthDisplay,1,LYYGDisplay,1,Star7Display,1,SmallYearRevertDisplay,1,LastXdatePanType,6,showXdate,1,GiSuongEnable,1,b_HallNumber,1,n_SkyFindStar_house,4095,n_StarFindSky_house,4095,n_explain_free_pan,4,n_explain_free_content,15,n_explain_free_house,4095",
    #    "eightword" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,1,b_PAN_FLOWYEAR,1,b_PAN_FLOWMONTH,1,b_PAN_FLOWDATE,1,b_PAN_FLOWTIME,1,b_PAN_FLOWMIN,0,n_LastPanType,5,n_LastXdatePanType,5,b_func_five_wang_display,1,b_func_par_display,1,b_func_4c_sky_mm_event,1,n_func_4c_earth_mm_event,31,b_func_fw_sky_mm_event,1,n_func_fw_earth_mm_event,31,b_SetPars,1,b_HallNumber,1",
    #    "xdate" => ""
    #   }
    # product_type = "demo"
    # product_content = getSystemValue("demoDates")
    # product_version = "PZN04"
    # return g_getFreeAmountProduct(ap_name,hPar,product_type,product_content,product_version)
  end
  def g_getFreeAmountProduct(ap_name,hPar,product_type,product_content,product_version)
    oProduct = Product.new
    oProduct.id = 0
    oProduct.product_type = product_type
    oProduct.product_name = ap_name
    oProduct.product_version = product_version
    oProduct.product_description = Pm.GetStr("IDS_#{ap_name.upcase}_#{product_type.upcase}")
    oProduct.original_amount = 0
    oProduct.discount_amount = 0
    oProduct.product_content = product_content
    oProduct.func_par = hPar["#{ap_name}"]
    t = Xdate.GetNowRails()
    oProduct.start_date = t
    oProduct.end_date = t + dateStrToIntDates(product_content)
    oProduct.status = 0
    oProduct.desc_url = ""
    return oProduct
  end

  def g_getShowProduct(ap_name)
    hPar = {"star" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,1,b_PAN_FLOWYEAR,1,b_PAN_FLOWMONTH,1,b_PAN_FLOWDATE,1,b_PAN_FLOWTIME,1,b_PAN_FLOWMIN,1,SelfFourHua,1,FlyoutFourHua,1,LastPanType,6,SetStarPars,1,GanZhiBirthDisplay,1,LYYGDisplay,1,Star7Display,1,SmallYearRevertDisplay,1,LastXdatePanType,6,showXdate,1,GiSuongEnable,1,b_HallNumber,1,n_SkyFindStar_house,4095,n_StarFindSky_house,4095,n_explain_free_pan,4,n_explain_free_content,15,n_explain_free_house,4095",
       "eightword" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,1,b_PAN_FLOWYEAR,1,b_PAN_FLOWMONTH,1,b_PAN_FLOWDATE,1,b_PAN_FLOWTIME,1,b_PAN_FLOWMIN,0,n_LastPanType,5,n_LastXdatePanType,5,b_func_five_wang_display,1,b_func_par_display,1,b_func_4c_sky_mm_event,1,n_func_4c_earth_mm_event,31,b_func_fw_sky_mm_event,1,n_func_fw_earth_mm_event,31",
       "xdate" => "",
       "wannianli" => "",
       "liuyaogua" => "b_SetPars,1,b_HallNumber,1,b_explain_pan,1",
      }
    product_type = "show"
    product_content = "1 year"
    product_version = "PZN04"
    return g_getFreeAmountProduct(ap_name,hPar,product_type,product_content,product_version)
  end

  def g_isDemoExist(user_id,ap_name)
    if (user_id == nil || ap_name == nil) then
      return true
    end
    oUserAp = UserAp.check_userid(user_id).check_demo().check_product_name(ap_name).last
    if (oUserAp == nil) then
      # always true因為不再提供Demo 2017/7/4 [mywarr/meen.tw] 紫微工具修正規格 (#42)
      return true
      # return false
    else
      return true
    end
  end

  def g_getFreeProduct(ap_name)
    # hPar = {"star" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,0,b_PAN_FLOWYEAR,0,b_PAN_FLOWMONTH,0,b_PAN_FLOWDATE,0,b_PAN_FLOWTIME,0,b_PAN_FLOWMIN,0,SelfFourHua,0,FlyoutFourHua,0,LastPanType,0,SetStarPars,0,GanZhiBirthDisplay,0,LYYGDisplay,0,Star7Display,0,SmallYearRevertDisplay,0,LastXdatePanType,0,showXdate,1,GiSuongEnable,0,b_HallNumber,0,n_SkyFindStar_house,341,n_StarFindSky_house,0,n_explain_free_pan,0,n_explain_free_content,1,n_explain_free_house,341",
    #    "eightword" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,0,b_PAN_FLOWYEAR,0,b_PAN_FLOWMONTH,0,b_PAN_FLOWDATE,0,b_PAN_FLOWTIME,0,b_PAN_FLOWMIN,0,n_LastPanType,0,n_LastXdatePanType,0,b_func_five_wang_display,1,b_func_par_display,0,b_func_4c_sky_mm_event,0,n_func_4c_earth_mm_event,5,b_func_fw_sky_mm_event,0,n_func_fw_earth_mm_event,0,b_SetPars,0,b_HallNumber,0",
    #    "xdate" => ""
    #   }
    # hPar = {"star" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,0,b_PAN_FLOWYEAR,0,b_PAN_FLOWMONTH,0,b_PAN_FLOWDATE,0,b_PAN_FLOWTIME,0,b_PAN_FLOWMIN,0,SelfFourHua,0,FlyoutFourHua,0,LastPanType,0,SetStarPars,1,GanZhiBirthDisplay,0,LYYGDisplay,0,Star7Display,0,SmallYearRevertDisplay,0,LastXdatePanType,0,showXdate,1,GiSuongEnable,0,b_HallNumber,0,n_SkyFindStar_house,4095,n_StarFindSky_house,4095,n_explain_free_pan,0,n_explain_free_content,1,n_explain_free_house,341",
    #    "eightword" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,0,b_PAN_FLOWYEAR,0,b_PAN_FLOWMONTH,0,b_PAN_FLOWDATE,0,b_PAN_FLOWTIME,0,b_PAN_FLOWMIN,0,n_LastPanType,0,n_LastXdatePanType,0,b_func_five_wang_display,1,b_func_par_display,0,b_func_4c_sky_mm_event,0,n_func_4c_earth_mm_event,5,b_func_fw_sky_mm_event,0,n_func_fw_earth_mm_event,0,b_SetPars,1,b_HallNumber,0",
    #    "xdate" => "",
    #    "wannianli" => "b_SetPars,0,n_LastXdatePanType,3",
    #    Xuexi_Ziwei.ap_name() => Xuexi_Ziwei.apfunc_default_str(),
    #    Xuexi_Ziwei.lunduan_ap_name() => Xuexi_Ziwei.apfunc_default_str(),
    #    Xuexi_Ziwei.pan_ap_name() => Xuexi_Ziwei.apfunc_default_str(),
    #    Xuexi_Ziwei.pars_ap_name() => Xuexi_Ziwei.apfunc_default_str()
    #   }
    # 42 修改 star
    hPar = {"star" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,1,b_PAN_FLOWYEAR,0,b_PAN_FLOWMONTH,0,b_PAN_FLOWDATE,0,b_PAN_FLOWTIME,0,b_PAN_FLOWMIN,0,SelfFourHua,1,FlyoutFourHua,0,LastPanType,2,SetStarPars,1,GanZhiBirthDisplay,1,LYYGDisplay,0,Star7Display,0,SmallYearRevertDisplay,0,LastXdatePanType,2,showXdate,1,GiSuongEnable,0,b_HallNumber,0,n_SkyFindStar_house,4095,n_StarFindSky_house,4095,n_explain_pan,-1,n_explain_free_pan,-1,n_explain_free_content,0,n_explain_free_house,0",
       "eightword" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,1,b_PAN_FLOWYEAR,0,b_PAN_FLOWMONTH,0,b_PAN_FLOWDATE,0,b_PAN_FLOWTIME,0,b_PAN_FLOWMIN,0,n_LastPanType,1,n_LastXdatePanType,1,b_func_five_wang_display,1,b_func_par_display,0,b_func_4c_sky_mm_event,1,n_func_4c_earth_mm_event,31,b_func_fw_sky_mm_event,1,n_func_fw_earth_mm_event,31,b_SetPars,1,b_HallNumber,0",
       "xdate" => "",
       "wannianli" => "b_SetPars,0,n_LastXdatePanType,3",
       Xuexi_Ziwei.ap_name() => Xuexi_Ziwei.apfunc_default_str(),
       Xuexi_Ziwei.lunduan_ap_name() => Xuexi_Ziwei.apfunc_default_str(),
       Xuexi_Ziwei.pan_ap_name() => Xuexi_Ziwei.apfunc_default_str(),
       Xuexi_Ziwei.pars_ap_name() => Xuexi_Ziwei.apfunc_default_str(),
       "liuyaogua" => "b_SetPars,1,b_HallNumber,0,b_explain_pan,0"
      }
    product_type = "free"
    product_content = "1 year"
    product_version = "PZN00"
    return g_getFreeAmountProduct(ap_name,hPar,product_type,product_content,product_version)
  end
  def g_getAllFreeProducts()
    return Product.check_free.all
  end

  def g_getForeverProduct(user_id,ap_name)
    oProduct = Product.check_forever.check_name(ap_name).last
    return oProduct
  end

  def g_getForeverUserApCounts(ap_name)
    return UserAp.check_forever.check_product_name(ap_name).all.count
  end

  def g_getForeverUserAp(user_id,ap_name)
    return UserAp.current.check_userid(user_id).check_forever.check_product_name(ap_name).last
  end

  def g_getMainApNames()
      a = Product.select(:product_name).check_main.distinct
      b = Array.new
      a.each do |p|
        b.push(p.product_name)
      end
      return b
  end
  def g_getProductMainNames(ap_name)
      a = Product.select(:product_version).check_main.check_name(ap_name).distinct
      b = Array.new
      a.each do |p|
        b.push(p.product_version)
      end
      return b
  end
  def g_getMainProducts(ap_name)
    products = Product.check_main.check_name(ap_name).check_amount_great(0).order("discount_amount ASC").all
    return products
  end

  def isApForever(user_ap,ap_name)
    return (user_ap.product_type == "forever") && isSameApName(user_ap,ap_name)
  end

  def isSameApName(user_ap,ap_name)
    return (user_ap.product_name == ap_name)
  end

  def g_ap_product_type(user_ap)
    return user_ap.product_type
  end

  def g_ap_product_name(user_ap)
    return user_ap.product_name
  end
  def g_ap_product_version(user_ap)
    return user_ap.product_version
  end
  def g_ap_product_addfunc(user_ap)
    return user_ap.ap_additionalfunc
  end

  def g_ap_product_addfunc_hash(user_ap)
    return Cfate.pan_par_dbfield2hash(g_ap_product_addfunc(user_ap))
  end

  def g_ap_product_func(user_ap)
    return Cfate.pan_par_hash2db(g_ap_product_func_hash(user_ap))
  end

  def g_ap_product_func_hash(user_ap)
    return getProductFuncParHash(user_ap.product_id,user_ap.product_type,user_ap.product_name)
  end

  def g_product_func_par_assign(ap_name,hPar,hFreePar)
    if (ap_name == "star") then
      return Star.product_func_par_assign(hPar,hFreePar)
    elsif (ap_name == "eightword") then
      return Eightword.product_func_par_assign(hPar,hFreePar)
    elsif (ap_name == "xdate") then
      return Xdate.product_func_par_assign(hPar,hFreePar)
    elsif (ap_name == "wannianli") then
      return Wannianli.product_func_par_assign(hPar,hFreePar)
    elsif (ap_name == "liuyaogua") then
      return Liuyaogua.product_func_par_assign(hPar,hFreePar)
    elsif (ap_name == Xuexi_Ziwei.ap_name()) then
      return Xuexi_Ziwei.product_func_par_assign(hPar,hFreePar)
    elsif (ap_name == Xuexi_Ziwei.lunduan_ap_name()) then
      return Xuexi_Ziwei.product_func_par_assign(hPar,hFreePar)
    elsif (ap_name == Xuexi_Ziwei.pan_ap_name()) then
      return Xuexi_Ziwei.product_func_par_assign(hPar,hFreePar)
    elsif (ap_name == Xuexi_Ziwei.pars_ap_name()) then
      return Xuexi_Ziwei.product_func_par_assign(hPar,hFreePar)
    end

    return Hash.new
  end
  def g_func_assign(ap_name,hApFunc)
    if (ap_name == "star") then
      return Star.func_assign(hApFunc)
    elsif (ap_name == "eightword") then
      return Eightword.func_assign(hApFunc)
    elsif (ap_name == "xdate") then
      return Xdate.func_assign(hApFunc)
    elsif (ap_name == "wannianli") then
      return Wannianli.func_assign(hApFunc)
    elsif (ap_name == "liuyaogua") then
      return Liuyaogua.func_assign(hApFunc)
    elsif (ap_name == Xuexi_Ziwei.ap_name()) then
      return Xuexi_Ziwei.func_assign(hApFunc)
    elsif (ap_name == Xuexi_Ziwei.lunduan_ap_name()) then
      return Xuexi_Ziwei.func_assign(hApFunc)
    elsif (ap_name == Xuexi_Ziwei.pan_ap_name()) then
      return Xuexi_Ziwei.func_assign(hApFunc)
    elsif (ap_name == Xuexi_Ziwei.pars_ap_name()) then
      return Xuexi_Ziwei.func_assign(hApFunc)
    end
    return Hash.new
  end

  def g_ap_product_allfunc_hash(user_ap)
    hPar = g_ap_product_func_hash(user_ap)
    hFreePar = g_GetFreeApFuncHash(g_ap_product_name(user_ap))
    hApAdd = g_ap_product_addfunc_hash(user_ap)
    hPar.merge!(hApAdd) { |key, v1, v2| (v1 >= v2) ? v1 : v2 }
    hApFunc = g_product_func_par_assign(g_ap_product_name(user_ap),hPar,hFreePar)
    hApAddFunc = g_func_assign(g_ap_product_name(user_ap),hApFunc)
    hApFunc.merge!(hApAddFunc) { |key, v1, v2| v1 }

    return hApFunc
  end

  def g_getUserCurrentAp(user_id,ap_name,customer_id=1)
    cur_user_ap = nil
    user_aps = g_getUserCurrentAps(user_id,ap_name,customer_id)
    user_aps.each do |user_ap|
      if (isSameApName(user_ap,ap_name)) then
        if (cur_user_ap == nil) then
          cur_user_ap = user_ap
        else
          cur_user_ap.ap_additionalfunc = mergeAddFunc(user_ap.ap_additionalfunc,cur_user_ap.ap_additionalfunc)
        end
        # 將所有參數全部放到 ap_additionalfunc,方便取用
        ap_func = getUserApFuncPar(user_ap)          
        cur_user_ap.ap_additionalfunc = mergeAddFunc(Cfate.pan_par_hash2db(ap_func),cur_user_ap.ap_additionalfunc)
      end
    end
    return cur_user_ap
  end

  def g_getUserCurrentAps(user_id,ap_name,customer_id=nil)
    user_aps = Array.new

    # 如果是全功能展示
    if (isFullFuncShow(customer_id)) then
      user_ap = g_getShowUserAp(user_id,ap_name)
      user_aps.push(user_ap)
      return user_aps
    end

    # 如果是未登入
    if (user_id == nil) then
      user_ap = g_getGuestUserAp(user_id,ap_name)
      user_aps.push(user_ap)
      return user_aps
    end

    user_ap = g_getForeverUserAp(user_id,ap_name)
    if (user_ap != nil) then
      user_aps.push(user_ap)
      return user_aps
    end

    user_aps2 = g_getMainUserAps(user_id,ap_name)
    if (user_aps2 != nil && user_aps2.length > 0) then
      user_aps += user_aps2
      return user_aps
    end

    user_ap = g_getDemoUserAp(user_id,ap_name)
    if (user_ap != nil) then
      user_aps.push(user_ap)
      return user_aps
    end

    user_ap = g_getFreeUserAp(user_id,ap_name)
    user_aps.push(user_ap)
    return user_aps
  end
  def g_getMainUserAps(user_id,ap_name)
    user_aps = UserAp.current.check_userid(user_id).check_main().check_product_name(ap_name).all
    return user_aps
  end
  def g_getMainUserAp(user_id,ap_name)
    user_ap = UserAp.current.check_userid(user_id).check_main().check_product_name(ap_name).last
    return user_ap
  end
  def g_getDemoUserAp(user_id,ap_name)
    user_ap = UserAp.current.check_userid(user_id).check_demo().check_product_name(ap_name).last
    return user_ap
  end
  def g_getAnyUserDemoUserAp(user_id,ap_name)
    user_ap = UserAp.check_demo().check_product_name(ap_name).last
    return user_ap
  end
  def g_getFreeUserAp(user_id,ap_name)
    oProduct = g_getFreeProduct(ap_name)
    return g_ApAssignUserAp(user_id,oProduct)
  end
  def g_getGuestUserAp(user_id,ap_name)
    oProduct = g_getGuestProduct(ap_name)
    return g_ApAssignUserAp(user_id,oProduct)
  end
  def g_getShowUserAp(user_id,ap_name)
    oProduct = g_getShowProduct(ap_name)
    return g_ApAssignUserAp(user_id,oProduct)
  end
  def g_ApAssignUserAp(user_id,oProduct)
    oUserAp = UserAp.new
    if (oProduct == nil) then
      return oUserAp
    end
    oUserAp.id = 0
    oUserAp.user_id = user_id
    oUserAp.product_name = oProduct.product_name
    oUserAp.product_version = oProduct.product_version
    oUserAp.product_type = oProduct.product_type
    oUserAp.rent_id = 0
    oUserAp.status = oProduct.status
    oUserAp.ap_additionalfunc = ""
    oUserAp.gift_days = 0
    oUserAp.product_id = oProduct.id
    oUserAp.start_date = oProduct.start_date
    oUserAp.end_date = oProduct.end_date
    return oUserAp
  end

  def getUserApFuncPar(user_ap)
        hProductionFunc = getProductFuncParHash(user_ap.product_id,user_ap.product_type,user_ap.product_name) # in peterapplication
    hAddFunc = Cfate.pan_par_dbfield2hash(user_ap.ap_additionalfunc)
    hProductionFunc.merge!(hAddFunc) { |key, v1, v2| (v1 >= v2) ? v1 : v2 }
    return hProductionFunc
  end

  def mergeAddFunc(sAddFunc1,sAddFunc2)
    h1 = Cfate.pan_par_dbfield2hash(sAddFunc1)
    h2 = Cfate.pan_par_dbfield2hash(sAddFunc2)
    # h1.merge!(h2) { |key, v1, v2| v2 }
    h1.merge!(h2) { |key, v1, v2| (v1 >= v2) ? v1 : v2 }
    return Cfate.pan_par_hash2db(h1)
  end

  def getUserApCounts(user_id,ap_name)
    useraps = UserAp.available.check_userid(user_id).check_main().check_product_name(ap_name).all
    return useraps.length
  end

  def g_createDemoUserAp(user_id,ap_name)
    oUserAp = UserAp.check_userid(user_id).check_demo().check_product_name(ap_name).first
    if (oUserAp == nil) then
      oProduct = g_getDemoProduct(ap_name)
      if (oProduct != nil) then
        g_createFreeAmountUserAp(user_id,ap_name,oProduct)
      end
    end
  end

  def g_createFreeAmountUserAp(user_id,ap_name,oProduct)
    oUserAp = UserAp.new
    oUserAp.user_id = user_id
    oUserAp.product_name = oProduct.product_name
    oUserAp.product_version = oProduct.product_version
    oUserAp.product_type = oProduct.product_type
    oUserAp.rent_id = 0
    oUserAp.status = 0
    oUserAp.ap_additionalfunc = ""
    oUserAp.gift_days = 0
    oUserAp.product_id = oProduct.id
    t = Xdate.GetNowRails()
    oUserAp.start_date = t
    oUserAp.end_date = t + dateStrToIntDates(oProduct.product_content)
    if (user_id != nil) then
      oUserAp.save!
    end
    return oUserAp
  end

  def g_GetGuestApFuncHash(ap_name)
    oProduct = g_getGuestProduct(ap_name)
    h1 = Hash.new
    if (oProduct != nil) then
      h1 = Cfate.pan_par_dbfield2hash(oProduct.func_par)
    end
    return h1
  end
  def g_getGuestProduct(ap_name)
    hPar = {"star" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,0,b_PAN_FLOWYEAR,0,b_PAN_FLOWMONTH,0,b_PAN_FLOWDATE,0,b_PAN_FLOWTIME,0,b_PAN_FLOWMIN,0,SelfFourHua,0,FlyoutFourHua,0,LastPanType,0,SetStarPars,0,GanZhiBirthDisplay,0,LYYGDisplay,0,Star7Display,0,SmallYearRevertDisplay,0,LastXdatePanType,0,showXdate,1,GiSuongEnable,0,b_HallNumber,0,n_SkyFindStar_house,273,n_StarFindSky_house,0,n_explain_free_pan,0,n_explain_free_content,1,n_explain_free_house,273",
       "eightword" => "b_PAN_NORMAL,1,b_PAN_TENYEAR,0,b_PAN_FLOWYEAR,0,b_PAN_FLOWMONTH,0,b_PAN_FLOWDATE,0,b_PAN_FLOWTIME,0,b_PAN_FLOWMIN,0,n_LastPanType,0,n_LastXdatePanType,0,b_func_five_wang_display,0,b_func_par_display,0,b_func_4c_sky_mm_event,0,n_func_4c_earth_mm_event,1,b_func_fw_sky_mm_event,0,n_func_fw_earth_mm_event,0,b_SetPars,0,b_HallNumber,0",
       "xdate" => "",
       "wannianli" => "b_SetPars,0,n_LastXdatePanType,3",
       "liuyaogua" => "b_SetPars,0,b_HallNumber,0,b_explain_pan,0"
      }
    product_type = "guest"
    product_content = "1 year"
    product_version = "00"
    return g_getFreeAmountProduct(ap_name,hPar,product_type,product_content,product_version)
  end
  def g_GetFreeApFuncHash(ap_name)
    oProduct = g_getFreeProduct(ap_name)
    h1 = Hash.new
    if (oProduct != nil) then
      h1 = Cfate.pan_par_dbfield2hash(oProduct.func_par)
    else
      aProduct = g_getAllFreeProducts()
      aProduct.each do |product|
        h1.merge!(Cfate.pan_par_dbfield2hash(product.func_par))
      end
    end
    return h1
  end
  def g_GetDemoApFuncHash(ap_name)
    oProduct = g_getDemoProduct(ap_name)
    h1 = Hash.new
    if (oProduct != nil) then
      h1 = Cfate.pan_par_dbfield2hash(oProduct.func_par)
    end
    return h1
  end
  def g_GetShowApFuncHash(ap_name)
    oProduct = g_getShowProduct(ap_name)
    h1 = Hash.new
    if (oProduct != nil) then
      h1 = Cfate.pan_par_dbfield2hash(oProduct.func_par)
    end
    return h1
  end

  def g_ap_name(ap_name,ap_namespace=nil)
    if (ap_name == nil || ap_name == "") then
      return g_ap_namespace_default_ap(ap_namespace)
    else
      if (ap_name.include?("star")) then
        return "star"
      elsif (ap_name.include?("eightword")) then
        return "eightword"
      elsif (ap_name.include?("wannianli")) then
        return "wannianli"
      elsif (ap_name.include?("liuyaogua")) then
        return "liuyaogua"
      elsif (ap_name.include?(Xuexi_Ziwei.lunduan_ap_name())) then
        return Xuexi_Ziwei.lunduan_ap_name()
      elsif (ap_name.include?(Xuexi_Ziwei.pan_ap_name())) then
        return Xuexi_Ziwei.pan_ap_name()
      elsif (ap_name.include?(Xuexi_Ziwei.pars_ap_name())) then
        return Xuexi_Ziwei.pars_ap_name()
      elsif (ap_name.include?(Xuexi_Ziwei.ap_name())) then
        return Xuexi_Ziwei.ap_name()
      else
        return ap_name
      end
    end
  end
  def g_ap_namespace_default_ap(ap_namespace)
    if (ap_namespace == nil || ap_namespace == "ifate") then
      return "star"
    elsif (ap_namespace == "xuexi")
      return Xuexi_Ziwei.ap_name()
    else
      return "star"
    end
  end
  def g_ap_namespace(ap_name)
    if (["star","eightword","wannianli","liuyaogua"].include?(ap_name)) then
      return "ifate"
    else
      if ([Xuexi_Ziwei.ap_name(),Xuexi_Ziwei.lunduan_ap_name(),Xuexi_Ziwei.pan_ap_name(),Xuexi_Ziwei.pars_ap_name()].include?(ap_name)) then
        return "xuexi"
      end
    end
    return "ifate"
  end
  def xuexi_ap_name_to_controller(ap_name)
    return ap_name.downcase.sub("_","/")
  end
  def g_user_ap_name(ap_name,user_id,ap_namespace=nil)
    if (user_id == nil) then
      return g_ap_name(ap_name,ap_namespace)
    end

    a = IfatePar.check_userid_customer(user_id).first
    if (a == nil) then
      a = IfatePar.new
      a.user_id = user_id
      a.ap_name = "customer"
      a.hPars = Hash.new
    end
    hPars = a.hPars
    if (ap_name == nil) then
      ap_name1 = g_ap_name(hPars["default_ap_name"],ap_namespace)
    else
      ap_name1 = g_ap_name(ap_name,ap_namespace)
    end
    ap_namespace1 = g_ap_namespace(ap_name1)
    if (ap_namespace != nil && ap_namespace1 != ap_namespace) then
      # 不同表示切換模組，工具到學習，或學習到工具，要用該模組預設的AP
      ap_name1 = g_ap_name(hPars["#{ap_namespace}_ap_name"],ap_namespace)
      ap_namespace1 = g_ap_namespace(ap_name1)
      if (ap_namespace1 != ap_namespace) then
        ap_name1 = g_ap_namespace_default_ap(ap_namespace)
      end
    else
      ap_namespace = ap_namespace1
    end
    hPars["default_ap_name"] = ap_name1
    hPars["#{ap_namespace}_ap_name"] = ap_name1
    a.hPars = hPars
    a.save!
    return ap_name1
  end

  def g_can_use_pars(oUserAp)
        hApFunc = g_ap_product_allfunc_hash(oUserAp)
        if (oUserAp.product_name == "star") then
          return hApFunc[Star::FUNC_SET_PARS]
        elsif (oUserAp.product_name == "eightword") then
          return hApFunc[Cfate::FUNC_SET_PARS]
        elsif (oUserAp.product_name == "wannianli") then
          return hApFunc[Cfate::FUNC_SET_PARS]
        elsif (oUserAp.product_name == "liuyaogua") then
          return hApFunc[Cfate::FUNC_SET_PARS]
        elsif (oUserAp.product_name == Xuexi_Ziwei.ap_name()) then
          return hApFunc[Cfate::FUNC_SET_PARS]
        elsif (oUserAp.product_name == Xuexi_Ziwei.lunduan_ap_name()) then
          return hApFunc[Cfate::FUNC_SET_PARS]
        elsif (oUserAp.product_name == Xuexi_Ziwei.pan_ap_name()) then
          return hApFunc[Cfate::FUNC_SET_PARS]
        elsif (oUserAp.product_name == Xuexi_Ziwei.pars_ap_name()) then
          return hApFunc[Cfate::FUNC_SET_PARS]
        else
          return true
        end
  end

  def g_get_pan_pars(ap_name,userId,customer_id=1)
    oUserAp = g_getUserCurrentAp(userId,ap_name,customer_id)
    if (ap_name == "star") then
      return Star.get_pan_pars(userId,g_can_use_pars(oUserAp))
    elsif (ap_name == "eightword") then
      return Eightword.get_pan_pars(userId,g_can_use_pars(oUserAp))
    elsif (ap_name == "xdate") then
      return Xdate.get_pan_pars(userId)
    elsif (ap_name == "wannianli") then
      return Wannianli.get_pan_pars(userId,g_can_use_pars(oUserAp))
    elsif (ap_name == "liuyaogua") then
      return Liuyaogua.get_pan_pars(userId,g_can_use_pars(oUserAp))
    elsif (ap_name == Xuexi_Ziwei.ap_name()) then
      return Xuexi_Ziwei.get_pan_pars(userId,g_can_use_pars(oUserAp))
    elsif (ap_name == Xuexi_Ziwei.lunduan_ap_name()) then
      return Xuexi_Ziwei.get_pan_pars(userId,g_can_use_pars(oUserAp))
    elsif (ap_name == Xuexi_Ziwei.pan_ap_name()) then
      return Xuexi_Ziwei.get_pan_pars(userId,g_can_use_pars(oUserAp))
    elsif (ap_name == Xuexi_Ziwei.pars_ap_name()) then
      return Xuexi_Ziwei.get_pan_pars(userId,g_can_use_pars(oUserAp))
    end
    return Hash.new
  end

  def g_hasForever(ap_name)
    oProduct = Product.check_forever.check_name(ap_name).last
    if (oProduct == nil) then
      return false
    else
      return true
    end
  end

  def g_hasMain(ap_name)
    oProduct = Product.check_main.check_name(ap_name).last
    if (oProduct == nil) then
      return false
    else
      return true
    end
  end

  def isFullFuncShow(customer_id)
      oUserCustomer = UserCustomer.find_by_id(customer_id)
      if (oUserCustomer != nil) && (oUserCustomer.user_id == 0) then
        return true
      end
      return false
  end
  def getFullFuncShow_CustomerId()
      oUserCustomer = UserCustomer.check_user_id(0).first
      if (oUserCustomer != nil) then
        return oUserCustomer.id
      end
      return 0
  end

  def g_canUsePan(customer_id)
  return (customer_id != nil) && (customer_id > 0) && (customer_id != getFullFuncShow_CustomerId())
  end

  def g_panPar2Db(hParAll,user_id,ap_name)
    panParType = IfatePar.check_userid(user_id).check_apname(ap_name).first
    if (panParType == nil) then
      panParType = IfatePar.new
      panParType.user_id = user_id
      panParType.ap_name = ap_name
    end
    panParType.hPars = hParAll
    panParType.save!
  end

  def saveTestDb(key,value)
      tdb = Testdb.new
      tdb.key = key
      tdb.value = value
      tdb.save!
  end
  def remote_ip()
    return request.remote_ip.to_s
    # return "yahoo.com"
  end
  def time_now_local()
    return Xdate.time_now_ip(remote_ip())
  end

  def canDealCheck_old(user_id,oUserAp,ap_name)
    userap_counts = getUserApCounts(user_id,ap_name)
    bCanBuy = true
    bCanReBuy = true
    if (userap_counts > 1) then
      # bCanBuy = false
      bCanReBuy = false
    end
    if (oUserAp.main && !oUserAp.canBuyAdditional) then
      bCanBuy = false
    end
    if (!g_hasMain(ap_name)) then
      bCanBuy = false
      bCanReBuy = false
    end
    hCanDeal = Hash.new
    hCanDeal["BUY"]  = (!oUserAp.forever && bCanBuy) ? true : false
    hCanDeal["REBUY"]  = (!oUserAp.forever && oUserAp.main && bCanReBuy) ? true : false
    hCanDeal["DEMO"]  = (g_getDemoProduct(ap_name) != nil) && (!oUserAp.forever && !g_isDemoExist(user_id,ap_name) && oUserAp.free) ? true : false
    forever_counts = g_getForeverUserApCounts(ap_name)
    # hCanDeal["FOREVER"]  = (oUserAp.free && (forever_counts < 100)) ? true : false
    hasForever = g_hasForever(ap_name)
    hCanDeal["FOREVER"]  = (hasForever && !oUserAp.forever && (forever_counts < 100)) ? true : false

    return hCanDeal
  end
  def canDealCheck(user_id,oUserAp,ap_name)
    hCanDeal = Hash.new
    hCanDeal["BUY"]  = true
    hCanDeal["REBUY"]  = false
    hCanDeal["DEMO"]  = false
    hCanDeal["FOREVER"]  = false

    return hCanDeal
  end

  def g_userap_addone_usecount(action,nPanType,oUserAp)
    if (nPanType == Cfate::PAN_NORMAL && action == "pan") then
      # Pm.saveTestDb("UserAp_UseCount_#{oUserAp.user_id}_#{oUserAp.id}","#{oUserAp.product_name}-#{oUserAp.product_type}")
    end
  end

  def g_getInfoForPan2(share_key,ap_name,action_name)
    a = ApShare.check_key(share_key).check_apname(ap_name).check_actionname(action_name).last

    if (a == nil) then
      return nil
    else
      hData = JSON.parse(a.ap_data)
      hData[Cfate::EYear] = a.eyear
      hData[Cfate::LeapMonth] = a.eleap
      hData[Cfate::EMonth] = a.emonth
      hData[Cfate::EDate] = a.eday
      hData[Cfate::WHour] = a.hour
      hData[Cfate::WMinute] = a.minute
      hData[Cfate::SkyEarth] = a.sky_earth
      hData["user_id"] = a.user_id
      hData["customer_id"] = a.customer_id
      hData["PanType"] = a.pantype

      if (hData["customer"] != nil) then
        hData["wyear"] = hData["customer"][Cfate::WYear]
        hData["wmonth"] = hData["customer"][Cfate::WMonth]
        hData["wday"] = hData["customer"][Cfate::WDate]
        hData["hour"] = hData["customer"][Cfate::WHour]
        hData["minute"] = hData["customer"][Cfate::WMinute]
        hData["sex"] = hData["customer"][Cfate::Sex] ? "0" : "1"
        hData["name"] = hData["customer"][Cfate::Name]
      end

      a.count += 1
      a.save!

      return hData
    end
  end

  def get_HallNumber_Adver_id(ap_name)
    return Pm.getSystemValue("hallnumber_adver_id_#{ap_name}").to_i
  end
  def set_HallNumber_Adver_id(ap_name,id)
    Pm.setSystemValue("hallnumber_adver_id_#{ap_name}",id)
  end
  def find_main_for_adver_useraps(ap_name,id)
    user_aps = UserAp.available.check_product_name(ap_name).check_main.great_than(id).all
    if (user_aps == nil && id > 0) then
      user_aps = UserAp.available.check_product_name(ap_name).check_main.less_than(id).all
    end
    return user_aps
  end
  def find_last_hallnumber_adver_userap(ap_name)
    hallnumber_adver_id = get_HallNumber_Adver_id(ap_name)
    user_aps = UserAp.available.check_product_name(ap_name).check_main.great_than(hallnumber_adver_id).all
    if (user_aps != nil) then
      user_aps.each_with_index do |user_ap,nIndex|
        hApFunc = g_ap_product_allfunc_hash(user_ap)
        if (hApFunc[Cfate::FUNC_HALL_NUMBER]) then
          set_HallNumber_Adver_id(ap_name,user_ap.id)
          return user_ap
        end
      end
    end
    if (hallnumber_adver_id > 0) then
      user_aps = UserAp.available.check_product_name(ap_name).check_main.less_than(hallnumber_adver_id).all
      if (user_aps != nil) then
        user_aps.each_with_index do |user_ap,nIndex|
          hApFunc = g_ap_product_allfunc_hash(user_ap)
          if (hApFunc[Cfate::FUNC_HALL_NUMBER]) then
            set_HallNumber_Adver_id(ap_name,user_ap.id)
            return user_ap
          end
        end
      end
    end

    return nil
  end
  def find_last_hallnumber_adver(ap_name,oUserAp,hApFunc)
    hHallNumberAdver = Hash.new
    if (hApFunc[Cfate::FUNC_HALL_NUMBER]) then
      user_ap = oUserAp
      hHallNumberAdver['has_hallnumber'] = true
    else
      user_ap = find_last_hallnumber_adver_userap(ap_name)
      hHallNumberAdver['has_hallnumber'] = false
    end
    if (user_ap == nil) then
      return hHallNumberAdver
    end
    panParType = IfatePar.check_userid(user_ap.user_id).check_apname(ap_name).first
    if (panParType == nil) then
      return hHallNumberAdver
    end
    hHallNumberAdver["s_adv_hall_number1"] = panParType.hPars[Cfate::ADV_HALL_NUMBER_1]
    hHallNumberAdver["s_adv_hall_number2"] = panParType.hPars[Cfate::ADV_HALL_NUMBER_2]
    hHallNumberAdver["s_adv_hall_number3"] = panParType.hPars[Cfate::ADV_HALL_NUMBER_3]
    return hHallNumberAdver
  end

  def get_max_free_customer_count()
    s = Pm.getSystemValue("max_free_customer_count")
    if (s == nil || s == "") then
      return 10
    end
    return s.to_i
  end
  def g_isMainUserAp(user_id)
    count = UserAp.available.check_userid(user_id).check_main().count
    return (count > 0)
  end
  def g_can_add_customer(user_id,is_admin)
    max_count = get_max_free_customer_count()
    if (g_isMainUserAp(user_id) || is_admin) then
      return true,max_count
    end
    count = UserCustomer.check_user_id(user_id).count
    return (count < max_count),max_count
  end
  def g_getlastcustomer(user_id)
    if (user_id == nil) then
      return nil
    end
    uc = UserCustomer.check_user_id(user_id).last
    if (uc == nil) then
      return nil
    end
    return uc.id
  end

  def getWeenBillingServer(url="")
    # return @@BillingServer
    sBillingServer = Pm.getSystemValue("WeenBillingServer")
    # return "#{sBillingServer}#{"/"}#{I18n.locale.to_s}#{url}"
    return "#{sBillingServer}#{"/"}#{url}"
  end
  def my_post(sHttp,data)
    if (sHttp.length > 0) then
      uri = URI.parse(sHttp)

      # if (sHttp.include?("http://127")) then
      #   res = post_http(uri,data)
      # else
        res = post_https(uri,data) # force all post as https
      # end
      # if (sHttp.include?("https://")) then
      #   res = post_https(uri,data)
      # else
      #   res = post_http(uri,data)
      # end

    else
      # render :layout => false
      res = nil
    end
    return res
  end
  def post_http(uri,data)
    res = Net::HTTP.post_form(uri,data)
  end
  def post_https(uri,data)
    # uri = URI.parse("https://secure.com/")
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    http.verify_mode = OpenSSL::SSL::VERIFY_NONE
    # http.verify_mode = OpenSSL::SSL::VERIFY_PEER
    
    # request = Net::HTTP::Get.new(uri.request_uri)
    request = Net::HTTP::Post.new(uri.request_uri)
    request.set_form_data(data)

    response = http.request(request)
    # response.body
    # response.status
    # response["header-here"] # All headers are lowercase
    return response
  end


  def g_eightword_SegmentGanZhi_pars(user_id)
    oEwUserAp = g_getUserCurrentAp(user_id,"eightword")
    parAll = Eightword.get_pan_pars(user_id,g_can_use_pars(oEwUserAp))

    bSegment = false
    nFirstTimeType = parAll[Eightword::PAR_FIRST_TIME_TYPE] #Xdate::FIRST_NEXT
    nFirstSegment = parAll[Eightword::PAR_FIRST_SEGMENT]  #Xdate::SEGMENT_SPRING
    bSpace = true
    nSegmentMove = parAll[Eightword::PAR_SEGMENT_MOVE]  #Xdate::SegmentNow

    return bSegment,nFirstTimeType,nFirstSegment,bSpace,nSegmentMove
  end

  # 2018/12/25 Peter Hsieh
  # 因為session timeout的時間太短，造成session清空的現象，所以將session的資料存在資料庫中
  def g_get_session_data(user_id,ap_name)
    session_data = Pm.get_session_data_from_db(user_id,ap_name)
    s_keys = session.keys + session_data.keys
    s_keys.uniq!
    s_keys.each do |key|
      if (session[key] == nil) then
        session[key] = session_data[key]
      end
    end
    # s_keys = session.keys
    # puts "s_keys: #{s_keys}"
    # s_values = session.values
    # puts "s_values: #{s_values}"
  end
  def g_save_session_data(user_id,ap_name)
    session_data = Pm.get_session_data_from_db(user_id,ap_name)
    s_keys = session.keys
    s_keys.uniq!
    s_keys.each do |key|
      session_data[key] = session[key]
    end
    Pm.save_session_data_to_db(user_id,ap_name,session_data)
  end

  def my_set_locale(h_par)
    I18n.locale = h_par["locale"] || params[:locale] || cookies[:locale] || http_head_locale || I18n.default_locale

    # after store current locale
    cookies[:locale] = params[:locale] if params[:locale]
  rescue I18n::InvalidLocale
    I18n.locale = I18n.default_locale
  end
end
