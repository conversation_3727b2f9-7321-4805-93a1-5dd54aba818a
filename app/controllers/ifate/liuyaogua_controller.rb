require("Liuyaogua.rb")
require("Pm.rb")
require("Cfate.rb")
require("Controller_Api.rb")
require("Jianggong.rb")

class Ifate::LiuyaoguaController < ApplicationController
  include Controller_Api

  before_action :authenticate_user!, :except => []
  before_action :authenticate, :except => []

  public
  def pan
    hPars = params.clone
    hPars = get_par_from_guainfo(hPars,params["gua_id"].to_i) if params["gua_id"].to_i > 0
    if !(params["gua_id"].to_i > 0) && !Cfate.ValueBoolCheck(params["new_pan"]) then
      h_liuyaogua_pars = Pm.findTestDbHashValue_key("#{@user_id}_liuyaogua_pars")
      hPars.merge!(h_liuyaogua_pars) if h_liuyaogua_pars != {}
    end
    prepare_data(hPars)
    pan_data(hPars)
    g_get_star_pan(hPars)
    g_get_eightword_pan(hPars)

    Pm.updateTestDbHashValue_key("#{@user_id}_liuyaogua_pars",get_liuyaogua_pars())
    # session["liuyaogua_pars"] = get_liuyaogua_pars()
    session["gua_lunduan"] = @gua_lunduan
    # @ApFunc["b_explain_pan"] = false
    if (@customer_id == nil) then
      redirect_to ifate_customer_index_path, :ap_name => @ap_name
    else
      render :layout => 'liuyaogua'
    end
  end

  def save_ajax
    if (params["ajax_data_key"] != nil) then
      hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    else
      hAjaxData = params
    end
    prepare_data(hAjaxData)
    pan_data(hAjaxData)

    @gua_id = save_liuyaogua_in_pe()
    # @liuyaogua_events = PersonalEvent.available.check_option_user_id(@user_id).check_user_customer_id(session["customer_id"]).check_main_key_liuyaogua().last(3).reverse
    @liuyaogua_events = PersonalEvent.available.check_option_user_id(@user_id).check_user_customer_id(session["customer_id"]).check_main_key_liuyaogua().order("updated_at DESC").limit(3)

    render :partial => "ifate/liuyaogua/rightbar_input" , :locals => {:sVisible => checkTabVisibility(@firstShowAdvertise,'rightbar_input')}
  end
  def get_luduan_ajax
    if (params["ajax_data_key"] != nil) then
      hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    else
      hAjaxData = params
    end
    prepare_data(hAjaxData)
    @gua_lunduan = true
    pan_data(hAjaxData)

    render :partial => "ifate/liuyaogua/rightbar_input_lunduan2" , :locals => {:sVisible => "visible"}
  end
  def gualist
    prepare_data(params)
    pan_data(params)
    # @liuyaogua_events = PersonalEvent.available.check_option_user_id(@user_id).check_user_customer_id(session["customer_id"]).check_main_key_liuyaogua().all.reverse
    @liuyaogua_events = PersonalEvent.available.check_option_user_id(@user_id).check_user_customer_id(session["customer_id"]).check_main_key_liuyaogua().order("updated_at DESC").all

    render :layout => "ifate"
  end
  def delete
    gua_id = params["gua_id"]
    @liuyaogua_event = PersonalEvent.find_by_id(gua_id)
    render :layout => "ifate"
  end

  def destroy
    gua_id = params["gua_id"].to_i
    PersonalEvent.delete(gua_id)

    redirect_to :controller => "ifate/liuyaogua" ,:action => "gualist", :ap_name => @ap_name
  end

  def panprint
    hPars = params.clone
    h_liuyaogua_pars = Pm.findTestDbHashValue_key("#{@user_id}_liuyaogua_pars")
    hPars.merge!(h_liuyaogua_pars) if h_liuyaogua_pars != {}
    # hPars.merge!(session["liuyaogua_pars"]) if session["liuyaogua_pars"] != nil
    prepare_data_share(hPars)
    pan_data_share(hPars)
    @customer_id = session["customer_id"]
    @oLiuyaogua = Liuyaogua.new

    # h = session["liuyaogua_pars"]
    h = Pm.findTestDbHashValue_key("#{@user_id}_liuyaogua_pars")

    @liuyaogua_event = PersonalEvent.find_by_id(h["gua_id"])
    @liuyaogua_event = PersonalEvent.new if @liuyaogua_event == nil

    if (@customer_id != nil) then
      @usercustomer = UserCustomer.find_by_id(session["customer_id"])
      render :layout => 'liuyaogua_pan_print'
    end
  end

  def panparedit
    if (@ApFunc[Cfate::FUNC_SET_PARS]) then
      if (params["PanPar"] == nil) then
        @nPanPar = Liuyaogua::PanPar_Type
      else
        @nPanPar = params["PanPar"].to_i
      end

      render :layout => 'ifate'
    else
      redirect_to ifate_customer_index_path, :ap_name => @ap_name
      # redirect_to :action => "management"
    end
  end

  def panparupdate
    # 用 simple_form_for f.input 時，要從submit_data中取出
    # 用 form_tag 時，一樣用
    hPars = params["submit_data"]

    if (hPars["PanPar"] == nil) then
      nPanPar = Liuyaogua::PanPar_Type
    else
      nPanPar = hPars["PanPar"].to_i
    end
    if (hPars["PanParReset"] == "1") then
      hParAll = Liuyaogua.pan_par_init_each(nPanPar,@ParAll)
    else
      hParAll = Liuyaogua.pan_par_assign_each(nPanPar,@ParAll,hPars)
    end
    panPar2Db(hParAll)

    redirect_to :action => "panparedit" , :PanPar => nPanPar, :action_result => Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
  end

  def liuyaogua_pan_show_ajax
    if (params["ajax_data_key"] != nil) then
      hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    else
      hAjaxData = params
    end
    prepare_data(hAjaxData)
    pan_data(hAjaxData)
    # session["liuyaogua_pars"] = get_liuyaogua_pars()
    Pm.updateTestDbHashValue_key("#{@user_id}_liuyaogua_pars",get_liuyaogua_pars())

    render :partial => "ifate/liuyaogua/liuyaogua_pan_show_ajax"
  end
  def pan2share
    @ap_name = "liuyaogua"
    @product_type = "pan2"
    hPars = params.clone
    h_liuyaogua_pars = Pm.findTestDbHashValue_key("#{@user_id}_liuyaogua_pars")
    # hPars.merge!(session["liuyaogua_pars"]) if session["liuyaogua_pars"] != nil
    hPars.merge!(h_liuyaogua_pars) if h_liuyaogua_pars != {}
    prepare_data_share(hPars)
    pan_data_share(hPars)
    @customer_id = session["customer_id"]

    h = Hash.new
    h["ParAll"] = @ParAll
    h["ApFunc"] = @ApFunc
    h2 = {}
    h2["question"] = @question
    h["calType"] = "#{@calType}"
    h["year"] = "#{@y}"
    h["month"] = "#{@m}"
    h["day"] = "#{@d}"
    h["hour"] = "#{@h}"
    h["minute"] = "#{@min}"
    h2["gua_bie"] = @gua_bie
    h2["wai"] = @wai
    h2["nei"] = @nei
    h2["bian"] = @bian
    @UserInfo[Cfate::Name] = "OOO"
    h2["name"] = @UserInfo[Cfate::Name]
    h2.merge!(@yao_input)
    h2["dasi_pisi"] = @dasi_pisi
    h2["gua_id"] = @gua_id
    h2["gua_lunduan"] = @gua_lunduan
    h["params"] = h2
    h["UserDefData"] = @UserDefData
    h["UserInfo"] = @UserInfo

    # 需判斷重覆的狀況
    a = ApShare.check_userid(@user_id).check_customerid(@customer_id).check_apname(@ap_name).check_actionname("pan2").check_ap_data(h.to_json)
    a = a.check_eyear(@UserDefData[Cfate::EYear])
    a = a.check_emonth(@UserDefData[Cfate::LeapMonth],@UserDefData[Cfate::EMonth])
    a = a.check_eday(@UserDefData[Cfate::EDate])
    a = a.check_hour(@UserDefData[Cfate::WHour])
    ap_share = a.check_minute(@UserDefData[Cfate::WMinute]).last

    if (ap_share == nil) then
      ap_share = new_ap_share(h)
    end

    sHost = Pm.getSystemValue("MEEN_URL")
    if (params["locale"] == nil || params["locale"] == "") then
      @url = "#{sHost}/#{params["controller"]}/pan2?k=#{ap_share.share_key}"
      @url_iframe = "<iframe width='560' height='315' src='#{@url}' frameborder='0' scrolling='yes'></iframe>"
    else
      @url = "#{sHost}/#{params["locale"]}/#{params["controller"]}/pan2?k=#{ap_share.share_key}"
      @url_iframe = "<iframe width='560' height='315' src='#{@url}' frameborder='0' scrolling='yes'></iframe>"
    end

    respond_to do |format|
      format.html { redirect_to root_path } #for my controller, i wanted it to be JS only
      format.js
    end
    render :partial => "ifate/star/pan2share", :layout => false, :locals => {}
  end
  def pan2
    hPars = params.clone

    @ap_name = "liuyaogua"
    @product_type = "pan2"
    hData = g_getInfoForPan2(hPars["k"],@ap_name,"pan2")
    # hData = g_getInfoForPan2("123333445566666666")

    if (hData == nil) then
      redirect_to root_path, :ap_name => @ap_name
    else
      @user_id = hData["user_id"]
      authenticate_without_signin(@user_id)

      prepare_data_share(hData)
      pan_data_share(hData["params"])

      @ApFunc = hData["ApFunc"]
      @ParAll = hData["ParAll"]

      # 精實創業，用來分析使用狀況
      # Pm.saveTestDb("#{hPars[:controller]}-#{hPars[:action]}-#{@oUserAp.user_id}-#{@oUserAp.product_type}",hPars.to_json)

      # no pan change event
      @bPanChange = false
      @bDeclare = true

      @liuyaogua_event = PersonalEvent.find_by_id(@gua_id)
      @liuyaogua_event = PersonalEvent.new if @liuyaogua_event == nil

      render :layout => 'liuyaogua_pan2'
    end
  end
  def xdate_ajax
    hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    @m_nPanType = hAjaxData["PanType"] == nil ? Cfate::PAN_FLOWMONTH : hAjaxData["PanType"].to_i
    hUserDefData = Jianggong.get_cur_date_info(hAjaxData)
    makeXdate_data(hUserDefData)

    render :partial => "ifate/liuyaogua/pan_xdate" , :locals => {:nPanType => @m_nPanType, :sMy_url => "liuyaogua_pan", :hApFunc => @ApFunc, :nPanLastYear => @PanLastYear, :nCurWYear => @hXdate["CurWYear"], :nCurWMonth => @hXdate["CurWMonth"], :nCurWDate => @hXdate["CurWDate"], :nCurETime => @hXdate["CurETime"], :nCurEMin => @hXdate["CurEMin"], :aShowXdate => @hXdate["ShowXdate"], :apan_xdate_normal => @hXdate["pan_xdate_normal"], :apan_xdate_tenyear => @hXdate["pan_xdate_tenyear"], :nHighLightStartEYear => @hXdate["nHighLightStartEYear"], :apan_xdate_flowyear_data => @hXdate["pan_xdate_flowyear_data"], :apan_xdate_flowyear_title => @hXdate["pan_xdate_flowyear_title"], :apan_xdate_flowmonth_data => @hXdate["pan_xdate_flowmonth_data"], :apan_xdate_flowmonth_title => @hXdate["pan_xdate_flowmonth_title"], :apan_xdate_flowdate_title => @hXdate["pan_xdate_flowdate_title"], :apan_xdate_flowdate_data_title => @hXdate["pan_xdate_flowdate_data_title"], :apan_xdate_flowdate_data => @hXdate["pan_xdate_flowdate_data"], :apan_xdate_flowtime_title => @hXdate["pan_xdate_flowtime_title"], :apan_xdate_flowtime_data_title => @hXdate["pan_xdate_flowtime_data_title"], :apan_xdate_flowtime_data => @hXdate["pan_xdate_flowtime_data"], :apan_xdate_flowmin_title => @hXdate["pan_xdate_flowmin_title"], :apan_xdate_flowmin_data_title => @hXdate["pan_xdate_flowmin_data_title"], :apan_xdate_flowmin_data => @hXdate["pan_xdate_flowmin_data"], :showYearOld => 1 , :sVisible => checkTabVisibility(@firstShowAdvertise,'rightbar_xdate'), :nPanFirstYear => @PanFirstYear } 
  end

  protected
  def save_liuyaogua_in_pe()
    oPe = PersonalEvent.find_by_id(@gua_id)
    oPe = PersonalEvent.new if (oPe == nil)

    oPe.user_id = @user_id
    oPe.user_customer_id = @customer_id
    oPe.pe_main_key = "liuyaogua"
    h = get_liuyaogua_pars()
    h["result"] = @result
    oPe.h_pe_desc = h
    oPe.status = 0
    oPe.save!

    h = oPe.h_pe_desc
    h["gua_id"] = oPe.id
    oPe.h_pe_desc = h
    oPe.save!
    return oPe.id
  end
  def get_xdate_date_info(hPars)
    return Jianggong.get_cur_date_info(hPars)
  end
  def checkTabVisibility(firstShowAdvertise,blockName)
    return (firstShowAdvertise == blockName) ? "visible" : "hidden"
  end
  def new_ap_share(h)
    ap_share = ApShare.new
    ap_share.user_id = @user_id
    ap_share.customer_id = @customer_id
    ap_share.ap_name = @ap_name
    ap_share.action_name = "pan2"
    ap_share.pantype = 0
    ap_share.share_key = SecureRandom.uuid
    ap_share.eyear = @UserDefData[Cfate::EYear]
    ap_share.eleap = @UserDefData[Cfate::LeapMonth]
    ap_share.emonth = @UserDefData[Cfate::EMonth]
    ap_share.eday = @UserDefData[Cfate::EDate]
    ap_share.hour = @UserDefData[Cfate::WHour]
    ap_share.minute = @UserDefData[Cfate::WMinute]
    ap_share.sky_earth = ""
    ap_share.hAll = h
    ap_share.count = 0
    ap_share.status = 0
    ap_share.save!

    return ap_share
  end
  def panPar2Db(hParAll)
    panParType = IfatePar.check_userid(@user_id).check_apname(@ap_name).first
    if (panParType == nil) then
      panParType = IfatePar.new
      panParType.user_id = @user_id
      panParType.ap_name = @ap_name
    end
    panParType.hPars = hParAll
    panParType.save!
  end
  def logged_in?
    return (current_user != nil)
  end

  def authenticate_without_signin(user_id)
    @user_id = user_id
    @user_level = ""
    new_ap_name = "liuyaogua"
    ap_namespace = "ifate"
    @user_name = ""
    @ap_name = g_user_ap_name(new_ap_name,@user_id,ap_namespace)
    @admin = false

    session["userlevel"] = @user_level

    getUserCurrentAp(@user_id)

    # session[:ap_name] = @ap_name
    session["ap_version"] = @ap_version
    session["user_name"] = { :value => @user_name, :expires => 1.minute.from_now }
    @ParAll = Liuyaogua.get_pan_pars(@user_id,g_can_use_pars(@oUserAp))
    liuyaogua_version_check()

    @action_result = params["action_result"]

    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?

    @can_add_customer,@customer_max_count = g_can_add_customer(@user_id,@admin)

    @simplepan = params["simplepan"] != nil ? (params["simplepan"] == "normal" ? false : true) : (session["simplepan"] == nil ? false : session["simplepan"])
    session["simplepan"] = @simplepan
  end
  def authenticate
    authenticate_liuyaogua
  end
  def authenticate_liuyaogua(new_ap_name="liuyaogua",ap_namespace="ifate")
    if (Pm.getSystemValue("WebSpeedTest") == "1") then
      @user_name = "<EMAIL>" #current_user.email
      @user_id = 3
    else
      #@user_name = current_user.name
      @user_name = current_user.email if logged_in?
      @user_id = current_user.id if logged_in?
    end
    @user_level = ""
    @ap_name = g_user_ap_name(new_ap_name,@user_id,ap_namespace)
    @admin = current_user.roles? :admin

    session["userlevel"] = @user_level

    getUserCurrentAp(@user_id)

    # session[:ap_name] = @ap_name
    session["ap_version"] = @ap_version
    session["user_name"] = { :value => @user_name, :expires => 1.minute.from_now }
    @ParAll = Liuyaogua.get_pan_pars(@user_id,g_can_use_pars(@oUserAp))
    liuyaogua_version_check()

    @action_result = params["action_result"]

    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?

    @can_add_customer,@customer_max_count = g_can_add_customer(@user_id,@admin)

    @simplepan = params["simplepan"] != nil ? (params["simplepan"] == "normal" ? false : true) : (session["simplepan"] == nil ? false : session["simplepan"])
    session["simplepan"] = @simplepan
  end

  def getUserCurrentAp(user_id)
    @oUserAp = g_getUserCurrentAp(user_id,@ap_name)
    @rent_user = false    
    @rent_user = @oUserAp.main if @oUserAp != nil
    @rent_user = @rent_user || @admin
    @showAdvertise = true
    # @firstShowAdvertise = params["firstShowAdvertise"] if params["firstShowAdvertise"] != nil
    @firstShowAdvertise = session["liuyaogua_firstShowAdvertise"] if session["liuyaogua_firstShowAdvertise"] != nil
    # @firstShowAdvertise = @oUserAp.firstShowAdvertise && @showAdvertise ? "pan_information_block" : "rightbar_input" if @firstShowAdvertise == nil
    @firstShowAdvertise = @oUserAp.firstShowAdvertise && @showAdvertise ? "rightbar_input" : "rightbar_input" if @firstShowAdvertise == nil
    session["liuyaogua_firstShowAdvertise"] = @firstShowAdvertise
    # @firstShowAdvertise = "rightbar_input"
    @canUsePan = g_canUsePan(session["customer_id"])
  end
  def g_get_eightword_pan(hPars)
    nPanType = @m_nPanType
    hUserInfo = g_get_user_customer(@user_id,@customer_id)
    # @firstShowAdvertise = "pan_star_block" if @sp_customer_id != nil
    hUserDefData = @UserDefData.clone
    # for test
    # @firstShowAdvertise = params["firstShowAdvertise"] if params["firstShowAdvertise"] != nil
    hoUserAp = g_getUserCurrentAp(@user_id,"eightword")    
    hApFunc = g_ap_product_allfunc_hash(hoUserAp)
    hParAll = Star.get_pan_pars(@user_id,g_can_use_pars(hoUserAp))
    if (!hoUserAp.main) then
      hUserInfo[Cfate::WYear] = hUserInfo[Cfate::WYear] - 1
    end
    @sp_eightword_pan = g_eightword_pan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
  end
  def g_get_star_pan(hPars)
    @show_change_button = false
    nPanType = @m_nPanType
    nPanType = Cfate::PAN_NORMAL
    @m_nSpPanType = nPanType
    hUserInfo = g_get_user_customer(@user_id,@customer_id)
    # @firstShowAdvertise = "pan_star_block" if @sp_customer_id != nil
    hUserDefData = @UserDefData.clone
    # for test
    # @firstShowAdvertise = params["firstShowAdvertise"] if params["firstShowAdvertise"] != nil
    hoUserAp = g_getUserCurrentAp(@user_id,"star")    
    hApFunc = g_ap_product_allfunc_hash(hoUserAp)
    hParAll = Star.get_pan_pars(@user_id,g_can_use_pars(hoUserAp))
    if (!hoUserAp.main) then
      hUserInfo[Cfate::WYear] = hUserInfo[Cfate::WYear] - 1
    end
    @sp_star_pan = g_star_pan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
  end

  def liuyaogua_version_check()
    # 權限控管
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @ApFunc[Cfate::FUNC_HALL_NUMBER] = true if @admin
    @hHallNumberAdver = find_last_hallnumber_adver(@ap_name,@oUserAp,@ApFunc)
    @hCanDeal = canDealCheck(@user_id,@oUserAp,@ap_name)
    @main_products = g_getMainProducts(@ap_name)
    @canAddPe = session["customer_id"] != nil
  end
  def pan_data(hPars)
    @ap_name = "liuyaogua"
    liuyaogua_pan(hPars)
    @m_nPanType = hPars["PanType"] == nil ? Cfate::PAN_FLOWMONTH : hPars["PanType"].to_i
    makeXdate_data(@Xdate_date)
    pan_prepare_personalevent_data()
  end
  def pan_data_share(hPars)
    @ap_name = "liuyaogua"
    @dasi_pisi = hPars["dasi_pisi"]
    @gua_id = hPars["gua_id"].to_i
    liuyaogua_pan(hPars)
  end

  def makeXdate_data(hUserDefData)
    @PanFirstYear = Cfate::PAN_FIRST_YEAR # 萬年曆第一年
    @PanLastYear = Cfate::PAN_LAST_YEAR # 萬年曆最後一年

    hAppData = Hash.new
    hNow = new_for_now()

    hAppData["CurWYear"] = hNow[Cfate::WYear]
    hAppData["CurWMonth"] = hNow[Cfate::WMonth]
    hAppData["CurWDate"] = hNow[Cfate::WDate]
    hAppData["CurWHour"] = hNow[Cfate::WHour]
    hAppData["CurWMin "] = hNow[Cfate::WMinute]
    hAppData["FlowWYear"] = hUserDefData[Cfate::WYear]
    hAppData["FlowWMonth"] = hUserDefData[Cfate::WMonth]
    hAppData["FlowWDate"] = hUserDefData[Cfate::WDate]
    hAppData["FlowWHour"] = hUserDefData[Cfate::WHour]
    hAppData["FlowWMin"] = hUserDefData[Cfate::WMinute]

    hParAll = @ParAll != nil ? @ParAll.clone : {}
    hParAll[Cfate::PAR_PAN_YEAR_DISPLAY] = @ParAll != nil ? @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY] : Cfate::PAN_DISPLAY_WEST

    hApFunc = @ApFunc.clone
    hApFunc[Cfate::LAST_XDATE_PAN_TYPE] = Cfate::PAN_FLOWMONTH 
    @hXdate = Xdate.MakeXdateInfo_Wannianli(hAppData,Cfate::PAN_FLOWMONTH,hApFunc,hParAll)
    # @firstShowAdvertise = 'rightbar_xdate'
  end
  def new_for_now()
    h = Hash.new

    t = time_now_local()

    h["user_id"] = session["user_id"]
    ey,em,ed,el = Xdate.West2East(t.year,t.month,t.day)
    h[Cfate::Name] = Xdate.make_timestamp(t.year,t.month,t.day,t.hour)
    h[Cfate::Sex] = false
    h[Cfate::CalType] = Xdate::CT_SOLAR
    h[Cfate::WYear] = t.year
    h[Cfate::WMonth] = t.month
    h[Cfate::WDate] = t.day
    h[Cfate::EYear] = ey
    h[Cfate::ELeapMonth] = el
    h[Cfate::EMonth] = em
    h[Cfate::EDate] = ed
    h[Cfate::WHour] = t.hour
    h[Cfate::WMinute] = t.min

    @user_customer = UserCustomer.new
    @user_customer.calType = Xdate::CT_SOLAR
    @user_customer.wyear = t.year
    @user_customer.wmonth = t.month
    @user_customer.wday = t.day
    @user_customer.hour = t.hour
    @user_customer.minute = t.min
    @user_customer.name = ""
    @user_customer.sex = 1

    return h
  end

  def get_pan_customer_info(hData=nil)
    hUserInfo = Hash.new

    hUserInfo["remote_ip"] = remote_ip()

    session["customer_id"] = hData["customer_id"] == nil ? session["customer_id"] : hData["customer_id"].to_i
    @liuyaogua_usercustomer = UserCustomer.find_by_id(session["customer_id"])

    @customer_id = session["customer_id"]
    if (@customer_id != nil && @customer_id > 0) then
      oCustomer = UserCustomer.check_option_user_id(@user_id).find(@customer_id)
      if (oCustomer != nil) then
        hUserInfo[Cfate::WYear] = oCustomer.wyear
        hUserInfo[Cfate::WMonth] = oCustomer.wmonth
        hUserInfo[Cfate::WDate] = oCustomer.wday
        hUserInfo[Cfate::WHour] = oCustomer.hour
        hUserInfo[Cfate::WMinute] = oCustomer.minute
        hUserInfo[Cfate::Sex] = oCustomer.sex == 1 ? false : true
        hUserInfo[Cfate::Name] = oCustomer.name
        @customer_user_id = oCustomer.user_id
      end
    end

    if (@customer_id != nil) then
      api_set_user_customer_updated_at(@customer_id)
    end

    return hUserInfo
  end
  def prepare_data(hPars)
    sLang = my_current_lang()
    get_liuyaogua_data(hPars) # 取得卜卦的日期 @hData @UserDefData
    @hData.merge!(hPars) { |key, v1, v2| v2 }

    @UserInfo = get_pan_customer_info(hPars) # 取得客戶資訊

    @Xdate_date = get_xdate_date_info(@hData) # 取得xdate的日期

    @par_YearDisplay = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]
    @starYearSelect = 1901
    @stopYearSelect = 2099
    t = time_now_local()
    @TodayYear = t.year
    @PanLastYear = Cfate::PAN_LAST_YEAR # 萬年曆最後一年

    find_search_customers(hPars)

    @ok_button = @hData["ok_button"].to_i

    @dasi_pisi = hPars["dasi_pisi"]
    @gua_id = hPars["gua_id"].to_i

    @gua_bie_list = gua_bie_list_for_select()
    @gua_list = customer_gua_list_for_select()
    @gua_yao_select = Divination.gua_yao_for_select(@ParAll[Liuyaogua::PAR_YAODISPLAYTYPE])

    @liuyaogua_event_count = PersonalEvent.available.check_option_user_id(@user_id).check_main_key_liuyaogua().count
    @bCanAddGua = @rent_user || @liuyaogua_event_count < 10 ? true : false
    # @bCanAddGua = false
  end
  def prepare_data_share(hPars)
    @UserDefData = hPars["UserDefData"]
    @UserInfo = hPars["UserInfo"] # 取得客戶資訊
  end
  def get_liuyaogua_data(hPars)
    @hData = Xdate.date_input_prepare_with_timestamp(hPars,"timestamp_udt")
    @UserDefData = Jianggong.get_cur_date_info(@hData)
    @calType = @hData["calType"]

    if (@hData["calType"] == Xdate::CT_SOLAR) then
      @y = @hData["wyear"]
      @l = false
      @m = @hData["wmonth"]
      @d = @hData["wday"]
    else
      @y = @hData["eyear"]
      @l = @hData["eleap"]
      @m = @hData["emonth"]
      @d = @hData["eday"]
    end
    @h = @hData["hour"]
    @min = @hData["minute"]
    @pe_desc = ""
  end
  def find_search_customers(hPars,nLastId=0,nCount=100)
    @hCustDisplay = {"b_name" => true}
    keys = ["b_name_abbr","b_sex","b_solar","b_solar_year","b_solar_md","b_solar_hm","b_lunar","b_lunar_year","b_lunar_md","b_lunar_hm","b_blood","b_showsum"]
    keys.each do |key|
      @hCustDisplay[key] = false
    end
    sSearchData = hPars["customer_search_data"]
    if (sSearchData == nil) then
      sSearchData = ""
    end
    @par_YearDisplay = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]
    user_id = @user_id
    session["customer_search_data2"],@all_user_customer2,@list_count2 = api_find_search_customers(@user_id,sSearchData,nLastId,nCount,@par_YearDisplay)
    @customer_search_data2 = session["customer_search_data2"]
    @customer_search_data = @customer_search_data2
  end
  def liuyaogua_pan_check(hPars)
    gl = hPars["gua_lunduan"] == nil ? session["gua_lunduan"] : hPars["gua_lunduan"]
    @gua_lunduan = Cfate.ValueBoolCheck(gl)
    @gua_bie = hPars["gua_bie"]
    if (@gua_bie == nil) then
      @gua_bie = Divination::Jiankang
    end
    @question = hPars["question"]
  end

  def liuyaogua_pan(hPars)
    qi_gua = "qi_gua_no"
    if (@gua_id > 0) then
      if (hPars["yao_1"] != nil) then
        qi_gua = "qi_gua_yao"
      end
    else
      if (@ParAll[Liuyaogua::PAR_QIGUAFANXI] == Liuyaogua::PAR_QIGUAFANXI_YAO) then
        qi_gua = "qi_gua_yao"
      end
    end
    liuyaogua_pan_check(hPars)
    @gua_yao = Array.new(6)
    if (qi_gua == nil || qi_gua == "" ||qi_gua == "qi_gua_no") then
      liuyaogua_pan_1(hPars)
    else
      liuyaogua_pan_2(hPars)
    end
    # @liuyaogua_events = PersonalEvent.available.check_option_user_id(@user_id).check_user_customer_id(session["customer_id"]).check_main_key_liuyaogua().last(3).reverse
    @liuyaogua_events = PersonalEvent.available.check_option_user_id(@user_id).check_user_customer_id(session["customer_id"]).check_main_key_liuyaogua().order("updated_at DESC").limit(3)
  end
  def liuyaogua_pan_1(hPars)
    year,month,day,hour = get_udt_dates()
    @user_name = hPars["name"]
    if (@user_name == nil) then
      if (@liuyaogua_usercustomer != nil) then
        @user_name = @liuyaogua_usercustomer.name
      end
    end
    if (hPars["wai"] != nil) then
      wai = hPars["wai"].to_i
    else
      wai = hPars["up"].to_i
    end
    if (hPars["nei"] != nil) then
      nei = hPars["nei"].to_i
    else
      nei = hPars["down"].to_i
    end
    if (hPars["bian"] != nil) then
      bian = hPars["bian"].to_i
    else
      bian = hPars["change"].to_i
    end

    oWeenApi = WeenApi.new
    @result = oWeenApi.g_pugua(wai,nei,bian,year,month,day,hour,@user_name,@question,@gua_bie,@ParAll,@ApFunc) if @gua_lunduan
    @result = oWeenApi.g_pugua_pan(wai,nei,bian,year,month,day,hour,@user_name,@question,@gua_bie,@ParAll,@ApFunc) if !@gua_lunduan
    @result = remove_for_free_user(@result) if !@rent_user
    @result = {} if hPars["wai"] == nil && hPars["up"] == nil
    @result = {} if wai == 0 && nei == 0 && bian == 0
    # puts "#{@result["pugua_pan"]["pan"]["parameter"]["hu_xing"]}"
    @wai = wai
    @nei = nei
    @bian = bian
    @yao_input = {}
  end
  def get_liuyaogua_pars()
    h = {}
    h["name"] = @user_name
    h["question"] = @question
    h["calType"] = "#{@calType}"
    h["year"] = "#{@y}"
    h["month"] = "#{@m}"
    h["day"] = "#{@d}"
    h["hour"] = "#{@h}"
    h["minute"] = "#{@min}"
    h["wai"] = @wai
    h["nei"] = @nei
    h["bian"] = @bian
    h["gua_bie"] = @gua_bie
    h["dasi_pisi"] = @dasi_pisi
    h["gua_id"] = @gua_id
    h["gua_lunduan"] = @gua_lunduan
    h.merge!(@yao_input)
    h["UserInfo"] = @UserInfo
    h["UserDefData"] = @UserDefData
    # h.merge!(@hD)
    return h
  end

  # http://127.0.0.1:3000/ifate/liuyaogua/pan?ap_name=liuyaogua&yao_1=6&yao_2=1&yao_3=0&yao_4=6&yao_5=9&yao_6=1&uy=2021&um=6&ud=7
  def liuyaogua_pan_2(hPars)
    year,month,day,hour = get_udt_dates()
    @user_name = hPars["name"]
    if (@user_name == nil) then
      if (@liuyaogua_usercustomer != nil) then
        @user_name = @liuyaogua_usercustomer.name
      end
    end
    @yao_input = {}
    (1..6).each do |yao_idx|
      @gua_yao[yao_idx - 1] = hPars["yao_#{yao_idx}"].to_i
      @yao_input["yao_#{yao_idx}"] = hPars["yao_#{yao_idx}"]
    end
    wai,nei,bian = Divination.yao_2_gua(@gua_yao)
  # puts "wai : #{wai}"
  # puts "nei : #{nei}"
  # puts "bian : #{bian}"
    oWeenApi = WeenApi.new
    @result = oWeenApi.g_pugua(wai,nei,bian,year,month,day,hour,@user_name,@question,@gua_bie,@ParAll,@ApFunc) if @gua_lunduan
    @result = oWeenApi.g_pugua_pan(wai,nei,bian,year,month,day,hour,@user_name,@question,@gua_bie,@ParAll,@ApFunc) if !@gua_lunduan
    @result = remove_for_free_user(@result) if !@rent_user
    @result = {} if hPars["yao_1"] == nil
    # puts "#{@result["pugua_pan"]["pan"]["parameter"]["hu_xing"]}"
    @wai = nil
    @nei = nil
    @bian = nil
  end
  def get_udt_dates()
    year = @UserDefData[Cfate::WYear]
    month = @UserDefData[Cfate::WMonth]
    day = @UserDefData[Cfate::WDate]
    hour = @UserDefData[Cfate::WHour]

    return year,month,day,hour
  end
  def customer_gua_list_for_select()
    h = Hash.new
    (0..2).each do |i|
      h["gua_#{i}"] = i
    end
    return h
  end
  def gua_bie_list_for_select()
    q = {Pm.t("liuyaogua.#{Divination::Jiankang}.question_desc") => Divination::Jiankang,
          Pm.t("liuyaogua.#{Divination::Qiucai}.question_desc") => Divination::Qiucai,
          Pm.t("liuyaogua.#{Divination::Nuweihun}.question_desc") => Divination::Nuweihun,
          Pm.t("liuyaogua.#{Divination::Nanweihun}.question_desc") => Divination::Nanweihun,
          Pm.t("liuyaogua.#{Divination::Nuyihun}.question_desc") => Divination::Nuyihun,
          Pm.t("liuyaogua.#{Divination::Nanyihun}.question_desc") => Divination::Nanyihun,
          Pm.t("liuyaogua.#{Divination::ZhiChang}.question_desc") => Divination::ZhiChang
          }
    return q
  end
  def pan_prepare_personalevent_data()
    tNow = time_now_local()
    oUserCustomer = UserCustomer.find_by_id(session["customer_id"])
    if (oUserCustomer == nil) then
      @starYearSelect = 1901
      @user_create_date = ""
    else
      @starYearSelect = oUserCustomer.wyear
      t = oUserCustomer.created_at
      @user_create_date = "#{t.year}/#{t.month}/#{t.day}#{Pm.t("IDS_S_CREATED")}"
    end
      if (oUserCustomer == nil || oUserCustomer.wyear <= tNow.year) then
        @stopYearSelect = tNow.year
      else
        @stopYearSelect = oUserCustomer.wyear + 10
      end

    @pe_title_data = Pm.GetStrWithQuote("IDS_PE_TITLE")
    # h.invert
    @pe_mains = Cfate.pan_par_dbfield2hash(Pm.GetPeStr("pe_mains"))
    @pe_main_key_default = @pe_mains.values[0]

    @pe_subs = Cfate.pan_par_dbfield2hash(Pm.GetPeStr(@pe_main_key_default))
    @pe_sub_key_default = @pe_subs.values[0]
    @pe_time = tNow
    @pe_year = @pe_time.year
    @pe_month = Xdate::DATE_NIL
    @pe_day = Xdate::DATE_NIL
    @pe_hour = Xdate::DATE_NIL
    @pe_min = Xdate::DATE_NIL

    @personal_events = PersonalEvent.available.check_option_user_id(@user_id).check_user_customer_id(session["customer_id"]).check_main_key_pe().last(3).reverse
    @canAddPe = session["customer_id"] != nil
  end

  def get_par_from_guainfo(hPars,gua_id) 
    oPe = PersonalEvent.find_by_id(gua_id)
    return hPars if oPe == nil
    oPe.updated_at = Xdate.GetNowRails()
    oPe.save!
    return oPe.h_pe_desc
  end

  def remove_for_free_user(result)
    result = remove_for_free_user_param(result)
    result = remove_for_free_user_main(result)
    result = remove_for_free_user_data(result)

    return result
  end
  def remove_for_free_user_data(result)
    result["pugua_pan"]["pan"]["data"]["ganzhi_liuqin"].fill("")

    return result
  end

  def remove_for_free_user_main(result)
    (0..5).each do |i|
      result["pugua_pan"]["pan"]["main_gua"]["bian_gua_liu_qin"][i] = ""
    # 有伏神
      # ["官鬼", "己", "亥", "水"] sample
      result["pugua_pan"]["pan"]["main_gua"]["fu_shen"][i].fill("")
      result["pugua_pan"]["pan"]["main_gua"]["bian_dong_jie_guo"][i].fill("")
    end
    return result
  end

  def remove_for_free_user_param(result)
    # 日辰
    result["pugua_pan"]["pan"]["parameter"]["chang_sheng"].fill("")

    # 卦身
    result["pugua_pan"]["pan"]["parameter"]["gua_shen"].fill("")

    # 劫煞
    result["pugua_pan"]["pan"]["parameter"]["jie_sha"] = ""

    # 桃花
    result["pugua_pan"]["pan"]["parameter"]["tao_hua"] = ""
    # 天喜
    result["pugua_pan"]["pan"]["parameter"]["tian_xi"] = ""
    # 驛馬
    result["pugua_pan"]["pan"]["parameter"]["yi_ma"] = ""
    # 干祿
    result["pugua_pan"]["pan"]["parameter"]["gan_lu"] = ""
    # 羊刃
    result["pugua_pan"]["pan"]["parameter"]["yang_ren"] = ""
    # 貴人
    result["pugua_pan"]["pan"]["parameter"]["gui_ren"].fill("")
    # 空亡
    result["pugua_pan"]["pan"]["parameter"]["kong_wang"].fill("")

    # 日合
    result["pugua_pan"]["pan"]["parameter"]["ri_he"] = ""

    # 月合
    result["pugua_pan"]["pan"]["parameter"]["yue_he"] = ""

    # 年合
    result["pugua_pan"]["pan"]["parameter"]["nian_he"] = ""
    # 日沖
    result["pugua_pan"]["pan"]["parameter"]["ri_chong"] = ""
    # 月沖
    result["pugua_pan"]["pan"]["parameter"]["yue_chong"] = ""
    # 年沖
    result["pugua_pan"]["pan"]["parameter"]["nian_chong"] = ""
    # 互刑
    result["pugua_pan"]["pan"]["parameter"]["hu_xing"]["jieguo_count"] = 0
    # 三刑
    result["pugua_pan"]["pan"]["parameter"]["san_xing"]["jieguo_count"] = 0
    # 三合
    result["pugua_pan"]["pan"]["parameter"]["san_he"]["jieguo_count"] = 0

    return result
  end
end
