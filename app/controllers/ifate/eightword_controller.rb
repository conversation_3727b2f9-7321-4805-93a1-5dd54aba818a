require("Eightword.rb")
require("Pm.rb")
require("Cfate.rb")
require("Controller_Api.rb")

class Ifate::EightwordController < ApplicationController
  include Controller_Api

  before_action :authenticate_user!, :except => [:pan2,:pansimple,:pansimpleinput,:xml_gen,:free,:show]
  before_action :authenticate, :except => [:pan2,:pansimple,:pansimpleinput,:xml_gen,:free,:show]
  layout "ifate"

  def logged_in?
    return (current_user != nil)
  end
  def get_session_data()
    user_id = current_user.id if logged_in?
    g_get_session_data(user_id,"Eightword")
  end
  def save_session_data()
    user_id = current_user.id if logged_in?
    g_save_session_data(user_id,"Eightword")
  end

  def authenticate
    get_session_data()
    if (Pm.getSystemValue("WebSpeedTest") == "1") then
      @user_name = "<EMAIL>" #current_user.email
      @user_id = 3
    else
      #@user_name = current_user.name
      @user_name = current_user.email if (current_user != nil)
      @user_id = current_user.id if (current_user != nil)
    end
    @admin = current_user.roles? :admin
    @user_level = ""
    @ap_name = g_user_ap_name("eightword",@user_id)

    session["userlevel"] = @user_level

    getUserCurrentAp(@user_id)

    # session["ap_name"] = @ap_name
    session["ap_version"] = @ap_version
    session["user_name"] = { :value => @user_name, :expires => 1.minute.from_now }
    @ParAll = Eightword.get_pan_pars(@user_id,g_can_use_pars(@oUserAp))
    ap_version_check()

    @action_result = params["action_result"]

    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?
    @canAddPe = session["customer_id"] != nil

    @can_add_customer,@customer_max_count = g_can_add_customer(@user_id,@admin)
  end

  def getUserCurrentAp(user_id)
    @oUserAp = g_getUserCurrentAp(@user_id,@ap_name)
    @rent_user = false    
    @rent_user = @oUserAp.main if @oUserAp != nil
    @rent_user = @rent_user || @admin
    @showAdvertise = true
    @firstShowAdvertise = @oUserAp.firstShowAdvertise && @showAdvertise ? "pan_information_block" : "pan_xdate_block"
    @firstShowAdvertise = session["eightword_firstShowAdvertise"] if session["eightword_firstShowAdvertise"] != nil
    @canUsePan = g_canUsePan(session["customer_id"])
  end

  def ap_version_check()
    # 權限控管
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @hHallNumberAdver = find_last_hallnumber_adver(@ap_name,@oUserAp,@ApFunc)
    @hCanDeal = canDealCheck(@user_id,@oUserAp,@ap_name)
    @main_products = g_getMainProducts(@ap_name)
  end

  public
  def pan
    # 精實創業，用來分析使用狀況
    # Pm.saveTestDb("#{params[:controller]}-#{params[:action]}-#{@oUserAp.user_id}-#{@oUserAp.product_type}",params.to_json)

    # 處理使用者傳入之資料及相關物件之準備
    pan_prepare_data(params)

    # no pan change event
    @bPanChange = true
    @bDeclare = false
    @nClassPost = ""

    @oEightword = Eightword.new
    @oEightword.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll,@ApFunc)
    # 排盤資料
    pan_pan_info()

    # 萬年曆
    pan_xdate_info()

    # 個人事件資料
    pan_prepare_personalevent_data()

    # 產品購買資訊
    pan_userap_info()

    @edit_customer_url = "/ifate/customer/edit"
    @select_customer_url = "/ifate/customer/index"
    @url_back = "/ifate/eightword/pan"

    @aUrlKey = []
    @aUrlValue = []

    g_userap_addone_usecount(params["action"],@m_nPanType,@oUserAp)

    # 取得副盤的資料
    @sp_customer_ids = g_get_user_customers(@user_id)
    g_get_eightword_pan(params)
    g_get_star_pan(params)

    save_session_data()
    if (@customer_id == nil) then
      redirect_to ifate_customer_index_path, :ap_name => @ap_name
    else
      render :layout => "eightword_pan"
    end
  end
  def update_sp_eightword_pan_ajax
    hData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    pan_prepare_data(hData)
    # 取得副盤的資料
    g_get_eightword_pan(hData)
    g_get_star_pan(hData)
    @sp_customer_ids = g_get_user_customers(@user_id)
    render :partial => "ifate/eightword/pan_eightword" , :layout => false, :locals => {}
  end

  protected
  def g_get_eightword_pan(hPar)
    nPanType = @m_nPanType
    nPanType = Cfate::PAN_NORMAL
    @m_nSpPanType = nPanType
    hUserInfo = g_get_sp_customer_info(hPar)
    hUserDefData = @UserDefData.clone
    # for test
    @firstShowAdvertise = params["firstShowAdvertise"] if params["firstShowAdvertise"] != nil
    session["eightword_firstShowAdvertise"] = @firstShowAdvertise
    @sp_eightword_pan = g_eightword_pan(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll)
  end
  def g_get_sp_customer_info(hPar)
    @sp_customer_id = hPar["sp_customer_id"] == nil ? session["sp_customer_id"] : hPar["sp_customer_id"].to_i
    @sp_customer_id = session["customer_id"] if @sp_customer_id == nil || @sp_customer_id == 0
    session["sp_customer_id"] = @sp_customer_id
    hUserInfo = g_get_user_customer(@user_id,@sp_customer_id)
    api_set_user_customer_updated_at(@sp_customer_id)
    return hUserInfo
  end
  def g_get_star_pan(hPar)
    nPanType = @m_nPanType
    hUserInfo = g_get_user_customer(@user_id,@customer_id)
    # @firstShowAdvertise = "pan_star_block" if @sp_customer_id != nil
    hUserDefData = @UserDefData.clone
    # for test
    # @firstShowAdvertise = params["firstShowAdvertise"] if params["firstShowAdvertise"] != nil
    hoUserAp = g_getUserCurrentAp(@user_id,"star")    
    hApFunc = g_ap_product_allfunc_hash(hoUserAp)
    hParAll = Star.get_pan_pars(@user_id,g_can_use_pars(hoUserAp))
    if (!@rent_user) then
      hUserInfo[Cfate::WYear] = hUserInfo[Cfate::WYear] - 1
    end
    @sp_star_pan = g_star_pan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
  end
  def pan_userap_info()
    oProduct = g_getProductByUserAp(@oUserAp)
    @buy_info = ""
    @ap_version_desc = oProduct.product_description if oProduct != nil
    if (oProduct.demo) then
      @ap_version_desc += "(#{Pm.GetStrWithColon("IDS_S_UNTILL")}#{@oUserAp.end_date.strftime("%Y-%m-%d")})"
    else
      if (oProduct.free) then
        if (g_isDemoExist(@user_id,@ap_name)) then
          @buy_info = "buy"
        else
          @buy_info = "demo"
        end
        @ap_version_desc = Pm.GetStr("IDS_E_APP_PAN_NOT_BUIED")
      else
        if ((@oUserAp.end_date < Xdate.GetNowRails() + 14.days) && getUserApCounts(@user_id,@ap_name) < 2) then
          @buy_info = "rebuy"
        end
        hApFunc = g_ap_product_allfunc_hash(@oUserAp)
        @ap_version_desc = Pm.GetStr("IDS_E_APP_PAN_BUIED")
        if (hApFunc[Cfate::FUNC_HALL_NUMBER]) then
          @ap_version_desc = Pm.GetStr("IDS_E_APP_PAN_BUIED_FULL")
        end
      end
    end
    if (@customer_user_id == 0) then
      @ap_version_desc = Pm.GetStr("IDS_S_ALL_SHOW")
    end
  end

  def pan_prepare_data(hData=nil)
    # 排盤資訊
    @PanLastYear = Cfate::PAN_LAST_YEAR # 萬年曆最後一年

    # 設定客戶資訊
    hUserInfo = set_pan_customer_info(hData)
    @UserInfo = hUserInfo

    # 處理流盤時間
    hUserDefData = set_pan_user_def_data(hData)
    @UserDefData = hUserDefData

    # 開始處理排盤相關資訊
    @WYear = hUserInfo[Cfate::WYear]
    @WMonth = hUserInfo[Cfate::WMonth]
    @WDate = hUserInfo[Cfate::WDate]
    @WHour = hUserInfo[Cfate::WHour]
    @WMinute = hUserInfo[Cfate::WMinute]
    @Sex = hUserInfo[Cfate::Sex]
    @Name = hUserInfo[Cfate::Name]

    @UA_EYear = hUserDefData[Cfate::EYear]
    @UA_EMonth = hUserDefData[Cfate::EMonth]
    @UA_EDate = hUserDefData[Cfate::EDate]
    @UA_WHour = hUserDefData[Cfate::WHour]
    @UA_WMinute = hUserDefData[Cfate::WMinute]
    @UA_LeapMonth = hUserDefData[Cfate::LeapMonth]

  end
  def set_pan_customer_info(hData=nil)
    hUserInfo = Hash.new

    hUserInfo["remote_ip"] = remote_ip()

    # 預設用現在時間當客戶生日排盤,男生
    t = time_now_local()
    hUserInfo[Cfate::WYear] = t.year
    hUserInfo[Cfate::WMonth] = t.month
    hUserInfo[Cfate::WDate] = t.day
    hUserInfo[Cfate::WHour] = t.hour
    hUserInfo[Cfate::WMinute] = t.min
    hUserInfo[Cfate::Sex] = false
    hUserInfo[Cfate::Name] = "Profate"

    session["customer_id"] = hData["customer_id"] == nil ? session["customer_id"] : hData["customer_id"].to_i
    nPanType = session["pan_type"] == nil ? Cfate::PAN_NORMAL : session["pan_type"]

    if (hData["PanType"] != nil) then
      nPanType = hData["PanType"].to_i
    end
    if (nPanType > @ApFunc["n_LastPanType"]) then
      nPanType = @ApFunc["n_LastPanType"]
    end

    @m_nPanType = nPanType
    if (hData["customer_id"] != nil) then
      @m_nPanType = nPanType = Cfate::PAN_NORMAL
    end
    session["pan_type"] = @m_nPanType

    @customer_id = session["customer_id"]
    if (@customer_id != nil && @customer_id > 0) then
      oCustomer = UserCustomer.check_option_user_id(@user_id).find(@customer_id)
      if (oCustomer != nil) then
        hUserInfo[Cfate::WYear] = oCustomer.wyear
        hUserInfo[Cfate::WMonth] = oCustomer.wmonth
        hUserInfo[Cfate::WDate] = oCustomer.wday
        hUserInfo[Cfate::WHour] = oCustomer.hour
        hUserInfo[Cfate::WMinute] = oCustomer.minute
        hUserInfo[Cfate::Sex] = oCustomer.sex == 1 ? false : true
        hUserInfo[Cfate::Name] = oCustomer.name
        hUserInfo[Eightword::U_BigWinStart] = oCustomer.hAll[Eightword::U_BigWinStart]
        hUserInfo[Eightword::U_UseGodFive] = oCustomer.hAll[Eightword::U_UseGodFive]
        @customer_user_id = oCustomer.user_id
      end
    end

    if ((hData != nil) && (hData["pan"] == "free" || hData["wyear"] != nil)) then
      hUserInfo[Cfate::WYear] = hData["wyear"]
      hUserInfo[Cfate::WMonth] = hData["wmonth"]
      hUserInfo[Cfate::WDate] = hData["wday"]
      hUserInfo[Cfate::WHour] = hData["hour"]
      hUserInfo[Cfate::WMinute] = hData["minute"]
      hUserInfo[Cfate::Sex] = hData["sex"].to_i == 1 ? false : true
      hUserInfo[Cfate::Name] = hData["name"]
    end

    if (@customer_id != nil) then
      api_set_user_customer_updated_at(@customer_id)
    end
    return hUserInfo
  end
  def set_pan_user_def_data(hData)
    # 處理流盤時間
    if (hData["USE_CUR_TIME"] != nil || ((session["user_def_data"] == nil) && (hData["UD_EYear"] == nil))) then
      hUserDefData = get_cur_date_info()
    else
      #若有傳入,以傳入為主
      if (hData["UD_EYear"] == nil) then
        hUserDefData = session["user_def_data"]
      else
        hUserDefData = Hash.new
        hUserDefData[Cfate::EYear] = hData["UD_EYear"].to_i
        hUserDefData[Cfate::EMonth] = hData["UD_EMonth"].to_i
        hUserDefData[Cfate::EDate] = hData["UD_EDate"].to_i
        hUserDefData[Cfate::LeapMonth] = (hData["UD_ELeap"].to_i == 0) ? false : true
        hUserDefData[Cfate::WHour] = hData["UD_WHour"].to_i
        hUserDefData[Cfate::WMinute] = hData["UD_WMin"].to_i
      end
    end

    hUserDefData[Cfate::SkyEarth] = hData["UD_SkyEarth"] == nil ? findSkyEarth(hUserDefData[Cfate::WHour]) : hData["UD_SkyEarth"]
    @nLeapMonth = hUserDefData[Cfate::LeapMonth] ? 1 : 0

    session["user_def_data"] = hUserDefData
#puts("session["user_def_data"]#{session["user_def_data"][Cfate::EYear]}")
    return hUserDefData
  end
  def findSkyEarth(nHour)
    # if (@m_nPanType == Cfate::PAN_NORMAL) then
    #   return nil
    # elsif (@m_nPanType == Cfate::PAN_FLOWTIME) then
    #   return (nHour % 2) == 1 ? Sky::SKY : Earth::EARTH
    # else
    #   return Sky::SKY
    # end
    return nil
  end

  def get_cur_date_info()
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
    hUserDefData = Hash.new
    hUserDefData[Cfate::EYear] = nEYear
    hUserDefData[Cfate::EMonth] = nEMonth
    hUserDefData[Cfate::EDate] = nEDate
    hUserDefData[Cfate::LeapMonth] = bLeapMonth
    hUserDefData[Cfate::WHour] = nWHour
    hUserDefData[Cfate::WMinute] = nWMin
    return hUserDefData
  end
  def pan_pan_info()
    upi_paninfo()
  end
  def upi_paninfo()
    # @h_Upi_MainStars = @oEightword.cp_upi_p_getByMainStars(@m_nPanType)
  end
  def pan_xdate_info()
    # 萬年曆
    makeXdate_data(@oEightword)
  end


  public
  def panprint
    # 精實創業，用來分析使用狀況
    # Pm.saveTestDb("#{params[:controller]}-#{params[:action]}-#{@oUserAp.user_id}-#{@oUserAp.product_type}",params.to_json)

    pan_prepare_data(params)
    @oEightword = Eightword.new
    @oEightword.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll,@ApFunc)

    # 排盤資料
    pan_pan_info()

    @edit_customer_url = ""
    @select_customer_url = ""
    @url_back = ""

    @aUrlKey = []
    @aUrlValue = []

    @usercustomer = UserCustomer.find_by_id(session["customer_id"])

    if (@customer_id == nil) then
      redirect_to ifate_customer_index_path, :ap_name => @ap_name
    else
      render :layout => 'eightword_pan_print'
    end
  end

  def panparedit
    if (params["PanPar"] == nil) then
      @nPanPar = Eightword::PanPar_Type
    else
      @nPanPar = params["PanPar"].to_i
    end

    render :layout => 'ifate'
  end

  def panparupdate
    hPar = params["submit_data"]

    if (hPar["PanPar"] == nil) then
      nPanPar = Eightword::PanPar_Type
    else
      nPanPar = hPar["PanPar"].to_i
    end

    if (hPar["PanParReset"] == "1") then
      hParAll = Eightword.pan_par_init_each(nPanPar,@ParAll)
    else
      hParAll = Eightword.pan_par_assign_each(nPanPar,@ParAll,hPar)
    end
    g_panPar2Db(hParAll,@user_id,"eightword")

    redirect_to :action => "panparedit" , :PanPar => nPanPar, :action_result => Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
  end

  def pansimple
  end

  def pansimpleinput
    # if (session["user_id"] == nil) then
      # redirect_to free_path()
    # else
    @user_id = current_user.id if logged_in?
    @ap_name = g_user_ap_name("eightword",@user_id)
      if (session["user_id"] != nil) then
        authenticate_user!
      end
    authenticate_without_signin(nil)
      @m_nPanType = Cfate::PAN_NORMAL
      # @par_YearDisplay = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]
      @StarYearSelect = 1901
      @StopYearSelect = 2099
      t = Xdate.GetNow()
      @TodayYear = t.year
      newUserCustomerNow

      @free_ap_name = "eightword"
    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?

      render :layout => 'star_pan_simple' #'customer'
    # end
  end

  def xml_gen
  end

  public
  def management
    @controller_name = "eightword"

    if (@ApFunc[Cfate::FUNC_SET_PARS]) then
      redirect_to :action => "panparedit" , :PanPar => Eightword::PanPar_Type
    else
      render :layout => 'ifate'
    end
  end

  protected
  def makeXdate_data(oEightword)
    hAppData = Hash.new

    hAppData["Ui_EY"] = oEightword.uig_EY()
    hAppData["Ui_SY"] = oEightword.uig_SY()
    hAppData["nSky"] = oEightword.gNormal_GetSky()
    hAppData["nEarth"] = oEightword.gNormal_GetEarth()
    hAppData["CurEYear"] = oEightword.cdg_EY()
    hAppData["CurEMonth"] = oEightword.cdg_EM()
    hAppData["CurEDate"] = oEightword.cdg_ED()
    hAppData["CurELeap"] = oEightword.cdg_EL()
    hAppData["CurETime"] = oEightword.cdg_ET()
    hAppData["CurEMin"] = oEightword.cdg_EMI()
    hAppData["CurWHour"] = oEightword.cdg_WH()
    hAppData["CurWMin"] = oEightword.cdg_WMI()
    hAppData["FlowEYear"] = oEightword.fdg_EY()
    hAppData["FlowWYear"] = oEightword.fdg_WY()
    hAppData["FlowEMonth"] = oEightword.fdg_EM()
    hAppData["FlowWMonth"] = oEightword.fdg_WM()
    hAppData["FlowEDate"] = oEightword.fdg_ED()
    hAppData["FlowWDate"] = oEightword.fdg_WD()
    hAppData["FlowELeap"] = oEightword.fdg_EL()
    hAppData["FlowETime"] = oEightword.fdg_ET()
    hAppData["FlowWHour"] = oEightword.fdg_WH()
    hAppData["FlowEMin"] = oEightword.fdg_EMI()
    hAppData["FlowWMin"] = oEightword.fdg_WMI()

    hAppData["ddg_EY"] = oEightword.ddg_EY()
    hAppData["LargeSan"] = oEightword.g_getLargeSan()
    hAppData["Large"] = oEightword.cp_GetLarge()

    hParAll = @ParAll.clone
    hParAll[Cfate::PAR_PAN_YEAR_DISPLAY] = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]
    hParAll[Cfate::PAR_FIRST_TIME_TYPE] = @ParAll[Eightword::PAR_FIRST_TIME_TYPE]
    hParAll[Cfate::PAR_FIRST_SEGMENT] = @ParAll[Eightword::PAR_FIRST_SEGMENT]

    hApFunc = @ApFunc.clone
    hApFunc[Cfate::LAST_XDATE_PAN_TYPE] = @ApFunc["n_LastXdatePanType"]

    @hXdate = Xdate.MakeXdateInfo_Eightword(hAppData,@m_nPanType,hApFunc,hParAll)
  end

  # 個人事件
  def pan_prepare_personalevent_data()
    tNow = time_now_local()
    oUserCustomer = UserCustomer.find_by_id(session["customer_id"])
    if (oUserCustomer == nil) then
      @starYearSelect = 1901
      @user_create_date = ""
    else
      @starYearSelect = oUserCustomer.wyear
      t = oUserCustomer.created_at
      @user_create_date = "#{t.year}/#{t.month}/#{t.day}#{Pm.t("IDS_S_CREATED")}"
    end
      if (oUserCustomer == nil || oUserCustomer.wyear <= tNow.year) then
        @stopYearSelect = tNow.year
      else
        @stopYearSelect = oUserCustomer.wyear + 10
      end

    @pe_title_data = Pm.GetStrWithQuote("ifate_pe_title")
    # h.invert
    @pe_mains = Cfate.pan_par_dbfield2hash(Pm.GetPeStr("pe_mains"))
    @pe_main_key_default = @pe_mains.values[0]

    @pe_subs = Cfate.pan_par_dbfield2hash(Pm.GetPeStr(@pe_main_key_default))
    @pe_sub_key_default = @pe_subs.values[0]
    @pe_time = tNow
    @pe_year = @pe_time.year
    @pe_month = Xdate::DATE_NIL
    @pe_day = Xdate::DATE_NIL
    @pe_hour = Xdate::DATE_NIL
    @pe_min = Xdate::DATE_NIL

    @personal_events = PersonalEvent.available.check_option_user_id(@user_id).check_user_customer_id(session["customer_id"]).check_main_key_pe().last(3).reverse
  end

  public
  # b_PAN_NORMAL,1,b_PAN_TENYEAR,0,b_PAN_FLOWYEAR,0,b_PAN_FLOWMONTH,0,b_PAN_FLOWDATE,0,b_PAN_FLOWTIME,0,b_PAN_FLOWMIN,0,n_LastPanType,0,n_LastXdatePanType,0,
  # b_func_five_wang_display,1,b_func_par_display,0,b_func_4c_sky_mm_event,0,n_func_4c_earth_mm_event,5,b_func_fw_sky_mm_event,0,n_func_fw_earth_mm_event,0
  def free
    @user_id = current_user.id if logged_in?
      @ap_name = g_user_ap_name("eightword",@user_id)
      @product_type = "free"
      authenticate_without_signin(nil)
      session["customer_id"] = nil

      # I18n.locale = extract_locale_from_tld || Cfate::DEFAULT_LANG
      sLang = my_current_lang()

      cust_name = params["name"]
      cust_sex = params["sex"]

      hDate = Xdate.date_input_prepare(params)
      pan_prepare_data(hDate)

      @m_nPanType = Cfate::PAN_NORMAL
      @oEightword = Eightword.new
      @ApFunc[Eightword::FUNC_PAR_DISPLAY] = true
      @oEightword.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll,@ApFunc)

      # 排盤資料
      pan_pan_info()
      pan_xdate_info()

      @edit_customer_url = "/free/eightword"
      @select_customer_url = "/free/eightword"
      if (hDate['calType'] == Xdate::CT_SOLAR) then
        @url_back = "ifate/eightword/free&calType=#{hDate['calType']}&year=#{hDate['wyear']}&month=#{hDate['wmonth']}&day=#{hDate['wday']}&hour=#{hDate['hour']}&minute=#{hDate['minute']}&sex=#{cust_sex}&name=#{cust_name}"
      else
        @url_back = "ifate/eightword/free&calType=#{hDate['calType']}&year=#{hDate['eyear']}&month=#{Xdate.makeMonthValue(hDate['calType'],hDate['eleap'],hDate['emonth'])}&day=#{hDate['eday']}&hour=#{hDate['hour']}&minute=#{hDate['minute']}&sex=#{cust_sex}&name=#{cust_name}"
      end

      @customer_id = 5
      @customer_user_id = 5

      @aUrlKey = ["pan","name","sex","calType","year","month","day","hour","minute","eleap"]
      if (hDate['calType'] == Xdate::CT_SOLAR) then
          @aUrlValue = [hDate["pan"],hDate["name"],hDate["sex"],hDate["calType"],hDate["wyear"],hDate["wmonth"],hDate["wday"],hDate["hour"],hDate["minute"],false]
      else
          @aUrlValue = [hDate["pan"],hDate["name"],hDate["sex"],hDate["calType"],hDate["eyear"],hDate["emonth"],hDate["eday"],hDate["hour"],hDate["minute"],hDate['eleap']]
      end
      @customer_id = 0
    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?
      
      render :layout => 'eightword_pan_free'
  end

  public
  def panparcopy
    par_set_name_key = "eightword_par_name"
    oIfatePar = IfatePar.check_userid(3).check_apname(par_set_name_key).last
    if (oIfatePar == nil) then
      @hName = {}
      @default_name = ""
    else
      hPar = oIfatePar.hPars
      names = hPar["names"]
      @hName = Hash[names.map {|x| [x,x]}]
      @default_name = params["par_set_name"]
    end
    render :layout => 'ifate'
  end
  def panparcopy_action()
    hPars = params["panparcopy_action"]
    if (hPars == nil) then
      hPars = params
    end
    email = hPars["par_copy_email"]
    oUser = User.find_by_email(email)
    if (oUser == nil) then
      redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
    else
      oIfatePar = IfatePar.check_userid_eightword(oUser.id).first
      if (oIfatePar == nil) then
        redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
      else
        oIfateParMe = IfatePar.check_userid_eightword(current_user.id).first
        if (oIfateParMe == nil) then
          oIfateParMe = IfatePar.new
          oIfateParMe.user_id = current_user.id
          oIfateParMe.ap_name = "eightword"
        end
        hUserType = oIfatePar.hPars
        hUserType = Eightword.pan_par_assign_hallnumber(hUserType,nil)
        hUserType = Eightword.pan_par_assign_print(hUserType,nil)
        oIfateParMe.hPars = hUserType
        oIfateParMe.save!

        redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
      end
    end
  end
  def panparsetname_action()
    par_set_name_key = "eightword_par_name"
    hPars = params["panparsetname_action"]
    if (hPars == nil) then
      hPars = params
    end
    name = hPars["par_set_name"]
    oIfatePar = IfatePar.check_userid(3).check_apname(par_set_name_key).last
    if (oIfatePar == nil) then
      # 第一個人設定
      oIfatePar = IfatePar.new
      oIfatePar.user_id = 3
      oIfatePar.ap_name = par_set_name_key
      hPar = {"names" => [name], "user_ids" => [@user_id]}
      oIfatePar.hPars = hPar
      oIfatePar.save!
      redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
    else
      hPar = oIfatePar.hPars
      names = hPar["names"]
      if (names.include?(name)) then
        # 代號已被設定
        redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
      else
        user_ids = hPar["user_ids"]
        idx = user_ids.index(@user_id)
        if (idx == nil) then
          # 這個人第一次設定
          names.push(name)
          user_ids.push(@user_id)
        else
          # 這個人更改設定
          names[idx] = name
        end
        hPar["names"] = names
        hPar["user_ids"] = user_ids
        oIfatePar.hPars = hPar
        oIfatePar.save!

        redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
      end
    end
  end
  def panparcopyname_action()
    par_set_name_key = "eightword_par_name"
    hPars = params["panparcopyname_action"]
    if (hPars == nil) then
      hPars = params
    end
    name = hPars["par_set_name"]
    oIfatePar = IfatePar.check_userid(3).check_apname(par_set_name_key).last
    if (oIfatePar == nil) then
      redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
    else
      hPar = oIfatePar.hPars
      names = hPar["names"]
      idx = names.index(name)
      # puts "names : #{names}"
      # puts "name : #{name}"
      # puts "hPars : #{hPars}"
      if (idx == nil) then
        redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
      else
        user_ids = hPar["user_ids"]
        from_user_id = user_ids[idx]
        oIfateParFrom = IfatePar.check_userid(from_user_id).check_apname(@ap_name).last
        if (oIfateParFrom == nil) then
          redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
        else
          oIfateParMe = IfatePar.check_userid(@user_id).check_apname(@ap_name).last
          if (oIfateParMe == nil) then
            oIfateParMe = IfatePar.new
            oIfateParMe.user_id = @user_id
            oIfateParMe.ap_name = @ap_name
          end
          hUserType = oIfateParFrom.hPars
          h = {"s_hn_hall_number1":"","s_adv_hall_number1":"","s_adv_hall_number2":"","s_adv_hall_number3":"","b_par_print_time_page":false,"n_par_print_time_page_type":0,"b_par_print_time_page_three_1":false,"b_par_print_time_page_three_2":false,"b_par_print_time_page_three_3":false,"s_par_print_pan_header":"","s_par_print_set_footer":""}
          hUserType.merge!(h) { |key, v1, v2| v2 }
          oIfateParMe.hPars = hUserType
          oIfateParMe.save!

          redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
        end
      end
    end
  end

  public
  def show
    @ap_name = "eightword"
    @product_type = "show"
    authenticate_without_signin(nil)
    session["customer_id"] = getFullFuncShow_CustomerId()
    @ParAll["full_show"] = true
    @ApFunc[Eightword::FUNC_PAR_DISPLAY] = true
    pan

    @edit_customer_url = ""
    @url_back = ""

    @aUrlKey = []
    @aUrlValue = []
    @customer_user_id = 0
    # render :layout => "eightword_pan"
  end
  protected
  def authenticate_without_signin(user_id)
    if (@product_type == "show") then
      @oUserAp = g_getShowUserAp(user_id,@ap_name)
    elsif (@product_type == "pan2")
      @oUserAp = g_getUserCurrentAp(user_id,@ap_name)
    else
      @oUserAp = g_getGuestUserAp(user_id,@ap_name)
    end
    @showAdvertise = true
    @firstShowAdvertise = @oUserAp.firstShowAdvertise && @showAdvertise ? "pan_information_block" : "pan_xdate_block"
    @firstShowAdvertise = session["eightword_firstShowAdvertise"] if session["eightword_firstShowAdvertise"] != nil

    @ParAll = Eightword.get_pan_pars(user_id,g_can_use_pars(@oUserAp))
    @canUsePan = g_canUsePan(session["customer_id"])

    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @hHallNumberAdver = find_last_hallnumber_adver(@ap_name,@oUserAp,@ApFunc)
    @hCanDeal = canDealCheck(user_id,@oUserAp,@ap_name)
    @main_products = g_getMainProducts(@ap_name)

    @canAddPe = session["customer_id"] != nil

    @sign_in = false
    @sign_in = true if (user_id != nil)  #if logged_in?
  end

  public
  def edit_customer
    pan_prepare_data(params)
    @oEightword = Eightword.new
    @oEightword.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll,@ApFunc)
    # 排盤資料
    pan_pan_info()

    render :layout => "ifate"
  end

  def update_customer
    hPar = params["update_customer"]

    pan_prepare_data(params)
    @oEightword = Eightword.new
    @oEightword.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll,@ApFunc)
    # 排盤資料
    pan_pan_info()

    if (session["customer_id"] == hPar["customer_id"].to_i) then
      hCust = Hash.new
      if (hPar["update_customer_reset"] == "1") then
        hCust[Eightword::U_BigWinStart] = @oEightword.cp_fp_LargeWin0_Start_YearOld_Org()
        aCust = Array.new(5,0)
        (0..4).each do |nUseGod|
          aCust[nUseGod] = @oEightword.cp_mp_ui_GetByUseGodValueOrg(nUseGod)
        end
        hCust[Eightword::U_UseGodFive] = aCust.clone
      else
        hCust[Eightword::U_BigWinStart] = hPar["bigWinStart"]
        aCust = Array.new(5,0)
        (0..4).each do |nUseGod|
          aCust[nUseGod] = hPar["ug_five_#{nUseGod}"].to_i
        end
        hCust[Eightword::U_UseGodFive] = aCust.clone
      end

      uc = UserCustomer.find(session["customer_id"])
      hAllOrg = uc.hAll
      hAllOrg.merge!(hCust) {|key, v1, v2| v2}
      uc.hAll = hAllOrg
      uc.save!
    end

    redirect_to ifate_eightword_pan_path
  end

  public
  def pan2share
    @ap_name = "eightword"
    @product_type = "pan2"

    pan_prepare_data(params)
    # 要先判斷SkyEarth，否則存到資料庫中，再找出來會受當時的時間影響
    @oEightword = Eightword.new
    @oEightword.SetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll,@ApFunc)
    @UserDefData[Cfate::SkyEarth] = @oEightword.ddg_SkyEarth()

    (Cfate::PAN_NORMAL..Cfate::PAN_FLOWMIN).each do |nPanType|
      @ApFunc[Cfate.gGetPanTypeHashKey(nPanType)] = false
    end
    @ApFunc[Cfate.gGetPanTypeHashKey(@m_nPanType)] = true

    h = Hash.new
    h["ParAll"] = @ParAll
    h["ApFunc"] = @ApFunc
    h["customer"] = @UserInfo

    if (@customer_id == nil) then
      @url = "empty"
      @url_iframe = "empty"
    else
      # 需判斷重覆的狀況
      a = ApShare.check_userid(@user_id).check_customerid(@customer_id).check_apname(@ap_name).check_pantype(@m_nPanType).check_actionname("pan2").check_ap_data(h.to_json)
      if (@m_nPanType == Cfate::PAN_NORMAL) then
        ap_share = a.last
      else
        a = a.check_eyear(@UserDefData[Cfate::EYear]).check_sky_earth(@UserDefData[Cfate::SkyEarth])
        if (@m_nPanType <= Cfate::PAN_FLOWYEAR) then
          ap_share = a.last
        else
          a = a.check_emonth(@UserDefData[Cfate::LeapMonth],@UserDefData[Cfate::EMonth])
          if (@m_nPanType == Cfate::PAN_FLOWMONTH) then
            ap_share = a.last
          else
            a = a.check_eday(@UserDefData[Cfate::EDate])
            if (@m_nPanType == Cfate::PAN_FLOWDATE) then
              ap_share = a.last
            else
              a = a.check_hour(@UserDefData[Cfate::WHour])
              if (@m_nPanType == Cfate::PAN_FLOWTIME) then
                ap_share = a.last
              else
                # Cfate::PAN_FLOWMIN
                ap_share = a.check_minute(@UserDefData[Cfate::WMinute]).last
              end
            end
          end
        end
      end

      if (ap_share == nil) then
        ap_share = new_ap_share(h)
      end
      # @b = ap_share.hAll["ParAll"]

      # @h = ap_share.hAll["UserDefData"]

      sHost = Pm.getSystemValue("MEEN_URL")
      if (params["locale"] == nil || params["locale"] == "") then
        @url = "#{sHost}/#{params["controller"]}/pan2?k=#{ap_share.share_key}"
        @url_iframe = "<iframe width='560' height='315' src='#{@url}' frameborder='0' scrolling='yes'></iframe>"
      else
        @url = "#{sHost}/#{params["locale"]}/#{params["controller"]}/pan2?k=#{ap_share.share_key}"
        @url_iframe = "<iframe width='560' height='315' src='#{@url}' frameborder='0' scrolling='yes'></iframe>"
      end
    end

    respond_to do |format|
      format.html { redirect_to root_path } #for my controller, i wanted it to be JS only
      format.js
    end
    render :partial => "ifate/eightword/pan2share", :layout => false, :locals => {}
    # render :partial => "ifate/eightword/pan2share" , :locals => {}
  end

  protected
  def new_ap_share(h)
    ap_share = ApShare.new
    ap_share.user_id = @user_id
    ap_share.customer_id = @customer_id
    ap_share.ap_name = @ap_name
    ap_share.action_name = "pan2"
    ap_share.pantype = @m_nPanType
    ap_share.share_key = SecureRandom.uuid
    ap_share.eyear = @UserDefData[Cfate::EYear]
    ap_share.eleap = @UserDefData[Cfate::LeapMonth]
    ap_share.emonth = @UserDefData[Cfate::EMonth]
    ap_share.eday = @UserDefData[Cfate::EDate]
    ap_share.hour = @UserDefData[Cfate::WHour]
    ap_share.minute = @UserDefData[Cfate::WMinute]
    ap_share.sky_earth = @UserDefData[Cfate::SkyEarth]
    ap_share.hAll = h
    ap_share.count = 0
    ap_share.status = 0
    ap_share.save!

    return ap_share
  end

  public
  def pan2
    @ap_name = "eightword"
    @product_type = "pan2"
    hData = g_getInfoForPan2(params["k"],@ap_name,"pan2")
    # hData = g_getInfoForPan2("123333445566666666")

    if (hData == nil) then
      redirect_to root_path, :ap_name => @ap_name
    else
      @user_id = hData["user_id"]
      authenticate_without_signin(@user_id)

      pan_prepare_data(hData)

      @ApFunc = hData["ApFunc"]
      @ParAll = hData["ParAll"]

      # 精實創業，用來分析使用狀況
      # Pm.saveTestDb("#{params[:controller]}-#{params[:action]}-#{@oUserAp.user_id}-#{@oUserAp.product_type}",params.to_json)

      # no pan change event
      @bPanChange = false
      @bDeclare = true
      @nClassPost = "_no_hover"
      @UserInfo[Cfate::Name] = "OOO"

      @oEightword = Eightword.new
      @oEightword.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll,@ApFunc)
      # 排盤資料
      pan_pan_info()
      # no user name
      # @PI_Name[1] = "OOO"

      # 萬年曆
      pan_xdate_info()

      # 個人事件資料
      pan_prepare_personalevent_data()

      # 產品購買資訊
      pan_userap_info()

      @edit_customer_url = "/ifate/customer/edit"
      @select_customer_url = "/ifate/customer/index"
      @url_back = "/ifate/eightword/pan"

      @aUrlKey = []
      @aUrlValue = []

      g_userap_addone_usecount(params["action"],@m_nPanType,@oUserAp)

      render :layout => 'eightword_pan2'
    end
  end

  def zeri_pan_show_ajax
    if (params["ajax_data_key"] != nil) then
      hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    else
      hAjaxData = params
    end
    @target_name = hAjaxData["target_name"]

    answer = Zeri.check_in_answers(@ap_name+@target_name,@user_id,hAjaxData["timestamp"])
    if (answer != nil) then
      zeri_pan_data(answer)
      # zeri_pan_data(hAjaxData)
      render :partial => "ifate/eightword/zeri_pan_show_ajax"
    else
      render :nothing => true
    end
  end
  def zeri_pan_answer
    @chaxuncanshuchucunmingcheng = params["chaxuncanshuchucunmingcheng"]
    @target_name = params["target_name"]
    @answers = Zeri.find_answers(@ap_name+@target_name,@user_id)
    if (@answers == []) then
      render :text => Zeri.get_zeri_str("meizhaodao")
    else
      hPars = Hash.new
      hPars["ap_name"] = "eightword"
      hPars["timestamp"] = @answers[0]["timestamp"]
      hPars["sex"] = @answers[0]["sex"]
      hPars["calType"] = @answers[0]["calType"]
      zeri_pan_data(hPars)

      render :layout => 'ifate'
      # render :layout => 'eightword_zeri_pan_answer'
    end
  end
  protected
  def zeri_pan_data(hPars)
    hData = Xdate.date_input_prepare(hPars)
    @customer_id = 0
    pan_prepare_data(hData)

    @m_nPanType = Cfate::PAN_NORMAL
    @bPanChange = true
    @bDeclare = false
    @nClassPost = ""

    @oEightword = Eightword.new
    @oEightword.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll,@ApFunc)
    # 排盤資料
    pan_pan_info()

    # 萬年曆
    pan_xdate_info()

    # 個人事件資料
    pan_prepare_personalevent_data()

    # 產品購買資訊
    pan_userap_info()

    @edit_customer_url = "/ifate/customer/edit"
    @select_customer_url = "/ifate/customer/index"
    @url_back = "/ifate/eightword/pan"

    @aUrlKey = []
    @aUrlValue = []

    g_userap_addone_usecount(params["action"],@m_nPanType,@oUserAp)

    @sMy_url = ""
  end

  public
  def fuwu_shuoming
    render :layout => 'ifate'
  end

  protected
  def newUserCustomerNow()
    @user_customer = UserCustomer.new
    new_for_now
  end

  def new_for_now()
    t = time_now_local()
    @user_customer.calType = Xdate::CT_SOLAR
    @user_customer.wyear = t.year
    @user_customer.wmonth = t.month
    @user_customer.wday = t.day
    @user_customer.hour = t.hour
    @user_customer.minute = t.min
    @user_customer.name = ""
    @user_customer.sex = 1
    @calType = Xdate::CT_SOLAR
  end

end
