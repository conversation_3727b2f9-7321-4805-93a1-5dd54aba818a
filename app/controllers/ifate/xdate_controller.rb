require("Pm.rb")
require("Cfate.rb")
require("Xdate.rb")

class Ifate::XdateController < ApplicationController
	public
  	def free
	  	@ap_name = params["ap_name"]
		# if (session["user_id"] != nil) then
		# 	authenticate_user!
		# 	authenticate
		# end
		# I18n.locale = extract_locale_from_tld || Cfate::DEFAULT_LANG
		sLang = my_current_lang()

		cust_name = params["name"]
		cust_sex = params["sex"]

		hDate = Xdate.date_input_prepare(params)
		#pan_prepare_data()
		@UserInfo = Hash.new
		@UserInfo[Cfate::WYear] = hDate["wyear"]
		@UserInfo[Cfate::WMonth] = hDate["wmonth"]
		@UserInfo[Cfate::WDate] = hDate["wday"]
		@UserInfo[Cfate::WHour] = hDate["hour"]
		@UserInfo[Cfate::Sex] = cust_sex == "1" ? false : true
		@UserInfo[Cfate::Name] = cust_name

		nEYear,nEMonth,nEDate,bLeapMonth,nWHour = hDate["eyear"],hDate["emonth"],hDate["eday"],hDate["eleap"],hDate["hour"]
		nWYear,nWMonth,nWDate,nWHour,nWMinute = hDate["wyear"],hDate["wmonth"],hDate["wday"],hDate["hour"],hDate["minute"]
		hDate["SunDate"] = makeSunDateStr(hDate)
		hDate["LunarDate"] = makeLunarDateStr(hDate)
		hDate["LunarGanZhi"] = makeLunarGanZhiStr(hDate)
		hDate["SegGanZhi"] = makeSegmentGanZhiStr(hDate)

		hDate["week"] = Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)
		hDate["chinaTime"] = Earth.GetIndexName(Xdate.Hour2ETime(nWHour))
		hDate["segment"] = makeSegmentStr(hDate)

		@date = hDate

	    @edit_customer_url = "/free/xdate"
	    @select_customer_url = "/free/xdate"
	    if (hDate['calType'] == Xdate::CT_SOLAR) then
	      @url_back = "ifate/xdate/free&calType=#{hDate['calType']}&year=#{hDate['wyear']}&month=#{hDate['wmonth']}&day=#{hDate['wday']}&hour=#{hDate['hour']}&minute=#{hDate['minute']}&sex=#{cust_sex}&name=#{cust_name}"
	    else
	      @url_back = "ifate/xdate/free&calType=#{hDate['calType']}&year=#{hDate['eyear']}&month=#{Xdate.makeMonthValue(hDate['calType'],hDate['eleap'],hDate['emonth'])}&day=#{hDate['eday']}&hour=#{hDate['hour']}&minute=#{hDate['minute']}&sex=#{cust_sex}&name=#{cust_name}"
	    end

		render :layout => 'star_pan_simple'
  	end

  	protected
	def makeSunDateStr(hDate)
		nWYear,nWMonth,nWDate,nWHour,nWMinute = hDate["wyear"],hDate["wmonth"],hDate["wday"],hDate["hour"],hDate["minute"]
		sOut = Xdate.makeSunDateStr(nWYear,nWMonth,nWDate,nWHour,nWMinute,Cfate::PAN_DISPLAY_WEST)
		return sOut
	end

	def makeLunarDateStr(hDate)
		nEYear,nEMonth,nEDate,bLeapMonth,nWHour = hDate["eyear"],hDate["emonth"],hDate["eday"],hDate["eleap"],hDate["hour"]
		sOut = Xdate.makeLunarDateStr(nEYear,nEMonth,nEDate,bLeapMonth,nWHour,Cfate::PAN_DISPLAY_WEST)
		return sOut
	end

	def makeLunarGanZhiStr(hDate)
		nWYear,nWMonth,nWDate,nWHour,nWMinute = hDate["wyear"],hDate["wmonth"],hDate["wday"],hDate["hour"],hDate["minute"]
		sOut = Xdate.makeLunarGanZhiStr(nWYear,nWMonth,nWDate,nWHour,nWMinute,Xdate::FIRST_NEXT)
		return sOut
	end

	def makeSegmentGanZhiStr(hDate)
		nWYear,nWMonth,nWDate,nWHour,nWMinute = hDate["wyear"],hDate["wmonth"],hDate["wday"],hDate["hour"],hDate["minute"]
		sOut = Xdate.makeSegmentGanZhiStr(nWYear, nWMonth,nWDate, nWHour,nWMinute,false,nil,Xdate::SEGMENT_SPRING)
		return sOut
	end

	def makeSegmentStr(hDate)
		nWYear,nWMonth,nWDate,nWHour,nWMinute = hDate["wyear"],hDate["wmonth"],hDate["wday"],hDate["hour"],hDate["minute"]
		return Xdate.Get8WordsSeg_Str(nWYear, nWMonth,nWDate, nWHour,nWMinute,nil,Xdate::SEGMENT_SPRING)
	end
end
