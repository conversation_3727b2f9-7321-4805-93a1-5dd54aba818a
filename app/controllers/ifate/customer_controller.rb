require("Pm.rb")
require("SearchParser.rb")
require("Star.rb")
require("Xdate.rb")
require("Eightword.rb")
require("Health.rb")
require("Customerparser.rb")
require("Xuexi_Ziwei.rb")
require("Controller_Api.rb")

class Ifate::CustomerController < ApplicationController
  include Controller_Api

  before_action :authenticate_user!, :except => [:update_dates_no_sign_in,:health,:health_key]
  before_action :authenticate, :except => [:user_loging,:update_dates_no_sign_in,:health,:all_info_modify,:health_key]
  # before_action :customer_check, :except => [:pan]
  before_action :check_can_add_customer, :only => [:new,:createcustomerfull,:indexwithnew,:createcustomersimple]

  public
  def user_loging
    # 進來後要派到工具網或學習網
    ap_name = g_user_ap_name(params["ap_name"],current_user.id,nil)
    ap_namespace = g_ap_namespace(ap_name)

    if (["ifate"].include?(ap_namespace)) then
      redirect_to :controller => "ifate/customer", :action => "index", :ap_name => ap_name
    elsif (["xuexi"].include?(ap_namespace)) then
      redirect_to :controller => xuexi_ap_name_to_controller(ap_name), :action => "xuexi", :ap_name => ap_name
    end
  end

  def update_password
    raw, hashed = Devise.token_generator.generate(User, :reset_password_token)
    input_email = params["email"]
    user = User.find_by(email: input_email)

    if (user != nil) then
      user.reset_password_token = hashed
      user.reset_password_sent_at = Time.now.utc
      user.save!

      @out_url = Pm.getHostUrl("/account/password/edit?reset_password_token=#{raw}")
      @out_raw = raw
      @out_hashed = hashed
      @user = user
    end
  end
  protected
  def authenticate
    authenticate_customer("ifate")
  end
  def authenticate_customer(ap_namespace)
    #@user_name = current_user.name
    @user_name = current_user.email
    @user_level = ""
    @user_id = current_user.id
    if ((params["ap_name"] == nil) && (params["ajax_data_key"] != nil)) then
      hPar = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
      @ap_name = g_user_ap_name(hPar["ap_name"],@user_id,ap_namespace)
      @full_ap_name = hPar["ap_name"]
    else
      @ap_name = g_user_ap_name(params["ap_name"],@user_id,ap_namespace)
      @full_ap_name = params["ap_name"]
    end

    session["user_name"] = { :value => @user_name, :expires => 1.minute.from_now }

    session["userlevel"] = @user_level

    getUserCurrentAp(@user_id)
    # session["ap_name"] = @ap_name
    session["ap_version"] = @ap_version
    @ParAll = g_get_pan_pars(@ap_name,@user_id)
    star_version_check()
    @canAddPe = session["customer_id"] != nil

    @admin = current_user.roles? :admin
    @can_add_customer,@customer_max_count = g_can_add_customer(@user_id,@admin)
  end
  def check_can_add_customer
    if (!@can_add_customer) then
      redirect_to :controller => "ifate/customer" ,:action => "index"
    end
  end
  def getUserCurrentAp(user_id_session)
      user_id = user_id_session
      # if (params[:ap_name] == nil && params[:ordernumber] != nil) then
      #   orderme_id = params[:ordernumber].to_i
      #   oOrderme = Orderme.find_by_id(orderme_id)
      #   oUserAp = UserAp.find_by_id(oOrderme.old_userap_id)
      #   @ap_name = oUserAp.product_name
      #   # user_id = oUserAp.user_id
      # end
    session["customer_id"] = params["customer_id"] == nil ? session["customer_id"] : params["customer_id"].to_i
    if (session["customer_id"] == nil) then
      session["customer_id"] = g_getlastcustomer(user_id)
    end
    @oUserAp = g_getUserCurrentAp(user_id,@ap_name)
    if (@oUserAp.custDisplayCheck) then
      @panParType = IfatePar.check_userid_customer(@user_id).first
      if (@panParType == nil) then
        @hCustDisplay = Hash.new
      else
        @hCustDisplay = @panParType.hPars
      end
    else
      @hCustDisplay = Hash.new
    end
    @canUsePan = g_canUsePan(session["customer_id"])
  end

  def star_version_check()
    # 權限控管
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @hCanDeal = canDealCheck(@user_id,@oUserAp,@ap_name)
  end
  def find_search_customers(sSearchData,nLastId,nCount)
    user_id = @user_id
    session["customer_search_data"],@all_user_customer,@list_count = api_find_search_customers(@user_id,sSearchData,nLastId,nCount,@par_YearDisplay)
    return session["customer_search_data"]
  end

  def find_search_customers2(sSearchData,nLastId,nCount)
    user_id = @user_id
    session["customer_search_data"] = sSearchData
    sSearchDataRet = sSearchData

    if ((sSearchData == nil) || (sSearchData.length == 0)) then
#     @all_user_customer = UserCustomer.find(:all, :order => "id DESC", :limit=>nCount.abs.to_s)
      @all_user_customer = UserCustomer.check_user_id(user_id).order("id DESC")#.limit(nCount.abs)
    else
      sps,sSearchDataRet = SearchParser.ParseSearchData(sSearchData,@par_YearDisplay)

      # aDataAfterCheck = UserCustomer.check_user_id(user_id).check_year(nWYear).check_month(nWMonth).check_day(nWDate).check_hour(nWHour).check_sex(nSex).check_name(sName)
      aDataAfterCheck = UserCustomer.check_user_id(user_id)
      aDataAfterCheck = aDataAfterCheck.check_sps(sps)
      # if (nType == 1) then
      #     # aDataAfterCheck = aDataAfterCheck.check_year(nWYear).check_month(nWMonth).check_day(nWDate)
      #     aDataAfterCheck = aDataAfterCheck.check_year_month_day(nWYear,nWMonth,nWDate)
      # elsif (nType == 2) then
      #     # aDataAfterCheck = aDataAfterCheck.check_ymd_and(nWYear,nWMonth,nWDate)
      #     aDataAfterCheck = aDataAfterCheck.check_years_months_days(nWYear,nWMonth,nWDate)
      # else
      #     aDataAfterCheck = aDataAfterCheck.check_ymd_or(nWYear,nWMonth,nWDate)
      # end
      # aDataAfterCheck = aDataAfterCheck.check_hour(nWHour)

      # aDataAfterCheck = aDataAfterCheck.check_sex(nSex)
      # if (sName == nil) then
      #   sName = ""
      # end
      # aDataAfterCheck = aDataAfterCheck.check_name(sName)
      if (nCount > 0) then
        @all_user_customer = aDataAfterCheck.next(nLastId).reverse #.limit(nCount).reverse
      elsif (nCount == 0) then
        @all_user_customer = aDataAfterCheck.reverse #.limit(nCount).reverse
      else
        @all_user_customer = aDataAfterCheck.prev(nLastId)#.limit(nCount.abs)
      end
    end
    session["customer_search_data"] = sSearchDataRet
    @list_count = nCount.abs
    return sSearchDataRet
  end

  def customer_check
    if (session["customer_id"] == nil) then
      redirect_to :controller => "ifate/customer" ,:action => "index"
    end
  end

  public
  def pan
    redirect_to :controller => "ifate/#{@ap_name.downcase}" ,:action => "pan", :ap_name => @ap_name, :customer_id => session["customer_id"], :gua_lunduan => false
  end
  def panparedit
    redirect_to :controller => "ifate/#{@ap_name.downcase}" ,:action => "panparedit", :PanPar => params["PanPar"], :ap_name => @ap_name
  end

  def panscoreinput
    getIndexData(params)
    @ap_name = "star_score"

    render :layout => 'ifate'
  end

  def index
    # if (["wannianli","liuyaogua"].include?(@ap_name)) then
    #   index = ["wannianli","liuyaogua"].index(@ap_name)
    #   controller_name = ["ifate/wannianli","ifate/liuyaogua"][index]
    if (["liuyaogua"].include?(@ap_name)) then
      # index = ["liuyaogua"].index(@ap_name)
      # controller_name = ["ifate/liuyaogua"][index]
      # redirect_to :controller => controller_name, :action => "pan", :ap_name => @ap_name
      getIndexData(params)
      if (UserCustomer.check_user_id(@user_id).count > 0) then
        render :layout => 'ifate'
      else
        redirect_to :action => "new", :ap_name => @ap_name
      end
    elsif (["star","eightword"].include?(@ap_name)) then
      getIndexData(params)
      if (UserCustomer.check_user_id(@user_id).count > 0) then
        render :layout => 'ifate'
      else
        redirect_to :action => "new", :ap_name => @ap_name
      end
    else
      @ap_name = "star"
      redirect_to :controller => "ifate/customer", :action => "index", :ap_name => @ap_name
    end
  end

  def indexwithnew
  getIndexData(params)

  render :layout => 'ifate'
  end

  protected
  def getIndexData(hPar)
    @customer_search_data = hPar["customer_search_data"]
    if (@customer_search_data == nil) then
      @customer_search_data = ""
    end
    @par_YearDisplay = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]
    @StarYearSelect = 1901
    @StopYearSelect = 2099
    t = time_now_local()
    @TodayYear = t.year
  # nil[1]

    nLastId = 0
    if (hPar["lastId"] != nil) then
      nLastId = hPar["lastId"]
    end
    nCount = 100
    if (hPar["preOrNextCount"] != nil) then
      nCount = hPar["preOrNextCount"].to_i
    end

    @customer_search_data = find_search_customers(@customer_search_data,nLastId,nCount)

    newUserCustomerNow(hPar)

    @hcount = {}
    @hcount["male"] = UserCustomer.check_user_id(@user_id).check_sex(1).count
    @hcount["female"] = UserCustomer.check_user_id(@user_id).check_sex(0).count
    @hcount["total"] = UserCustomer.check_user_id(@user_id).count
  end

  public
  def show
    nCustId = params["CustId"]
    @user_customer = UserCustomer.check_user_id(@user_id).find(nCustId)
  end

  def new
    newUserCustomerNow(params)

    # @next_action = params["next_action"]

    render :layout => 'ifate'
  end

  def createcustomersimple
    hPar = params
    newUserCustomer(hPar)

    @user_customer.save
    redirect_to :action => "indexwithnew" , :customer_search_data => session["customer_search_data"], :ap_name => @ap_name
  end

  protected
  def newUserCustomerNow(hPar)
    newUserCustomer(hPar)
    new_for_now
  end

  def new_for_now()
  t = time_now_local()
  @user_customer.calType = Xdate::CT_SOLAR
  @user_customer.wyear = t.year
  @user_customer.wmonth = t.month
  @user_customer.wday = t.day
  @user_customer.hour = t.hour
  @user_customer.minute = t.min
  @user_customer.name = ""
  @user_customer.sex = 1
  @calType = Xdate::CT_SOLAR
  end


  public
  def createcustomerfull
    hPar = params
    newUserCustomer(hPar)

    if (@user_customer.save) then
      redirect_to :controller => "ifate/#{@ap_name.downcase}" ,:action => "pan" , :customer_search_data => session["customer_search_data"] , :customer_id => @user_customer.id, :ap_name => @ap_name
    else
      render_to :action => "new" , :customer_search_data => session["customer_search_data"]
    end
  end

  protected
  def newUserCustomer(hPar)
    hAll = getUserInfoHash(hPar)
    assignUserCustomer(hAll,nil)
  end

  def assignUserCustomer(hAll,uc_id)
    user_id = @user_id
    if (uc_id != nil) then
      u = UserCustomer.check_user_id(user_id).find(uc_id)
    else
      u = UserCustomer.new
    end

    if (u != nil) then
      u.user_id = user_id
      u.name = hAll["name"]
      u.sex = Cfate.HashOrDefault(hAll,"sex",0)
      u.calType = Cfate.HashOrDefault(hAll,"calType",Xdate::CT_SOLAR)
      u.wyear = Cfate.HashOrDefault(hAll,"wyear",Xdate::DATE_NIL)
      u.wmonth = Cfate.HashOrDefault(hAll,"wmonth",Xdate::DATE_NIL)
      u.wday = Cfate.HashOrDefault(hAll,"wday",Xdate::DATE_NIL)
      u.eyear = Cfate.HashOrDefault(hAll,"eyear",Xdate::DATE_NIL)
      u.eleap = Cfate.Bool2Int(hAll["eleap"])
      u.emonth = Cfate.HashOrDefault(hAll,"emonth",Xdate::DATE_NIL)
      u.eday = Cfate.HashOrDefault(hAll,"eday",Xdate::DATE_NIL)
      u.hour = Cfate.HashOrDefault(hAll,"hour",Xdate::DATE_NIL)
      u.minute = Cfate.HashOrDefault(hAll,"minute",Xdate::DATE_NIL)
      u.blood = hAll["blood"]
      u.phone = hAll["phone1"]
      u.address = hAll["address1"]
      u.email = hAll["email1"]
      u.userclass = hAll["userclass"]
      hAllOrg = u.hAll
      hAllOrg.merge!(hAll) {|key, v1, v2| v2}
      u.hAll = hAllOrg
      # u.all_info = Cfate.pan_par_hash2db(hAll)
    end
    @user_customer = u
  end

  def getUserInfoHash(hPar)
    hDate = Xdate.date_input_prepare(hPar)

    hAll = Hash.new
    hAll.merge!(hDate)
    hAll["name"] = hPar["name"]
    hAll["sex"] = hPar["sex"]
    hAll["address1"] = hPar["address"]
    hAll["address2"] = hPar["address2"]
    hAll["phone1"] = hPar["phone"]
    hAll["phone2"] = hPar["phone2"]
    hAll["blood"] = hPar["blood"]
    hAll["addressborn"] = hPar["addressborn"]
    hAll["longitude"] = hPar["longitude"]
    hAll["latitude"] = hPar["latitude"]
    hAll["stature"] = hPar["stature"]
    hAll["constellation"] = hPar["constellation"]
    hAll["educationbackground"] = hPar["educationbackground"]
    hAll["email1"] = hPar["email"]
    hAll["email2"] = hPar["email2"]
    hAll["webaddress1"] = hPar["webaddress"]
    hAll["webaddress2"] = hPar["webaddress2"]
    hAll["introducer"] = hPar["introducer"]
    hAll["userclass"] = hPar["userclass"]
    hAll["others"] = hPar["others"]
    hAll["end"] = "end"

    return hAll
  end

  public
  def edit
    nCustId = params["CustId"]
    @UrlBack = params["UrlBack"]
    # if (@UrlBack != nil) then
    #   @UrlBack += "?customer_id=" + nCustId
    # end

    @user_customer = UserCustomer.check_user_id(@user_id).find(nCustId)
    @BloodSelected = Array.new(4,'')
    if (@user_customer.blood != nil) then
      @BloodSelected[@user_customer.blood] = "selected"
    end
    render :layout => 'ifate'
  end

  def update
    urlBack = params["UrlBack"]
    hAll = getUserInfoHash(params)

    uc_id = params["customer_id"]
    assignUserCustomer(hAll,uc_id)

    if (@user_customer != nil) then
      @user_customer.save!

      if (urlBack == nil || urlBack.length == 0) then
        redirect_to :action => "index" , :customer_search_data => session["customer_search_data"], :ap_name => @ap_name
      else
        # h = {}
        # h[:host] = urlBack
        # h[:customer_search_data] = session[:customer_search_data]
        # h[:ap_name] = @ap_name
        # redirect_to h.merge(:only_path => true)
        # redirect_to params.merge(:host => 'myhost.com')
        redirect_to URI.parse(urlBack).path  , :customer_search_data => session["customer_search_data"], :ap_name => @ap_name
        # redirect_to urlBack , :customer_search_data => session["customer_search_data"], :ap_name => @ap_name
      end
    else
      redirect_to "/users/sign_out"
    end
  end

  def delete
    nCustId = params["CustId"]
    @user_customer = UserCustomer.check_user_id(@user_id).find(nCustId)
    render :layout => 'ifate'
  end

  def destroy
    customer = params["user_customer"]
    if (customer != nil) then
      nCustId = customer["id"]
      uc = UserCustomer.check_user_id(@user_id).find(nCustId)
      if (uc != nil) then
        @user_customer = UserCustomer.destroy(nCustId)
        # uc.flag = 0
        # uc.save!

        redirect_to :action => "index" , :customer_search_data => session["customer_search_data"], :ap_name => @ap_name
      else
        redirect_to "/users/sign_out"
      end
    else
      redirect_to "/users/sign_out"
    end
  end

  public
  def update_dates_no_sign_in()
    update_dates()
  end
  def update_dates()
    dates = params["ajax_data_key"]
    hDate = Cfate.pan_par_dbfield2hash(dates)

    @ap_name = g_user_ap_name(hDate["ap_name"],@user_id,"ifate")
    @ParAll = g_get_pan_pars(@ap_name,@user_id)

    actionItem = hDate["actionItem"]
    starYearSelect = hDate["start_year"].to_i
    stopYearSelect = hDate["end_year"].to_i
    calType = hDate["calType"].to_i
    year = Xdate.date_s2int(hDate["year"])
    month,leap = Xdate.monthValue2month(Xdate.date_s2int(hDate["month"]))
    day = Xdate.date_s2int(hDate["day"])
    hour = Xdate.date_s2int(hDate["hour"])
    minute = Xdate.date_s2int(hDate["minute"])
    need_minute = Cfate.HashBoolCheck(hDate,"need_minute",false)
    we_exchange = Cfate.HashBoolCheck(hDate,"we_exchange",false)
    two_line = Cfate.HashBoolCheck(hDate,"two_line",false)
    bIncBlank = Cfate.HashBoolCheck(hDate,"bIncBlank",false)
    bCanAdd = Cfate.HashBoolCheck(hDate,"bCanAdd",false)
    need_hour = Cfate.HashBoolCheck(hDate,"need_hour",false)
    need_seven_days = Cfate.HashBoolCheck(hDate,"need_seven_days",false)
    seven_days = Cfate.HashBoolCheck(hDate,"seven_days",false)

    if (we_exchange && !check_date_blank(year,month,day)) then
      if (actionItem == "calType")
        if (calType == Xdate::CT_SOLAR) then
          year,month,day = Xdate.East2West(year, month,day, leap)
        else
            year, month,day, leap = Xdate.West2East(year,month,day)
        end
      end
    end

  # saveTestDb("customer:date_input_ajax",params)
  # saveTestDb("customer:date_input_ajax",hDate)
    render :partial => "date_input_ajax", :locals => {:calType => calType,:year => year,:month => month,:day => day,:hour => hour,:leap => leap,:minute => minute, :bIncBlank => bIncBlank, :par_YearDisplay => @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY], :starYearSelect => starYearSelect, :stopYearSelect => stopYearSelect, :we_exchange => we_exchange, :need_minute => need_minute, :two_line => two_line, :bCanAdd => bCanAdd, :need_hour => need_hour, :need_seven_days => need_seven_days, :seven_days => seven_days }
  end

  protected
  def check_date_blank(year,month,day)
    if (year == Xdate::DATE_NIL) then
      return true
    elsif (month == Xdate::DATE_NIL) then
      return true
    elsif (day == Xdate::DATE_NIL) then
      return true
    else
      return false
    end
  end

  public
  def all_info_modify
    if (current_user.email == "<EMAIL>") then
      ucs = IfatePar.all
      ucs.each do |uc|
        par = uc.parameter
        uc.hPars = Cfate.pan_par_dbfield2hash(par)
        uc.save!
      end
    end
    redirect_to ifate_customer_index_path
  end
  public
  def search
    dates = params["ajax_data_key"]
    hPar = Cfate.pan_par_dbfield2hash(dates)
    getIndexData(hPar)

    if (@full_ap_name.include?("_score")) then
      @ap_name = @full_ap_name
      render :partial => 'customer_list_score', :locals => {:ap_name => @ap_name, :need_search => false, :scustomer_search_data => @customer_search_data,:aall_user_customer => @all_user_customer, :npar_YearDisplay =>  @par_YearDisplay}
    else
      render :partial => 'customer_list_data'
    end
  end

  # health
  public
  def health
    query = params["ajax_data_key"]
    hPar = Cfate.pan_par_dbfield2hash(query)

    @symptom = hPar["symptom"]
    if (@symptom != nil) then
      @Health = Health.query(@symptom)
    else
      @Health = nil
    end
    if (@Health != nil) then
      render :partial => 'health_data'
    end
  end
  def health_key
    query = params["ajax_data_key"]
    hPar = Cfate.pan_par_dbfield2hash(query)

    @symptom = hPar["symptom"]
    if (@symptom != nil) then
      @nicks = Health.find_nick(@symptom)
    else
      @nicks = nil
    end
    if (@nicks != nil) then
      render :partial => 'health_key'
    end
  end

  public
  def setup
    if (@oUserAp.custDisplayCheck) then
      hPars = params["submit_data"]
      @panParType = IfatePar.check_userid_customer(@user_id).first
      if (@panParType == nil) then
        @panParType = IfatePar.new
        @panParType.user_id = @user_id
        @panParType.ap_name = "customer"
        @panParType.hPars = Hash.new
      end
      if (hPars != nil) then
        @panParType.user_id = @user_id
        @panParType.ap_name = "customer"
        @panParType.hPars = @panParType.hPars.merge(hPars)
        @panParType.save!
        redirect_to :action => "index" , :customer_search_data => "", :ap_name => @ap_name
      else
        render :layout => 'ifate'
      end
    else
      redirect_to :action => "index" , :customer_search_data => "", :ap_name => @ap_name
    end
  end

  def manualinsert
    if (current_user.email == "<EMAIL>") then
      user_id = params["user_id"].to_i
      a = Customerparser.parse()
      a.each_index do |i|
      # s = a[1]
        if (i >= 1) then
          s = a[i]
          d = s.split(",")
          h = manualinsert_new_customer_hash(d)
          manualinsert_new_customer(h,user_id)
        end
      end
    end
    redirect_to ifate_customer_index_path
  end
  # @@data = "編號,姓名,出生日期,自定生日,性別,血型,出生地,經度,緯度,身高,排行,學歷,建檔日期,電話,Email,地址,備註,介紹人,客戶類別
  def manualinsert_new_customer(h,user_id)
    # y,m,d,h,s,n = h["wyear"].to_i,h["wmonth"].to_i,h["wday"].to_i,h["hour"].to_i,h["sex"].to_i,h["name"]
    # uc = UserCustomer.check_user_id(user_id).check_year(nWYear).check_month(nWMonth).check_day(nWDate).check_hour(nWHour).check_sex(nSex).check_name(sName).first
    sps = [[h["wyear"].to_i,h["wmonth"].to_i,h["wday"].to_i,h["hour"].to_i,h["sex"].to_i,h["name"]]]
    uc = UserCustomer.check_sps(sps).first
    if (uc == nil) then
      new_cust = true
      uc = UserCustomer.new
      uc.user_id = user_id
      uc.name = h["name"]
      uc.sex = h["sex"].to_i
      uc.blood = h["blood"]
      uc.email = h["email1"]
      uc.cust_userid = nil
      uc.flag = 1
      uc.wyear = h["wyear"].to_i
      uc.wmonth = h["wmonth"].to_i
      uc.wday = h["wday"].to_i
      uc.hour = h["hour"].to_i
      uc.minute = h["minute"].to_i
      uc.eyear = h["eyear"].to_i
      uc.eleap = h["eleap"].to_i
      uc.emonth = h["emonth"].to_i
      uc.eday = h["eday"].to_i
      uc.address = h["address1"]
      uc.phone = h["phone1"]
      uc.userclass = h["userclass"]
      uc.hAll = h
      uc.calType = Xdate::CT_SOLAR
      if (new_cust && h["created_at"] != nil) then
        uc.created_at = h["created_at"]
      end
      uc.save!
    end
        # puts "#{h}"
  end
  def manualinsert_new_customer_hash(d)
    bloodAll = {"" => nil, "O" => 0,"A" => 1,"B" => 2,"AB" => 3}
    h = Hash.new
    h["calType"] = Xdate::CT_SOLAR
    a = d[2].split(" ")
    a1 = a[0].split("/")
    a2 = a[1].split(":")
    wyear = a1[0].to_i
    wmonth = a1[1].to_i
    wday = a1[2].to_i
    hour = a2[0].to_i
    minute = a2[1].to_i
    eyear, emonth,eday, eleap = Xdate.West2East(wyear,wmonth,wday)
    h["wyear"] = wyear
    h["wmonth"] = wmonth
    h["wday"] = wday
    h["hour"] = hour
    h["minute"] = minute
    h["eyear"] = eyear
    h["eleap"] = eleap ? 1 : 0
    h["emonth"] = emonth
    h["eday"] = eday
    h["birthday"] = d[2]
    h["self_def_birthday"] = d[3]
    h["name"] = d[1]
    h["sex"] = Name.isMale(d[4]) ? 1 : 0
    h["address1"] = d[15]
    h["address2"] = ""
    h["phone1"] = d[13]
    h["phone2"] = ""
    h["blood"] = bloodAll["#{d[5]}"]
    h["addressborn"] = d[6]
    h["longitude"] = d[7]
    h["latitude"] = d[8]
    h["stature"] = d[9]
    h["constellation"] = d[10]
    h["educationbackground"] = d[11]
    h["created_at"] = d[12]
    h["email1"] = d[14]
    h["email2"] = ""
    h["webaddress1"] = ""
    h["webaddress2"] = ""
    h["introducer"] = d[17]
    h["userclass"] = d[18]
    h["others"] = d[16]
    h["end"] = "end"

    return h
  end

  public
  def zeri_pan_save_customer_ajax
    hPars = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    customer_timestamps = hPars["customer_timestamps"].split(",")
    chaxuncanshuchucunmingcheng = hPars["chaxuncanshuchucunmingcheng"]

    customer_timestamps.each do |timestamp|
      y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp)
      ey,em,ed,el = Xdate.West2East(y,m,d)
      user_customer = UserCustomer.new
      user_customer.calType = Xdate::CT_SOLAR
      user_customer.wyear = y
      user_customer.wmonth = m
      user_customer.wday = d
      user_customer.hour = h
      user_customer.minute = 0
      user_customer.eyear = ey
      user_customer.eleap = el ? 1 : 0
      user_customer.emonth = em
      user_customer.eday = ed
      user_customer.name = "#{chaxuncanshuchucunmingcheng}_#{Xdate.date_2_name(0,m,d,h)}"
      user_customer.sex = sex
      user_customer.user_id = @user_id
      user_customer.save!
    end
    # render :nothing => true
    if (customer_timestamps.length == 0) then
      s = Zeri.get_ziwei_taitou_str("meiyokerhu")
    else
      s = Zeri.get_ziwei_taitou_str("chucunkerhuchengong")
    end
    render :text => s
  end

  def agerange
    customers = UserCustomer.check_user_id(@user_id).all
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
    @customer_rages = Array.new(7,0)
    i = 0
    customers.each do |customer|
      yearold = Xdate.GetYearOld(customer.eyear,nEYear)
      yearold_index = yearold / 10
      yearold_index = 6 if yearold_index >= 6
      @customer_rages[yearold_index] += 1
      i += 1
    end
    @customer_total = i

    render :layout => 'ifate'
  end
end
