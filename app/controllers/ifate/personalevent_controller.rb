require("Pm.rb")
require("Cfate.rb")
require("Star.rb")
require("Eightword.rb")
require("Xdate.rb")
require("Controller_Api.rb")

class Ifate::PersonaleventController < ApplicationController
  include Controller_Api

    before_action :authenticate_user!, :except => []
    before_action :authenticate, :except => []

  protected
  def authenticate
    #@user_name = current_user.name
    @user_name = current_user.email
    session["user_id"] = current_user.id

    @user_level = ""
    @ap_name = g_user_ap_name(params["ap_name"],session["user_id"])

    session["userlevel"] = @user_level

    @ParAll = g_get_pan_pars(@ap_name,session["user_id"])

    @controller_name = "personalevent"
    if (params["customer_id"] != nil) then
      session["customer_id"] = params["customer_id"].to_i
    end
    @customer = UserCustomer.find_by_id(session["customer_id"])

    @canUsePan = g_canUsePan(session["customer_id"])
    @oUserAp = g_getUserCurrentAp(session["user_id"],@ap_name)
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @hCanDeal = canDealCheck(session["user_id"],@oUserAp,@ap_name)
    @canAddPe = session["customer_id"] != nil

    @admin = current_user.roles? :admin
    @can_add_customer,@customer_max_count = g_can_add_customer(session["user_id"],@admin)
  end

  public
  # 第二種方式：使用 JavaScript 腳本
  def pe_new_ajax
  # saveTestDb("personalevent:pe_new",Cfate.pan_par_hash2db(params.to_hash))
    pe_all = params["ajax_data_key"]
    hpe_all = Cfate.pan_par_dbfield2hash(pe_all)

    oPe = save_pe(hpe_all)

    pan_prepare_personalevent_data(oPe,hpe_all["pe_main_key"],hpe_all["pe_sub_key"])
      t = @customer.created_at
    @user_create_date = "#{t.year}/#{t.month}/#{t.day}#{Pm.t("IDS_S_CREATED")}"

    # render :partial => "ifate/personalevent/personal_event" , :locals => {hpe_all}
    render :partial => "ifate/personalevent/pe_edit_ajax" , :locals => {:pe_title_data => @pe_title_data, :pe_mains => @pe_mains, :pe_main_key_default => @pe_main_key_default, :pe_subs => @pe_subs, :pe_sub_key_default => @pe_sub_key_default, :starYearSelect => @starYearSelect, :stopYearSelect => @stopYearSelect, :calType => @calType, :year => @year, :leap => @leap, :month => @month, :day => @day, :hour => @hour, :minute => @minute, :pe_time => @pe_time, :par_YearDisplay => @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY] , :sVisible => "visible",:personal_events => @personal_events, :ap_name => @ap_name, :bCanAdd => true }
  end

  def update_peitems
  # Pm.saveTestDb("update_peitems",params["ajax_data_key"])
    ajax_data_key = params["ajax_data_key"]
    ajax_data = Cfate.pan_par_dbfield2hash(ajax_data_key)
    pe_main_key = ajax_data["pe_main_key"]
    pe_sub_key = ajax_data["pe_sub_key"]
  # saveTestDb("pe_main",pe_main)
    pan_prepare_personalevent_data(nil,pe_main_key,pe_sub_key)

    render :partial => "pe_items", :locals => { :pe_mains => @pe_mains, :pe_main_key_default => @pe_main_key_default, :pe_subs => @pe_subs, :pe_sub_key_default => @pe_sub_key_default, :pe_sub_self => @pe_sub_self, :pe_desc => @pe_desc }
  end

  protected
  # 個人事件
  def pan_prepare_personalevent_data(oPe,pe_main_key,pe_sub_key)
    tNow = time_now_local()
    oUserCustomer = UserCustomer.find_by_id(session["customer_id"])
    if (oUserCustomer == nil) then
      @starYearSelect = 1901
    else
      @starYearSelect = oUserCustomer.wyear
    end
    if (oUserCustomer == nil || oUserCustomer.wyear <= tNow.year) then
      @stopYearSelect = tNow.year
    else
      @stopYearSelect = oUserCustomer.wyear + 10
    end

    # h.invert
    @pe_mains = Cfate.pan_par_dbfield2hash(Pm.GetPeStr("pe_mains"))
    if (pe_main_key == nil) then
      @pe_main_key_default = @pe_mains.values[0]
    else
      @pe_main_key_default = pe_main_key
    end
    @pe_subs = Cfate.pan_par_dbfield2hash(Pm.GetPeStr(@pe_main_key_default))

    @pe_title_data = Pm.GetStrWithQuote("ifate_pe_title")

    if (oPe == nil) then
      if (pe_sub_key == nil) then
        @pe_sub_key_default = @pe_subs.values[0]
      else
        @pe_sub_key_default = pe_sub_key
      end
      # @pe_sub_key_default = @pe_subs.values[0]
      @calType = Xdate::CT_SOLAR
      @year = tNow.year
      @leap = false
      @month = tNow.month
      @day = tNow.day
      @hour = tNow.hour
      @minute = tNow.min
      @pe_sub_self = ""
      @pe_desc = ""
    else
      @pe_sub_key_default = oPe.pe_sub_key
      @calType = oPe.caltype
      @year = Xdate.date_int2s(oPe.year)
      @leap = Cfate.ValueBoolCheck(oPe.leap)
      @month = Xdate.date_int2s(oPe.month)
      @day = Xdate.date_int2s(oPe.day)
      @hour = Xdate.date_int2s(oPe.hour)
      @minute = Xdate.date_int2s(oPe.minute)
      @pe_sub_self = oPe.pe_sub_self
      @pe_desc = oPe.pe_desc
    end

    @personal_events = PersonalEvent.available.check_user_id(current_user.id).check_user_customer_id(session["customer_id"]).check_main_key_pe().last(3).reverse
    @canAddPe = session["customer_id"] != nil
    if (session["customer_id"] != nil) then
      api_set_user_customer_updated_at(@customer_id)
    end
  end

  public
  def index
    @personal_events = PersonalEvent.available.check_user_id(current_user.id).check_user_customer_id(session["customer_id"]).check_main_key_pe().all.reverse
    render :layout => "ifate"
  end

  def edit
    # saveTestDb("pe:edit",params["personal_event_id"])
    personal_event_id = params["personal_event_id"]
    @personal_event = PersonalEvent.find_by_id(personal_event_id)
    pan_prepare_personalevent_data(@personal_event,@personal_event.pe_main_key,@personal_event.pe_sub_key)
    # saveTestDb("pe:oPe.pe_main_key",oPe.pe_main_key)

    render :layout => "ifate"
  end

  def show
  end

  def new
    pan_prepare_personalevent_data(nil,nil,nil)
    @personal_event = nil
    @month = Xdate::DATE_NIL
    @day = Xdate::DATE_NIL
    @hour = Xdate::DATE_NIL
    @minute = Xdate::DATE_NIL

    render :layout => "ifate"
  end

  def update
    @personal_event = save_pe(params)

    redirect_to :controller => "ifate/personalevent" ,:action => "index", :ap_name => @ap_name
  end

  def delete
    personal_event_id = params["personal_event_id"]
    @personal_event = PersonalEvent.find_by_id(personal_event_id)
    render :layout => "ifate"
  end

  def destroy
    personal_event_id = params["personal_event_id"].to_i
    PersonalEvent.delete(personal_event_id)

    redirect_to :controller => "ifate/personalevent" ,:action => "index", :ap_name => @ap_name
  end

  protected
  def save_pe(hpe_all)
  # saveTestDb("personalevent:save_pe",Cfate.pan_par_hash2db(hpe_all.to_hash))
    personal_event_id = hpe_all["personal_event_id"]
    @pe_mains = Cfate.pan_par_dbfield2hash(Pm.GetPeStr("pe_mains"))
    @pe_subs = Cfate.pan_par_dbfield2hash(Pm.GetPeStr(hpe_all["pe_main_key"]))
    caltype = hpe_all["calType"].to_i
    year = Xdate.date_s2int(hpe_all["year"])
    month = Xdate.date_s2int(hpe_all["month"])
    month,leap = Xdate.monthValue2month(month)
    day = Xdate.date_s2int(hpe_all["day"])
    hour = Xdate.date_s2int(hpe_all["hour"])
    minute = Xdate.date_s2int(hpe_all["minute"])
    pe_main_key = hpe_all["pe_main_key"]
    pe_sub_key = hpe_all["pe_sub_key"]
    pe_sub_self = checkOrgComma(hpe_all["pe_sub_self"])
    pe_desc = checkOrgComma(hpe_all["pe_desc"])

    if (personal_event_id == nil) then
      oPe = PersonalEvent.new
      # 確認該事件是否已被輸入
      oPeOld = PersonalEvent.available.check_user_id(current_user.id).check_user_customer_id(session["customer_id"])
      oPeOld = oPeOld.check_main_key(pe_main_key).check_sub_key(pe_sub_key).check_sub_self(pe_sub_self)
      oPeOld = oPeOld.check_caltype(caltype).check_year(year).check_leap(leap).check_month(month).check_day(day).check_hour(hour).check_minute(minute).first
      # oPeOld = oPeOld.check_pe_desc(hpe_all["pe_desc"]).first
    else
      oPe = PersonalEvent.find_by_id(personal_event_id)
      oPeOld = nil
      if ((oPe.user_id != current_user.id) || (oPe.user_customer_id != session["customer_id"])) then
        return nil
      end
    end

    oPe.user_id = current_user.id
    oPe.user_customer_id = session["customer_id"]
    oPe.pe_main_key = pe_main_key
    oPe.pe_main_value = @pe_mains.invert[pe_main_key]
    oPe.pe_sub_key = pe_sub_key
    oPe.pe_sub_value = @pe_subs.invert[pe_sub_key]
    oPe.pe_sub_self = pe_sub_self
    oPe.caltype = caltype
    oPe.year = year
    oPe.leap = leap
    oPe.month = month
    oPe.day = day
    oPe.hour = hour
    oPe.minute = minute
    oPe.pe_desc = pe_desc
    oPe.status = 0
    if (oPeOld == nil) then
      oPe.save!
    end

    return oPe
  end

  def checkOrgComma(value)
    if (value == nil) then
      return value
    end
    newValue = value.gsub(/___/,",");
    return newValue
  end

end
