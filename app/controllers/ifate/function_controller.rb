require("Xdate.rb")
require("Star.rb")
require("Eightword.rb")
require("Zeri.rb")
require("MyNet.rb")
require("Wannianli.rb")
require("Xuexi_Ziwei.rb")

class Ifate::FunctionController < ApplicationController
    before_action :authenticate_user!, :except => [:pansimpleinput,:xdate_free]
    before_action :authenticate, :except => [:pansimpleinput,:xdate_free]

  def zeri_pan
    zeri_pan_data(params)
    # 每次尋找都新開一頁
    @target_name = Xdate.make_now_timestamp() # "blank"
    @free_ap = false


    if (@zeri_pan_type == "search") then
      # 暫時先存在資料庫中，待紫微或八字再取出使用，因為redirect_to是Get http，傳的資料太大會錯誤，
      # 存session也有資料上限，所以用資料庫來當暫存區
      Zeri.save_answers(@ap_name+@target_name,session["user_id"],@answers)
      redirect_to :controller => "ifate/#{@ap_name.downcase}" , :action => "zeri_pan_answer", :chaxuncanshuchucunmingcheng => @zeri["chaxuncanshuchucunmingcheng"], :target_name => @target_name
    else
    # nil[]
      render :layout => 'ifate'
    end
  end
  def show_pan
    redirect_to :controller => "ifate/#{@ap_name.downcase}" ,:action => "pan"
  end
  def zeri_pan_show_ajax
    hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    ap_name = hAjaxData["ap_name"]
    timestamp = hAjaxData["timestamp"]
    # sex = hAjaxData["sex"]
    # calType = hAjaxData["calType"]
    target_name = hAjaxData["target_name"]
    # lifa = hAjaxData["lifa"]

    redirect_to :controller => "ifate/#{@ap_name.downcase}" ,:action => "zeri_pan_show_ajax", :ap_name => ap_name, :timestamp => timestamp, :target_name => target_name
    # redirect_to :controller => "ifate/#{@ap_name.downcase}" ,:action => "zeri_pan_show_ajax", :ap_name => ap_name, :timestamp => timestamp, :sex => sex, :calType => calType, :target_name => target_name, :lifa => lifa
  end

  def xdate
    @user_id = current_user.id if logged_in?
    wannialli_free_data(params)
    oWannianli = Wannianli.new
    oWannianli.g_GetPanInfo(Cfate::PAN_NORMAL,@UserInfo,@UserDefData,@ParAll)
    @par_YearDisplay = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]
    @starYearSelect = 1901
    @stopYearSelect = 2099
    @wannianli_free_result = oWannianli.free_info()
    @free_ap = true
    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?

    render :layout => 'ifate'
  end
  def xdate_free
    @user_id = current_user.id if logged_in?
    wannialli_free_data(params)
    oWannianli = Wannianli.new
    oWannianli.g_GetPanInfo(Cfate::PAN_NORMAL,@UserInfo,@UserDefData,@ParAll)
    @par_YearDisplay = @ParAll != nil ? @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY] : Cfate::PAN_DISPLAY_WEST
    @starYearSelect = 1901
    @stopYearSelect = 2099
    @wannianli_free_result = oWannianli.free_info()
    @free_ap = true
    @free_ap_name = "xdate_free"
    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?

    render :layout => 'xdate_free'
  end
  def fuwu_shuoming
    redirect_to :controller => "ifate/#{@ap_name.downcase}" ,:action => "fuwu_shuoming"
  end

  def ji_xiong_chaxun_ajax
    hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    ap_name = hAjaxData["ap_name"]

    redirect_to :controller => "ifate/#{@ap_name.downcase}" ,:action => "ji_xiong_chaxun_ajax", :ap_name => ap_name, :ajax_data_key => params["ajax_data_key"]
  end

  protected
  def logged_in?
    return (current_user != nil)
  end
  protected
  def wannialli_free_data(hData)
    zeri_pan_data(hData)
    pan_prepare_data(@zeri)
  end
  def zeri_pan_data(hData)
    @zeri_pan_type = hData["zeri_pan_type"]
    @zeris = Zeri.find_from_ifatepar(@user_id,@ap_name)
    can_zeri = @oUserAp != nil ? @oUserAp.can_zeri : false
    @zeri_simple = Zeri.zeri_simple(can_zeri,@ap_name)
    @ifatepar_zeri_index = hData["ifatepar_zeri_index"].to_i

    if (@zeri_pan_type == nil) then
      @ifatepar_zeri_index = -1
      @zeri_pan_type = "new"
    end
    if (@zeri_pan_type == "show") then
      if ((@zeri_simple == 1 || @zeris.length == 0)) then
        @ifatepar_zeri_index = -1
        @zeri_pan_type = "new"
      end
    end
    @const_data = Zeri.zeri_pan_data_constant_data(@ap_name,@zeri_simple)

    if (@zeri_pan_type == "show") then
      zeri_pan_data_show(hData)
    elsif (@zeri_pan_type == "new") then
        zeri_pan_data_new()
    elsif (@zeri_pan_type == "search")
      zeri_pan_data_search(hData)
    elsif (@zeri_pan_type == "save")
      zeri_pan_data_save(hData)
    elsif (@zeri_pan_type == "delete")
      zeri_pan_data_delete(hData)
    end
    userinfo_to_usercustomer(@zeri["UserInfo"])
  end
  def zeri_pan_data_show(hData)
    @zeri = @zeris[@ifatepar_zeri_index]
  end
  def zeri_pan_data_new()
    @zeri = Hash.new
    @zeri["need_seven_days"] = Zeri.need_seven_days(@zeri_simple,@ap_name)
    @zeri["UserInfo"] = new_for_now
    @zeri["seven_days"] = false
    @zeri["answers"] = Array.new
    @zeri["days"] = 1
    @zeri.merge!(Zeri.zeri_pan_data_default(@ap_name,@zeri_simple))
    @answers = []
    @ifatepar_zeri_index = -1
  end
  def zeri_pan_data_pars(hData)
    @zeri = Hash.new
    # @zeri_simple = hData["simple"].to_i
    @zeri["need_seven_days"] = Zeri.need_seven_days(@zeri_simple,@ap_name)
    @zeri["UserInfo"] = newUserInfo(hData)
    @zeri["seven_days"] = Cfate.ValueBoolCheck(hData["seven_days"])

    @zeri.merge!(Zeri.zeri_pan_data_parsing(@ap_name,@zeri_simple,hData))

    days = 1
    if (@zeri["seven_days"]) then
      days = 7
    end
    @zeri["days"] = days
  end
  def zeri_pan_data_save(hData)
    zeri_pan_data_pars(hData)
    if (@zeri["chaxuncanshuchucunmingcheng"] == nil || @zeri["chaxuncanshuchucunmingcheng"] == "") then
      @zeri["chaxuncanshuchucunmingcheng"] = Zeri.get_ziwei_taitou_str("weimingming")
    end
    @zeris,@ifatepar_zeri_index = Zeri.save_to_ifatepar(@ifatepar_zeri_index,@user_id,@ap_name,@zeri)
  end
  def zeri_pan_data_delete(hData)
    @ifatepar_zeri_index = hData["ifatepar_zeri_index"].to_i
    @zeris,@ifatepar_zeri_index = Zeri.delete_from_ifatepar(@ifatepar_zeri_index,@user_id,@ap_name,@zeri)
    if (@ifatepar_zeri_index == -1) then
      zeri_pan_data_new()
    else
      @zeri = @zeris[@ifatepar_zeri_index]
    end
  end
  def zeri_pan_data_search(hData)
    zeri_pan_data_pars(hData)
    @answers = zeri_pan_answer(@zeri)
  end
  def authenticate
    authenticate_function(params["ap_name"],"ifate")
  end
  def authenticate_function(new_ap_name="star",ap_namespace="ifate")
    #@user_name = current_user.name
    @user_name = current_user.email
    session["user_id"] = current_user.id

    @user_level = ""
    @ap_name = g_user_ap_name(new_ap_name,session["user_id"],ap_namespace)

    session["userlevel"] = @user_level

    @ParAll = g_get_pan_pars(@ap_name,session["user_id"])

    @controller_name = "function"
    if (params["customer_id"] != nil) then
      session["customer_id"] = params["customer_id"].to_i
    end
    @customer = UserCustomer.find_by_id(session["customer_id"])

    @canUsePan = g_canUsePan(session["customer_id"])
    @oUserAp = g_getUserCurrentAp(session["user_id"],@ap_name)
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @hCanDeal = canDealCheck(session["user_id"],@oUserAp,@ap_name)
    @canAddPe = session["customer_id"] != nil

    @admin = current_user.roles? :admin
    @can_add_customer,@customer_max_count = g_can_add_customer(session["user_id"],@admin)
    @user_id = session["user_id"]
  end
  def userinfo_to_usercustomer(hUserInfo)
    @user_customer = UserCustomer.new
    @user_customer.calType = hUserInfo[Cfate::CalType]
    @user_customer.wyear = hUserInfo[Cfate::WYear]
    @user_customer.wmonth = hUserInfo[Cfate::WMonth]
    @user_customer.wday = hUserInfo[Cfate::WDate]
    @user_customer.hour = hUserInfo[Cfate::WHour]
    @user_customer.minute = hUserInfo[Cfate::WMinute] == -1 || hUserInfo[Cfate::WMinute] == nil ? 59 : hUserInfo[Cfate::WMinute]
    @user_customer.name = hUserInfo[Cfate::Name]
    @user_customer.sex = hUserInfo[Cfate::Sex] ? 0 : 1
    @user_customer.eyear = hUserInfo[Cfate::EYear]
    @user_customer.eleap = hUserInfo[Cfate::LeapMonth]
    @user_customer.emonth = hUserInfo[Cfate::EMonth]
    @user_customer.eday = hUserInfo[Cfate::EDate]
  end

  def new_for_now()
    h = Hash.new

    t = time_now_local()

    h["user_id"] = session["user_id"]
    ey,em,ed,el = Xdate.West2East(t.year,t.month,t.day)
    h[Cfate::Name] = Xdate.make_timestamp(t.year,t.month,t.day,t.hour)
    h[Cfate::Sex] = false
    h[Cfate::CalType] = Xdate::CT_SOLAR
    h[Cfate::WYear] = t.year
    h[Cfate::WMonth] = t.month
    h[Cfate::WDate] = t.day
    h[Cfate::EYear] = ey
    h[Cfate::ELeapMonth] = el
    h[Cfate::EMonth] = em
    h[Cfate::EDate] = ed
    h[Cfate::WHour] = t.hour
    h[Cfate::WMinute] = t.min

    @user_customer = UserCustomer.new
    @user_customer.calType = Xdate::CT_SOLAR
    @user_customer.wyear = t.year
    @user_customer.wmonth = t.month
    @user_customer.wday = t.day
    @user_customer.hour = t.hour
    @user_customer.minute = t.min
    @user_customer.name = ""
    @user_customer.sex = 1

    return h
  end
  def newUserInfo(hPar)
    hAll = getUserInfoHash(hPar)
    hUserInfo = assignUserInfo(hAll)
    return hUserInfo
  end
  def getUserInfoHash(hPar)
    hDate = Xdate.date_input_prepare(hPar)

    hAll = Hash.new
    hAll.merge!(hDate)
    hAll["end"] = "end"

    return hAll
  end
  def assignUserInfo(hAll)
    h = Hash.new

    h["user_id"] = session["user_id"]
    h[Cfate::Sex] = Cfate.HashOrDefault(hAll,"sex",0) == 1 ? false : true
    h[Cfate::CalType] = Cfate.HashOrDefault(hAll,"calType",Xdate::CT_SOLAR)
    h[Cfate::WYear] = Cfate.HashOrDefault(hAll,"wyear",Xdate::DATE_NIL)
    h[Cfate::WMonth] = Cfate.HashOrDefault(hAll,"wmonth",Xdate::DATE_NIL)
    h[Cfate::WDate] = Cfate.HashOrDefault(hAll,"wday",Xdate::DATE_NIL)
    h[Cfate::EYear] = Cfate.HashOrDefault(hAll,"eyear",Xdate::DATE_NIL)
    h[Cfate::ELeapMonth] = Cfate.Bool2Int(hAll["eleap"])
    h[Cfate::EMonth] = Cfate.HashOrDefault(hAll,"emonth",Xdate::DATE_NIL)
    h[Cfate::EDate] = Cfate.HashOrDefault(hAll,"eday",Xdate::DATE_NIL)
    h[Cfate::WHour] = Cfate.HashOrDefault(hAll,"hour",Xdate::DATE_NIL)
    wy,wm,wd,wh = h[Cfate::WYear],h[Cfate::WMonth],h[Cfate::WDate],h[Cfate::WHour]
    h[Cfate::Name] = Xdate.make_timestamp(wy,wm,wd,wh)
    hAll["minute"] = 59 if (hAll["minute"] == -1 || hAll["minute"] == nil)
    h[Cfate::WMinute] = Cfate.HashOrDefault(hAll,"minute",Xdate::DATE_NIL)

    u = UserCustomer.new

    if (u != nil) then
      u.user_id = session["user_id"]
      u.name = hAll["name"]
      u.sex = Cfate.HashOrDefault(hAll,"sex",0)
      u.calType = Cfate.HashOrDefault(hAll,"calType",Xdate::CT_SOLAR)
      u.wyear = Cfate.HashOrDefault(hAll,"wyear",Xdate::DATE_NIL)
      u.wmonth = Cfate.HashOrDefault(hAll,"wmonth",Xdate::DATE_NIL)
      u.wday = Cfate.HashOrDefault(hAll,"wday",Xdate::DATE_NIL)
      u.eyear = Cfate.HashOrDefault(hAll,"eyear",Xdate::DATE_NIL)
      u.eleap = Cfate.Bool2Int(hAll["eleap"])
      u.emonth = Cfate.HashOrDefault(hAll,"emonth",Xdate::DATE_NIL)
      u.eday = Cfate.HashOrDefault(hAll,"eday",Xdate::DATE_NIL)
      u.hour = Cfate.HashOrDefault(hAll,"hour",Xdate::DATE_NIL)
      u.minute = Cfate.HashOrDefault(hAll,"minute",Xdate::DATE_NIL)
      u.blood = hAll["blood"]
      u.phone = hAll["phone1"]
      u.address = hAll["address1"]
      u.email = hAll["email1"]
      u.userclass = hAll["userclass"]
      hAllOrg = u.hAll
      hAllOrg.merge!(hAll) {|key, v1, v2| v2}
      u.hAll = hAllOrg
      # u.all_info = Cfate.pan_par_hash2db(hAll)
    end
    @user_customer = u

    return h
  end
  def zeri_pan_answer(zeri)
    a = Array.new

    pan_prepare_data(zeri)

    oZeri = Zeri.new
    a = oZeri.ziwei_pan_answer(@ap_name,@UserInfo,@UserDefData,@ParAll)

    return a
  end
  def pan_prepare_data(zeri)
    # 設定客戶資訊
    @UserInfo = set_pan_user_info(zeri)

    # 處理流盤時間
    @UserDefData = set_pan_user_def_data()
    @UserDefData["zeri_pars"] = zeri
  end
  def set_pan_user_info(zeri)
    hUserInfo = zeri["UserInfo"]
    hUserInfo["remote_ip"] = remote_ip()

    return hUserInfo
  end
  def set_pan_user_def_data()
    # 處理流盤時間
    hUserDefData = get_cur_date_info()

    return hUserDefData
  end
  def get_cur_date_info()
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
    hUserDefData = Hash.new
    hUserDefData[Cfate::EYear] = nEYear
    hUserDefData[Cfate::EMonth] = nEMonth
    hUserDefData[Cfate::EDate] = nEDate
    hUserDefData[Cfate::LeapMonth] = bLeapMonth
    hUserDefData[Cfate::WHour] = nWHour
    hUserDefData[Cfate::WMinute] = nWMin
    return hUserDefData
  end

  public
  def pansimpleinput
    # puts "current_user.sign_in_count : #{current_user.sign_in_count}" if logged_in?
    if (logged_in? && current_user.sign_in_count == 1) then
      redirect_to  :controller => "ifate/customer" ,:action => "index"
    else
      if (params["ap_name"] == 'eightword') then
        redirect_to :controller => "ifate/eightword" ,:action => "pansimpleinput"
      elsif (params["ap_name"] == 'star') then
        redirect_to :controller => "ifate/star" ,:action => "pansimpleinput"
      elsif (params["ap_name"] == 'wannianli') then
        redirect_to :controller => "ifate/wannianli" ,:action => "pan_free"
      elsif (params["ap_name"] == 'xdate') then
        redirect_to :controller => "ifate/function" ,:action => "xdate_free"
      else
        redirect_to :controller => "ifate/star" ,:action => "pansimpleinput"
      end
    end
  end
end
