require("Talent.rb")
require("meen_forum.rb")

# redis-server /usr/local/etc/redis.conf

class Ifate::TalentController < ApplicationController
  include MeenForum

  protect_from_forgery with: :null_session
  before_action :authenticate_user!, :except => []
  before_action :authenticate, :except => []
  # after_action :allow_iframe, only: :pan2


  public
  def setup
    save_session_data()
    # if ( current_user.email == "<EMAIL>" || current_user.email == "<EMAIL>" || current_user.email == "<EMAIL>" || current_user.id == 128) then
    if (current_user.admin?) then
      get_talent_data(params)
      get_stars_for_select(params)
      @remain_star_weight = remain_star_weight()
      if (@category_level == "L1") then
        setup_L1()
      elsif (@category_level == "L2") then
        setup_L2()
      elsif (@category_level == "L3") then
        setup_L3()
      elsif (@category_level == "MP") then
        setup_MP()
      end
      set_global_var(@category_level)

      render :layout => 'ifate'
    else
      redirect_to ifate_customer_index_path
    end
  end
  def setting
    save_session_data()
    if (current_user.admin?) then
    	get_talent_data(params)
  	  get_trait_def(params)  	
  	  # get trait rule
      if (@category_level == "L2") then
        setup_rule_L2_cmd("e",params)
      elsif (@category_level == "L3") then
        setup_rule_L3_cmd("e",params)
      elsif (@category_level == "MP") then
        setup_rule_MP_cmd("e",params)
      end

  	  set_talent_data()
      set_global_var(@category_level)

      redirect_to :action => "setup", :category_id => @category_id, :item_id => @item_id, :trait_id => @trait_id, :category_level => @category_level
    else
      redirect_to ifate_customer_index_path
    end
  end
  def change_talent
    save_session_data()
  	h = params["ajax_data_key"]
  	hPar = Cfate.pan_par_dbfield2hash(h)
  	get_talent_data(hPar)
    @h_copy = Pm.findTestDbHashValue_key("talent_copy")
    @h_copy_L2 = Pm.findTestDbHashValue_key("talent_copy_L2")
    @h_copy_L3 = Pm.findTestDbHashValue_key("talent_copy_L3")
    @h_copy_MP = Pm.findTestDbHashValue_key("talent_copy_MP")
    render :partial => "ifate/talent/setup_all" , :layout => false, :locals => {}
  end
  def category
    save_session_data()
  	h = params["submit_data"]
  	if (h == nil) then
  	  h = params
  	end
  	get_talent_data(h)
  	category = h["category"]
  	category_id = h["category_id"]
  	cmd = h["cmd"]
  	# puts "cmd = #{cmd}"
  	category_cmd(cmd,category,category_id)
  	set_talent_data()
    set_global_var(@category_level)
  end
  def item
    save_session_data()
  	h = params["submit_data"]
  	if (h == nil) then
  	  h = params
  	end
  	get_talent_data(h)
  	category_id = h["category_id"]
  	item = h["item"]
  	item_id = h["item_id"]
  	cmd = h["cmd"]
  	# puts "cmd = #{cmd}"
  	item_cmd(cmd,category_id,item,item_id)
  	set_talent_data()
    set_global_var(@category_level)
  end
  def trait
    save_session_data()
  	h = params["submit_data"]
  	if (h == nil) then
  	  h = params
  	end
  	get_talent_data(h)
  	category_id = h["category_id"]
  	item_id = h["item_id"]
  	trait = h["trait"]
    trait_id = h["trait_id"]
  	trait_code = h["trait_code"]
  	cmd = h["cmd"]
  	# puts "cmd = #{cmd}"
  	trait_cmd(cmd,category_id,item_id,trait,trait_id,trait_code)
  	set_talent_data()
    set_global_var(@category_level)
  end
  def setup_star_rule
    save_session_data()

  	get_talent_data(params)
  	get_stars_for_select(params)
  	@cmd = params["cmd"]
  	mode = params["mode"]
    @star_for_rule = params["star_for_rule"] == nil ? @stars_all[@stars_all.keys[0]] : params["star_for_rule"]
  	if (@cmd == "a") 
    	@cur_rule = rule_par_setting_new({},@star_for_rule)
    else
      @cur_rule = @par_setting[@category_id][@item_id][@trait_id][@star_for_rule]
      @cur_rule = rule_par_setting_new({},@star_for_rule) if @cur_rule == nil || @cur_rule == {}
      @cur_rule["sihua"]["sihua_star_weight"] = Talent.sihua_star_weight_par_setting_new() if @cur_rule["sihua"]["sihua_star_weight"] == nil
      @cur_rule["shenggong_weight"] = shenggong_new() if @cur_rule["shenggong_weight"] == nil
      @cur_rule["sihua"]["sihua_sign"] = Talent.sihua_sign_new() if @cur_rule["sihua"]["sihua_sign"] == nil
      @cur_rule["sihua"]["sihua_sign_exception_house"] = Talent.sihua_sign_exception_house_new() if @cur_rule["sihua"]["sihua_sign_exception_house"] == nil
    end
    @max_star_weight = remain_star_weight() + @cur_rule["star_weight"].to_i

  	if (@cmd != nil && mode == "submit") then
  		setup_star_rule_cmd(@cmd,params)
      if (@cmd == "p" || @cmd == "e") then
        @cur_rule = @par_setting[@category_id][@item_id][@trait_id][@star_for_rule]
      end
  		set_talent_data()
      if (@cmd == "d") then
        redirect_to :action => "setup", :category_id => @category_id, :item_id => @item_id, :trait_id => @trait_id, :category_level => @category_level
      end
      if (@cmd == "p" || @cmd == "c")  then
        @cmd = "e"
      end
  	else
  	end
    if (@cmd == "a" || @cmd == "" || @cmd == nil)  then
      @cmd = "e"
    end
  end
  def setup_rule
    save_session_data()
    if (current_user.admin?) then
      get_talent_data(params)
      if (@category_level == "L2") then
        setup_rule_L2()
      elsif (@category_level == "L3") then
        setup_rule_L3()
      elsif (@category_level == "MP") then
        setup_rule_MP()
      end
      redirect_to :action => "setup", :category_id => @category_id, :item_id => @item_id, :trait_id => @trait_id, :category_level => @category_level
    else
      redirect_to ifate_customer_index_path
    end
  end
  def trait_score
    save_session_data()
    h = params["submit_data"]
    if (h == nil) then
      h = params
    end
    get_talent_data(h)
    category_id = h["category_id"]
    # item_id = h["item_id"]
    # trait_id = h["trait_id"]
    @timestamp_user = h["timestamp_user"]

    # @timestamp_user = "20190918233800M"
    nPanType = h["pan_type"].to_i
    nPanType = Cfate::PAN_NORMAL if nPanType == nil

    @timestamp_udt = h["timestamp_udt"]
    obj_talent = Talent.new
    hUserDefData = get_user_def_data(@timestamp_udt)
    hUserDefData["categorys"] = [category_id] if category_id != nil
    hUserDefData["categorys"] = [] if category_id == ""
    hUserDefData["talent_key"] = Talent.get_talent_key(@category_level,my_current_lang()) 
    # hUserDefData["talent_L1_key"] = Talent.get_talent_key("L1",my_current_lang()) 
    # hUserDefData["talent_L2_key"] = Talent.get_talent_key("L2",my_current_lang()) 
    # hUserDefData["talent_L3_key"] = Talent.get_talent_key("L3",my_current_lang()) 
    hUserInfo = get_user_info(@timestamp_user)
    getPanType(nPanType)
    get_stars_for_select(h)
    hUserDefData["category_level"] = @category_level

    hUserDefData["flow_rate"] = get_ts_rate(obj_talent,nPanType,h)

    # @Score,@Detail = obj_talent.getScore(nPanType,hUserInfo,hUserDefData)
    @Score,@Detail = trait_score_getdata(obj_talent,nPanType,hUserInfo,hUserDefData)
    set_global_var(@category_level)
    # puts "@Score : #{@Score}"
    # puts "@Detail : #{@Detail}"

  end
  protected
    def trait_score_getdata(obj_talent,nPanType,hUserInfo,hUserDefData)
        a_rate = hUserDefData["flow_rate"]
        if nPanType == 0 then # 本命
          score1,detail = obj_talent.getScore(nPanType,hUserInfo,hUserDefData)
          score = trait_score_getdata_out(score1,nil,nil,100,0,0,score1)
        elsif nPanType == 1 then # 十年
          score1,detail1 = obj_talent.getScore(0,hUserInfo,hUserDefData)
          # obj_talent.wc_init_Scores()
          score2,detail2 = obj_talent.getScore(1,hUserInfo,hUserDefData)
          score = trait_score_getdata_out(score1,score2,nil,a_rate[0],a_rate[1],a_rate[2],score2)
          detail = detail2.clone
        elsif (nPanType == 2) then # 流年
          score1,detail1 = obj_talent.getScore(0,hUserInfo,hUserDefData)
          # obj_talent.wc_init_Scores()
          score2,detail2 = obj_talent.getScore(1,hUserInfo,hUserDefData)
          # obj_talent.wc_init_Scores()
          score3,detail3 = obj_talent.getScore(2,hUserInfo,hUserDefData) 
          score = trait_score_getdata_out(score1,score2,score3,a_rate[0],a_rate[1],a_rate[2],score3)
          detail = detail3.clone
        elsif (nPanType == 3) then # 流月
          score1,detail1 = obj_talent.getScore(0,hUserInfo,hUserDefData)
          # obj_talent.wc_init_Scores()
          score2,detail2 = obj_talent.getScore(2,hUserInfo,hUserDefData)
          # obj_talent.wc_init_Scores()
          score3,detail3 = obj_talent.getScore(3,hUserInfo,hUserDefData) 
          score = trait_score_getdata_out(score1,score2,score3,a_rate[0],a_rate[1],a_rate[2],score3)
          detail = detail3.clone
        end
        return score,detail
    end
    def trait_score_getdata_out(a0,a1,a2,n0,n1,n2,cur_a)
        hOut = cur_a.clone
        pr1 = 0.0
        pr2 = 0.0
        score1 = 0.0
        score2 = 0.0
        a0.keys.each do |category_id|
          items0 = a0[category_id]
          items1 = a1[category_id] if a1 != nil
          items2 = a2[category_id] if a2 != nil
          items0.keys.each do |item_id|
            traits0 = a0[category_id][item_id] 
            traits1 = a1[category_id][item_id] if a1 != nil 
            traits2 = a2[category_id][item_id] if a2 != nil
            traits0.keys.each do |trait_id|
              pr0 = a0[category_id][item_id][trait_id]["pr"]
              pr0 = 0 if pr0 == nil
              pr1 = a1[category_id][item_id][trait_id]["pr"] if a1 != nil
              pr1 = 0 if pr1 == nil
              pr2 = a2[category_id][item_id][trait_id]["pr"] if a2 != nil
              pr2 = 0 if pr2 == nil
              flow_pr = pr0 * n0
              flow_pr = flow_pr + pr1 * n1 if a1 != nil
              flow_pr = flow_pr + pr2 * n2 if a2 != nil
              flow_pr = flow_pr / 100
              flow_pr = flow_pr.round(2)
              hOut[category_id][item_id][trait_id]["pr"] = flow_pr
              hOut[category_id][item_id][trait_id]["all_pr"] = [pr0,pr1,pr2]

              score0 = a0[category_id][item_id][trait_id]["score"]
              score1 = a1[category_id][item_id][trait_id]["score"] if a1 != nil
              score2 = a2[category_id][item_id][trait_id]["score"] if a2 != nil
              flow_score = score0 * n0
              flow_score = flow_score + score1 * n1 if a1 != nil
              flow_score = flow_score + score2 * n2 if a2 != nil
              flow_score = flow_score / 100
              flow_score = flow_score.round(2)
              hOut[category_id][item_id][trait_id]["score"] = flow_score
              hOut[category_id][item_id][trait_id]["all_score"] = [score0,score1,score2]
            end
          end
        end
        return hOut
    end

  public
  def category_score
    save_session_data()
    # if ( current_user.email == "<EMAIL>" || current_user.email == "<EMAIL>" || current_user.email == "<EMAIL>" || current_user.id == 128) then
    if (current_user.admin?) then
      h = params["submit_data"]
      if (h == nil) then
        h = params
      end
      get_talent_data(h)
      category_id = h["category_id"]
      n_years = h["n_years"].to_i
      n_years = 7 if n_years == 0

      # item_id = h["item_id"]
      # trait_id = h["trait_id"]
      @timestamp_user_start = h["timestamp_user_start"]
      @timestamp_user_stop = h["timestamp_user_stop"]
      # @timestamp_user = "20190918233800M"
      nPanType = h["pan_type"].to_i
      nPanType = Cfate::PAN_NORMAL if nPanType == nil
      @timestamp_udt = h["timestamp_udt"]
      hUserDefData = get_user_def_data(@timestamp_udt)
      hUserDefData["category_id"] = h["category_id"]
      hUserDefData["item_id"] = h["item_id"]
      hUserDefData["trait_id"] = h["trait_id"]
      hUserDefData["categorys"] = [category_id] if category_id != nil
      hUserDefData["categorys"] = [] if category_id == "" || category_id == nil
      hUserDefData["talent_key"] = Talent.get_talent_key(@category_level,my_current_lang())
      hUserDefData["insert_only"] = Cfate.ValueBoolCheck(h["insert_only"])

      getPanType(nPanType)
      get_stars_for_select(h)

      if ["c","c_bg"].include?(h["cmd"]) then
        # h3 = {}
        # hUserDefData["categorys"].each do |category_id|
        #   h3[category_id] = @talent["version"][category_id]
        # end
        # hUserDefData["talent_version"] = h3
        hUserDefData["talent_version"] = @talent["version"] # 全部給
        hUserDefData["timestamp_udt"] = @timestamp_udt
        hUserDefData["category_level"] = @category_level
        hUserDefData["trait_code"] = get_level_trait_code(@category_level)
    
        hUserInfo = get_user_info(@timestamp_user_start)
        hUserInfo["timestamp_user_start"] = get_user_info(@timestamp_user_start)
        hUserInfo["timestamp_user_stop"] = get_user_info(@timestamp_user_stop)
        @t_start = Xdate.GetNowRails()
        if h["cmd"] == "c" then
          obj_talent = Talent.new
          obj_talent.getUsersScore(nPanType,hUserInfo,hUserDefData)
        elsif h["cmd"] == "c_bg"        
          # MeenForum::BgWorker.perform_async("getUsersScore",nPanType,hUserInfo,hUserDefData)
          multi_thread_getUsersScore(nPanType,hUserInfo,hUserDefData,n_years)
        end
        @t_stop = Xdate.GetNowRails()
      end
      # 只計算某個trait_id
      # if (h["cmd"] == "cc" || h["cmd"] == "" || h["cmd"] == nil) then
        # @category_id = h["category_id"]
      item_trait_id(h,@category_level)
      # end 
      set_global_var(@category_level)
    else
      redirect_to ifate_customer_index_path
    end
  end

  def backup
    save_session_data()
    if (current_user.admin?) then
      get_talent_data(params)
      set_global_var(@category_level)
      @cmd = params["cmd"]
      if @cmd == "b" then
        backup_talent_par(my_current_lang())
      elsif @cmd == "r" then
        restore_talent_par(my_current_lang())
      elsif @cmd == "ra" then
        restore_to_all_talent_par(my_current_lang())
      elsif @cmd == "r_p" then
        restore_talent_par_only(my_current_lang())
      end
      redirect_to :action => "main", :category_level => @category_level, :locale => @current_lang
    else
      redirect_to ifate_customer_index_path
    end
  end

  # http://127.0.0.1:3000/ifate/talent/show_statistic?cmd=pr&category_level=L1&category_id=category_1&item_id=item_1&trait_id=trait_4
  def show_statistic
    if (current_user.admin?) then
      get_talent_data(params)
      set_global_var(@category_level)
      cmd = params["cmd"]
      cmd = "raw" if cmd == nil
      show_all() if (cmd == "all")
      show_statistics() if (cmd == "statistic")
      show_pr() if (cmd == "pr")
      show_raw() if (cmd == "raw")      
    else
      redirect_to ifate_customer_index_path
    end
  end

  def api_called_count
    @api_callers = {"jianggong_#{Pm.t("account.company1")}" => -101, "sti_#{Pm.t("account.company1")}" => -102, "gpc_#{Pm.t("account.company1")}" => -103, "gpc_#{Pm.t("account.company2")}" => -104, "gpc" => "-103,-104"}
    @api_caller = params["api_caller"]
    @api_caller = @api_callers["gpc"] if @api_caller == nil
    @api_caller_arr = @api_caller.split(",")
    nDays = 0
    # nDays = -7 if params["start_date"] == nil
    @start_date = Xdate.timestamp_to_dbTime(params["start_date"],nDays)
    @end_date = Xdate.timestamp_to_dbTime(params["end_date"])

    if (@api_caller != nil && @api_caller != "") then
      @profate_payments = ProfatePayment.check_userid_in(@api_caller_arr[0].to_i,0).created_at_between(@start_date,@end_date).group(:user_id,:p_type).order(:user_id,:p_type).size if @api_caller_arr.length == 1
      @profate_payments = ProfatePayment.check_userid_in(@api_caller_arr[0].to_i,@api_caller_arr[1].to_i).created_at_between(@start_date,@end_date).group(:user_id,:p_type).order(:user_id,:p_type).size if @api_caller_arr.length == 2
    else
      @profate_payments = ProfatePayment.created_at_between(@start_date,@end_date).group(:user_id,:p_type).order(:user_id,:p_type).size
    end
    @hOut = {}
    @profate_payments.each do |pp|
      caller_id = pp[0][0]
      api_name = pp[0][1]
      count = pp[1]

      @hOut["#{@api_callers.key(caller_id)}"] = {} if @hOut["#{@api_callers.key(caller_id)}"] == nil
      @hOut["#{@api_callers.key(caller_id)}"][api_name] = count
    end

  # <% @profate_payments.each do |pp| %>
  #   <%= "#{pp[0]} : #{pp[1]}" %>
  #   </br>
  # <% end %>
  # <br>
  end
  protected
  def get_ts_rate(obj_talent,pan_type,h)
    a = obj_talent.get_ts_default_rate(pan_type)
    @rate_1,@rate_2,@rate_3 = a[0],a[1],a[2]
    @rate_1 = h["rate_1"].to_i if h["rate_1"] != nil && h["rate_1"] != ""
    @rate_2 = h["rate_2"].to_i if h["rate_2"] != nil && h["rate_2"] != ""
    @rate_3 = h["rate_3"].to_i if h["rate_3"] != nil && h["rate_3"] != ""
    return [@rate_1,@rate_2,@rate_3]
  end

  # http://127.0.0.1:3000/ifate/talent/show_all?cmd=statistic&trait_code=A11
  def show_all()
    out_all = {}

    show_statistics()
    out_all.merge!(@out)

    show_pr()
    out_all.merge!(@out)

    show_raw()
    out_all.merge!(@out)

    @out = out_all.clone
  end
  # http://127.0.0.1:3000/ifate/talent/show_statistic?cmd=statistic&trait_code=A11
  def show_statistics()
      obj_talent = Talent.new
      final = obj_talent.wc_get_statistic(@category_id,@category_level)

      key = Talent.get_talent_key(@category_level,my_current_lang()) 
      hTalent = Pm.getSystemValue_hash(key)
      h_ParSetting = hTalent["par_setting"]
      c_idx,i_idx,t_idx = obj_talent.wc_find_cit_index2(h_ParSetting,@category_id,@item_id,@trait_id)

      @out = {}

      @out["trait_code"] = @talent["category"][@category_id]["item"][@item_id]["trait"][@trait_id]["trait_code"]
      @out["max"] = final["max"][i_idx][t_idx]
      @out["min"] = final["min"][i_idx][t_idx]
      @out["avg"] = final["avg"][i_idx][t_idx]
      @out["mid_25"] = final["mid_25"][i_idx][t_idx]
      @out["mid"] = final["mid"][i_idx][t_idx]
      @out["mid_75"] = final["mid_75"][i_idx][t_idx]
  end
  def show_pr()
      obj_talent = Talent.new
      pr_value = obj_talent.wc_get_pr2(@category_id,@category_level)

      key = Talent.get_talent_key(@category_level,my_current_lang()) 
      hTalent = Pm.getSystemValue_hash(key)
      h_ParSetting = hTalent["par_setting"]
      c_idx,i_idx,t_idx = obj_talent.wc_find_cit_index2(h_ParSetting,@category_id,@item_id,@trait_id)
      @out = {}
      # i = pr_value[i_idx][t_idx].keys.index("86.97")
      # @out["pr"] = pr_value[i_idx][t_idx].values[i] if i != nil
      # @out["i"] = i
      @out["trait_code"] = @talent["category"][@category_id]["item"][@item_id]["trait"][@trait_id]["trait_code"]
      @out["pr_values"] = pr_value[i_idx][t_idx]
  end
  # http://127.0.0.1:3000/ifate/talent/show_statistic?cmd=pr&category_level=L1&category_id=category_1&item_id=item_1&trait_id=trait_4
  def show_raw()
      obj_talent = Talent.new
      h_final = Talent.get_category_statistic(@category_id,@category_level)
      talent_key = Talent.get_talent_score_key(@category_level)
      h_final[@category_id]["raw"] = Talent.get_raw_from_testdb(talent_key,@category_id,@talent,@category_level)
      # h_final = Pm.findTestDbHashValue_key("talent_score_statistic")
      raw_value = h_final[@category_id]["raw"]

      key = Talent.get_talent_key(@category_level,my_current_lang()) 
      hTalent = Pm.getSystemValue_hash(key)
      h_ParSetting = hTalent["par_setting"]
      c_idx,i_idx,t_idx = obj_talent.wc_find_cit_index2(h_ParSetting,@category_id,@item_id,@trait_id)
      @out = {}
      @out["trait_code"] = @talent["category"][@category_id]["item"][@item_id]["trait"][@trait_id]["trait_code"]
      @out["raw_values"] = raw_value[i_idx][t_idx].sort.reverse
  end
  def trait_code_2_c_i_t(talent,trait_code,category_id,item_id,trait_id)
    category_id = talent["category"].keys[0] if category_id == nil && talent != nil && talent["category"] != nil && talent["category"].keys.length > 0
    item_id = talent["category"][category_id]["item"].keys[0] if item_id == nil && talent != nil && talent["category"] != nil && talent["category"][category_id] != nil && talent["category"][category_id]["item"].keys.length > 0
    trait_id = talent["category"][category_id]["item"][item_id]["trait"].keys[0] if trait_id == nil && talent["category"] != nil && talent["category"][category_id] != nil && talent["category"][category_id]["item"] != nil && talent["category"][category_id]["item"][item_id] != nil && talent["category"][category_id]["item"][item_id]["trait"].keys.length > 0
    return category_id,item_id,trait_id if trait_code == nil || trait_code == ""
    talent["category"].keys.each_with_index do |c_id,i|
      talent["category"][c_id]["item"].keys.each_with_index do |i_id,i|
        talent["category"][c_id]["item"][i_id]["trait"].keys.each_with_index do |t_id,j|
          if (trait_code == talent["category"][c_id]["item"][i_id]["trait"][t_id]["trait_code"]) then
            return c_id,i_id,t_id
          end
        end
      end
    end
    return category_id,item_id,trait_id
  end
  def backup_talent_par(current_lang=Cfate::DEFAULT_LANG)
    h = read_talent_data(@category_level,current_lang)
    save_backup_talent_data(h,@category_level,current_lang)
  end
  def restore_talent_par(current_lang=Cfate::DEFAULT_LANG)
    h = read_backup_talent_data(@category_level,current_lang)
    save_talent_data(h,@category_level,current_lang)
  end
  def restore_to_all_talent_par(current_lang=Cfate::DEFAULT_LANG)
    h = read_backup_talent_data(@category_level,current_lang)
    save_talent_data(h,@category_level,"zh-TW")
    save_talent_data(h,@category_level,"zh-CN")
  end
  def restore_talent_par_only(current_lang=Cfate::DEFAULT_LANG)
    current_lang2 = "zh-CN" if current_lang == "zh-TW"
    current_lang2 = "zh-TW" if current_lang == "zh-CN"
    h = read_backup_talent_data(@category_level,current_lang)
    save_talent_data_par_only(h,@category_level,current_lang2) if h != {}
  end
  def multi_thread_getUsersScore(nPanType,hUserInfo,hUserDefData,n_years=7)
    ts_s = Xdate.make_timestamp_hash(hUserInfo["timestamp_user_start"])
    ts_t = Xdate.make_timestamp_hash(hUserInfo["timestamp_user_stop"])

    nYears = n_years

    while (ts_s <= ts_t) do
      ts_t1 = Xdate.next_timestamp_years(ts_s,nYears)
      ts_s1 = ts_t1
      ts_t1 = Xdate.next_timestamp_days(ts_t1,-1) #12/31
      ts_t1 = ts_t if ts_t1 > ts_t
      hUserInfo["timestamp_user_start"] = get_user_info(ts_s)
      hUserInfo["timestamp_user_stop"] = get_user_info(ts_t1)
      MeenForum::BgWorker.perform_async("getUsersScore",nPanType,hUserInfo,hUserDefData)
      ts_s = ts_s1
    end
  end
  public
  def default_score_data
    save_session_data()
    h = params["submit_data"]
    if (h == nil) then
      h = params
    end
    @timestamp_user_start = h["timestamp_user_start"]
    @timestamp_user_stop = h["timestamp_user_stop"]
    # @timestamp_user = "20190918233800M"
    nPanType = h["pan_type"].to_i
    nPanType = Cfate::PAN_NORMAL if nPanType == nil
    @timestamp_udt = h["timestamp_udt"]
    hUserDefData = get_user_def_data(@timestamp_udt)
    hUserDefData["timestamp_udt"] = @timestamp_udt
    getPanType(nPanType)

    hUserInfo = get_user_info(@timestamp_user_start)
    hUserInfo["timestamp_user_start"] = get_user_info(@timestamp_user_start)
    hUserInfo["timestamp_user_stop"] = get_user_info(@timestamp_user_stop)
    hUserDefData["category_level"] = @category_level
    if (@timestamp_user_start != nil) then
      @t_start = Xdate.GetNowRails()
      obj_talent = Talent.new
      obj_talent.save_default_all_users(nPanType,hUserInfo,hUserDefData)
      @t_stop = Xdate.GetNowRails()
    end
  end

  # 1，計算所有數值by category; category_score api;
  # 2，計算統計資料 get_score_statistic，包含 max,min,avg,mid,pr,raw，寫回system_par; cmd=c
  # 3，輸出raw data; cmd=wr
  # 4，顯示統計資料，包含 max,min,avg,mid,pr; cmd=t
  # 5，取得PR值 get_pr_value_and_save，寫回Talent_Scores; cmd=pr
  def score_statistic
    save_session_data()
    # if ( current_user.email == "<EMAIL>" || current_user.email == "<EMAIL>" || current_user.email == "<EMAIL>" || current_user.id == 128) then
    if (current_user.admin?) then
      h = params["submit_data"]
      if (h == nil) then
        h = params
      end
      get_talent_data(h)
      set_global_var(@category_level)
      @category_id = h["category_id"]

      @final = {}
      @final[@category_id] = {}
      @final[@category_id]["max"] = nil
      @final[@category_id]["min"] = nil
      @final[@category_id]["avg"] = nil
      @final[@category_id]["mid"] = nil
      @final[@category_id]["raw"] = nil

      cmd = h["cmd"]
      if (cmd == "c_bg") then
        raw_exist_check = h["raw_exist_check"] == "f" ? false : true
        MeenForum::BgWorker.perform_async("get_score_statistic",@category_id,@talent,raw_exist_check,@category_level)
      elsif (cmd == "c") then
        raw_exist_check = h["raw_exist_check"] == "f" ? false : true
        obj_talent = Talent.new
        @final = obj_talent.get_score_statistic(@category_id,@talent,raw_exist_check,@category_level)
        @category_id = "category_1" if @category_id == nil || @category_id == ""
      elsif (cmd == "t") then
        trans_to_category()
      elsif (cmd == "pr") then
        timestamp_user_1 = h["timestamp_user_1"] #"1960010100f"
        timestamp_user_2 = h["timestamp_user_2"] #"1960013122m"
        pr_exist_check = h["pr_exist_check"] == "f" ? false : true
        obj_talent = Talent.new
        @final = obj_talent.get_pr_value_and_save(@category_id,@talent,pr_exist_check,timestamp_user_1,timestamp_user_2,@category_level)
        @category_id = "category_1" if @category_id == nil || @category_id == ""
      elsif (cmd == "pr_bg") then
        timestamp_user_1 = h["timestamp_user_1"] #"1960010100f"
        timestamp_user_2 = h["timestamp_user_2"] #"1960013122m"
        pr_exist_check = h["pr_exist_check"] == "f" ? false : true
        MeenForum::BgWorker.perform_async("get_pr_value_and_save",@category_id,@talent,pr_exist_check,timestamp_user_1,timestamp_user_2,@category_level)
      elsif (cmd == "wr") then
        write_raw_to_file()
      end
    else
      redirect_to ifate_customer_index_path
    end
  end
  def talent_scores
    save_session_data()
    # if ( current_user.email == "<EMAIL>" || current_user.email == "<EMAIL>" || current_user.email == "<EMAIL>" || current_user.id == 128) then
    if (current_user.admin?) then
      h = params["submit_data"]
      if (h == nil) then
        h = params
      end
      get_talent_data(h)
      set_global_var(@category_level)
      @talent_scores = Talent.get_talent_scores(h,@category_level)
    else
      redirect_to ifate_customer_index_path
    end
  end
  def main
    save_session_data()
    # if ( current_user.email == "<EMAIL>" || current_user.email == "<EMAIL>" || current_user.email == "<EMAIL>" || current_user.id == 128) then
    if (current_user.admin?) then
      h = params["submit_data"]
      if (h == nil) then
        h = params
      end
      get_talent_data(h)
      cmd = h["cmd"]
      if (cmd == "u") then
        if (current_user.email == "<EMAIL>") then
          update_talent_pars(my_current_lang())
        end
      end
      set_global_var(@category_level)
    else
      redirect_to ifate_customer_index_path
    end
  end
  protected
  def item_trait_id(h,category_level)
    if (category_level == "L1") then
      item_trait_id_L1(h,category_level)
    elsif (category_level == "L2") then
      item_trait_id_L2(h,category_level)
    elsif (category_level == "L3") then
      item_trait_id_L3(h,category_level)
    elsif (category_level == "MP") then
      item_trait_id_MP(h,category_level)
    end
  end
  def item_trait_id_L1(h,category_level)
    @item_id = h["item_id"]
    @trait_id = h["trait_id"]
    # @category_id = "" if @category_id == nil
    @item_id = "" if @item_id == nil
    @trait_id = "" if @trait_id == nil || @item_id == ""
    @trait_id_hash = {} if @item_id == ""
  end
  def update_talent_pars(current_lang=Cfate::DEFAULT_LANG)
    if (@category_level == "L1") then
      @talent = read_talent_data("L1",current_lang)
      update_new_par_setting_L1()
      save_talent_data(@talent,"L1",current_lang)
    elsif (@category_level == "L2") then
      @talent_L2 = read_talent_data("L2",current_lang)
      update_new_par_setting_L2()
      save_talent_data(@talent_L2,"L2",current_lang)
    elsif (@category_level == "L3") then
      @talent_L3 = read_talent_data("L3",current_lang)
      update_new_par_setting_L3()
      save_talent_data(@talent_L3,"L3",current_lang)
    elsif (@category_level == "MP") then
      @talent_MP = read_talent_data("MP",current_lang)
      update_new_par_setting_MP()
      save_talent_data(@talent_MP,"MP",current_lang)
    end
  end
  def update_new_par_setting_L1()
    @par_setting = @talent["par_setting"]
    return if (@par_setting == nil || @par_setting == {})

    # weight_conditions 增加到20個
    # @par_setting.keys.each_with_index do |category_id,i|
    #     @par_setting[category_id].keys.each_with_index do |item_id,i|
    #       @par_setting[category_id][item_id].keys.each_with_index do |trait_id,i|
    #         @par_setting[category_id][item_id][trait_id].keys.each_with_index do |star_for_rule,i|
    #           n = 25 - @par_setting[category_id][item_id][trait_id][star_for_rule]["star"]["weight_condition"].length
    #           if (n > 0) then
    #             weight_conditions = Array.new(n) {{"wc_star" => "", "wc_weight" => 0, "weight_condition" => []}}
    #             @par_setting[category_id][item_id][trait_id][star_for_rule]["star"]["weight_condition"] += weight_conditions
    #           end
    #           @par_setting[category_id][item_id][trait_id][star_for_rule]["shenggong_weight"] = shenggong_new() if @par_setting[category_id][item_id][trait_id][star_for_rule]["shenggong_weight"] == nil
    #         end
    #       end
    #     end
    # end

    # weight_conditions 20增加到25個, sihua 5 -> 8 2020/1/5 Peter
    @par_setting.keys.each_with_index do |category_id,i|
        @par_setting[category_id].keys.each_with_index do |item_id,i|
          @par_setting[category_id][item_id].keys.each_with_index do |trait_id,i|
            @par_setting[category_id][item_id][trait_id].keys.each_with_index do |star_for_rule,i|
              # 星性條件
              n = 25 - @par_setting[category_id][item_id][trait_id][star_for_rule]["star"]["weight_condition"].length
              if (n > 0) then
                weight_conditions = Array.new(n) {{"wc_star" => "", "wc_weight" => 0, "weight_condition" => []}}
                @par_setting[category_id][item_id][trait_id][star_for_rule]["star"]["weight_condition"] += weight_conditions
              end

              # 四化條件
              @par_setting[category_id][item_id][trait_id][star_for_rule]["sihua"]["sihua_star_weight"] = [] if @par_setting[category_id][item_id][trait_id][star_for_rule]["sihua"]["sihua_star_weight"] == nil
              n = 8 - @par_setting[category_id][item_id][trait_id][star_for_rule]["sihua"]["sihua_star_weight"].length
              if (n > 0) then
                sihua_weight_conditions = Talent.sihua_star_weight_par_setting_new2(n)
                @par_setting[category_id][item_id][trait_id][star_for_rule]["sihua"]["sihua_star_weight"] += sihua_weight_conditions
              end
            end
          end
        end
    end

    @talent["par_setting"] = @par_setting
  end
  def trans_to_category()
    h_final = Talent.get_category_statistic(@category_id,@category_level)
    talent_key = Talent.get_talent_score_key(@category_level)
    h_final[@category_id]["raw"] = Talent.get_raw_from_testdb(talent_key,@category_id,@talent,@category_level)
    # h_final = Pm.findTestDbHashValue_key("talent_score_statistic")
    @final = {}
    @final[@category_id] = {}
    @final[@category_id]["max"] = show_category_final(h_final[@category_id]["max"])
    @final[@category_id]["min"] = show_category_final(h_final[@category_id]["min"])
    @final[@category_id]["avg"] = show_category_final(h_final[@category_id]["avg"])
    @final[@category_id]["mid_25"] = show_category_final(h_final[@category_id]["mid_25"])
    @final[@category_id]["mid"] = show_category_final(h_final[@category_id]["mid"])
    @final[@category_id]["mid_75"] = show_category_final(h_final[@category_id]["mid_75"])
    @final[@category_id]["pr"] = h_final[@category_id]["pr"][0][0]
    @final[@category_id]["raw"] = h_final[@category_id]["raw"][0][0].sort.reverse
  end
  # show pr
  def raw_category()
    @final = Talent.get_category_statistic(@category_id,@category_level)
    talent_key = Talent.get_talent_score_key(@category_level)
    @final[@category_id]["raw"] = Talent.get_raw_from_testdb(talent_key,@category_id,@talent,@category_level)
    # @final = Pm.findTestDbHashValue_key("talent_score_statistic")
    val = @final[@category_id]["raw"]
    # @final[@category_id]["raw"] = val[0][0].length
    @final[@category_id]["raw"] = Talent.cal_pr_value(val[0][0])
  end
  def show_category_final(val)
    a = []
    category_desc = @talent["category"][@category_id]["desc"]
    @talent["category"][@category_id]["item"].keys.each_with_index do |item_id,i|
      item_desc = @talent["category"][@category_id]["item"][item_id]["desc"]
      @talent["category"][@category_id]["item"][item_id]["trait"].keys.each_with_index do |trait_id,j|
        trait_desc = @talent["category"][@category_id]["item"][item_id]["trait"][trait_id]["desc"]
        value = val[i][j]
        a.push("#{category_desc},#{item_desc},#{trait_desc},#{value}")
      end
    end
    return a
  end
  def write_raw_to_file()
    h_final = Talent.get_category_statistic(@category_id,@category_level)
    ts_user = Pm.findTestDbHashValue_key("raw_#{@category_level}_timestamp_user")
 
    talent_key = Talent.get_talent_score_key(@category_level)
    h_final[@category_id]["raw"] = Talent.get_raw_from_testdb(talent_key,@category_id,@talent,@category_level)
    # h_final = Pm.findTestDbHashValue_key("talent_score_statistic")
    raw = h_final[@category_id]["raw"]
  # puts "raw : #{raw[0][0]}"
  # puts "raw : #{raw.length} / #{raw[0].length} / #{raw[0][0].length}"
    file = File.open("talent_scores_raw.csv", "w")

    title = []
    title.push("timestamp_user")
    category_desc = @talent["category"][@category_id]["desc"]
    @talent["category"][@category_id]["item"].keys.each_with_index do |item_id,i|
      item_desc = @talent["category"][@category_id]["item"][item_id]["desc"]
      @talent["category"][@category_id]["item"][item_id]["trait"].keys.each_with_index do |trait_id,j|
        trait_desc = @talent["category"][@category_id]["item"][item_id]["trait"][trait_id]["desc"]
        # title.push("#{category_desc}/#{item_desc}/#{trait_desc}")
        title.push("#{@talent["category"][@category_id]["item"][item_id]["trait"][trait_id]["trait_code"]}")
      end
    end
    file.puts(title.join(","))

    v = []
    # raw_b = raw.collect {|x| x.collect {|y| y.sort if y != nil}}
    raw_b = raw.clone
  # puts "raw_b : #{raw_b.length} / #{raw_b[0].length} / #{raw_b[0][0].length}"
    (0...raw[0][0].length).each do |i|
      v = []
      v.push(ts_user[i])
      raw_b.each_with_index do |ai,ai_i|
        ai.each_with_index do |aj,aj_j|
  # puts "raw 3 : #{ai_i}/#{aj_j}/#{aj}"
          v.push(aj[i])
        end
      end
      file.puts(v.join(","))
    end
    file.close
    @final = {}
    @final[@category_id] = {}
    @final[@category_id]["max"] = raw[0][0].sort[0..99]
    @final[@category_id]["mid"] = raw_b[0][0][0..99]
    @final[@category_id]["raw"] = "talent_scores_raw.csv"
  end
  def write_raw_to_file_3()
    h_final = Talent.get_category_statistic(@category_id,@category_level)
    talent_key = Talent.get_talent_score_key(@category_level)
    h_final[@category_id]["raw"] = Talent.get_raw_from_testdb(talent_key,@category_id,@talent,@category_level)
    # h_final = Pm.findTestDbHashValue_key("talent_score_statistic")
    raw = h_final[@category_id]["raw"]
    file = File.open("talent_scores_raw.csv", "w")

    title = []
    category_desc = @talent["category"][@category_id]["desc"]
    @talent["category"][@category_id]["item"].keys.each_with_index do |item_id,i|
      item_desc = @talent["category"][@category_id]["item"][item_id]["desc"]
      @talent["category"][@category_id]["item"][item_id]["trait"].keys.each_with_index do |trait_id,j|
        trait_desc = @talent["category"][@category_id]["item"][item_id]["trait"][trait_id]["desc"]
        title.push("#{category_desc}/#{item_desc}/#{trait_desc}")
      end
    end
    file.puts(title.join(","))

    v = []
    raw_b = raw.collect {|x| x.collect {|y| y.sort}}
    raw_a = raw_b.collect {|x| x.transpose}
    (0...raw[0][0].length).each do |i|
    # (0...4).each do |i|
      (0...raw_a.length).each do |j|
        v.push(raw_a[j][i].join(","))
      end
      file.puts(v.join(","))
      v = []
    end
    file.close
    @final = {}
    @final[@category_id] = {}
    @final[@category_id]["mid"] = raw[0][0].length
    @final[@category_id]["raw"] = "talent_scores_raw.csv"
  end
  def write_raw_to_file_2()
    h_final = Talent.get_category_statistic(@category_id,@category_level)
    talent_key = Talent.get_talent_score_key(@category_level)
    h_final[@category_id]["raw"] = Talent.get_raw_from_testdb(talent_key,@category_id,@talent,@category_level)
    # h_final = Pm.findTestDbHashValue_key("talent_score_statistic")
    val = h_final[@category_id]["raw"]
    file = File.open("talent_scores_raw.csv", "w")
    category_desc = @talent["category"][@category_id]["desc"]
    @talent["category"][@category_id]["item"].keys.each_with_index do |item_id,i|
      item_desc = @talent["category"][@category_id]["item"][item_id]["desc"]
      @talent["category"][@category_id]["item"][item_id]["trait"].keys.each_with_index do |trait_id,j|
        trait_desc = @talent["category"][@category_id]["item"][item_id]["trait"][trait_id]["desc"]
        value = val[i][j]
        file.puts("#{category_desc};#{item_desc};#{trait_desc};#{value.sort.join(",")}")
      end
    end
    file.close
    @final = {}
    @final[@category_id] = {}
    @final[@category_id]["mid"] = val[0][0].length
    @final[@category_id]["raw"] = "talent_scores_raw.csv"
  end
  def getPanType(nPanType=Cfate::PAN_NORMAL,start_pan_type=Cfate::PAN_NORMAL,end_pan_type=Cfate::PAN_FLOWMIN)
    @hPanType = Hash.new
    if (nPanType == nil) then
      @PanType = Cfate::PAN_NORMAL
    else
      @PanType = nPanType
    end

    (start_pan_type..end_pan_type).each do |nPanType|
      @hPanType[Cfate.GetPanName(nPanType)] = nPanType
    end
  end
  def get_user_info(timestamp)
    hUserInfo = {}
    y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp)
    hUserInfo[Cfate::WYear] = y
    hUserInfo[Cfate::WMonth] = m
    hUserInfo[Cfate::WDate] = d
    hUserInfo[Cfate::WHour] = h
    hUserInfo[Cfate::WMinute] = min
    hUserInfo[Cfate::Sex] = sex == 1 ? false : true
    hUserInfo[Cfate::Name] = timestamp
    hUserInfo[Cfate::Multiple_births] = mb
    return hUserInfo
  end
  def get_user_def_data(timestamp)
    hUserDefData = Hash.new
    y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp)
    ey,em,ed,bLeapMonth = Xdate.West2East(y,m,d)
    hUserDefData[Cfate::EYear] = ey
    hUserDefData[Cfate::EMonth] = em
    hUserDefData[Cfate::EDate] = ed
    hUserDefData[Cfate::LeapMonth] = bLeapMonth
    hUserDefData[Cfate::WHour] = h
    hUserDefData[Cfate::WMinute] = min
    hUserDefData["my_current_lang"] = my_current_lang()
    return hUserDefData
  end

  def get_setup_sihua_rule_pars(h)
  	@sihua_house_weight = {}
    ["hua_lu","hua_quan","hua_ke","hua_ji"].each do |hua|
    	@sihua_house_weight[hua] = Array.new(12)
  		(1..12).each do |nHouseId|
  			@sihua_house_weight[hua][nHouseId - 1] = h["#{hua}_house_weight_#{nHouseId}"]
  		end
  	end
    @sihuas_weight = h["sihuas_weight"]

    @sihua_star_weights = Array.new(8)
    (1..8).each do |i|
      @sihua_star_weights[i-1] = {}
      @sihua_star_weights[i-1]["sihua_wc_star"] = h["sihua_wc_star_#{i}"]
      @sihua_star_weights[i-1]["sihua_wc_weight"] = h["sihua_wc_weight_#{i}"]
      @sihua_star_weights[i-1]["sihua_star_condition"] = h["sihua_star_condition_#{i}"]
      @sihua_star_weights[i-1]["sihua_star_condition"] = "sihua_same_house" if h["sihua_star_condition_#{i}"] == nil
      
      if (h["sihua_wc_condition_#{i}"] == nil) then
        @sihua_star_weights[i-1]["sihua_wc_condition"] = []
      else
        @sihua_star_weights[i-1]["sihua_wc_condition"] = h["sihua_wc_condition_#{i}"]
      end
    end

    @sihua_sign = Array.new(4)
    ["hua_lu","hua_quan","hua_ke","hua_ji"].each_with_index do |hua,i|
      @sihua_sign[i] = h["sihua_sign_#{hua}"]
    end
    @sihua_sign_exception_house = Talent.sihua_sign_exception_house_new()
    ["hua_lu","hua_quan","hua_ke","hua_ji"].each_with_index do |hua,i|
      (0..3).each do |j|
        @sihua_sign_exception_house[i][j] = h["sihua_sign_exception_house_#{hua}_#{j}"]
      end
    end
  end
  def setup_L1()
    cmd = params["cmd"]
    if (cmd != nil) then
      setup_cmd_L1(cmd,params)
    end
    @h_copy = Pm.findTestDbHashValue_key("talent_copy")
  end
  def setup_cmd_L1(cmd,h)
    target = h["target"]
    if (cmd == "c") then # copy
      h_copy = get_one_copy(target)
      h_copy["target"] = target
      Pm.updateTestDbHashValue_key("talent_copy",h_copy)
    elsif (cmd == "p") then # paste
      h_copy = Pm.findTestDbHashValue_key("talent_copy")
      target = h_copy["target"]
      set_one_copy(target,h_copy)
      Pm.updateTestDbHashValue_key("talent_copy",{})
      save_talent_data(@talent,"L1",my_current_lang())
      get_talent_data(h)
    end 
  end
  def get_one_copy(target)
    h_copy = get_one_copy_category(target)

    h_copy = get_one_copy_trait_def(target,h_copy)

    h_copy = get_one_copy_par_setting(target,h_copy)

    return h_copy
  end
  def get_one_copy_category(target)
    h_copy = {}
    if (["category","item","trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id] = @talent["category"][@category_id]
    end
    if (["item","trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id] = {}
      h_copy["category"][@category_id]["item"] = {}
      h_copy["category"][@category_id]["item"][@item_id] = @talent["category"][@category_id]["item"][@item_id]
    end
    if (["trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id] = {}
      h_copy["category"][@category_id]["item"] = {}
      h_copy["category"][@category_id]["item"][@item_id] = {}
      h_copy["category"][@category_id]["item"][@item_id]["trait"] = {}
      h_copy["category"][@category_id]["item"][@item_id]["trait"][@trait_id] = @talent["category"][@category_id]["item"][@item_id]["trait"][@trait_id]
    end
    return h_copy
  end
  def get_one_copy_trait_def(target,h_copy)
    h_copy["trait_def"] = {}
    if ("category" == target) then
      h_copy["trait_def"][@category_id] = @talent["trait_def"][@category_id]
    elsif ("item" == target) then
      h_copy["trait_def"] = {}
      h_copy["trait_def"][@category_id] = {}
      h_copy["trait_def"][@category_id][@item_id] = @talent["trait_def"][@category_id][@item_id]
    elsif ("trait" == target) then
      h_copy["trait_def"] = {}
      h_copy["trait_def"][@category_id] = {}
      h_copy["trait_def"][@category_id][@item_id] = {}
      h_copy["trait_def"][@category_id][@item_id][@trait_id] = @talent["trait_def"][@category_id][@item_id][@trait_id]
    end
    return h_copy
  end
  def get_one_copy_par_setting(target,h_copy)
    h_copy["par_setting"] = {}
    if ("category" == target) then
      h_copy["par_setting"][@category_id] = @talent["par_setting"][@category_id]
    elsif ("item" == target) then
      h_copy["par_setting"] = {}
      h_copy["par_setting"][@category_id] = {}
      h_copy["par_setting"][@category_id][@item_id] = @talent["par_setting"][@category_id][@item_id]
    elsif ("trait" == target) then
      h_copy["par_setting"] = {}
      h_copy["par_setting"][@category_id] = {}
      h_copy["par_setting"][@category_id][@item_id] = {}
      h_copy["par_setting"][@category_id][@item_id][@trait_id] = @talent["par_setting"][@category_id][@item_id][@trait_id]
    end
    return h_copy
  end
  def set_one_copy(target,h_copy)
    t_ids,h_ids = set_one_copy_category(target,h_copy)
    set_one_copy_trait_def(target,h_copy,t_ids,h_ids)
    set_one_copy_par_setting(target,h_copy,t_ids,h_ids)
  end
  def set_one_copy_category(target,h_copy)
    category_id_copy = h_copy["category"].keys[0]
    i = find_new_index("category_",@talent["category"].keys)
    category_id_new = "category_#{i}"
    if ("category" == target) then
      @talent["category"][category_id_new] = h_copy["category"][category_id_copy]
      return [category_id_new,0,0],[category_id_copy,0,0]
    end
    item_id_copy = h_copy["category"][category_id_copy]["item"].keys[0]
    i = find_new_index("item_",@talent["category"][@category_id]["item"].keys)
    item_id_new = "item_#{i}"
    if ("item" == target) then
      @talent["category"][@category_id]["item"][item_id_new]= h_copy["category"][category_id_copy]["item"][item_id_copy]
      return [@category_id,item_id_new,0],[category_id_copy,item_id_copy,0]
    end
    trait_id_copy = h_copy["category"][category_id_copy]["item"][item_id_copy]["trait"].keys[0]
    i = find_new_index("trait_",@talent["category"][@category_id]["item"][@item_id]["trait"].keys)
    trait_id_new = "trait_#{i}"
    if ("trait" == target) then
      @talent["category"][@category_id]["item"][@item_id]["trait"][trait_id_new] = h_copy["category"][category_id_copy]["item"][item_id_copy]["trait"][trait_id_copy]
      return [@category_id,@item_id,trait_id_new],[category_id_copy,item_id_copy,trait_id_copy]
    end
    return [nil,nil,nil],[nil,nil,nil]
  end
  def set_one_copy_trait_def(target,h_copy,t_ids,h_ids)
    if ("category" == target) then
      @talent["trait_def"][t_ids[0]] = h_copy["trait_def"][h_ids[0]]
    elsif ("item" == target) then
      @talent["trait_def"][t_ids[0]][t_ids[1]] = h_copy["trait_def"][h_ids[0]][h_ids[1]]
    elsif ("trait" == target) then
      @talent["trait_def"][t_ids[0]][t_ids[1]][t_ids[2]] = h_copy["trait_def"][h_ids[0]][h_ids[1]][h_ids[2]]
    end
  end
  def set_one_copy_par_setting(target,h_copy,t_ids,h_ids)
    if ("category" == target) then
      @talent["par_setting"][t_ids[0]] = h_copy["par_setting"][h_ids[0]]
    elsif ("item" == target) then
      @talent["par_setting"][t_ids[0]][t_ids[1]] = h_copy["par_setting"][h_ids[0]][h_ids[1]]
    elsif ("trait" == target) then
      @talent["par_setting"][t_ids[0]][t_ids[1]][t_ids[2]] = h_copy["par_setting"][h_ids[0]][h_ids[1]][h_ids[2]]
    end
  end

  def set_global_var(category_level)
    if (category_level == "L2") then
      set_global_var_L2(category_level)
    elsif (category_level == "L3") then
      set_global_var_L3(category_level)
    elsif (category_level == "MP") then
      set_global_var_MP(category_level)
    end
  end  

  def find_new_index(pattern,a)
    i = 0
    a.each do |n|
      i1 = get_index(pattern,n)
      i = i1 if (i1 > i)
    end
    return i + 1
  end
  def get_index(pattern,n)
    i = n.gsub(pattern,"")
    return i.to_i
  end
  def setup_star_rule_cmd(cmd,hPar)
  	get_setup_star_rule_pars(hPar)
    get_setup_sihua_rule_pars(hPar)
  	hSfr = @par_setting[@category_id][@item_id][@trait_id][@star_for_rule] == nil ? {} : @par_setting[@category_id][@item_id][@trait_id][@star_for_rule]
  	if (cmd == "a") then
  		@par_setting[@category_id][@item_id][@trait_id][@star_for_rule] = rule_par_setting(hSfr,@star_for_rule,@star_weight,@star_house_weight,@stars_weight,@weight_conditions,@sihua_house_weight,@sihuas_weight,@sihua_star_weights,@shenggong_weight,@sihua_sign,@sihua_sign_exception_house)
  	elsif (cmd == "d") then
  		@par_setting[@category_id][@item_id][@trait_id].delete(@star_for_rule)
  	elsif (cmd == "e") then
      org_star_for_rule = hPar["org_star_for_rule"]
      # if (@star_for_rule == org_star_for_rule) then
      # 不處理更換star_for_rule的情形，直接變成新增一個，若star_for_rule相同，就覆蓋。
      if (true) then
        @par_setting[@category_id][@item_id][@trait_id][@star_for_rule] = rule_par_setting(hSfr,@star_for_rule,@star_weight,@star_house_weight,@stars_weight,@weight_conditions,@sihua_house_weight,@sihuas_weight,@sihua_star_weights,@shenggong_weight,@sihua_sign,@sihua_sign_exception_house)
      else
        keys = @par_setting[@category_id][@item_id][@trait_id].keys
        h = {}
        keys.each do |k|
          if (k == org_star_for_rule) then
            h[@star_for_rule] = rule_par_setting(hSfr,k,@star_weight,@star_house_weight,@stars_weight,@weight_conditions,@sihua_house_weight,@sihuas_weight,@sihua_star_weights,@shenggong_weight,@sihua_sign,@sihua_sign_exception_house)
            h[@star_for_rule]["star_for_rule"] = @star_for_rule
          else
            h[k] = @par_setting[@category_id][@item_id][@trait_id][k]
          end            
        end
        @par_setting[@category_id][@item_id][@trait_id] = h
      end
    elsif (cmd == "c") then # copy
      h_copy = {}
      h_copy["data"] = @par_setting[@category_id][@item_id][@trait_id][@star_for_rule]
      h_copy["target"] = "setup_star_rule_L1"
      Pm.updateTestDbHashValue_key("setup_star_rule_L1_copy",h_copy)
    elsif (cmd == "p") then # paste
      h_copy = Pm.findTestDbHashValue_key("setup_star_rule_L1_copy")
      return if h_copy == nil || h_copy == {}
      target = h_copy["target"]
      @par_setting[@category_id][@item_id][@trait_id][@star_for_rule] = h_copy["data"]
      @par_setting[@category_id][@item_id][@trait_id][@star_for_rule]["star_for_rule"] = @star_for_rule
      @par_setting[@category_id][@item_id][@trait_id][@star_for_rule]["star_weight"] = @star_weight
      @talent["par_setting"][@category_id][@item_id][@trait_id][@star_for_rule] = @par_setting[@category_id][@item_id][@trait_id][@star_for_rule]
      save_talent_data(@talent,"L1",my_current_lang())
      get_talent_data(hPar)
  	end	
  end
  def get_setup_star_rule_pars(h)
  	@star_for_rule = h["star_for_rule"]
  	@star_weight = h["star_weight"]
    @shenggong_weight = get_shenggong_weight(h)
  	@star_house_weight = Array.new(12)
  	(1..12).each do |nHouseId|
  		@star_house_weight[nHouseId - 1] = h["house_weight_#{nHouseId}"]
  	end
    @stars_weight = h["stars_weight"]
  	# @weight_conditions = Array.new(20) {{"wc_star" => "", "wc_weight" => 0, "weight_condition" => []}}
  	@weight_conditions = Array.new(25)
  	(1..25).each do |i|
  		@weight_conditions[i-1] = {}
  		@weight_conditions[i-1]["wc_star"] = h["wc_star_#{i}"]
  		@weight_conditions[i-1]["wc_weight"] = h["wc_weight_#{i}"]
  		if (h["wc_condition_#{i}"] == nil) then
  			@weight_conditions[i-1]["weight_condition"] = []
  		else
  			@weight_conditions[i-1]["weight_condition"] = h["wc_condition_#{i}"]
  		end
  	end
  end
  def get_shenggong_weight(h)
    shenggong_weight = shenggong_new()
    (0..2).each do |shenggong_weight_index|
      shenggong_weight[shenggong_weight_index]["use_shenggong"] = Cfate.ValueBoolCheck(h["sw_use_shenggong_#{shenggong_weight_index}"]) if h["sw_use_shenggong_#{shenggong_weight_index}"] != nil
      shenggong_weight[shenggong_weight_index]["weight"] = h["sw_weight_#{shenggong_weight_index}"].to_i
      shenggong_weight[shenggong_weight_index]["condition"] = h["sw_condition_#{shenggong_weight_index}"]
    end
    return shenggong_weight
  end

  def category_cmd(cmd,category,category_id)
    if (@category_level == "L1") then
      category_cmd_L1(cmd,category,category_id)
    elsif (@category_level == "L2") then
      category_cmd_L2(cmd,category,category_id)
    elsif (@category_level == "L3") then
      category_cmd_L3(cmd,category,category_id)
    elsif (@category_level == "MP") then
      category_cmd_MP(cmd,category,category_id)
    end
  end
  def category_cmd_L1(cmd,category,category_id)
  	if (cmd == "a") then
  	  category_add_L1(category)
  	elsif (cmd == "d") then
  	  category_remove_L1(category_id)
  	elsif (cmd == "e") then
  	  category_edit_L1(category_id,category)
  	elsif (cmd == "cc") then # change category
  	  category_change_L1(category_id,nil,nil)
  	end	
  end
  def category_add_L1(category)
  	if (category != nil) then
  	  @categorys.push(category)
      category_id = next_category_id(@category_ids.last)
  	  @category_ids.push(category_id)

  	  # add item
      @trait_def = {} if @trait_def == nil
      @trait_def[category_id] = {}

      @par_setting = {} if @par_setting == nil
      @par_setting[category_id] = {}

      @traits_desc[category_id] = {}
      @traits_score_desc[category_id] = {}
      @traits_code[category_id] = {}
  	end
  end
  def category_edit_L1(category_id,category)
  	if (category_id != nil && category != nil) then
  	  i = @category_ids.index(category_id)
  	  if (i == nil) then return end
  	  @categorys[i] = category
  	end
  end
  def next_category_id(category_id)
  	return "category_1" if (category_id == nil || category_id == "")
  	return "category_#{category_id.gsub("category_","").to_i + 1}"
  end
  def category_remove_L1(category_id)
  	if (category_id != nil) then
  		i = @category_ids.index(category_id)
  		if (i == nil) then return end
  		@categorys.delete_at(i)
  		@category_ids.delete_at(i)

   		# remove item
  		@items.delete(category_id)
  		@item_ids.delete(category_id)

 		# remove trait
  		@traits.delete(category_id)
  		@trait_ids.delete(category_id)

    	# remove trait_def
  		@trait_def.delete(category_id) if @trait_def != nil
      @par_setting.delete(category_id) if @par_setting != nil

      @traits_desc.delete(category_id) if @traits_desc != nil
      @traits_score_desc.delete(category_id) if @traits_score_desc != nil
      @traits_code.delete(category_id) if @traits_code != nil
  	end
  end

  def item_cmd(cmd,category_id,item,item_id)
    if (@category_level == "L1") then
      item_cmd_L1(cmd,category_id,item,item_id)
    elsif (@category_level == "L2") then
      item_cmd_L2(cmd,category_id,item,item_id)
    elsif (@category_level == "L3") then
      item_cmd_L3(cmd,category_id,item,item_id)
    elsif (@category_level == "MP") then
      item_cmd_MP(cmd,category_id,item,item_id)
    end
  end
  def item_cmd_L1(cmd,category_id,item,item_id)
  	if (cmd == "a") then
  	  item_add_L1(category_id,item)
  	elsif (cmd == "d") then
  	  item_remove_L1(category_id,item_id)
  	elsif (cmd == "e") then
  	  item_edit_L1(category_id,item_id,item)
  	elsif (cmd == "cc") then # change category
  	  category_change_L1(category_id,item_id,nil)
  	end	
  end
  def category_change_L1(category_id,item_id,trait_id)
  	@category_id = category_id if category_id != nil
  	@item_id = item_id if item_id != nil
  	@trait_id = trait_id if trait_id != nil

  	# get setting parameters
  end
  def item_add_L1(category_id,item)
  	if (item != nil) then
  	  @items[category_id].push(item) if @items[category_id] != nil
      item_id = next_item_id(@item_ids[category_id].last)
  	  @item_ids[category_id].push(item_id) if @item_ids[category_id] != nil

      @trait_def[category_id] = {} if @trait_def[category_id] == nil
      @trait_def[category_id][item_id] = {}

      @par_setting[category_id] = {} if @par_setting[category_id] == nil
      @par_setting[category_id][item_id] = {}

      @traits_desc[category_id][item_id] = {}
      @traits_score_desc[category_id][item_id] = {}
      @traits_code[category_id][item_id] = {}
  	end
  end
  def item_edit_L1(category_id,item_id,item)
  	if (item_id != nil && item != nil) then
  	  i = @item_ids[category_id].index(item_id)
  	  if (i == nil) then return end
  	  @items[category_id][i] = item
  	end
  end
  def next_item_id(item_id)
  	return "item_1" if (item_id == nil || item_id == "")
  	return "item_#{item_id.gsub("item_","").to_i + 1}"
  end
  def item_remove_L1(category_id,item_id)
  	if (item_id != nil) then
  		i = @item_ids[category_id].index(item_id)
  		if (i == nil) then return end
  		@items[category_id].delete_at(i)
  		@item_ids[category_id].delete_at(i)

  		# remove trait
  		@traits[category_id].delete(item_id)
  		@trait_ids[category_id].delete(item_id)

    	# remove @trait_def
      @trait_def[category_id].delete(item_id) if @trait_def[category_id] != nil
  		@par_setting[category_id].delete(item_id) if @par_setting[category_id] != nil

      @traits_desc[category_id].delete(item_id)
      @traits_score_desc[category_id].delete(item_id)
      @traits_code[category_id].delete(item_id)
  	end
  end

  # traits
  def trait_cmd(cmd,category_id,item_id,trait,trait_id,trait_code)
    if (@category_level == "L1") then
      trait_cmd_L1(cmd,category_id,item_id,trait,trait_id,trait_code)
    elsif (@category_level == "L2") then
      trait_cmd_L2(cmd,category_id,item_id,trait,trait_id,trait_code)
    elsif (@category_level == "L3") then
      trait_cmd_L3(cmd,category_id,item_id,trait,trait_id,trait_code)
    elsif (@category_level == "MP") then
      trait_cmd_MP(cmd,category_id,item_id,trait,trait_id,trait_code)
    end
  end
  def trait_cmd_L1(cmd,category_id,item_id,trait,trait_id,trait_code)
  	if (cmd == "a") then
  	  trait_add_L1(category_id,item_id,trait,trait_code)
  	elsif (cmd == "d") then
  	  trait_remove_L1(category_id,item_id,trait_id)
  	elsif (cmd == "e") then
  	  trait_edit_L1(category_id,item_id,trait_id,trait,trait_code)
  	elsif (cmd == "cc") then # change category
  	  category_change_L1(category_id,item_id,trait_id)
  	end	
  end
  def trait_add_L1(category_id,item_id,trait,trait_code)
  	if (trait != nil) then
  	  @traits[category_id][item_id].push(trait) if @traits[category_id][item_id] != nil
      trait_id = next_trait_id(@trait_ids[category_id][item_id].last)
  	  @trait_ids[category_id][item_id].push(trait_id) if @trait_ids[category_id][item_id] != nil
        
      @trait_def[category_id][item_id][trait_id] = trait_def_empty()
      @par_setting[category_id][item_id][trait_id] = par_setting_new()

      @traits_desc[category_id][item_id][trait_id] = ""
      @traits_score_desc[category_id][item_id][trait_id] = ""
      @traits_code[category_id][item_id][trait_id] = trait_code
  	end
  end
  def trait_edit_L1(category_id,item_id,trait_id,trait,trait_code)
  	if (trait_id != nil && trait != nil) then
  	  i = @trait_ids[category_id][item_id].index(trait_id)
  	  if (i == nil) then return end
  	  @traits[category_id][item_id][i] = trait
      @traits_code[category_id][item_id][trait_id] = trait_code
  	end
  end
  def next_trait_id(trait_id)
  	return "trait_1" if (trait_id == nil || trait_id == "")
  	return "trait_#{trait_id.gsub("trait_","").to_i + 1}"
  end
  def trait_remove_L1(category_id,item_id,trait_id)
  	if (trait_id != nil) then
  		i = @trait_ids[category_id][item_id].index(trait_id)
  		if (i == nil) then return end
  		@traits[category_id][item_id].delete_at(i)
  		@trait_ids[category_id][item_id].delete_at(i)

      @traits_desc[category_id][item_id].delete(trait_id)
      @traits_score_desc[category_id][item_id].delete(trait_id)
      @traits_code[category_id][item_id].delete(trait_id)
  		# remove @trait_def
      if (@trait_def[category_id] != nil && @trait_def[category_id][item_id] != nil) then
  		  @trait_def[category_id][item_id].delete(trait_id)
      end

      if (@par_setting[category_id] != nil && @par_setting[category_id][item_id] != nil) then
        @par_setting[category_id][item_id].delete(trait_id)
      end
  	end
  end

  def get_array(d)
  	if d == nil then
  		return []
  	else
  		return d
  	end
  end
  def get_hash(d)
  	if d == nil then
  		return {}
  	else
  		return d
  	end
  end
  def read_backup_talent_data(category_level="L1",current_lang=Cfate::DEFAULT_LANG)
    return read_talent_data_by_key("#{category_level}_Backup",current_lang)
  end
  def read_talent_data_by_key(k,current_lang=Cfate::DEFAULT_LANG)
    key = Talent.get_talent_key(k,current_lang)
    return Pm.getSystemValue_hash(key)
  end
  def save_backup_talent_data(h,category_level="L1",current_lang=Cfate::DEFAULT_LANG)
    change_org_backup_talent_data_key(category_level,current_lang)
    key = Talent.get_talent_backup_key(category_level,current_lang)
    Pm.setSystemValue_hash(key,h)
  end
  def read_talent_data(category_level="L1",current_lang=Cfate::DEFAULT_LANG)
    if (category_level == "L1") then
      return read_talent_data_L1(current_lang)
    elsif (category_level == "L2") then
      return read_talent_data_L2(current_lang)
    elsif (category_level == "L3") then
      return read_talent_data_L3(current_lang)
    elsif (category_level == "MP") then
      return read_talent_data_MP(current_lang)
    end
  end
  def read_talent_data_L1(current_lang=Cfate::DEFAULT_LANG)
    key = Talent.get_talent_key("L1",current_lang)
    return Pm.getSystemValue_hash(key)
  end
  def change_org_backup_talent_data_key(category_level="L1",current_lang=Cfate::DEFAULT_LANG)
    key_old = Talent.get_talent_backup_key(category_level,current_lang)
    key_new = Talent.get_talent_backup_copy_key(category_level,current_lang)
    return Pm.changeSystemValue_key(key_old,key_new)
  end
  def save_talent_data(h,category_level="L1",current_lang=Cfate::DEFAULT_LANG)
    if (category_level == "L1") then
      save_talent_data_L1(h,current_lang)
    elsif (category_level == "L2") then
      save_talent_data_L2(h,current_lang)
    elsif (category_level == "L3") then
      save_talent_data_L3(h,current_lang)
    elsif (category_level == "MP") then
      save_talent_data_MP(h,current_lang)
    end
  end
  def save_talent_data_L1(h,current_lang=Cfate::DEFAULT_LANG)
    key = Talent.get_talent_key("L1",current_lang)
    hOrg = Pm.getSystemValue_hash(key)

    if (h["version"] == nil) then
      h["version"] = {}
    end
    categorys = h["category"].keys
    categorys.each do |category_id|
      if (h["version"][category_id] == nil) || (!check_category_the_same(hOrg,h,category_id)) then
        h["version"][category_id] = Xdate.make_now_timestamp2()
      end
    end
  	Pm.setSystemValue_hash(key,h)

    value_categorys = categorys.join(",")
    Pm.setSystemValue("#{key}_categorys",value_categorys)

    categorys.each do |category_id|
      h2 = {}
      h2["category"] = {}
      h2["trait_def"] = {}
      h2["par_setting"] = {}
      h2["version"] = {}
      h2["category"][category_id] = h["category"][category_id]
      h2["trait_def"][category_id] = h["trait_def"][category_id]
      h2["par_setting"][category_id] = h["par_setting"][category_id]
      h2["version"][category_id] = h["version"][category_id]
      Pm.setSystemValue_hash("#{key}_#{category_id}",h2)
    end
  end
  def save_talent_data_par_only(h,category_level="L1",current_lang=Cfate::DEFAULT_LANG)
    key = Talent.get_talent_key(category_level,current_lang)
    hOrg = Pm.getSystemValue_hash(key)

    hOrg["par_setting"] = h["par_setting"]
    hOrg["version"] = h["version"]

    Pm.setSystemValue_hash(key,hOrg)
  end
  def check_category_the_same(hOrg,h,category_id)
    return false if hOrg == nil
    return false if hOrg["category"] == nil
    return false if h == nil
    return false if h["category"] == nil
    if (!hOrg["category"][category_id].eql?(h["category"][category_id])) then
      return false
    end
    # if (!hOrg["trait_def"][category_id].eql?(h["trait_def"][category_id])) then
    #   return false
    # end
    if (!hOrg["par_setting"][category_id].eql?(h["par_setting"][category_id])) then
      return false
    end
    return true
  end
  def logged_in?
    return (current_user != nil)
  end
  def get_session_data()
    # s_keys = session.keys
    # puts "s_keys: #{s_keys}"
    # s_values = session.values
    # puts "s_values: #{s_values}"
    user_id = current_user.id if logged_in?
    g_get_session_data(user_id,"Star")
    # s_keys = session.keys
    # puts "s_keys: #{s_keys}"
    # s_values = session.values
    # puts "s_values: #{s_values}"
  end
  def save_session_data()
    user_id = current_user.id if logged_in?
    g_save_session_data(user_id,"Star")
  end

  def authenticate
    get_session_data()
    authenticate_star
  end
  def authenticate_star(new_ap_name="star",ap_namespace="ifate")
    if (Pm.getSystemValue("WebSpeedTest") == "1") then
      @user_name = "<EMAIL>" #current_user.email
      @user_id = 3
    else
      #@user_name = current_user.name
      @user_name = current_user.email if logged_in?
      @user_id = current_user.id if logged_in?
    end
    @user_level = ""
    @ap_name = g_user_ap_name(new_ap_name,@user_id,ap_namespace)

    session["userlevel"] = @user_level

    getUserCurrentAp(@user_id)

    # session["ap_name"] = @ap_name
    session["ap_version"] = @ap_version
    session["user_name"] = { :value => @user_name, :expires => 1.minute.from_now }
    @ParAll = Star.get_pan_pars(@user_id,g_can_use_pars(@oUserAp))

    star_version_check()

    @action_result = params["action_result"]

    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?

    @admin = current_user.admin?
    @can_add_customer,@customer_max_count = g_can_add_customer(@user_id,@admin)

    @simplepan = params["simplepan"] != nil ? (params["simplepan"] == "normal" ? false : true) : (session["simplepan"] == nil ? false : session["simplepan"])
    session["simplepan"] = @simplepan
  end

  def getUserCurrentAp(user_id)
    @oUserAp = g_getUserCurrentAp(user_id,@ap_name)    
    @showAdvertise = true
    @firstShowAdvertise = @oUserAp.firstShowAdvertise && @showAdvertise ? "pan_information_block" : "pan_xdate_block"
    @canUsePan = g_canUsePan(session["customer_id"])
  end

  def star_version_check()
    # 權限控管
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @hHallNumberAdver = find_last_hallnumber_adver(@ap_name,@oUserAp,@ApFunc)
    @hCanDeal = canDealCheck(@user_id,@oUserAp,@ap_name)
    @main_products = g_getMainProducts(@ap_name)
    @canAddPe = session["customer_id"] != nil
  end
  def get_level_trait_code(category_level)
    if (category_level == "L1") then
      return @trait_code
    elsif (category_level == "L2") then
      return @trait_code_L2
    elsif (category_level == "L3") then
      return @trait_code_L3
    elsif (category_level == "MP") then
      return @trait_code_MP
    end
    return @trait_code
  end
  def get_talent_data(h)
    @category_level = h["category_level"] == nil ? "L1" : h["category_level"]
    @current_lang = my_current_lang()
    @another_current_lang = @current_lang == "zh-TW" ? "zh-CN" : "zh-TW"
    if (@category_level == "L1") then
      get_talent_data_L1(h)
    elsif (@category_level == "L2") then
      get_talent_data_L2(h)
    elsif (@category_level == "L3") then
      get_talent_data_L3(h)
    elsif (@category_level == "MP") then
      get_talent_data_MP(h)
    end
  end
  def get_talent_data_L1(h)
    category_level = "L1"
    @talent = read_talent_data(category_level,my_current_lang())
    # @category_id = h["category_id"] == nil ? "category_1" : h["category_id"]
    # @item_id = h["item_id"] == nil ? @talent["category"][@category_id]["item"].keys[0] : h["item_id"]
    # @trait_id = h["trait_id"] == nil ? "trait_1" : h["trait_id"]
    @category_id = h["category_id"]
    @item_id = h["item_id"]
    @trait_id = h["trait_id"]
    @trait_code = h["trait_code"]
    @category_id,@item_id,@trait_id = trait_code_2_c_i_t(@talent,@trait_code,@category_id,@item_id,@trait_id)
    if @trait_code == nil && @trait_code == "" && @category_id != nil && @category_id != "" && @item_id != nil && @item_id != "" && @trait_id != nil && @trait_id != "" then
      @trait_code = @talent["category"][@category_id]["item"][@item_id]["trait"][@trait_id]["trait_code"]
    end
    parse_talent_hash(category_level)
  end

  def set_talent_data()
  	make_talent_hash()
    if (@category_level == "L1") then
      save_talent_data(@talent,"L1",my_current_lang())
    elsif (@category_level == "L2") then
  	  save_talent_data(@talent_L2,"L2",my_current_lang())
    elsif (@category_level == "L3") then
      save_talent_data(@talent_L3,"L3",my_current_lang())
    elsif (@category_level == "MP") then
      save_talent_data(@talent_MP,"MP",my_current_lang())
    end
  end
  def make_talent_hash()
    if (@category_level == "L1") then
      make_talent_hash_L1()
    elsif (@category_level == "L2") then
      make_talent_hash_L2()
    elsif (@category_level == "L3") then
      make_talent_hash_L3()
    elsif (@category_level == "MP") then
      make_talent_hash_MP()
    end
  end
  def make_talent_hash_L1()
  	make_talent_hash_category_L1()
  	make_talent_hash_trait_def_L1()
  	make_talent_hash_par_setting_L1()
  end
  def make_talent_hash_category_L1()
  	@talent["category"] = {}
  	@category_ids.each_with_index do |category_id,i|
  		one_category = {"desc" => @categorys[i]}
  		one_category["item"] = {}
  		if (@item_ids[category_id] != nil) then
  		  @item_ids[category_id].each_with_index do |item_id,i|
  			  one_category["item"][item_id] = {"desc" => @items[category_id][i]}
  			  one_category["item"][item_id]["trait"] = {}
  		    if (@trait_ids[category_id] != nil && @trait_ids[category_id][item_id] != nil) then
  			    @trait_ids[category_id][item_id].each_with_index do |trait_id,i|
              one_category["item"][item_id]["trait"][trait_id] = {"desc" => @traits[category_id][item_id][i]}
              one_category["item"][item_id]["trait"][trait_id]["trait_desc"] = @traits_desc[category_id][item_id][trait_id]
              one_category["item"][item_id]["trait"][trait_id]["trait_score_desc"] = @traits_score_desc[category_id][item_id][trait_id]
  				    one_category["item"][item_id]["trait"][trait_id]["trait_code"] = @traits_code[category_id][item_id][trait_id]
  			    end
  			  end
  		  end
  		end
		  @talent["category"][category_id] = one_category.clone
  	end
  end
  def make_talent_hash_trait_def_L1()
    @talent["trait_def"] = @trait_def
  end
  def make_talent_hash_par_setting_L1()
    @talent["par_setting"] = @par_setting
  end

  def parse_talent_hash(category_level)
    if (category_level == "L1") then
      parse_talent_hash_L1()    
    elsif (category_level == "L2") then
      parse_talent_hash_L2()    
    elsif (category_level == "L3") then
      parse_talent_hash_L3()    
    elsif (category_level == "MP") then
      parse_talent_hash_MP()    
    end
  end
  def parse_talent_hash_L1()
    parse_talent_hash_category_L1()
    parse_talent_hash_trait_def_L1()
    parse_talent_hash_par_setting_L1()
  end
  def parse_talent_hash_category_L1()
  	@talent["category"] = {} if @talent["category"] == nil
  	@category_ids = @talent["category"].keys.clone
  	@categorys = []
  	@category_id_hash = {}
  	@item_ids = {}
  	@items = {}
  	@item_id_hash = {}
  	@trait_ids = {}
  	@traits = {}
    @trait_id_hash = {}
  	@trait_id_code_hash = {}
    @traits_desc = {}
    @traits_score_desc = {}
    @traits_code = {}
  	@category_ids.each_with_index do |category_id,i|
      category_desc = @talent["category"][category_id]["desc"]
      category_desc += "_1" if (@categorys.include?(category_desc))
  		@categorys.push(category_desc)
  		@category_id_hash[category_desc] = category_id
  		
  		@item_ids[category_id] = @talent["category"][category_id]["item"].keys.clone
  		@items[category_id] = []
  		@item_ids[category_id].each_with_index do |item_id,i|
        item_desc = @talent["category"][category_id]["item"][item_id]["desc"]
        item_desc += "_1" if (@items[category_id].include?(item_desc))
  			@items[category_id].push(item_desc)
  			@item_id_hash[item_desc] = item_id if (@category_id == category_id)
  		end
  	end
  	@item_id = @item_ids[@category_id][0] if (@item_ids[@category_id] != nil && @item_ids[@category_id].index(@item_id) == nil)
  	@item_id = @item_id == nil ? "item_1" : @item_id

  	@category_ids.each_with_index do |category_id,i|
  		@trait_ids[category_id] = {}
  		@traits[category_id] = {}
      @traits_desc[category_id] = {}
      @traits_score_desc[category_id] = {}
      @traits_code[category_id] = {}
  		@item_ids[category_id].each_with_index do |item_id,i|
  			@traits[category_id][item_id] = []
        @traits_desc[category_id][item_id] = {}
        @traits_score_desc[category_id][item_id] = {}
        @traits_code[category_id][item_id] = {}
  			t = @talent["category"][category_id]["item"][item_id]["trait"]
  			@trait_ids[category_id][item_id] = t.keys.clone
  			@trait_ids[category_id][item_id].each_with_index do |trait_id,i|
          trait_desc = t[trait_id]["desc"]
          trait_desc += "_1" if (@traits[category_id][item_id].include?(trait_desc))
  				@traits[category_id][item_id].push(trait_desc)
          @traits_code[category_id][item_id][trait_id] = t[trait_id]["trait_code"]
          trait_code = @traits_code[category_id][item_id][trait_id]
          @trait_id_hash["#{trait_desc}"] = trait_id if (@category_id == category_id && @item_id == item_id)
  				@trait_id_code_hash["#{trait_code}:#{trait_desc}"] = trait_id if (@category_id == category_id && @item_id == item_id)

          @traits_desc[category_id][item_id][trait_id] = t[trait_id]["trait_desc"]
          @traits_score_desc[category_id][item_id][trait_id] = t[trait_id]["trait_score_desc"]
  			end
  		end
  	end
  	@trait_id = @trait_ids[@category_id][@item_id][0] if (@trait_ids[@category_id] != nil && @trait_ids[@category_id][@item_id] != nil && @trait_ids[@category_id][@item_id].index(@trait_id) == nil)
  	@trait_id = @trait_id == nil ? "trait_1" : @trait_id

    if (@trait_id_hash.length > 0) then
      @trait_desc_cur_trait = @traits_desc[@category_id][@item_id][@trait_id]
      @trait_score_desc_cur_trait = @traits_score_desc[@category_id][@item_id][@trait_id]
      @trait_code_cur_trait = @traits_code[@category_id][@item_id][@trait_id]
    else
      @trait_desc_cur_trait = ""
      @trait_score_desc_cur_trait = ""
      @trait_code_cur_trait = ""
    end
  end
  def parse_talent_hash_trait_def_L1()
  	if @talent["trait_def"] == nil || @talent["trait_def"] == {} then
  		@trait_def = {}
  		@category_ids.each_with_index do |category_id,i|
  	    	@trait_def[category_id] = {}
  	    	@item_ids[category_id].each_with_index do |item_id,i|
  	        	@trait_def[category_id][item_id] = {}
  		    	@trait_ids[category_id][item_id].each_with_index do |trait_id,i|
  		    		@trait_def[category_id][item_id][trait_id] = trait_def_empty()
  		    	end
  	    	end
  		end
  	else
  		@trait_def = @talent["trait_def"]
  	end
  	@trait_def[@category_id][@item_id][@trait_id] = trait_def_empty() if (@trait_def[@category_id] != nil && @trait_def[@category_id][@item_id] != nil && @trait_def[@category_id][@item_id][@trait_id] == nil)
  	if (@trait_def[@category_id] == nil || @trait_def[@category_id][@item_id] == nil || @trait_def[@category_id][@item_id][@trait_id] == nil) then
  		@trait_def_cur_trait = trait_def_empty()
  	else
  		@trait_def_cur_trait = @trait_def[@category_id][@item_id][@trait_id]
  	end
  end
  def trait_def_empty()
    return Talent.trait_def_empty()
  end
  def get_trait_def(h)
    if (@category_level == "L1") then    
      get_trait_def_L1(h)
    elsif (@category_level == "L2") then    
      get_trait_def_L2(h)
    elsif (@category_level == "L3") then    
      get_trait_def_L3(h)
    elsif (@category_level == "MP") then    
      get_trait_def_MP(h)
    end
  end
  def get_trait_def_L1(h)
    @trait_desc_cur_trait = h["trait_desc"] == nil ? "" : h["trait_desc"]
    @trait_score_desc_cur_trait = h["trait_score_desc"] == nil ? "" : h["trait_score_desc"]
    @trait_code_cur_trait = h["trait_code"] == nil ? "" : h["trait_code"]

    @traits_desc[@category_id][@item_id][@trait_id] = @trait_desc_cur_trait
    @traits_score_desc[@category_id][@item_id][@trait_id] = @trait_score_desc_cur_trait
    @traits_code[@category_id][@item_id][@trait_id] = @trait_code_cur_trait

  	(1..4).each do |i|
  		@trait_def[@category_id][@item_id][@trait_id]["score_drop_#{i}"]["up"] = h["score_drop_up_#{i}"]
  		@trait_def[@category_id][@item_id][@trait_id]["score_drop_#{i}"]["down"] = h["score_drop_down_#{i}"]
  		@trait_def[@category_id][@item_id][@trait_id]["score_drop_#{i}"]["trait_content"] = h["trait_content_#{i}"]
  		@trait_def[@category_id][@item_id][@trait_id]["score_drop_#{i}"]["proposal"] = h["proposal_#{i}"]
  	end
  end

  def get_trait_L1_all(h)
    get_talent_data_L1(h)
    trait_L1_all = {}
    key = ""
    value = ""
    @category_ids.each_with_index do |category_id,i|
      key = @categorys[i]
      value = category_id
      if (@item_ids[category_id] != nil) then
        @item_ids[category_id].each_with_index do |item_id,i|
          key2 = "#{key}/#{@items[category_id][i]}"
          value2 = "#{value}/#{item_id}"
          if (@trait_ids[category_id] != nil && @trait_ids[category_id][item_id] != nil) then
            @trait_ids[category_id][item_id].each_with_index do |trait_id,i|
              trait_code = @talent["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_code"]
              key3 = "#{trait_code}_L1:#{key2}/#{@traits[category_id][item_id][i]}"
              value3 = "L1:#{value2}/#{trait_id}"
# puts "trait : #{@talent["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_code"]}" if i == 0
              trait_L1_all[key3] = value3
            end
          end
        end
      end
    end
    return trait_L1_all
  end
  def parse_talent_hash_par_setting_L1()
  	if @talent["par_setting"] == nil || @talent["par_setting"] == {} then
  		@par_setting = {}
  		@category_ids.each_with_index do |category_id,i|
  	    	@par_setting[category_id] = {}
  	    	@item_ids[category_id].each_with_index do |item_id,i|
  	        @par_setting[category_id][item_id] = {}
  		    	@trait_ids[category_id][item_id].each_with_index do |trait_id,i|
  		    		@par_setting[category_id][item_id][trait_id] = par_setting_new()
  		    	end
  	    	end
  		end
  	else
  		@par_setting = @talent["par_setting"]
  	end
  	# if (@par_setting[@category_id] != nil && @par_setting[@category_id][@item_id] != nil && @par_setting[@category_id][@item_id][@trait_id] == nil) then
    #  if (@trait_id_hash.length > 0) then
  	# 	@par_setting[@category_id][@item_id][@trait_id] = {} 
	  # end
    # @cur_stars_weight = @par_setting[@category_id][@item_id][@trait_id]["stars_weight"]
    # @cur_sihuas_weight = @par_setting[@category_id][@item_id][@trait_id]["sihuas_weight"]
  end
  def par_setting_new()
    h = {}

    return h
  end
  def rule_par_setting_new(h,star_for_rule)
  	star_weight = remain_star_weight()

  	star_house_weight = [130,100,100,100,110,100,120,100,110,100,110,100]
    stars_weight = 70
    weight_conditions = Array.new(25) {{"wc_star" => "", "wc_weight" => 0, "weight_condition" => []}}
  	shenggong_weight = shenggong_new()

    sihua_house_weight = sihua_rule_par_setting_new()
    sihuas_weight = 30
    sihua_star_weights = Talent.sihua_star_weight_par_setting_new()
    sihua_sign = Talent.sihua_sign_new()
    sihua_sign_exception_house = Talent.sihua_sign_exception_house_new()

  	h = rule_par_setting(h,star_for_rule,star_weight,star_house_weight,stars_weight,weight_conditions,sihua_house_weight,sihuas_weight,sihua_star_weights,shenggong_weight,sihua_sign,sihua_sign_exception_house)

  	return h
  end
  def shenggong_new()
    a = Array.new(3) {{"use_shenggong" => false, "weight" => 30, "condition" => "sw_star_sum"}}
    # a[0] = {"use_shenggong" => false, "weight" => 30, "condition" => "sw_star_sum"}
    # a[1] = {"use_shenggong" => false, "weight" => 30, "condition" => "sw_star_sum"}
    # a[2] = {"use_shenggong" => false, "weight" => 30, "condition" => "sw_star_sum"}
    return a
  end
  def remain_star_weight()
    t = 300
    if (@trait_ids == nil) then
      return t
    end
    if (@trait_ids.length == 0) then
      return t
    end
    if (@par_setting == nil) then
      return t
    end
    if (@par_setting[@category_id] == nil) then
      return t
    end
    if (@par_setting[@category_id][@item_id] == nil) then
      return t
    end
    if (@par_setting[@category_id][@item_id][@trait_id] == nil) then
      return t
    end
    @par_setting[@category_id][@item_id][@trait_id].keys.each do |star_for_rule|
      t -= @par_setting[@category_id][@item_id][@trait_id][star_for_rule]["star_weight"].to_i
    end
    return t
  end
  def rule_par_setting(h,star_for_rule,star_weight,star_house_weight,stars_weight,weight_conditions,sihua_house_weight,sihuas_weight,sihua_star_weights,shenggong_weight,sihua_sign,sihua_sign_exception_house)
  	h["star_for_rule"] = star_for_rule # 星曜
    h["star_weight"] = star_weight # 星曜比重
    h["shenggong_weight"] = shenggong_weight # 身宮比重
    
    h["star"] = {}
  	h["star"]["house_weight"] = star_house_weight # 宮位權重
    h["star"]["stars_weight"] = stars_weight # 星垣總比重
  	h["star"]["weight_condition"] = weight_conditions # 特殊加權

    h["sihua"] = {}
    h["sihua"]["house_weight"] = sihua_house_weight # 四化加權比例
    h["sihua"]["sihuas_weight"] = sihuas_weight # 四化總比重
    if sihua_star_weights == nil then
      sihua_star_weights = Talent.sihua_star_weight_par_setting_new()
    end
    h["sihua"]["sihua_star_weight"] = sihua_star_weights # 四化星條件

    h["sihua"]["sihua_sign"] = sihua_sign # 四化最後值正負
    h["sihua"]["sihua_sign_exception_house"] = sihua_sign_exception_house # 四化最後值正負，例外，使用原值

  	return h
  end
  def sihua_rule_par_setting_new()
  	h = {}
  	h["hua_lu"] = [100,100,100,100,100,100,100,100,100,100,100,100]
  	h["hua_quan"] = [100,100,100,100,100,100,100,100,100,100,100,100]
  	h["hua_ke"] = [100,100,100,100,100,100,100,100,100,100,100,100]
  	h["hua_ji"] = [100,100,100,100,100,100,100,100,100,100,100,100]

  	return h
  end

    # 同宮 對宮 三方 夾 祿 權 科 忌
  def get_weight_condition_for_select()
  	a = {}
  	id = ["same_house","opposite_house","three_house","jia",
          "zihua_lu_quan_ke","zihua_lu","zihua_quan","zihua_ke","zihua_ji",
          "sheng_lu_quan_ke","sheng_lu","sheng_quan","sheng_ke","sheng_ji",
          "no_main_star"]
  	name = id.collect { |x| Pm.GetStr("talent.L1.#{x}")}
	  # id.each_with_index do |x,i|
	  #   a.push(OpenStruct.new(id: x, name: name[i]))
	  # end
	  id.each_with_index do |x,i|
	    a[x] = name[i]
	  end

  	return a
  end
  def get_sihua_weight_condition_for_select()
    a = {}
    id = ["zihua_lu_quan_ke","zihua_lu","zihua_quan","zihua_ke","zihua_ji",
          "sheng_lu_quan_ke","sheng_lu","sheng_quan","sheng_ke","sheng_ji"]
    name = id.collect { |x| Pm.GetStr("talent.L1.#{x}")}
    # id.each_with_index do |x,i|
    #   a.push(OpenStruct.new(id: x, name: name[i]))
    # end
    id.each_with_index do |x,i|
      a[x] = name[i]
    end

    return a
  end


  # 應用指標 L2
  def setup_L2()
    cmd = params["cmd"]
    if (cmd != nil) then
      setup_cmd_L2(cmd,params)
    end
    @h_copy_L2 = Pm.findTestDbHashValue_key("talent_copy_L2")
  end
  def setup_cmd_L2(cmd,h)
    target = h["target"]
    if (cmd == "c") then # copy
      h_copy = get_one_copy_L2(target)
      h_copy["target"] = target
      Pm.updateTestDbHashValue_key("talent_copy_L2",h_copy)
    elsif (cmd == "p") then # paste
      h_copy = Pm.findTestDbHashValue_key("talent_copy_L2")
      target = h_copy["target"]
      set_one_copy_L2(target,h_copy)
      Pm.updateTestDbHashValue_key("talent_copy_L2",{})
      save_talent_data(@talent_L2,"L2",my_current_lang())
      get_talent_data(h)
    end 
  end
  def get_one_copy_L2(target)
    h_copy = get_one_copy_category_L2(target)

    h_copy = get_one_copy_trait_def_L2(target,h_copy)

    h_copy = get_one_copy_par_setting_L2(target,h_copy)

    return h_copy
  end
  def get_one_copy_category_L2(target)
    h_copy = {}
    if (["category","item","trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id_L2] = @talent_L2["category"][@category_id_L2]
    end
    if (["item","trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id_L2] = {}
      h_copy["category"][@category_id_L2]["item"] = {}
      h_copy["category"][@category_id_L2]["item"][@item_id_L2] = @talent_L2["category"][@category_id_L2]["item"][@item_id_L2]
    end
    if (["trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id_L2] = {}
      h_copy["category"][@category_id_L2]["item"] = {}
      h_copy["category"][@category_id_L2]["item"][@item_id_L2] = {}
      h_copy["category"][@category_id_L2]["item"][@item_id_L2]["trait"] = {}
      h_copy["category"][@category_id_L2]["item"][@item_id_L2]["trait"][@trait_id_L2] = @talent_L2["category"][@category_id_L2]["item"][@item_id_L2]["trait"][@trait_id_L2]
    end
    return h_copy
  end
  def get_one_copy_trait_def_L2(target,h_copy)
    h_copy["trait_def"] = {}
    if ("category" == target) then
      h_copy["trait_def"][@category_id_L2] = @talent_L2["trait_def"][@category_id_L2]
    elsif ("item" == target) then
      h_copy["trait_def"] = {}
      h_copy["trait_def"][@category_id_L2] = {}
      h_copy["trait_def"][@category_id_L2][@item_id_L2] = @talent_L2["trait_def"][@category_id_L2][@item_id_L2]
    elsif ("trait" == target) then
      h_copy["trait_def"] = {}
      h_copy["trait_def"][@category_id_L2] = {}
      h_copy["trait_def"][@category_id_L2][@item_id_L2] = {}
      h_copy["trait_def"][@category_id_L2][@item_id_L2][@trait_id_L2] = @talent_L2["trait_def"][@category_id_L2][@item_id_L2][@trait_id_L2]
    end
    return h_copy
  end
  def get_one_copy_par_setting_L2(target,h_copy)
    h_copy["par_setting"] = {}
    if ("category" == target) then
      h_copy["par_setting"][@category_id_L2] = @talent_L2["par_setting"][@category_id_L2]
    elsif ("item" == target) then
      h_copy["par_setting"] = {}
      h_copy["par_setting"][@category_id_L2] = {}
      h_copy["par_setting"][@category_id_L2][@item_id_L2] = @talent_L2["par_setting"][@category_id_L2][@item_id_L2]
    elsif ("trait" == target) then
      h_copy["par_setting"] = {}
      h_copy["par_setting"][@category_id_L2] = {}
      h_copy["par_setting"][@category_id_L2][@item_id_L2] = {}
      h_copy["par_setting"][@category_id_L2][@item_id_L2][@trait_id_L2] = @talent_L2["par_setting"][@category_id_L2][@item_id_L2][@trait_id_L2]
    end
    return h_copy
  end
  def set_one_copy_L2(target,h_copy)
    t_ids,h_ids = set_one_copy_category_L2(target,h_copy)
    set_one_copy_trait_def_L2(target,h_copy,t_ids,h_ids)
    set_one_copy_par_setting_L2(target,h_copy,t_ids,h_ids)
  end
  def set_one_copy_category_L2(target,h_copy)
    category_id_copy = h_copy["category"].keys[0]
    i = find_new_index("category_",@talent_L2["category"].keys)
    category_id_new = "category_#{i}"
    if ("category" == target) then
      @talent_L2["category"][category_id_new] = h_copy["category"][category_id_copy]
      return [category_id_new,0,0],[category_id_copy,0,0]
    end
    item_id_copy = h_copy["category"][category_id_copy]["item"].keys[0]
    i = find_new_index("item_",@talent_L2["category"][@category_id_L2]["item"].keys)
    item_id_new = "item_#{i}"
    if ("item" == target) then
      @talent_L2["category"][@category_id_L2]["item"][item_id_new]= h_copy["category"][category_id_copy]["item"][item_id_copy]
      return [@category_id_L2,item_id_new,0],[category_id_copy,item_id_copy,0]
    end
    trait_id_copy = h_copy["category"][category_id_copy]["item"][item_id_copy]["trait"].keys[0]
    i = find_new_index("trait_",@talent_L2["category"][@category_id_L2]["item"][@item_id_L2]["trait"].keys)
    trait_id_new = "trait_#{i}"
    if ("trait" == target) then
      @talent_L2["category"][@category_id_L2]["item"][@item_id_L2]["trait"][trait_id_new] = h_copy["category"][category_id_copy]["item"][item_id_copy]["trait"][trait_id_copy]
      return [@category_id_L2,@item_id_L2,trait_id_new],[category_id_copy,item_id_copy,trait_id_copy]
    end
    return [nil,nil,nil],[nil,nil,nil]
  end
  def set_one_copy_trait_def_L2(target,h_copy,t_ids,h_ids)
    if ("category" == target) then
      @talent_L2["trait_def"][t_ids[0]] = h_copy["trait_def"][h_ids[0]]
    elsif ("item" == target) then
      @talent_L2["trait_def"][t_ids[0]][t_ids[1]] = h_copy["trait_def"][h_ids[0]][h_ids[1]]
    elsif ("trait" == target) then
      @talent_L2["trait_def"][t_ids[0]][t_ids[1]][t_ids[2]] = h_copy["trait_def"][h_ids[0]][h_ids[1]][h_ids[2]]
    end
  end
  def set_one_copy_par_setting_L2(target,h_copy,t_ids,h_ids)
    if ("category" == target) then
      @talent_L2["par_setting"][t_ids[0]] = h_copy["par_setting"][h_ids[0]]
    elsif ("item" == target) then
      @talent_L2["par_setting"][t_ids[0]][t_ids[1]] = h_copy["par_setting"][h_ids[0]][h_ids[1]]
    elsif ("trait" == target) then
      @talent_L2["par_setting"][t_ids[0]][t_ids[1]][t_ids[2]] = h_copy["par_setting"][h_ids[0]][h_ids[1]][h_ids[2]]
    end
  end
  def rule_L2_new()
    rule_L2s = Array.new(10) {{"rl_use" => false, "rl_reverse" => false, "rl_trait_L1" => "L1:category_1/item_1/trait_1", "rl_weight" => 100}}
    return rule_L2s
  end
  def setup_rule_L2_cmd(cmd,hPar)
    rule_L2s = @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2][@rule_L2] == nil ? rule_L2_new() : @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2][@rule_L2]
    if (cmd == "a") then
      @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2][@rule_L2] = rule_L2_setting(rule_L2s,hPar)
    elsif (cmd == "d") then
      @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2][@rule_L2] = rule_L2_new()
    elsif (cmd == "e") then
      @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2][@rule_L2] = rule_L2_setting(rule_L2s,hPar)
    elsif (cmd == "c") then # copy
      h_copy = {}
      h_copy["data"] = @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2][@rule_L2]
      h_copy["target"] = "setup_rule_L2"
      Pm.updateTestDbHashValue_key("setup_rule_L2_copy",h_copy)
    elsif (cmd == "p") then # paste
      h_copy = Pm.findTestDbHashValue_key("setup_rule_L2_copy")
      return if h_copy == nil || h_copy == {}
      target = h_copy["target"]
      @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2][@rule_L2] = h_copy["data"]
      @talent_L2["par_setting"][@category_id_L2][@item_id_L2][@trait_id_L2][@rule_L2] = @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2][@rule_L2]
      save_talent_data(@talent_L2,"L2",my_current_lang())
      get_talent_data(hPar)
    end 
  end
  def rule_L2_setting(rule_L2s,hPar)
    (0...10).each do |rule_L2_index|
      rule_L2s[rule_L2_index]["rl_use"] = Cfate.ValueBoolCheck(hPar["rl_use_#{rule_L2_index}"])
      rule_L2s[rule_L2_index]["rl_reverse"] = Cfate.ValueBoolCheck(hPar["rl_reverse_#{rule_L2_index}"])
      rule_L2s[rule_L2_index]["rl_trait_L1"] = hPar["rl_trait_L1_#{rule_L2_index}"]
      rule_L2s[rule_L2_index]["rl_weight"] = hPar["rl_weight_#{rule_L2_index}"].to_i
    end
    return rule_L2s
  end

  def setup_rule_L2()
    save_session_data()

    get_talent_data(params)
    get_stars_for_select(params)
    @cmd = params["cmd"]
    mode = params["mode"]

    if (@cmd == "a") 
      @cur_rule_L2 = rule_L2_new()
    else
      @cur_rule_L2 = @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2][@rule_L2]
      @cur_rule_L2 = rule_L2_new() if @cur_rule_L2 == nil || @cur_rule_L2 == {}
    end

    if (@cmd != nil && mode == "submit") then
      setup_rule_L2_cmd(@cmd,params)
      set_talent_data()
      set_global_var(@category_level)
      if (@cmd == "a" || @cmd == "e" || @cmd == "d" || @cmd == "c" || @cmd == "p") then
        # redirect_to :action => "setup", :category_id => @category_id_L2, :item_id => @item_id_L2, :trait_id => @trait_id_L2, :category_level => @category_level
      end
    else
      set_global_var(@category_level)
    end
  end
  def item_trait_id_L2(h,category_level)
    @item_id_L2 = h["item_id"]
    @trait_id_L2 = h["trait_id"]
    # @category_id = "" if @category_id == nil
    @item_id_L2 = "" if @item_id_L2 == nil
    @trait_id_L2 = "" if @trait_id_L2 == nil || @item_id_L2 == ""
    @trait_id_hash_L2 = {} if @item_id_L2 == ""
  end
  def update_new_par_setting_L2()
  end
  def set_global_var_L2(category_level)
    @h_copy = @h_copy_L2
    @talent = @talent_L2
    @category_id = @category_id_L2
    @item_id = @item_id_L2
    @trait_id = @trait_id_L2
    @par_setting = @par_setting_L2
    @category_ids = @category_ids_L2
    @trait_def = @trait_def_L2
    @traits_desc = @traits_desc_L2
    @traits_score_desc = @traits_score_desc_L2
    @trait_code = @trait_code_L2
    @trait_code_cur_trait = @trait_code_cur_trait_L2
    @categorys = @categorys_L2
    @item_ids = @item_ids_L2
    @items = @items_L2
    @trait_ids = @trait_ids_L2
    @traits = @traits_L2
    @category_id_hash = @category_id_hash_L2
    @item_id_hash = @item_id_hash_L2
    @trait_id_hash = @trait_id_hash_L2
    @trait_id_code_hash = @trait_id_code_hash_L2
    @trait_score_desc_cur_trait = @trait_score_desc_cur_trait_L2
    @trait_desc_cur_trait = @trait_desc_cur_trait_L2
    @trait_def_cur_trait = @trait_def_cur_trait_L2
    @cur_rule = @cur_rule_L2
    @rule = @rule_L2
    @trait_List_all = @trait_L1_all
    @traits_code = @traits_code_L2
  end
  # L2
  def category_cmd_L2(cmd,category,category_id)
    if (cmd == "a") then
      category_add_L2(category)
    elsif (cmd == "d") then
      category_remove_L2(category_id)
    elsif (cmd == "e") then
      category_edit_L2(category_id,category)
    elsif (cmd == "cc") then # change category
      category_change_L2(category_id,nil,nil)
    end 
  end
  def category_add_L2(category)
    if (category != nil) then
      @categorys_L2.push(category)
      category_id = next_category_id(@category_ids_L2.last)
      @category_ids_L2.push(category_id)

      # add item
      @trait_def_L2 = {} if @trait_def_L2 == nil
      @trait_def_L2[category_id] = {}

      @par_setting_L2 = {} if @par_setting_L2 == nil
      @par_setting_L2[category_id] = {}

      @traits_desc_L2[category_id] = {}
      @traits_score_desc_L2[category_id] = {}
      @traits_code_L2[category_id] = {}
    end
  end
  def category_edit_L2(category_id,category)
    if (category_id != nil && category != nil) then
      i = @category_ids_L2.index(category_id)
      if (i == nil) then return end
      @categorys_L2[i] = category
    end
  end
  def category_remove_L2(category_id)
    if (category_id != nil) then
      i = @category_ids_L2.index(category_id)
      if (i == nil) then return end
      @categorys_L2.delete_at(i)
      @category_ids_L2.delete_at(i)

      # remove item
      @items_L2.delete(category_id)
      @item_ids_L2.delete(category_id)

    # remove trait
      @traits_L2.delete(category_id)
      @trait_ids_L2.delete(category_id)

      # remove trait_def
      @trait_def_L2.delete(category_id) if @trait_def_L2 != nil
      @par_setting_L2.delete(category_id) if @par_setting_L2 != nil

      @traits_desc_L2.delete(category_id) if @traits_desc_L2 != nil
      @traits_score_desc_L2.delete(category_id) if @traits_score_desc_L2 != nil
      @traits_code_L2.delete(category_id) if @traits_code_L2 != nil
    end
  end
  def category_change_L2(category_id,item_id,trait_id)
    @category_id_L2 = category_id if category_id != nil
    @item_id_L2 = item_id if item_id != nil
    @trait_id_L2 = trait_id if trait_id != nil

    # get setting parameters
  end
  # L2
  def item_cmd_L2(cmd,category_id,item,item_id)
    if (cmd == "a") then
      item_add_L2(category_id,item)
    elsif (cmd == "d") then
      item_remove_L2(category_id,item_id)
    elsif (cmd == "e") then
      item_edit_L2(category_id,item_id,item)
    elsif (cmd == "cc") then # change category
      category_change_L2(category_id,item_id,nil)
    end 
  end
  def item_add_L2(category_id,item)
    if (item != nil) then
      @items_L2[category_id].push(item) if @items_L2[category_id] != nil
      item_id = next_item_id(@item_ids_L2[category_id].last)
      @item_ids_L2[category_id].push(item_id) if @item_ids_L2[category_id] != nil

      @trait_def_L2[category_id] = {} if @trait_def_L2[category_id] == nil
      @trait_def_L2[category_id][item_id] = {}

      @par_setting_L2[category_id] = {} if @par_setting_L2[category_id] == nil
      @par_setting_L2[category_id][item_id] = {}

      @traits_desc_L2[category_id][item_id] = {}
      @traits_score_desc_L2[category_id][item_id] = {}
      @traits_code_L2[category_id][item_id] = {}
    end
  end
  def item_remove_L2(category_id,item_id)
    if (item_id != nil) then
      i = @item_ids_L2[category_id].index(item_id)
      if (i == nil) then return end
      @items_L2[category_id].delete_at(i)
      @item_ids_L2[category_id].delete_at(i)

      # remove trait
      @traits_L2[category_id].delete(item_id)
      @trait_ids_L2[category_id].delete(item_id)

      # remove @trait_def_L2
      @trait_def_L2[category_id].delete(item_id) if @trait_def_L2[category_id] != nil
      @par_setting_L2[category_id].delete(item_id) if @par_setting_L2[category_id] != nil

      @traits_desc_L2[category_id].delete(item_id)
      @traits_score_desc_L2[category_id].delete(item_id)
      @traits_code_L2[category_id].delete(item_id)
    end
  end
  def item_edit_L2(category_id,item_id,item)
    if (item_id != nil && item != nil) then
      i = @item_ids_L2[category_id].index(item_id)
      if (i == nil) then return end
      @items_L2[category_id][i] = item
    end
  end
  def trait_cmd_L2(cmd,category_id,item_id,trait,trait_id,trait_code)
    if (cmd == "a") then
      trait_add_L2(category_id,item_id,trait,trait_code)
    elsif (cmd == "d") then
      trait_remove_L2(category_id,item_id,trait_id)
    elsif (cmd == "e") then
      trait_edit_L2(category_id,item_id,trait_id,trait,trait_code)
    elsif (cmd == "cc") then # change category
      category_change_L2(category_id,item_id,trait_id)
    end 
  end
  def trait_add_L2(category_id,item_id,trait,trait_code)
    if (trait != nil) then
      @traits_L2[category_id][item_id].push(trait) if @traits_L2[category_id][item_id] != nil
      trait_id = next_trait_id(@trait_ids_L2[category_id][item_id].last)
      @trait_ids_L2[category_id][item_id].push(trait_id) if @trait_ids_L2[category_id][item_id] != nil
        
      @trait_def_L2[category_id][item_id][trait_id] = trait_def_empty()
      @par_setting_L2[category_id][item_id][trait_id] = par_setting_new()

      @traits_desc_L2[category_id][item_id][trait_id] = ""
      @traits_score_desc_L2[category_id][item_id][trait_id] = ""
      @traits_code_L2[category_id][item_id][trait_id] = trait_code
    end
  end
  def trait_edit_L2(category_id,item_id,trait_id,trait,trait_code)
    if (trait_id != nil && trait != nil) then
      i = @trait_ids_L2[category_id][item_id].index(trait_id)
      if (i == nil) then return end
      @traits_L2[category_id][item_id][i] = trait
      @traits_code_L2[category_id][item_id][trait_id] = trait_code
    end
  end
  def trait_remove_L2(category_id,item_id,trait_id)
    if (trait_id != nil) then
      i = @trait_ids_L2[category_id][item_id].index(trait_id)
      if (i == nil) then return end
      @traits_L2[category_id][item_id].delete_at(i)
      @trait_ids_L2[category_id][item_id].delete_at(i)

      @traits_desc_L2[category_id][item_id].delete(trait_id)
      @traits_score_desc_L2[category_id][item_id].delete(trait_id)
      @traits_code_L2[category_id][item_id].delete(trait_id)
      # remove @trait_def_L2
      if (@trait_def_L2[category_id] != nil && @trait_def_L2[category_id][item_id] != nil) then
        @trait_def_L2[category_id][item_id].delete(trait_id)
      end

      if (@par_setting_L2[category_id] != nil && @par_setting_L2[category_id][item_id] != nil) then
        @par_setting_L2[category_id][item_id].delete(trait_id)
      end
    end
  end
  def read_talent_data_L2(current_lang=Cfate::DEFAULT_LANG)
    key = Talent.get_talent_key("L2",current_lang)
    return Pm.getSystemValue_hash(key)
  end
  def save_talent_data_L2(h,current_lang=Cfate::DEFAULT_LANG)
    key = Talent.get_talent_key("L2",current_lang)
    hOrg = Pm.getSystemValue_hash(key)

    if (h["version"] == nil) then
      h["version"] = {}
    end
    categorys = h["category"].keys
    categorys.each do |category_id|
      if (h["version"][category_id] == nil) || (!check_category_the_same(hOrg,h,category_id)) then
        h["version"][category_id] = Xdate.make_now_timestamp2()
      end
    end
    Pm.setSystemValue_hash(key,h)

    value_categorys = categorys.join(",")
    Pm.setSystemValue("#{key}_categorys",value_categorys)

    categorys.each do |category_id|
      h2 = {}
      h2["category"] = {}
      h2["trait_def"] = {}
      h2["par_setting"] = {}
      h2["version"] = {}
      h2["category"][category_id] = h["category"][category_id]
      h2["trait_def"][category_id] = h["trait_def"][category_id]
      h2["par_setting"][category_id] = h["par_setting"][category_id]
      h2["version"][category_id] = h["version"][category_id]
      Pm.setSystemValue_hash("#{key}_#{category_id}",h2)
    end
  end
  def get_talent_data_L2(h)
    category_level = "L2"
    @talent_L2 = read_talent_data(category_level,my_current_lang())
    # @category_id_L2 = h["category_id"] == nil ? "category_1" : h["category_id"]
    # @item_id_L2 = h["item_id"] == nil ? "item_1" : h["item_id"]
    # @trait_id_L2 = h["trait_id"] == nil ? "trait_1" : h["trait_id"]
    @category_id_L2 = h["category_id"]
    @item_id_L2 = h["item_id"]
    @trait_id_L2 = h["trait_id"]
    @trait_code_L2 = h["trait_code"]
    @category_id_L2,@item_id_L2,@trait_id_L2 = trait_code_2_c_i_t(@talent_L2,@trait_code_L2,@category_id_L2,@item_id_L2,@trait_id_L2)
    if @trait_code_L2 == nil && @trait_code_L2 == "" && @category_id_L2 != nil && @category_id_L2 != "" && @item_id_L2 != nil && @item_id_L2 != "" && @trait_id_L2 != nil && @trait_id_L2 != "" then
      @trait_code_L2 = @talent_L2["category"][@category_id_L2]["item"][@item_id_L2]["trait"][@trait_id_L2]["trait_code"] if @trait_code_L2 == nil
    end
    @rule_L2 = h["rule_L2"]
    @rule_L2 = "Rule" if @rule_L2 == nil

    parse_talent_hash(category_level)
    if (@par_setting_L2[@category_id_L2] != nil && @par_setting_L2[@category_id_L2][@item_id_L2] != nil && @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2] != nil ) then
      @cur_rule_L2 = @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2][@rule_L2]
    end
    @cur_rule_L2 = rule_L2_new() if @cur_rule_L2 == nil || @cur_rule_L2 == {}      
  end
  def make_talent_hash_L2()
    make_talent_hash_category_L2()
    make_talent_hash_trait_def_L2()
    make_talent_hash_par_setting_L2()
  end
  def make_talent_hash_category_L2()
    @talent_L2["category"] = {}
    @category_ids_L2.each_with_index do |category_id,i|
      one_category = {"desc" => @categorys_L2[i]}
      one_category["item"] = {}
      if (@item_ids_L2[category_id] != nil) then
        @item_ids_L2[category_id].each_with_index do |item_id,i|
          one_category["item"][item_id] = {"desc" => @items_L2[category_id][i]}
          one_category["item"][item_id]["trait"] = {}
          if (@trait_ids_L2[category_id] != nil && @trait_ids_L2[category_id][item_id] != nil) then
            @trait_ids_L2[category_id][item_id].each_with_index do |trait_id,i|
              one_category["item"][item_id]["trait"][trait_id] = {"desc" => @traits_L2[category_id][item_id][i]}
              one_category["item"][item_id]["trait"][trait_id]["trait_desc"] = @traits_desc_L2[category_id][item_id][trait_id]
              one_category["item"][item_id]["trait"][trait_id]["trait_score_desc"] = @traits_score_desc_L2[category_id][item_id][trait_id]
              one_category["item"][item_id]["trait"][trait_id]["trait_code"] = @traits_code_L2[category_id][item_id][trait_id]
            end
          end
        end
      end
      @talent_L2["category"][category_id] = one_category.clone
    end
  end
  def make_talent_hash_trait_def_L2()
    @talent_L2["trait_def"] = @trait_def_L2
  end
  def make_talent_hash_par_setting_L2()
    @talent_L2["par_setting"] = @par_setting_L2
  end
  def parse_talent_hash_L2()
    parse_talent_hash_category_L2()
    parse_talent_hash_trait_def_L2()
    parse_talent_hash_par_setting_L2()
  end
  def parse_talent_hash_category_L2()
    @talent_L2["category"] = {} if @talent_L2["category"] == nil
    @category_ids_L2 = @talent_L2["category"].keys.clone
    @categorys_L2 = []
    @category_id_hash_L2 = {}
    @item_ids_L2 = {}
    @items_L2 = {}
    @item_id_hash_L2 = {}
    @trait_ids_L2 = {}
    @traits_L2 = {}
    @trait_id_hash_L2 = {}
    @trait_id_code_hash_L2 = {}
    @traits_desc_L2 = {}
    @traits_score_desc_L2 = {}
    @traits_code_L2 = {}
    @category_ids_L2.each_with_index do |category_id,i|
      category_desc = @talent_L2["category"][category_id]["desc"]
      category_desc += "_1" if (@categorys_L2.include?(category_desc))
      @categorys_L2.push(category_desc)
      @category_id_hash_L2[category_desc] = category_id
      
      @item_ids_L2[category_id] = @talent_L2["category"][category_id]["item"].keys.clone
      @items_L2[category_id] = []
      @item_ids_L2[category_id].each_with_index do |item_id,i|
        item_desc = @talent_L2["category"][category_id]["item"][item_id]["desc"]
        item_desc += "_1" if (@items_L2[category_id].include?(item_desc))
        @items_L2[category_id].push(item_desc)
        @item_id_hash_L2[item_desc] = item_id if (@category_id_L2 == category_id)
      end
    end
    @item_id_L2 = @item_ids_L2[@category_id_L2][0] if (@item_ids_L2[@category_id_L2] != nil && @item_ids_L2[@category_id_L2].index(@item_id_L2) == nil)
    @item_id_L2 = @item_id_L2 == nil ? "item_1" : @item_id_L2

    @category_ids_L2.each_with_index do |category_id,i|
      @trait_ids_L2[category_id] = {}
      @traits_L2[category_id] = {}
      @traits_desc_L2[category_id] = {}
      @traits_score_desc_L2[category_id] = {}
      @traits_code_L2[category_id] = {}
      @item_ids_L2[category_id].each_with_index do |item_id,i|
        @traits_L2[category_id][item_id] = []
        @traits_desc_L2[category_id][item_id] = {}
        @traits_score_desc_L2[category_id][item_id] = {}
        @traits_code_L2[category_id][item_id] = {}
        t = @talent_L2["category"][category_id]["item"][item_id]["trait"]
        @trait_ids_L2[category_id][item_id] = t.keys.clone
        @trait_ids_L2[category_id][item_id].each_with_index do |trait_id,i|
          trait_desc = t[trait_id]["desc"]
          trait_desc += "_1" if (@traits_L2[category_id][item_id].include?(trait_desc))
          @traits_L2[category_id][item_id].push(trait_desc)
          @traits_code_L2[category_id][item_id][trait_id] = t[trait_id]["trait_code"]
          trait_code = @traits_code_L2[category_id][item_id][trait_id]
          @trait_id_hash_L2["#{trait_desc}"] = trait_id if (@category_id_L2 == category_id && @item_id_L2 == item_id)
          @trait_id_code_hash_L2["#{trait_code}:#{trait_desc}"] = trait_id if (@category_id_L2 == category_id && @item_id_L2 == item_id)

          @traits_desc_L2[category_id][item_id][trait_id] = t[trait_id]["trait_desc"]
          @traits_score_desc_L2[category_id][item_id][trait_id] = t[trait_id]["trait_score_desc"]
        end
      end
    end
    @trait_id_L2 = @trait_ids_L2[@category_id_L2][@item_id_L2][0] if (@trait_ids_L2[@category_id_L2] != nil && @trait_ids_L2[@category_id_L2][@item_id_L2] != nil && @trait_ids_L2[@category_id_L2][@item_id_L2].index(@trait_id_L2) == nil)
    @trait_id_L2 = @trait_id_L2 == nil ? "trait_1" : @trait_id_L2

    if (@trait_id_hash_L2.length > 0) then
      @trait_desc_cur_trait_L2 = @traits_desc_L2[@category_id_L2][@item_id_L2][@trait_id_L2]
      @trait_score_desc_cur_trait_L2 = @traits_score_desc_L2[@category_id_L2][@item_id_L2][@trait_id_L2]
      @trait_code_cur_trait_L2 = @traits_code_L2[@category_id_L2][@item_id_L2][@trait_id_L2]
    else
      @trait_desc_cur_trait_L2 = ""
      @trait_score_desc_cur_trait_L2 = ""
      @trait_code_cur_trait_L2 = ""
    end
  end
  def parse_talent_hash_trait_def_L2()
    if @talent_L2["trait_def"] == nil || @talent_L2["trait_def"] == {} then
      @trait_def_L2 = {}
      @category_ids_L2.each_with_index do |category_id,i|
          @trait_def_L2[category_id] = {}
          @item_ids_L2[category_id].each_with_index do |item_id,i|
              @trait_def_L2[category_id][item_id] = {}
            @trait_ids_L2[category_id][item_id].each_with_index do |trait_id,i|
              @trait_def_L2[category_id][item_id][trait_id] = trait_def_empty()
            end
          end
      end
    else
      @trait_def_L2 = @talent_L2["trait_def"]
    end
    @trait_def_L2[@category_id_L2][@item_id_L2][@trait_id_L2] = trait_def_empty() if (@trait_def_L2[@category_id_L2] != nil && @trait_def_L2[@category_id_L2][@item_id_L2] != nil && @trait_def_L2[@category_id_L2][@item_id_L2][@trait_id_L2] == nil)
    if (@trait_def_L2[@category_id_L2] == nil || @trait_def_L2[@category_id_L2][@item_id_L2] == nil || @trait_def_L2[@category_id_L2][@item_id_L2][@trait_id_L2] == nil) then
      @trait_def_cur_trait_L2 = trait_def_empty()
    else
      @trait_def_cur_trait_L2 = @trait_def_L2[@category_id_L2][@item_id_L2][@trait_id_L2]
    end
  end
  def get_trait_def_L2(h)
    @trait_desc_cur_trait_L2 = h["trait_desc"] == nil ? "" : h["trait_desc"]
    @trait_score_desc_cur_trait_L2 = h["trait_score_desc"] == nil ? "" : h["trait_score_desc"]
    @trait_code_cur_trait_L2 = h["trait_code"] == nil ? "" : h["trait_code"]

    @traits_desc_L2[@category_id_L2][@item_id_L2][@trait_id_L2] = @trait_desc_cur_trait_L2
    @traits_score_desc_L2[@category_id_L2][@item_id_L2][@trait_id_L2] = @trait_score_desc_cur_trait_L2
    @traits_code_L2[@category_id_L2][@item_id_L2][@trait_id_L2] = @trait_code_cur_trait_L2

    (1..4).each do |i|
      @trait_def_L2[@category_id_L2][@item_id_L2][@trait_id_L2]["score_drop_#{i}"]["up"] = h["score_drop_up_#{i}"]
      @trait_def_L2[@category_id_L2][@item_id_L2][@trait_id_L2]["score_drop_#{i}"]["down"] = h["score_drop_down_#{i}"]
      @trait_def_L2[@category_id_L2][@item_id_L2][@trait_id_L2]["score_drop_#{i}"]["trait_content"] = h["trait_content_#{i}"]
      @trait_def_L2[@category_id_L2][@item_id_L2][@trait_id_L2]["score_drop_#{i}"]["proposal"] = h["proposal_#{i}"]
    end
  end
   def parse_talent_hash_par_setting_L2()
    if @talent_L2["par_setting"] == nil || @talent_L2["par_setting"] == {} then
      @par_setting_L2 = {}
      @category_ids_L2.each_with_index do |category_id,i|
          @par_setting_L2[category_id] = {}
          @item_ids_L2[category_id].each_with_index do |item_id,i|
            @par_setting_L2[category_id][item_id] = {}
            @trait_ids_L2[category_id][item_id].each_with_index do |trait_id,i|
              @par_setting_L2[category_id][item_id][trait_id] = par_setting_new()
            end
          end
      end
    else
      @par_setting_L2 = @talent_L2["par_setting"]
    end
    # if (@par_setting_L2[@category_id_L2] != nil && @par_setting_L2[@category_id_L2][@item_id_L2] != nil && @par_setting_L2@category_id_L2][@item_id_L2][@trait_id_L2] == nil) then
   #  if (@trait_id_hash_L2.length > 0) then
    #   @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2] = {} 
    # end
    # @cur_stars_weight_L2 = @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2]["stars_weight"]
    # @cur_sihuas_weight_L2 = @par_setting_L2[@category_id_L2][@item_id_L2][@trait_id_L2]["sihuas_weight"]
  end
  def get_trait_L2_all(h)
    get_talent_data_L2(h)
    trait_L2_all = {}
    key = ""
    value = ""
    @category_ids_L2.each_with_index do |category_id,i|
      key = @categorys_L2[i]
      value = category_id
      if (@item_ids_L2[category_id] != nil) then
        @item_ids_L2[category_id].each_with_index do |item_id,i|
          key2 = "#{key}/#{@items_L2[category_id][i]}"
          value2 = "#{value}/#{item_id}"
          if (@trait_ids_L2[category_id] != nil && @trait_ids_L2[category_id][item_id] != nil) then
            @trait_ids_L2[category_id][item_id].each_with_index do |trait_id,i|
              trait_code = @talent_L2["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_code"]
              key3 = "#{trait_code}_L2:#{key2}/#{@traits_L2[category_id][item_id][i]}"
              value3 = "L2:#{value2}/#{trait_id}"

              trait_L2_all[key3] = value3
            end
          end
        end
      end
    end
    return trait_L2_all
  end


  # 應用指標 L3
  def setup_L3()
    cmd = params["cmd"]
    if (cmd != nil) then
      setup_cmd_L3(cmd,params)
    end
    @h_copy_L3 = Pm.findTestDbHashValue_key("talent_copy_L3")
  end
  def setup_cmd_L3(cmd,h)
    target = h["target"]
    if (cmd == "c") then # copy
      h_copy = get_one_copy_L3(target)
      h_copy["target"] = target
      Pm.updateTestDbHashValue_key("talent_copy_L3",h_copy)
    elsif (cmd == "p") then # paste
      h_copy = Pm.findTestDbHashValue_key("talent_copy_L3")
      target = h_copy["target"]
      set_one_copy_L3(target,h_copy)
      Pm.updateTestDbHashValue_key("talent_copy_L3",{})
      save_talent_data(@talent_L3,"L3",my_current_lang())
      get_talent_data(h)
    end 
  end
  def get_one_copy_L3(target)
    h_copy = get_one_copy_category_L3(target)

    h_copy = get_one_copy_trait_def_L3(target,h_copy)

    h_copy = get_one_copy_par_setting_L3(target,h_copy)

    return h_copy
  end
  def get_one_copy_category_L3(target)
    h_copy = {}
    if (["category","item","trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id_L3] = @talent_L3["category"][@category_id_L3]
    end
    if (["item","trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id_L3] = {}
      h_copy["category"][@category_id_L3]["item"] = {}
      h_copy["category"][@category_id_L3]["item"][@item_id_L3] = @talent_L3["category"][@category_id_L3]["item"][@item_id_L3]
    end
    if (["trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id_L3] = {}
      h_copy["category"][@category_id_L3]["item"] = {}
      h_copy["category"][@category_id_L3]["item"][@item_id_L3] = {}
      h_copy["category"][@category_id_L3]["item"][@item_id_L3]["trait"] = {}
      h_copy["category"][@category_id_L3]["item"][@item_id_L3]["trait"][@trait_id_L3] = @talent_L3["category"][@category_id_L3]["item"][@item_id_L3]["trait"][@trait_id_L3]
    end
    return h_copy
  end
  def get_one_copy_trait_def_L3(target,h_copy)
    h_copy["trait_def"] = {}
    if ("category" == target) then
      h_copy["trait_def"][@category_id_L3] = @talent_L3["trait_def"][@category_id_L3]
    elsif ("item" == target) then
      h_copy["trait_def"] = {}
      h_copy["trait_def"][@category_id_L3] = {}
      h_copy["trait_def"][@category_id_L3][@item_id_L3] = @talent_L3["trait_def"][@category_id_L3][@item_id_L3]
    elsif ("trait" == target) then
      h_copy["trait_def"] = {}
      h_copy["trait_def"][@category_id_L3] = {}
      h_copy["trait_def"][@category_id_L3][@item_id_L3] = {}
      h_copy["trait_def"][@category_id_L3][@item_id_L3][@trait_id_L3] = @talent_L3["trait_def"][@category_id_L3][@item_id_L3][@trait_id_L3]
    end
    return h_copy
  end
  def get_one_copy_par_setting_L3(target,h_copy)
    h_copy["par_setting"] = {}
    if ("category" == target) then
      h_copy["par_setting"][@category_id_L3] = @talent_L3["par_setting"][@category_id_L3]
    elsif ("item" == target) then
      h_copy["par_setting"] = {}
      h_copy["par_setting"][@category_id_L3] = {}
      h_copy["par_setting"][@category_id_L3][@item_id_L3] = @talent_L3["par_setting"][@category_id_L3][@item_id_L3]
    elsif ("trait" == target) then
      h_copy["par_setting"] = {}
      h_copy["par_setting"][@category_id_L3] = {}
      h_copy["par_setting"][@category_id_L3][@item_id_L3] = {}
      h_copy["par_setting"][@category_id_L3][@item_id_L3][@trait_id_L3] = @talent_L3["par_setting"][@category_id_L3][@item_id_L3][@trait_id_L3]
    end
    return h_copy
  end
  def set_one_copy_L3(target,h_copy)
    t_ids,h_ids = set_one_copy_category_L3(target,h_copy)
    set_one_copy_trait_def_L3(target,h_copy,t_ids,h_ids)
    set_one_copy_par_setting_L3(target,h_copy,t_ids,h_ids)
  end
  def set_one_copy_category_L3(target,h_copy)
    category_id_copy = h_copy["category"].keys[0]
    i = find_new_index("category_",@talent_L3["category"].keys)
    category_id_new = "category_#{i}"
    if ("category" == target) then
      @talent_L3["category"][category_id_new] = h_copy["category"][category_id_copy]
      return [category_id_new,0,0],[category_id_copy,0,0]
    end
    item_id_copy = h_copy["category"][category_id_copy]["item"].keys[0]
    i = find_new_index("item_",@talent_L3["category"][@category_id_L3]["item"].keys)
    item_id_new = "item_#{i}"
    if ("item" == target) then
      @talent_L3["category"][@category_id_L3]["item"][item_id_new]= h_copy["category"][category_id_copy]["item"][item_id_copy]
      return [@category_id_L3,item_id_new,0],[category_id_copy,item_id_copy,0]
    end
    trait_id_copy = h_copy["category"][category_id_copy]["item"][item_id_copy]["trait"].keys[0]
    i = find_new_index("trait_",@talent_L3["category"][@category_id_L3]["item"][@item_id_L3]["trait"].keys)
    trait_id_new = "trait_#{i}"
    if ("trait" == target) then
      @talent_L3["category"][@category_id_L3]["item"][@item_id_L3]["trait"][trait_id_new] = h_copy["category"][category_id_copy]["item"][item_id_copy]["trait"][trait_id_copy]
      return [@category_id_L3,@item_id_L3,trait_id_new],[category_id_copy,item_id_copy,trait_id_copy]
    end
    return [nil,nil,nil],[nil,nil,nil]
  end
  def set_one_copy_trait_def_L3(target,h_copy,t_ids,h_ids)
    if ("category" == target) then
      @talent_L3["trait_def"][t_ids[0]] = h_copy["trait_def"][h_ids[0]]
    elsif ("item" == target) then
      @talent_L3["trait_def"][t_ids[0]][t_ids[1]] = h_copy["trait_def"][h_ids[0]][h_ids[1]]
    elsif ("trait" == target) then
      @talent_L3["trait_def"][t_ids[0]][t_ids[1]][t_ids[2]] = h_copy["trait_def"][h_ids[0]][h_ids[1]][h_ids[2]]
    end
  end
  def set_one_copy_par_setting_L3(target,h_copy,t_ids,h_ids)
    if ("category" == target) then
      @talent_L3["par_setting"][t_ids[0]] = h_copy["par_setting"][h_ids[0]]
    elsif ("item" == target) then
      @talent_L3["par_setting"][t_ids[0]][t_ids[1]] = h_copy["par_setting"][h_ids[0]][h_ids[1]]
    elsif ("trait" == target) then
      @talent_L3["par_setting"][t_ids[0]][t_ids[1]][t_ids[2]] = h_copy["par_setting"][h_ids[0]][h_ids[1]][h_ids[2]]
    end
  end
  def rule_L3_new()
    rule_L3s = Array.new(10) {{"rl_use" => false, "rl_reverse" => false, "rl_trait_L1" => "category_1/item_1/trait_1", "rl_weight" => 100}}
    return rule_L3s
  end
  def setup_rule_L3_cmd(cmd,hPar)
    rule_L3s = @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3][@rule_L3] == nil ? rule_L3_new() : @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3][@rule_L3]
    if (cmd == "a") then
      @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3][@rule_L3] = rule_L3_setting(rule_L3s,hPar)
    elsif (cmd == "d") then
      @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3][@rule_L3] = rule_L3_new()
    elsif (cmd == "e") then
      @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3][@rule_L3] = rule_L3_setting(rule_L3s,hPar)
    elsif (cmd == "c") then # copy
      h_copy = {}
      h_copy["data"] = @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3][@rule_L3]
      h_copy["target"] = "setup_rule_L3"
      Pm.updateTestDbHashValue_key("setup_rule_L3_copy",h_copy)
    elsif (cmd == "p") then # paste
      h_copy = Pm.findTestDbHashValue_key("setup_rule_L3_copy")
      return if h_copy == nil || h_copy == {}
      target = h_copy["target"]
      @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3][@rule_L3] = h_copy["data"]
      @talent_L3["par_setting"][@category_id_L3][@item_id_L3][@trait_id_L3][@rule_L3] = @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3][@rule_L3]
      save_talent_data(@talent_L3,"L3",my_current_lang())
      get_talent_data(hPar)
    end 
  end
  def rule_L3_setting(rule_L3s,hPar)
    (0...10).each do |rule_L3_index|
      rule_L3s[rule_L3_index]["rl_use"] = Cfate.ValueBoolCheck(hPar["rl_use_#{rule_L3_index}"])
      rule_L3s[rule_L3_index]["rl_reverse"] = Cfate.ValueBoolCheck(hPar["rl_reverse_#{rule_L3_index}"])
      rule_L3s[rule_L3_index]["rl_trait_L1"] = hPar["rl_trait_L1_#{rule_L3_index}"]
      rule_L3s[rule_L3_index]["rl_weight"] = hPar["rl_weight_#{rule_L3_index}"].to_i
    end
    return rule_L3s
  end
  def setup_rule_L3()
    save_session_data()

    get_talent_data(params)
    get_stars_for_select(params)
    @cmd = params["cmd"]
    mode = params["mode"]

    if (@cmd == "a") 
      @cur_rule_L3 = rule_L3_new()
    else
      @cur_rule_L3 = @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3][@rule_L3]
      @cur_rule_L3 = rule_L3_new() if @cur_rule_L3 == nil || @cur_rule_L3 == {}
    end

    if (@cmd != nil && mode == "submit") then
      setup_rule_L3_cmd(@cmd,params)
      set_talent_data()
      set_global_var(@category_level)

      if (@cmd == "a" || @cmd == "e" || @cmd == "d" || @cmd == "c" || @cmd == "p") then
        # redirect_to :action => "setup", :category_id => @category_id_L3, :item_id => @item_id_L3, :trait_id => @trait_id_L3, :category_level => @category_level
      end
    else
      set_global_var(@category_level)
    end
  end
  def item_trait_id_L3(h,category_level)
    @item_id_L3 = h["item_id"]
    @trait_id_L3 = h["trait_id"]
    # @category_id = "" if @category_id == nil
    @item_id_L3 = "" if @item_id_L3 == nil
    @trait_id_L3 = "" if @trait_id_L3 == nil || @item_id_L3 == ""
    @trait_id_hash_L3 = {} if @item_id_L3 == ""
  end
  def update_new_par_setting_L3()
  end
  def set_global_var_L3(category_level)
    @h_copy = @h_copy_L3
    @talent = @talent_L3
    @category_id = @category_id_L3
    @item_id = @item_id_L3
    @trait_id = @trait_id_L3
    @par_setting = @par_setting_L3
    @category_ids = @category_ids_L3
    @trait_def = @trait_def_L3
    @traits_desc = @traits_desc_L3
    @traits_score_desc = @traits_score_desc_L3
    @trait_code = @trait_code_L3
    @trait_code_cur_trait = @trait_code_cur_trait_L3
    @categorys = @categorys_L3
    @item_ids = @item_ids_L3
    @items = @items_L3
    @trait_ids = @trait_ids_L3
    @traits = @traits_L3
    @category_id_hash = @category_id_hash_L3
    @item_id_hash = @item_id_hash_L3
    @trait_id_hash = @trait_id_hash_L3
    @trait_id_code_hash = @trait_id_code_hash_L3
    @trait_score_desc_cur_trait = @trait_score_desc_cur_trait_L3
    @trait_desc_cur_trait = @trait_desc_cur_trait_L3
    @trait_def_cur_trait = @trait_def_cur_trait_L3
    @cur_rule = @cur_rule_L3
    @rule = @rule_L3
    @trait_List_all = @trait_L2_all
    @traits_code = @traits_code_L3
  end
  # L2
  def category_cmd_L3(cmd,category,category_id)
    if (cmd == "a") then
      category_add_L3(category)
    elsif (cmd == "d") then
      category_remove_L3(category_id)
    elsif (cmd == "e") then
      category_edit_L3(category_id,category)
    elsif (cmd == "cc") then # change category
      category_change_L3(category_id,nil,nil)
    end 
  end
  def category_add_L3(category)
    if (category != nil) then
      @categorys_L3.push(category)
      category_id = next_category_id(@category_ids_L3.last)
      @category_ids_L3.push(category_id)

      # add item
      @trait_def_L3 = {} if @trait_def_L3 == nil
      @trait_def_L3[category_id] = {}

      @par_setting_L3 = {} if @par_setting_L3 == nil
      @par_setting_L3[category_id] = {}

      @traits_desc_L3[category_id] = {}
      @traits_score_desc_L3[category_id] = {}
      @traits_code_L3[category_id] = {}
    end
  end
  def category_edit_L3(category_id,category)
    if (category_id != nil && category != nil) then
      i = @category_ids_L3.index(category_id)
      if (i == nil) then return end
      @categorys_L3[i] = category
    end
  end
  def category_remove_L3(category_id)
    if (category_id != nil) then
      i = @category_ids_L3.index(category_id)
      if (i == nil) then return end
      @categorys_L3.delete_at(i)
      @category_ids_L3.delete_at(i)

      # remove item
      @items_L3.delete(category_id)
      @item_ids_L3.delete(category_id)

    # remove trait
      @traits_L3.delete(category_id)
      @trait_ids_L3.delete(category_id)

      # remove trait_def
      @trait_def_L3.delete(category_id) if @trait_def_L3 != nil
      @par_setting_L3.delete(category_id) if @par_setting_L3 != nil

      @traits_desc_L3.delete(category_id) if @traits_desc_L3 != nil
      @traits_score_desc_L3.delete(category_id) if @traits_score_desc_L3 != nil
      @traits_code_L3.delete(category_id) if @traits_code_L3 != nil
    end
  end
  def category_change_L3(category_id,item_id,trait_id)
    @category_id_L3 = category_id if category_id != nil
    @item_id_L3 = item_id if item_id != nil
    @trait_id_L3 = trait_id if trait_id != nil

    # get setting parameters
  end
  # L3
  def item_cmd_L3(cmd,category_id,item,item_id)
    if (cmd == "a") then
      item_add_L3(category_id,item)
    elsif (cmd == "d") then
      item_remove_L3(category_id,item_id)
    elsif (cmd == "e") then
      item_edit_L3(category_id,item_id,item)
    elsif (cmd == "cc") then # change category
      category_change_L3(category_id,item_id,nil)
    end 
  end
  def item_add_L3(category_id,item)
    if (item != nil) then
      @items_L3[category_id].push(item) if @items_L3[category_id] != nil
      item_id = next_item_id(@item_ids_L3[category_id].last)
      @item_ids_L3[category_id].push(item_id) if @item_ids_L3[category_id] != nil

      @trait_def_L3[category_id] = {} if @trait_def_L3[category_id] == nil
      @trait_def_L3[category_id][item_id] = {}

      @par_setting_L3[category_id] = {} if @par_setting_L3[category_id] == nil
      @par_setting_L3[category_id][item_id] = {}

      @traits_desc_L3[category_id][item_id] = {}
      @traits_score_desc_L3[category_id][item_id] = {}
      @traits_code_L3[category_id][item_id] = {}
    end
  end
  def item_remove_L3(category_id,item_id)
    if (item_id != nil) then
      i = @item_ids_L3[category_id].index(item_id)
      if (i == nil) then return end
      @items_L3[category_id].delete_at(i)
      @item_ids_L3[category_id].delete_at(i)

      # remove trait
      @traits_L3[category_id].delete(item_id)
      @trait_ids_L3[category_id].delete(item_id)

      # remove @trait_def_L3
      @trait_def_L3[category_id].delete(item_id) if @trait_def_L3[category_id] != nil
      @par_setting_L3[category_id].delete(item_id) if @par_setting_L3[category_id] != nil

      @traits_desc_L3[category_id].delete(item_id)
      @traits_score_desc_L3[category_id].delete(item_id)
      @traits_code_L3[category_id].delete(item_id)
    end
  end
  def item_edit_L3(category_id,item_id,item)
    if (item_id != nil && item != nil) then
      i = @item_ids_L3[category_id].index(item_id)
      if (i == nil) then return end
      @items_L3[category_id][i] = item
    end
  end
  def trait_cmd_L3(cmd,category_id,item_id,trait,trait_id,trait_code)
    if (cmd == "a") then
      trait_add_L3(category_id,item_id,trait,trait_code)
    elsif (cmd == "d") then
      trait_remove_L3(category_id,item_id,trait_id)
    elsif (cmd == "e") then
      trait_edit_L3(category_id,item_id,trait_id,trait,trait_code)
    elsif (cmd == "cc") then # change category
      category_change_L3(category_id,item_id,trait_id)
    end 
  end
  def trait_add_L3(category_id,item_id,trait,trait_code)
    if (trait != nil) then
      @traits_L3[category_id][item_id].push(trait) if @traits_L3[category_id][item_id] != nil
      trait_id = next_trait_id(@trait_ids_L3[category_id][item_id].last)
      @trait_ids_L3[category_id][item_id].push(trait_id) if @trait_ids_L3[category_id][item_id] != nil
        
      @trait_def_L3[category_id][item_id][trait_id] = trait_def_empty()
      @par_setting_L3[category_id][item_id][trait_id] = par_setting_new()

      @traits_desc_L3[category_id][item_id][trait_id] = ""
      @traits_score_desc_L3[category_id][item_id][trait_id] = ""
      @traits_code_L3[category_id][item_id][trait_id] = trait_code
    end
  end
  def trait_edit_L3(category_id,item_id,trait_id,trait,trait_code)
    if (trait_id != nil && trait != nil) then
      i = @trait_ids_L3[category_id][item_id].index(trait_id)
      if (i == nil) then return end
      @traits_L3[category_id][item_id][i] = trait
      @traits_code_L3[category_id][item_id][trait_id] = trait_code
    end
  end
  def trait_remove_L3(category_id,item_id,trait_id)
    if (trait_id != nil) then
      i = @trait_ids_L3[category_id][item_id].index(trait_id)
      if (i == nil) then return end
      @traits_L3[category_id][item_id].delete_at(i)
      @trait_ids_L3[category_id][item_id].delete_at(i)

      @traits_desc_L3[category_id][item_id].delete(trait_id)
      @traits_score_desc_L3[category_id][item_id].delete(trait_id)
      @traits_code_L3[category_id][item_id].delete(trait_id)
      # remove @trait_def_L3
      if (@trait_def_L3[category_id] != nil && @trait_def_L3[category_id][item_id] != nil) then
        @trait_def_L3[category_id][item_id].delete(trait_id)
      end

      if (@par_setting_L3[category_id] != nil && @par_setting_L3[category_id][item_id] != nil) then
        @par_setting_L3[category_id][item_id].delete(trait_id)
      end
    end
  end
  def read_talent_data_L3(current_lang=Cfate::DEFAULT_LANG)
    key = Talent.get_talent_key("L3",current_lang)
    return Pm.getSystemValue_hash(key)
  end
  def save_talent_data_L3(h,current_lang=Cfate::DEFAULT_LANG)
    key = Talent.get_talent_key("L3",current_lang)
    hOrg = Pm.getSystemValue_hash(key)

    if (h["version"] == nil) then
      h["version"] = {}
    end
    categorys = h["category"].keys
    categorys.each do |category_id|
      if (h["version"][category_id] == nil) || (!check_category_the_same(hOrg,h,category_id)) then
        h["version"][category_id] = Xdate.make_now_timestamp2()
      end
    end
    Pm.setSystemValue_hash(key,h)

    value_categorys = categorys.join(",")
    Pm.setSystemValue("#{key}_categorys",value_categorys)

    categorys.each do |category_id|
      h2 = {}
      h2["category"] = {}
      h2["trait_def"] = {}
      h2["par_setting"] = {}
      h2["version"] = {}
      h2["category"][category_id] = h["category"][category_id]
      h2["trait_def"][category_id] = h["trait_def"][category_id]
      h2["par_setting"][category_id] = h["par_setting"][category_id]
      h2["version"][category_id] = h["version"][category_id]
      Pm.setSystemValue_hash("#{key}_#{category_id}",h2)
    end
  end
  def get_talent_data_L3(h)
    category_level = "L3"
    @talent_L3 = read_talent_data(category_level,my_current_lang())
    # @category_id_L3 = h["category_id"] == nil ? "category_1" : h["category_id"]
    # @item_id_L3 = h["item_id"] == nil ? "item_1" : h["item_id"]
    # @trait_id_L3 = h["trait_id"] == nil ? "trait_1" : h["trait_id"]
    @category_id_L3 = h["category_id"]
    @item_id_L3 = h["item_id"]
    @trait_id_L3 = h["trait_id"]
    @trait_code_L3 = h["trait_code"]
    @category_id_L3,@item_id_L3,@trait_id_L3 = trait_code_2_c_i_t(@talent_L3,@trait_code_L3,@category_id_L3,@item_id_L3,@trait_id_L3)
    if @trait_code_L3 == nil && @trait_code_L3 == "" && @category_id_L3 != nil && @category_id_L3 != "" && @item_id_L3 != nil && @item_id_L3 != "" && @trait_id_L3 != nil && @trait_id_L3 != "" then
      @trait_code_L3 = @talent_L3["category"][@category_id_L3]["item"][@item_id_L3]["trait"][@trait_id_L3]["trait_code"] if @trait_code_L3 == nil
    end
    @rule_L3 = h["rule_L3"]
    @rule_L3 = "Rule" if @rule_L3 == nil

    parse_talent_hash(category_level)
    if (@par_setting_L3[@category_id_L3] != nil && @par_setting_L3[@category_id_L3][@item_id_L3] != nil && @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3] != nil ) then
      @cur_rule_L3 = @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3][@rule_L3]
    end
    @cur_rule_L3 = rule_L3_new() if @cur_rule_L3 == nil || @cur_rule_L3 == {}      
  end
  def make_talent_hash_L3()
    make_talent_hash_category_L3()
    make_talent_hash_trait_def_L3()
    make_talent_hash_par_setting_L3()
  end
  def make_talent_hash_category_L3()
    @talent_L3["category"] = {}
    @category_ids_L3.each_with_index do |category_id,i|
      one_category = {"desc" => @categorys_L3[i]}
      one_category["item"] = {}
      if (@item_ids_L3[category_id] != nil) then
        @item_ids_L3[category_id].each_with_index do |item_id,i|
          one_category["item"][item_id] = {"desc" => @items_L3[category_id][i]}
          one_category["item"][item_id]["trait"] = {}
          if (@trait_ids_L3[category_id] != nil && @trait_ids_L3[category_id][item_id] != nil) then
            @trait_ids_L3[category_id][item_id].each_with_index do |trait_id,i|
              one_category["item"][item_id]["trait"][trait_id] = {"desc" => @traits_L3[category_id][item_id][i]}
              one_category["item"][item_id]["trait"][trait_id]["trait_desc"] = @traits_desc_L3[category_id][item_id][trait_id]
              one_category["item"][item_id]["trait"][trait_id]["trait_score_desc"] = @traits_score_desc_L3[category_id][item_id][trait_id]
              one_category["item"][item_id]["trait"][trait_id]["trait_code"] = @traits_code_L3[category_id][item_id][trait_id]
            end
          end
        end
      end
      @talent_L3["category"][category_id] = one_category.clone
    end
  end
  def make_talent_hash_trait_def_L3()
    @talent_L3["trait_def"] = @trait_def_L3
  end
  def make_talent_hash_par_setting_L3()
    @talent_L3["par_setting"] = @par_setting_L3
  end
  def parse_talent_hash_L3()
    parse_talent_hash_category_L3()
    parse_talent_hash_trait_def_L3()
    parse_talent_hash_par_setting_L3()
  end
  def parse_talent_hash_category_L3()
    @talent_L3["category"] = {} if @talent_L3["category"] == nil
    @category_ids_L3 = @talent_L3["category"].keys.clone
    @categorys_L3 = []
    @category_id_hash_L3 = {}
    @item_ids_L3 = {}
    @items_L3 = {}
    @item_id_hash_L3 = {}
    @trait_ids_L3 = {}
    @traits_L3 = {}
    @trait_id_hash_L3 = {}
    @trait_id_code_hash_L3 = {}
    @traits_desc_L3 = {}
    @traits_score_desc_L3 = {}
    @traits_code_L3 = {}
    @category_ids_L3.each_with_index do |category_id,i|
      category_desc = @talent_L3["category"][category_id]["desc"]
      category_desc += "_1" if (@categorys_L3.include?(category_desc))
      @categorys_L3.push(category_desc)
      @category_id_hash_L3[category_desc] = category_id
      
      @item_ids_L3[category_id] = @talent_L3["category"][category_id]["item"].keys.clone
      @items_L3[category_id] = []
      @item_ids_L3[category_id].each_with_index do |item_id,i|
        item_desc = @talent_L3["category"][category_id]["item"][item_id]["desc"]
        item_desc += "_1" if (@items_L3[category_id].include?(item_desc))
        @items_L3[category_id].push(item_desc)
        @item_id_hash_L3[item_desc] = item_id if (@category_id_L3 == category_id)
      end
    end
    @item_id_L3 = @item_ids_L3[@category_id_L3][0] if (@item_ids_L3[@category_id_L3] != nil && @item_ids_L3[@category_id_L3].index(@item_id_L3) == nil)
    @item_id_L3 = @item_id_L3 == nil ? "item_1" : @item_id_L3

    @category_ids_L3.each_with_index do |category_id,i|
      @trait_ids_L3[category_id] = {}
      @traits_L3[category_id] = {}
      @traits_desc_L3[category_id] = {}
      @traits_score_desc_L3[category_id] = {}
      @traits_code_L3[category_id] = {}
      @item_ids_L3[category_id].each_with_index do |item_id,i|
        @traits_L3[category_id][item_id] = []
        @traits_desc_L3[category_id][item_id] = {}
        @traits_score_desc_L3[category_id][item_id] = {}
        @traits_code_L3[category_id][item_id] = {}
        t = @talent_L3["category"][category_id]["item"][item_id]["trait"]
        @trait_ids_L3[category_id][item_id] = t.keys.clone
        @trait_ids_L3[category_id][item_id].each_with_index do |trait_id,i|
          trait_desc = t[trait_id]["desc"]
          trait_desc += "_1" if (@traits_L3[category_id][item_id].include?(trait_desc))
          @traits_L3[category_id][item_id].push(trait_desc)
          @traits_code_L3[category_id][item_id][trait_id] = t[trait_id]["trait_code"]
          trait_code = @traits_code_L3[category_id][item_id][trait_id]
          @trait_id_hash_L3["#{trait_desc}"] = trait_id if (@category_id_L3 == category_id && @item_id_L3 == item_id)
          @trait_id_code_hash_L3["#{trait_code}:#{trait_desc}"] = trait_id if (@category_id_L3 == category_id && @item_id_L3 == item_id)

          @traits_desc_L3[category_id][item_id][trait_id] = t[trait_id]["trait_desc"]
          @traits_score_desc_L3[category_id][item_id][trait_id] = t[trait_id]["trait_score_desc"]
        end
      end
    end
    @trait_id_L3 = @trait_ids_L3[@category_id_L3][@item_id_L3][0] if (@trait_ids_L3[@category_id_L3] != nil && @trait_ids_L3[@category_id_L3][@item_id_L3] != nil && @trait_ids_L3[@category_id_L3][@item_id_L3].index(@trait_id_L3) == nil)
    @trait_id_L3 = @trait_id_L3 == nil ? "trait_1" : @trait_id_L3

    if (@trait_id_hash_L3.length > 0) then
      @trait_desc_cur_trait_L3 = @traits_desc_L3[@category_id_L3][@item_id_L3][@trait_id_L3]
      @trait_score_desc_cur_trait_L3 = @traits_score_desc_L3[@category_id_L3][@item_id_L3][@trait_id_L3]
      @trait_code_cur_trait_L3 = @traits_code_L3[@category_id_L3][@item_id_L3][@trait_id_L3]
    else
      @trait_desc_cur_trait_L3 = ""
      @trait_score_desc_cur_trait_L3 = ""
      @trait_code_cur_trait_L3 = ""
    end
  end
  def parse_talent_hash_trait_def_L3()
    if @talent_L3["trait_def"] == nil || @talent_L3["trait_def"] == {} then
      @trait_def_L3 = {}
      @category_ids_L3.each_with_index do |category_id,i|
          @trait_def_L3[category_id] = {}
          @item_ids_L3[category_id].each_with_index do |item_id,i|
              @trait_def_L3[category_id][item_id] = {}
            @trait_ids_L3[category_id][item_id].each_with_index do |trait_id,i|
              @trait_def_L3[category_id][item_id][trait_id] = trait_def_empty()
            end
          end
      end
    else
      @trait_def_L3 = @talent_L3["trait_def"]
    end
    @trait_def_L3[@category_id_L3][@item_id_L3][@trait_id_L3] = trait_def_empty() if (@trait_def_L3[@category_id_L3] != nil && @trait_def_L3[@category_id_L3][@item_id_L3] != nil && @trait_def_L3[@category_id_L3][@item_id_L3][@trait_id_L3] == nil)
    if (@trait_def_L3[@category_id_L3] == nil || @trait_def_L3[@category_id_L3][@item_id_L3] == nil || @trait_def_L3[@category_id_L3][@item_id_L3][@trait_id_L3] == nil) then
      @trait_def_cur_trait_L3 = trait_def_empty()
    else
      @trait_def_cur_trait_L3 = @trait_def_L3[@category_id_L3][@item_id_L3][@trait_id_L3]
    end
  end
  def get_trait_def_L3(h)
    @trait_desc_cur_trait_L3 = h["trait_desc"] == nil ? "" : h["trait_desc"]
    @trait_score_desc_cur_trait_L3 = h["trait_score_desc"] == nil ? "" : h["trait_score_desc"]
    @trait_code_cur_trait_L3 = h["trait_code"] == nil ? "" : h["trait_code"]

    @traits_desc_L3[@category_id_L3][@item_id_L3][@trait_id_L3] = @trait_desc_cur_trait_L3
    @traits_score_desc_L3[@category_id_L3][@item_id_L3][@trait_id_L3] = @trait_score_desc_cur_trait_L3
    @traits_code_L3[@category_id_L3][@item_id_L3][@trait_id_L3] = @trait_code_cur_trait_L3

    (1..4).each do |i|
      @trait_def_L3[@category_id_L3][@item_id_L3][@trait_id_L3]["score_drop_#{i}"]["up"] = h["score_drop_up_#{i}"]
      @trait_def_L3[@category_id_L3][@item_id_L3][@trait_id_L3]["score_drop_#{i}"]["down"] = h["score_drop_down_#{i}"]
      @trait_def_L3[@category_id_L3][@item_id_L3][@trait_id_L3]["score_drop_#{i}"]["trait_content"] = h["trait_content_#{i}"]
      @trait_def_L3[@category_id_L3][@item_id_L3][@trait_id_L3]["score_drop_#{i}"]["proposal"] = h["proposal_#{i}"]
    end
  end
  def parse_talent_hash_par_setting_L3()
    if @talent_L3["par_setting"] == nil || @talent_L3["par_setting"] == {} then
      @par_setting_L3 = {}
      @category_ids_L3.each_with_index do |category_id,i|
          @par_setting_L3[category_id] = {}
          @item_ids_L3[category_id].each_with_index do |item_id,i|
            @par_setting_L3[category_id][item_id] = {}
            @trait_ids_L3[category_id][item_id].each_with_index do |trait_id,i|
              @par_setting_L3[category_id][item_id][trait_id] = par_setting_new()
            end
          end
      end
    else
      @par_setting_L3 = @talent_L3["par_setting"]
    end
    # if (@par_setting_L3[@category_id_L3] != nil && @par_setting_L3[@category_id_L3][@item_id_L3] != nil && @par_setting_L3@category_id_L3][@item_id_L3][@trait_id_L3] == nil) then
   #  if (@trait_id_hash_L3.length > 0) then
    #   @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3] = {} 
    # end
    # @cur_stars_weight_L3 = @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3]["stars_weight"]
    # @cur_sihuas_weight_L3 = @par_setting_L3[@category_id_L3][@item_id_L3][@trait_id_L3]["sihuas_weight"]
  end
  def get_trait_L3_all(h)
    get_talent_data_L3(h)
    trait_L3_all = {}
    key = ""
    value = ""
    @category_ids_L3.each_with_index do |category_id,i|
      key = @categorys_L3[i]
      value = category_id
      if (@item_ids_L3[category_id] != nil) then
        @item_ids_L3[category_id].each_with_index do |item_id,i|
          key2 = "#{key}/#{@items_L3[category_id][i]}"
          value2 = "#{value}/#{item_id}"
          if (@trait_ids_L3[category_id] != nil && @trait_ids_L3[category_id][item_id] != nil) then
            @trait_ids_L3[category_id][item_id].each_with_index do |trait_id,i|
              trait_code = @talent_L3["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_code"]
              key3 = "#{trait_code}_L3:#{key2}/#{@traits_L3[category_id][item_id][i]}"
              value3 = "L3:#{value2}/#{trait_id}"

              trait_L3_all[key3] = value3
            end
          end
        end
      end
    end
    return trait_L3_all
  end


  # 輸出指標 MP
  def setup_MP()
    cmd = params["cmd"]
    if (cmd != nil) then
      setup_cmd_MP(cmd,params)
    end
    @h_copy_MP = Pm.findTestDbHashValue_key("talent_copy_MP")
  end
  def setup_cmd_MP(cmd,h)
    target = h["target"]
    if (cmd == "c") then # copy
      h_copy = get_one_copy_MP(target)
      h_copy["target"] = target
      Pm.updateTestDbHashValue_key("talent_copy_MP",h_copy)
    elsif (cmd == "p") then # paste
      h_copy = Pm.findTestDbHashValue_key("talent_copy_MP")
      target = h_copy["target"]
      set_one_copy_MP(target,h_copy)
      Pm.updateTestDbHashValue_key("talent_copy_MP",{})
      save_talent_data(@talent_MP,"MP",my_current_lang())
      get_talent_data(h)
    end 
  end
  def get_one_copy_MP(target)
    h_copy = get_one_copy_category_MP(target)

    h_copy = get_one_copy_trait_def_MP(target,h_copy)

    h_copy = get_one_copy_par_setting_MP(target,h_copy)

    return h_copy
  end
  def get_one_copy_category_MP(target)
    h_copy = {}
    if (["category","item","trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id_MP] = @talent_MP["category"][@category_id_MP]
    end
    if (["item","trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id_MP] = {}
      h_copy["category"][@category_id_MP]["item"] = {}
      h_copy["category"][@category_id_MP]["item"][@item_id_MP] = @talent_MP["category"][@category_id_MP]["item"][@item_id_MP]
    end
    if (["trait"].include?(target)) then
      h_copy["category"] = {}
      h_copy["category"][@category_id_MP] = {}
      h_copy["category"][@category_id_MP]["item"] = {}
      h_copy["category"][@category_id_MP]["item"][@item_id_MP] = {}
      h_copy["category"][@category_id_MP]["item"][@item_id_MP]["trait"] = {}
      h_copy["category"][@category_id_MP]["item"][@item_id_MP]["trait"][@trait_id_MP] = @talent_MP["category"][@category_id_MP]["item"][@item_id_MP]["trait"][@trait_id_MP]
    end
    return h_copy
  end
  def get_one_copy_trait_def_MP(target,h_copy)
    h_copy["trait_def"] = {}
    if ("category" == target) then
      h_copy["trait_def"][@category_id_MP] = @talent_MP["trait_def"][@category_id_MP]
    elsif ("item" == target) then
      h_copy["trait_def"] = {}
      h_copy["trait_def"][@category_id_MP] = {}
      h_copy["trait_def"][@category_id_MP][@item_id_MP] = @talent_MP["trait_def"][@category_id_MP][@item_id_MP]
    elsif ("trait" == target) then
      h_copy["trait_def"] = {}
      h_copy["trait_def"][@category_id_MP] = {}
      h_copy["trait_def"][@category_id_MP][@item_id_MP] = {}
      h_copy["trait_def"][@category_id_MP][@item_id_MP][@trait_id_MP] = @talent_MP["trait_def"][@category_id_MP][@item_id_MP][@trait_id_MP]
    end
    return h_copy
  end
  def get_one_copy_par_setting_MP(target,h_copy)
    h_copy["par_setting"] = {}
    if ("category" == target) then
      h_copy["par_setting"][@category_id_MP] = @talent_MP["par_setting"][@category_id_MP]
    elsif ("item" == target) then
      h_copy["par_setting"] = {}
      h_copy["par_setting"][@category_id_MP] = {}
      h_copy["par_setting"][@category_id_MP][@item_id_MP] = @talent_MP["par_setting"][@category_id_MP][@item_id_MP]
    elsif ("trait" == target) then
      h_copy["par_setting"] = {}
      h_copy["par_setting"][@category_id_MP] = {}
      h_copy["par_setting"][@category_id_MP][@item_id_MP] = {}
      h_copy["par_setting"][@category_id_MP][@item_id_MP][@trait_id_MP] = @talent_MP["par_setting"][@category_id_MP][@item_id_MP][@trait_id_MP]
    end
    return h_copy
  end
  def set_one_copy_MP(target,h_copy)
    t_ids,h_ids = set_one_copy_category_MP(target,h_copy)
    set_one_copy_trait_def_MP(target,h_copy,t_ids,h_ids)
    set_one_copy_par_setting_MP(target,h_copy,t_ids,h_ids)
  end
  def set_one_copy_category_MP(target,h_copy)
    category_id_copy = h_copy["category"].keys[0]
    i = find_new_index("category_",@talent_MP["category"].keys)
    category_id_new = "category_#{i}"
    if ("category" == target) then
      @talent_MP["category"][category_id_new] = h_copy["category"][category_id_copy]
      return [category_id_new,0,0],[category_id_copy,0,0]
    end
    item_id_copy = h_copy["category"][category_id_copy]["item"].keys[0]
    i = find_new_index("item_",@talent_MP["category"][@category_id_MP]["item"].keys)
    item_id_new = "item_#{i}"
    if ("item" == target) then
      @talent_MP["category"][@category_id_MP]["item"][item_id_new]= h_copy["category"][category_id_copy]["item"][item_id_copy]
      return [@category_id_MP,item_id_new,0],[category_id_copy,item_id_copy,0]
    end
    trait_id_copy = h_copy["category"][category_id_copy]["item"][item_id_copy]["trait"].keys[0]
    i = find_new_index("trait_",@talent_MP["category"][@category_id_MP]["item"][@item_id_MP]["trait"].keys)
    trait_id_new = "trait_#{i}"
    if ("trait" == target) then
      @talent_MP["category"][@category_id_MP]["item"][@item_id_MP]["trait"][trait_id_new] = h_copy["category"][category_id_copy]["item"][item_id_copy]["trait"][trait_id_copy]
      return [@category_id_MP,@item_id_MP,trait_id_new],[category_id_copy,item_id_copy,trait_id_copy]
    end
    return [nil,nil,nil],[nil,nil,nil]
  end
  def set_one_copy_trait_def_MP(target,h_copy,t_ids,h_ids)
    if ("category" == target) then
      @talent_MP["trait_def"][t_ids[0]] = h_copy["trait_def"][h_ids[0]]
    elsif ("item" == target) then
      @talent_MP["trait_def"][t_ids[0]][t_ids[1]] = h_copy["trait_def"][h_ids[0]][h_ids[1]]
    elsif ("trait" == target) then
      @talent_MP["trait_def"][t_ids[0]][t_ids[1]][t_ids[2]] = h_copy["trait_def"][h_ids[0]][h_ids[1]][h_ids[2]]
    end
  end
  def set_one_copy_par_setting_MP(target,h_copy,t_ids,h_ids)
    if ("category" == target) then
      @talent_MP["par_setting"][t_ids[0]] = h_copy["par_setting"][h_ids[0]]
    elsif ("item" == target) then
      @talent_MP["par_setting"][t_ids[0]][t_ids[1]] = h_copy["par_setting"][h_ids[0]][h_ids[1]]
    elsif ("trait" == target) then
      @talent_MP["par_setting"][t_ids[0]][t_ids[1]][t_ids[2]] = h_copy["par_setting"][h_ids[0]][h_ids[1]][h_ids[2]]
    end
  end
  def rule_MP_new()
    rule_MPs = Array.new(10) {{"rl_use" => false, "rl_reverse" => false, "rl_trait_L1" => "category_1/item_1/trait_1", "rl_weight" => 100}}
    return rule_MPs
  end
  def setup_rule_MP_cmd(cmd,hPar)
    rule_MPs = @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP][@rule_MP] == nil ? rule_MP_new() : @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP][@rule_MP]
    if (cmd == "a") then
      @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP][@rule_MP] = rule_MP_setting(rule_MPs,hPar)
    elsif (cmd == "d") then
      @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP][@rule_MP] = rule_MP_new()
    elsif (cmd == "e") then
      @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP][@rule_MP] = rule_MP_setting(rule_MPs,hPar)
    elsif (cmd == "c") then # copy
      h_copy = {}
      h_copy["data"] = @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP][@rule_MP]
      h_copy["target"] = "setup_rule_MP"
      Pm.updateTestDbHashValue_key("setup_rule_MP_copy",h_copy)
    elsif (cmd == "p") then # paste
      h_copy = Pm.findTestDbHashValue_key("setup_rule_MP_copy")
      return if h_copy == nil || h_copy == {}
      target = h_copy["target"]
      @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP][@rule_MP] = h_copy["data"]
      @talent_MP["par_setting"][@category_id_MP][@item_id_MP][@trait_id_MP][@rule_MP] = @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP][@rule_MP]
      save_talent_data(@talent_MP,"MP",my_current_lang())
      get_talent_data(hPar)
    end 
  end
  def rule_MP_setting(rule_MPs,hPar)
    (0...10).each do |rule_MP_index|
      rule_MPs[rule_MP_index]["rl_use"] = Cfate.ValueBoolCheck(hPar["rl_use_#{rule_MP_index}"])
      rule_MPs[rule_MP_index]["rl_reverse"] = Cfate.ValueBoolCheck(hPar["rl_reverse_#{rule_MP_index}"])
      rule_MPs[rule_MP_index]["rl_trait_L1"] = hPar["rl_trait_L1_#{rule_MP_index}"]
      rule_MPs[rule_MP_index]["rl_weight"] = hPar["rl_weight_#{rule_MP_index}"].to_i
    end
    return rule_MPs
  end
  def setup_rule_MP()
    save_session_data()

    get_talent_data(params)
    get_stars_for_select(params)
    @cmd = params["cmd"]
    mode = params["mode"]

    if (@cmd == "a") 
      @cur_rule_MP = rule_MP_new()
    else
      @cur_rule_MP = @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP][@rule_MP]
      @cur_rule_MP = rule_MP_new() if @cur_rule_MP == nil || @cur_rule_MP == {}
    end

    if (@cmd != nil && mode == "submit") then
      setup_rule_MP_cmd(@cmd,params)
      set_talent_data()
      set_global_var(@category_level)

      if (@cmd == "a" || @cmd == "e" || @cmd == "d" || @cmd == "c" || @cmd == "p") then
        # redirect_to :action => "setup", :category_id => @category_id_MP, :item_id => @item_id_MP, :trait_id => @trait_id_MP, :category_level => @category_level
      end
    else
      set_global_var(@category_level)
    end
  end
  def item_trait_id_MP(h,category_level)
    @item_id_MP = h["item_id"]
    @trait_id_MP = h["trait_id"]
    # @category_id = "" if @category_id == nil
    @item_id_MP = "" if @item_id_MP == nil
    @trait_id_MP = "" if @trait_id_MP == nil || @item_id_MP == ""
    @trait_id_hash_MP = {} if @item_id_MP == ""
  end
  def update_new_par_setting_MP()
  end
  def set_global_var_MP(category_level)
    @h_copy = @h_copy_MP
    @talent = @talent_MP
    @category_id = @category_id_MP
    @item_id = @item_id_MP
    @trait_id = @trait_id_MP
    @par_setting = @par_setting_MP
    @category_ids = @category_ids_MP
    @trait_def = @trait_def_MP
    @traits_desc = @traits_desc_MP
    @traits_score_desc = @traits_score_desc_MP
    @trait_code = @trait_code_MP
    @trait_code_cur_trait = @trait_code_cur_trait_MP
    @categorys = @categorys_MP
    @item_ids = @item_ids_MP
    @items = @items_MP
    @trait_ids = @trait_ids_MP
    @traits = @traits_MP
    @category_id_hash = @category_id_hash_MP
    @item_id_hash = @item_id_hash_MP
    @trait_id_hash = @trait_id_hash_MP
    @trait_id_code_hash = @trait_id_code_hash_MP
    @trait_id_code_hash = @trait_id_code_hash_MP
    @trait_score_desc_cur_trait = @trait_score_desc_cur_trait_MP
    @trait_desc_cur_trait = @trait_desc_cur_trait_MP
    @trait_def_cur_trait = @trait_def_cur_trait_MP
    @cur_rule = @cur_rule_MP
    @rule = @rule_MP
    @trait_List_all = @trait_L3_all
    @traits_code = @traits_code_MP
  end
  # MP
  def category_cmd_MP(cmd,category,category_id)
    if (cmd == "a") then
      category_add_MP(category)
    elsif (cmd == "d") then
      category_remove_MP(category_id)
    elsif (cmd == "e") then
      category_edit_MP(category_id,category)
    elsif (cmd == "cc") then # change category
      category_change_MP(category_id,nil,nil)
    end 
  end
  def category_add_MP(category)
    if (category != nil) then
      @categorys_MP.push(category)
      category_id = next_category_id(@category_ids_MP.last)
      @category_ids_MP.push(category_id)

      # add item
      @trait_def_MP = {} if @trait_def_MP == nil
      @trait_def_MP[category_id] = {}

      @par_setting_MP = {} if @par_setting_MP == nil
      @par_setting_MP[category_id] = {}

      @traits_desc_MP[category_id] = {}
      @traits_score_desc_MP[category_id] = {}
      @traits_code_MP[category_id] = {}
    end
  end
  def category_edit_MP(category_id,category)
    if (category_id != nil && category != nil) then
      i = @category_ids_MP.index(category_id)
      if (i == nil) then return end
      @categorys_MP[i] = category
    end
  end
  def category_remove_MP(category_id)
    if (category_id != nil) then
      i = @category_ids_MP.index(category_id)
      if (i == nil) then return end
      @categorys_MP.delete_at(i)
      @category_ids_MP.delete_at(i)

      # remove item
      @items_MP.delete(category_id)
      @item_ids_MP.delete(category_id)

    # remove trait
      @traits_MP.delete(category_id)
      @trait_ids_MP.delete(category_id)

      # remove trait_def
      @trait_def_MP.delete(category_id) if @trait_def_MP != nil
      @par_setting_MP.delete(category_id) if @par_setting_MP != nil

      @traits_desc_MP.delete(category_id) if @traits_desc_MP != nil
      @traits_score_desc_MP.delete(category_id) if @traits_score_desc_MP != nil
      @traits_code_MP.delete(category_id) if @traits_code_MP != nil
    end
  end
  def category_change_MP(category_id,item_id,trait_id)
    @category_id_MP = category_id if category_id != nil
    @item_id_MP = item_id if item_id != nil
    @trait_id_MP = trait_id if trait_id != nil

    # get setting parameters
  end
  # MP
  def item_cmd_MP(cmd,category_id,item,item_id)
    if (cmd == "a") then
      item_add_MP(category_id,item)
    elsif (cmd == "d") then
      item_remove_MP(category_id,item_id)
    elsif (cmd == "e") then
      item_edit_MP(category_id,item_id,item)
    elsif (cmd == "cc") then # change category
      category_change_MP(category_id,item_id,nil)
    end 
  end
  def item_add_MP(category_id,item)
    if (item != nil) then
      @items_MP[category_id].push(item) if @items_MP[category_id] != nil
      item_id = next_item_id(@item_ids_MP[category_id].last)
      @item_ids_MP[category_id].push(item_id) if @item_ids_MP[category_id] != nil

      @trait_def_MP[category_id] = {} if @trait_def_MP[category_id] == nil
      @trait_def_MP[category_id][item_id] = {}

      @par_setting_MP[category_id] = {} if @par_setting_MP[category_id] == nil
      @par_setting_MP[category_id][item_id] = {}

      @traits_desc_MP[category_id][item_id] = {}
      @traits_score_desc_MP[category_id][item_id] = {}
      @traits_code_MP[category_id][item_id] = {}
    end
  end
  def item_remove_MP(category_id,item_id)
    if (item_id != nil) then
      i = @item_ids_MP[category_id].index(item_id)
      if (i == nil) then return end
      @items_MP[category_id].delete_at(i)
      @item_ids_MP[category_id].delete_at(i)

      # remove trait
      @traits_MP[category_id].delete(item_id)
      @trait_ids_MP[category_id].delete(item_id)

      # remove @trait_def_MP
      @trait_def_MP[category_id].delete(item_id) if @trait_def_MP[category_id] != nil
      @par_setting_MP[category_id].delete(item_id) if @par_setting_MP[category_id] != nil

      @traits_desc_MP[category_id].delete(item_id)
      @traits_score_desc_MP[category_id].delete(item_id)
      @traits_code_MP[category_id].delete(item_id)
    end
  end
  def item_edit_MP(category_id,item_id,item)
    if (item_id != nil && item != nil) then
      i = @item_ids_MP[category_id].index(item_id)
      if (i == nil) then return end
      @items_MP[category_id][i] = item
    end
  end
  def trait_cmd_MP(cmd,category_id,item_id,trait,trait_id,trait_code)
    if (cmd == "a") then
      trait_add_MP(category_id,item_id,trait,trait_code)
    elsif (cmd == "d") then
      trait_remove_MP(category_id,item_id,trait_id)
    elsif (cmd == "e") then
      trait_edit_MP(category_id,item_id,trait_id,trait,trait_code)
    elsif (cmd == "cc") then # change category
      category_change_MP(category_id,item_id,trait_id)
    end 
  end
  def trait_add_MP(category_id,item_id,trait,trait_code)
    if (trait != nil) then
      @traits_MP[category_id][item_id].push(trait) if @traits_MP[category_id][item_id] != nil
      trait_id = next_trait_id(@trait_ids_MP[category_id][item_id].last)
      @trait_ids_MP[category_id][item_id].push(trait_id) if @trait_ids_MP[category_id][item_id] != nil
        
      @trait_def_MP[category_id][item_id][trait_id] = trait_def_empty()
      @par_setting_MP[category_id][item_id][trait_id] = par_setting_new()

      @traits_desc_MP[category_id][item_id][trait_id] = ""
      @traits_score_desc_MP[category_id][item_id][trait_id] = ""
      @traits_code_MP[category_id][item_id][trait_id] = trait_code
    end
  end
  def trait_edit_MP(category_id,item_id,trait_id,trait,trait_code)
    if (trait_id != nil && trait != nil) then
      i = @trait_ids_MP[category_id][item_id].index(trait_id)
      if (i == nil) then return end
      @traits_MP[category_id][item_id][i] = trait
      @traits_code_MP[category_id][item_id][trait_id] = trait_code
    end
  end
  def trait_remove_MP(category_id,item_id,trait_id)
    if (trait_id != nil) then
      i = @trait_ids_MP[category_id][item_id].index(trait_id)
      if (i == nil) then return end
      @traits_MP[category_id][item_id].delete_at(i)
      @trait_ids_MP[category_id][item_id].delete_at(i)

      @traits_desc_MP[category_id][item_id].delete(trait_id)
      @traits_score_desc_MP[category_id][item_id].delete(trait_id)
      @traits_code_MP[category_id][item_id].delete(trait_id)
      # remove @trait_def_MP
      if (@trait_def_MP[category_id] != nil && @trait_def_MP[category_id][item_id] != nil) then
        @trait_def_MP[category_id][item_id].delete(trait_id)
      end

      if (@par_setting_MP[category_id] != nil && @par_setting_MP[category_id][item_id] != nil) then
        @par_setting_MP[category_id][item_id].delete(trait_id)
      end
    end
  end
  def read_talent_data_MP(current_lang=Cfate::DEFAULT_LANG)
    key = Talent.get_talent_key("MP",current_lang)
    return Pm.getSystemValue_hash(key)
  end
  def save_talent_data_MP(h,current_lang=Cfate::DEFAULT_LANG)
    key = Talent.get_talent_key("MP",current_lang)
    hOrg = Pm.getSystemValue_hash(key)

    if (h["version"] == nil) then
      h["version"] = {}
    end
    categorys = h["category"].keys
    categorys.each do |category_id|
      if (h["version"][category_id] == nil) || (!check_category_the_same(hOrg,h,category_id)) then
        h["version"][category_id] = Xdate.make_now_timestamp2()
      end
    end
    Pm.setSystemValue_hash(key,h)

    value_categorys = categorys.join(",")
    Pm.setSystemValue("#{key}_categorys",value_categorys)

    categorys.each do |category_id|
      h2 = {}
      h2["category"] = {}
      h2["trait_def"] = {}
      h2["par_setting"] = {}
      h2["version"] = {}
      h2["category"][category_id] = h["category"][category_id]
      h2["trait_def"][category_id] = h["trait_def"][category_id]
      h2["par_setting"][category_id] = h["par_setting"][category_id]
      h2["version"][category_id] = h["version"][category_id]
      Pm.setSystemValue_hash("#{key}_#{category_id}",h2)
    end
  end
  def get_talent_data_MP(h)
    category_level = "MP"
    @talent_MP = read_talent_data(category_level,my_current_lang())
    # @category_id_MP = h["category_id"] == nil ? "category_1" : h["category_id"]
    # @item_id_MP = h["item_id"] == nil ? "item_1" : h["item_id"]
    # @trait_id_MP = h["trait_id"] == nil ? "trait_1" : h["trait_id"]
    @category_id_MP = h["category_id"]
    @item_id_MP = h["item_id"]
    @trait_id_MP = h["trait_id"]
    @trait_code_MP = h["trait_code"]
    @category_id_MP,@item_id_MP,@trait_id_MP = trait_code_2_c_i_t(@talent_MP,@trait_code_MP,@category_id_MP,@item_id_MP,@trait_id_MP)
    if @trait_code_MP == nil && @trait_code_MP == "" && @category_id_MP != nil && @category_id_MP != "" && @item_id_MP != nil && @item_id_MP != "" && @trait_id_MP != nil && @trait_id_MP != "" then
      @trait_code_MP = @talent_MP["category"][@category_id_MP]["item"][@item_id_MP]["trait"][@trait_id_MP]["trait_code"] if @trait_code_MP == nil
    end
    @rule_MP = h["rule_MP"]
    @rule_MP = "Rule" if @rule_MP == nil

    parse_talent_hash(category_level)
    if (@par_setting_MP[@category_id_MP] != nil && @par_setting_MP[@category_id_MP][@item_id_MP] != nil && @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP] != nil ) then
      @cur_rule_MP = @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP][@rule_MP]
    end
    @cur_rule_MP = rule_MP_new() if @cur_rule_MP == nil || @cur_rule_MP == {}      
  end
  def make_talent_hash_MP()
    make_talent_hash_category_MP()
    make_talent_hash_trait_def_MP()
    make_talent_hash_par_setting_MP()
  end
  def make_talent_hash_category_MP()
    @talent_MP["category"] = {}
    @category_ids_MP.each_with_index do |category_id,i|
      one_category = {"desc" => @categorys_MP[i]}
      one_category["item"] = {}
      if (@item_ids_MP[category_id] != nil) then
        @item_ids_MP[category_id].each_with_index do |item_id,i|
          one_category["item"][item_id] = {"desc" => @items_MP[category_id][i]}
          one_category["item"][item_id]["trait"] = {}
          if (@trait_ids_MP[category_id] != nil && @trait_ids_MP[category_id][item_id] != nil) then
            @trait_ids_MP[category_id][item_id].each_with_index do |trait_id,i|
              one_category["item"][item_id]["trait"][trait_id] = {"desc" => @traits_MP[category_id][item_id][i]}
              one_category["item"][item_id]["trait"][trait_id]["trait_desc"] = @traits_desc_MP[category_id][item_id][trait_id]
              one_category["item"][item_id]["trait"][trait_id]["trait_score_desc"] = @traits_score_desc_MP[category_id][item_id][trait_id]
              one_category["item"][item_id]["trait"][trait_id]["trait_code"] = @traits_code_MP[category_id][item_id][trait_id]
            end
          end
        end
      end
      @talent_MP["category"][category_id] = one_category.clone
    end
  end
  def make_talent_hash_trait_def_MP()
    @talent_MP["trait_def"] = @trait_def_MP
  end
  def make_talent_hash_par_setting_MP()
    @talent_MP["par_setting"] = @par_setting_MP
  end
  def parse_talent_hash_MP()
    parse_talent_hash_category_MP()
    parse_talent_hash_trait_def_MP()
    parse_talent_hash_par_setting_MP()
  end
  def parse_talent_hash_category_MP()
    @talent_MP["category"] = {} if @talent_MP["category"] == nil
    @category_ids_MP = @talent_MP["category"].keys.clone
    @categorys_MP = []
    @category_id_hash_MP = {}
    @item_ids_MP = {}
    @items_MP = {}
    @item_id_hash_MP = {}
    @trait_ids_MP = {}
    @traits_MP = {}
    @trait_id_hash_MP = {}
    @trait_id_code_hash_MP = {}
    @traits_desc_MP = {}
    @traits_score_desc_MP = {}
    @traits_code_MP = {}
    @category_ids_MP.each_with_index do |category_id,i|
      category_desc = @talent_MP["category"][category_id]["desc"]
      category_desc += "_1" if (@categorys_MP.include?(category_desc))
      @categorys_MP.push(category_desc)
      @category_id_hash_MP[category_desc] = category_id
      
      @item_ids_MP[category_id] = @talent_MP["category"][category_id]["item"].keys.clone
      @items_MP[category_id] = []
      @item_ids_MP[category_id].each_with_index do |item_id,i|
        item_desc = @talent_MP["category"][category_id]["item"][item_id]["desc"]
        item_desc += "_1" if (@items_MP[category_id].include?(item_desc))
        @items_MP[category_id].push(item_desc)
        @item_id_hash_MP[item_desc] = item_id if (@category_id_MP == category_id)
      end
    end
    @item_id_MP = @item_ids_MP[@category_id_MP][0] if (@item_ids_MP[@category_id_MP] != nil && @item_ids_MP[@category_id_MP].index(@item_id_MP) == nil)
    @item_id_MP = @item_id_MP == nil ? "item_1" : @item_id_MP

    @category_ids_MP.each_with_index do |category_id,i|
      @trait_ids_MP[category_id] = {}
      @traits_MP[category_id] = {}
      @traits_desc_MP[category_id] = {}
      @traits_score_desc_MP[category_id] = {}
      @traits_code_MP[category_id] = {}
      @item_ids_MP[category_id].each_with_index do |item_id,i|
        @traits_MP[category_id][item_id] = []
        @traits_desc_MP[category_id][item_id] = {}
        @traits_score_desc_MP[category_id][item_id] = {}
        @traits_code_MP[category_id][item_id] = {}
        t = @talent_MP["category"][category_id]["item"][item_id]["trait"]
        @trait_ids_MP[category_id][item_id] = t.keys.clone
        @trait_ids_MP[category_id][item_id].each_with_index do |trait_id,i|
          trait_desc = t[trait_id]["desc"]
          trait_desc += "_1" if (@traits_MP[category_id][item_id].include?(trait_desc))
          @traits_MP[category_id][item_id].push(trait_desc)
          @traits_code_MP[category_id][item_id][trait_id] = t[trait_id]["trait_code"]
          trait_code = @traits_code_MP[category_id][item_id][trait_id]
          @trait_id_hash_MP["#{trait_desc}"] = trait_id if (@category_id_MP == category_id && @item_id_MP == item_id)
          @trait_id_code_hash_MP["#{trait_code}:#{trait_desc}"] = trait_id if (@category_id_MP == category_id && @item_id_MP == item_id)

          @traits_desc_MP[category_id][item_id][trait_id] = t[trait_id]["trait_desc"]
          @traits_score_desc_MP[category_id][item_id][trait_id] = t[trait_id]["trait_score_desc"]
        end
      end
    end
    @trait_id_MP = @trait_ids_MP[@category_id_MP][@item_id_MP][0] if (@trait_ids_MP[@category_id_MP] != nil && @trait_ids_MP[@category_id_MP][@item_id_MP] != nil && @trait_ids_MP[@category_id_MP][@item_id_MP].index(@trait_id_MP) == nil)
    @trait_id_MP = @trait_id_MP == nil ? "trait_1" : @trait_id_MP

    if (@trait_id_hash_MP.length > 0) then
      @trait_desc_cur_trait_MP = @traits_desc_MP[@category_id_MP][@item_id_MP][@trait_id_MP]
      @trait_score_desc_cur_trait_MP = @traits_score_desc_MP[@category_id_MP][@item_id_MP][@trait_id_MP]
      @trait_code_cur_trait_MP = @traits_code_MP[@category_id_MP][@item_id_MP][@trait_id_MP]
    else
      @trait_desc_cur_trait_MP = ""
      @trait_score_desc_cur_trait_MP = ""
      @trait_code_cur_trait_MP = ""
    end
  end
  def parse_talent_hash_trait_def_MP()
    if @talent_MP["trait_def"] == nil || @talent_MP["trait_def"] == {} then
      @trait_def_MP = {}
      @category_ids_MP.each_with_index do |category_id,i|
          @trait_def_MP[category_id] = {}
          @item_ids_MP[category_id].each_with_index do |item_id,i|
              @trait_def_MP[category_id][item_id] = {}
            @trait_ids_MP[category_id][item_id].each_with_index do |trait_id,i|
              @trait_def_MP[category_id][item_id][trait_id] = trait_def_empty()
            end
          end
      end
    else
      @trait_def_MP = @talent_MP["trait_def"]
    end
    @trait_def_MP[@category_id_MP][@item_id_MP][@trait_id_MP] = trait_def_empty() if (@trait_def_MP[@category_id_MP] != nil && @trait_def_MP[@category_id_MP][@item_id_MP] != nil && @trait_def_MP[@category_id_MP][@item_id_MP][@trait_id_MP] == nil)
    if (@trait_def_MP[@category_id_MP] == nil || @trait_def_MP[@category_id_MP][@item_id_MP] == nil || @trait_def_MP[@category_id_MP][@item_id_MP][@trait_id_MP] == nil) then
      @trait_def_cur_trait_MP = trait_def_empty()
    else
      @trait_def_cur_trait_MP = @trait_def_MP[@category_id_MP][@item_id_MP][@trait_id_MP]
    end
  end
  def get_trait_def_MP(h)
    @trait_desc_cur_trait_MP = h["trait_desc"] == nil ? "" : h["trait_desc"]
    @trait_score_desc_cur_trait_MP = h["trait_score_desc"] == nil ? "" : h["trait_score_desc"]
    @trait_code_cur_trait_MP = h["trait_code"] == nil ? "" : h["trait_code"]

    @traits_desc_MP[@category_id_MP][@item_id_MP][@trait_id_MP] = @trait_desc_cur_trait_MP
    @traits_score_desc_MP[@category_id_MP][@item_id_MP][@trait_id_MP] = @trait_score_desc_cur_trait_MP
    @traits_code_MP[@category_id_MP][@item_id_MP][@trait_id_MP] = @trait_code_cur_trait_MP

    (1..4).each do |i|
      @trait_def_MP[@category_id_MP][@item_id_MP][@trait_id_MP]["score_drop_#{i}"]["up"] = h["score_drop_up_#{i}"]
      @trait_def_MP[@category_id_MP][@item_id_MP][@trait_id_MP]["score_drop_#{i}"]["down"] = h["score_drop_down_#{i}"]
      @trait_def_MP[@category_id_MP][@item_id_MP][@trait_id_MP]["score_drop_#{i}"]["trait_content"] = h["trait_content_#{i}"]
      @trait_def_MP[@category_id_MP][@item_id_MP][@trait_id_MP]["score_drop_#{i}"]["proposal"] = h["proposal_#{i}"]
    end
  end
   def parse_talent_hash_par_setting_MP()
    if @talent_MP["par_setting"] == nil || @talent_MP["par_setting"] == {} then
      @par_setting_MP = {}
      @category_ids_MP.each_with_index do |category_id,i|
          @par_setting_MP[category_id] = {}
          @item_ids_MP[category_id].each_with_index do |item_id,i|
            @par_setting_MP[category_id][item_id] = {}
            @trait_ids_MP[category_id][item_id].each_with_index do |trait_id,i|
              @par_setting_MP[category_id][item_id][trait_id] = par_setting_new()
            end
          end
      end
    else
      @par_setting_MP = @talent_MP["par_setting"]
    end
    # if (@par_setting_MP[@category_id_MP] != nil && @par_setting_MP[@category_id_MP][@item_id_MP] != nil && @par_setting_MP@category_id_MP][@item_id_MP][@trait_id_MP] == nil) then
   #  if (@trait_id_hash_MP.length > 0) then
    #   @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP] = {} 
    # end
    # @cur_stars_weight_MP = @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP]["stars_weight"]
    # @cur_sihuas_weight_MP = @par_setting_MP[@category_id_MP][@item_id_MP][@trait_id_MP]["sihuas_weight"]
  end



  # 共用部分
  def get_stars_for_select(h)
    @stars_all = Talent.AllStarsForSelect()
    @stars_condition_all = Talent.AllConditionForSelect()
    @weight_condition_for_select = get_weight_condition_for_select()
    @sihua_stars_all = Talent.AllSihuaStarsForSelect()
    @sihua_weight_condition_for_select = get_sihua_weight_condition_for_select()
    @trait_L1_all = hash_sort(get_trait_L1_all(h))
    @trait_L2_all = hash_sort(@trait_L1_all.merge(get_trait_L2_all(h))) # merge 沒有改變 @trait_L1_all
    @trait_L3_all = hash_sort(@trait_L2_all.merge(get_trait_L3_all(h)))
    @house_id_all = Talent.HouseIdForSelect()
  end
  def hash_sort(h)
    a1 = h.to_a
    a1.sort!{|a, b| a[0] <=> b[0]}
    h = a1.to_h
    return h
  end
end
