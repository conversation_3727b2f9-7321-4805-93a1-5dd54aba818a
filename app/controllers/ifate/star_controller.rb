require("Star.rb")
require("PanXml.rb")
require("PanWeb.rb")
require("Explain.rb")
require("Score.rb")
require("Pm.rb")
require("SkyEarthFive_Function.rb")
require("SearchParser.rb")
require("PanGiSuong.rb")
require("WeenApi.rb")
require("Xuexi_Ziwei.rb")
require("Eightword.rb")
require("Controller_Api.rb")

class Ifate::StarController < ApplicationController
  include Controller_Api

  before_action :authenticate_user!, :except => [:pan2,:pansimple,:pansimpleinput,:xml_gen,:free,:explain_ajax,:show,:zeri_pan_show1]
  before_action :authenticate, :except => [:pan2,:pansimple,:pansimpleinput,:xml_gen,:free,:explain_ajax,:show,:zeri_pan_show1]
  after_action :allow_iframe, only: :pan2
  layout "star_pan", :only => [:pan]

private

  def allow_iframe
    response.headers.except! 'X-Frame-Options'
  end
  
  protected
  def logged_in?
    return (current_user != nil)
  end
  def get_session_data()
    # s_keys = session.keys
    # puts "s_keys: #{s_keys}"
    # s_values = session.values
    # puts "s_values: #{s_values}"
    user_id = current_user.id if logged_in?
    g_get_session_data(user_id,"Star")
    # s_keys = session.keys
    # puts "s_keys: #{s_keys}"
    # s_values = session.values
    # puts "s_values: #{s_values}"
  end
  def save_session_data()
    user_id = current_user.id if logged_in?
    g_save_session_data(user_id,"Star")
  end

  def authenticate
    get_session_data()
    authenticate_star
  end
  def authenticate_star(new_ap_name="star",ap_namespace="ifate")
    if (Pm.getSystemValue("WebSpeedTest") == "1") then
      @user_name = "<EMAIL>" #current_user.email
      @user_id = 3
    else
      #@user_name = current_user.name
      @user_name = current_user.email if logged_in?
      @user_id = current_user.id if logged_in?
    end
    @admin = current_user.roles? :admin
    @user_level = ""
    @ap_name = g_user_ap_name(new_ap_name,@user_id,ap_namespace)

    session["userlevel"] = @user_level

    getUserCurrentAp(@user_id)

    # session["ap_name"] = @ap_name
    session["ap_version"] = @ap_version
    session["user_name"] = { :value => @user_name, :expires => 1.minute.from_now }
    @ParAll = Star.get_pan_pars(@user_id,g_can_use_pars(@oUserAp))

    star_version_check()

    @action_result = params["action_result"]

    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?

    @can_add_customer,@customer_max_count = g_can_add_customer(@user_id,@admin)

    @simplepan = params["simplepan"] != nil ? (params["simplepan"] == "normal" ? false : true) : (session["simplepan"] == nil ? false : session["simplepan"])
    session["simplepan"] = @simplepan
  end

  def getUserCurrentAp(user_id)
    @oUserAp = g_getUserCurrentAp(user_id,@ap_name)
    @rent_user = false    
    @rent_user = @oUserAp.main if @oUserAp != nil
    @rent_user = @rent_user || @admin
    @showAdvertise = true
    @firstShowAdvertise = @oUserAp.firstShowAdvertise && @showAdvertise ? "pan_information_block" : "pan_xdate_block"
    @firstShowAdvertise = session["star_firstShowAdvertise"] if session["star_firstShowAdvertise"] != nil
    @firstShowAdvertise = "pan_xdate_block" if @firstShowAdvertise == "rightbar_input"
    @canUsePan = g_canUsePan(session["customer_id"])
  end

  def star_version_check()
    # 權限控管
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @hHallNumberAdver = find_last_hallnumber_adver(@ap_name,@oUserAp,@ApFunc)
    @hCanDeal = canDealCheck(@user_id,@oUserAp,@ap_name)
    @main_products = g_getMainProducts(@ap_name)
    @canAddPe = session["customer_id"] != nil
  end

  public
  def management
    @controller_name = "star"

    if (@ApFunc[Star::FUNC_SET_PARS]) then
      redirect_to :action => "panparedit" , :PanPar => Star::PanPar_Type
    else
      render :layout => 'ifate'
    end
  end

  public
  def panscore
    if (current_user.email == "<EMAIL>" || current_user.email == "<EMAIL>" || current_user.id == 128) then
      sLang = my_current_lang()

      pan_prepare_data(params)
      hData = Xdate.udt_date_input_prepare(params)

      @par_YearDisplay = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]
      @starYearSelect = 1901
      @stopYearSelect = 2099
      @calType = hData["calType"]
      if (@calType == Xdate::CT_SOLAR)
        wyear = @year = hData["wyear"]
        @leap = false
        wmonth = @month = hData["wmonth"]
        wday = @day = hData["wday"]
        @hour = hData["hour"]
        @minute = hData["minute"]
        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(@year,@month,@day)

        @UserDefData[Cfate::EYear] = nEYear
        @UserDefData[Cfate::EMonth] = nEMonth
        @UserDefData[Cfate::EDate] = nEDate
        @UserDefData[Cfate::LeapMonth] = bLeapMonth
        @UserDefData[Cfate::WHour] = @hour
        @UserDefData[Cfate::WMinute] = @minute
      else
        @year = hData["eyear"]
        @leap = hData["eleap"]
        @month = hData["emonth"]
        @day = hData["eday"]
        @hour = hData["hour"]
        @minute = hData["minute"]
        wyear,wmonth,wday = Xdate.East2West(@year, @month, @day, @leap)

        @UserDefData[Cfate::EYear] = @year
        @UserDefData[Cfate::EMonth] = @month
        @UserDefData[Cfate::EDate] = @day
        @UserDefData[Cfate::LeapMonth] = @leap
        @UserDefData[Cfate::WHour] = @hour
        @UserDefData[Cfate::WMinute] = @minute
      end

      if (params["pan_type"] == nil) then
        @m_nPanType = Cfate::PAN_NORMAL
      else
        @m_nPanType = params["pan_type"].to_i
      end
      if (@m_nPanType == Cfate::PAN_NORMAL) then
        @UserDefData[Star::HOUSE_NAME] = 99
      else
        if (params["star_house"] == nil || params["star_house"].to_i == 0) then
          @UserDefData[Star::HOUSE_NAME] = 99 # 1
        else
          @UserDefData[Star::HOUSE_NAME] = params["star_house"].to_i
        end
      end
      @UserDefData["ParAll"] = {}

      # @birth = Xdate.GetFullDateTimeStr(wyear,wmonth,wday,@hour,@minute,nil,@par_YearDisplay)
      y,m,d = @UserInfo[Cfate::WYear],@UserInfo[Cfate::WMonth],@UserInfo[Cfate::WDate]
      h,min = @UserInfo[Cfate::WHour],@UserInfo[Cfate::WMinute]
      @birth = Xdate.GetFullDateTimeStr(y,m,d,h,min,nil,@par_YearDisplay)
      getpanscoreHouseNames(@UserDefData[Star::HOUSE_NAME])
      getPanType(@m_nPanType)
      # b = PanWeb.new
      # @WebPan = b.pw_getPan(PanWeb::PW_STAR,@m_nPanType,@UserInfo,@UserDefData,nil) #@ParAll)

      # ex = Explain.new
      # @Explain = ex.ex_getExplain(Pm::AP_STAR,sLang,@m_nPanType,@UserInfo,@UserDefData,@ApFunc,@ParAll)

      ps = Score.new
      @Score = ps.ps_getScore(Pm::AP_STAR,@m_nPanType,@UserInfo,@UserDefData)

      if (@UserDefData[Star::HOUSE_NAME] == 99) then
        final_to_csv()
      end

      render :layout => 'ifate'
     # respond_to do |format|
     #    format.json {render json: @Score.keys}
     #  end
    else
      redirect_to ifate_customer_panscoreinput_path
    end
  end

  protected
  def getpanscoreHouseNames(star_house)
    @hHouse = Hash.new
    #sHouseKey = "IDS_S_HOUSE_NAME_1"
    @sHouse = star_house #Pm.GetStr(sHouseKey)

    (1..12).each do |nHouse|
      sHouseKey = "IDS_S_HOUSE_NICK_NAME_#{nHouse}"
      @hHouse[Pm.GetStr(sHouseKey)] = nHouse
    end
  end
  def getPanType(nPanType,start_pan_type=Cfate::PAN_NORMAL,end_pan_type=Cfate::PAN_FLOWDATE)
    @hPanType = Hash.new
    if (nPanType == nil) then
      @PanType = Cfate::PAN_NORMAL
    else
      @PanType = nPanType
    end

    (start_pan_type..end_pan_type).each do |nPanType|
      @hPanType[Cfate.GetPanName(nPanType)] = nPanType
    end
  end


  public
  def xml_gen
    @ap_name = g_user_ap_name("star",@user_id)
    if ((params["User"] != "mitsubishi") || (params["Pwd"] != "30888")) then
      @XmlPan = "<Auth> Fail! </Auth>"
    else
      hUserInfo = Hash.new
        hUserInfo["remote_ip"] = remote_ip()

      hUserInfo[Cfate::WYear] = params["WYear"].to_i
      hUserInfo[Cfate::WMonth] = params["WMonth"].to_i
      hUserInfo[Cfate::WDate] = params["WDate"].to_i
      hUserInfo[Cfate::WHour] = params["WHour"].to_i
      hUserInfo[Cfate::Sex] = params["Sex"].to_i == 0 ? false : true

      nPanType = Cfate::PAN_NORMAL
      if (params["PanType"] != nil) then
        nPanType = params["PanType"].to_i
      end
      a = PanXml.new
      @XmlPan = a.px_getPan(PanXml::PX_STAR,nPanType,hUserInfo,nil,nil)
    end

    respond_to do |format|
      format.html { render :layout => false}# xml_gen.html.erb
  #     format.html { render :xml => @XmlPan }
  #     format.xml { render :xml => @XmlPan } 直接送出,不必再經過xml_gen.xml.erb
      format.xml { render :layout => false}  # 經過xml_gen.xml.erb    end
    end
  end

  public
  def pansimple
    redirect_to free_path()
    # @ap_name = g_user_ap_name("star",@user_id)
    # if (session["user_id"] != nil) then
    #   authenticate_user!
    # end
    # authenticate_without_signin
    # # I18n.locale = extract_locale_from_tld || Cfate::DEFAULT_LANG
    # sLang = my_current_lang()

    # cust_name = params["name"]
    # cust_sex = params["sex"]

    # hDate = Xdate.date_input_prepare(params)
    # #pan_prepare_data()
    # @UserInfo = Hash.new
    # @UserInfo[Cfate::WYear] = hDate["wyear"]
    # @UserInfo[Cfate::WMonth] = hDate["wmonth"]
    # @UserInfo[Cfate::WDate] = hDate["wday"]
    # @UserInfo[Cfate::WHour] = hDate["hour"]
    # @UserInfo[Cfate::Sex] = cust_sex == "1" ? false : true
    # @UserInfo[Cfate::Name] = cust_name

    # @m_nPanType = Cfate::PAN_NORMAL
    # # 測試驗證用
    # b = PanWeb.new
    # @WebPan = b.pw_getPan(PanWeb::PW_STAR,@m_nPanType,@UserInfo,nil,nil)#@UserDefData,@ParAll)

    # star_house = params["star_house"]
    # @PanDesc = ""
    # hUserDefData = get_cur_date_info()
    # e = Explain.new
    # if (star_house == nil) then
    #   hUserDefData[Star::HOUSE_NAME] = Star::EX_HOUSE_NONE
    # else
    #   hUserDefData[Star::HOUSE_NAME] = star_house.to_i
    # end

    # star_version_check
    # @PanDesc = e.ex_Star_getSimpleExplain(sLang,@UserInfo,hUserDefData,@ApFunc,@ParAll)

    # render :layout => 'star_pan_simple'
  end

  def pansimpleinput
    # if (session["user_id"] == nil) then
      # redirect_to free_path()
    # else
    @user_id = current_user.id if logged_in?
    @ap_name = g_user_ap_name("star",@user_id)
      if (session["user_id"] != nil) then
        authenticate_user!
      end
    authenticate_without_signin(nil)
      star_version_check
      @m_nPanType = Cfate::PAN_NORMAL
      @par_YearDisplay = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]
      @StarYearSelect = 1901
      @StopYearSelect = 2099
      t = Xdate.GetNow()
      @TodayYear = t.year
      newUserCustomerNow
      getHouseNames

    @free_ap_name = "star"
    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?

      render :layout => 'star_pan_simple' #'customer'
    # end
  end

  protected
  def getHouseNames()
    @hHouse = Hash.new
    #sHouseKey = "IDS_S_HOUSE_NAME_1"
    @sHouse = "1" #Pm.GetStr(sHouseKey)
    @sign_in = false

    sHouseKey = "IDS_S_HOUSE_NICK_NAME_#{Star::EX_HOUSE_NONE}"
    @hHouse[Pm.GetStr(sHouseKey)] = Star::EX_HOUSE_NONE
    if (logged_in?) then  #if logged_in?
      sHouseKey = "IDS_S_HOUSE_NICK_NAME_#{Star::EX_HOUSE_ALL}"
      @hHouse[Pm.GetStr(sHouseKey)] = Star::EX_HOUSE_ALL
      @sHouse = "#{Star::EX_HOUSE_ALL}"
      @sign_in = true
    end
    (1..12).each do |nHouse|
      if (checkExplain(@ApFunc,@m_nPanType,nHouse)) then
        sHouseKey = "IDS_S_HOUSE_NICK_NAME_#{nHouse}"
        @hHouse[Pm.GetStr(sHouseKey)] = nHouse
      end
    end
  end

  def newUserCustomerNow()
    @user_customer = UserCustomer.new
    new_for_now
  end

  def new_for_now()
    t = time_now_local()
    @user_customer.calType = Xdate::CT_SOLAR
    @user_customer.wyear = t.year
    @user_customer.wmonth = t.month
    @user_customer.wday = t.day
    @user_customer.hour = t.hour
    @user_customer.minute = t.min
    @user_customer.name = ""
    @user_customer.sex = 1
    @calType = Xdate::CT_SOLAR
  end


  public
  def panparedit
    if (@ApFunc[Star::FUNC_SET_PARS]) then
      if (params["PanPar"] == nil) then
        @nPanPar = Star::PanPar_Type
      else
        @nPanPar = params["PanPar"].to_i
      end
      if (@nPanPar == Star::PanPar_PrivateFourHua) then
        @hAStars = getFourHuaAllStars()
      end

      render :layout => 'ifate'
    else
      redirect_to :action => "management"
    end
  end

  protected
  def getFourHuaAllStars()
    hAStars = Hash.new
    (Star.PrivateFourHua_FirstAStar()..Star.PrivateFourHua_LastAStar()).each do |nStar|
      hAStars[Star.GetAStarName(nStar)] = nStar
    end
    return hAStars
  end

  def panPar2Db(hParAll)
    panParType = IfatePar.check_userid(@user_id).check_apname(@ap_name).first
    if (panParType == nil) then
      panParType = IfatePar.new
      panParType.user_id = @user_id
      panParType.ap_name = @ap_name
    end
    panParType.hPars = hParAll
    panParType.save!
  end

  public
  def panparupdate
    # 用 simple_form_for f.input 時，要從submit_data中取出
    # 用 form_tag 時，一樣用
    hPar = params["submit_data"]

    if (hPar["PanPar"] == nil) then
      nPanPar = Star::PanPar_Type
    else
      nPanPar = hPar["PanPar"].to_i
    end
    if (hPar["PanParReset"] == "1") then
      hParAll = Star.pan_par_init_each(nPanPar,@ParAll)
    else
      hParAll = Star.pan_par_assign_each(nPanPar,@ParAll,hPar)
    end
    panPar2Db(hParAll)

    redirect_to :action => "panparedit" , :PanPar => nPanPar, :action_result => Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
  end

  public
  # def pan_main
  def pan
    pan_data(params)

    save_session_data()
    if (@customer_id == nil) then
      redirect_to ifate_customer_index_path, :ap_name => @ap_name
    end
  end
  protected
  # peter here
  def prepare_simplepan_pars()
    h = {"dis_astar_1" => 1,"dis_astar_2" => 1,"dis_astar_3" => 1,"dis_astar_4" => 1,"dis_astar_5" => 1,"dis_astar_6" => 1,"dis_astar_7" => 1,"dis_astar_8" => 1,"dis_astar_9" => 1,"dis_astar_10" => 1,"dis_astar_11" => 1,"dis_astar_12" => 1,"dis_astar_13" => 1,"dis_astar_14" => 1,"dis_astar_15" => 1,"dis_astar_16" => 1,"dis_astar_17" => 1,"dis_astar_18" => 1,"dis_astar_19" => 1,"dis_astar_20" => 1,"dis_astar_21" => 1,"dis_astar_22" => 1,"dis_astar_23" => 1,"dis_astar_24" => 1,"dis_astar_25" => 1,"dis_bstar_1" => 1,"dis_bstar_2" => 1,"dis_bstar_3" => 1,"dis_bstar_4" => 1,"dis_bstar_5" => 1,"dis_bstar_6" => 1,"dis_bstar_7" => 1,"dis_bstar_8" => 1,"dis_bstar_9" => 0,"dis_bstar_10" => 0,"dis_bstar_11" => 0,"dis_bstar_12" => 0,"dis_bstar_13" => 0,"dis_bstar_14" => 0,"dis_bstar_15" => 1,"dis_bstar_16" => 1,"dis_bstar_17" => 1,"dis_bstar_18" => 0,"dis_bstar_19" => 0,"dis_bstar_20" => 0,"dis_bstar_21" => 0,"dis_bstar_22" => 0,"dis_bstar_23" => 0,"dis_bstar_24" => 0,"dis_bstar_25" => 0,"dis_bstar_26" => 0,"dis_bstar_27" => 0,"dis_bstar_28" => 0,"dis_bstar_29" => 0,"dis_bstar_30" => 0,"dis_bstar_31" => 0,"dis_bstar_32" => 0,"dis_bstar_33" => 0,"dis_bstar_34" => 0,"dis_bstar_35" => 0}
    h1 = @ParAll.select {|k,v| h.keys.include?(k)}
    h.merge!(h1) { |key, v1, v2| v2 }
    @ParAll.merge!(h) { |key, v1, v2| v2 }
    h2 = {"dis_doctor" => 0,"dis_live" => 0,"dis_yearstar" => 0,"dis_yeargod" => 0,"dis_flowchan" => 0,"dis_skycook" => 1,"dis_lyyg" => 0,"dis_7star" => 0}
    @ParAll.merge!(h2) { |key, v1, v2| v2 }

    if (@ParAll[Star::PAR_FLOWYEAR_HUA] == Star::PAN_YEAR_HUA_STACK) then
      @ParAll[Star::PAR_FLOWYEAR_HUA] = Star::PAN_YEAR_HUA_FLOW
    end
    @simplepan_tail = "_simplepan"
  end

  def pan_data(hPars)
    # 精實創業，用來分析使用狀況
    # Pm.saveTestDb("#{hPars[:controller]}-#{hPars[:action]}-#{@oUserAp.user_id}-#{@oUserAp.product_type}",hPars.to_json)

    # 處理使用者傳入之資料及相關物件之準備
    pan_prepare_data(hPars)

    if (@simplepan == true) then
      prepare_simplepan_pars()
    else
      @simplepan_tail = ""
    end

    @oStar = Star.new
    @oStar.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll)

    # 排盤資料
    pan_pan_info()

    # 萬年曆
    pan_xdate_info()

    # 個人事件資料
    pan_prepare_personalevent_data()

    # 產品購買資訊
    pan_userap_info()

    @edit_customer_url = "/ifate/customer/edit"
    @select_customer_url = "/ifate/customer/index"
    @url_back = "/ifate/star/pan"

    @aUrlKey = []
    @aUrlValue = []

    g_userap_addone_usecount(hPars["action"],@m_nPanType,@oUserAp)

    # 取得副盤的資料
    g_get_star_pan(hPars)
    g_get_eightword_pan(hPars)
    @sp_customer_ids = g_get_user_customers(@user_id)
  end
  def g_get_star_pan(hPar)
    nPanType = @m_nPanType
    nPanType = Cfate::PAN_NORMAL
    @m_nSpPanType = nPanType
    hUserInfo = g_get_sp_customer_info(hPar)
    hUserDefData = @UserDefData.clone
    # for test
    @firstShowAdvertise = params["firstShowAdvertise"] if params["firstShowAdvertise"] != nil
    session["star_firstShowAdvertise"] = @firstShowAdvertise
    @sp_star_pan = g_star_pan(nPanType,hUserInfo,hUserDefData,@ApFunc,@ParAll)
  end
  def g_get_sp_customer_info(hPar)
    @sp_customer_id = hPar["sp_customer_id"] == nil ? session["sp_customer_id"] : hPar["sp_customer_id"].to_i
    @sp_customer_id = session["customer_id"] if @sp_customer_id == nil || @sp_customer_id == 0
    session["sp_customer_id"] = @sp_customer_id
    hUserInfo = g_get_user_customer(@user_id,@sp_customer_id)
    api_set_user_customer_updated_at(@sp_customer_id)
    return hUserInfo
  end
  def g_get_eightword_pan(hPar)
    nPanType = @m_nPanType
    hUserInfo = g_get_user_customer(@user_id,@customer_id)
    # @firstShowAdvertise = "pan_star_block" if @sp_customer_id != nil
    hUserDefData = @UserDefData.clone
    # for test
    # @firstShowAdvertise = params["firstShowAdvertise"] if params["firstShowAdvertise"] != nil
    hoUserAp = g_getUserCurrentAp(@user_id,"eightword")    
    hApFunc = g_ap_product_allfunc_hash(hoUserAp)
    hParAll = Star.get_pan_pars(@user_id,g_can_use_pars(hoUserAp))
    if (!@rent_user) then
      hUserInfo[Cfate::WYear] = hUserInfo[Cfate::WYear] - 1
    end
    @sp_eightword_pan = g_eightword_pan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
  end
  public
  def update_sp_star_pan_ajax
    hData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    pan_data(hData)
    render :partial => "ifate/star/pan_star" , :layout => false, :locals => {}
  end
  def update_firstShowAdvertise_ajax
    hData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    @firstShowAdvertise = hData["firstShowAdvertise"]
    ap_name = hData["ap_name"]
    session["#{ap_name}_firstShowAdvertise"] = @firstShowAdvertise if @firstShowAdvertise != nil
  end
  def explain_ajax
    @ap_name = "star"
    if (logged_in?) then
      authenticate_user!
      authenticate
    else
      @product_type = "show"
      authenticate_without_signin(nil)
    end

    sLang = my_current_lang()
    hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])

    if (hAjaxData["calType"] != nil && hAjaxData["year"] != nil) then
      # when free pan
      hData = Xdate.date_input_prepare(hAjaxData)
    else
      hData = hAjaxData
    end
    pan_prepare_data(hData)
    star_house = 1 # house earth
    star_house = hAjaxData["star_house"] if hAjaxData["star_house"] != nil

    @PanDesc = ""
    e = Explain.new
    @UserDefData[Star::HOUSE_EARTH] = star_house.to_i
  # saveTestDb("explain_ajax",@UserDefData[Star::HOUSE_EARTH])
    hPar = @ParAll.clone
    hPar[Star::PAR_FLOWYEAR_HUA] = Star::PAN_YEAR_HUA_CALD
    hExplain = e.ex_Star_getHouseExplain(sLang,@m_nPanType,@UserInfo,@UserDefData,@ApFunc,hPar)

    # 精實創業，用來分析使用狀況
    # Pm.saveTestDb("#{params[:controller]}-#{params[:action]}-#{@oUserAp.user_id}-#{@oUserAp.product_type}","#{hExplain['ex_pantype']},#{hExplain['ex_house']},#{hExplain['ex_sky']},#{hExplain['ex_earth']}")

    save_session_data()

    render :partial => "ifate/star/explain_ajax" , :layout => false, :locals => {:hExplain => hExplain }
  end

  def xdate
    session["user_def_data"] = nil
    pan_prepare_data(params)
    if (params["PanType"] == nil) then
      @m_nPanType = Cfate::PAN_NORMAL
      # t = Time.new
      # @UserDefData[Cfate::EYear] = t.year
    end
    t = time_now_local()
    if (@UserDefData[Cfate::EYear] - 40 < Xdate::MIN_YEAR) then
      wyear = Xdate::MIN_YEAR
    else
      wyear = @UserDefData[Cfate::EYear] - 40
    end
    nWYear,nWMonth,nWDate = Xdate.GetLegalWDate(wyear,1,1)
    @UserInfo[Cfate::WYear] = nWYear
    @UserInfo[Cfate::WMonth] = nWMonth
    @UserInfo[Cfate::WDate] = nWDate
    @UserInfo[Cfate::WHour] = t.hour
    @UserInfo[Cfate::Sex] = false
    @UserInfo[Cfate::Name] = "Profate"
    # if (@m_nPanType == Cfate::PAN_NORMAL) then
      # t = Time.new
      # if (@UserDefData[Cfate::EYear] == t.year) then
        # @UserDefData[Cfate::EYear] = t.year - 40
      # end
      # @UserInfo[Cfate::WYear] = @UserDefData[Cfate::EYear] - 40
      # if (@UserInfo[Cfate::WYear] < Xdate::BASE_YEAR) then
      #   @UserInfo[Cfate::WYear] = Xdate::BASE_YEAR
      # end
    # end

    @oStar = Star.new
    @oStar.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll)

    # 排盤資料
    pan_pan_info()

    # 萬年曆
    pan_xdate_info()
    sMy_url = "/ifate/star/xdate"
    render :layout => 'xdate'
  end

  def xdate_ajax
    # 處理使用者傳入之資料及相關物件之準備
    pan_prepare_data(params)
      hData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
# saveTestDb("xdate_ajax",params["ajax_data_key"])
    @UserDefData[Cfate::EYear] = hData["UD_EYear"].to_i
    @UserDefData[Cfate::EMonth] = hData["UD_EMonth"].to_i
    @UserDefData[Cfate::EDate] = hData["UD_EDate"].to_i
    @UserDefData[Cfate::LeapMonth] = (hData["UD_ELeap"].to_i == 0) ? false : true
    @UserDefData[Cfate::WHour] = hData["UD_WHour"].to_i
    @UserDefData[Cfate::WMinute] = hData["UD_WMin"].to_i

# saveTestDb("UserDefData",Cfate.pan_par_hash2db(@UserDefData))
    @oStar = Star.new
    @oStar.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll)

    # 排盤資料
    pan_pan_info()

    # 萬年曆
    pan_xdate_info()
    sMy_url = "/ifate/star/xdate_ajax"

      render :partial => "ifate/star/xdate_ajax" , :locals => {:nPanType => @m_nPanType, :sMy_url => sMy_url, :hApFunc => @ApFunc, :nPanLastYear => @PanLastYear, :nCurEYear => @CurEYear, :nCurEMonth => @CurEMonth, :bCurELeap => @CurELeap, :nCurEDate => @CurEDate, :nCurETime => @CurETime, :nCurEMin => @CurEMin, :aShowXdate => @ShowXdate, :apan_xdate_normal => @pan_xdate_normal, :apan_xdate_tenyear => @pan_xdate_tenyear, :nHighLightStartEYear => @nHighLightStartEYear, :bHasGiSuong => @HasGiSuong, :aPanGiSuong => @aPanGiSuong, :apan_xdate_flowyear_data => @pan_xdate_flowyear_data, :apan_xdate_flowyear_title => @pan_xdate_flowyear_title, :apan_xdate_flowmonth_data => @pan_xdate_flowmonth_data, :apan_xdate_flowmonth_title => @pan_xdate_flowmonth_title, :apan_xdate_flowdate_title => @pan_xdate_flowdate_title, :apan_xdate_flowdate_data_title => @pan_xdate_flowdate_data_title, :apan_xdate_flowdate_data => @pan_xdate_flowdate_data, :apan_xdate_flowtime_title => @pan_xdate_flowtime_title, :apan_xdate_flowtime_data_title => @pan_xdate_flowtime_data_title, :apan_xdate_flowtime_data => @pan_xdate_flowtime_data, :apan_xdate_flowmin_title => @pan_xdate_flowmin_title, :apan_xdate_flowmin_data_title => @pan_xdate_flowmin_data_title, :apan_xdate_flowmin_data => @pan_xdate_flowmin_data }
  end

  protected
  def pan_prepare_data(hData=nil)
    # 排盤資訊
    @PanLastYear = Cfate::PAN_LAST_YEAR # 萬年曆最後一年

    # 設定客戶資訊
    hUserInfo = set_pan_customer_info(hData)
    @UserInfo = hUserInfo

    # 處理流盤時間
    hUserDefData = set_pan_user_def_data(hData)
    @UserDefData = hUserDefData

    # 開始處理排盤相關資訊
    @WYear = hUserInfo[Cfate::WYear]
    @WMonth = hUserInfo[Cfate::WMonth]
    @WDate = hUserInfo[Cfate::WDate]
    @WHour = hUserInfo[Cfate::WHour]
    @Sex = hUserInfo[Cfate::Sex]
    @Name = hUserInfo[Cfate::Name]

    @UA_EYear = hUserDefData[Cfate::EYear]
    @UA_EMonth = hUserDefData[Cfate::EMonth]
    @UA_EDate = hUserDefData[Cfate::EDate]
    @UA_WHour = hUserDefData[Cfate::WHour]
    @UA_WMinute = hUserDefData[Cfate::WMinute]
    @UA_LeapMonth = hUserDefData[Cfate::LeapMonth]
    @UA_HouseName = hUserDefData[Star::HOUSE_NAME]

    @bPanChange = true
    @bDeclare = false
  end

  def pan_pan_info()
    nPanType = @m_nPanType
    @IsSelFourHuaStack = @oStar.IsSelFourHuaStack?()
    @IsMioWongNum = @oStar.IsMioWongNum?()

    pan_init_instant_var(@oStar)

    # 三方四正，用比例的方式
    ptEarth34 = [[75,100],[25,100],[0,100],[0,75],[0,25],[0,0],[25,0],[75,0],[100,0],[100,25],[100,75],[100,100]]

    nHouse1Earth = @oStar.gHouse_GetFirstHouseEarth(nPanType)
    (1..12).each do |nEarth|
      # 各宮共同資訊
      pan_house_general_info(@oStar,nPanType,nEarth)

      # 各流盤資訊
      pan_house_flow_info(@oStar,nPanType,nEarth)

      # 各宮在中宮的資訊 三方四正及 四化飛出
      pan_house_in_middle_info(@oStar,nPanType,nEarth,nHouse1Earth,ptEarth34)
    end

    # personal info
    pan_personal_info(@oStar)
  end

  def pan_xdate_info()
    # 萬年曆
    makeXdate_data(@oStar)


    # 吉凶垂象
    MakeGiSuongInfo(@oStar)
  end

  def pan_userap_info()
    oProduct = g_getProductByUserAp(@oUserAp)
    @buy_info = ""
    @ap_version_desc = oProduct.product_description if oProduct != nil
    if (oProduct.demo) then
      @ap_version_desc += "(#{Pm.GetStrWithColon("IDS_S_UNTILL")}#{@oUserAp.end_date.strftime("%Y-%m-%d")})"
    else
      if (oProduct.free) then
        if (g_isDemoExist(@user_id,@ap_name)) then
          @buy_info = "buy"
        else
          @buy_info = "demo"
        end
        @ap_version_desc = Pm.GetStr("IDS_S_APP_PAN_NOT_BUIED")
      else
        if ((@oUserAp.end_date < Xdate.GetNowRails() + 14.days) && getUserApCounts(@user_id,@ap_name) < 2) then
          @buy_info = "rebuy"
        end
        hApFunc = g_ap_product_allfunc_hash(@oUserAp)
        @ap_version_desc = Pm.GetStr("IDS_S_APP_PAN_BUIED")
        if (hApFunc[Cfate::FUNC_HALL_NUMBER] || hApFunc["GiSuongEnable"] || hApFunc["explain"]) then
          @ap_version_desc = Pm.GetStr("IDS_S_APP_PAN_BUIED_FULL")
        end
      end
    end
    if (@customer_user_id == 0) then
      @ap_version_desc = Pm.GetStr("IDS_S_ALL_SHOW")
    end  
  end

  # explain_free,1,explain_free_pan,0,explain_free_content,1,explain_free_house,341,n_SkyFindStar_house,341,n_StarFindSky_house,0
  def checkExplain(hApFunc,nPanType,nHouse)
    nHouseKey = 1 << (nHouse - 1)
    bExist = ((nPanType <= hApFunc["n_explain_pan"])) && (hApFunc["n_explain_content"] > 0) && ((hApFunc["n_explain_house"] & nHouseKey) == nHouseKey)
    bExist = bExist || ((nPanType <= hApFunc["n_explain_free_pan"])) && (hApFunc["n_explain_free_content"] > 0) && ((hApFunc["n_explain_free_house"] & nHouseKey) == nHouseKey)
    return bExist
  end

  def pan_init_instant_var(oStar)
    @Doctor = Array.new(12)
    @God = Array.new(12)
    @YearGod = Array.new(12)  # 歲建
    @YearStar = Array.new(12)  # 將星
    @AStarInfos = Array.new(12)
    @AStarMioWong = Array.new(12)
    @AStarOrgFourHua = Array.new(12)
    @AStarFourHuaSky = Array.new(12)
    @BStarInfos = Array.new(12)
    @BStarMioWong = Array.new(12)
    @Small = Array.new(12)
    @Large = Array.new(12)
    @LargeSan = Star.HouseIndex2Earth(oStar.gHouse_getLargeSan())
    @BodyHouse = oStar.gPanHouse_GetBodyHouse()
    @HouseId = Array.new(12)
    @HouseName = Array.new(12)
    @HouseName_Normal = Array.new(12)
    @HouseFiveStr = Array.new(12)
    @HouseFive = Array.new(12)
    @HouseSky = Array.new(12)
    @SkyAStarFourHua = Array.new(12)
    @HouseSkyStr = Array.new(12)
    @HouseEarthStr = Array.new(12)
    @PanSmallSanYear = Array.new(12)
    @PanFlowYear = Array.new(12)
    @PanYear = Array.new(12)
    @PanYearOld = Array.new(12)
    @PanMonth = Array.new(12)
    @PanMonthStr = Array.new(12)
    @PanDate = Array.new(12)
    @PanTime = Array.new(12)
    @PanTimeStr = Array.new(12)
    @PanMinHour = Array.new(12)
    @PanMin = Array.new(12)
    @PanMinStr = Array.new(12)
    @SStarInfos = Array.new(12)
    @Star7Infos = Array.new(12)

    @PanBg34 = Array.new(12)
    @HouseOutFourHua = Array.new(12) {Array.new(4,"")}
    @AStarSelfFourHua = Array.new(12)
    @AStarSelfFourHuaCount = Array.new(12)
    @Pan34Pt = Array.new(4,[0,0])

    @HasGiSuong = false
    @GiSuongEarth = nil

    @SmallDisplay = oStar.gHouse_GetSmallDisplay()
    @LargeDisplay = oStar.gHouse_GetLargeDisplay()
    @PI_GanZhiBirthDisplay = oStar.gHouse_Get8WordsDisplay() && @ApFunc["GanZhiBirthDisplay"]
    @SmallYearRevertDisplay = oStar.gHouse_GetSmallYearRevertDisplay() && @ApFunc["SmallYearRevertDisplay"]
    @DoctorDisplay = oStar.gHouse_GetDoctorDisplay()
    @GodDisplay = oStar.gHouse_GetGodDisplay()
    @YearStarDisplay = oStar.gHouse_GetYearStarDisplay()
    @YearGodDisplay = oStar.gHouse_GetYearGodDisplay()
    @LyygDisplay = oStar.gHouse_GetLyygDisplay() && @ApFunc["LYYGDisplay"]
    @Star7Display = @ApFunc["Star7Display"]
    @ShowStar7 = oStar.gHouse_Get7StarDisplay() && @ApFunc["Star7Display"]
  end

  def pan_house_general_info(oStar,nPanType,nEarth)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)

    @Doctor[nEarthIndex] = oStar.gHouse_GetDoctorName(nPanType,nEarth)
    @God[nEarthIndex] = oStar.gHouse_GetGodName(nPanType,nEarth)
    @AStarInfos[nEarthIndex] = oStar.gHouse_GetAStarInfos(nPanType,nEarth,false)

    @AStarMioWong[nEarthIndex] = oStar.gHouse_GetAStarsMioWongInfo(nPanType,nEarth,false,false)
    @AStarOrgFourHua[nEarthIndex] = oStar.gHouse_GetAStarsFourHua(nPanType,nEarth)
    @AStarSelfFourHua[nEarthIndex],@AStarSelfFourHuaCount[nEarthIndex] = oStar.gHouse_GetAStarsSelfFourHua(nPanType,nEarth)
    @AStarSelfFourHuaDisplay = oStar.gHouse_GetAStarsSelfFourHuaDisplay()

    fa_starInfos = oStar.get_HouseFAStarInfos(nPanType,nEarth)
    @AStarInfos[nEarthIndex] = @AStarInfos[nEarthIndex] + fa_starInfos

    @YearGod[nEarthIndex] = oStar.gHouse_GetYearGodName(nPanType,nEarth)
    @YearStar[nEarthIndex] = oStar.gHouse_GetYearStarName(nPanType,nEarth)
    @SStarInfos[nEarthIndex] = oStar.get_HouseSStarInfos(nPanType,nEarth)

    @BStarInfos[nEarthIndex] = oStar.gHouse_GetBStarInfos(nPanType,nEarth)
    @BStarInfos[nEarthIndex] = @BStarInfos[nEarthIndex] + oStar.get_HouseFBStarInfos(nPanType,nEarth)
    @BStarMioWong[nEarthIndex] = oStar.gHouse_GetBStarsMioWongInfo(nPanType,nEarth)

    @HouseId[nEarthIndex] = oStar.g_House_GetHouseId(nPanType,nEarth)
    @HouseName[nEarthIndex] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
    @HouseName_Normal[nEarthIndex] = oStar.gHouse_GetHouseNameWithQuota(Cfate::PAN_NORMAL,nEarth)
    @HouseFiveStr[nEarthIndex] = oStar.gHouse_FiveStr(nPanType,nEarth)
    @HouseFive[nEarthIndex] = oStar.gHouse_Five(nPanType,nEarth)
    @HouseSkyStr[nEarthIndex] = oStar.gHouse_GetSkyName(nPanType,nEarth)
    @HouseSky[nEarthIndex] = oStar.gHouse_GetSky(nPanType,nEarth)
    @SkyAStarFourHua[nEarthIndex] = oStar.gNormal_FindSkyFourHuaStar(@HouseSky[nEarthIndex])

    @HouseEarthStr[nEarthIndex] = oStar.gHouse_GetEarthName(nEarth)

    @Star7Infos[nEarthIndex] = oStar.gHouse_Get7Star(nEarth)

    # 祿存疊四層(不含生年祿存)
    # 紫微工具 新增功能 #3
    astarInfos = oStar.star_GetLuYangTuoInfos(nPanType,nEarth)
    @AStarInfos[nEarthIndex] = @AStarInfos[nEarthIndex] + astarInfos
    @AStarMioWong[nEarthIndex] = @AStarMioWong[nEarthIndex] + oStar.star_GetAStarsMioWongInfo(fa_starInfos)
    @AStarMioWong[nEarthIndex] = @AStarMioWong[nEarthIndex] + oStar.star_GetAStarsMioWongInfo(astarInfos)

    # aStar,bUseOpp = oStar.gHouse_GetAStars(nPanType,nEarth,false)
    aStar = Star.AStarInfos_2_Stars(@AStarInfos[nEarthIndex])
    @AStarFourHuaSky[nEarthIndex] = oStar.gNormal_FindAStarsFourHuaSky(aStar)
end

  def pan_house_flow_info(oStar,nPanType,nEarth)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)

    # normal and ten year
    @Small[nEarthIndex] = oStar.gHouse_GetSmall(nEarth)
    @Large[nEarthIndex] = oStar.gHouse_GetLarge(nEarth)

    # flow year
    if (nPanType >= Cfate::PAN_FLOWYEAR) then
      @PanSmallSanYear[nEarthIndex] = oStar.gPanHouse_FlowYear_SmallSan(nEarth,nPanType)
      @PanFlowYear[nEarthIndex],@PanYearOld[nEarthIndex],@PanYear[nEarthIndex] = oStar.gPanHouse_FlowYear(nEarth,nPanType)
    end

    # flow month
    if (nPanType >= Cfate::PAN_FLOWMONTH) then
      bFlowHouse,@PanMonthStr[nEarthIndex],@PanMonth[nEarthIndex] = oStar.gPanHouse_FlowMonth(nEarth)
      if (bFlowHouse) then
        @nFlowMonthEarth = nEarth
      end
    end

    # flow date
    if (nPanType >= Cfate::PAN_FLOWDATE) then
      bFlowHouse,@nFlowDate,aPanFlowDate = oStar.gPanHouse_FlowDate(nEarth)
      nPanFlowDateLen = aPanFlowDate[2] == nil ? 2 : 3
      @PanDate[nEarthIndex] = aPanFlowDate[0..nPanFlowDateLen-1]
      if (bFlowHouse) then
        @nFlowDateEarth = nEarth
      end
    end

    # flow time
    if (nPanType >= Cfate::PAN_FLOWTIME) then
      bFlowHouse,@PanTimeStr[nEarthIndex],nfETime = oStar.gPanHouse_FlowTime(nEarth)
      @PanTime[nEarthIndex] = Xdate.ETime2Hour(nfETime)
      if (bFlowHouse) then
        @nFlowTimeEarth = nEarth
      end
    end

    # flow min
    if (nPanType >= Cfate::PAN_FLOWMIN) then
      bFlowHouse,@PanMinStr[nEarthIndex],nfEMin = oStar.gPanHouse_FlowMin2(nEarth,true)
      nWHourStart,nWHourStop = Xdate.GetEMinHour(oStar.fdg_ET(nPanType),nfEMin)
      @PanMinHour[nEarthIndex] = nWHourStart
      @PanMin[nEarthIndex] = Xdate.GetEMin(nfEMin)
      if (bFlowHouse) then
        @nFlowMinEarth = nEarth
      end
    end
  end

  def pan_house_in_middle_info(oStar,nPanType,nEarth,nHouse1Earth,ptEarth34)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)
    # 三方四正色彩顯示
    @PanBg34[nEarthIndex] = oStar.gHouse_Check34House(nHouse1Earth,nEarth)

    # 標示命宮三方四正
    nMiddle34 = oStar.gHouse_CheckMiddle34House(nHouse1Earth,nEarth)
    #bNeed34 = oStar.int_LifeLine
    if (nMiddle34 != Star::HOUSE_34_NONE) then
      @Pan34Pt[nMiddle34] = ptEarth34[nEarthIndex]
    end

    # four hua fly out
    @HouseOutFourHua[nEarthIndex] = oStar.gHouse_GetHouseOutFourHuaStr(nPanType,nEarth)
  end

  # personal info
  def pan_personal_info(oStar)
    @PI_Name = oStar.gPan_PI_Name()
    @PI_LunarYearOld = oStar.gPan_PI_LunarYearOld()
    @PI_IYMF = oStar.gPan_PI_IYMF()
    @PI_Animal = oStar.gPan_PI_Animal()
    @PI_SolarBirth = oStar.gPan_PI_SolarBirth2()
    @PI_LunarBirth = oStar.gPan_PI_LunarBirth2()
    @PI_GanZhiBirth = oStar.gPan_PI_GanZhiBirth()

    bSegment,nFirstTimeType,nFirstSegment,bSpace,nSegmentMove = g_eightword_SegmentGanZhi_pars(@user_id)
    @PI_Segment_GanZhiBirth = oStar.gPan_PI_SegmentGanZhiBirth(bSegment,nFirstTimeType,nFirstSegment,bSpace,nSegmentMove)

    @PI_FiveType = oStar.gPan_PI_FiveType()
    @PI_LifeHouse = oStar.gPan_PI_LifeHouse()
    @PI_BodyHouse = oStar.gPan_PI_BodyHouse()
    @PI_Life = oStar.gPan_PI_Life()
    @PI_Body = oStar.gPan_PI_Body()
    @PI_God = oStar.gPan_PI_God()
    @CI_Data1 = Star.gPan_CI_Data1(@ApFunc,@ParAll)
    @CI_Data2 = Star.gPan_CI_Data2(@ApFunc,@ParAll)
    @CI_Data3 = Star.gPan_CI_Data3(@ApFunc,@ParAll)

    @DI_Info = Array.new(3,"")
    @DI_Info[0],@DI_Info[1],@DI_Info[2] = oStar.gHouse_GetFlowTimeStr(@m_nPanType)
  end

  def extract_locale_from_tld
    parsed_locale = request.host.split('.').last
    I18n.available_locales.include?(parsed_locale.to_sym) ? parsed_locale  : nil
  end

  def set_pan_customer_info(hData=nil)
    hUserInfo = Hash.new
    hUserInfo["remote_ip"] = remote_ip()
    hUserInfo[Cfate::Multiple_births] = ""

    # 預設用現在時間當客戶生日排盤,男生
    t = time_now_local()
    hUserInfo[Cfate::WYear] = t.year
    hUserInfo[Cfate::WMonth] = t.month
    hUserInfo[Cfate::WDate] = t.day
    hUserInfo[Cfate::WHour] = t.hour
    hUserInfo[Cfate::Sex] = false
    hUserInfo[Cfate::Name] = "Profate"

    session["customer_id"] = hData["customer_id"] == nil ? session["customer_id"] : hData["customer_id"].to_i
    nPanType = session["pan_type"] == nil ? Cfate::PAN_NORMAL : session["pan_type"]

    if (hData["PanType"] != nil) then
      nPanType = hData["PanType"].to_i
    end
    if (nPanType > @ApFunc["LastPanType"]) then
      nPanType = @ApFunc["LastPanType"]
    end
    @m_nPanType = nPanType
    if (hData["PanType"] == nil) then
      if (hData["customer_id"] != nil) then
        @m_nPanType = nPanType = Cfate::PAN_NORMAL
      end
    end
    session["pan_type"] = @m_nPanType

    @customer_id = session["customer_id"]
    if (@customer_id != nil && @customer_id > 0) then
      oCustomer = UserCustomer.check_option_user_id(@user_id).find(@customer_id)
      if (oCustomer != nil) then
        hUserInfo[Cfate::WYear] = oCustomer.wyear
        hUserInfo[Cfate::WMonth] = oCustomer.wmonth
        hUserInfo[Cfate::WDate] = oCustomer.wday
        hUserInfo[Cfate::WHour] = oCustomer.hour
        hUserInfo[Cfate::WMinute] = oCustomer.minute
        hUserInfo[Cfate::Sex] = oCustomer.sex == 1 ? false : true
        hUserInfo[Cfate::Name] = oCustomer.name
        hAllInfo = oCustomer.hAll
        hUserInfo["longitude"] = hAllInfo["longitude"] == "" || hAllInfo["longitude"] == nil ? nil : hAllInfo["longitude"].to_f
        hUserInfo["latitude"] = hAllInfo["latitude"] == "" || hAllInfo["latitude"] == nil ? nil : hAllInfo["latitude"].to_f
        @customer_user_id = oCustomer.user_id # 給全功能體驗版使用
      end
    end

    if ((hData != nil) && (hData["pan"] == "free" || hData["wyear"] != nil)) then
      hUserInfo[Cfate::WYear] = hData["wyear"]
      hUserInfo[Cfate::WMonth] = hData["wmonth"]
      hUserInfo[Cfate::WDate] = hData["wday"]
      hUserInfo[Cfate::WHour] = hData["hour"]
      hUserInfo[Cfate::WMinute] = hData["minute"]
      hUserInfo[Cfate::Sex] = hData["sex"].to_i == 1 ? false : true
      hUserInfo[Cfate::Name] = hData["name"]
    end
    if (hData["timestamp"] != nil) then
      timestamp = hData["timestamp"]
      y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp)
      hUserInfo[Cfate::WYear] = y
      hUserInfo[Cfate::WMonth] = m
      hUserInfo[Cfate::WDate] = d
      hUserInfo[Cfate::WHour] = h
      hUserInfo[Cfate::WMinute] = min
      hUserInfo[Cfate::Sex] = sex == 1 ? false : true
      hUserInfo[Cfate::Name] = timestamp
      hUserInfo[Cfate::Multiple_births] = mb
    end
    if (@customer_id != nil) then
      api_set_user_customer_updated_at(@customer_id)
    end
    hUserInfo["longitude"] = hData["longitude"] == nil || hData["longitude"] == "" ? nil : hData["longitude"].to_f if (hUserInfo["longitude"] == nil)
    hUserInfo["latitude"] = hData["latitude"] == nil || hData["latitude"] == "" ? nil : hData["latitude"].to_f if (hUserInfo["latitude"] == nil)

    return hUserInfo
  end

  def check_nil_to_i(arg1,argSelf)
    if (arg1 == nil) then
      return argSelf
    end
    return arg1.to_i
  end

  def get_cur_date_info()
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
    hUserDefData = Hash.new
    hUserDefData[Cfate::EYear] = nEYear
    hUserDefData[Cfate::EMonth] = nEMonth
    hUserDefData[Cfate::EDate] = nEDate
    hUserDefData[Cfate::LeapMonth] = bLeapMonth
    hUserDefData[Cfate::WHour] = nWHour
    hUserDefData[Cfate::WMinute] = nWMin
    return hUserDefData
  end

  def set_pan_user_def_data(hData)
    # 處理流盤時間
    if (hData["USE_CUR_TIME"] != nil || ((session["user_def_data"] == nil) && (hData["UD_EYear"] == nil))) then
      hUserDefData = get_cur_date_info()
    else
      #若有傳入,以傳入為主
      if (hData["UD_EYear"] == nil) then
        hUserDefData = session["user_def_data"]
      else
        hUserDefData = Hash.new
        hUserDefData[Cfate::EYear] = hData["UD_EYear"].to_i
        hUserDefData[Cfate::EMonth] = hData["UD_EMonth"].to_i
        hUserDefData[Cfate::EDate] = hData["UD_EDate"].to_i
        hUserDefData[Cfate::LeapMonth] = (hData["UD_ELeap"].to_i == 0) ? false : true
        hUserDefData[Cfate::WHour] = hData["UD_WHour"].to_i
        hUserDefData[Cfate::WMinute] = hData["UD_WMin"].to_i
      end
    end

    @nLeapMonth = hUserDefData[Cfate::LeapMonth] ? 1 : 0
    hUserDefData[Star::HOUSE_NAME] = hData["star_house"] == nil ? 99 : hData["star_house"].to_i

    session["user_def_data"] = hUserDefData
#puts("session["user_def_data"]#{session["user_def_data"][Cfate::EYear]}")
    return hUserDefData
  end

  def NeedXdate?(nDisPanType,nCurPanType)
    if (@ApFunc["showXdate"] == false) then  # 免費版
      return false
    end
    if (nDisPanType > @ApFunc["LastXdatePanType"]) then
      return false
    end
    # if (Cfate::PAN_NORMAL == nCurPanType) then
    #   return false
    # end
    # if (Cfate::PAN_TENYEAR == nCurPanType) then
    #   return false
    # end
    # if (Cfate::PAN_FLOWTIME == nCurPanType) then
    #   return false
    # end
    # if (Cfate::PAN_FLOWMIN == nCurPanType) then
    #   return false
    # end
    if (nDisPanType <= nCurPanType) then
      return true
    end
    if (nDisPanType == nCurPanType) then
      return true
    end
    if (nDisPanType == (nCurPanType - 1)) then
      return true
    end
    return false
  end

  public
  def panprint
    #pan_prepare_data()
    # @oStar = Star.new
    # @oStar.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll)
    #b = PanWeb.new
    #@WebPan = b.pw_getPan(PanWeb::PW_STAR,@m_nPanType,@UserInfo,@UserDefData,@ParAll)
    pan
    @GiSuongEarth = nil
    if (@customer_id != nil) then
      @usercustomer = UserCustomer.find_by_id(session["customer_id"])
      render :layout => 'star_pan_print'
    end
  end

  protected
  def panprint_old
    pan_prepare_data(params)
    b = PanWeb.new
    @WebPan = b.pw_getPan(PanWeb::PW_STAR,@m_nPanType,@UserInfo,@UserDefData,@ParAll)

    render :layout => 'star_pan_print'

  end

  #垂象
  def MakeGiSuongInfo(oStar)
    if (@ApFunc["GiSuongEnable"] == false) then
      return
    end
    if (params["action"] == "panprint") then
      return
    end
    if (@m_nPanType == Cfate::PAN_NORMAL) then
      return
    end

    # 吉凶垂象
    if (params["GiSuongHouseEarth"] != nil) then
      nGiSuongHouseEarth = params["GiSuongHouseEarth"].to_i
      nHouseId = oStar.g_House_GetHouseId(@m_nPanType,nGiSuongHouseEarth)
      if (session["GiSuongHouseId"] == nHouseId) then
        session["GiSuongHouseId"] = nil
      else
        session["GiSuongHouseId"] = nHouseId
      end
    end

    @aPanGiSuong = Array.new(7,nil) # NORMAL -- FLOWMIN
#     aDataGiSuong = Array.new(30) {Array.new(2,"O")}
#   @aPanGiSuong[Cfate::PAN_TENYEAR] = aDataGiSuong
    if (session["GiSuongHouseId"] != nil && session["GiSuongHouseId"] != 99) then
      nGiSuongHouseId = session["GiSuongHouseId"]
      @GiSuongEarth = oStar.g_House_GetEarth(@m_nPanType,nGiSuongHouseId)
      oPanGiSuong = PanGiSuong.new
      oPanGiSuong.ap_Star_Init(oStar,@m_nPanType,@UserInfo,@UserDefData,nGiSuongHouseId)

      MakeGiSuongInfo_AllPan(oStar,oPanGiSuong)
    end
  end

  def MakeGiSuongInfo_AllPan(oStar,oPanGiSuong)
    # 精實創業，用來分析使用狀況
    aaa = Pm.GetStr("IDS_S_HOUSE_NAME_#{session["GiSuongHouseId"]}")
    # Pm.saveTestDb("ifate/star-GiSuong-#{@oUserAp.user_id}-#{@oUserAp.product_type}","GiSuongHouseId:#{session["GiSuongHouseId"]},#{aaa}")
    nPanType = @m_nPanType

    @HasGiSuong = true
    @Ui_EY = oStar.uig_EY()
    @nSky = oStar.gNormal_GetSky()
    @nEarth = oStar.gNormal_GetEarth()
    @CurEYear = oStar.cdg_EY()
    @CurEMonth = oStar.cdg_EM()
    @CurEDate = oStar.cdg_ED()
    @CurELeap = oStar.cdg_EL()
    @CurETime = oStar.cdg_ET()
    @CurEMin = oStar.cdg_EMI()
    @CurWMin  = oStar.cdg_WMI()
    @FlowEYear = oStar.fdg_EY(nPanType)
    @FlowEMonth = oStar.fdg_EM(nPanType)
    @FlowEDate = oStar.fdg_ED(nPanType)
    @FlowELeap = oStar.fdg_EL(nPanType)
    @FlowETime = oStar.fdg_ET(nPanType)
    @FlowWHour = oStar.fdg_WH(nPanType)
    @FlowEMin = oStar.fdg_EMI(nPanType)

    if (NeedXdate?(Cfate::PAN_TENYEAR,@m_nPanType)) then
      MakeGiSuongInfo_Tenyear(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWYEAR,@m_nPanType)) then
      MakeGiSuongInfo_Flowyear(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWMONTH,@m_nPanType)) then
      MakeGiSuongInfo_Flowmonth(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWDATE,@m_nPanType)) then
      MakeGiSuongInfo_Flowdate(oStar,oPanGiSuong)
    end
    if (NeedXdate?(Cfate::PAN_FLOWTIME,@m_nPanType)) then
      MakeGiSuongInfo_Flowtime(oStar,oPanGiSuong)
    end
  end


  def MakeGiSuongInfo_Tenyear(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId"]

    #elsif (@m_nPanType == Cfate::PAN_TENYEAR) then
    nSky = @nSky
    nEarth = @nEarth
    if (Cfate::PAN_TENYEAR <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(30) {Array.new(2)}

      #取得十年大限開始歲數
      nLargeHouseIndex = oStar.gHouse_getLargeSan()
      nEarthIndex = Star.HouseIndex2EarthIndex(nLargeHouseIndex)
      nETenYearStartYearOld = @Large[nEarthIndex]

      # 十年大限開始年
      nHighLightStartEYear = @Ui_EY + nETenYearStartYearOld - 1

      # 萬年曆十年盤起始年
      if (nETenYearStartYearOld > 10) then
        nDisStartEYear = nHighLightStartEYear - 10
      else
        nDisStartEYear = @Ui_EY
      end
      #垂象
      nStartNum = nHighLightStartEYear - nDisStartEYear

      # 萬年曆十年盤起始年紀
      nStartEYearOld = oStar.gNormal_GetYearOld(@Ui_EY,nDisStartEYear)

      # 起始干支
      nSky = Sky.ModifySky(nSky + nStartEYearOld - 1)
      nEarth = Earth.ModifyEarth(nEarth + nStartEYearOld - 1)

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      a = [nDisStartEYear,nHighLightStartEYear,nHighLightStartEYear+10,nHighLightStartEYear+20]
      a.each_index do |i|
        hGisuongUserData[Cfate::EYear] = a[i]
        hGisuongUserData[Cfate::EMonth] = 6
        hGisuongUserData[Cfate::EDate] = 1
        hGisuongUserData[Cfate::LeapMonth] = false
        hGisuongUserData[Cfate::WHour] = 0
        # hgs = oWeenApi.g_explode_text(Cfate::PAN_TENYEAR,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        hgs = oWeenApi.g_explode_text(Cfate::PAN_TENYEAR,@UserInfo,hGisuongUserData,@ApFunc,nil,false)
        h[a[i]] = hgs["explode_text"]
      end

      # new from WeenApi 1
      (1..30).each do |nRow|
        nRowIndex = nRow - 1
        nAge = nStartEYearOld + nRowIndex
        nStartEYear = nDisStartEYear + nRowIndex

        # new from WeenApi 0
        nGiSuongStartEYear = mgsi_ty_find_eyear(a,nStartEYear)
        szGi = h[nGiSuongStartEYear]["gi"][nStartEYear - nGiSuongStartEYear]
        szSuong = h[nGiSuongStartEYear]["xiong"][nStartEYear - nGiSuongStartEYear]
        # new from WeenApi 1

        # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_TenYear(nSky,nEarth,nAge,nRowIndex,nStartNum)
        aDataGiSuong[nRowIndex][0] = szGi
        aDataGiSuong[nRowIndex][1] = szSuong

        nSky = Sky.ModifySky(nSky + 1)
        nEarth = Earth.ModifyEarth(nEarth + 1)
      end
      @aPanGiSuong[Cfate::PAN_TENYEAR] = aDataGiSuong
    end
  end
  def mgsi_ty_find_eyear(a,nStartEYear)
    if (nStartEYear < a[1]) then
      return a[0]
    elsif (nStartEYear < a[2]) then
      return a[1]
    elsif (nStartEYear < a[3]) then
      return a[2]
    else
      return a[3]
    end
  end

  def MakeGiSuongInfo_Flowyear(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId"]

#   elsif (@m_nPanType == Cfate::PAN_FLOWYEAR) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    if (Cfate::PAN_FLOWYEAR <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(2) {Array.new}
      nAge = 0

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      (0..1).each do |index|
        hGisuongUserData[Cfate::EYear] = @FlowEYear + index
        hGisuongUserData[Cfate::EMonth] = 6
        hGisuongUserData[Cfate::EDate] = 1
        hGisuongUserData[Cfate::LeapMonth] = false
        hGisuongUserData[Cfate::WHour] = 0
        # hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWYEAR,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWYEAR,@UserInfo,hGisuongUserData,@ApFunc,nil,false)
        h[@FlowEYear + index] = hgs["explode_text"]
      end
      # new from WeenApi 1

      (0..1).each do |index|
        nEYear = @FlowEYear + index

        # 取得該年月資訊
        aaMonth = Xdate.GetEastMonthArray(@hXdate["pan_xdate_flowyear_title"][index][0])
        aaMonth.each_index do |nMonthIndex|
          aData = Array.new

          aMonth = aaMonth[nMonthIndex]

          nEMonth = aMonth[0]
          nSky,nEarth = Xdate.GetLunarMonthGanZhiSkyEarth(nEYear,nEMonth)

          # new from WeenApi 0
          szGi = h[nEYear]["gi"][nMonthIndex]
          szSuong = h[nEYear]["xiong"][nMonthIndex]
          # new from WeenApi 1

          # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowYear(nSky,nEarth,nAge,nEYear)

            aData.push(szGi)
            aData.push(szSuong)

            aDataGiSuong[index].push(aData)
        end
      end
      @aPanGiSuong[Cfate::PAN_FLOWYEAR] = aDataGiSuong
    end
  end

  def MakeGiSuongInfo_Flowmonth(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId"]

    #elsif (@m_nPanType == Cfate::PAN_FLOWMONTH) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    bLeapMonth = @FlowELeap
    if (Cfate::PAN_FLOWMONTH <= @ApFunc["LastXdatePanType"]) then
      # 取得該年月資訊
      aaMonth = Xdate.GetEastMonthArray(nEYear)
      nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,aaMonth)
      aDataGiSuong = Array.new
      nAge = 0
      nDays = aaMonth[nMonthIndex][1]

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      hGisuongUserData[Cfate::EYear] = nEYear
      hGisuongUserData[Cfate::EMonth] = nEMonth
      hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
      hGisuongUserData[Cfate::EDate] = 1
      hGisuongUserData[Cfate::WHour] = 0
      hGisuongUserData[Cfate::ETime] = 0
      # hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWMONTH,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
      hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWMONTH,@UserInfo,hGisuongUserData,@ApFunc,nil,false)
      h[nEMonth] = hgs["explode_text"]
      # new from WeenApi 1
# nil[1]
      (1..aaMonth[nMonthIndex][1]).each do |nEDate|
        aData = Array.new

        # new from WeenApi 0
        szGi = h[nEMonth]["gi"][nEDate - 1]
        szSuong = h[nEMonth]["xiong"][nEDate - 1]
        # new from WeenApi 1

        # nSky,nEarth = Xdate.GetLunarDateGanZhiSkyEarth(nEYear,nEMonth,nEDate,6,bLeapMonth)
        # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowMonth(nEYear,nEMonth,bLeapMonth,nSky,nEarth,nAge)

        aData.push(szGi)
        aData.push(szSuong)

        aDataGiSuong.push(aData)
      end
      @aPanGiSuong[Cfate::PAN_FLOWMONTH] = aDataGiSuong
    end
  end

  def MakeGiSuongInfo_Flowdate(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId"]

#   elsif (@m_nPanType == Cfate::PAN_FLOWDATE) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    bLeapMonth = @FlowELeap
    nEDate = @FlowEDate
    if (Cfate::PAN_FLOWDATE <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(2) {Array.new}

      # 年月表頭
      nEYear = @FlowEYear
      nEMonth = @FlowEMonth
      bLeapMonth = @FlowELeap

      # 日的訊息
      nEDate = @FlowEDate
      (0..1).each do |index|
        # new from WeenApi 0
        oWeenApi = WeenApi.new
        h = Hash.new
        hGisuongUserData[Cfate::EYear] = nEYear
        hGisuongUserData[Cfate::EMonth] = nEMonth
        hGisuongUserData[Cfate::EDate] = nEDate
        hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
        hGisuongUserData[Cfate::WHour] = 0
        hGisuongUserData[Cfate::ETime] = 0
        # hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWDATE,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWDATE,@UserInfo,hGisuongUserData,@ApFunc,nil,false)
        h[nEDate] = hgs["explode_text"]
        # new from WeenApi 1

        # 日表頭
        (1..12).each do |nEarth|
          nETime = nEarth - 1
          aData = Array.new

          # new from WeenApi 0
          szGi = h[nEDate]["gi"][nEarth - 1]
          szSuong = h[nEDate]["xiong"][nEarth - 1]
          # new from WeenApi 1

          # nSky,nEarth = Xdate.GetLunarTimeGanZhiSkyEarth(nEYear,nEMonth,nEDate,nETime,bLeapMonth)
          # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowDate(nEYear,nEMonth,bLeapMonth,nEDate,nSky,nEarth,0)


          aData.push(szGi)
          aData.push(szSuong)

          aDataGiSuong[index].push(aData)
        end
        # 換到下一天
        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      end
      @aPanGiSuong[Cfate::PAN_FLOWDATE] = aDataGiSuong
    end
  end
  # 選流日時，萬年曆顯示的流月，流年，十年的吉凶錯誤；流日流時的吉凶未做
  def MakeGiSuongInfo_Flowtime(oStar,oPanGiSuong)
    hGisuongUserData = @UserDefData.clone
    hGisuongUserData[Star::HOUSE_NAME] = session["GiSuongHouseId"]

#   elsif (@m_nPanType == Cfate::PAN_FLOWDATE) then
    nEYear = @FlowEYear
    nEMonth = @FlowEMonth
    bLeapMonth = @FlowELeap
    nEDate = @FlowEDate
    nETime = @FlowETime
    nWHour = @FlowWHour
    if (Cfate::PAN_FLOWTIME <= @ApFunc["LastXdatePanType"]) then
      aDataGiSuong = Array.new(2) {Array.new}

      # 年月表頭
      nEYear = @FlowEYear
      nEMonth = @FlowEMonth
      bLeapMonth = @FlowELeap

      # 日的訊息
      nEDate = @FlowEDate
      (0..1).each do |index|
        # new from WeenApi 0
        oWeenApi = WeenApi.new
        h = Hash.new
        hGisuongUserData[Cfate::EYear] = nEYear
        hGisuongUserData[Cfate::EMonth] = nEMonth
        hGisuongUserData[Cfate::EDate] = nEDate
        hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
        hGisuongUserData[Cfate::ETime] = Pm.TestNo12(nETime)
        hGisuongUserData[Cfate::WHour] = Xdate.ETimeFirstHour(nETime)
        # hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWTIME,@UserInfo,hGisuongUserData,@ApFunc,@ParAll,false)
        hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWTIME,@UserInfo,hGisuongUserData,@ApFunc,nil,false)
        h[hGisuongUserData[Cfate::ETime]] = hgs["explode_text"]
        # new from WeenApi 1

        # 日表頭
        # (1..12).each do |nEarth|
        (1..12).each do |i|
          # nETime = nEarth - 1
          aData = Array.new

          # new from WeenApi 0
          szGi = h[hGisuongUserData[Cfate::ETime]]["gi"][i - 1]
          szSuong = h[hGisuongUserData[Cfate::ETime]]["xiong"][i - 1]
          # new from WeenApi 1

          # nSky,nEarth = Xdate.GetLunarTimeGanZhiSkyEarth(nEYear,nEMonth,nEDate,nETime,bLeapMonth)
          # szGi,szSuong = oPanGiSuong.ap_Star_GetGiSuong_FlowDate(nEYear,nEMonth,bLeapMonth,nEDate,nSky,nEarth,0)


          aData.push(szGi)
          aData.push(szSuong)

          aDataGiSuong[index].push(aData)
        end
        # 換到下一天
        nETime += 1
        if (nETime == 1) then
          nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
        end
      end
      @aPanGiSuong[Cfate::PAN_FLOWTIME] = aDataGiSuong
    end
  end

  # 個人事件
  def pan_prepare_personalevent_data()
    tNow = time_now_local()
    oUserCustomer = UserCustomer.find_by_id(session["customer_id"])
    if (oUserCustomer == nil) then
      @starYearSelect = 1901
      @user_create_date = ""
    else
      @starYearSelect = oUserCustomer.wyear
      t = oUserCustomer.created_at
      @user_create_date = "#{t.year}/#{t.month}/#{t.day}#{Pm.t("IDS_S_CREATED")}"
    end
      if (oUserCustomer == nil || oUserCustomer.wyear <= tNow.year) then
        @stopYearSelect = tNow.year
      else
        @stopYearSelect = oUserCustomer.wyear + 10
      end

    @pe_title_data = Pm.GetStrWithQuote("IDS_PE_TITLE")
    # h.invert
        @pe_mains = Cfate.pan_par_dbfield2hash(Pm.GetPeStr("pe_mains"))
    @pe_main_key_default = @pe_mains.values[0]

    @pe_subs = Cfate.pan_par_dbfield2hash(Pm.GetPeStr(@pe_main_key_default))
    @pe_sub_key_default = @pe_subs.values[0]
    @pe_time = tNow
    @pe_year = @pe_time.year
    @pe_month = Xdate::DATE_NIL
    @pe_day = Xdate::DATE_NIL
    @pe_hour = Xdate::DATE_NIL
    @pe_min = Xdate::DATE_NIL

    @personal_events = PersonalEvent.available.check_option_user_id(@user_id).check_user_customer_id(session["customer_id"]).check_main_key_pe().last(3).reverse
    @canAddPe = session["customer_id"] != nil
  end

  public
  # IDS_PAR_SETUP_PAN_BASE : 設定排盤參數
  # IDS_PAR_SETUP_PAN_INTERFACE : 設定操作介面
  # IDS_PAR_SETUP_PAN_DISPLAY : 設定星曜顯示
  # IDS_PAR_SETUP_PAN_PRIVATEFOURHUA : 自訂四化
  def panparcopy()
    par_set_name_key = "star_par_name"
    oIfatePar = IfatePar.check_userid(3).check_apname(par_set_name_key).last
    if (oIfatePar == nil) then
      @hName = {}
      @default_name = ""
    else
      hPar = oIfatePar.hPars
      names = hPar["names"]
      @hName = Hash[names.map {|x| [x,x]}]
      @default_name = params["par_set_name"]
    end
    render :layout => 'ifate'
  end

  def panparcopy_action()
    hPars = params["panparcopy_action"]
    if (hPars == nil) then
      hPars = params
    end
    email = hPars["par_copy_email"]
    oUser = User.find_by_email(email)
    if (oUser == nil) then
      redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
    else
      oIfatePar = IfatePar.check_userid(oUser.id).check_apname(@ap_name).first
      # oIfatePar = IfatePar.check_userid_star(oUser.id).first
      if (oIfatePar == nil) then
        redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
      else
        oIfateParMe = IfatePar.check_userid(@user_id).check_apname(@ap_name).first
        # oIfateParMe = IfatePar.check_userid_star(@user_id).first
        if (oIfateParMe == nil) then
          oIfateParMe = IfatePar.new
          oIfateParMe.user_id = @user_id
          oIfateParMe.ap_name = @ap_name
        end
        hUserType = oIfatePar.hPars
        if (@ap_name == "star") then
          hUserType = Star.pan_par_assign_hallnumber(hUserType,nil)
          hUserType = Star.pan_par_assign_print(hUserType,nil)
        end
        oIfateParMe.hPars = hUserType
        oIfateParMe.save!

        redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
      end
    end
  end
  def panparsetname_action()
    par_set_name_key = "star_par_name"
    hPars = params["panparsetname_action"]
    if (hPars == nil) then
      hPars = params
    end
    name = hPars["par_set_name"]
    oIfatePar = IfatePar.check_userid(3).check_apname(par_set_name_key).last
    if (oIfatePar == nil) then
      # 第一個人設定
      oIfatePar = IfatePar.new
      oIfatePar.user_id = 3
      oIfatePar.ap_name = par_set_name_key
      hPar = {"names" => [name], "user_ids" => [@user_id]}
      oIfatePar.hPars = hPar
      oIfatePar.save!
      redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
    else
      hPar = oIfatePar.hPars
      names = hPar["names"]
      if (names.include?(name)) then
        # 代號已被設定
        redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
      else
        user_ids = hPar["user_ids"]
        idx = user_ids.index(@user_id)
        if (idx == nil) then
          # 這個人第一次設定
          names.push(name)
          user_ids.push(@user_id)
        else
          # 這個人更改設定
          names[idx] = name
        end
        hPar["names"] = names
        hPar["user_ids"] = user_ids
        oIfatePar.hPars = hPar
        oIfatePar.save!

        redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
      end
    end
  end
  def panparcopyname_action()
    par_set_name_key = "star_par_name"
    hPars = params["panparcopyname_action"]
    if (hPars == nil) then
      hPars = params
    end
    name = hPars["par_set_name"]
    oIfatePar = IfatePar.check_userid(3).check_apname(par_set_name_key).last
    if (oIfatePar == nil) then
      redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
    else
      hPar = oIfatePar.hPars
      names = hPar["names"]
      idx = names.index(name)
      if (idx == nil) then
        redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
      else
        user_ids = hPar["user_ids"]
        from_user_id = user_ids[idx]
        oIfateParFrom = IfatePar.check_userid(from_user_id).check_apname(@ap_name).last
        if (oIfateParFrom == nil) then
          redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL")
        else
          oIfateParMe = IfatePar.check_userid(@user_id).check_apname(@ap_name).last
          if (oIfateParMe == nil) then
            oIfateParMe = IfatePar.new
            oIfateParMe.user_id = @user_id
            oIfateParMe.ap_name = @ap_name
          end
          hUserType = oIfateParFrom.hPars
          h = {"hall_number1":"","hall_number2":"","hall_number3":"","s_adv_hall_number1":"","s_adv_hall_number2":"","s_adv_hall_number3":"","par_print_time_page":false,"par_print_time_page_type":0,"par_print_time_page_three_1":false,"par_print_time_page_three_2":false,"par_print_time_page_three_3":false,"par_print_pan_header":"","par_print_set_footer":""}
          hUserType.merge!(h) { |key, v1, v2| v2 }
          oIfateParMe.hPars = hUserType
          oIfateParMe.save!

          redirect_to :action => "panparcopy", :action_result => Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
        end
      end
    end
  end

  protected
  def makeXdate_data(oStar)
    hAppData = Hash.new
    nPanType = @m_nPanType

    hAppData["Ui_EY"] = oStar.uig_EY()
    hAppData["nSky"] = oStar.gNormal_GetSky()
    hAppData["nEarth"] = oStar.gNormal_GetEarth()
    hAppData["CurEYear"] = oStar.cdg_EY()
    hAppData["CurEMonth"] = oStar.cdg_EM()
    hAppData["CurEDate"] = oStar.cdg_ED()
    hAppData["CurELeap"] = oStar.cdg_EL()
    hAppData["CurETime"] = oStar.cdg_ET()
    hAppData["CurEMin"] = oStar.cdg_EMI()
    hAppData["CurWMin "] = oStar.cdg_WMI()
    hAppData["FlowEYear"] = oStar.fdg_EY(nPanType)
    hAppData["FlowEMonth"] = oStar.fdg_EM(nPanType)
    hAppData["FlowEDate"] = oStar.fdg_ED(nPanType)
    hAppData["FlowELeap"] = oStar.fdg_EL(nPanType)
    hAppData["FlowETime"] = oStar.fdg_ET(nPanType)
    hAppData["FlowWHour"] = oStar.fdg_WH(nPanType)
    hAppData["FlowEMin"] = oStar.fdg_EMI(nPanType)

    hAppData["ddg_EY"] = oStar.ddg_EY()
    hAppData["LargeSan"] = Star.HouseIndex2EarthIndex(oStar.gHouse_getLargeSan())
    hAppData["Large"] = oStar.gHouse_GetLarge()

    hParAll = @ParAll
    hParAll[Cfate::PAR_PAN_YEAR_DISPLAY] = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]

    hApFunc = @ApFunc.clone
    hApFunc[Cfate::LAST_XDATE_PAN_TYPE] = @ApFunc["LastXdatePanType"]

    @hXdate = Xdate.MakeXdateInfo_Star(hAppData,@m_nPanType,hApFunc,hParAll)

    @Ui_EY = oStar.uig_EY()
    @nSky = oStar.gNormal_GetSky()
    @nEarth = oStar.gNormal_GetEarth()
    @CurEYear = oStar.cdg_EY()
    @CurEMonth = oStar.cdg_EM()
    @CurEDate = oStar.cdg_ED()
    @CurELeap = oStar.cdg_EL()
    @CurETime = oStar.cdg_ET()
    @CurEMin = oStar.cdg_EMI()
    @CurWMin  = oStar.cdg_WMI()
    @FlowEYear = oStar.fdg_EY(nPanType)
    @FlowEMonth = oStar.fdg_EM(nPanType)
    @FlowEDate = oStar.fdg_ED(nPanType)
    @FlowELeap = oStar.fdg_EL(nPanType)
    @FlowETime = oStar.fdg_ET(nPanType)
    @FlowWHour = oStar.fdg_WH(nPanType)
    @FlowEMin = oStar.fdg_EMI(nPanType)

  end

  public
  def free
    @user_id = current_user.id if logged_in?
    @ap_name = g_user_ap_name("star",@user_id)
      if (session["user_id"] != nil) then
        authenticate_user!
      end
    @product_type = "free"
    authenticate_without_signin(nil)
    bCanUseIfatePars = true
    @ParAll = Star.get_pan_pars(@user_id,bCanUseIfatePars)
    session["customer_id"] = nil

    # I18n.locale = extract_locale_from_tld || Cfate::DEFAULT_LANG
    sLang = my_current_lang()

    cust_name = params["name"]
    cust_sex = params["sex"]

    hData = Xdate.date_input_prepare(params)
    pan_prepare_data(hData)

    @m_nPanType = Cfate::PAN_NORMAL
    @oStar = Star.new
    @oStar.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll)

    # 排盤資料
    pan_pan_info()
    makeXdate_data(@oStar)

      @edit_customer_url = "/free/star"
      @select_customer_url = "/free/star"
      if (hData['calType'] == Xdate::CT_SOLAR) then
        @url_back = "ifate/star/free&pan=free&calType=#{hData['calType']}&year=#{hData['wyear']}&month=#{hData['wmonth']}&day=#{hData['wday']}&hour=#{hData['hour']}&minute=#{hData['minute']}&sex=#{cust_sex}&name=#{cust_name}"
      else
        @url_back = "ifate/star/free&pan=free&calType=#{hData['calType']}&year=#{hData['eyear']}&month=#{Xdate.makeMonthValue(hData['calType'],hData['eleap'],hData['emonth'])}&day=#{hData['eday']}&hour=#{hData['hour']}&minute=#{hData['minute']}&sex=#{cust_sex}&name=#{cust_name}"
      end
      @customer_id = 5
      @customer_user_id = 5

      @aUrlKey = ["pan","name","sex","calType","year","month","day","hour","minute","eleap"]
      if (hData['calType'] == Xdate::CT_SOLAR) then
        @aUrlValue = [hData["pan"],hData["name"],hData["sex"],hData["calType"],hData["wyear"],hData["wmonth"],hData["wday"],hData["hour"],hData["minute"],false]
    else
        @aUrlValue = [hData["pan"],hData["name"],hData["sex"],hData["calType"],hData["eyear"],hData["emonth"],hData["eday"],hData["hour"],hData["minute"],hData['eleap']]
    end
    @customer_id = 0
    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?
    
    render :layout => 'star_pan_free'
  end

  public
  def show
    @ap_name = "star"
    @product_type = "show"
    authenticate_without_signin(nil)
    bCanUseIfatePars = true
    @ParAll = Star.get_pan_pars(@user_id,bCanUseIfatePars)
    session["customer_id"] = getFullFuncShow_CustomerId()
    @ParAll["full_show"] = true
    @simplepan = params["simplepan"] != nil ? (params["simplepan"] == "normal" ? false : true) : (session["simplepan"] == nil ? false : session["simplepan"])
    session["simplepan"] = @simplepan
    pan

    @edit_customer_url = ""
    @url_back = ""

    @aUrlKey = []
    @aUrlValue = []
    @customer_user_id = 0
    render :layout => "star_pan"
  end

  protected
  def authenticate_without_signin(user_id)
    if (@product_type == "show") then
      @oUserAp = g_getShowUserAp(user_id,@ap_name)
    elsif (@product_type == "pan2")
      @oUserAp = g_getUserCurrentAp(user_id,@ap_name)
    else
      @oUserAp = g_getGuestUserAp(user_id,@ap_name)
    end
    @showAdvertise = true
    @firstShowAdvertise = @oUserAp.firstShowAdvertise && @showAdvertise ? "pan_information_block" : "pan_xdate_block"
    @firstShowAdvertise = session["star_firstShowAdvertise"] if session["star_firstShowAdvertise"] != nil
    @ParAll = Star.get_pan_pars(user_id,g_can_use_pars(@oUserAp))
    @canUsePan = g_canUsePan(session["customer_id"])

    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @hHallNumberAdver = find_last_hallnumber_adver(@ap_name,@oUserAp,@ApFunc)
    @hCanDeal = canDealCheck(user_id,@oUserAp,@ap_name)
    @main_products = g_getMainProducts(@ap_name)

    @canAddPe = session["customer_id"] != nil

    @sign_in = false
    @sign_in = true if (user_id != nil)  #if logged_in?
  end

  public
  def pan2share
    @ap_name = "star"
    @product_type = "pan2"

    pan_prepare_data(params)
    # 關閉吉凶
    @ApFunc["GiSuongEnable"] = false
    # 關閉論斷
    @ApFunc["n_explain_pan"] = -1
    @ApFunc["n_explain_free_pan"] = -1
    (Cfate::PAN_NORMAL..Cfate::PAN_FLOWMIN).each do |nPanType|
      @ApFunc[Cfate.gGetPanTypeHashKey(nPanType)] = false
    end
    @ApFunc[Cfate.gGetPanTypeHashKey(@m_nPanType)] = true

    h = Hash.new
    h["ParAll"] = @ParAll
    h["ApFunc"] = @ApFunc
    h["customer"] = @UserInfo

    if (@customer_id == nil) then
      @url = "empty"
      @url_iframe = "empty"
    else
      # 需判斷重覆的狀況
      a = ApShare.check_userid(@user_id).check_customerid(@customer_id).check_apname(@ap_name).check_pantype(@m_nPanType).check_actionname("pan2").check_ap_data(h.to_json)
      if (@m_nPanType == Cfate::PAN_NORMAL) then
        ap_share = a.last
      else
        a = a.check_eyear(@UserDefData[Cfate::EYear])
        if (@m_nPanType <= Cfate::PAN_FLOWYEAR) then
          ap_share = a.last
        else
          a = a.check_emonth(@UserDefData[Cfate::LeapMonth],@UserDefData[Cfate::EMonth])
          if (@m_nPanType == Cfate::PAN_FLOWMONTH) then
            ap_share = a.last
          else
            a = a.check_eday(@UserDefData[Cfate::EDate])
            if (@m_nPanType == Cfate::PAN_FLOWDATE) then
              ap_share = a.last
            else
              a = a.check_hour(@UserDefData[Cfate::WHour])
              if (@m_nPanType == Cfate::PAN_FLOWTIME) then
                ap_share = a.last
              else
                # Cfate::PAN_FLOWMIN
                ap_share = a.check_minute(@UserDefData[Cfate::WMinute]).last
              end
            end
          end
        end
      end

      if (ap_share == nil) then
        ap_share = new_ap_share(h)
      end

      # @h = ap_share.hAll["UserDefData"]

      sHost = Pm.getSystemValue("MEEN_URL")
      if (params["locale"] == nil || params["locale"] == "") then
        @url = "#{sHost}/#{params["controller"]}/pan2?k=#{ap_share.share_key}"
        @url_iframe = "<iframe width='560' height='315' src='#{@url}' frameborder='0' scrolling='yes'></iframe>"
      else
        @url = "#{sHost}/#{params["locale"]}/#{params["controller"]}/pan2?k=#{ap_share.share_key}"
        @url_iframe = "<iframe width='560' height='315' src='#{@url}' frameborder='0' scrolling='yes'></iframe>"
      end
    end

    respond_to do |format|
      format.html { redirect_to root_path } #for my controller, i wanted it to be JS only
      format.js
    end
    render :partial => "ifate/star/pan2share", :layout => false, :locals => {}
  end

  protected
  def new_ap_share(h)
    ap_share = ApShare.new
    ap_share.user_id = @user_id
    ap_share.customer_id = @customer_id
    ap_share.ap_name = @ap_name
    ap_share.action_name = "pan2"
    ap_share.pantype = @m_nPanType
    ap_share.share_key = SecureRandom.uuid
    ap_share.eyear = @UserDefData[Cfate::EYear]
    ap_share.eleap = @UserDefData[Cfate::LeapMonth]
    ap_share.emonth = @UserDefData[Cfate::EMonth]
    ap_share.eday = @UserDefData[Cfate::EDate]
    ap_share.hour = @UserDefData[Cfate::WHour]
    ap_share.minute = @UserDefData[Cfate::WMinute]
    ap_share.sky_earth = ""
    ap_share.hAll = h
    ap_share.count = 0
    ap_share.status = 0
    ap_share.save!

    return ap_share
  end

  public
  def pan2
    @ap_name = "star"
    @product_type = "pan2"
    hData = g_getInfoForPan2(params["k"],@ap_name,"pan2")
    # hData = g_getInfoForPan2("123333445566666666")

    if (hData == nil) then
      redirect_to root_path, :ap_name => @ap_name
    else
      @user_id = hData["user_id"]
      authenticate_without_signin(@user_id)

      pan_prepare_data(hData)

      @ApFunc = hData["ApFunc"]
      @ParAll = hData["ParAll"]

      # 精實創業，用來分析使用狀況
      # Pm.saveTestDb("#{params[:controller]}-#{params[:action]}-#{@oUserAp.user_id}-#{@oUserAp.product_type}",params.to_json)

      # no pan change event
      @bPanChange = false
      @bDeclare = true
      @UserInfo[Cfate::Name] = "OOO"

      @oStar = Star.new
      @oStar.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll)

      # 排盤資料
      pan_pan_info()
      # no user name
      # @PI_Name[1] = "OOO"

      # 萬年曆
      pan_xdate_info()

      # 個人事件資料
      pan_prepare_personalevent_data()

      # 產品購買資訊
      pan_userap_info()

      @edit_customer_url = "/ifate/customer/edit"
      @select_customer_url = "/ifate/customer/index"
      @url_back = "/ifate/star/pan"

      @aUrlKey = []
      @aUrlValue = []

      g_userap_addone_usecount(params["action"],@m_nPanType,@oUserAp)

      render :layout => 'star_pan2'
    end
  end

  def zeri_pan_show_ajax
    if (params["ajax_data_key"] != nil) then
      hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    else
      hAjaxData = params
    end
    @target_name = hAjaxData["target_name"]

    answer = Zeri.check_in_answers(@ap_name+@target_name,@user_id,hAjaxData["timestamp"])
    if (answer != nil) then
      zeri_pan_data(answer)
      # zeri_pan_data(hAjaxData)
      render :partial => "ifate/star/zeri_pan_show_ajax"
    else
      render :nothing => true
    end
  end
  def zeri_pan_answer
    @chaxuncanshuchucunmingcheng = params["chaxuncanshuchucunmingcheng"]
    @target_name = params["target_name"]
    @answers = Zeri.find_answers(@ap_name+@target_name,@user_id)
    if (@answers == []) then
      render :text => Zeri.get_zeri_str("meizhaodao")
    else
      hPars = Hash.new
      hPars["ap_name"] = "star"
      hPars["timestamp"] = @answers[0]["timestamp"]
      hPars["sex"] = @answers[0]["sex"]
      hPars["calType"] = @answers[0]["calType"]
      zeri_pan_data(hPars)

      render :layout => 'star_zeri_pan_answer'
    end
  end
  protected
  def zeri_pan_data(hPars)
    hData = Xdate.date_input_prepare(hPars)
    pan_prepare_data(hData)
    # 放在後面，沒有選customer時，才不會出現bug
    @customer_id = 0

    @m_nPanType = Cfate::PAN_NORMAL
    @oStar = Star.new
    @oStar.g_GetPanInfo(@m_nPanType,@UserInfo,@UserDefData,@ParAll)

    # 排盤資料
    pan_pan_info()

    # 萬年曆
    pan_xdate_info()

    # 個人事件資料
    pan_prepare_personalevent_data()

    # 產品購買資訊
    pan_userap_info()

    @edit_customer_url = "/ifate/customer/edit"
    @select_customer_url = "/ifate/customer/index"
    @url_back = "/ifate/star/pan"

    @aUrlKey = []
    @aUrlValue = []

    # 精實創業，用來分析使用狀況
    # Pm.saveTestDb("#{hPars[:controller]}-#{hPars[:action]}-#{@oUserAp.user_id}-#{@oUserAp.product_type}","#{hData}")

    @sMy_url = ""
  end

  protected
  def pan_prepare_data2(hData=nil)
    # 排盤資訊
    @PanLastYear = Cfate::PAN_LAST_YEAR # 萬年曆最後一年

    # 設定客戶資訊
    hUserInfo = set_pan_customer_info2(hData)
    @UserInfo2 = hUserInfo

    # 處理流盤時間
    hUserDefData = set_pan_user_def_data2(hData)
    @UserDefData2 = hUserDefData
  end
  def set_pan_customer_info2(hData=nil)
    hUserInfo = Hash.new
    hUserInfo["remote_ip"] = remote_ip()

    # 預設用現在時間當客戶生日排盤,男生
    t = time_now_local()
    hUserInfo[Cfate::WYear] = t.year
    hUserInfo[Cfate::WMonth] = t.month
    hUserInfo[Cfate::WDate] = t.day
    hUserInfo[Cfate::WHour] = t.hour
    hUserInfo[Cfate::Sex] = false
    hUserInfo[Cfate::Name] = "JiXiong"

    session["gi_xiong_chaxun_customer_id"] = hData["customer_id"] == nil ? session["customer_id"] : hData["customer_id"].to_i
    nPanType = session["gi_xiong_chaxun_pan_type"] == nil ? Cfate::PAN_NORMAL : session["gi_xiong_chaxun_pan_type"]

    if (hData["pantype"] != nil) then
      nPanType = hData["pantype"].to_i
    end
    if (nPanType > @ApFunc["LastPanType"]) then
      nPanType = @ApFunc["LastPanType"]
    end
    @m_nPanType2 = nPanType
    session["gi_xiong_chaxun_pan_type"] = @m_nPanType2

    @gi_xiong_chaxun_customer_id = session["gi_xiong_chaxun_customer_id"]
    if (@gi_xiong_chaxun_customer_id != nil && @gi_xiong_chaxun_customer_id > 0) then
      oCustomer = UserCustomer.check_option_user_id(@user_id).find(@gi_xiong_chaxun_customer_id)
      if (oCustomer != nil) then
        hUserInfo[Cfate::WYear] = oCustomer.wyear
        hUserInfo[Cfate::WMonth] = oCustomer.wmonth
        hUserInfo[Cfate::WDate] = oCustomer.wday
        hUserInfo[Cfate::WHour] = oCustomer.hour
        hUserInfo[Cfate::Sex] = oCustomer.sex == 1 ? false : true
        hUserInfo[Cfate::Name] = oCustomer.name
      end
    end
    @gi_xiong_chaxun_usercustomer = UserCustomer.find_by_id(@gi_xiong_chaxun_customer_id)

    return hUserInfo
  end
  def set_pan_user_def_data2(hData)
    # 處理流盤時間
    if (hData["USE_CUR_TIME"] != nil) then
      hUserDefData = get_cur_date_info()
    else
      #若有傳入,以傳入為主
      hUserDefData = Hash.new
      hUserDefData[Cfate::EYear] = hData["eyear"].to_i
      hUserDefData[Cfate::EMonth] = hData["emonth"].to_i
      hUserDefData[Cfate::EDate] = hData["eday"].to_i
      hUserDefData[Cfate::LeapMonth] = hData["eleap"]
      hUserDefData[Cfate::WHour] = hData["hour"].to_i
      hUserDefData[Cfate::WMinute] = hData["minute"].to_i
    end

    return hUserDefData
  end
  def ji_xiong_chaxun_prepare_data(hPars)
    sLang = my_current_lang()
    @hData = Xdate.date_input_prepare(hPars)
    @hData.merge!(hPars) { |key, v1, v2| v1 }
    pan_prepare_data2(@hData)
    @par_YearDisplay = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]
    @starYearSelect = 1901
    @stopYearSelect = 2099
    t = time_now_local()
    @TodayYear = t.year
    if (@hData["calType"] == Xdate::CT_SOLAR) then
      @y = @hData["wyear"]
      @l = false
      @m = @hData["wmonth"]
      @d = @hData["wday"]
    else
      @y = @hData["eyear"]
      @l = @hData["eleap"]
      @m = @hData["emonth"]
      @d = @hData["eday"]
    end

    @h = @hData["hour"]
    @min = @hData["minute"]

    getPanType(@m_nPanType2,Cfate::PAN_TENYEAR,Cfate::PAN_FLOWTIME)

    find_search_customers(hPars)

    @ok_button = @hData["ok_button"].to_i
  end
  def find_search_customers(hPars,nLastId=0,nCount=100)
    @hCustDisplay = {"b_name" => true}
    keys = ["b_name_abbr","b_sex","b_solar","b_solar_year","b_solar_md","b_solar_hm","b_lunar","b_lunar_year","b_lunar_md","b_lunar_hm","b_blood","b_showsum"]
    keys.each do |key|
      @hCustDisplay[key] = false
    end
    sSearchData = hPars["customer_search_data"]
    if (sSearchData == nil) then
      sSearchData = ""
    end
    @par_YearDisplay = @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY]
    user_id = @user_id
    session["customer_search_data2"],@all_user_customer2,@list_count2 = api_find_search_customers(@user_id,sSearchData,nLastId,nCount,@par_YearDisplay)
    @customer_search_data2 = session["customer_search_data2"]
  end

  def make_ji_xiong_jieguos()
    oStar = Star.new
    oStar.g_GetPanInfo(@m_nPanType2,@UserInfo2,@UserDefData2,nil)
    oPanGiSuong = PanGiSuong.new

    ji_xiong_jieguos_gong = Array.new
    (Star::EX_HOUSE_1..Star::EX_HOUSE_12).each do |gong|
      oPanGiSuong.ap_Star_Init(oStar,@m_nPanType2,@UserInfo2,@UserDefData2,gong)
      a,shijian_taitou = make_ji_xiong_jieguos2(oStar,oPanGiSuong,gong)
      ji_xiong_jieguos_gong.push(a)
      @shijian_taitou = shijian_taitou
    end

    shijian_length = ji_xiong_jieguos_gong[0].length
    b = Array.new(shijian_length) {Array.new(12,"")}
    c = Array.new(shijian_length)
    c1 = Array.new(shijian_length)
    c2 = Array.new(shijian_length)
    j = 0
    (Star::EX_HOUSE_1..Star::EX_HOUSE_12).each do |gong|
      (0..shijian_length-1).each do |shijian_idx|
        b[shijian_idx][gong - 1] = ji_xiong_jieguos_gong[gong - 1][shijian_idx]["ji_xiong"]
        if (gong == Star::EX_HOUSE_1) then
          c[shijian_idx] = ji_xiong_jieguos_gong[gong - 1][shijian_idx]["shijian"]
          c1[shijian_idx] = ji_xiong_jieguos_gong[gong - 1][shijian_idx]["shijian1"]
          c2[shijian_idx] = ji_xiong_jieguos_gong[gong - 1][shijian_idx]["shijian2"]
        end
      end
    end
    @ji_xiong_jieguos = Array.new
    (0..shijian_length-1).each do |shijian_idx|
      h = Hash.new
      h["shijian"] = c[shijian_idx] + c1[shijian_idx] + c2[shijian_idx]
      h["ji_xiong"] = b[shijian_idx]
      @ji_xiong_jieguos.push(h)
    end
  end

  def make_ji_xiong_jieguos2(oStar,oPanGiSuong,gong)
    nPanType = @m_nPanType2

    if (nPanType == Cfate::PAN_TENYEAR) then
      jieguos,shijian_taitou = make_ji_xiong_jieguos_Tenyear(nPanType,oStar,oPanGiSuong,gong)
    end
    if (nPanType == Cfate::PAN_FLOWYEAR) then
      jieguos,shijian_taitou = make_ji_xiong_jieguos_Flowyear(nPanType,oStar,oPanGiSuong,gong)
    end
    if (nPanType == Cfate::PAN_FLOWMONTH) then
      jieguos,shijian_taitou = make_ji_xiong_jieguos_Flowmonth(nPanType,oStar,oPanGiSuong,gong)
    end
    if (nPanType == Cfate::PAN_FLOWDATE) then
      jieguos,shijian_taitou = make_ji_xiong_jieguos_Flowdate(nPanType,oStar,oPanGiSuong,gong)
    end
    if (nPanType >= Cfate::PAN_FLOWTIME) then
      jieguos,shijian_taitou = make_ji_xiong_jieguos_Flowtime(nPanType,oStar,oPanGiSuong,gong)
    end

    return jieguos,shijian_taitou
  end
  def make_ji_xiong_jieguos_Tenyear(nPanType,oStar,oPanGiSuong,gong)
    hGisuongUserData = @UserDefData2.clone
    hGisuongUserData[Star::HOUSE_NAME] = gong
    wy,wm,wd,wh,wmin,ey,em,ed,el,et,emin = Xdate.gHouse_GetNow(remote_ip())

    if (Cfate::PAN_TENYEAR <= @ApFunc["LastXdatePanType"]) then
      #取得十年大限開始歲數
      nLargeHouseIndex = oStar.gHouse_getLargeSan()
      nEarthIndex = Star.HouseIndex2EarthIndex(nLargeHouseIndex)
      nEarth = Earth.EarthIndex2Earth(nEarthIndex)
      nETenYearStartYearOld = oStar.gHouse_GetLarge(nEarth)

      # 十年大限開始年
      nHighLightStartEYear = oStar.uig_EY() + nETenYearStartYearOld - 1
      nDisStartEYear = nHighLightStartEYear

      # 萬年曆十年盤起始年紀
      nStartEYearOld = oStar.gNormal_GetYearOld(oStar.uig_EY(),nDisStartEYear)

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      a = [nHighLightStartEYear]
      a.each_index do |i|
        hGisuongUserData[Cfate::EYear] = a[i]
        hGisuongUserData[Cfate::EMonth] = 6
        hGisuongUserData[Cfate::EDate] = 1
        hGisuongUserData[Cfate::LeapMonth] = false
        hGisuongUserData[Cfate::WHour] = 0
        hgs = oWeenApi.g_explode_text(Cfate::PAN_TENYEAR,@UserInfo2,hGisuongUserData,@ApFunc,nil,false)
        h[a[i]] = hgs["explode_text"]
      end

      jieguos = Array.new
      # new from WeenApi 1
      (1..10).each do |nRow|
        nRowIndex = nRow - 1
        nAge = nStartEYearOld + nRowIndex
        nStartEYear = nDisStartEYear + nRowIndex

        # new from WeenApi 0
        nGiSuongStartEYear = nDisStartEYear
        szGi = h[nGiSuongStartEYear]["gi"][nStartEYear - nGiSuongStartEYear]
        szSuong = h[nGiSuongStartEYear]["xiong"][nStartEYear - nGiSuongStartEYear]
        # new from WeenApi 1

        h2 = Hash.new
        h2["shijian"] = "#{nStartEYear}#{Pm.t("IDS_X_YEAR")}"
        h2["shijian1"] = Xdate.GetLunarYearGanZhiStr(nStartEYear)
        h2["shijian2"] = Xdate.GetYearOldStr(nAge)
        h2["ji_xiong"] = [szGi,szSuong]

        jieguos.push(h2)

        if (nStartEYear == ey) then
          @current_ji_xiong_line = nRowIndex
        end
      end
      shijian_taitou = "#{nDisStartEYear}~#{nDisStartEYear + 10}"
    end
    return jieguos,shijian_taitou
  end
  def make_ji_xiong_jieguos_Flowyear(nPanType,oStar,oPanGiSuong,gong)
    hGisuongUserData = @UserDefData2.clone
    hGisuongUserData[Star::HOUSE_NAME] = gong
    wy,wm,wd,wh,wmin,ey,em,ed,el,et,emin = Xdate.gHouse_GetNow(remote_ip())

#   elsif (@m_nPanType == Cfate::PAN_FLOWYEAR) then
    if (Cfate::PAN_FLOWYEAR <= @ApFunc["LastXdatePanType"]) then
      nAge = 0

      nEYear = oStar.fdg_EY(nPanType)
      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      hGisuongUserData[Cfate::EYear] = nEYear
      hGisuongUserData[Cfate::EMonth] = 6
      hGisuongUserData[Cfate::EDate] = 1
      hGisuongUserData[Cfate::LeapMonth] = false
      hGisuongUserData[Cfate::WHour] = 0
      hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWYEAR,@UserInfo2,hGisuongUserData,@ApFunc,nil,false)
      h[nEYear] = hgs["explode_text"]
      # new from WeenApi 1

      # 取得該年月資訊
      aaMonth = Xdate.GetEastMonthArray(nEYear)
      jieguos = Array.new
      aaMonth.each_index do |nMonthIndex|
        aData = Array.new

        aMonth = aaMonth[nMonthIndex]

        nEMonth = aMonth[0]
        nSky,nEarth = Xdate.GetLunarMonthGanZhiSkyEarth(nEYear,nEMonth)

        # new from WeenApi 0
        szGi = h[nEYear]["gi"][nMonthIndex]
        szSuong = h[nEYear]["xiong"][nMonthIndex]
          # new from WeenApi 1
        h2 = Hash.new
        m = aMonth[2] ? "#{Pm.t("IDS_X_LEAP")}#{nEMonth}#{Pm.t("IDS_X_MONTH")}" : "#{nEMonth}#{Pm.t("IDS_X_MONTH")}"
        h2["shijian"] = "#{m}"
        h2["shijian1"] = ""
        h2["shijian2"] = ""
        h2["ji_xiong"] = [szGi,szSuong]

        jieguos.push(h2)

        if ([nEYear,nEMonth,aMonth[2]] == [ey,em,el]) then
          @current_ji_xiong_line = nMonthIndex
        end
      end
      shijian_taitou = "#{nEYear}#{Pm.t("IDS_X_YEAR")}"
    end
    return jieguos,shijian_taitou
  end
  def make_ji_xiong_jieguos_Flowmonth(nPanType,oStar,oPanGiSuong,gong)
    hGisuongUserData = @UserDefData2.clone
    hGisuongUserData[Star::HOUSE_NAME] = gong
    wy,wm,wd,wh,wmin,ey,em,ed,el,et,emin = Xdate.gHouse_GetNow(remote_ip())

    if (Cfate::PAN_FLOWMONTH <= @ApFunc["LastXdatePanType"]) then
      nEYear = oStar.fdg_EY(nPanType)
      nEMonth = oStar.fdg_EM(nPanType)
      bLeapMonth = oStar.fdg_EL(nPanType)
      # 取得該年月資訊
      aaMonth = Xdate.GetEastMonthArray(nEYear)
      nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,aaMonth)
      aDataGiSuong = Array.new
      nAge = 0
      nDays = aaMonth[nMonthIndex][1]

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      hGisuongUserData[Cfate::EYear] = nEYear
      hGisuongUserData[Cfate::EMonth] = nEMonth
      hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
      hGisuongUserData[Cfate::EDate] = 1
      hGisuongUserData[Cfate::WHour] = 0
      hGisuongUserData[Cfate::ETime] = 0
      hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWMONTH,@UserInfo2,hGisuongUserData,@ApFunc,nil,false)
      h[nEMonth] = hgs["explode_text"]
      # new from WeenApi 1
      jieguos = Array.new
      (1..aaMonth[nMonthIndex][1]).each_with_index do |nEDate,nRowIndex|
        aData = Array.new

        # new from WeenApi 0
        szGi = h[nEMonth]["gi"][nEDate - 1]
        szSuong = h[nEMonth]["xiong"][nEDate - 1]
        # new from WeenApi 1

        h2 = Hash.new
        h2["shijian"] = "#{nEDate}#{Pm.t("IDS_X_DAY")}"
        h2["shijian1"] = ""
        h2["shijian2"] = ""
        h2["ji_xiong"] = [szGi,szSuong]

        jieguos.push(h2)

        if ([nEYear,nEMonth,bLeapMonth,nEDate] == [ey,em,el,ed]) then
          @current_ji_xiong_line = nRowIndex
        end
      end
      m = bLeapMonth ? "#{Pm.t("IDS_X_LEAP")}#{nEMonth}#{Pm.t("IDS_X_MONTH")}" : "#{nEMonth}#{Pm.t("IDS_X_MONTH")}"
      shijian_taitou = "#{nong()}#{nEYear}#{Pm.t("IDS_X_YEAR")}#{m}"
    end
    return jieguos,shijian_taitou
  end
  def make_ji_xiong_jieguos_Flowdate(nPanType,oStar,oPanGiSuong,gong)
    hGisuongUserData = @UserDefData2.clone
    hGisuongUserData[Star::HOUSE_NAME] = gong
    wy,wm,wd,wh,wmin,ey,em,ed,el,et,emin = Xdate.gHouse_GetNow(remote_ip())

    if (Cfate::PAN_FLOWDATE <= @ApFunc["LastXdatePanType"]) then
      nEYear = oStar.fdg_EY(nPanType)
      nEMonth = oStar.fdg_EM(nPanType)
      bLeapMonth = oStar.fdg_EL(nPanType)
      nEDate = oStar.fdg_ED(nPanType)

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      hGisuongUserData[Cfate::EYear] = nEYear
      hGisuongUserData[Cfate::EMonth] = nEMonth
      hGisuongUserData[Cfate::EDate] = nEDate
      hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
      hGisuongUserData[Cfate::WHour] = 0
      hGisuongUserData[Cfate::ETime] = 0
      hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWDATE,@UserInfo2,hGisuongUserData,@ApFunc,nil,false)
      h[nEDate] = hgs["explode_text"]

      # 日表頭
      jieguos = Array.new
      (1..12).each_with_index do |nEarth,nRowIndex|
        nETime = nEarth - 1
        aData = Array.new

        # new from WeenApi 0
        szGi = h[nEDate]["gi"][nEarth - 1]
        szSuong = h[nEDate]["xiong"][nEarth - 1]
        # new from WeenApi 1

        h2 = Hash.new
        h2["shijian"] = "#{Xdate.GetETimeStr(nETime)}"
        h2["shijian1"] = ""
        h2["shijian2"] = ""
        h2["ji_xiong"] = [szGi,szSuong]

        jieguos.push(h2)

        if ([nEYear,nEMonth,bLeapMonth,nEDate,nETime] == [ey,em,el,ed,et]) then
          @current_ji_xiong_line = nRowIndex
        end
      end
      m = bLeapMonth ? "#{Pm.t("IDS_X_LEAP")}#{nEMonth}#{Pm.t("IDS_X_MONTH")}" : "#{nEMonth}#{Pm.t("IDS_X_MONTH")}"
      shijian_taitou = "#{nong()}#{nEYear}#{Pm.t("IDS_X_YEAR")}#{m}#{nEDate}#{Pm.t("IDS_X_DAY")}"
    end
    return jieguos,shijian_taitou
  end
  def make_ji_xiong_jieguos_Flowtime(nPanType,oStar,oPanGiSuong,gong)
    hGisuongUserData = @UserDefData2.clone
    hGisuongUserData[Star::HOUSE_NAME] = gong
    wy,wm,wd,wh,wmin,ey,em,ed,el,et,emin = Xdate.gHouse_GetNow(remote_ip())

    if (Cfate::PAN_FLOWTIME <= @ApFunc["LastXdatePanType"]) then
      nEYear = oStar.fdg_EY(nPanType)
      nEMonth = oStar.fdg_EM(nPanType)
      bLeapMonth = oStar.fdg_EL(nPanType)
      nEDate = oStar.fdg_ED(nPanType)
      nETime = oStar.fdg_ET(nPanType)
      nWHour = oStar.fdg_WH(nPanType)

      # new from WeenApi 0
      oWeenApi = WeenApi.new
      h = Hash.new
      hGisuongUserData[Cfate::EYear] = nEYear
      hGisuongUserData[Cfate::EMonth] = nEMonth
      hGisuongUserData[Cfate::EDate] = nEDate
      hGisuongUserData[Cfate::LeapMonth] = bLeapMonth
      hGisuongUserData[Cfate::ETime] = Pm.TestNo12(nETime)
      hGisuongUserData[Cfate::WHour] = Xdate.ETimeFirstHour(nETime)
      hgs = oWeenApi.g_explode_text(Cfate::PAN_FLOWTIME,@UserInfo2,hGisuongUserData,@ApFunc,nil,false)
      h[hGisuongUserData[Cfate::ETime]] = hgs["explode_text"]
      # new from WeenApi 1

      jieguos = Array.new
      (1..12).each_with_index do |i,nRowIndex|
        # nETime = nEarth - 1
        aData = Array.new

        # new from WeenApi 0
        szGi = h[hGisuongUserData[Cfate::ETime]]["gi"][i - 1]
        szSuong = h[hGisuongUserData[Cfate::ETime]]["xiong"][i - 1]
        # new from WeenApi 1

        h2 = Hash.new
        min = Xdate.GetEMinRangeStr(nETime,i - 1)
        h2["shijian"] = "#{min}"
        h2["shijian1"] = ""
        h2["shijian2"] = ""
        h2["ji_xiong"] = [szGi,szSuong]

        jieguos.push(h2)

        if ([nEYear,nEMonth,bLeapMonth,nEDate,nETime,i-1] == [ey,em,el,ed,et,emin]) then
          @current_ji_xiong_line = nRowIndex
        end
      end
      m = bLeapMonth ? "#{Pm.t("IDS_X_LEAP")}#{nEMonth}#{Pm.t("IDS_X_MONTH")}" : "#{nEMonth}#{Pm.t("IDS_X_MONTH")}"
      shijian_taitou = "#{nong()}#{nEYear}#{Pm.t("IDS_X_YEAR")}#{m}#{nEDate}#{Pm.t("IDS_X_DAY")} #{Xdate.GetETimeStr(nETime)}"
    end
    return jieguos,shijian_taitou
  end
  def nong()
    s = Pm.t("IDS_S_UI_LUNAR_CAL")
    return s[0]
  end
  public
  def fuwu_shuoming
    render :layout => 'ifate'
  end

  def setup_zeri_par
    render :layout => 'ifate'
  end

  def ji_xiong_chaxun
    ji_xiong_chaxun_prepare_data(params)

    render :layout => 'ifate'
  end
  def ji_xiong_chaxun_ajax2
    @ap_name = "star"
    if (logged_in?) then
      authenticate_user!
      authenticate
    else
      @product_type = "show"
      authenticate_without_signin(nil)
    end
    hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    ji_xiong_chaxun_prepare_data(hAjaxData)
# puts "gi_xiong_chaxun_customer_id : #{@gi_xiong_chaxun_customer_id}"
    make_ji_xiong_jieguos()

    render :partial => "ifate/star/ji_xiong_chaxun_ajax2" , :layout => false, :locals => {:ji_xiong_jieguos => @ji_xiong_jieguos }
  end
  def ji_xiong_chaxun_ajax
    @ap_name = "star"
    if (logged_in?) then
      authenticate_user!
      authenticate
    else
      @product_type = "show"
      authenticate_without_signin(nil)
    end
    hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    ji_xiong_chaxun_prepare_data(hAjaxData)

    make_ji_xiong_jieguos()

    respond_to do |format|
      format.html { redirect_to root_path } #for my controller, i wanted it to be JS only
      format.js
    end
    render :partial => "ifate/star/ji_xiong_chaxun_ajax" , :layout => false, :locals => {:ji_xiong_jieguos => @ji_xiong_jieguos }
  end

  def user_customer_search
    dates = params["ajax_data_key"]
    hPars = Cfate.pan_par_dbfield2hash(dates)
    find_search_customers(hPars)

    render :partial => 'ifate/customer/customer_list_data2'
  end

  public
  def final_to_csv()
    hAllHouse = @Score["AllHouse"]
    @csv_out = Array.new
    i = 0    
    s = "#{@UserInfo[Cfate::Name]} #{Pm.GetStr("IDS_S_UI_SOLAR_CAL")} #{@birth}"
    if (@leap) then s2 = Pm.t("IDS_X_LEAP") else s2 = "" end
    if (@calType == Xdate::CT_SOLAR) then s3 = Pm.t("IDS_S_UI_SOLAR_CAL") else s3 = Pm.t("IDS_S_UI_LUNAR_CAL") end
    s1 = "#{s3} #{@year}/#{s2}#{@month}/#{@day} #{@hour}:#{@minute}"
    @csv_out.push(["#{s}","#{Pm.t("star.score.each_house_summary_table")}","#{Cfate.GetPanName(@m_nPanType)}","#{s1}"].join(";"))
    i += 1
    hAllHouse.each_pair do |key, value|
      @csv_out.push([i,key,value,""].join(";"))
      i += 1
    end
    hDetail = @Score["Details"]
    hDetail.each_pair do |key, value|
      @csv_out.push([i,value["house_name"],"",value["house_name"][0]].join(";"))
      i += 1
      @csv_out.push([i,"#{value["house_name"]}#{Pm.t("star.score.summary")}",value["house_score"],value["house_name"][0]].join(";"))
      i += 1
      hHouseDetail = value["house_detail"]
      hStars = hHouseDetail["star"]
      @csv_out.push([i,"#{Pm.t("star.score.each_star")}","",value["house_name"][0]].join(";"))
      i += 1
          @csv_out.push([i,"#{Pm.t("star.score.a_star")}","",value["house_name"][0]].join(";"))
          i += 1
          hStars[Star::A_STAR].each_pair do |key1, value1|
            value1.each_pair do |key2, value2|
              @csv_out.push([i,key2,value2,value["house_name"][0]].join(";"))
              i += 1
            end
          end
          @csv_out.push([i,"#{Pm.t("star.score.b_star")}","",value["house_name"][0]].join(";"))
          i += 1
          hStars[Star::B_STAR].each_pair do |key1, value1|
            if (key1 <= 29) then
              value1.each_pair do |key2, value2|
                @csv_out.push([i,key2,value2,value["house_name"][0]].join(";"))
                i += 1
              end
            end
          end
          @csv_out.push([i,"#{Pm.t("star.score.c_star")}","",value["house_name"][0]].join(";"))
          i += 1
          hStars[Star::B_STAR].each_pair do |key1, value1|
            if (key1 > 29) then
              value1.each_pair do |key2, value2|
                @csv_out.push([i,key2,value2,value["house_name"][0]].join(";"))
                i += 1
              end
            end
          end
          @csv_out.push([i,"#{Pm.t("star.score.god")}","",value["house_name"][0]].join(";"))
          i += 1
          hStars[Star::GOD].each_pair do |key1, value1|
            value1.each_pair do |key2, value2|
              @csv_out.push([i,key2,value2,value["house_name"][0]].join(";"))
              i += 1
            end
          end
          @csv_out.push([i,"#{Pm.t("star.score.doctor")}","",value["house_name"][0]].join(";"))
          i += 1
          hStars[Star::DOCTOR].each_pair do |key1, value1|
            value1.each_pair do |key2, value2|
              @csv_out.push([i,key2,value2,value["house_name"][0]].join(";"))
              i += 1
            end
          end
          @csv_out.push([i,"#{Pm.t("star.score.yearstar")}","",value["house_name"][0]].join(";"))
          i += 1
          hStars[Star::YEARSTAR].each_pair do |key1, value1|
            value1.each_pair do |key2, value2|
              @csv_out.push([i,key2,value2,value["house_name"][0]].join(";"))
              i += 1
            end
          end
          @csv_out.push([i,"#{Pm.t("star.score.yeargod")}","",value["house_name"][0]].join(";"))
          i += 1
          hStars[Star::YEARGOD].each_pair do |key1, value1|
            value1.each_pair do |key2, value2|
              @csv_out.push([i,key2,value2,value["house_name"][0]].join(";"))
              i += 1
            end
          end
      hFourHua = hHouseDetail["four_hua"]
      @csv_out.push([i,"#{Pm.t("star.score.sihua_summary")}",hFourHua["summary"],value["house_name"][0]].join(";"))
      i += 1
      @csv_out.push([i,"#{Pm.t("star.score.sihua_0")}",hFourHua[Star::FH_LU],value["house_name"][0]].join(";"))
      i += 1
      @csv_out.push([i,"#{Pm.t("star.score.sihua_1")}",hFourHua[Star::FH_CHUAN],value["house_name"][0]].join(";"))
      i += 1
      @csv_out.push([i,"#{Pm.t("star.score.sihua_2")}",hFourHua[Star::FH_KER],value["house_name"][0]].join(";"))
      i += 1
      @csv_out.push([i,"#{Pm.t("star.score.sihua_3")}",hFourHua[Star::FH_GI],value["house_name"][0]].join(";"))
      i += 1
    end
    
  end
end
