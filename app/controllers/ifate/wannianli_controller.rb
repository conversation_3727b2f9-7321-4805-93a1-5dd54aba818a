require("Wannianli.rb")
require("WeenApi.rb")

class Ifate::<PERSON><PERSON>liController < ApplicationController
  before_action :authenticate_user!, :except => [:pan_free,:pan_ajax_no_sign_in]
  before_action :authenticate, :except => [:pan_free,:pan_ajax_no_sign_in]

  WanNianLi = "wannianli"

  protected
  def logged_in?
    return (current_user != nil)
  end

  public
  def logged_in?
    return (current_user != nil)
  end
  def get_session_data()
    user_id = current_user.id if logged_in?
    g_get_session_data(user_id,"<PERSON><PERSON><PERSON>")
  end
  def save_session_data()
    user_id = current_user.id if logged_in?
    g_save_session_data(user_id,"<PERSON>nian<PERSON>")
  end
  def pan
    zeri_pan_data(params)
    # render :layout => 'wannianli_pan'
    save_session_data()
    @show_func = true
    render :layout => 'ifate'
  end
  def pan_free
    @user_id = current_user.id if logged_in?
    @ap_name = "wannian<PERSON>"
    @oUserAp = g_getUserCurrentAp(3,"wannianli")
    # @oUserAp = g_getGuestUserAp(3,"wannianli")
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    # @oUserAp.can_use_pars = true
    zeri_pan_data(params)
    # render :layout => 'wannianli_pan'
    # save_session_data()
    @free_ap_name = "wannianli_pan_free" 
    @show_func = false
    @sign_in = false
    @sign_in = true if (@user_id != nil)  #if logged_in?

    render :layout => 'wannianli_pan_free'
  end
  def pan_ajax
    hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])

    zeri_pan_data(hAjaxData)

    save_session_data()
    @show_func = true
    render :partial => "ifate/wannianli/pan_ajax" , :locals => {}
  end
  def pan_ajax_no_sign_in
    @oUserAp = g_getUserCurrentAp(3,"wannianli")
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])

    zeri_pan_data(hAjaxData)

    save_session_data()
    @show_func = false

    render :partial => "ifate/wannianli/pan_ajax" , :locals => {}
  end
  def zeri_pan_answer
    @chaxuncanshuchucunmingcheng = params["chaxuncanshuchucunmingcheng"]
    @target_name = params["target_name"]

    @answers = Zeri.find_answers(@ap_name+@target_name,@user_id)
    if (@answers == []) then
      render :text => Zeri.get_zeri_str("meizhaodao")
    else
      hPars = Hash.new
      hPars["ap_name"] = "wannianli"
      hPars["timestamp"] = @answers[0]["timestamp"]
      hPars["calType"] = @answers[0]["calType"]
      hPars["lifa"] = @answers[0]["lifa"]
      zeri_pan_data(hPars)

      render :layout => 'ifate'
      # render :layout => 'wannianli_zeri_pan_answer'
    end
  end
  def ganzhi_change
    hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])

    tiangan = hAjaxData["tiangan"].to_i
    dizhi = hAjaxData["dizhi"].to_i
    @const_data = Wannianli.zeri_pan_data_constant_data_ganzhi_select(tiangan,dizhi)
    @zeri = Hash.new
    @zeri["tiangan"] = tiangan
    @zeri["dizhi"] = dizhi

    render :partial => "ifate/wannianli/zeri_pan_ganzhi" , :locals => {}
  end
  def zeri_pan_show_ajax
    if (params["ajax_data_key"] != nil) then
      hAjaxData = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
    else
      hAjaxData = params
    end
    @target_name = hAjaxData["target_name"]

    answer = Zeri.check_in_answers(@ap_name+@target_name,@user_id,hAjaxData["timestamp"])
    if (answer != nil) then
      zeri_pan_data(answer)
      # zeri_pan_data(hAjaxData)
      render :partial => "ifate/wannianli/zeri_pan_show_ajax"
    else
      render :nothing => true
    end
  end
  def panparedit
    hPars = params["submit_data"]
    if (hPars == nil) then
      hPars = params
    end
    if (@ApFunc[Cfate::FUNC_SET_PARS]) then
      if (hPars["PanPar"] == nil) then
        @nPanPar = Wannianli::PanPar_Type
      else
        @nPanPar = hPars["PanPar"].to_i
      end
      if (hPars["PanParReset"] == "1") then
        hParAll = Wannianli.pan_par_init_each(@nPanPar,@ParAll)
        panPar2Db(hParAll)
      elsif (hPars["PanParUpdate"] == "1") then
        hParAll = Wannianli.pan_par_assign_each(@nPanPar,@ParAll,hPars)
        panPar2Db(hParAll)
        @action_result = Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
      elsif (hPars["PanParCopyAction"] == "1") then
        panparcopy_action(hPars)
        @action_result = Pm.GetStr("IDS_PAR_SETUP_SUCCESS")
      end
      @ParAll = Wannianli.get_pan_pars(@user_id,g_can_use_pars(@oUserAp))

      render :layout => 'ifate'
    else
      redirect_to :action => "pan"
    end
  end
  def management
    @controller_name = "wannianli"

    if (@ApFunc[Cfate::FUNC_SET_PARS]) then
      redirect_to :action => "panparedit" , :PanPar => Star::PanPar_Type
    else
      redirect_to :action => "pan"
      # render :layout => 'ifate'
    end
  end

  protected
  def authenticate
    get_session_data()
    #@user_name = current_user.name
    @user_name = current_user.email
    @user_level = ""
    @user_id = current_user.id

    # 使用過萬年曆會被記起來，下次進來就直接是萬年曆
    if ((params["ap_name"] == nil) && (params["ajax_data_key"] != nil)) then
      hPar = Cfate.pan_par_dbfield2hash(params["ajax_data_key"])
      @ap_name = hPar["ap_name"]
    else
      @ap_name = params["ap_name"]
    end
    if (@ap_name == nil) then
      @ap_name = "wannianli"
    end
    @ap_name = g_user_ap_name(@ap_name,@user_id)

    session["user_name"] = { :value => @user_name, :expires => 1.minute.from_now }

    session["userlevel"] = @user_level

    getUserCurrentAp(@user_id)
    # session["ap_name"] = @ap_name
    session["ap_version"] = @ap_version
    @ParAll = g_get_pan_pars(@ap_name,@user_id)

    star_version_check()
    @canAddPe = session["customer_id"] != nil

    @admin = current_user.roles? :admin
    @can_add_customer,@customer_max_count = g_can_add_customer(@user_id,@admin)
  end
  def getUserCurrentAp(user_id_session)
      user_id = user_id_session
      # if (params["ap_name"] == nil && params["ordernumber"] != nil) then
      #   orderme_id = params["ordernumber"].to_i
      #   oOrderme = Orderme.find_by_id(orderme_id)
      #   oUserAp = UserAp.find_by_id(oOrderme.old_userap_id)
      #   @ap_name = oUserAp.product_name
      #   # user_id = oUserAp.user_id
      # end
    session["customer_id"] = params["customer_id"] == nil ? session["customer_id"] : params["customer_id"].to_i
    if (session["customer_id"] == nil) then
      session["customer_id"] = g_getlastcustomer(user_id)
    end
    @oUserAp = g_getUserCurrentAp(user_id,@ap_name)
    if (@oUserAp.custDisplayCheck) then
      @panParType = IfatePar.check_userid_customer(@user_id).first
      if (@panParType == nil) then
        @hCustDisplay = Hash.new
      else
        @hCustDisplay = @panParType.hPars
      end
    else
      @hCustDisplay = Hash.new
    end
    @canUsePan = g_canUsePan(session["customer_id"])
  end

  def star_version_check()
    # 權限控管
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @hCanDeal = canDealCheck(@user_id,@oUserAp,@ap_name)
    @main_products = g_getMainProducts(@ap_name)
  end

  def zeri_pan_data(hPars)
    hData = Xdate.date_input_prepare(hPars)

    @customer_id = 0
    pan_prepare_data(hData)
    @lifa = hPars["lifa"]

    @oWannianli = Wannianli.new
    @oWannianli.g_GetPanInfo(Cfate::PAN_NORMAL,@UserInfo,@UserDefData,@ParAll)

    @m_nPanType = hPars["PanType"].to_i
    if (hPars["PanType"] == nil) then
      @m_nPanType = Cfate::PAN_FLOWMONTH
    end
    # 排盤資料
    pan_pan_info()

    pan_shiju_info()

    # # 萬年曆
    pan_xdate_info()

    # # 個人事件資料
    # pan_prepare_personalevent_data()

    # # 產品購買資訊
    pan_userap_info()

    # @edit_customer_url = "/ifate/customer/edit"
    # @select_customer_url = "/ifate/customer/index"
    # @url_back = "/ifate/star/pan"

    # @aUrlKey = []
    # @aUrlValue = []

    # # 精實創業，用來分析使用狀況
    # # Pm.saveTestDb("#{hPars[:controller]}-#{hPars[:action]}-#{@oUserAp.user_id}-#{@oUserAp.product_type}","#{hData}")

    # @sMy_url = ""
    @showAdvertise = true
  end
  def pan_prepare_data(hData=nil)
    # 排盤資訊
    @PanFirstYear = Cfate::PAN_FIRST_YEAR # 萬年曆第一年
    @PanLastYear = Cfate::PAN_LAST_YEAR # 萬年曆最後一年

    # 設定客戶資訊
    hUserInfo = set_pan_customer_info(hData)
    @UserInfo = hUserInfo
    userinfo_to_usercustomer(hUserInfo)

    # 處理流盤時間
    hUserDefData = set_pan_user_def_data(hData)
    @UserDefData = hUserDefData

    # 開始處理排盤相關資訊
    @WYear = hUserInfo[Cfate::WYear]
    @WMonth = hUserInfo[Cfate::WMonth]
    @WDate = hUserInfo[Cfate::WDate]
    @WHour = hUserInfo[Cfate::WHour]
    @Sex = hUserInfo[Cfate::Sex]
    @Name = hUserInfo[Cfate::Name]

    @UA_EYear = hUserDefData[Cfate::EYear]
    @UA_EMonth = hUserDefData[Cfate::EMonth]
    @UA_EDate = hUserDefData[Cfate::EDate]
    @UA_WHour = hUserDefData[Cfate::WHour]
    @UA_WMinute = hUserDefData[Cfate::WMinute]
    @UA_LeapMonth = hUserDefData[Cfate::LeapMonth]
    @UA_HouseName = hUserDefData[Star::HOUSE_NAME]

    @bPanChange = true
    @bDeclare = false
  end
  def set_pan_customer_info(hData=nil)
    hUserInfo = Hash.new
    hUserInfo["remote_ip"] = remote_ip()

    # 預設用現在時間當客戶生日排盤,男生
    t = time_now_local()
    hUserInfo[Cfate::CalType] = Xdate::CT_SOLAR
    hUserInfo[Cfate::WYear] = t.year
    hUserInfo[Cfate::WMonth] = t.month
    hUserInfo[Cfate::WDate] = t.day
    hUserInfo[Cfate::WHour] = 0 #t.hour 用t.hour會遇到23點，日干支會往後一天
    hUserInfo[Cfate::Sex] = false
    hUserInfo[Cfate::Name] = "Profate"
    hUserInfo[Cfate::WMinute] = 0 #t.min

    # session["customer_id"] = hData["customer_id"] == nil ? session["customer_id"] : hData["customer_id"].to_i

    # @customer_id = session["customer_id"]
    # if (@customer_id != nil && @customer_id > 0) then
    #   oCustomer = UserCustomer.check_option_user_id(@user_id).find(@customer_id)
    #   if (oCustomer != nil) then
    #     hUserInfo[Cfate::WYear] = oCustomer.wyear
    #     hUserInfo[Cfate::WMonth] = oCustomer.wmonth
    #     hUserInfo[Cfate::WDate] = oCustomer.wday
    #     hUserInfo[Cfate::WHour] = oCustomer.hour
    #     hUserInfo[Cfate::Sex] = oCustomer.sex == 1 ? false : true
    #     hUserInfo[Cfate::Name] = oCustomer.name
    #     @customer_user_id = oCustomer.user_id # 給全功能體驗版使用
    #   end
    # end

    if ((hData != nil) && (hData["wyear"] != nil)) then
      hUserInfo[Cfate::CalType] = hData["calType"]
      hUserInfo[Cfate::WYear] = hData["wyear"]
      hUserInfo[Cfate::WMonth] = hData["wmonth"]
      hUserInfo[Cfate::WDate] = hData["wday"]
      hUserInfo[Cfate::WHour] = hData["hour"]
      hUserInfo[Cfate::WMinute] = hData["minute"] == nil || hData["minute"] == -1 ? 59 : hData["minute"]
      hUserInfo[Cfate::Sex] = hData["sex"].to_i == 1 ? false : true
      hUserInfo[Cfate::Name] = hData["name"]
      hUserInfo[Cfate::EYear] = hData["eyear"]
      hUserInfo[Cfate::EMonth] = hData["emonth"]
      hUserInfo[Cfate::EDate] = hData["eday"]
    end
    return hUserInfo
  end
  def set_pan_user_def_data(hData)
    # 處理流盤時間
    if (hData["USE_CUR_TIME"] != nil || ((session["user_def_data"] == nil) && (hData["UD_EYear"] == nil))) then
      hUserDefData = get_cur_date_info()
    else
      #若有傳入,以傳入為主
      if (hData["UD_EYear"] == nil) then
        hUserDefData = session["user_def_data"]
      else
        hUserDefData = Hash.new
        hUserDefData[Cfate::EYear] = hData["UD_EYear"].to_i
        hUserDefData[Cfate::EMonth] = hData["UD_EMonth"].to_i
        hUserDefData[Cfate::EDate] = hData["UD_EDate"].to_i
        hUserDefData[Cfate::LeapMonth] = (hData["UD_ELeap"].to_i == 0) ? false : true
        hUserDefData[Cfate::WHour] = hData["UD_WHour"].to_i
        hUserDefData[Cfate::WMinute] = hData["UD_WMin"].to_i
      end
    end

    @nLeapMonth = hUserDefData[Cfate::LeapMonth] ? 1 : 0
    hUserDefData[Star::HOUSE_NAME] = hData["star_house"] == nil ? 99 : hData["star_house"].to_i

    session["user_def_data"] = hUserDefData
#puts("session["user_def_data"]#{session["user_def_data"][Cfate::EYear]}")
    return hUserDefData
  end
  def get_cur_date_info()
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
    hUserDefData = Hash.new
    hUserDefData[Cfate::WYear] = nWYear
    hUserDefData[Cfate::WMonth] = nWMonth
    hUserDefData[Cfate::WDate] = nWDate
    hUserDefData[Cfate::EYear] = nEYear
    hUserDefData[Cfate::EMonth] = nEMonth
    hUserDefData[Cfate::EDate] = nEDate
    hUserDefData[Cfate::LeapMonth] = bLeapMonth
    hUserDefData[Cfate::WHour] = nWHour
    hUserDefData[Cfate::WMinute] = nWMin
    return hUserDefData
  end
  def userinfo_to_usercustomer(hUserInfo)
    @user_customer = UserCustomer.new
    @user_customer.calType = hUserInfo[Cfate::CalType]
    @user_customer.wyear = hUserInfo[Cfate::WYear]
    @user_customer.wmonth = hUserInfo[Cfate::WMonth]
    @user_customer.wday = hUserInfo[Cfate::WDate]
    @user_customer.hour = hUserInfo[Cfate::WHour]
    @user_customer.minute = hUserInfo[Cfate::WMinute]
    @user_customer.name = hUserInfo[Cfate::Name]
    @user_customer.sex = hUserInfo[Cfate::Sex] ? 0 : 1
    @user_customer.eyear = hUserInfo[Cfate::EYear]
    @user_customer.emonth = hUserInfo[Cfate::EMonth]
    @user_customer.eday = hUserInfo[Cfate::EDate]
  end

  def new_for_now()
    h = Hash.new

    t = time_now_local()

    h["user_id"] = session["user_id"]
    ey,em,ed,el = Xdate.West2East(t.year,t.month,t.day)
    h[Cfate::Name] = Xdate.make_timestamp(t.year,t.month,t.day,t.hour)
    h[Cfate::Sex] = false
    h[Cfate::CalType] = Xdate::CT_SOLAR
    h[Cfate::WYear] = t.year
    h[Cfate::WMonth] = t.month
    h[Cfate::WDate] = t.day
    h[Cfate::EYear] = ey
    h[Cfate::ELeapMonth] = el
    h[Cfate::EMonth] = em
    h[Cfate::EDate] = ed
    h[Cfate::WHour] = t.hour
    h[Cfate::WMinute] = t.min

    @user_customer = UserCustomer.new
    @user_customer.calType = Xdate::CT_SOLAR
    @user_customer.wyear = t.year
    @user_customer.wmonth = t.month
    @user_customer.wday = t.day
    @user_customer.hour = t.hour
    @user_customer.minute = t.min
    @user_customer.name = ""
    @user_customer.sex = 1

    return h
  end
  def pan_pan_info()
    lifa = Wannianli::Lifa_nongli
    @wannianli_free_result = @oWannianli.free_info()
    @pan = {}
    calendar_title = "#{@oWannianli.uig_WY()} #{Zeri.get_zeri_str("wannianli.taitou.suici")} #{@oWannianli.uig_date_ganzhi_str(Wannianli::FuHe_GanZhi_year,lifa)}"
    @pan["calendar_title"] = calendar_title
    left_1 = @oWannianli.uig_WM() < 10 ? Xdate.da_su_zi(@oWannianli.uig_WM()) : @oWannianli.uig_WM().to_s
    left_1 = "#{left_1}#{Zeri.get_zeri_str("wannianli.taitou.yue")}"
    @pan["cal_left_1"] = left_1  # 2月
    cal_left_2 = @oWannianli.uig_date_ganzhi_str(Wannianli::FuHe_GanZhi_month,lifa) # 甲寅  /月
    @pan["cal_left_2"] = cal_left_2[0,2]
    cal_left_3 = @oWannianli.uig_date_jieqi_str() # 立春
    @pan["cal_left_3"] = cal_left_3
    @pan["cal_center"] = @oWannianli.uig_WD()
    @pan["cal_center_low_1"] = @oWannianli.GetWWeekDayFullStr()
    @pan["cal_center_low_2"] = @oWannianli.uig_date_ganzhi_str(Wannianli::FuHe_GanZhi_day,lifa)
    s1,s2,s3,s4 = @oWannianli.uig_E_BirthDate_GuoZi_Str2()
    @pan["cal_right1"] = s1
    @pan["cal_right2"] = s2
    @pan["cal_right3"] = s3
    @pan["cal_right4"] = s4
    @pan["cal_weekday"] = @oWannianli.uig_get_weekday()
  end
  def pan_shiju_info()
    @shijus = @oWannianli.uig_ri_shiju()
    y,m,d,h = @oWannianli.uig_W_YMDH()
    @farmcal = Xdate.farmercal(y,m,d)
  end
  def pan_xdate_info()
    # 萬年曆
    makeXdate_data(@oWannianli)
  end
  def makeXdate_data(oWannianli)
    hAppData = Hash.new
    nPanType = @m_nPanType
    hNow = new_for_now()

    hAppData["CurWYear"] = hNow[Cfate::WYear]
    hAppData["CurWMonth"] = hNow[Cfate::WMonth]
    hAppData["CurWDate"] = hNow[Cfate::WDate]
    hAppData["CurWHour"] = hNow[Cfate::WHour]
    hAppData["CurWMin "] = hNow[Cfate::WMinute]
    hAppData["FlowWYear"] = oWannianli.uig_WY()
    hAppData["FlowWMonth"] = oWannianli.uig_WM()
    hAppData["FlowWDate"] = oWannianli.uig_WD()
    hAppData["FlowWHour"] = oWannianli.uig_WH()
    hAppData["FlowWMin"] = oWannianli.uig_WMI()

    hParAll = @ParAll != nil ? @ParAll.clone : {}
    hParAll[Cfate::PAR_PAN_YEAR_DISPLAY] = @ParAll != nil ? @ParAll[Cfate::PAR_PAN_YEAR_DISPLAY] : Cfate::PAN_DISPLAY_WEST

    hApFunc = @ApFunc.clone
    # hApFunc[Cfate::LAST_XDATE_PAN_TYPE] = Cfate::PAN_FLOWMONTH

    @hXdate = Xdate.MakeXdateInfo_Wannianli(hAppData,@m_nPanType,hApFunc,hParAll)
    @firstShowAdvertise = 'pan_xdate_block_ifate'

    # @Ui_EY = oStar.uig_EY()
    # @nSky = oStar.gNormal_GetSky()
    # @nEarth = oStar.gNormal_GetEarth()
    # @CurEYear = oStar.cdg_EY()
    # @CurEMonth = oStar.cdg_EM()
    # @CurEDate = oStar.cdg_ED()
    # @CurELeap = oStar.cdg_EL()
    # @CurETime = oStar.cdg_ET()
    # @CurEMin = oStar.cdg_EMI()
    # @CurWMin  = oStar.cdg_WMI()
    # @FlowEYear = oStar.fdg_EY(nPanType)
    # @FlowEMonth = oStar.fdg_EM(nPanType)
    # @FlowEDate = oStar.fdg_ED(nPanType)
    # @FlowELeap = oStar.fdg_EL(nPanType)
    # @FlowETime = oStar.fdg_ET(nPanType)
    # @FlowWHour = oStar.fdg_WH(nPanType)
    # @FlowEMin = oStar.fdg_EMI(nPanType)
    remove_data_for_free_user
  end
  # 免費客戶之限制
  def remove_data_for_free_user
    if (@oUserAp == nil || !@oUserAp.can_use_pars) then
      # 十年
      if (@hXdate["ShowXdate"].include?(Cfate::PAN_TENYEAR)) then
        # 十年之太歲
        @hXdate["pan_xdate_tenyear"].each_with_index do |d,nDataIndex|
          @hXdate["pan_xdate_tenyear"][nDataIndex][4] = ""
        end
      end
      # 流年
      if (@hXdate["ShowXdate"].include?(Cfate::PAN_FLOWYEAR)) then
        # 流年之表頭之太歲
        @hXdate["pan_xdate_flowyear_title"].each_with_index do |d,nIndex|
          @hXdate["pan_xdate_flowyear_title"][nIndex][5] = ""
        end
        # 流年之 臘月 及 月煞
        @hXdate["pan_xdate_flowyear_data"].each_with_index do |d,nIndex|
          @hXdate["pan_xdate_flowyear_data"][nIndex].each_with_index do |e,nDataIndex|
            @hXdate["pan_xdate_flowyear_data"][nIndex][nDataIndex][6] = ""
            @hXdate["pan_xdate_flowyear_data"][nIndex][nDataIndex][7] = ""
          end
        end
      end
      # 萬年曆 流月之奇門局不顯示
      if (@hXdate["ShowXdate"].include?(Cfate::PAN_FLOWMONTH)) then
        # 萬年曆 流月之奇門局不顯示
        @hXdate["pan_xdate_flowmonth_data"].each_with_index do |d,nDataIndex|
          @hXdate["pan_xdate_flowmonth_data"][nDataIndex][5] = ""
        end
      end
      # 時局不顯示
      @shijus = []
    end

  end

  def pan_userap_info()
    oProduct = g_getProductByUserAp(@oUserAp)
    @buy_info = ""
    @ap_version_desc = oProduct.product_description if oProduct != nil
    if (oProduct.demo) then
      @ap_version_desc += "(#{Pm.GetStrWithColon("IDS_S_UNTILL")}#{@oUserAp.end_date.strftime("%Y-%m-%d")})"
    else
      if (oProduct.free) then
        if (g_isDemoExist(@user_id,@ap_name)) then
          @buy_info = "buy"
        else
          @buy_info = "demo"
        end
        @ap_version_desc = Pm.GetStr("IDS_W_APP_PAN_NOT_BUIED")
      else
        if ((@oUserAp.end_date < Xdate.GetNowRails() + 14.days) && getUserApCounts(@user_id,@ap_name) < 2) then
          @buy_info = "rebuy"
        end
        @ap_version_desc = Pm.GetStr("IDS_W_APP_PAN_BUIED")
      end
    end
      if (@customer_user_id == 0) then
        @ap_version_desc = Pm.GetStr("IDS_S_ALL_SHOW")
      end
  end
  def panPar2Db(hParAll)
    panParType = IfatePar.check_userid_wannianli(@user_id).first
    if (panParType == nil) then
      panParType = IfatePar.new
      panParType.user_id = @user_id
      panParType.ap_name = "wannianli"
    end
    panParType.hPars = hParAll
    panParType.save!
  end
  def panparcopy_action(hPars)
    email = hPars["par_copy_email"]
    oUser = User.find_by_email(email)
    if (oUser == nil) then
      redirect_to :action => "panparedit", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL"), :PanPar => Wannianli::PanPar_Copy
    else
      oIfatePar = IfatePar.check_userid_wannianli(oUser.id).last
      if (oIfatePar == nil) then
        redirect_to :action => "panparedit", :action_result => Pm.GetStr("IDS_PAR_SETUP_FAIL"), :PanPar => Wannianli::PanPar_Copy
      else
        oIfateParMe = IfatePar.check_userid_wannianli(@user_id).last
        if (oIfateParMe == nil) then
          oIfateParMe = IfatePar.new
          oIfateParMe.user_id = @user_id
          oIfateParMe.ap_name = "wannianli"
        end
        hUserType = oIfatePar.hPars
        oIfateParMe.hPars = hUserType
        oIfateParMe.save!
      end
    end
  end

  public
  def fuwu_shuoming
    render :layout => 'ifate'
  end
end
