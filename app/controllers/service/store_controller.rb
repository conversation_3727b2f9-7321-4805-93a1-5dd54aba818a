
# 在Controller的每個action中你都可以使用下面變量:

# action_name：當前action的名字
# cookies：查看和設置瀏覽器cookie
# headers：response將要使用的HTTP頭信息組成的一個hash，一般設置Content-Type時才用
# params：這個很常用，請求參數組成的一個hash，params[:id]與params['id']相等。
# request：收到的請求，包含以下屬性：
#   request_method：返回請求方法，有:delete,:get,:head,:post,:put
#   method：與request_method相同，除了:head會返回:get。因為這兩個從程序角度來看功能是一樣的。
#   delete?,get?,head?,post?,put?：判斷請求方法返回true或false
#   xml_http_request?和xhr?：ajax請求返回true，否則返回false。注意它和method參數是無關的。
#   url：返回request的完整URL
#   remote_ip：返回遠程IP地址。如果客戶端使用proxy可能返回多於一個地址。
#   headers：請求的http headers
#   body：請求的body構成的I/O流
# response：響應對象
# session：session組成的hash
require 'net/http'
require 'uri'
require("Pm.rb")
require("Cfate.rb")
require("Star.rb")
require("Eightword.rb")
require("Xdate.rb")
require("Xuexi_Ziwei.rb")

class Service::StoreController < ApplicationController
  before_action :authenticate_user!, :except => [:buy_products_complete,:buy_products_complete_forever,:free,:star,:eightword,:xdate,:show]
  before_action :authenticate, :except => [:buy_products_complete,:buy_products_complete_forever,:free,:star,:eightword,:xdate,:show]

  # @@Web_Server = "peter.pro58"  # "me.profate"
  # @@BillingServer = "http://billing2.profate.com.tw"  # test server
  # @@BillingServer = "http://billing.profate.com.tw"  # production server

  protected
  def authenticate
      authenticate2(params["ap_name"])
  end
  def authenticate2(ap_name)
    #@user_name = current_user.name
    @user_name = current_user.email if (current_user != nil)
    @user_level = ""

    session["user_id"] = current_user.id if (current_user != nil)
    @user_level = ""

    @ap_name = g_user_ap_name(ap_name,session["user_id"])

    session["userlevel"] = @user_level
    @oUserAp = g_getUserCurrentAp(session["user_id"],@ap_name)
    # @oUserAps = g_getUserCurrentAps(session["user_id"],@ap_name)
    @canUsePan = g_canUsePan(session["customer_id"])

    session["user_name"] = { :value => @user_name, :expires => 1.minute.from_now }
    @ParAll = g_get_pan_pars(@ap_name,session["user_id"])
    star_version_check()

    @controller_name = "store"
    @canAddPe = session["customer_id"] != nil

    @admin = current_user.roles? :admin
    @can_add_customer,@customer_max_count = g_can_add_customer(session["user_id"],@admin)

    @client_ip = remote_ip()
  end

  def star_version_check()
    # 權限控管
    @ApFunc = g_ap_product_allfunc_hash(@oUserAp)
    @hCanDeal = canDealCheck(session["user_id"],@oUserAp,@ap_name)
  end

  public
  def show
    @sign_in = false
    if (session["user_id"] != nil) then  #if logged_in?
      @sign_in = true
    end

    # @ap_name = params["ap_name"]
    # if (@ap_name == nil) then
      # render :layout => 'star_pan_simple' #'customer'
    # else
    #   if (session["user_id"] != nil) then
    #     authenticate_user!
    #     authenticate2(@ap_name)
    #     session["customer_id"] = getFullFuncShow_CustomerId()
    #       redirect_to :controller => "ifate/#{@ap_name}" ,:action => "pan" , :ap_name => @ap_name
    #   else
    #     authenticate2(@ap_name)
    #     session["customer_id"] = getFullFuncShow_CustomerId()
    #       redirect_to eval("show_#{@ap_name}_path()") , :ap_name => @ap_name
    #     end
   #    end
  end

  def demo_apply
    @userap_id = @oUserAp.id
    @product = g_getDemoProduct(@ap_name)
    if (@product != nil) then
      @product_additional = nil

      render :layout => 'ifate'
    else
      redirect_to :action => "buied_products", :ap_name => @ap_name
    end
  end
  def demo_apply_complete
    @product = g_getDemoProduct(@ap_name)
    if (@product != nil) then
        @userap_id = params["userap_id"].to_i
      g_createDemoUserAp(session["user_id"],@ap_name)
      if (@userap_id > 0) then
        oUserAp = UserAp.find_by_id(@userap_id)
        if (!oUserAp.isAvailable) then
          oUserAp.status = -1
          oUserAp.save!
        end
      end
      redirect_to :action => "buied_products", :ap_name => @ap_name
    end
  end

  def management
    # saveTestDb("service:management",Cfate.pan_par_hash2db(@hCanDeal))
    # if (@hCanDeal["DEMO"]) then
    #   redirect_to :action => "demo_apply", :ap_name => @ap_name
    # elsif (@hCanDeal["BUY"]) then
    #   redirect_to :action => "buy_products", :ap_name => @ap_name, :buy_type => "BUY"
    # elsif (@hCanDeal["REBUY"]) then
    #   redirect_to :action => "buy_products", :ap_name => @ap_name, :buy_type => "REBUY"
    # else
      redirect_to :action => "buied_products", :ap_name => @ap_name
      # render :layout => 'ifate'
    # end
  end

  protected
  def check_agent_code(agent_code)
    if (agent_code == nil) then
      return "PZN04"
    end
    a = Product.select(:product_version).check_main.check_name(@ap_name).check_code(agent_code).distinct

    if (a == nil || a.length == 0) then
      return "PZN04"
    else
      return agent_code
    end
  end

    public
  # 購買產品
    def buy_products
      @agent_code = check_agent_code(params["agent_code"])
      @buy_type = params["buy_type"]
      @ws_dates = getWsDates(params["ws_dates"],@buy_type,@oUserAp,@agent_code)
      @int_ws_dates = dateStrToIntDates(@ws_dates)

      if (@hCanDeal[@buy_type]) then
        buy_products_data
        if (@products == nil && @product_additional == nil) then
          redirect_to :back, :ap_name => @ap_name
        else
        render :layout => 'ifate'
      end
      else
        redirect_to :back, :ap_name => @ap_name
      end
    end

  def buy_products_ajax
    dataIn = params["ajax_data_key"]
    hData = Cfate.pan_par_dbfield2hash(dataIn)
    @buy_type = hData["buy_type"]
    @ws_dates = hData["ws_dates"]
    @ap_name = hData["ap_name"]
    @agent_code = check_agent_code(hData["agent_code"])

    buy_products_data
    render :partial => "products_content", :locals => { :buy_type => @buy_type, :ws_dates => @ws_dates, :product => @product, :apFunc => @ApFunc, :ap_version => @ap_version, :product_additional => @product_additional, :aBuiedProductId => @aBuiedProductId, :ws_dates_buied => @ws_dates_buied, :aAllWsDates => @aAllWsDates }
  end

  protected
  def buy_products_data()
    # BUY 新購買 : 新買一段時間，原免費取消
    # BUY 新購買附件 : 在原購買時間內加附件，需退未使用時間之費用
    # REBUY 續約 : 新買一段時間
    if (@hCanDeal[@buy_type]) then
      @userap_id = @oUserAp.id
      @ap_version = @oUserAp.product_version
      getProducts(@buy_type,@ap_name,@oUserAp,@ws_dates,@agent_code)
      getProduct_additional(@buy_type,@ap_name,@oUserAp,@ws_dates)
      # if (@hCanDeal["REBUY"]) then
      #   @ws_dates_buied = getBuiedWsDates("",@userap_id,@agent_code)
      # end
      @user_id = session["user_id"]
      getBuiedProducts(@user_id)
      @AllAps = g_getMainApNames()
      if (!@AllAps.include?(@ap_name)) then
        @ap_name = @AllAps[0]
      end
      @aAllWsDates = getAllWsDates(@ap_name,@agent_code)
      @comment = Pm.getSystemValue("discount_comment")

      @user_main_aps = merge_main_aps(@user_id,@ap_name)
    end
  end
  def merge_main_aps(user_id,ap_name)
    main_aps = g_getUserMainAps(user_id,ap_name)
    h = {}
    # product_names = g_getProductMainNames(ap_name)
    # no need all product_versions in products
    product_names = Array.new
    main_aps.each_with_index do |ap,i|
      if (h["#{ap.product_name}_#{ap.product_version}"] == nil) then
        h["#{ap.product_name}_#{ap.product_version}"] = ap.end_date + dateStrToIntDates("#{ap.gift_days} days")
      else
        h["#{ap.product_name}_#{ap.product_version}"] = h["#{ap.product_name}_#{ap.product_version}"] >= ap.end_date + dateStrToIntDates("#{ap.gift_days} days") ? h["#{ap.product_name}_#{ap.product_version}"] : ap.end_date + dateStrToIntDates("#{ap.gift_days} days")
      end
      pnames = check_main_in_aps(product_names,ap)
      pnames.each do |pname|
        if (h["#{ap.product_name}_#{pname}"] == nil) then
          h["#{ap.product_name}_#{pname}"] = ap.end_date + dateStrToIntDates("#{ap.gift_days} days")
        else
          h["#{ap.product_name}_#{pname}"] = h["#{ap.product_name}_#{pname}"] >= ap.end_date + dateStrToIntDates("#{ap.gift_days} days") ? h["#{ap.product_name}_#{pname}"] : ap.end_date + dateStrToIntDates("#{ap.gift_days} days")
        end
      end      
    end

    return h
  end
  def check_main_in_aps(product_names,user_ap)
    ap_func = getUserApFuncPar(user_ap)          
    h2 = Cfate.pan_par_dbfield2hash(user_ap.ap_additionalfunc)
    ap_func.merge!(h2) { |key, v1, v2| (v1 >= v2) ? v1 : v2 }
    keys = ap_func.keys
    return product_names & keys
  end
  def checkWsDates(ws_dates,ap_name,agent_code)
    a = getAllWsDates(ap_name,agent_code)
    if (ws_dates == nil) then
      return a[0]
    end
    if (a.include?(ws_dates)) then
      return ws_dates
    end
    return a[0]
  end

  def getWsDates(ws_dates,buy_type,oUserAp,agent_code)
    # if ((buy_type == "BUY") && (oUserAp.main || oUserAp.forever)) then
    #   return getBuiedWsDates("",oUserAp.id,agent_code)
    # end
    return checkWsDates(ws_dates,oUserAp.product_name,agent_code)
  end

  def getAllWsDates(ap_name,agent_code="PZN04")
    # a = Product.select(:product_content).check_main.check_name(ap_name).check_code(agent_code).order("original_amount ASC").distinct
    a = Product.select([:product_content,:original_amount]).check_main.check_name(ap_name).check_code(agent_code).order("original_amount ASC").distinct

    b = Array.new
    a.each do |p|
      if (!b.include?(p.product_content)) then
        b.push(p.product_content)
      end
    end
    return b
  end

  def getProducts_old(buy_type,ap_name,oUserAp,ws_dates,agent_code)
    oProduct = g_getProductByUserAp(oUserAp)
    case (buy_type)
    when ("BUY") then
      if (!oUserAp.main) then
        # 原先是免費或試用的用戶
          @product = Product.check_main.check_ws_dates(ws_dates).check_name(ap_name).check_code(agent_code).check_amount_great(0).order("product_version ASC").last
      else
          # 已購買了主產品，或為試用版本
          # @product = Product.check_main.check_ws_dates(ws_dates).excludeId(oUserAp.product_id).check_name(ap_name).order("product_version ASC").last
          @product = nil
      end
    when ("REBUY") then
        @product = Product.check_main.check_ws_dates(ws_dates).check_name(ap_name).check_code(agent_code).check_amount_great(0).order("product_version ASC").last
    else
      @product = nil #Product.check_name(ap_name).check_amount_great(0).order("product_version ASC").last
    end
    if (@product != nil) then
      sInfo = getAgentInfo(@product)
      if (sInfo.length > 0) then
        @product.product_description += "(#{sInfo})"
      end
    end
  end
  # #42 
  def getProducts(buy_type,ap_name,oUserAp,ws_dates,agent_code)
    @products = Product.check_main.check_ws_dates(ws_dates).check_name(ap_name).check_amount_great(0).order("product_version ASC")
  end

  def getAllAdditional(ws_dates,ap_name)
    a = Product.select(:product_version).check_additional.check_ws_dates(ws_dates).check_name(ap_name).distinct
    b = Array.new
    a.each do |p|
      b.push(p.product_version)
    end
    return b
  end

  def checkAdditionInProduct(all_additional,hApFunc)
    hAddProd = Hash.new
    all_additional.each do |additional|
      if (hApFunc[additional]) then
        hAddProd[additional] = true
      end
    end

    return hAddProd
  end
  def getProduct_additional_old(buy_type,ap_name,oUserAp,ws_dates)
    all_additional = getAllAdditional(ws_dates,ap_name)
    case (buy_type)
      when ("BUY") then
        if (!oUserAp.main) then
          hApFunc = g_product_par_hash(@product)
          hAddProd = checkAdditionInProduct(all_additional,hApFunc)
          if (hAddProd.length == 0) then
            @product_additional = Product.check_additional.check_ws_dates(ws_dates).check_name(ap_name).all
          else
            @product_additional = Product.check_additional.check_ws_dates(ws_dates).excludeVersion(hAddProd.keys).check_name(ap_name).all
          end
        else
          # hAddProd = g_ap_product_addfunc_hash(oUserAp)
          hApFunc = g_ap_product_allfunc_hash(oUserAp)
          hAddProd = checkAdditionInProduct(all_additional,hApFunc)
          if (hAddProd.length == 0) then
            @product_additional = Product.check_additional.check_ws_dates(ws_dates).check_name(ap_name).all
          else
            @product_additional = Product.check_additional.check_ws_dates(ws_dates).excludeVersion(hAddProd.keys).check_name(ap_name).all
          end
        end
      when ("REBUY") then
        hApFunc = g_product_par_hash(@product)
        hAddProd = checkAdditionInProduct(all_additional,hApFunc)
        if (hAddProd.length == 0) then
          @product_additional = Product.check_additional.check_ws_dates(ws_dates).check_name(ap_name).all
        else
          @product_additional = Product.check_additional.check_ws_dates(ws_dates).excludeVersion(hAddProd.keys).check_name(ap_name).all
        end
      else
        @product_additional = nil
    end
  end
  def getProduct_additional(buy_type,ap_name,oUserAp,ws_dates)
    return nil
  end
  public
  def buy_products_confirm
    # 取得輸入的資訊
    @agent_code = check_agent_code(params["agent_code"])
    @buy_type = params["buy_type"]
    @product_ids = params["product_ids"]
    @userap_id = params["userap_id"]
    @ws_dates = getBuiedWsDates(params["ws_dates"],@userap_id,@agent_code)
    aProdAdd_Buied = nil

    if (@product_ids == nil || @product_ids == []) then
      redirect_to :back, :ap_name => @ap_name
    else
      # 計算總價，包含主約及附加功能
      hAmount,hAmountDetails = bpc_compute_amount(@buy_type,@product_ids,@ws_dates,@userap_id,aProdAdd_Buied)
    # 儲存到Orderme中
      nOrdermeId = saveOrdermes(@product_ids,@ws_dates,@buy_type,hAmount,hAmountDetails,@userap_id,aProdAdd_Buied)
      # 準備送到billing
      @orderno = nOrdermeId
      @paytitle = getBillingPayTitle(@buy_type,@product_ids,@ws_dates,aProdAdd_Buied,@userap_id)
      @paymemo = getBillingPayMemo(@buy_type,@product_ids,@ws_dates,aProdAdd_Buied,hAmount["paid_amount"],hAmount["refund_amount"],nOrdermeId)
      @sHttp = Pm.getBillingServer("/dispatchpaymentconfirm")
      # @sHttp = "dispatchpaymentconfirm"
      @original_amount = hAmount["original_amount"]
      @discount_amount = hAmount["discount_amount"]
      @paid_amount = getPaidAmount(hAmount["paid_amount"],hAmount["refund_amount"])
      oUser = User.find_by_id(session["user_id"])
      @payname = oUser.name
      @payphone = ""
      @duedate = ""
      @user_id = session["user_id"]
      @server_name = getWebServer()
      @feedback_url = Pm.getHostUrl("/service/store/buy_products_complete")
      @receive_url = Pm.getHostUrl("/ifate/customer/index?ap_name=#{@ap_name}")

      if (@paid_amount == 0) then
        # redirect_to :controller => "service/store" ,:action => "buy_products_complete" , :rc => 0, :order_id => 0, :orderme_id => nOrdermeId
     #        url = URI.parse(@feedback_url)
     #          res = Net::HTTP.post_form(url,{:rc => 0, :orderme_id => nOrdermeId, :order_id => 0 })
        # redirect_to @receive_url
      # else
      #   render :layout => 'ifate'
        @sHttp = @feedback_url
        @rc = 0
        @orderme_id = nOrdermeId
        @order_id = 0
      end
      render :layout => 'ifate'
    end
  end

  public
  def buy_products_complete
    rc = params["rc"].to_i
    order_id = params["order_id"].to_i
    orderme_id = params["orderme_id"].to_i
    rc1 = -1

    # saveTestDb("#{orderme_id}buy_products_complete",Cfate.pan_par_hash2db(params.to_hash))
    if (orderme_id > 0) then
      oOrderme = Orderme.find_by_id(orderme_id)
      oPayment = Payment.check_orderid(order_id).last
      # if (oOrderme != nil) then
      #   if (rc == 0 && (oPayment != nil || (oOrderme.paid_amount <= oOrderme.refund_amount))) then
      #     buy_products_complete_db_action(oOrderme,orderme_id,order_id,rc)
      #   else
      #     buy_products_complete_db_action(oOrderme,orderme_id,order_id,-2)
      #   end
      # end
      rc1 = buy_products_complete_succeed_check(oPayment,oOrderme,rc)
      buy_products_complete_db_action(oOrderme,orderme_id,order_id,rc1)
    end
    if (oOrderme != nil && oOrderme.paid_amount <= oOrderme.refund_amount) then
      # 免費或者退款多於付款
      receive_url = params["receive_url"]

      redirect_to URI.parse(receive_url).path 
      # redirect_to receive_url
    else
      # render :layout => false
      if (rc1 == 0) then
        render :text => "1|OK"
      else
        render :text => "0|error"
      end
    end
  end
  # 2016/1/11
  def buy_products_complete_succeed_check(oPayment,oOrderme,rc)
    if (oOrderme == nil) then
      return -1 # 沒有訂單
    end
    if (rc != 0) then
      return -2 # 付款失敗
    end
    if (oPayment == nil) then
      # 退貨大於付款，就不必付款，直接可以使用
      if (oOrderme.paid_amount <= oOrderme.refund_amount) then
        return rc
      else
        return -3 # 該付款卻沒付款
      end
    end
    if (oPayment.paid_amount == (oOrderme.paid_amount - oOrderme.refund_amount)) then
      return rc
    else
      return -4 # 偷改了付款金額，不予使用
    end
    return -5
  end

    protected
    def getPaidAmount(paid_amount,refund_amount)
      if (paid_amount <= refund_amount) then
        return 0
      end
      return paid_amount - refund_amount
    end

    def getAgentInfos(product_ids)
      sOut = ""
      product_ids.each do |product_id|
        oProduct = g_getProduct(product_id)
        s = getAgentInfo(oProduct)
        if (s.length > 0) then
          if (sOut == "") then
            sOut = s
          else
            sOut += ",#{s}"
          end
        end
      end
      return sOut
    end
    def getAgentInfo(oProduct)
      if (oProduct == nil) then
        return ""
      end
      all_additional = getAllAdditional(oProduct.product_content,oProduct.product_name)
      hApFunc = g_product_par_hash(oProduct)
      hAddProd = checkAdditionInProduct(all_additional,hApFunc)

      if (hAddProd.keys.length == 0 && (hApFunc["n_gift_days"] == nil || hApFunc["n_gift_days"] == 0)) then
        return ""
      end

      sOut = Pm.GetStrWithColon("ifate_store_agent_title")

      a = Array.new
      hAddProd.each_pair { |key, value|
        product = Product.check_additional.check_name(oProduct.product_name).check_version(key).check_ws_dates(oProduct.product_content).last
        if (product != nil) then
          a.push(getProductDesc(product.id,product.product_content))
        end
      }
      if (a.length > 0) then
        sOut += a.join(",")
      end

      if (hApFunc["n_gift_days"] != nil && hApFunc["n_gift_days"] > 0) then
        if (a.length > 0) then
          sOut += ","
        end
        sOut += getAgentGiftsDaysDesc(hApFunc["n_gift_days"])
      end

      return sOut
    end
    def getAgentGiftsDaysDesc(gift_days)
      if (gift_days == nil) then
        return ""
      end
      return "#{Pm.GetStr('ifate_store_agent_gift_day_pre')}#{gift_days}#{Pm.GetStr('ifate_store_agent_day')}"
    end

    def getBillingPayTitle(buy_type,product_ids,ws_dates,aProdAdd_Buied,userap_id)
      sInfo = getAgentInfos(product_ids)
      sInfo = "," + sInfo if sInfo.length > 0

      sTitle = ""
      case (buy_type)
      when ("FOREVER") then
          sTitle += getProductsDesc(product_ids,ws_dates)
      when ("BUY") then
        sTitle += getProductsDesc(product_ids,ws_dates)
        bNeedInclude = true
        sTitle += getAddProductDesc(aProdAdd_Buied,ws_dates,bNeedInclude,nil)
        sTitle += sInfo
      when ("REBUY") then
        sTitle +=  getProductsDesc(product_ids,ws_dates)
        bNeedInclude = true
        sTitle += getAddProductDesc(aProdAdd_Buied,ws_dates,bNeedInclude,nil)
        sTitle += sInfo
      when ("BUY_WEB_ADD") then
        bNeedInclude = false
        sTitle += getAddProductDesc(aProdAdd_Buied,ws_dates,bNeedInclude,userap_id)
      else
          return ""
      end
      return sTitle
    end

    def getProductTitle(product_id,ws_dates)
      return getProductDesc(product_id,ws_dates)
    end

    def getProductsDesc(product_ids,ws_dates)
      s = ""
      product_ids.each do |product_id|
        s += getProductDesc(product_id,ws_dates) + ","
      end
      return s[0,s.length - 1]
    end
    def getProductDesc(product_id,ws_dates)
      oProduct = g_getProduct(product_id)
      return "#{oProduct.product_description}(#{makeRentDates(ws_dates)})"
    end
    def getProductsName(product_ids)
      a = []
      product_ids.each do |product_id|
        a.push(getProductName(product_id))
      end
      return a.join(",")
    end
    def getProductName(product_id)
      oProduct = g_getProduct(product_id)
      return "#{oProduct.product_name})"
    end

  def makeRentDates(key)
    # a = key.split(/\s*.\s*/)
    # a2 = a[1].split(//,2)
    # a = key.split(//,2)
    a = key.split
    s = a.join(".")
    # s = "#{a[0]}.#{a[1]}"
    sf = getWsDates_forever("star")

    if (sf == nil) then
      sOut = "#{a[0]}#{getDateStr(a[1])}"
    else
      sf = sf.split.join(".")
      if (eval(s) >= eval(sf)) then
        sOut = Pm.GetStr("IDS_S_APP_FOREVER")
      else
        sOut = "#{a[0]}#{getDateStr(a[1])}"
      end
    end
    return sOut
  end

  def getDateStr(sDate)
    if (sDate.index("year") != nil) then
      return Pm.GetStr("IDS_X_YEAR")
    elsif (sDate.index("month") != nil) then
      return Pm.GetStr("IDS_S_MONTH_UNIT") + Pm.GetStr("IDS_X_MONTH")
    elsif (sDate.index("day") != nil) then
      return Pm.GetStr("IDS_S_DAY")
    end
    return ""
  end

    def getAddProductDesc(aProdAdd_Buied,ws_dates,bNeedInclude,userap_id)
      sOut = ""
      if (aProdAdd_Buied == nil) then
        return sOut
      end

      if (bNeedInclude && (aProdAdd_Buied.length > 0)) then
        sOut = Pm.GetStrWithColon("IDS_X_INCLUDE")
      end
      a = Array.new
      aProdAdd_Buied.each do |product_id|
        a.push(getProductDesc(product_id,ws_dates))
      end
      sOut += a.join(",")

      sOut += getAddNotUsedDaysDesc(userap_id)

      return sOut
    end

    def getAddNotUsedDaysDesc(userap_id)
      nNoUsedDays = getNoUsedDays(userap_id)
    if (nNoUsedDays > 0) then
      return "#{Pm.GetStrWithColon("IDS_STORE_PAYMNET_NOUSED")}#{nNoUsedDays}#{getDateStr("day")}#{Pm.GetStr("IDS_STORE_PAYMNET_NEEDREFUND")}"
    else
      return ""
    end
    end

    def getBillingPayMemo(buy_type,oProduct,ws_dates,aProdAdd_Buied,paid_amount,refund_amount,nOrdermeId)
      return "" #Pm.GetStr("IDS_S_APP_#{buy_type}")
    end

    def bpc_compute_amount(buy_type,product_ids,ws_dates,userap_id,aProdAdd_Buied=nil)
    case (buy_type)
      when ("BUY") then
          hAmount,hAmountDetails = bpc_compute_amount_buy(product_ids,ws_dates,userap_id,aProdAdd_Buied)
      when ("REBUY") then
          hAmount,hAmountDetails = bpc_compute_amount_rebuy(product_ids,ws_dates,aProdAdd_Buied)
      else
        hAmount = bpc_make_amount_hash(0,0,0,0)
        hAmountDetails = Hash.new
    end
      return hAmount,hAmountDetails
  end

  def bpc_make_amount_hash(original_amount,discount_amount,paid_amount,refund_amount)
    hOut = Hash.new
    hOut["original_amount"] = original_amount
    hOut["discount_amount"] = discount_amount
    hOut["paid_amount"] = paid_amount
    hOut["refund_amount"] = refund_amount
    return hOut
  end

  # 購買的價格計算原則
  # 包括主產品，附加功能及租賃期限 ==> 決定實際價格
  # 沒有已購買需退回的部分
  # 根據租賃期限決定折扣
  # original_amount : 原始價格,discount_amount : 優惠價格
  # paid_amount : 應付價格,refund_amount : 當次交易需退回金額
  def bpc_compute_amount_buy(product_ids,ws_dates,userap_id,aProdAdd_Buied=nil)
      original_amount,discount_amount,paid_amount = computeProductsAmount(product_ids,ws_dates,aProdAdd_Buied)
      refund_amount = 0

      hAmount = bpc_make_amount_hash(original_amount,discount_amount,paid_amount,refund_amount)
      hAmountDetails = computeProductsAmountDetails(product_ids,ws_dates,aProdAdd_Buied)
      return hAmount,hAmountDetails
  end
  def bpc_compute_amount_rebuy(product_ids,ws_dates,aProdAdd_Buied=nil)
      original_amount,discount_amount,paid_amount = computeProductsAmount(product_ids,ws_dates,aProdAdd_Buied)
      hAmount = bpc_make_amount_hash(original_amount,discount_amount,paid_amount,0)
      hAmountDetails = computeProductsAmountDetails(product_ids,ws_dates,aProdAdd_Buied)
      return hAmount,hAmountDetails
  end

  def bpc_compute_amount_buywebadd(ws_dates,userap_id,aProdAdd_Buied)
    # 取得附加功能未使用到時間而退費的金額，因買附加功能不會更改購買時間，所以已使用過之時間耍扣除
    return getAddsAllAmountWithRefund(ws_dates,userap_id,aProdAdd_Buied)
  end

  # 計算升級時所需退還的費用
  def getAddsAllAmountWithRefund(ws_dates,userap_id,aProdAdd_Buied)
    # 計算應付金額
      hAmountDetails = Hash.new
    original_amount_sum = discount_amount_sum = paid_amount_sum = refund_amount_sum = 0
    aProdAdd_Buied.each do |product_id|
      original_amount,discount_amount,paid_amount,refund_amount = getAddAllAmountWithRefund(ws_dates,userap_id,product_id)
      original_amount_sum += original_amount
      discount_amount_sum += discount_amount
      paid_amount_sum += paid_amount
      refund_amount_sum += refund_amount
        hAmountDetails[getProductIdKey(product_id)] = bpc_make_amount_hash(original_amount,discount_amount,paid_amount,refund_amount)
    end
      hAmount = bpc_make_amount_hash(original_amount_sum,discount_amount_sum,paid_amount_sum,refund_amount_sum)

    return hAmount,hAmountDetails
  end

  def getAddAllAmountWithRefund(ws_dates,userap_id,product_id)
    # 計算應付金額
    original_amount,discount_amount,paid_amount = getProductAmountWithPaid(product_id,ws_dates)

    # 依據租賃契約取得已使用天數
    nNoUsedDays = getNoUsedDays(userap_id)

    # 取得訂單的購買期限
    howlong = getRentDates(ws_dates)

    # 根據實付金額計算該退還的金額
    refund_amount = computeRefundAmount(paid_amount,nNoUsedDays,howlong)

    return original_amount,discount_amount,paid_amount,refund_amount
  end

  def getNoUsedDays(userap_id)
    if (userap_id == nil) then
      return 0
    end

    # 取得客戶原購買的AP
    oUserAp = UserAp.find_by_id(userap_id)
    oRent = Rent.find_by_id(oUserAp.rent_id)

    if (oRent == nil) then
      if (oUserAp.main) then
        if (oUserAp.end_date <= Xdate.GetNowRails()) then
          return 0
        end
        # 使用天數/366*金額
        nNoUsedDays = overDays(oUserAp.start_date,Xdate.GetNowRails())

        return nNoUsedDays.to_i
      else
        return 0
      end
    end
    if (oRent.end_date <= Xdate.GetNowRails()) then
      return 0
    end
    # 使用天數/366*金額
    nNoUsedDays = overDays(oRent.start_date,Xdate.GetNowRails())

    return nNoUsedDays.to_i
  end

  def computeRefundAmount(paid_amount,nNoUsedDays,howlong)
    if (nNoUsedDays == -1) then
      return 0
    end
    # 使用天數 / 購買期限 * 金額 = 已使用額度
    noused_amount = ((nNoUsedDays * paid_amount).day / howlong)
    refund_amount = noused_amount.to_i
    if (refund_amount > paid_amount) then
      refund_amount = paid_amount
    end
    return refund_amount
  end

  def getProductAmountWithPaid(product_id,ws_dates)
    original_amount,discount_amount = getProductAmount(product_id,ws_dates)
    # 取得折扣數
    discount_rate = getDiscountRate(ws_dates)
    # 計算折扣後金額
    paid_amount = discount_amount * discount_rate / 100

    return original_amount,discount_amount,paid_amount
  end

  def computeProductsAmount(product_ids,ws_dates,aProdAdd_Buied=nil)
    # 取得產品價格
    original_amount,discount_amount = getProductsAmount(product_ids,ws_dates)
    # 取得附加功能總價
    original_amount_add,discount_amount_add = getProductAddAmount(aProdAdd_Buied,ws_dates)
    # 算出總價
    original_amount_sum = original_amount + original_amount_add
    discount_amount_sum = discount_amount + discount_amount_add
    # 取得折扣數
    discount_rate = getDiscountRate(ws_dates)
    # 計算折扣後金額
    paid_amount = discount_amount_sum * discount_rate / 100

    return original_amount_sum,discount_amount_sum,paid_amount
  end

  def computeProductsAmountDetails(product_ids,ws_dates,aProdAdd_Buied=nil)
    hAmountDetails = Hash.new
    # 取得主產品價格
    product_ids.each do |product_id|
      original_amount,discount_amount,paid_amount = getProductAmountWithPaid(product_id,ws_dates)
      hAmountDetails[getProductIdKey(product_id)] = bpc_make_amount_hash(original_amount,discount_amount,paid_amount,0)
    end

    # 取得附加功能價格
    if (aProdAdd_Buied != nil) then
      aProdAdd_Buied.each do |pid|
        original_amount,discount_amount,paid_amount = getProductAmountWithPaid(pid,ws_dates)
        hAmountDetails[getProductIdKey(pid)] = bpc_make_amount_hash(original_amount,discount_amount,paid_amount,0)
      end
    end

    return hAmountDetails
  end

  def getProductIdKey(product_id)
    return "product_id_#{product_id}"
  end
  def getProductsAmount(product_ids,ws_dates)
    original_amount,discount_amount = 0,0
    product_ids.each do |product_id|
      o,d = getProductAmount(product_id,ws_dates)
      original_amount += o
      discount_amount += d
    end
    return original_amount,discount_amount
  end
  def getProductAmount(product_id,ws_dates)
    oProduct = g_getProduct(product_id)
    if (oProduct != nil) then
      product_dates = getRentDates(oProduct.product_content)
      rent_dates = getRentDates(ws_dates)
      power_rate = rent_dates / product_dates
      power_rate = power_rate.to_i
      original_amount = oProduct.original_amount * power_rate
      discount_amount = oProduct.discount_amount * power_rate
      return original_amount,discount_amount
    else
      return 0,0
    end
  end

  def getProductAddAmount(aProdAdd_Buied,ws_dates)
    if (aProdAdd_Buied == nil) then
      return 0,0
    end
    original_amount_add = discount_amount_add = 0
    aProdAdd_Buied.each do |product_id|
      original_amount,discount_amount = getProductAmount(product_id,ws_dates)
      original_amount_add += original_amount
      discount_amount_add += discount_amount
    end
    return original_amount_add,discount_amount_add
  end

  def getOrdermeFromUserAp(oUserAp)
    # 取得當初的租賃契約
    oRent = Rent.find_by_id(oUserAp.rent_id)
    # 取得當初租賃契約的訂單
    oOrderme = Orderme.find_by_id(oRent.orderme_id)
    # 取得訂單中的主版本
    oOrdermeDetailMain = OrdermeDetail.check_ordermeid(oOrderme.id).check_main.first

    # 取得訂單中的附加功能
    oOrdermeDetailAdd = getOrdermeDetailAdd(oUserAp.ap_additionalfunc)

    return oRent,oOrderme,oOrdermeDetailMain,oOrdermeDetailAdd
  end

  def getOrdermeDetailAdd(ap_additionalfunc)
    hOrgAddFunc = Cfate.pan_par_dbfield2hash(ap_additionalfunc)
    orderme_ids = Array.new
        hOrgAddFunc.each_pair { |key,value|
          if (key.index("_ordermedetailid") != nil) then
            orderme_ids.push(hOrgAddFunc[key])
          end
        }
        if (orderme_ids.length == 0) then
          orderme_details = nil
        else
      orderme_details = OrdermeDetail.check_ids(orderme_ids).all
    end
        return orderme_details
  end

  def getRemainDays(oRent)
    if (oRent == nil) then
      return 0
    end
    if (oRent.end_date <= Xdate.GetNowRails()) then
      return 0
    end
    # 使用天數/366*金額
    nRemainDays = (oRent.end_date - Xdate.GetNowRails()) / 86400

    return nRemainDays.to_i
  end

  def getDiscountRate(ws_dates)
    return 100
  end

  def getRentDates(ws_dates)
    # a = key.split(/\s*_\s*/)
    # sOut = ws_dates.split(//,2).join(".")
    sOut = ws_dates.split.join(".")
    return eval(sOut)
  end

    def saveOrdermes(product_ids,ws_dates,buy_type,hAmount,hAmountDetails,userap_id,aProdAdd_Buied)
      oProduct = g_getProduct(product_ids[0])
      agent_code = oProduct == nil ? nil : oProduct.product_version

      oOrderme = Orderme.new
      oOrderme.user_id = session["user_id"]
      oOrderme.product_id = product_ids[0]
      oOrderme.original_amount = getHashAmount(hAmount,"original_amount")
      oOrderme.discount_amount = getHashAmount(hAmount,"discount_amount")
      oOrderme.paid_amount = getHashAmount(hAmount,"paid_amount")
      oOrderme.refund_amount = getHashAmount(hAmount,"refund_amount")
      oOrderme.ws_dates = getBuiedWsDates(ws_dates,userap_id,agent_code)
      oOrderme.billing_order_id = nil
      oOrderme.buy_type = buy_type
      oOrderme.status = -1
      oOrderme.old_userap_id = userap_id
      oOrderme.save!
      nOrdermeId = oOrderme.id

      saveOrdermeDetails(nOrdermeId,product_ids,hAmountDetails)

      if (aProdAdd_Buied != nil) then
        aProdAdd_Buied.each do |pid|
          saveOrdermeDetail(nOrdermeId,pid,hAmountDetails)
        end
      end

      return nOrdermeId
    end

    def getBuiedWsDates(ws_dates,userap_id,agent_code)
      oUserAp = UserAp.find_by_id(userap_id)
      if (oUserAp == nil) then
        ap_name = @ap_name
      else
        ap_name = oUserAp.product_name
      end
      if (ws_dates != nil && ws_dates != "") then
        return checkWsDates(ws_dates,ap_name,agent_code)
      end

      if (oUserAp == nil) then
        return ""
      end

      if (oUserAp.product_id > 0 && oUserAp.main) then
        oProduct = g_getProduct(oUserAp.product_id)
        return oProduct.product_content
      else
        return ""
      end
    end

    def saveOrdermeDetails(nOrdermeId,product_ids,hAmountDetails)
      product_ids.each do |product_id|
        saveOrdermeDetail(nOrdermeId,product_id,hAmountDetails)
      end
    end
    def saveOrdermeDetail(nOrdermeId,product_id,hAmountDetails)
      oProduct = g_getProduct(product_id)

      if (oProduct != nil) then
        oOrdermeDetail = OrdermeDetail.new
        oOrdermeDetail.orderme_id = nOrdermeId
        oOrdermeDetail.product_id = product_id
        oOrdermeDetail.product_type = oProduct.product_type
        oOrdermeDetail.product_name = oProduct.product_name
        oOrdermeDetail.product_version = oProduct.product_version
        oOrdermeDetail.product_description = oProduct.product_description
        oOrdermeDetail.original_amount = getHashDetailAmount(hAmountDetails,product_id,"original_amount")
        oOrdermeDetail.discount_amount = getHashDetailAmount(hAmountDetails,product_id,"discount_amount")
        oOrdermeDetail.paid_amount = getHashDetailAmount(hAmountDetails,product_id,"paid_amount")
        oOrdermeDetail.refund_amount = getHashDetailAmount(hAmountDetails,product_id,"refund_amount")
        oOrdermeDetail.save!
      end
    end

    def getHashDetailAmount(hAmountDetails,product_id,amount_key)
      if (hAmountDetails == nil) then
        return 0
      end
      return getHashAmount(hAmountDetails[getProductIdKey(product_id)],amount_key)
    end

    def getHashAmount(hAmount,amount_key)
      if (hAmount == nil) then
        return 0
      end
      if (hAmount[amount_key] == nil) then
        return 0
      end
      return hAmount[amount_key].to_i
    end

  protected
  def buy_products_complete_db_action(oOrderme,orderme_id,order_id,rc)
    # saveTestDb("buy_products_complete_db_action","#{rc}")
    buy_products_complete_db_updateOrderme(orderme_id,order_id,rc)
    if (rc == 0) then
      rent_id = buy_products_complete_db_save_rent(oOrderme)
      buy_products_complete_db_updateUserAp(oOrderme,rent_id)
    end
  end

  def buy_products_complete_db_updateOrderme(orderme_id,order_id,rc)
    oOrderme = Orderme.find_by_id(orderme_id)
    if (oOrderme != nil) then
      oOrderme.billing_order_id = order_id
      oOrderme.status = rc
      oOrderme.save!
    end
  end
  def buy_products_complete_db_save_rent(oOrderme)
    oRent = Rent.new
    oRent.user_id = oOrderme.user_id
    oRent.orderme_id = oOrderme.id
    oRent.rent_date = Xdate.GetNowRails()
    oRent.flag_date = Xdate.GetNowRails()
    oRent.flag = oOrderme.buy_type
    oRent.status = 0

    oUserAp = UserAp.find_by_id(oOrderme.old_userap_id)
    if (oOrderme.buy_type == "FOREVER") then
      oRent.start_date = Xdate.GetNowRails()
      oRent.end_date = oRent.start_date + oOrderme.howlong
    elsif (oOrderme.buy_type == "BUY") then
      oRent.start_date = Xdate.GetNowRails()
      oRent.end_date = oRent.start_date + oOrderme.howlong
    elsif (oOrderme.buy_type == "REBUY") then
      oRent.start_date = oUserAp.end_date
      oRent.end_date = oRent.start_date + oOrderme.howlong
    elsif (oOrderme.buy_type == "BUY_WEB_ADD") then
      oRent.start_date = Xdate.GetNowRails()
      oRent.end_date = oUserAp.end_date
    end
    oRent.save!
    return oRent.id
  end

  def buy_products_complete_db_updateUserAp(oOrderme,rent_id)
    # saveTestDb("#{oOrderme.id}buy_products_complete_db_updateUserAp","oOrderme.product_id:#{oOrderme.product_id},rent_id:#{rent_id}")

    orderme_details = OrdermeDetail.check_ordermeid(oOrderme.id).all
    orderme_details.each do |orderme_detail|
      buy_products_complete_db_addNewUserAp(oOrderme.user_id,orderme_detail.product_id,rent_id)
    end
  end
  def buy_products_complete_db_addNewUserAp(user_id,product_id,rent_id)
    # saveTestDb("buy_products_complete_db_addNewUserAp","user_id : #{user_id}")
      oProduct = g_getProduct(product_id)
      hApFunc = g_product_par_hash(oProduct)
      remainGiftdays = hApFunc["n_gift_days"].to_i
      last_userap = UserAp.available.check_userid(user_id).check_product_name(oProduct.product_name).check_product_version(oProduct.product_version).order("end_date DESC").first

      if (last_userap == nil) then
        h = merge_main_aps(user_id,oProduct.product_name)
        if (h["#{oProduct.product_name}_#{oProduct.product_version}"] == nil) then
          start_date = Xdate.GetNowRails()
        else
          start_date = h["#{oProduct.product_name}_#{oProduct.product_version}"]
        end
      else
        s = "#{last_userap.gift_days} days"
        start_date = last_userap.end_date + dateStrToIntDates(s)
      end
      end_date = start_date + dateStrToIntDates(oProduct.product_content)

      oRent = Rent.find_by_id(rent_id)
      oUserAp = UserAp.new
      oUserAp.user_id = user_id
      oUserAp.product_type = oProduct.product_type
      oUserAp.product_name = oProduct.product_name
      oUserAp.product_version = oProduct.product_version
      oUserAp.product_id = oProduct.id
      oUserAp.start_date = start_date
      oUserAp.end_date = end_date
      oUserAp.gift_days = remainGiftdays
      oUserAp.rent_id = rent_id
      oUserAp.status = 0
      oUserAp.ap_additionalfunc = ""
      oUserAp.save!
  end

  def getOldGiftdaysToNewGiftdays(oUserApOld)
    # return oUserApOld.oldGiftdays
    if (oUserApOld == nil) then
      return 0
    end

    return remainGiftdays(oUserApOld.gift_days,overDays(oUserApOld.end_date))
  end

  def remainGiftdays(orgGiftdays,minusDays)
    if (orgGiftdays > minusDays) then
      return (orgGiftdays - minusDays)
    else
      return 0
    end
  end

  def overDays(d1,d2=nil)
    if (d2 == nil) then
      d2 = Xdate.GetNowRails()
    end
    if (d1 >= d2) then
      return 0
    end
    days = (d2 - d1) / 86400
    return days.to_i
  end

  def computeOnedayAmount(oOrderme)
    # oRent = Rent.find_by_id(rent_id)
    # rent_days = ((oRent.end_date - oRent.start_date) / 86400)
    rent_days = oOrderme.howlong / 1.day
    oneday_amount = (oOrderme.paid_amount / rent_days)
    return oneday_amount
  end

  def findAdditionalFunc(oUserAp,orderme_id)
    # 客戶原先就有的附加功能
    if (oUserAp == nil) then
      hOrgAddFunc = Hash.new
    else
      hOrgAddFunc = Cfate.pan_par_dbfield2hash(oUserAp.ap_additionalfunc)
    end

    # 附加產品的功能
    hOrderAddFunc = getOrdermeAddFunc(orderme_id)

    # 判斷主產品的功能中沒有該附加功能的才加入客戶的附加功能中
    # hOrderAddFunc.each {|key, value|
      # if (hProductFunc.has_key?(key)) then
      #   hOrgAddFunc.delete_if {|orgkey, orgvalue| orgkey == key }
      #   hOrgAddFunc.delete_if {|orgkey, orgvalue| orgkey == "#{key}_info" }
      # else
        # hOrgAddFunc[key] = value
      # end
    # }

    hOrgAddFunc.merge!(hOrderAddFunc) { |key, v1, v2| v2 }

    return Cfate.pan_par_hash2db(hOrgAddFunc)
  end

  def getOrdermeAddFunc(orderme_id)
    hFunc = Hash.new
    orderme_details = OrdermeDetail.check_ordermeid(orderme_id).check_additional.all
        orderme_details.each_with_index do |orderme_detail,index|
          hProductionFunc = getProductFuncParHash(orderme_detail.product_id,orderme_detail.product_type,orderme_detail.product_name) # in peterapplication
      # hFunc.merge!(hProductionFunc) { |key, v1, v2| v2 }
      hProductionFunc.each {|key, value|
        hFunc[key] = value
        hFunc["#{key}_ordermedetailid"] = "#{orderme_detail.id}"
      }
        end
        return hFunc
  end

  def changeRentStatus(rent_id_old,buy_type)
    oRent = Rent.find_by_id(rent_id_old)
    aBuyType = ["RENT","BUY","UPGRADE","REBUY","REFUND","VOID","BUY_WEB_ADD","FOREVER"]
    aStatus = [0,0,2,0,4,5,0,0]
    # 將原先的RENT改為UPGRADE
    if (oRent != nil) then
      # oRent.flag_date = getDateTime(0)
      # oRent.flag = oOrderme.buy_type
      oRent.status = aStatus[aBuyType.index(buy_type)]
      oRent.save!
    end
  end

  def save_orderme(params)
    oOrderme = Orderme.new
    oOrderme.user_id = params["user_id"]
    oOrderme.original_amount = params["original_amount"]
    oOrderme.discount_amount = params["discount_amount"]
    oOrderme.paid_amount = params["paid_amount"]
    # oOrderme.amount = params["amount"]
    oOrderme.ws_dates = params["ws_dates"]
    oOrderme.status = -1
    oOrderme.save!
    return oOrderme.id
  end

    def getDateTime(nElapsedDays)
      # sBirthday = "#{nWYear}-#{nWMonth}-#{nWDate} #{nWHour}:#{nWMin}:#{nWSec}"
      return Xdate.GetNowRails() + nElapsedDays.days
    end

    public
    def buied_products
      user_id = session["user_id"]
    getBuiedProducts(user_id)

    render :layout => 'ifate'
    end

  def list_products
    prods = g_getMainProducts(@ap_name)
    @products = Array.new
    h = Hash.new
    prods.each do |product|
      if (h[product.product_version] == nil) then
        @products.push(product)
      end
      h[product.product_version] = 1
    end
    getBuiedProducts(session["user_id"])
    getBuiedHistProducts(session["user_id"])

    render :layout => 'ifate'
  end

    protected
    def getBuiedProducts(user_id)
      oUserAps = UserAp.available.check_userid(user_id).check_main.check_product_name(@ap_name).order("end_date").all

      @aBuied,@aBuiedProductId = parseBuiedProducts(oUserAps)
    end
    def getBuiedHistProducts(user_id)
      oUserAps = UserAp.not_available.check_userid(user_id).check_main.check_product_name(@ap_name).order("end_date DESC").all

      @aBuiedHist,@aBuiedHistProductId = parseBuiedProducts(oUserAps)
    end
    def parseBuiedProducts(oUserAps)
      aBuied = Array.new
      aBuiedProductId = Array.new

      oUserAps.each do |oUserAp|
        oProduct = g_getProductByUserAp(oUserAp)
        hBuied = Hash.new
        hBuied["start_date"] = oUserAp.start_date
        if (oUserAp.product_type == "forever") then
          hBuied["end_date"] = nil
        else
          hBuied["end_date"] = oUserAp.end_date + oUserAp.gift_days.days
        end
        hBuied["product_description"] = oProduct.product_description
        sInfo = getAgentInfo(oProduct)
        if (sInfo.length > 0) then
          hBuied["product_description"] = hBuied["product_description"] + "," + sInfo
        end
        hBuied["product_type"] = Pm.GetStr("IDS_S_PRODUCT_TYPE_#{oProduct.product_type.upcase}")
        hBuied["ws_dates"] = oProduct.product_content
        hBuied["product_id"] = oProduct.id
        hBuied["additionalfunc"] = Cfate.pan_par_dbfield2hash(oUserAp.ap_additionalfunc)
        hBuied["additionalfunc"][oProduct.product_version] = true
        aBuied.push(hBuied)
        aBuiedProductId.push(oProduct.id)
        # 附屬功能
        # hAddProd = g_func_assign(@ap_name,g_ap_product_addfunc_hash(oUserAp))
        # all_additional = getAllAdditional(oProduct.product_content,oProduct.product_name)
        # hAddFunc = checkAdditionInProduct(all_additional,hAddProd)

        # hAddFunc.each_pair { |key, value|
        #   product = Product.check_additional.check_name(oProduct.product_name).check_version(key).check_ws_dates(oProduct.product_content).last
        #   if (product != nil) then
        #     hBuied = Hash.new
        #     hBuied["start_date"] = oUserAp.start_date
        #     hBuied["end_date"] = oUserAp.end_date + oUserAp.gift_days.days
        #     hBuied["product_description"] = product.product_description
        #     hBuied["product_type"] = Pm.GetStr("IDS_S_PRODUCT_TYPE_#{product.product_type.upcase}")
        #     hBuied["ws_dates"] = product.product_content
        #     @aBuied.push(hBuied)
        #     @aBuiedProductId.push(product.id)
        #   end
        # }

      end
      return aBuied,aBuiedProductId
    end

    def getBuiedProducts2(user_id)
        @aBuied = Array.new
        @aBuiedProductId = Array.new
      if (@oUserAp.free) then
        oProduct = g_getProductByUserAp(@oUserAp)
          hBuied = Hash.new
          hBuied["start_date"] = @oUserAp.start_date
          hBuied["end_date"] = @oUserAp.end_date
        hBuied["product_description"] = oProduct.product_description
        hBuied["product_type"] = Pm.GetStr("IDS_S_PRODUCT_TYPE_#{oProduct.product_type.upcase}")
        hBuied["ws_dates"] = oProduct.product_content
        @aBuied.push(hBuied)
        @aBuiedProductId.push(oProduct.id)
      else
          rents = Rent.available.check_userid(user_id).all
          rents.each do |rent|
            oOrderme = Orderme.find_by_id(rent.orderme_id)
            ordermeDetails = OrdermeDetail.check_ordermeid(rent.orderme_id).all
            ordermeDetails.each do |ordermeDetail|
              hBuied = Hash.new
              hBuied["start_date"] = rent.start_date
              hBuied["end_date"] = rent.end_date
              hBuied["product_description"] = ordermeDetail.product_description
              hBuied["product_type"] = Pm.GetStr("IDS_S_PRODUCT_TYPE_#{ordermeDetail.product_type.upcase}")
            hBuied["ws_dates"] = oOrderme.ws_dates
              @aBuied.push(hBuied)
              @aBuiedProductId.push(ordermeDetail.product_id)
            end
          end
      end
  end

  public
  def updatedb
    # updatedb_userap
    # updatedb_orderme
    # updatedb_ordermedetail
    # check_userap_date
    # update_product

    # redirect_to :action => "buied_products", :ap_name => @ap_name
  end

  protected
  def check_userap_date
    aUserAp = UserAp.date_legal.check_outofdate.all
    aUserAp.each_with_index do |oUserAp,index|
      oUserAp.status = -2
      oUserAp.save!
    end
  end

  def updatedb_userap
    aUserAp = UserAp.all
    aUserAp.each_with_index do |oUserAp,index|
      oProduct = g_getProductByUserAp(oUserAp)
      oUserAp.product_type = oProduct.product_type
      oUserAp.product_name = oProduct.product_name
      oUserAp.product_version = oProduct.product_version
      oUserAp.save!
    end
  end
  def updatedb_orderme
    aOrderme = Orderme.all
    aOrderme.each_with_index do |oOrderme,index|
      oOrderme.ws_dates = new_product_content(oOrderme.ws_dates)
      oOrderme.save!
    end
  end
  def updatedb_ordermedetail
    aOrdermeDetail = OrdermeDetail.all
    aOrdermeDetail.each_with_index do |oOrdermeDetail,index|
      oProduct = g_getProduct(oOrdermeDetail.product_id)
      oOrdermeDetail.product_type = oProduct.product_type
      oOrdermeDetail.product_name = oProduct.product_name
      oOrdermeDetail.product_version = oProduct.product_version
      oOrdermeDetail.product_description = oProduct.product_description
      oOrdermeDetail.save!
    end
  end

  def update_product
    aProduct = Product.all
    aProduct.each_with_index do |oProduct,index|
      oProduct.product_content = new_product_content(oProduct.product_content)
      oProduct.save!
    end
  end
  def new_product_content(old_content)
    a = old_content.split
    if (a.length == 1) then
      a = old_content.split(//,2)
    end
    return a.join(" ")
  end

  public
  def buy_products_forever()
      @buy_type = params["buy_type"]
      @ap_name = params["ap_name"]
      @ws_dates = getWsDates_forever(@ap_name)

      if (@hCanDeal[@buy_type]) then
        buy_products_data_forever
        if (@product == nil) then
          redirect_to :back, :ap_name => @ap_name
        else
        render :layout => 'ifate'
      end
      else
        redirect_to :back, :ap_name => @ap_name
      end
    end

  def buy_products_confirm_forever
    # 取得輸入的資訊
    @buy_type = params["buy_type"]
    @product_ids = params["product_ids"]
    @userap_id = params["userap_id"]
    oProduct = g_getProduct(@product_id)
    @ws_dates = getBuiedWsDates(params["ws_dates"],@userap_id,oProduct.product_version)

    aProdAdd_Buied = Array.new
    if (@product_id == nil && aProdAdd_Buied.length == 0) then
      redirect_to :back, :ap_name => @ap_name
    else
      # 計算總價，包含主約及附加功能
      hAmount,hAmountDetails = bpc_compute_amount(@buy_type,@product_ids,@ws_dates,@userap_id,aProdAdd_Buied)
    # 儲存到Orderme中
      nOrdermeId = saveOrdermes(@product_ids,@ws_dates,@buy_type,hAmount,hAmountDetails,@userap_id,aProdAdd_Buied)
      # 準備送到billing
      @orderno = nOrdermeId
      @paytitle = getBillingPayTitle(@buy_type,@product_ids,@ws_dates,aProdAdd_Buied,@userap_id)
      @paymemo = getBillingPayMemo(@buy_type,@product_ids,@ws_dates,aProdAdd_Buied,hAmount["paid_amount"],hAmount["refund_amount"],nOrdermeId)
      @sHttp = Pm.getBillingServer("/dispatchpaymentconfirm")
      # @sHttp = "dispatchpaymentconfirm"
      @original_amount = hAmount["original_amount"]
      @discount_amount = hAmount["discount_amount"]
      @paid_amount = getPaidAmount(hAmount["paid_amount"],hAmount["refund_amount"])
          oUser = User.find_by_id(session["user_id"])
      @payname = oUser.name
      @payphone = ""
      @duedate = ""
      @user_id = session["user_id"]
      @server_name = getWebServer()
      @feedback_url = Pm.getHostUrl("/service/store/buy_products_complete_forever")
      @receive_url = Pm.getHostUrl("/ifate/customer/index")


      if (@paid_amount == 0) then
        # redirect_to :controller => "service/store" ,:action => "buy_products_complete" , :rc => 0, :order_id => 0, :orderme_id => nOrdermeId
     #        url = URI.parse(@feedback_url)
     #          res = Net::HTTP.post_form(url,{:rc => 0, :orderme_id => nOrdermeId, :order_id => 0 })
        # redirect_to @receive_url
      # else
      #   render :layout => 'ifate'
        @sHttp = @feedback_url
        @rc = 0
        @orderme_id = nOrdermeId
        @order_id = 0
      end
      render :layout => 'ifate'
      end
    end

    def buy_products_complete_forever
    rc = params["rc"].to_i
    order_id = params["order_id"].to_i
    orderme_id = params["orderme_id"].to_i

    # saveTestDb("#{orderme_id}buy_products_complete_forever",Cfate.pan_par_hash2db(params.to_hash))
    if (orderme_id > 0) then
      oOrderme = Orderme.find_by_id(orderme_id)
      oPayment = Payment.check_orderid(order_id).last
      if (oOrderme != nil) then
        if (rc == 0 && (oPayment != nil || (oOrderme.paid_amount <= oOrderme.refund_amount))) then
          buy_products_complete_db_action(oOrderme,orderme_id,order_id,rc)
        else
          buy_products_complete_db_action(oOrderme,orderme_id,order_id,-2)
        end
      end
    end
    if (oOrderme.paid_amount <= oOrderme.refund_amount) then
      receive_url = params["receive_url"]

      redirect_to URI.parse(receive_url).path 
      # redirect_to receive_url
    else
      render :layout => false
    end
    end

    protected
    def buy_products_data_forever()
      if (@hCanDeal[@buy_type]) then
        @userap_id = @oUserAp.id
        @ap_version = @oUserAp.product_version

        @product = Product.check_forever.check_name(@ap_name).last

        @user_id = session["user_id"]

        @aAllWsDates = getAllWsDates_forever(@ap_name)
        forever_counts = g_getForeverUserApCounts(@ap_name)
        @comment = Pm.getSystemValue("forever_comment") % (forever_counts)
      end
    end

    def getWsDates_forever(ap_name)
      b = getAllWsDates_forever(ap_name)
      return b[0]
    end

    def getAllWsDates_forever(ap_name)
      a = Product.select(:product_content).check_forever.check_name(ap_name).distinct
      if (a == nil) then
        b = [""]
      else
        b = Array.new
        a.each do |p|
          b.push(p.product_content)
        end
      end
      return b
    end

    public
  def star
      @ap_name = "star"
    free_data
    render :layout => 'star_pan_simple' #'customer'
  end
  def eightword
      @ap_name = "eightword"
    free_data
    render :layout => 'star_pan_simple' #'customer'
  end
  def xdate
      @ap_name = "xdate"
    free_data
    render :layout => 'star_pan_simple' #'customer'
  end
  def free
    free_data
    render :layout => 'star_pan_simple' #'customer'
  end

  protected
  def free_data
    if (session["user_id"] != nil) then
      authenticate_user!
    end
    authenticate2(@ap_name)
    @sign_in = false
    if (session["user_id"] != nil) then  #if logged_in?
      @sign_in = true
    end
    @m_nPanType = Cfate::PAN_NORMAL
    @par_YearDisplay = Cfate::PAN_DISPLAY_WEST
    @StarYearSelect = 1901
    @StopYearSelect = 2099

    newUserCustomerNow()
    getApNames
  end
  def getApNames()
    @hAps = Hash.new
    @hAps[ap_title("star")] = "star"
    @hAps[ap_title("eightword")] = "eightword"
    @hAps[ap_title("xdate")] = "xdate"
    @sAp = @ap_name
  end
  def ap_title(ap_name)
    return Pm.GetStr("IDS_APNAME_#{ap_name.upcase}")
  end
  def newUserCustomerNow()
    newUserCustomer
    # new_for_now
  end

  def new_for_now()
    t = time_now_local()
    @user_customer.calType = Xdate::CT_SOLAR
    @user_customer.wyear = t.year
    @user_customer.wmonth = t.month
    @user_customer.wday = t.day
    @user_customer.hour = t.hour
    @user_customer.minute = t.min
    @user_customer.name = ""
    @user_customer.sex = 1
    @calType = Xdate::CT_SOLAR
  end
  def newUserCustomer()
    hAll = getUserInfoHash()
    assignUserCustomer(hAll,nil)
  end
  def assignUserCustomer(hAll,uc_id)
    user_id = session["user_id"]
    if (uc_id != nil) then
      u = UserCustomer.check_user_id(user_id).find(uc_id)
    else
      u = UserCustomer.new
    end

    t = time_now_local()
    if (u != nil) then
      u.user_id = user_id
      u.name = hAll["name"]
      u.sex = Cfate.HashOrDefault(hAll,"sex",0)
      u.calType = Cfate.HashOrDefault(hAll,"calType",Xdate::CT_SOLAR)
      u.wyear = Cfate.HashOrDefault(hAll,"wyear",t.year)
      u.wmonth = Cfate.HashOrDefault(hAll,"wmonth",t.month)
      u.wday = Cfate.HashOrDefault(hAll,"wday",t.day)
      u.eyear = Cfate.HashOrDefault(hAll,"eyear",Xdate::DATE_NIL)
      u.eleap = Cfate.Bool2Int(hAll["eleap"])
      u.emonth = Cfate.HashOrDefault(hAll,"emonth",Xdate::DATE_NIL)
      u.eday = Cfate.HashOrDefault(hAll,"eday",Xdate::DATE_NIL)
      u.hour = Cfate.HashOrDefault(hAll,"hour",t.hour)
      u.minute = Cfate.HashOrDefault(hAll,"minute",t.min)
      u.blood = hAll["blood"]
      u.phone = hAll["phone1"]
      u.address = hAll["address1"]
      u.email = hAll["email1"]
      u.userclass = hAll["userclass"]
      hAllOrg = u.hAll
      hAllOrg.merge!(hAll) {|key, v1, v2| v2}
      u.hAll = hAllOrg
      # u.all_info = Cfate.pan_par_hash2db(hAll)
    end
    @user_customer = u
  end

  def getUserInfoHash()
    hDate = Xdate.date_input_prepare(params)

    hAll = Hash.new
    hAll.merge!(hDate)
    hAll["name"] = params["name"]
    hAll["sex"] = params["sex"]
    hAll["address1"] = params["address"]
    hAll["address2"] = params["address2"]
    hAll["phone1"] = params["phone"]
    hAll["phone2"] = params["phone2"]
    hAll["blood"] = params["blood"]
    hAll["addressborn"] = params["addressborn"]
    hAll["longitude"] = params["longitude"]
    hAll["latitude"] = params["latitude"]
    hAll["stature"] = params["stature"]
    hAll["constellation"] = params["constellation"]
    hAll["educationbackground"] = params["educationbackground"]
    hAll["email1"] = params["email"]
    hAll["email2"] = params["email2"]
    hAll["webaddress1"] = params["webaddress"]
    hAll["webaddress2"] = params["webaddress2"]
    hAll["introducer"] = params["introducer"]
    hAll["userclass"] = params["userclass"]
    hAll["others"] = params["others"]
    hAll["end"] = "end"

    return hAll
  end


  public
  def input_agent_code
    @buy_type = params["buy_type"]
    if (@oUserAp.main && @buy_type == "BUY") then
      oProduct = Product.find_by_id(@oUserAp.product_id)
      @agent_code = oProduct.product_version
      @ws_dates = oProduct.product_content
      redirect_to :action => "buy_products", :ap_name => @ap_name, :buy_type => @buy_type, :agent_code => @agent_code, :ws_dates => @ws_dates
    else
      render :layout => 'ifate'
    end
  end
 end
