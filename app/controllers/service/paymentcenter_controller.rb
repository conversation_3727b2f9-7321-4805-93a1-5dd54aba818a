require "net/https"
require 'net/http'
require 'uri'
require 'digest/md5'
require("Pm.rb")

class Service::PaymentcenterController < ApplicationController
  # skip_before_action :verify_authenticity_token

  @@NONCREDIT_PT = ["ATM","WEBATM","CS","MMK","ALIPAY"]
  @@CREDIT_PT = ["CREDIT"]
  @@BankId = "007"

  # @@MerchantNumber_NonCredit = "455159"
  # @@MerchantNumber_Credit = "757858"

  @@MerchantNumber_NonCredit = "455159"
  @@MerchantNumber_Credit = "759737"

  # 測試的放在system_pars表格中
  # @@CreditServer_D = "https://maple2.neweb.com.tw/NewebmPP/cdcard.jsp"
  # @@NonCreditServer_D = "http://maple2.neweb.com.tw/CashSystemFrontEnd/Payment"
  # @@Code_NonCredit_Shop_D               = "abcd1234"
  # @@Code_NonCredit_FeedbackReceive_D               = "abcd1234"
  # @@Code_Credit_Confirm_D        = "abcd1234"
  # @@Code_Credit_FeedbackReceive_D        = "abcd1234"

  @@CreditServer = "https://taurus.neweb.com.tw/NewebmPP/cdcard.jsp"
  @@NonCreditServer = "https://aquarius.neweb.com.tw/CashSystemFrontEnd/Payment"
  @@Code_NonCredit_Shop               = "cdai3y9m"
  @@Code_NonCredit_FeedbackReceive               = "cdai3y9m"
  @@Code_Credit_Confirm        = "v6zqs8ne"
  @@Code_Credit_FeedbackReceive        = "v6zqs8ne"

  public
  # 若無paymenttype,則會執行2次 dispatchpaymentconfirm
  def dispatchpaymentconfirm
    dispatchpaymentconfirm_neweb
  end
  def dispatchreceive
    dispatchreceive_neweb
  end
  def dispatchresult
    dispatchresult_neweb
  end
  def dispatchfeedback
    dispatchfeedback_neweb
  end

  protected
  def getCreditServer()
    sData = Pm.getSystemValue("Neweb_CreditServer")
    if (sData != "") then
      return sData
    else
      return @@CreditServer
    end
  end

  def getNonCreditServer()
    sData = Pm.getSystemValue("Neweb_NonCreditServer")
    if (sData != "") then
      return sData
    else
      return @@NonCreditServer
    end
  end

  def getCode_NonCredit_Shop()
    sData = Pm.getSystemValue("Neweb_Code")
    if (sData != "") then
      return sData
    else
      return @@Code_NonCredit_Shop
    end
  end

  def getCode_NonCredit_FeedbackReceive()
    sData = Pm.getSystemValue("Neweb_Code")
    if (sData != "") then
      return sData
    else
      return @@Code_NonCredit_FeedbackReceive
    end
  end

  def getCode_Credit_Confirm()
    sData = Pm.getSystemValue("Neweb_Code")
    if (sData != "") then
      return sData
    else
      return @@Code_Credit_Confirm
    end
  end

  def getCode_Credit_FeedbackReceive()
    sData = Pm.getSystemValue("Neweb_Code")
    if (sData != "") then
      return sData
    else
      return @@Code_Credit_FeedbackReceive
    end
  end


  protected
  # 若無paymenttype,則會執行2次 dispatchpaymentconfirm
  def dispatchpaymentconfirm_neweb
      sOrderno = params["ordernumber"]
      if (sOrderno == nil) then
        sOrderno = makeNewOrderno()
      end
      sOrderid = params["order_id"]
      if (sOrderid == nil) then
        client_ip = request.remote_ip
        order_id = save_order(params,sOrderno,client_ip) # return order_id
      else
        order_id = sOrderid.to_i
      end

      paymenttype = params["paymenttype"]
      if (paymenttype == nil || paymenttype == "") then
        dispatchpaymentconfirm_selectpaymenttype(order_id,sOrderno)
      else
        if (isNewebPayment?(paymenttype)) then # 藍新
          dispatchpaymentconfirm_direct_Neweb(order_id,sOrderno)
        # elsif (isPaypalPayment?(paymenttype)) then # Paypal
          # dispatchpaymentconfirm_direct_Paypal(order_id,sOrderno)
        # elsif (isChtotpPayment?(paymenttype)) then # 潗大
          # dispatchpaymentconfirm_direct_Chtotp(order_id,sOrderno)
        else # 重新選 paymenttype
          dispatchpaymentconfirm_selectpaymenttype(order_id,sOrderno)
        end
      end
  end

  protected
  def isNewebPayment?(paymenttype)
    if (isCredit?(paymenttype)) then
      return true
    end
    if (isNonCredit?(paymenttype)) then
      return true
    end
    return false
  end

  def isPaypalPayment?(paymenttype)
    return ("PAYPAL" == paymenttype)
  end

  def isChtotpPayment?(paymenttype)
    return ("CHTOTP" == paymenttype)
  end

  def getConfirmParams()
	    @original_amount = params["original_amount"]
      @discount_amount = params["discount_amount"]
      @paid_amount = params["paid_amount"]
  	  @paymenttype = params["paymenttype"]
      if (@paymenttype == "") then
        @paymenttype = nil
      end
      @paytitle = params["paytitle"]
      @paymemo = params["paymemo"]
      @user_id = params["user_id"]
      @product_id = params["product_id"]
      @server_name = params["server_name"]
      @feedback_url = params["feedback_url"]
      @receive_url = params["receive_url"]
      @client_ip = request.remote_ip
      @payname = params["payname"]
      @payphone = params["payphone"]
      @duedate = params["duedate"]
  end

  def dispatchpaymentconfirm_selectpaymenttype(order_id,sOrderno)
      # saveTestDb("#{order_id}dispatchpaymentconfirm_selectpaymenttype",pan_par_hash2db(params.to_hash))
      getConfirmParams()
      @paymenttype = nil

      @order_id = order_id

      @nexturl = @receive_url
      @aPaymentTitle,@aPaymentType,@aaPayment = getAllPaymentType()

      render :layout => "billing"
 end

  def dispatchpaymentconfirm_direct_Neweb(order_id,sOrderno)
      # saveTestDb("#{order_id}dispatchpaymentconfirm_direct_Neweb",pan_par_hash2db(params.to_hash))
      getConfirmParams()

      @order_id = order_id
      update_order_paymenttype(@order_id,@paymenttype)

      @aPaymentTitle,@aPaymentType,@aaPayment = getAllPaymentType()
      ptIndex = @aPaymentType.index(@paymenttype)
      @sHttp = @aaPayment[ptIndex][2]
      #hPar["next_url"] = "http://#{request.host}:#{request.port}#{'/service/paymentcenter/dispatchreceive'}"

      @hash = makeMd5Hash_Confirm(@paymenttype,@order_id,@paid_amount)

      @paymenttype_partialtail = getPaymenttypeSum(@paymenttype)
      @merchantnumber = getMerchantNumber(@paymenttype)
      # @orderurl = "http://#{request.host}/service/paymentcenter/dispatchreceive"
      # @nexturl = "http://#{request.host}/service/paymentcenter/dispatchfeedback"
      @orderurl = Pm.getBillingServer("/service/paymentcenter/dispatchfeedback")
      @nexturl = Pm.getBillingServer("/service/paymentcenter/dispatchreceive")

      @bankid = @@BankId
      # h = Hash.new
      # h["sHttp"] = @sHttp
      # h["orderurl"] = @orderurl
      # h["nexturl"] = @nexturl
      # saveTestDb("#{order_id}dispatchpaymentconfirm_direct_Neweb_post",pan_par_hash2db(h))

      render :layout => false
  end

  def update_order_paymenttype(order_id,paymenttype)
    oOrder = Order.find_by_id(order_id)
    oOrder.payment_type = paymenttype
    oOrder.save!
  end

  def getAllPaymentType()
    sNoneCreditHttp = getNonCreditServer()
    sCreditHttp = getCreditServer()
    sGiDaHttp = ""
    sPaypalHttp = ""
    aPaymentTitle = ["PaymentType","Description","Submit"]
    aPaymentType = ["ATM","WEBATM","CS","MMK","ALIPAY","CREDIT","CHTOTP","PayPal"]
    aaPayment = [
                 ["ATM",Pm.GetStr("IDS_PAYMENT_ATM"),sNoneCreditHttp],
                 ["WEBATM",Pm.GetStr("IDS_PAYMENT_WEBATM"),sNoneCreditHttp],
                 ["CS",Pm.GetStr("IDS_PAYMENT_CS"),sNoneCreditHttp],
                 ["MMK",Pm.GetStr("IDS_PAYMENT_MMK"),sNoneCreditHttp],
                 ["ALIPAY",Pm.GetStr("IDS_PAYMENT_ALIPAY"),sNoneCreditHttp],
                 ["CREDIT",Pm.GetStr("IDS_PAYMENT_CREDIT"),sCreditHttp]#,
                 # ["CHTOTP",Pm.GetStr("IDS_PAYMENT_CHTOTP"),sGiDaHttp],
                 # ["PayPal",Pm.GetStr("IDS_PAYMENT_PAYPAL"),sPaypalHttp]
                 ]
    return aPaymentTitle,aPaymentType,aaPayment
  end

  protected
  def isNonCredit?(paymenttype)
    if (paymenttype == nil) then
      return true
    end
    if (@@NONCREDIT_PT.index(paymenttype) == nil) then
      return false
    end
    return true
  end
  def isCredit?(paymenttype)
    if (@@CREDIT_PT.index(paymenttype) == nil) then
      return false
    end
    return true
  end

  def getPaymenttypeSum(paymenttype)
    if (isCredit?(paymenttype)) then
       return "_credit"
    else #if (isNonCredit?(paymenttype)) then
       return "_noncredit"
    end
    return ""
  end

  def getMerchantNumber(paymenttype)
    if (isCredit?(paymenttype)) then
       return @@MerchantNumber_Credit
    else #if (isNonCredit?(paymenttype)) then
       return @@MerchantNumber_NonCredit
    end
    # return ""
  end

  def getHashCodeSend(paymenttype)
    if (isCredit?(paymenttype)) then
       return getCode_Credit_Confirm()
    else #if (isNonCredit?(paymenttype)) then
       return getCode_NonCredit_Shop()
    end
    return ""
  end

  def getHashCodeFeedbackReceive(paymenttype)
    if (isCredit?(paymenttype)) then
       return getCode_Credit_FeedbackReceive()
    else #if (isNonCredit?(paymenttype)) then
       return getCode_NonCredit_FeedbackReceive()
    end
    return ""
  end

  def makeMd5Hash_Confirm(paymenttype,ordernumer,amount)
    # saveTestDb("#{ordernumer}makeMd5Hash_Confirm","#{paymenttype}#{ordernumer}:#{amount}")
    merchantnumber = getMerchantNumber(paymenttype)

    sCode = getHashCodeSend(paymenttype)

    if (isCredit?(paymenttype)) then
       chkstr = "#{merchantnumber}#{ordernumer}#{sCode}#{Pm.makeDouble(amount)}"
    else #if (isNonCredit?(paymenttype)) then
       chkstr = "#{merchantnumber}#{sCode}#{amount}#{ordernumer}"
    # else
       # chkstr = ""
    end

     sHash = Digest::MD5.hexdigest(chkstr)

     return sHash
  end

  def makeNewOrderno()
    t = Xdate.GetNowRails()
    sOrderno = "%04d%02d%02d%02d%02d%02d" % [t.year,t.month,t.day,t.hour,t.min,t.sec]
    if (Order.find_by_orderno(sOrderno) != nil) then
      return makeNewOrderno()
    end
    return sOrderno
  end

  def save_order(params,orderno,client_ip)
  	oOrder = Order.new
    oOrder.server_name = params["server_name"]
  	oOrder.user_id = params["user_id"]
  	oOrder.orderno = orderno
  	oOrder.product_id = params["product_id"]
    oOrder.original_amount = params["original_amount"]
    oOrder.discount_amount = params["discount_amount"]
    oOrder.paid_amount = params["paid_amount"]
  	# oOrder.amount = params["amount"]
  	oOrder.payment_type = params["paymenttype"]
  	oOrder.feedback_url = params["feedback_url"]
  	oOrder.receive_url = params["receive_url"]
    oOrder.client_ip = client_ip
    oOrder.status = -1
  	oOrder.save!
    return oOrder.id
  end

  protected
  def dispatchreceive_neweb
      getReceivePars()
      # saveTestDb("#{@order_id}dispatchreceive",pan_par_hash2db(params.to_hash))
      if (@order_id != nil && @order_id.length > 0) then
          oOrder = Order.find_by_id(@order_id.to_i)
          # saveTestDb("#{@order_id}receive_url","#{oOrder.payment_type} : #{oOrder.receive_url}")

          redirect_to :controller => "service/paymentcenter" ,:action => "dispatchresult", :rc => @rc , :payment_type => oOrder.payment_type, :order_id => @order_id, :receive_url => oOrder.receive_url
          #redirect_to :controller => "ifate/customer" ,:action => "index", :rc => rc , :order_id => order_id
      else
          receive_url = "/ifate/customer/index"
          payment_type = ""
          oOrder = Order.check_clientip(request.remote_ip).check_status(-1).last
          if (oOrder != nil) then
            receive_url = oOrder.receive_url
            payment_type = oOrder.payment_type
          end
          # saveTestDb("#{request.remote_ip}receive_url","#{payment_type} : #{receive_url}")
          redirect_to :controller => "service/paymentcenter" ,:action => "dispatchresult", :rc => @rc, :payment_type => payment_type, :receive_url => receive_url
      end
  end

  protected
  def getReceivePars()
     order_id = params["P_OrderNumber"]
     sCheckSum = params["P_CheckSum"]
     if (order_id != nil && sCheckSum != nil) then
        getReceivePars_Credit()
     else
        paymenttype = params["paymenttype"]
        # if (isNonCredit?(paymenttype)) then
          getReceivePars_NonCredit()
        # end
     end
  end

  def getReceivePars_Credit()
     @order_id = getParams("P_OrderNumber")
     @final_result = getParams("final_result")
     @final_return_PRC = getParams("final_return_PRC")
     @final_return_SRC = getParams("final_return_SRC")
     @amount = getParams("P_Amount").to_i
     @hash = getParams("P_CheckSum")
     @final_return_ApproveCode = getParams("final_return_ApproveCode")
     @final_return_BankRC = getParams("final_return_BankRC")
     @final_return_BatchNumber = getParams("final_return_BatchNumber")
     @paymenttype = @@CREDIT_PT[0]
     @merchantnumber = getMerchantNumber(@paymenttype)
     @timepaid = nil
     @rc = checkReceiveResult_Credit(@paymenttype,@order_id,@amount)
  end
  def getReceivePars_NonCredit()
     @order_id    = getParams("ordernumber")
     @amount    = getParams("amount").to_i
     @paymenttype    = getParams("paymenttype")
     if (getParams("rc") == nil) then
       @rc = 0
     else
      @rc = getParams("rc").to_i
     end
  end

  def checkReceiveResult(paymenttype,ordernumer,amount)
    if (isCredit?(paymenttype)) then
       rc = checkReceiveResult_Credit(paymenttype,ordernumer,amount)
    else
    # elsif (isNonCredit?(paymenttype)) then
      if (params["rc"] != nil) then
         rc = params["rc"].to_i
      else
         rc = 0
      end
    end
    return rc
  end

  def checkReceiveResult_Credit(paymenttype,ordernumer,amount)
      final_result = params["final_result"]
      final_return_PRC = params["final_return_PRC"]
      final_return_SRC = params["final_return_SRC"]
      final_return_BankRC = params["final_return_BankRC"]
      rc = 0
      if(final_result == "1") then
        if (checkMd5Hash_Receive(paymenttype,ordernumer,amount) == false) then
          rc = 99
        end
      else
          if(final_return_PRC == "8" && final_return_SRC == "204") then
               rc = 8204
          elsif (final_return_PRC == "34" && final_return_SRC == "171") then
               rc = 34171
          else
               rc = 99999
          end
      end
      return rc
  end

  def checkMd5Hash_Receive(paymenttype,ordernumer,amount)
    merchantnumber = getMerchantNumber(paymenttype)
    sCode = getHashCodeSend(paymenttype)
    if (isCredit?(paymenttype)) then
        ordernumber = params["P_OrderNumber"]
        amount = params["P_Amount"].to_i
        final_result = params["final_result"]
        final_return_PRC = params["final_return_PRC"]
        final_return_SRC = params["final_return_SRC"]
        sCheckSum = params["P_CheckSum"]
        if(sCheckSum.length > 0) then
          chkstr = "#{merchantnumber}#{ordernumer}#{final_result}#{final_return_PRC}#{sCode}#{final_return_SRC}#{Pm.makeDouble(amount)}"
          sHash = Digest::MD5.hexdigest(chkstr)
          return (sHash == sCheckSum)
        end
    # elsif (isNonCredit?(paymenttype)) then
    #     return true
  	end
    return true
  end

  protected
  def dispatchresult_neweb
      @rc = params["rc"].to_i
      @payment_type = params["payment_type"]
      @order_id = params["order_id"]
      @receive_url = params["receive_url"]
      # saveTestDb("#{@order_id}dispatchresult",pan_par_hash2db(params.to_hash))

      oOrder = Order.find_by_id(@order_id)
      @ordernumber = oOrder.orderno if (oOrder != nil)
      # @receive_url += "?rc=#{@rc}&ordernumber=#{oOrder.orderno}"

      render :layout => "billing"
  end

  protected
  def dispatchfeedback_neweb
      getFeedbackPars()
      rc = checkFeedbackResult()

      # saveTestDb("#{@order_id}dispatchfeedback",pan_par_hash2db(params.to_hash))

   		feedback_url,receive_url,nSuccess = update_order_status(@order_id,rc)
  		hPar = params.to_hash
  		hPar["status"] = nSuccess
  		if (nSuccess == 0) then
  		  save_payment(@order_id,hPar)
  		end

  		sHttp = feedback_url

      uri = URI.parse(sHttp)
        # saveTestDb("sHttp","#{sHttp}")
      if (sHttp.length > 0) then
          oOrder = Order.find_by_id(@order_id)
          #1: Simple POST
        #   if (isCredit?(@paymenttype)) then
        #   	res = Net::HTTP.post_form(uri,{:rc => nSuccess, :orderme_id => oOrder.orderno, :order_id => @order_id, :MerchantNumber => @merchantnumber, :OrderNumber => oOrder.orderno, :Amount => @amount, :final_result => @final_result, :PRC => @PRC, :SRC => @SRC, :CheckSum => @hash, :ApprovalCode => @ApprovalCode, :BankResponseCode => @BankResponseCode, :BatchNumber => @BatchNumber })
		      # else
        #     res = Net::HTTP.post_form(uri,{:rc => nSuccess, :orderme_id => oOrder.orderno, :order_id => @order_id, :merchantnumber => @merchantnumber, :ordernumber => oOrder.orderno, :amount => @amount, :paymenttype => @paymenttype, :serialnumber => @serialnumber, :writeoffnumber => @writeoffnumber, :timepaid => @timepaid, :tel => @tel, :hash => @hash })
        #   end
          # res = Net::HTTP.post_form(uri,{:rc => nSuccess, :order_id => @order_id, :MerchantNumber => @merchantnumber, :OrderNumber => oOrder.orderno, :Amount => @amount, :final_result => @final_result, :PRC => @PRC, :SRC => @SRC, :CheckSum => @hash, :ApprovalCode => @ApprovalCode, :BankResponseCode => @BankResponseCode, :BatchNumber => @BatchNumber, :merchantnumber => @merchantnumber, :ordernumber => oOrder.orderno, :amount => @amount, :paymenttype => @paymenttype, :serialnumber => @serialnumber, :writeoffnumber => @writeoffnumber, :timepaid => @timepaid, :tel => @tel, :hash => @hash  })
          if (isCredit?(@paymenttype)) then
            data = {:rc => nSuccess, :orderme_id => oOrder.orderno, :order_id => @order_id, :MerchantNumber => @merchantnumber, :OrderNumber => oOrder.orderno, :Amount => @amount, :final_result => @final_result, :PRC => @PRC, :SRC => @SRC, :CheckSum => @hash, :ApprovalCode => @ApprovalCode, :BankResponseCode => @BankResponseCode, :BatchNumber => @BatchNumber }
          else
            data = {:rc => nSuccess, :orderme_id => oOrder.orderno, :order_id => @order_id, :merchantnumber => @merchantnumber, :ordernumber => oOrder.orderno, :amount => @amount, :paymenttype => @paymenttype, :serialnumber => @serialnumber, :writeoffnumber => @writeoffnumber, :timepaid => @timepaid, :tel => @tel, :hash => @hash }
          end

          if (sHttp.include?("https://")) then
            res = post_https(uri,data)
          else
            res = post_http(uri,data)
          end
        # saveTestDb("res","#{res}")
      else
          render :layout => false
      end
  end

  protected
  def post_http(uri,data)
    res = Net::HTTP.post_form(uri,data)
  end
  def post_https(uri,data)
    # uri = URI.parse("https://secure.com/")
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    http.verify_mode = OpenSSL::SSL::VERIFY_NONE
    # http.verify_mode = OpenSSL::SSL::VERIFY_PEER

    # request = Net::HTTP::Get.new(uri.request_uri)
    request = Net::HTTP::Post.new(uri.request_uri)
    request.set_form_data(data)

    response = http.request(request)
    # response.body
    # response.status
    # response["header-here"] # All headers are lowercase
    return response
  end
  def post_https_basicauth(uri,data)
    # uri = URI.parse(@url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    http.verify_mode = OpenSSL::SSL::VERIFY_PEER
    http.ca_file = "equifax_ca.crt"
    
    request = Net::HTTP::Post.new(uri.request_uri,initheader = {‘Content-Type’ =>’application/json’})
    request.body = data.to_json #payload is defined earlier as a hash with key/value pair
    
    request.basic_auth @username, @apikey
    response = http.request(request)
    return response.body  
  end

  def checkFeedbackResult()
    if (isCredit?(@paymenttype)) then
      return checkFeedbackResult_Credit()
    else #if (isNonCredit?(@paymenttype)) then
      return checkFeedbackResult_NonCredit()
    end
    # return 0
  end

  def checkFeedbackResult_Credit()
      if (@PRC == "0" && @SRC == "0") then
          # 回傳成功，但結果有可能遭竄改，因此需和編碼內容比較
        if (checkMd5Hash_Feedback(@paymenttype) == true) then
               # // 交易成功
               rc = 0
        else
               # //-- 資料遭竄改
               rc = 99
  		end
     elsif (@PRC == "34" && @SRC == "171") then
          # // 交易失敗(金融失敗)
               rc = 34171
     elsif (@PRC == "8" && @SRC == "204") then
          # // 訂單編號重複!
               rc = 8204
     elsif(@PRC == "52" && @SRC == "554") then
          # // 使用者帳號密碼錯誤!
               rc = 52554
     else
          # // 交易失敗(系統錯誤)
               rc = 99999
     end
     return rc
  end

  def checkFeedbackResult_NonCredit()
     rc = 99
     if (checkMd5Hash_Feedback(@paymenttype) == true) then
          rc = 0
     end
     return rc
   end

  def getFeedbackPars()
     order_id = params["OrderNumber"]
     sCheckSum = params["CheckSum"]
     if (order_id != nil && sCheckSum != nil) then
        getFeedbackPars_Credit()
     else
        paymenttype = params["paymenttype"]
        # if (isNonCredit?(paymenttype)) then
          getFeedbackPars_NonCredit()
        # end
     end
  end

  def getFeedbackPars_Credit()
     @order_id = getParams("OrderNumber")
     @final_result = getParams("final_result")
     @PRC = getParams("PRC")
     @SRC = getParams("SRC")
     @amount = getParams("Amount").to_i
     @hash = getParams("CheckSum")
     @ApprovalCode = getParams("ApprovalCode")
     @BankResponseCode = getParams("BankResponseCode")
     @BatchNumber = getParams("BatchNumber")
     @paymenttype = @@CREDIT_PT[0]
     @merchantnumber = getMerchantNumber(@paymenttype)
     @timepaid = nil
  end
  def getFeedbackPars_NonCredit()
     @order_id    = getParams("ordernumber")
     @amount    = getParams("amount").to_i
     @paymenttype    = getParams("paymenttype")
     @hash    = getParams("hash")
     @merchantnumber = getMerchantNumber(@paymenttype)
     @serialnumber    = getParams("serialnumber")
     @writeoffnumber    = getParams("writeoffnumber")
     @timepaid    = getParams("timepaid")
     @tel    = getParams("tel")
  end

  def checkMd5Hash_Feedback(paymenttype)
    if (@hash == nil) then
      return true
    end

    sCode = getHashCodeFeedbackReceive(paymenttype)
    amount = @amount

    if (isCredit?(paymenttype)) then
      # chkstr = $MerchantNumber.$OrderNumber.$PRC.$SRC.$Code.$Amount;
      chkstr = "#{@merchantnumber}#{@order_id}#{@PRC}#{@SRC}#{sCode}#{Pm.makeDouble(amount)}"
    else #if (isNonCredit?(paymenttype)) then
      chkstr = "merchantnumber=#{@merchantnumber}" +
               "&ordernumber=#{@order_id}" +
               "&serialnumber=#{@serialnumber}" +
               "&writeoffnumber=#{@writeoffnumber}" +
               "&timepaid=#{@timepaid}" +
               "&paymenttype=#{@paymenttype}" +
               "&amount=#{amount}" +
               "&tel=#{@tel}" +
               sCode
     # else
     #    return true
     end

     sHash = Digest::MD5.hexdigest(chkstr)

     if (sHash == @hash) then
        # saveTestDb("#{@order_id}HashComplete",sHash)
        return true
     else
        # saveTestDb("#{@order_id}HashFail","#{sHash},chrstr,#{chkstr}")
        return false
     end
  end
  def getParams(key)
    if (params[key] == nil) then
      return ""
    end
    return params[key]
  end

  protected
 # 0：已建立（僅建立訂單）1：已銷帳（消費者已繳款，並與收款業者銷帳）
  def update_order_status(order_id,rc)
    # saveTestDb("#{order_id}update_order_status","order_id,#{order_id},rc,#{rc}")
    if (order_id == nil) then
      return "","",-1
    end

  	oOrder = Order.find_by_id(order_id)
    if (oOrder != nil) then
      if (oOrder.status != -1) then
        return "","",-1
      end

    	if (rc == 0) then
    		oOrder.status = 0
    		nSuccess = 0
    	else
    		oOrder.status = rc
    		nSuccess = -1
    	end
    	feedback_url = oOrder.feedback_url
    	receive_url = oOrder.receive_url
      oOrder.save!
    	return feedback_url,receive_url,nSuccess
    else
      return "","",-1
    end
  end

  def save_payment(order_id,hPar)
	  oPayment = Payment.new
    oPayment.order_id = order_id
    oOrder = Order.find_by_id(oPayment.order_id)
    oPayment.server_name = oOrder.server_name
    oPayment.user_id = oOrder.user_id
  	oPayment.orderno = oOrder.orderno
    oPayment.original_amount = oOrder.original_amount
    oPayment.discount_amount = oOrder.discount_amount
  	oPayment.paid_amount = @amount
  	oPayment.payment_type = @paymenttype
    oPayment.paid_date = @timepaid == nil ? Xdate.GetNowRails() : @timepaid
  	oPayment.vendor_srv_data = pan_par_hash2db(hPar)
  	oPayment.vendor_srv_status = hPar["status"]
	  oPayment.save!
    # saveTestDb("#{order_id}save_payment",pan_par_hash2db(hPar))
  end

  def saveTestDb(key,value)
      tdb = Testdb.new
      tdb.key = "billing:#{key}"
      tdb.value = value
      tdb.save!
  end
  def pan_par_hash2db(h)
    a = h.to_a#.flatten
    return pan_par_join(a)
  end

  def pan_par_join(a)
    s = a.join(",")
    return s
  end

end
