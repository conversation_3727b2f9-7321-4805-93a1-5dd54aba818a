class Service::ProductsController < ApplicationController
  before_action :authenticate_user!

  public
  def showaps
    @ap_names = g_getMainApNames()
    if (@ap_names.length == 1) then
      redirect_to :controller => "ifate/customer" ,:action => "index" , :ap_name => @ap_names[0]
    end
  end

  protected
  def product_params
    params.require(:product).permit(:product_type, :product_name,:product_version,:product_description,:original_amount,:discount_amount,:product_content,:func_par,:start_date,:end_date,:status,:desc_url)
  end
end
