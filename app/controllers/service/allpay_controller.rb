require("Pm.rb")

class Service::AllpayController < ApplicationController
  # @@PaymentCompany = "neweb"
  @@ween_checkout = ""

  public
  # 若無paymenttype,則會執行2次 dispatchpaymentconfirm
  def dispatchpaymentconfirm
    # render :layout => "ifate"
    dispatchpaymentconfirm_allpay
  end
  def dispatchreceive
    dispatchreceive_allpay
  end
  def dispatchresult
    dispatchresult_allpay
  end
  def dispatchfeedback
    dispatchfeedback_allpay
  end

  protected
  def dispatchpaymentconfirm_allpay
    sOrderno = params["ordernumber"]
    if (sOrderno == nil) then
      sOrderno = makeNewOrderno()
    end
    @paymenttype = params["paymenttype"]
    if (@paymenttype == nil || @paymenttype == "") then
      dispatchpaymentconfirm_allpay_selectpaymenttype(params.clone,sOrderno)
    else
      # 歐付寶
      dispatchpaymentconfirm_allpay_send(params.clone,sOrderno,@paymenttype)
    end
  end

  def dispatchfeedback_allpay
  end

  def dispatchreceive_allpay
  end

  def dispatchresult_allpay
  end

  protected
  def dispatchpaymentconfirm_allpay_selectpaymenttype(hpars,sOrderno)
    # sHttp = getWeenBillingServer("api/v1/supported_paymenttype")
    # res = my_post(sHttp,{})
    # @paymenttypes = JSON.parse(res.body)["supported_paymenttype"]
    # Pm.saveTestDb("device type",request.headers["HTTP_USER_AGENT"])
    @paymenttypes = Allpay_ChoosePayment.supported_paymenttype(request.headers["HTTP_USER_AGENT"])["supported_paymenttype"]
    @hPars = hpars
    @hPars.delete("paymenttype")
    @order_id = @hPars["ordernumber"]
    @paid_amount = @hPars["paid_amount"]
    @paytitle = @hPars["paytitle"]
    @paymemo = ""
  end
  def pan_par_hash2db(h)
    a = h.to_a#.flatten
    return pan_par_join(a)
  end

  def pan_par_join(a)
    s = a.join(",")
    return s
  end

  def dispatchpaymentconfirm_allpay_send(hPars,sOrderno,paymenttype)
    hPars["paymenttype"] = paymenttype
    sHttp2 = getWeenBillingServer("api/v1/allpay_checkout")
    hPars["server_name"] = "meen.tw"
    hPars["orderno"] = sOrderno
    hPars["paymemo"] = hPars["paytitle"]

    res = my_post(sHttp2,hPars)
    @paymentinfo = JSON.parse(res.body)

    @paymentinfo["allpay"].each do |keyvalue|
      if (keyvalue["name"] == "MerchantTradeNo") then
        @order_id = keyvalue["value"]
      end
      if (keyvalue["name"] == "TotalAmount") then
        @paid_amount = keyvalue["value"]
      end
    end
    oOrder = Order.find_by_id(@order_id)
    if (oOrder != nil) then
      oOrder.client_ip = hPars["client_ip"]
      oOrder.save!
    end
  end

  def makeNewOrderno()
    t = Xdate.GetNowRails()
    sOrderno = "%04d%02d%02d%02d%02d%02d" % [t.year,t.month,t.day,t.hour,t.min,t.sec]
    if (Order.find_by_orderno(sOrderno) != nil) then
      return makeNewOrderno()
    end
    return sOrderno
  end
end
