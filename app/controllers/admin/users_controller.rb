module Admin
  class UsersController < Admin::ApplicationController
    def index
      @users = User.all
      if params[:q]
        qstr = "%#{params[:q].downcase}%"
        @users = @users.where('lower(login) LIKE ? or lower(email) LIKE ?', qstr, qstr)
      end
      if params[:type].present?
        @users = @users.where(type: params[:type])
      end
      @users = @users.order(id: :desc).paginate page: params[:page], per_page: 30
    end

    def show
      @user = User.find(params[:id])
    end

    def new
      @user = User.new
      @user._id = nil
    end

    def edit
      @user = User.find(params[:id])
    end

    def create
      @user = User.new(params[:user].permit!)
      @user.email = params[:user][:email]
      @user.login = params[:user][:login]
      @user.state = params[:user][:state]
      @user.verified = params[:user][:verified]

      if @user.save
        redirect_to(admin_users_path, notice: 'User was successfully created.')
      else
        render action: 'new'
      end
    end

    def update
      @user = User.find_login!(params[:id])
      @user.email = params[:user][:email]
      @user.login = params[:user][:login]
      @user.state = params[:user][:state]
      @user.verified = params[:user][:verified]

      if @user.update_attributes(params[:user].permit!)
        redirect_to(edit_admin_user_path(@user.id), notice: 'User was successfully updated.')
      else
        render action: 'edit'
      end
    end

    def destroy
      @user = User.find(params[:id])
      if @user.user_type == :user
        @user.soft_delete
      else
        @user.destroy
      end

      redirect_to(admin_users_url)
    end

  end
end
