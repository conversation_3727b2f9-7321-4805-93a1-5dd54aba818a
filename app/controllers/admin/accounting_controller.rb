module Admin
  class AccountingController < Admin::ApplicationController
    layout 'admin'

    public
    def admin_func
      # render layout: 'admin'
    end

    def admin_func_users
      @func = 'user_customer'
      @user_search_data = params['user_search_data']
      @admin_all_users = admin_find_search_customers(@user_search_data, false)
      # render layout: 'admin'
    end

    def admin_func_users_all
      @func = 'user_customer'
      @admin_all_users = admin_find_search_customers('', true)
      # render layout: 'admin'
    end

    def admin_find_search_customers(sSearchData, bAll = false)
      admin_all_users = if bAll
                          User.order('last_sign_in_at DESC')
                        elsif sSearchData.nil? || sSearchData.empty?
                          # admin_all_users = User.check_sign_in_count(20).order("sign_in_count DESC").first(100)
                          User.order('sign_in_count DESC').first(100)
                        elsif sSearchData[0] == '/'
                          # admin_all_users = User.check_sign_in_count(20).order("sign_in_count DESC").first(100)
                          User.order('id DESC').first(100)
                        else
                          User.check_email(sSearchData).order('sign_in_count DESC').first(100)
                        end
      all_users = []
      admin_all_users.each do |user|
        h = {}
        h['user'] = user
        h['customer_count'] = UserCustomer.check_user_id(user.id).count
        all_users.push(h)
      end
      h_all_users = {}
      h_all_users['admin_all_users'] = all_users
      h_all_users['all_users_count'] = User.count
      h_all_users
    end

    def admin_func_user_search
      dates = params['ajax_data_key']
      hPar = Cfate.pan_par_dbfield2hash(dates)
      @user_search_data = hPar['user_search_data']
      @func = hPar['func']
      @admin_all_users = admin_find_search_customers(@user_search_data, false)

      render partial: 'admin_func_user_list_data'
    end

    def admin_func_user_customer
      @admin_user_id = params['admin_user_id']
      @all_user_customer = UserCustomer.check_user_id(@admin_user_id).order('id DESC') # .limit(nCount.abs)
      # render layout: 'admin'
    end

    def admin_func_user_ap_users
      @func = 'user_ap'
      @user_search_data = params['user_search_data']
      @admin_all_users = admin_find_search_customers(@user_search_data, false)
      # render layout: 'admin'
    end

    def admin_func_user_ap
      @admin_user_id = params[:admin_user_id]
      @all_avail_user_ap = g_getUserAvailAps(@admin_user_id)
      @all_not_avail_user_ap = g_getUserNotAvailAps(@admin_user_id)
      admin_func_successfully_orders_data
      # render layout: 'admin'
    end

    def admin_func_user_ap_edit
      @admin_user_id = params['admin_user_id']
      @user_ap_id = params['user_ap_id']
      @user_ap = UserAp.find(@user_ap_id)
      # render layout: 'admin'
    end

    def admin_func_user_ap_update
      @admin_user_id = params['admin_user_id']
      @user_ap_id = params['user_ap_id']
      admin_func_user_ap_update_data(@user_ap_id, params)
      redirect_to admin_func_user_ap_path(admin_user_id: @admin_user_id)
    end

    protected

    def admin_func_user_ap_update_data(user_ap_id, hPar)
      user_ap = UserAp.find(user_ap_id)
      user_ap.product_name = hPar['product_name']
      user_ap.product_version = hPar['product_version']
      user_ap.product_type = hPar['product_type']
      user_ap.rent_id = hPar['rent_id'].to_i
      user_ap.ap_additionalfunc = hPar['ap_additionalfunc']
      user_ap.gift_days = hPar['gift_days'].to_i
      user_ap.product_id = hPar['product_id'].to_i
      user_ap.start_date = hPar['start_date']
      user_ap.end_date = hPar['end_date']
      user_ap.status = hPar['status'].to_i
      user_ap.save!
    end

    public

    def admin_func_manual_order_users
      @func = 'manual_order'
      @user_search_data = params['user_search_data']
      @admin_all_users = admin_find_search_customers(@user_search_data, false)
      # render layout: 'admin'
    end

    def admin_func_manual_order
      @admin_user_id = params['admin_user_id']
      @user = User.find(@admin_user_id)
      @hProduct, @product_id = admin_func_manual_order_product_all
      product = Product.find(@product_id)
      @product_additional = admin_func_manual_order_product_get_additionals(product.product_content, product.product_name)
      # render layout: 'admin'
    end

    protected

    def admin_func_manual_order_product_all
      h = {}
      bfirst = true
      product_id = 0
      oProducts = Product.check_main.all
      oProducts.each do |p|
        if bfirst
          product_id = p.id
          bfirst = false
        end
        key = "#{p.product_name}_#{p.product_type}_#{p.product_version}_#{p.product_description}_#{p.discount_amount}_#{p.product_content}"
        h[key] = p.id
      end
      [h, product_id]
    end

    public

    def admin_func_manual_order_update
      admin_user_id = params['admin_user_id']
      product_id = params['product_id']
      product = Product.find(product_id)
      products_additional = admin_func_manual_order_product_get_additionals(product.product_content, product.product_name)
      aProdAdd_Buied = []
      products_additional.each do |product|
        sKey = "#{product.product_version}#{product.id}"
        unless params[sKey].nil?
          aProdAdd_Buied.push(product.id)
        end
      end
      gift_days = params['gift_days']
      admin_func_manual_order_new(@user_id, admin_user_id, product, aProdAdd_Buied, gift_days)

      redirect_to admin_func_manual_order_users_path
    end

    def admin_func_manual_order_product_get_additional
      dates = params['ajax_data_key']
      hPar = Cfate.pan_par_dbfield2hash(dates)
      product_id = hPar['product_id']
      product = Product.find(product_id)
      @product_additional = admin_func_manual_order_product_get_additionals(product.product_content, product.product_name)

      render partial: 'admin_func_manual_order_product_get_additional'
    end

    protected

    def admin_func_manual_order_product_get_additionals(ws_dates, ap_name)
      Product.check_additional.check_ws_dates(ws_dates).check_name(ap_name).all
    end

    def admin_func_manual_order_new(_user_id, admin_user_id, product, aProdAdd_Buied, gift_days)
      oOrderme = admin_func_manual_order_new_saveOrdermes(admin_user_id, product, aProdAdd_Buied)
      oRent = admin_func_manual_order_new_save_rent(oOrderme)
      oOrder = admin_func_manual_order_new_save_order(admin_user_id, product, oOrderme)

      oOrderme.billing_order_id = oOrder.id
      oOrderme.status = 0
      oOrderme.save!
      admin_func_manual_order_new_save_userap(oOrderme, product, oRent, gift_days)
    end

    def admin_func_manual_order_getProductAddAmount(aProdAdd_Buied)
      if aProdAdd_Buied.nil?
        return 0, 0
      end
      original_amount_add = discount_amount_add = 0
      aProdAdd_Buied.each do |product_id|
        oProduct = g_getProduct(product_id)
        original_amount_add += oProduct.original_amount
        discount_amount_add += oProduct.discount_amount
      end
      [original_amount_add, discount_amount_add]
    end

    def admin_func_manual_order_new_saveOrdermes(admin_user_id, product, aProdAdd_Buied)
      original_amount_add, discount_amount_add = admin_func_manual_order_getProductAddAmount(aProdAdd_Buied)
      oOrderme = Orderme.new
      oOrderme.user_id = admin_user_id
      oOrderme.product_id = product.id
      oOrderme.original_amount = product.original_amount + original_amount_add
      oOrderme.discount_amount = product.discount_amount + discount_amount_add
      oOrderme.paid_amount = oOrderme.discount_amount
      oOrderme.refund_amount = 0
      oOrderme.ws_dates = product.product_content
      oOrderme.billing_order_id = nil
      oOrderme.buy_type = 'BUY'
      oOrderme.status = -1
      oOrderme.old_userap_id = 0
      oOrderme.save!
      nOrdermeId = oOrderme.id
      admin_func_manual_order_new_saveOrdermeDetail(nOrdermeId, product.id)
      aProdAdd_Buied.each do |pid|
        admin_func_manual_order_new_saveOrdermeDetail(nOrdermeId, pid)
      end
      oOrderme
    end

    def admin_func_manual_order_new_saveOrdermeDetail(nOrdermeId, product_id)
      oProduct = g_getProduct(product_id)

      unless oProduct.nil?
        oOrdermeDetail = OrdermeDetail.new
        oOrdermeDetail.orderme_id = nOrdermeId
        oOrdermeDetail.product_id = product_id
        oOrdermeDetail.product_type = oProduct.product_type
        oOrdermeDetail.product_name = oProduct.product_name
        oOrdermeDetail.product_version = oProduct.product_version
        oOrdermeDetail.product_description = oProduct.product_description
        oOrdermeDetail.original_amount = oProduct.original_amount
        oOrdermeDetail.discount_amount = oProduct.discount_amount
        oOrdermeDetail.paid_amount = oProduct.discount_amount
        oOrdermeDetail.refund_amount = 0
        oOrdermeDetail.save!
      end
    end

    def admin_func_manual_order_new_save_rent(oOrderme)
      oRent = Rent.new
      oRent.user_id = oOrderme.user_id
      oRent.orderme_id = oOrderme.id
      oRent.rent_date = Xdate.GetNowRails()
      oRent.flag_date = Xdate.GetNowRails()
      oRent.flag = oOrderme.buy_type
      oRent.status = 0

      oRent.start_date = Xdate.GetNowRails()
      oRent.end_date = oRent.start_date + oOrderme.howlong
      oRent.save!
      oRent
    end

    def admin_func_manual_order_new_save_userap(oOrderme, oProduct, oRent, gift_days)
      oUserAp = UserAp.new
      oUserAp.user_id = oOrderme.user_id
      oUserAp.product_type = oProduct.product_type
      oUserAp.product_name = oProduct.product_name
      oUserAp.product_version = oProduct.product_version
      oUserAp.product_id = oProduct.id
      oUserAp.start_date = oRent.start_date
      oUserAp.end_date = oRent.end_date
      oUserAp.gift_days = gift_days
      oUserAp.rent_id = oRent.id
      oUserAp.status = 0
      oUserAp.ap_additionalfunc = admin_func_manual_order_new_findAdditionalFunc(nil, oOrderme.id)
      oUserAp.save!
    end

    def admin_func_manual_order_new_findAdditionalFunc(oUserAp, orderme_id)
      # 客戶原先就有的附加功能
      hOrgAddFunc = if oUserAp.nil?
                      {}
                    else
                      Cfate.pan_par_dbfield2hash(oUserAp.ap_additionalfunc)
                    end

      # 附加產品的功能
      hOrderAddFunc = admin_func_manual_order_new_getOrdermeAddFunc(orderme_id)

      hOrgAddFunc.merge!(hOrderAddFunc) { |_key, _v1, v2| v2 }

      Cfate.pan_par_hash2db(hOrgAddFunc)
    end

    def admin_func_manual_order_new_getOrdermeAddFunc(orderme_id)
      hFunc = {}
      orderme_details = OrdermeDetail.check_ordermeid(orderme_id).check_additional.all
      orderme_details.each_with_index do |orderme_detail, _index|
        hProductionFunc = getProductFuncParHash(orderme_detail.product_id, orderme_detail.product_type, orderme_detail.product_name) # in peterapplication
        hProductionFunc.each do |key, value|
          hFunc[key] = value
          hFunc["#{key}_ordermedetailid"] = orderme_detail.id.to_s
        end
      end
      hFunc
    end

    def admin_func_manual_order_new_save_order(admin_user_id, product, orderme)
      server_name = getWebServer
      oOrder = Order.new
      oOrder.server_name = getWebServer
      oOrder.user_id = admin_user_id
      oOrder.orderno = orderme.id
      oOrder.product_id = product.id
      oOrder.original_amount = orderme.original_amount
      oOrder.discount_amount = orderme.discount_amount
      oOrder.paid_amount = orderme.paid_amount
      # oOrder.amount = params[:amount]
      oOrder.payment_type = 'MANUAL'
      oOrder.feedback_url = ''
      oOrder.receive_url = ''
      oOrder.client_ip = request.remote_ip
      oOrder.status = 0
      oOrder.save!

      admin_func_manual_order_new_save_payment(oOrder)
      oOrder
    end

    def admin_func_manual_order_new_save_payment(oOrder)
      oPayment = Payment.new
      oPayment.order_id = oOrder.id
      oPayment.server_name = oOrder.server_name
      oPayment.user_id = oOrder.user_id
      oPayment.orderno = oOrder.orderno
      oPayment.original_amount = oOrder.original_amount
      oPayment.discount_amount = oOrder.discount_amount
      oPayment.paid_amount = oOrder.paid_amount
      oPayment.payment_type = oOrder.payment_type
      oPayment.paid_date = Xdate.GetNowRails()
      oPayment.vendor_srv_data = Cfate.pan_par_hash2db({})
      oPayment.vendor_srv_status = 0
      oPayment.save!
    end

    public

    def admin_func_successfully_orders
      admin_func_successfully_orders_data
      # render layout: 'admin'
    end

    protected

    def admin_func_successfully_orders_data
      @orders = []
      admin_user_id = params['admin_user_id']
      rents = if admin_user_id.nil?
                Rent.available.all.order('created_at DESC')
              else
                # rents = Rent.available.check_userid(admin_user_id).all
                Rent.check_userid(admin_user_id).all.order('created_at DESC')
              end
      rents.each do |rent|
        next if rent.orderme_id.nil?
        h = {}
        h['rent'] = rent
        h['user'] = User.find(rent.user_id)

        h['orderme'] = Orderme.find(rent.orderme_id)
        h['product'] = unless h['orderme'].product_id.nil?
                         Product.find(h['orderme'].product_id)
                       end
        orderme_details = OrdermeDetail.check_ordermeid(rent.orderme_id).all
        h['orderme_details'] = admin_func_successfully_orders_orderme_details(orderme_details)
        h['order'] = if !h['orderme'].billing_order_id.nil? && h['orderme'].billing_order_id > 0
                       Order.find(h['orderme'].billing_order_id)
                     end
        @orders.push(h)
      end
    end

    def admin_func_successfully_orders_orderme_details(orderme_details)
      a = []
      orderme_details.each_with_index do |orderme_detail, _index|
        # if (orderme_detail.product_type == "additional") then
        product = g_getProduct(orderme_detail.product_id)
        unless product.nil?
          a.push(product.product_description)
        end
        # end
      end
      a.join(',')
    end

    public

    def admin_func_user_successfully_orders_users
      @func = 'successfully_orders'
      @user_search_data = params['user_search_data']
      @admin_all_users = admin_find_search_customers(@user_search_data, false)
      # render layout: 'admin'
    end
  
    def admin_func_all_orders
      count = params["count"].to_i
      @orders = []
      ods = if (count == 0) then
              Order.check_servername("meen").user_id_legal.all.order('created_at DESC')
            else
              Order.check_servername("meen").user_id_legal.limit(count).order('created_at DESC')
            end
      ods.each do |order|
        next if order.user_id.nil?
        h = Hash.new
        h['order'] = order
        h['user'] = User.find(order.user_id)
        h['orderme'] = Orderme.find(order.orderno)
        # h['product'] = unless h['orderme'].product_id.nil?
        #                   Product.find(h['orderme'].product_id)
        #                 end
        orderme_details = OrdermeDetail.check_ordermeid(order.orderno).all
        h['orderme_details'] = admin_func_successfully_orders_orderme_details(orderme_details)
  
        @orders.push(h)
      end
      @order_msg = ["success","failed in ecpay","failed in meen","MacValueError"]
    end

    # ween
    def admin_func_ween_successfully_orders
      admin_func_ween_successfully_orders_data
      # render layout: 'admin'
    end
    def admin_func_ween_all_orders
      count = params["count"].to_i
      @orders = []
      ods = if (count == 0) then
              Order.check_servername("Profate").all.order('created_at DESC')
            else
              Order.check_servername("Profate").limit(count).order('created_at DESC')
            end
      ods.each do |order|
        h = Hash.new
        h['order'] = order
  
        @orders.push(h)
      end
      @order_msg = ["success","failed in ecpay","failed in meen","MacValueError"]
    end

    protected

    def admin_func_ween_successfully_orders_data
      sHttp = getWeenBillingServer('api/v1/profate_finish_orders')
      res = my_post(sHttp, {})
      @profate_orders = JSON.parse(res.body)
    end

  end
end
