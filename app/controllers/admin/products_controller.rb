module Admin
  class ProductsController < Admin::ApplicationController
    before_action :authenticate
    layout 'admin'

    protected
    def authenticate
      @ap_name = params[:ap_name] == nil ? "" : params[:ap_name]
    end

    public
    def index
      # @products = Product.unscoped.all.reverse
      @products = Product.unscoped.check_name(@ap_name).all
    end

    def edit
      @product = Product.unscoped.find(params[:id])
    end

    def show
      @product = Product.unscoped.find(params[:id])
    end

    def new
      @product = Product.new
    end

    # POST /products
    # POST /products.json
    def create
      @product = Product.new(product_params)

      if @product.save
        redirect_to(admin_products_path, notice: 'Product was successfully created.')
      else
        render action: 'new'
      end
    end

    # PUT /users/1
    # PUT /users/1.json
    def update
      @product = Product.unscoped.find(params[:id])

      if @product.update_attributes(product_params)
        redirect_to(admin_products_path(ap_name: @ap_name), notice: 'Product was successfully updated.')
      else
        render action: 'edit'
      end
    end

    def delete
      @product = Product.unscoped.find(params[:id])
    end

    # DELETE /users/1
    # DELETE /users/1.json
    def destroy
      @product = Product.unscoped.find(params[:id])
      @product.destroy

      redirect_to(admin_products_path(ap_name: @ap_name))
    end

    private
    def product_params
      params.require(:product).permit(:product_type, :product_name, :product_version, :product_description, :original_amount, :discount_amount, :product_content, :func_par, :start_date, :end_date, :status, :desc_url)
    end
  end
end
