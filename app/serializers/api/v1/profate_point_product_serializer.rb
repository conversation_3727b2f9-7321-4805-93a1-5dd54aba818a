# class Api::V1::ProfatePointProductSerializer < ActiveModel::Serializer
#   attributes :id, :p_name, :p_type, :p_desc, :amount, :point, :free_coupon, :start_date, :end_date, :desc_url, :status
# end


class Api::V1::ProfatePointProductSerializer
  include FastJsonapi::ObjectSerializer
  attributes :id, :p_name, :p_type, :p_desc, :amount, :point, :free_coupon, :start_date, :end_date, :desc_url, :status
end