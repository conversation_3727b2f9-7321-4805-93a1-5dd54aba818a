# class Api::V1::ProfateCouponSerializer < ActiveModel::Serializer
  # attributes :id, :uuid, :start_date, :end_date, :use_date, :remote_ip, :buy_user_id, :profate_order_id, :create_user_id, :send_user_id, :use_user_id, :flag
# end


class Api::V1::ProfateCouponSerializer
  include FastJsonapi::ObjectSerializer
  attributes :id, :uuid, :start_date, :end_date, :use_date, :remote_ip, :buy_user_id, :profate_order_id, :create_user_id, :send_user_id, :use_user_id, :flag
end