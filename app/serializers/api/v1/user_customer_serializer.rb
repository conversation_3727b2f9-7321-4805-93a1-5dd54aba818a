# class Api::V1::UserCustomerSerializer < ActiveModel::Serializer
#   attributes :id, :user_id, :name, :sex, :blood, :created_at, :updated_at, :email, :cust_userid, :flag, :wyear, :wmonth, :wday, :hour, :minute, :eyear, :eleap, :emonth, :eday, :address, :phone, :userclass, :calType
# end


class Api::V1::UserCustomerSerializer
  include FastJsonapi::ObjectSerializer
  attributes :id, :user_id, :name, :sex, :blood, :created_at, :updated_at, :email, :cust_userid, :flag, :wyear, :wmonth, :wday, :hour, :minute, :eyear, :eleap, :emonth, :eday, :address, :phone, :userclass, :calType
end