# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/

# rightbar 上的event
is_parent = (element_c,element_p) ->
  element = element_c
  while (element)
    return true if (element.id == element_p.id)
    element = element.offsetParent if ((element.id != element_p.id))

  return false

is_gong_parent = (gong_dizhi,element_c) ->
  return true if gong_dizhi == 0
  sId = "earth" + gong_dizhi.toString()
  gong = document.getElementById(sId)
  return is_parent(element_c,gong)

tiangan_value = 0

# 要被網頁中的event 呼叫的前面要加上 @ ,彼此呼叫的不必加
@xuexi_tiangan_over = (gong_dizhi,tiangan) ->
  if tiangan_value != 0
    if tiangan_value != tiangan || gong_value != gong_dizhi
      xuexi_tiangan_out()
      xuexi_tiangan_hover(gong_dizhi,tiangan)
    else
      xuexi_tiangan_out()
  else
    xuexi_tiangan_hover(gong_dizhi,tiangan)

xuexi_tiangan_hover = (gong_dizhi,value) ->
  sId = "housesky" + value.toString()
  tiangans = getElementsByName(sId)
  sClass = 'housesky_hover'
  for tiangan in tiangans
    tiangan.className = sClass if is_gong_parent(gong_dizhi,tiangan)
  tiangan_value = tiangan

xuexi_tiangan_out = () ->
  sId = 'housesky'
  xuexi_tiangan_original(sId + j.toString()) for j in [1..10]
  tiangan_value = 0

xuexi_tiangan_original = (sId) ->
  tiangans = getElementsByName(sId)
  sClass = 'housesky'
  tiangan.className = sClass for tiangan in tiangans

dizhi_value = 0

@xuexi_dizhi_over = (value) ->
  if dizhi_value != 0
    if dizhi_value != value
      xuexi_dizhi_out()
      xuexi_dizhi_hover(value)
    else
      xuexi_dizhi_out()
  else
    xuexi_dizhi_hover(value)

xuexi_dizhi_hover = (value) ->
  sId = "houseearth_pos_" + value.toString()
  dizhi = document.getElementById(sId)
  sClass = 'houseearth_pos_over'
  dizhi.className = sClass
  dizhi_value = value

xuexi_dizhi_out = () ->
  sId = 'houseearth_pos_'
  xuexi_dizhi_original(sId + j.toString()) for j in [1..12]
  dizhi_value = 0

xuexi_dizhi_original = (sId) ->
  dizhi = document.getElementById(sId)
  sClass = 'houseearth_pos'
  dizhi.className = sClass

wuxing_value = 0
@xuexi_wuxing_over = (gong_dizhi,value) ->
  if wuxing_value != 0
    if wuxing_value != value || gong_value != gong_dizhi
      xuexi_wuxing_out()
      xuexi_wuxing_hover(gong_dizhi,value)
    else
      xuexi_wuxing_out()
  else
    xuexi_wuxing_hover(gong_dizhi,value)

xuexi_wuxing_hover = (gong_dizhi,value) ->
  sId = "housefive" + value.toString()
  wuxings = getElementsByName(sId)
  sClass = 'housefive_pos_hover'
  for wuxing in wuxings
    wuxing.className = sClass if is_gong_parent(gong_dizhi,wuxing)
  wuxing_value = value

xuexi_wuxing_out = () ->
  sId = 'housefive'
  xuexi_wuxing_original(sId + j.toString()) for j in [1..5]
  wuxing_value = 0

xuexi_wuxing_original = (sId) ->
  wuxings = getElementsByName(sId)
  sClass = 'housefive_pos'
  wuxing.className = sClass for wuxing in wuxings

gong_value = 0
@xuexi_gong_over = (value) ->
  if gong_value != 0
    if gong_value != value
      xuexi_gong_out()
      xuexi_gong_hover(value)
    else
      xuexi_gong_out()
  else
    xuexi_gong_hover(value)

xuexi_gong_hover = (value) ->
  highlight_gong(0,value,true)
  gong_value = value

find_gong = (value) =>
  sId = "pan_flow_houseinfo_housename_flow_" + value.toString()
  gong = document.getElementById(sId)
  if (gong is null)
    sId = "pan_normal_houseinfo_housename_" + value.toString()
    gong = document.getElementById(sId)
  return gong

highlight_gong = (bg34idx,dizhi,hightlight) =>
  sId = "bg34_" + bg34idx.toString()
  objgong = document.getElementById(sId)
  objgong.className = sId + "_" + dizhi.toString() if hightlight
  objgong.className = sId + "_" + 0.toString() if !hightlight

xuexi_gong_out = () ->
  highlight_gong(0,gong_value,false)
  gong_value = 0

# zhuxingxi
xings_str = ""
@xuexi_ziewi_xings_over = (objThis,new_xings_str) ->
  if xings_str != ""
    if xings_str != new_xings_str
      xuexi_ziewi_xings_out()
      xuexi_ziewi_xings_hover(new_xings_str)
    else
      xuexi_ziewi_xings_out()
  else
    xuexi_ziewi_xings_hover(new_xings_str)

xuexi_ziewi_xings_hover = (new_xings_str) ->
  new_zhuxingxi_xings = new_xings_str.split(",")
  for new_zhuxingxi_xing in new_zhuxingxi_xings
    xuexi_ziewi_xing_hover(new_zhuxingxi_xing)
  xings_str = new_xings_str

xuexi_ziewi_xing_hover = (sId) =>
  xing = document.getElementById(sId)
  if (xing != null)
    xing.className = xing.className + '_hover'

xuexi_ziewi_xings_out = () ->
  zhuxingxi_xings = xings_str.split(",")
  for zhuxingxi_xing in zhuxingxi_xings
    xing = document.getElementById(zhuxingxi_xing)
    if (xing != null)
      s = xing.className
      xing.className = xing.className.replace("_hover","")
  xings_str = ""

@xuexi_ziwei_xings_click = (objThis,xuexi_data,new_xings_str,shuoming_id) ->
  xuexi_ziwei_click(objThis,xuexi_data,shuoming_id)

gongs_value = []
@xuexi_gongs_over = (value,gongs,show_svg) ->
  if gong_value != 0
    if gong_value != value
      xuexi_gongs_out()
      xuexi_gongs_hover(value,gongs,show_svg)
    else
      xuexi_gongs_out()
  else
    xuexi_gongs_hover(value,gongs,show_svg)

xuexi_gongs_hover = (value,gongs,show_svg) ->
  xuexi_gong_hover(value)
  i = 0
  for gong in gongs
    i = i + 1
    highlight_gong(i,gong,true)
  svg_34_show(value,gongs,show_svg)
  gongs_value = gongs

xuexi_gongs_out = () ->
  xuexi_gong_out()
  i = 0
  for gong in gongs_value
    i = i + 1
    highlight_gong(i,gong,false)
  gongs_value = []
  svg_34_hide()

@xuexi_gongstr_over = (gongstr) ->
  gongs = gongstr.split(",")
  xuexi_gongs_over(gongs[0],gongs.slice(1),false)

pt34 = [[0,0],[75,100],[25,100],[0,100],[0,75],[0,25],[0,0],[25,0],[75,0],[100,0],[100,25],[100,75],[100,100]]
svg_34_show = (dizhi,dizhis,show_svg) ->
  dizhis2 = []
  dizhis2.push(dizhi)
  dizhis2 = dizhis2.concat(dizhis)
  svg_34_show2(dizhis2) if (show_svg)

svg_34_show2 = (dizhis) ->
  if (dizhis.length >= 4)
    svg_34_show2_line(0,dizhis[0],dizhis[1])
    svg_34_show2_line(1,dizhis[1],dizhis[2])
    svg_34_show2_line(2,dizhis[2],dizhis[0])
    svg_34_show2_line(3,dizhis[0],dizhis[3])

svg_34_show2_line = (svgidx,dizhi1,dizhi2) ->
  svg_obj = 'svg_34_' + svgidx.toString()
  pt1 = pt34[dizhi1]
  pt2 = pt34[dizhi2]
  x1 = svg_34_percent_value(pt1[0])
  y1 = svg_34_percent_value(pt1[1])
  x2 = svg_34_percent_value(pt2[0])
  y2 = svg_34_percent_value(pt2[1])
  svg_show(svg_obj,x1,y1,x2,y2)

svg_34_percent_value = (value) =>
  s = value.toString()
  s = s + "%"
  return s

svg_34_hide = () ->
  svg_34_hide2(0)
  svg_34_hide2(1)
  svg_34_hide2(2)
  svg_34_hide2(3)

svg_34_hide2 = (i) ->
  svg_obj = 'svg_34_' + i.toString()
  svg_show(svg_obj,0,0,0,0)

# 宮干 宮支 宮五行
@xuexi_gong_gdw_over = (gong_dizhi,which_gdw,gdw_value) ->
  xuexi_tiangan_over(gong_dizhi,gdw_value) if (which_gdw == "tiangan")
  xuexi_dizhi_over(gdw_value) if (which_gdw == "dizhi")
  xuexi_wuxing_over(gong_dizhi,gdw_value) if (which_gdw == "wuxing")
  # 宮一定要最後，因為天干，五行有重覆的問題，要靠宮的不同來判斷
  xuexi_gong_over(gong_dizhi)

@xuexi_gong_xing_over = (gong_dizhi,xing_value) ->
  xuexi_ziewi_xings_over(gong_dizhi,xing_value)
  xuexi_gong_over(gong_dizhi)

@xuexi_ziwei_bdl_click = (objThis,xuexi_data,gongs,shuoming_id) ->
  xuexi_ziwei_click(objThis,xuexi_data,shuoming_id)

@xuexi_ziwei_click = (objThis,xuexi_data,shuoming_id) ->
  sData = ""
  sData = sData + addItem("xuexi_data",xuexi_data,false)
  sData = sData + addItem("end","end",true)
  # ajax_div("/xuexi_ziwei_xuexi_ajax",sData,shuoming_id)
  ajax_div("/xuexi_ziwei_xuexi_ajax",sData,"#xuexi_shuoming")

@xuexi_shuoming_click = (objThis,xuexi_data,gong,xings) ->
  xuexi_change_pan(objThis,xuexi_data)

@xuexi_gong_xing_out_over = (gong_dizhi,xing_value) ->
  xuexi_ziewi_xings_out()
  xuexi_gong_out()
  xuexi_ziewi_xings_over(gong_dizhi,xing_value)
  xuexi_gong_over(gong_dizhi)

xuexi_change_pan = (objThis,xuexi_data) ->
  sData = ""
  sData = sData + addItem("xuexi_data",xuexi_data,false)
  sData = sData + addItem("end","end",true)
  ajax_div("/xuexi_ziwei_pan_ajax",sData,"#ists_content")

@lianxi_ziwei_click = (objThis,xuexi_class,xuexi_item,mudi,arr_of_hashs,div_name) ->
  sData = ""
  sData = sData + addItem("xuexi_class",xuexi_class,false)
  sData = sData + addItem("xuexi_item",xuexi_item,true)
  sData = sData + addItem("mudi",mudi,true)
  sData = sData + addItem(arr_of_hash["name"],arr_of_hash["value"],true) for arr_of_hash in arr_of_hashs
  sData = sData + addItem("end","end",true)
  ajax_div("/xuexi_ziwei_lianxi_ajax",sData,div_name)


@xuexi_ziwei_lianxi_answer = (objThis,xuexi_class,xuexi_item,shuoming_id,objType) ->
  lianxi_id = document.getElementById("lianxi_id").value
  xuaxiong_length = document.getElementById("xuaxiong_length").value
  arr_of_hash = []
  arr_of_hash.push(xuexi_make_hash("lianxi_id",lianxi_id))
  if (objType == "checkbox")
    s = get_checkbox_xuaxiong_values(xuaxiong_length)
  else
    s = get_radiobox_xuaxiong_value_str()
  arr_of_hash.push(xuexi_make_hash("answer",s))
  lianxi_ziwei_click(objThis,xuexi_class,xuexi_item,"jieguo",arr_of_hash,shuoming_id)

Array::unique = ->
  output = {}
  output[@[key]] = @[key] for key in [0...@length]
  value for key, value of output

get_checkbox_xuaxiong_values = (xuaxiong_length) ->
  s = []
  for i in [1..xuaxiong_length]
    s.push(i) if get_checkbox_xuaxiong_value(i)
  name = "xuaxiong[]"
  objs = getElementsByName(name)
  for obj in objs
    s.push(obj.value) if (obj.checked == true)
  return s.unique().join(",")

get_checkbox_xuaxiong_value = (i) ->
  name = "submit_data[xuaxiong_" + i.toString() + "]"
  objs = getElementsByName(name)
  s = []
  for obj in objs
    s.push(obj.value) if (obj.checked == true)
  return s.length > 0

get_radiobox_xuaxiong_value_str = () ->
  objs = getElementsByName("submit_data[xuaxiong]")
  s = []
  for obj in objs
    s.push(obj.value) if (obj.checked == true)
  objs = getElementsByName("xuaxiong")
  for obj in objs
    s.push(obj.value) if (obj.checked == true)
  return s.unique().join(",")

@xuexi_ziwei_lianxi_next = (objThis,xuexi_class,xuexi_item) ->
  lianxi_ziwei_click(objThis,xuexi_class,xuexi_item,"timu",[],"#xuexi_data")

@chuangguan_ziwei_click = (objThis,xuexi_class,xuexi_item,mudi,arr_of_hashs,div_name) ->
  sData = ""
  sData = sData + addItem("xuexi_class",xuexi_class,false)
  sData = sData + addItem("xuexi_item",xuexi_item,true)
  sData = sData + addItem("mudi",mudi,true)

  sData = sData + addItem(arr_of_hash["name"],arr_of_hash["value"],true) for arr_of_hash in arr_of_hashs

  sData = sData + addItem("end","end",true)
  ajax_div("/xuexi_ziwei_chuangguan_ajax",sData,div_name)

@xuexi_ziwei_chuangguan_jieguo = (objThis,xuexi_class,xuexi_item,shuoming_id,objType) ->
  chuangguan_id = document.getElementById("chuangguan_id").value
  xuaxiong_length = document.getElementById("xuaxiong_length").value
  arr_of_hash = []
  arr_of_hash.push(xuexi_make_hash("chuangguan_id",chuangguan_id))
  if (objType == "checkbox")
    s = get_checkbox_xuaxiong_values(xuaxiong_length)
  else
    s = get_radiobox_xuaxiong_value_str()
  arr_of_hash.push(xuexi_make_hash("answer",s))
  dijiti = document.getElementById("dijiti").value
  arr_of_hash.push(xuexi_make_hash("dijiti",dijiti))
  chuangguan_ziwei_click(objThis,xuexi_class,xuexi_item,"jieguo",arr_of_hash,shuoming_id)

@xuexi_ziwei_chuangguan_nextti = (objThis,xuexi_class,xuexi_item) ->
  arr_of_hash = []
  chuangguan_id = document.getElementById("chuangguan_id").value
  arr_of_hash.push(xuexi_make_hash("chuangguan_id",chuangguan_id))
  dijiti = document.getElementById("dijiti").value
  arr_of_hash.push(xuexi_make_hash("dijiti",dijiti))
  chuangguan_ziwei_click(objThis,xuexi_class,xuexi_item,"timu",arr_of_hash,"#xuexi_data")

@xuexi_ziwei_chuangguan_jieguo_nextitem = (objThis,xuexi_class,xuexi_item) ->
  arr_of_hash = []
  chuangguan_id = document.getElementById("chuangguan_id").value
  arr_of_hash.push(xuexi_make_hash("chuangguan_id",chuangguan_id))
  arr_of_hash.push(xuexi_make_hash("nextitem",true))
  chuangguan_ziwei_click(objThis,xuexi_class,xuexi_item,"timu",arr_of_hash,"#xuexi_data")

@xuexi_ziwei_chuangguan_jieguo_onemore = (objThis,xuexi_class,xuexi_item) ->
  arr_of_hash = []
  chuangguan_ziwei_click(objThis,xuexi_class,xuexi_item,"timu",arr_of_hash,"#xuexi_data")

@xuexi_ziwei_sihua_tiangan_click = (objThis,xuexi_data,tiangan,tiangan_astar_sihua,shuoming_id) ->
  sId = "housesky" + tiangan.toString()
  tiangans = getElementsByName(sId)
  # HouseSkyMouseOver2(tiangans[0],tiangan_astar_sihua,true)
  HouseSkyMouseUp(tiangans[0],tiangan_astar_sihua,true)
  xuexi_ziwei_click(objThis,xuexi_data,shuoming_id)


# pan 上的 event
# 宮位的事件
@Xuexi_gong_click = (objThis,xuexi_class,xuexi_item,nPanType,house_id,timestamp) ->
  arr_of_hash = []
  arr_of_hash.push(xuexi_make_hash("event_type","gong"))
  arr_of_hash.push(xuexi_make_hash("xing",xing))
  arr_of_hash.push(xuexi_make_hash("house_id",house_id))
  arr_of_hash.push(xuexi_make_hash("PanType",nPanType))
  arr_of_hash.push(xuexi_make_hash("timestamp",timestamp))
  xuexi_pan_click(objThis,xuexi_class,xuexi_item,arr_of_hash)

xuexi_make_hash = (name,value) ->
  {"name":name, "value": value}

# 星的事件
@Xuexi_xing_click = (objThis,xuexi_class,xuexi_item,nPanType,xing,xingming,xingxing,timestamp) ->
  arr_of_hash = []
  arr_of_hash.push(xuexi_make_hash("event_type","xing"))
  arr_of_hash.push(xuexi_make_hash("xing",xing))
  arr_of_hash.push(xuexi_make_hash("xingming",xingming))
  arr_of_hash.push(xuexi_make_hash("xingxing",xingxing))
  arr_of_hash.push(xuexi_make_hash("PanType",nPanType))
  arr_of_hash.push(xuexi_make_hash("timestamp",timestamp))
  xuexi_pan_click(objThis,xuexi_class,xuexi_item,arr_of_hash)

xuexi_pan_click = (objThis,xuexi_class,xuexi_item,arr_of_hashs) ->
  sData = ""
  sData = sData + addItem("xuexi_class",xuexi_class,false)
  sData = sData + addItem("xuexi_item",xuexi_item,true)
  sData = sData + addItem(arr_of_hash["name"],arr_of_hash["value"],true) for arr_of_hash in arr_of_hashs
  sData = sData + addItem("end","end",true)
  # ajax_div("/xuexi_ziwei_pan_event_ajax",sData,"#explain_block")
  # overlay()

@ziwei_kecheng_change_video_link = (obj,video_url,link_url,link_text) ->
  v = document.getElementById("ziwei_kecheng_video")
  if(navigator.appName == "Microsoft Internet Explorer")
    v.src = video_url
    v.contentWindow.location.reload(true)
  else
    v2 = document.getElementById("ziwei_kecheng_video1")
    v.src = v2.src
