@import "vars";

.subnav {
  margin-bottom:-18px;
  .nav-tabs {
    border-bottom:0px;
    padding-left:20px;
  }
  .nav-tabs > li > a:hover {
    border-color:transparent;
    background:none;
    text-decoration:underline;
  }
  .nav-tabs > .active > a,.nav-tabs > .active > a:hover {
    color: #555555;
    background-color: #ffffff;
    border: 1px solid #ddd;
    border-bottom-color: transparent;
    cursor: default;
  }
}

.page-users {
  .nav-tabs {
    border-bottom: 0px;
  }
}

.node-topics {
  border-bottom:1px solid #ddd;
  tbody > tr > td { padding: 8px; color: #666; }
  td.title {
    a:link,
    a:visited { font-size: 15px; text-decoration: none; }
    a:hover { text-decoration: underline; }
    em { font-style:normal; font-size:12px; color:#bbb; }
    i.icon { margin-bottom: -1px; }
  }
  td.node {
    a { color: #666; }
  }
  tr.head {
    td { border-top:none; padding-top:14px; color:#CCC; font-weight:bold; font-size:12px; }
  }
  tr.odd {
    td { background: #fafafa; }
  }
  tr.topic {
    td.author {
      width:80px;
      a { color:#666; font-weight:bold; }
    }
  }
}

.recent-topics {
  ul {
    li {
      .title {
        font-size:14px;
      }
      i.icon { margin-bottom: -1px; }
      .info { margin-top:3px; font-size:12px; color:#bbb; }
      .node {
        margin-right:5px;
        a { color:#444; }
      }
    }
  }
}

.recent-replies {
  padding: 0 15px;
  margin-bottom: 0;
  li {
    &.list-group-item {
      border-radius: 0 !important;
      border-left: 0px;
      border-right: 0px;
      padding: 10px 0;
      border-color: #ddd;
    }
    &.list-group-item:even {
      background: #f9f9f9;
    }
    .title {
      font-size:15px;
      .info { font-size:12px; color:#bbb; }
    }
    .body {
      a { color: #666; }
      margin-top:6px; color: #666;
      p { font-size:13px; }
      img { max-width:680px; }
    }
  }
}


.row > .span13 { margin-left:0;}
#main .userinfo h1 { text-align: left; display:inline; }
.userinfo {
  .tagline { text-align:left; margin-top:-8px; margin-bottom:20px;}
  .media-right {
    padding-left: 15px;
    text-align: center;
    .avatar {
      margin-bottom: 10px;
    }
  }
  .list-group { margin-bottom: 0px; }
  li {
    border-color: #f0f0f0;
    font-size:13px;
    label { color:#999; margin-right:8px; display:inline-block; width:80px; text-align: right;}
  }

  .panel-footer {
  }
}

.bio {
  font-size:12px; line-height:180%;
  p:last-child { margin-bottom:0; }
}

.replies ul {
  margin:0;
  h6 { color:#999; font-weight:normal; }
  li { line-height:180%; border-bottom:1px solid #ddd; list-style: none;}
  blockquote { line-height:160%; }
}

.content > .tabs {
  border-bottom:2px solid #ccc;
  .active { margin-bottom:0;}
}

table.node-topics {
  td {
    a { color:#333; font-weight:normal; }
  }
  td.replied-at { width:80px; }
}

.user-list {
  h2 { font-size:14px; margin:0; }
  .user {
    text-align:center; margin-bottom:20px;
    overflow: hidden;;
    .avatar {
      img { width:48px; height:48px; margin: 0 auto;}
      margin-bottom: 5px;
    }
    .name {
      a { color: #333; }
    }
  }
}

.bloced-users {
  .item {
    text-align: left; margin-bottom: 10px;
    .media-object { display: inline; }
  }
}

.sidebar {
  padding-right: 0;
  .profile {
    .avatar {
      .level { margin-top: 6px; text-align: center; }
    }
    .item { margin-bottom: 5px; }
    .item a { color: #666; }
    .number { color: #999; }
    .counts {
      color: #999;
      span { color: #666; }
    }
    .follow-info {
      border-top: 1px solid #f0f0f0;
      text-align: center; margin-top: 15px; padding-top: 15px;
      a { display:block; text-decoration: none; }
      a.counter {
        font-size: 32px; color: $blue;
        &:hover { color: $blueLight; }
      }
      a.text {
        color: #999;
      }
    }
    .buttons {
      border-top: 1px solid #f0f0f0;
      margin-top: 15px; padding-top: 15px;
    }
    .social {
      font-size: 18px;
      a { color: #999; margin-right: 8px; }
      a:hover { color: #666; }
    }
    .tagline {
      border-top: 1px solid #f0f0f0;
      margin-top: 10px;
      color: #999;
      line-height: 100%;
      padding: 10px; padding-bottom: 0;
    }
  }

  .user-teams {
    .media-object { display: inline-block; margin:4px 2px; }
  }
}

.user-card {
  margin-bottom: 15px;
  padding-left: 15px;
  .media-heading {
    font-weight:bold;
    a { color: #333; }
  }
  .infos {
    color:#999; font-size: 12px;
    .item { margin-top: 5px; }
  }
}

#user_github_repos {
  .more { text-align:right; }
  ul { margin:0; }
  li {
    .title {
      position:relative; margin-bottom: 5px;
      a { color:#333; font-weight:bold; }
      .watchers { position:absolute; top:2px; right:0; color: #999; }
    }
    .desc { font-size:12px; color:#888; padding: 0; margin: 0; }
  }
}

.user-activity-graph {
  overflow-x: scroll;
  text-align: center;
  svg { margin: 0 auto; }
}
