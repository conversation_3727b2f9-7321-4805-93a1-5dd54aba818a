/*
  *= require font-awesome
  * require highlight
  * require jquery.fluidbox
  *= require awesome-bootstrap-checkbox
  *= require animate
  *= require toastr.min
  *= require ./ifate/eightword
  *= require ./ifate/eightword_print
  *= require ./ifate/star
  *= require ./ifate/star_print
  *= require ./ifate/star_lin
  *= require ./ifate/star_print_lin
  *= require ./ifate/xdate
  *= require ./ifate/personalevent
  *= require ./ifate/calendar
  *= require ./ifate/liuyaogua
  *= require ./xuexi/ziwei
  *= require ./xuexi/ziweipan
  *= require_self
  *= require tweaks
*/

@import "bootstrap.min";
@import "vars";

@mixin clearfix() {
  &:before,
  &:after {
    content: " ";
    display: table;
  }
  &:after {
    clear: both;
  }
  & {
    zoom: 1;
  }
}

textarea,
#preview {
  &::-webkit-scrollbar {
    width: 12px;
  }

  &::-webkit-scrollbar-track {
  }

  &::-webkit-scrollbar-thumb {
    background: #f0f0f0;
    border: 3px solid #fff;
    border-radius: 9px;
  }
}

/* Bootstrap Theme */
body {
  background: #f0f0f0;
  color: rgba(33, 33, 33, 1);
  font-family: Helvetica, Arial, "PingFang TC", "Hiragino Sans GB",
    "Source Han Sans TWHK", "Noto Sans TC", "Roboto", "Heiti TC",
    "Microsoft Jhenghei", sans-serif;
  // padding-top: 65px;
}

.header {
  .navbar {
    background-color: #fff;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.11);
    margin-bottom: 15px;
    border: 0px;
    color: $blueText;
    z-index: 1030;
    .fa-reorder {
      color: $red;
      line-height: 1.5;
    }
  }
  //
  .navbar-brand {
    color: #666 !important;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-weight: bold;
    margin-left: 0 !important;
    // text-shadow: rgba(158,158,158,1) 1px 1px 2px !important;
    b {
      color: $red;
    }
  }
  .navbar-default .navbar-brand,
  .navbar-default .navbar-nav > li > a {
    color: #333;
    border-bottom: 3px solid #fff;
    padding-bottom: 12px;
  }
  .navbar-default .navbar-nav > li > a:active {
    background-color: $grayDark;
    border-bottom-color: #999;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
    color: #999 !important;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu li.active a {
    border-bottom: 0px;
    color: $red;
  }
  .navbar-inverse .navbar-toggle {
    color: #333;
    padding: 3px 9px;
    font-size: 18px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(255, 255, 255, 0.05);
  }
  .navbar-inverse .navbar-toggle:focus {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .navbar-inverse .navbar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .navbar-inverse .navbar-collapse,
  .navbar-inverse .navbar-form {
    border-color: $blueText;
  }
  nav.navbar-collapse.bs-navbar-collapse {
    border-color: rgba(255, 255, 255, 0.3);
  }
  .navbar-inverse .navbar-nav > .open > a,
  .navbar-inverse .navbar-nav > .open > a:focus,
  .navbar-inverse .navbar-nav > .open > a:hover {
    background: #fff;
    color: #333;
  }
  // .navbar-inverse .navbar-nav .open .dropdown-menu { position: absolute; left: auto; right: 0; background: #FFF; }
  .navbar-inverse .navbar-nav .open .dropdown-menu {
    background: #fff;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: #333;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
    background-color: #f0f0f0;
  }
  .navbar-nav {
    li.active a,
    li.active a:focus,
    li.active a:hover {
      color: $red;
      background: transparent;
    }
  }

  #main-nav-menu {
    .navbar-nav {
      margin: 0px;

      a {
        color: #333;
        transition: all 0.2s ease-in-out;
      }
      li a:hover {
        color: $red;
      }
    }
    .navbar-nav > li > a:hover,
    .navbar-nav > li.active > a,
    .navbar-nav > li.active > a:focus,
    .navbar-nav > li.active > a:hover {
      border-bottom: 3px solid transparent;
      color: $red;
      background: transparent;
      padding-bottom: 12px;
    }
  }
  //
  .user-bar {
    li > a {
      padding-left: 11px !important;
      padding-right: 11px !important;
    }
    .dropdown-avatar {
      margin-right: 6px;
      .dropdown-toggle {
        padding-top: 14px !important;
        padding-bottom: 9px !important;
      }
    }
    .navbar-toggle {
      display: none;
      margin: 0;
      padding: 6px 9px;
    }
    .avatar-32 {
      display: inline-block;
      width: 24px;
      height: 24px;
      opacity: 0.6;
    }
  }
  //
  @media (max-width: 767px) {
    .navbar-brand {
      margin-left: 1em !important;
    }

    .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a,
    .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus,
    .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover {
      background: #fff !important;
    }

    .navbar-nav > li.active > a:hover,
    .navbar-inverse .navbar-nav .open .dropdown-menu li.active a {
      border-bottom: 0;
    }
    .navbar-header {
      text-align: center;
    }
    .user-bar .dropdown.open .navbar-toggle {
      background-color: $grayDark;
    }
  }
}

.sub-navbar {
  padding: 18px 0;
  background: #f9f9f9;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.11);
  margin-top: -14px;
  margin-bottom: 15px;
}

.sidebar.col-md-3 {
  padding-left: 0;
}

.dropdown-menu {
  border-color: #fff;
  border-radius: 2px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.15);
}

a {
  color: #333;
}
a:hover {
  color: #303030;
}

.btn {
  border-radius: 3px;
  border: 1px solid #ccc;
  padding: 5px 12px;
  outline: 0 !important;
}

.btn-default,
.btn-default:visited {
  background: #fff;
  border-color: #dadada;
}
.btn-default:hover {
  background: #f9f9f9;
  border-color: #ddd;
}
.btn-default.active {
  background: #f0f0f0;
  border-color: #d0d0d0;
}
.btn-primary,
.btn-primary:visited {
  background: $blue;
  border-color: $blueDark;
  color: #fff;
}
.btn-primary:hover {
  background: $blueLight;
  border-color: $blue;
}
.btn-primary.active {
  background: $blueDark;
  border-color: $blueDark;
}
.btn-danger,
.btn-danger:visited {
  background: $red;
  border-color: $redDark;
}
.btn-danger:hover {
  background: $redLight;
  border-color: $red;
}
.btn-danger.active {
  background: $redDark;
  border-color: $redDark;
}
.btn-warning,
.btn-warning:visited {
  background: $yellow;
  border-color: $yellow;
}
.btn-warning:hover {
  background: $yellowLight;
  border-color: $yellowLight;
}
.btn-warning.active {
  background: $yellowDark;
  border-color: $yellowDark;
}
.btn-success,
.btn-success:visited {
  background: $green;
  border-color: $greenDark;
}
.btn-success:hover {
  background: $greenLight;
  border-color: $green;
}
.btn-success.active {
  background: $greenDark;
  border-color: $greenDark;
}
.open > .dropdown-toggle.btn-primary {
  background: #0059c7;
}

.navbar-btn {
  background: rgba(255, 255, 255, 0.1);
  color: $blueText;
  border: 0px;
}
.navbar-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: $blueText;
}

.label {
  font-weight: normal;
  border-radius: 2px;
  padding: 2px 4px;
}
.label-default {
  background: $grayLabel;
  color: $grayLabelText;
}
.label-primary {
  background: $blue;
}
.label-warning {
  background: $yellowLabel;
  color: $yellowLabelText;
}
.label-info {
  background: $blueLabel;
  color: $blueLabelText;
}
.label-danger {
  background: $redLabel;
  color: $redLabelText;
}
.label-success {
  background: $greenLabel;
  color: $greenLabelText;
}

.alert-success {
  color: $greenLabel;
}
.alert-info {
  color: $blueLabel;
}
.alert-warning {
  color: $yellowLabelText;
  hr {
    border-color: #dac264 !important;
  }
}
.alert-danger {
  color: $redLabel;
}

.text-success {
  color: $green;
}
.text-primary {
  color: $blue;
}
.text-warning {
  color: $yellow;
}
.text-danger {
  color: $red;
}

.control-label {
  color: #333;
  font-weight: normal;
}
.control-label.checkbox {
  text-align: left;
}
.form-control {
  border-radius: 3px;
  border-color: #ddd;
  box-shadow: inset 0 0 0px rgba(0, 0, 0, 0) !important;
  transition: none;
  -webkit-appearance: none;
  &:focus {
    box-shadow: inset 0 0px 0px #fff;
    border-color: $blueLight;
  }
  &[disabled],
  &[readonly] {
    background: #f0f0f0;
  }
}
textarea.form-control,
#preview.form-control {
}
#preview.form-control {
  border-radius: 0px 3px 3px 3px;
}
textarea.form-control,
#preview.form-control,
textarea.form-control:focus {
  padding: 6px;
  border-bottom-width: 1px;
}
#preview.form-control {
  display: none;
}
.editor-toolbar {
  .nav-pills {
  }
  .nav-pills > li > a {
    border-radius: 3px 3px 0 0;
    padding: 3px 23px;
    margin-right: 5px;
    background-color: #f0f0f0;
    color: #999;
  }
  .nav-pills > li.active > a:link,
  .nav-pills > li.active > a:visited,
  .nav-pills > li.active > a:hover {
    background-color: #d0d0d0;
    color: #666;
  }
}
textarea.topic-editor {
  border-radius: 0px 3px 3px 3px;
  &:focus {
    border-color: #ddd;
  }
}
form {
  #preview {
    min-height: 500px;
    overflow-y: scroll;
  }
  .help-block {
    font-size: 12px;
    color: #aaa;
    margin-bottom: 0px;
  }
  .form-actions {
  }
}

.table-bordered > thead > tr > td,
.table-bordered > thead > tr > th,
.table > tr > th {
  border-bottom-width: 1px;
  background: #f0f0f0;
  color: #888;
  font-weight: normal;
  font-size: 14px;
}
.table-striped > tr:nth-of-type(odd) {
  background: #fff;
}
.table-striped > tr:nth-of-type(even) {
  background: #f9f9f9;
}

.input-group-addon {
  background: #f0f0f0;
  border-color: #ddd;
  color: #999;
  padding: 0 15px;
}

.input-group-btn > .btn {
  padding: 6px 12px;
  &:focus {
    background: #fff;
    border-color: #ccc;
  }
}
.input-group-btn:first-child > .btn {
  border-right: 0px;
}

.input-group-captcha {
  background: #fff;
  border-radius: 3px 0 0 3px;
  img {
    border-radius: 3px 0 0 3px;
  }
}

.popover {
  border-radius: 3px;
  border-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.18);
}
.popover > .arrow {
  border-bottom-color: #f0f0f0 !important;
}

.popover-content {
  padding: 15px;
}

.alert {
  padding: 5px 10px 3px;
  margin-bottom: 15px;
  border: 0px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
  border-radius: 0px;
  font-size: 13px;
  transform: scale(1, 1);
  transition: all 0.3s;

  strong {
    color: $redDark;
  }

  & > ul {
    padding: 5px 18px;
    list-style-type: square;
  }
  a.close {
    font-size: 14px;
    text-shadow: 0 0 0;
  }
}

.nav-tabs {
  li:first-child {
    margin-left: 15px;
  }
}

.pagination {
  margin: 0;
  & > li:first-child > a,
  & > li:first-child > span {
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
  }

  & > li:last-child > a,
  & > li:last-child > span {
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
  }
  li > a {
    color: #777;
  }

  li > a,
  .disabled > a,
  li > span {
    border-color: #e0e0e0 !important;
  }

  li > a:hover {
    color: #555;
    background: $gray;
  }

  li.active > a,
  li.active > a:hover {
    background-color: #cfdffc;
    border-color: #bdcfef !important;
    color: $blue;
  }
}
.pager {
  margin: 0px;
  .info {
    line-height: 32px;
    color: #ccc;
    samp {
      color: #999;
    }
  }
  li > a,
  li > span {
    color: #666;
    border-radius: 3px;
    border: 0px;
    background: transparent;
    &:hover {
      background: #fff;
    }
  }
  li.disabled > a,
  li.disabled > span {
    color: #ddd;
    background: transparent;
    &:hover {
      color: #ddd;
      background: transparent;
    }
  }
}
abbr[title] {
  border-bottom: 0px;
  cursor: text;
}

kbd {
  background-color: #f5f5f5;
  color: #999;
  border-radius: 2px;
  border-color: #fafafa;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

.table > tbody > tr > td,
.table > tbody > tr > th,
.table > tfoot > tr > td,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > thead > tr > th {
  padding: 4px 5px;
}

.panel {
  border: 0px;
  border-radius: 3px;
  border: 1px;
  border-color: #e5e6e9 #dfe0e4 #d0d1d5;
  margin-bottom: 15px;
  /*  box-shadow: 0 0px 3px 2px rgba(0,0,0,0.03); */

  .panel-heading {
    background: #fafafa;
    padding: 6px 15px;
    border-bottom-color: #eee;
    color: #777;
  }
  .panel-footer {
    padding: 6px 15px;
    border-top-color: #eee;
    background: #fafafa;
  }
}

.nav-stacked {
  margin-bottom: 15px;
}
.nav-stacked.nav-pills > li > a {
  text-align: right;
  border-radius: 3px 0 0 3px;
  box-shadow: 0 0px 1px rgba(0, 0, 0, 0.09);
  padding: 5px 15px;
  margin-bottom: 0px;
  background: #fff;
  border-right: 2px solid $blackLight;
  color: #666;
}
.nav-stacked.nav-pills > li > a:hover {
  border-color: $blueLight;
  color: $blueLight;
  background-color: #fff;
}
.nav-stacked.nav-pills > li.active > a,
.nav-stacked.nav-pills > li.active > a:focus,
.nav-stacked.nav-pills > li.active > a:hover {
  color: $red;
  background-color: #fff;
  border-color: $redLight;
}

/* Modal */
.modal {
  .modal-content {
    h4.title {
      margin-bottom: 20px;
      font-size: 15px;
    }
    border: 0px;
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15),
      0px 0px 1px 1px rgba(0, 0, 0, 0.05);

    p {
      margin-bottom: 5px;
    }
  }
  .modal-header {
    border: 0px;
    padding-bottom: 0px;
    .close {
      margin-top: -8px;
      font-weight: normal;
      outline: none !important;
    }
    .modal-title {
      font-size: 16px;
    }
  }
  .modal-footer {
    border-color: #e5e5e5;
  }
}

.modal-backdrop.in {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

.list-group {
  .list-group-item {
    border-color: #eee;
  }
}

.fa-spin {
  -webkit-animation: fa-spin 0.8s infinite linear;
  animation: fa-spin 0.8s infinite linear;
}

/* App Style */
.opts {
  color: #666;
  a {
  }
  a:link,
  a:visited {
    color: #666;
    padding: 3px;
    text-decoration: none;
  }
  a:hover {
    background: #f0f0f0;
    color: #666;
    text-decoration: none;
  }
  .loading {
  }
}

.turbolinks-progress-bar {
  background-color: $red !important;
  height: 2px !important;
}

.pull-right.opts {
  a {
    margin-left: 5px;
    margin-right: 0px;
  }
}
.avatar {
  .uface,
  .media-object {
    border-radius: 120px;
  }
}
.avatar-16 {
  width: 16px;
  height: 16px;
  border-radius: 120px;
}
.avatar-32 {
  width: 32px;
  height: 32px;
  border-radius: 120px;
}
.avatar-48 {
  width: 48px;
  height: 48px;
  border-radius: 120px;
}
.avatar-96 {
  width: 96px;
  height: 96px;
  border-radius: 120px;
}
@media (max-width: 480px) {
  .avatar-48 {
    width: 32px;
    height: 32px;
  }
}

.uname {
  color: #666;
}
img.emoji {
  width: 20px;
  height: 20px;
}
.node-name {
  background: #f0f0f0;
  padding: 1px 3px;
  color: #777;
  margin-right: 5px;
  &:hover {
    color: #555;
    text-decoration: none;
    background: #e0e0e0;
  }
}
.fa.awesome {
  font-size: 13px;
  color: $red;
}

.opts a.active {
  .fa {
    @extend .animated;
    @extend .bounceIn;
    color: #e76f3c;
  }
}

@media (max-width: 480px) {
  body {
    padding-top: 50px;
  }
  .container {
    padding: 0;
  }
  .panel {
    border-radius: 0;
  }
  .row {
    margin: 0;
    .col-md-9,
    .col-md-8,
    .col-md-6,
    .col-md-5,
    .col-md-4,
    .col-md-3 {
      padding: 0;
    }
  }
  .hidden-mobile {
    display: none !important;
  }
  .pagination {
    display: block;
    li {
      display: none;
    }
    li.prev,
    li.next {
      float: left;
      display: block;
      a {
        border-radius: 20px !important;
      }
    }
    li.next {
      float: right;
    }
  }
}
