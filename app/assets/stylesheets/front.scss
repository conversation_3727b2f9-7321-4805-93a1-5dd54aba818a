/*
 *= require application
 *= require home
 *= require users
 *= require_self
 */

@import "vars";

.node-list {
  .node {
    margin-bottom: 10px;
    margin-top: 0px;
    &:last-child { margin-bottom: 0px; }
    .media-left { min-width: 130px; }
    label { font-weight: normal; color: #aaa; text-align: right; }
    .name {
      margin-bottom: 10px;
      width: 100px;
      display: block;
      float: left;
      text-align: left;
      a:link,a:visited { color: #333; }
    }
  }
}

.topics {
  .panel-body {
    padding: 0 15px;
  }
  .node-info {
    .name {
      strong {
        font-weight: bold; color: #666;
        text-align: left;
        font-size: 16px; padding-bottom: 10px;
      }
      .total { font-size: 12px; font-weight: normal; margin-left: 8px; }
      .button-block-node { font-size: 13px; border:1px solid #e0e0e0; }
      .button-block-node.active { background-color: #e0e0e0; border-radius: #d0d0d0; }
    }
    .summary {
      color: #888;
      h1,h2,h3,h4,h5 { font-size: 14px; }
      font-size: 13px;
    }
  }
  .filter {
    padding: 3px 0 2px 0;
    font-size: 13px;
    color: #ccc;
    span.l { color: #bbb; margin-right: 5px; }
    a:link,a:visited { color: #333; margin: 0 3px; }
    a:hover { text-decoration:underline; }
    a.popular { color: $red; }
    a.active { color: #333; border-bottom: 1px dotted #ccc; text-decoration: none; }
    a.popular.active { color: $red; border-bottom-color: $red;}
  }
  .topics-group:first-child {
    @media (max-width: 991px) {
      .topic:last-child { border-bottom: 1px solid #F0F0F0; }
    }
  }
  .topic {
    min-height: 68px;
    border-bottom: 1px solid #F0F0F0;
    padding: 10px 15px;
    margin: 0 -15px;
    vertical-align: top;
    &:last-child { border-bottom: 0px; }
    .avatar { text-align:center; }
    .title {
      font-size: 15px;
      margin-bottom: 0;
      a:link,
      a:visited { color: #222; font-weight: 400; line-height: 30px; }
      i.fa { color: #999; margin-left: 3px; }
      i.fa-diamond { color: $red; }
      i.fa-check { color: $green; }
    }

    .info {
      color: #ABA8A6; font-size: 12px; margin-top: 0;
      a { color: #ABA8A6; }
    }

    .count {
      width: 100px; text-align: right; padding-top: 15px;
      a:link,
      a:hover,
      a:visited {
        line-height: 11px;
        color: #fff;
        min-width: 32px;
        text-align: center;
        border-radius: 80px; padding: 3px 8px 3px 8px;
        display: inline-block;
        text-decoration: none;
      }

      a:link { background: #CFD3E6; }
      a:hover { background: #A9BBDC; }
      a.state-true,
      a:visited { background: #f0f0f0; }
    }
  }
}

.topic-detail {
  margin-bottom: 15px;
  .panel-heading {
    padding: 15px;
    transition: all .3s;
    h1 {
      margin-top:0; font-size:24px; color:#333;text-align: left; line-height:100%; margin-bottom:8px;
      i.fa-check { color: $green; font-size: 16px; }
    }
    .avatar { text-align:right; }
  }

  .label-awesome {
    font-size: 13px;
    background: #FCF8F7;
    padding: 5px 15px;
    border-bottom: 1px solid #f3f0f0;
    color: #eb5424;

    a { color: #aAa5a4; }
  }
  .label-nopoint {
    font-size: 13px;
    background:#FCF8E3;
    padding: 3px 15px;
    border-top: 1px solid #FAEBCC;
    color: #ae938B;

    a { color: #ae938B; }
  }

  .info {
    color:#c0c0c0;
    font-size:12px;
    a { color: #444; }
    .node {
      color: #999;
      font-weight: bold;
    }
    .user-name { color: $blue; font-size: 13px; }
    .team-name {
      font-size: 13px;
      color: $green;
    }
    em { font-style:normal; }

    .opts {
      a {
        margin-left: 5px; color: #999;
        &:hover { color: #333; }
      }
    }
  }
}

#topic-sidebar {
  position: fixed;
  display: none;
  width: 260px;

  @media (min-width: 960px) {
    display: block;
    width: 242px;
  }

  @media (min-width: 1200px) {
    display: block;
   width: 292px;
  }

  .group {
    text-align: center; margin-bottom: 20px;
  }

  .buttons {
    margin-top: 20px;
    .likes {
      a { display: block; width: 90px; margin: 0 auto; border-radius: 5px; padding: 10px 0;}
      a:link,a:hover,a:visited { text-decoration: none; color: #333; }
      a:hover { background: rgba(0,0,0,0.03); }
      i.fa { display: block; font-size: 40px;  margin-bottom: 10px; color: #666; }
      a.active {
        i.fa { color: #e76f3c; }
      }
    }
  }
  .reply-buttons {
    text-align:center;
    .total { margin-bottom: 10px; }
  }
  a.btn-move-page { color: #666; }
}

#replies {
  margin-bottom: 15px;
  .panel-body {
    padding-top: 0px;
    padding-bottom: 0px;
  }
  .info {
    .uname { color: #777; font-weight: bold; }
    .opts {
      a {
        font-size: 13px;
        margin-left: 5px; color: #999;
        &:hover { color: #333; }
      }
      a.edit { display: none; }
    }
  }
  .reply {
    margin: 0 -15px;
    padding: 15px 15px;
    position: relative;
    border-bottom: 1px solid #eee;
    padding-left: 74px;
    &.reply-system,
    &.reply-deleted {
      padding: 8px 15px;
      font-size: 12px;
      color: #666;
      border-bottom: 1px solid #F0F0F0;

      img.media-object {
        border-radius: 180px;
        display: inline-block; margin-right: 3px; vertical-align: text-bottom;
      }

      .time { margin-left: 4px; color: #aaa; }
    }
    .infos { min-height: 48px; }
    .avatar { position: absolute; top: 15px; left: 15px; }
    &:last-child {
      border-bottom: 0px;
    }
    &.none {
      text-align: center;
      color: #999;
      min-height: 32px;
    }
    &.light { background:#F7F2FC; }
    &.popular { background:#fffce9; }

    .info {
      .name {
        font-weight: bold;
        font-size: 13px;
        a { color: #555; }
      }
      color: #999;
      margin-bottom: 6px;
      font-size: 12px;
      .reply-floor { color: #7AA87A; }
    }

    .opts {
      a {
        display: inline-block;
        vertical-align: baseline;
        line-height: 22px;
        padding: 2px 5px;
        height: 22px;
        min-width: 22px;
        text-align: center;
      }
    }

    .markdown {
      pre {
        margin-right: 0px;
        margin-left: 0px;
      }
    }

    @media (min-width: 1026px) {
      .hideable { display: none; }
    }
    &:hover {
      .hideable { display: inline-block; }
    }
  }

  @media (max-width: 480px) {
    .reply {
      padding-left: 57px;
    }
  }
}

#node-selector {
  .panel { box-shadow: 0 0 0; padding: 0; margin: 0; }
  .panel-heading { display: none; }
  .panel-body { padding: 0 20px; margin: 0; }
}

#site_nodes .site {
  margin-bottom: 10px;
  a { color: #777; }
}

.sidebar {
  .panel-body {
    word-break: break-all;
  }
}


.api-doc {
  .route-list {
    padding: 20px 0;
    border-right: 2px dashed #ddd;
    li {
      line-height: 200%;  color:#999;
      a:link,
      a:visited { color: #404040 !important; text-decoration: underline !important; }
    }

  }
  .route {
    margin-top: 15px;
    h5 {
      color: #333;
      border-bottom: 1px solid $gray;
      margin: 0; margin-bottom: 10px;
      padding: 5px 0 0 0;
      label {
        font-size: 12px;
        font-weight: normal; display: inline-block; width: 50px; color: $blackLight;
      }
    }
    .content { margin: 0 15px; }
    .desc {
      h4 { border: 0px; font-size: 13px !important; margin:0; color: #999; }
    }
    h6 { color: #999; }
    table.params {
      td.field { width: 80px; }
      td.type { width: 70px; }
      td.required { width: 50px; }
      td.values { width: 180px; }
      td.default { width: 100px; }

    }
  }
}

// Fix searchbox style
.bs-searchbox .form-control {
  float: none;
}

@media (min-width: 744px) and (max-width: 1200px) {
  .sidebar .panel .panel-body .feed-button {
    float: none !important;
    margin-top: 15px;
  }
}
/* Social Share Button */
.social-share-button {
  height: 16px;
  a {
    i.fa { font-size: 24px; margin: 0 4px; }
    &:link,&:visited { color: #777; }
    &:hover {
      color: $blueLight;
    }
  }
}
.popover-content {
  .social-share-button { display: block; }
}

/* Markdown Styles */
.markdown {
  position:relative;
  line-height: 1.8em; font-size:14px; text-overflow: ellipsis; word-wrap: break-word;
  font-family: "PingFang SC","Hiragino Sans GB",Helvetica,Arial,"Source Han Sans CN",Roboto,"Heiti SC","Microsoft Yahei",sans-serif !important;
  img { max-width: 100%; }
  p,
  pre,
  ul,
  ol,
  blockquote { margin-bottom: 16px; }

  p { font-size: 14px; line-height: 1.5em; }
  hr { border:2px dashed $gray; border-bottom:0px; margin: 18px auto; width:50%; }
  blockquote {
    margin: 0 18px 15px 18px;
    padding: 0;
    padding-left: 32px;
    border: 0px;
    quotes: "\201C""\201D""\2018""\2019";
    position: relative;
    line-height: 1.45;
    p { display:inline; color: #999; }
    &:before,
    &:after {
      display: block;
      content: "\201C";
      font-size: 35px;
      position: absolute;
      font-family: serif;
      left: 0px;
      top: 0px;
      color: #aaa;
    }
  }
  pre {
    font-family: Menlo,Monaco,"Courier New",monospace;
    font-size: 12px;
    background-color: #f9f9f9;
    border: 0px;
    border-top: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    margin: 0 -15px 15px -15px;
    padding: 5px 15px;
    color: #444;
    overflow: auto;
    border-radius: 0px;
    code {
      display: block;
      line-height: 150%;
      padding: 0!important;
      font-size: 12px!important;
      background-color: #f9f9f9 !important;
      border: none!important;
    }
  }
  p:last-child,
  blockquote:last-child,
  pre:last-child { margin-bottom:0; }
  pre::-webkit-scrollbar {
    height: 8px;
    width: 8px; }

  pre::-webkit-scrollbar-thumb:horizontal {
    width: 25px;
    background-color: #ccc;
    -webkit-border-radius: 4px; }

  pre::-webkit-scrollbar-track-piece {
    margin-bottom: 10px;
    background-color: #e5e5e5;
    border-bottom-left-radius: 4px 4px;
    border-bottom-right-radius: 4px 4px;
    border-top-left-radius: 4px 4px;
    border-top-right-radius: 4px 4px; }

  pre::-webkit-scrollbar-thumb:vertical {
    height: 25px;
    background-color: #ccc;
    -webkit-border-radius: 4px;
    -webkit-box-shadow: 0 1px 1px white; }

  code {
    display: inline-block;
    font-size: 12px!important;
    background-color: #f5f5f5 !important;
    border: 0px;
    color: #444 !important;
    padding: 1px 4px !important;
    margin: 2px;
    border-radius: 3px;
    word-break: break-all;
    line-height: 20px;
    font-family:Monaco,Menlo,"Courier New",monospace;
  }
  a:link,
  a:visited {
    color:#0069D6 !important; text-decoration: none !important;
  }
  a:hover { text-decoration: underline !important; color:#00438A !important; }
  a.mention-floor { color:#60b566 !important; margin-right: 3px;  }
  a.mention {
    color:#777 !important; font-weight: bold;
    margin-right: 2px;
    b { color:#777 !important; font-weight: normal; }
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight:bold; text-align:left;
    margin-top: 10px !important; margin-bottom: 16px;
  }
  h1 { font-size: 26px !important; text-align: center; margin-bottom: 30px !important; }
  h2,
  h3,
  h4 {
    text-align: left;
    font-weight: bold;
    font-size: 16px !important;
    line-height: 100%;
    margin: 0; color: #555;
    margin-top: 16px; margin-bottom:16px;
    border-bottom:1px solid #eee;
    padding-bottom: 5px;
  }
  h2 { font-size: 20px !important; border-bottom-width: 2px; padding-bottom: 15px; margin-top: 20px; margin-bottom:20px; color: #111; }
  h3 { font-size: 18px !important; padding-bottom: 10px; margin-top: 20px; margin-bottom: 20px; color: #333; }
  h5,h6 { font-size: 15px; line-height: 100%; color: #777; }
  h6 { font-size: 14px; color: #999; }

  strong { color:#000; }
  ul,
  ol {
    list-style-position: inside;
    list-style-type: square;
    margin:0;
    margin-bottom: 20px;
    padding:0px 20px;
    p,
    blockquote,
    pre { margin-bottom:8px; }
    li { line-height:1.6em; padding:2px 0; color:#333; }
    ul { list-style-type: circle; margin-bottom: 0px; }
  }
  ol {
    list-style-type: decimal;
    ol { list-style-type: lower-alpha; margin-bottom: 0px; }
  }
  img { vertical-align: top; max-width: 100%; }
  a.zoom-image { cursor: zoom-in; }
  a.at_floor { color: #60B566 !important; }
  a.at_user,a.user-mention { color: #0069D6 !important; }
  // img.twemoji { width: 20px; }
}


footer {
  margin-top: 10px; margin-bottom: 20px;
  color: $blackLight;
  a { color: #666; }
  .links { color: #ddd; }
  .socials {
    a { font-size: 20px; margin-right: 8px; }
  }
}


.profiler-results { display: none !important; }
@media (min-width: 1100px) {
  .profiler-results { display:block !important; }
  .profiler-results.profiler-left {
    top: 80px !important;
    position: absolute !important;
    background-color: transparent;
  }
  .profiler-results .profiler-button,.profiler-results .profiler-controls {
    border-radius: 0px !important;
    background-color: transparent !important;
    color: $grayDark !important;
    border: 0px !important;
  }
  .profiler-result .profiler-number { color: #aaa !important;}
  .profiler-result .profiler-unit { color: #ccc !important;}
  .profiler-results .profiler-popup {

  }
}

.notify-updated {
  display: none;
  padding: 4px 15px;
  margin-bottom: 20px;
  text-align: left;
  background: #FDF8A6; border:1px solid #F5E3A4; color: $redLabelText;
  a:link,
  a:visited { color: $yellowText; }
}

.dz-preview { display: none; }
// textarea.div-dropzone-focus { border-color: #BBE1C9; background: #fafafa; }

// .emoji-modal {
//   .modal-dialog {
//     max-width: 496px;
//     .close { margin-top: 0px; }
//   }
//   .modal-header { border: 0px; padding: 8px; }
//   .modal-body { padding: 0 8px 8px 8px; }
//   .twemoji { width: 20px; height: 20px; }

//   .nav > li > a {
//     padding: 5px 8px;
//   }

//   .nav-tabs li:first-child { margin-left: 8px; }

//   .tab-pane {
//     padding: 0px;
//     height: 180px;
//     overflow: scroll;

//     &::-webkit-scrollbar {
//       width: 4px;
//       border-radius: 3px;
//     }
//     &::-webkit-scrollbar-thumb {
//       background: #e0e0e0;
//     }

//     a {
//       padding: 5px;
//       display: inline-block; width: 30px; height: 30px;
//       &:hover { background: #f0f0f0; }
//     }
//   }
//   .modal-footer {
//     padding: 8px;
//     text-align: left;
//     font-size: 16px;
//     .emoji { width: 48px; height: 48px; margin-right: 10px; }
//   }
// }
