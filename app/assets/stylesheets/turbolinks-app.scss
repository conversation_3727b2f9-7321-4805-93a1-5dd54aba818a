@import "vars";

$primary: #e23c28;
$primary-dark: #af2913;

.hide-ios {
  display: none !important;
}

body {
  padding-top: 0;
  background: #e9e3e3;
}

.btn-primary {
  background: $primary !important;
  border-color: $primary-dark !important;

  &.active,
  &:active {
    background: $primary-dark !important;
    border-color: $primary-dark !important;
  }
}

.btn-danger {
  background: #555 !important;
  border-color: #333 !important;

  &:active {
    background: #333 !important;
    border-color: #111 !important;
  }
}

.footer,
.header,
.index-locations,
.index-sections {
  display: none;
}

#node-selector .panel {
  display: block;
  padding: 0;
  box-shadow: 0 0 0;
  border: 0;

  .panel-body {
    border: 0;
  }

  .media-left {
    display: none;
  }
}

.container {
  padding: 0;
}

.sidebar {
  padding: 0;
  display: none;
}

#main > .row {
  margin: 0;

  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-8,
  .col-md-9 {
    padding: 0;
  }
}

.panel {
  border-radius: 0;
  margin-bottom: 0 !important;
  box-shadow: 0 0 0;
  border-top: 0 solid #f0f0f0;
  border-bottom: 0 solid #ece2d7;

  &:last-child {
    border-bottom: 0;
  }

  .panel-footer,
  .panel-heading {
    background: #fffbf8;
    border-color: #ece2d7;
    border-radius: 0 !important;
  }
}

.alert {
  padding: 10px;
  font-size: 14px;
  border-top: 0;
  margin-bottom: 0;

  .close {
    opacity: .4;
    margin-top: 2px;
  }
}

.alert-success {
  background: #c3ffb8;
  border-color: #b6f3b8;
  color: #00a21c;
}

.alert-warning {
  background: #fff8b8;
  color: #CD8546;
  border-color: #faebcc;
}

.form-control {
  -webkit-appearance: none;

  &:focus {
    border-color: #ccc;
  }
}

.pagination {
  display: none;
}

.markdown {
  pre {
    margin-left: -15px;
    margin-right: -15px;

    code {
      margin: 8px;
    }
  }
}

.topics {
  .topic-list-heading {
    display: none;
  }
}

.editor-toolbar {
  .dropdown-menu {
    left: auto;
    right: 0;
  }
}

.topic-editor {
  height: 140px;
}

.no-result {
  display: none;
}

.new_reply {
  .submit-buttons {
    #reply-button {
      display: block;
      width: 100%;
    }

    .help-inline,
    .pull-right {
      display: none;
    }
  }
}

.edit-reply {
  .panel-heading {
    display: none;
  }

  .col-xs-2 {
    display: block;
    width: 50%;
  }
}

.topics {
  .topic {
    min-height: 60px;

    &:active {
      background: #f5f5f5;
    }

    .avatar {
      padding-top: 3px;
    }

    .title {
      margin-bottom: 0;

      a {
        font-size: 14px;
        line-height: 22px !important;
      }

      a:active,
      a:hover {
        text-decoration: none;
      }
    }

    .info {
      a.node {
        color: #909090;
      }
    }

    &.topic-visited {
      .title {
        a:link,
        a:visited {
          color: #666;
        }
      }

      .info {
        color: #bbb;

        a {
          color: #bbb;
        }
      }

      .count {
        a:link,
        a:visited {
          color: #ddd;
        }
      }
    }

    .count {
      padding-top: 0;
      padding-left: 0;
      width: auto;
      text-align: right;

      a:hover,
      a:link,
      a:visited {
        line-height: 11px;
        min-width: 28px;
        text-align: right;
        border-radius: 80px;
        padding: 0;
        display: inline-block;
        text-decoration: none;
      }

      a:link {
        background: transparent;
        color: #666;
      }

      a:hover {
        background: transparent;
      }

      a.state-true,
      a:visited {
        background: transparent;
        color: #ddd;
      }
    }
  }
}

.topic-detail {
  .label-awesome {
    padding: 10px 15px;
    margin-top: -1px;
    background: #fff2c4;
    border: 0;
    color: $primary;

    .fa.awesome {
      color: $primary;
    }

    a {
      color: #cccaa2;
    }
  }

  .panel-heading {
    h1 {
      font-size: 18px;
    }
  }

  .info {
    color: #999;

    .user-name {
      color: #666;
    }

    .node {
      color: $primary;
    }
  }

  .markdown {
    .zoom-image {
      overflow: hidden;
      display: block;
      margin: 8px -15px;

      img {
        width: 100%;
      }
    }

    pre {
      background: #fffefc !important;
      border-color: #f9f4f0 !important;

      code {
        background: #fffefc !important;
      }
    }
  }
}

.opts {
  a:link,
  a:visited {
    color: #444 !important;
  }

  a.active {
    .fa {
      color: $primary;
    }
  }
}

.page-topics {
  .panel {
    &#replies,
    &#reply,
    &.topic-detail {
      border-bottom-width: 1px;
    }
  }
}

.page-pages {
  .wiki-sidebar {
    display: block;
  }
}

.page-users {
  .sidebar {
    display: block;

    .profile {
      .opts {
        display: none;
      }
    }
  }

  #user_github_repos {
    display: none;
  }

  .nav-tabs {
    margin-top: 15px;

    li > a {
      border: 0;
      padding: 8px;
    }

  }

  .node-topics {
    border-bottom: 1px solid #ddd;
  }
}
