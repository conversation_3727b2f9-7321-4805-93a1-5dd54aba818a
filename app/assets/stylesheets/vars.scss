$black: rgba(33,33,33 ,1);
$blackLight: #9CA4A9;

$grayLabel: #EBEDEE;
$gray: #F0F4F6;
$grayDark: #EEF3F5;
$grayLabelText: #9A9DA0;

$red: rgba(244,67,54 ,1);
$redLight: rgba(229,115,115 ,1);
$redDark: rgba(211,47,47 ,1);
$redText: rgba(183,28,28 ,1);
$redLabel: rgba(255,23,68 ,1);
$redLabelText: rgba(183,28,28 ,1);

$yellow: rgba(255,235,59 ,1);
$yellowLight: rgba(255,241,118 ,1);
$yellowDark: rgba(251,192,45 ,1);
$yellowText: rgba(245,127,23 ,1);
$yellowLabel: rgba(255,234,0 ,1);
$yellowLabelText: rgba(245,127,23 ,1);

$blue: rgba(33,150,243 ,1);
$blueLight: rgba(100,181,246 ,1);
$blueDark: rgba(25,118,210 ,1);
$blueText: rgba(13,71,161 ,1);
$blueLabel: rgba(41,121,255 ,1);
$blueLabelText: rgba(13,71,161 ,1);

$green: rgba(76,175,80 ,1);
$greenLight: rgba(129,199,132 ,1);
$greenDark: rgba(56,142,60 ,1);
$greenText: rgba(27,94,32 ,1);
$greenLabel: rgba(0,230,118 ,1);
$greenLabelText: rgba(27,94,32 ,1);

/* Animations */
.animated {
  -webkit-animation-duration: .5s;
  animation-duration: .5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
@-webkit-keyframes bounceIn {
  0% {
    opacity: 0;
    -webkit-transform: scale(.5);
  }

  50% {
    opacity: 1;
    -webkit-transform: scale(1.5);
  }

  70% {
    -webkit-transform: scale(.9);
  }

  100% {
    -webkit-transform: scale(1);
  }
}
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(.5);
  }

  50% {
    opacity: 1;
    transform: scale(1.5);
  }

  70% {
    transform: scale(.9);
  }

  100% {
    transform: scale(1);
  }
}

.bounceIn {
  animation-name: bounceIn;
}
