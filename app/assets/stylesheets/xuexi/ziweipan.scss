// Place all the styles related to the xuexi/ziweipan controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import "ifate/color_font";
@import "ifate/commonfunc";

#xuexi-ziweipan {
  padding-top: 60px;
  height: 100%;
  // display: flex;
  font-size: 16px;
  @media (max-width: 980px) {
    font-size: 15px;
  }
  @media (max-width: 768px) {
    font-size: 14px;
  }
  @media (max-width: 640px) {
    font-size: 13px;
  }
  @media (max-width: 480px) {
    font-size: 12px;
    padding: 5px 0 0;
    margin-right: 0;
    margin-left: 0;
    // transform: scale(.8);
  }

  // as absolute divs' parent,should be declared as relative explicitly.
  .ists_xuexi {
    position: relative;
    height: 47em;
    width: 68em;
    margin: auto;
  }

  #tooltip {
    position: absolute;
    height: 22em;
    width: 22em;
    bottom: 1.1em;
    left: 1em;
    overflow-y: auto;
    z-index: 5;
    color: #fff;
    background-color: rgba(0,0,0,.6);

    a {
      color: #fff;
    }

    .list-group {
      width: 90%;
      margin: auto;
    }

    .list-group-item {
      background-color: transparent;
    }

    .prev {
      position: absolute;
      top: 5px;
      left: 5px;
    }

    .next {
      position: absolute;
      top: 5px;
      right: 5px;
    }

  }

  #ists_header {
    position: absolute;
    top: 0;
    left: 1em;
    // width: 69.2em;
    height: 2em;
    background-color: transparent;
    text-align: center;
    width: auto;
  }

  #ists_main {
    position: absolute;
    top: 1.8em;
    left: 1em;
    // width: 70.2em;
    height: 44em;
    background-color: transparent;
    line-height: 1.2em;
    width: auto;
  }

}

// #div_hint {
//   //visibility: hidden;
//   visibility: visible;
//   -webkit-box-shadow: 0 4px 16px rgba(0,0,0,.2);
//   -moz-box-shadow: 0 4px 16px rgba(0,0,0,.2);
//   box-shadow: 0 4px 116px rgba(0,0,0,.2);
//   width: makeEm(10);
//   height: makeEm(10);
//   background: white;
//   background-clip: padding-box;
//   border: 1px solid #ACACAC;
//   border: 1px solid rgba(0,0,0,.333);
//   outline: 0;
//   position: absolute;
//   z-index: 101;
//   line-height : makeEm(1.2);
//
//   background-color: #ffffff;
//   border: 1px solid #73a7f0;
//   width: 200px;
//   height: 100px;
//   margin-left: 32px;
//   position:relative;
//   border-radius: 4px;
//   -moz-border-radius: 4px;
//   -webkit-border-radius: 4px;
//   box-shadow: 0px 0px 8px -1px black;
//   -moz-box-shadow: 0px 0px 8px -1px black;
//   -webkit-box-shadow: 0px 0px 8px -1px black;
// }

// #toolTip {
//   //position:relative;
//   position: absolute;
//   width: 200px;
//   height: auto;
//   padding: 10px;
//   background-color: rgba(255,87,34 ,1);
//   -webkit-border-radius: 10px;
//   -moz-border-radius: 10px;
//   border-radius: 10px;
//   -webkit-box-shadow: 4px 4px 10px rgba(0,0,0,0.4);
//   -moz-box-shadow: 4px 4px 10px rgba(0,0,0,0.4);
//   box-shadow: 4px 4px 10px rgba(0,0,0,0.4);
//   pointer-events: none;
//   z-index: 1000;
//   visibility: hidden;
// }
// #toolTip p {
//     padding:10px;
//     background-color:rgba(0,0,0,0.4);
//     // border:solid 1px #a0c7ff;
//     // border-radius: 5px;
// }

// #tailShadow {
//     position:absolute;
//     bottom:-8px;
//     left:28px;
//     width:0;height:0;
//     border:solid 2px #fff;
//     box-shadow:0 0 10px 1px #555;
// }

// #tail1 {
//     position:absolute;
//     bottom:-20px;
//     left:20px;
//     width:0;height:0;
//     border-color:#a0c7ff transparent transparent transparent;
//     border-width:10px;
//     border-style:solid;
// }

// #tail2 {
//     position:absolute;
//     bottom:-18px;
//     left:20px;
//     width:0;height:0;
//     // border-color:#f9f9f9 transparent transparent transparent;
//     // border-width:10px;
//     // border-style:solid;
// }

#toolTip_up {
  //position:relative;
  position: absolute;
  // width: 200px;
  width: auto;
  height: auto;
  padding: 10px 10px 0;
  background-color: rgba(0,0,0,.6);
  border-radius: 3px;
  box-shadow: 4px 4px 10px rgba(0,0,0,.2);
  pointer-events: none;
  z-index: 1000;
  left: 300px;
  top: 0;
  visibility: hidden;
}

#toolTip_up p {
  color: white;
}

// #tail3 {
//     position:absolute;
//     top:-20px;
//     left:20px;
//     width:0;height:0;
//     border-color:transparent transparent #a0c7ff transparent;
//     border-width:10px;
//     border-style:solid;
// }

#tail4 {
  position: absolute;
  top: -33px;
  left: 20px;
  width: 0;
  height: 0;
  border-color: transparent transparent rgba(244,67,54 ,1);
  border-width: 20px 10px;
  border-style: solid;
}
