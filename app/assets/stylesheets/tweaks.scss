html {
  // font-family: "Helvetica","Noto Sans TC","Arial",sans-serif;
  font-weight: 400;
  color: rgba(66,66,66 ,1);
  // text-shadow: rgba(158,158,158,1) .01em .01em .01em !important;
  text-shadow: rgba(158,158,158,1) .5px .5px 1px !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-smoothing: always;
}

pre {
  font-family: 'Courier New',monospace;
}

a:-webkit-any-link {
  // color: rgba(233,30,99,1);
  cursor: auto;
  text-decoration: none !important;

  // text-shadow: rgba(158,158,158,1) .5px .5px 1px !important;
  // -webkit-font-smoothing: antialiased;
  // -moz-osx-font-smoothing: grayscale;
  // font-smoothing: always;
}

::selection {
  color: #fff;
  background: #ff5e99;
  text-shadow: none; //
}

#uni_wrapper {
  font-size: 16px;
  // padding-top: 65px;
}

#in_ifate_pan {
  font-size: 16px;
  padding-top: 55px;
}

#main {
  // font-size: 16px;
  padding-top: 65px;
}

#setting50 {
  font-size: 16px;
  width: 50%;
  margin: auto;
  padding-top: 65px;
}

#setting80 {
  font-size: 16px;
  width: 80%;
  margin: auto;
  padding-top: 65px;
}
#setting90 {
  font-size: 16px;
  width: 90%;
  margin: auto;
  padding-top: 65px;
}

#setting100 {
  font-size: 16px;
  width: 100%;
  margin: auto;
  padding-top: 65px;
}

.input-group-addon {
  color: rgba(250,250,250 ,1);
  background-color: rgba(158,158,158 ,1);
  // border-color: rgba(117,117,117 ,1);
}

// needed in pan-filter
.embed-responsive-1by1 {
  padding-bottom: percentage((1 / 1)); // 100%;
}

.table-responsive {
  overflow-x: visible !important;
  overflow-y: visible !important;
}

.col-centered {
  float: none;
  margin: 0 auto;
}
@keyframes blinker {
  50% {
    opacity: 0;
  }
}
@keyframes pulsar {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.3);
  }
}
