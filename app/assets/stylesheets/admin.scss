/*
 *= require application
 *= require_self
 */
body {
  color: #333;
}

body,
ol,
p,
td,
ul {
  font-family: verdana,arial,helvetica,sans-serif;
  font-size: 13px;
  line-height: 18px;
}

.panel-body h1,
fieldset legend {
  border: 0;
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 20px;
  padding: 0;
  line-height: 100%;
}

pre {
  background-color: #eee;
  padding: 10px;
  font-size: 11px;
}

.group {
  margin-bottom: 20px;
}

.group h2 {
  font-size: 12px;
  margin-bottom: 8px;
}

.group ul {
  margin: 0 20px;
}

.pagination {
  clear: both;
  margin-top: 10px;
  text-align: left;
}

.toolbar {
  margin-bottom: 15px;
}

table tr.deleted td {
  text-decoration: line-through;
  color: #999;
}

table tr td a.fa {
  text-decoration: normal;
  color: #666;
  margin-right: 4px;

  &:hover {
    color: #333;
    text-decoration: none;
  }
}

.nav > li > a {
  padding: 14px 6px;
}
