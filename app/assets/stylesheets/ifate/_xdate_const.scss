$contentGap: 1;

$px_button_width: 2;
$px_button_height: 2;

$rightbar_content_width: 18;
$rightbarTop: 0;
$rbt_height: 1.5;
$rbt_width: 1.5;
$rbt_gap: $contentGap / 5;
$rbt_top: 0 - $rbt_height - $rbt_gap;
$rightbarWidth: $rightbar_content_width + $px_button_width + $rbt_gap;

$rbb_top: $rbt_top + $rbt_height + $rbt_gap;
$rbb_width: $rightbarWidth;
$rbb_top_ifate: $rbt_height + .2;

$d_gap: 0.05;
$b_gap: 0;

$xdateHeight: $nHouseWidth * 4;

//for xdate
$rightbarLeft: 10;
$rightbarHeight: $xdateHeight;
$rbb_height: $rightbarHeight - $rbb_top;
