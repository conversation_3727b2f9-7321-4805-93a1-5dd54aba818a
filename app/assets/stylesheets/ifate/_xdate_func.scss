//@import 'color_font';
//@import 'xdate_color_font';
//@import 'xdate_const';
@mixin px_title_color($color: $px_title_color,$bkColor: $px_title_bk_color,$cursor: default) {
   @include colors($color,$bkColor);
  cursor: $cursor;
}
@mixin px_highlight_color($color: black,$bkColor: $px_highlight_color,$cursor: pointer) {
  //@include colors($color,$bkColor);
  background-color: $bkColor;
  cursor: $cursor;
}
@mixin px_jieqi_highlight_color($color: black,$bkColor: $px_jieqi_highlight_color,$cursor: pointer) {
  //@include colors($color,$bkColor);
  background-color: $bkColor;
  cursor: $cursor;
}
@function pn_l($index) {
   @if ($index == 1){
    @return .4;
  }
  @else if ($index == 2) {
     @return .4;
  }
  @return .4;
}
@mixin px_rb_tab_block() {
  #rb_tab_block {
    @include ItemPos($rbt_top,0,$rbb_width/3,$rbt_height);
    @include bk-transparent(0);
  }

  #rb_tab_block_ifate {
    //@include ItemPos(0,0,$rbb_width/3,$rbt_height);
    @include ItemPos(0,0,$rbb_width,$rbt_height);
    @include bk-transparent(0);
    #star_user_manage {
      @include normal-text;
      float:right;
    }
  }

  .rb_tab,
  .rb_tab_over,
  .rb_tab_select,
  .rbt_button {
    @include ItemPos(0,0,$rbt_width,$rbt_height);
  }

  .rb_tab {
    @include colors(black,$background_color_default);
    @include rb_border();
  }

  .rb_tab_select {
    @include colors(black,$background_color_highlight_default);
    @include lt_border();
    cursor: pointer;
  }

  .rb_tab_over {
    @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
    @include rb_border();
    cursor: pointer;
  }

  .rbt_button {
    @include align_center($rbt_height);
  }
}
@mixin px_pan_xdate_block() {
  $pxt_width: $rbb_width; // - $borderWidth;

  #pan_xdate_block * {
    position: absolute;
  }

  #pan_xdate_block {
    //    position:absolute;
    @include ItemPos_x($rbb_top,0,$rbb_width,$rbb_height);
    @include lt_border();
    @include bk-transparent();
  }

  #pan_right_block_ifate {
    @include ItemPos_x(0,0,$rbb_width + $px_button_width + $contentGap,$rbb_height + $rbt_height);
    //    @include lt_border();
    // @include bk-transparent();
    position: relative; //div relative { div absolute}
  }

  #pan_xdate_block_ifate * {
    position: absolute;
  }

  #pan_xdate_block_ifate {
    @include ItemPos_x($rbb_top_ifate,0,$rbb_width,$rbb_height);
    @include lt_border();
    @include bk-transparent();
    position: absolute; //div relative { div absolute}
  }

  .px_normal {
    @include ItemPos_x(0,0,$rbb_width,$rbb_height);
    @include bk-transparent(0);
  }
  $nXdateNormalRowHeight: $rbb_height / 11;
  @for $i from 1 through 11 {
    .px_normal_row_#{$i} {
      @include ItemPos_x(($i - 1) * $nXdateNormalRowHeight,0,$rbb_width,$nXdateNormalRowHeight);
      @include rb_border();
    }
  }

  $nXdateNormalColWidth: $rbb_width / 8;
  @for $i from 1 through 8 {
    .px_normal_col_#{$i},
    .px_normal_col_#{$i}_noevent,
    .px_normal_col_#{$i}_over,
    .px_normal_highlight_col_#{$i},
    .px_normal_highlight_col_#{$i}_over {
      @include ItemPos_x(0,($i - 1) * $nXdateNormalColWidth,$nXdateNormalColWidth,$nXdateNormalRowHeight);
    }

    .px_normal_col_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }

    .px_normal_highlight_col_#{$i} {
      @include px_highlight_color();
    }

    .px_normal_highlight_col_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }

    .px_normal_col_#{$i}_noevent {
      cursor: default;
    }
  }
  $nXdateNormalRowHeightInCol: $nXdateNormalRowHeight / 4;
  @for $i from 1 through 4 {
    .px_normal_col_n_#{$i} {
      @include ItemPos_x(($i - 1) * $nXdateNormalRowHeightInCol,pn_l($i),$nXdateNormalColWidth - pn_l($i),1);
      @include align_center(1);
    }
  }

  .px_normal_col_n_1_data,
  .px_normal_col_n_2_data,
  .px_normal_col_n_3_data,
  .px_normal_col_n_4_data {
    @include ItemPos_x(0,0,2,1);
    @include align_center(1);
  }

  .px_normal_col_n_1_data {
    @include my-text(.625em);
  }

  .px_normal_col_n_2_data,
  .px_normal_col_n_3_data {
    @include my-text(.813em);
  }

  .px_normal_col_n_4_data {
    @include my-text(.625em);
  }

  .px_tenyear {
    @include ItemPos_x(0,0,$rbb_width,$rbb_height);
    @include rb_border();
  }
  $nXdateTenRowHeight: $rbb_height / 30;
  @for $i from 1 through 30 {
    .px_tenyear_row_#{$i},
    .px_tenyear_row_#{$i}_highlight,
    .px_tenyear_row_#{$i}_over {
      @include ItemPos_x(($i - 1) * $nXdateTenRowHeight,0,$rbb_width,$nXdateTenRowHeight);
      @include rb_border();
    }

    .px_tenyear_row_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }

    .px_tenyear_row_#{$i}_highlight {
      @include px_highlight_color();
    }
  }
  @for $i from 1 through 20 {
    .px_tenyear_highlight_#{$i} {
      @include ItemPos_x(($i - 1) * $nXdateTenRowHeight,0,$rbb_width,$nXdateTenRowHeight * 10);
      @include px_highlight_color();
    }
  }

  .px_tenyear_row_n_1,
  .px_tenyear_row_n_2,
  .px_tenyear_row_n_3,
  .px_tenyear_row_n_4,
  .px_tenyear_row_n_5 {
    position: absolute;
    top: makeEm(getMiddleTop($nXdateTenRowHeight,1));
    height: makeEm($nXdateTenRowHeight);
    @include align_center($nXdateTenRowHeight);
  }

  .px_tenyear_row_n_1 {
    left: .2em;
    width: 3em;
  }

  .px_tenyear_row_n_2 {
    left: 3em;
    width: 5em;
  }

  .px_tenyear_row_n_3 {
    left: 8em;
    width: 2em;
  }

  .px_tenyear_row_n_4 {
    left: 10em;
    width: 2em;
  }

  .px_tenyear_row_n_5 {
    left: 12em;
    width: 3em;
  }

  .px_tenyear_row_n_data {
    @include small-text;
  }

  .px_tenyear_gisuong {
    @include ItemPos_x(getMiddleTop($nXdateTenRowHeight,1),15,2,$nXdateTenRowHeight);
    @include align_center($nXdateTenRowHeight);
  }

  .px_row_n_data_gi {
    @include ItemPos_x(0,0,1,1);
    @include my-text(13px,$data_gi_bk_color);
  }

  .px_row_n_data_suong {
    @include ItemPos_x(0,1.5,1,1);
    @include my-text(13px,$data_suong_bk_color);
  }

  // wannianli
  .px_tenyear_row_n_10,
  .px_tenyear_row_n_6,
  .px_tenyear_row_n_7,
  .px_tenyear_row_n_8,
  .px_tenyear_row_n_9 {
    position: absolute;
    top: makeEm(getMiddleTop($nXdateTenRowHeight,1));
    height: makeEm($nXdateTenRowHeight);
    @include align_center($nXdateTenRowHeight);
  }

  .px_tenyear_row_n_6 {
    left: .2em;
    width: 3em;
  }

  .px_tenyear_row_n_7 {
    left: 3.2em;
    width: 5em;
  }

  .px_tenyear_row_n_8 {
    left: 8.2em;
    width: 4em;
  }

  .px_tenyear_row_n_9 {
    left: 12.2em;
    width: 2em;
  }

  .px_tenyear_row_n_10 {
    left: 14.2em;
    width: 4em;
  }
  // wannianli end

  $nXdateYearRowHeight_26: $rbb_height / 26;
  $nXdateYearRowTop_26: getMiddleTop($nXdateYearRowHeight_26,1);
  $nXdateYearTitleRowTop_26: $nXdateYearRowTop_26 / 4;
  $nXdateYearRowHeight_27: $rbb_height / 27;
  $nXdateYearRowTop_27: getMiddleTop($nXdateYearRowHeight_27,1);
  $nXdateYearTitleRowTop_27: $nXdateYearRowTop_27 / 4;

  .px_flowyear_26 {
    @include ItemPos_x(0,0,$rbb_width,$nXdateYearRowHeight_26 * 26);
    @include rb_border();
  }

  .px_flowyear_27 {
    @include ItemPos_x(0,0,$rbb_width,$nXdateYearRowHeight_27 * 27);
    @include rb_border();
  }

  .px_flowyear_26_block_1_12 {
    @include ItemPos_x(0,0,$rbb_width,$nXdateYearRowHeight_26 * 13);
    @include rb_border();
  }

  .px_flowyear_27_block_1_12 {
    @include ItemPos_x(0,0,$rbb_width,$nXdateYearRowHeight_27 * 13);
    @include rb_border();
  }

  .px_flowyear_27_block_1_13 {
    @include ItemPos_x(0,0,$rbb_width,$nXdateYearRowHeight_27 * 14);
    @include rb_border();
  }

  .px_flowyear_26_block_2_12 {
    @include ItemPos_x($nXdateYearRowHeight_26 * 13,0,$rbb_width,$nXdateYearRowHeight_26 * 13);
    @include rb_border();
  }

  .px_flowyear_27_block_2_12 {
    @include ItemPos_x($nXdateYearRowHeight_27 * 14,0,$rbb_width,$nXdateYearRowHeight_27 * 13);
    @include rb_border();
  }

  .px_flowyear_27_block_2_13 {
    @include ItemPos_x($nXdateYearRowHeight_27 * 13,0,$rbb_width,$nXdateYearRowHeight_27 * 14);
    @include rb_border();
  }

  .px_flowyear_title_26 {
    @include ItemPos_x($d_gap,$d_gap,$pxt_width - $d_gap,$nXdateYearRowHeight_26 - $d_gap);
    @include title_color();
    @include rb_border();
    @include align_center($nXdateYearRowHeight_26);
  }

  .px_flowyear_title_27 {
    @include ItemPos_x($d_gap,$d_gap,$pxt_width - $d_gap,$nXdateYearRowHeight_27 - $d_gap);
    @include title_color();
    @include rb_border();
    @include align_center($nXdateYearRowHeight_27);
  }
  @for $i from 1 through 5 {
    .px_flowyear_title_col_#{$i} {
      @include ItemPos_x($nXdateYearTitleRowTop_27,0,0,$nXdateYearRowHeight_27);
      @include my-text(1em,$px_title_color);
      @include align_center($nXdateYearRowHeight_27);
    }
  }

  .px_flowyear_title_col_1_over {
    @include ItemPos_x($nXdateYearTitleRowTop_27,0,0,$nXdateYearRowHeight_27);
    @include my-text(1em,$px_title_color);
    @include align_center($nXdateYearRowHeight_27);
  }

  .px_flowyear_title_col_1,
  .px_flowyear_title_col_1_over {
    left: 0;
    width: 4em;
  }
  // wannianli
  .px_flowyear_title_col_1_over {
    @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
    cursor: pointer;
  }
  // wannianli end
  .px_flowyear_title_col_2 {
    left: 4em;
    width: 6em;
  }

  .px_flowyear_title_col_3 {
    left: 10em;
    width: 3em;
  }

  .px_flowyear_title_col_4 {
    left: 13em;
    width: 3em;
  }

  .px_flowyear_title_col_5 {
    left: 16em;
    width: 4em;
  }
  @for $i from 1 through 13 {
    .px_flowyear_data_26_row_#{$i},
    .px_flowyear_data_26_row_#{$i}_over,
    .px_flowyear_highlight_data_26_row_#{$i},
    .px_flowyear_highlight_data_26_row_#{$i}_over {
      @include ItemPos_x($i * $nXdateYearRowHeight_26,0,$rbb_width,$nXdateYearRowHeight_26);
      @include rb_border();
    }

    .px_flowyear_highlight_data_26_row_#{$i} {
      @include px_highlight_color();
    }

    .px_flowyear_data_26_row_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }

    .px_flowyear_highlight_data_26_row_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }

    .px_flowyear_data_27_row_#{$i},
    .px_flowyear_data_27_row_#{$i}_over,
    .px_flowyear_highlight_data_27_row_#{$i},
    .px_flowyear_highlight_data_27_row_#{$i}_over {
      @include ItemPos_x($i * $nXdateYearRowHeight_27,0,$rbb_width,$nXdateYearRowHeight_27);
      @include rb_border();
    }

    .px_flowyear_highlight_data_27_row_#{$i} {
      @include px_highlight_color();
    }

    .px_flowyear_data_27_row_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }

    .px_flowyear_highlight_data_27_row_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }
  }

  .px_flowyear_data_col_1,
  .px_flowyear_data_col_2,
  .px_flowyear_data_col_3,
  .px_flowyear_data_col_4,
  .px_flowyear_data_col_5,
  .px_flowyear_data_col_6,
  .px_flowyear_data_col_7 {
    @include ItemPos_x($nXdateYearRowTop_27,0,0,$nXdateYearRowHeight_27);
    @include align_center($nXdateYearRowHeight_27);
  }

  .px_flowyear_data_col_1 {
    left: .2em;
    width: 3em;
  }

  .px_flowyear_data_col_2 {
    left: 3.5em;
    width: 4em;
  }

  .px_flowyear_data_col_3 {
    left: 6.5em;
    width: 2.5em;
  }

  .px_flowyear_data_col_4 {
    left: 9em;
    width: 3em;
  }

  .px_flowyear_data_col_5 {
    left: 12em;
    width: 3em;
  }

  .px_flowyear_data_col_6 {
    left: 15em;
    width: 2em;
  }

  .px_flowyear_data_col_7 {
    left: 17em;
    width: 4em;
  }

  .px_flowyear_row_n_data {
    @include small-text;
  }

  // wannianli
  .px_flowyear_w_data_col_1,
  .px_flowyear_w_data_col_2,
  .px_flowyear_w_data_col_3,
  .px_flowyear_w_data_col_4,
  .px_flowyear_w_data_col_5,
  .px_flowyear_w_data_col_6,
  .px_flowyear_w_data_col_7 {
    @include ItemPos_x($nXdateYearRowTop_27,0,0,$nXdateYearRowHeight_27);
    @include align_center($nXdateYearRowHeight_27);
  }

  .px_flowyear_w_data_col_1 {
    left: .2em;
    width: 2em;
  }

  .px_flowyear_w_data_col_2 {
    left: 2.4em;
    width: 4.8em;
  }

  .px_flowyear_w_data_col_3 {
    left: 7.4em;
    width: 2em;
  }

  .px_flowyear_w_data_col_4 {
    left: 9.4em;
    width: 2em;
  }

  .px_flowyear_w_data_col_5 {
    left: 11.4em;
    width: 3em;
  }

  .px_flowyear_w_data_col_6 {
    left: 14.4em;
    width: 2em;
  }

  .px_flowyear_w_data_col_7 {
    left: 16.4em;
    width: 4em;
  }

  .px_flowyear_w_row_n_data {
    @include small-text;
  }
  // wannianli end
  .px_flowyear_gisuong {
    @include ItemPos_x($nXdateYearRowTop_27,15,3,$nXdateYearRowHeight_27);
    @include align_center($nXdateYearRowHeight_27);
  }

  $nXdateMonthRowHeight: $rbb_height / 31;
  $nXdateMonthRowTop: getMiddleTop($nXdateMonthRowHeight,1);
  $nXdateMonthTitleRowTop: $nXdateMonthRowTop / 4;
  @for $j from 29 through 35 {
    .px_flowmonth_#{$j} {
      @include ItemPos_x(0,0,$rbb_width,row_h($rbb_height,$j) * $j);
      //@include rb_border();
    }

    .px_flowmonth_#{$j}_title,
    .px_flowmonth_w_#{$j}_title1,
    .px_flowmonth_w_#{$j}_title2 {
      @include ItemPos_x($d_gap,$d_gap,$pxt_width - $d_gap,row_h($rbb_height,$j) - $d_gap);
      @include title_color();
      @include rb_border();
      @include align_center(row_h($rbb_height,$j));
    }

    .px_flowmonth_w_#{$j}_title2 {
      @include ItemPos_x($d_gap + row_h($rbb_height,$j),$d_gap,$pxt_width - $d_gap,row_h($rbb_height,$j) - $d_gap);
    }

    .px_flowmonth_#{$j}_title_col_1,
    .px_flowmonth_#{$j}_title_col_2,
    .px_flowmonth_#{$j}_title_col_3 {
      @include ItemPos_x($nXdateMonthTitleRowTop ,0,0,row_h($rbb_height,$j));
      @include my-text(1em,$px_title_color);
      @include align_center(row_h($rbb_height,$j));
    }

    .px_flowmonth_#{$j}_title_col_1 {
      left: 0;
      width: 7em;
    }

    .px_flowmonth_#{$j}_title_col_2 {
      left: 7em;
      width: 7em;
    }

    .px_flowmonth_#{$j}_title_col_3 {
      left: 14em;
      width: 3em;
    }
    // for wannianli
    .px_flowmonth_w_#{$j}_title_col_1,
    .px_flowmonth_w_#{$j}_title_col_2,
    .px_flowmonth_w_#{$j}_title_col_3 {
      @include ItemPos_x($nXdateMonthTitleRowTop ,0,0,row_h($rbb_height,$j));
      @include my-text(1em,$px_title_color);
      @include align_center(row_h($rbb_height,$j));
    }

    .px_flowmonth_w_#{$j}_title_col_1 {
      left: 0;
      width: 6em;
    }

    .px_flowmonth_w_#{$j}_title_col_2 {
      left: 6em;
      width: 7em;
    }

    .px_flowmonth_w_#{$j}_title_col_3 {
      left: 13em;
      width: 3em;
    }

    .px_flowmonth_w_#{$j}_title_col_10,
    .px_flowmonth_w_#{$j}_title_col_10_over,
    .px_flowmonth_w_#{$j}_title_col_4,
    .px_flowmonth_w_#{$j}_title_col_5,
    .px_flowmonth_w_#{$j}_title_col_5_over,
    .px_flowmonth_w_#{$j}_title_col_6,
    .px_flowmonth_w_#{$j}_title_col_7,
    .px_flowmonth_w_#{$j}_title_col_8,
    .px_flowmonth_w_#{$j}_title_col_9 {
      @include ItemPos_x($nXdateMonthTitleRowTop ,0,0,row_h($rbb_height,$j));
      @include my-text(1em,$px_title_color);
      @include align_center(row_h($rbb_height,$j));
    }

    .px_flowmonth_w_#{$j}_title_col_4 {
      left: 16em;
      width: 3em;
    }

    .px_flowmonth_w_#{$j}_title_col_4_data {
      @include ItemPos_x(.3,0,4,row_h($rbb_height,$j));
      @include my-text(13px,$px_title_color);
      @include align_center(row_h($rbb_height,$j));
    }

    .px_flowmonth_w_#{$j}_title_col_5,
    .px_flowmonth_w_#{$j}_title_col_5_over {
      left: 0;
      // width: 3.5em;
      width: 3.5em;
    }

    .px_flowmonth_w_#{$j}_title_col_5_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }

    .px_flowmonth_w_#{$j}_title_col_5_data {
      @include ItemPos_x(.3,0,4,row_h($rbb_height,$j));
      @include my-text(13px,$px_title_color);
      @include align_center(row_h($rbb_height,$j));
    }

    .px_flowmonth_w_#{$j}_title_col_6_highlight,
    .px_flowmonth_w_#{$j}_title_col_7_highlight,
    .px_flowmonth_w_#{$j}_title_col_8_highlight,
    .px_flowmonth_w_#{$j}_title_col_9_highlight {
      @include ItemPos_x($nXdateMonthTitleRowTop ,0,0,row_h($rbb_height,$j));
      @include my-text(1em,$px_title_color);
      @include align_center(row_h($rbb_height,$j));
    }

    .px_flowmonth_w_#{$j}_title_col_6,
    .px_flowmonth_w_#{$j}_title_col_6_highlight {
      // left: 4em;
      left: 3em;
      width: 2em;
    }

    .px_flowmonth_w_#{$j}_title_col_7,
    .px_flowmonth_w_#{$j}_title_col_7_highlight {
      // left: 6em;
      left: 5em;
      width: 5em;
    }

    .px_flowmonth_w_#{$j}_title_col_8,
    .px_flowmonth_w_#{$j}_title_col_8_highlight {
      // left: 12em;
      left: 10em;
      width: 2em;
    }

    .px_flowmonth_w_#{$j}_title_col_9,
    .px_flowmonth_w_#{$j}_title_col_9_highlight {
      // left: 14em;
      left: 12em;
      width: 5em;
    }

    .px_flowmonth_w_#{$j}_title_col_7_data,
    .px_flowmonth_w_#{$j}_title_col_7_data_highlight,
    .px_flowmonth_w_#{$j}_title_col_9_data,
    .px_flowmonth_w_#{$j}_title_col_9_data_highlight {
      @include ItemPos_x(.3,0,6,row_h($rbb_height,$j));
      @include my-text(13px,$px_title_color);
      @include align_center(row_h($rbb_height,$j));
    }

    .px_flowmonth_w_#{$j}_title_col_6_highlight,
    .px_flowmonth_w_#{$j}_title_col_7_highlight,
    .px_flowmonth_w_#{$j}_title_col_8_highlight,
    .px_flowmonth_w_#{$j}_title_col_9_highlight {
      @include px_jieqi_highlight_color();
      cursor: default;
    }

    .px_flowmonth_w_#{$j}_title_col_7_data_highlight,
    .px_flowmonth_w_#{$j}_title_col_9_data_highlight {
      @include px_jieqi_highlight_color();
      cursor: default;
    }

    .px_flowmonth_w_#{$j}_title_col_10,
    .px_flowmonth_w_#{$j}_title_col_10_over {
      // left: 19em;
      left: 16.5em;
      width: 3.5em;
    }

    .px_flowmonth_w_#{$j}_title_col_10_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }

    .px_flowmonth_w_#{$j}_title_col_10_data {
      @include ItemPos_x(.3,0,4,row_h($rbb_height,$j));
      @include my-text(13px,$px_title_color);
      @include align_center(row_h($rbb_height,$j));
    }
    // for wannianli end
    @for $i from 1 through 35 {
      .px_flowmonth_#{$j}_data_row_#{$i},
      .px_flowmonth_#{$j}_data_row_#{$i}_over,
      .px_flowmonth_highlight_#{$j}_data_row_#{$i},
      .px_flowmonth_highlight_#{$j}_data_row_#{$i}_over {
        @include ItemPos_x($i * row_h($rbb_height,$j),0,$rbb_width,row_h($rbb_height,$j));
        @include rb_border();
      }

      .px_flowmonth_highlight_#{$j}_data_row_#{$i} {
        @include px_highlight_color();
      }

      .px_flowmonth_#{$j}_data_row_#{$i}_over {
        @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
        cursor: pointer;
      }

      .px_flowmonth_highlight_#{$j}_data_row_#{$i}_over {
        @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
        cursor: pointer;
      }

      //wannianli
      .px_flowmonth_w_jieqi_highlight_#{$j}_data_row_#{$i},
      .px_flowmonth_w_jieqi_highlight_#{$j}_data_row_#{$i}_over {
        @include ItemPos_x($i * row_h($rbb_height,$j),0,$rbb_width,row_h($rbb_height,$j));
        @include rb_border();
      }

      .px_flowmonth_w_jieqi_highlight_#{$j}_data_row_#{$i} {
        @include px_jieqi_highlight_color();
      }

      .px_flowmonth_w_jieqi_highlight_#{$j}_data_row_#{$i}_over {
        @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
        cursor: pointer;
      }
      //wannianli end
    }
    @for $i from 1 through 5 {
      .px_flowmonth_#{$j}_data_col_pos_#{$i} {
        @include ItemPos_x($nXdateMonthRowTop,0,0,row_h($rbb_height,$j));
        @include align_center(row_h($rbb_height,$j));
      }
    }

    .px_flowmonth_#{$j}_data_col_pos_1 {
      left: .2em;
      width: 4em;
    }

    .px_flowmonth_#{$j}_data_col_pos_2 {
      left: 4em;
      width: 3em;
    }

    .px_flowmonth_#{$j}_data_col_pos_3 {
      left: 7em;
      width: 3em;
    }

    .px_flowmonth_#{$j}_data_col_pos_4 {
      left: 10em;
      width: 3em;
    }

    .px_flowmonth_#{$j}_data_col_pos_5 {
      left: 13em;
      width: 2em;
    }

    .px_flowmonth_#{$j}_gisuong {
      @include ItemPos_x($nXdateMonthRowTop,15,3,row_h($rbb_height,$j));
      @include align_center(row_h($rbb_height,$j));
    }

    //wannianli
    @for $i from 6 through 9 {
      .px_flowmonth_w_#{$j}_data_col_pos_#{$i} {
        @include ItemPos_x($nXdateMonthRowTop,0,0,row_h($rbb_height,$j));
        @include align_center(row_h($rbb_height,$j));
      }
    }

    .px_flowmonth_w_#{$j}_data_col_pos_6 {
      left: .2em;
      width: 2em;
    }

    .px_flowmonth_w_#{$j}_data_col_pos_7 {
      left: 2.2em;
      width: 2em;
    }

    .px_flowmonth_w_#{$j}_data_col_pos_8 {
      left: 4.2em;
      width: 6em;
    }

    .px_flowmonth_w_#{$j}_data_col_pos_9 {
      left: 9.2em;
      width: 6em;
    }

    .px_flowmonth_w_#{$j}_data_col_pos_10 {
      left: 16.2em;
      width: 4em;
    }
    //wannianli end
  }

  .px_flowmonth_data {
    @include small-text;
  }

  .px_flowmonth_w_color_data {
    @include small-text;
    color: red;
  }

  $nXdateDateRowHeight: $rbb_height / 27;
  $nXdateDateRowTop: getMiddleTop($nXdateDateRowHeight,1);
  $nXdateDateTitleRowTop: $nXdateDateRowTop / 4;

  .px_flowdate {
    @include ItemPos_x(0,0,$rbb_width,$nXdateDateRowHeight * 27);
  }

  .px_flowdate_title {
    @include ItemPos_x($d_gap,$d_gap,$pxt_width - $d_gap,$nXdateDateRowHeight - $d_gap);
    @include title_color();
    @include rb_border();
    @include align_center($nXdateDateRowHeight);
  }

  .px_flowdate_title_col_1,
  .px_flowdate_title_col_2,
  .px_flowdate_title_col_3 {
    @include ItemPos_x($nXdateDateTitleRowTop,0,0,$nXdateDateRowHeight);
    @include my-text(1em,$px_title_color);
    @include align_center($nXdateDateRowHeight);
  }

  .px_flowdate_title_col_1 {
    left: 0;
    width: 8em;
  }

  .px_flowdate_title_col_2 {
    left: 8em;
    width: 7em;
  }

  .px_flowdate_title_col_3 {
    left: 15em;
    width: 3em;
  }
  @for $i from 1 through 2 {
    .px_flowdate_block_#{$i} {
      @include ItemPos_x((($i - 1) * 13 + 1) * $nXdateDateRowHeight,0,$rbb_width,$nXdateDateRowHeight * 13);
      //@include rb_border();
    }
  }

  .px_flowdate_block_title {
    @include ItemPos_x($b_gap,$d_gap,$pxt_width - $d_gap,$nXdateDateRowHeight - $b_gap);
    @include title_color(BLACK);
    @include rb_border();
    @include align_center($nXdateDateRowHeight);
  }

  .px_flowdate_block_title_col_1,
  .px_flowdate_block_title_col_2,
  .px_flowdate_block_title_col_3,
  .px_flowdate_block_title_col_4,
  .px_flowdate_block_title_col_5 {
    @include ItemPos_x($nXdateDateTitleRowTop,0,0,$nXdateDateRowHeight);
    @include my-text(1em,$px_block_title_color);
    @include align_center($nXdateDateRowHeight);
  }

  .px_flowdate_block_title_col_1 {
    left: 0;
    width: 4em;
  }

  .px_flowdate_block_title_col_2 {
    left: 4em;
    width: 4em;
  }

  .px_flowdate_block_title_col_3 {
    left: 8em;
    width: 3em;
  }

  .px_flowdate_block_title_col_4 {
    left: 11em;
    width: 3em;
  }

  .px_flowdate_block_title_col_5 {
    left: 14em;
    width: 3em;
  }
  @for $i from 1 through 12 {
    .px_flowdate_block_data_#{$i},
    .px_flowdate_block_data_#{$i}_over,
    .px_flowdate_highlight_block_data_#{$i},
    .px_flowdate_highlight_block_data_#{$i}_over {
      @include ItemPos_x($i * $nXdateDateRowHeight,0,$rbb_width,$nXdateDateRowHeight);
      @include rb_border();
      @include align_center($nXdateDateRowHeight);
    }

    .px_flowdate_highlight_block_data_#{$i} {
      @include px_highlight_color();
    }

    .px_flowdate_block_data_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }

    .px_flowdate_highlight_block_data_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }
  }

  .px_flowdate_block_data_col_1,
  .px_flowdate_block_data_col_2,
  .px_flowdate_block_data_col_3 {
    @include ItemPos_x($nXdateDateRowTop,0,0,$nXdateDateRowHeight);
    @include small-text;
    @include align_center($nXdateDateRowHeight);
  }

  .px_flowdate_block_data_col_1 {
    left: 0;
    width: 4em;
  }

  .px_flowdate_block_data_col_2 {
    left: 4em;
    width: 8em;
  }

  .px_flowdate_block_data_col_3 {
    left: 12em;
    width: 3em;
  }

  .px_flowdate_gisuong {
    @include ItemPos_x($nXdateDateRowTop,15,3,$nXdateDateRowHeight);
    @include align_center($nXdateDateRowHeight);
  }

  $nXdateTimeRowHeight: $rbb_height / 28;
  $nXdateTimeRowTop: getMiddleTop($nXdateTimeRowHeight,1);
  $nXdateTimeTitleRowTop: $nXdateTimeRowTop / 4;

  .px_flowtime {
    @include ItemPos_x(0,0,$rbb_width,$nXdateTimeRowHeight * 28);
  }

  .px_flowtime_title {
    @include ItemPos_x($d_gap,$d_gap,$pxt_width - $d_gap,$nXdateTimeRowHeight * 2 - $d_gap);
    @include title_color();
    @include rb_border();
  }

  .px_flowtime_title_col_1,
  .px_flowtime_title_col_2,
  .px_flowtime_title_col_3,
  .px_flowtime_title_col_4,
  .px_flowtime_title_col_5,
  .px_flowtime_title_col_6 {
    @include ItemPos_x(0,0,0,$nXdateTimeRowHeight);
    @include align_center($nXdateTimeRowHeight);
  }

  .px_flowtime_title_col_1 {
    top: makeEm($nXdateTimeTitleRowTop);
    left: 0;
    width: 10em;
  }

  .px_flowtime_title_col_2 {
    top: makeEm($nXdateTimeTitleRowTop);
    left: 10em;
    width: 6em;
  }

  .px_flowtime_title_col_3 {
    top: makeEm($nXdateTimeTitleRowTop);
    left: 16em;
    width: 2em;
  }

  .px_flowtime_title_col_4 {
    top: makeEm($nXdateTimeTitleRowTop + $nXdateTimeRowHeight);
    left: 0;
    width: 10em;
  }

  .px_flowtime_title_col_5 {
    top: makeEm($nXdateTimeTitleRowTop + $nXdateTimeRowHeight);
    left: 10em;
    width: 2em;
  }

  .px_flowtime_title_col_6 {
    top: makeEm($nXdateTimeTitleRowTop + $nXdateTimeRowHeight);
    left: 12em;
    width: 4em;
  }

  .px_flowtime_title_col_1_data,
  .px_flowtime_title_col_2_data,
  .px_flowtime_title_col_3_data,
  .px_flowtime_title_col_4_data,
  .px_flowtime_title_col_5_data,
  .px_flowtime_title_col_6_data {
    @include ItemPos_x(0,0,0,$nXdateTimeRowHeight);
    @include my-text(1em,$px_title_color);
    @include align_center($nXdateTimeRowHeight);
  }

  .px_flowtime_title_col_1_data {
    width: 10em;
  }

  .px_flowtime_title_col_2_data {
    width: 6em;
  }

  .px_flowtime_title_col_3_data {
    width: 2em;
  }

  .px_flowtime_title_col_4_data {
    width: 10em;
  }

  .px_flowtime_title_col_5_data {
    width: 2em;
  }

  .px_flowtime_title_col_6_data {
    width: 4em;
  }

  .px_flowtime_block_1,
  .px_flowtime_block_2 {
    @include ItemPos_x(0,0,$rbb_width,$nXdateTimeRowHeight * 13);
  }

  .px_flowtime_block_1 {
    top: makeEm($nXdateTimeRowHeight * 2);
  }

  .px_flowtime_block_2 {
    top: makeEm($nXdateTimeRowHeight * 15);
  }

  .px_flowtime_block_title {
    @include ItemPos_x($b_gap,$d_gap,$pxt_width - $d_gap,$nXdateTimeRowHeight - $b_gap);
    @include title_color(BLACK);
    @include rb_border();
    @include align_center($nXdateTimeRowHeight);
  }

  .px_flowtime_block_title_col_1,
  .px_flowtime_block_title_col_2,
  .px_flowtime_block_title_col_3 {
    @include ItemPos_x($nXdateTimeTitleRowTop,0,0,$nXdateTimeRowHeight);
    @include align_center($nXdateTimeRowHeight);
  }

  .px_flowtime_block_title_col_1 {
    left: 1em;
    width: 4em;
  }

  .px_flowtime_block_title_col_2 {
    left: 5em;
    width: 8em;
  }

  .px_flowtime_block_title_col_3 {
    left: 13em;
    width: 3em;
  }
  @for $i from 1 through 12 {
    .px_flowtime_block_data_#{$i},
    .px_flowtime_block_data_#{$i}_over,
    .px_flowtime_highlight_block_data_#{$i},
    .px_flowtime_highlight_block_data_#{$i}_over {
      @include ItemPos_x($i * $nXdateTimeRowHeight,0,$rbb_width,$nXdateTimeRowHeight);
      @include rb_border();
      @include align_center($nXdateTimeRowHeight);
    }

    .px_flowtime_highlight_block_data_#{$i} {
      @include px_highlight_color();
    }

    .px_flowtime_block_data_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }

    .px_flowtime_highlight_block_data_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }
  }

  .px_flowtime_block_data_col_1,
  .px_flowtime_block_data_col_2 {
    @include ItemPos_x($nXdateTimeRowTop,0,0,$nXdateTimeRowHeight);
    @include small-text;
    @include align_center($nXdateTimeRowHeight);
  }

  .px_flowtime_block_data_col_1 {
    left: 0;
    width: 8em;
  }

  .px_flowtime_block_data_col_2 {
    left: 8em;
    width: 3em;
  }

  .px_flowtime_gisuong {
    @include ItemPos_x($nXdateTimeRowTop,15,3,$nXdateTimeRowHeight);
    @include align_center($nXdateTimeRowHeight);
  }

  $nXdateMinRowHeight: $rbb_height / 28;
  $nXdateMinRowTop: getMiddleTop($nXdateMinRowHeight,1);
  $nXdateMinTitleRowTop: $nXdateMinRowTop / 4;

  .px_flowmin {
    @include ItemPos_x(0,0,$rbb_width,$nXdateMinRowHeight * 28);
  }

  .px_flowmin_title {
    @include ItemPos_x($d_gap,$d_gap,$pxt_width - $d_gap,$nXdateMinRowHeight * 2 - $d_gap);
    @include title_color();
    @include rb_border();
  }

  .px_flowmin_title_col_1,
  .px_flowmin_title_col_2,
  .px_flowmin_title_col_3,
  .px_flowmin_title_col_4,
  .px_flowmin_title_col_5,
  .px_flowmin_title_col_6 {
    @include ItemPos_x(0,0,0,$nXdateMinRowHeight);
    @include my-text(1em,$px_title_color);
    @include align_center($nXdateMinRowHeight);
  }

  .px_flowmin_title_col_1 {
    top: makeEm($nXdateMinTitleRowTop);
    left: 0;
    width: 10em;
  }

  .px_flowmin_title_col_2 {
    top: makeEm($nXdateMinTitleRowTop);
    left: 10em;
    width: 6em;
  }

  .px_flowmin_title_col_3 {
    top: makeEm($nXdateMinTitleRowTop);
    left: 16em;
    width: 2em;
  }

  .px_flowmin_title_col_4 {
    top: makeEm($nXdateMinTitleRowTop + $nXdateMinRowHeight);
    left: 0;
    width: 10em;
  }

  .px_flowmin_title_col_5 {
    top: makeEm($nXdateMinTitleRowTop + $nXdateMinRowHeight);
    left: 10em;
    width: 2em;
  }

  .px_flowmin_title_col_6 {
    top: makeEm($nXdateMinTitleRowTop + $nXdateMinRowHeight);
    left: 12em;
    width: 4em;
  }

  .px_flowmin_title_col_1_data,
  .px_flowmin_title_col_2_data,
  .px_flowmin_title_col_3_data,
  .px_flowmin_title_col_4_data,
  .px_flowmin_title_col_5_data,
  .px_flowmin_title_col_6_data {
    @include ItemPos_x(0,0,0,$nXdateMinRowHeight);
    @include my-text(13px,$px_title_color);
    @include align_center($nXdateMinRowHeight);
  }

  .px_flowmin_title_col_1_data {
    width: 10em;
  }

  .px_flowmin_title_col_2_data {
    width: 6em;
  }

  .px_flowmin_title_col_3_data {
    width: 2em;
  }

  .px_flowmin_title_col_4_data {
    width: 10em;
  }

  .px_flowmin_title_col_5_data {
    width: 2em;
  }

  .px_flowmin_title_col_6_data {
    width: 4em;
  }

  .px_flowmin_block_1,
  .px_flowmin_block_2 {
    @include ItemPos_x(0,0,$rbb_width,$nXdateMinRowHeight * 13);
  }

  .px_flowmin_block_1 {
    top: makeEm($nXdateMinRowHeight * 2);
  }

  .px_flowmin_block_2 {
    top: makeEm($nXdateMinRowHeight * 15);
  }

  .px_flowmin_block_title {
    @include ItemPos_x($b_gap,$d_gap,$pxt_width - $d_gap,$nXdateMinRowHeight - $b_gap);
    @include title_color(BLACK);
    @include rb_border();
    @include align_center($nXdateMinRowHeight);
  }

  .px_flowmin_block_title_col_1,
  .px_flowmin_block_title_col_2,
  .px_flowmin_block_title_col_3 {
    @include ItemPos_x($nXdateMinTitleRowTop,0,0,$nXdateMinRowHeight);
    @include align_center($nXdateMinRowHeight);
  }

  .px_flowmin_block_title_col_1 {
    left: 1em;
    width: 4em;
  }

  .px_flowmin_block_title_col_2 {
    left: 5em;
    width: 8em;
  }

  .px_flowmin_block_title_col_3 {
    left: 13em;
    width: 3em;
  }
  @for $i from 1 through 12 {
    .px_flowmin_block_data_#{$i},
    .px_flowmin_block_data_#{$i}_over,
    .px_flowmin_highlight_block_data_#{$i},
    .px_flowmin_highlight_block_data_#{$i}_over {
      @include ItemPos_x($i * $nXdateMinRowHeight,0,$rbb_width,$nXdateMinRowHeight);
      @include rb_border();
      @include align_center($nXdateMinRowHeight);
    }

    .px_flowmin_highlight_block_data_#{$i} {
      @include px_highlight_color();
    }

    .px_flowmin_block_data_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }

    .px_flowmin_highlight_block_data_#{$i}_over {
      @include colors($px_mouseover_color_default,$px_mouseover_bk_color_default);
      cursor: pointer;
    }
  }

  .px_flowmin_block_data_col_1,
  .px_flowmin_block_data_col_2 {
    @include ItemPos_x($nXdateMinRowTop,0,0,$nXdateMinRowHeight);
    @include small-text;
    @include align_center($nXdateMinRowHeight);
  }

  .px_flowmin_block_data_col_1 {
    left: 0;
    width: 8em;
  }

  .px_flowmin_block_data_col_2 {
    left: 8em;
    width: 2em;
  }

  $nXdateButtonLeft: $rbb_width + $contentGap / 2;

  .px_flowdate_button,
  .px_flowdate_button_over,
  .px_flowdate_button_select,
  .px_flowmin_button,
  .px_flowmin_button_over,
  .px_flowmin_button_select,
  .px_flowmonth_button,
  .px_flowmonth_button_over,
  .px_flowmonth_button_select,
  .px_flowtime_button,
  .px_flowtime_button_over,
  .px_flowtime_button_select,
  .px_flowyear_button,
  .px_flowyear_button_over,
  .px_flowyear_button_select,
  .px_normal_button,
  .px_normal_button_over,
  .px_normal_button_select,
  .px_tenyear_button,
  .px_tenyear_button_over,
  .px_tenyear_button_select {
    @include ItemPos_x(0,$nXdateButtonLeft,$px_button_width,$px_button_height);
    @include align_center($px_button_height);
    cursor: pointer;
  }

  .px_flowdate_button,
  .px_flowmin_button,
  .px_flowmonth_button,
  .px_flowtime_button,
  .px_flowyear_button,
  .px_normal_button,
  .px_tenyear_button {
    @include colors($px_button_color,$px_button_bk_color);
    @include rb_border();
  }

  .px_flowdate_button_select,
  .px_flowmin_button_select,
  .px_flowmonth_button_select,
  .px_flowtime_button_select,
  .px_flowyear_button_select,
  .px_normal_button_select,
  .px_tenyear_button_select {
    @include colors($px_button_select_color,$px_button_select_bk_color);
    @include lt_border();
  }

  .px_flowdate_button_over,
  .px_flowmin_button_over,
  .px_flowmonth_button_over,
  .px_flowtime_button_over,
  .px_flowyear_button_over,
  .px_normal_button_over,
  .px_tenyear_button_over {
    @include colors($px_button_over_bk_color,$px_button_over_bk_color);
    @include rb_border();
    cursor: pointer;
  }

  .px_button_data {
    top: .3em;
    left: .2em;
    @include small-text;
    @include align_center($px_button_height);
    white-space: nowrap;
  }

  .px_button_data_one {
    @include ItemPos_x(0,0,$px_button_width,$px_button_height);
    top: 0;
    left: 0;
    @include align_center($px_button_height);
  }

  .px_arrow-up {
    top: .2em;
    border-color: $px_jieqi_highlight_color transparent;
    border-style: solid;
    border-width: 0 20px 20px 20px;
    height: 0;
    width: 0;
  }

  .px_arrow-down {
    top: .2em;
    border-color: $px_jieqi_highlight_color transparent;
    border-style: solid;
    border-width: 20px 20px 0 20px;
    height: 0;
    width: 0;
  }

  .px_arrow-up_yue {
    top: .6em;
    border-color: $px_jieqi_highlight_color transparent;
    border-style: solid;
    border-width: 0 8px 8px 8px;
    height: 0;
    width: 0;
  }

  .px_arrow-down_yue {
    top: .6em;
    border-color: $px_jieqi_highlight_color transparent;
    border-style: solid;
    border-width: 8px 8px 0 8px;
    height: 0;
    width: 0;
  }

  .px_arrow-up_ri {
    top: .2em;
    border-color: $px_jieqi_highlight_color transparent;
    border-style: solid;
    border-width: 0 8px 8px 8px;
    height: 0;
    width: 0;
  }

  .px_arrow-down_ri {
    top: .2em;
    border-color: $px_jieqi_highlight_color transparent;
    border-style: solid;
    border-width: 8px 8px 0 8px;
    height: 0;
    width: 0;
    left: 3em;
  }
}
