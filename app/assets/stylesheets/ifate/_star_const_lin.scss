//@import 'commonfunc';
//@import 'xdate_const';

  //different here
  $nHouseWidth: 12;

  //same below
  $headerHeight: 2;
  $contentGap: 1;
  $borderWidth:  .1;
  $leftbarWidth: 0; //9.2;

  $globalHeaderheight: 0;
  $contentWidth: $nHouseWidth * 4;
  $leftbarHeight: $contentWidth;

  $border_width: 1px;

  //$nHouseInnerWidth: $nHouseWidth;

  $globalHeaderheight: $globalHeaderheight;
  $borderWidth:  .1;

  $contentWidth: $nHouseWidth * 4;
  $nMaxAStars: $nHouseWidth - 3;
  $nMaxBStars: $nHouseWidth;
  $ifateTop:  $globalHeaderheight + $contentGap;
  $ifateLeft: 0;
  $ifateWidth: $contentWidth + $leftbarWidth + $rightbarWidth + $contentGap * 4 + $px_button_width;
  $ifateHeight: $headerHeight + $contentGap + $contentWidth + $contentGap;

  $headerWidth: $ifateWidth - $contentGap;
  $headerLeft: $contentGap;

  $mainTop: $headerHeight;
  $mainLeft: $contentGap;
  $mainWidth: $ifateWidth;// - (2 * $contentGap);
  $mainHeight: $contentWidth;

  $leftbarTop: 0;
  $leftbarLeft: 0;
  $contentTop: 0;
  $contentLeft: $leftbarWidth + $contentGap;
  $contentHeight: $contentWidth;

  $nHouseLine2Height: $nHouseWidth - 8;
  $nHouseLine2_1Width: 6;
  $nHouseLine3Top: $nHouseLine2Height + 2;
  $nHouseLine3Height: 3;
  $nHouseLine4Top: $nHouseLine3Top + $nHouseLine3Height;
  $nHouseLine4Height: 3;
  $nSmallSanLeft: 1.3;
  $nSmallSanWidth: ($nHouseWidth - ($nSmallSanLeft * 2)) / 8;
  $flowdateWidth: 1.3;
  $panInfoLeft: 2 / $flowdateWidth;
  $panInfoWidth: $nHouseWidth - $panInfoLeft - 2;

  $footerTop: $ifateTop + $contentGap + $ifateHeight;
  $footerLeft: $leftbarWidth + $contentGap;
  $footerWidth: $contentWidth;
  $footerHeight: 5;

  $a_start_shift: .2;

  //for xdate
  $rightbarLeft: $contentLeft + $contentWidth + $contentGap;
  $rightbarHeight: $contentWidth - 4;
  $rbb_height: $rightbarHeight - $rbb_top;

//content box shadow
$scbs_h: 8;
$scbs_v: 8;
$scbs_blur: 6;
$scbs_spread: 0;
$scbs_a: .4;

