@import 'color_font';
@import 'commonfunc';
@import 'xdate_const';
@import 'star_const_lin';

@media print {

  #istsl {
    @include ItemPos($ifateTop,$ifateLeft,$ifateWidth - $rightbarWidth - 2,$ifateHeight + 2 + $footerHeight);
  }

  #istsl_header {
    @include ItemPos(0,$headerLeft + 1.2,$headerWidth - $rightbarWidth - 2,$headerHeight + 2);
  }

  #istsl_main {
    @include ItemPos($mainTop + 2,$mainLeft + 2,$mainWidth - $rightbarWidth - 2,$mainHeight);
  }

  #istsl_content {
    box-shadow: none;
  }

  .istsl_owner_block {
    @include ItemPos($mainHeight + 1,1.2,$mainWidth - $rightbarWidth - 2,2);
  }

}
