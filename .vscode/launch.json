{
  // 使用 IntelliSense 以得知可用的屬性。
  // 暫留以檢視現有屬性的描述。
  // 如需詳細資訊，請瀏覽: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Local File",
      "type": "Ruby",
      "request": "launch",
      "cwd": "${workspaceRoot}",
      "program": "${file}"
    },
    {
      "name": "Listen for rdebug-ide",
      "type": "Ruby",
      "request": "attach",
      "cwd": "${workspaceRoot}",
      "remoteHost": "127.0.0.1",
      "remotePort": "1234",
      "remoteWorkspaceRoot": "${workspaceRoot}"
    },
    {
      "name": "Rails server",
      "type": "Ruby",
      "request": "launch",
      "cwd": "${workspaceRoot}",
      "program": "${workspaceRoot}/bin/rails",
      "useBundler": true,
      "pathToBundler": "${env:HOME}/.rbenv/shims/bundle",
      "showDebuggerOutput": true,
      "pathToRDebugIDE": "${workspaceRoot}/bin/rdebug-ide",
      "args": ["server"]
    },
    {
      "name": "Sidekiq",
      "type": "Ruby",
      "request": "launch",
      "cwd": "${workspaceRoot}",
      "program": "${workspaceRoot}/bin/bundle",
      "args": ["exec", "sidekiq"]
    },
    {
      "name": "RSpec - all",
      "type": "Ruby",
      "request": "launch",
      "cwd": "${workspaceRoot}",
      "program": "${workspaceRoot}/bin/rspec",
      "args": ["-I", "${workspaceRoot}"]
    },
    {
      "name": "RSpec - active spec file only",
      "type": "Ruby",
      "request": "launch",
      "cwd": "${workspaceRoot}",
      "program": "${workspaceRoot}/bin/rspec",
      "args": ["-I", "${workspaceRoot}", "${file}"]
    },
    {
      "name": "Cucumber",
      "type": "Ruby",
      "request": "launch",
      "cwd": "${workspaceRoot}",
      "program": "${workspaceRoot}/bin/cucumber"
    }
  ]
}
