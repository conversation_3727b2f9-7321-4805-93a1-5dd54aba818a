source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '2.5.1'
gem 'dotenv-rails'

gem 'rails', '~> 5.0.6'
gem 'sprockets'
gem 'sass-rails'
gem 'coffee-rails'
gem 'uglifier'
gem 'jquery-rails'
gem 'jbuilder'
gem 'turbolinks'
gem 'pg', '>= 0.18', '< 2.0'
gem 'rails-i18n'
gem 'http_accept_language'
gem 'font-awesome-rails'

# A little ActiveRecord extension for helping to insert lots of rows in a single insert statement.
gem 'bulk_insert'

# 驗證碼，頭像
gem 'rucaptcha'

# 用戶系統
gem 'devise'
gem 'devise-encryptable'

# 分頁
gem 'will_paginate'

# Permission
gem 'cancancan'

# Redis
gem 'redis'
gem 'hiredis'
gem 'redis-namespace'
gem 'redis-objects'

# Cache
gem 'second_level_cache'

# Setting
gem 'rails-settings-cached'

# 隊列
gem 'sidekiq'
gem 'sidekiq-cron'

# 表單
gem 'simple_form'

# API
# gem 'active_model_serializers'
gem 'fast_jsonapi'
# gem 'rabl'

# Memcache_store
gem 'dalli'

gem 'puma'

# API cors
gem 'rack-cors', require: 'rack/cors'
gem 'rack-utf8_sanitizer'

# gem 'airbrake', '~> 5.0'
gem 'exception_notification'
gem 'status-page'

# from the need of original meen.tw
gem 'iconv'
gem 'geoip'
gem 'geocoder'

gem 'sitemap_generator'
gem 'whenever', require: false

group :development do
  gem 'capistrano', require: false
  gem 'capistrano-rails', require: false
  gem 'capistrano3-puma', require: false
  gem 'capistrano-bundler', require: false
  gem 'capistrano-sidekiq', require: false
  gem 'capistrano-rvm', require: false
  gem 'bundler-audit', ">= 0.7.0", require: false
  gem 'meta_request' # Supporting gem for Rails Panel (Google Chrome extension for Rails development).
end

# VSCode debug dependence
group :development do
  gem 'ruby-debug-ide', ">= 0.7.0", require: false
  gem 'debase', require: false
  gem 'rubocop', '~> 0.58.2', require: false
  gem "rubocop-rails_config"
end

group :development, :test do
  gem 'listen', '>= 3.0.5', '< 3.2'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring'
  gem 'spring-watcher-listen', '~> 2.0.0'
  gem 'letter_opener'
end
