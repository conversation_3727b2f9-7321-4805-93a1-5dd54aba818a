#test
#add demo\lib to ruby searched directory
#$:<<"D:\\Pro58_2010\\Ruby\\projects\\demo\\lib"
#require("Xdate_Variable.rb")
require("Xdate_Constant.rb")
require("Xdate_Variable.rb")
require("Xdate_json.rb")
require("Xdate_lunar.rb")
require("Xdate_segment.rb")
require("Xdate_sun.rb")
require("Xdate_realsuntime.rb")
require("Pm.rb")
require("SkyEarthFive_Function.rb")
require("Star_Constant.rb")
require 'active_support/time'
require 'geoip'
require("Xdate_farmer.rb")

class Xdate

  #---------------------------------------------------------------------
  #constructor initialize
  def initialize()
    @@num_objects += 1
    #instance variable
  end

  #---------------------------------------------------------------------
  def Xdate.Init()
    if (@@SegTimeArray == nil) then
      @@SegTimeArray = Xdate.LoadSegTbl()
    end
  end

  #---------------------------------------------------------------------
  def Xdate.GetSegTbl()
    Xdate.Init()
    return @@SegTimeArray.clone
  end

  #---------------------------------------------------------------------
  def Xdate.IsYearLegal?(nYear)
    if (nYear == nil) then
      return false
    end
    return nYear.between?(1900,2099)
  end
  def Xdate.getLegalYear(nYear)
    return nYear if Xdate.IsYearLegal?(nYear)
    return Xdate.getLegalYear(nYear + 60) if nYear < 1900
    return Xdate.getLegalYear(nYear - 60) if nYear > 2099
  end

  #---------------------------------------------------------------------
  def Xdate.IsWMonthLegal?(nMonth)
    if (nMonth == nil) then
      return false
    end
    return (nMonth.between?(1,12))
  end

  def Xdate.GetLegalHour(nWHour)
    if (Xdate.IsHourLegal?(nWHour)) then
      return nWHour
    else
      return Xdate.GetNowRails().hour
    end
  end

  def Xdate.IsHourLegal?(nWHour)
      return nWHour.between?(0,23) #hours
  end

  def Xdate.GetLegalMinute(nWMin)
    if (Xdate.IsMinuteLegal?(nWMin)) then
      return nWMin
    else
      return Xdate.GetNowRails().min
    end
  end

  def Xdate.IsMinuteLegal?(nWMin)
      return nWMin.between?(0,59) #minutes
  end

  #---------------------------------------------------------------------
  def Xdate.GetBy60Five(nSky,nEarth)
    return @@By60Five[nSky][nEarth]
  end

  #---------------------------------------------------------------------
  def Xdate.GetSkyFive(nSky)
    return (@@SkyFiveTable[nSky])
  end

  #---------------------------------------------------------------------
  def Xdate.GetEarthFive(nEarth)
    return (@@EarthFiveTable[nEarth])
  end

  #---------------------------------------------------------------------
  def Xdate.EastYearToChina(nEYear)
    szNum = ""
    szOut = ""

    m = nEYear - 1911
    if (m <= 0) then
      m = 1 - m;
      szNum = Pm.IntToStr(m)
      szOut = Pm.GetStr("IDS_X_KEY_CHINAYEAR_BEFORE_1")
      szOut += szNum
    elsif (m == 1) then
      szOut = Pm.GetStr("IDS_X_KEY_CHINAYEAR_FIRST_1")
    else
      szNum = Pm.IntToStr(m)
      szOut = szNum
    end
    return szOut
  end

  #---------------------------------------------------------------------
  def Xdate.WestYearToChina(nWYear)
    szNum = ""
    szOut = ""

    m = nWYear - 1911;
    if (m <= 0) then
      m = 1 - m;
      szNum = Pm.IntToStr(m)
      szOut = Pm.GetStr("IDS_X_KEY_CHINAYEAR_BEFORE")
      szOut += szNum
      szOut += Pm.GetStr ("IDS_X_YEAR")
    elsif (m == 1) then
      szOut = Pm.GetStr("IDS_X_KEY_CHINAYEAR_FIRST")
    else
      szNum = Pm.IntToStr(m)
      szOut = Pm.GetStr("IDS_X_KEY_CHINAYEAR")
      szOut += szNum
      szOut += Pm.GetStr("IDS_X_YEAR")
    end
  end

  #---------------------------------------------------------------------
  def Xdate.GetCurTime(remote_ip)
    t = Xdate.GetNow(remote_ip)

    return t.year,t.month,t.day,t.hour,t.min,t.sec
  end
  def Xdate.GetNowRails()
    return Time.now
  end
  #---------------------------------------------------------------------
  # Input the east(農曆)  "Year", and then return the GanZhi of year.
  def Xdate.GetYearGanZhi(nYear)
    #nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,6,1,false)
    nGanZhiYear = ((nYear - Xdate::BASE_YEAR) + Xdate::BASE_GANZHI_YEAR) % 60

    return nGanZhiYear
  end
  # 求出nAfterThisYear之後的第一個nGanZhiYear的年
  def Xdate.GetGanZhiYear(nGanZhiYear,nAfterThisYear)
    nGanZhiAfterThisYear = Xdate.GetYearGanZhi(nAfterThisYear)
    n = nGanZhiYear - nGanZhiAfterThisYear
    if (n < 0) then
      n += 60
    end

    return nAfterThisYear + n,nGanZhiYear
  end
  def Xdate.GetPreGanZhiYear(nGanZhiYear,nAfterThisYear)
    nGanZhiAfterThisYear = Xdate.GetYearGanZhi(nAfterThisYear)
    n = nGanZhiYear - nGanZhiAfterThisYear
    if (n > 0) then
      n -= 60
    end

    return nAfterThisYear + n,nGanZhiYear
  end

  #---------------------------------------------------------------------
  # Input the west "Year" and "Month", and then return the GanZhi of month.
  # nHour is from "0" to "24".
  def Xdate.GetMonthGanZhi(nYear, nMonth)
    nSkyIndex,nEarthIndex = Xdate.GetMonthSkyEarthIndex(nYear, nMonth)
    return Xdate.SkyEarthIndex2GanZhi(nSkyIndex,nEarthIndex)
  end
  def Xdate.GetMonthSkyEarthIndex(nYear, nMonth)
    nYearGanZhi = Xdate.GetYearGanZhi(nYear)
    nYearSkyIndex = Xdate.GetGanZhiSkyIndex(nYearGanZhi)
    nEarthIndex = Earth.Modify(nMonth - 1 + 2)
    nSkyIndex = Xdate.FiveTigerOut(nYearSkyIndex,nEarthIndex)
    return nSkyIndex,nEarthIndex
  end
  def Xdate.GetMonthSkyEarth(nYear, nMonth)
    nSkyIndex,nEarthIndex = Xdate.GetMonthSkyEarthIndex(nYear, nMonth)
    return Sky.SkyIndex2Sky(nSkyIndex),Earth.EarthIndex2Earth(nEarthIndex)
  end
  # 求出nAfterThisYear,nAfterThisMonth之後的第一個nGanZhiMonth的年,月
  def Xdate.GetGanZhiMonth(nGanZhiMonth,nAfterThisYear,nAfterThisMonth)
    nGanZhiAfterThisMonth = Xdate.GetMonthGanZhi(nAfterThisYear,nAfterThisMonth)
    n = nGanZhiMonth - nGanZhiAfterThisMonth
    if (n < 0) then
      n += 60
    end
    nYear,nMonth = Xdate.NextWMonths(nAfterThisYear,nAfterThisMonth,n)
    nGanZhiYear = Xdate.GetYearGanZhi(nYear)

    return nYear,nMonth,nGanZhiYear,nGanZhiMonth
  end

  def Xdate.GetDateGanZhi(nDays)
      nGanZhiDay = (nDays + BASE_GANZHI_DAY) % 60
      return nGanZhiDay
  end

  def Xdate.GetTimeGanZhi(nDays,nETime)
      nGanZhiHour = ((nDays % 5) * 12 + nETime + BASE_GANZHI_HOUR) % 60
      return nGanZhiHour
  end

  #---------------------------------------------------------------------
  #return array of nMonth,nDay,nHour,nMinute,like [[1,1,1,1],[1,22,1,2],...]
  def Xdate.LoadSegTbl()
    # Open Segment Table
#   fileName = File.absolute_path("AInfo.tbl")
    #fileName = "lib/AInfo.tbl"
    filePath = File.expand_path('', __FILE__)
    fileName = File.dirname(filePath) + "/AInfo.tbl"
    #fileName = File.expand_path(fileName)
    if (!File.exist?(fileName)) then
      return nil
    end

    f = File.new(fileName,"r")

    s = f.read(2)
    nBaseYear = s.getbyte(1) * 256 + s.getbyte(0)
    nBaseYear += 138

    s = f.read(2)
    nLastYear = s.getbyte(1) * 256 + s.getbyte(0)
    nLastYear += 249

    if (nBaseYear != BASE_YEAR) then
      f.close
      return nil
    end

    s = f.read()
    f.close

    segArray = Array.new
    segTime = Array.new
    i = 0
    s.each_byte{|c|
      c -= @@ByCode3[segTime.length]
      segTime.push(c)
      if (segTime.length == 4) then
        segArray.push(segTime)
          segTime = Array.new
      end
    }
    return segArray
  end

  def Xdate.ETime2Hour(nETime)
    if (nETime == 12) then
      return 23
    end
    return (nETime * 2)  #出生日(農曆)
  end
  def Xdate.ETime2PanHour(nETime)
    if (nETime == 12) then
      return 0
    end
    return (nETime * 2)  #出生日(農曆)
  end
  def Xdate.ETimeFirstHour(nETime)
    if (nETime == 12 || nETime == 0) then
      return 23
    end
    return (nETime * 2) - 1  #出生日(農曆)
  end

  def Xdate.Hour2ETime(nHour)
    return (nHour + 1) >> 1
  end

  def Xdate.Hour2WTime(nHour)
    nWTime = (nHour + 1) >> 1
    if (nWTime == 12) then
      nWTime = 0
    end
    return nWTime
  end
  def Xdate.Hour2Earth(nHour)
    nWTime = (nHour + 1) >> 1
    if (nWTime == 12) then
      nWTime = 0
    end
    return Earth.EarthIndex2Earth(nWTime)
  end


  def Xdate.GetGanZhi_Str(nGanZhi)
    nSky,nEarth = Xdate.GetGanZhiSkyEarth(nGanZhi)
    sBuf = Sky.GetName(nSky)
    sBuf += Earth.GetName(nEarth)
    return sBuf
  end
  def Xdate.GetGanZhiYearMonthDay_Str(nGanZhiYear,nGanZhiMonth,nGanZhiDay)
    return Xdate.GetGanZhiYear_Str(nGanZhiYear) + Xdate.GetGanZhiMonth_Str(nGanZhiMonth) + Xdate.GetGanZhiDate_Str(nGanZhiDay)
  end
  def Xdate.GetGanZhiYear_Str(nGanZhi)
    sBuf = Xdate.GetGanZhi_Str(nGanZhi)
    sBuf += Pm.GetStr("IDS_X_YEAR")
    return sBuf
  end
  def Xdate.GetGanZhiMonth_Str(nGanZhi)
    sBuf = Xdate.GetGanZhi_Str(nGanZhi)
    sBuf += Pm.GetStr("IDS_X_MONTH")
    return sBuf
  end
  def Xdate.GetGanZhiDate_Str(nGanZhi)
    sBuf = Xdate.GetGanZhi_Str(nGanZhi)
    sBuf += Pm.GetStr("IDS_X_DAY")
    return sBuf
  end
  def Xdate.GetGanZhiTime_Str(nGanZhi)
    sBuf = Xdate.GetGanZhi_Str(nGanZhi)
    sBuf += Pm.GetStr("IDS_X_HOUR")
    return sBuf
  end
  def Xdate.GetGanZhiYear_Str2(nGanZhi)
    sBuf = Xdate.GetGanZhi_Str(nGanZhi)
    return sBuf
  end
  def Xdate.GetGanZhiMonth_Str2(nGanZhi)
    sBuf = Xdate.GetGanZhi_Str(nGanZhi)
    return sBuf
  end
  def Xdate.GetGanZhiDate_Str2(nGanZhi)
    sBuf = Xdate.GetGanZhi_Str(nGanZhi)
    return sBuf
  end
  def Xdate.GetGanZhiTime_Str2(nGanZhi)
    sBuf = Xdate.GetGanZhi_Str(nGanZhi)
    return sBuf
  end

  def Xdate.GetGanZhiSkyEarth(nGanZhi)
    nSky = Xdate.GetGanZhiSky(nGanZhi)
    nEarth = Xdate.GetGanZhiEarth(nGanZhi)
    return nSky,nEarth
  end
  def Xdate.GetGanZhiSkyIndex(nGanZhi)
    return Sky.GetSkyIndex(nGanZhi)
  end
  def Xdate.GetGanZhiEarthIndex(nGanZhi)
    return Earth.GetEarthIndex(nGanZhi)
  end
  def Xdate.GetGanZhiSky(nGanZhi)
    nSkyIndex = Sky.GetSkyIndex(nGanZhi)
    return Sky.SkyIndex2Sky(nSkyIndex)
  end
  def Xdate.GetGanZhiEarth(nGanZhi)
    nEarthIndex = Earth.GetEarthIndex(nGanZhi)
    return Earth.EarthIndex2Earth(nEarthIndex)
  end

  # gan_zhis => [[t1,d1],[t2,d2],...,[tn,dn]]
  # t is tiangan; d is dizhi
  # return [gan_zhi1,gan_zhi2,...]
  def Xdate.MakeGanZhiValues(gan_zhis)
    a = Array.new
    gan_zhis.each do |gd|
      tiangan = gd[0]
      dizhi = gd[1]
      a.push(Xdate.MakeGanZhiValue(tiangan,dizhi))
    end
    a.flatten!
    a.uniq!
    a.sort!
    return a
  end
  def Xdate.MakeGanZhiValue(tiangan,dizhi,tg_add=0,dz_add=0)
    # 變成index 從0開始
    tiangan_index = tiangan - 1
    dizhi_index = dizhi - 1
    if (tiangan == 0 && dizhi == 0) then
      return []
    elsif (tiangan == 0) then
      a = Array.new
      (0...5).each do |i|
        n = dizhi_index + i * 12
        a.push(n)
      end
      return a
    elsif (dizhi == 0) then
      a = Array.new
      (0...6).each do |i|
        n = tiangan_index + i * 10
        a.push(n)
      end
      return a
    else
      if ((tiangan + tg_add) == (dizhi + dz_add)) then
        n = tiangan_index + tg_add
        return [n]
      else
        if ((tiangan + tg_add) < (dizhi + dz_add)) then
          tg_add += 10
        else
          dz_add += 12
        end
        return Xdate.MakeGanZhiValue(tiangan,dizhi,tg_add,dz_add)
      end
    end
  end

  #return addDay,new nETime
  def Xdate.CheckETime12(nETime)
    if (nETime == 12) then
      return 1,0
    else
      return 0,nETime
    end
  end

  def Xdate.GetYearStr(nYear)
    sBuf = ""
    sBuf += nYear.to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    return sBuf
  end

  def Xdate.GetMonthStr(nEMonth,bLeapMonth)
    sBuf = ""
    if (bLeapMonth) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += nEMonth.to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    return sBuf
  end

  def Xdate.GetDateStr(nEDate)
    sBuf = ""
    sBuf += nEDate.to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    return sBuf
  end

  def Xdate.GetHourStr(nWHour)
    sBuf = ""
    sBuf += nWHour.to_s
    sBuf += Pm.GetStr("IDS_X_HOUR")
    return sBuf
  end
  def Xdate.GetETimeStr(nETime)
    sBuf = ""
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(nETime))
    sBuf += Pm.GetStr("IDS_X_HOUR")
    return sBuf
  end
  def Xdate.GetHolidayDayStrs()
    nWeekDay = 6
    return [Xdate.GetWeekDayStr(nWeekDay),Xdate.GetWeekDayStr(nWeekDay+1)]
  end

  def Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)
    nWeekDay = Xdate.GetWWeekDay(nWYear, nWMonth, nWDate)
    return Xdate.GetWeekDayStr(nWeekDay)
  end
  def Xdate.GetWWeekDayFullStr(nWYear, nWMonth, nWDate)
    xing_qi = Xdate.nongminli_str("xing_qi")
    return "#{xing_qi}#{Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)}"
  end
  def Xdate.GetWeekDayStr(nWeekDay)
    return Pm.GetStr("IDS_S_WEEKDAY_#{nWeekDay}")
  end

  def Xdate.GetWMonthDateStr(nWMonth,nWDate)
    return "#{nWMonth}/#{nWDate}"
  end
  def Xdate.is_holiday?(nWYear, nWMonth, nWDate)
    nWeekDay = Xdate.GetWWeekDay(nWYear, nWMonth, nWDate)
    return [6,7].include?(nWeekDay)
  end

  def Xdate.GetWYearMonthDateStr(nWYear,nWMonth,nWDate)
    return "#{nWYear}/#{nWMonth}/#{nWDate}"
  end

  def Xdate.GetChinaWYearMonthDateStr(nWYear,nWMonth,nWDate)
    return "#{Xdate.GetChinaYear(nWYear)}/#{nWMonth}/#{nWDate}"
  end

  def Xdate.GetFullWDateTimeLunarStr(nWYear,nWMonth,nWDate,nWHour,nYearDisplay)
    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
    nETime = Xdate.Hour2ETime(nWHour)
    nEYear,nEMonth,nEDate,bLeapMonth,nETime = Xdate.NextEDateFromTime(nEYear,nEMonth,nEDate,bLeapMonth,nETime)
    return Xdate.GetFullEDateTimeStr(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nYearDisplay)
  end
  def Xdate.GetFullEDateTimeStr(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nYearDisplay)
    sBuf = ""
    sBuf += Xdate.GetMoonDateStr(nEYear,nEMonth,nEDate,bLeapMonth,nYearDisplay)

    if (nETime == 12) then
      nETime = 0
    end
    if (nETime.between?(0,11)) then
      sBuf += Earth.GetName(Earth.EarthIndex2Earth(nETime))
      sBuf += Pm.GetStr("IDS_X_HOUR")
    end
    return sBuf
  end
  def Xdate.GetFullEDateTimeStr_Abbr(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nYearDisplay)
    sBuf = ""
    sBuf += Xdate.GetMoonDateStr_Abbr(nEYear,nEMonth,nEDate,bLeapMonth,nYearDisplay)

    if (nETime == 12) then
      nETime = 0
    end
    if (nETime.between?(0,11)) then
      sBuf += Earth.GetName(Earth.EarthIndex2Earth(nETime))
      sBuf += Pm.GetStr("IDS_X_HOUR")
    else
      sBuf += "X"
      sBuf += Pm.GetStr("IDS_X_HOUR")
    end
    return sBuf
  end

  def Xdate.GetFullEDateTimeNumStr(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nYearDisplay)
    sBuf = ""
    sBuf += Xdate.GetNumberEDateTimeStr(nEYear,nEMonth,nEDate,bLeapMonth,nYearDisplay)

    if (nETime.between?(0,11)) then
    # if (nWHour.between?(0,23)) then
      sBuf += " "
      sBuf += Earth.GetName(Earth.EarthIndex2Earth(nETime))
      # sBuf += nWHour.to_s
      sBuf += Pm.GetStr("IDS_X_HOUR")
    end
    return sBuf
  end

  def Xdate.GetSolarDateStr(nWYear,nWMonth,nWDate,nYearDisplay)
    sBuf = Xdate.YearDisplayStr(nWYear,nYearDisplay)

    sBuf += Xdate.GetMonthStr(nWMonth,false)

    sBuf += Xdate.GetDateStr(nWDate)
    return sBuf
  end

  def Xdate.YearDisplayStr(nYear,nYearDisplay)
    sBuf = Xdate.YearDisplay(nYear,nYearDisplay)
    sBuf += Pm.GetStr("IDS_X_YEAR")
  end

  def Xdate.GetMoonDateStr(nEYear,nEMonth,nEDate,bLeapMonth,nYearDisplay)
    sBuf = ""
    if (Xdate.IsYearLegal?(nEYear)) then
      if (nYearDisplay == Cfate::PAN_DISPLAY_GANZHI) then
        sBuf += Xdate.GetLunarYearGanZhiStr(nEYear)
      else
        sBuf += Xdate.GetNumberYearStr(nEYear,nYearDisplay)
      end
    end

    if (Xdate.IsEMonthLegal?(nEYear,nEMonth,bLeapMonth)) then
      sBuf += Xdate.GetMonthStr(nEMonth,bLeapMonth)
    end

    if (Xdate.IsEDateLegal?(nEYear,nEMonth,nEDate,bLeapMonth)) then
      sBuf += Xdate.GetDateStr(nEDate)
    end
    return sBuf
  end
  def Xdate.GetMoonDateStr_Abbr(nEYear,nEMonth,nEDate,bLeapMonth,nYearDisplay)
    sBuf = ""
    if (Xdate.IsYearLegal?(nEYear)) then
      if (nYearDisplay == Cfate::PAN_DISPLAY_GANZHI) then
        sBuf += Xdate.GetLunarYearGanZhiStr(nEYear)
      else
        sBuf += Xdate.GetNumberYearStr(nEYear,nYearDisplay)
      end
    else
      sBuf += "XXXX"
      sBuf += Pm.GetStr("IDS_X_YEAR")
    end

    if (Xdate.IsWMonthLegal?(nEMonth)) then
      sBuf += Xdate.GetMonthStr(nEMonth,bLeapMonth)
    else
      sBuf += "XX"
      sBuf += Pm.GetStr("IDS_X_MONTH")
    end

    if (nEDate.between?(1,30)) then
      sBuf += Xdate.GetDateStr(nEDate)
    else
      sBuf += "XX"
      sBuf += Pm.GetStr("IDS_X_DAY")
    end
    return sBuf
  end
  def Xdate.GetNumberYearStr(nYear,nYearDisplay)
    sBuf = Xdate.YearDisplayOnlyNumber(nYear,nYearDisplay)
    sBuf += Pm.GetStr("IDS_X_YEAR")
    return sBuf
  end

  def Xdate.GetMoonDateNumStr(nEYear,nEMonth,nEDate,bLeapMonth,nYearDisplay)
    sBuf = ""
    if (Xdate.IsYearLegal?(nEYear)) then
      sBuf += Xdate.YearDisplayOnlyNumber(nEYear,nYearDisplay)
    end

    if (Xdate.IsEMonthLegal?(nEYear,nEMonth,bLeapMonth)) then
      sBuf += "/"
      sBuf += Xdate.GetMonthStr(nEMonth,bLeapMonth)
    end

    if (Xdate.IsEDateLegal?(nEYear,nEMonth,nEDate,bLeapMonth)) then
      sBuf += Xdate.GetDateStr(nEDate)
    end
    return sBuf
  end

  def Xdate.YearDisplay(nYear,nYearDisplay=Cfate::PAN_DISPLAY_WEST)
    if (nYearDisplay == Cfate::PAN_DISPLAY_WEST) then
      return nYear.to_s
    else
      return Xdate.GetChinaYearStr(nYear)
    end
  end
  def Xdate.YearDisplayOnlyNumber(nYear,nYearDisplay)
    if (nYearDisplay == Cfate::PAN_DISPLAY_WEST) then
      return nYear.to_s
    else
      return Xdate.GetChinaYear(nYear).to_s
    end
  end

  def Xdate.Year2West(nYear,nYearDisplay)
    if (nYearDisplay == Cfate::PAN_DISPLAY_WEST) then
      return nYear
    else
      return nYear + 1911
    end
  end

  def Xdate.GetFullDateTimeStr(nWYear,nWMonth,nWDate,nWHour,nMin,nSec,nYearDisplay)
    # sDate = "#{Xdate.YearDisplayOnlyNumber(nWYear,nYearDisplay)}/#{nWMonth}/#{nWDate}"
    # sDate = "#{Xdate.YearDisplay(nWYear,nYearDisplay)}/#{nWMonth}/#{nWDate}"
    sDate = ""
    if (nWYear.between?(1801,2099)) then
      sDate += Xdate.YearDisplay(nWYear,nYearDisplay)
    end
    if (nWMonth.between?(1,12)) then
      sDate += "/"
      sDate += nWMonth.to_s
    end
    if (nWDate.between?(1,31)) then
      sDate += "/"
      sDate += nWDate.to_s
    end
    if (nWHour.between?(0,23)) then
      sDate += " "
      sDate += nWHour.to_s
    end
    if ((nMin != nil) && (nMin >= 0)) then
      sDate += ":"
      sDate += nMin.to_s
      if ((nSec != nil) && (nSec >= 0)) then
        sDate += ":"
        sDate += nSec.to_s
      end
    else
      sDate += Pm.GetStr("IDS_X_HOUR")
    end
    return sDate
  end
  def Xdate.GetFullDateTimeStr_Abbr(nWYear,nWMonth,nWDate,nWHour,nMin,nSec,nYearDisplay)
    # sDate = "#{Xdate.YearDisplayOnlyNumber(nWYear,nYearDisplay)}/#{nWMonth}/#{nWDate}"
    # sDate = "#{Xdate.YearDisplay(nWYear,nYearDisplay)}/#{nWMonth}/#{nWDate}"
    sDate = ""
    if (nWYear.between?(1801,2099)) then
      sDate += Xdate.YearDisplay(nWYear,nYearDisplay)
    else
      sDate += "XXXX"
    end
    sDate += "/"
    if (nWMonth.between?(1,12)) then
      sDate += nWMonth.to_s
    else
      sDate += "XX"
    end
    sDate += "/"
    if (nWDate.between?(1,31)) then
      sDate += nWDate.to_s
    else
      sDate += "XX"
    end
    sDate += " "
    if (nWHour.between?(0,23)) then
      sDate += nWHour.to_s
    else
      sDate += "XX"
    end
    if ((nMin != nil) && (nMin >= 0)) then
      sDate += ":"
      sDate += nMin.to_s
      if ((nSec != nil) && (nSec >= 0)) then
        sDate += ":"
        sDate += nSec.to_s
      end
    else
      sDate += Pm.GetStr("IDS_X_HOUR")
    end
    return sDate
  end

  def Xdate.GetFullWDateStr(nWYear,nWMonth,nWDate,nYearDisplay)
    # sDate = "#{Xdate.YearDisplayOnlyNumber(nWYear,nYearDisplay)}/#{nWMonth}/#{nWDate}"
    # sDate = "#{Xdate.YearDisplay(nWYear,nYearDisplay)}/#{nWMonth}/#{nWDate}"
    sDate = ""
    if (nWYear.between?(1801,2099)) then
      sDate += Xdate.YearDisplayOnlyNumber(nWYear,nYearDisplay)
    end
    if (nWMonth.between?(1,12)) then
      sDate += "/"
      sDate += nWMonth.to_s
    end
    if (nWDate.between?(1,31)) then
      sDate += "/"
      sDate += nWDate.to_s
    end

    return sDate
  end

  def Xdate.GetNumberEDateTimeStr(nEYear,nEMonth,nEDate,bLeapMonth,nYearDisplay)
    sDate = ""
    if (nEYear.between?(1801,2099)) then
      sDate += Xdate.YearDisplayOnlyNumber(nEYear,nYearDisplay)
    end
    if (nEMonth.between?(1,12)) then
      sDate += "/"
      if (bLeapMonth) then
        sDate += Pm.GetStr("IDS_X_LEAP")
      end
      sDate += nEMonth.to_s
    end
    if (nEDate.between?(1,30)) then
      sDate += "/"
      sDate += nEDate.to_s
    end
    return sDate
  end

  def Xdate.GetMonthDateStr(nEMonth,nEDate,bLeapMonth)
    sBuf = ""
    if (bLeapMonth) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += "#{nEMonth}/#{nEDate}"
    return sBuf
  end

  def Xdate.GetEMonthBigSmall(nEDays)
    sBuf = ""
    if (nEDays == 30) then
      sBuf += Pm.GetStr("IDS_S_EDAYS_BIG")
    else
      sBuf += Pm.GetStr("IDS_S_EDAYS_SMALL")
    end
    return sBuf
  end

  def Xdate.GetEYearAnimal(nEYear)
    nEarth = Earth.GetYearEarth(nEYear)
    sBuf = Xdate.GetAnimal(nEarth)
    return sBuf
  end

  def Xdate.GetAnimal(nEarth)
    sBuf = Pm.GetStr("IDS_S_ANIMAL_#{nEarth}")
    return sBuf
  end

  def Xdate.GetEYearOldStr(nBirthEYear,nFlowEYear)
      nFlowAge = nFlowEYear - nBirthEYear + 1
      return Xdate.GetYearOldStr(nFlowAge)
  end

  def Xdate.GetYearOldStr(nYearOld)
    return (nYearOld.to_s + Pm.GetStr("IDS_S_YEAR_OLD"))
  end

  def Xdate.GetETimeRangeStr(nEarth)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)
    nWHourStart = (nEarthIndex * 2) - 1
    if (nWHourStart < 0) then
      nWHourStart += 24
    end
    nWHourStop = (nEarthIndex * 2) + 1

    sk = "%02d" % nWHourStart
    sm = "%02d" % 0
    sk1 = "%02d" % nWHourStop
    sm1 = "%02d" % 0
    sBuf = "#{sk}:#{sm} ~ #{sk1}:#{sm1}"
    return sBuf
  end
  def Xdate.GetETimeRangeStr2(nEarth)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)
    nWHourStart = (nEarthIndex * 2) - 1
    if (nWHourStart < 0) then
      nWHourStart += 24
    end
    nWHourStop = (nEarthIndex * 2) + 1

    sk = "%02d" % nWHourStart
    sk1 = "%02d" % nWHourStop
    sBuf = "#{sk}~#{sk1}"
    return sBuf
  end

  def Xdate.GetEMinRangeStr(nETime,nEMin)
    nWHourStart,nWHourStop = Xdate.GetEMinHour(nETime,nEMin)
    sk = "%02d" % (nWHourStart)
    sm = "%02d" % Xdate.GetEMin(nEMin)
    sk1 = "%02d" % (nWHourStop)
    sm1 = "%02d" % Xdate.GetEMin(nEMin + 1)
    sBuf = "#{sk}:#{sm} ~ #{sk1}:#{sm1}"
    return sBuf
  end
  def Xdate.GetEMinStartStr(nETime,nEMin)
    nWHourStart,nMinute = Xdate.GetEMinStart(nETime,nEMin)
    return "#{nWHourStart}:#{nMinute}"
  end
  def Xdate.GetEMinStart(nETime,nEMin)
    nWHourStart,nWHourStop = Xdate.GetEMinHour(nETime,nEMin)
    return nWHourStart,Xdate.GetEMin(nEMin)
  end

  def Xdate.GetEMinHour(nETime,nEMin)
    nWHourStart = (nETime * 2) - 1
    if (nWHourStart < 0) then
      nWHourStart += 24
    end
    nWHourStart = (nWHourStart + (nEMin / 6))
    if (nWHourStart == 24) then
      nWHourStart = 0
    end
    nWHourStop = (nWHourStart + ((Xdate.GetEMin(nEMin) + 10) / 60))
    return nWHourStart,nWHourStop
  end

  def Xdate.GetEMin(nEMin)
    return ((nEMin % 6) * 10)
  end

  def Xdate.GetChinaYear(nYear)
    return nYear - 1911
  end

  def Xdate.GetChinaYearStr(nYear)
    nChinaYear = Xdate.GetChinaYear(nYear)

    if (nChinaYear == 1) then
      sBuf =  "#{Pm.GetStr('IDS_X_KEY_CHINAYEAR_FIRST')}"
    elsif (nChinaYear > 1) then
      sBuf =  "#{Pm.GetStr('IDS_X_KEY_CHINAYEAR')}#{nChinaYear}"
    else
      i = 1 - nChinaYear
      sBuf =  "#{Pm.GetStr('IDS_X_KEY_CHINAYEAR_BEFORE')}#{i.abs}"
         end
         return sBuf
  end

  def Xdate.makeMonthValue(calType,leap,month)
    if (calType == Xdate::CT_SOLAR) then
      return month
    else
      nLeap = 0
      if (leap == true) then
        nLeap = 100
      end
      return (nLeap + month)
    end
  end

  def Xdate.monthValue2month(monthValue)
    if (monthValue == Xdate::DATE_NIL) then
      return Xdate::DATE_NIL,false
    end
    nMonthValue = monthValue.to_i
    if (nMonthValue > 100) then
      return (nMonthValue - 100),true
    else
      return nMonthValue,false
    end
  end

  def Xdate.date_s2int(date)
    if (date == nil) then
      return Xdate::DATE_NIL
    end
    if (date.length == 0) then
      return Xdate::DATE_NIL
    else
      return date.to_i
    end
  end

  def Xdate.date_int2s(date)
    if (date == Xdate::DATE_NIL) then
      return Xdate::DATE_NIL.to_s
    end
    return date.to_s
  end

  def Xdate.GetSegmentFromWest_Eightwords(nWYear,nWMonth,nWDate,nHour,nMinute)
    if (!Xdate.IsWDateLegal?(nWYear, nWMonth, nWDate,nHour, nMinute)) then
      return -1
    end
    bFrontYear = false

    Xdate.Init()
      nSegment = -1
      nSegmentTime = 0
      nSegCountPerYear = Xdate::SEGMENT_COUNT_PER_YEAR
      nYearCount = nWYear - Xdate::BASE_YEAR
      nSegMonthCount = nYearCount * 12

      # nFirstSeg = 大雪 (國曆年前一個節)
      nFrontSeg = nYearCount * nSegCountPerYear - 2
    aFrontSeg = @@SegTimeArray[nFrontSeg]
      nFrontSegDays = Xdate.GetWDaysThisYear(nWYear - 1, aFrontSeg[Xdate::ST_MONTH], aFrontSeg[Xdate::ST_DAY])
      nFrontMinutes = aFrontSeg[Xdate::ST_HOUR] * 60 + aFrontSeg[Xdate::ST_MINUTE]

    nDays = Xdate.GetWDaysThisYear(nWYear - 1, aFrontSeg[Xdate::ST_MONTH], 31)
      nFrontSegOffset = nDays - nFrontSegDays

      # nFirstSeg = 小寒 (國曆年第一個節氣)
      nFirstSeg = nYearCount * nSegCountPerYear
    aCurSeg = @@SegTimeArray[nFirstSeg]
      nDays = Xdate.GetWDaysThisYear(nWYear, nWMonth, nWDate)
      if (nHour > 0) then
          nAllMinutes = nHour * 60 + nMinute
      else
          nAllMinutes = nMinute
    end
      nSegmentDays = 0
      (0..nSegCountPerYear).each do |i|
        if (nSegment == -1) then
            nSegDays = Xdate.GetWDaysThisYear(nWYear, aCurSeg[Xdate::ST_MONTH], aCurSeg[Xdate::ST_DAY])
            if (nDays == nSegDays) then
                nCurMinutes = aCurSeg[Xdate::ST_HOUR] * 60 + aCurSeg[Xdate::ST_MINUTE]
                if (nAllMinutes < nCurMinutes) then
                    if (i == 0) then
                        nSegment = nSegCountPerYear - 1 # 屬去年冬至
                        nWYear -= 1
                        bFrontYear = true
                        nSegmentDays = nFrontSegOffset + nDays
                    else
                        nSegment = i - 1
                        nSegmentDays = nDays - nFrontSegOffset
                    end
                    if (nAllMinutes < nFrontMinutes) then
                        nSegmentDays -= 1
                        nSegmentTime = 24 * 60 + nAllMinutes - nFrontMinutes
                    else
                        nSegmentTime = nAllMinutes - nFrontMinutes
                    end
                else
                    nSegment = i
                    if (i % 2 == 0) then
                       nSegmentDays = 0
                       nSegmentTime = nAllMinutes - nCurMinutes
                    else
                        nSegmentDays = nDays - nFrontSegOffset
                        if (nAllMinutes < nFrontMinutes) then
                            nSegmentDays -= 1
                            nSegmentTime = 24 * 60 + nAllMinutes - nFrontMinutes
                        else
                            nSegmentTime = nAllMinutes - nFrontMinutes
                        end
                    end
                end
            elsif (nDays < nSegDays) then # The nWDate < 01/06 (小寒)
                if (i == 0) then
                    nSegment = nSegCountPerYear - 1 # 屬去年冬至
                    nWYear -= 1
                    bFrontYear = true
                    nSegmentDays = nFrontSegOffset + nDays
                    if (nAllMinutes < nFrontMinutes) then
                        nSegmentDays -= 1
                        nSegmentTime = 24 * 60 + nAllMinutes - nFrontMinutes
                    else
                        nSegmentTime = nAllMinutes - nFrontMinutes
                    end
                else
                    nSegment = i - 1
                    nSegmentDays = nDays - nFrontSegOffset
                    if (nAllMinutes < nFrontMinutes) then
                        nSegmentDays -= 1
                        nSegmentTime = 24 * 60 + nAllMinutes - nFrontMinutes
                    else
                        nSegmentTime = nAllMinutes - nFrontMinutes
                    end
                end
            else
                if (i % 2 == 0 && nDays < nSegDays + 60) then
                    nFrontSegOffset = nSegDays
                  nFrontMinutes = aCurSeg[Xdate::ST_HOUR] * 60 + aCurSeg[Xdate::ST_MINUTE]
                end
            end
            nFirstSeg += 1
        aCurSeg = @@SegTimeArray[nFirstSeg]
        end
      end
      if (nSegment == -1) then
          nSegment = nSegCountPerYear - 1     # 冬至
          nSegmentDays = nDays - nFrontSegOffset
          if (nAllMinutes < nFrontMinutes) then
              nSegmentDays -= 1
              nSegmentTime = 24 * 60 + nAllMinutes - nFrontMinutes
          else
              nSegmentTime = nAllMinutes - nFrontMinutes
          end
      end

      return nSegment,nSegmentDays,nSegmentTime
  end

  def Xdate.GetWDateForFirstEDay(nWYear)
    return Xdate.East2West(nWYear,1,1,false)
  end

  # ---------------------------------------------------------------------
  # Input the west "Year" and "Month", and then return the Segment (節氣).
  # nHour is from "0" to "24".
  def Xdate.GetStandupFive (nWYear, nWMonth, nWDate, nHour, nMinute)
      nSegment = -1
      nSegCountPerYear = Xdate::SEGMENT_COUNT_PER_YEAR
      nYearCount = nWYear - Xdate::BASE_YEAR
      nSegMonthCount = nYearCount * 12
      nDays = Xdate.GetWDaysThisYear(nWYear, nWMonth, nWDate)
      if (nHour > 0) then
          nAllMinutes = nHour * 60 + nMinute
      else
          nAllMinutes = nMinute
      end
      # nFirstStandSeg = 立春
      nFirstSeg = nYearCount * nSegCountPerYear + 2
      segTime = @@SegTimeArray[nFirstSeg]
      nSegDays = Xdate.GetWDaysThisYear(nWYear, segTime[Xdate::ST_MONTH], segTime[Xdate::ST_DAY])
      nFrontMinutes = segTime[Xdate::ST_HOUR] * 60 + segTime[Xdate::ST_MINUTE]
      nDiffSegDays = nSegDays - nDays
      if (nSegDays >= nDays && (nDiffSegDays < 18 || (nDiffSegDays == 18 && nFrontMinutes <= nAllMinutes))) then
          nStandFive = 3  # 四季屬土
      elsif (nSegDays > nDays) then
          nStandFive = 0
      else
          # nSecondStandSeg = 立夏
          nFirstSeg = nYearCount * nSegCountPerYear + 8
        segTime = @@SegTimeArray[nFirstSeg]
        nSegDays = Xdate.GetWDaysThisYear(nWYear, segTime[Xdate::ST_MONTH], segTime[Xdate::ST_DAY])
        nFrontMinutes = segTime[Xdate::ST_HOUR] * 60 + segTime[Xdate::ST_MINUTE]
          nDiffSegDays = nSegDays - nDays
          if (nSegDays >= nDays && (nDiffSegDays < 18 || (nDiffSegDays == 18 && nFrontMinutes <= nAllMinutes))) then
              nStandFive = 3
          elsif (nSegDays > nDays) then
              nStandFive = 1
          else
              # nThirdStandSeg = 立秋
              nFirstSeg = nYearCount * nSegCountPerYear + 14
          segTime = @@SegTimeArray[nFirstSeg]
          nSegDays = Xdate.GetWDaysThisYear(nWYear, segTime[Xdate::ST_MONTH], segTime[Xdate::ST_DAY])
          nFrontMinutes = segTime[Xdate::ST_HOUR] * 60 + segTime[Xdate::ST_MINUTE]
              nDiffSegDays = nSegDays - nDays
              if (nSegDays >= nDays && (nDiffSegDays < 18 || (nDiffSegDays == 18 && nFrontMinutes <= nAllMinutes))) then
                  nStandFive = 3
              elsif (nSegDays > nDays) then
                  nStandFive = 2
              else
                  # nForthStandSeg = 立冬
                  nFirstSeg = nYearCount * nSegCountPerYear + 20
            segTime = @@SegTimeArray[nFirstSeg]
            nSegDays = Xdate.GetWDaysThisYear(nWYear, segTime[Xdate::ST_MONTH], segTime[Xdate::ST_DAY])
            nFrontMinutes = segTime[Xdate::ST_HOUR] * 60 + segTime[Xdate::ST_MINUTE]
                  nDiffSegDays = nSegDays - nDays
                  if (nSegDays >= nDays && (nDiffSegDays < 18 || (nDiffSegDays == 18 && nFrontMinutes <= nAllMinutes))) then
                      nStandFive = 3
                  elsif (nSegDays > nDays) then
                      nStandFive = 4
                  else
                      nStandFive = 0
                  end
              end
          end
      end
      return nStandFive
  end

  #---------------------------------------------------------------------
  # 2002.06.18
  # Input the west "Year" and "Month", and then return the Segment (節氣).
  # nHour is from "0" to "24".
  # Get the days between the Input Time and lpCurSeg (or lpNextSeg)
  def Xdate.GetNextSegmentDays(nWYear,nWMonth,nWDate,nHour,nMinute,bNextSeg,bMustSeg=true)
    nSegYear = nWYear
      nDiffMins = nDiffHours = nDiffDays = 0
      nSegment = -1
      nSegCountPerYear = Xdate::SEGMENT_COUNT_PER_YEAR
      nYearCount = nWYear - Xdate::BASE_YEAR
      nSegMonthCount = nYearCount * 12
      # nFirstSeg = 小寒 (國曆年第一個節, 小寒為'節', 大寒為'氣')
      nFirstSeg = nYearCount * nSegCountPerYear
      segTimeCur = @@SegTimeArray[nFirstSeg]
    nDays = Xdate.GetWDaysThisYear(nWYear, nWMonth, nWDate)
      if (nHour >= 0) then
        nAllMinutes = nHour * 60 + nMinute
      else
        nAllMinutes = nMinute
      end
      (0...nSegCountPerYear).each do |i|
        if (nSegment == -1) then
        nSegDays = Xdate.GetWDaysThisYear(nWYear, segTimeCur[Xdate::ST_MONTH], segTimeCur[Xdate::ST_DAY])
            if (nDays == nSegDays && nHour >= 0) then
                bSameDay = true
            nCurMinutes = segTimeCur[Xdate::ST_HOUR] * 60 + segTimeCur[Xdate::ST_MINUTE]
                if (nAllMinutes < nCurMinutes) then
                    if (i == 0) then
                        nSegment = nSegCountPerYear - 1  # 屬去年冬至
                        nSegYear -= 1
                    else
                        nSegment = i - 1
                    end
                end
            elsif (nDays < nSegDays) then # The nWDay < 01/06 (小寒)
                if (i == 0) then
                    nSegment = nSegCountPerYear - 1  # 屬去年冬至
                    nSegYear -= 1
                else
                    nSegment = i - 1
                end
            end
        end
          if (nSegment == -1) then
              nFirstSeg += 1
          segTimeCur = @@SegTimeArray[nFirstSeg]
        end
      end

      if (nSegment == -1) then
          nSegment = nSegCountPerYear - 1    # 冬至
      end

    # 以下的計算只考慮'節', 跳過'氣'
      # nNextSegYear = nWYear;
      # if (nSegment == SEGMENT_WINTER)
      #     nNextSegYear ++;
      # lpCurSeg == nSegment == Previouse Segment;
      nFirstSeg -= 1
      segTimeCur = @@SegTimeArray[nFirstSeg]
      if ((nSegment % 2) == 0) then
        segTimeNext = @@SegTimeArray[nFirstSeg + 2]
      else
        segTimeNext = @@SegTimeArray[nFirstSeg + 1]
      if (bMustSeg || bNextSeg) then
          nFirstSeg -= 1
          segTimeCur = @@SegTimeArray[nFirstSeg]
        nSegment -= 1
        if (nSegment < 0) then
          nSegment += Xdate::SEGMENT_COUNT_PER_YEAR
          nSegYear = nWYear - 1
        end
      end
      end
      nNewSeg = nSegment
      nNextSegYear = nWYear
      if (bNextSeg) then
          nNewSeg = nSegment + 2
          if (nNewSeg >= Xdate::SEGMENT_COUNT_PER_YEAR) then
              nNewSeg -= Xdate::SEGMENT_COUNT_PER_YEAR
              nNextSegYear = nSegYear + 1
          end
      nNextSegDays = Xdate.GetWDaysThisYear(nNextSegYear, segTimeNext[Xdate::ST_MONTH], segTimeNext[Xdate::ST_DAY])
          if (nNextSegYear > nWYear) then
        nNextSegDays += Xdate.GetWDaysThisYear(nWYear, 12, 31)
      end

          # 2002.06.18
          # nDiffDays = nNextSegDays - nDays;
          nDiffDays = nNextSegDays - nDays
      nCurMinutes = segTimeNext[Xdate::ST_HOUR] * 60 + segTimeNext[Xdate::ST_MINUTE]
          nOldMins = nAllMinutes
          nNewMins = nCurMinutes
      else
          nNewSeg = nSegment
      nCurSegDays = Xdate.GetWDaysThisYear(nSegYear, segTimeCur[Xdate::ST_MONTH], segTimeCur[Xdate::ST_DAY])
          if (nSegYear < nWYear) then
        nDays += Xdate.GetWDaysThisYear(nSegYear, 12, 31)
      end
          nDiffDays = nDays - nCurSegDays
      nCurMinutes = segTimeCur[Xdate::ST_HOUR] * 60 + segTimeCur[Xdate::ST_MINUTE]
          nOldMins = nCurMinutes
          nNewMins = nAllMinutes
      end

      if (nNewMins >= nOldMins) then
          nDiffMins = nNewMins - nOldMins
          nDiffHours = nDiffMins / 60
          nDiffMins = nDiffMins % 60
      else
          nDiffDays -= 1
          nDiffMins = nNewMins - nOldMins
          nDiffMins += 1440 # 24 Hours * 60 Minutes
          nDiffHours = nDiffMins / 60
          nDiffMins = nDiffMins % 60
      end

      hNextSeg = Hash.new
      hNextSeg[Xdate::Segment] = nSegment
      hNextSeg[Xdate::Sd_Segment] = nNewSeg
      hNextSeg[Xdate::Sd_Days] = nDiffDays
      hNextSeg[Xdate::Sd_Hours] = nDiffHours
      hNextSeg[Xdate::Sd_Mins] = nDiffMins
      return hNextSeg
  end

  def Xdate.GetWestDate(nWYear,nWMonth,nWDate,nDates)
    nDays = 0

      nDays = Xdate.GetTotalWDaysFrom190011(nWYear, nWMonth, nWDate)
      nDays += nDates
      nDays += 30;
      nWYear,nWMonth,nWDate = Xdate.GetWDateFrom190011Days(nDays)

    return nWYear,nWMonth,nWDate
  end

  def Xdate.GetWDateFrom190011Days(nDays)
    iY = 1900
    while (nDays > 0)
      niYDays = Xdate.GetWYearDays(iY)
      if (nDays > niYDays) then
        nDays -= niYDays
      else
        nWYear = iY
        mArray = Xdate.GetWMonthArray(nWYear)
        iM = 0
        while (nDays > 0)
          if (nDays > mArray[iM]) then
            nDays -= mArray[iM]
          else
            nWMonth = iM + 1
            nWDate = nDays
            nDays = 0
          end
          iM += 1
        end
      end
      iY += 1
    end
    return nWYear,nWMonth,nWDate
  end

  def Xdate.gHouse_GetNow(ip)
    t = Xdate.GetNow(ip)
    nWYear,nWMonth,nWDate,nWHour,nWMin = t.year,t.month,t.day,t.hour,t.min
    nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.West2East_Pan(nWYear,nWMonth,nWDate,nWHour,nWMin)
    return nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin
  end
# # Use the city ipv6 database:
# GeoIP.new('GeoLiteCityv6.dat').city('::*************')
# => ["::*************", "::*************", "IT", "ITA", "Italy", "EU", "05", "Piacenza", "", 45.016699999999986, 9.666699999999992, nil, nil, "Europe/Rome"]
# >> pdt = ActiveSupport::TimeZone.new("Pacific Time (US & Canada)")
# => #<ActiveSupport::TimeZone:0x10b0bd040 @current_period=nil, @tzinfo=#<TZInfo::TimezoneProxy: America/Los_Angeles>, @name="Pacific Time (US & Canada)", @utc_offset=nil>
# >> pdt.now
# => Wed, 12 Sep 2012 17:59:25 PDT -07:00
# >> pdt.now.formatted_offset
# => "-07:00"

  def Xdate.West2East_Pan(nWYear,nWMonth,nWDay,nWHour,nWMin)
    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDay)
    nETime = Xdate.Hour2ETime(nWHour)
    nEMin = Xdate.GetPanMinute(nWHour,nWMin)
    return nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin
  end


  def Xdate.GetPanMinute(nHour,nMin)
    return ((nMin / 10) + (((nHour + 1) % 2) * 6))    #分鐘以10分鐘為一單位，一個時辰共有12個單位
  end

  def Xdate.GetPanWMinute(nEMin)
    return ((nEMin % 6) * 10)    #分鐘以10分鐘為一單位，一個時辰共有12個單位
  end


  def Xdate.GetYearOld(nBirthEYear,nFlowEYear)
    # if (!Xdate.IsYearLegal?(nFlowEYear)) then
    #   return 1
    # end
    if (!Xdate.IsYearLegal?(nBirthEYear)) then
      return 1
    end
    if (nBirthEYear > nFlowEYear) then
      return 0
    end
      nFlowAge = nFlowEYear - nBirthEYear + 1
      return nFlowAge
  end

  def Xdate.GetYearStr_WC(nYear,par_YearDisplay)
    if (par_YearDisplay == Cfate::PAN_DISPLAY_WEST) then
      return Xdate.GetYearStr(nYear)
    else
      return Xdate.GetYearStr(Xdate.GetChinaYear(nYear))
    end
  end

  def Xdate.get_pan_pars(userId)
    panPar = IfatePar.check_userid_xdate(userId).first
    if (panPar == nil) then
      hUserType = Xdate.pan_par_assign(nil,userId)
    else
      hUserType = Xdate.pan_par_assign(panPar.hPars,userId)
    end
    return hUserType
  end
  def Xdate.pan_par_assign(hIn,userId)
    hUserType = Hash.new

    hUserType[Cfate::PAR_PAN_YEAR_DISPLAY] = Cfate::PAN_DISPLAY_WEST

    return hUserType
  end
  # 客戶購買的功能參數
  def Xdate.product_func_par_assign(hdbInput,hDefault)
    if (hdbInput == nil) then
      hdbInput = Hash.new
    end
    bhasDefault = true
    if (hDefault == nil) then
      bhasDefault = false
      hDefault = Hash.new
    end
    hApFunc = Hash.new
    hDemo = Xdate.getDemoApFuncHash()
    if (hDemo == nil) then
      if (bhasDefault) then
        hDefault.each {|key,value|
          hApFunc[key] = Cfate.ParValueCheck(key,hdbInput[key],hDefault[key])
        }
      else
        hdbInput.each {|key,value|
          hApFunc[key] = Cfate.ParValueCheck(key,hdbInput[key],hDefault[key])
        }
      end
    else
      hDemo.each {|key,value|
        hApFunc[key] = Cfate.ParValueCheck(key,hdbInput[key],hDefault[key])
      }
    end
    return hApFunc
  end
  def Xdate.func_assign(hInput)
    hOutput = Hash.new
    hInput.each {|key,value|
      hOutput[key] = Cfate.ParValueCheck(key,hInput[key],hInput[key])
    }
    return hOutput
  end
  def Xdate.getDemoApFuncHash()
    oProduct = Product.check_demo.check_name("xdate").last
    if (oProduct == nil) then
      return nil
    end
    return Cfate.pan_par_dbfield2hash(oProduct.func_par)
  end

  def Xdate.time_now_ip(ip)
    c = GeoIP.new(Rails.root.join('lib','geodata','GeoLiteCity.dat')).city(ip)
    if (c == nil) then
      return DateTime.now
    end
    # offset = ActiveSupport::TimeZone[c.timezone].utc_offset
    # t = Time.now
    return DateTime.now.in_time_zone(c.timezone)
  end
  def Xdate.GetNow(ip="127.0.0.1")
    return Xdate.time_now_ip(ip)
  end
  def Xdate.west_date_str_array(y,m,d,h,bOnlyNumber=false)
    a = Array.new

    a.push(y)
    a.push(Pm.GetStr("IDS_X_YEAR")) if (!bOnlyNumber)

    a.push(m)
    a.push(Pm.GetStr("IDS_X_MONTH")) if (!bOnlyNumber)

    a.push(d)
    a.push(Pm.GetStr("IDS_X_DAY")) if (!bOnlyNumber)

    a.push(h)
    a.push(Pm.GetStr("IDS_X_HOUR")) if (!bOnlyNumber)

    return a
  end
  def Xdate.china_date_str_array(y,m,d,h,bOnlyNumber=false)
    a = Array.new

    a.push(Xdate.GetChinaYear(y))
    a.push(Pm.GetStr("IDS_X_YEAR")) if (!bOnlyNumber)

    a.push(m)
    a.push(Pm.GetStr("IDS_X_MONTH")) if (!bOnlyNumber)

    a.push(d)
    a.push(Pm.GetStr("IDS_X_DAY")) if (!bOnlyNumber)

    a.push(h)
    a.push(Pm.GetStr("IDS_X_HOUR")) if (!bOnlyNumber)

    return a
  end
  def Xdate.ganzhi_date_str(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,bSpace=false)
    sBuf = Xdate.GetGanZhi_Str(nGanZhiYear)
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += " " if bSpace

    sBuf += Xdate.GetGanZhi_Str(nGanZhiMonth)
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += " " if bSpace

    sBuf += Xdate.GetGanZhi_Str(nGanZhiDay)
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += " " if bSpace

    sBuf += Xdate.GetGanZhi_Str(nGanZhiHour)
    sBuf += Pm.GetStr("IDS_X_HOUR")

    return sBuf
  end
  def Xdate.ganzhi_date_str2(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,bSpace=false)
    sBuf = Xdate.GetGanZhi_Str(nGanZhiYear)
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += " " if bSpace

    sBuf += Xdate.GetGanZhi_Str(nGanZhiMonth)
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += " " if bSpace

    sBuf += Xdate.GetGanZhi_Str(nGanZhiDay)
    sBuf += Pm.GetStr("IDS_X_DAY")

    return sBuf
  end
  def Xdate.ganzhi_date_str3(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,bSpace=true)
    sBuf = Xdate.GetGanZhi_Str(nGanZhiYear)
    sBuf += " " if bSpace

    sBuf += Xdate.GetGanZhi_Str(nGanZhiMonth)
    sBuf += " " if bSpace

    sBuf += Xdate.GetGanZhi_Str(nGanZhiDay)

    return sBuf
  end
  def Xdate.ganzhi_date_str_array(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour)
    a = Array.new

    a.push(Xdate.GetGanZhi_Str(nGanZhiYear))
    a.push(Xdate.GetGanZhi_Str(nGanZhiMonth))
    a.push(Xdate.GetGanZhi_Str(nGanZhiDay))
    a.push(Xdate.GetGanZhi_Str(nGanZhiHour))

    return a
  end
  def Xdate.ganzhi_date_str_array2(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour)
    a = Array.new

    a.push(Xdate.GetGanZhi_Str(nGanZhiYear))
    a.push(Xdate.GetGanZhi_Str(nGanZhiMonth))
    a.push(Xdate.GetGanZhi_Str(nGanZhiDay))
    a.push(Xdate.GetGanZhi_Str(nGanZhiHour))

    return a
  end
  def Xdate.Get_Nian_Yue_Jieqi_Index(nWYear,nWMonth)
    nSegCountPerYear = Xdate::SEGMENT_COUNT_PER_YEAR
    nYearCount = nWYear - Xdate::BASE_YEAR
    # jieqiIndex = 小寒 (國曆年第一個節, 小寒為'節', 大寒為'氣')
    jieqiIndex = nYearCount * nSegCountPerYear
    jieqiIndex = jieqiIndex + (nWMonth - 1) * 2
    return jieqiIndex
  end
  def Xdate.GetJieqiArrayByIndex(jieqiIndex)
    Xdate.Init()
    return @@SegTimeArray[jieqiIndex]
  end
  def Xdate.GetJieqiShiJianByIndex(jieqiIndex)
    jieqi = Xdate.GetJieqiArrayByIndex(jieqiIndex)
    return jieqi[Xdate::ST_MONTH], jieqi[Xdate::ST_DAY],jieqi[Xdate::ST_HOUR],jieqi[Xdate::ST_MINUTE]
  end
  def Xdate.da_su_zi(n)
    return Pm.GetStr("da_su_zi.big_no_#{n}")
  end
  def Xdate.bigno(s)
    n = s.to_i
    if (n == 0 && s == "0") then
      return Xdate.da_su_zi(n)
    elsif (n >= 1 && n <=9) then
      return Xdate.da_su_zi(n)
    else
      return s
    end
  end
  def Xdate.bignos(n)
    s = n.to_s
    a = s.split(//)
    a2 = a.map! {|n| Xdate.bigno(n)}
    return a2.join
  end
  def Xdate.no_2_bignos(a)
    return a.map! {|n| Xdate.bignos(n)}
  end
  def Xdate.make_now_timestamp()
    return Xdate.make_time_timestamp(Time.now)
  end
  def Xdate.make_now_timestamp2()
    return Xdate.make_time_timestamp2(Time.now)
  end
  def Xdate.make_now_timestamp_sex(sex=1)
    return "#{Xdate.make_now_timestamp()}#{sex}"
  end
  def Xdate.make_time_timestamp(t)
    return "%04d%02d%02d%02d%02d" % [t.year,t.month,t.day,t.hour,t.min]
    # return "%04d%02d%02d%02d%02d%02d" % [t.year,t.month,t.day,t.hour,t.min,t.sec]
  end
  def Xdate.make_time_timestamp2(t)
    return "%04d%02d%02d%02d%02d%02d" % [t.year,t.month,t.day,t.hour,t.min,t.sec]
  end
  def Xdate.make_timesex_timestamp(y,m,d,h,s)
    return "%04d%02d%02d%02d%s" % [y,m,d,h,s]
  end
  def Xdate.make_timestamp_hash(h)
    longitude = h["longitude"]
    latitude = h["latitude"]

    return Xdate.make_timestamp_hash2(h,longitude,latitude)
  end
  def Xdate.make_full_user_timestamp(h)
    s = Xdate.make_timestamp_hash2(h)
    mb = h[Cfate::Multiple_births]
    return s if mb == nil || mb == ""
    mb.upcase!
    return s if ["S1","D1"].include?(mb)
    return "#{s}#{mb}"
  end
  def Xdate.make_timestamp_hash2(h,longitude=nil,latitude=nil)
    if (h[Cfate::WYear] != nil) then
      nWYear = h[Cfate::WYear]
      nWMonth = h[Cfate::WMonth]
      nWDate = h[Cfate::WDate]
      nWHour = h[Cfate::WHour]
      nWMin = h[Cfate::WMinute]
    elsif (h[Cfate::EYear] != nil) then
      nEYear,nEMonth,nEDate,bLeapMonth = h[Cfate::EYear],h[Cfate::EMonth],h[Cfate::EDate],h[Cfate::LeapMonth] 
      nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,nEMonth,nEDate,bLeapMonth)
      nWHour = h[Cfate::WHour]
      nWMin = h[Cfate::WMinute]
    else
      nWYear,nWMonth,nWDate,nWHour,sex,nWMin,mb = Xdate.parse_timestamp(nil) # now
      h[Cfate::Sex] = sex
    end
    nWsYear,nWsMonth,nWsDate,nWsHour,nWsMin = Xdate.api_rst_localtime_to_localsuntime(nWYear, nWMonth,nWDate, nWHour,nWMin,longitude,latitude)
    sex = h[Cfate::Sex] ? 0 : 1
    return Xdate.make_timestamp_solar2(nWsYear,nWsMonth,nWsDate,nWsHour,sex)
  end
  def Xdate.make_timestamp(y,m=0,d=0,h=99)
    if (y < Cfate::PAN_FIRST_YEAR) then
      y = Cfate::PAN_FIRST_YEAR
    elsif (y > Cfate::PAN_LAST_YEAR) then
      y = Cfate::PAN_LAST_YEAR
    end
    return Xdate.make_timestamp1(y,m,d,h)
  end
  def Xdate.make_timestamp1(y,m=0,d=0,h=99)
    return "%04d%02d%02d%02d" % [y,m,d,h]
  end
  def Xdate.make_timestamp2(y,m=0,d=0,h=0,min=0)
    return "%04d%02d%02d%02d%02d" % [y,m,d,h,min]
  end
  def Xdate.make_timestamp_solar(y,m=0,d=0,h=99,sex=0)
    s = sex == 1 ? "m" : "f"
    return "%04d%02d%02d%02d%s" % [y,m,d,h,s]
  end
  def Xdate.make_timestamp_solar2(y,m=0,d=0,h=99,sex=0)
    s = sex == 1 ? "m" : "f"
    nETime = Xdate.Hour2ETime(h)
    h = Xdate.ETime2Hour(nETime)
    return "%04d%02d%02d%02d%s" % [y,m,d,h,s]
  end
  def Xdate.make_timestamp_lunar(y,m=0,d=0,h=99,bl=0,sex=0)
    s = sex == 1 ? "m" : "f"
    return "L%01d%04d%02d%02d%02d%s" % [bl,y,m,d,h,s]
  end
  def Xdate.caltype_timestamp(timestamp=nil)
    if (timestamp == nil) then
      return Xdate::CT_SOLAR
    end
    if (["l","L"].include?(timestamp[0])) then
      return Xdate::CT_LUNAR
    end
    return Xdate::CT_SOLAR
  end
  def Xdate.timestamp_to_dbTime(timestamp=nil,nDays=0)
    timestamp.gsub!("-","") if timestamp != nil && timestamp.index("-") != nil
    timestamp.gsub!(":","") if timestamp != nil && timestamp.index(":") != nil

    timestamp = Xdate.next_timestamp_days(timestamp,nDays)
    y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp)
    return "%04d-%02d-%02d" % [y,m,d]
  end
  def Xdate.parse_timestamp(timestamp=nil)
    if (timestamp == nil) || (!["l","L"].include?(timestamp[0])) then
      return Xdate.parse_timestamp2(timestamp)
    else
      return Xdate.parse_timestamp_lunar(timestamp)
    end
  end
  def Xdate.next_timestamp(timestamp=nil,nHours=2)
    y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp)
    nWYear,nWMonth,nWDate,nWHour = Xdate.NextNHour(y,m,d,h,nHours)
    ts = Xdate.make_timestamp_solar(nWYear,nWMonth,nWDate,nWHour,sex)
    return ts
  end
  def Xdate.next_timestamp_years(timestamp,nYears)
    y,m,d,nWHour,sex,min,mb = Xdate.parse_timestamp(timestamp)
    nWYear,nWMonth,nWDate = Xdate.NextWYears(y,m,d,nYears)
    ts = Xdate.make_timestamp_solar(nWYear,nWMonth,nWDate,nWHour,sex)
    return ts
  end
  def Xdate.next_timestamp_days(timestamp,nDays)
    y,m,d,nWHour,sex,min,mb = Xdate.parse_timestamp(timestamp)
    nWYear,nWMonth,nWDate = Xdate.NextWDates(y,m,d,nDays)
    ts = Xdate.make_timestamp_solar(nWYear,nWMonth,nWDate,nWHour,sex)
    return ts
  end
  def Xdate.make_timestamp_udt_next_days(pan_type,timestamp_udt,nDays)
    ts = Xdate.next_timestamp_days(timestamp_udt,nDays)
    return Xdate.make_timestamp_udt(pan_type,ts)
  end
  def Xdate.make_timestamp_udt_next_hours(pan_type,timestamp_udt,nHours)
    ts = Xdate.next_timestamp(timestamp_udt,nHours)
    return Xdate.make_timestamp_udt(pan_type,ts)
  end
  def Xdate.make_timestamp_udt2(pan_type,timestamp_udt)
    y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp_udt)
    timestamp_udt = Xdate.make_timestamp2(y,m,d,h,min)
    return timestamp_udt
  end
  def Xdate.make_timestamp_udt(pan_type,timestamp_udt)
      y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp_udt)
      case pan_type
      when Cfate::PAN_NORMAL
          timestamp_udt = Xdate.make_timestamp2(0)
      when Cfate::PAN_TENYEAR
          timestamp_udt = Xdate.make_timestamp2(y)
      when Cfate::PAN_FLOWYEAR
          timestamp_udt = Xdate.make_timestamp2(y)
      when Cfate::PAN_FLOWMONTH
          timestamp_udt = Xdate.make_timestamp2(y,m)
      when Cfate::PAN_FLOWDATE
          timestamp_udt = Xdate.make_timestamp2(y,m,d)
      when Cfate::PAN_FLOWTIME
          timestamp_udt = Xdate.make_timestamp2(y,m,d,h)
      when Cfate::PAN_FLOWMIN
          timestamp_udt = Xdate.make_timestamp2(y,m,d,h,min)
      end
      return timestamp_udt
  end
  def Xdate.rand_timestampsex(y=nil,m=nil,d=nil,h=nil,sex=nil)
    y1,m1,d1,h1,min1,s1 = Xdate.GetCurTime("192.168.1.1")
    y2 = y == nil ? y1 - rand(y1 - 1950) : y
    m2 = m == nil ? m1 : m
    d2 = d == nil ? d1 : d
    h2 = h == nil ? h1 : h
    sex2 = sex == nil ? rand(1) : sex
    s = sex2 == 1 ? "m" : "f"
    return Xdate.make_timesex_timestamp(y2,m2,d2,h2,s)
  end
  def Xdate.parse_timestamp_lunar(timestamp)
    y,m,d,h,sex,min,mb = Xdate.parse_timestamp1()
    ey,em,ed,bLeapMonth = Xdate.West2East(y,m,d)
    ebl = timestamp[1].to_i == 1 ? true : false
    ts = timestamp[2,timestamp.length-2]
    ey,em,ed,h,sex,min,mb = Xdate.parse_timestamp1(ts,ey,em,ed,h,sex,min)

    y,m,d = Xdate.East2West(ey,em,ed,ebl)
    return y,m,d,h,sex,min,mb
  end
  def Xdate.parse_timestamp2(timestamp=nil)
    y,m,d,h,sex,min,mb = Xdate.parse_timestamp1()
    if (timestamp != nil) then
      y,m,d,h,sex,min,mb = Xdate.parse_timestamp1(timestamp,y,m,d,h,sex,min)
    end
    y,m,d = Xdate.GetLegalWDate(y,m,d)
    return y,m,d,h,sex,min,mb
  end
  def Xdate.parse_timestamp1(timestamp=nil,y1=2016,m1=1,d1=1,h1=0,sex1=1,min1=0)
    if (timestamp == nil) then
      timestamp = Xdate.make_now_timestamp()
    end
    y = y1
    if (timestamp.length >= 4) then
      if (timestamp[0,4] != "0000") then
        y = timestamp[0,4].to_i
      end
    end
    m = m1
    if (timestamp.length >= 6) then
      if (timestamp[4,2] != "00") then
        m = timestamp[4,2].to_i
      end
    end
    d = d1
    if (timestamp.length >= 8) then
      if (timestamp[6,2] != "00") then
        d = timestamp[6,2].to_i
      end
    end
    h = h1
    if (timestamp.length >= 10) then
      if (timestamp[8,2] != "99") then
        h = timestamp[8,2].to_i
        h = Pm.TestNo24(h)
      end
    end
    sex = sex1
    if (timestamp.length == 11) then
      sex = Xdate.parse_sex(timestamp[10,1])
    end
    min = min1
    if (timestamp.length >= 12) then
      if (timestamp[10,2] != "99") then
        min = timestamp[10,2].to_i
      end
    end
    if (timestamp.length >= 13) then
      sex = Xdate.parse_sex(timestamp[12,1])
    end
    # multiple births
    mb = timestamp[-2,2]

    # if (timestamp.length >= 14) then
    #   sec = timestamp[12,2].to_i
    # end
    # if (timestamp.length == 15) then
    #   sex = timestamp[14,1].to_i
    # end
    return y,m,d,h,sex,min,mb
  end
  def Xdate.parse_sex(s)
    if (["1","m","M","male",false,1].include?(s)) then
      return 1
    else
      return 0
    end
  end
  def Xdate.date_2_name(y,m,d,h)
    if (y != 0) then
      sBuf = "%04d%02d%02d" % [y,m,d]
    else
      sBuf = "%02d%02d" % [m,d]
    end
    nETime = Xdate.Hour2ETime(h)
    sBuf += Xdate.GetETimeStr(nETime)
    return sBuf
  end

  def Xdate.month_day_guo_zi2(nWMonth,nWDate)
    s1 = Xdate.guo_zi_sus_str(nWMonth)
    s2 = Pm.GetStr("IDS_X_MONTH")
    s3 = Xdate.guo_zi_sus_str(nWDate)
    s4 = Pm.GetStr("IDS_X_DAY")
    return s1,s2,s3,s4
  end
  def Xdate.month_day_guo_zi(nWMonth,nWDate)
    return Xdate.month_guo_zi(nWMonth) + Xdate.day_guo_zi(nWDate)
  end
  def Xdate.month_guo_zi(nWMonth)
    s = Xdate.guo_zi_sus_str(nWMonth)
    s += Pm.GetStr("IDS_X_MONTH")
    return s
  end
  def Xdate.emonth_guo_zi(nEMonth,bLeapMonth)
    s = ""
    if (bLeapMonth) then
      s += Pm.GetStr("IDS_X_LEAP")
    end
    s += Xdate.guo_zi_sus_str(nEMonth)
    s += Pm.GetStr("IDS_X_MONTH")
    return s
  end
  def Xdate.day_guo_zi(nWDate)
    s = Xdate.guo_zi_sus_str(nWDate)
    s += Pm.GetStr("IDS_X_DAY")
    return s
  end
  def Xdate.guo_zi_sus_str(num)
    if (num == 10) then
      return Pm.GetStr("guo_zi_su.guo_zi_su_#{num}")
    end
    a = num.to_s.split("")
    s = ""
    a_length = a.length
    a.each_with_index do |n,i|
      pos = a_length - i - 1
      s += Xdate.guo_zi_su_str(n.to_i,pos)
    end
    return Xdate.remove_guo_zi_0(s)
  end
  def Xdate.guo_zi_su_str(num,pos)
    s = ""
    if (pos == 1 && num == 1) then
      n = 10 ** pos
      return Pm.GetStr("guo_zi_su.guo_zi_su_#{n}")
    elsif (pos == 0 && num == 0) then
      return ""
    elsif (pos == 4 && num == 0) then
    else
      s = Pm.GetStr("guo_zi_su.guo_zi_su_#{num}")
    end
    if (pos > 0 && num != 0) || (pos == 4) then
      n = 10 ** pos
      s += Pm.GetStr("guo_zi_su.guo_zi_su_#{n}")
    end
    return s
  end
  def Xdate.remove_guo_zi_0(s)
    if (s.length == 1) then
      return s
    end
    s = s.split("").reverse
    bFirst = true
    sOut = ""
    s.each do |n|
      if (Pm.GetStr("guo_zi_su.guo_zi_su_#{0}") == n) then
        if (!bFirst) then
          sOut += n
        end
      else
        sOut += n
        bFirst = false
      end
    end
    ss = "#{Pm.GetStr("guo_zi_su.guo_zi_su_#{0}")}#{Pm.GetStr("guo_zi_su.guo_zi_su_#{0}")}"
    sOut.reverse!
    while (sOut.include?(ss))
      sOut.gsub!(ss,Pm.GetStr("guo_zi_su.guo_zi_su_#{0}"))
    end
    ss2 = "#{Pm.GetStr("guo_zi_su.guo_zi_su_#{2}")}#{Pm.GetStr("guo_zi_su.guo_zi_su_#{10}")}"
    sOut.gsub!(ss2,Pm.GetStr("guo_zi_su.guo_zi_su_#{20}"))
    return sOut
  end
end
