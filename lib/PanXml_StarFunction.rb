# coding: UTF-8

require("Star.rb")

class PanXml
	@PanType
	@Star
	
	TAG_STARPAN = "StarPan"
	TAG_HOUSE_INFO = "HouseInfo"
	TAG_STAR_INFO = "StarInfo"
	TAG_HOUSE = "House_"
	TAG_STAR = "Star_"
	TAG_ASTAR = "AStar"
	TAG_BSTAR = "BStar"
	TAG_NAME = "Name"
	TAG_EARTH = "Earth"
	TAG_COUNT = "Count"
	
	def px_Star_getPan(nPanType,hUserInfo,hUserAskData,hUserType)
		@PanType = nPanType
		@Star = Star.new
		@StarInfo = @Star.g_GetStarPanInfo(nPanType,hUserInfo,hUserAskData,hUserType)
		return px_Star_createXmlPan()
	end
	
	def px_Star_createXmlPan()
		sBuf = px_AddTag(PanXml::TAG_STARPAN)
			sBuf += px_AddTag(PanXml::TAG_HOUSE_INFO)
				nEarth = @Star.gHouse_GetFirstHouseEarth(@PanType)
				(1..12).each do |i|
					sBuf += px_Star_AddHouseInfo(nEarth,i)
					# 尋找下一個宮名
					nEarth = @Star.gHouse_GetNextHouseEarth(nEarth)
				end
			sBuf += px_AddTagEnd(PanXml::TAG_HOUSE_INFO)
		sBuf += px_AddTagEnd(PanXml::TAG_STARPAN)
	end
	
	def px_Star_AddHouseInfo(nEarth,nIndex)
		sBuf = px_AddTag(PanXml::TAG_HOUSE + nIndex.to_s) # + nEarth.to_s)
			sBuf += px_AddTagValue(PanXml::TAG_NAME,@Star.gHouse_GetHouseName(@PanType,nEarth))
			#sBuf += px_AddTagValue(PanXml::TAG_EARTH,Earth.GetName(nEarth))
			sBuf += px_AddStarInfo(nEarth)
		sBuf += px_AddTagEnd(PanXml::TAG_HOUSE + nIndex.to_s) # + nEarth.to_s)
	end
	
	def px_AddStarInfo(nEarth)
		sBuf = px_AddTag(PanXml::TAG_STAR_INFO)
			sBuf += px_AddAStarInfo(nEarth)
			sBuf += px_AddBStarInfo(nEarth)
		sBuf += px_AddTagEnd(PanXml::TAG_STAR_INFO)
	end

	def px_AddAStarInfo(nEarth)
		sBuf = px_AddTag(PanXml::TAG_ASTAR)
			starCount = @Star.gHouse_GetAStarCount(@PanType,nEarth)
			sBuf += px_AddTagValue(PanXml::TAG_COUNT,starCount.to_s)
			(1..starCount).each do |i|
				sBuf += px_AddTagValue(PanXml::TAG_STAR + i.to_s,@Star.gHouse_GetAStarName_Each(@PanType,nEarth,i-1))
			end
		sBuf += px_AddTagEnd(PanXml::TAG_ASTAR)
	end

	def px_AddBStarInfo(nEarth)
		sBuf = px_AddTag(PanXml::TAG_BSTAR)
			starCount = @Star.gHouse_GetBStarCount(@PanType,nEarth)
			sBuf += px_AddTagValue(PanXml::TAG_COUNT,starCount.to_s)
			(1..starCount).each do |i|
				sBuf += px_AddTagValue(PanXml::TAG_STAR + i.to_s,@Star.gHouse_GetBStarName_Each(@PanType,nEarth,i-1))
			end
		sBuf += px_AddTagEnd(PanXml::TAG_BSTAR)
	end
	
end
