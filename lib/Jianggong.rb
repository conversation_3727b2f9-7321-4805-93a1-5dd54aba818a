# coding: UTF-8
require("Cfate.rb")
require("Xdate.rb")
require("Pm.rb")
require("WeenApi.rb")
require("Controller_Api.rb")
require("Score.rb")

class Jianggong
  def jianggong_api_key(hUserInfo,hUserDefData)
    ts = Xdate.make_timestamp_hash(hUserInfo)
    y_udt = hUserDefData[Cfate::WYear]
    # 每一個資料儲存一個 timestamp 的一年度資料
    key = "Jianggong_#{ts}_#{y_udt}"
    return key
  end
  def jianggong_api_1(h_par_org,hApFunc,hParAll)
    # h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
    
    nPanType = h_par_org["pt"].to_i <= Cfate::PAN_FLOWYEAR || h_par_org["pt"].to_i > Cfate::PAN_FLOWTIME ? Cfate::PAN_FLOWMONTH : h_par_org["pt"].to_i
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserInfo = Jianggong.get_user_info(h_par_org)

    result = {}
    if (nPanType == Cfate::PAN_FLOWMONTH) then
      result = jianggong_api_one_month(hUserInfo,hUserDefData,hApFunc,hParAll)
    elsif (nPanType == Cfate::PAN_FLOWDATE) then
      result = jianggong_api_one_day(hUserInfo,hUserDefData,hApFunc,hParAll)
    elsif (nPanType == Cfate::PAN_FLOWTIME) then
      result = jianggong_api_one_hour(hUserInfo,hUserDefData,hApFunc,hParAll)
    end
    result["final_success"] = true
    return result
    # return jianggong_gixiongscore(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
  end
  def jianggong_api_find_year(hUserInfo,hUserDefData)
    key = jianggong_api_key(hUserInfo,hUserDefData)
    h = Pm.findTestDbHashValue_key(key)
    h = jianggong_api_save_year_empty(hUserInfo,hUserDefData) if h == {}
    return h
  end
  def jianggong_api_find_year2(h_par_org)
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserInfo = Jianggong.get_user_info(h_par_org)
    return jianggong_api_find_year(hUserInfo,hUserDefData)
  end
  def jianggong_api_save_year(hUserInfo,hUserDefData,value)
    key = jianggong_api_key(hUserInfo,hUserDefData)
    Pm.updateTestDbHashValue_key(key,value)
  end
  def jianggong_api_find_month2(h_par_org)
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserInfo = Jianggong.get_user_info(h_par_org)
    return jianggong_api_find_month(hUserInfo,hUserDefData)
  end
  def jianggong_api_find_month(hUserInfo,hUserDefData)
    h = jianggong_api_find_year(hUserInfo,hUserDefData)
    if h == {} then
      return {}
    end
    m_udt = hUserDefData[Cfate::WMonth]
    # 回傳某個月的所有資料
    h2 = {}
    h2["m_#{m_udt}"] = h["m_#{m_udt}"]
    h2["m_#{m_udt}"] = {} if h2["m_#{m_udt}"] == nil

    return h2
  end
  def jianggong_api_save_year_empty(hUserInfo,hUserDefData)
    h = {}
    (1..12).each do |m_udt|
      h["m_#{m_udt}"] = {}
    end
    jianggong_api_save_year(hUserInfo,hUserDefData,h)
    return h
  end
  # 此為背景呼叫的api，用jobs呼叫
  def jianggong_api_all(h_par_org)
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserInfo = Jianggong.get_user_info(h_par_org)
    jianggong_api_all2(hUserInfo,hUserDefData)
  end
  def jianggong_api_all2(hUserInfo,hUserDefData)
    hApFunc = nil
    hParAll = nil

    m_udt = hUserDefData[Cfate::WMonth]
    # 每次計算，都算1個月
    h = jianggong_api_find_month(hUserInfo,hUserDefData)
    if h["m_#{m_udt}"] == {} then
      h["m_#{m_udt}"]["count"] = h["m_#{m_udt}"]["count"].to_i + 1
      h_y = jianggong_api_find_year(hUserInfo,hUserDefData)
      h_y["m_#{m_udt}"] = h["m_#{m_udt}"]
      jianggong_api_save_year(hUserInfo,hUserDefData,h_y)

      h_y = jianggong_api_find_year(hUserInfo,hUserDefData)
      h = jianggong_api_one_month_all_house(hUserInfo,hUserDefData,hApFunc,hParAll)
      h_y["m_#{m_udt}"] = h
      jianggong_api_save_year(hUserInfo,hUserDefData,h_y)
    end

    # 以後要前後1個月，還是往後2個月，再看看
    # hUserDefData = jianggong_next_month(hUserDefData,1)
    # h = jianggong_api_one_month_all_house(hUserInfo,hUserDefData,hApFunc,hParAll)

    # hUserDefData = jianggong_next_month(hUserDefData,1)
    # h = jianggong_api_one_month_all_house(hUserInfo,hUserDefData,hApFunc,hParAll)
  end
  def jianggong_api_one_month(hUserInfo,hUserDefData,hApFunc,hParAll)
    nPanType = Cfate::PAN_FLOWMONTH
    return jianggong_gixiongscore(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
  end
  def jianggong_api_one_month_h(hUserInfo,hUserDefData,hApFunc,hParAll)
    # h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
    h = {}

    h2 = jianggong_api_one_month(hUserInfo,hUserDefData,hApFunc,hParAll)
    y = hUserDefData[Cfate::WYear]
    l = []
    # nPanType = h_par_org["pt"].to_i <= Cfate::PAN_FLOWYEAR || h_par_org["pt"].to_i > Cfate::PAN_FLOWTIME ? Cfate::PAN_FLOWMONTH : h_par_org["pt"].to_i
    h2["guoli"].each_with_index do |guoli,i|
      d_h = {}
      a2 = guoli.split("/")
      if (a2.length == 3) then
        y = a2[0].to_i
        m = a2[1].to_i
        d = a2[2].to_i
      else
        m = a2[0].to_i
        d = a2[1].to_i
      end
      ts = Xdate.make_timestamp(y,m,d,0)
      h3 = {"timestamp" => ts}
      d_h["wannianli"] = jianggong_api_farmercal(h3)
      d_h["gi_xiong"] = make_api4_day_gixiong(h2,i)

      l.push(d_h)
    end
    h["house_info"] = Pm.t("flyingstar.jianggong.gong_in_life_name_#{hUserDefData[Star::HOUSE_NAME]}")
    h["caltype"] = hUserDefData[Cfate::CalType]
    if hUserDefData[Cfate::CalType] == Xdate::CT_LUNAR then
      h["date"] = "#{hUserDefData[Cfate::EYear]}-#{hUserDefData[Cfate::EMonth]}" 
    else
      h["date"] = "#{y}-#{hUserDefData[Cfate::WMonth]}"
    end
    h["list"] = l

    h["final_success"] = true

    return h
  end
  def gpc_api_one_month_h(hUserInfo,hUserDefData,hApFunc,hParAll)
    # h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
    h = {}

    h2 = jianggong_api_one_month(hUserInfo,hUserDefData,hApFunc,hParAll)
    y = hUserDefData[Cfate::WYear]
    l = []
    # nPanType = h_par_org["pt"].to_i <= Cfate::PAN_FLOWYEAR || h_par_org["pt"].to_i > Cfate::PAN_FLOWTIME ? Cfate::PAN_FLOWMONTH : h_par_org["pt"].to_i
    h2["guoli"].each_with_index do |guoli,i|
      d_h = {}
      a2 = guoli.split("/")
      if (a2.length == 3) then
        y = a2[0].to_i
        m = a2[1].to_i
        d = a2[2].to_i
      else
        m = a2[0].to_i
        d = a2[1].to_i
      end
      ts = Xdate.make_timestamp(y,m,d,0)
      h3 = {"timestamp_udt" => ts}
      d_h["wannianli"] = gpc_api_farmercal(h3)
      d_h["gi_xiong"] = make_api4_day_gixiong(h2,i)

      l.push(d_h)
    end
    h["house_info"] = Pm.t("flyingstar.jianggong.gong_in_life_name_#{hUserDefData[Star::HOUSE_NAME]}")
    h["caltype"] = hUserDefData[Cfate::CalType]
    if hUserDefData[Cfate::CalType] == Xdate::CT_LUNAR then
      h["date"] = "#{hUserDefData[Cfate::EYear]}-#{hUserDefData[Cfate::EMonth]}" 
    else
      h["date"] = "#{y}-#{hUserDefData[Cfate::WMonth]}"
    end
    h["list"] = l

    h["final_success"] = true

    return h
  end
  def jianggong_api_one_month_all_house(hUserInfo,hUserDefData,hApFunc,hParAll)
    h = {}
    [1,3,5,6,9].each do |house|
      h2 = hUserDefData.clone
      h2[Star::HOUSE_NAME] = house
      h["house_#{house}"] = jianggong_api_one_month_all(hUserInfo,h2,hApFunc,hParAll)
    end
    return h    
  end
  def jianggong_api_one_month_all(hUserInfo,hUserDefData,hApFunc,hParAll)
    # h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
    h = {}

    h2 = jianggong_api_one_month(hUserInfo,hUserDefData,hApFunc,hParAll)
    y = hUserDefData[Cfate::WYear]
    l = []
    # nPanType = h_par_org["pt"].to_i <= Cfate::PAN_FLOWYEAR || h_par_org["pt"].to_i > Cfate::PAN_FLOWTIME ? Cfate::PAN_FLOWMONTH : h_par_org["pt"].to_i
    h2["guoli"].each_with_index do |guoli,i|
      d_h = {}
      a2 = guoli.split("/")
      if (a2.length == 3) then
        y = a2[0].to_i
        m = a2[1].to_i
        d = a2[2].to_i
      else
        m = a2[0].to_i
        d = a2[1].to_i
      end
      ts = Xdate.make_timestamp(y,m,d,0)
      h3 = {"timestamp_udt" => ts}
      d_h["wannianli"] = jianggong_api_farmercal(h3)
      d_h["gi_xiong"] = make_api4_day_gixiong(h2,i)

      h_udt = {"timestamp_udt" => ts}
      hUd2 = Jianggong.get_cur_date_info(h_udt)
      hUserDefData.merge!(hUd2) { |key, v1, v2| v2 }
      a = jianggong_api_one_day_a(hUserInfo,hUserDefData,hApFunc,hParAll)
      d_h["gi_xiong"]["day_vos"] = a
      l.push(d_h)
    end
    h["date"] = "#{y}-#{hUserDefData[Cfate::WMonth]}"
    h["list"] = l

    return h
  end
  def jianggong_api_one_day(hUserInfo,hUserDefData,hApFunc,hParAll)
      nPanType = Cfate::PAN_FLOWDATE
      return jianggong_gixiongscore(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
  end
  def jianggong_api_one_day_a(hUserInfo,hUserDefData,hApFunc,hParAll)
      y = hUserDefData[Cfate::WYear]
      m = hUserDefData[Cfate::WMonth]
      d = hUserDefData[Cfate::WDate]
      h4 = jianggong_api_one_day(hUserInfo,hUserDefData,hApFunc,hParAll)
      a = []
      h4["hour"].each_with_index do |hour,j|
        a_h = make_api4_hour_gixiong(h4,j)
        # ts2 = Xdate.make_timestamp(y,m,d,j * 2)
        # h_udt2 = {"timestamp_udt" => ts2}
        # hUd3 = Jianggong.get_cur_date_info(h_udt2)
        # hUserDefData.merge!(hUd3) { |key, v1, v2| v2 }
        # a_m = jianggong_api_one_hour_a(hUserInfo,hUserDefData,hApFunc,hParAll)
        # a_h["hour_vos"] = a_m
        a.push(a_h)
      end
      return a
  end
  def jianggong_api_one_hour(hUserInfo,hUserDefData,hApFunc,hParAll)
      nPanType = Cfate::PAN_FLOWTIME
      return jianggong_gixiongscore(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
  end
  def jianggong_api_one_hour_a(hUserInfo,hUserDefData,hApFunc,hParAll)
      h5 = jianggong_api_one_hour(hUserInfo,hUserDefData,hApFunc,hParAll)
      a_m = []
      h5["minute"].each_with_index do |minute,k|
        a_m.push(make_api4_minute_gixiong(h5,k))
      end
      return a_m
  end
  def Jianggong.get_cur_date_info(h)
    hUserDefData = {}

    timestamp = h["timestamp_udt"]

    y,m,d,hour,sex,min,mb = Xdate.parse_timestamp(timestamp)
    hUserDefData[Cfate::WYear] = y
    hUserDefData[Cfate::WMonth] = m
    hUserDefData[Cfate::WDate] = d
    hUserDefData[Cfate::WHour] = hour
    hUserDefData[Cfate::WMinute] = min
    hUserDefData[Cfate::Sex] = sex == 1 ? false : true 
    hUserDefData[Cfate::Name] = h["name"] == nil ? timestamp : h["name"]

    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(hUserDefData[Cfate::WYear],hUserDefData[Cfate::WMonth],hUserDefData[Cfate::WDate])
    hUserDefData[Cfate::EYear] = nEYear
    hUserDefData[Cfate::EMonth] = nEMonth
    hUserDefData[Cfate::EDate] = nEDate
    hUserDefData[Cfate::LeapMonth] = bLeapMonth

    hUserDefData[Star::HOUSE_NAME] = !([1,3,5,6,9].include?(h["house"].to_i)) ? Star::EX_HOUSE_1 : h["house"].to_i
    hUserDefData[Cfate::CalType] = h["caltype"] == nil ? Xdate::CT_SOLAR : h["caltype"].to_i
    hUserDefData["timestamp_udt"] = timestamp

    return hUserDefData
  end
  def Jianggong.get_user_info(h)
    hUserInfo = {}

    hUserInfo["longitude"] = h["longitude"] == nil ? nil : h["longitude"].to_f
    hUserInfo["latitude"] = h["latitude"] == nil ? nil : h["latitude"].to_f

    timestamp = h["timestamp"]
    y,m,d,hour,sex,min,mb = Xdate.parse_timestamp(timestamp)
    hUserInfo[Cfate::WYear] = y
    hUserInfo[Cfate::WMonth] = m
    hUserInfo[Cfate::WDate] = d
    hUserInfo[Cfate::WHour] = hour
    hUserInfo[Cfate::WMinute] = min
    hUserInfo[Cfate::Sex] = sex == 1 ? false : true 
    hUserInfo[Cfate::Name] = h["name"] == nil ? timestamp : h["name"]
    hUserInfo[Cfate::Multiple_births] = mb

    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(hUserInfo[Cfate::WYear],hUserInfo[Cfate::WMonth],hUserInfo[Cfate::WDate])
    hUserInfo[Cfate::EYear] = nEYear
    hUserInfo[Cfate::EMonth] = nEMonth
    hUserInfo[Cfate::EDate] = nEDate
    hUserInfo[Cfate::LeapMonth] = bLeapMonth
    hUserInfo["timestamp"] = timestamp

    return hUserInfo
  end
  # 參數house:EX_HOUSE_1 = 1 運氣(命宮) ;  3 感情(夫妻宮); 5 財運(財帛宮); 6 疾病(疾厄宮); 9 事業(官祿宮)
  # pt: 流盤 十年 1 流年 2 流月 3 流日 4 流時 5
  # timestamp: 客戶生日 YYYYMMDDhhmms   (s:M male, F female) 客戶出生時間
  # 吉凶分析 需求說明：
  # 1、流月輸出內容 ---> 項目、國曆月日、星期、干支日、農曆月日、吉凶分數
  # 2、流日輸出內容 ---> 項目、時辰(03:00 ~ 05:00)、干支時、吉凶分數
  # 3、流時輸出內容 ---> 項目、時間(00:00 ~ 10:00)、吉凶分數

  def jianggong_gixiongscore(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
    nWYear, nWMonth,nWDate = hUserDefData[Cfate::WYear],hUserDefData[Cfate::WMonth],hUserDefData[Cfate::WDate]
    nWHour,nWMin,sex = hUserDefData[Cfate::WHour],hUserDefData[Cfate::WMinute],hUserDefData[Cfate::Sex] ? 0 : 1
    oWeenApi = WeenApi.new
    h = {}
    h["house_info"] = Pm.t("flyingstar.jianggong.gong_in_life_name_#{hUserDefData[Star::HOUSE_NAME]}")
    if (nPanType == Cfate::PAN_FLOWMONTH) then
      if (hUserDefData["CurrentDate"] != nil && hUserDefData["CurrentDate"]) then
        h2 = jianggong_gixiongscore_month_oneday(oWeenApi,nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
        h.merge!(h2)
      elsif (hUserDefData["days"] != nil && hUserDefData["days"] > 0) then
        h2 = jianggong_gixiongscore_month_days(oWeenApi,nPanType,hUserInfo,hUserDefData,hApFunc,hParAll,hUserDefData["days"].to_i)
        h.merge!(h2)
      else
        if (hUserDefData[Cfate::CalType] == Xdate::CT_LUNAR) then
          h2 = jianggong_gixiongscore_month_lunar(oWeenApi,nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
          h.merge!(h2)
        else
          hUdt = hUserDefData.clone
          hUdt[Cfate::WDate] = 1
          h2 = jianggong_gixiongscore_month_solar(oWeenApi,nPanType,hUserInfo,hUdt,hApFunc,hParAll)
          h.merge!(h2)
        end
      end
    elsif (nPanType == Cfate::PAN_FLOWDATE) then
        h2 = oWeenApi.g_fe_score(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
        h3 = jianggong_gixiongscore_month_values(h2)
        h3.delete("guoli")
        h.merge!(h3) { |key, v1, v2| v2 }
        h4 = jianggong_gixiongscore_month_other_values(nPanType,nWYear,nWMonth,nWDate,nWHour,0)
        h.merge!(h4) { |key, v1, v2| v2 }
    elsif (nPanType == Cfate::PAN_FLOWTIME)
        h2 = oWeenApi.g_fe_score(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
        h3 = jianggong_gixiongscore_month_values(h2)
        h3.delete("guoli")
        h.merge!(h3) { |key, v1, v2| v2 }
        h4 = jianggong_gixiongscore_month_other_values(nPanType,nWYear,nWMonth,nWDate,nWHour,0)
        h.merge!(h4) { |key, v1, v2| v2 }
    end
    return h
  end
  # 陰曆的吉凶
  def jianggong_gixiongscore_month_lunar(oWeenApi,nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
    h = {}
    nEYear, nEMonth,nEDate,bLeapMonth = hUserDefData[Cfate::EYear],hUserDefData[Cfate::EMonth],hUserDefData[Cfate::EDate],hUserDefData[Cfate::LeapMonth]
    h2 = oWeenApi.g_fe_score(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
    nDays = Xdate.GetEMonthDays(nEYear,nEMonth,bLeapMonth)
    g = []
    (1..nDays).each do |nD|
      g.push("#{nEMonth}/#{nD}")
    end
    h["yinli"] = g
    h2 = jianggong_gixiongscore_month_values(h2)
    h.merge!(h2)
    nWYear, nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth, 1, bLeapMonth)
    nWHour,nWMin,sex = hUserDefData[Cfate::WHour],hUserDefData[Cfate::WMinute],hUserDefData[Cfate::Sex] ? 0 : 1
    h4 = jianggong_gixiongscore_month_other_values(nPanType,nWYear,nWMonth,nWDate,nWHour,nDays)
    h.merge!(h4) { |key, v1, v2| v2 }
    
    return h
  end
  # 國曆的吉凶
  def jianggong_gixiongscore_month_solar(oWeenApi,nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
    nWYear, nWMonth,nWDate = hUserDefData[Cfate::WYear],hUserDefData[Cfate::WMonth],hUserDefData[Cfate::WDate]
    nWHour,nWMin,sex = hUserDefData[Cfate::WHour],hUserDefData[Cfate::WMinute],hUserDefData[Cfate::Sex] ? 0 : 1
    # 開始陰曆月分
    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear, nWMonth, 1)
    nDays = Xdate.GetWMonthDays(nWYear,nWMonth)
    # 結束陰曆月分
    nEYear2,nEMonth2,nEDate2,bLeapMonth2 = Xdate.West2East(nWYear, nWMonth, nDays)
    # 為了讓loop迴圈正常結束，所以結束的月分要變成下一個月
    nEYear2,nEMonth2,nEDate2,bLeapMonth2 = Xdate.NextEMonth(nEYear2,nEMonth2,bLeapMonth2)
    h = {}
    h["guoli"] = []
    h["gi"] = []
    h["xiong"] = []
    loop do
      bl = bLeapMonth ? 1 : 0
      ts = Xdate.make_timestamp_lunar(nEYear,nEMonth,nEDate,nWHour,bl,sex)
      hudt = {"timestamp_udt" => ts, "name" => hUserDefData[Cfate::Name], "house" => hUserDefData[Star::HOUSE_NAME], "caltype" => hUserDefData[Cfate::CalType]}
      hUserDefData = Jianggong.get_cur_date_info(hudt)
      h2 = oWeenApi.g_fe_score(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
      h3 = jianggong_gixiongscore_month_values(h2)
      h = jianggong_gixiongscore_month_values_add(h,h3)

      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEMonth(nEYear,nEMonth,bLeapMonth)
      break if Xdate.same_month(nEYear,nEMonth,bLeapMonth,nEYear2,nEMonth2,bLeapMonth2)
    end
    nFirst = h["guoli"].index("#{nWMonth}/#{1}")
    nLast = nFirst + nDays
    h["guoli"] = h["guoli"][nFirst...nLast]
    h["gi"] = h["gi"][nFirst...nLast]
    h["xiong"] = h["xiong"][nFirst...nLast]
    h4 = jianggong_gixiongscore_month_other_values(nPanType,nWYear,nWMonth,nWDate,nWHour,nDays)
    h.merge!(h4) { |key, v1, v2| v2 }

    return h
  end
  def jianggong_gixiongscore_month_oneday(oWeenApi,nPanType,hUserInfo,hUserDefData,hApFunc,hParAll,nDays=1)
    nWYear, nWMonth,nWDate,nWHour = hUserDefData[Cfate::WYear],hUserDefData[Cfate::WMonth],hUserDefData[Cfate::WDate],hUserDefData[Cfate::WHour]
    nWHour,nWMin,sex = hUserDefData[Cfate::WHour],hUserDefData[Cfate::WMinute],hUserDefData[Cfate::Sex] ? 0 : 1
    # 開始陰曆月分
    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear, nWMonth, 1)
    # nDays = 1
    h2 = oWeenApi.g_fe_score(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
    h = jianggong_gixiongscore_month_values(h2)

    nFirst = h["guoli"].index("#{nWMonth}/#{nWDate}")
    nLast = nFirst + nDays
    h["guoli"] = h["guoli"][nFirst...nLast]
    h["gi"] = h["gi"][nFirst...nLast]
    h["xiong"] = h["xiong"][nFirst...nLast]
    h4 = jianggong_gixiongscore_month_other_values(nPanType,nWYear,nWMonth,nWDate,nWHour,nDays)
    h.merge!(h4) { |key, v1, v2| v2 }

    return h
  end
  def jianggong_gixiongscore_month_days(oWeenApi,nPanType,hUserInfo,hUserDefData,hApFunc,hParAll,nDays=5)
    nDays = 31 if nDays > 31
    hUdt = hUserDefData.clone
    h = jianggong_gixiongscore_month_oneday(oWeenApi,nPanType,hUserInfo,hUdt,hApFunc,hParAll,nDays)
    nRemainDays = nDays - h["guoli"].length
    if nRemainDays > 0 then
      hUdt[Cfate::WYear],hUdt[Cfate::WMonth] = Xdate.NextWMonth(hUdt[Cfate::WYear],hUdt[Cfate::WMonth])
      hUdt[Cfate::WDate] = 1
      h2 = jianggong_gixiongscore_month_oneday(oWeenApi,nPanType,hUserInfo,hUdt,hApFunc,hParAll,nRemainDays)
      h = jianggong_gixiongscore_month_values_add(h,h2)

      nRemainDays = nDays - h["guoli"].length
      if nRemainDays > 0 then
        hUdt[Cfate::WYear],hUdt[Cfate::WMonth] = Xdate.NextWMonth(hUdt[Cfate::WYear],hUdt[Cfate::WMonth])
        hUdt[Cfate::WDate] = 1
        h2 = jianggong_gixiongscore_month_oneday(oWeenApi,nPanType,hUserInfo,hUdt,hApFunc,hParAll,nRemainDays)
        h = jianggong_gixiongscore_month_values_add(h,h2)
      end
    end
    return h
  end
  def jianggong_gixiongscore_month_values(h2)
    h = {}
    h["guoli"] = h2["xAxis"]["guoli"] # is Array
    h["gi"] = h2["explode"]["gi"].values.collect {|x| (x / 10.0).round(1)}
    h["xiong"] = h2["explode"]["xiong"].values.collect {|x| (x / 10.0).round(1)}

    return h
  end
  # 1、流月輸出內容 ---> 項目、國曆月日、星期、干支日、農曆月日、吉凶分數
  # 2、流日輸出內容 ---> 項目、時辰(03:00 ~ 05:00)、干支時、吉凶分數
  # 3、流時輸出內容 ---> 項目、時間(00:00 ~ 10:00)、吉凶分數
  def jianggong_gixiongscore_month_other_values(nPanType, nWYear, nWMonth, nWDate, nWHour, nDays)
    h = {}
    g = []
    a = []
    w = []
    l = []
    if (nPanType == Cfate::PAN_FLOWMONTH) then
      nY2 = nWYear
      nW2 = nWMonth
      nD2 = nWDate
      (1..nDays).each do |nD|
        # => {"jieqi_ganzhi_year"=>"庚子", "jieqi_ganzhi_month"=>"己卯", "jieqi_ganzhi_day"=>"丙子", "jieqi_ganzhi_hour"=>"庚寅", "yinli_ganzhi_year"=>"庚子", "yinli_ganzhi_month"=>"庚辰", "yinli_ganzhi_day"=>"丙子", "yinli_ganzhi_hour"=>"庚寅", "timestamp"=>"2020040304"}
        # nD2 = nDays == 1 ? nWDate : nD
        h2 = Xdate.api_ganzhi_str(nY2, nW2,nD2,2)
        g.push("#{nY2}/#{nW2}/#{nD2}")
        a.push(h2["jieqi_ganzhi_day"])
        w.push(Xdate.GetWWeekDayStr(nY2, nW2, nD2))
        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nY2, nW2, nD2)
        l.push("#{Xdate.GetSimpleEMonthDateStr(nEYear,nEMonth,nEDate,bLeapMonth)}")

        nY2,nW2,nD2 = Xdate.NextWDate(nY2,nW2,nD2)
      end
      h["guoli"] = g
      h["week_day"] = w
      h["jieqi_ganzhi_day"] = a
      h["yinli"] = l
    elsif (nPanType == Cfate::PAN_FLOWDATE) then
      (0..11).each do |nETime|
        nEarth = nETime + 1
        w.push("#{Xdate.GetETimeRangeStr(nEarth)}")
        h2 = Xdate.api_ganzhi_str(nWYear,nWMonth,nWDate,Xdate.ETime2Hour(nETime))
        a.push(h2["jieqi_ganzhi_hour"])
      end
      h["hour"] = w
      h["jieqi_ganzhi_hour"] = a
      h2 = Xdate.api_ganzhi_str(nWYear,nWMonth,nWDate,0)
      h["jieqi_ganzhi_day"] = h2["jieqi_ganzhi_day"]
    elsif (nPanType == Cfate::PAN_FLOWTIME) then
      nETime = Xdate.Hour2ETime(nWHour)
      (0..11).each do |nEMin|
        w.push("#{Xdate.GetEMinRangeStr(nETime,nEMin)}")
      end
      h["minute"] = w
      h2 = Xdate.api_ganzhi_str(nWYear,nWMonth,nWDate,Xdate.ETime2Hour(nETime))
      h["jieqi_ganzhi_hour"] = h2["jieqi_ganzhi_hour"]
    end
    return h
  end
  def jianggong_gixiongscore_month_values_add(h2,h3)
    h = {}
    h["gi"] = h2["gi"] + h3["gi"]
    h["xiong"] = h2["xiong"] + h3["xiong"]
    h["guoli"] = h2["guoli"] + h3["guoli"]
    return h
  end
  def make_api4_day_gixiong(h2,i)
    h = {}
    h["guoli"] = h2["guoli"][i]
    h["gi"] = h2["gi"][i]
    h["xiong"] = h2["xiong"][i]
    h["week_day"] = h2["week_day"][i]
    h["jieqi_ganzhi_day"] = h2["jieqi_ganzhi_day"][i]
    h["yinli"] = h2["yinli"][i]
    return h
  end
  def make_api4_hour_gixiong(h2,i)
    h = {}
    h["hour"] = h2["hour"][i]
    h["gi"] = h2["gi"][i]
    h["xiong"] = h2["xiong"][i]
    h["jieqi_ganzhi_hour"] = h2["jieqi_ganzhi_hour"][i]
    return h
  end
  def make_api4_minute_gixiong(h2,i)
    h = {}
    h["minute"] = h2["minute"][i]
    h["gi"] = h2["gi"][i]
    h["xiong"] = h2["xiong"][i]
    return h
  end

  def jianggong_api_farmercal(h_par_org)
    hData = Jianggong.get_cur_date_info(h_par_org) if h_par_org["timestamp_udt"] != nil
    hData = Jianggong.get_user_info(h_par_org) if h_par_org["timestamp"] != nil && hData == nil
    hData = Jianggong.get_cur_date_info(h_par_org) if hData == nil
    # hData = Jianggong.get_user_info(h_par_org)
    result = jianggong_api_farmercal2(hData)
    result["final_success"] = true
    return result
  end
  def jianggong_api_farmercal2(hData)
    oWeenApi = WeenApi.new
    return oWeenApi.g_farmercal_jianggong(hData)
  end
  def gpc_api_farmercal(h_par_org)
    hData = Jianggong.get_cur_date_info(h_par_org) if h_par_org["timestamp_udt"] != nil
    hData = Jianggong.get_user_info(h_par_org) if h_par_org["timestamp"] != nil && hData == nil
    hData = Jianggong.get_cur_date_info(h_par_org) if hData == nil

    oWeenApi = WeenApi.new
    result = oWeenApi.g_farmercal_gpc(hData)
    result["final_success"] = true
    return result
  end
  def gpc_api_farmercal_days(h_par_org)
      days = h_par_org["days"].to_i
      days = 1 if days < 1
      days = 31 if days > 31

      result = {}
      hData = Jianggong.get_cur_date_info(h_par_org)
      a = []
      (1..days).each do |d|
        r = gpc_api_farmercal(hData)
        a.push(r)
        hData["timestamp_udt"] = Xdate.next_timestamp_days(hData["timestamp_udt"],1)
      end
      result["cals"] = a
      
      return result
  end
  def gpc_api_farmercal_month(y,m)
    result = {}
    h = {}

    return result if y == 0 || m == 0

    days = Xdate.GetWMonthDays(y,m)
    a = []
    (1..days).each do |d|
      h["timestamp_udt"] = Xdate.make_timestamp1(y,m,d,1)
      r = gpc_api_farmercal(h)
      a.push(r)
    end
    result["cals"] = a

    return result
  end
  def gpc_api_farmercal_years(start_year,stop_year)
    result = {}
    h = {}

    return result if start_year == 0 || stop_year == 0

    (start_year..stop_year).each do |y|
      a1 = []
      (1..12).each do |m|
        h2 = gpc_api_farmercal_month(y,m)
        a2 = h2["cals"]
        a1.push(a2)
      end
      result["y_#{y}"] = a1
    end
    return result
  end
  # 一天之內，所有house的吉凶 
  def jianggong_api_oneday_allhosue_gixiong(h_par_org,hApFunc,hParAll)
    h = {}

    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserInfo = Jianggong.get_user_info(h_par_org)

    nPanType = Cfate::PAN_FLOWDATE

    # 1 運氣(命宮) ;  3 感情(夫妻宮); 5 財運(財帛宮); 6 疾病(疾厄宮); 9 事業(官祿宮)
    d_h = {}
    [1,3,5,6,9].each do |house|

      hUserDefData[Star::HOUSE_NAME] = house
      # nPanType = Cfate::PAN_FLOWDATE
      a = jianggong_api_one_day_a(hUserInfo,hUserDefData,hApFunc,hParAll)
      d_h["house_#{house}"] = a
      # h2 = jianggong_gixiongscore(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
      # d_h["house_#{house}"] = h2
    end
    h["date"] = "#{hUserDefData[Cfate::WYear]}-#{hUserDefData[Cfate::WMonth]}-#{hUserDefData[Cfate::WDate]}"
    h["list"] = d_h
    
    h["final_success"] = true

    return h
  end

  # 3、萬年曆查詢輸出：
      # 輸入 ==> 查詢國曆或農曆(輸入年月日時分)、出生地經緯度
      # 輸出 --> 國曆年月日時、農曆年月日時、真太陽時(年月日時分) 
      # 重慶 Xdate.api_rst_localtime_to_localsuntime(2020,3,5,0,35,106.97357,29.63438) [2020, 3, 4, 23, 31]
      # http://127.0.0.1:3000/api/v1/jianggong?api_id=api_3&longitude=106.97357&latitude=29.63438&timestamp=202003050035M
  # ============================
  def jianggong_api_wannianli(h_par_org)
    h = Jianggong.get_cur_date_info(h_par_org) if h_par_org["timestamp_udt"] != nil
    h = Jianggong.get_user_info(h_par_org) if h_par_org["timestamp"] != nil && h == nil
    h = Jianggong.get_cur_date_info(h_par_org) if h == nil
    nWYear, nWMonth,nWDate = h[Cfate::WYear],h[Cfate::WMonth],h[Cfate::WDate]
    nWHour,nWMin = h[Cfate::WHour],h[Cfate::WMinute]
    longitude,latitude = h["longitude"],h["latitude"]
    nWsYear,nWsMonth,nWsDate,nWsHour,nWsMin = Xdate.api_rst_localtime_to_localsuntime(nWYear, nWMonth,nWDate, nWHour,nWMin,longitude,latitude)
    h["Rs_WYear"] = nWsYear
    h["Rs_WMonth"] = nWsMonth
    h["Rs_WDate"] = nWsDate
    h["Rs_Hour"] = nWsHour
    h["Rs_Min"] = nWsMin
    nEsYear,nEsMonth,nEsDate,bsLeapMonth = Xdate.West2East(nWsYear,nWsMonth,nWsDate)
    h["Rs_EYear"] = nEsYear
    h["Rs_EMonth"] = nEsMonth
    h["Rs_EDate"] = nEsDate
    h["Rs_ELeapMonth"] = bsLeapMonth
    h[Cfate::Sex] = h[Cfate::Sex] ? "F" : "M"
    h.delete("Sex")
    h.delete("Name")

    h["final_success"] = true

    return h
  end

  def jianggong_api_one_month_oneday_houses(hUserInfo,hUserDefData,hApFunc,hParAll)
    h = {}
    y_udt = hUserDefData[Cfate::WYear]
    m_udt = hUserDefData[Cfate::WMonth]
    d_udt = hUserDefData[Cfate::WDate]
    # 預防早子時的問題 2021/1/12 23:00 bug fixed
    hUserDefData[Cfate::WHour] = 0
    a = Array.new
    [1,3,5,6,9].each do |house|
      h2 = hUserDefData.clone
      h2[Star::HOUSE_NAME] = house
      h3 = jianggong_api_one_month_oneday(hUserInfo,h2,hApFunc,hParAll)
      h3["house"] = house
      h3["type"] = house
      a.push(h3)
    end
    h["date"] = "#{y_udt}-#{m_udt}-#{d_udt}"
    h["list"] = a

    h["final_success"] = true

    return h    
  end
  def jianggong_api_one_month_oneday(hUserInfo,hUserDefData,hApFunc,hParAll)
    # h_par_org["timestamp"] = "198103021022F" #["198103021022F","200108091123M","197803282345M"][rand(3)]
    h = {}

    hUserDefData["CurrentDate"] = true
    h2 = jianggong_api_one_month(hUserInfo,hUserDefData,hApFunc,hParAll)
    y_udt = hUserDefData[Cfate::WYear]
    m_udt = hUserDefData[Cfate::WMonth]
    d_udt = hUserDefData[Cfate::WDate]
    # nPanType = h_par_org["pt"].to_i <= Cfate::PAN_FLOWYEAR || h_par_org["pt"].to_i > Cfate::PAN_FLOWTIME ? Cfate::PAN_FLOWMONTH : h_par_org["pt"].to_i
    h2["guoli"].each_with_index do |guoli,i|
      a2 = guoli.split("/")
      if (a2.length == 3) then
        y = a2[0].to_i
        m = a2[1].to_i
        d = a2[2].to_i
      else
        m = a2[0].to_i
        d = a2[1].to_i
      end
      if ([m,d] == [m_udt,d_udt]) then
        h = make_api4_day_gixiong(h2,i)
      end
    end

    return h
  end
end
