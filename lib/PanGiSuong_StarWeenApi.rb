require("Star.rb")
require("Pm.rb")

# For Star
# rule 1 錯誤版本: 四化飛出的宮位，有碰到先天四化或宮干自化者，即為吉（Rule1 不管飛出宮位之宮干自化）

# 正確版本
# rule 1: 四化飛出的宮位，有碰到先天四化，即為吉
#         飛出化忌者，尋找該宮位之主星有化權者，該化權的天干為尋找區間的干支之天干者為吉；
#         飛出化祿者，尋找該宮位之主星有化科，者，該化科的天干為尋找區間的干支之天干者為吉；
# rule 2: 四化飛出的宮位，若碰到宮干自化祿權科者為吉，碰到宮干自化忌者為凶
# rule 3: 四化之化忌飛出的宮位及其對宮的地支，在此區間地支相同者為凶即為凶
class PanGiSuong
  def wae_Star_Reset_Result()
    aRule1 = aRule2 = aRule3 = nil
    @FlowEYear = 0
    @FlowEMonth = 0
    @FlowLeap = false
    @FlowEDate = 0
  end

  def wae_Star_GetGiSuong_TenYear(nPanType,hUserInfo,hUserDefData,hUserType,nGiSuongHouseId)
    oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    # hStarInfo = oStar.g_GetStarPanInfo(nPanType,hUserInfo,hUserDefData,@UserType)
    # flow_title = "#{oStar.g_getLargeSanStartYear()}~#{oStar.g_getLargeSanStartYear() + 9}"
    flow_title = oStar.gWeen_GetFlowTimeStr(nPanType)

    aRule1,aRule2,aRule3 = wae_GetGiSuong_Rule(oStar,Cfate::PAN_TENYEAR)
    #return aRule1,aRule2,aRule3
    vRule1,vRule2,vRule3 = wae_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)

    h,a = wae_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3)
    return h,a,flow_title
  end
  def wae_Star_GetGiSuong_FlowYear(nPanType,hUserInfo,hUserDefData,hUserType,nGiSuongHouseId)
    # oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    hUserDefData[Cfate::EMonth] = 6
    hUserDefData[Cfate::LeapMonth] = false
    # hUserType[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN
    hUserType[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN_FLOW_SKY
    oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    # hStarInfo = oStar.g_GetStarPanInfo(nPanType,hUserInfo,hUserDefData,hUserType)
    # flow_title = Xdate.GetYearStr(oStar.fdg_EY(nPanType))
    flow_title = oStar.gWeen_GetFlowTimeStr(nPanType)

    aRule1,aRule2,aRule3 = wae_GetGiSuong_Rule(oStar,Cfate::PAN_FLOWYEAR)

    vRule1,vRule2,vRule3 = wae_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)
    h,a = wae_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3)
    return h,a,flow_title
  end
  def wae_Star_GetGiSuong_FlowMonth(nPanType,hUserInfo,hUserDefData,hUserType,nGiSuongHouseId)
    oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    # hStarInfo = oStar.g_GetStarPanInfo(nPanType,hUserInfo,hUserDefData,hUserType)
    # flow_title = Xdate.GetYearStr(oStar.fdg_EY(nPanType)) + Xdate.GetMonthStr(oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType))
    flow_title = oStar.gWeen_GetFlowTimeStr(nPanType)

    aRule1,aRule2,aRule3 = wae_GetGiSuong_Rule(oStar,Cfate::PAN_FLOWMONTH)

    vRule1,vRule2,vRule3 = wae_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)

    h,a = wae_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3)
    return h,a,flow_title
  end
  def wae_Star_GetGiSuong_FlowDate(nPanType,hUserInfo,hUserDefData,hUserType,nGiSuongHouseId)
    oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    # hStarInfo = oStar.g_GetStarPanInfo(nPanType,hUserInfo,hUserDefData,hUserType)
    # flow_title = Xdate.GetYearStr(oStar.fdg_EY(nPanType)) + Xdate.GetMonthStr(oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType))
    # flow_title += Xdate.GetDateStr(oStar.fdg_ED(nPanType))
    flow_title = oStar.gWeen_GetFlowTimeStr(nPanType)

    aRule1,aRule2,aRule3 = wae_GetGiSuong_Rule(oStar,nPanType)

    vRule1,vRule2,vRule3 = wae_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)
    h,a = wae_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3)
    return h,a,flow_title
  end
  def wae_Star_GetGiSuong_FlowTime(nPanType,hUserInfo,hUserDefData,hUserType,nGiSuongHouseId)
    oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    # hStarInfo = oStar.g_GetStarPanInfo(nPanType,hUserInfo,hUserDefData,hUserType)
    # flow_title = Xdate.GetYearStr(oStar.fdg_EY(nPanType)) + Xdate.GetMonthStr(oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType))
    # flow_title += Xdate.GetDateStr(oStar.fdg_ED(nPanType))
    # flow_title += Xdate.GetETimeStr(oStar.fdg_ET(nPanType))
    flow_title = oStar.gWeen_GetFlowTimeStr(nPanType)

    aRule1,aRule2,aRule3 = wae_GetGiSuong_Rule(oStar,nPanType)

    vRule1,vRule2,vRule3 = wae_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)

    h,a = wae_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3)
    return h,a,flow_title
  end

  def wae_GetGiSuong_Rule(oStar,nPanType)
    aRule1 = aRule2 = aRule3 = nil
    aRule1 = wae_GetGiSuong_Rule1(oStar,nPanType)
    aRule2 = wae_GetGiSuong_Rule2(oStar,nPanType)
    aRule3 = wae_GetGiSuong_Rule3(oStar,nPanType)
# puts("aRule1 : #{aRule1}")
# puts("aRule2 : #{aRule2}")
# puts("aRule3 : #{aRule3}")
    return aRule1,aRule2,aRule3
  end


  # 吉年找法
  def wae_GetGiSuong_Rule1(oStar,nPanType)
    nHouseEarth = oStar.gGiSuong_GetHouseEarth(nPanType,@m_nShowGiSuongHouseId)

    # 尋找天干
    nSky = oStar.gHouse_GetSky(nPanType,nHouseEarth)
# puts("nSky : #{nSky}")

    # find flow sky
    nFlowSky = oStar.gGiSuong_GetGiSuong_Rule1_OrgSky(nPanType)
# puts("nFlowSky : #{nFlowSky}")

    # 尋找四化
    aFourHuaHouse = oStar.gGiSuong_FindFourHuaHouse(nPanType,nSky)  # 所選天干四化
    aFlowFourHuaHouse = oStar.gGiSuong_FindFourHuaHouse(nPanType,nFlowSky) # 先天四化
# puts("aFourHuaHouse : #{aFourHuaHouse}")
# puts("aFlowFourHuaHouse : #{aFlowFourHuaHouse}")

    # 吉凶宮位判別
    # 判斷四化飛出有沒有跟先天四化遭遇，若完全沒有遇到就結束，表示沒有變化
    aFocusHouse = wae_GetGiSuong_Rule1_HouseCheck(aFourHuaHouse,aFlowFourHuaHouse,oStar,nPanType)
    if (aFocusHouse.length == 0) then
      return nil
    end
# puts("aFocusHouse : #{aFocusHouse}")

    # 尋找刻應的天干
    aRule = wae_GetGiSuong_Rule1_FindSky(oStar,nPanType,aFocusHouse)

    return aRule
  end

  # 吉凶宮位判別
  def wae_GetGiSuong_Rule1_HouseCheck(aFourHuaHouse,aFlowFourHuaHouse,oStar,nPanType)
    #aFocusHouse = aFourHuaHouse & aFlowFourHuaHouse
    aFocusHouse = Array.new(4,nil)

    (0..3).each do |nFourHuaIndex|
      # 先天四化
      if (aFlowFourHuaHouse.index(aFourHuaHouse[nFourHuaIndex]) != nil) then
        aFocusHouse[nFourHuaIndex] = aFourHuaHouse[nFourHuaIndex]
        # Rule1 不管飛出之宮的宮干自化，只看先天四化
      else
        # 宮自化 2021/8/3 重新開放此判斷
        aaFourHua,nFourHuaCount = oStar.gHouse_GetAStarsSelfFourHua(nPanType,aFourHuaHouse[nFourHuaIndex])
        if (nFourHuaCount > 0) then
          aFocusHouse[nFourHuaIndex] = aFourHuaHouse[nFourHuaIndex]
        end
      end
    end
    return aFocusHouse
  end

  # 尋找刻應的天干
  def wae_GetGiSuong_Rule1_FindSky(oStar,nPanType,aFocusHouse)
    aFourHuaToFind = [3,2,1,0] # 祿配忌，權配科

    aRule = Array.new
    (0..3).each do |nFourHuaIndex|
      aRule += wae_GetGiSuong_Rule1_FindSky_FourHua(oStar,nPanType,aFourHuaToFind[nFourHuaIndex],aFocusHouse[nFourHuaIndex],nFourHuaIndex)
    end
    return aRule
  end

  # 尋找四化之一刻應的天干
  def wae_GetGiSuong_Rule1_FindSky_FourHua(oStar,nPanType,nFourHuaToFind,nFocusHouseEarth,nFourHuaIndex)
    aRule = Array.new
    (1..10).each do |nSky|
      nStar = oStar.gGiSuong_GetFourHuaStarValue(nSky,nFourHuaToFind)
      nHouseEarth = oStar.gHouse_GetAStar_HouseEarth(nPanType,nStar)
      if (nHouseEarth == nFocusHouseEarth) then
        a = Array.new
        a.push(nFocusHouseEarth)
        a.push(nSky)
        a.push(nFourHuaToFind)
        a.push(nFourHuaIndex) # 儲存是由哪一個四化找到的刻應的
        aRule.push(a)
      end
    end
    return aRule
  end

  # rule 2
  def wae_GetGiSuong_Rule2(oStar,nPanType)
    nHouseEarth = oStar.gGiSuong_GetHouseEarth(nPanType,@m_nShowGiSuongHouseId)

    # 尋找天干
    nSky = oStar.gHouse_GetSky(nPanType,nHouseEarth)

    # 尋找四化
    aFourHuaHouse = oStar.gGiSuong_FindFourHuaHouse(nPanType,nSky)  # 所選天干四化
    # 尋找刻應的小限應期，自化祿權科為吉年，忌為凶年
    aRule = wae_GetGiSuong_Rule2_Find(oStar,nPanType,aFourHuaHouse)

    return aRule
  end

  # 尋找刻應的小限應期，自化祿權科為吉年，忌為凶年
  def wae_GetGiSuong_Rule2_Find(oStar,nPanType,aFourHuaHouse)
    aRule2 = Array.new(2) { Array.new }
    (0..3).each do |nFourHuaIndex|
      aRule = wae_GetGiSuong_Rule2_Find_FourHua(oStar,nPanType,aFourHuaHouse[nFourHuaIndex],nFourHuaIndex)
      if (aRule[0] != nil) then aRule2[0] += aRule[0] end
      if (aRule[1] != nil) then aRule2[1] += aRule[1] end
    end
    return aRule2
  end

  # 尋找四化之一刻應的小限應期，宮自化祿權科為吉年，忌為凶年
  def wae_GetGiSuong_Rule2_Find_FourHua(oStar,nPanType,nFocusHouseEarth,nFocusFourHuaIndex)
    aRule2 = Array.new(2) { Array.new }

    if (nFocusHouseEarth == nil) then
      return aRule2
    end

    # 找出宮天干
    #nHouseSky = oStar.gHouse_GetSky(nPanType,nFocusHouseEarth)

    # 判斷有無在此宮內,即宮自化
    aaFourHua,nFourHuaCount = oStar.gHouse_GetAStarsSelfFourHua(nPanType,nFocusHouseEarth)

    aaFourHua.each do |aFourHua|
      aRule = wae_GetGiSuong_Rule2_Find_FourHua_Result(oStar,nPanType,nFocusHouseEarth,aFourHua[0],nFocusFourHuaIndex)
      if (aRule[0] != nil) then aRule2[0] += aRule[0] end
      if (aRule[1] != nil) then aRule2[1] += aRule[1] end
    end
    return aRule2
  end

  def wae_GetGiSuong_Rule2_Find_FourHua_Result(oStar,nPanType,nFocusHouseEarth,nFourHuaIndex,nFocusFourHuaIndex)
    aRule = Array.new(2)
    if (nFourHuaIndex != 99) then
      if (nFourHuaIndex < 3) then
        # 祿權科
        nGiSuongIndex = 0
      else
        # 忌
        nGiSuongIndex = 1
      end
      if (nPanType == Cfate::PAN_TENYEAR) then
        aRule[nGiSuongIndex] = wae_GetGiSuong_Rule2_Find_FourHua_Result_TenYear(oStar,nPanType,nFocusHouseEarth,nFocusFourHuaIndex)
      elsif (nPanType >= Cfate::PAN_FLOWYEAR) then
        aRule[nGiSuongIndex] = wae_GetGiSuong_Rule2_Find_FourHua_Result_FlowYear(oStar,nPanType,nFocusHouseEarth,nFocusFourHuaIndex)
      end
    end
    return aRule
  end

  def wae_GetGiSuong_Rule2_Find_FourHua_Result_TenYear(oStar,nPanType,nFocusHouseEarth,nFocusFourHuaIndex)
    nLargeSan = oStar.gHouse_getLargeSan()
    nStartAge = oStar.gGiSuong_getLargeSanStartYearOld(nLargeSan)
    nStopAge = nStartAge + 9

    aRule = Array.new
    m = oStar.gGiSuong_GetSmallSan(nFocusHouseEarth)
    (0..7).each do |i|
      if ((m >= nStartAge) && (m <= nStopAge)) then
        a = Array.new
        a.push(nFocusHouseEarth)
        a.push(m)
        a.push(nFocusFourHuaIndex)
        aRule.push(a)
      end

      m += 12
    end
    return aRule

  end

  def wae_GetGiSuong_Rule2_Find_FourHua_Result_FlowYear(oStar,nPanType,nFocusHouseEarth,nFocusFourHuaIndex)
    aRule = Array.new
    a = Array.new
    a.push(nFocusHouseEarth)
    a.push(0)
    a.push(nFocusFourHuaIndex)  # 儲存是由哪一個四化找到的刻應的
    aRule.push(a)
    return aRule
  end

  def wae_GetGiSuong_Rule3(oStar,nPanType)
    nHouseEarth = oStar.gGiSuong_GetHouseEarth(nPanType,@m_nShowGiSuongHouseId)
#puts("nHouseEarth3:#{nHouseEarth}")

    # 尋找天干
    nSky = oStar.gHouse_GetSky(nPanType,nHouseEarth)
#puts("nSky3:#{nSky}")

    # 尋找四化
    aFourHuaHouse = oStar.gGiSuong_FindFourHuaHouse(nPanType,nSky)  # 所選天干四化
#puts("aFourHuaHouse3:#{aFourHuaHouse}")

    # 化忌之星所在宮位及其對宮之地支為凶年
    aRule = wae_GetGiSuong_Rule3_Find(aFourHuaHouse[3],oStar,nPanType)

    return aRule
  end

  def wae_GetGiSuong_Rule3_Find(nHuaGiHouseEarth,oStar,nPanType)
    # 不必儲存是由哪一個四化找到的刻應的，因為只有化忌
    aRule = Array.new
    aRule.push(nHuaGiHouseEarth)
    aRule.push(Earth.ModifyEarth(nHuaGiHouseEarth+6))
    return aRule
  end

  def wae_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)
    vRule1 = wae_GetGiSuong_Value_Rule1(nPanType,oStar,aRule1)
    vRule2 = wae_GetGiSuong_Value_Rule2(nPanType,oStar,aRule2)
    vRule3 = wae_GetGiSuong_Value_Rule3(nPanType,oStar,aRule3)
# puts("vRule1 : #{vRule1}")
# puts("vRule2 : #{vRule2}")
# puts("vRule3 : #{vRule3}")
    return vRule1,vRule2,vRule3
  end
  def wae_GetGiSuong_Value_Rule1(nPanType,oStar,aRule1)
    vRule1 = Array.new(10,0)
    if (aRule1 == nil) then
      return vRule1
    end
    aRule1.each do |r|
      nSkyIndex = Sky.Sky2SkyIndex(r[1])
      if (vRule1[nSkyIndex] > 0) then
        vRule1[nSkyIndex] += 23
      else
        vRule1[nSkyIndex] += 30
      end
    end
    return vRule1
  end

  def wae_GetGiSuong_Value_Rule2(nPanType,oStar,aRule2)
    case (nPanType)
      when (Cfate::PAN_TENYEAR) then
        return wae_GetGiSuong_Value_Rule2_TenYear(nPanType,oStar,aRule2)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        return wae_GetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,aRule2)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        return wae_GetGiSuong_Value_Rule2_FlowMonth(nPanType,oStar,aRule2)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        return wae_GetGiSuong_Value_Rule2_FlowDate(nPanType,oStar,aRule2)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        return wae_GetGiSuong_Value_Rule2_FlowTime(nPanType,oStar,aRule2)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        return Hash.new    # PAN_FLOWMIN
    end
  end
  def wae_GetGiSuong_Value_Rule2_TenYear(nPanType,oStar,aRule2)
    vRule2 = [Array.new(10,0),Array.new(10,0)]
    if (aRule2 == nil) then
      return vRule2
    end
    v = [18,-18]
    nLargeSan = oStar.gHouse_getLargeSan()
    nStartAge = oStar.gGiSuong_getLargeSanStartYearOld(nLargeSan)
    # 吉 0,凶 1
    aRule2.each_index do |i|
      aRule2[i].each do |r|
        nAge = r[1]
        vRule2[i][nAge - nStartAge] += v[i]
      end
    end
    return vRule2
  end
  def wae_GetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,aRule2)
    vRule2 = [Array.new(12,0),Array.new(12,0)]
    if (aRule2 == nil) then
      return vRule2
    end
    v = [18,-18]
    # 吉 0,凶 1
    aRule2.each_index do |i|
      aRule2[i].each do |r|
        nFocusHouseEarthIndex = Earth.Earth2EarthIndex(r[0])
        vRule2[i][nFocusHouseEarthIndex] += v[i]
      end
    end
    return vRule2
  end
  def wae_GetGiSuong_Value_Rule2_FlowMonth(nPanType,oStar,aRule2)
    return wae_GetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,aRule2)
  end
  def wae_GetGiSuong_Value_Rule2_FlowDate(nPanType,oStar,aRule2)
    return wae_GetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,aRule2)
  end
  def wae_GetGiSuong_Value_Rule2_FlowTime(nPanType,oStar,aRule2)
    return wae_GetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,aRule2)
  end
  def wae_GetGiSuong_Value_Rule3(nPanType,oStar,aRule3)
    vRule3 = Array.new(12,0)
    if (aRule3 == nil) then
      return vRule3
    end
    aRule3.each do |nHouseEarth|
      nEarthIndex = Earth.Earth2EarthIndex(nHouseEarth)
      vRule3[nEarthIndex] += -30
    end
    return vRule3
  end

  def wae_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3)
    h = Hash.new
    h["rule1"],a = wae_SetGiSuong_Value_Rule1(nPanType,oStar,vRule1)
    h["rule2"] = wae_SetGiSuong_Value_Rule2(nPanType,oStar,vRule2)
    h["rule3"] = wae_SetGiSuong_Value_Rule3(nPanType,oStar,vRule3)
# puts("hRule1 : #{h["rule1"]}")
# puts("hRule2 : #{h["rule2"]}")
# puts("hRule3 : #{h["rule3"]}")
    return h,a
  end
  def wae_SetGiSuong_Value_Rule1(nPanType,oStar,vRule1)
    case (nPanType)
      when (Cfate::PAN_TENYEAR) then
        return wae_SetGiSuong_Value_Rule1_TenYear(nPanType,oStar,vRule1)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        return wae_SetGiSuong_Value_Rule1_FlowYear(nPanType,oStar,vRule1)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        return wae_SetGiSuong_Value_Rule1_FlowMonth(nPanType,oStar,vRule1)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        return wae_SetGiSuong_Value_Rule1_FlowDate(nPanType,oStar,vRule1)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        return wae_SetGiSuong_Value_Rule1_FlowTime(nPanType,oStar,vRule1)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        return Hash.new,[]    # PAN_FLOWMIN
    end
  end
  def wae_SetGiSuong_Value_Rule1_TenYear(nPanType,oStar,vRule1)
    h = Hash.new
    a = Array.new
    nLargeSan = Earth.Modify(oStar.gHouse_getLargeSan())
    nStartEYear = oStar.gGiSuong_getLargeSanStartYear(nLargeSan)
    (0..9).each do |i|
      nEYear = nStartEYear + i
      nSky,nEarth = Xdate.GetLunarYearGanZhiSkyEarth(nEYear)
      h["#{Xdate.GetYearStr(nEYear)}"] = vRule1[Sky.Sky2SkyIndex(nSky)]
      nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,1,1,false)
      a.push(Xdate.GetWYearMonthDateStr_WC(nWYear,nWMonth,nWDate,Cfate::PAN_DISPLAY_WEST))
    end
    return h,a
  end
  def wae_SetGiSuong_Value_Rule1_FlowYear(nPanType,oStar,vRule1)
    h = Hash.new
    a = Array.new
    nEYear = oStar.fdg_EY(nPanType)
    mArray = Xdate.GetEastMonthArray(nEYear)
    (0...mArray.length).each do |nMonthIndex|
      nEMonth = mArray[nMonthIndex][0]
      bLeapMonth = mArray[nMonthIndex][2]
      nSky,nEarth = Xdate.GetLunarMonthGanZhiSkyEarth(nEYear,nEMonth)
      h["#{Xdate.GetMonthStr(nEMonth,bLeapMonth)}"] = vRule1[Sky.Sky2SkyIndex(nSky)]
      nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,nEMonth,1,bLeapMonth)
      a.push(Xdate.GetWYearMonthDateStr_WC(nWYear,nWMonth,nWDate,Cfate::PAN_DISPLAY_WEST))
    end
    return h,a
  end
  def wae_SetGiSuong_Value_Rule1_FlowMonth(nPanType,oStar,vRule1)
    h = Hash.new
    a = Array.new
    nEYear,nEMonth,bLeapMonth = oStar.fdg_EY(nPanType),oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType)
    nDays = Xdate.GetEMonthDays(nEYear,nEMonth,bLeapMonth)
    nDays = 1 if oStar.ddg_CurrentDate()
    (1..nDays).each do |nEDate|
      nD = nDays == 1 ? oStar.fdg_ED(nPanType) : nEDate

      nSky,nEarth = Xdate.GetLunarDateGanZhiSkyEarth(nEYear,nEMonth,nD,1,bLeapMonth)
      h["#{Xdate.GetDateStr(nD)}"] = vRule1[Sky.Sky2SkyIndex(nSky)]
      nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,nEMonth,nD,bLeapMonth)
      # a.push(Xdate.GetWYearMonthDateStr_WC(nWYear,nWMonth,nWDate,Cfate::PAN_DISPLAY_WEST))
      a.push("#{nWMonth}/#{nWDate}")
    end
    return h,a
  end
  def wae_SetGiSuong_Value_Rule1_FlowDate(nPanType,oStar,vRule1)
    h = Hash.new
    a = Array.new
    nEYear,nEMonth,bLeapMonth,nEDate = oStar.fdg_EY(nPanType),oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType),oStar.fdg_ED(nPanType)
    (1..12).each do |nETime|
      nSky,nEarth = Xdate.GetLunarTimeGanZhiSkyEarth(nEYear,nEMonth,nEDate,nETime - 1,bLeapMonth)
      h["#{Xdate.GetETimeStr(nETime - 1)}"] = vRule1[Sky.Sky2SkyIndex(nSky)]
      nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,nEMonth,nEDate,bLeapMonth)
      nHour = Xdate.ETime2Hour(nETime - 1)
      # a.push(Xdate.GetWYearMonthDateTimeStr_WC(nWYear,nWMonth,nWDate,nHour,0,Cfate::PAN_DISPLAY_WEST))
      sh = "%02d" % nHour
      sm = "%02d" % 0
      s = "#{sh}:#{sm}"
      a.push(s)
    end
    return h,a
  end
  def wae_SetGiSuong_Value_Rule1_FlowTime(nPanType,oStar,vRule1)
    h = Hash.new
    a = Array.new
    nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType)
    nSky = oStar.gHouse_GetSky(nPanType,nEarth)
    nGanZhi = Xdate.SkyEarth2GanZhi(nSky,nEarth)
    nEYear,nEMonth,bLeapMonth,nEDate,nETime = oStar.fdg_EY(nPanType),oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType),oStar.fdg_ED(nPanType),oStar.fdg_ET(nPanType)
    nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,nEMonth,nEDate,bLeapMonth)
    (1..12).each do |nEMinute|
      nGanZhiMin = nGanZhi + nEMinute - 1
      nSky,nEarth = Xdate.GetGanZhiSkyEarth(nGanZhiMin)
      h["#{Xdate.GetEMinRangeStr(oStar.fdg_ET(nPanType),nEMinute - 1)}"] = vRule1[Sky.Sky2SkyIndex(nSky)]
      nWHourStart,nMinute = Xdate.GetEMinStart(nETime,nEMinute - 1)
      # a.push(Xdate.GetWYearMonthDateTimeStr_WC(nWYear,nWMonth,nWDate,nWHourStart,nMinute,Cfate::PAN_DISPLAY_WEST))
      sh = "%02d" % nWHourStart
      sm = "%02d" % nMinute
      s = "#{sh}:#{sm}"
      a.push(s)
    end
    return h,a
  end
  def wae_SetGiSuong_Value_Rule2(nPanType,oStar,vRule2)
    case (nPanType)
      when (Cfate::PAN_TENYEAR) then
        return wae_SetGiSuong_Value_Rule2_TenYear(nPanType,oStar,vRule2)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        return wae_SetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,vRule2)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        return wae_SetGiSuong_Value_Rule2_FlowMonth(nPanType,oStar,vRule2)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        return wae_SetGiSuong_Value_Rule2_FlowDate(nPanType,oStar,vRule2)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        return wae_SetGiSuong_Value_Rule2_FlowTime(nPanType,oStar,vRule2)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        return Hash.new    # PAN_FLOWMIN
    end
  end
  def wae_SetGiSuong_Value_Rule2_TenYear(nPanType,oStar,vRule2)
    h = Hash.new
    nLargeSan = Earth.Modify(oStar.gHouse_getLargeSan())
    nStartEYear = oStar.gGiSuong_getLargeSanStartYear(nLargeSan)
    nStartEYearOld = oStar.gGiSuong_getLargeSanStartYearOld(nLargeSan)
    # a = Array.new(10,"")
    # b = Array.new(10,0)
    # c = Array.new(10,0)
    b = Hash.new
    c = Hash.new
    (0..9).each do |i|
      nEYear = nStartEYear + i
      # a[i] = "#{nEYear}:#{nStartEYearOld + i}"
      b["#{Xdate.GetYearStr(nEYear)}"] = vRule2[0][i]
      c["#{Xdate.GetYearStr(nEYear)}"] = vRule2[1][i]
    end
    # h["title"] = a
    h["gi"] = b
    h["xiong"] = c
    return h
  end
  def wae_SetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,vRule2)
    h = Hash.new
    mArray = Xdate.GetEastMonthArray(oStar.fdg_EY(nPanType))
    nEarth = wae_GetGod(oStar) # 子年斗君所在的宮位為1月
    # a = Array.new(mArray.length,"")
    # b = Array.new(mArray.length,0)
    # c = Array.new(mArray.length,0)
    b = Hash.new
    c = Hash.new
    (0...mArray.length).each do |nMonthIndex|
      nEMonth = mArray[nMonthIndex][0]
      bLeapMonth = mArray[nMonthIndex][2]
      nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nEMonth - 1 + nEarth)
      # a[nMonthIndex] = "#{bLeapMonth}#{nEMonth}"
      # b[nMonthIndex] = vRule2[0][nFocusHouseEarthIndex]
      # c[nMonthIndex] = vRule2[1][nFocusHouseEarthIndex]
      b["#{Xdate.GetMonthStr(nEMonth,bLeapMonth)}"] = vRule2[0][nFocusHouseEarthIndex]
      c["#{Xdate.GetMonthStr(nEMonth,bLeapMonth)}"] = vRule2[1][nFocusHouseEarthIndex]
    end
    # h["title"] = a
    h["gi"] = b
    h["xiong"] = c
    return h
  end
  # 子年斗君
  def wae_GetGod(oStar)
    nVal = oStar.cp_getGod()
    nVal = Earth.EarthIndex2Earth(nVal)
    return nVal
  end
  def wae_SetGiSuong_Value_Rule2_FlowMonth(nPanType,oStar,vRule2)
    h = Hash.new
    nEYear,nEMonth,bLeapMonth = oStar.fdg_EY(nPanType),oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType)
    nDays = Xdate.GetEMonthDays(nEYear,nEMonth,bLeapMonth)
    nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType) # 命宮所在宮位起1日
    # a = Array.new(nDays,"")
    # b = Array.new(nDays,0)
    # c = Array.new(nDays,0)
    b = Hash.new
    c = Hash.new
    nDays = 1 if oStar.ddg_CurrentDate()
    (1..nDays).each do |nEDate|
      nD = nDays == 1 ? oStar.fdg_ED(nPanType) : nEDate
      nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nD - 1 + nEarth)
      # a[nD - 1] = "#{nD}:#{nFocusHouseEarthIndex}:#{nEarth}"
      # b[nD - 1] = vRule2[0][nFocusHouseEarthIndex]
      # c[nD - 1] = vRule2[1][nFocusHouseEarthIndex]
      b["#{Xdate.GetDateStr(nD)}"] = vRule2[0][nFocusHouseEarthIndex]
      c["#{Xdate.GetDateStr(nD)}"] = vRule2[1][nFocusHouseEarthIndex]
    end
    # h["title"] = a
    h["gi"] = b
    h["xiong"] = c
    return h
  end
  def wae_SetGiSuong_Value_Rule2_FlowDate(nPanType,oStar,vRule2)
    h = Hash.new
    # a = Array.new(12,"")
    # b = Array.new(12,0)
    # c = Array.new(12,0)
    b = Hash.new
    c = Hash.new
    (1..12).each do |nETime|
      nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nETime)
      # a[nETime - 1] = "#{nETime}:#{nFocusHouseEarthIndex}"
      # b[nETime - 1] = vRule2[0][nFocusHouseEarthIndex]
      # c[nETime - 1] = vRule2[1][nFocusHouseEarthIndex]
      b["#{Xdate.GetETimeStr(nETime - 1)}"] = vRule2[0][nFocusHouseEarthIndex]
      c["#{Xdate.GetETimeStr(nETime - 1)}"] = vRule2[1][nFocusHouseEarthIndex]
    end
    # h["title"] = a
    h["gi"] = b
    h["xiong"] = c
    return h
  end
  def wae_SetGiSuong_Value_Rule2_FlowTime(nPanType,oStar,vRule2)
    h = Hash.new
    nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType) # 命宮所在宮位起1日
    # a = Array.new(12,"")
    # b = Array.new(12,0)
    # c = Array.new(12,0)
    b = Hash.new
    c = Hash.new
    (1..12).each do |nEMinute|
      nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nEMinute - 1 + nEarth)
      # a[nEMinute - 1] = "#{nEMinute}:#{nFocusHouseEarthIndex}"
      # b[nEMinute - 1] = vRule2[0][nFocusHouseEarthIndex]
      # c[nEMinute - 1] = vRule2[1][nFocusHouseEarthIndex]
      b["#{Xdate.GetEMinRangeStr(oStar.fdg_ET(nPanType),nEMinute - 1)}"] = vRule2[0][nFocusHouseEarthIndex]
      c["#{Xdate.GetEMinRangeStr(oStar.fdg_ET(nPanType),nEMinute - 1)}"] = vRule2[1][nFocusHouseEarthIndex]
    end
    # h["title"] = a
    h["gi"] = b
    h["xiong"] = c
    return h
  end
  def wae_SetGiSuong_Value_Rule3(nPanType,oStar,vRule3)
    case (nPanType)
      when (Cfate::PAN_TENYEAR) then
        return wae_SetGiSuong_Value_Rule3_TenYear(nPanType,oStar,vRule3)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        return wae_SetGiSuong_Value_Rule3_FlowYear(nPanType,oStar,vRule3)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        return wae_SetGiSuong_Value_Rule3_FlowMonth(nPanType,oStar,vRule3)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        return wae_SetGiSuong_Value_Rule3_FlowDate(nPanType,oStar,vRule3)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        return wae_SetGiSuong_Value_Rule3_FlowTime(nPanType,oStar,vRule3)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        return Hash.new    # PAN_FLOWMIN
    end
  end
  def wae_SetGiSuong_Value_Rule3_TenYear(nPanType,oStar,vRule3)
    h = Hash.new
    nLargeSan = Earth.Modify(oStar.gHouse_getLargeSan())
    nStartEYear = oStar.gGiSuong_getLargeSanStartYear(nLargeSan)
    (0..9).each do |i|
      nEYear = nStartEYear + i
      nSky,nEarth = Xdate.GetLunarYearGanZhiSkyEarth(nEYear)
      h["#{Xdate.GetYearStr(nEYear)}"] = vRule3[Earth.Earth2EarthIndex(nEarth)]
    end
    return h
  end
  def wae_SetGiSuong_Value_Rule3_FlowYear(nPanType,oStar,vRule3)
    h = Hash.new
    nEYear = oStar.fdg_EY(nPanType)
    mArray = Xdate.GetEastMonthArray(nEYear)
    nEarth = wae_GetGod(oStar) # 子年斗君所在的宮位為1月
    (0...mArray.length).each do |nMonthIndex|
      nEMonth = mArray[nMonthIndex][0]
      bLeapMonth = mArray[nMonthIndex][2]
      nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nEMonth - 1 + nEarth)
      h["#{Xdate.GetMonthStr(nEMonth,bLeapMonth)}"] = vRule3[nFocusHouseEarthIndex]
    end
    return h
  end
  def wae_SetGiSuong_Value_Rule3_FlowMonth(nPanType,oStar,vRule3)
    h = Hash.new
    nEYear,nEMonth,bLeapMonth = oStar.fdg_EY(nPanType),oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType)
    nDays = Xdate.GetEMonthDays(nEYear,nEMonth,bLeapMonth)
    nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType)
    nDays = 1 if oStar.ddg_CurrentDate()
    (1..nDays).each do |nEDate|
      nD = nDays == 1 ? oStar.fdg_ED(nPanType) : nEDate
      nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nD - 1 + nEarth)
      h["#{Xdate.GetDateStr(nD)}"] = vRule3[nFocusHouseEarthIndex]
    end
    return h
  end
  def wae_SetGiSuong_Value_Rule3_FlowDate(nPanType,oStar,vRule3)
    h = Hash.new
    (1..12).each do |nETime|
      nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nETime)
      h["#{Xdate.GetETimeStr(nETime - 1)}"] = vRule3[nFocusHouseEarthIndex]
    end
    return h
  end
  def wae_SetGiSuong_Value_Rule3_FlowTime(nPanType,oStar,vRule3)
    h = Hash.new
    nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType)
    (1..12).each do |nEMinute|
      nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nEMinute - 1 + nEarth)
      h["#{Xdate.GetEMinRangeStr(oStar.fdg_ET(nPanType),nEMinute - 1)}"] = vRule3[nFocusHouseEarthIndex]
    end
    return h
  end

  def wae_GetGiSuongText_Rule1(nSky)
    szGi = ""
    if (@aRule1 == nil) then
      return szGi
    end

    @aRule1.each do |nRuleSky|
      if (nRuleSky == nSky) then
        szGi = Pm.GetStr("IDS_GISUONG_GI")
      end
    end
    return szGi
  end

  def wae_GetGiSuongText_Rule2(nAge)
    szGi = szSuong = ""
    if (@aRule2 == nil) then
      return szGi,szSuong
    end

    @aRule2[0].each do |nRuleAge|
      if (nRuleAge == nAge) then
        szGi = Pm.GetStr("IDS_GISUONG_GI")
      end
    end

    @aRule2[1].each do |nRuleAge|
      if (nRuleAge == nAge) then
        szSuong = Pm.GetStr("IDS_GISUONG_SUONG")
      end
    end

    return szGi,szSuong
  end

  def wae_GetGiSuongText_Rule3(nEarth)
    szSuong = ""
    if (@aRule3 == nil) then
      return szSuong
    end

    @aRule3.each do |nRuleEarth|
      if (nRuleEarth == nEarth) then
        szSuong = Pm.GetStr("IDS_GISUONG_SUONG")
      end
    end

    return szSuong
  end

end
