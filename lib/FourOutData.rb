# coding: UTF-8
require "Pm.rb"

class FourOutData

  def FourOutData.get_lunduan_data(nPanType)
    return Pm.GetStr("lun_duan.fouroutdata.fostr#{nPanType}")
  end
  def FourOutData.getDesc_byid(nPanType,housestay,houseaccept,hua)
    sHouseStay = Pm.GetStr("IDS_S_HOUSE_NAME_#{housestay}")
    sHouseAccept = Pm.GetStr("IDS_S_HOUSE_NAME_#{houseaccept}")
    sHuaType = Pm.GetStr("IDS_S_FOUR_HUA_#{hua}")
    return FourOutData.getDesc(nPanType,sHouseStay,sHouseAccept,sHuaType)
  end

  def FourOutData.getDesc(nPanType,sHouseStay,sHouseAccept,sHuaType)
    sToFind = FourOutData.get_lunduan_data(nPanType)

    sihua_taitou = Pm.t("lun_duan.sihua_taitou",:sihua => sHuaType)
    sFind = "#{nPanType},,#{sHouseStay},,#{sHouseAccept},,#{sHuaType},,"
    sFind2 = " " #"\n"
    foStr = "sToFind.index('#{sFind}')"
    nStartPos = eval(foStr)
    if (nStartPos == nil) then
      return sihua_taitou + ""
    end
    nStartPos += sFind.length
    foStr = "sToFind.index('#{sFind2}',#{nStartPos})"
    nStopPos = eval(foStr)
    if (nStopPos == nil) then
      return sihua_taitou + ""
    end
    foStr = "sToFind[#{nStartPos}..#{nStopPos-1}]"
    sOut = eval(foStr)
    return sihua_taitou + sOut
  end

end
