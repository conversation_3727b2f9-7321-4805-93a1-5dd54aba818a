# coding: UTF-8

require("Cfate.rb")
require("Star.rb")
require("PanWeb_StarFunction.rb")
require("PanWeb_ClassFunction.rb")
require("Pm.rb")

class PanWeb
	PW_STAR = "Star"
	PW_EIGHT = "Eight"
	
	def pw_getPan(pwType,nPanType,hUserInfo,hUserAskData,hUserType)
		return eval("pw_#{pwType}_getPan(nPanType,hUserInfo,hUserAskData,hUserType)")
	end
	
	def PanWeb.Modify2TwoWords(sIn)
		sOut = sIn
		if (sIn.length == 1) then
			sOut = sIn + Cfate.GetSpace(1)
		end
		return sOut
	end
	
	def PanWeb.GetSmallSpace(nCnt)
		sBuf = ""
		nCnt.times do |i|
			sBuf += " "
		end
		return sBuf
	end

	def PanWeb.pw_GetPan_table()
		sBuf = "<TABLE>"
	end

	def PanWeb.pw_GetPan_table_end()
		sBuf = "</TABLE>"
	end
	
	def PanWeb.pw_GetPan_table_tr()
		sBuf = "<TR>"
	end
	
	def PanWeb.pw_GetPan_table_tr_end()
		sBuf = "</TR>"
	end

	def PanWeb.pw_GetPan_table_td()
		sBuf = "<TD>"
	end

	def PanWeb.pw_GetPan_table_td_2(rs_rows,rs_cols)
		sBuf = "<TD ROWSPAN=#{rs_rows} COLSPAN=#{rs_cols}>"
	end
	
	def PanWeb.pw_GetPan_table_td_end()
		sBuf = "</TD>"
	end

	def PanWeb.pw_GetPan_br()
		sBuf = "<BR>"
	end
	
	# <CENTER>
	def PanWeb.pw_GetCenter()
		sBuf = "<CENTER>"
	end
	
	def PanWeb.pw_GetB()
		sBuf = "<B>"
	end
	
	def PanWeb.pw_GetB_end()
		sBuf = "</B>"
	end

	def PanWeb.pw_Get_i()
		sBuf = "<i>"
	end
	
	def PanWeb.pw_Get_i_end()
		sBuf = "</i>"
	end

	def PanWeb.pw_Get_Font_end()
		sBuf = "</FONT>"
	end

	def PanWeb.pw_Get_a_end()
	 sBuf = "</a>"
	end
		
end
