class Divination
  # main gua 區塊
  def main_gua(hParAll={})
    h = Hash.new

    # h["gua_name"] = Divination.Gua_64_name(@wai,@nei)
    # h["wai_sky_str"] = Divination.IB_Up_Sky_Str(@wai)
    # h["nei_sky_str"] = Divination.IB_Down_Sky_Str(@nei)

    # h["dong_yao_str"] = Divination.bian_Yao_Str(@dong_yao)

    # h["bian_gua_name"] = Divination.Gua_64_name(@bian_wai,@bian_nei)
    # h["bian_wai_sky_str"] = Divination.IB_Up_Sky_Str(@bian_wai)
    # h["bian_nei_sky_str"] = Divination.IB_Down_Sky_Str(@bian_nei)

    # 六爻
    h["liu_yao"] = Divination.liu_yao_str(@wai,@nei,@bian_wai,@bian_nei,hParAll[Liuyaogua::PAR_YAODISPLAYTYPE])

    # 本卦干支
    h["ben_gua_gan_zhi"] = Divination.ben_gua_sky_earths_str(@all_ylqgzws)

    # 變卦干支
    h["bian_gua_gan_zhi"] = Divination.bian_gua_sky_earths_str(@all_ylqgzws)

    # 六親
    h["ben_gua_liu_qin"] = Divination.Gua_64_liu_qins_str(@wai,@nei,nil)

    h["bian_gua_liu_qin"] = Divination.bian_gua_liu_qin_str(@wai,@nei,@bian_wai,@bian_nei)

    # 世應
    h["shi_yin"] = Divination.Gua_64_shiyin_strs(@wai,@nei)

    # 伏神
    h["fu_shen"] = Divination.Gua_64_fu_shens_str(@wai,@nei)

    # 六獸
    h["liu_shou"] = Divination.liu_shou_strs(Xdate.GetGanZhiSky(@nGanZhiDay))

    # 變動結果  化長生(十二長生)、化回頭剋、化回頭生、化退神、化進神、反吟、伏吟、化合
    # 化長生(十二長生)
    h["bian_dong_jie_guo"] = Divination.cr_bian_result(@all_ylqgzws)
    # h["cr_chang_sheng"] = Divination.Cr_find_chang_sheng_str(all_ylqgzws)
    # # 化合
    # h["cr_huahe"] = Divination.Cr_find_hua_he_str(all_ylqgzws)
    # # 化回頭剋、化回頭生
    # h["cr_back_head"] = Divination.Cr_find_backhead_str(all_ylqgzws)
    # # 化退神、化進神
    # h["cr_back_ahead_god"] = Divination.Cr_find_head_back_god_str(all_ylqgzws)
    # # 卦反吟
    # h["cr_gua_fan_yin"] = Divination.Cr_find_gua_fan_yin_str(@wai,@nei,@bian_wai,@bian_nei)
    # h["cr_yao_fan_yin"] = Divination.Cr_find_yao_fan_yin_str(all_ylqgzws)
    # # 伏吟
    # h["cr_fu_yin"] = Divination.Cr_find_fu_yin(@wai,@nei,@bian_wai,@bian_nei)
    return h
  end
  # inborn 先天八卦
  def Divination.Inborn_Bagua(nInborn_Bagua)
    nInborn_Bagua = Divination.get_second() if nInborn_Bagua == 0
    nIB = Pm.TestNo8(nInborn_Bagua)
    nIB = 8 if (nIB == 0)
    return nIB
  end
  def Divination.get_second()
    t = Xdate.GetNow()
    return t.sec
  end
  def Divination.Inborn_Bagua_Str(nIB)
    return Divination.liuyaogua_str("inborn_bagua_#{nIB}")
  end
  Inborn_Bagua_1 = 1      # 乾
  Inborn_Bagua_2 = 2    # 兌
  Inborn_Bagua_3 = 3    # 離
  Inborn_Bagua_4 = 4    # 震
  Inborn_Bagua_5 = 5    # 巽
  Inborn_Bagua_6 = 6    # 坎
  Inborn_Bagua_7 = 7    # 艮
  Inborn_Bagua_8 = 8      # 坤
  # postnatal 後天八卦

  # 卦五行
  # 乾宮金,兌宮金,離宮火,震宮木,巽宮木,坎宮水,艮宮土,坤宮土
  @@Gua_Five = [4,4,2,1,1,5,3,3]
  def Divination.Gua_Five(nGua)
    return @@Gua_Five[nGua - 1]
  end

  # 變卦
  def Divination.bian_for_wai_guas(a_change)
    a = Array.new
    a_change.each do |c|
      b = Divination.bian_for_wai_gua(c)
      a.push(b)
    end
    return a
  end
  def Divination.bian_for_wai_gua(change)
    if (change < 4) then
      return 0
    end
    return change - 3
  end
  def Divination.bian_for_nei_guas(a_change)
    a = Array.new
    a_change.each do |c|
      b = Divination.bian_for_nei_gua(c)
      a.push(b)
    end
    return a
  end
  def Divination.bian_for_nei_gua(change)
    if (change > 3) then
      return 0
    end
    return change
  end
  def Divination.bian_guas(gua,a_change)
    g = gua
    a_change.each do |c|
      g = Divination.bian_gua(g,c)
    end
    return g
  end
  def Divination.bian_gua(gua,change)
    if (change == 0) then
      return gua
    end
    gua_value = Divination.Gua_InYangValue(gua)
    bian_gua_value = gua_value ^ (1 << (change - 1))
    bian_gua = Divination.InYangValue_Gua(bian_gua_value)
    return bian_gua
  end
  def Divination.bian_full_guas(wai_gua,nei_gua,a_dong_yao)
    a = Divination.bian_wai_guas(wai_gua,a_dong_yao)
    b = Divination.bian_nei_guas(nei_gua,a_dong_yao)
    return [a,b]
  end
  def Divination.bian_full_gua(wai_gua,nei_gua,dong_yao)
    a = Divination.bian_wai_gua(wai_gua,dong_yao)
    b = Divination.bian_nei_gua(nei_gua,dong_yao)
    return [a,b]
  end
  def Divination.bian_wai_guas(wai_gua,a_dong_yao)
    a_change = Divination.bian_for_wai_guas(a_dong_yao)
    gua = Divination.bian_guas(wai_gua,a_change)
    return gua
  end
  def Divination.bian_wai_gua(wai_gua,dong_yao)
    change = Divination.bian_for_wai_gua(dong_yao)
    gua = Divination.bian_gua(wai_gua,change)
    return gua
  end
  def Divination.bian_nei_guas(nei_gua,a_dong_yao)
    a_change = Divination.bian_for_nei_guas(a_dong_yao)
    gua = Divination.bian_guas(nei_gua,a_change)
    return gua
  end
  def Divination.bian_nei_gua(nei_gua,dong_yao)
    change = Divination.bian_for_nei_gua(dong_yao)
    gua = Divination.bian_gua(nei_gua,change)
    return gua
  end
  # 六爻
  def Divination.bian_gua_inyang_values(wai_gua_iy_value,nei_gua_in_value,dong_yao)
    a = Divination.bian_wai_gua_inyang_value(wai_gua_iy_value,dong_yao)
    b = Divination.bian_nei_gua_inyang_value(nei_gua_in_value,dong_yao)
    return [a,b]
  end
  def Divination.bian_wai_gua_inyang_value(gua_iy_value,dong_yao)
    change = Divination.bian_for_wai_gua(dong_yao)
    return Divination.bian_gua_inyang_value(gua_iy_value,change)
  end
  def Divination.bian_nei_gua_inyang_value(gua_iy_value,dong_yao)
    change = Divination.bian_for_nei_gua(dong_yao)
    return Divination.bian_gua_inyang_value(gua_iy_value,change)
  end
  def Divination.bian_gua_inyang_value(gua_iy_value,change)
    if (change == 0) then
      return gua_iy_value
    end
    bian_gua_value = gua_iy_value ^ (1 << (change - 1))
    return bian_gua_value
  end
  def Divination.bian_Yaos(nYaoIn)
    if nYaoIn.class == Array then
      a_YaoIn = nYaoIn.clone
    else
      a_YaoIn = [nYaoIn]
    end
    a = Array.new
    return [0] if a_YaoIn.length == 0
    a_YaoIn.each do |y|
      a.push(Divination.bian_Yao(y)) if y != 0
    end
    return a
  end
  def Divination.bian_Yao(nYaoIn)
    nYaoIn = Divination.get_second() if nYaoIn == nil || nYaoIn == 0
    nYao = Pm.TestNo6(nYaoIn)
    nYao = 6 if (nYao == 0)
    return nYao
  end
  def Divination.bian_Yao_Str(nYaoIn)
    nYao = Divination.bian_Yao(nYaoIn)
    return Divination.liuyaogua_str("yao_#{nYao}")
  end
  Yao_1 = 1     # 初爻
  Yao_2 = 2   # 二爻
  Yao_3 = 3   # 三爻
  Yao_4 = 4   # 四爻
  Yao_5 = 5     # 五爻
  Yao_6 = 6     # 六爻

  def Divination.Yao_InYang_Str(nInYang)
    return Divination.liuyaogua_str("yao_inyang_#{nInYang}")
  end
  Yao_InYang_0 = 0    # 陰
  Yao_InYang_1 = 1      # 陽

  @@GuaIndex2InYangValue = [
                  7, # [1,1,1],# 乾
                  3, # [0,1,1],# 兌
                  5, # [1,0,1],# 離
                  1, # [0,0,1],# 震
                  6, # [1,1,0],# 巽
                  2, # [0,1,0],# 坎
                  4, # [1,0,0],# 艮
                  0  # [0,0,0] # 坤
              ]
  @@InYangValue2Gua = [
                  8, # [0,0,0] # 坤
                  4, # [0,0,1],# 震
                  6, # [0,1,0],# 坎
                  2, # [0,1,1],# 兌
                  7, # [1,0,0],# 艮
                  3, # [1,0,1],# 離
                  5, # [1,1,0],# 巽
                  1  # [1,1,1],# 乾
              ]
  def Divination.Gua_InYangValue(nBaGuaIn)
    nBaGua = Divination.Inborn_Bagua(nBaGuaIn)
    return @@GuaIndex2InYangValue[nBaGua - 1]
  end
  def Divination.Gua_InYangValue_Str(nGuaValue)
    a = Array.new(3)
      (0..2).each do |i|
        a[i] = Divination.Yao_InYang_Str((nGuaValue >> (2 - i)) & 1)
      end
    return a
  end
  def Divination.InYangValue_Gua(nGuaValue)
    return @@InYangValue2Gua[nGuaValue]
  end

  # five 五行
  def Divination.Five_San_Ke(five1,five2)
    if (five1 == five2) then
      return Five_Same
    end
    n = Divination.check_five_san(five1,five2)
    if (n != Five_San_Ke_None) then
      return n
    end
    n = Divination.check_five_ke(five1,five2)
    return n
  end
  @@Fives_San = [2,3,4,5,1]
  def Divination.check_five_san(five1,five2)
    five3 = Divination.get_five(@@Fives_San,five1)
    if (five3 == five2) then
      return Five_San
    end
    five3 = Divination.get_five(@@Fives_San,five2)
    if (five3 == five1) then
      return Five_Saned
    end
    return Five_San_Ke_None
  end
  @@Fives_Ke = [3,4,5,1,2]
  def Divination.check_five_ke(five1,five2)
    five3 = Divination.get_five(@@Fives_Ke,five1)
    if (five3 == five2) then
      return Five_Ke
    end
    five3 = Divination.get_five(@@Fives_Ke,five2)
    if (five3 == five1) then
      return Five_Keed
    end
    return Five_San_Ke_None
  end
  def Divination.get_five(fives,five)
    return fives[five - 1]
  end
  def Divination.Five(nFiveIn)
    nFive = Pm.TestNo5(nFiveIn)
    nFive = 5 if (nFive == 0)
    return nFive
  end
  def Divination.Five_Str(nFiveIn)
    nFive = Divination.Five(nFiveIn)
    return Divination.liuyaogua_str("five_#{nFive}")
  end
  Five_Wood = 1  # 木
  Five_Fire = 2  # 火
  Five_Dust = 3  # 土
  Five_Golden = 4  # 金
  Five_Water = 5  # 水

  Five_San_Ke_None = 0
  Five_San = 1
  Five_Ke = 2
  Five_Saned = 3
  Five_Keed = 4
  Five_Same = 5

  # sky 天干
  def Divination.Sky(nSkyIn)
    nSky = Pm.TestNo10(nSkyIn)
    nSky = 10 if (nSky == 0)
    return nSky
  end
  def Divination.Sky_Str(nSkyIn)
    nSky = Divination.Sky(nSkyIn)
    return Divination.liuyaogua_str("sky_#{nSky}")
  end
  Sky_1 = 1 # 甲
  Sky_2 = 2 # 乙
  Sky_3 = 3 # 丙
  Sky_4 = 4 # 丁
  Sky_5 = 5 # 戊
  Sky_6 = 6 # 己
  Sky_7 = 7 # 庚
  Sky_8 = 8 # 辛
  Sky_9 = 9 # 壬
  Sky_10 = 10 # 癸

    # earth 地支
  def Divination.Earth(nEarthIn)
    nEarth = Pm.TestNo12(nEarthIn)
    nEarth = 12 if (nEarth == 0)
    return nEarth
  end
  def Divination.Earth_Strs(a)
    aOut = Array.new(a.length)
    a.each_index do |i|
      aOut[i] = Divination.Earth_Str(a[i])
    end
    return aOut
    end
  def Divination.Earth_Str(nEarthIn)
    nEarth = Divination.Earth(nEarthIn)
    return Divination.liuyaogua_str("earth_#{nEarth}")
  end
  Earth_1 = 1 # 子
  Earth_2 = 2 # 丑
  Earth_3 = 3 # 寅
  Earth_4 = 4 # 卯
  Earth_5 = 5 # 辰
  Earth_6 = 6 # 巳
  Earth_7 = 7 # 午
  Earth_8 = 8 # 未
  Earth_9 = 9 # 申
  Earth_10 = 10 # 酉
  Earth_11 = 11 # 戌
  Earth_12 = 12 # 亥

  @@Earth_Five = [5,3,1,1,3,2,2,3,4,4,3,5]
  def Divination.Earth_Five(nEarth)
    return @@Earth_Five[nEarth - 1]
  end
  def Divination.Earth_Five_str(nEarth)
    nFive = Divination.Earth_Five(nEarth)
    return Divination.Five_Str(nFive)
  end

    # 起本卦之天干
  # 乾,       兌,離,震,巽,坎,艮,坤
  # 外壬、內甲,丁,己,庚,辛,戊,丙,外癸、內乙

  @@Up_Sky = [9,4,6,7,8,5,3,10]
  def Divination.IB_Up_Sky(nInborn_Bagua)
    nIB = Divination.Inborn_Bagua(nInborn_Bagua)
    return @@Up_Sky[nIB - 1]
  end
  def Divination.IB_Up_Sky_Str(nInborn_Bagua)
    nSky = Divination.IB_Up_Sky(nInborn_Bagua)
    return Divination.Sky_Str(nSky)
  end
  @@Down_Sky = [1,4,6,7,8,5,3,2]
  def Divination.IB_Down_Sky(nInborn_Bagua)
    nIB = Divination.Inborn_Bagua(nInborn_Bagua)
    return @@Down_Sky[nIB - 1]
  end
  def Divination.IB_Down_Sky_Str(nInborn_Bagua)
    nSky = Divination.IB_Down_Sky(nInborn_Bagua)
    return Divination.Sky_Str(nSky)
  end

  # 六親
  Liu_Qin_1 = 1 # 父母
  Liu_Qin_2 = 2 # 兄弟
  Liu_Qin_3 = 3 # 官鬼
  Liu_Qin_4 = 4 # 妻財
  Liu_Qin_5 = 5 # 子孫
  def Divination.par_liu_qins_str(par_five_levels,all_ylqgzws)
    par_liu_qins = Divination.par_liu_qins(par_five_levels,all_ylqgzws)
    aOut = Array.new(par_liu_qins.length)
    par_liu_qins.each_index do |i|
      aOut[i] = Divination.liu_qin_str(par_liu_qins[i])
    end
    return aOut
  end
  def Divination.liu_qins_str(a)
    aOut = Array.new(a.length)
    a.each_index do |i|
      aOut[i] = Divination.liu_qin_str(a[i])
    end
    return aOut
  end
  def Divination.liu_qin_str(n)
    return Divination.liuyaogua_str("liu_qin_#{n}")
  end
  @@Liu_Qin_Ke = [4,3,5,2,1]
  def Divination.liu_qins_ke(n)
    return @@Liu_Qin_Ke[n - 1]
  end
  def Divination.gua_yao_liu_qin(gua_liu_qins,yao)
    yao_index = Divination.Yao2Index(yao)
    return gua_liu_qins[yao_index]
  end
  # 六合,六沖
  HeChong_0 = 0 # 無
  HeChong_1 = 1 # 合
  HeChong_2 = 2 # 沖
  def Divination.HeChong_str(n)
    if (n == HeChong_1) then
      return Divination.liuyaogua_str("he_chong_1")
    elsif (n == HeChong_2) then
      return Divination.liuyaogua_str("he_chong_2")
    else
      return ""
    end
  end

  Te_Shu_Gua_7 = 7 # 7遊魂卦
  Te_Shu_Gua_8 = 8 # 8歸魂卦
  def Divination.Gua_64_special_gua_str(n)
    if (n == Te_Shu_Gua_7) then
      Divination.liuyaogua_str("special_gua_7")
    elsif (n == Te_Shu_Gua_8) then
      Divination.liuyaogua_str("special_gua_8")
    else
      return ""
    end
  end

  def Divination.Hsi_str(nYao)
    return [nYao,Divination.liuyaogua_str("hy_shi")]
  end
  def Divination.Yin_str(nYao)
    return [nYao,Divination.liuyaogua_str("hy_yin")]
  end
  # Inborn_Bagua_1 = 1      # 乾 天 InYangValue : 7
  # Inborn_Bagua_2 = 2    # 兌 澤 InYangValue : 3
  # Inborn_Bagua_3 = 3    # 離 火 InYangValue : 5
  # Inborn_Bagua_4 = 4    # 震 雷 InYangValue : 1
  # Inborn_Bagua_5 = 5    # 巽 風 InYangValue : 6
  # Inborn_Bagua_6 = 6    # 坎 水 InYangValue : 2
  # Inborn_Bagua_7 = 7        # 艮 山 InYangValue : 4
  # Inborn_Bagua_8 = 8      # 坤 地 InYangValue : 0
  # 乾為天,[天,天],卦爻陰陽值[7,7],[父母,兄弟,官鬼,父母,妻財,子孫],[戌,申,午,辰,寅,子],[六沖],世應[世6爻,應3],伏神[],[卦名（1為乾宮金)，Te_Shu_Gua_7(遊魂卦)]
  # 天風姤 伏神 二爻 妻財  甲寅木 [[2,4,[1,3,1]]]
  # 六十四卦
  @@Gua_64s = {
    "g_1_1" => ["gua_name_1_1",[Inborn_Bagua_1,1],[7,7],[Liu_Qin_1,2,3,1,4,5],[Earth_11,9,7,5,3,1],HeChong_2,[Yao_6,3],[],[1,1]],
    "g_1_5" => ["gua_name_1_5",[1,5],[7,6],[1,2,3,2,5,1],[11,9,7,10,12,2],HeChong_0,[1,4],[[2,4,[1,3,1]]],[1,2]],
    "g_1_7" => ["gua_name_1_7",[1,7],[7,4],[1,2,3,2,3,1],[11,9,7,9,7,5],HeChong_0,[2,5],[[1,5,[1,1,5]],[2,4,[1,3,1]]],[1,3]],
    "g_1_8" => ["gua_name_1_8",[1,8],[7,0],[1,2,3,4,3,1],[11,9,7,4,6,8],HeChong_1,[3,6],[[1,5,[1,1,5]]],[1,4]],
    "g_5_8" => ["gua_name_5_8",[5,8],[6,0],[4,3,1,4,3,1],[4,6,8,4,6,8],HeChong_0,[4,1],[[1,5,[1,1,5]],[5,2,[9,9,4]]],[1,5]],
    "g_7_8" => ["gua_name_7_8",[7,8],[4,0],[4,5,1,4,3,1],[3,1,11,4,6,8],HeChong_0,[5,2],[[5,2,[9,9,4]]],[1,6]],
    "g_3_8" => ["gua_name_3_8",[3,8],[5,0],[3,1,2,4,3,1],[6,8,10,4,6,8],HeChong_0,[4,1],[[1,5,[1,1,5]]],[1,7]],
    "g_3_1" => ["gua_name_3_1",[3,1],[5,7],[3,1,2,1,4,5],[6,8,10,5,3,1],HeChong_0,[3,6],[],[1,8]],

    "g_2_2" => ["gua_name_2_2",[2,2],[3,3],[1,2,5,1,4,3],[8,10,12,2,4,6],HeChong_2,[6,3],[],[2,1]],
    "g_2_6" => ["gua_name_2_6",[2,6],[3,2],[1,2,5,3,1,4],[8,10,12,7,5,3],HeChong_1,[1,4],[],[2,2]],
    "g_2_8" => ["gua_name_2_8",[2,8],[3,0],[1,2,5,4,3,1],[8,10,12,4,6,8],HeChong_0,[2,5],[],[2,3]],
    "g_2_7" => ["gua_name_2_7",[2,7],[3,4],[1,2,5,2,3,1],[8,10,12,9,7,5],HeChong_0,[3,6],[[2,4,[4,4,1]]],[2,4]],
    "g_6_7" => ["gua_name_6_7",[6,7],[2,4],[5,1,2,2,3,1],[1,11,9,9,7,5],HeChong_0,[4,1],[[2,4,[4,4,1]]],[2,5]],
    "g_8_7" => ["gua_name_8_7",[8,7],[0,4],[2,5,1,2,3,1],[10,12,2,9,7,5],HeChong_0,[5,2],[[2,4,[4,4,1]]],[2,6]],
    "g_4_7" => ["gua_name_4_7",[4,7],[1,4],[1,2,3,2,3,1],[11,9,7,9,7,5],HeChong_0,[4,1],[[2,4,[4,4,1]],[4,5,[4,12,5]]],[2,7]],
    "g_4_2" => ["gua_name_4_2",[4,2],[1,3],[1,2,3,1,4,3],[11,9,7,2,4,6],HeChong_0,[3,6],[[4,5,[4,12,5]]],[2,8]],

    "g_3_3" => ["gua_name_3_3",[3,3],[5,5],[2,5,4,3,5,1],[6,8,10,12,2,4],HeChong_2,[6,3],[],[3,1]],
    "g_3_7" => ["gua_name_3_7",[3,7],[5,4],[2,5,4,4,2,5],[6,8,10,9,7,5],HeChong_1,[1,4],[[1,1,[6,4,1]],[3,3,[6,12,5]]],[3,2]],
    "g_3_5" => ["gua_name_3_5",[3,5],[5,6],[2,5,4,4,3,5],[6,8,10,10,12,2],HeChong_0,[2,5],[[1,1,[6,4,1]]],[3,3]],
    "g_3_6" => ["gua_name_3_6",[3,6],[5,2],[2,5,4,2,5,1],[6,8,10,7,5,3],HeChong_0,[3,6],[[3,3,[6,12,5]]],[3,4]],
    "g_7_6" => ["gua_name_7_6",[7,6],[4,2],[1,3,5,2,5,1],[3,1,11,7,5,3],HeChong_0,[4,1],[[4,4,[6,10,4]]],[3,5]],
    "g_5_6" => ["gua_name_5_6",[5,6],[6,2],[1,2,5,2,5,1],[4,6,8,7,5,3],HeChong_0,[5,2],[[3,3,[6,12,5]],[4,4,[6,10,4]]],[3,6]],
    "g_1_6" => ["gua_name_1_6",[1,6],[7,2],[5,4,2,2,5,1],[11,9,7,7,5,3],HeChong_0,[4,1],[[3,3,[6,12,5]]],[3,7]],
    "g_1_3" => ["gua_name_1_3",[1,3],[7,5],[5,4,2,3,5,1],[11,9,7,12,2,4],HeChong_0,[3,6],[],[3,8]],

    "g_4_4" => ["gua_name_4_4",[4,4],[1,1],[4,3,5,4,2,1],[11,9,7,5,3,1],HeChong_2,[6,3],[],[4,1]],
    "g_4_8" => ["gua_name_4_8",[4,8],[1,0],[4,3,5,2,5,4],[11,9,7,4,6,8],HeChong_1,[1,4],[[1,1,[7,1,5]]],[4,2]],
    "g_4_6" => ["gua_name_4_6",[4,6],[1,2],[4,3,5,5,4,2],[11,9,7,7,5,3],HeChong_0,[2,5],[[1,1,[7,1,5]]],[4,3]],
    "g_4_5" => ["gua_name_4_5",[4,5],[1,6],[4,3,5,3,1,4],[11,9,7,10,12,2],HeChong_0,[3,6],[[2,2,[7,3,1]]],[4,4]],
    "g_8_5" => ["gua_name_8_5",[8,5],[0,6],[3,1,4,3,1,4],[10,12,2,10,12,2],HeChong_0,[4,1],[[2,2,[7,3,1]],[4,5,[7,7,2]]],[4,5]],
    "g_6_5" => ["gua_name_6_5",[6,5],[2,6],[1,4,3,3,1,4],[1,11,9,10,12,2],HeChong_0,[5,2],[[2,2,[7,3,1]],[4,5,[7,7,2]]],[4,6]],
    "g_2_5" => ["gua_name_2_5",[2,5],[3,6],[4,3,1,3,1,4],[8,10,12,10,12,2],HeChong_0,[4,1],[[2,2,[7,3,1]],[4,5,[7,7,2]]],[4,7]],
    "g_2_4" => ["gua_name_2_4",[2,4],[3,1],[4,3,1,4,2,1],[8,10,12,5,3,1],HeChong_0,[3,6],[[4,5,[7,7,2]]],[4,8]],

    "g_5_5" => ["gua_name_5_5",[5,5],[6,6],[2,5,4,3,1,4],[4,6,8,10,12,2],HeChong_2,[6,3],[],[5,1]],
    "g_5_1" => ["gua_name_5_1",[5,1],[6,7],[2,5,4,4,2,1],[4,6,8,5,3,1],HeChong_0,[1,4],[[3,3,[8,10,4]]],[5,2]],
    "g_5_3" => ["gua_name_5_3",[5,3],[6,5],[2,5,4,1,4,2],[4,6,8,12,2,4],HeChong_0,[2,5],[[3,3,[8,10,4]]],[5,3]],
    "g_5_4" => ["gua_name_5_4",[5,4],[6,1],[2,5,4,4,2,1],[4,6,8,5,3,1],HeChong_0,[3,6],[[3,3,[8,10,4]]],[5,4]],
    "g_1_4" => ["gua_name_1_4",[1,4],[7,1],[4,3,5,4,2,1],[11,9,7,5,3,1],HeChong_2,[4,1],[],[5,5]],
    "g_3_4" => ["gua_name_3_4",[3,4],[5,1],[5,4,3,4,2,1],[6,8,10,5,3,1],HeChong_0,[5,2],[],[5,6]],
    "g_7_4" => ["gua_name_7_4",[7,4],[4,1],[2,1,4,4,2,1],[3,1,11,5,3,1],HeChong_0,[4,1],[[3,3,[8,10,4]],[5,5,[8,6,2]]],[5,7]],
    "g_7_5" => ["gua_name_7_5",[7,5],[4,6],[2,1,4,3,1,4],[3,1,11,10,12,2],HeChong_0,[3,6],[[5,5,[8,6,2]]],[5,8]],

    "g_6_6" => ["gua_name_6_6",[6,6],[2,2],[2,3,1,4,3,5],[1,11,9,7,5,3],HeChong_2,[6,3],[],[6,1]],
    "g_6_2" => ["gua_name_6_2",[6,2],[2,3],[2,3,1,3,5,4],[1,11,9,2,4,6],HeChong_1,[1,4],[],[6,2]],
    "g_6_4" => ["gua_name_6_4",[6,4],[2,1],[2,3,1,3,5,2],[1,11,9,5,3,1],HeChong_0,[2,5],[[3,4,[5,7,2]]],[6,3]],
    "g_6_3" => ["gua_name_6_3",[6,3],[2,5],[2,3,1,2,3,5],[1,11,9,12,2,4],HeChong_0,[3,6],[[3,4,[5,7,2]]],[6,4]],
    "g_2_3" => ["gua_name_2_3",[2,3],[3,5],[3,1,2,2,3,5],[8,10,12,12,2,4],HeChong_0,[4,1],[[3,4,[5,7,2]]],[6,5]],
    "g_4_3" => ["gua_name_4_3",[4,3],[1,5],[3,1,4,2,3,5],[11,9,7,12,2,4],HeChong_0,[5,2],[],[6,6]],
    "g_8_3" => ["gua_name_8_3",[8,3],[0,5],[1,2,3,2,3,5],[10,12,2,12,2,4],HeChong_0,[4,1],[[3,4,[5,7,2]]],[6,7]],
    "g_8_6" => ["gua_name_8_6",[8,6],[0,2],[1,2,3,4,3,5],[10,12,2,7,5,3],HeChong_0,[3,6],[],[6,8]],

    "g_7_7" => ["gua_name_7_7",[7,7],[4,4],[3,4,2,5,1,2],[3,1,11,9,7,5],HeChong_2,[6,3],[],[7,1]],
    "g_7_3" => ["gua_name_7_3",[7,3],[4,5],[3,4,2,4,2,3],[3,1,11,12,2,4],HeChong_1,[1,4],[[2,1,[3,7,2]],[3,5,[3,9,4]]],[7,2]],
    "g_7_1" => ["gua_name_7_1",[7,1],[4,7],[3,4,2,2,3,4],[3,1,11,5,3,1],HeChong_0,[2,5],[[2,1,[3,7,2]],[3,5,[3,9,4]]],[7,3]],
    "g_7_2" => ["gua_name_7_2",[7,2],[4,3],[3,4,2,2,3,1],[3,1,11,2,4,6],HeChong_0,[3,6],[[3,5,[3,9,4]]],[7,4]],
    "g_3_2" => ["gua_name_3_2",[3,2],[5,3],[1,2,5,2,3,1],[6,8,10,2,4,6],HeChong_0,[4,1],[[5,4,[3,1,5]]],[7,5]],
    "g_1_2" => ["gua_name_1_2",[1,2],[7,3],[2,5,1,2,3,1],[11,9,7,2,4,6],HeChong_0,[5,2],[[5,4,[3,1,5]]],[7,6]],
    "g_5_2" => ["gua_name_5_2",[5,2],[6,3],[3,1,2,2,3,1],[4,6,8,2,4,6],HeChong_0,[4,1],[[4,5,[3,9,4]],[5,4,[3,1,5]]],[7,7]],
    "g_5_7" => ["gua_name_5_7",[5,7],[6,4],[3,1,2,5,1,2],[4,6,8,9,7,5],HeChong_0,[3,6],[[5,4,[3,1,5]]],[7,8]],

    "g_8_8" => ["gua_name_8_8",[8,8],[0,0],[5,4,2,3,1,2],[10,12,2,4,6,8],HeChong_2,[6,3],[],[8,1]],
    "g_8_4" => ["gua_name_8_4",[8,4],[0,1],[5,4,2,2,3,4],[10,12,2,5,3,1],HeChong_1,[1,4],[[2,1,[2,6,2]]],[8,2]],
    "g_8_2" => ["gua_name_8_2",[8,2],[0,3],[5,4,2,2,3,1],[10,12,2,2,4,6],HeChong_0,[2,5],[],[8,3]],
    "g_8_1" => ["gua_name_8_1",[8,1],[0,7],[5,4,2,2,3,4],[10,12,2,5,3,1],HeChong_1,[3,6],[[2,1,[2,6,2]]],[8,4]],
    "g_4_1" => ["gua_name_4_1",[4,1],[1,7],[2,5,1,2,3,4],[11,9,7,5,3,1],HeChong_2,[4,1],[],[8,5]],
    "g_2_1" => ["gua_name_2_1",[2,1],[3,7],[2,5,4,2,3,4],[8,10,12,5,3,1],HeChong_0,[5,2],[[2,1,[2,6,2]]],[8,6]],
    "g_6_1" => ["gua_name_6_1",[6,1],[2,7],[4,2,5,2,3,4],[1,11,9,5,3,1],HeChong_0,[4,1],[[2,1,[2,6,2]]],[8,7]],
    "g_6_8" => ["gua_name_6_8",[6,8],[2,0],[4,2,5,3,1,2],[1,11,9,4,6,8],HeChong_0,[3,6],[],[8,8]]
  }
  def Divination.Yao2Index(yao)
    n = Pm.TestNo6(yao - 1)
    return 5 - n
  end
  def Divination.Index2Yao(index)
    n = 6 - Pm.TestNo6(index)
    return n
  end
  def Divination.Gua_64(wai,nei)
    key = "g_#{wai}_#{nei}"
    return @@Gua_64s[key].clone
  end
  def Divination.Gua_64_name(wai,nei)
    a = Divination.Gua_64(wai,nei)
    return Divination.liuyaogua_str(a[0])
  end
  def Divination.liuyaogua_str(key)
    return Pm.GetStr("liuyaogua.#{key}")
  end
  def Divination.Gua_64_gua(wai,nei)
    a = Divination.Gua_64(wai,nei)
    return a[1].clone
  end
  def Divination.Gua_64_inyang_value(wai,nei)
    a = Divination.Gua_64(wai,nei)
    return a[2].clone
  end
  def Divination.Gua_64_inyang_yaos(wai,nei)
    a = Divination.Gua_64_inyang_value(wai,nei)
    aOut = Divination.inyang_value_2_yaos(a[0])
    aOut2 = Divination.inyang_value_2_yaos(a[1])
    return aOut + aOut2
  end
  def Divination.inyang_value_2_yaos(inyang_value)
    aOut = Array.new(3)
    (0..2).each do |i|
      current_yao = (inyang_value >> (2 - i)) & 1
      aOut[i] = current_yao
    end
    return aOut
  end
  def Divination.liu_yao_bian_check(wai,nei,bian_wai,bian_nei)
    ben_inyang_values = Divination.Gua_64_inyang_value(wai,nei)
    bian_inyang_values = Divination.Gua_64_inyang_value(bian_wai,bian_nei)

    aOut = Divination.three_yao_bian_check(ben_inyang_values[0],bian_inyang_values[0])
    aOut2 = Divination.three_yao_bian_check(ben_inyang_values[1],bian_inyang_values[1])
    return aOut + aOut2
  end
  def Divination.three_yao_bian_check(current_inyang_value,bian_inyang_value)
    aOut = Array.new(3)
    (0..2).each do |i|
      current_yao = (current_inyang_value >> (2 - i)) & 1
      dong_yao = (bian_inyang_value >> (2 - i)) & 1
      aOut[i] = Divination.yao_bian_check(current_yao,dong_yao)
    end
    return aOut
  end
  Bian_Yao_In = 2
  Bian_Yao_Yang = 3
  def Divination.yao_bian_check(current_yao,dong_yao)
    if (current_yao != dong_yao) then
      if (current_yao == Yao_InYang_1) then
        return Bian_Yao_Yang
      else
        return Bian_Yao_In
      end
    else
      return current_yao  # 1 or 0
    end
  end
  def Divination.liu_yao_str(wai,nei,bian_wai,bian_nei,yao_display_type=Liuyaogua::PAR_YAODISPLAYTYPE_DEFAULT)
    aOut = Array.new(6)
    a = Divination.liu_yao_bian_check(wai,nei,bian_wai,bian_nei)
    a.each_index do |i|
      aOut[i] = Divination.yao_str(a[i],yao_display_type)
    end
    return aOut
  end
  def Divination.liuyaogua_pan_str_key(s)
    return "liuyaogua_pan.#{s}"
  end
  def Divination.liuyaogua_pan_str(s)
    k = Divination.liuyaogua_pan_str_key(s)
    return Pm.t(k)
  end
  def Divination.yao_str(yao,yao_display_type=Liuyaogua::PAR_YAODISPLAYTYPE_DEFAULT)
    a = Divination.get_yao_display(yao_display_type)

    if (yao >= Bian_Yao_In) then
      if (yao == Bian_Yao_Yang) then
        return a[3]
        # return Divination.liuyaogua_pan_str("yao_sign_9")
        # return "◯"
        # return Divination.liuyaogua_str("yao_yang_change")
      else
        return a[2]
        # return Divination.liuyaogua_pan_str("yao_sign_6")
        # return "✕"
        # return Divination.liuyaogua_str("yao_in_change")
      end
    else
      if (yao == Yao_InYang_1) then
        return a[1]
        # return Divination.liuyaogua_pan_str("yao_sign_7")
        # return "⎮"
        # return Divination.liuyaogua_str("yao_yang")
      else
        return a[0]
        # return Divination.liuyaogua_pan_str("yao_sign_8")
        # return "⎮⎮"
        # return Divination.liuyaogua_str("yao_in")
      end
    end
  end
  def Divination.Gua_64_liu_qins(wai,nei)
    a = Divination.Gua_64(wai,nei)
    return a[3].clone
  end
  def Divination.Gua_64_liu_qins_str(wai,nei,liu_yao_change=nil)
    a = Divination.Gua_64_liu_qins(wai,nei)
    aOut = Divination.liu_qins_str(a)
    if (liu_yao_change != nil) then
      liu_yao_change.each_index do |i|
        if (liu_yao_change[i] < Bian_Yao_In) then
          aOut[i] = ""
        end
      end
    end
    return aOut
  end
  # 2、以本卦地支以五行生剋分六親。
  # 同我者=》兄弟(簡稱「兄」)
  # 我生者=》子孫(簡稱「子」)
  # 我剋者=》妻財(簡稱「財」)
  # 剋我者=》官鬼(簡稱「官」)
  # 生我者=》父母(簡稱「父」)
  def Divination.bian_gua_liu_qins(wai,nei,bian_wai,bian_nei)
    liu_yao_change = Divination.liu_yao_bian_check(wai,nei,bian_wai,bian_nei)
    bian_earths = Divination.Gua_64_earth(bian_wai,bian_nei)
    ben_gua_five = Divination.Gua_64_info_five(wai,nei)
    aOut = Array.new(6,nil)
    liu_yao_change.each_index do |i|
      if (liu_yao_change[i] >= Bian_Yao_In) then
        yao_five = Divination.Earth_Five(bian_earths[i])
        aOut[i] = Divination.find_liu_qin(ben_gua_five,yao_five)
      end
    end

    return aOut
  end
  def Divination.bian_gua_liu_qin_str(wai,nei,bian_wai,bian_nei)
    aOut = Array.new(6,"")
    liu_qins = Divination.bian_gua_liu_qins(wai,nei,bian_wai,bian_nei)
    liu_qins.each_index do |i|
      liu_qin = liu_qins[i]
      if (liu_qin != nil) then
        aOut[i] = Divination.liu_qin_str(liu_qin)
      end
    end
    return aOut
  end
  def Divination.find_liu_qin(ben_gua_earth_five,yao_earth_five)
    san_ke = Divination.Five_SanKe(ben_gua_earth_five,yao_earth_five)
    if (san_ke == Five_SK_Pin) then
      return Liu_Qin_2  # 兄弟
    elsif (san_ke == Five_SK_BeSan)
      return Liu_Qin_5 # 子孫
    elsif (san_ke == Five_SK_BeKe)
      return Liu_Qin_4 # 妻財
    elsif (san_ke == Five_SK_Ke)
      return Liu_Qin_3 # 官鬼
    elsif (san_ke == Five_SK_San)
      return Liu_Qin_1 # 父母
    end
  end

  def Divination.Gua_64_earth(wai,nei)
    a = Divination.Gua_64(wai,nei)
    return a[4].clone
  end
  def Divination.Gua_64_he_chong(wai,nei)
    a = Divination.Gua_64(wai,nei)
    return a[5]
  end
  def Divination.Gua_64_he_chong_str(wai,nei)
    n = Divination.Gua_64_he_chong(wai,nei)
    return Divination.HeChong_str(n)
  end
  def Divination.Gua_64_shiyin(wai,nei)
    a = Divination.Gua_64(wai,nei)
    return a[6].clone
  end
  def Divination.Gua_64_shiyin_str(wai,nei)
    a = Divination.Gua_64_shiyin(wai,nei)
    aOut = Array.new
    aOut.push(Divination.Hsi_str(a[0]))
    aOut.push(Divination.Yin_str(a[1]))
    return aOut
  end
  def Divination.Gua_64_shiyin_strs(wai,nei)
    a = Divination.Gua_64_shiyin(wai,nei)

    aOut = [[],[],[],[],[],[]]
    a2 = Divination.Hsi_str(a[0])
    aOut = Divination.insert_yao_str(aOut,a2)

    a2 = Divination.Yin_str(a[1])
    aOut = Divination.insert_yao_str(aOut,a2)

    return aOut
  end
  def Divination.Gua_64_fu_shens(wai,nei)
    a = Divination.Gua_64(wai,nei)
    return a[7].clone
  end
  def Divination.Gua_64_fu_shens_str(wai,nei)
    a = Divination.Gua_64_fu_shens(wai,nei)
    aOut = [[],[],[],[],[],[]]
    a.each_index do |i|
      a2 = Divination.Gua_64_fu_shen_str(a[i])
      aOut = Divination.insert_yao_str2(aOut,a2)
    end
    return aOut
  end
  def Divination.insert_yao_str2(a,a2)
    index = Divination.Yao2Index(a2[0])
    if (a2[1].length > 0) then
      a[index] = a2[1]
    end
    return a
  end
  def Divination.Gua_64_fu_shen_str(a)
    # 伏神 二爻 妻財  甲寅木 [[2,4,[1,3,1]]]
    if (a == nil) then
      return [0,""]
    end
    liu_qin = Divination.fu_shen_liu_qin(a)
    tian_gan = Divination.fu_shen_tian_gan(a)
    di_zhi = Divination.fu_shen_di_zhi(a)
    wu_xing = Divination.fu_shen_wu_xing(a)
    # s = "#{Divination.liu_qin_str(liu_qin)} #{Divination.Sky_Str(tian_gan)}#{Divination.Earth_Str(di_zhi)}#{Divination.Five_Str(wu_xing)}"
    s = [Divination.liu_qin_str(liu_qin),Divination.Sky_Str(tian_gan),Divination.Earth_Str(di_zhi),Divination.Five_Str(wu_xing)]
    return [a[0],s]
  end
  def Divination.fu_shen_yao(a)
    return a[0]
  end
  def Divination.fu_shen_liu_qin(a)
    return a[1]
  end
  def Divination.fu_shen_gan_zi_wuxing(a)
    return a[2]
  end
  def Divination.fu_shen_tian_gan(a)
    return a[2][0]
  end
  def Divination.fu_shen_di_zhi(a)
    return a[2][1]
  end
  def Divination.fu_shen_wu_xing(a)
    return a[2][2]
  end
  def Divination.Gua_64_info(wai,nei)
    a = Divination.Gua_64(wai,nei)
    return a[8].clone
  end
  def Divination.Gua_64_info_gua_group_str(wai,nei)
    a = Divination.Gua_64_info(wai,nei)
    return Divination.Inborn_Bagua_Str(a[0])
  end
  def Divination.Gua_64_info_special_no(wai,nei)
    a = Divination.Gua_64_info(wai,nei)
    return a[1]
  end
  def Divination.Gua_64_info_special_str(wai,nei)
    a = Divination.Gua_64_info(wai,nei)
    return Divination.Gua_64_special_gua_str(a[1])
  end
  def Divination.Gua_64_info_five(wai,nei)
    a = Divination.Gua_64_info(wai,nei)
    return Divination.Gua_Five(a[0])
  end
  def Divination.Gua_64_info_five_str(wai,nei)
    five = Divination.Gua_64_info_five(wai,nei)
    return Divination.Five_Str(five)
  end
  def Divination.ben_gua_sky_earths(wai,nei)
    earths = Divination.Gua_64_earth(wai,nei)
    wai_sky = Divination.IB_Up_Sky(wai)
    nei_sky = Divination.IB_Down_Sky(nei)

    aOut = Array.new(6)
    earths.each_index do |i|
      if (i < 3) then
        aOut[i] = Divination.sky_earth_five(wai_sky,earths[i])
      else
        aOut[i] = Divination.sky_earth_five(nei_sky,earths[i])
      end
    end

    return aOut
  end
  def Divination.ben_gua_sky_earths_str(all_ylqgzws)
    ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    return Divination.gua_sky_earths_str(ylqgzws)
  end
  def Divination.sky_earth_five(sky,earth)
    a = Array.new
    a.push(sky)
    a.push(earth)
    a.push(Divination.Earth_Five(earth))
    return a
  end
  def Divination.gua_sky_earths_str(ylqgzws)
    h = {"yao_6" => "","yao_5" => "","yao_4" => "","yao_3" => "","yao_2" => "","yao_1" => ""}
    ylqgzws.each_index do |i|
      a = ylqgzws[i]
      yao = Divination.fu_shen_yao(a)
      yao_index = Divination.Yao2Index(yao)
      sky = Divination.fu_shen_tian_gan(a)
      earth = Divination.fu_shen_di_zhi(a)
      five = Divination.fu_shen_wu_xing(a)
      h["yao_#{yao}"] = Divination.sky_earth_five_str(sky,earth,five)
    end
    return h
  end
  def Divination.bian_gua_sky_earths(wai,nei,bian_wai,bian_nei)
    liu_yao_change = Divination.liu_yao_bian_check(wai,nei,bian_wai,bian_nei)
    earths = Divination.Gua_64_earth(bian_wai,bian_nei)
    wai_sky = Divination.IB_Up_Sky(bian_wai)
    nei_sky = Divination.IB_Down_Sky(bian_nei)

    aOut = Array.new(6)
    earths.each_index do |i|
      if (liu_yao_change[i] >= Bian_Yao_In) then
        if (i < 3) then
          aOut[i] = Divination.sky_earth_five(wai_sky,earths[i])
        else
          aOut[i] = Divination.sky_earth_five(nei_sky,earths[i])
        end
      else
        aOut[i] = [0,0,0]
      end
    end
    return aOut
  end
  def Divination.bian_gua_sky_earths_str(all_ylqgzws)
    ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    return Divination.gua_sky_earths_str(ylqgzws)
  end
  def Divination.sky_earth_five_str(sky,earth,five)
    a = Array.new
    a.push(sky > 0 ? Divination.Sky_Str(sky) : "")
    a.push(earth > 0 ? Divination.Earth_Str(earth) : "")
    a.push(five > 0 ? Divination.Five_Str(five) : "")
    return a
  end

  # 六獸
  Liu_Shou_1 = 1 # 青龍
  Liu_Shou_2 = 2 # 朱雀
  Liu_Shou_3 = 3 # 勾陳
  Liu_Shou_4 = 4 # 騰蛇
  Liu_Shou_5 = 5 # 白虎
  Liu_Shou_6 = 6 # 玄武
  def Divination.liu_shou_str(n)
    return Divination.liuyaogua_str("liu_shou_#{n}")
    end
    # 從六爻開始
    @@Liu_Shous = [
                    [6,5,4,3,2,1],  # 甲日
                    [6,5,4,3,2,1],  # 乙日
                    [1,6,5,4,3,2],  # 丙日
                    [1,6,5,4,3,2],  # 丁日
                    [2,1,6,5,4,3],  # 戊日
                    [3,2,1,6,5,4],  # 己日
                    [4,3,2,1,6,5],  # 庚日
                    [4,3,2,1,6,5],  # 辛日
                    [5,4,3,2,1,6],  # 壬日
                    [5,4,3,2,1,6]   # 癸日
                    ]
  def Divination.liu_shou(liu_shous,yao)
    i = Divination.Yao2Index(yao)
    return liu_shous[i]
  end
  def Divination.liu_shous(nDaySky)
    return @@Liu_Shous[nDaySky - 1]
  end
  def Divination.liu_shou_strs(nDaySky)
    a = Divination.liu_shous(nDaySky)
    aOut = Array.new(a.length)
    a.each_index do |i|
      aOut[i] = Divination.liu_shou_str(a[i])
    end
    return aOut
  end

                       # 五行 木 火 水 金
                       #           土
  Chang_Sheng_1  =  1   # 長生 亥 寅￼申 巳
  Chang_Sheng_2  =  2   # 沐浴 子 卯￼酉￼午
  Chang_Sheng_3  =  3   # 冠帶 丑 辰 戌 未
  Chang_Sheng_4  =  4   # 臨官 寅 巳 亥 申
  Chang_Sheng_5  =  5   # 帝旺 卯 午 子 酉
  Chang_Sheng_6  =  6   # 衰   辰 未 丑 戌
  Chang_Sheng_7  =  7   # 病   巳 申 寅 亥
  Chang_Sheng_8  =  8   # 死   午 酉 卯 子
  Chang_Sheng_9  =  9   # 墓   未 戌 辰 丑
  Chang_Sheng_10 =  10  # 絕   申 亥 巳 寅
  Chang_Sheng_11 =  11  # 胎   酉 子 午 卯
  Chang_Sheng_12 =  12  # 養   戌 丑 未 辰
  @@Chang_Sheng_table = {
    "f1" => [2,3,4,5,6,7,8,9,10,11,12,1],
    "f2" => [11,12,1,2,3,4,5,6,7,8,9,10],
    "f3" => [5,6,7,8,9,10,11,12,1,2,3,4],
    "f4" => [8,9,10,11,12,1,2,3,4,5,6,7],
    "f5" => [5,6,7,8,9,10,11,12,1,2,3,4]
  }
  def Divination.chang_sheng(five,earth)
    a = @@Chang_Sheng_table["f#{five}"]
    return a[earth - 1]
  end
  def Divination.chang_sheng_strs(a)
    aOut = Array.new(a.length)
    a.each_index do |i|
      aOut[i] = Divination.chang_sheng_str(a[i])
    end
    return aOut
  end
  def Divination.chang_sheng_str(n)
    return Divination.liuyaogua_str("chang_sheng_#{n}")
  end
  def Divination.cr_hua_str()
    return Divination.liuyaogua_str("cr_hua")
  end
  # 變動結果  化長生(十二長生)、化回頭剋、化回頭生、化退神、化進神、反吟、伏吟、化合
  # 4.1 十二長生(本例化衰):以本卦變爻在初爻地支為「子」五行為「水」,
  # 變爻為「丑土」,以「子」之五行「水」查十二長生表-「水」欄位查
  # 到「丑」對應左側十二長生「衰」,顯示為「化衰」。
  def Divination.cr_bian_result(all_ylqgzws)
    aOut = [[],[],[],[],[],[]]
    # 化長生(十二長生)
    a2 = Divination.Cr_find_chang_sheng_str(all_ylqgzws)
    aOut = Divination.insert_yao_str(aOut,a2)
    # 化合
    a2 = Divination.Cr_find_hua_he_str(all_ylqgzws)
    aOut = Divination.insert_yao_str(aOut,a2)
    # 化回頭剋、化回頭生
    a2 = Divination.Cr_find_backhead_str(all_ylqgzws)
    aOut = Divination.insert_yao_str(aOut,a2)
    # 化退神、化進神
    a2 = Divination.Cr_find_head_back_god_str(all_ylqgzws)
    aOut = Divination.insert_yao_str(aOut,a2)
    # 卦反吟
    a2 = Divination.Cr_find_yao_fan_yin_str(all_ylqgzws)
    aOut = Divination.insert_yao_str(aOut,a2)

    return aOut
  end
  def Divination.insert_yao_str(a,a2)
    index = Divination.Yao2Index(a2[0])
    if (a2[1].length > 0) then
      a[index].push(a2[1])
    end
    return a
  end
  def Divination.Cr_find_chang_sheng(all_ylqgzws)
    ben_ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_ylqgzws[i]
      bian_yao = Divination.fu_shen_yao(bian_ylqgzw)
      ben_ylqgzw = Divination.par_find_ylqgzw_by_yao(ben_ylqgzws,bian_yao)
      ben_wu_xing = Divination.fu_shen_wu_xing(ben_ylqgzw)  # 本卦五行
      bian_di_zhi = Divination.fu_shen_di_zhi(bian_ylqgzw)   # 變卦地支
      return bian_yao ,Divination.chang_sheng(ben_wu_xing,bian_di_zhi)
    end
    return 0,0
  end
  def Divination.Cr_find_chang_sheng_str(all_ylqgzws)
    dong_yao,chang_sheng = Divination.Cr_find_chang_sheng(all_ylqgzws)
    return Divination.Cr_chang_sheng_str(dong_yao,chang_sheng)
  end
  def Divination.Cr_chang_sheng_str(dong_yao,chang_sheng)
    return [dong_yao,""] if chang_sheng == 0
    sh = Divination.cr_hua_str()
    s = Divination.chang_sheng_str(chang_sheng)
    return [dong_yao,sh + s]
  end

  # 化合
  # 六合
  # 地支  六合  六沖  刑 害
  # 寅   亥 申 巳 巳
  # 卯   戌 酉 子 辰
  # 辰   酉 戌 辰 卯
  # 巳   申 亥 申 寅
  # 午   未 子 午 丑
  # 未   午 丑 丑 子
  # 申   巳 寅 寅 亥
  # 酉   辰 卯 酉 戌
  # 戌   卯 辰 未 酉
  # 亥   寅 巳 亥 申
  # 子   丑 午 卯 未
  # 丑   子 未 戌 午

  # 六合
  @@Di_Zhi_Liu_Hes = [2,1,12,11,10,9,8,7,6,5,4,3]
  def Divination.di_zhi_liu_he(nEarth)
    return @@Di_Zhi_Liu_Hes[nEarth - 1]
  end
  def Divination.di_zhi_liu_he_check(nEarth,nEarth2)
    return Divination.di_zhi_liu_he(nEarth) == nEarth2
  end

  # 六沖
  @@Di_Zhi_Liu_Chongs = [7,8,9,10,11,12,1,2,3,4,5,6]
  def Divination.di_zhi_liu_chong(nEarth)
    return @@Di_Zhi_Liu_Chongs[nEarth - 1]
  end

  # 刑
  @@Di_Zhi_Xings = [4,11,6,1,5,9,7,2,3,10,8,12]
  def Divination.di_zhi_xing(nEarth)
    return @@Di_Zhi_Xings[nEarth - 1]
  end

  # 害
  @@Di_Zhi_Hais = [8,7,6,5,4,3,2,1,12,11,10,9]
  def Divination.di_zhi_hai(nEarth)
    return @@Di_Zhi_Hais[nEarth - 1]
  end

    # 4.2 本例「化合」,以本卦「子水」變卦「丑土」,子丑為六合,顯示化
    # 合。(三合亦同)(化回頭剋)稱為合剋。例外:辰化辰=》化刑、
    # 化伏吟、化墓。
  def Divination.Cr_find_hua_he(all_ylqgzws)
    ben_ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_ylqgzws[i]
      bian_yao = Divination.fu_shen_yao(bian_ylqgzw)
      ben_ylqgzw = Divination.par_find_ylqgzw_by_yao(ben_ylqgzws,bian_yao)
      ben_di_zhi = Divination.fu_shen_wu_xing(ben_ylqgzw)  # 本卦地支
      bian_di_zhi = Divination.fu_shen_di_zhi(bian_ylqgzw)   # 變卦地支
      return bian_yao ,Divination.di_zhi_liu_he_check(ben_di_zhi,bian_di_zhi)
    end
    return 0,0
  end
  def Divination.Cr_find_hua_he_str(all_ylqgzws)
    dong_yao,is_liu_he = Divination.Cr_find_hua_he(all_ylqgzws)
    return Divination.Cr_hua_he_str(dong_yao,is_liu_he)
  end
  def Divination.Cr_hua_he_str(dong_yao,is_liu_he)
    if (is_liu_he) then
      sh = Divination.cr_hua_str()
      s = Divination.cr_he_str()
      return [dong_yao,sh + s]
    else
      return [dong_yao,""]
    end
  end
  def Divination.cr_he_str()
    return Divination.liuyaogua_str("cr_he")
  end

  # 4.3 化回頭剋、化回頭生:以本卦與變卦之五行比較。以本卦「子水」 變卦「丑土」,土剋水,由變卦五行剋本卦,
  # 則顯示「化回頭剋」, 若變卦之五行生本卦,「化回頭生」。
  # 變卦生本卦(五行生):稱回頭生;變卦剋本卦(五行剋):稱回頭生剋。
  Five_SK_San = 1
  Five_SK_Ke = 2
  Five_SK_Pin = 3
  Five_SK_BeSan = 4
  Five_SK_BeKe = 5
  def Divination.Five_SanKe(cur_five,chg_five)
    if (Divination.Five_San?(cur_five,chg_five)) then
      return Five_SK_San
    end
    if (Divination.Five_Ke?(cur_five,chg_five)) then
      return Five_SK_Ke
    end
    if (Divination.Five_Pin?(cur_five,chg_five)) then
      return Five_SK_Pin
    end
    if (Divination.Five_San?(chg_five,cur_five)) then
      return Five_SK_BeSan
    end
    if (Divination.Five_Ke?(chg_five,cur_five)) then
      return Five_SK_BeKe
    end
  end
  def Divination.Five_San?(cur_five,chg_five)
    return cur_five == @@Fives_San[chg_five - 1]
  end
  def Divination.Five_Ke?(cur_five,chg_five)
    return cur_five == @@Fives_Ke[chg_five - 1]
  end
  def Divination.Five_Pin?(cur_five,chg_five)
    return cur_five == chg_five
  end
  def Divination.cr_back_head()
  return Divination.liuyaogua_str("cr_back_head")
  end
  def Divination.cr_sanke_str(n)
  return Divination.liuyaogua_str("cr_five_sk_#{n}")
  end
  def Divination.Cr_find_backhead(all_ylqgzws)
    ben_ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_ylqgzws[i]
      bian_yao = Divination.fu_shen_yao(bian_ylqgzw)
      ben_ylqgzw = Divination.par_find_ylqgzw_by_yao(ben_ylqgzws,bian_yao)
      ben_wu_xing = Divination.fu_shen_wu_xing(ben_ylqgzw)  # 本卦五行
      bian_wu_xing = Divination.fu_shen_wu_xing(bian_ylqgzw)   # 變卦五行
      return bian_yao ,Divination.Five_SanKe(ben_wu_xing,bian_wu_xing)
    end
    return 0,0
  end
  def Divination.Cr_find_backhead_str(all_ylqgzws)
    dong_yao,sanke = Divination.Cr_find_backhead(all_ylqgzws)
    return Divination.Cr_backhead_str(dong_yao,sanke)
  end
  def Divination.Cr_backhead_str(dong_yao,sanke)
    sh = Divination.cr_hua_str()
    sb = Divination.cr_back_head()
    s = Divination.cr_sanke_str(sanke)
    if (sanke == Five_SK_San) then
    return [dong_yao,sh + sb + s]
    elsif (sanke == Five_SK_Ke) then
    return [dong_yao,sh + sb + s]
    else
      return [dong_yao,""]
    end
  end

  # 4.4 化退神、化進神:本卦之地支有對應到變爻之地支,即是化退神 或化進神。資料顯示為「化進神」或「化退神」
  # 4.4 化退神、化進神
  Five_HB_No = 0
  Five_HB_GoBack = 1
  Five_HB_GoAHead = 2
  def Divination.Cr_HB_Ahead_Back(cur_earth,chg_earth)
    if (Divination.HB_GoBack?(cur_earth,chg_earth)) then
      return Five_SK_San
    end
    if (Divination.HB_GoAhead?(cur_earth,chg_earth)) then
      return Five_SK_Ke
    end
    return 0
  end
  # 化退神
  @@HB_GoBack = [12,11,0,3,2,0,6,5,0,9,8,0]
  def Divination.HB_GoBack?(cur_earth,chg_earth)
    return  @@HB_GoBack[cur_earth - 1] == chg_earth
  end
  # 化進神
  @@HB_GoAhead = [0,5,4,0,8,7,0,11,10,0,2,1]
  def Divination.HB_GoAhead?(cur_earth,chg_earth)
    return  @@HB_GoAhead[cur_earth - 1] == chg_earth
  end
  def Divination.Cr_find_head_back_god(all_ylqgzws)
    ben_ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_ylqgzws[i]
      bian_yao = Divination.fu_shen_yao(bian_ylqgzw)
      ben_ylqgzw = Divination.par_find_ylqgzw_by_yao(ben_ylqgzws,bian_yao)
      ben_di_zhi = Divination.fu_shen_di_zhi(ben_ylqgzw)  # 本卦地支
      bian_di_zhi = Divination.fu_shen_di_zhi(bian_ylqgzw)   # 變卦地支
      return bian_yao ,Divination.Cr_HB_Ahead_Back(ben_di_zhi,bian_di_zhi)
    end
    return 0,0
  end
  def Divination.Cr_find_head_back_god_str(all_ylqgzws)
    dong_yao,n = Divination.Cr_find_head_back_god(all_ylqgzws)
    return Divination.Cr_head_back_god_str(dong_yao,n)
  end
  def Divination.Cr_head_back_god_str(dong_yao,n)
    sh = Divination.cr_hua_str()
    s = Divination.head_back_god_str(n)
    if (n == Five_HB_No) then
      return [dong_yao,""]
    else
      return [dong_yao,sh + s]
    end
  end
  def Divination.head_back_god_str(n)
    return Divination.liuyaogua_str("five_hb_#{n}")
  end

  # 4.5 反吟:分為卦反吟及爻反吟
  # 卦首反吟:
  # 乾為天=>巽為風 , 巽為風=>乾為天
  # 天風姤=>風天小畜 , 風天小畜=>天風姤
  # 坎為水=>離為火, 離為火=>坎為水
  # 水火既濟 => 火水未濟 ,火水未濟=>水火既濟
  # 艮為山 => 坤為地 , 坤為地=>艮為山
  # 震為雷=>兌為澤 , 兌為澤=>震為雷
  # 雷澤歸妹=>澤雷隨 , 澤雷隨=>雷澤歸妹
  @@Gua_Anti_Yin = {
    "g_1_1" => [5,5],
    "g_5_5" => [1,1],
    "g_1_5" => [5,1],
    "g_5_1" => [1,5],
    "g_6_6" => [3,3],
    "g_3_3" => [6,6],
    "g_6_3" => [3,6],
    "g_3_6" => [6,3],
    "g_7_7" => [8,8],
    "g_8_8" => [7,7],
    "g_4_4" => [2,2],
    "g_2_2" => [4,4],
    "g_4_2" => [2,4],
    "g_2_4" => [4,2]
  }
  def Divination.Cr_find_gua_fan_yin(wai,nei,bian_wai,bian_nei)
    a = @@Gua_Anti_Yin["g_#{wai}_#{nei}"]
    return a == [bian_wai,bian_nei]
  end
  def Divination.Cr_find_gua_fan_yin_str(wai,nei,bian_wai,bian_nei)
    if (Divination.Cr_find_gua_fan_yin(wai,nei,bian_wai,bian_nei)) then
      return Divination.gua_head_fan_yin_str()
    end
    return ""
  end
  def Divination.gua_head_fan_yin_str()
    return Divination.liuyaogua_str("gua_head_fan_yin")
  end
  # 爻反吟:以本卦與變爻之地支相衝,即為反吟。
  # @@Yao_Anti_Yin = [7,8,9,10,11,12,0,0,0,0,0,0]
  @@Yao_Anti_Yin = [0,8,9,10,11,12,1,0,0,0,0,0]
  def Divination.Cr_find_yao_fan_yin(all_ylqgzws)
    ben_ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_ylqgzws[i]
      bian_yao = Divination.fu_shen_yao(bian_ylqgzw)
      ben_ylqgzw = Divination.par_find_ylqgzw_by_yao(ben_ylqgzws,bian_yao)
      ben_di_zhi = Divination.fu_shen_di_zhi(ben_ylqgzw)  # 本卦地支
      bian_di_zhi = Divination.fu_shen_di_zhi(bian_ylqgzw)   # 變卦地支
      return bian_yao ,Divination.find_yao_fan_yin(ben_di_zhi,bian_di_zhi)
    end
    return 0,0
  end
  def Divination.find_yao_fan_yin(cur_earth,chg_earth)
    n = @@Yao_Anti_Yin[cur_earth - 1]
    return n == chg_earth
  end
  def Divination.Cr_find_yao_fan_yin_str(all_ylqgzws)
    dong_yao,bAntiYin = Divination.Cr_find_yao_fan_yin(all_ylqgzws)
    if (bAntiYin) then
      return [dong_yao,Divination.yao_fan_yin_str()]
    end
    return [dong_yao,""]
    end
    def Divination.yao_fan_yin_str()
    return Divination.liuyaogua_str("yao_fan_yin")
  end

  # 4.6 伏吟:有下例情形為內外卦伏吟 內卦伏吟:
  # 內卦伏吟:
  # 火天大有=>火雷噬嗑  , 火雷噬嗑=>火天大有
  # 水雷屯=>水天需  , 水天需=>水雷屯
  # 山天大畜=>山雷頤  , 山雷頤山=>天大畜
  # 澤雷隨=>澤天夬  , 澤天夬=>澤雷隨
  # 風天小畜=>風雷益  , 風雷益=>風天小畜
  # 地天泰=>地雷復  , 地雷復=>地天泰
  @@InGua_Fu_Yin = {
    "g_3_1" => [3,4],
    "g_3_4" => [3,1],
    "g_6_4" => [6,1],
    "g_6_1" => [6,4],
    "g_7_1" => [7,4],
    "g_7_4" => [7,1],
    "g_2_4" => [2,1],
    "g_2_1" => [2,4],
    "g_5_1" => [5,4],
    "g_5_4" => [5,1],
    "g_8_1" => [8,4],
    "g_8_4" => [8,1]
  }
  def Divination.Cr_find_ingua_fu_yin(wai,nei,bian_wai,bian_nei)
    a = @@InGua_Fu_Yin["g_#{wai}_#{nei}"]
    return a == [bian_wai,bian_nei]
  end
  def Divination.Cr_find_ingua_fu_yin_str(wai,nei,bian_wai,bian_nei)
    if (Divination.Cr_find_ingua_fu_yin(wai,nei,bian_wai,bian_nei)) then
      return Divination.ingua_fu_yin_str()
    end
    return ""
  end
  def Divination.ingua_fu_yin_str()
    return Divination.liuyaogua_str("ingua_fu_yin")
  end

  # 外卦伏吟:
  # 天風姤=>雷風恒 ,雷風恒=>天風姤
  # 天山遯=>雷山小過 ,雷山小過=>天山遯
  # 天地否=>雷地豫 ,雷地豫=>天地否
  # 雷火豐=>天火同人 ,天火同人=>雷火豐
  # 天澤履=>雷澤歸妹 ,雷澤歸妹=>天澤履
  # 雷水解=>天水訟 ,天水訟=>雷水解
   @@OutGua_Fu_Yin = {
    "g_1_5" => [4,5],
    "g_4_5" => [1,5],
    "g_1_7" => [4,7],
    "g_4_7" => [1,7],
    "g_1_8" => [4,8],
    "g_4_8" => [1,8],
    "g_4_3" => [1,3],
    "g_1_3" => [4,3],
    "g_1_2" => [4,2],
    "g_4_2" => [1,2],
    "g_4_6" => [1,6],
    "g_1_6" => [4,6]
  }
  def Divination.Cr_find_outgua_fu_yin(wai,nei,bian_wai,bian_nei)
    a = @@OutGua_Fu_Yin["g_#{wai}_#{nei}"]
    return a == [bian_wai,bian_nei]
  end
  def Divination.Cr_find_outgua_fu_yin_str(wai,nei,bian_wai,bian_nei)
    if (Divination.Cr_find_outgua_fu_yin(wai,nei,bian_wai,bian_nei)) then
      return Divination.outgua_fu_yin_str()
    end
    return ""
  end
  def Divination.outgua_fu_yin_str()
    return Divination.liuyaogua_str("outgua_fu_yin")
  end

  # 內外卦伏吟:
  # 乾為天=>震為雷 , 震為雷=>乾為天
  # 天雷旡妄=>雷天大壯 , 雷天大壯=>天雷旡妄
  @@InOutGua_Fu_Yin = {
    "g_1_1" => [4,4],
    "g_4_4" => [1,1],
    "g_1_4" => [4,1],
    "g_4_1" => [1,4]
  }
  def Divination.Cr_find_inoutgua_fu_yin(wai,nei,bian_wai,bian_nei)
    a = @@InOutGua_Fu_Yin["g_#{wai}_#{nei}"]
    return a == [bian_wai,bian_nei]
    end
  def Divination.Cr_find_inoutgua_fu_yin_str(wai,nei,bian_wai,bian_nei)
    if (Divination.Cr_find_inoutgua_fu_yin(wai,nei,bian_wai,bian_nei)) then
      return Divination.inoutgua_fu_yin_str()
    end
    return ""
  end
  def Divination.inoutgua_fu_yin_str()
    return Divination.liuyaogua_str("inoutgua_fu_yin")
  end

  def Divination.Cr_find_fu_yin(wai,nei,bian_wai,bian_nei)
    if (Divination.Cr_find_ingua_fu_yin(wai,nei,bian_wai,bian_nei)) then
      return Divination.ingua_fu_yin_str()
    elsif (Divination.Cr_find_outgua_fu_yin(wai,nei,bian_wai,bian_nei)) then
      return Divination.outgua_fu_yin_str()
    elsif (Divination.Cr_find_inoutgua_fu_yin(wai,nei,bian_wai,bian_nei)) then
      return Divination.inoutgua_fu_yin_str()
    else
      return ""
    end
  end

  def Divination.yao_2_yao_guayao(yao_idx,yao_value)
    return 0,0 if yao_value == 0 || yao_value == Divination::Gua_In # 陰爻
    return 1,0 if yao_value == 1  || yao_value == Divination::Gua_Yang # 陽爻
    return 0,yao_idx if yao_value == Divination::Gua_Lao_In # 老陰
    return 1,yao_idx if yao_value == Divination::Gua_Lao_Yang # 老陽
    return 0,0
  end
  def Divination.yao_2_gua(a_yao)
    gua_yao = Array.new(6)
    dong_yao = Array.new(6)
    a_yao.each_with_index do |yao,i|
      gua_yao[i],dong_yao[i] = Divination.yao_2_yao_guayao(i + 1,yao)
    end
    wai,nei = Divination.gua_yao_2_wai_nei_gua(gua_yao)
    return wai,nei,dong_yao
  end
  def Divination.gua_yao_2_wai_nei_gua(a_gua_yao)
    nei = Divination.gua_yao_2_gua(a_gua_yao[0,3])
    wai = Divination.gua_yao_2_gua(a_gua_yao[3,3])
    return wai,nei
  end
  def Divination.gua_yao_2_gua(a_gua_yao)
    n = 0
    a_gua_yao.each_with_index do |yao,i|
      n += yao * (2**i)
    end
    return Divination.InYangValue_Gua(n)
  end
  def Divination.get_yao_display(yao_display_type=Liuyaogua::PAR_YAODISPLAYTYPE_DEFAULT)
    ydt = yao_display_type
    ydt = Liuyaogua::PAR_YAODISPLAYTYPE_DEFAULT if ydt == nil
    ydt = Liuyaogua::PAR_YAODISPLAYTYPE_DEFAULT if ![Liuyaogua::PAR_YAODISPLAYTYPE_NORMAL,Liuyaogua::PAR_YAODISPLAYTYPE_SEP].include?(ydt)
    yao_d = ["yao_display_type_normal","yao_display_sep"]
    return Divination.liuyaogua_pan_str(yao_d[ydt]).split(" ")
  end
  Gua_In = 8
  Gua_Yang = 7
  Gua_Lao_In = 6
  Gua_Lao_Yang = 9
  # 6為老陰，7為少陽，8為少陰，9為老陽
  def Divination.gua_yao_for_select(yao_display_type=Liuyaogua::PAR_YAODISPLAYTYPE_DEFAULT)
    hOut = Hash.new
    a = [Divination::Gua_In,Divination::Gua_Yang,Divination::Gua_Lao_In,Divination::Gua_Lao_Yang]
    a2 = Divination.get_yao_display(yao_display_type)
    a.each_with_index do |y,i|
      s = Divination.liuyaogua_pan_str("yao_name_#{y}")
      s += "(#{a2[i]})"
      hOut[s] = y
    end
    return hOut
  end


end
