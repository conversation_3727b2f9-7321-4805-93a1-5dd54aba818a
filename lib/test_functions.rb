require("Xdate_Function.rb")
require("Star_Function.rb")
require("PanWeb.rb")
require("SkyEarthFive_Function.rb")
#require("sanitize.rb")
require 'sanitize.rb'

class LibTest
	
	def LibTest.TestXdate()
		Pm.SetLang(Pm::Lang_Big5)
		sOut = Xdate.GetTotalWDaysFrom190011(2011,1,1).to_s + Xdate.EastYearToChina(1912) + Xdate.WestYearToChina(2011) + Earth.GetName(1)
		sOut += "\n"
		# sOut += Star.GetStarName(Star::A_STAR_1)
	end
	
	def LibTest.TestStar()
		h = Hash.new
		h[Star::WYear] = 2000
		h[Star::WMonth] = 3
		h[Star::WDate] = 13
		h[Star::WHour] = 1
		h[Star::Sex] = true
		a = PanWeb.new
		sWebPan = a.pw_getPan(PanWeb::PW_STAR,Star::PAN_NORMAL,h)
		
		return sWebPan
#		return Sanitize.clean(sWeb<PERSON><PERSON>, Sanitize::Config::RELAXED)
	end
end
