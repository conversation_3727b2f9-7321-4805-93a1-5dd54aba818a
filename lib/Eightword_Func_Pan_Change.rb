require("SkyEarthFive_Function.rb")

class Eightword
  # 三方會局
  def cp_mp_by4Unit()
    by4Unit = Array.new(4,0)

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = cp_mp_getByEarthIndex(byWhatIdx)
          by4Unit[byWhatIdx] = m
    end
    return by4Unit
  end
  def cp_mp_byEarthMask_3Dir()
    byEarthMask = Array.new(12,0)

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = cp_mp_getByEarthIndex(byWhatIdx)
      byEarthMask[m] = byEarthMask[m] + 1
    end
    return byEarthMask
  end
  def cp_mp_byEarthComb1_3Dir()
    byEarthComb1 = Array.new(12,0)

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = cp_mp_getByEarthIndex(byWhatIdx)
          if (byWhatIdx == Eightword::ByYearIdx) then
              byEarthComb1[m] = byEarthComb1[m] + 1
          else
              if (byWhatIdx < Eightword::ByHourIdx) then
                  byEarthComb1[m] = byEarthComb1[m] + 1
                end
          end
    end
    return byEarthComb1
  end
  def cp_mp_byEarthComb2_3Dir()
    byEarthComb2 = Array.new(12,0)

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = cp_mp_getByEarthIndex(byWhatIdx)
          if (byWhatIdx != Eightword::ByYearIdx) then
              byEarthComb2[m] = byEarthComb2[m] + 1
          end
    end
    return byEarthComb2
  end

  # 確認三方會局的屬性
  def cp_mp_check3Dir(bWinType)
    nEarthType = Array.new(5,-1)

    by4Unit = cp_mp_by4Unit()
    byEarthMask = cp_mp_byEarthMask_3Dir()
    byEarthComb1 = cp_mp_byEarthComb1_3Dir()
    byEarthComb2 = cp_mp_byEarthComb2_3Dir()
    bSuccess,k,m,nEarthType = cp_mp_c3d_five(byEarthMask,bWinType,byEarthComb1,byEarthComb2)

    return bSuccess,k,m
  end

    def cp_mp_getOEarthFive(arrayIndex=nil)
    return cp_mp_getGodStyleInfo(Eightword::NOEarthFive_5,"cp_mp_NOEarthFive_5()",arrayIndex)
    end
    def cp_mp_setOEarthFive(byValue,arrayIndex=nil)
      cp_mp_setGodStyleInfo(Eightword::NOEarthFive_5,byValue,arrayIndex)
    end
  def cp_mp_NOEarthFive_5()
  end

    def cp_mp_getFiveSum(arrayIndex=nil)
    return cp_mp_getGodStyleInfo(Eightword::NFiveSum_5,"cp_mp_NFiveSum_5()",arrayIndex)
    end
    def cp_mp_setFiveSum(byValue,arrayIndex=nil)
      cp_mp_setGodStyleInfo(Eightword::NFiveSum_5,byValue,arrayIndex)
    end
  def cp_mp_NFiveSum_5()
    cp_mp_ChangePan()
  end

  def cp_mp_ChangePan()
    nFiveSum = Array.new(5,0)
    nMFiveSum = Array.new(5,0)
    nOFive = Array.new(5,0)
    nSkyLevel = Array.new(3,0)
    nOEarthFive = Array.new(5,0)
    nEarthType = Array.new(5,-1)

    byEarthIndex1 = cp_mp_getByEarthIndex().clone
    byEarthFive1 = cp_mp_getByEarthFive().clone

    bySkyIndex1 = cp_cp_getBySkyIndex() # pmCombineSky
    bySkyFive1 = cp_cp_getBySkyFive()

    bySkyIndex = cp_mp_getBySkyIndex()
    byEarthIndex = cp_mp_getByEarthIndex()

    bWinType = false
    by4Unit = cp_mp_by4Unit()
    byEarthMask = cp_mp_byEarthMask_3Dir()
    byEarthComb1 = cp_mp_byEarthComb1_3Dir()
    byEarthComb2 = cp_mp_byEarthComb2_3Dir()

    bSuccess,k,m,nEarthType = cp_mp_c3d_five(byEarthMask,nEarthType)
    if (bSuccess) then
      # 三方會局
      nOFive[k] += 12
          nFiveSum[k] += 12

          n = byEarthIndex[Eightword::ByDayIdx]
      if (n == m) then
        byEarthFive1[Eightword::ByDayIdx] = k
        byEarthIndex1[Eightword::ByDayIdx] = @@ByChangeFive[k][0]
      else
        [Eightword::ByHourIdx,Eightword::ByMonthIdx,Eightword::ByYearIdx].each do |byWhatIdx|
          n = byEarthIndex[byWhatIdx]
          if (n == m) then
            byEarthFive1[byWhatIdx] = k
            byEarthIndex1[byWhatIdx] = @@ByChangeFive[k][0]
          end
        end
      end

      nSkyLevel[0] = 15
      nSkyLevel[1] = 18
      nSkyLevel[2] = 21
    else
      # 三合局
      bSuccess,k,l,m,nEarthType = cp_mp_c3h_five(byEarthMask,nEarthType)

          if (bSuccess) then
        nOFive[k] += 9
              nFiveSum[k] += 9

        n = byEarthIndex[Eightword::ByDayIdx]
        j = 0
        if (n == l || n == m) then
          byEarthFive1[Eightword::ByDayIdx] = k
          byEarthIndex1[Eightword::ByDayIdx] = @@ByChangeFive[k][0]
          j += 1
          if (n == l) then
            l = -1
          else
            m = -1
          end
        end

        bStop = false
        [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |byWhatIdx|
          if (!bStop) then
            n = byEarthIndex[byWhatIdx]
            if (n == m || n == l) then
              byEarthFive1[byWhatIdx] = k
              byEarthIndex1[byWhatIdx] = @@ByChangeFive[k][0]
              j += 1
              if (j == 2) then
                bStop = true
              end
            end
          end
        end
      end
      nSkyLevel[0] = 12
      nSkyLevel[1] = 15
      nSkyLevel[2] = 18
    end

    if (bSuccess) then
      nFiveSum = cp_mp_c3dh_With_SkyLevel(nFiveSum,k,nSkyLevel,bySkyFive1,byEarthFive1)
    end

    if (!bSuccess) then
      bSuccess,nFiveSum,nOrgFive,fSkyArray,nEarthType1,byEarthIndex1,byEarthFive1 = cp_Check2Pair(nFiveSum,nOFive,byEarthIndex1,byEarthFive1,nil)
          if (!bWinType) then
              m_bTwoPair = bSuccess
            end
            nEarthType = nEarthType1
      end
    if (!bSuccess) then
      # 半方會局
      nFiveSum,nOFive,by4Unit,byEarthMask = cp_mp_fs_checkHalfDir(nFiveSum,nOFive,by4Unit,byEarthMask,byEarthFive1,nEarthType)
      # 地支六合
      nFiveSum,nOFive,by4Unit,byEarthMask,byEarthIndex1,byEarthFive1,nEarthType = cp_mp_fs_checkEarth6Her(nFiveSum,nOFive,by4Unit,byEarthMask,byEarthIndex1,byEarthFive1,nEarthType)

      # 扣分
          # 三刑
      bSuccess,nMFiveSum,nEarthType = cp_mp_fs_checkEarth3Hsin(byEarthMask,nMFiveSum,nEarthType)

          if (!bSuccess) then
          # 六沖
        by4Unit,byEarthMask,nMFiveSum,nEarthType = cp_mp_fs_checkEarth6Chuon(by4Unit,byEarthMask,nMFiveSum,nEarthType)

        # 相刑 自刑
        by4Unit,nMFiveSum,nEarthType = cp_mp_fs_checkEarthSelfHsin(by4Unit,nMFiveSum,nEarthType)

        # 六害
        by4Unit,nMFiveSum,nEarthType = cp_mp_fs_checkEarth6Hai(by4Unit,nMFiveSum,nEarthType)
      end
    end

    # 藏干加分
    nFiveSum = cp_mp_fs_checkChungSky(nFiveSum,bySkyFive1)

    # 天干地支分數加分
    nFiveSum,nOFive = cp_mp_fs_checkSkyEarthScore(nFiveSum,nOFive,bySkyFive1,byEarthFive1)

    (0..4).each do |i|
        if (!bWinType) then
              nFiveSum[i] = nFiveSum[i] - nMFiveSum[i];
              nOEarthFive[i] = nOFive[i] - nMFiveSum[i]
          else
              nFiveSum[i] = nFiveSum[i] - nMFiveSum[i]
          end
      end


      # return nFiveSum,nOEarthFive,byEarthIndex1,byEarthFive1
      cp_mp_setFiveSum(nFiveSum)
      cp_mp_setOEarthFive(nOEarthFive)
      # cp_mp_setnEarthType(nEarthType)
      cp_cp_setByEarthIndex(byEarthIndex1)
      cp_cp_setByEarthFive(byEarthFive1)
      # cp_cp_setEarthType(nEarthType1)
  end
  def cp_cp_bChangePan()
    bySkyIndex = cp_mp_getBySkyIndex()
    byEarthIndex = cp_mp_getByEarthIndex()
    bySkyIndex1 = cp_cp_getBySkyIndex()
    byEarthIndex1 = cp_cp_getByEarthIndex()

        bChangePan = false
    (0..3).each do |i|
      if (!bStop) then
          if (bySkyIndex1[i] != bySkyIndex[i]) then
            bChangePan = true
            bStop = true
          elsif (byEarthIndex1[i] != byEarthIndex[i]) then
            bChangePan = true
            bStop = true
          end
      end
      end
      cp_cp_setbChangePan(bChangePan)
  end
  def cp_cp_getbChangePan()
    cp_cp_getScoreInfo(Eightword::BChangePan,"cp_cp_bChangePan()")
  end
  def cp_cp_setbChangePan(byValue)
    cp_cp_setScoreInfo(Eightword::BChangePan,byValue)
  end

  def cp_Check2Pair(nOrgFiveSum,nOrgFive,byEarthIndex1,byEarthFive1,fOrgSkyArray=nil)
    byEarthIndex1_Org = byEarthIndex1.clone
    byEarthFive1_Org = byEarthFive1.clone

    bFlowPan = (@m_nPanType != Cfate::PAN_NORMAL)

    nFiveSum = nOrgFiveSum.clone
    nOFive = Array.new(5,0)
    nEarthType1_Org = Array.new(5,-1)
    nEarthType1 = Array.new(5,-1)

    # Special Two Pair
    by4Unit = cp_mp_by4Unit()

    if (fOrgSkyArray == nil) then
      fSkyArray = Array.new(10,0.0)
    else
      fSkyArray = fOrgSkyArray.clone
    end

    # 半方會局
    nFiveSum,nOFive,by4Unit,nEarthType1,fSkyArray = cp_c2p_HalfDir(nFiveSum,nOFive,by4Unit,nEarthType1,fSkyArray,bFlowPan,byEarthFive1)

    # 地支六合
    nFiveSum,nOFive,by4Unit,nEarthType1,fSkyArray,byEarthIndex1,byEarthFive1 = cp_c2p_Earth6Her(nFiveSum,nOFive,by4Unit,nEarthType1,fSkyArray,bFlowPan,byEarthIndex1,byEarthFive1)

    bTwoPair = true
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      if (bTwoPair) then
        if (by4Unit[byWhatIdx] != 0xFF) then
          bTwoPair = false
        end
      end
    end

    if (bTwoPair) then
          if (!bFlowPan) then
            (0..4).each do |i|
              nOrgFive[i] += nOFive[i]
            end
          end

      if (fOrgSkyArray == nil) then
        return bTwoPair,nFiveSum,nOrgFive,nil,nEarthType1,byEarthIndex1,byEarthFive1
      else
        return bTwoPair,nFiveSum,nOrgFive,fSkyArray,nEarthType1,byEarthIndex1,byEarthFive1
      end
    else
      return bTwoPair,nOrgFiveSum,nOrgFive,fOrgSkyArray,nEarthType1_Org,byEarthIndex1_Org,byEarthFive1_Org
    end
  end

  # 半方會局
  def cp_c2p_HalfDir(nFiveSum,nOFive,by4Unit,nEarthType,fSkyArray,bFlowPan,byEarthFive1)
    [Eightword::ByMonthIdx,Eightword::ByDayIdx].each do |byWhatIdx|
      m = by4Unit[byWhatIdx - 1]
      n = by4Unit[byWhatIdx]
      if (cp_mp_chd_isWood?(m,n)) then # 木局
        m = 1
              fSkyArray[0] += 16
              fSkyArray[1] += 16
      elsif (cp_mp_chd_isFire?(m,n)) then # 火局
        m = 2
              fSkyArray[2] += 16
              fSkyArray[3] += 16
      elsif (cp_mp_chd_isGolden?(m,n)) then # 金局
        m = 4
              fSkyArray[6] += 16
              fSkyArray[7] += 16
      elsif (cp_mp_chd_isWater?(m,n)) then # 水局
        m = 0
              fSkyArray[8] += 16
              fSkyArray[9] += 16
      else
        m = -1
      end
      if (m >= 0) then
        nEarthType[byWhatIdx] = COMBINE_3
        nEarthType[byWhatIdx - 1] = COMBINE_3
        by4Unit[byWhatIdx] = 0xFF
        by4Unit[byWhatIdx - 1] = 0xFF
        nFiveSum[m] += 8
              nOFive[m] += 8
              if (!bFlowPan) then
            l = 0
            [0,1,3].each do |j|
              a = byEarthFive1[j]
              if (a == m) then
                l += 1
              end
            end
            if (l == 1) then
              nFiveSum[m] += 11
            elsif (l == 2) then
              nFiveSum[m] += 14
            elsif (l == 3)
              nFiveSum[m] += 17
          end
              end
      end
    end

    return nFiveSum,nOFive,by4Unit,nEarthType,fSkyArray
  end

  # 地支六合
  def cp_c2p_Earth6Her(nFiveSum,nOFive,by4Unit,nEarthType,fSkyArray,bFlowPan,byEarthIndex1,byEarthFive1)
    [Eightword::ByMonthIdx,Eightword::ByDayIdx].each do |byWhatIdx|
      m = by4Unit[byWhatIdx - 1]
      n = by4Unit[byWhatIdx]
      if (m + n == 13 || m + n == 1) then
        m = [m,n].min
              if (m == 0) then # 化為土
          m = l = 3
                  fSkyArray[5] += 32
        elsif (m == 2) then # 化為木
          m = l = 1
                  fSkyArray[0] += 64.0 / 3
                  fSkyArray[8] += 32.0 / 2
        elsif (m == 3) then # 化為火
          m = l = 2
                  fSkyArray[3] += 64.0 / 3
                  fSkyArray[1] += 32.0 / 2
        elsif (m == 4) then # 化為金
          m = l = 4
                  fSkyArray[7] += 64.0 / 3
                  fSkyArray[4] += 32.0 / 2
        elsif (m == 5) then # 化為水
          m = l = 0
                  fSkyArray[8] += 64.0 / 3
                  fSkyArray[6] += 32.0 / 2
        elsif (m == 6) then # 化為火
          m = l = 2
                  fSkyArray[3] += 32
        else
          m = -1
        end
        if (m >= 0) then
          nEarthType[byWhatIdx] = COMBINE_5
          nEarthType[byWhatIdx - 1] = COMBINE_5
          by4Unit[byWhatIdx] = 0xFF
          by4Unit[byWhatIdx - 1] = 0xFF
          nFiveSum[l] += 4
                  nOFive[l] += 4
                  byEarthFive1[byWhatIdx] = l
                  byEarthFive1[byWhatIdx - 1] = l
                  nEarthFive1 = cp_mp_getByEarthFive(byWhatIdx)
                  nEarthFive2 = cp_mp_getByEarthFive(byWhatIdx - 1)

          if (nEarthFive1 != l && nEarthFive2 != l) then
            nEarthIndex = cp_mp_getByEarthIndex(byWhatIdx)
            if (l == 2) then
              if (nEarthIndex == 3) then
                byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][0]
                byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][1]
              else
                byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][1]
                byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][0]
              end
            else
              if (nEarthIndex == 5) then
                byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][0]
                byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][1]
              else
                byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][1]
                byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][0]
              end
            end
          elsif (nEarthFive2 == l) then
            nEarthIndex = cp_mp_getByEarthIndex(byWhatIdx - 1)
            if (nEarthIndex == @@ByChangeFive[l][0]) then
              byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][1]
            else
              byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][0]
            end
          elsif (nEarthFive1 == l) then
            nEarthIndex = cp_mp_getByEarthIndex(byWhatIdx)
            if (nEarthIndex == @@ByChangeFive[l][0]) then
              byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][1]
            else
              byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][0]
            end
          end
                  if (!bFlowPan) then
              m = 0
              [0,1,3].each do |j|
              if (byEarthFive1[j] == l) then
                m += 1
                end
              end
              if (m == 1) then
                nFiveSum[l] += 7
              elsif (m == 2) then
                nFiveSum[l] += 10
              elsif (m == 3) then
                nFiveSum[l] += 13
            end
                  end
        end
      end
    end
    return nFiveSum,nOFive,by4Unit,nEarthType,fSkyArray,byEarthIndex1,byEarthFive1
  end

  # 判斷三方會局屬於何局
  def cp_mp_c3d_five(byEarthMask,nEarthType,bWinType=false,byEarthComb1=nil,byEarthComb2=nil)
    bSuccess = false
    k = m = 0
    if (cp_mp_c3d_isWood?(byEarthMask,bWinType,byEarthComb1,byEarthComb2)) then
     # 木局
      bSuccess = true
      k = 1 # 木
      m = 4
          nEarthType = cp_SetCombineParm(2, 3, 4, COMBINE_1,nEarthType)
    elsif (cp_mp_c3d_isFire?(byEarthMask,bWinType,byEarthComb1,byEarthComb2)) then
    # 火局
      bSuccess = true
      k = 2   # 火
      m = 7
          nEarthType = cp_SetCombineParm(5, 6, 7, COMBINE_1,nEarthType)
    elsif (cp_mp_c3d_isGolden?(byEarthMask,bWinType,byEarthComb1,byEarthComb2)) then
        # 金局
      bSuccess = true
      k = 4  # 金
      m = 10
          nEarthType = cp_SetCombineParm(8, 9, 10, COMBINE_1,nEarthType)
    elsif (cp_mp_c3d_isWater?(byEarthMask,bWinType,byEarthComb1,byEarthComb2)) then
         # 水局
      bSuccess = true
      k = 0  # 水
      m = 1
          nEarthType = cp_SetCombineParm(11, 0, 1, COMBINE_1,nEarthType)
    end
    return bSuccess,k,m,nEarthType
  end

  # 三方會局
  # 木局
  def cp_mp_c3d_isWood?(byEarthMask,bWinType=false,byEarthComb1=nil,byEarthComb2=nil)
      if (!bWinType) then
        return (byEarthMask[2] == 1 && byEarthMask[3] == 1 && byEarthMask[4] == 1)
      else
          return ((byEarthComb1[2] == 1 && byEarthComb1[3] == 1 && byEarthComb1[4] == 1) ||
              (byEarthComb2[2] == 1 && byEarthComb2[3] == 1 && byEarthComb2[4] == 1))
        end
  end
  # 火局
  def cp_mp_c3d_isFire?(byEarthMask,bWinType=false,byEarthComb1=nil,byEarthComb2=nil)
      if (!bWinType) then
        return (byEarthMask[5] == 1 && byEarthMask[6] == 1 && byEarthMask[7] == 1)
    else
        return ((byEarthComb1[5] == 1 && byEarthComb1[6] == 1 && byEarthComb1[7] == 1) ||
              (byEarthComb2[5] == 1 && byEarthComb2[6] == 1 && byEarthComb2[7] == 1))
      end
  end
    # 金局
  def cp_mp_c3d_isGolden?(byEarthMask,bWinType=false,byEarthComb1=nil,byEarthComb2=nil)
      if (!bWinType) then
        return (byEarthMask[8] == 1 && byEarthMask[9] == 1 && byEarthMask[10] == 1)
    else
        return ((byEarthComb1[8] == 1 && byEarthComb1[9] == 1 && byEarthComb1[10] == 1) ||
              (byEarthComb2[8] == 1 && byEarthComb2[9] == 1 && byEarthComb2[10] == 1))
      end
  end
    # 水局
  def cp_mp_c3d_isWater?(byEarthMask,bWinType=false,byEarthComb1=nil,byEarthComb2=nil)
      if (!bWinType) then
        return (byEarthMask[11] == 1 && byEarthMask[0] == 1 && byEarthMask[1] == 1)
    else
        return ((byEarthComb1[11] == 1 && byEarthComb1[0] == 1 && byEarthComb1[1] == 1) ||
              (byEarthComb2[11] == 1 && byEarthComb2[0] == 1 && byEarthComb2[1] == 1))
      end
  end

  # 三合局
  # 判斷三合局屬於何局
  def cp_mp_c3h_five(byEarthMask,nEarthType,bWinType=false,byEarthComb1=nil,byEarthComb2=nil)
    bSuccess = false
    k = m = 0
    if (cp_mp_c3h_isWood?(byEarthMask,bWinType,byEarthComb1,byEarthComb2)) then
     # 木局
      bSuccess = true
      k = 1
      l = 7
      m = 11
          nEarthType = cp_SetCombineParm(11, 3, 7, COMBINE_2,nEarthType)
    elsif (cp_mp_c3h_isFire?(byEarthMask,bWinType,byEarthComb1,byEarthComb2)) then
    # 火局
      bSuccess = true
      k = 2
      l = 2
      m = 10
      nEarthType = cp_SetCombineParm(2, 6, 10, COMBINE_2,nEarthType)
    elsif (cp_mp_c3h_isGolden?(byEarthMask,bWinType,byEarthComb1,byEarthComb2)) then
        # 金局
      bSuccess = true
      k = 4
      l = 1
      m = 5
      nEarthType = cp_SetCombineParm(5, 9, 1, COMBINE_2,nEarthType)
    elsif (cp_mp_c3h_isWater?(byEarthMask,bWinType,byEarthComb1,byEarthComb2)) then
         # 水局
      bSuccess = true
      k = 0
      l = 4
      m = 8
      nEarthType = cp_SetCombineParm(8, 0, 4, COMBINE_2,nEarthType)
    end
    return bSuccess,k,l,m,nEarthType
  end

  # 木局
  def cp_mp_c3h_isWood?(byEarthMask,bWinType=false,byEarthComb1=nil,byEarthComb2=nil)
      if (!bWinType) then
        return (byEarthMask[11] == 1 && byEarthMask[3] == 1 && byEarthMask[7] == 1)
      else
          return ((byEarthComb1[11] == 1 && byEarthComb1[3] == 1 && byEarthComb1[7] == 1) ||
                (byEarthComb2[11] == 1 && byEarthComb2[3] == 1 && byEarthComb2[7] == 1))
        end
  end
  # 火局
  def cp_mp_c3h_isFire?(byEarthMask,bWinType=false,byEarthComb1=nil,byEarthComb2=nil)
      if (!bWinType) then
        return (byEarthMask[2] == 1 && byEarthMask[6] == 1 && byEarthMask[10] == 1)
    else
        return ((byEarthComb1[2] == 1 && byEarthComb1[6] == 1 && byEarthComb1[10] == 1) ||
                (byEarthComb2[2] == 1 && byEarthComb2[6] == 1 && byEarthComb2[10] == 1))
      end
  end

    # 金局
  def cp_mp_c3h_isGolden?(byEarthMask,bWinType=false,byEarthComb1=nil,byEarthComb2=nil)
      if (!bWinType) then
        return (byEarthMask[5] == 1 && byEarthMask[9] == 1 && byEarthMask[1] == 1)
    else
        return ((byEarthComb1[5] == 1 && byEarthComb1[9] == 1 && byEarthComb1[1] == 1) ||
                (byEarthComb2[5] == 1 && byEarthComb2[9] == 1 && byEarthComb2[1] == 1))
      end
  end

    # 水局
  def cp_mp_c3h_isWater?(byEarthMask,bWinType=false,byEarthComb1=nil,byEarthComb2=nil)
      if (!bWinType) then
        return (byEarthMask[8] == 1 && byEarthMask[0] == 1 && byEarthMask[4] == 1)
    else
        return ((byEarthComb1[8] == 1 && byEarthComb1[0] == 1 && byEarthComb1[4] == 1) ||
                (byEarthComb2[8] == 1 && byEarthComb2[0] == 1 && byEarthComb2[4] == 1))
      end
  end

  # 三方會局及三合局用 SkyLevel 調整值
  def cp_mp_c3dh_With_SkyLevel(nFiveSum,nFive,nSkyLevel,bySkyFive1,byEarthFive1)
    k = nFive
    m = 0
    [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |byWhatIdx|
      i = byEarthFive1[byWhatIdx]
      if (i == k) then
        n = bySkyFive1[byWhatIdx]
        if (n == k) then
          m += 1
        end
      end
    end

    if (m >= 1) then
      if (m > 3) then
        m = 3
      end
      nFiveSum[k] += nSkyLevel[m - 1]
    end
    return nFiveSum
  end

  # 半方會局
  def cp_mp_fs_checkHalfDir(nFiveSum,nOFive,by4Unit,byEarthMask,byEarthFive1,nEarthType)
    m = by4Unit[0]
    k = 0
    [Eightword::ByMonthIdx,Eightword::ByDayIdx,Eightword::ByHourIdx].each do |byWhatIdx|
      n = by4Unit[byWhatIdx]
      if (cp_mp_chd_isWood?(m,n)) then
       # 木局
        byEarthMask[m] = byEarthMask[m] - 1
        byEarthMask[n] = byEarthMask[n] - 1
        nEarthType = cp_SetCombineParm(m, n, -1, COMBINE_3,nEarthType)
        m = 1
      elsif (cp_mp_chd_isFire?(m,n)) then
       # 火局
        byEarthMask[m] = byEarthMask[m] - 1
        byEarthMask[n] = byEarthMask[n] - 1
        nEarthType = cp_SetCombineParm(m, n, -1, COMBINE_3,nEarthType)
        m = 2
      elsif (cp_mp_chd_isGolden?(m,n)) then
       # 金局
        byEarthMask[m] = byEarthMask[m] - 1
        byEarthMask[n] = byEarthMask[n] - 1
        nEarthType = cp_SetCombineParm(m, n, -1, COMBINE_3,nEarthType)
        m = 4
      elsif (cp_mp_chd_isWater?(m,n)) then
       # 水局
        byEarthMask[m] = byEarthMask[m] - 1
        byEarthMask[n] = byEarthMask[n] - 1
        nEarthType = cp_SetCombineParm(m, n, -1, COMBINE_3,nEarthType)
        m = 0
      else
        m = -1
      end

      if (m >= 0) then
        by4Unit[byWhatIdx] = 0xFF
        by4Unit[byWhatIdx - 1] = 0xFF
        nFiveSum[m] += 8
              nOFive[m] += 8
        l = 0
        [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |j|
          if (byEarthFive1[j] == m) then
            l += 1
          end
        end
        if (l == 1) then
          nFiveSum[m] += 11
        elsif (l == 2) then
          nFiveSum[m] += 14
        elsif (l == 3) then
          nFiveSum[m] += 17
        end
        n = -1
      end
      m = n
    end

    return nFiveSum,nOFive,by4Unit,byEarthMask,nEarthType
  end

  # 木局
  def cp_mp_chd_isWood?(m,n)
    return ((m == 2 && n == 3) || (m == 3 && n == 2))
  end

  # 火局
  def cp_mp_chd_isFire?(m,n)
    return ((m == 5 && n == 6) || (m == 6 && n == 5))
  end

  # 金局
  def cp_mp_chd_isGolden?(m,n)
    return ((m == 8 && n == 9) || (m == 9 && n == 8))
  end

  # 水局
  def cp_mp_chd_isWater?(m,n)
    return ((m == 11 && n == 0) || (m == 0 && n == 11))
  end

  # 地支六合
  def cp_mp_fs_checkEarth6Her(nFiveSum,nOFive,by4Unit,byEarthMask,byEarthIndex1,byEarthFive1,nEarthType)
    m = by4Unit[0]
    k = 0
    byEarthIndex = cp_mp_getByEarthIndex()
    byEarthFive = cp_mp_getByEarthFive()

    [Eightword::ByMonthIdx,Eightword::ByDayIdx,Eightword::ByHourIdx].each do |byWhatIdx|
      n = by4Unit[byWhatIdx]
          if (m + n == 13 || m + n == 1) then
        l = [m, n].min
        if (l == 0) then # 化為土
          l = 3
        elsif (l == 2) then # 化為木
          l = 1
        elsif (l == 3) then # 化為火
          l = 2
        elsif (l == 4) then # 化為金
          l = 4
        elsif (l == 5) then # 化為水
          l = 0
                    if (byEarthMask[2] > 0) then # 三刑
                       l = -1
                    end
              elsif (l == 6) then # 化為火
          l = 2
        else
          l = -1
        end
                if (l >= 0) then
          byEarthMask[m] = byEarthMask[m] - 1
            byEarthMask[n] = byEarthMask[n] - 1
                    m = l
                    nEarthType = cp_SetCombineParm(by4Unit[byWhatIdx - 1],by4Unit[byWhatIdx], -1, COMBINE_5,nEarthType)
          by4Unit[byWhatIdx] = 0xFF
          by4Unit[byWhatIdx - 1] = 0xFF
          nFiveSum[m] += 4
                    nOFive[m] += 4

          byEarthFive1[byWhatIdx] = byEarthFive1[byWhatIdx - 1] = l
          if (byEarthFive[byWhatIdx] != l && byEarthFive[byWhatIdx - 1] != l) then
            if (l == 2) then
              if (byEarthIndex[byWhatIdx] == 3) then
                byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][0]
                byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][1]
              else
                byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][1]
                byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][0]
              end
            else
              if (byEarthIndex[byWhatIdx] == 5) then
                byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][0]
                byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][1]
              else
                byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][1]
                byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][0]
              end
            end
          elsif (byEarthFive[byWhatIdx] != l) then
            if (byEarthIndex1[byWhatIdx - 1] == @@ByChangeFive[l][1]) then
              byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][0]
            else
              byEarthIndex1[byWhatIdx] = @@ByChangeFive[l][1]
            end
          else
            if (byEarthIndex1[byWhatIdx] == @@ByChangeFive[l][1]) then
              byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][0]
            else
              byEarthIndex1[byWhatIdx - 1] = @@ByChangeFive[l][1]
            end
          end

          l = 0
          [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |j|
            if (byEarthFive1[j] == m) then
              l += 1
            end
          end
          if (l == 1) then
            nFiveSum[m] += 7
          elsif (l == 2) then
            nFiveSum[m] += 10
          elsif (l == 3) then
            nFiveSum[m] += 13
          end
          n = -1
        end
      end
      m = n
    end
    return nFiveSum,nOFive,by4Unit,byEarthMask,byEarthIndex1,byEarthFive1,nEarthType
  end

  # 三刑
  def cp_mp_fs_checkEarth3Hsin(byEarthMask,nMFiveSum,nEarthType)
      bSuccess = false
    if (byEarthMask[5] >= 1 && byEarthMask[8] >= 1 &&
      byEarthMask[2] >= 1) then
      nMFiveSum[2] += 4    #巳火
      nMFiveSum[4] += 4    #申金
      nMFiveSum[1] += 4    #寅木
      bSuccess = true
      nEarthType = cp_SetKeParm(5, 8, 2, COMBINE_7,nEarthType)
    elsif (byEarthMask[1] >= 1 && byEarthMask[10] >= 1 &&
      byEarthMask[7] >= 1) then
      nMFiveSum[3] += 4    #丑土
      nMFiveSum[3] += 4    #戌土
      nMFiveSum[3] += 4    #未土
      bSuccess = true
      nEarthType = cp_SetKeParm(1, 10, 7, COMBINE_7,nEarthType)
    elsif (byEarthMask[0] >= 1 && byEarthMask[6] >= 1 &&
      byEarthMask[3] >= 1)
      nMFiveSum[0] += 4    #子水
      nMFiveSum[2] += 4    #午火
      nMFiveSum[1] += 4    #卯木
      bSuccess = true
      nEarthType = cp_SetKeParm(0, 6, 3, COMBINE_7,nEarthType)
    end

    return bSuccess,nMFiveSum,nEarthType
  end

    # 六沖
  def cp_mp_fs_checkEarth6Chuon(by4Unit,byEarthMask,nMFiveSum,nEarthType)
      m = by4Unit[0]
    [Eightword::ByMonthIdx,Eightword::ByDayIdx,Eightword::ByHourIdx].each do |byWhatIdx|
        n = by4Unit[byWhatIdx]
        if (m < 12 && n < 12 && (m - n == 6 || n - m == 6)) then
          byEarthMask[m] = byEarthMask[m] - 1
          byEarthMask[n] = byEarthMask[n] - 1
          nEarthType = cp_SetKeParm(m, n, -1, COMBINE_6,nEarthType)
          nMFiveSum[@@EarthFiveTable[m]] += 4
          nMFiveSum[@@EarthFiveTable[n]] += 4
                n = 255
          by4Unit[byWhatIdx] = 0xFF
          by4Unit[byWhatIdx - 1] = 0xFF
        end
        m = n
      end

      return by4Unit,byEarthMask,nMFiveSum,nEarthType
  end

  # 相刑 自刑
  def cp_mp_fs_checkEarthSelfHsin(by4Unit,nMFiveSum,nEarthType)
    [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByDayIdx].each do |byWhatIdx|
      m = by4Unit[byWhatIdx]
      if (m < 12) then
        (byWhatIdx + 1..Eightword::ByHourIdx).each do |j|
          n = by4Unit[j]
          k = [m, n].min
          l = [m, n].max
                    if ((n < 12) && (k != -1)) then
                        if (m == n && (m == 4 || m == 6 || m == 9 || m == 11)) then # 自刑
                            k = -1
                            nEarthType = cp_SetKeParm(m, n, -1, COMBINE_8,nEarthType)
                        elsif ((k == 2 && (l == 5 || l == 8)) || (k == 5 && l == 8)) then # 無恩之刑
                            k = -1
                            nEarthType = cp_SetKeParm(m, n, -1, COMBINE_9,nEarthType)
                        elsif ((k == 1 && (l == 7 || l == 10)) || (k == 7 && l == 10)) then # 恃勢之刑
                            k = -1
                            nEarthType = cp_SetKeParm(m, n, -1, COMBINE_10,nEarthType)
                        elsif ((k == 0 && (l == 3 || l == 6)) || (k == 3 && l == 6)) then # 無禮之刑
                            k = -1
                            nEarthType = cp_SetKeParm(m, n, -1, COMBINE_11,nEarthType)
                        end
                        if (k == -1) then
                nMFiveSum[@@EarthFiveTable[m]] += 2
                nMFiveSum[@@EarthFiveTable[n]] += 2
                by4Unit[byWhatIdx] = 0xFF
                by4Unit[j] = 0xFF
              end
                    end
        end
      end
    end
    return by4Unit,nMFiveSum,nEarthType
  end

  # 六害
  def cp_mp_fs_checkEarth6Hai(by4Unit,nMFiveSum,nEarthType)
    m = by4Unit[0]
    [Eightword::ByMonthIdx,Eightword::ByDayIdx,Eightword::ByHourIdx].each do |byWhatIdx|
      n = by4Unit[byWhatIdx]
      if (m < 12 && n < 12 && (m + n == 7 || m + n == 19)) then
        nEarthType = cp_SetKeParm(m, n, -1, COMBINE_12,nEarthType)
        nMFiveSum[@@EarthFiveTable[m]] += 2
        nMFiveSum[@@EarthFiveTable[n]] += 2
        n = 255
        by4Unit[byWhatIdx] = 0xFF
        by4Unit[byWhatIdx - 1] = 0xFF
      end
      m = n
    end

    return by4Unit,nMFiveSum,nEarthType
  end

  # 藏干加分
  def cp_mp_fs_checkChungSky(nFiveSum,bySkyFive1)
      (0...3).each do |j|
      n = cp_mp_getByChungSky(Eightword::ByMonthIdx,j)
      if (j == 0) then
        l = 5
      elsif (j == 1) then
        l = 3
      else
        l = 2
      end
      if (n > 0) then
        m = cp_mp_getByChungFive(Eightword::ByMonthIdx,j)
        (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
          n = bySkyFive1[byWhatIdx]
          if (m == n) then
            nFiveSum[m] += l
          end
        end
      end
    end

    return nFiveSum
  end

  # 天干地支分數加分
  def cp_mp_fs_checkSkyEarthScore(nFiveSum,nOFive,bySkyFive1,byEarthFive1)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = bySkyFive1[byWhatIdx]
      n = cp_mp_getBySkyScore(byWhatIdx)
      nFiveSum[m] += n
          nOFive[m] += n
          m = byEarthFive1[byWhatIdx]
          n = cp_mp_getByEarthScore(byWhatIdx)
      nFiveSum[m] += n
          nOFive[m] += 16
    end

    return nFiveSum,nOFive
  end

  def cp_SetCombineParm(nP1,nP2,nP3,nCombinType,nEarthType)
    m = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    if (m == nP1 || m == nP2 || m == nP3) then
      nEarthType[Eightword::ByDayIdx] = nCombinType
      if (m == nP1) then
        nP1 = -1
      elsif (m == nP2) then
        nP2 = -1
      else
        nP3 = -1
      end
    end
    [Eightword::ByHourIdx,Eightword::ByMonthIdx,Eightword::ByYearIdx].each do |byWhatIdx|
      m = cp_mp_getByEarthIndex(byWhatIdx)
      if (m == nP1 || m == nP2 || m == nP3) then
        nEarthType[byWhatIdx] = nCombinType
        if (m == nP1) then
          nP1 = -1
        elsif (m == nP2) then
          nP2 = -1
        else
          nP3 = -1
        end
      end
    end
      if (nP1 >= 0 || nP2 >= 0 || nP3 >= 0) then
          nEarthType[4] = nCombinType
      end
      return nEarthType
  end

  def cp_SetKeParm(nP1, nP2, nP3, nCombinType,nEarthType)
    m = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    if (m == nP1 || m == nP2 || m == nP3) then
      nEarthType[Eightword::ByDayIdx] = nCombinType
      if (m == nP1) then
        nP1 = -1
      elsif (m == nP2) then
        nP2 = -1
      else
        nP3 = -1
      end
    end
    [Eightword::ByHourIdx,Eightword::ByMonthIdx,Eightword::ByYearIdx].each do |byWhatIdx|
      m = cp_mp_getByEarthIndex(byWhatIdx)
      if (m == nP1 || m == nP2 || m == nP3) then
        nEarthType[byWhatIdx] = nCombinType
        if (m == nP1) then
          nP1 = -1
        elsif (m == nP2) then
          nP2 = -1
        else
          nP3 = -1
        end
      end
    end
      if (nP1 >= 0 || nP2 >= 0 || nP3 >= 0) then
          nEarthType[4] = nCombinType
      end
      return nEarthType
  end

  # change pan of main pan
  def cp_cp_getPanInfo(key,sFunc,byWhatIdx=nil,arrayIndex=nil)
    # if (key == Eightword::ByEarthScore_4) then
    #   Pm.saveTestDb("cp_mp_getPanInfo",@EightwordInfo)
    # end
    if (byWhatIdx != nil) then
      if (arrayIndex == nil) then
        if (@EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx] == nil) then
          eval(sFunc)
        end
        byValue = @EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx]
      else
        if (@EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx][arrayIndex] == nil) then
          eval(sFunc)
        end
        byValue = @EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx][arrayIndex]
      end
    else
      if (arrayIndex == nil) then
        if (@EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key][Eightword::ByYearIdx] == nil) then
          eval(sFunc)
        end
        byValue = @EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key]
      else
        if (@EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key][arrayIndex] == nil) then
          eval(sFunc)
        end
        byValue = @EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key][arrayIndex]
      end
    end
    return cloneValue(byValue)
  end
  def cp_cp_setPanInfo(key,byValue,byWhatIdx=nil,arrayIndex=nil)
    byValue = cloneValue(byValue)
    if (byWhatIdx != nil) then
      if (arrayIndex == nil) then
        @EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx] = byValue
      else
        @EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx][arrayIndex] = byValue
      end
    else
      if (arrayIndex == nil) then
        @EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key] = byValue
      else
        @EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL][key][arrayIndex] = byValue
      end
    end
  end
  def cp_cp_getScoreInfo(key,sFunc,arrayIndex=nil)
    if (arrayIndex == nil) then
      if ((@EightwordInfo[Eightword::CHANGE_PAN][Eightword::Scores][key] == nil) ||
        (@EightwordInfo[Eightword::CHANGE_PAN][Eightword::Scores][key][0] == nil)) then
        eval(sFunc)
      end
      return @EightwordInfo[Eightword::CHANGE_PAN][Eightword::Scores][key]
    else
      if (@EightwordInfo[Eightword::CHANGE_PAN][Eightword::Scores][key][arrayIndex] == nil) then
        eval(sFunc)
      end
      return @EightwordInfo[Eightword::CHANGE_PAN][Eightword::Scores][key][arrayIndex]
    end
  end
  def cp_cp_setScoreInfo(key,byValue,arrayIndex=nil)
    byValue = cloneValue(byValue)
    if (arrayIndex == nil) then
      @EightwordInfo[Eightword::CHANGE_PAN][Eightword::Scores][key] = byValue
    else
      @EightwordInfo[Eightword::CHANGE_PAN][Eightword::Scores][key][arrayIndex] = byValue
    end
  end

  def cp_cp_BySkyIndex()
    # 一開始先將ChangePan設為跟 Main Pan一樣，之後再慢慢修改 cp_mp_CombineSky
    bySkyIndex1 = cp_mp_getBySkyIndex()

    bySkyIndex = cp_mp_getBySkyIndex()
    byEarthIndex = cp_mp_getByEarthIndex()

    m = bySkyIndex[Eightword::ByYearIdx]
    k = byEarthIndex[Eightword::ByYearIdx]

    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |i|
      n = bySkyIndex[i]
      l = byEarthIndex[i]
      if (m == n + 5 || n == m + 5) then
        bSuccess,m,n,k,l,nType = cp_mp_CheckCombineSuccess(m,n,k,l)

        if (nType == "m0_k10_l5") then
          if (bySkyIndex[i] != 0) then
            bySkyIndex1[i - 1] = 4
          else
            bySkyIndex1[i] = 4
          end
        elsif (nType == "m1_k9_l4") then
          if (bySkyIndex[i] != 1) then
            bySkyIndex1[i - 1] = 7
          else
            bySkyIndex1[i] = 7
          end
        elsif (nType == "m2_k8_l1") then
          if (bySkyIndex[i] != 2) then
            bySkyIndex1[i - 1] = 8
            bySkyIndex1[i] = 9
          else
            bySkyIndex1[i] = 8
            bySkyIndex1[i - 1] = 9
          end
        elsif (nType == "m3_k7_l2") then
          if (bySkyIndex[i] != 3) then
            bySkyIndex1[i - 1] = 1
            bySkyIndex1[i] = 0
          else
            bySkyIndex1[i] = 1
            bySkyIndex1[i - 1] = 0
          end
        elsif (nType == "m4_k6_l5") then
          if (bySkyIndex[i] != 4) then
            bySkyIndex1[i - 1] = 2
            bySkyIndex1[i] = 3
          else
            bySkyIndex1[i] = 2
            bySkyIndex1[i - 1] = 3
          end
        end

              k = 0
              if (!bSuccess && i < 3) then
                  if (bySkyIndex[i + 1] == bySkyIndex[i - 1]) then
                      k = -1
                  end
              end
              if (k != -1) then
                  n = -1
            l = -1
              end
      end
      m = n
      k = l
    end
    cp_cp_setBySkyIndex(bySkyIndex1)
  end
  def cp_cp_getBySkyIndex(byWhatIdx=nil)
    return cp_cp_getPanInfo(Eightword::BySky_4,"cp_cp_BySkyIndex()",byWhatIdx)
  end
  def cp_cp_setBySkyIndex(byValue,byWhatIdx=nil)
    cp_cp_setPanInfo(Eightword::BySky_4,byValue,byWhatIdx)
  end
  def cp_sfi_getBySkyIndex(byWhatIdx=nil)
    return cp_fi_getScoreInfo(Eightword::BySky_4,"cp_sfi_setBySkyIndex()",byWhatIdx)
  end
  def cp_sfi_setBySkyIndex(bySkyIndex=nil,byWhatIdx=nil)
      if (ddg_SkyEarth() == Sky::SKY) then
        return cp_sfi_setBySkyIndex_Sky(bySkyIndex,byWhatIdx)
      else
        return cp_sfi_setBySkyIndex_Earth(bySkyIndex,byWhatIdx)
      end
  end
  def cp_sfi_setBySkyIndex_Sky(bySkyIndex=nil,byWhatIdx=nil)
    if (bySkyIndex == nil) then
      bySkyIndex = cp_mp_getBySkyIndex().clone
    end
    cp_fi_setScoreInfo(Eightword::BySky_4,bySkyIndex,byWhatIdx)
  end
  def cp_sfi_setBySkyIndex_Earth(bySkyIndex=nil,byWhatIdx=nil)
    if (bySkyIndex == nil) then
      bySkyIndex = cp_cp_getBySkyIndex().clone
    end
    cp_fi_setScoreInfo(Eightword::BySky_4,bySkyIndex,byWhatIdx)
  end

  def cp_cp_BySkyFive()
    # 一開始先將ChangePan設為跟 Main Pan一樣，之後再慢慢修改 cp_mp_CombineSky
    bySkyFive1 = cp_mp_getBySkyFive().clone

    bySkyIndex = cp_mp_getBySkyIndex()
    byEarthIndex = cp_mp_getByEarthIndex()

    m = bySkyIndex[Eightword::ByYearIdx]
    k = byEarthIndex[Eightword::ByYearIdx]

    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |i|
      n = bySkyIndex[i]
      l = byEarthIndex[i]
      if (m == n + 5 || n == m + 5) then
        bSuccess,m,n,k,l,nType = cp_mp_CheckCombineSuccess(m,n,k,l)

        if (nType == "m0_k10_l5") then
          bySkyFive1[i] = bySkyFive1[i - 1] = 3
        elsif (nType == "m1_k9_l4") then
          bySkyFive1[i] = bySkyFive1[i - 1] = 4
        elsif (nType == "m2_k8_l1") then
          bySkyFive1[i] = bySkyFive1[i - 1] = 0
        elsif (nType == "m3_k7_l2") then
          bySkyFive1[i] = bySkyFive1[i - 1] = 1
        elsif (nType == "m4_k6_l5") then
          bySkyFive1[i] = bySkyFive1[i - 1] = 2
        end

              k = 0
              if (!bSuccess && i < 3) then
                  if (bySkyIndex[i + 1] == bySkyIndex[i - 1]) then
                      k = -1
                  end
              end
              if (k != -1) then
                  n = -1
            l = -1
              end
      end
      m = n
      k = l
    end
    cp_cp_setBySkyFive(bySkyFive1)
  end
  def cp_cp_getBySkyFive(byWhatIdx=nil)
    return cp_cp_getPanInfo(Eightword::BySkyFive_4,"cp_cp_BySkyFive()",byWhatIdx)
  end
  def cp_cp_setBySkyFive(byValue,byWhatIdx=nil)
    cp_cp_setPanInfo(Eightword::BySkyFive_4,byValue,byWhatIdx)
  end
  def cp_sfi_getBySkyFive(byWhatIdx=nil)
    return cp_fi_getScoreInfo(Eightword::BySkyFive_4,"cp_sfi_setBySkyFive()",byWhatIdx)
  end
  def cp_sfi_setBySkyFive(bySkyFive=nil,byWhatIdx=nil)
      if (ddg_SkyEarth() == Sky::SKY) then
        return cp_sfi_setBySkyFive_Sky(bySkyFive,byWhatIdx)
      else
        return cp_sfi_setBySkyFive_Earth(bySkyFive,byWhatIdx)
      end
  end
  def cp_sfi_setBySkyFive_Sky(bySkyFive=nil,byWhatIdx=nil)
    if (bySkyFive == nil) then
      bySkyFive = cp_mp_getBySkyFive().clone
    end

    cp_fi_setScoreInfo(Eightword::BySkyFive_4,bySkyFive,byWhatIdx)
  end
  def cp_sfi_setBySkyFive_Earth(bySkyFive=nil,byWhatIdx=nil)
    if (bySkyFive == nil) then
      bySkyFive = cp_cp_getBySkyFive().clone
    end
    cp_fi_setScoreInfo(Eightword::BySkyFive_4,bySkyFive,byWhatIdx)
  end

  def cp_cp_nSkyType()
    # 一開始先將ChangePan設為跟 Main Pan一樣，之後再慢慢修改 cp_mp_CombineSky
    nSkyType1 = Array.new(5,-1)

    bySkyIndex = cp_mp_getBySkyIndex()
    byEarthIndex = cp_mp_getByEarthIndex()

    m = bySkyIndex[Eightword::ByYearIdx]
    k = byEarthIndex[Eightword::ByYearIdx]

    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |i|
      n = bySkyIndex[i]
      l = byEarthIndex[i]
      if (m == n + 5 || n == m + 5) then
        bSuccess,m,n,k,l,nType = cp_mp_CheckCombineSuccess(m,n,k,l)

        if (nType == "m0_k10_l5") then
                    nSkyType1[i] = nSkyType1[i - 1] = 3
        elsif (nType == "m1_k9_l4") then
                    nSkyType1[i] = nSkyType1[i - 1] = 4
        elsif (nType == "m2_k8_l1") then
                    nSkyType1[i] = nSkyType1[i - 1] = 0
        elsif (nType == "m3_k7_l2") then
                    nSkyType1[i] = nSkyType1[i - 1] = 1
        elsif (nType == "m4_k6_l5") then
                    nSkyType1[i] = nSkyType1[i - 1] = 2
        end

              k = 0
              if (!bSuccess && i < 3) then
                  if (bySkyIndex[i + 1] == bySkyIndex[i - 1]) then
                      k = -1
                  end
              end
              if (k != -1) then
                  n = -1
            l = -1
              end
      end
      m = n
      k = l
    end
    cp_cp_setnSkyType(nSkyType1)
  end
  def cp_cp_getnSkyType(arrayIndex=nil)
    return cp_cp_getScoreInfo(Eightword::NSkyType_5,"cp_cp_nSkyType()",arrayIndex)
  end
  def cp_cp_setnSkyType(byValue,arrayIndex=nil)
    cp_cp_setScoreInfo(Eightword::NSkyType_5,byValue,arrayIndex)
  end
  def cp_sfi_getnSkyType(arrayIndex=nil)
    return cp_fi_getScoreInfo(Eightword::NSkyType_5,"cp_sfi_setnSkyType()",arrayIndex)
  end
  def cp_sfi_setnSkyType(byValue=nil,arrayIndex=nil)
      if (ddg_SkyEarth() == Sky::SKY) then
        return cp_sfi_setnSkyType_Sky(byValue,arrayIndex)
      else
        return cp_sfi_setnSkyType_Earth(byValue,arrayIndex)
      end
  end
  def cp_sfi_setnSkyType_Sky(byValue=nil,arrayIndex=nil)
    if (byValue == nil) then
      byValue = cp_cp_getnSkyType().clone
    end
    cp_fi_setScoreInfo(Eightword::NSkyType_5,byValue,arrayIndex)
  end
  def cp_sfi_setnSkyType_Earth(byValue=nil,arrayIndex=nil)
    if (byValue == nil) then
      byValue = cp_cp_getnSkyType().clone
    end
    cp_fi_setScoreInfo(Eightword::NSkyType_5,byValue,arrayIndex)
  end

  # Earth
  def cp_cp_ByEarthIndex()
    cp_mp_ChangePan()
  end
  def cp_cp_getByEarthIndex(byWhatIdx=nil)
    return cp_cp_getPanInfo(Eightword::ByEarth_4,"cp_cp_ByEarthIndex()",byWhatIdx)
  end
  def cp_cp_setByEarthIndex(byValue,byWhatIdx=nil)
    cp_cp_setPanInfo(Eightword::ByEarth_4,byValue,byWhatIdx)
  end
  def cp_sfi_getByEarthIndex(byWhatIdx=nil)
    return cp_fi_getScoreInfo(Eightword::ByEarth_4,"cp_sfi_ByEarthIndex()",byWhatIdx)
  end
  def cp_sfi_ByEarthIndex()
      if (ddg_SkyEarth() == Sky::SKY) then
        return cp_sfi_ByEarthIndex_Sky()
      else
        return cp_sfi_ByEarthIndex_Earth()
      end
  end
  def cp_sfi_ByEarthIndex_Sky()
    byEarthIndex = cp_cfp_getByEarthIndex_Sky()
    cp_sfi_setByEarthIndex(byEarthIndex)
  end
  def cp_sfi_ByEarthIndex_Earth()
    cp_ChangeFlowPan()
  end
  def cp_sfi_setByEarthIndex(byEarthIndex,byWhatIdx=nil)
    cp_fi_setScoreInfo(Eightword::ByEarth_4,byEarthIndex,byWhatIdx)
  end


  def cp_cp_ByEarthFive()
    cp_mp_ChangePan()
  end
  def cp_cp_getByEarthFive(byWhatIdx=nil)
    return cp_cp_getPanInfo(Eightword::ByEarthFive_4,"cp_cp_ByEarthFive()",byWhatIdx)
  end
  def cp_cp_setByEarthFive(byValue,byWhatIdx=nil)
    cp_cp_setPanInfo(Eightword::ByEarthFive_4,byValue,byWhatIdx)
  end
  def cp_sfi_getByEarthFive(byWhatIdx=nil)
    return cp_fi_getScoreInfo(Eightword::ByEarthFive_4,"cp_sfi_ByEarthFive()",byWhatIdx)
  end
  def cp_sfi_ByEarthFive()
      if (ddg_SkyEarth() == Sky::SKY) then
        cp_sfi_ByEarthFive_Sky()
      else
        cp_sfi_ByEarthFive_Earth()
      end
  end
  def cp_sfi_ByEarthFive_Sky()
    byEarthFive = cp_cfp_getByEarthFive_Sky()
    cp_sfi_setByEarthFive(byEarthFive)
  end
  def cp_sfi_ByEarthFive_Earth()
    cp_ChangeFlowPan()
  end

  def cp_sfi_setByEarthFive(byEarthFive=nil,byWhatIdx=nil)
    cp_fi_setScoreInfo(Eightword::ByEarthFive_4,byEarthFive)
  end

  def cp_sfi_getScoreInfo2(key,sFunc,byWhatIdx=nil,arrayIndex=nil)
    byValue = cp_fi_getScoreInfo(key,sFunc,byWhatIdx)
    if (byWhatIdx == nil) then
      return byValue
    else
      if (arrayIndex == nil) then
        return byValue
      else
        return byValue[arrayIndex]
      end
    end
  end

  # byChungSky
  def cp_cp_ByChungSky()
    byChungSky = cp_mp_getByChungSky()
    cp_cp_setByChungSky(byChungSky)
  end
  def cp_cp_getByChungSky(byWhatIdx=nil,arrayIndex=nil)
    return cp_cp_getPanInfo(Eightword::ByChungSky_4_3,"cp_cp_ByChungSky()",byWhatIdx,arrayIndex)
  end
  def cp_cp_setByChungSky(byValue,byWhatIdx=nil,arrayIndex=nil)
    cp_cp_setPanInfo(Eightword::ByChungSky_4_3,byValue,byWhatIdx,arrayIndex)
  end
  def cp_sfi_getByChungSky(byWhatIdx=nil,arrayIndex=nil)
    return cp_sfi_getScoreInfo2(Eightword::ByChungSky_4_3,"cp_sfi_setByChungSky()",byWhatIdx,arrayIndex)
  end
  def cp_sfi_setByChungSky(byChungSky=nil,byWhatIdx=nil)
      if (ddg_SkyEarth() == Sky::SKY) then
        cp_sfi_setByChungSky_Sky(byChungSky,byWhatIdx)
      else
        cp_sfi_setByChungSky_Earth(byChungSky,byWhatIdx)
      end
  end
  def cp_sfi_setByChungSky_Sky(byChungSky=nil,byWhatIdx=nil)
    if (byChungSky == nil) then
      byChungSky = cp_mp_getByChungSky().clone
    end
    cp_fi_setScoreInfo(Eightword::ByChungSky_4_3,byChungSky)
  end
  def cp_sfi_setByChungSky_Earth(byChungSky=nil,byWhatIdx=nil)
    if (byChungSky == nil) then
      byChungSky = cp_mp_getByChungSky().clone
    end
    cp_fi_setScoreInfo(Eightword::ByChungSky_4_3,byChungSky,byWhatIdx)
  end

  # byChungFive
  def cp_cp_ByChungFive()
    byChungFive = cp_mp_getByChungFive()
    cp_cp_setByChungFive(byChungFive)
  end
  def cp_cp_getByChungFive(byWhatIdx=nil,arrayIndex=nil)
    return cp_cp_getPanInfo(Eightword::ByChungFive_4_3,"cp_cp_ByChungFive()",byWhatIdx,arrayIndex)
  end
  def cp_cp_setByChungFive(byValue,byWhatIdx=nil,arrayIndex=nil)
    cp_cp_setPanInfo(Eightword::ByChungFive_4_3,byValue,byWhatIdx,arrayIndex)
  end
  def cp_sfi_ByChungFive()
      if (ddg_SkyEarth() == Sky::SKY) then
        cp_sfi_ByChungFive_Sky()
      else
        cp_sfi_ByChungFive_Earth()
      end
  end
  def cp_sfi_ByChungFive_Sky()
    byChungFive = cp_mp_getByChungFive().clone
    cp_sfi_setByChungFive(byChungFive)
  end
  def cp_sfi_ByChungFive_Earth()
    byChungFive = cp_mp_getByChungFive().clone
    cp_sfi_setByChungFive(byChungFive)
  end
  def cp_sfi_getByChungFive(byWhatIdx=nil,arrayIndex=nil)
    return cp_sfi_getScoreInfo2(Eightword::ByChungFive_4_3,"cp_sfi_ByChungFive()",byWhatIdx,arrayIndex)
  end
  def cp_sfi_setByChungFive(byChungFive=nil,byWhatIdx=nil)
    cp_fi_setScoreInfo(Eightword::ByChungFive_4_3,byChungFive,byWhatIdx)
  end

end

