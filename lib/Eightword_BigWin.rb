require("SkyEarthFive_Function.rb")

class Eightword

  def cp_cfp_getByEarthIndex(byWhatIdx=nil)
      if (ddg_SkyEarth() == Sky::SKY) then
        return cp_cfp_getByEarthIndex_Sky(byWhatIdx)
      else
        return cp_cfp_getByEarthIndex_Earth(byWhatIdx)
      end
  end
  def cp_cfp_getByEarthIndex_Sky(byWhatIdx=nil)
    return cp_cp_getByEarthIndex()
  end
  def cp_cfp_getByEarthIndex_Earth(byWhatIdx=nil)
    return cp_mp_getByEarthIndex()
  end

  def cp_cfp_getByEarthFive()
      if (ddg_SkyEarth() == Sky::SKY) then
        cp_cfp_getByEarthFive_Sky()
      else
        cp_cfp_getByEarthFive_Earth()
      end
  end
  def cp_cfp_getByEarthFive_Sky()
    return cp_cp_getByEarthFive().clone
  end
  def cp_cfp_getByEarthFive_Earth()
    return cp_mp_getByEarthFive().clone
  end


    def cp_ChangeFlowPan()
    bWinType = false

    nEarthIndex = cp_fi_getCurEarthIndex()
    byEarthIndex1 = cp_fi_getByEarthIndex()
    byEarthFive1 = cp_fi_getByEarthFive()

    nFiveSum = Array.new(5,0)
    nMFiveSum = Array.new(5,0)
    nOFive = Array.new(5,0)
    nSkyLevel = Array.new(3,0)
    nOEarthFive = Array.new(5,0)
    nEarthArray = Array.new(5,16)
    # 天干
    bySkyMask = Array.new(10,0)
    fSkyArray = Array.new(10,0)

    nEarthType = Array.new(5,-1)

    by4Unit = cp_mp_by4Unit()
    by4Unit[4] = nEarthIndex

    byType,byCombType,bCuongKe,nCombineNum = cp_cfp_init_byCombType(nEarthIndex,by4Unit)

    # 地支
    byEarthMask = cp_cfp_byEarthMask_3Dir(nEarthIndex)
    bSuccess,m,i,j,k,nS1,nS2 = cp_cfp_CheckSuccess_3Dir(nEarthIndex,bCuongKe,by4Unit,byType,byEarthMask)
      nDoCombine = 0
    bSuccess,byCombType,nDoCombine = cp_cfp_CheckSuccess_byCombType(nEarthIndex,bSuccess,by4Unit,byCombType,byEarthMask,nCombineNum,m,i,j,k,nDoCombine)

    byEarthFive = cp_cfp_getByEarthFive()
    byEarthIndex = cp_cfp_getByEarthIndex()
    if (bSuccess) then
      nFiveSum,fSkyArray,byEarthFive,byEarthIndex,by4Unit,nEarthArray,nSkyLevel,nEarthType = cp_cfp_HandleSuccess_byCombType(by4Unit,m,i,j,k,nS1,nS2,nFiveSum,fSkyArray,byEarthFive,byEarthIndex,nEarthArray,nSkyLevel,nEarthType)
      cp_sfi_setByEarthFive(byEarthFive)
      cp_sfi_setByEarthIndex(byEarthIndex)
    else
      bSuccess,byCombType,nDoCombine,m,i,j,k,nS1,nS2 = cp_cfp_CheckSuccess_3Her(nEarthIndex,byEarthMask,by4Unit,bCuongKe,byType,byCombType,nDoCombine,nCombineNum)
      if (bSuccess)
        nFiveSum,fSkyArray,byEarthFive,byEarthIndex,by4Unit,nEarthArray,nSkyLevel,nEarthType = cp_cfp_HandleSuccess_3Her(by4Unit,nFiveSum,byEarthFive,byEarthIndex,nEarthArray,fSkyArray,m,i,j,k,nS1,nS2,nSkyLevel,nEarthType)
        cp_sfi_setByEarthFive(byEarthFive)
        cp_sfi_setByEarthIndex(byEarthIndex)
      end
    end

      if (!bSuccess && !bCuongKe && nCombineNum == 0 && nDoCombine == 0) then
        bSuccess,nFiveSum,nOFive,fSkyArray,nEarthType = cp_Check2Pair(nFiveSum,nil,byEarthIndex1,byEarthFive1,fSkyArray)
      if (bSuccess) then
        (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
          by4Unit[byWhatIdx] = 0xFF
                  nEarthArray[byWhatIdx] = 0
        end
        nFiveSum = Array.new(5,0)
        fSkyArray = Array.new(10,0)
              return nFiveSum,fSkyArray
      end
    end

    # 半方會局
    nFiveSum,nEarthArray,by4Unit,fSkyArray,byEarthMask,byCombType,nEarthType = cp_cfp_HalfDir(nFiveSum,byEarthMask,by4Unit,byCombType,byType,nCombineNum,fSkyArray,nEarthArray,nEarthType)

    # 地支六合
    nFiveSum,nEarthArray,by4Unit,fSkyArray,byEarthMask,byCombType,byEarthFive,byEarthIndex,nEarthType = cp_cfp_Earth6Her(nFiveSum,byEarthMask,by4Unit,byCombType,byType,nCombineNum,fSkyArray,nEarthArray,byEarthFive,byEarthIndex,nEarthType)
    cp_sfi_setByEarthFive(byEarthFive)
    cp_sfi_setByEarthIndex(byEarthIndex)
# nil[1]
    # 扣分
      # 三刑
    nMFiveSum,nEarthArray,by4Unit,fSkyArray,byEarthMask,nEarthType = cp_cfp_Earth3Hsin(nMFiveSum,byEarthMask,by4Unit,fSkyArray,nEarthArray,nEarthType)

    # 六沖
    nMFiveSum,nEarthArray,by4Unit,fSkyArray,byEarthMask,nEarthType = cp_cfp_Earth6Chuon(nMFiveSum,byEarthMask,by4Unit,fSkyArray,nEarthArray,nEarthType)

    # 相刑 自刑
    nMFiveSum,nEarthArray,by4Unit,fSkyArray,byEarthMask,nEarthType  = cp_cfp_SelfHsin(nMFiveSum,byEarthMask,by4Unit,fSkyArray,nEarthArray,nEarthType)

    # 六害
    nMFiveSum,nEarthArray,by4Unit,fSkyArray,nEarthType = cp_cfp_Earth6Hai(nMFiveSum,by4Unit,fSkyArray,nEarthArray,nEarthType)

  # nil[1]
    (0...5).each do |i|
      nFiveSum[i] = nFiveSum[i] - nMFiveSum[i]
    end
  # nil[1]
    return nFiveSum,fSkyArray,nEarthType,nEarthArray
  end

  def cp_cfp_init_byCombType(nEarthIndex,by4Unit)
      n = nEarthIndex
    bCuongKe = false
    nCombineNum = 0
    byType = Array.new(4,0)
    byCombType = Array.new(4,0)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = by4Unit[byWhatIdx]
      j = [m, n].min
      k = [m, n].max
      if (k - j == 6) then
        byType[byWhatIdx] = 0xFF
        bCuongKe = true
          elsif (j + k == 1 || j + k == 13) then
              byCombType[byWhatIdx] = 1
              nCombineNum += 1
          end
        end
        return byType,byCombType,bCuongKe,nCombineNum
  end

  # 三方會局
  def cp_cfp_byEarthMask_3Dir(nEarthIndex)
    byEarthMask = Array.new(12,0)

    byEarthMask[nEarthIndex] = 1

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = cp_mp_getByEarthIndex(byWhatIdx)
      byEarthMask[m] = byEarthMask[m] + 1
    end
    return byEarthMask
  end

  # 三方會局
  def cp_cfp_CheckSuccess_3Dir(nEarthIndex,bCuongKe,by4Unit,byType,byEarthMask)
    bSuccess = false

    if (byEarthMask[2] >= 1 && byEarthMask[3] >= 1 &&
      byEarthMask[4] >= 1) then # 木局
      bSuccess = true
      m = 1
      i = 2
      j = 3
      k = 4
          nS1 = 0
          nS2 = 1
    elsif (byEarthMask[5] >= 1 && byEarthMask[6] >= 1 &&
      byEarthMask[7] >= 1) then # 火局
      bSuccess = true
      m = 2
      i = 5
      j = 6
      k = 7
          nS1 = 2
          nS2 = 3
    elsif (byEarthMask[8] >= 1 && byEarthMask[9] >= 1 &&
      byEarthMask[10] >= 1) then # 金局
      bSuccess = true
      m = 4
      i = 8
      j = 9
      k = 10
          nS1 = 6
          nS2 = 7
    elsif (byEarthMask[11] >= 1 && byEarthMask[0] >= 1 &&
      byEarthMask[1] >= 1) then # 水局
      bSuccess = true
      m = 0
      i = 11
      j = 0
      k = 1
          nS1 = 8
          nS2 = 9
    end
      if (bSuccess && bCuongKe) then
      n = nEarthIndex
      if (n != i && n != j && n != k) then
        (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |l|
          n = by4Unit[l]
          if (n == i || n == j || n == k) then
            if (byType[l] == 0xFF) then
              bSuccess = false
            end
          end
        end
      end
    end
    return bSuccess,m,i,j,k,nS1,nS2
  end

  def cp_cfp_CheckSuccess_byCombType(nEarthIndex,bSuccess,by4Unit,byCombType,byEarthMask,nCombineNum,m,i,j,k,nDoCombine)
      if (bSuccess) then
          if (byEarthMask[i] > 1 || byEarthMask[j] > 1 ||
          byEarthMask[k] > 1) then
              if (byEarthMask[nEarthIndex] > 1 && (nEarthIndex == i || nEarthIndex == j || nEarthIndex == k)) then
                  if (nCombineNum <= 0) then
                      bSuccess = false
            (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |l|
                  n = by4Unit[l]
                          if (n == nEarthIndex) then
                              byCombType[l] = 2
                              nDoCombine += 1
                            end
                        end
                  else
                      bSuccess = false
                      bStop = false
            (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |l|
              if (!bStop) then
                    n = by4Unit[l]
                            if (byCombType[l] == 1) then
                                if (n != i && n != j && n != k) then
                                    bSuccess = true
                                    bStop = true
                                elsif (n == nEarthIndex) then
                                    byCombType[l] = 2
                                    nDoCombine += 1
                                end
                            end
                        end
                      end
                  end
              end
          elsif (nCombineNum >= 1) then
              bSuccess = false
              bStop = false
        (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |l|
          if (!bStop) then
            n = by4Unit[l]
                    if (byCombType[l] == 1) then
                        if (n != i && n != j && n != k) then
                            bSuccess = true
                            bStop = true
                        elsif (nCombineNum == 1 && (m == 2 || m == 0))
                            bSuccess = true
                            bStop = true
                        else
                            byCombType[l] = 2
                            nDoCombine += 1
                        end
                    end
                end
              end
          end
      end

      return bSuccess,byCombType,nDoCombine
    end

  def cp_cfp_HandleSuccess_byCombType(by4Unit,m,i,j,k,nS1,nS2,nFiveSum,fSkyArray,byEarthFive,byEarthIndex,nEarthArray,nSkyLevel,nEarthType)
# nil[1]
    nFiveSum[m] += 12
        fSkyArray[nS1] = fSkyArray[nS2] = 24
    nEarthType = cp_SetCombineParm(i, j, k, COMBINE_1,nEarthType)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    n = nDayEarthIndex
    if (n == k) then
      byEarthFive[2] = m
      byEarthIndex[2] = @@ByChangeFive[m][0]
      by4Unit[2] = 0xFF
            nEarthArray[2] = 0
      k = -1
    elsif (n == i || n == j) then
      by4Unit[2] = 0xFF
            nEarthArray[2] = 0
      if (n == i) then
        i = -1
      else
        j = -1
      end
    end
    (0..4).each do |l|
      n = by4Unit[l]
      if (n < 12 && n >= 0) then
        if (n == i || n == j || n == k) then
          by4Unit[l] = 0xFF
                    nEarthArray[l] = 0
          if (n == k) then
            if (l < 4) then
                            byEarthFive[l] = m
                byEarthIndex[l] = @@ByChangeFive[m][0]
                        end
            k = -1
                    elsif (n == i) then
                        i = -1
                    elsif (n == j) then
                        j = -1
                    end
        end
      end
    end
    nSkyLevel[0] = 15
    nSkyLevel[1] = 18
    nSkyLevel[2] = 21

    return nFiveSum,fSkyArray,byEarthFive,byEarthIndex,by4Unit,nEarthArray,nSkyLevel,nEarthType
  end
  def cp_cfp_cs_3h_check(nEarthIndex,byEarthMask,bCuongKe,by4Unit)
    bSuccess = false
    # 三合局
    if (byEarthMask[11] >= 1 && byEarthMask[3] >= 1 &&
      byEarthMask[7] >= 1) then # 木局
      bSuccess = true
      m = 1
      i = 7
      j = 11
      k = 3
            nS1 = 1
            nS2 = 8
    elsif (byEarthMask[2] >= 1 && byEarthMask[6] >= 1 &&
      byEarthMask[10] >= 1) then # 火局
      bSuccess = true
      m = 2
      i = 2
      j = 10
      k = 6
            nS1 = 0
            nS2 = 2
    elsif (byEarthMask[5] >= 1 && byEarthMask[9] >= 1 &&
      byEarthMask[1] >= 1) then # 金局
      bSuccess = true
      m = 4
      i = 1
      j = 5
      k = 9
            nS1 = 7
            nS2 = 5
    elsif (byEarthMask[8] >= 1 && byEarthMask[0] >= 1 &&
      byEarthMask[4] >= 1) then # 水局
      bSuccess = true
      m = 0
      i = 4
      j = 8
      k = 0
            nS1 = 9
            nS2 = 6
        end
        if (bSuccess && bCuongKe) then
      n = nEarthIndex
      if (n != i && n != j && n != k) then
        (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |l|
          n = by4Unit[l]
          if (n == i || n == j || n == k) then
            if (byType[l] == 0xFF) then
              bSuccess = false
            end
          end
        end
      end
    end
        return bSuccess,m,i,j,k,nS1,nS2
  end
  def cp_cfp_CheckSuccess_3Her(nEarthIndex,byEarthMask,by4Unit,bCuongKe,byType,byCombType,nDoCombine,nCombineNum)
    bSuccess,m,i,j,k,nS1,nS2 = cp_cfp_cs_3h_check(nEarthIndex,byEarthMask,bCuongKe,by4Unit)

        if (bSuccess) then
      bSuccess,byCombType,nDoCombine = cp_cfp_CheckSuccess_byCombType(nEarthIndex,bSuccess,by4Unit,byCombType,byEarthMask,nCombineNum,-1,i,j,k,nDoCombine)
        end
        return bSuccess,byCombType,nDoCombine,m,i,j,k,nS1,nS2
    end

  def cp_cfp_HandleSuccess_3Her(by4Unit,nFiveSum,byEarthFive,byEarthIndex,nEarthArray,fSkyArray,m,i,j,k,nS1,nS2,nSkyLevel,nEarthType)
    nFiveSum[m] += 9
        fSkyArray[nS1] = fSkyArray[nS2] = 24
    nEarthType = cp_SetCombineParm(i, j, k, COMBINE_2,nEarthType)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    n = nDayEarthIndex
    if (n == i || n == j) then
      byEarthFive[2] = m
      byEarthIndex[2] = @@ByChangeFive[m][0]
      by4Unit[2] = 0xFF
            nEarthArray[2] = 0
      if (n == i) then
        i = -1
      else
        j = -1
      end
    elsif (n == k) then
      k = -1
      by4Unit[2] = 0xFF
            nEarthArray[2] = 0
    end
    (0..4).each do |l|
      n = by4Unit[l]
      if (n < 12 && n >= 0) then
        if (n == i || n == j || n == k) then
          by4Unit[l] = 0xFF
                    nEarthArray[l] = 0
          if (n == i || n == j) then
            if (l < 4) then
                            byEarthFive[l] = m
                byEarthIndex[l] = @@ByChangeFive[m][0]
                        end
            if (n == i) then
              i = -1
            else
              j = -1
            end
          elsif (n == k) then
            k = -1
          end
        end
      end
    end
    nSkyLevel[0] = 12
    nSkyLevel[1] = 15
    nSkyLevel[2] = 18

    return nFiveSum,fSkyArray,byEarthFive,byEarthIndex,by4Unit,nEarthArray,nSkyLevel,nEarthType
  end

    def cp_cfp_nEarthArray()
      if (ddg_SkyEarth() == Sky::SKY) then
        return cp_cfp_nEarthArray_Sky()
      else
        return cp_cfp_nEarthArray_Earth()
      end
  end

    def cp_cfp_nEarthArray_Sky()
  end

    def cp_cfp_nEarthArray_Earth()
        nEarthArray = Array.new(5,16)
      return nEarthArray
    end

    # 半方會局 byEarthMask
    def cp_cfp_byEarthMask_HalfDir(byEarthMask,by4Unit)
    (0...10).each do |i|
      byEarthMask[i] = 0
    end
    (0...5).each do |i|
      m = by4Unit[i]
      if (m >= 0 && m < 12) then
        byEarthMask[m] = byEarthMask[m] + 1
      end
    end

    return byEarthMask
    end

    # 半方會局
    def cp_cfp_HalfDir(nFiveSum,byEarthMask,by4Unit,byCombType,byType,nCombineNum,fSkyArray,nEarthArray,nEarthType)
      byEarthMask = cp_cfp_byEarthMask_HalfDir(byEarthMask,by4Unit)
    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |i|
      n = by4Unit[i]
          k = byCombType[i]
          l = byCombType[i - 1]
          m = by4Unit[i - 1]

          bSuccess = false
          if ((m == 2 && n == 3) || (m == 3 && n == 2)) then # 木局
        bSuccess = true
          elsif ((m == 5 && n == 6) || (m == 6 && n == 5)) then # 火局
        bSuccess = true
      elsif ((m == 8 && n == 9) || (m == 9 && n == 8)) then # 金局
        bSuccess = true
      elsif ((m == 11 && n == 0) || (m == 0 && n == 11)) then # 水局
        bSuccess = true
      end
          if (bSuccess && byType[i - 1] == 0 && byType[i] == 0 && l != 2 && k != 2) then
        if (l == 1 || k == 1) then
                  if (nCombineNum > 1) then
                      if (i == 1) then
                          if ((byCombType[2] == 1 && by4Unit[2] != 0xFF) ||
                              (byCombType[3] == 1 && by4Unit[3] != 0xFF)) then
                              bSuccess = true
                          end
                      elsif (i == 2) then
                          if ((byCombType[0] == 1 && by4Unit[0] != 0xFF) ||
                              (byCombType[3] == 1 && by4Unit[3] != 0xFF)) then
                              bSuccess = true
                          end
                      else
                          if (by4Unit[0] == 0xFF && by4Unit[1] == 0xFF) then
                              bSuccess = false
                          elsif (by4Unit[0] == 0 && byCombType[0] == 1) then
                              bSuccess = true
                          elsif (by4Unit[1] == 0 && byCombType[1] == 1) then
                              bSuccess = true
                          else
                              bSuccess = false
                          end
                      end
                  else
                      bSuccess = false
                  end
                  if (!bSuccess) then
                      if (byCombType[i - 1] == 1) then
                          byCombType[i - 1] = 2
                      elsif (byCombType[i] == 1) then
                          byCombType[i] = 2
                      end
                  end
              end
              if (bSuccess) then
                  if ((m == 2 && n == 3) || (m == 3 && n == 2)) then # 木局
            byEarthMask[m] = byEarthMask[m] - 1
            byEarthMask[n] = byEarthMask[n] - 1
            nEarthType = cp_SetCombineParm(m, n, -1, COMBINE_3,nEarthType)
            m = 1
                      fSkyArray[0] = fSkyArray[1] = 24
          elsif ((m == 5 && n == 6) || (m == 6 && n == 5)) then # 火局
            byEarthMask[m] = byEarthMask[m] - 1
            byEarthMask[n] = byEarthMask[n] - 1
            nEarthType = cp_SetCombineParm(m, n, -1, COMBINE_3,nEarthType)
            m = 2
                      fSkyArray[2] = fSkyArray[3] = 24
          elsif ((m == 8 && n == 9) || (m == 9 && n == 8)) then # 金局
            byEarthMask[m] = byEarthMask[m] - 1
            byEarthMask[n] = byEarthMask[n] - 1
            nEarthType = cp_SetCombineParm(m, n, -1, COMBINE_3,nEarthType)
            m = 4
                      fSkyArray[6] = fSkyArray[7] = 24
          elsif ((m == 11 && n == 0) || (m == 0 && n == 11)) then # 水局
            byEarthMask[m] = byEarthMask[m] - 1
            byEarthMask[n] = byEarthMask[n] - 1
            nEarthType = cp_SetCombineParm(m, n, -1, COMBINE_3,nEarthType)
            m = 0
                      fSkyArray[8] = fSkyArray[9] = 24
          else
            m = -1
          end
                  if (m >= 0) then
            by4Unit[i] = 0xFF
            by4Unit[i - 1] = 0xFF
                      nEarthArray[i] = 0
                      nEarthArray[i - 1] = 0
            nFiveSum[m] += 8
          end
              end
      end
      end

    m = by4Unit[4]
    if (m >= 0 && m < 12) then
      bStop = false
      (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
        if (!bStop) then
          n = by4Unit[i]
          if (byType[i] == 0 && byEarthMask[m] > 0 && (n >= 0 && n < 12) && byCombType[i] != 2) then
            if ((m == 2 && n == 3) || (m == 3 && n == 2)) then # 木局
              byEarthMask[m] = byEarthMask[m] - 1
              byEarthMask[n] = byEarthMask[n] - 1
              m = 1
                        fSkyArray[0] += 64.0 / 3
                        fSkyArray[8] += 32.0 / 3
            elsif ((m == 5 && n == 6) || (m == 6 && n == 5)) then # 火局
              byEarthMask[m] = byEarthMask[m] - 1
              byEarthMask[n] = byEarthMask[n] - 1
              m = 2
                        fSkyArray[3] += 64.0 / 3
                        fSkyArray[1] += 32.0 / 3
            elsif ((m == 8 && n == 9) || (m == 9 && n == 8)) then # 金局
              byEarthMask[m] = byEarthMask[m] - 1
              byEarthMask[n] = byEarthMask[n] - 1
              m = 4
                        fSkyArray[7] += 64.0 / 3
                        fSkyArray[4] += 32.0 / 3
            elsif ((m == 11 && n == 0) || (m == 0 && n == 11)) then # 水局
              byEarthMask[m] = byEarthMask[m] - 1
              byEarthMask[n] = byEarthMask[n] - 1
              m = 0
                        fSkyArray[8] += 64.0 / 3
                        fSkyArray[6] += 32.0 / 3
            else
              m = -1
            end
            if (m >= 0) then
              nEarthType = cp_SetCombineParm(n, -1, -1, COMBINE_3,nEarthType)
              by4Unit[i] = 0xFF
              by4Unit[4] = 0xFF
              bStop = true
              i = 4
              nFiveSum[m] += 8
                        nEarthArray[i] = 0
            end
          end
        end
      end
    end
    return nFiveSum,nEarthArray,by4Unit,fSkyArray,byEarthMask,byCombType,nEarthType
  end

  # 地支六合
    def cp_cfp_Earth6Her(nFiveSum,byEarthMask,by4Unit,byCombType,byType,nCombineNum,fSkyArray,nEarthArray,byEarthFive,byEarthIndex,nEarthType)
    k = 0
      mp_byEarthFive = cp_mp_getByEarthFive()
      mp_byEarthIndex = cp_mp_getByEarthIndex()
    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |i|
      n = by4Unit[i]
          m = by4Unit[i - 1]
      if (byType[i - 1] == 0 && byType[i] == 0 &&
        (m + n == 13 || m + n == 1) &&
              byCombType[i - 1] != 2 && byCombType[i] != 2) then
        if (byCombType[i - 1] == 1 ||  byCombType[i] == 1) then
                  if (nCombineNum > 1) then
                      if (i == 1) then
                          if ((byCombType[2] == 1 && by4Unit[2] != 0xFF) ||
                              (byCombType[3] == 1 && by4Unit[3] != 0xFF)) then
                              bSuccess = true
                          end
                      elsif (i == 2) then
                          if ((byCombType[0] == 1 && by4Unit[0] != 0xFF) ||
                              (byCombType[3] == 1 && by4Unit[3] != 0xFF)) then
                              bSuccess = true
                          end
                      else
                          if (by4Unit[0] == 0xFF && by4Unit[1] == 0xFF) then
                              bSuccess = false
                          elsif (by4Unit[0] == 0 && byCombType[0] == 1) then
                              bSuccess = true
                          elsif (by4Unit[1] == 0 && byCombType[1] == 1) then
                              bSuccess = true
                          else
                              bSuccess = false
                          end
                      end
                  else
                      bSuccess = false
                  end
                  if (!bSuccess) then
                      if (byCombType[i - 1] == 1) then
                          byCombType[i - 1] = 2
                      elsif (byCombType[i] == 1) then
                          byCombType[i] = 2
                      end
                  end
              else
                  bSuccess = true
              end
              if (bSuccess) then
                  l = [m, n].min
            if (l == 0) then # 化為土
              l = 3
                      fSkyArray[5] += 32
            elsif (l == 2) then # 化為木
              l = 1
                      fSkyArray[0] += 64.0 / 3
                      fSkyArray[8] += 32.0 / 3
            elsif (l == 3) then # 化為火
              l = 2
                      fSkyArray[3] += 64.0 / 3
                      fSkyArray[1] += 32.0 / 3
            elsif (l == 4) then # 化為金
              l = 4
                      fSkyArray[7] += 64.0 / 3
                      fSkyArray[4] += 32.0 / 3
            elsif (l == 5) then # 化為水
              if (byEarthMask[2] > 0) then # 三刑
                          l = -1
                      else
                          l = 0
                          fSkyArray[8] += 64.0 / 3
                          fSkyArray[6] += 32.0 / 3
                      end
                  elsif (l == 6) then # 化為火
              l = 2
                      fSkyArray[3] += 32
            else
              l = -1
          end
            if (l >= 0) then
              byEarthMask[m] = byEarthMask[m] - 1
                byEarthMask[n] = byEarthMask[n] - 1
                      m = l
                      nEarthType = cp_SetCombineParm(by4Unit[i - 1],by4Unit[i], -1, COMBINE_5,nEarthType)
              by4Unit[i] = 0xFF
              by4Unit[i - 1] = 0xFF
                      nEarthArray[i] = nEarthArray[i - 1] = 0
              nFiveSum[m] += 4
              byEarthFive[i] = byEarthFive[i - 1] = l

              if (mp_byEarthFive[i] != l && mp_byEarthFive[i - 1] != l) then
                if (l == 2) then
                  if (mp_byEarthIndex[i] == 3) then
                    byEarthIndex[i] = @@ByChangeFive[l][1]
                    byEarthIndex[i - 1] = @@ByChangeFive[l][0]
                  else
                    byEarthIndex[i] = @@ByChangeFive[l][0]
                    byEarthIndex[i - 1] = @@ByChangeFive[l][1]
                  end
                else
                  if (mp_byEarthIndex[i] == 5) then
                    byEarthIndex[i] = @@ByChangeFive[l][0]
                    byEarthIndex[i - 1] = @@ByChangeFive[l][1]
                  else
                    byEarthIndex[i] = @@ByChangeFive[l][1]
                    byEarthIndex[i - 1] = @@ByChangeFive[l][0]
                  end
                end
              elsif (mp_byEarthFive[i] != l) then
              if (byEarthIndex[i - 1] == @@ByChangeFive[l][1]) then
                byEarthIndex[i] = @@ByChangeFive[l][0]
              else
                byEarthIndex[i] = @@ByChangeFive[l][1]
              end
            else
              if (byEarthIndex[i] == @@ByChangeFive[l][1])
                byEarthIndex[i - 1] = @@ByChangeFive[l][0]
              else
                byEarthIndex[i - 1] = @@ByChangeFive[l][1]
              end
            end
              n = -1
            end
              else
              n = -1
          end
          end
      m = n
    end
    m = by4Unit[4]
    if (m >= 0 && m < 12) then
      bStop = false
      (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
        if (!bStop) then
          n = by4Unit[i]
                if (byType[i] == 0 && byCombType[i] == 1 &&
                    n >= 0 && n < 12 && (m + n == 13 || m + n == 1)) then
            l = [m, n].min
            if (l == 0) then # 化為土
              l = 3
                        fSkyArray[5] += 32
            elsif (l == 2) then # 化為木
              l = 1
                        fSkyArray[0] += 22
                        fSkyArray[8] += 10
            elsif (l == 3) then # 化為火
              l = 2
                        fSkyArray[3] += 22
                        fSkyArray[1] += 10
            elsif (l == 4) then # 化為金
              l = 4
                        fSkyArray[7] += 22
                        fSkyArray[4] += 10
            elsif (l == 5) then # 化為水
              if (byEarthMask[2] > 0) then # 三刑
                            l = -1
                        else
                            l = 0
                            fSkyArray[8] += 22
                            fSkyArray[6] += 10
                        end
                    elsif (l == 6) then # 化為火
              l = 2
                        fSkyArray[3] += 32
            else
              l = -1
            end
            if (l >= 0) then
              byEarthMask[m] = byEarthMask[m] - 1
                byEarthMask[n] = byEarthMask[n] - 1
                        m = l
                        nEarthType = cp_SetCombineParm(n, -1, -1, COMBINE_5,nEarthType)
              by4Unit[i] = 0xFF
              by4Unit[4] = 0xFF
                        nEarthArray[i] = nEarthArray[4] = 0
              nFiveSum[m] += 4
              byEarthFive[i] = l
              if (mp_byEarthFive[i] != l) then
                if (m == @@ByChangeFive[l][0]) then
                                byEarthIndex[i] = @@ByChangeFive[l][1]
                            elsif (m == @@ByChangeFive[l][1]) then
                                byEarthIndex[i] = @@ByChangeFive[l][0]
                            end
                        end
              bStop = true
            end
          end
        end
      end
    end

    return nFiveSum,nEarthArray,by4Unit,fSkyArray,byEarthMask,byCombType,byEarthFive,byEarthIndex,nEarthType
  end

  # 扣分
    # 三刑
    def cp_cfp_Earth3Hsin(nMFiveSum,byEarthMask,by4Unit,fSkyArray,nEarthArray,nEarthType)
    bSuccess = false
    if (byEarthMask[5] >= 1 && byEarthMask[8] >= 1 &&
      byEarthMask[2] >= 1) then
      nMFiveSum[2] += 4   # 巳火
      nMFiveSum[4] += 4   # 申金
      nMFiveSum[1] += 4   # 寅木
      bSuccess = true
      nEarthType = cp_SetKeParm(5, 8, 2, COMBINE_7,nEarthType)
      i = 5
      j = 8
      k = 2
          fSkyArray[6] += 8
          fSkyArray[2] += 4
          fSkyArray[4] += 4
    elsif (byEarthMask[1] >= 1 && byEarthMask[10] >= 1 &&
      byEarthMask[7] >= 1) then
      nMFiveSum[3] += 4   # 丑土
      nMFiveSum[3] += 4   # 戌土
      nMFiveSum[3] += 4   # 未土
      bSuccess = true
      nEarthType = cp_SetKeParm(1, 10, 7, COMBINE_7,nEarthType)
      i = 1
      j = 10
      k = 7
          fSkyArray[4] += 8
          fSkyArray[5] += 4
          fSkyArray[3] += 4
    elsif (byEarthMask[0] >= 1 && byEarthMask[6] >= 1 &&
      byEarthMask[3] >= 1) then
      nMFiveSum[0] += 4   # 子水
      nMFiveSum[2] += 4   # 午火
      nMFiveSum[1] += 4   # 卯木
      bSuccess = true
      nEarthType = cp_SetKeParm(0, 6, 3, COMBINE_7,nEarthType)
      i = 0
      j = 6
      k = 3
          fSkyArray[1] += 8
          fSkyArray[8] += 4
          fSkyArray[3] += 4
    end
    if (bSuccess) then
      byEarthMask[i] = byEarthMask[i] - 1
      byEarthMask[j] = byEarthMask[j] - 1
      byEarthMask[k] = byEarthMask[k] - 1
      (0...5).each do |l|
        n = by4Unit[l]
        if (i < 12 && n == i) then
          i = 255
          by4Unit[l] = 0xFF
                  nEarthArray[l] = 0
        elsif (j < 12 && n == j) then
          j = 255
          by4Unit[l] = 0xFF
                  nEarthArray[l] = 0
        elsif (k < 12 && n == k) then
          k = 255
          by4Unit[l] = 0xFF
                  nEarthArray[l] = 0
        end
      end
    end
    return nMFiveSum,nEarthArray,by4Unit,fSkyArray,byEarthMask,nEarthType
  end

  # 六沖
    def cp_cfp_Earth6Chuon(nMFiveSum,byEarthMask,by4Unit,fSkyArray,nEarthArray,nEarthType)
    m = by4Unit[0]
    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |i|
      n = by4Unit[i]
      if (m < 12 && n < 12 && (m - n == 6 || n - m == 6)) then
        if (i < 3) then
                  k = by4Unit[i + 1]
                  if (k > 12 || (k != m && k != n)) then
                      k = by4Unit[4]
                  end
              else
                  k = by4Unit[4]
              end
              if (k < 12 && (k == m || k == n) && (k == 0 || k == 2 || k == 3 || k == 5)) then
                  if (k == 0) then
                      fSkyArray[9] += 0
                  elsif (k == 2) then
                      fSkyArray[6] += 0
                  elsif (k == 3) then
                      fSkyArray[7] += 0
                  elsif (k == 5) then
                      fSkyArray[8] += 0
                  end
                  by4Unit[4] = 0xFF
              else
                  k = [m, n].min
                  if (k == 0) then
                      fSkyArray[9] += 8
                  elsif (k == 1) then
                      fSkyArray[5] += 8
                  elsif (k == 2) then
                      fSkyArray[6] += 8
                  elsif (k == 3) then
                      fSkyArray[7] += 8
                  elsif (k == 4) then
                      fSkyArray[4] += 8
                  elsif (k == 5) then
                      fSkyArray[8] += 8
                  end
              end
              byEarthMask[m] = byEarthMask[m] - 1
        byEarthMask[n] = byEarthMask[n] - 1
        nEarthType = cp_SetKeParm(m, n, -1, COMBINE_6,nEarthType)
        nMFiveSum[@@EarthFiveTable[m]] += 4
        nMFiveSum[@@EarthFiveTable[n]] += 4
        n = 255
        by4Unit[i] = 0xFF
        by4Unit[i - 1] = 0xFF
              nEarthArray[i] = nEarthArray[i - 1] = 0
      end
      m = n
    end
    m = by4Unit[4]
    if (m < 12) then
      bStop = false
      (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
        if (!bStop) then
          n = by4Unit[i]
          if (n < 12 && (m - n == 6 || n - m == 6)) then
            byEarthMask[m] = byEarthMask[m] - 1
            byEarthMask[n] = byEarthMask[n] - 1
            nEarthType = cp_SetKeParm(m, n, -1, COMBINE_6,nEarthType)
            nMFiveSum[@@EarthFiveTable[m]] += 4
            nMFiveSum[@@EarthFiveTable[n]] += 4
            by4Unit[i] = 0xFF
            by4Unit[4] = 0xFF
                    nEarthArray[i] = nEarthArray[4] = 0
                    k = [m, n].min
                    if (k == 0) then
                        fSkyArray[9] += 8
                    elsif (k == 1) then
                        fSkyArray[5] += 8
                    elsif (k == 2) then
                        fSkyArray[6] += 8
                    elsif (k == 3) then
                        fSkyArray[7] += 8
                    elsif (k == 4) then
                        fSkyArray[4] += 8
                    elsif (k == 5) then
                        fSkyArray[8] += 8
                    end
            bStop = true
          end
        end
      end
    end

    return nMFiveSum,nEarthArray,by4Unit,fSkyArray,byEarthMask,nEarthType
  end

  # 相刑 自刑
    def cp_cfp_SelfHsin(nMFiveSum,byEarthMask,by4Unit,fSkyArray,nEarthArray,nEarthType)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      m = by4Unit[i]
      if (m < 12) then
        bStop = false
        (i+1...5).each do |j|
          if (!bStop) then
            n = by4Unit[j]
            k = [m, n].min
            l = [m, n].max
                    if (n < 12) then
                        if (m == n && (m == 4 || m == 6 || m == 9 || m == 11)) then # 自刑
                            k = -1
                            if (j == 4) then
                              nEarthType = cp_SetKeParm(m, -1, -1, COMBINE_8,nEarthType)
                            else
                                nEarthType = cp_SetKeParm(m, n, -1, COMBINE_8,nEarthType)
                            end
                            if (m == 4) then
                                fSkyArray[4] += 12
                                fSkyArray[1] += 8
                                nEarthArray[i] = nEarthArray[j] = 0
                            elsif (m == 6) then
                                fSkyArray[3] += 12
                                fSkyArray[5] += 8
                                nEarthArray[i] = nEarthArray[j] = 0
                            elsif (m == 9) then
                                fSkyArray[7] += 20
                                nEarthArray[i] = nEarthArray[j] = 0
                            elsif (m == 11) then
                                fSkyArray[8] += 12
                                fSkyArray[0] += 8
                                nEarthArray[i] = nEarthArray[j] = 0
                            end
                        elsif ((k == 2 && (l == 5 || l == 8)) || (k == 5 && l == 8)) then # 無恩之刑
                            k = -1
                            if (j == 4) then
                              nEarthType = cp_SetKeParm(m, -1, -1, COMBINE_9,nEarthType)
                            else
                                nEarthType = cp_SetKeParm(m, n, -1, COMBINE_9,nEarthType)
                            end
                        elsif ((k == 1 && (l == 7 || l == 10)) || (k == 7 && l == 10)) then # 恃勢之刑
                            k = -1
                            if (j == 4) then
                              nEarthType = cp_SetKeParm(m, -1, -1, COMBINE_10,nEarthType)
                            else
                                nEarthType = cp_SetKeParm(m, n, -1, COMBINE_10,nEarthType)
                            end
                        elsif ((k == 0 && (l == 3 || l == 6)) || (k == 3 && l == 6)) then # 無禮之刑
                            k = -1
                            if (j == 4) then
                              nEarthType = cp_SetKeParm(m, -1, -1, COMBINE_11,nEarthType)
                            else
                                nEarthType = cp_SetKeParm(m, n, -1, COMBINE_11,nEarthType)
                            end
                        end
                        if (k == -1) then
                by4Unit[j] = 0xFF
                  byEarthMask[i] = byEarthMask[i] - 1
                  byEarthMask[j] = byEarthMask[j] - 1
                  nMFiveSum[@@EarthFiveTable[m]] += 2
                  by4Unit[i] = 0xFF
                  bStop = true
              end
                    end
                end
        end
      end
    end
    return nMFiveSum,nEarthArray,by4Unit,fSkyArray,byEarthMask,nEarthType
  end

  # 六害
    def cp_cfp_Earth6Hai(nMFiveSum,by4Unit,fSkyArray,nEarthArray,nEarthType)
    m = by4Unit[0]
    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |i|
      n = by4Unit[i]
      if (m < 12 && n < 12 && (m + n == 7 || m + n == 19)) then
        nEarthType = cp_SetKeParm(m, n, -1, COMBINE_10,nEarthType)
        nMFiveSum[@@EarthFiveTable[m]] += 2
        nMFiveSum[@@EarthFiveTable[n]] += 2
              k = [m, n].min
              if (k == 0) then
                  fSkyArray[5] += 12
                  fSkyArray[1] += 8
              elsif (k == 1) then
                  fSkyArray[5] += 12
                  fSkyArray[7] += 8
              elsif (k == 2) then
                  fSkyArray[2] += 12
                  fSkyArray[4] += 8
              elsif (k == 3) then
                  fSkyArray[1] += 12
                  fSkyArray[9] += 8
              elsif (k == 8) then
                  fSkyArray[8] += 12
                  fSkyArray[6] += 8
              elsif (k == 9) then
                  fSkyArray[7] += 12
                  fSkyArray[4] += 8
              end
        n = 255
        by4Unit[i] = 0xFF
        by4Unit[i - 1] = 0xFF
              nEarthArray[i] = nEarthArray[i - 1] = 0
      end
      m = n
    end
    m = by4Unit[4]
    if (m < 12) then
      bStop = false
      (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
        if (!bStop) then
          n = by4Unit[i]
          if (n < 12 && (m + n == 7 || m + n == 19)) then
            k = [m, n].min
                    if (k == 0) then
                        fSkyArray[5] += 12
                        fSkyArray[1] += 8
                    elsif (k == 1) then
                        fSkyArray[5] += 12
                        fSkyArray[7] += 8
                    elsif (k == 2) then
                        fSkyArray[2] += 12
                        fSkyArray[4] += 8
                    elsif (k == 3) then
                        fSkyArray[1] += 12
                        fSkyArray[9] += 8
                    elsif (k == 8) then
                        fSkyArray[8] += 12
                        fSkyArray[6] += 8
                    elsif (k == 9) then
                        fSkyArray[7] += 12
                        fSkyArray[4] += 8
                    end
                    nEarthArray[i] = nEarthArray[4] = 0
                    nEarthType = cp_SetKeParm(n, -1, -1, COMBINE_10,nEarthType)
            nMFiveSum[@@EarthFiveTable[n]] += 2
            by4Unit[i] = 0xFF
                    by4Unit[4] = 0xFF
                    bStop = true
          end
        end
      end
    end
    return nMFiveSum,nEarthArray,by4Unit,fSkyArray,nEarthType
  end

  def cp_BigWin_Sky()
    fSkyScore = Array.new(5,16.0)
    fSkyScore1 = Array.new(5,1.0)
    fChungSkyScore = Array.new(4) {Array.new(3,0.0)}
    fChungSkyScore1 = Array.new(4) {Array.new(3,0.0)}
    fOrgChungSkyScore = Array.new(4) {Array.new(3,0.0)}
    fOrgChungSkyScore1 = Array.new(4) {Array.new(3,0.0)}

      nfiSkyIndex = cp_fi_getCurSkyIndex()
      nfiEarthIndex = cp_fi_getCurEarthIndex()
      bySkyFlag = Array.new(5,0)

      byfiSkyIndex = cp_sfi_getBySkyIndex()
      byfiSkyFive = cp_sfi_getBySkyFive()
      nfiSkyType = cp_sfi_getnSkyType()
      byfiChungSky = cp_sfi_getByChungSky()
      byfiChungFive = cp_sfi_getByChungFive()

      nCpSkyType = cp_cp_getnSkyType()

      bySkyIndex = cp_mp_getBySkyIndex()
      byEarthIndex = cp_mp_getByEarthIndex()

    fChungSkyScore,fOrgChungSkyScore,fChungSkyScore1,fOrgChungSkyScore1,bysfiChungSky = cp_bws_fSkyScore_init_fChungSkyScore(fChungSkyScore,fOrgChungSkyScore,fChungSkyScore1,fOrgChungSkyScore1)

      nCombineNum,nSameNum,byCombinFlag,bCanNotCombine = cp_bws_nCombinePar_Org()
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      n = cp_mp_getBySkyIndex(byWhatIdx)
      l = cp_mp_getByEarthIndex(byWhatIdx)
      if ((nfiSkyIndex == n + 5 || n == nfiSkyIndex + 5) && bySkyFlag[4] == 0) then
        # 合化
        bCombine,m = cp_bws_bCombine(n,l,nfiSkyIndex,nfiEarthIndex,byWhatIdx,byfiSkyIndex)

        nCombineNum,bySkyFlag,fSkyScore,fSkyScore1,byfiSkyIndex,byfiSkyFive,nfiSkyType = cp_bws_fSkyScore_Her(bCombine,m,byCombinFlag,bCanNotCombine,nCombineNum,nSameNum,bySkyFlag,fSkyScore,fSkyScore1,byWhatIdx,byfiSkyIndex,byfiSkyFive,nfiSkyType,nCpSkyType)
          elsif (nCpSkyType[byWhatIdx] == -1) then
        fSkyScore,fSkyScore1,bySkyFlag = cp_bws_fSkyScore_NonSkyType(byWhatIdx,bySkyIndex,fSkyScore,fSkyScore1,bySkyFlag)
      end
    end

    # 原來進入
    byfiSkyIndex,byfiSkyFive,nfiSkyType,fSkyScore,fSkyScore1,bySkyFlag = cp_bws_fSkyScore_nOrgCombine(bySkyIndex,byEarthIndex,nCpSkyType,byfiSkyIndex,byfiSkyFive,nfiSkyType,fSkyScore,fSkyScore1,bySkyFlag)

    byfiChungSky,byfiChungFive,fChungSkyScore,fChungSkyScore1 = cp_bws_fSkyScore_fChungSkyScore(nfiSkyIndex,nfiEarthIndex,byEarthIndex,byfiChungSky,bysfiChungSky,byfiChungFive,fChungSkyScore,fOrgChungSkyScore,fChungSkyScore1,fOrgChungSkyScore1)

    nFive = Array.new(5,0)
    fGodScore = Array.new(10,0.0)
    fHRScore = Array.new(10,0.0)
    nfiSick = Array.new(10,0)
    nfiGodValue = Array.new(10,0)
    fGodScore,fHRScore,byfiChungSky,nfiSick,nFive,nfiGodValue = cp_bws_fSkyScore_fGodScore(nfiSkyIndex,byfiSkyIndex,fSkyScore,fSkyScore1,byfiChungSky,fChungSkyScore,fChungSkyScore1,fGodScore,fHRScore,nfiSick,nFive,nfiGodValue)

    nfiFiveSum = nFive.clone
    nfiFiveValue = nFive.clone

    cp_fi_setFiveSum(nfiFiveSum)
    cp_fi_setSkyScore(fSkyScore)
    cp_fi_setfGodScore(fGodScore)
    cp_fi_setnGodValue(nfiGodValue)
    cp_fi_setfHRScore(fHRScore)
    cp_fi_setnSick(nfiSick)
  end

  def cp_bws_nCombinePar_Org()
      nCombineNum = 0
      nSameNum = 0
      byCombinFlag = Array.new(4,0)
      bCanNotCombine = false

      nfiSkyIndex = cp_fi_getCurSkyIndex()
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      n = cp_mp_getBySkyIndex(i)
      # l = cp_mp_getByEarthIndex(i)
      if (nfiSkyIndex == n + 5 || n == nfiSkyIndex + 5) then
        # 合
        byCombinFlag[i] = 0xFF
              nCombineNum += 1
            elsif (n == nfiSkyIndex) then
              nSameNum += 1
      end
    end
      if (nCombineNum != nSameNum + 1) then
          bCanNotCombine = true
        end

    return nCombineNum,nSameNum,byCombinFlag,bCanNotCombine
  end


  def cp_bws_fSkyScore_init_fChungSkyScore(fChungSkyScore,fOrgChungSkyScore,fChungSkyScore1,fOrgChungSkyScore1)
    bysfiChungSky = Array.new(4) {Array.new(3,0)}
    bysfiChungSky
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      m = cp_mp_getByEarthIndex(i)
      (0..2).each do |j|
        n = cp_mp_getByChungSky(i,j)
        if (m == 0 || m == 3 || m == 9) then
          if (j == 0) then
            bysfiChungSky[i][0] = n
            fOrgChungSkyScore1[i][0] = fChungSkyScore1[i][0] = 1.0
            bysfiChungSky[i][1] = bysfiChungSky[i][2] = 0
                  else
                      fOrgChungSkyScore1[i][j] = fChungSkyScore1[i][j] = 0
                  end
        else
          if (n == 0) then
            fChungSkyScore1[i][0] += @@FLevel[j]
                      fOrgChungSkyScore1[i][0] = fChungSkyScore1[i][0]
            fOrgChungSkyScore1[i][j] = fChungSkyScore1[i][j] = 0
            bysfiChungSky[i][j] = 0
          else
            fOrgChungSkyScore1[i][j] = fChungSkyScore1[i][j] = @@FLevel[j]
            bysfiChungSky[i][j] = n
          end
        end
        if (n == 0) then
          fChungSkyScore[i][0] += @@NLevel[j]
                  fOrgChungSkyScore[i][0] = fChungSkyScore[i][0]
                  fOrgChungSkyScore[i][j] = fChungSkyScore[i][j] = 0
        else
                fOrgChungSkyScore[i][j] = fChungSkyScore[i][j] = @@NLevel[j]
              end
      end
    end

    return fChungSkyScore,fOrgChungSkyScore,fChungSkyScore1,fOrgChungSkyScore1,bysfiChungSky
  end


  def cp_bws_bCombine(n,l,nfiSkyIndex,nfiEarthIndex,byWhatIdx,byfiSkyIndex)
    bCombine = false
    m = nfiSkyIndex
    k = nfiEarthIndex
    bCombine,m,n,k,l,nType = cp_mp_CheckCombineSuccess(m,n,k,l)

    if (nType == "m0_k10_l5") then
      if (byfiSkyIndex[byWhatIdx] == 0) then
        m = 4
      else
        m = -1
      end
      bCombine = true
    elsif (nType == "m1_k9_l4") then
      if (byfiSkyIndex[byWhatIdx] == 1) then
        m = 7
      else
        m = -1
      end
      bCombine = true
    elsif (nType == "m2_k8_l1") then
      if (byfiSkyIndex[byWhatIdx] == 2) then
        m = 8
      elsif (byfiSkyIndex[byWhatIdx] == 7) then
        m = 9
      end
      bCombine = true
    elsif (nType == "m3_k7_l2") then
      if (byfiSkyIndex[byWhatIdx] == 3) then
        m = 1
      elsif (byfiSkyIndex[byWhatIdx] == 8) then
        m = 0
      end
      bCombine = true
    elsif (nType == "m4_k6_l5") then
      if (byfiSkyIndex[byWhatIdx] == 4) then
        m = 2
      elsif (byfiSkyIndex[byWhatIdx] == 9) then
        m = 3
      end
      bCombine = true
    end

    return bCombine,m
  end

  def cp_bws_fSkyScore_Her(bCombine,m,byCombinFlag,bCanNotCombine,nCombineNum,nSameNum,bySkyFlag,fSkyScore,fSkyScore1,byWhatIdx,byfiSkyIndex,byfiSkyFive,nfiSkyType,nCpSkyType)
    if (bCombine) then
      if (byCombinFlag[byWhatIdx] == 0xFF && bCanNotCombine) then
                if (nCombineNum > 1 && nSameNum >= 1 && nCpSkyType[byWhatIdx] >= 0) then
                    nCombineNum -= 1
                else
                    bySkyFlag[byWhatIdx] = 0xFF
                    bySkyFlag[4] = 0xFF
                    fSkyScore[byWhatIdx] /= 2
              fSkyScore[4] /= 2
              fSkyScore1[byWhatIdx] /= 2
              fSkyScore1[4] /= 2
                end
            elsif (m >= 0) then
                byfiSkyIndex[byWhatIdx] = m
          byfiSkyFive[byWhatIdx] = @@SkyFiveTable[m]
                nfiSkyType[byWhatIdx] = @@SkyFiveTable[m]
                bySkyFlag[byWhatIdx] = 0xFF
                bySkyFlag[4] = 0xFF
            end
    else
      if (byCombinFlag[byWhatIdx] == 0xFF && bCanNotCombine) then
                fSkyScore[byWhatIdx] /= 2
          fSkyScore[4] /= 2
          fSkyScore1[byWhatIdx] /= 2
          fSkyScore1[4] /= 2
                bySkyFlag[byWhatIdx] = 0xFF
                bySkyFlag[4] = 0xFF
            end
    end
# nil[1]
    return nCombineNum,bySkyFlag,fSkyScore,fSkyScore1,byfiSkyIndex,byfiSkyFive,nfiSkyType
    end

    def cp_bws_fSkyScore_NonSkyType(byWhatIdx,bySkyIndex,fSkyScore,fSkyScore1,bySkyFlag)
      nBetween = cp_fi_nBetween_Sky()
      fBetweenLevel = cp_fi_fBetweenLevel()
        m = @@SkyFiveTable[bySkyIndex[byWhatIdx]]
        bStop = false
        (0..4).each do |j|
          if (!bStop) then
        if (m == nBetween[j]) then
          fSkyScore[byWhatIdx] += @@BetweenLevel[j].to_f
          fSkyScore1[byWhatIdx] += fBetweenLevel[j].to_f
          bStop = true
        end
      end
    end
        bySkyFlag[byWhatIdx] = 0xFF

        return fSkyScore,fSkyScore1,bySkyFlag
  end

  # 原來進入
    def cp_bws_fSkyScore_nOrgCombine(bySkyIndex,byEarthIndex,nCpSkyType,byfiSkyIndex,byfiSkyFive,nfiSkyType,fSkyScore,fSkyScore1,bySkyFlag)
    nOrgCombine = cp_mp_getnOrgCombine()
      if (nOrgCombine >= 1) then
          bCombine = false
          nSkyIndex1 = bySkyIndex[Eightword::ByYearIdx]
        nEarthIndex1 = byEarthIndex[Eightword::ByYearIdx]
      (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |i|
          n = bySkyIndex[i]
          l = byEarthIndex[i]

          bCombine = false
          if (nCpSkyType[i - 1] >= 0 && nCpSkyType[i] >= 0 &&
                  bySkyFlag[i - 1] == 0 && bySkyFlag[i] == 0 &&
                  nSkyIndex1 == n + 5 || n == nSkyIndex1 + 5) then
            # 合化
          bCombine,m,n,k,l,nType = cp_mp_CheckCombineSuccess(nSkyIndex1,n,nEarthIndex1,l)

          if (nType == "m0_k10_l5") then
              if (byfiSkyIndex[i] == 0) then
                byfiSkyIndex[i] = 4
                    byfiSkyFive[i] = @@SkyFiveTable[4]
                            nfiSkyType[i] = @@SkyFiveTable[4]
              else
                byfiSkyIndex[i - 1] = 4
                    byfiSkyFive[i - 1] = @@SkyFiveTable[4]
                            nfiSkyType[i - 1] = @@SkyFiveTable[4]
                        end
              bCombine = true
          elsif (nType == "m1_k9_l4") then
              if (byfiSkyIndex[i] == 1) then
                byfiSkyIndex[i] = 7
                    byfiSkyFive[i] = @@SkyFiveTable[7]
                            nfiSkyType[i] = @@SkyFiveTable[7]
              else
                byfiSkyIndex[i - 1] = 7
                    byfiSkyFive[i - 1] = @@SkyFiveTable[7]
                            nfiSkyType[i - 1] = @@SkyFiveTable[7]
                        end
                        bCombine = true
          elsif (nType == "m2_k8_l1") then
              if (byfiSkyIndex[i] == 2) then
                byfiSkyIndex[i] = 8
                    byfiSkyFive[i] = @@SkyFiveTable[8]
                            byfiSkyIndex[i - 1] = 9
                    byfiSkyFive[i - 1] = @@SkyFiveTable[9]
              elsif (byfiSkyIndex[i] == 7) then
                byfiSkyIndex[i] = 9
                    byfiSkyFive[i] = @@SkyFiveTable[9]
                            byfiSkyIndex[i - 1] = 8
                    byfiSkyFive[i - 1] = @@SkyFiveTable[8]
                        end
                        nfiSkyType[i] = nfiSkyType[i - 1] = @@SkyFiveTable[8]
              bCombine = true
          elsif (nType == "m3_k7_l2") then
              if (byfiSkyIndex[i] == 3) then
                byfiSkyIndex[i] = 1
                    byfiSkyFive[i] = @@SkyFiveTable[1]
                            byfiSkyIndex[i - 1] = 0
                    byfiSkyFive[i - 1] = @@SkyFiveTable[0]
              elsif (byfiSkyIndex[i] == 8) then
                byfiSkyIndex[i] = 0
                    byfiSkyFive[i] = @@SkyFiveTable[0]
                            byfiSkyIndex[i - 1] = 1
                    byfiSkyFive[i - 1] = @@SkyFiveTable[1]
                        end
                        nfiSkyType[i] = nfiSkyType[i - 1] = @@SkyFiveTable[0]
              bCombine = true
          elsif (nType == "m4_k6_l5") then
              if (byfiSkyIndex[i] == 4) then
                byfiSkyIndex[i] = 2
                    byfiSkyFive[i] = @@SkyFiveTable[2]
                            byfiSkyIndex[i - 1] = 3
                    byfiSkyFive[i - 1] = @@SkyFiveTable[3]
              elsif (byfiSkyIndex[i] == 9) then
                byfiSkyIndex[i] = 3
                    byfiSkyFive[i] = @@SkyFiveTable[3]
                            byfiSkyIndex[i - 1] = 2
                    byfiSkyFive[i - 1] = @@SkyFiveTable[2]
                        end
                        nfiSkyType[i] = nfiSkyType[i - 1] = @@SkyFiveTable[2]
              bCombine = true
            end

                  k = 0
                  if (!bCombine && i < 3) then
                      k = bySkyIndex[i + 1]
                      if (k == nSkyIndex1) then
                          k = -1
                      end
                  end
                  if (k != -1) then
                if (!bCombine) then
                  fSkyScore[i] -= 4.5
                  if (fSkyScore[4] > 5) then
                    fSkyScore[4] -= 4.5
                end
                  fSkyScore1[i] -= 0.5
                  if (fSkyScore1[4] > 0.9) then
                    fSkyScore1[4] -= 0.5
                end
                end
                      bySkyFlag[i - 1] = bySkyFlag[i] = 0xFF
                  end
          end
              nSkyIndex1 = bySkyIndex[i]
          nEarthIndex1 = byEarthIndex[i]
        end
      end

      return byfiSkyIndex,byfiSkyFive,nfiSkyType,fSkyScore,fSkyScore1,bySkyFlag
  end

    def cp_bws_fSkyScore_fChungSkyScore(nfiSkyIndex,nfiEarthIndex,byEarthIndex,byfiChungSky,bysfiChungSky,byfiChungFive,fChungSkyScore,fOrgChungSkyScore,fChungSkyScore1,fOrgChungSkyScore1)
      byChungSky = cp_mp_getByChungSky()
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      (0..2).each do |j|
        n = byChungSky[i][j]
        l = byEarthIndex[i]
        if (n > 0) then
          n -= 1
          if (j == 0 && (nfiSkyIndex == n + 5 || n == nfiSkyIndex + 5)) then
            # 合化
            bCombine,m,n,k,l,nType = cp_mp_CheckCombineSuccess(nfiSkyIndex,n,nfiEarthIndex,l)

            if (nType == "m0_k10_l5") then
              if (byChungSky[i][j] == 1) then
                m = 5
              else
                m = -1
              end
              k = 3
              bCombine = true
            elsif (nType == "m1_k9_l4") then
              if (byChungSky[i][j] == 2) then
                m = 8
              else
                m = -1
              end
              k = 4
              bCombine = true
            elsif (nType == "m2_k8_l1") then
              if (byChungSky[i][j] == 3) then
                m = 9
              elsif (byChungSky[i][j] == 8) then
                m = 10
              end
              k = 0
              bCombine = true
            elsif (nType == "m3_k7_l2") then
              if (byChungSky[i][j] == 4) then
                m = 2
              elsif (byChungSky[i][j] == 9) then
                m = 1
              end
              k = 1
              bCombine = true
            elsif (nType == "m4_k6_l5") then
              if (byChungSky[i][j] == 5) then
                m = 3
              elsif (byChungSky[i][j] == 10) then
                m = 4
              end
              k = 2
              bCombine = true
            end

                      if (bCombine && m > 0) then
                          byfiChungSky[i][j] = m
              byfiChungFive[i][j] = k
            elsif (!bCombine) then
              fChungSkyScore[i][j] = fOrgChungSkyScore[i][j] / 2.0
              if (bysfiChungSky[i][j] > 0) then
                fChungSkyScore1[i][j] = fOrgChungSkyScore1[i][j] / 2.0
              end
            end
          else
              nBetween = cp_fi_nBetween_Sky()
              fBetweenLevel = cp_fi_fBetweenLevel()
            m = @@SkyFiveTable[n]
            bStop = false
            (0..4).each do |k|
              if (!bStop) then
                if (m == nBetween[k]) then
                  fChungSkyScore[i][j] += fOrgChungSkyScore[i][j] * fBetweenLevel[k]
                  if (bysfiChungSky[i][j] > 0) then
                    fChungSkyScore1[i][j] += fOrgChungSkyScore1[i][j] * fBetweenLevel[k]
                  end
                  bStop = true
                end
              end
            end
          end
        end
      end
    end

    return byfiChungSky,byfiChungFive,fChungSkyScore,fChungSkyScore1
  end

    def cp_bws_fSkyScore_fGodScore(nfiSkyIndex,byfiSkyIndex,fSkyScore,fSkyScore1,byfiChungSky,fChungSkyScore,fChungSkyScore1,fGodScore,fHRScore,nfiSick,nFive,nfiGodValue)
      byChungSky = cp_mp_getByChungSky()
      bySkyIndex = cp_mp_getBySkyIndex()

    fGodScore[nfiSkyIndex] += fSkyScore[4]
    fHRScore[nfiSkyIndex] += fSkyScore1[4]
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      n = byfiSkyIndex[i]
      fGodScore[n] += fSkyScore[i]
      fHRScore[n] += fSkyScore1[i]
    end

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      (0..2).each do |j|
        n = byfiChungSky[i][j]
              if (n > 0 && n <= 10) then
                  n -= 1
                  fGodScore[n] += fChungSkyScore[i][j]
                  fHRScore[n] += fChungSkyScore1[i][j]
        end
              byfiChungSky[i][j] = byChungSky[i][j]
      end
    end

      nA = nB = 0
      (0..9).each do |i|
      if (fGodScore[i] < 0) then
              fGodScore[i] = 0
          end
          n = (fGodScore[i] + 0.5).to_i
      nfiSick[i] = n
      m = @@SkyFiveTable[i]
      nFive[m] += n
    end

    m = bySkyIndex[Eightword::ByDayIdx]
    n = m % 2
    if (n == 1) then
        (0..9).each do |i|
        nfiGodValue[i] = nfiSick[m]
        m = Sky.Modify(m - 1)
      end
    else
      j = 0
      (0..4).each do |i|
        nfiGodValue[j] = nfiSick[m]
        nfiGodValue[j + 1] = nfiSick[m + 1]
        m = Sky.Modify(m - 2)
        j += 2
      end
    end

    return fGodScore,fHRScore,byfiChungSky,nfiSick,nFive,nfiGodValue
  end
end
