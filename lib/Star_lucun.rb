class Star
  def Star.ziwei_str(key)
    return Pm.GetStr("star.#{key}")
  end
  def Star.ziwei_xing_str(key)
    return Star.ziwei_str("xing.#{key}")
  end
  def Star.pantype_to_key(nPanType)
    return "pt_#{nPanType}"
  end
  def Star.astar_to_key(nStar)
    return "astar_#{nStar}"
  end
  def Star.ziwei_lucun_name(nPanType)
    key = Star.pantype_to_key(nPanType)
    return Star.ziwei_xing_str("lucun.#{key}")
  end
  def Star.ziwei_yangren_name(nPanType)
    key = Star.pantype_to_key(nPanType)
    return Star.ziwei_xing_str("yangren.#{key}")
  end
  def Star.ziwei_tuoluo_name(nPanType)
    key = Star.pantype_to_key(nPanType)
    return Star.ziwei_xing_str("tuoluo.#{key}")
  end
  def Star.ziwei_astar_name(nPanType,nStar,lutype)
    # 1、設本命時，各流盤之祿存不顯示。
    if (lutype == Star::PAN_LU_LIFE) then
      nPanType = Cfate::PAN_NORMAL
    end
    # 2、設定本命、流年、流日之選項，則大祿、月祿不顯示，只顯示本命祿、年祿、日祿之顯示。
    if (lutype == Star::PAN_LU_FLOW) then
      if (nPanType == Cfate::PAN_TENYEAR) then
        nPanType = Cfate::PAN_NORMAL
      elsif (nPanType == Cfate::PAN_FLOWMONTH) then
        nPanType = Cfate::PAN_FLOWYEAR
      elsif ([Cfate::PAN_FLOWTIME,Cfate::PAN_FLOWMIN].include?(nPanType)) then
        nPanType = Cfate::PAN_FLOWDATE
      end
    end
    pt_key = Star.pantype_to_key(nPanType)
    star_key = Star.astar_to_key(nStar)

    return Star.ziwei_xing_str("#{star_key}.#{pt_key}")
  end
  def star_GetLuYangTuoInfos(nPanType,nEarth)
    # 各流運重疊顯示 時，才算疊宮
    if (nPanType == Cfate::PAN_NORMAL || @dis_FlowLYT != Star::STAR_DIS_FLOW_DIE) then
      return []
    end
    a = Array.new
    # 1、設本命時，各流盤之祿存不顯示。
    if (@par_LuType == Star::PAN_LU_LIFE) then
      return []
    # 2、設定本命、流年、流日之選項，則大祿、月祿不顯示，只顯示本命祿、年祿、日祿之顯示。
    elsif (@par_LuType == Star::PAN_LU_FLOW) then
      pt_array = Array.new
      if ([Cfate::PAN_FLOWYEAR,Cfate::PAN_FLOWMONTH].include?(nPanType)) then
        pt_array = [Cfate::PAN_NORMAL]
      elsif ([Cfate::PAN_FLOWDATE].include?(nPanType)) then
        pt_array = [Cfate::PAN_NORMAL,Cfate::PAN_FLOWYEAR]
      elsif ([Cfate::PAN_FLOWTIME,Cfate::PAN_FLOWMIN].include?(nPanType)) then
        pt_array = [Cfate::PAN_NORMAL,Cfate::PAN_FLOWYEAR,Cfate::PAN_FLOWDATE]
      end
      pt_array.each do |pt|
        a += star_luyangtuo_starinfo(pt,nEarth,19) # 祿存
        a += star_luyangtuo_starinfo(pt,nEarth,20) # 羊刃
        a += star_luyangtuo_starinfo(pt,nEarth,21) # 陀羅
      end
      return a
    else # PAN_LU_CALD
      # 一定從本命盤開始顯示
      first_pt = 0
      # 流時 流分不顯示，只顯示流日以上的疊宮
      # 目前盤的祿存在原來的資料中就有，名字也在其中修改。
      # 其他的盤在這裡取得，所以要 first_pt...last_pt ...表示目前盤不抓
      last_pt = nPanType >= 5 ? 5 : nPanType
      (first_pt...last_pt).each do |pt|
        a += star_luyangtuo_starinfo(pt,nEarth,19) # 祿存
        a += star_luyangtuo_starinfo(pt,nEarth,20) # 羊刃
        a += star_luyangtuo_starinfo(pt,nEarth,21) # 陀羅
      end
      return a
    end
  end
  def star_luyangtuo_earth(nPanType,nStar)
    return gHouse_GetAStar_HouseEarth(nPanType,nStar)
  end
  def star_luyangtuo_starinfo(nPanType,nEarth,nStar)
    starInfo = get_A_Star_Info(nPanType,nStar,nEarth)
    if (starInfo == nil) then
      return []
    else
      return [starInfo]
    end
  end

  def star_GetAStarsMioWongInfo(astarInfos)
     aMioWongInfo = Array.new
     astarInfos.each do |starInfo|
      nMioWong = Star.Get_AStarInfo_MioWong(starInfo)
      aMioWong = Array.new
      aMioWong.push(nMioWong == nil ? 99 : nMioWong)
      aMioWong.push(getMioWongStr(nMioWong))
      aMioWong.push(isMioWongNeedOutline?(nMioWong))

      aMioWongInfo.push(aMioWong)
     end
     return aMioWongInfo
  end
end
