
class Eightword
  def yearDisplay(nYear)
    return Xdate.YearDisplay(nYear,@par_YearDisplay)
  end
  def yearDisplayOnlyNumber(nYear)
    return Xdate.YearDisplayOnlyNumber(nYear,@par_YearDisplay)
  end

  def GetWYearMonthDateStr(nWYear,nWMonth,nWDate)
    return Xdate.GetWYearMonthDateStr_WC(nWYear,nWMonth,nWDate,@par_YearDisplay)
  end

  def GetYearStr(nYear)
    return Xdate.GetYearStr_WC(nYear,@par_YearDisplay)
  end


#排盤部分
  @m_nPanType         #排盤類別

  @m_nUserDefinedDate
  def dds_E_YMDLT(y,m,d,l,t)
    dds_EY(y)
    dds_EM(m)
    dds_ED(d)
    dds_EL(l)
    dds_ET(t)
  end
  def ddg_E_YMDLT()
    return ddg_EY(),ddg_EM(),ddg_ED(),ddg_EL(),ddg_ET()
  end
  def dds_EY(nVal) return @m_nUserDefinedDate[Cfate::EYear] = nVal end
  def dds_EM(nVal) return @m_nUserDefinedDate[Cfate::EMonth] = nVal end
  def dds_ED(nVal) return @m_nUserDefinedDate[Cfate::EDate] = nVal end
  def dds_ET(nVal) return @m_nUserDefinedDate[Cfate::ETime] = nVal end
  def dds_EMI(nVal) return @m_nUserDefinedDate[Cfate::EMinute] = nVal end
  def dds_EL(nVal) return @m_nUserDefinedDate[Cfate::LeapMonth] = nVal end

  def ddg_EY() return @m_nUserDefinedDate[Cfate::EYear] end
  def ddg_EM() return @m_nUserDefinedDate[Cfate::EMonth] end
  def ddg_ED() return @m_nUserDefinedDate[Cfate::EDate] end
  def ddg_ET() return @m_nUserDefinedDate[Cfate::ETime] == nil ? 0 : @m_nUserDefinedDate[Cfate::ETime] end
  def ddg_EMI() return @m_nUserDefinedDate[Cfate::EMinute] == nil ? 0 : @m_nUserDefinedDate[Cfate::EMinute] end
  def ddg_EL() return @m_nUserDefinedDate[Cfate::LeapMonth] end

  def dds_WY(nVal) return @m_nUserDefinedDate[Cfate::WYear] = nVal end
  def dds_WM(nVal) return @m_nUserDefinedDate[Cfate::WMonth] = nVal end
  def dds_WD(nVal) return @m_nUserDefinedDate[Cfate::WDate] = nVal end
  def dds_WH(nVal) return @m_nUserDefinedDate[Cfate::WHour] = nVal end
  def dds_WMI(nVal) return @m_nUserDefinedDate[Cfate::WMinute] = nVal end

  def ddg_WY() return @m_nUserDefinedDate[Cfate::WYear] end
  def ddg_WM() return @m_nUserDefinedDate[Cfate::WMonth] end
  def ddg_WD() return @m_nUserDefinedDate[Cfate::WDate] end
  def ddg_WH() return @m_nUserDefinedDate[Cfate::WHour] == nil ? 0 : @m_nUserDefinedDate[Cfate::WHour] end
  def ddg_WMI() return @m_nUserDefinedDate[Cfate::WMinute] == nil ? 0 : @m_nUserDefinedDate[Cfate::WMinute] end

  def dds_SkyEarth(nVal) @m_nUserDefinedDate[Cfate::SkyEarth] = nVal end
  def ddg_SkyEarth() return @m_nUserDefinedDate[Cfate::SkyEarth] end

  @m_nFlowDate        #流年時間Hash
  def fds_E_YMDL(y,m,d,l)
    fds_EY(y)
    fds_EM(m)
    fds_ED(d)
    fds_EL(l)
  end
  def fds_E_YMDLT(y,m,d,l,t)
    fds_EY(y)
    fds_EM(m)
    fds_ED(d)
    fds_EL(l)
    fds_ET(t)
  end
  def fdg_E_YMDLT()
    return fdg_EY(),fdg_EM(),fdg_ED(),fdg_EL(),fdg_ET()
  end
  def fdg_E_YMDL()
    return fdg_EY(),fdg_EM(),fdg_ED(),fdg_EL()
  end
  #@m_nFlowEYear       #流年農曆年
  def fdg_EY() return @m_nFlowDate[Cfate::EYear] end
  def fds_EY(nVal) @m_nFlowDate[Cfate::EYear] = nVal end
  #@m_nFlowEMonth      #流年農曆月
  def fdg_EM() return @m_nFlowDate[Cfate::EMonth] end
  def fds_EM(nVal) @m_nFlowDate[Cfate::EMonth] = nVal end
  #@m_nFlowEDate       #流年農曆日
  def fdg_ED() return @m_nFlowDate[Cfate::EDate] end
  def fds_ED(nVal) @m_nFlowDate[Cfate::EDate] = nVal end
  #@m_nFlowETime        #流年時
  def fdg_ET() return @m_nFlowDate[Cfate::ETime] end
  def fds_ET(nVal) @m_nFlowDate[Cfate::ETime] = nVal end
  #@m_nFlowEMinute        #流年分
  def fdg_EMI() return @m_nFlowDate[Cfate::EMinute] end
  def fds_EMI(nVal) @m_nFlowDate[Cfate::EMinute] = nVal end
  #@m_bFlowLeap        #流年是否潤月
  def fdg_EL() return @m_nFlowDate[Cfate::LeapMonth] end
  def fds_EL(nVal) @m_nFlowDate[Cfate::LeapMonth] = nVal end

  def fds_W_YMDHm(y,m,d,h,min)
    fds_WY(y)
    fds_WM(m)
    fds_WD(d)
    fds_WH(h)
    fds_WMI(min)
  end
  def fdg_W_YMDHm()
    return fdg_WY(),fdg_WM(),fdg_WD(),fdg_WH(),fdg_WMI()
  end
  def fds_W_YMD(y,m,d)
    fds_WY(y)
    fds_WM(m)
    fds_WD(d)
  end
  def fdg_W_YMDT()
    return fdg_WY(),fdg_WM(),fdg_WD(),fdg_ET()
  end
  def fdg_W_YMD()
    return fdg_WY(),fdg_WM(),fdg_WD()
  end
  #@m_nFlowWYear       #流年西曆年
  def fdg_WY() return @m_nFlowDate[Cfate::WYear] end
  def fds_WY(nVal) @m_nFlowDate[Cfate::WYear] = nVal end
  #@m_nFlowWMonth      #流年西曆月
  def fdg_WM() return @m_nFlowDate[Cfate::WMonth] end
  def fds_WM(nVal) @m_nFlowDate[Cfate::WMonth] = nVal end
  #@m_nFlowWDate       #流年西曆日
  def fdg_WD() return @m_nFlowDate[Cfate::WDate] end
  def fds_WD(nVal) @m_nFlowDate[Cfate::WDate] = nVal end

  def fdg_WT() return (Xdate.Hour2WTime(fdg_WH())) end
  def fds_WH(nVal) return @m_nFlowDate[Cfate::WHour] = nVal end
  def fdg_WH() return @m_nFlowDate[Cfate::WHour] end
  #@m_nFlowWMin         #流年分
  def fdg_WMI() return @m_nFlowDate[Cfate::WMinute] end
  def fds_WMI(nVal) @m_nFlowDate[Cfate::WMinute] = nVal end

  def fds_s_YMDHmS(nSYear,nSMonth,nSDate,nWHour,nWMinute,nSegmentIndex)
    fds_SY(nSYear)
    fds_SM(nSMonth)
    fds_SD(nSDate)
    fds_SH(nWHour)
    fds_SMI(nWMinute)
    fds_SS(nSegmentIndex)
  end
  def fdg_s_YMDHmS()
    return fdg_SY(),fdg_SM(),fdg_SD(),fdg_SH(),fdg_SMI(),fds_SS()
  end
  def fdg_SY() return @m_nFlowDate["SYear"] end
  def fds_SY(nVal) @m_nFlowDate["SYear"] = nVal end
  #@m_nFlowWMonth      #節氣曆月
  def fdg_SM() return @m_nFlowDate["SMonth"] end
  def fds_SM(nVal) @m_nFlowDate["SMonth"] = nVal end
  #@m_nFlowWDate       #節氣曆日
  def fdg_SD() return @m_nFlowDate["SDate"] end
  def fds_SD(nVal) @m_nFlowDate["SDate"] = nVal end
  def fdg_SH() return @m_nFlowDate["SHour"] end
  def fds_SH(nVal) @m_nFlowDate["SHour"] = nVal end
  def fdg_SMI() return @m_nFlowDate["SMinute"] end
  def fds_SMI(nVal) @m_nFlowDate["SMinute"] = nVal end
  def fdg_SS() return @m_nFlowDate["Segment"] end
  def fds_SS(nVal) @m_nFlowDate["Segment"] = nVal end

  #目前時間
  @m_nCurDate
  def cds_W_YMDH(y,m,d,h)
    cds_WY(y)
    cds_WM(m)
    cds_WD(d)
    cds_WH(h)
  end
  def cds_E_YMDL(y,m,d,l)
    cds_EY(y)
    cds_EM(m)
    cds_ED(d)
    cds_EL(l)
  end
  def cds_E_YMDLT(y,m,d,l,t)
    cds_E_YMDL(y,m,d,l)
    cds_ET(t)
  end
  def cdg_E_YMDLT()
    return cdg_EY(),cdg_EM(),cdg_ED(),cdg_EL(),cdg_ET()
  end
  def cdg_W_YMDH()
    return cdg_WY(),cdg_WM(),cdg_WD(),cdg_WH()
  end
  # @m_nCurEYear        #目前農曆年
  def cdg_EY() return @m_nCurDate[Cfate::EYear] end
  def cds_EY(nVal) @m_nCurDate[Cfate::EYear] = nVal end
  # @m_nCurEMonth       #目前農曆月
  def cdg_EM() return @m_nCurDate[Cfate::EMonth] end
  def cds_EM(nVal) @m_nCurDate[Cfate::EMonth] = nVal end
  # @m_nCurEDate        #目前農曆日
  def cdg_ED() return @m_nCurDate[Cfate::EDate] end
  def cds_ED(nVal) @m_nCurDate[Cfate::EDate] = nVal end
  # @m_nCurWYear        #目前西曆年
  def cdg_WY() return @m_nCurDate[Cfate::WYear] end
  def cds_WY(nVal) @m_nCurDate[Cfate::WYear] = nVal end
  # @m_nCurWMonth       #目前西曆月
  def cdg_WM() return @m_nCurDate[Cfate::WMonth] end
  def cds_WM(nVal) @m_nCurDate[Cfate::WMonth] = nVal end
  # @m_nCurWDate        #目前西曆日
  def cdg_WD() return @m_nCurDate[Cfate::WDate] end
  def cds_WD(nVal) @m_nCurDate[Cfate::WDate] = nVal end
  # @m_nCurETime        #目前時
  def cdg_ET() return @m_nCurDate[Cfate::ETime] end
  def cds_ET(nVal) @m_nCurDate[Cfate::ETime] = nVal end
  # @m_nCurHour         #24小時制
  def cdg_WH() return @m_nCurDate[Cfate::WHour] end
  def cds_WH(nVal) @m_nCurDate[Cfate::WHour] = nVal end
  def cdg_WMI() return @m_nCurDate[Cfate::WMinute] end
  def cds_WMI(nVal) @m_nCurDate[Cfate::WMinute] = nVal end
  # @m_nCurMinute       #目前分
  def cdg_EMI() return @m_nCurDate[Cfate::EMinute] end
  def cds_EMI(nVal) @m_nCurDate[Cfate::EMinute] = nVal end
  # @m_bCurLeapMonth    #目前是否潤月
  def cdg_EL() return @m_nCurDate[Cfate::LeapMonth] end
  def cds_EL(nVal) @m_nCurDate[Cfate::LeapMonth] = nVal end

  #流盤使用者設定時間 Hash
  @m_nPanDate
  def pdg_E_YMDLT()
    return pdg_EY(),pdg_EM(),pdg_ED(),pdg_EL(),pdg_ET()
  end
  def pdg_W_YMDH()
    return pdg_WY(),pdg_WM(),pdg_WD(),pdg_WH()
  end
  # @m_nPanEYear        #目前農曆年
  def pdg_EY() return @m_nPanDate[Cfate::EYear] end
  def pds_EY(nVal) @m_nPanDate[Cfate::EYear] = nVal end
  # @m_nPanEMonth       #目前農曆月
  def pdg_EM() return @m_nPanDate[Cfate::EMonth] end
  def pds_EM(nVal) @m_nPanDate[Cfate::EMonth] = nVal end
  # @m_nPanEDate        #目前農曆日
  def pdg_ED() return @m_nPanDate[Cfate::EDate] end
  def pds_ED(nVal) @m_nPanDate[Cfate::EDate] = nVal end
  # @m_nPanWYear        #目前西曆年
  def pdg_WY() return @m_nPanDate[Cfate::WYear] end
  def pds_WY(nVal) @m_nPanDate[Cfate::WYear] = nVal end
  # @m_nPanWMonth       #目前西曆月
  def pdg_WM() return @m_nPanDate[Cfate::WMonth] end
  def pds_WM(nVal) @m_nPanDate[Cfate::WMonth] = nVal end
  # @m_nPanWDate        #目前西曆日
  def pdg_WD() return @m_nPanDate[Cfate::WDate] end
  def pds_WD(nVal) @m_nPanDate[Cfate::WDate] = nVal end
  # @m_nPanETime        #目前時
  def pdg_ET() return @m_nPanDate[Cfate::ETime] end
  def pds_ET(nVal) @m_nPanDate[Cfate::ETime] = nVal end
  # @m_nPanHour         #24小時制
  def pdg_WH() return @m_nPanDate[Cfate::WHour] end
  def pds_WH(nVal) @m_nPanDate[Cfate::WHour] = nVal end
  # @m_nPanMinute       #目前分
  def pdg_WMI() return @m_nPanDate[Cfate::WMinute] end
  def pds_WMI(nVal) @m_nPanDate[Cfate::WMinute] = nVal end
  # @m_bPanLeapMonth    #目前是否潤月
  def pdg_EL() return @m_nPanDate[Cfate::LeapMonth] end
  def pds_EL(nVal) @m_nPanDate[Cfate::LeapMonth] = nVal end

  #個人資訊
  @m_UserInfo #個人資訊 hash
  def uig_FlowAge() fdg_EY() - uig_EY() + 1 end
  def uis_Name()
    return @m_UserInfo[Cfate::Name]
  end
  def uig_Name()
    return @m_UserInfo[Cfate::Name]
  end

  def uis_E_YMDL(y,m,d,l)
    uis_EY(y)
    uis_EM(m)
    uis_ED(d)
    uis_EL(l)
  end
  def uis_E_YMDLT(y,m,d,l,t)
    uis_E_YMDL(y,m,d,l)
    uis_ET(t)
  end

  def uig_E_YMDLT()
    return uig_EY(),uig_EM(),uig_ED(),uig_EL(),uig_ET()
  end

  #    1963年　1月　30日　寅時
  def uig_E_BirthDate_Str()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_EY())
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += Cfate.GetSpace(1)
    if (uig_EL()) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += uig_EM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_ED().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_E_BirthDate_ganzhi_Str()
    # sBuf = Cfate.GetSpace(1)
    sBuf = yearDisplay(uig_EY())
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    # sBuf += Cfate.GetSpace(1)
    if (uig_EL()) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += uig_EM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    # sBuf += Cfate.GetSpace(1)
    sBuf += uig_ED().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end

  def uig_E_BirthDate_Str2()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_EY())
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    if (uig_EL()) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += uig_EM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_ED().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_E_BirthDate_Str3()
    sBuf = yearDisplay(uig_EY())
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    if (uig_EL()) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += uig_EM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_ED().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
  end
  def Eightword.Lunar_Cal_Name()
    return Pm.GetStr("IDS_E_UI_LUNAR_CAL")
  end
  def uig_E_BirthDate_Array()
      y,m,d,l = Xdate.West2East(uig_WY(),uig_WM(),uig_WD())
    a = Array.new
    a.push(Eightword.Lunar_Cal_Name())
    a.push(yearDisplayOnlyNumber(y))
    a.push(Pm.GetStr("IDS_X_YEAR"))
    a.push(m.to_s)
    a.push(Pm.GetStr("IDS_X_MONTH"))
    a.push(d.to_s)
    a.push(Pm.GetStr("IDS_X_DAY"))
    if (@dis_Earth0_Hour == Eightword::DIS_E0HD_LATE) then
      if (uig_WH() == 23) then
        a.push(Pm.GetStr("IDS_E_UI_LATE"))
      elsif (uig_WH() == 0)
        a.push(Pm.GetStr("IDS_E_UI_EARLY"))
      end
    end
    a.push(Earth.GetIndexName(uig_ET()))
    a.push(Pm.GetStr("IDS_X_TIME"))
    if ((uig_ET() != 0) || (@dis_Earth0_Hour != Eightword::DIS_E0HD_LATE)) then
      a.push("")
    end
    if (@func_FiveWang_Display) then
      a.push(Xdate.GetSegmentStr(cp_mp_get8Words(Eightword::Segment)))
      nWYear, nWMonth, nWDate, nHour, nMinute = uig_W_YMDHm()
      nFive = Xdate.GetStandupFive(nWYear, nWMonth, nWDate, nHour, nMinute)
      a.push(Eightword.getFiveName(nFive) + Pm.GetStr("IDS_E_AT_LIN"))
    else
      a.push("")
      a.push("")
    end
    return a
  end
  def uig_E_BirthDate_Array2()
    y,m,d,l = Xdate.West2East(uig_WY(),uig_WM(),uig_WD())
    a = Array.new
    a.push(Eightword.Lunar_Cal_Name())
    if (@dis_Earth0_Hour == Eightword::DIS_E0HD_LATE) then
      a.push(yearDisplayOnlyNumber(y))
      a.push(Pm.GetStr("IDS_X_YEAR"))
      if (l) then
        a.push(Pm.GetStr("IDS_X_LEAP"))
      end
      a.push(m.to_s)
      a.push(Pm.GetStr("IDS_X_MONTH"))
      a.push(d.to_s)
      a.push(Pm.GetStr("IDS_X_DAY"))
      if (uig_WH() == 23) then
        a.push(Pm.GetStr("IDS_E_UI_LATE"))
      elsif (uig_WH() == 0)
        a.push(Pm.GetStr("IDS_E_UI_EARLY"))
      end
      a.push(Earth.GetIndexName(uig_ET()))
      a.push(Pm.GetStr("IDS_X_TIME"))
      if (uig_ET() != 0) then
        a.push("")
      end
    else
      a.push(yearDisplayOnlyNumber(uig_EY()))
      a.push(Pm.GetStr("IDS_X_YEAR"))
      if (uig_EL()) then
        a.push(Pm.GetStr("IDS_X_LEAP"))
      end
      a.push(uig_EM().to_s)
      a.push(Pm.GetStr("IDS_X_MONTH"))
      a.push(uig_ED().to_s)
      a.push(Pm.GetStr("IDS_X_DAY"))
      a.push(Earth.GetIndexName(uig_ET()))
      a.push(Pm.GetStr("IDS_X_TIME"))
      a.push("")
    end
    a.push(Xdate.GetSegmentStr(cp_mp_get8Words(Eightword::Segment)))
    nWYear, nWMonth, nWDate, nHour, nMinute = uig_W_YMDHm()
    nFive = Xdate.GetStandupFive(nWYear, nWMonth, nWDate, nHour, nMinute)
    a.push(Eightword.getFiveName(nFive) + Pm.GetStr("IDS_E_AT_LIN"))

    return a
  end

  #出生年(農曆)
  def uig_EY()
    return @m_UserInfo[Cfate::EYear]
  end
  def uis_EY(nVal)
    @m_UserInfo[Cfate::EYear] = nVal
  end
  #出生月(農曆)
  def uig_EM() return @m_UserInfo[Cfate::EMonth] end
  def uis_EM(nVal) @m_UserInfo[Cfate::EMonth] = nVal end
  #出生日(農曆)
  def uig_ED() return @m_UserInfo[Cfate::EDate] end
  def uis_ED(nVal) @m_UserInfo[Cfate::EDate] = nVal end
  #出生時(地支時，十二個時辰)
  def uig_ET() return @m_UserInfo[Cfate::ETime] end
  def uis_ET(nVal) @m_UserInfo[Cfate::ETime] = nVal end
  #出生分(農曆)
  def uig_EMI() return @m_UserInfo[Cfate::EMinute] end
  def uis_EMI(nVal) @m_UserInfo[Cfate::EMinute] = nVal end
  #出生月是否為潤月
  def uig_EL() return @m_UserInfo[Cfate::LeapMonth] end
  def uis_EL(nVal) @m_UserInfo[Cfate::LeapMonth] = nVal end

  def uis_W_YMD(y,m,d)
    uis_WY(y)
    uis_WM(m)
    uis_WD(d)
  end
  def uig_W_YMD()
    return uig_WY(),uig_WM(),uig_WD()
  end
  def uig_W_YMDH()
    return uig_WY(),uig_WM(),uig_WD(),uig_WH()
  end
  def uig_W_timestamp()
    return Xdate.make_timestamp(uig_WY(),uig_WM(),uig_WD(),uig_WH())
  end

  def uig_W_YMDHm()
    return uig_WY(),uig_WM(),uig_WD(),uig_WH(),uig_WMI()
  end
  def uig_GanZhi_Birth_Str()
    return Xdate.GetLunar8Words_Str(uig_WY(), uig_WM(),uig_WD(), Xdate.Hour2ETime(uig_WH())) #uig_ET())
  end
  def uig_Seg8Words_Str(bSegment=true,nFirstTimeType=Xdate::FIRST_NEXT,nFirstSegment=Xdate::SEGMENT_SPRING,bSpace=false,nSegmentMove=Xdate::SegmentNow)
    return Xdate.GetSeg8Words_Str(uig_WY(), uig_WM(),uig_WD(), uig_WH(),uig_WMI(),bSegment,nFirstTimeType,nFirstSegment,bSpace,nSegmentMove)
  end
  #　　　1963年　2月　23日　4時
  def uig_W_BirthDate_Str()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_WY())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_WM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_WD().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_WH().to_s
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end

  def uig_W_BirthDate_Str2()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_WY())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += uig_WM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_WD().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += uig_WH().to_s
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_W_BirthDate_Str3()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_WY())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += uig_WM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_WD().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
  end
  def uig_W_BirthDate_ganzhishi_Str()
    # sBuf = Cfate.GetSpace(1)
    sBuf = yearDisplay(uig_WY())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    # sBuf += Cfate.GetSpace(1)
    sBuf += uig_WM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    # sBuf += Cfate.GetSpace(1)
    sBuf += uig_WD().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += Xdate.GetETimeStr(uig_ET())
  end

  def Eightword.Solar_Cal_Name()
    return Pm.GetStr("IDS_E_UI_SOLAR_CAL")
  end
  def uig_W_BirthDate_Array()
    a = Array.new
    a.push(Eightword.Solar_Cal_Name())
    a.push(yearDisplayOnlyNumber(uig_WY()))
    a.push(Pm.GetStr("IDS_X_YEAR"))
    a.push(uig_WM().to_s)
    a.push(Pm.GetStr("IDS_X_MONTH"))
    a.push(uig_WD().to_s)
    a.push(Pm.GetStr("IDS_X_DAY"))
    if (uig_WH().between?(13,24)) then
      a.push("PM")
      h = uig_WH() - 12
    else
      a.push("AM")
      h = uig_WH()
    end
    a.push("%02d:%02d" % [h,uig_WMI()])
    return a
  end

  #出生年(西曆)
  def uig_WY() return @m_UserInfo[Cfate::WYear] end
  def uis_WY(nVal) @m_UserInfo[Cfate::WYear] = nVal end
  #出生月(西曆)
  def uig_WM() return @m_UserInfo[Cfate::WMonth] end
  def uis_WM(nVal) @m_UserInfo[Cfate::WMonth] = nVal end
  #出生日(西曆)
  def uig_WD() return @m_UserInfo[Cfate::WDate] end
  def uis_WD(nVal) @m_UserInfo[Cfate::WDate] = nVal end
  #出生時(西曆)
  def uig_WH() return @m_UserInfo[Cfate::WHour] end
  def uis_WH(nVal) @m_UserInfo[Cfate::WHour] = nVal end
  #出生分(西曆)
  def uig_WMI()
    nMinute = @m_UserInfo[Cfate::WMinute] == nil ? 0 : @m_UserInfo[Cfate::WMinute]
    return  nMinute
  end
  def uis_WMI(nVal) @m_UserInfo[Cfate::WMinute] = nVal end
  #性別 true 是女性,false是男性
  def uig_bS_Val() return (uig_bS() ? 1 : 0) end
  def uig_bS() return @m_UserInfo[Cfate::Sex] end
  def uis_bS(nVal) @m_UserInfo[Cfate::Sex] = nVal end
    # 在網頁中，1代表男性（在此為false)，0代表女性(在此為true)
  def uig_bS_Ui_Val() return (uig_bS() ? 0 : 1) end
  def uig_bS_str() return (Pm.GetStr("IDS_S_CUST_MF_#{uig_bS_Ui_Val()}")) end
  #0為陽,1為陰(陰男,陽男,陰女,陽女用)
  def uig_nSIY() return @m_UserInfo[Cfate::SexInYang] end
  def uis_nSIY(nVal) @m_UserInfo[Cfate::SexInYang] = nVal end

  def uis_SY(nVal) @m_UserInfo["SYear"] = nVal end
  def uig_SY() return @m_UserInfo["SYear"] end

  def uig_BigWinStart()
    if (@m_UserInfo[Eightword::U_BigWinStart] != nil) then
      return @m_UserInfo[Eightword::U_BigWinStart].to_i
    else
      return nil
    end
  end
  def uig_UseGodFive()
    if (@m_UserInfo[Eightword::U_UseGodFive] != nil) then
      aUseGodFive = @m_UserInfo[Eightword::U_UseGodFive]
    else
      return nil
    end
  end

  @m_nLargeSan #目前的大限


  # 某人的八字資訊
  @EightwordInfo     # 包含 以下及@AllPanInfo
    #本命資訊
    #h[Eightword::U_LIFE]    @m_nLife    #本命命宮所在地支位置(以地支表示)
    #h[Eightword::U_GOD]    @m_nGod     #本命子年斗君所在地支位置(以地支表示)
    #h[Eightword::U_BODY]    @m_nBody    #本命身宮所在地支位置(以地支表示)
    #h[Eightword::U_SKY]    @m_nSky     #本命(出生年)天干
    #h[Eightword::U_EARTH]    @m_nEarth   #本命(出生年)地支
    #h[Eightword::U_FIVE]   @m_nFive    #本命五行局(2~6)
    #h[Eightword::U_SMALL]  @m_nSmall   #小限Array
    #h[Eightword::U_LARGE]       @m_nLarge   #大限Array
    # 各盤資訊
    #h[Eightword::U_PAN_INFO]       @AllPanInfo   #各盤資訊

  # 各盤資訊
  # A = @AllPanInfo[Cfate::PAN_CNT] of @PanInfo
  @AllPanInfo

    # @PanInfo  #各宮資訊  @PanInfo == @AllPanInfo[@m_nPanType]中
    #身宮位置,sEarthIndex = @PanInfo[Eightword::U_BODY],
    #小限,aSmallSam = h[Eightword::SMALLSAM] 取得小限 Array
    #大限,aLargeSam = h[Eightword::LARGESAM] 取得大限 Array ([起始年,結束年,起始年-結束年字串"]

  #論斷部分


end
