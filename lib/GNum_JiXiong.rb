# coding: UTF-8

class GNum
# 384爻31圖表                
# 吉 3
# 平 2
# 凶 1
# 4位數代表開始 過程 過程 結果 的 吉凶平
# 爻序  開始  過程  過程  結果
  @@JiXiong = {
      "111" => [3, 3, 3, 2],
      "112" => [3, 3, 2, 1],
      "113" => [3, 2, 1, 3],
      "114" => [3, 3, 1, 2],
      "115" => [3, 3, 3, 1],
      "116" => [3, 3, 3, 3],
      "121" => [3, 2, 1, 1],
      "122" => [3, 2, 1, 2],
      "123" => [3, 2, 1, 3],
      "124" => [3, 1, 2, 2],
      "125" => [3, 1, 2, 1],
      "126" => [3, 1, 2, 3],
      "131" => [1, 3, 2, 3],
      "132" => [1, 3, 2, 3],
      "133" => [1, 3, 2, 2],
      "134" => [2, 3, 2, 3],
      "135" => [2, 3, 2, 3],
      "136" => [2, 3, 2, 2],
      "141" => [2, 2, 3, 3],
      "142" => [2, 2, 3, 3],
      "143" => [2, 2, 3, 1],
      "144" => [1, 2, 3, 3],
      "145" => [1, 2, 3, 1],
      "146" => [1, 2, 3, 1],
      "151" => [2, 3, 3, 3],
      "152" => [2, 3, 3, 3],
      "153" => [2, 3, 3, 1],
      "154" => [1, 1, 1, 3],
      "155" => [1, 1, 1, 1],
      "156" => [1, 1, 1, 1],
      "161" => [1, 2, 1, 3],
      "162" => [1, 2, 1, 3],
      "163" => [1, 2, 1, 2],
      "164" => [3, 2, 1, 1],
      "165" => [3, 2, 1, 2],
      "166" => [3, 2, 1, 3],
      "171" => [3, 3, 2, 1],
      "172" => [3, 3, 2, 2],
      "173" => [3, 3, 2, 3],
      "174" => [1, 1, 1, 1],
      "175" => [1, 1, 1, 3],
      "176" => [1, 1, 1, 1],
      "181" => [3, 2, 3, 2],
      "182" => [3, 2, 3, 1],
      "183" => [3, 2, 3, 3],
      "184" => [1, 3, 1, 1],
      "185" => [1, 3, 1, 3],
      "186" => [1, 3, 1, 1],
      "211" => [3, 3, 3, 2],
      "212" => [3, 3, 3, 1],
      "213" => [3, 3, 3, 3],
      "214" => [3, 3, 3, 1],
      "215" => [3, 3, 3, 2],
      "216" => [3, 3, 3, 3],
      "221" => [3, 2, 1, 1],
      "222" => [3, 2, 1, 2],
      "223" => [3, 2, 1, 2],
      "224" => [3, 1, 2, 1],
      "225" => [3, 1, 2, 2],
      "226" => [3, 1, 2, 3],
      "231" => [1, 3, 2, 3],
      "232" => [1, 3, 2, 3],
      "233" => [1, 3, 2, 2],
      "234" => [2, 3, 2, 1],
      "235" => [2, 3, 2, 3],
      "236" => [2, 3, 2, 2],
      "241" => [2, 2, 3, 3],
      "242" => [2, 2, 3, 3],
      "243" => [2, 2, 3, 1],
      "244" => [1, 2, 3, 3],
      "245" => [1, 2, 3, 3],
      "246" => [1, 2, 3, 1],
      "251" => [2, 3, 3, 3],
      "252" => [2, 3, 3, 3],
      "253" => [2, 3, 3, 1],
      "254" => [1, 1, 1, 3],
      "255" => [1, 1, 1, 3],
      "256" => [1, 1, 1, 1],
      "261" => [1, 2, 1, 3],
      "262" => [1, 2, 1, 3],
      "263" => [1, 2, 1, 2],
      "264" => [3, 2, 1, 3],
      "265" => [3, 2, 1, 1],
      "266" => [3, 2, 1, 3],
      "271" => [3, 3, 2, 1],
      "272" => [3, 3, 2, 2],
      "273" => [3, 3, 2, 3],
      "274" => [1, 1, 1, 2],
      "275" => [1, 1, 1, 1],
      "276" => [1, 1, 1, 1],
      "281" => [3, 2, 3, 2],
      "282" => [3, 2, 3, 1],
      "283" => [3, 2, 3, 3],
      "284" => [1, 3, 1, 2],
      "285" => [1, 3, 1, 1],
      "286" => [1, 3, 1, 1],
      "311" => [2, 2, 2, 3],
      "312" => [2, 2, 2, 3],
      "313" => [2, 2, 2, 2],
      "314" => [1, 3, 3, 3],
      "315" => [1, 3, 3, 3],
      "316" => [1, 3, 3, 2],
      "321" => [2, 1, 3, 1],
      "322" => [2, 1, 3, 3],
      "323" => [2, 1, 3, 2],
      "324" => [1, 1, 1, 3],
      "325" => [1, 1, 1, 3],
      "326" => [1, 1, 1, 2],
      "331" => [3, 2, 3, 1],
      "332" => [3, 2, 3, 2],
      "333" => [3, 2, 3, 3],
      "334" => [3, 3, 2, 1],
      "335" => [3, 3, 2, 2],
      "336" => [3, 3, 2, 3],
      "341" => [3, 1, 1, 1],
      "342" => [3, 1, 1, 2],
      "343" => [3, 1, 1, 3],
      "344" => [1, 2, 3, 2],
      "345" => [1, 2, 3, 1],
      "346" => [1, 2, 3, 3],
      "351" => [3, 2, 2, 2],
      "352" => [3, 2, 2, 1],
      "353" => [3, 2, 2, 1],
      "354" => [1, 1, 1, 2],
      "355" => [1, 1, 1, 1],
      "356" => [1, 1, 1, 3],
      "361" => [1, 1, 3, 2],
      "362" => [1, 1, 3, 1],
      "363" => [1, 1, 3, 3],
      "364" => [2, 2, 3, 1],
      "365" => [2, 2, 3, 3],
      "366" => [2, 2, 3, 1],
      "371" => [1, 2, 3, 3],
      "372" => [1, 2, 3, 3],
      "373" => [1, 2, 3, 1],
      "374" => [3, 1, 1, 3],
      "375" => [3, 1, 1, 1],
      "376" => [3, 1, 1, 1],
      "381" => [1, 1, 1, 3],
      "382" => [1, 1, 1, 1],
      "383" => [1, 1, 1, 1],
      "384" => [3, 3, 2, 3],
      "385" => [3, 3, 2, 1],
      "386" => [3, 3, 2, 1],
      "411" => [1, 1, 1, 3],
      "412" => [1, 1, 1, 1],
      "413" => [1, 1, 1, 1],
      "414" => [2, 3, 3, 3],
      "415" => [2, 3, 3, 3],
      "416" => [2, 3, 3, 1],
      "421" => [1, 3, 1, 3],
      "422" => [1, 3, 1, 3],
      "423" => [1, 3, 1, 1],
      "424" => [2, 1, 1, 3],
      "425" => [2, 1, 1, 3],
      "426" => [2, 1, 1, 1],
      "431" => [1, 1, 3, 2],
      "432" => [1, 1, 3, 1],
      "433" => [1, 1, 3, 3],
      "434" => [3, 3, 2, 1],
      "435" => [3, 3, 2, 2],
      "436" => [3, 3, 2, 3],
      "441" => [3, 3, 2, 2],
      "442" => [3, 3, 2, 1],
      "443" => [3, 3, 2, 1],
      "444" => [3, 2, 3, 2],
      "445" => [3, 2, 3, 1],
      "446" => [3, 2, 3, 1],
      "451" => [3, 1, 1, 1],
      "452" => [3, 1, 1, 2],
      "453" => [3, 1, 1, 3],
      "454" => [3, 1, 1, 2],
      "455" => [3, 1, 1, 1],
      "456" => [3, 1, 1, 1],
      "461" => [3, 3, 1, 1],
      "462" => [3, 3, 1, 2],
      "463" => [3, 3, 1, 3],
      "464" => [1, 2, 3, 1],
      "465" => [1, 2, 3, 3],
      "466" => [1, 2, 3, 2],
      "471" => [2, 1, 3, 1],
      "472" => [2, 1, 3, 3],
      "473" => [2, 1, 3, 2],
      "474" => [1, 1, 1, 3],
      "475" => [1, 1, 1, 1],
      "476" => [1, 1, 1, 3],
      "481" => [2, 3, 2, 3],
      "482" => [2, 3, 2, 3],
      "483" => [2, 3, 2, 2],
      "484" => [1, 3, 2, 3],
      "485" => [1, 3, 2, 1],
      "486" => [1, 3, 2, 3],
      "511" => [1, 1, 1, 3],
      "512" => [1, 1, 1, 1],
      "513" => [1, 1, 1, 1],
      "514" => [2, 3, 1, 3],
      "515" => [2, 3, 1, 3],
      "516" => [2, 3, 1, 1],
      "521" => [1, 2, 3, 3],
      "522" => [1, 2, 3, 3],
      "523" => [1, 2, 3, 1],
      "524" => [2, 2, 3, 3],
      "525" => [2, 2, 3, 3],
      "526" => [2, 2, 3, 1],
      "531" => [1, 1, 3, 2],
      "532" => [1, 1, 3, 1],
      "533" => [1, 1, 3, 3],
      "534" => [3, 1, 3, 2],
      "535" => [3, 1, 3, 1],
      "536" => [3, 1, 3, 1],
      "541" => [3, 2, 2, 2],
      "542" => [3, 2, 2, 1],
      "543" => [3, 2, 2, 1],
      "544" => [3, 2, 2, 1],
      "545" => [3, 2, 2, 3],
      "546" => [3, 2, 2, 3],
      "551" => [3, 1, 1, 2],
      "552" => [3, 1, 1, 2],
      "553" => [3, 1, 1, 3],
      "554" => [3, 1, 1, 1],
      "555" => [3, 1, 1, 3],
      "556" => [3, 1, 1, 3],
      "561" => [3, 2, 3, 2],
      "562" => [3, 2, 3, 2],
      "563" => [3, 2, 3, 3],
      "564" => [1, 1, 1, 3],
      "565" => [1, 1, 1, 1],
      "566" => [1, 1, 1, 3],
      "571" => [2, 1, 3, 1],
      "572" => [2, 1, 3, 3],
      "573" => [1, 1, 3, 2],
      "574" => [1, 2, 3, 1],
      "575" => [1, 2, 3, 3],
      "576" => [1, 2, 3, 2],
      "581" => [2, 2, 2, 3],
      "582" => [2, 2, 2, 3],
      "583" => [2, 2, 2, 2],
      "584" => [1, 3, 3, 1],
      "585" => [1, 3, 3, 3],
      "586" => [1, 3, 3, 2],
      "611" => [3, 2, 3, 1],
      "612" => [3, 2, 3, 2],
      "613" => [3, 2, 3, 3],
      "614" => [1, 3, 1, 3],
      "615" => [1, 3, 1, 3],
      "616" => [1, 3, 1, 2],
      "621" => [3, 1, 1, 3],
      "622" => [3, 1, 1, 1],
      "623" => [3, 1, 1, 3],
      "624" => [1, 2, 3, 3],
      "625" => [1, 2, 3, 3],
      "626" => [1, 2, 3, 2],
      "631" => [2, 2, 3, 1],
      "632" => [2, 2, 3, 3],
      "633" => [2, 2, 3, 1],
      "634" => [1, 1, 3, 2],
      "635" => [1, 1, 3, 1],
      "636" => [1, 1, 3, 3],
      "641" => [1, 1, 1, 1],
      "642" => [1, 1, 1, 3],
      "643" => [1, 1, 1, 2],
      "644" => [3, 2, 2, 1],
      "645" => [3, 2, 2, 2],
      "646" => [3, 2, 2, 3],
      "651" => [1, 2, 3, 3],
      "652" => [1, 2, 3, 1],
      "653" => [3, 2, 3, 3],
      "654" => [3, 1, 1, 1],
      "655" => [3, 1, 1, 2],
      "656" => [3, 1, 1, 3],
      "661" => [3, 1, 1, 3],
      "662" => [3, 1, 1, 1],
      "663" => [3, 1, 1, 1],
      "664" => [3, 1, 1, 3],
      "665" => [3, 1, 1, 1],
      "666" => [3, 1, 1, 1],
      "671" => [1, 2, 3, 2],
      "672" => [1, 2, 3, 1],
      "673" => [1, 2, 3, 1],
      "674" => [2, 2, 3, 1],
      "675" => [2, 2, 3, 3],
      "676" => [2, 2, 3, 1],
      "681" => [1, 1, 1, 1],
      "682" => [1, 1, 1, 3],
      "683" => [1, 1, 1, 1],
      "684" => [2, 3, 3, 1],
      "685" => [2, 3, 3, 3],
      "686" => [2, 3, 3, 1],
      "711" => [1, 1, 1, 1],
      "712" => [1, 1, 1, 3],
      "713" => [1, 1, 1, 1],
      "714" => [3, 3, 2, 1],
      "715" => [3, 3, 2, 2],
      "716" => [3, 3, 2, 3],
      "721" => [1, 3, 1, 2],
      "722" => [1, 3, 1, 1],
      "723" => [1, 3, 1, 1],
      "724" => [3, 2, 3, 1],
      "725" => [3, 2, 3, 2],
      "726" => [3, 2, 3, 3],
      "731" => [3, 1, 2, 3],
      "732" => [3, 1, 2, 1],
      "733" => [3, 1, 2, 1],
      "734" => [1, 1, 3, 3],
      "735" => [1, 1, 3, 3],
      "736" => [1, 1, 3, 1],
      "741" => [1, 3, 3, 3],
      "742" => [1, 3, 3, 1],
      "743" => [1, 3, 3, 3],
      "744" => [2, 2, 2, 1],
      "745" => [2, 2, 2, 3],
      "746" => [2, 2, 2, 2],
      "751" => [1, 1, 1, 1],
      "752" => [1, 1, 1, 3],
      "753" => [1, 1, 1, 2],
      "754" => [2, 1, 3, 1],
      "755" => [2, 1, 3, 3],
      "756" => [2, 1, 3, 2],
      "761" => [2, 3, 1, 1],
      "762" => [2, 3, 1, 3],
      "763" => [2, 3, 1, 1],
      "764" => [1, 1, 1, 2],
      "765" => [1, 1, 1, 1],
      "766" => [1, 1, 1, 1],
      "771" => [3, 1, 2, 3],
      "772" => [3, 1, 2, 1],
      "773" => [3, 1, 2, 3],
      "774" => [3, 2, 1, 3],
      "775" => [3, 2, 1, 1],
      "776" => [3, 2, 1, 3],
      "781" => [3, 3, 3, 1],
      "782" => [3, 3, 3, 2],
      "783" => [3, 3, 3, 3],
      "784" => [3, 3, 3, 3],
      "785" => [3, 3, 3, 1],
      "786" => [3, 3, 3, 3],
      "811" => [1, 1, 1, 1],
      "812" => [1, 1, 1, 3],
      "813" => [1, 1, 1, 1],
      "814" => [3, 3, 2, 2],
      "815" => [3, 3, 2, 1],
      "816" => [3, 3, 2, 3],
      "821" => [1, 3, 1, 2],
      "822" => [1, 3, 1, 1],
      "823" => [1, 3, 1, 1],
      "824" => [3, 2, 3, 2],
      "825" => [3, 2, 3, 1],
      "826" => [3, 2, 3, 3],
      "831" => [3, 1, 2, 3],
      "832" => [3, 1, 2, 1],
      "833" => [3, 1, 2, 1],
      "834" => [1, 1, 3, 3],
      "835" => [1, 1, 3, 1],
      "836" => [1, 1, 3, 1],
      "841" => [1, 3, 3, 3],
      "842" => [1, 3, 3, 1],
      "843" => [1, 3, 3, 3],
      "844" => [2, 2, 2, 3],
      "845" => [2, 2, 2, 3],
      "846" => [2, 2, 2, 2],
      "851" => [1, 1, 1, 1],
      "852" => [1, 1, 1, 3],
      "853" => [1, 1, 1, 2],
      "854" => [2, 1, 3, 3],
      "855" => [2, 1, 3, 3],
      "856" => [2, 1, 3, 2],
      "861" => [2, 3, 1, 1],
      "862" => [2, 3, 1, 3],
      "863" => [2, 3, 1, 1],
      "864" => [1, 1, 1, 1],
      "865" => [1, 1, 1, 3],
      "866" => [1, 1, 1, 1],
      "871" => [3, 1, 2, 3],
      "872" => [3, 1, 2, 1],
      "873" => [3, 1, 2, 3],
      "874" => [3, 2, 1, 1],
      "875" => [3, 2, 1, 2],
      "876" => [3, 2, 1, 3],
      "881" => [3, 3, 3, 1],
      "882" => [3, 3, 1, 2],
      "883" => [3, 1, 2, 3],
      "884" => [3, 3, 2, 1],
      "885" => [3, 3, 3, 2],
      "886" => [3, 3, 3, 3]
    }

  def GNum.find_JiXiong(no)
    return @@JiXiong[no]
  end
  def GNum.find_Process(no)
      a = GNum.find_JiXiong(no)
      return GNum.JiXiong_to_Process(a)
  end
  def GNum.JiXiong_to_Process(a)
      aOut = []
      a.each_with_index do |n,i|
        aOut.push("#{i + 1}#{4 - n}")
      end
      return aOut
  end
end
