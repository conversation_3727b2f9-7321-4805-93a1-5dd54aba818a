require("SkyEarthFive_Function.rb")

class Eightword
	# 與 MainPan info有關
	# 客戶資訊
	# 命造(陰男,陽男,陰女,陽女)
	def cp_mp_ui_IYMF()
		sBuf = Pm.GetStr("IDS_S_UI_IY_#{uig_nSIY()}")
		sBuf += Pm.GetStr("IDS_S_UI_MF_#{uig_bS_Val()}")
		return sBuf
	end

	def cp_mp_ui_Animal()
		sBuf = Pm.GetStr("IDS_E_UI_PRE_ANIMAL")
		sBuf += cp_mp_ui_GetAnimal()
		return sBuf

	end
	def cp_mp_ui_YearOld()
		Xdate.GetYearOldStr(Xdate.GetYearOld(uig_SY(),cdg_EY()))
	end
	def cp_mp_ui_GetAnimal()
		nEarth = Earth.EarthIndex2Earth(cp_getAnimalEarthIndex())
		sBuf = Xdate.GetAnimal(nEarth)
		return sBuf
	end
	def cp_mp_ui_GetSolor_BirthDate()
		return uig_W_BirthDate_Array()
	end
	def cp_mp_ui_GetLunar_BirthDate()
		return uig_E_BirthDate_Array()
	end

	# 交大運
	def cp_mp_ui_GetMatchYear()
		a = Array.new
		b = Array.new
		c = Array.new
		b = b + Pm.GetStr("IDS_E_EVERY").split(//)
		b.push(Sky.GetIndexName(cp_mp_getByMatchYear_2(0)))
		b.push(Pm.GetStr("IDS_E_OR"))
		b.push(Sky.GetIndexName(cp_mp_getByMatchYear_2(1)))
		b.push(Pm.GetStr("IDS_X_YEAR"))

		c = c + Xdate.GetSegmentStr(cp_mp_getBySegment()).split(//)
		c.push(Pm.GetStr("IDS_E_AFTER"))
		c.push("%02d" % cp_mp_getByMatchDay())
		c.push(Pm.GetStr("IDS_E_SKY"))
		c.push(Pm.GetStr("IDS_E_MATCH"))
		c = c + Pm.GetStr("IDS_E_BIGWIN").split(//)

		a = Array.new
		a.push(b)
		a.push(c)

		return a
	end
  def cp_mp_ui_GetMatchYear2()
    a = cp_mp_ui_GetMatchYear()
    a.map! {|x| x.join }
    return a
  end

	# 出生後交大運
	def cp_mp_ui_GetBirthAfter()
		a = Array.new
		b = Array.new
		c = Array.new
		b = b + Pm.GetStr("IDS_E_BIRTH_AFTER").split(//)
		b.push("%02d" % cp_mp_getByAfterYear())
		b.push(Pm.GetStr("IDS_X_YEAR"))

		c.push("%02d" % cp_mp_getByAfterMonth())
		c.push(Pm.GetStr("IDS_X_MONTH"))
		c.push("%02d" % cp_mp_getByAfterDate())
		c.push(Pm.GetStr("IDS_X_DAY"))
		c.push(Pm.GetStr("IDS_E_MATCH"))
		c = c + Pm.GetStr("IDS_E_BIGWIN").split(//)

		a.push(b)
		a.push(c)
    return a
  end
  def cp_mp_ui_GetBirthAfter2()
    a = cp_mp_ui_GetBirthAfter()
    a.map! {|x| x.join }
		return a
	end

	# 命宮
	def cp_mp_ui_GetLifeHouse()
		if (@func_Par_Display) then
      return cp_mp_ui_GetLifeHouse2()
		else
			return nil
  end
		end
  def cp_mp_ui_GetLifeHouse2()
    h = cp_mp_getLifeHouse()
    return Eightword.getHashSkyEarthIndexName(h)
	end
	def cp_mp_ui_par_sky_need_event()
		bNeed = cp_mp_isSkyMouseOverEvent() || cp_fp_isSkyMouseOverEvent()
		return bNeed && @func_Par_Display
	end
	def cp_mp_ui_GetLifeHouse_SkyIndex()
		if (cp_mp_ui_par_sky_need_event()) then
			h = cp_mp_getLifeHouse()
			return h[Sky::SKY]
		else
			return 99
		end
	end
	def cp_mp_ui_GetLifeHouse_EarthIndex()
		if (@func_Par_Display) then
			h = cp_mp_getLifeHouse()
			return h[Earth::EARTH]
		else
			return 99
		end
	end
	# 命格
	def cp_mp_ui_GetLifeStyle()
		if (@func_Par_Display) then
      return cp_mp_ui_GetLifeStyle2()
		else
			return ""
    end
		end
  def cp_mp_ui_GetLifeStyle2()
    nLifeStyle = cp_mp_getLifeStyle()
    return pan_par_getLifeStyleStr(nLifeStyle)
	end
	# 胎元
	def cp_mp_ui_GetTaiUan()
		if (@func_Par_Display) then
      return cp_mp_ui_GetTaiUan2()
		else
			return nil
    end
		end
  def cp_mp_ui_GetTaiUan2()
    h = cp_mp_getTaiUan()
    return Eightword.getHashSkyEarthIndexName(h)
	end
	def cp_mp_ui_GetTaiUan_SkyIndex()
		if (cp_mp_ui_par_sky_need_event()) then
			h = cp_mp_getTaiUan()
			return h[Sky::SKY]
		else
			return 99
		end
	end
	def cp_mp_ui_GetTaiUan_EarthIndex()
		if (@func_Par_Display) then
			h = cp_mp_getTaiUan()
			return h[Earth::EARTH]
		else
			return 99
		end
	end
	# 胎息
	def cp_mp_ui_GetTaiHsi()
		if (@func_Par_Display) then
      return cp_mp_ui_GetTaiHsi2()
		else
			return nil
		end
	end
  def cp_mp_ui_GetTaiHsi2()
    h = cp_mp_getTaiHsi()
    return Eightword.getHashSkyEarthIndexName(h)
  end
	def cp_mp_ui_GetTaiHsi_SkyIndex()
		if (cp_mp_ui_par_sky_need_event()) then
			h = cp_mp_getTaiHsi()
			return h[Sky::SKY]
		else
			return 99
		end
	end
	def cp_mp_ui_GetTaiHsi_EarthIndex()
		if (@func_Par_Display) then
			h = cp_mp_getTaiHsi()
			return h[Earth::EARTH]
		else
			return 99
		end
	end
	def Eightword.getHashSkyEarthIndexName(h)
		sSky = Sky.GetIndexName(h[Sky::SKY])
		sEarth = Earth.GetIndexName(h[Earth::EARTH])
		return sSky + sEarth
	end
	# 年空
	def cp_mp_ui_GetYearEmpty()
		if (@func_Par_Display) then
      return cp_mp_ui_GetYearEmpty2()
		else
			return nil
		end
	end
  def cp_mp_ui_GetYearEmpty2()
    a = cp_mp_getYearEmpty()
    return Eightword.getArrayEarthIndexName(a)
  end
	def cp_mp_ui_GetYearEmpty_index(i)
		if (@func_Par_Display) then
			a = cp_mp_getYearEmpty()
			return a[i]
		else
			return 99
		end
	end
	# 日空
	def cp_mp_ui_GetDayEmpty()
		if (@func_Par_Display) then
      return cp_mp_ui_GetDayEmpty2()
		else
			return nil
  end
		end
  def cp_mp_ui_GetDayEmpty2()
    a = cp_mp_getDayEmpty()
    return Eightword.getArrayEarthIndexName(a)
	end
	def cp_mp_ui_GetDayEmpty_index(i)
		if (@func_Par_Display) then
			a = cp_mp_getDayEmpty()
			return a[i]
		else
			return nil
		end
	end
	def Eightword.getArrayEarthIndexName(a)
		sEarth1 = Earth.GetIndexName(a[0])
		sEarth2 = Earth.GetIndexName(a[1])
		return sEarth1 + sEarth2
	end
 	# 人元用事
 	def cp_mp_ui_UsePeople()
		if (@func_Par_Display) then
      return cp_mp_ui_UsePeople2()
		else
			return ""
  end
		end
  def cp_mp_ui_UsePeople2()
    sSky = cp_mp_ui_UsePeople_Sky2()
    sMainStar = cp_mp_ui_UsePeople_MainStar2()
    return sSky + " " + sMainStar
 	end
 	def cp_mp_ui_UsePeople_SkyIndex()
		if (cp_mp_ui_par_sky_need_event()) then
	 		nSkyIndex = cp_mp_getUsePeople()
	 		return nSkyIndex
	 	else
	 		return 99
	 	end
	end

 	def cp_mp_ui_UsePeople_Sky()
		if (@func_Par_Display) then
      return cp_mp_ui_UsePeople_Sky2()
    else
      return ""
    end
  end
  def cp_mp_ui_UsePeople_Sky2()
	 		nSkyIndex = cp_mp_getUsePeople()
			sSky = Sky.GetIndexName(nSkyIndex)
			return sSky
  end
  def cp_mp_ui_UsePeople_MainStar()
    if (@func_Par_Display) then
      return cp_mp_ui_UsePeople_MainStar2()
		else
			return ""
		end
	end
  def cp_mp_ui_UsePeople_MainStar2()
	 		nSkyIndex = cp_mp_getUsePeople()
			nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
			nMainStar = Eightword.MainStar(nDaySkyIndex,nSkyIndex)
			sMainStar = cp_mp_4c_getMainStarName(nMainStar)
			return sMainStar
		end

 	# 日主旺度
	def cp_mp_ui_GetNAvsNB()
 		nA = cp_mp_getNA()
 		nB = cp_mp_getNB()
 		if (nA >= nB) then
 			sOut = "A#{nA} > #{nB}B"
 		else
 			sOut = "A#{nA} < #{nB}B"
		end
		return sOut
 	end

 	# 陰陽氣合
 	def cp_mp_ui_NInYangValue()
  	if (!@func_Par_Display) then
 			return ""
 		end
    return cp_mp_ui_NInYangValue2()
 	end
  def cp_mp_ui_NInYangValue2()
    nInYangValue = cp_mp_getNInYangValue()
    sOut = ""
    sOut += "-" if nInYangValue < 0
    nInYangValue = nInYangValue.abs
    sOut += "#{nInYangValue / 10}.#{nInYangValue % 10}"

    return sOut
  end
 	def cp_mp_ui_GetByUseGodValue(arrayIndex)
 		if (uig_UseGodFive() == nil) then
 			return cp_mp_getByUseGod(arrayIndex)
 		else
 			aUseGodFive = uig_UseGodFive()
 			return aUseGodFive[arrayIndex]
 		end
 	end
 	def cp_mp_ui_GetByUseGodValueOrg(arrayIndex)
 		return cp_mp_getByUseGod(arrayIndex)
 	end

 	# 旺相休囚死
 	def cp_mp_ui_GetFiveLevel(arrayIndex)
 		if (@func_FiveWang_Display) then
      return cp_mp_ui_GetFiveLevel2(arrayIndex)
		else
			return ""
		end
 	end
  def cp_mp_ui_GetFiveLevel2(arrayIndex)
    nFiveIndex = cp_mp_getByFiveLevel(arrayIndex)
    return Eightword.getFiveName(nFiveIndex)
  end


 	# 與 Flow Info UI有關 func 喜用神
 	def cp_fi_ui_GetByUseGodValue(arrayIndex)
  		if (@m_nPanType == Cfate::PAN_NORMAL) then
 			return cp_mp_ui_GetByUseGodValue(arrayIndex)
 		else
 			return cp_mp_ui_GetByUseGodValue(arrayIndex)
 		end
	end
 	def cp_fi_ui_GetByUseGod(arrayIndex)
 		if (@func_Par_Display) then
	 		nFive = cp_fi_ui_GetByUseGodValue(arrayIndex)
			return Eightword.getFiveName(nFive)
		else
			return ""
		end
 	end
 	def cp_fi_ui_GetByUseGodOrg(arrayIndex)
 		if (@func_Par_Display) then
      return cp_fi_ui_GetByUseGodOrg2(arrayIndex)
		else
			""
		end
 	end
  def cp_fi_ui_GetByUseGodOrg2(arrayIndex)
    nFive = cp_mp_ui_GetByUseGodValueOrg(arrayIndex)
    return Eightword.getFiveName(nFive)
  end
 	def cp_fi_ui_GetNAvsNB(nPanType)
 		if (@func_Par_Display) then
      return cp_fi_ui_GetNAvsNB2(nPanType)
	 	else
	 		return ""
	 	end
	end
  def cp_fi_ui_GetNAvsNB2(nPanType)
      if (nPanType == Cfate::PAN_NORMAL) then
        return cp_mp_ui_GetNAvsNB()
      else
        return cp_fi_ui_GetNAvsNB1()
      end
  end

 	def cp_fi_ui_GetNAvsNB1()
 		nA = cp_fi_getNA()
 		nB = cp_fi_getNB()
 		if (nA >= nB) then
 			sOut = "A#{nA} > #{nB}B"
 		else
 			sOut = "A#{nA} < #{nB}B"
		end
		return sOut
 	end

 	# 中用神
 	def cp_fi_ui_GetMidGod()
 		if (!@func_Par_Display) then
 			return ""
 		end
    return cp_fi_ui_GetMidGod2()
	end
  def cp_fi_ui_GetMidGod2()
    sOut = ""
    nUseGodStyle = cp_mp_getUseGodStyle()
    m = 0
    sOut2 = ""
    if (nUseGodStyle == 1) then
      byUseGodSky = cp_mp_getByUseGodSky()
      sOut += Sky.GetIndexName(byUseGodSky)

      byLikeGodSky = cp_mp_getByLikeGodSky()

      bStop = false
      m = 0
      (0..3).each do |i|
        if (!bStop) then
          n = byLikeGodSky[i]
          if (n >= 0 && n < 10) then
            sOut2 += Sky.GetIndexName(n)
            m += 1
          else
            bStop = true
          end
        end
      end
    end
    n = 1
    if (m < 3) then
      n = 2
    end
    sOut3 = "#{sOut}#{Cfate.GetSpace(n)}#{sOut2}"
    return sOut3
  end

	# 格局用神
 	def cp_fi_ui_GetSpecialGod()
  	if (!@func_Par_Display) then
 			return ""
 		end
    return cp_fi_ui_GetSpecialGod2()
	end
  def cp_fi_ui_GetSpecialGod2()
    sOut = ""
    nSpecialType = cp_mp_getNSpecialType()
    if (nSpecialType >= 0) then
      sOut = Pm.GetStr("IDS_E_SPECIAL_GOD_#{nSpecialType + 1}")
    end
    return sOut
  end

	def cp_fi_ui_GetnSick(i)
 		if (!@func_Par_Display) then
 			return ""
 		end
    if (@dis_Sick == Eightword::DIS_SICK_HIDE) then
      return ""
    end
    return cp_fi_ui_GetnSick2(i)
 	end
  def cp_fi_ui_GetnSick2(i)
    if (@m_nPanType == Cfate::PAN_NORMAL) then
      return cp_mp_getnSick(i)
    else
      return cp_fi_getnSick(i)
    end
  end
	def cp_fi_ui_GetnSick_alert(i)
 		if (!@func_Par_Display) then
 			return false
 		end
    if (@dis_Sick == Eightword::DIS_SICK_HIDE) then
      return false
    end
 		j = (i % 2 == 1) ? i - 1 : i + 1
 		if (@m_nPanType == Cfate::PAN_NORMAL) then
 			a = cp_mp_getnSick(i)
 			b = cp_mp_getnSick(j)
 		else
 			a = cp_fi_getnSick(i)
 			b = cp_fi_getnSick(j)
 		end
 		diff = (a - b)
 		return diff.abs >= @dis_SickValue
 	end

	def cp_fi_ui_GetnGodValue(i)
 		if (!@func_Par_Display) then
 			return ""
 		end
    return cp_fi_ui_GetnGodValue2(i)
 	end
  def cp_fi_ui_GetnGodValue2(i)
    if (@m_nPanType == Cfate::PAN_NORMAL) then
      return cp_mp_getnGodValue(i)
    else
      return cp_fi_getnGodValue(i)
    end
  end

	def cp_fi_ui_GetnFiveValue(i)
 		if (!@func_Par_Display) then
 			return ""
 		end
    return cp_fi_ui_GetnFiveValue2(i)
 	end
  def cp_fi_ui_GetnFiveValue2(i)
    if (@m_nPanType == Cfate::PAN_NORMAL) then
      return cp_mp_getnFiveValue(i)
    else
      return cp_fi_getnFiveValue(i)
    end
  end


end
