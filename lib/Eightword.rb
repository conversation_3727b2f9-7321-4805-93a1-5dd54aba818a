require("Cfate.rb")
require("Xdate.rb")
require("Pm.rb")
require("Eightword_Constant.rb")
require("Eightword_ClassVariable.rb")
require("Eightword_InstanceVariable.rb")
require("Eightword_Func_Pan.rb")
require("Eightword_ParFunction.rb")
require("Eightword_Func_Pan_Ui.rb")
require("Eightword_Func_Pan_Change.rb")
require("Eightword_BigWin.rb")
require("Eightword_Pan_fi.rb")
require("Eightword_Pan_fp.rb")
require("SkyEarthFive_Function.rb")
require("Constant.rb")
require("Eightword_WeenApi.rb")
require("Eightword_zeri.rb")

class Eightword
	#---------------------------------------------------------------------
	#constructor initialize
	def initialize()
		initAll(Cfate::PAN_NONE,nil,nil)
	end

	def g_GetPanInfo(nPanType,hUserInfo,hUserDefDate,*args)
		if ((@m_UserInfo != hUserInfo) || (@m_nUserDefinedDate != hUserDefDate) || !LargeSanExist?()) then
			CreateAllPan(nPanType,hUserInfo,hUserDefDate,args[0],args[1])
		end

		# return @AllPanInfo[nPanType]
	end

  def SetPanInfo(nPanType,hUserInfo,hUserDefDate,hParAll,hApFunc)
    setPanType(nPanType)

    #初始化物件變數
    initAll(nPanType,hParAll,hApFunc)

    #設定客戶資訊
    setUserInfo(hUserInfo)

    #取得目前系統時間
    GetCurTime(hUserInfo["remote_ip"])

    if (hUserDefDate != nil) then
      @m_nUserDefinedDate = hUserDefDate.clone
      CheckUserAskDate()
    end

    set_fp_TimeInfo()
  end

	def CreateAllPan(nPanType,hUserInfo,*args)
    SetPanInfo(nPanType,hUserInfo,args[0],args[1],args[2])

		cp_CreateMainPan()
		cp_CreateFlowPan(@m_nPanType)
		# cp_CreateFlowInfo()
	end

	def setPanType(nPanType)
		@m_nPanType = nPanType
	end
	#---------------------------------------------------------------------
	def initAll(nPanType,hUserType=nil,hApFunc=nil)
		initType(hUserType)
		initFunc(hApFunc)
		initDate()
		initUserInfo()
		initPanInfo(nPanType)
	end

	def initType(hUserType=nil)
		if (hUserType == nil) then
			hUserType = Hash.new
		end
		pan_par_hash2var_all(hUserType) # 設定所有參數值
	end
	def initFunc(hApFunc=nil)
		if (hApFunc == nil) then
			hApFunc = Hash.new
		end
		pan_func_hash2var_all(hApFunc) # 設定所有參數值
	end

	def initDate()
		@m_nUserDefinedDate = Hash.new
		@m_nFlowDate = Hash.new
		@m_nCurDate = Hash.new
	  	@m_nPanDate = Hash.new
	end

	#---------------------------------------------------------------------
	def initUserInfo()
		@m_UserInfo = Hash.new()
		nEYear = 1900
		nEMonth = 1
		nEDate = 1
		nETime = 0
		bLeap = false
	  	uis_EY(nEYear)     #出生年(農曆)
	  	uis_EM(nEMonth)    #出生月(農曆)
	  	uis_ED(nEDate)      #出生日(農曆)
	  	uis_ET(nETime)     #出生時(地支時，十二個時辰)
		uis_EL(bLeap)    #出生月是否為潤月
		uis_EMI(0)    #出生分

		nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,nEMonth,nEDate,bLeap)
		uis_WY(nWYear)
		uis_WM(nWMonth)
		uis_WD(nWDate)
		uis_WH(Xdate.ETime2Hour(nETime))   #西元時24小時
		uis_WMI(0)    			#西元分鐘(60分鐘)
		uis_bS(true)     #性別 true 是女性,false是男性
		uis_nSIY(0)    	  #0為陽,1為陰(陰男,陽男,陰女,陽女用)
	end

	#---------------------------------------------------------------------
	def initPanInfo(nPanType)
		@EightwordInfo = Hash.new   # 包含 以下

		initMainPanInfo(nPanType) # 主盤
		initChangePanInfo(nPanType) # 變盤
		initFlowInfo() # 目前流盤
		initFlowPanInfo(nPanType) # 大運盤
	end
	def initMainPanInfo(nPanType)
		@EightwordInfo[Eightword::MAIN_PAN] = Hash.new
		@EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL] = init_mp_4ColInfo()
		@EightwordInfo[Eightword::MAIN_PAN][Eightword::USER_INFO] = init_mp_UserInfo()
		@EightwordInfo[Eightword::MAIN_PAN][Eightword::GOD_STYLE] = init_mp_GodStyle()
	end
	def init_mp_4ColInfo()
		h = Hash.new
		h[Eightword::UPI_GANZHI_8WORDS] = initGanzhi_8words(nil)
		h[Eightword::ByMainStar_4] = initGanzhi_8words(nil)
		h[Eightword::BySky_4] = initGanzhi_8words(nil)
		h[Eightword::BySkyFive_4] = initGanzhi_8words(nil)
		h[Eightword::ByEarth_4] = initGanzhi_8words(nil)
		h[Eightword::ByEarthFive_4] = initGanzhi_8words(nil)
		h[Eightword::ByChungSky_4_3] = initGanzhi_8words_array(3,nil)
		h[Eightword::ByChungFive_4_3] = initGanzhi_8words_array(3,nil)
		h[Eightword::BySubStar_4_3] = initGanzhi_8words_array(3,nil)
		h[Eightword::By12Win_4] = initGanzhi_8words(nil)
		h[Eightword::DwStarType_4] = initGanzhi_8words_array(0,nil)
		h[Eightword::DwBStarType_4] = initGanzhi_8words_array(0,nil)
		h[Eightword::ByNaIn_4] = initGanzhi_8words(nil)
	    h[Eightword::EmptyDead]	 = Array.new(12,0)
	    h[Eightword::ByFiveLevel_5] = Array.new(5,nil)
	    h[Eightword::BySkyScore_4]	 = initGanzhi_8words(nil)
	    h[Eightword::ByEarthScore_4]	 = initGanzhi_8words(nil)
	    h[Eightword::ByChungScore_4_3]	 = initGanzhi_8words_array(3,nil)
	    return h
	end

	# 客戶基本資料
	def init_mp_UserInfo()
		h = Hash.new
	    h[Eightword::ByLargeYear_2]	 = Array.new(2,nil)
	    h[Eightword::ByMatchYear_2]	 = Array.new(2,nil)
	    h[Eightword::BySegment]	 = nil
	    h[Eightword::ByMatchDay]	 = nil
	    h[Eightword::ByAfterYear]	= nil
	    h[Eightword::ByAfterMonth]	 = nil
	    h[Eightword::ByAfterDate]	 = nil

	    h[Eightword::LifeHouse]	 = nil  # 命宮 干支
	    h[Eightword::LifeStyle]	 = nil  # 命格 食神...
	    h[Eightword::TaiUan]	 = nil  # 胎元 乙巳
	    h[Eightword::TaiHsi]	 = nil  # 胎息 辛亥
	    h[Eightword::YearEmpty]	 = nil  # 年空 戌亥
	    h[Eightword::DayEmpty]	 = nil  # 日空 戌亥
	    h[Eightword::UsePeople]	 = nil  # 人元用事

	    return h
	end
	# 判斷特殊格局
	# 格局用神 喜神及旺相休囚死
	def init_mp_GodStyle()
		h = Hash.new
	    h[Eightword::NSpecialType]  = nil
	    h[Eightword::ByFiveLevel_5]	 = nil # Array.new(5,nil)
	    h[Eightword::ByUseGod_5]	 = nil # Array.new(5,nil)

	    h[Eightword::NUseGodStyle]	= nil
	    h[Eightword::ByUseGodSky]	 = nil
	    h[Eightword::ByLikeGodSky_4]	 = nil # Array.new(4,nil)

	    h[Eightword::BStrongLife]	 = nil
	    h[Eightword::NInYangValue]	 = nil
	    h[Eightword::NFiveSum_5]	 = nil # Array.new(5,nil)
	    h[Eightword::NA]	 = nil
	    h[Eightword::NB]	 = nil

	    h[Eightword::NOrgCombine]	 = nil

	    h[Eightword::NFiveValue_5]	 = nil # Array.new(5,nil)
	    h[Eightword::NGodValue_10]	 = nil # Array.new(5,nil)
	    h[Eightword::NSick_10]	 = nil # Array.new(5,nil)
	    h[Eightword::ByChungScore_4_3]	 = nil

	    return h
	end

	def initChangePanInfo(nPanType)
		@EightwordInfo[Eightword::CHANGE_PAN] = Hash.new
		@EightwordInfo[Eightword::CHANGE_PAN][Eightword::PAN_FOUR_COL] = init_mp_4ColInfo()
		@EightwordInfo[Eightword::CHANGE_PAN][Eightword::Scores] = init_fi_ScoreInfo()
	end

	def initFlowInfo()
		@EightwordInfo[Eightword::FLOW_INFO] = Hash.new
		@EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL] = init_mp_4ColInfo()
		@EightwordInfo[Eightword::FLOW_INFO][Eightword::Scores] = init_fi_ScoreInfo()
		@Large = Array.new(12,nil)
	end
	def init_fi_ScoreInfo()
		h = Hash.new
		# 陰陽氣合
		h[Eightword::NEarthType_5] = Array.new(5,nil)
	    # 日主旺度 A90 > 30B
	    h[Eightword::NA]	 = nil
	    h[Eightword::NB]	 = nil
	    h[Eightword::BStrongLife]	 = nil
	    h[Eightword::BChangePan]	 = nil


	    h[Eightword::FSkyArray]	 = Array.new(10,nil)
	    h[Eightword::BySkyScore_4]	 = Array.new(10,nil)
	    # 疾病檢核表
	    h[Eightword::NSick_10]	 = Array.new(10,nil)
	    # 十神分數
	    h[Eightword::FHRScore_10]	 = Array.new(10,nil)
	    h[Eightword::NGodScore_10]	 = Array.new(10,nil)
	    h[Eightword::NGodValue_10]	 = Array.new(10,nil)
    	# 五行分數
	    h[Eightword::NFiveSum_5]	 = Array.new(5,nil)
	    h[Eightword::NFiveValue_5]	 = Array.new(5,nil)
	    h[Eightword::NSkyType_5]	 = Array.new(5,nil)

		h[Eightword::BySky_4] = initGanzhi_8words(nil)
		h[Eightword::BySkyFive_4] = initGanzhi_8words(nil)
		h[Eightword::ByEarth_4] = initGanzhi_8words(nil)
		h[Eightword::ByEarthFive_4] = initGanzhi_8words(nil)
		h[Eightword::ByChungSky_4_3] = initGanzhi_8words(nil)
		h[Eightword::ByChungFive_4_3] = initGanzhi_8words(nil)

		return h
	end

	# 八字基本資料
	def initGanzhi_8words(value)
		h = Hash.new
	    h[Eightword::ByYearIdx]  = value
	    h[Eightword::ByMonthIdx]  = value
	    h[Eightword::ByDayIdx]  = value
	    h[Eightword::ByHourIdx]  = value
	    return h
	end
	def initGanzhi_8words_array(arraySize,value)
		h = Hash.new
		if (arraySize > 0) then
		    h[Eightword::ByYearIdx]  = Array.new(arraySize,value)
		    h[Eightword::ByMonthIdx]  = Array.new(arraySize,value)
		    h[Eightword::ByDayIdx]  = Array.new(arraySize,value)
		    h[Eightword::ByHourIdx]  = Array.new(arraySize,value)
		else
		    h[Eightword::ByYearIdx]  = Array.new
		    h[Eightword::ByMonthIdx]  = Array.new
		    h[Eightword::ByDayIdx]  = Array.new
		    h[Eightword::ByHourIdx]  = Array.new
		end
	    return h
	end

	def initFlowPanInfo(nPanType)
		@m_nLargeSan = nil       #目前大限

		@EightwordInfo[Eightword::FLOW_PAN] = Hash.new
		@EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL] = init_fp_4ColInfos()
		@EightwordInfo[Eightword::FLOW_PAN][Eightword::WIN_INFO] = init_fp_WinInfo()
		@EightwordInfo[Eightword::FLOW_PAN][Eightword::Scores] = init_fp_ScoreInfo()
	end
	def init_fp_4ColInfos()
		h = Hash.new
		(0..11).each do |i|
			sKey = fp_get4ColKey(i)
			h[sKey] = init_fp_4ColInfo()
		end
		return h
	end
	def init_fp_4ColInfo()
		h = Hash.new
		h[Eightword::ByDateValue1] = nil
		h[Eightword::ByDateValue2] = nil
		h[Eightword::UPI_GANZHI_8WORDS] = initGanzhi_8words(nil)
		h[Eightword::ByMainStar_4] = initGanzhi_8words(nil)
		h[Eightword::BySky_4] = initGanzhi_8words(nil)
		h[Eightword::BySkyFive_4] = initGanzhi_8words(nil)
		h[Eightword::ByEarth_4] = initGanzhi_8words(nil)
		h[Eightword::ByEarthFive_4] = initGanzhi_8words(nil)
		h[Eightword::ByChungSky_4_3] = initGanzhi_8words_array(3,nil)
		h[Eightword::ByChungFive_4_3] = initGanzhi_8words_array(3,nil)
		h[Eightword::BySubStar_4_3] = initGanzhi_8words_array(3,nil)
		h[Eightword::By12Win_4] = initGanzhi_8words(nil)
		h[Eightword::DwStarType_4] = initGanzhi_8words_array(0,nil)
		h[Eightword::DwBStarType_4] = initGanzhi_8words_array(0,nil)
		h[Eightword::ByNaIn_4] = initGanzhi_8words(nil)
	    h[Eightword::EmptyDead]	 = Array.new(12,0)
	    h[Eightword::ByFiveLevel_5] = Array.new(5,nil)
	    return h
	end
	def fp_get4ColKey(i)
		sKey = "COL_#{i}"
		return sKey
	end

	def init_fp_WinInfo()
		h = Hash.new
		return h
	end
	def init_fp_ScoreInfo()
		h = Hash.new
	    h[Eightword::ByDateFiveLevel_5]	 = Array.new(5,nil)
	    h[Eightword::BySkyScore_4]	 = initGanzhi_8words(nil)
	    h[Eightword::ByEarthScore_4]	 = initGanzhi_8words(nil)
	    h[Eightword::ByChungScore_4_3]	 = initGanzhi_8words_array(3,nil)
	    h[Eightword::NSkyScore_10]	 = Array.new(10,nil)
	    h[Eightword::NGodScore_10]	 = Array.new(10,nil)
	    h[Eightword::NFiveScore_5]	 = Array.new(5,nil)
	    h[Eightword::NInYangValue]	 = nil
	    h[Eightword::NSkyType_5]	 = Array.new(5,nil)
	    h[Eightword::NEarthType_5]	 = Array.new(5,nil)

	    # 日主旺度 A90 > 30B
	    h[Eightword::NA]	 = nil
	    h[Eightword::NB]	 = nil
	    h[Eightword::BStrongLife]	 = nil
	    # 疾病檢核表
	    h[Eightword::NSick_10]	 = Array.new(10,nil)
	    # 十神分數
	    h[Eightword::NGodValue_10]	 = Array.new(10,nil)
    	# 五行分數
	    h[Eightword::NFiveSum_5]	 = Array.new(5,nil)
	    h[Eightword::NFiveValue_5]	 = Array.new(5,nil)

	    h[Eightword::NWinValue]	 = nil
		return h
	end


	#input : Hash of UserInfo
	def setUserInfo(hUserInfo)
		@m_UserInfo = hUserInfo.clone
		if (Xdate.IsWDateLegal?(uig_WY(),uig_WM(),uig_WD(),uig_WH())) then
			y,m,d,l = Xdate.West2East(uig_WY(),uig_WM(),uig_WD())

			uis_ET(Xdate.Hour2ETime(uig_WH()))
			y,m,d,l,t = Xdate.NextEDateFromTime(y,m,d,l, uig_ET())
			uis_E_YMDLT(y,m,d,l,t)
		elsif (Xdate.IsEDateLegal?(uig_EY(),uig_EM(),uig_ED(),uig_EL(),uig_WH())) then
			y,m,d = Xdate.East2West(uig_EY(),uig_EM(),uig_ED(),uig_EL())
			uis_W_YMD(y,m,d)

			uis_ET(Xdate.Hour2ETime(uig_WH()))
			y,m,d,l,t = Xdate.NextEDateFromTime(uig_EY(), uig_EM(), uig_ED(), uig_EL(), uig_ET())
			uis_E_YMDLT(y,m,d,l,t)
		end

		cp_GetnSex()

		nWYear, nWMonth, nWDate,nHour = uig_W_YMDH()
		nMinute = uig_WMI()
		nSYear = Xdate.GetSegYear(nWYear, nWMonth, nWDate,nHour,nMinute,@par_FirstSegment)
		uis_SY(nSYear)
	end

	#---------------------------------------------------------------------
	def set_fp_TimeInfo()
		setFlowTimeInfo(@m_nPanType)  #設定流盤資訊
	end


	def cp_CreateMainPan()
		cp_mp_4ColInfo()
		cp_mp_UserInfo()
		# cp_mp_GodStyle()
	end

	#---------------------------------------------------------------------
	#計算各盤資訊
	def cp_mp_4ColInfo()
		cp_mp_GanZhi8Words()
	    cp_mp_BySkyIndex_4()
	    cp_mp_ByEarthIndex_4()
	    cp_mp_ByChungSky_4_3()
	    cp_mp_BySkyFive_4()
	    cp_mp_ByEarthFive_4()
	    cp_mp_ByChungFive_4_3()
	    cp_mp_ByMainStar_4()
	    cp_mp_BySubStar_4_3()
	    cp_mp_By12Win_4()
	    cp_mp_DwStarType_4()
	    cp_mp_DwBStarType_4()
	    cp_mp_ByNaIn_4()
	    cp_mp_ByFiveLevel_5()
	end
	def cp_mp_UserInfo()
		cp_mp_UserInfos()
	  	# 命宮，命格，胎元，胎息，年空，日空
  		cp_mp_LifeHouse()
  		cp_mp_LifeStyle()
  		cp_mp_TaiUan()
  		cp_mp_TaiHsi()
  		cp_mp_UsePeople()
		cp_mp_YearEmpty()
  		cp_mp_DayEmpty()
	end
	# 用神
	def cp_mp_GodStyle(nPanType)
	    cp_mp_gs_NSpecialType(nPanType)
	    cp_mp_gs_ByFiveLevel_5(nPanType)
	    cp_mp_gs_ByUseGod_5(nPanType)

	    cp_mp_gs_NUseGodStyle(nPanType)
	    cp_mp_gs_ByUseGodSky(nPanType)
	    cp_mp_gs_ByLikeGodSky_4(nPanType)
	end

	def cp_CreateFlowInfo
		cp_fi_4ColInfo()
		# cp_fi_ScoreInfo()
	end

	def cp_fi_4ColInfo()
		cp_fi_GanZhi8Words(@m_nPanType)
	    cp_fi_BySkyIndex_4()
	    cp_fi_ByEarthIndex_4()
	    cp_fi_ByChungSky_4_3()
	    cp_fi_BySkyFive_4()
	    cp_fi_ByEarthFive_4()
	    cp_fi_ByChungFive_4_3()
	    cp_fi_ByMainStar_4()
	    cp_fi_BySubStar_4_3()
	    cp_fi_By12Win_4()
	    cp_fi_DwStarType_4()
	    cp_fi_DwBStarType_4()
	    cp_fi_ByNaIn_4()
	    cp_fi_ByFiveLevel_5()
	end
	def cp_fi_ScoreInfo(nPanType)
	    cp_fi_NInYangValue(nPanType)
	end

	def	cp_CreateFlowPan(nPanType)
		# cp_mp_4ColInfo(nPanType,Eightword::FLOW_PAN)
		cp_fp_4ColInfos(nPanType)
		cp_fp_WinInfo()
		# cp_fp_ScoreInfo()
	end
	def cp_fp_4ColInfos(nPanType)
		(0..11).each do |nIndex|
			cp_fp_4ColInfo(nPanType,nIndex)
		end
	end
	# 各流盤資訊
	def cp_fp_4ColInfo(nPanType,nIndex)
		cp_fp_GanZhi8Words(nIndex,nPanType)
		cp_fp_BySkyIndex_4(nIndex)
		cp_fp_BySkyFive_4(nIndex)
		cp_fp_ByEarthIndex_4(nIndex)
		cp_fp_ByEarthFive_4(nIndex)
		cp_fp_ByChungSky_4_3(nIndex)
		cp_fp_ByChungFive_4_3(nIndex)
		cp_fp_ByMainStar_4(nIndex)
		cp_fp_BySubStar_4_3(nIndex)
		cp_fp_By12Win_4(nIndex)
		cp_fp_DwStarType_4(nIndex)
		cp_fp_DwBStarType_4(nIndex)
		cp_fp_ByNaIn_4(nIndex)
	end
	def cp_fp_WinInfo()
	end
	def cp_fp_ScoreInfo(nPanType)
	    cp_fp_si_ByDateFiveLevel_5(nPanType)
	    cp_fp_si_BySkyScore_4(nPanType)
	    cp_fp_si_ByEarthScore_4(nPanType)
	    cp_fp_si_ByChungScore_4_3(nPanType)
	    cp_fp_si_NSkyScore_10(nPanType)
	    cp_fp_si_NGodScore_10(nPanType)
	    cp_fp_si_NFiveScore_5(nPanType)
	    cp_fp_si_NSkyType_5(nPanType)
	    cp_fp_si_NEarthType_5(nPanType)

    	# 五行分數
	    cp_fpi_si_NFiveSum_5(nPanType)
	    cp_fpi_si_NFiveValue_5(nPanType)

	    # 日主旺度 A90 > 30B
	    cp_fpi_si_NA(nPanType)
	    cp_fpi_si_NB(nPanType)
	    cp_fpi_si_BStrongLife(nPanType)
	    # 疾病檢核表
	    cp_fpi_si_NSick_10(nPanType)
	    # 十神分數
	    cp_fpi_si_NGodValue_10(nPanType)

	    cp_fpi_si_NWinValue(nPanType)
	end

	def test()
		@EightwordInfo[Eightword::U_PAN_INFO][Cfate::PAN_NORMAL][Eightword::UPI_PAN_INFO][Eightword::ByYearIdx][Eightword::BySky_4]
	end
end
