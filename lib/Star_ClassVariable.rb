require "Star_Constant.rb"

class Star

  #類別變數

  #基本固定參數
  @@Five<PERSON>ord = [2,4,6,8,0,2,4,6,8,0]
  @@FiveMouse = [0,2,4,6,8,0,2,4,6,8]
  @@ByPurple = #  byPurple[5][30],尋找紫微星時使用
    [[1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,0,0,1,1,2,2,3,3,4],
     [4,1,2,5,2,3,6,3,4,7,4,5,8,5,6,9,6,7,10,7,8,11,8,9,0,9,10,1,10,11],
     [11,4,1,2,0,5,2,3,1,6,3,4,2,7,4,5,3,8,5,6,4,9,6,7,5,10,7,8,6,11],
     [6,11,4,1,2,7,0,5,2,3,8,1,6,3,4,9,2,7,4,5,10,3,8,5,6,11,4,9,6,7],
     [9,6,11,4,1,2,10,7,0,5,2,3,11,8,1,6,3,4,0,9,2,7,4,5,1,10,3,8,5,6]]
  @@BuLu = [0,1,3,4,3,4,6,7,9,10]
  @@ByFire = [2,3,1,9,2,3,1,9,2,3,1,9]
  @@ByLin = [10,10, 3,10,10,10, 3,10,10,10, 3,10]
  @@ByKue = [11,10,9,9,11,10,11,4,1,1]
  @@ByUei = [5,6,7,7,5,6,5,0,3,3]
  # 勸學齋主排法
  # 年 干 甲 乙 丙 丁 戊 己 庚 辛 壬 癸
  # 天魁  未 申 酉 亥 丑 子 丑 寅 卯 巳
  @@ByKue_Su = [5,6,7,9,11,10,11,0,1,3]
  # 天鉞  丑 子 亥 酉 未 申 未 午 巳 卯
  @@ByUei_Su = [11,10,9,7,5,6,5,4,3,1]
  @@ByWu = [3,6,0,9]
  @@ByTGuan = [5,2,3,0,1,7,9,7,8,4]
  @@ByTFu2 = [7,6,10,9,1,0,4,3,4,3]
  @@ByTMoon = [8,3,2,0,5,1,9,5,0,4,8,0]
  @@ByHorse = [6,3,0,9]  # 6是指的申位，3是巳位，0是寅   9 亥

  @@LifeStar = [2,3,4,5,6,5,4,3,2,1,0,1]   # 命主各星
  @@ByFu = [6,11,10,9,8,7,0,5,4,3,2,1] #

  @@By60Five = [[2,0,4],[0,4,3],[4,3,1],[3,1,2],[1,2,0]]
  @@ByFlowChung = [3,4,6,7,6,7,9,10,0,1]

  @@A2Star = [6, 9, 10, 13, 20]  #需要做雙星判斷者 廉貞,貪狼,巨門,七殺,擎羊
  @@A2Star1 = [6, 9, 10, 13]  #需要做雙星判斷者 廉貞,貪狼,七殺,巨門，此4顆星與雙星組合中的星碰到都要算雙星
  @@DoubleAStar_One = [[7,9,11,13,14],  # 廉貞的雙星
             [1,4,6],  # 貪狼的雙星
             [2,3,5],  # 巨門的雙星
             [1,4,6]  # 七殺的雙星
            ]

  @@A2Star2 = [20]    #需要做雙星判斷者  擎羊，同宮中有雙星組合,擎羊才算雙星
  @@AStar_Main = [1,2,3,4,5,6,7,8,9,10,11,12,13,14]

  # 1 紫微天府   9  太陽太陰  17  天同太陰
  # 2 紫微貪狼  10  太陽巨門  18  天同巨門
  # 3 紫微天相  11  太陽天梁  19  天同天梁
  # 4 紫微七殺  12  武曲天府  20  廉貞天府
  # 5 紫微破軍  13  武曲貪狼  21  廉貞貪狼
  # 6 天機太陰  14  武曲天相  22  廉貞天相
  # 7 天機巨門  15  武曲七殺  23  廉貞七殺
  # 8 天機天梁  16  武曲破軍  24  廉貞破軍
  # 雙星組合共有二十四種組合，分佈於十二宮。
  @@DoubleAStar_All = [[1,7],[1,9],[1,11],[1,13],[1,14],  # 紫微
             [2,8],[2,10],[2,12], # 天機
             [3,8],[3,10],[3,12], # 太陽
             [4,7],[4,9],[4,11],[4,13],[4,14], # 武曲
             [5,8],[5,10],[5,12],   # 天同
             [6,7],[6,9],[6,11],[6,13],[6,14]  # 廉貞
            ]

  @@ByChangeFive = [4, 0, 2, 4, 4]

  # 水=0 木=1 金=2 土=3 火=4
  @@AStarFive = [3, 1, 4, 2, 0, 1, 3, 0, 1, 0,
                   0, 3, 2, 0, 2, 0, 3, 0, 3, 2,
                   2, 4, 4, 4, 4, 3, 1, 0, 0]

  @@BStarFive = [4, 4, 4, 0, 4,-1,-1, 0, 3, 3,
                   2, 3, 0, 3, 0, 4, 4, 4, 4, 3,
                   3,-1,-1, 3, 3, 4, 3, 1, 3,-1,
                   -1,-1, 0, 0,-1]

  @@CStarFive = [ [0, 4, 0, 4, 1, 2, 4, 0, 0, 4, 4, 4],   # Doctor
                   [-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1],   # God
                   [-1,-1,-1,-1, 1,-1, 4,-1,-1,-1,-1,-1],   # YearStar
                    [4,-1, 1,-1, 4, 4, 4,-1, 2,-1, 4, 0]]   # YearGod

  @@DoctorFive = [0, 4, 0, 4, 1, 2, 4, 0, 0, 4, 4, 4]   # Doctor
  @@GodFive = [-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1]
  @@YearStarFive = [-1,-1,-1,-1, 1,-1, 4,-1,-1,-1,-1,-1]
  @@YearGodFive = [4,-1, 1,-1, 4, 4, 4,-1, 2,-1, 4, 0]

  # 局數 水二局,木三局,金四局,土五局,火六局  byFive[]
  @@ByFive = [2,6,5,3,4,
        6,5,3,4,2,
        3,4,2,6,5,
        5,3,4,2,6,
        4,2,6,5,3,
        6,5,3,4,2]

    # For Score class variable
  # [StarFive][HouseFive]
  # 水=0 木=1 金=2 土=3 火=4
  @@ByFiveScore =   [[6, 2, 4, 6, 8],
                   [8, 4, 2, 4, 6],
                   [5, 5, 5, 5, 5],
                   [4, 8, 6, 8, 2],
                   [2, 6, 8, 2, 4]
                    ]
  # 暫時不用陰陽
  @@ByInScore = [[2, 6, 8, 6, 4],
                 [6, 2, 4, 8, 8],
                 [4, 8, 6, 4, 2],
                 [5, 5, 5, 5, 5],
                 [8, 4, 2, 2, 6]]
  @@ByYangScore = [[7, 1, 3, 1, 9],
                   [1, 7, 9, 3, 3],
                   [9, 3, 1, 9, 7],
                   [5, 5, 5, 5, 5],
                   [3, 9, 7, 7, 1]]

  # 非五行星性數值表    命 兄 夫 子 財 疾 遷 朋 官 田 福 父   [21][12]
  @@NoFive =            [8, 1,-9,-1,-6, 2, 6, 3,-5, 4, 9, 7, # 1:BSTAR_6 解神
                           2, 1, 1, 1,-8, 4, 7,-9,-9, 5, 6, 3, # 2:BSTAR_7 陰煞
                           7, 2, 2, 2, 9, 5, 7, 6, 9, 1, 7, 5, # 3:BSTAR_22 天巫
                           8, 1, 1, 1, 5,-9, 9, 1, 6, 4, 7, 2, # 4:BSTAR_23 天月
                          -5,-1,-6,-1,-9,-2,-7,-1,-8,-4,-9,-3, # 5:BSTAR_30 截空
                          -6,-1,-1,-1,-9,-4,-8,-2,-9,-5,-7,-3, # 6:BSTAR_31 旬中
                          -6,-1,-1,-1,-9,-4,-8,-2,-9,-5,-7,-3, # 7:BSTAR_32 旬空
                           9, 1, 1, 1, 8, 5, 6, 4, 9, 1, 7, 3, # 8:YSTAR_1 將星
                           9, 1, 3, 1, 4, 1, 8, 7, 9, 6, 5, 2, # 9:YSTAR_2 攀鞍
                           8,-1,-4,-1, 9, 5, 7,-3, 9, 2, 6,-1, # 10:YSTAR_3 歲驛
                          -9, 1, 4, 1, 5,-7, 8, 2,-6, 3,-9, 1, # 11:YSTAR_4 息神
                          -8, 1, 2, 1,-5,-9,-7, 3,-6,-4,-9, 1, # 12:YSTAR_6 劫煞
                          -6, 1, 2, 1, 4,-7, 3, 8,-9, 1,-5, 9, # 13:YSTAR_8 天煞
                           2, 1, 1, 1, 8, 5,-7,-9,-9, 4,-6, 3, # 14:YSTAR_9 指背
                           7, 3, 9,-2, 1,-5, 1,-9, 6, 4, 8, 1, # 15:YSTAR_10 咸池
                          -5, 6,-9, 8, 1,-3, 1, 2, 1, 9,-4,-7, # 16:YSTAR_11 月煞
                          -6, 1,-2, 1,-9,-8, 5, 4, 9, 3,-7,-1, # 17:YSTAR_12 亡神
                          -9,-1,-3,-1,-8,-6,-5,-2,-9,-4,-7,-1, # 18:YGOD_2 晦氣
                          -9, 1, 3, 2,-8, 7, 5, 6,-9, 4,-5, 1, # 19:YGOD_4 貫索
                           9, 1, 4, 1, 7, 1, 6, 3, 9, 5, 8, 2, # 20:YGOD_8 龍德
                          -8, 1, 3, 1, 9, 5, 6, 1, 7, 2,-4, 9] # 21:YGOD_10 天德


  @@NoFiveBStar = [6, 7, 22, 23, 30, 31, 32, 35]
  # 非五行星性數值表    命 兄 夫 子 財 疾 遷 朋 官 田 福 父
  # @@NoFive_BStar =      [[8, 1,-9,-1,-6, 2, 6, 3,-5, 4, 9, 7], # 1:BSTAR_6 解神
  #                          [2, 1, 1, 1,-8, 4, 7,-9,-9, 5, 6, 3], # 2:BSTAR_7 陰煞
  #                          [7, 2, 2, 2, 9, 5, 7, 6, 9, 1, 7, 5], # 3:BSTAR_22 天巫
  #                          [8, 1, 1, 1, 5,-9, 9, 1, 6, 4, 7, 2], # 4:BSTAR_23 天月
  #                         [-5,-1,-6,-1,-9,-2,-7,-1,-8,-4,-9,-3], # 5:BSTAR_30 截空
  #                         [-6,-1,-1,-1,-9,-4,-8,-2,-9,-5,-7,-3], # 6:BSTAR_31 旬中
  #                         [-6,-1,-1,-1,-9,-4,-8,-2,-9,-5,-7,-3], # 7:BSTAR_32 旬空
  #                         [ 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]] # 7:BSTAR_35 天廚

  # after 2020/12/12
  @@NoFive_BStar =      [[8, 1,-9,-1,-6, 5, 6, 3,-5, 4, 9, 7], # 1:BSTAR_6 解神
                           [2, 1, 1, 1,-8, -4, 7,-9,-9, 5, 6, 3], # 2:BSTAR_7 陰煞
                           [7, 2, 2, 2, 9, 5, 7, 6, 9, 1, 7, 5], # 3:BSTAR_22 天巫
                           [8, 1, 1, 1, 5,-9, 9, 1, 6, 4, 7, 2], # 4:BSTAR_23 天月
                          [-9,-1,-6,-1,-9,7,-7,-1,-9,-4,-9,-3], # 5:BSTAR_30 截空
                          [-6,-1,-1,-1,-9,4,-8,-2,-9,-5,-7,-3], # 6:BSTAR_31 旬中
                          [-6,-1,-1,-1,-9,4,-8,-2,-9,-5,-7,-3], # 7:BSTAR_32 旬空
                          [ 9, 5, 7, 3, 7, 3, 4, 2, 7, 5, 6, 5]] # 7:BSTAR_35 天廚

  # 非五行星性數值表    命 兄 夫 子 財 疾 遷 朋 官 田 福 父
    # @@NoFive_YearStar =   [[9, 1, 1, 1, 8, 5, 6, 4, 9, 1, 7, 3], # 8:YSTAR_1 將星
    #                        [9, 1, 3, 1, 4, 1, 8, 7, 9, 6, 5, 2], # 9:YSTAR_2 攀鞍
    #                        [8,-1,-4,-1, 9, 5, 7,-3, 9, 2, 6,-1], # 10:YSTAR_3 歲驛
    #                       [-9, 1, 4, 1, 5,-7, 8, 2,-6, 3,-9, 1], # 11:YSTAR_4 息神
    #                       [-8, 1, 2, 1,-5,-9,-7, 3,-6,-4,-9, 1], # 12:YSTAR_6 劫煞
    #                       [-6, 1, 2, 1, 4,-7, 3, 8,-9, 1,-5, 9], # 13:YSTAR_8 天煞
    #                        [2, 1, 1, 1, 8, 5,-7,-9,-9, 4,-6, 3], # 14:YSTAR_9 指背
    #                        [7, 3, 9,-2, 1,-5, 1,-9, 6, 4, 8, 1], # 15:YSTAR_10 咸池
    #                       [-5, 6,-9, 8, 1,-3, 1, 2, 1, 9,-4,-7], # 16:YSTAR_11 月煞
    #                       [-6, 1,-2, 1,-9,-8, 5, 4, 9, 3,-7,-1]] # 17:YSTAR_12 亡神
  # after 2020/12/12
    @@NoFive_YearStar =   [[9, 1, 1, 1, 8, 5, 6, 4, 9, 1, 7, 3], # 8:YSTAR_1 將星
                           [9, 1, 3, 1, 4, 1, 8, 7, 9, 6, 5, 2], # 9:YSTAR_2 攀鞍
                           [8,-1,-4,-1, 9, 5, 7,-3, 9, 2, 6,-1], # 10:YSTAR_3 歲驛
                          [-9, 1, 4, 1, 5,-7, 8, 2,-6, 3,-9, 1], # 11:YSTAR_4 息神
                          [-8, 1, 2, 1,-5,-9,-7, 3,-6,-4,-9, 1], # 12:YSTAR_6 劫煞
                          [-6, 1, 2, 1, 4,-7, 3, 8,-9, 1,-5, 9], # 13:YSTAR_8 天煞
                           [2, 1, 1, 1, -8, -5,-7,-9,-9, 4,-6, 3], # 14:YSTAR_9 指背
                           [7, 3, 9,-2, 1,-5, 1,-9, 6, 4, 8, 1], # 15:YSTAR_10 咸池
                          [-5, 6,-9, 8, 1,-3, 1, 2, -3, 9,-4,-7], # 16:YSTAR_11 月煞
                          [-6, 1,-2, 1,-9,-8, 5, 4, -9, 3,-7,-1]] # 17:YSTAR_12 亡神

  # 非五行星性數值表    命 兄 夫 子 財 疾 遷 朋 官 田 福 父
    # @@NoFive_YearGod =   [[-9,-1,-3,-1,-8,-6,-5,-2,-9,-4,-7,-1], # 18:YGOD_2 晦氣
    #                       [-9, 1, 3, 2,-8, 7, 5, 6,-9, 4,-5, 1], # 19:YGOD_4 貫索
    #                       [ 9, 1, 4, 1, 7, 1, 6, 3, 9, 5, 8, 2], # 20:YGOD_8 龍德
    #                       [-8, 1, 3, 1, 9, 5, 6, 1, 7, 2,-4, 9]] # 21:YGOD_10 天德
  # after 2020/12/12
    @@NoFive_YearGod =   [[-9,-1,-3,-1,-8,-6,-5,-2,-9,-4,-7,-1], # 18:YGOD_2 晦氣
                          [-9, 1, 3, 2,-8, -7, 5, 6,-9, 4,-5, 1], # 19:YGOD_4 貫索
                          [ 9, 1, 4, 1, 7, 5, 6, 3, 9, 5, 8, 2], # 20:YGOD_8 龍德
                          [9, 1, 3, 1, 9, 5, 6, 1, 9, 2, 9, 9]] # 21:YGOD_10 天德

    # 各級星的加權分表(一般通則)
  # before 2020/12/12
  # [PanType][StarLevel] [6][5]  orignnal byFlowStarLevel in VC++
  #                     A甲  B乙 B丙  丁  戊   級星
  # @@ByStarLevelPower = [[100, 80, 50, 20, 10],  # PAN_NORMAL
  #                         [100, 80, 60, 30, 20],  # PAN_TENYEAR
  #                         [100, 80, 70, 45, 30],  # PAN_FLOWYEAR
  #                          [50, 60, 80,100, 60],  # PAN_FLOWMONTH
  #                          [30, 50, 70,100, 80],  # PAN_FLOWDATE
  #                          [10, 50, 80,100,100]]  # PAN_FLOWTIME

  # after 2020/12/12
  @@ByStarLevelPower = [[100, 80, 50, 30, 20],  # PAN_NORMAL
                          [100, 80, 60, 30, 20],  # PAN_TENYEAR
                          [100, 80, 70, 45, 30],  # PAN_FLOWYEAR
                           [50, 60, 80,100, 80],  # PAN_FLOWMONTH
                           [30, 50, 70,100, 80],  # PAN_FLOWDATE
                           [10, 50, 80,100,100]]  # PAN_FLOWTIME

  @@ByStarLevelPower_Except =  [[],  # PAN_NORMAL
                               [],  # PAN_TENYEAR
                               [],  # PAN_FLOWYEAR
                               # 流月盤，則月系星以100%原分數計算。
                                # 左輔、右弼、天刑、天姚、天馬、解神、天巫、天月、陰煞共九顆星
                                # A17、A18、B3、B4、B5、B6、B22、B23、B7共九顆星
                               [[A_STAR,17], [A_STAR,18],[B_STAR,3],[B_STAR,4],[B_STAR,5],[B_STAR,6],[B_STAR,22],[B_STAR,23],[B_STAR,7]],  # PAN_FLOWMONTH
                               # 以流日盤時，則日系星則以100%原分數計算。
                               # 三台、八座、思光、天貴共四顆星。
                               # B24、B25、26、B27共四顆星。
                               [[B_STAR,24], [B_STAR,25], [B_STAR,26],[B_STAR,27]],  # PAN_FLOWDATE
                               # 以流時盤時，則時系星則以100%原分數計算。
                               # 文昌、文曲、火星、鈴星、地劫、天空、台輔、封誥共八顆星。
                               # A15、A16、A22、A23、B1、B2、B20、B21共八顆星。
                               [[A_STAR,15], [A_STAR,16],[A_STAR,22], [A_STAR,23],[B_STAR,1],[B_STAR,2],[B_STAR,20],[B_STAR,21]]  # PAN_FLOWTIME
                                 ]
  # 將星中等級屬於丁的星,其餘等級為戊
  @@YearStarLevel4 = [1,2,3,5]
  # 歲建中等級屬於丁的星,其餘等級為戊
  @@YearGodLevel4 = [1,8,10]

  # Score class variable end


  # 欽天派四化
  # 廉6  破14 武4  陽3
  # 機2  梁12 紫1  陰8
  # 同5  機2  昌15 廉6
  # 陰8  同5  機2  巨10
  # 貪9  陰8  右18 機2
  # 武4  貪9  梁12 曲16
  # 陽3  武4  陰8  同5
  # 巨10 陽3  曲16 昌15
  # 梁12 紫1  左17 武4
  # 破14 巨10 陰8  貪9
  @@m_dwHua_Steven = [[ 6,14, 4, 3],
                      [ 2,12, 1, 8],
                      [ 5, 2,15, 6],
                      [ 8, 5, 2,10],
                      [ 9, 8,18, 2],
                      [ 4, 9,12,16],
                      [ 3, 4, 8, 5],
                      [10, 3,16,15],
                      [12, 1,17, 4],
                      [14,10, 8, 9]]

  # 占驗門(許金發理事長)四化星要改成可設定
  # 廉6  破14 曲16- 陽3
  # 機2  梁12 紫1  陰8
  # 同5  機2  昌15 廉6
  # 陰8  同5  機2  巨10
  # 貪9  陰8  右18 機2
  # 武4  貪9  梁12 曲16
  # 陽3  武4  同5-  相11-
  # 巨10 陽3  武4- 昌15
  # 梁12 紫1  府7-  武4-
  # 破14 巨10 陰8  貪9
  @@m_dwHua_Hsu = [[ 6,14,16, 3],
                   [ 2,12, 1, 8],
                   [ 5, 2,15, 6],
                   [ 8, 5, 2,10],
                   [ 9, 8,18, 2],
                   [ 4, 9,12,16],
                   [ 3, 4, 5,11],
                   [10, 3, 4,15],
                   [12, 1, 7, 4],
                   [14,10, 8, 9]]

  # 十八飛星(謝昕潤)四化星要改成可設定
  # 廉6  破14 武4  陽3
  # 機2  梁12 紫1  陰8
  # 同5  機2  昌15 廉6
  # 陰8  同5  機2  巨10
  # 貪9  陰8  右18 機2
  # 武4  貪9  梁12 破14-
  # 陽3  武4  府7- 同5
  # 巨10 陽3  曲16 梁12-
  # 梁12 紫1  左17 武4
  # 破14 巨10 陰8  貪9
  @@m_dwHua_18FlyStar = [[ 6,14, 4, 3],
                         [ 2,12, 1, 8],
                         [ 5, 2,15, 6],
                         [ 8, 5, 2,10],
                         [ 9, 8,18, 2],
                         [ 4, 9,12,14],
                         [ 3, 4, 7, 5],
                         [10, 3,16,12],
                         [12, 1,17, 4],
                         [14,10, 8, 9]]


  #                       廟                   旺        利    平     陷
  @@MioWong = [[[6,7,10,11,12,13,19,22,23],[1,3,6,7,14],[5],[9,16],[15,21]],  #寅宮
               [[3,10,12,19],[1,2,7,13,16],[4,9,15,22,23],[5,6],[8,11,14,20]],
               [[4,7,9,12,13,20,21],[1,3,11,13,15,16],[2,6],[5],[8,10,22,23]],
               [[5,15,16,19],[1,3,7,10,11,18,19],[],[2,4,13,14],[6,8,9,12,21]],
               [[1,2,11,12,14,19,22,23],[3,4,7,9,10,13],[],[6],[5,8,15,16,20]],
               [[1,4,7,9,13,20,21],[3,11,12,14,16],[6,15,22,23],[],[2,5,8,10]],
               [[6,10,11,13,19],[1,2,3,4,5,7,14,15,16],[8],[9],[12,21,22,23]],
               [[10,15,16,19],[1,2,7,8,12,13,22,23],[4,9],[3,5,6],[11,14,20]],
               [[4,7,9,12,13,20,21,22,23],[1,8,11,14],[2,6],[5],[3,10,15,16]],
               [[5,8,19],[1,7,10,11,16],[15,22,23],[2,4,13,14],[3,6,9,12,21]],
               [[2,7,8,11,12,14,19],[4,5,9,10,13,15,16],[],[1,6],[3,20,22,23]],
               [[1,4,7,8,9,11,13,15,16,20,21],[12,14,22,23],[6],[],[2,3,5,10]]]

# DWORD   dwMioWong[5][12] = [
#                             [6,7,10,11,12,13,19,22,23],[3,10,12,19],[4,7,9,12,13,20,21],[5,15,16,19],[1,2,11,12,14,19,22,23],[1,4,7,9,13,20,21],
#                              [6,10,11,13,19],[10,15,16,19],[4,7,9,12,13,20,21,22,23],[5,8,19],[2,7,8,11,12,14,19],[1,4,7,8,9,11,13,15,16,20,21],
#
#                             [1,3,6,7,14],[1,2,7,13,16],[1,3,11,13,15,16],[1,3,7,10,11,18,19],[3,4,7,9,10,13],[3,11,12,14,16],
#                             [1,2,3,4,5,7,14,15,16],[1,2,7,8,12,13,22,23],[1,8,11,14],[1,7,10,11,16],[4,5,9,10,13,15,16],[12,14,22,23],
#
#                             [5],[4,9,15,22,23],[2,6],[], [], [6,15,22,23],
#                             [8], [4,9], [2,6], [15,22,23], [], [6],
#
#                             [9,16], [5,6], [5], [2,4,13,14], [6], [],
#                             [9], [3,5,6], [5], [2,4,13,14], [1,6], [],
#
#                             [15,21], [8,11,14,20], [8,10,22,23], [6,8,9,12,21], [5,8,15,16,20], [2,5,8,10],
#                             [12,21,22,23], [11,14,20], [3,10,15,16], [3,6,9,12,21], [3,20,22,23], [2,3,5,10]]

# 內湖 林信銘老師
#  廟 1,旺2 ,得地3 ,利益4 ,平和5 ,不得地6 ,陷7 ,落閑0(數字外圍圈表示)
#
#  nEarth : 地支
#  回傳: BYTE 0x10: 落閑 ;  0x00 : 無落閑
#        . : 1 ~ 7  表示 廟 1,旺2 ,得地3 ,利益4 ,平和5 ,不得地6 ,陷7

  @@MioWongLin = [
    [0x15,0x01,0x02,0x02,0x13,0x02,0x01,0x01,0x02,0x02,0x03,0x12],
    [0x01,0x07,0x03,0x02,0x04,0x15,0x01,0x07,0x03,0x02,0x04,0x05],
    [0x07,0x07,0x02,0x01,0x02,0x02,0x02,0x03,0x03,0x05,0x07,0x07],
    [0x02,0x01,0x03,0x04,0x01,0x05,0x02,0x01,0x13,0x04,0x01,0x05],
    [0x02,0x06,0x04,0x05,0x05,0x01,0x07,0x06,0x02,0x05,0x05,0x01],
    [0x05,0x04,0x05,0x05,0x04,0x07,0x05,0x04,0x01,0x05,0x04,0x07],
    [0x01,0x01,0x01,0x03,0x01,0x03,0x02,0x01,0x03,0x02,0x01,0x03],
    [0x01,0x01,0x07,0x07,0x07,0x07,0x07,0x04,0x04,0x02,0x02,0x01],
    [0x02,0x01,0x15,0x04,0x01,0x07,0x02,0x01,0x15,0x04,0x01,0x07],
    [0x02,0x06,0x01,0x01,0x05,0x02,0x02,0x05,0x01,0x01,0x05,0x02],
    [0x01,0x01,0x01,0x07,0x13,0x03,0x01,0x03,0x01,0x07,0x13,0x03],
    [0x01,0x02,0x01,0x01,0x01,0x15,0x01,0x02,0x07,0x13,0x01,0x07],
    [0x02,0x01,0x01,0x02,0x11,0x05,0x02,0x01,0x01,0x02,0x01,0x15],
    [0x01,0x02,0x03,0x07,0x02,0x15,0x01,0x02,0x13,0x07,0x02,0x05],
    [0x01,0x00,0x01,0x01,0x00,0x01,0x01,0x00,0x01,0x01,0x00,0x01],
    [0x03,0x01,0x07,0x01,0x03,0x01,0x07,0x01,0x03,0x01,0x07,0x01],
    [0x03,0x01,0x05,0x02,0x03,0x01,0x07,0x02,0x03,0x01,0x07,0x02],
    [0x07,0x01,0x00,0x07,0x01,0x00,0x07,0x01,0x00,0x07,0x01,0x00],
    [0x00,0x01,0x07,0x00,0x01,0x07,0x00,0x01,0x07,0x00,0x01,0x07],
    [0x07,0x03,0x01,0x04,0x07,0x03,0x01,0x04,0x07,0x03,0x01,0x04],
    [0x07,0x03,0x01,0x04,0x07,0x03,0x01,0x04,0x07,0x03,0x01,0x04],
    [0x07,0x01,0x01,0x07,0x07,0x01,0x07,0x07,0x01,0x01,0x01,0x01],
    [0x01,0x07,0x01,0x07,0x07,0x01,0x01,0x01,0x01,0x07,0x07,0x01],
    [0x07,0x07,0x01,0x01,0x07,0x07,0x07,0x07,0x07,0x01,0x01,0x07],
    [0x07,0x07,0x07,0x01,0x07,0x07,0x07,0x07,0x07,0x01,0x01,0x01],
    [0x07,0x01,0x07,0x01,0x07,0x07,0x07,0x07,0x01,0x07,0x07,0x07],
    [0x07,0x01,0x07,0x01,0x07,0x07,0x07,0x07,0x01,0x07,0x07,0x07],
    [0x04,0x01,0x04,0x04,0x01,0x04,0x04,0x04,0x04,0x04,0x04,0x04],
    [0x07,0x01,0x04,0x02,0x01,0x03,0x07,0x04,0x07,0x07,0x01,0x01],
    [0x07,0x01,0x04,0x02,0x01,0x03,0x07,0x04,0x07,0x07,0x01,0x07],
    [0x07,0x01,0x04,0x02,0x01,0x03,0x01,0x04,0x01,0x07,0x01,0x01],
    [0x04,0x01,0x07,0x02,0x03,0x07,0x07,0x03,0x04,0x04,0x07,0x07]
  ]

#  nStarLevel : 星級 1:A,2:B,3:C
#  dwStar : 星之值
  @@MioWongStar = [
    [A_STAR,1],[A_STAR,2],[A_STAR,3],[A_STAR,4],[A_STAR,5],[A_STAR,6],[A_STAR,7],[A_STAR,8],
    [A_STAR,9],[A_STAR,10],[A_STAR,11],[A_STAR,12],[A_STAR,13],[A_STAR,14],[A_STAR,19],[A_STAR,15],
    [A_STAR,16],[A_STAR,20],[A_STAR,21],[A_STAR,22],[A_STAR,23],[B_STAR,33],[B_STAR,34],[B_STAR,3],
    [B_STAR,4],[B_STAR,11],[B_STAR,12],[B_STAR,15],[A_STAR,26],[A_STAR,27],[A_STAR,28],[A_STAR,29]
  ]


  def Star.IsPanType?(nPanType)
    return (nPanType.between?(Cfate::PAN_NORMAL,Cfate::PAN_FLOWMIN))
  end

  def Star.HouseIndex2EarthIndex(nHouseIndex) #寅宮為0轉變為2
    return Earth.Modify(nHouseIndex + 2)
  end

  def Star.EarthIndex2HouseIndex(nEarthIndex) #寅宮為0轉變為2
    return Earth.Modify(nEarthIndex - 2)
  end

  def Star.HouseIndex2Earth(nHouseIndex) #寅宮為0轉變為3
    nEarthIndex = Star.HouseIndex2EarthIndex(nHouseIndex)
    return Earth.EarthIndex2Earth(nEarthIndex)
  end

  def Star.Earth2HouseIndex(nEarth) #寅宮為3轉變為0
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)
    return EarthIndex2HouseIndex(nEarthIndex)
  end

  def Star.Earth2House(nEarth) #寅宮為3轉變為1
    nHouseIndex = Star.Earth2HouseIndex(nEarth)
    return Earth.EarthIndex2Earth(nHouseIndex)
  end

  def Star.EarthIndex2Earth(nEarthIndex) #子宮為0轉變為1
    return (nEarthIndex + 1)
  end

  def Star.Star2StarIndex(nStar)
    return (nStar - 1)
  end

  def Star.StarIndex2Star(nStarIndex)
    return (nStarIndex + 1)
  end

  def Star.GetStarInHousePower(sHashKey,nHouseIndex)
    return nil if @@StarInHousePower[sHashKey] == nil
    return @@StarInHousePower[sHashKey][nHouseIndex]
  end

  def Star.GetStarInHousePowers()
    return @@StarInHousePower
  end
    # 命宮  兄弟  夫妻  子女  財帛  疾厄  遷移  朋友  官祿  田宅  福德  父母
  #   @@StarInHousePower = {
  #     'A_STAR_1' => [120,nil,nil,nil,nil,nil,nil,nil,120,nil,120,nil],     # 紫微
  #   'A_STAR_2' => [nil,120,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil],     # 天機
  #   'A_STAR_3' =>  [nil,nil,nil,nil,nil,nil,nil,nil,120,nil,nil,nil],    # 太陽
  #   'A_STAR_4' =>  [nil,nil,nil,nil,120,nil,nil,nil,nil,nil,nil,nil],    # 武曲
  #   'A_STAR_5' =>  [nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,120,nil],    # 天同
  #   'A_STAR_6' =>  [nil,80,nil,nil,nil,nil,nil,nil,120,nil,nil,nil],     # 廉貞
  #   'A_STAR_7' =>  [nil,nil,nil,nil,120,nil,nil,nil,nil,120,nil,nil],    # 天府
  #   'A_STAR_8' =>  [nil,nil,nil,nil,120,nil,nil,nil,nil,120,nil,nil],    # 太陰
  #   'A_STAR_9' =>  [nil,80,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil],     # 貪狼
  #   'A_STAR_10' =>  [nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil],   # 巨門
  #   'A_STAR_11' =>  [nil,nil,nil,nil,nil,nil,nil,nil,120,nil,nil,nil],   # 天相
  #   'A_STAR_12' =>  [nil,nil,nil,nil,nil,120,nil,nil,nil,nil,nil,120],   # 天梁
  #   'A_STAR_14' =>  [nil,80,120,120,nil,nil,nil,120,nil,nil,nil,nil],    # 破軍
  #   'A_STAR_19' =>  [nil,nil,nil,nil,150,nil,nil,nil,nil,150,nil,nil]    # 祿存
  # }
  # after 2020/12/12
  @@StarInHousePower = {
      'A_STAR_1' => [120,nil,nil,nil,nil,nil,nil,nil,120,nil,120,nil],     # 紫微
    'A_STAR_2' => [nil,120,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil],     # 天機
    'A_STAR_3' =>  [nil,nil,nil,nil,nil,nil,nil,nil,120,nil,nil,nil],    # 太陽
    'A_STAR_4' =>  [nil,nil,nil,nil,120,nil,nil,nil,nil,nil,nil,nil],    # 武曲
    'A_STAR_5' =>  [nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,120,nil],    # 天同
    'A_STAR_6' =>  [nil,80,nil,nil,nil,nil,nil,nil,120,nil,nil,nil],     # 廉貞
    'A_STAR_7' =>  [nil,nil,nil,nil,120,nil,nil,nil,nil,120,nil,nil],    # 天府
    'A_STAR_8' =>  [nil,nil,nil,nil,120,nil,nil,nil,nil,120,nil,nil],    # 太陰
    'A_STAR_9' =>  [nil,80,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil],     # 貪狼
    'A_STAR_10' =>  [nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil],   # 巨門
    'A_STAR_11' =>  [nil,nil,nil,nil,nil,nil,nil,nil,120,nil,nil,nil],   # 天相
    'A_STAR_12' =>  [nil,nil,nil,nil,nil,120,nil,nil,nil,nil,nil,120],   # 天梁
    'A_STAR_13' =>  [nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil],    # 七殺
    'A_STAR_14' =>  [nil,80,120,120,nil,nil,nil,120,nil,nil,110,nil],    # 破軍
    'A_STAR_19' =>  [nil,nil,nil,nil,150,nil,nil,nil,nil,150,nil,nil],    # 祿存
    'B_STAR_9' =>  [nil,nil,nil,nil,nil,nil,nil,nil,110,nil,nil,nil],    # 天官
    'B_STAR_10' =>  [110,nil,nil,nil,nil,nil,nil,nil,nil,nil,120,nil],    # 天福
    'B_STAR_20' =>  [nil,nil,nil,nil,nil,nil,nil,nil,110,nil,nil,nil],    # 台輔
    'B_STAR_21' =>  [nil,nil,nil,nil,nil,nil,nil,nil,110,nil,nil,nil],    # 封誥
    'B_STAR_24' =>  [nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil],    # 三台
    'B_STAR_25' =>  [nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil],    # 八座
    'B_STAR_26' =>  [120,nil,nil,nil,nil,nil,nil,nil,110,nil,nil,110],    # 恩光
    'B_STAR_27' =>  [120,nil,nil,nil,nil,nil,nil,nil,110,nil,nil,nil],    # 天貴
    'B_STAR_28' =>  [120,nil,nil,nil,nil,nil,nil,nil,nil,nil,120,nil],    # 天才
    'B_STAR_29' =>  [nil,nil,nil,nil,nil,110,nil,nil,nil,nil,nil,nil],    # 天壽
    'DOCTOR_6' =>  [110,nil,nil,nil,nil,nil,nil,nil,110,nil,nil,nil]    # 奏書
}


  def Star.GetStarInHouseSign(sHashKey,nHouseIndex)
    return 0 if @@StarInHouseSign[sHashKey] == nil
    return @@StarInHouseSign[sHashKey][nHouseIndex]
  end

  def Star.GetStarInHouseSigns()
    return @@StarInHouseSign
  end
    # 命宮  兄弟  夫妻  子女  財帛  疾厄  遷移  朋友  官祿  田宅  福德  父母
  # @@StarInHouseSign = {
  # 'A_STAR_15'   =>  [1,1,1,1,1,-1,1,1,1,1,1,1],                   # 文昌
  # 'A_STAR_16'   =>  [1,1,1,1,1,-1,1,1,1,1,1,1],                   # 文曲
  # 'A_STAR_17'   =>  [1,1,1,1,1,-1,1,1,1,1,1,1],                   # 左輔
  # 'A_STAR_18'   =>  [1,1,1,1,1,-1,1,1,1,1,1,1],                   # 右弼
  # 'A_STAR_19'   =>  [1,1,1,1,1,-1,1,1,1,1,1,1],                   # 祿存
  # 'A_STAR_20'   =>  [-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1],        # 擎羊
  # 'A_STAR_21'   =>  [-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1],        # 陀羅
  # 'A_STAR_22'   =>  [1,-1,-1,-1,1,-1,-1,-1,1,-1,-1,1],            # 火星
  # 'A_STAR_23'   =>  [1,-1,-1,-1,1,-1,-1,-1,1,-1,-1,1],            # 鈴星
  # 'A_STAR_24'   =>  [1,1,1,1,-1,1,-1,-1,-1,1,-1,1],               # 天魁
  # 'A_STAR_25'   =>  [1,1,1,1,-1,1,-1,-1,-1,1,-1,1],               # 天鉞
  # 'B_STAR_1'    =>  [-1,-1,-1,-1,-1,1,-1,-1,-1,-1,-1,-1],         # 地劫
  # 'B_STAR_2'    =>  [-1,-1,-1,-1,-1,1,-1,-1,-1,-1,-1,-1],         # 天空
  # 'B_STAR_3'    =>  [-1,1,1,1,1,-1,1,1,1,1,1,1],                  # 天刑
  # 'B_STAR_8'    =>  [1,1,1,1,1,-1,1,1,1,1,1,1],                   # 天喜
  # 'B_STAR_10'   =>  [1,1,1,1,1,1,1,1,1,1,-1,1],                   # 天福
  # 'B_STAR_11'   =>  [-1,1,1,1,1,-1,1,1,1,1,-1,1],                 # 天哭
  # 'B_STAR_12'   =>  [-1,1,1,1,1,-1,1,1,1,1,1,1],                  # 天虛
  # 'B_STAR_16'   =>  [1,-1,-1,-1,-1,1,1,-1,-1,-1,1,-1],            # 孤辰
  # 'B_STAR_17'   =>  [1,-1,-1,-1,-1,1,1,-1,-1,-1,1,-1],            # 寡宿
  # 'B_STAR_18'   =>  [-1,1,1,1,1,-1,1,1,1,1,-1,-1],                # 蜚廉
  # 'B_STAR_19'   =>  [-1,1,1,1,1,-1,1,1,1,1,-1,-1],                # 破碎
  # 'YEARSTAR_5'  =>  [-1,1,1,1,1,-1,1,1,-1,1,-1,1],                # 華蓋
  # 'YEARSTAR_6'  =>  [1,1,1,1,-1,-1,1,1,-1,-1,1,1],                # 劫煞
  # 'YEARSTAR_10' =>  [1,1,1,1,1,-1,1,1,1,1,1,1],                   # 咸池
  # 'DOCTOR_4'    =>  [1,1,1,1,-1,-1,1,1,-1,1,1,1],                 # 小耗
  # 'DOCTOR_9'    =>  [-1,1,1,1,1,-1,1,1,1,1,-1,1],                 # 病符
  # 'DOCTOR_10'   =>  [1,1,1,1,-1,1,1,1,-1,-1,1,1],                 # 大耗
  # 'DOCTOR_11'   =>  [-1,1,1,1,-1,1,-1,1,1,-1,1,1],                # 伏兵
  # 'DOCTOR_12'   =>  [-1,1,1,1,-1,1,-1,1,1,-1,1,1],                # 官府
  # 'YEARGOD_1'   =>  [-1,1,1,1,1,1,-1,1,1,1,1,1],                  # 歲建
  # 'YEARGOD_3'   =>  [-1,1,1,1,1,-1,1,1,1,1,1,-1],                 # 喪門
  # 'YEARGOD_5'   =>  [1,1,1,1,-1,1,1,-1,-1,-1,1,1],                # 官符
  # 'YEARGOD_6'   =>  [-1,1,1,1,-1,-1,1,-1,-1,-1,1,1],              # 小耗
  # 'YEARGOD_7'   =>  [-1,1,1,1,-1,-1,1,-1,-1,-1,1,1],              # 大耗
  # 'YEARGOD_9'   =>  [1,-1,-1,-1,1,-1,1,-1,-1,1,1,-1],             # 白虎
  # 'YEARGOD_11'  =>  [-1,-1,-1,-1,1,-1,1,-1,1,1,-1,-1],            # 弔客
  # 'YEARGOD_12'  =>  [-1,-1,-1,-1,1,-1,1,-1,1,1,-1,-1]             # 病符
  # }
    # 命宮  兄弟  夫妻  子女  財帛  疾厄  遷移  朋友  官祿  田宅  福德  父母
    # after 2020/12/12 0原值 1絕對正 -1絕對負
  @@StarInHouseSign = {
  'A_STAR_15'   =>  [0,0,0,0,0,-1,0,0,0,0,0,0],                   # 文昌
  'A_STAR_16'   =>  [0,0,0,0,0,-1,0,0,0,0,0,0],                   # 文曲
  'A_STAR_17'   =>  [0,0,0,0,0,-1,0,0,0,0,0,0],                   # 左輔
  'A_STAR_18'   =>  [0,0,0,0,0,-1,0,0,0,0,0,0],                   # 右弼
  'A_STAR_19'   =>  [0,0,0,0,0,-1,0,0,0,0,0,0],                   # 祿存
  'A_STAR_20'   =>  [-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1],        # 擎羊
  'A_STAR_21'   =>  [-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1],        # 陀羅
  'A_STAR_22'   =>  [0,-1,-1,-1,0,-1,-1,-1,0,-1,-1,0],            # 火星
  'A_STAR_23'   =>  [0,-1,-1,-1,0,-1,-1,-1,0,-1,-1,0],            # 鈴星
  'A_STAR_24'   =>  [1,1,1,1,-1,1,-1,-1,-1,1,-1,1],               # 天魁
  'A_STAR_25'   =>  [1,1,1,1,-1,1,-1,-1,-1,1,-1,1],               # 天鉞
  'B_STAR_1'    =>  [-1,-1,-1,-1,-1,0,-1,-1,-1,-1,-1,-1],         # 地劫
  'B_STAR_2'    =>  [-1,-1,-1,-1,-1,0,-1,-1,-1,-1,-1,-1],         # 天空
  'B_STAR_3'    =>  [-1,0,0,0,0,-1,0,0,0,0,0,0],                  # 天刑
  'B_STAR_8'    =>  [0,0,0,0,0,-1,0,0,0,0,0,0],                   # 天喜
  'B_STAR_11'   =>  [-1,0,0,0,0,-1,0,0,0,0,-1,0],                 # 天哭
  'B_STAR_12'   =>  [-1,0,0,0,0,-1,0,0,0,0,-1,0],                 # 天虛
  'B_STAR_15'   =>  [0,0,0,0,0,-1,0,0,0,0,0,0],                   # 紅鸞
  'B_STAR_16'   =>  [0,-1,-1,-1,-1,0,0,-1,-1,-1,0,-1],            # 孤辰
  'B_STAR_17'   =>  [0,-1,-1,-1,-1,0,0,-1,-1,-1,0,-1],            # 寡宿
  'B_STAR_18'   =>  [-1,0,0,0,0,-1,0,0,0,0,-1,-1],                # 蜚廉
  'B_STAR_19'   =>  [-1,0,0,0,0,-1,0,0,0,0,-1,-1],                # 破碎
  'YEARSTAR_5'  =>  [-1,0,0,0,0,-1,0,0,-1,0,-1,0],                # 華蓋
  'YEARSTAR_6'  =>  [0,0,0,0,-1,-1,0,0,-1,-1,0,0],                # 劫煞
  'YEARSTAR_10' =>  [0,0,0,0,0,-1,0,0,0,0,0,0],                   # 咸池
  'DOCTOR_4'    =>  [0,0,0,0,-1,-1,0,0,-1,0,0,0],                 # 小耗
  'DOCTOR_7'    =>  [0,0,0,0,0,-1,0,0,0,0,-1,0],                  # 飛廉
  'DOCTOR_9'    =>  [-1,0,0,0,0,-1,0,0,0,0,-1,0],                 # 病符
  'DOCTOR_10'   =>  [0,0,0,0,-1,0,0,0,-1,-1,0,0],                 # 大耗
  'DOCTOR_11'   =>  [-1,0,0,0,-1,0,-1,0,0,-1,0,0],                # 伏兵
  'DOCTOR_12'   =>  [-1,0,0,0,-1,0,-1,0,0,-1,0,0],                # 官府
  'YEARGOD_1'   =>  [-1,0,0,0,0,0,-1,0,0,0,0,0],                  # 歲建
  'YEARGOD_3'   =>  [-1,0,0,0,0,-1,0,0,0,0,0,-1],                 # 喪門
  'YEARGOD_5'   =>  [0,0,0,0,-1,0,0,-1,-1,-1,0,0],                # 官符
  'YEARGOD_6'   =>  [-1,0,0,0,-1,-1,0,-1,-1,-1,0,0],              # 小耗
  'YEARGOD_7'   =>  [-1,0,0,0,-1,-1,0,-1,-1,-1,0,0],              # 大耗
  'YEARGOD_9'   =>  [0,-1,-1,-1,0,-1,0,-1,-1,0,0,-1],             # 白虎
  'YEARGOD_11'  =>  [-1,-1,-1,-1,0,-1,0,-1,0,0,-1,-1],            # 弔客
  'YEARGOD_12'  =>  [-1,-1,-1,-1,0,-1,0,-1,0,0,-1,-1]             # 病符
  }

  # 1.7.1 四化固定值 [4]
  @@FuaFix = [8, 5, 2, -10]

  # 四化飛碰表 [4][4]
  # 說明：以宮之宮干飛出，與其他的四化是否有相碰撞。
  @@FuaFly =    [[2, 8, 6,-10],
                 [8, 2, 7, -5],
                 [6, 7,-2, -3],
                 [-10,-5,-3,-2]]

  @@OrgFuaRate = [50, 50, 15]
  @@SkyFuaRate = [50, 50, 15]
  @@FuaPongRate = [50, 50, 15]


  # 1.6、十二長生加權表
  # 加權計算方式：以星曜在各宮計算完成，最後再加權分數，例如星曜結算完成數為30，加權為30，計算式為30*1.3=39，
  # 若加權為-30，計算式為30*(1-0.3)=21
   #命宮  兄弟  夫妻  子女  財帛  疾厄  遷移  朋友  官祿  田宅  福德  父母
  # @@HouseGodPower = [
  #             [ 10,  10,  10,  10,   10,  -10,   10,   10,   10,   10,   10,   10],     # 長生
  #           [ 20,  20,  20,  20,   20,  -20,   20,   20,   20,   20,   20,   20],     # 沐浴
  #           [ 30,  30,  30,  30,   30,  -30,   30,   30,   30,   30,   30,   30],     # 冠帶
  #           [ 40,  40,  40,  40,   40,  -40,   40,   40,   40,   40,   40,   40],     # 臨官
  #           [ 50,  50,  50,  50,   50,  -50,   50,   50,   50,   50,   50,   50],     # 帝旺
  #             [  0,   0,   0,   0,    0,    0,    0,    0,    0,    0,    0,    0],     # 衰
  #             [-20, -20, -20, -20,  -20,   20,  -20,  -20,  -20,  -20,  -20,  -20],     # 病
  #             [-40, -40, -40, -40,  -40,   30,  -40,  -40,  -40,  -40,  -40,  -40],     # 死
  #             [-20, -20, -20, -20,  -20,   40,  -20,  -20,  -20,  -20,  -20,  -20],     # 墓
  #             [-50, -50, -50, -50,  -50,   50,  -50,  -50,  -50,  -50,  -50,  -50],     # 絕
  #             [-10, -10, -10, -10,  -10,   10,  -10,  -10,  -10,  -10,  -10,  -10],     # 胎
  #             [  0,   0,   0,   0,    0,    0,    0,    0,    0,    0,    0,    0]      # 養
  #           ]

  # after 2020/12/12
  @@HouseGodPower = [
              [ 20,  20,  20,  20,   20,  -10,   20,   20,   20,   20,   20,   20],     # 長生
            [ 20,  20,  20,  20,   20,  -20,   20,   20,   20,   20,   20,   20],     # 沐浴
            [ 30,  30,  30,  30,   30,  -30,   30,   30,   30,   30,   30,   30],     # 冠帶
            [ 40,  40,  40,  40,   40,  -40,   40,   40,   40,   40,   40,   40],     # 臨官
            [ 50,  50,  50,  50,   50,  -50,   50,   50,   50,   50,   50,   50],     # 帝旺
              [  0,   0,   0,   0,    0,    0,    0,    0,    0,    0,    0,    0],     # 衰
              [-20, -20, -20, -20,  -20,   20,  -20,  -20,  -20,  -20,  -20,  -20],     # 病
              [-40, -40, -40, -40,  -40,   30,  -40,  -40,  -40,  -40,  -40,  -40],     # 死
              [10, 10, 10, 10,  10,   40,  10,  10,  10,  10,  10,  10],     # 墓
              [-50, -50, -50, -50,  -50,   50,  -50,  -50,  -50,  -50,  -50,  -50],     # 絕
              [-10, -10, -10, -10,  -10,   10,  -10,  -10,  -10,  -10,  -10,  -10],     # 胎
              [  0,   0,   0,   0,    0,    0,    0,    0,    0,    0,    0,    0]      # 養
            ]

  def Star.sex_word(bSex)
    s = bSex ? "1" : "0"
    return Pm.t("IDS_S_UI_MF_#{s}")
  end
end




