class Divination
  # data 區塊
  def data()
  	h = Hash.new
    nYearEarth = Xdate.GetGanZhiEarth(@nGanZhiYear)
    nMonthEarth = Xdate.GetGanZhiEarth(@nGanZhiMonth)
    nDayEarth = Xdate.GetGanZhiEarth(@nGanZhiDay)

    # 姓名
    h["name"] = @name
    # 事 由
    h["question"] = @question
    # 國曆
  	a = Divination.d_West_Date_str(@ParAll[Cfate::PAR_PAN_YEAR_DISPLAY],@year, @month,@day, @hour)
    h["westen_date"] = Divination.no_2_bignos(a)
    # 干支
  	h["date_ganzhi"] = Divination.d_ganzhi_date_str(@nGanZhiYear,@nGanZhiMonth,@nGanZhiDay,@nGanZhiHour)

    # 干支六親
  	h["ganzhi_liuqin"] = Divination.d_ganzhi_liu_qins_str(nYearEarth,nMonth<PERSON>arth,nDayEarth,@all_ylqgzws)

    # 本卦
    a = Divination.d_gua_info(@wai,@nei)
    h["ben_gua_info"] = Divination.no_2_bignos(a)
    # 變卦
    a = Divination.d_gua_info(@bian_wai,@bian_nei)
    h["bian_gua_info"] = Divination.no_2_bignos(a)

    # 卦反吟
    h["gua_fan_yin"] = Divination.Cr_find_gua_fan_yin_str(@wai,@nei,@bian_wai,@bian_nei)
    # 伏吟
    h["fu_yin"] = Divination.Cr_find_fu_yin(@wai,@nei,@bian_wai,@bian_nei)
  	return h
  end

  def Divination.bigno(s)
    n = s.to_i
    if (n == 0 && s == "0") then
      return Divination.liuyaogua_str("big_no_#{n}")
    elsif (n >= 1 && n <=9) then
      return Divination.liuyaogua_str("big_no_#{n}")
    else
      return s
    end
  end
  def Divination.bignos(n)
    s = n.to_s
    a = s.split(//)
    a2 = a.map! {|n| Divination.bigno(n)}
    return a2.join
  end
  def Divination.no_2_bignos(a)
    return a.map! {|n| Divination.bignos(n)}
  end

  # data 區塊
  def Divination.d_West_Date_str(par_YearDisplay,y,m,d,h)
    if par_YearDisplay == nil || par_YearDisplay == Cfate::PAN_DISPLAY_WEST then
      return Xdate.west_date_str_array(y,m,d,h,true)
    else
      return Xdate.china_date_str_array(y,m,d,h,true)
    end
  end
  def Divination.d_ganzhi_date_str(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour)
    return Xdate.ganzhi_date_str_array(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour) 
  end

  def Divination.d_ganzhi_liu_qins_str(nYearEarth,nMonthEarth,nDayEarth,all_ylqgzws)
    ylqgzws = Divination.par_all_gua_ylqgzws(all_ylqgzws)

    a = Array.new
    a.push(Divination.d_earth_2_liu_qin(nYearEarth,ylqgzws))
    a.push(Divination.d_earth_2_liu_qin(nMonthEarth,ylqgzws))
    a.push(Divination.d_earth_2_liu_qin(nDayEarth,ylqgzws))
    return Divination.liu_qins_str(a)
  end
  def Divination.d_earth_2_liu_qin(nEarth,ylqgzws)
    a = Divination.d_earth_2_ylqgzw(nEarth,ylqgzws)
    liu_qin = Divination.fu_shen_liu_qin(a)
    return liu_qin
  end
  def Divination.d_earth_2_ylqgzw(nEarth,ylqgzws)
    five = Divination.Earth_Five(nEarth)
    return Divination.par_find_ylqgzw_by_wu_xing(ylqgzws,five)
  end
  def Divination.d_gua_info(wai,nei)
    a = Array.new
    a.push(Divination.Gua_64_name(wai,nei))
    a.push(Divination.Gua_64_info_gua_group_str(wai,nei))
    a.push(Divination.Gua_64_info_special_no(wai,nei))
    a.push(Divination.Gua_64_info_five_str(wai,nei))
    a.push(Divination.Gua_64_info_special_str(wai,nei))
    a.push(Divination.Gua_64_he_chong_str(wai,nei))
    return a
  end

end
