require("Xdate.rb")
require("WeenApi.rb")


module Controller_Api_Profate_Order
  def profate_order_get_data_from_ween_billing(user_id,profate_event,point_product,client_ip,hPars)
    # 準備付款相關資料
    # 建立order的所有資料庫，回傳你背景的url,需要傳給歐付寶的參數
    if (hPars["payment"] == nil) then
      hPayment = hPars
    else
      hPayment = hPars["payment"]
    end
    hProfate = create_profate_orders(user_id,profate_event,point_product,hPayment)
    h = ween_billing_get_allpay_checkout_data(hProfate,client_ip)
    if (profate_event != nil) then
      h[:uuid] = profate_event.uuid
    else
      h[:uuid] = ""
    end
    return h
  end
  def create_profate_orders(user_id,profate_event,point_product,hPayment)
    clientbackurl = hPayment["ClientBackURL"]
    h = Hash.new
    return_url = "profate_order"
    if (profate_event == nil) then
      clientbackurl_final = "#{clientbackurl}"
    else
      clientbackurl_final = "#{clientbackurl}/#{profate_event.uuid}"
    end
    # profate自己的order
    h_profate_order = create_profate_order_info(user_id,point_product,return_url,clientbackurl_final,profate_event,hPayment)

    return h_profate_order
  end
  def create_profate_order_info(user_id,point_product,return_url,clientbackurl_final,profate_event,hPayment)
    choosepayment = hPayment["PaymentType"]
    paymenttype = Allpay_ChoosePayment.check_choosepayment(choosepayment)
    email = hPayment["email"]
    phoneno = hPayment["phoneno"]
    username = hPayment["username"]
    devicesource = ["P","M"].include?(hPayment["devicesource"]) ? hPayment["devicesource"] : "P"
    # 存入profate_orders
    p_order = save_profate_order(user_id,point_product,profate_event)

    h = Hash.new
    h["point_product"] = point_product.serializable_hash
    h["profate_order"] = p_order.serializable_hash
    h["return_url"] = return_url
    h["clientbackurl"] = clientbackurl_final
    h["paymenttype"] = paymenttype
    h["email"] = email
    h["phoneno"] = phoneno
    h["username"] = username
    h["DeviceSource"] = devicesource

    h2 = profate_orderinfo_2_orderinfo(h)
    return h2
  end
  def profate_orderinfo_2_orderinfo(h)
    hOut = Hash.new

    hOut["server_name"] = "Profate Server"
    hOut["user_id"] = h["profate_order"]["user_id"]
    hOut["orderno"] = h["profate_order"]["id"]
    hOut["product_id"] = h["point_product"]["id"]
    hOut["original_amount"] = h["point_product"]["amount"]
    hOut["discount_amount"] = h["point_product"]["amount"]
    hOut["paid_amount"] = h["point_product"]["amount"]
    hOut["amount"] = h["point_product"]["amount"]
    hOut["paymenttype"] = h["paymenttype"]
    hOut["feedback_url"] = h["return_url"]
    hOut["receive_url"] = h["clientbackurl"]

    hOut["paytitle"] = "#{h["point_product"]["p_name"]}X1"
    hOut["paymemo"] = h["point_product"]["p_desc"]
    hOut["DeviceSource"] = h["DeviceSource"] # "P"  # pc

    hOut["email"] = h["email"]
    hOut["phoneno"] = h["phoneno"]
    hOut["username"] = h["username"]

    return hOut
  end

  def find_point_product(event_name,amount)
    point_product = ProfatePointProduct.check_type(event_name).check_amount(amount).last
    return point_product
  end
  def find_point_product_by_id(id)
    point_product = ProfatePointProduct.find_by_id(id)
    return point_product
  end
  def save_profate_order(user_id,point_product,profate_event)
    p_order = ProfateOrder.new
    p_order.user_id = user_id
    p_order.point_product_id = point_product.id
    p_order.p_name = point_product.p_name
    p_order.p_type = point_product.p_type
    p_order.billing_order_id = nil
    p_order.amount = point_product.amount
    p_order.point = point_product.point
    p_order.free_coupon = point_product.free_coupon
    if (profate_event != nil) then
      p_order.profate_event_id = profate_event.id
    end
    p_order.hParam = nil
    p_order.status = -1

    p_order.save!
    return p_order
  end

  def finish_profate_order(order_id,is_RtnCode_valid,rc)
    oOrder = Order.find_by_id(order_id)
    p_order = ProfateOrder.find_by_id(oOrder.orderno)

    p_order = update_profate_order_status(p_order,order_id,rc,is_RtnCode_valid)
    save_profate_payment(p_order)
    if (p_order.p_type == "buy") then
      profate_user_points_add(p_order)
      profate_user_add_coupons(p_order)
    else
      update_profate_event_status(p_order)
    end
  end
  def update_profate_order_status(p_order,order_id,rc,is_RtnCode_valid)
    if (p_order != nil) then
      p_order.billing_order_id = order_id
      p_order.status = is_RtnCode_valid ? 0 : rc
      p_order.save!
    end
    return p_order
  end
  def save_profate_payment(p_order)
    if (p_order == nil) then
      return nil
    end
    p_payment = ProfatePayment.new
    p_payment.user_id = p_order.user_id
    p_payment.profate_order_id = p_order.id
    p_payment.point_product_id = p_order.point_product_id
    p_payment.p_name = p_order.p_name
    p_payment.p_type = p_order.p_type
    p_payment.amount = p_order.amount
    p_payment.point = p_order.point
    p_payment.save!
    return p_payment
  end
  def save_profate_payment2(user_id,p_name,p_type,point)
    p_payment = ProfatePayment.new
    p_payment.user_id = user_id
    p_payment.p_name = p_name
    p_payment.p_type = p_type
    p_payment.point = point
    p_payment.save!
    return p_payment
  end

  def profate_user_points_add(p_order)
    user_point = ProfateUserPoint.where(:user_id => p_order.user_id).last
    if (user_point == nil) then
      return
    end
    user_point.point += p_order.point
    user_point.save!
  end
  def profate_user_add_coupons(p_order)
    coupon_count = p_order.free_coupon
    (0..coupon_count).each do |i|
      coupon = ProfateCoupon.new
      coupon.uuid = get_profateevent_coupon()
      coupon.buy_user_id = p_order.user_id
      coupon.flag = 1 # 已發出 1
      coupon.profate_order_id = p_order.id
      coupon.save!
    end
  end

  def update_profate_event_status(p_order)
    p_event = ProfateEvent.find(p_order.profate_event_id)
    if (p_event != nil) then
      p_event.flag = 2 # 已付款
      p_event.save!
    end
  end
end
