require("Star.rb")
require("Score.rb")
require("Pm.rb")
require("Talent_Star.rb")
require("Talent_Sti.rb")
require("Jianggong.rb")

class Talent
	@Obj_Score
	@Obj_Star
    def Talent.get_talent_key(category_level="L1",current_lang)
        cl = "_#{category_level}"
        cl = "" if category_level == "L1"
        return "talent#{cl}_" + current_lang
    end
    def Talent.get_talent_backup_copy_key(category_level="L1",current_lang)
        cl = "_#{category_level}"
        t = Time.now
        sBk = "%04d%02d%02d%02d%02d%02d" % [t.year,t.month,t.day,t.hour,t.min,t.sec]
        return "talent#{cl}_Backup_#{sBk}_" + current_lang
    end
    def Talent.get_talent_backup_key(category_level="L1",current_lang)
        return Talent.get_talent_key("#{category_level}_Backup",current_lang)
    end

    def gpc_getdata(nPanType,hUserInfo,hUserDefData)
        hOut = {}

        bFinal_Sucess = true

        hUdt = hUserDefData.clone
        my_current_lang = hUdt["my_current_lang"]
        @my_current_lang = my_current_lang
        nPanType = Star.LegalPanType(nPanType)
        trait_codes = hUserDefData["trait_codes"]
        hData = {}
        trait_codes.each do |trait_code|
            bFind,category_level,category_id,item_id,trait_id,trait_desc,trait_def = wc_get_pars_from_trait_code(my_current_lang,trait_code)
            if bFind then
                hUdt["category_level"] = category_level
                hScore,hDetail = getScore_trait(nPanType,hUserInfo,hUdt,category_id,item_id,trait_id)
                pr_value = wc_get_pr(category_id,category_level)

                hData[trait_code] = make_gpc_trait_score_hash(my_current_lang,hScore,pr_value,category_level,category_id,item_id,trait_id,trait_code,trait_desc,trait_def)
            else
                bFinal_Sucess = fasle
                hData[trait_code] = {"trait_code" => "","trait_desc" => "", "score" => 0, "pr" => 0, "pr_desc" => {"taitou" => "", "trait_content" => ""}}
            end
        end
        hOut["name"] = hUserInfo[Cfate::Name]
        hOut["timestamp"] = hUserInfo["timestamp"]
        hOut["timestamp_solar"] = Xdate.make_timestamp_hash(hUserInfo)
        hOut["timestamp_udt"] = hUserDefData["timestamp_udt"]
        hOut["longitude"] = hUserInfo["longitude"] if hUserInfo["longitude"] != nil
        hOut["latitude"] = hUserInfo["latitude"] if hUserInfo["latitude"] != nil
        hOut["Data"] = hData
        hOut["final_success"] = bFinal_Sucess

        return hOut
    end
    def make_gpc_trait_score_hash(my_current_lang,hScore,pr_value,category_level,category_id,item_id,trait_id,trait_code,trait_desc,trait_def)
        hOut = {}

        h_ParSetting,h_Version = wc_get_pars_setting(my_current_lang,category_level)

        c_idx,i_idx,t_idx = wc_find_cit_index2(h_ParSetting,category_id,item_id,trait_id)
        hOut["trait_code"] = trait_code
        hOut["trait_desc"] = trait_desc
        score = hScore[category_id][item_id][trait_id]["score"].to_f
        hOut["score"] = score
        pr = wc_find_score_pr2(c_idx,i_idx,t_idx,pr_value,score)
        hOut["pr"] = pr
        hOut["pr_desc"] = Talent.trait_def_pr_check(trait_def,pr,category_level)

        return hOut
    end
    def wc_get_pars_setting_all(my_current_lang)
        @my_current_lang = my_current_lang
        h_ParSetting,h_Version = wc_get_pars_setting(my_current_lang,"L1")
        h_ParSetting,h_Version = wc_get_pars_setting(my_current_lang,"L2")
        h_ParSetting,h_Version = wc_get_pars_setting(my_current_lang,"L3")
        h_ParSetting,h_Version = wc_get_pars_setting(my_current_lang,"MP")
    end
    def wc_get_talent_all(my_current_lang)
        @my_current_lang = my_current_lang
        h_talent = wc_get_talent(my_current_lang,"L1")
        h_talent = wc_get_talent(my_current_lang,"L2")
        h_talent = wc_get_talent(my_current_lang,"L3")
        h_talent = wc_get_talent(my_current_lang,"MP")
    end
    # 此函數本身都是直接計算，參數取用的則取資料庫資料，資料庫沒有，才直接計算
    def getScore(nPanType,hUserInfo,hUserDefData)
        @category_level = hUserDefData["category_level"]
        # if (@category_level == "L1") then
        #     return getScore_L1(nPanType,hUserInfo,hUserDefData)
        # elsif (@category_level == "L2") then
        #     return getScore_L2(nPanType,hUserInfo,hUserDefData)
        # elsif (@category_level == "L3") then
        #     return getScore_L3(nPanType,hUserInfo,hUserDefData)
        # elsif (@category_level == "MP") then
        #     return getScore_MP(nPanType,hUserInfo,hUserDefData)
        # end
        return wc_getScore(nPanType,hUserInfo,hUserDefData,@category_level,@category_level,false)
    end
    # 此函數本身都是直接計算，參數取用的則取資料庫資料，資料庫沒有，才直接計算
    def getScore_db(nPanType,hUserInfo,hUserDefData)
        @category_level = hUserDefData["category_level"]
        return wc_getScore(nPanType,hUserInfo,hUserDefData,@category_level,@category_level,false)
    end
  def wc_init_Scores()
    @Score_L1 = nil
    @Score_L2 = nil
    @Score_L3 = nil
    @Score_MP = nil
    @Detail_L1 = nil
    @Detail_L2 = nil
    @Detail_L3 = nil
    @Detail_MP = nil
    @Score_L1_db = nil
    @Score_L2_db = nil
    @Score_L3_db = nil
    @Score_MP_db = nil

    @Score_L1s = [{},{},{},{},{},{}]
    @Score_L2s = [{},{},{},{},{},{}]
    @Score_L3s = [{},{},{},{},{},{}]
    @Score_MPs = [{},{},{},{},{},{}]
    @Detail_L1s = [{},{},{},{},{},{}]
    @Detail_L2s = [{},{},{},{},{},{}]
    @Detail_L3s = [{},{},{},{},{},{}]
    @Detail_MPs = [{},{},{},{},{},{}]
    @Score_L1_dbs = [{},{},{},{},{},{}]
    @Score_L2_dbs = [{},{},{},{},{},{}]
    @Score_L3_dbs = [{},{},{},{},{},{}]
    @Score_MP_dbs = [{},{},{},{},{},{}]

    @Obj_Score = nil
    @Obj_Star = nil
  end
  # 從talent_scores取得資料，若無，重新計算
  def wc_getScore(nPanType,hUserInfo,hUserDefData,category_level,category_level_org,bFromDb)
    # puts "caller : #{caller[0]} : #{category_id} : #{category_level}"
    # @Score_L1 = {} if @Score_L1 == nil
    # @Score_L2 = {} if @Score_L2 == nil
    # @Score_L3 = {} if @Score_L3 == nil
    # @Score_MP = {} if @Score_MP == nil
    # @Detail_L1 = {} if @Detail_L1 == nil
    # @Detail_L2 = {} if @Detail_L2 == nil
    # @Detail_L3 = {} if @Detail_L3 == nil
    # @Detail_MP = {} if @Detail_MP == nil
    # @Score_L1_db = {} if @Score_L1_db == nil
    # @Score_L2_db = {} if @Score_L2_db == nil
    # @Score_L3_db = {} if @Score_L3_db == nil
    # @Score_MP_db = {} if @Score_MP_db == nil

    # 若沒有初始化，則初始化
    wc_init_Scores() if @Score_L1s == nil

    # 取得已算出的值，若有，則不必再計算，直接回覆
    @Score_L1 = @Score_L1s[nPanType]
    @Score_L2 = @Score_L2s[nPanType]
    @Score_L3 = @Score_L3s[nPanType]
    @Score_MP = @Score_MPs[nPanType]
    @Detail_L1 = @Detail_L1s[nPanType]
    @Detail_L2 = @Detail_L2s[nPanType]
    @Detail_L3 = @Detail_L3s[nPanType]
    @Detail_MP = @Detail_MPs[nPanType]
    @Score_L1_db = @Score_L1_dbs[nPanType]
    @Score_L2_db = @Score_L2_dbs[nPanType]
    @Score_L3_db = @Score_L3_dbs[nPanType]
    @Score_MP_db = @Score_MP_dbs[nPanType]

    if (category_level == "L1") then
        if (@Score_L1 == {}) then
            # 若沒有算過，先從資料庫中取得
            @Score_L1,@Detail_L1,@Score_L1_db = wc_getScore_db(nPanType,hUserInfo,hUserDefData,category_level,bFromDb) if @Score_L1_db == {}
            # 若資料庫中沒有，或資料版本不同，表示已修改過，則重新計算
            @Score_L1,@Detail_L1,@Score_L1_db = wc_getScore_C(nPanType,hUserInfo,hUserDefData,category_level,category_level_org) if @Score_L1_db == {}
            # 將已計算的結果儲存下來，減少重新計算的時間
            @Score_L1s[nPanType],@Detail_L1s[nPanType],@Score_L1_dbs[nPanType] = @Score_L1,@Detail_L1,@Score_L1_db
        end
        return @Score_L1,@Detail_L1,@Score_L1_db
    elsif (category_level == "L2") then
        if (@Score_L2 == {}) then
            @Score_L2,@Detail_L2,@Score_L2_db = wc_getScore_db(nPanType,hUserInfo,hUserDefData,category_level,bFromDb) if @Score_L2_db == {}
            @Score_L2,@Detail_L2,@Score_L2_db = wc_getScore_C(nPanType,hUserInfo,hUserDefData,category_level,category_level_org) if @Score_L2_db == {}
            @Score_L2s[nPanType],@Detail_L2s[nPanType],@Score_L2_dbs[nPanType] = @Score_L2,@Detail_L2,@Score_L2_db
        end
        return @Score_L2,@Detail_L2,@Score_L2_db
    elsif (category_level == "L3") then
        if (@Score_L3 == {}) then
            @Score_L3,@Detail_L3,@Score_L3_db = wc_getScore_db(nPanType,hUserInfo,hUserDefData,category_level,bFromDb) if @Score_L3_db == {}
            @Score_L3,@Detail_L3,@Score_L3_db = wc_getScore_C(nPanType,hUserInfo,hUserDefData,category_level,category_level_org) if @Score_L3_db == {}
            @Score_L3s[nPanType],@Detail_L3s[nPanType],@Score_L3_dbs[nPanType] = @Score_L3,@Detail_L3,@Score_L3_db
        end
        return @Score_L3,@Detail_L3,@Score_L3_db
    elsif (category_level == "MP") then
        if (@Score_MP == {}) then
            @Score_MP,@Detail_MP,@Score_MP_db = wc_getScore_db(nPanType,hUserInfo,hUserDefData,category_level,bFromDb) if @Score_MP_db == {}
            @Score_MP,@Detail_MP,@Score_MP_db = wc_getScore_C(nPanType,hUserInfo,hUserDefData,category_level,category_level_org) if @Score_MP_db == {}
            @Score_MPs[nPanType],@Detail_MPs[nPanType],@Score_MP_dbs[nPanType] = @Score_MP,@Detail_MP,@Score_MP_db
        end
        return @Score_MP,@Detail_MP,@Score_MP_db
    end
    return {},{},{}
  end
  # 用資料庫抓回來
  def wc_getScore_db(nPanType,hUserInfo,hUserDefData,category_level,bFromDb)
    return {},{},{} if !bFromDb
    categorys,h_ParSetting,h_Version = get_pars(hUserDefData,category_level)
    y,m,d,h,min,bSex = getUserDate_all(hUserInfo)
    timestamp_udt = Score.make_timestamp_udt(nPanType,hUserDefData["timestamp_udt"])
    timestamp_user = Xdate.make_timestamp_solar(y,m,d,h,bSex ? 0 : 1)
    talent_key = Talent.get_talent_score_key(category_level)
    pan_type = nPanType
    hScore,hScore_db = find_talent_scores_from_db(talent_key,timestamp_user,pan_type,timestamp_udt,0,category_level)
    hDetail = {}

    bChanged = false
    hScore_db.keys.each do |category_id|
# puts "Version hScore_db[category_id] : #{hScore_db[category_id]["version"]}"
# puts "Version h_Version[category_id] : #{h_Version[category_id]}"
        bChanged = true if hScore_db[category_id]["version"] != h_Version[category_id]
    end
    # 版本不同，表示有修改過，就重算
    return {},{},{} if bChanged

    return hScore,hDetail,hScore_db
  end

  def wc_getScore_C(nPanType,hUserInfo,hUserDefData,category_level,category_level_org)
    hUdt = hUserDefData.clone
    talent_key = Talent.get_talent_key(category_level,hUdt["my_current_lang"]) 
    hUdt["talent_key"] = talent_key
    hUdt["categorys"] = [] if category_level != category_level_org # 如果是不同的category_level則全抓
    hScore,hDetail = wc_getScore2(nPanType,hUserInfo,hUdt,category_level,category_level_org)
    hScore_final = make_final_score(hScore,{})
    hScore_db = {}
    hScore_final.keys.each do |category_id|
        hScore_db[category_id] = hScore_final[category_id]["score"]
    end
    return hScore,hDetail,hScore_db
  end
    def wc_getScore2(nPanType,hUserInfo,hUserDefData,category_level,category_level_org)
        @category_level = category_level
        if (@category_level == "L1") then
            hScore,hDetail = getScore_L1(nPanType,hUserInfo,hUserDefData)
        elsif (@category_level == "L2") then
            hScore,hDetail = getScore_L2(nPanType,hUserInfo,hUserDefData)
        elsif (@category_level == "L3") then
            hScore,hDetail = getScore_L3(nPanType,hUserInfo,hUserDefData)
        elsif (@category_level == "MP") then
            hScore,hDetail = getScore_MP(nPanType,hUserInfo,hUserDefData)
        end
        @category_level = category_level_org
        return hScore,hDetail
    end
    # L1 一定自己算
	def getScore_L1(nPanType,hUserInfo,hUserDefData)
        # return @Score_L1,@Detail_L1 if @Score_L1  != nil && @Score_L1 != {} 
		# categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"L1")
        # @par_setting_L1 = h_ParSetting.clone
        # hScore,hDetail = getUserScore(nPanType,hUserInfo,hUserDefData,categorys,h_ParSetting,true)
    	hScore_L1,hDetail_L1 = getUserScore(nPanType,hUserInfo,hUserDefData,categorys,h_ParSetting,true)

    	return hScore_L1,hDetail_L1
	end
    def getScore_L2(nPanType,hUserInfo,hUserDefData)
        # return @Score_L2,@Detail_L2 if @Score_L2  != nil && @Score_L2 != {} 

        hScore_L1,hDetail_L1,hScore_L1_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L1",@category_level,false)
    # puts "@Score_L1 : #{@Score_L1}"

        # 切換回 L2
        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"L2")
        # categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        # @par_setting_L2 = h_ParSetting.clone
        hScore_L2,hDetail_L2 = getUserScore(nPanType,hUserInfo,hUserDefData,categorys,h_ParSetting,false)
        hScore_L2_db = make_final_score(hScore_L2,{})

        return hScore_L2,hDetail_L2
    end
    def getScore_L3(nPanType,hUserInfo,hUserDefData)
        # return @Score_L3,@Detail_L3 if @Score_L3  != nil && @Score_L3 != {} 

        hScore_L1,hDetail_L1,hScore_L1_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L1",@category_level,false)
        hScore_L2,hDetail_L2,hScore_L2_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L2",@category_level,false)

        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"L3")
        # categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        # @par_setting_L3 = h_ParSetting.clone

        hScore_L3,hDetail_L3 = getUserScore(nPanType,hUserInfo,hUserDefData,categorys,h_ParSetting,false)
        hScore_L3_db = make_final_score(hScore_L3,{})

        return hScore_L3,hDetail_L3
    end
    def getScore_MP(nPanType,hUserInfo,hUserDefData)
        # return @Score_MP,@Detail_MP if @Score_MP  != nil && @Score_MP != {} 

        hScore_L1,hDetail_L1,hScore_L1_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L1",@category_level,false)
        hScore_L2,hDetail_L2,hScore_L2_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L2",@category_level,false)
        hScore_L3,hDetail_L3,hScore_L3_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L3",@category_level,false)

        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"MP")
        # categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        # @par_setting_MP = h_ParSetting.clone

        hScore_MP,hDetail_MP = getUserScore(nPanType,hUserInfo,hUserDefData,categorys,h_ParSetting,false)
        hScore_MP_db = make_final_score(hScore_MP,{})

        return hScore_MP,hDetail_MP
    end
    # 計算分數
	def getUserScore(nPanType,hUserInfo,hUserDefData,categorys,h_ParSetting,bNewOrg)
		org_Score = getOrgData(nPanType,hUserInfo,hUserDefData,bNewOrg)
    # s = "Talent : line 132: org_Score : #{org_Score}"
    # Pm.saveTestDb("logger_info Talent : line 132",s)
		if (categorys == []) then
			hScore,hDetail = wc_all_category_score(nPanType,h_ParSetting)
		else
			hScore,hDetail = wc_categorys_score(categorys,nPanType,h_ParSetting)
    	end
    	return hScore,hDetail
	end
    def getScore_trait(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id)
        @category_level = hUserDefData["category_level"]
        if (@category_level == "L1") then
            return getScore_trait_L1(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id)
        elsif (@category_level == "L2") then
            return getScore_trait_L2(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id)
        elsif (@category_level == "L3") then
            return getScore_trait_L3(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id)
        elsif (@category_level == "MP") then
            return getScore_trait_MP(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id)
        end
    end
    def getScore_trait_L1(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id)
        # categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"L1")
        # @par_setting_L1 = h_ParSetting.clone
        hScore,hDetail = getUserScore_trait(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id,h_ParSetting,true)

        return hScore,hDetail
    end
    def getScore_trait_L2(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id)
        hScore_L1,hDetail_L1,hScore_L1_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L1",@category_level,false)

        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"L2")
        # categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        # @par_setting_L2 = h_ParSetting.clone

        hScore,hDetail = getUserScore_trait(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id,h_ParSetting,false)

        return hScore,hDetail
    end
    def getScore_trait_L3(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id)
        hScore_L1,hDetail_L1,hScore_L1_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L1",@category_level,false)
        hScore_L2,hDetail_L2,hScore_L2_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L2",@category_level,false)

        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"L3")
        # categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        # @par_setting_L3 = h_ParSetting.clone

        hScore,hDetail = getUserScore_trait(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id,h_ParSetting,false)

        return hScore,hDetail
    end
    def getScore_trait_MP(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id)
        hScore_L1,hDetail_L1,hScore_L1_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L1",@category_level,false)
        hScore_L2,hDetail_L2,hScore_L2_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L2",@category_level,false)
        hScore_L3,hDetail_L3,hScore_L3_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L3",@category_level,false)

        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"MP")
        # categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        # @par_setting_MP = h_ParSetting.clone

        hScore,hDetail = getUserScore_trait(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id,h_ParSetting,false)

        return hScore,hDetail
    end
    def getUserScore_trait(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id,h_ParSetting,bNewOrg)
        org_Score = getOrgData(nPanType,hUserInfo,hUserDefData,bNewOrg)

        hScore,hDetail = wc_one_trait_score(category_id,item_id,trait_id,nPanType,h_ParSetting)

        return hScore,hDetail
    end
    def getScore_item(nPanType,hUserInfo,hUserDefData,category_id,item_id)
        @category_level = hUserDefData["category_level"]
        if (@category_level == "L1") then
            return getScore_item_L1(nPanType,hUserInfo,hUserDefData,category_id,item_id)
        elsif (@category_level == "L2") then
            return getScore_item_L2(nPanType,hUserInfo,hUserDefData,category_id,item_id)
        elsif (@category_level == "L3") then
            return getScore_item_L3(nPanType,hUserInfo,hUserDefData,category_id,item_id)
        elsif (@category_level == "MP") then
            return getScore_item_MP(nPanType,hUserInfo,hUserDefData,category_id,item_id)
        end
    end
    def getScore_item_L1(nPanType,hUserInfo,hUserDefData,category_id,item_id)
        # categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"L1")
        # @par_setting_L1 = h_ParSetting.clone
        hScore,hDetail = getUserScore_item(nPanType,hUserInfo,hUserDefData,category_id,item_id,h_ParSetting,true)

        return hScore,hDetail
    end
    def getScore_item_L2(nPanType,hUserInfo,hUserDefData,category_id,item_id)
        hScore_L1,hDetail_L1,hScore_L1_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L1",@category_level,false)

        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"L2")
        # categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        # @par_setting_L2 = h_ParSetting.clone

        hScore,hDetail = getUserScore_item(nPanType,hUserInfo,hUserDefData,category_id,item_id,h_ParSetting,false)

        return hScore,hDetail
    end
    def getScore_item_L3(nPanType,hUserInfo,hUserDefData,category_id,item_id)
        hScore_L1,hDetail_L1,hScore_L1_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L1",@category_level,false)
        hScore_L2,hDetail_L2,hScore_L2_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L2",@category_level,false)

        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"L3")
        # categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        # @par_setting_L3 = h_ParSetting.clone

        hScore,hDetail = getUserScore_item(nPanType,hUserInfo,hUserDefData,category_id,item_id,h_ParSetting,false)

        return hScore,hDetail
    end
    def getScore_item_MP(nPanType,hUserInfo,hUserDefData,category_id,item_id)
        hScore_L1,hDetail_L1,hScore_L1_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L1",@category_level,false)
        hScore_L2,hDetail_L2,hScore_L2_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L2",@category_level,false)
        hScore_L3,hDetail_L3,hScore_L3_db = wc_getScore(nPanType,hUserInfo,hUserDefData,"L3",@category_level,false)

        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,"MP")
        # categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        # @par_setting_MP = h_ParSetting.clone

        hScore,hDetail = getUserScore_item(nPanType,hUserInfo,hUserDefData,category_id,item_id,h_ParSetting,false)

        return hScore,hDetail
    end
    def getUserScore_item(nPanType,hUserInfo,hUserDefData,category_id,item_id,h_ParSetting,bNewOrg)
        org_Score = getOrgData(nPanType,hUserInfo,hUserDefData,bNewOrg)

        hScore,hDetail = wc_one_item_score(category_id,item_id,nPanType,h_ParSetting)

        return hScore,hDetail
    end
	def get_pars(hUserDefData,category_level="L1")
		categorys = hUserDefData["categorys"]
        my_current_lang = hUserDefData["my_current_lang"]
        @my_current_lang = my_current_lang
        h_ParSetting,h_Version = wc_get_pars_setting(hUserDefData["my_current_lang"],category_level)
		# key = hUserDefData["talent_key"]
		# hTalent = Pm.getSystemValue_hash(key)
		# h_ParSetting = hTalent["par_setting"]
		return categorys,h_ParSetting,h_Version
	end
  def init_all()
    # 清除記憶體空間
    wc_init_Scores()
    init_category_statistic()
    init_talent()
    init_pars_setting()
  end
	def getUsersScore(nPanType,hUserInfo,hUserDefData)
        @category_level = hUserDefData["category_level"]
		# categorys,h_ParSetting,h_Version = get_pars(hUserDefData)
        categorys,h_ParSetting,h_Version = get_pars(hUserDefData,@category_level)
        @talent_version = hUserDefData["talent_version"]
        @talent_version = {} if @talent_version == nil
        # @par_setting_L1 = h_ParSetting if @category_level == "L1"
        # @par_setting_L2 = h_ParSetting if @category_level == "L2"
        # @par_setting_L3 = h_ParSetting if @category_level == "L3"
        # @par_setting_MP = h_ParSetting if @category_level == "MP"
    	b = hUserInfo["timestamp_user_start"]
    	e = hUserInfo["timestamp_user_stop"]
    	bY,bM,bD = b[Cfate::WYear],b[Cfate::WMonth],b[Cfate::WDate]
    	eY,eM,eD = e[Cfate::WYear],e[Cfate::WMonth],e[Cfate::WDate]

        @insert_only = Cfate.ValueBoolCheck(hUserDefData["insert_only"])

    	nDays = Xdate.GetWDaysBetweenDates(bY, bM, bD,eY, eM, eD)
    	(0..nDays).each do |i|
    		hUserInfo = setUserInfo(hUserInfo,bY,bM,bD,0,0,true)
    		getUserScore_OneDay(nPanType,hUserInfo,hUserDefData,categorys,h_ParSetting)
    		bY,bM,bD = Xdate.NextWDate(bY, bM, bD)
    	end
        
        # 清除記憶體空間
        init_all()
	end
    def getUserScore_OneDay(nPanType,hUserInfo,hUserDefData,categorys,h_ParSetting)
        # 只有item 沒有trait 時 還沒寫
        if (hUserDefData["trait_id"] != nil && hUserDefData["trait_id"] != "") then
          category_id = hUserDefData["category_id"]
          item_id = hUserDefData["item_id"]
          trait_id = hUserDefData["trait_id"]
          getUserScore_OneDay_trait(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id,h_ParSetting)
        elsif (hUserDefData["item_id"] != nil && hUserDefData["item_id"] != "")
          category_id = hUserDefData["category_id"]
          item_id = hUserDefData["item_id"]
          getUserScore_OneDay_item(nPanType,hUserInfo,hUserDefData,category_id,item_id,h_ParSetting)
        else
          getUserScore_OneDay_all(nPanType,hUserInfo,hUserDefData,categorys,h_ParSetting)
        end
    end
	def getUserScore_OneDay_all(nPanType,hUserInfo,hUserDefData,categorys,h_ParSetting)
		y,m,d = getUserDate(hUserInfo)
        # timestamp_udt = hUserDefData["timestamp_udt"]
        timestamp_udt = Score.make_timestamp_udt(nPanType,hUserDefData["timestamp_udt"])
      ActiveRecord::Base.transaction do
    	(1..12).each do |h|
    		[true,false].each do |bSex|
    			hUserInfo = setUserInfo(hUserInfo,y,m,d,(h - 1) * 2,0,bSex)
    			# hScore,hDetail = getUserScore(nPanType,hUserInfo,hUserDefData,categorys,h_ParSetting,true)
                wc_init_Scores()
                hScore,hDetail,hScore_db = getScore_db(nPanType,hUserInfo,hUserDefData)
    			# puts "#{y}/#{m}/#{d},#{(h - 1) * 2},#{bSex}"
                timestamp_user = Xdate.make_timestamp_solar(y,m,d,(h - 1) * 2,bSex ? 0 : 1)
    			save_user_score(nPanType,timestamp_user,timestamp_udt,hScore,hDetail)
    		end
        end
      end
	end
    def getUserScore_OneDay_trait(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id,h_ParSetting)
        y,m,d = getUserDate(hUserInfo)
        # timestamp_udt = hUserDefData["timestamp_udt"]
        timestamp_udt = Score.make_timestamp_udt(nPanType,hUserDefData["timestamp_udt"])
      ActiveRecord::Base.transaction do
        (1..12).each do |h|
            [true,false].each do |bSex|
                hUserInfo = setUserInfo(hUserInfo,y,m,d,(h - 1) * 2,0,bSex)
                wc_init_Scores()
                hScore,hDetail = getScore_trait(nPanType,hUserInfo,hUserDefData,category_id,item_id,trait_id)
                # puts "#{y}/#{m}/#{d},#{(h - 1) * 2},#{bSex}"
                timestamp_user = Xdate.make_timestamp_solar(y,m,d,(h - 1) * 2,bSex ? 0 : 1)
                save_user_score_trait(nPanType,timestamp_user,timestamp_udt,hScore,hDetail,h_ParSetting,category_id,item_id,trait_id)
            end
        end
      end
    end
    def getUserScore_OneDay_item(nPanType,hUserInfo,hUserDefData,category_id,item_id,h_ParSetting)
        y,m,d = getUserDate(hUserInfo)
        # timestamp_udt = hUserDefData["timestamp_udt"]
        timestamp_udt = Score.make_timestamp_udt(nPanType,hUserDefData["timestamp_udt"])
      ActiveRecord::Base.transaction do
        (1..12).each do |h|
            [true,false].each do |bSex|
                hUserInfo = setUserInfo(hUserInfo,y,m,d,(h - 1) * 2,0,bSex)
                wc_init_Scores()
                hScore,hDetail = getScore_item(nPanType,hUserInfo,hUserDefData,category_id,item_id)
                # puts "#{y}/#{m}/#{d},#{(h - 1) * 2},#{bSex}"
                timestamp_user = Xdate.make_timestamp_solar(y,m,d,(h - 1) * 2,bSex ? 0 : 1)
                save_user_score_item(nPanType,timestamp_user,timestamp_udt,hScore,hDetail,h_ParSetting,category_id,item_id)
            end
        end
      end
    end
	def save_user_score(nPanType,timestamp_user,timestamp_udt,hScore,hDetail)
		# save to db
        talent_key = Talent.get_talent_score_key(@category_level)
        scores = {}
        scores["score"] = hScore
        scores["detail"] = hDetail
        hScoreSaved = make_final_score(hScore,@talent_version)
        save_TalentScore(talent_key,timestamp_user,nPanType,timestamp_udt,0,hScoreSaved)
	end
    def save_user_score_trait(nPanType,timestamp_user,timestamp_udt,hScore,hDetail,h_ParSetting,category_id,item_id,trait_id)
        talent_key = Talent.get_talent_score_key(@category_level)
        ts = find_talent_score(talent_key,timestamp_user,nPanType,timestamp_udt,0)
        hScore_db = ts.hScores
        if (hScore_db == {}) then return end
        hScoreSaved = make_final_score_trait(hScore,@talent_version,hScore_db,h_ParSetting,category_id,item_id,trait_id)
        save_TalentScore(talent_key,timestamp_user,nPanType,timestamp_udt,0,hScoreSaved)
    end
    def save_user_score_item(nPanType,timestamp_user,timestamp_udt,hScore,hDetail,h_ParSetting,category_id,item_id)
        talent_key = Talent.get_talent_score_key(@category_level)
        ts = find_talent_score(talent_key,timestamp_user,nPanType,timestamp_udt,0)
        hScore_db = ts.hScores
        if (hScore_db == {}) then return end
        hScoreSaved = make_final_score_item(hScore,@talent_version,hScore_db,h_ParSetting,category_id,item_id)
        save_TalentScore(talent_key,timestamp_user,nPanType,timestamp_udt,0,hScoreSaved)
    end
    def Talent.get_talent_score_key(category_level="L1")
        s = "_#{category_level}"
        s = "" if category_level == "L1"
        s = "" if category_level == nil
        talent_key = "talent_categorys#{s}"
        return talent_key
    end
    def make_final_score(hScore,talent_version)
        hOut = {}
        hScore.keys.each do |category_id|
            b = []
            hScore[category_id].keys.each do |item_id|
                c = []
                hScore[category_id][item_id].keys.each do |trait_id|
                    c.push(hScore[category_id][item_id][trait_id]["score"])
                end
                b.push(c)
            end
            hOut[category_id] = {}
            hOut[category_id]["score"] = b
            hOut[category_id]["version"] = talent_version[category_id]
        end
        return hOut
    end
    def db_final_score_2_score(hDbScore,category_level)
        hScore = {}
        return hScore,hDbScore if hDbScore == {}
        par_setting = wc_get_one_hash(category_level,@par_setting_L1,@par_setting_L2,@par_setting_L3,@par_setting_MP)
    # puts "category_level : #{category_level}"
    # puts "@par_setting_L1 : #{@par_setting_L1.keys}"
    # puts "par_setting : #{par_setting}"
        return hScore,hDbScore if par_setting == nil
        hDbScore.keys.each do |category_id|
            hScore[category_id] = db_final_score_2_score_category(hDbScore[category_id],category_id,par_setting)
        end
        return hScore,hDbScore
    end
    def db_final_score_2_score_category(category_score,category_id,par_setting)
        hDbScore = category_score["score"]
        hScore = {}
        hDbScore.each_with_index do |ai,i_idx|
            item_id = par_setting[category_id].keys[i_idx]
            hScore[item_id] = {}
            ai.each_with_index do |v,t_idx|
                trait_id = par_setting[category_id][item_id].keys[t_idx]
                hScore[item_id][trait_id] = {"score" => v}
            end
        end
        return hScore
    end
    def make_final_score_trait(hScore,talent_version,hScore_db,h_ParSetting,category_id,item_id,trait_id)
        hOut = hScore_db.clone

        c_idx,i_idx,t_idx = wc_find_cit_index2(h_ParSetting,category_id,item_id,trait_id)
        l = hOut[category_id]["score"][i_idx].length
        if (l <= t_idx) then
            hOut[category_id]["score"][i_idx] = hOut[category_id]["score"][i_idx] + Array.new(t_idx - l + 1,0)
        end
        hOut[category_id]["score"][i_idx][t_idx] = hScore[category_id][item_id][trait_id]["score"]
        hOut[category_id]["version"] = talent_version[category_id]
        return hOut
    end
    def make_final_score_item(hScore,talent_version,hScore_db,h_ParSetting,category_id,item_id)
        hOut = hScore_db.clone

        hScore[category_id][item_id].keys.each do |trait_id|
            c_idx,i_idx,t_idx = wc_find_cit_index2(h_ParSetting,category_id,item_id,trait_id)
            l = hOut[category_id]["score"].length
            if (l <= i_idx) then
                hOut[category_id]["score"] = hOut[category_id]["score"] + Array.new(i_idx - l + 1) {Array.new(hScore[category_id][item_id].keys.length,0)}
            end
            l = hOut[category_id]["score"][i_idx].length
            if (l <= t_idx) then
                hOut[category_id]["score"][i_idx] = hOut[category_id]["score"][i_idx] + Array.new(t_idx - l + 1,0)
            end
            hOut[category_id]["score"][i_idx][t_idx] = hScore[category_id][item_id][trait_id]["score"]
        end
        hOut[category_id]["version"] = talent_version[category_id]
        return hOut
    end
    def save_default_all_users(nPanType,hUserInfo,hUserDefData)
        b = hUserInfo["timestamp_user_start"]
        e = hUserInfo["timestamp_user_stop"]
        bY,bM,bD = b[Cfate::WYear],b[Cfate::WMonth],b[Cfate::WDate]
        eY,eM,eD = e[Cfate::WYear],e[Cfate::WMonth],e[Cfate::WDate]
        talent_key = "talent_"
        # timestamp_udt = hUserDefData["timestamp_udt"]
        timestamp_udt = Score.make_timestamp_udt(nPanType,hUserDefData["timestamp_udt"])

        save_default_all_users2(bY,bM,bD,eY,eM,eD,talent_key,nPanType,timestamp_udt,hUserInfo,hUserDefData)
    end
    def save_default_all_users2(bY,bM,bD,eY,eM,eD,talent_key,pan_type,timestamp_udt,hUI,hUserDefData)
        # talent_key = "talent_"
        # bY,bM,bD = 1900, 1, 1
        # eY,eM,eD = 2100, 12, 31
        hUserInfo = hUI.clone
        # timestamp_udt = Score.make_timestamp_udt(pan_type,timestamp_udt)
        nDays = Xdate.GetWDaysBetweenDates(bY, bM, bD,eY, eM, eD)
        y,m,d = bY,bM,bD
        # hUserInfo = {}
        # hUserInfo = setUserInfo(hUserInfo,y,m,d,0,0,true)
        (0..nDays).each do |i|
            (1..12).each do |h|
                [true,false].each do |bSex|
                    hUserInfo = setUserInfo(hUserInfo,y,m,d,(h - 1) * 2,0,bSex)
                    # timestamp_user = Score.get_user_info_timestamp(hUserInfo)
                    org_Score = getOrgData(pan_type,hUserInfo,hUserDefData,true)
                    # save_default_TalentScore(talent_key,timestamp_user,pan_type,timestamp_udt)
                end
            end
            y,m,d = Xdate.NextWDate(y,m,d)
        end
    end
    def save_default_TalentScore(talent_key,timestamp_user,pan_type,timestamp_udt)
        status = -1
        scores = {}
        save_TalentScore(talent_key,timestamp_user,pan_type,timestamp_udt,status,scores)
    end
    def save_TalentScore(talent_key,timestamp_user,pan_type,timestamp_udt,status,scores)
        # rails generate model talent_score talent_key:string timestamp_user:string pan_type:integer timestamp_udt:string status:integer scores:text
        ts = find_talent_score(talent_key,timestamp_user,pan_type,timestamp_udt,status)
        ts.talent_key = talent_key
        ts.timestamp_user = timestamp_user
        ts.pan_type = pan_type
        ts.timestamp_udt = timestamp_udt
        ts.status = status
        h = ts.hScores
        # if (h.keys.include?(scores.keys[0])) then
        #   category_id = scores.keys[0]
        #   h[category_id].merge!(scores[category_id]) { |key, v1, v2| v2 }
        # else
        #   h[category_id] = scores[category_id]
        # end
        # 重算分數之後，PR值也要重算，所以放棄原先的PR值
        h.merge!(scores) { |key, v1, v2| v2 }
        ts.hScores = h
        ts.save!
    end
    def find_talent_scores_from_db(talent_key,timestamp_user,pan_type,timestamp_udt,status,category_level)
        ts = find_talent_score(talent_key,timestamp_user,pan_type,timestamp_udt,status)
        hScore,hScores_db = db_final_score_2_score(ts.hScores,category_level)
        return hScore,hScores_db
    end
    def find_talent_score(talent_key,timestamp_user,pan_type,timestamp_udt,status)
        ts = nil
        @insert_only = Cfate.ValueBoolCheck(@insert_only)
        if (!@insert_only) then
            ts = TalentScore.check_talent_key(talent_key)
            ts = ts.check_timestamp_user(timestamp_user)
            ts = ts.check_pan_type(pan_type)
            ts = ts.check_timestamp_udt(timestamp_udt).last
            # ts = nil # 先預設資料庫是空的
        end
        if (ts == nil) then
            ts = TalentScore.new
        end
        return ts
    end
	def getOrgData(nPanType,hUserInfo,hUserDefData,bNewOrg)
        return @Obj_Score.get_org_score() if !bNewOrg && @Obj_Score != nil

    	hParAll = {}
    	hParAll[Star::PAR_FLOWYEAR_HUA] = Star::PAN_TYPE_FLOW
    	hParAll[Star::PAR_LU_TYPE] = Star::PAN_LU_CALD
    	hParAll[Star::DIS_FLOWLYT] = Star::STAR_DIS_FLOW_DIE

    	hUserDefData["ParAll"] = hParAll
        hUserDefData[Star::HOUSE_NAME] = 99 # 1

		@Obj_Score = Score.new
		org_Score = @Obj_Score.ps_getScore(Pm::AP_STAR,nPanType,hUserInfo,hUserDefData,true)

    	@Obj_Star = Star.new
        # @Obj_Score_Star.g_GetPanInfo(nPanType,hUserInfo,hUserDefData,hParAll)
        # 此函式會將紫微盤所有資料算出來,2021/8/3遇到trait score 跟 api_1 api_1_all 部分資料不同，
        # 原因是宮位中的主星還沒有全算出，取出時，197710261400，宮位9官祿，應該有1，14 紫微跟破軍，但卻只有 1 紫微
        # 導致分數不同
    	@Obj_Star.g_GetAllPanInfo(nPanType,hUserInfo,hUserDefData,hParAll)

        return org_Score
	end
	def setUserInfo(hUserInfo,y,m,d,h,min,bSex)
    	hUserInfo[Cfate::WYear] = y
    	hUserInfo[Cfate::WMonth] = m
    	hUserInfo[Cfate::WDate] = d
    	hUserInfo[Cfate::WHour] = h
    	hUserInfo[Cfate::WMinute] = min
    	hUserInfo[Cfate::Sex] = bSex
    	return hUserInfo
	end
	def getUserDate(hUserInfo)
    	y = hUserInfo[Cfate::WYear]
    	m = hUserInfo[Cfate::WMonth]
    	d = hUserInfo[Cfate::WDate]
    	return y,m,d
	end
    def getUserDate_all(hUserInfo)
        y,m,d = getUserDate(hUserInfo)
        h = hUserInfo[Cfate::WHour]
        min = hUserInfo[Cfate::WMinute]
        bSex = hUserInfo[Cfate::Sex]
        return y,m,d,h,min,bSex
    end

    # 包含內容：代碼 trait_code、項目名稱 desc、數值score、PR值 pr、
    # 指標意義（效標定義）trait_desc、定義（效標分數說明）trait_score_desc、
    # 指標判讀說明（特質文字定義之「特質內容」）trait_content、引導建言及注意事項（特質文字定義之「建議方案」）proposal
    def make_sti_trait_score_hash(my_current_lang,hScore,hDetail,pr_value,category_level,category_id,item_id,trait_id,trait_code,desc,trait_desc,trait_score_desc,trait_def)
        @my_current_lang = my_current_lang
        hOut = {}

        h_ParSetting,h_Version = wc_get_pars_setting(my_current_lang,category_level)

        c_idx,i_idx,t_idx = wc_find_cit_index2(h_ParSetting,category_id,item_id,trait_id)
        hOut["trait_code"] = trait_code
        hOut["desc"] = desc
        # hOut["trait_def"] = trait_def
        score = hScore[category_id][item_id][trait_id]["score"].to_f
        hOut["score"] = score

        if (category_level != "MP") then
            pr = wc_find_score_pr2(c_idx,i_idx,t_idx,pr_value,score)
            hOut["pr"] = pr.round(2)
            hOut["trait_desc"] = trait_desc
            hOut["trait_score_desc"] = trait_score_desc
            pr_desc = Talent.sti_trait_def_pr_check(trait_def,pr,category_level)
            hOut["pr_desc"] = pr_desc["taitou"]
            hOut["trait_content"] = pr_desc["trait_content"]
            hOut["proposal"] = pr_desc["proposal"]
            hOut["pr_score_drop"] = pr_desc["score_drop"]
        elsif (@bNeedDrop != nil && @bNeedDrop) then
            pr = hScore[category_id][item_id][trait_id]["pr"].to_f
            hOut["pr"] = pr.round(2)
            hOut["trait_desc"] = trait_desc
            hOut["trait_score_desc"] = trait_score_desc
            pr_desc = Talent.sti_trait_def_pr_check(trait_def,pr,category_level)
            hOut["pr_desc"] = pr_desc["taitou"]
            hOut["trait_content"] = pr_desc["trait_content"]
            hOut["proposal"] = pr_desc["proposal"]
            hOut["pr_score_drop"] = pr_desc["score_drop"]
        else
            pr = hScore[category_id][item_id][trait_id]["pr"].to_f
            hOut["pr"] = pr.round(2)
            hOut["trait_desc"] = trait_desc
            hOut["trait_score_desc"] = trait_score_desc
            hOut["trait_def"] = trait_def
        end

        # 各分數低於 15%含 以下，顯示規則修正 2021/8/27
        hOut["pr"] = modify_for_low_pr(hOut["pr"].to_f)
        hOut["pr_stars"] = pr_stars(hOut["pr"].to_f)
        pr = hOut["pr"]

        if (category_level != "L1") then
            hOut["detail"],h2 = make_sti_trait_detail_pr(category_id,item_id,trait_id,hDetail)
            # hOut.merge!(h2) { |key, v1, v2| v2 }
        end

        return hOut,pr
    end

    # 各分數低於 15%含 以下，顯示規則修正。
    # 分數在 5%含以下 ＝》變成  12% 各分數之小數二位不變。
    # 分數在 5%不含以上～10% 含以下 ＝》變成  13%  各分數之小數二位不變。
    # 分數在 10%不含以上～14% 含以下 ＝》變成  14% 各分數之小數二位不變。
    def modify_for_low_pr(old_pr)
        return old_pr.round(2) if (old_pr >= 15.0)

        sn = (old_pr.to_f - old_pr.to_i).to_f
        return (12.0 + sn).round(2) if old_pr <= 5.0
        return (13.0 + sn).round(2) if old_pr > 5.0 && old_pr <= 10.0
        return (14.0 + sn).round(2) if old_pr > 10.0 && old_pr < 15.0

        return old_pr.round(2)
    end
    def pr_stars(pr)
        a = 75.0
        b = 99.99
        return 4 if (pr.between?(a,b))

        a = 50.0
        b = 74.99
        return 3 if (pr.between?(a,b))

        a = 25.0
        b = 49.99
        return 2 if (pr.between?(a,b))

        a = 1.0
        b = 24.99
        return 1 if (pr.between?(a,b))

        return 3 # 不會發生
    end

  def make_sti_trait_detail_pr(category_id,item_id,trait_id,hDetail)
    aOut = []
    bH = {}
    pr = 0.0
    hDetail[category_id][item_id][trait_id]["Detail"].each do |hd|
      h = {}
      h["trait_code"] = hd["trait_code"]
      h["desc"] = hd["desc"]
      h["pr"] = hd["pr_each_after_reverse"]
      # 各分數低於 15%含 以下，顯示規則修正 2021/8/27
      h["pr"] = modify_for_low_pr(h["pr"].to_f)
      h["pr_stars"] = pr_stars(h["pr"].to_f)

      aOut.push(h)
      if (h["pr"] > pr) then
        bH = make_sti_trait_high_score_desc(hd,h["pr"])
        pr = h["pr"]
      end
    end
    return aOut,bH
  end
  def make_sti_trait_high_score_desc(hd,pr)
    hOut = {}
    trait_def = hd["trait_def"]
    trait_id = hd["trait_id"]
    category_level,category_id,item_id,trait_id = split_rl_trait(trait_id)
    pr_desc = Talent.sti_trait_def_pr_check(trait_def,pr,category_level)
    hOut["pr_desc"] = pr_desc["taitou"]
    hOut["trait_content"] = pr_desc["trait_content"]
    hOut["proposal"] = pr_desc["proposal"]
    hOut["pr_score_drop"] = pr_desc["score_drop"]
    return hOut
  end
  def Talent.sti_trait_def_pr_check(trait_def,pr,category_level)
    td = Talent.trait_def_init_check(trait_def)
    (1..4).each do |i|
      bFind,h = Talent.sti_trait_def_pr_desc(td,i,pr,category_level)
      return h if bFind
    end
    return {"taitou" => "", "trait_content" => "", "proposal" => ""}
  end
  def Talent.sti_trait_def_pr_desc(trait_def,i,pr,category_level)
    h = {}
    up,down,trait_content,proposal = Talent.get_one_trait_def(trait_def,i)
    if (pr.to_f.between?(up.to_f,down.to_f + 0.99)) then
      trait_def_area = Pm.t("talent.big_nos")[i - 1]

      n = up.to_f
      n = 1.0 if up.to_f == 0.0
      h["taitou"] = Pm.t("talent.#{category_level}.trait_def_taitou",:trait_def_area => trait_def_area,:pr => "#{(n).to_i}")
      h["trait_content"] = trait_content
      h["proposal"] = proposal
      h["score_drop"] = i

      return true,h
    end
    return false,{"taitou" => "", "trait_content" => "", "proposal" => ""}
  end
  def sti_get_pars_from_trait_code(my_current_lang,trait_code)
    @my_current_lang = my_current_lang
    category_levels = ["L1","L2","L3","MP"]
    category_levels.each do |category_level|
      bFind,category_id,item_id,trait_id,desc,trait_desc,trait_score_desc,trait_def = sti_get_pars_from_trait_code_by_category_level(my_current_lang,category_level,trait_code)
      return bFind,category_level,category_id,item_id,trait_id,desc,trait_desc,trait_score_desc,trait_def if bFind
    end
    return false,"",0,0,0,"","","",Talent.trait_def_empty()
  end
  def sti_get_pars_from_trait_code_by_category_level(my_current_lang,category_level,trait_code)
      h_ParSetting,h_Version = wc_get_pars_setting(my_current_lang,category_level)
      h_talent = wc_get_talent(my_current_lang,category_level)
      h_ParSetting.keys.each do |category_id|
          h_ParSetting[category_id].keys.each do |item_id|
              h_ParSetting[category_id][item_id].keys.each do |trait_id|    
                trait_code_find = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_code"]
                desc = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["desc"]
                trait_desc = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_desc"]
                trait_score_desc = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_score_desc"]
                trait_def = Talent.trait_def_init_check(h_talent["trait_def"][category_id][item_id][trait_id])
                return true,category_id,item_id,trait_id,desc,trait_desc,trait_score_desc,trait_def if trait_code_find == trait_code
              end
          end
      end
      return false,0,0,0,"","","",Talent.trait_def_empty()
  end

  def gpc_sti_getdata_1_2(hUserInfo,hUserDefData)
    hOut = sti_getdata_1_1_by_pantype(0,hUserInfo,hUserDefData)
    hOut = gpc_sti_getdata_1_2_desc(hOut)
    return hOut
  end
  def gpc_sti_getdata_1_2_desc(a0)
      hOut = a0.clone
      hOut.delete("trait_code_max_pr")
      a0["Data"].keys.each do |trait_code|
          hOut["Data"][trait_code].delete("trait_desc")
          hOut["Data"][trait_code].delete("trait_score_desc")
          hOut["Data"][trait_code].delete("trait_def")
          hOut["Data"][trait_code].delete("detail")
          hOut["Data"][trait_code].delete("score")
      end

      return hOut
  end
  def gpc_sti_getdata_1_3(hUserInfo,hUserDefData)
    hOut = sti_getdata_1_1_by_pantype(0,hUserInfo,hUserDefData)
    hOut = gpc_sti_getdata_1_2_desc(hOut)
    return hOut
  end
  def gpc_sti_getdata_1_4(hUserInfo,hUserDefData)
    hOut = sti_getdata_1_1_by_pantype(0,hUserInfo,hUserDefData)
    hOut = gpc_sti_getdata_1_2_desc(hOut)
    return hOut
  end
end
