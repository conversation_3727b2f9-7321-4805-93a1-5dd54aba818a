class Divination
  # parameter 區塊
  def parameter()
  	h = Hash.new

    nDaySky = Xdate.GetGanZhiSky(@nGanZhiDay)
    nYearEarth = Xdate.GetGanZhiEarth(@nGanZhiYear)
    nMonthEarth = Xdate.GetGanZhiEarth(@nGanZhiMonth)
    nDayEarth = Xdate.GetGanZhiEarth(@nGanZhiDay)

  	# 月 令
  	h["yue_ling"] = Divination.Earth_Str(nMonthEarth)
  	# 旺 相 休 囚 死
  	h["wang_xiang_xiu_qiu_si"] = Divination.par_five_levels_five_str(nMonthEarth)

  	# 六 親
  	h["liu_qin"] = Divination.par_liu_qins_str(@par_five_levels,@all_ylqgzws)

  	# 世 應
    h["shi"] = Divination.par_shi(@all_ylqgzws,@par_five_levels,@par_shi_yin)
    h["yin"] = Divination.par_yin(@all_ylqgzws,@par_five_levels,@par_shi_yin)

  	# 長 生
    h["chang_sheng"] = Divination.chang_sheng_strs(@par_chang_sheng)

  	# 空 亡
  	h["kong_wang"] = Divination.par_Kong_Wang_str(nDaySky,nDayEarth)
  	# 貴 人
  	h["gui_ren"] = Divination.par_gui_ren_str(nDaySky,1)
  	# 羊 刃
  	h["yang_ren"] = Divination.Yang_Ren_str(nDaySky)
  	# 干 祿
  	h["gan_lu"] = Divination.Gan_Lu_str(nDaySky)
  	# 驛 馬
  	h["yi_ma"] = Divination.Ten_ma_str(nDayEarth)

  	# 桃 花
    h["tao_hua"] = Divination.Tao_Wow_str(nDayEarth)
  	# 卦 身
    h["gua_shen"] = Divination.par_gua_body_str(@par_shi_yin,@gua_yaos,@all_ylqgzws)

  	# 三 合
  	dong_yao_di_zhi = [nYearEarth,nMonthEarth,nDayEarth]
  	h["san_he"] = Divination.par_san_he_str(dong_yao_di_zhi,@all_ylqgzws)
  	# 三 刑
  	h["san_xing"] = Divination.par_san_xing_str(dong_yao_di_zhi,@all_ylqgzws)
  	# 互 刑
  	h["hu_xing"] = Divination.par_hu_xing_str(dong_yao_di_zhi,@all_ylqgzws)
  	# 自 刑
  	h["zi_xing"] = Divination.par_zi_xing_str([nYearEarth,nMonthEarth,nDayEarth],@all_ylqgzws)

  	# 年 衝
  	h["nian_chong"] = Divination.par_date_chong(nYearEarth,@all_ylqgzws)
  	# 月 衝
  	h["yue_chong"] = Divination.par_date_chong(nMonthEarth,@all_ylqgzws)
  	# 日 衝
  	h["ri_chong"] = Divination.par_date_chong(nDayEarth,@all_ylqgzws)
  	# 年 合
  	h["nian_he"] = Divination.par_date_he(nYearEarth,@all_ylqgzws)
  	# 月 合
  	h["yue_he"] = Divination.par_date_he(nMonthEarth,@all_ylqgzws)
  	# 日 合
  	h["ri_he"] = Divination.par_date_he(nDayEarth,@all_ylqgzws)

    # 天喜
    h["tian_xi"] = Divination.tianxi_dizhi_str(nMonthEarth)
    # 劫煞
    h["jie_sha"] = Divination.jiesha_dizhi_str(nDayEarth)
  	return h
  end
  # 月 令
  # OK

  # 節氣月    旺 相 休 囚 死
  Five_level_1 = 1  # 旺
  Five_level_2 = 2  # 相
  Five_level_3 = 3  # 休
  Five_level_4 = 4  # 囚
  Five_level_5 = 5  # 死
  def Divination.five_level_str(n)
    return Divination.liuyaogua_str("five_level_#{n}")
  end

  # 春:寅、卯 木 火 水 金 土    
  # 夏:巳、午 火 土 木 水 金
  # 秋:申、酉 金 水 土 火 木
  # 冬:亥、子 水 木 金 土 火
  # 四季:辰、 土 金 火 木 水
  # 未、戌、丑
  Season_Spring = 1
  Season_Summer = 2
  Season_Autumn = 3
  Season_Winter = 4
  Season_Four = 5
  @@Earth_season = [4,5,1,1,5,2,2,5,3,3,5,4]
  def Divination.Earth_season(nEarth)
    return @@Earth_season[nEarth - 1]
  end
  #                    旺 相 休 囚 死
  @@Seg_five_level = [
                       [1,2,5,4,3],  # 春
                       [2,3,1,5,4],  # 夏
                       [4,5,3,2,1],  # 秋
                       [5,1,4,3,2],  # 冬
                       [3,4,2,1,5]   # 四季
                     ]
  def Divination.par_five_levels(nEarth)
    season = Divination.Earth_season(nEarth)
    return @@Seg_five_level[season - 1]
  end
  def Divination.par_five_levels_five_str(nEarth)
    a = Divination.par_five_levels(nEarth)
    aOut = Array.new(a.length)
    a.each_index { |i|  aOut[i] = Divination.Five_Str(a[i])}
    return aOut
  end
  def Divination.par_find_five_level(par_five_levels,five)
    five_level_index = Divination.par_find_five_level_index(par_five_levels,five)
    return five_level_index + 1
  end
  def Divination.par_find_five_level_index(par_five_levels,five)
    return par_five_levels.index(five)
  end

  # 六 親
  def Divination.par_liu_qins(par_five_levels,all_ylqgzws)
    ylqgzws = Divination.par_all_gua_ylqgzws(all_ylqgzws)
    aOut = Array.new(par_five_levels.length,nil)
    par_five_levels.each_index do |i|
      wu_xing = par_five_levels[i]
      a = Divination.par_find_ylqgzw_by_wu_xing(ylqgzws,wu_xing)
      aOut[i] = Divination.fu_shen_liu_qin(a)
    end

    return aOut
  end
  def Divination.par_liu_qin_from_fu_shens(five,fu_shens)
    fu_shens.each_index do |i|
      fs_wu_xing = Divination.fu_shen_wu_xing(fu_shens[i])
      if (five == fs_wu_xing) then
        return a
      end
    end
    return nil
  end
  # 世 應
  def Divination.par_yao_earth(all_ylqgzws,yao)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_by_yao(ylqgzws,yao)
    return Divination.fu_shen_di_zhi(ylqgzw)
  end
  def Divination.par_yao_five(all_ylqgzws,yao)
    earth = Divination.par_yao_earth(all_ylqgzws,yao)
    return Divination.Earth_Five(earth)
  end
  def Divination.par_shi_yao(par_shi_yin)
    return par_shi_yin[0]
  end
  def Divination.par_shi_earth(all_ylqgzws,par_shi_yin)
    yao = Divination.par_shi_yao(par_shi_yin)
    return Divination.par_yao_earth(all_ylqgzws,yao)
  end
  def Divination.par_shi_five(all_ylqgzws,par_shi_yin)
    yao = Divination.par_shi_yao(par_shi_yin)
    return Divination.par_yao_five(all_ylqgzws,yao)
  end
  def Divination.par_shi(all_ylqgzws,par_five_levels,par_shi_yin)
    ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    aOut = Array.new(par_five_levels.length,"")

    yao = Divination.par_shi_yao(par_shi_yin)

    ylqgzw = Divination.par_find_ylqgzw_by_yao(ylqgzws,yao)
    wu_xing = Divination.fu_shen_wu_xing(ylqgzw)

    n = Divination.par_find_five_level_index(par_five_levels,wu_xing)
    a = Divination.Hsi_str(yao)
    aOut[n] = a[1]

    return aOut
  end
  def Divination.par_yin_yao(par_shi_yin)
    return par_shi_yin[1]
  end
  def Divination.par_yin_earth(all_ylqgzws,par_shi_yin)
    yao = Divination.par_yin_yao(par_shi_yin)
    return Divination.par_yao_earth(all_ylqgzws,yao)
  end
  def Divination.par_yin_five(all_ylqgzws,par_shi_yin)
    yao = Divination.par_yin_yao(par_shi_yin)
    return Divination.par_yao_five(all_ylqgzws,yao)
  end
  def Divination.par_yin(all_ylqgzws,par_five_levels,par_shi_yin)
    ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    aOut = Array.new(par_five_levels.length,"")

    yao = Divination.par_yin_yao(par_shi_yin)

    ylqgzw = Divination.par_find_ylqgzw_by_yao(ylqgzws,yao)
    wu_xing = Divination.fu_shen_wu_xing(ylqgzw)

    n = Divination.par_find_five_level_index(par_five_levels,wu_xing)
    a = Divination.Yin_str(yao)
    aOut[n] = a[1]
    
    return aOut
  end

  # 長 生
  def Divination.par_chang_sheng(par_five_levels,nDayEarth)
    aOut = Array.new(par_five_levels.length,nil)
    par_five_levels.each_index do |i|
      five = par_five_levels[i]
      aOut[i] = Divination.chang_sheng(five,nDayEarth)
    end

    return aOut
  end

  # 空 亡
  # 以日干支查詢空亡,例如 丙子日,查天干丙對支子,查左邊之藍字為「XXXX午、 未 (申酉)」為空亡。
  # 空亡位  甲  乙  丙  丁  戊  己  庚  辛  壬  癸
  # 戌亥    子  丑  寅  卯  辰  巳  午  未  申  酉
  # 申酉    戌  亥  子  丑  寅  卯  辰  巳  午  未
  # 午未    申  酉  戌  亥  子  丑  寅  卯  辰  巳
  # 辰巳    午  未  申  酉  戌  亥  子  丑  寅  卯
  # 寅卯    辰  巳  午  未  申  酉  戌  亥  子  丑
  # 子丑    寅  卯  辰  巳  午  未  申  酉  戌  亥
  @@Kong_Wang = [[11,12],[9,10],[7,8],[5,6],[3,4],[1,2]]
  @@Kong_Wang_Sky_Earth = [
                             [1,11,9,7,5,3],
                             [2,12,10,8,6,4],
                             [3,1,11,9,7,5],
                             [4,2,12,10,8,6],
                             [5,3,1,11,9,7],
                             [6,4,2,12,10,8],
                             [7,5,3,1,11,9],
                             [8,6,4,2,12,10],
                             [9,7,5,3,1,11],
                             [10,8,6,4,2,12]
                          ]
  def Divination.Kong_Wang(nDaySky,nDayEarth)
    a = @@Kong_Wang_Sky_Earth[nDaySky - 1]
    n = a.index(nDayEarth)
    return @@Kong_Wang[n]
  end
  def Divination.par_Kong_Wang_str(nDaySky,nDayEarth)
    a = Divination.Kong_Wang(nDaySky,nDayEarth)
    return Divination.Kong_Wang_str(a)
  end
  def Divination.Kong_Wang_str(a)
    aOut = Array.new(a.length,"")
    a.each_index do |i|
      aOut[i] = Divination.Earth_Str(a[i])
    end
    return aOut
  end
  # 貴 人
    # 占卜日之日天干決定。
    # 日干   甲  乙   丙   丁   戊   己   庚   辛   壬   癸
    # 貴人1 丑未 子申 酉亥 酉亥 丑未  子申 寅午 寅午 卯己 卯己
    # 貴人2                             丑未
  @@Gui_Ren1 = [
                   [2,8],
                   [1,9],
                   [10,12],
                   [10,12],
                   [2,8],
                   [1,9],
                   [3,7],
                   [3,7],
                   [4,6],
                   [4,6]
                 ]
  @@Gui_Ren2 = [
                   [],
                   [],
                   [],
                   [],
                   [],
                   [],
                   [2,8],
                   [],
                   [],
                   []
                 ]
  def Divination.gui_ren(nDaySky,n=1)
    if (n == 1) then
      return @@Gui_Ren1[nDaySky - 1]
    else
      return @@Gui_Ren2[nDaySky - 1]
    end
  end
  def Divination.par_gui_ren_str(nDaySky,n=1)
    aEarth = Divination.gui_ren(nDaySky,n)
    return Divination.gui_ren_str(aEarth)
  end
  def Divination.gui_ren_str(aEarth)
    aOut = Array.new(aEarth.length,"")
    aEarth.each_index do |i|
      aOut[i] = Divination.Earth_Str(aEarth[i])
    end
    return aOut
  end


  # 羊 刃
    # 占卜日之日天干決定。
    # 日干 甲 乙 丙 丁 戊 己 庚 辛 壬 癸
    # 擎羊 卯 辰 午 未 午 未 酉 戌 子 丑
  @@Yang_Ren = [4,5,7,8,7,8,10,11,1,2]
  def Divination.Yang_Ren(nDaySky)
    return @@Yang_Ren[nDaySky - 1]
  end
  def Divination.Yang_Ren_str(nDaySky)
    nEarth = Divination.Yang_Ren(nDaySky)
    return Divination.Earth_Str(nEarth)
  end

  # 干 祿
    # 占卜日之日天干決定。
    # 日干 甲 乙 丙 丁 戊 己 庚 辛 壬 癸
    # 干祿 寅 卯 巳 午 巳 午 申 酉 亥 子
  @@Gan_Lu = [3,4,6,7,6,7,9,10,12,1]
  def Divination.Gan_Lu(nDaySky)
    return @@Gan_Lu[nDaySky - 1]
  end
  def Divination.Gan_Lu_str(nDaySky)
    nEarth = Divination.Gan_Lu(nDaySky)
    return Divination.Earth_Str(nEarth)
  end

  # 驛 馬
  # 占卜日之日地支決定。
  # 日支 子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
  # 天馬 寅 亥 申 巳 寅 亥 申 巳 寅 亥 申 巳
  @@Ten_ma = [3,12,9,6,3,12,9,6,3,12,9,6]
  def Divination.Ten_ma(nDayEarth)
    return @@Ten_ma[nDayEarth - 1]
  end
  def Divination.Ten_ma_str(nDayEarth)
    nEarth = Divination.Ten_ma(nDayEarth)
    return Divination.Earth_Str(nEarth)
  end

  # 桃 花
  # 日支 子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
  # 桃花 酉 午 卯 子 酉 午 卯 子 酉 午 卯 子
  @@Tao_Wow = [10,7,4,1,10,7,4,1,10,7,4,1]
  def Divination.Tao_Wow(nDayEarth)
    return @@Tao_Wow[nDayEarth - 1]
  end
  def Divination.Tao_Wow_str(nDayEarth)
    nEarth = Divination.Tao_Wow(nDayEarth)
    return Divination.Earth_Str(nEarth)
  end

  # 卦 身
  #     陽爻 陰爻
  # 六爻 巳  亥 
  # 五爻 辰  戌 
  # 四爻 卯  酉 
  # 三爻 寅  申 
  # 二爻 丑  未 
  # 初爻 子  午 
  # [陰爻，陽爻]
  @@Gua_Body = [[12,11,10,9,8,7],[6,5,4,3,2,1]]
  def Divination.par_gua_body(par_shi_yin,gua_yaos,all_ylqgzws)
    aOut = Array.new(2)

    # 世所在的卦爻的位置
    shi_yao = par_shi_yin[0]
    shi_yao_index = Divination.Yao2Index(shi_yao)

    # 世所在的卦爻的陰陽值，0陰， 1陽
    gua_yao = gua_yaos[shi_yao_index]

    # 卦身的地支
    gua_body_earths = @@Gua_Body[gua_yao]
    gua_body_earth = gua_body_earths[shi_yao_index]

    # 卦身地支的五行
    wu_xing = Divination.Earth_Five(gua_body_earth)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    a = Divination.par_find_ylqgzw_by_wu_xing(ylqgzws,wu_xing)
    liu_qin = Divination.fu_shen_liu_qin(a)

    aOut[0] = gua_body_earth
    aOut[1] = liu_qin
    
    return aOut
  end
  def Divination.par_gua_body_str(par_shi_yin,gua_yaos,all_ylqgzws)
    gua_body = Divination.par_gua_body(par_shi_yin,gua_yaos,all_ylqgzws)
    return Divination.gua_body_str(gua_body)
  end
  def Divination.gua_body_str(gua_body)
    earth_str = Divination.Earth_Str(gua_body[0])
    liu_qin_str = Divination.liu_qin_str(gua_body[1])
    return [earth_str,liu_qin_str]
  end

  # 三 合
      # 動爻:是指占卜日之年、月、日的干支。
      # 靜爻:是指本卦及變卦之干支。
      # 地 支 三 合 : 申子辰合化水、巳酉丑合化金、寅午戌合化火、亥卯未合化木。 三合顯示規則:不論是在動爻或靜爻,
      # 只有三組地支同時存在,在參數區中顯示三合之訊息。
  @@San_Hes = [
                [[9,1,5],5],
                [[6,10,2],4],
                [[3,7,11],2],
                [[12,4,8],1]
              ]
  def Divination.san_he(aEarth)
    aOut = Array.new
    a2 = aEarth.uniq
    @@San_Hes.each_index do |i|
      a = @@San_Hes[i]
      a3 = a[0].select { |n| a2.index(n) != nil  } 
      if (a3 == a[0]) then
        aOut.push(a)
      end
    end
    return aOut
  end
  def Divination.san_he_check(dong_yao_di_zhi,jing_yao_di_zhi)
    aOut = Array.new
    aEarth = dong_yao_di_zhi + jing_yao_di_zhi
    a2 = aEarth.uniq
    @@San_Hes.each_index do |i|
      a = @@San_Hes[i]
      a3 = a[0].select { |n| a2.index(n) != nil  } 
      if (a3 == a[0]) then
        if (Divination.has_same_item(dong_yao_di_zhi,a[0])) then
          aOut.push(a[0])
        end
      end
    end
    return aOut
  end
  def Divination.par_san_he_str(dong_yao_di_zhi,all_ylqgzws)
    jing_yao_di_zhi = Divination.par_benbian_gua_di_zhis(all_ylqgzws)

    aEarth = dong_yao_di_zhi + jing_yao_di_zhi
    a = Divination.san_he(aEarth)
    return Divination.san_he_str(a)
  end
  def Divination.san_he_str(a)
    h = Hash.new
    h["jieguo_count"] = a.length
    a.each_index do |i|
      h["jieguo_#{i + 1}"] = Divination.Earth_Strs(a[i][0])
    end
    return h
  end

  # 三 刑
  # 靜爻三刑不計算,其他動爻或是動與靜爻其中有即是。
  # 地 支 三 刑 : 寅、巳、申。丑、戌、未。
  @@San_Xing = [
                [3,6,9],
                [2,11,8]
              ]
  def Divination.San_Xing(dong_yao_di_zhi,jing_yao_di_zhi)
    aOut = Array.new
    aEarth = dong_yao_di_zhi + jing_yao_di_zhi
    a2 = aEarth.uniq
    @@San_Xing.each_index do |i|
      a = @@San_Xing[i]
      a3 = a.select { |n| a2.index(n) != nil  } 
      if (a3 == a) then
        # 靜爻三刑不計算,其他動爻或是動與靜爻其中有即是。
        if (Divination.has_same_item(dong_yao_di_zhi,a)) then
          aOut.push(a)
        end
      end
    end
    return aOut
  end
  def Divination.has_same_item(a1,a2)
    a = a1 & a2
    return a.length > 0
  end
  def Divination.par_san_xing_str(dong_yao_di_zhi,all_ylqgzws)
    jing_yao_di_zhi = Divination.par_benbian_gua_di_zhis(all_ylqgzws)
    a = Divination.San_Xing(dong_yao_di_zhi,jing_yao_di_zhi)
    return Divination.San_Xing_str(a)
  end
  def Divination.San_Xing_str(a)
    h = Hash.new
    h["jieguo_count"] = a.length
    a.each_index do |i|
      h["jieguo_#{i + 1}"] = Divination.Earth_Strs(a[i])
    end
    return h
  end

  # 互 刑
    # 互刑:子、卯。
  @@Hu_Xing = [
                [1,4]
              ]
  def Divination.Hu_Xing(aEarth)
    aOut = Array.new
    a2 = aEarth.uniq
    @@Hu_Xing.each_index do |i|
      a = @@Hu_Xing[i]
      a3 = a.select { |n| a2.index(n) != nil  } 
      if (a3 == a) then
        aOut.push(a)
      end
    end
    return aOut
  end
  def Divination.par_hu_xing_str(dong_yao_di_zhi,all_ylqgzws)
    jing_yao_di_zhi = Divination.par_benbian_gua_di_zhis(all_ylqgzws)

    aEarth = dong_yao_di_zhi + jing_yao_di_zhi
    a = Divination.Hu_Xing_Check(dong_yao_di_zhi,jing_yao_di_zhi)
    a.uniq!
    return Divination.Hu_Xing_str(a)
  end
  def Divination.Hu_Xing_Check(dong_yao_di_zhi,jing_yao_di_zhi)
    aOut = Array.new
    dong_yao_di_zhi.each_index do |i|
      jing_yao_di_zhi.each_index do |j|
        nEarth = dong_yao_di_zhi[i]
        nEarth2 = jing_yao_di_zhi[j]
        if (Divination.par_is_hu_xing?(nEarth,nEarth2)) then
          aOut.push([nEarth,nEarth2])
        end
      end
    end
    return aOut
  end
  def Divination.Hu_Xing_str(a)
    h = Hash.new
    h["jieguo_count"] = a.length
    a.each_index do |i|
      h["jieguo_#{i + 1}"] = Divination.Earth_Strs(a[i])
    end
    return h
  end
  @@Par_Hu_Xings = [4,0,0,1,5,0,7,0,0,10,0,12]
  def Divination.par_is_hu_xing?(nEarth,nEarth2)
    return @@Par_Hu_Xings[nEarth - 1] == nEarth2
  end

  # 自刑
    # 自 刑 : 辰、午、酉、亥。辰見辰、午見午、酉見酉、亥見亥。
  @@Zi_Xing = [5,7,10,12]
  def Divination.zi_xing(aDateEarth,aGuaEarth)
    aOut = Array.new
    @@Zi_Xing.each_index do |i|
      n = @@Zi_Xing[i]
      if (aDateEarth.include?(n) && aGuaEarth.include?(n)) then
        aOut.push(n)
      end
    end
    return aOut
  end
  def Divination.par_zi_xing_str(aDateEarth,all_ylqgzws)
    aGuaEarth = Divination.par_benbian_gua_di_zhis(all_ylqgzws)
    a = Divination.zi_xing(aDateEarth,aGuaEarth)
    return Divination.zi_xing_str(a)
  end
  def Divination.zi_xing_str(a)
    h = Hash.new
    h["jieguo_count"] = a.length
    a.each_index do |i|
      h["jieguo_#{i + 1}"] = Divination.Earth_Str(a[i])
    end
    return h
  end
  # 六合
  # 年 合  月 合  日 合
  # 地支 子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
  #     丑 子 亥 戌 酉 申 未 午 巳 辰 卯 寅
  @@Par_Her6 = [2,1,12,11,10,9,8,7,6,5,4,3]
  def Divination.par_liu_he(nEarth)
    return @@Par_Her6[nEarth - 1]
  end
  def Divination.par_is_liu_he?(nEarth,nEarth2)
    return Divination.par_liu_he(nEarth) == nEarth2
  end
  # # 日合生
  # @@Par_He_San = [[辰,酉],[午,未],[亥,寅]]
  # # 日合
  # @@Par_He = [[酉 辰],[未,午],[寅,亥],[戌,卯],[申,巳],[子,丑]]
  # # 日合剋
  # @@Par_He_Ke = [[卯,戌],[巳,申],[丑,子]]
  # 日合生
  @@Par_He_San = [[5,10],[7,8],[12,3]]
  def Divination.par_is_he_san?(nEarth,nEarth2)
    @@Par_He_San.each_index do |i|
      if (@@Par_He_San[i] == [nEarth,nEarth2]) then 
        return true
      end
    end
    return false
  end
  # 日合
  @@Par_He = [[10,5],[8,7],[3,12],[11,4],[9,6],[1,2]]
  def Divination.par_is_he?(nEarth,nEarth2)
    @@Par_He.each_index do |i|
      if (@@Par_He[i] == [nEarth,nEarth2]) then 
        return true
      end
    end
    return false
  end
  # 日合剋
  @@Par_He_Ke = [[4,11],[6,9],[2,1]]
  def Divination.par_is_he_ke?(nEarth,nEarth2)
    @@Par_He_Ke.each_index do |i|
      if (@@Par_He_Ke[i] == [nEarth,nEarth2]) then 
        return true
      end
    end
    return false
  end
  
  def Divination.par_liu_he_str(nEarthIn)
    nEarth = Divination.par_liu_he(nEarthIn)
    return Divination.Earth_Str(nEarth)
  end
  def Divination.par_date_he(nEarth,all_ylqgzws)
    all_gua_earths = Divination.par_benbian_gua_di_zhis(all_ylqgzws)

    earth_he = Divination.par_liu_he(nEarth)
    if (all_gua_earths.include?(earth_he)) then
      return Divination.Earth_Str(earth_he)
    end
    return ""
  end

  # 六沖
  # 年 衝  月 衝  日 衝
  # 地支 子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
  @@Par_Chung6 = [7,8,9,10,11,12,1,2,3,4,5,6]
  def Divination.par_liu_chong(nEarth)
    return @@Par_Chung6[nEarth - 1]
  end
  def Divination.par_is_liu_chong?(nEarth,nEarth2)
    return Divination.par_liu_chong(nEarth) == nEarth2
  end
  def Divination.par_liu_chong_str(nEarthIn)
    nEarth = Divination.par_liu_chong(nEarthIn)
    return Divination.Earth_Str(nEarth)
  end
  
  def Divination.par_date_chong(nEarth,all_ylqgzws)
    all_gua_earths = Divination.par_benbian_gua_di_zhis(all_ylqgzws)

    nEarthChong = Divination.par_liu_chong(nEarth)
    if (all_gua_earths.include?(nEarthChong)) then
      return Divination.Earth_Str(nEarthChong)
    end
    return ""
  end
  def Divination.par_all_earths(cur_earth,chg_earth,liu_yao_change)
    aOut = cur_earth
    liu_yao_change.each_index do |i|
      if (liu_yao_change[i] >= Bian_Yao_In) then
        aOut.push(chg_earth[i])
      end
    end
    return aOut
  end
  def Divination.g_make_all_ylqgzw_s(nGanZhiYear,nGanZhiMonth,nGanZhiDay,wai,nei,bian_wai,bian_nei)
    ben_gua_sky_earths = Divination.ben_gua_sky_earths(wai,nei)
    bian_gua_sky_earths = Divination.bian_gua_sky_earths(wai,nei,bian_wai,bian_nei)
    ben_liu_qins = Divination.Gua_64_liu_qins(wai,nei)
    bian_liu_qins = Divination.bian_gua_liu_qins(wai,nei,bian_wai,bian_nei)
    liu_yao_change = Divination.liu_yao_bian_check(wai,nei,bian_wai,bian_nei)
    fu_shens = Divination.Gua_64_fu_shens(wai,nei)
    yao_ylqgzws = Divination.par_make_all_yao_ylqgzws(ben_gua_sky_earths,bian_gua_sky_earths,ben_liu_qins,bian_liu_qins,liu_yao_change,fu_shens)
    ganzhi_ylqgzws = Divination.par_ganzhi_ylqgzw_s(nGanZhiYear,nGanZhiMonth,nGanZhiDay,yao_ylqgzws)
    all_ylqgzws = yao_ylqgzws
    all_ylqgzws[Divination::Ylqgzw_Date] = ganzhi_ylqgzws

    return all_ylqgzws
  end
  Ylqgzw_BenGua = 0
  Ylqgzw_BianGua = 1
  Ylqgzw_FuSheng = 2
  Ylqgzw_Date = 3
  Ylqgzw_JingYao = 4
  def Divination.par_make_all_yao_ylqgzws(ben_gua_sky_earths,bian_gua_sky_earths,ben_liu_qins,bian_liu_qins,liu_yao_change,fu_shens=nil)
    aOut = Array.new(5)
    aOut[Divination::Ylqgzw_BenGua] = ben_liu_qins.map.with_index { |x,i| 
      [Divination.Index2Yao(i),ben_liu_qins[i],[ben_gua_sky_earths[i][0],ben_gua_sky_earths[i][1],ben_gua_sky_earths[i][2]]]
    }

    aOut[Divination::Ylqgzw_BianGua] = Array.new
    liu_yao_change.each_index do |i|
      if (liu_yao_change[i] >= Bian_Yao_In) then
        yao = Divination.Index2Yao(i)
        liu_qin = bian_liu_qins[i]
        gan,zi,wu_xing = bian_gua_sky_earths[i][0],bian_gua_sky_earths[i][1],bian_gua_sky_earths[i][2]
        a = Divination.par_make_yao_liu_qin_gan_zi_wu_xing(yao,liu_qin,gan,zi,wu_xing)
        aOut[Divination::Ylqgzw_BianGua].push(a)
      end
    end

    aOut[Divination::Ylqgzw_FuSheng] = Array.new
    if (fu_shens != nil) then
      fu_shens.each_index do |i|
        aOut[Divination::Ylqgzw_FuSheng].push(fu_shens[i])
      end
    end

    a2 = aOut[Divination::Ylqgzw_BenGua].clone
    liu_yao_change.each_index do |i|
      if (liu_yao_change[i] >= Bian_Yao_In) then
        yao = Divination.Index2Yao(i)
        a2.select! { |n| n[0] != yao  } 
      end
    end
    aOut[Divination::Ylqgzw_JingYao] = a2

    return aOut
  end
  def Divination.par_ylqgzws_2_di_zhis(ylqgzws)
    aOut = Array.new
    ylqgzws.each_index do |i|
      aOut.push(Divination.fu_shen_di_zhi(ylqgzws[i]))
    end
    return aOut
  end
  def Divination.par_ylqgzws_2_tian_gans(ylqgzws)
    aOut = Array.new
    ylqgzws.each_index do |i|
      aOut.push(Divination.fu_shen_tian_gan(ylqgzws[i]))
    end
    return aOut
  end
  def Divination.par_ylqgzws_2_liu_qins(ylqgzws)
    aOut = Array.new
    ylqgzws.each_index do |i|
      aOut.push(Divination.fu_shen_liu_qin(ylqgzws[i]))
    end
    return aOut
  end
  def Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    return all_ylqgzws[Divination::Ylqgzw_BenGua]
  end
  def Divination.par_ben_gua_tian_gans(all_ylqgzws)
    ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    return Divination.par_ylqgzws_2_tian_gans(ylqgzws)
  end
  def Divination.par_ben_gua_di_zhis(all_ylqgzws)
    ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    return Divination.par_ylqgzws_2_di_zhis(ylqgzws)
  end
  def Divination.par_ben_gua_liu_qins(all_ylqgzws)
    ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    return Divination.par_ylqgzws_2_liu_qins(ylqgzws)
  end
  def Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    return all_ylqgzws[Divination::Ylqgzw_BianGua]
  end
  def Divination.par_bian_gua_tian_gans(all_ylqgzws)
    ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    return Divination.par_ylqgzws_2_tian_gans(ylqgzws)
  end
  def Divination.par_bian_gua_di_zhis(all_ylqgzws)
    ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    return Divination.par_ylqgzws_2_di_zhis(ylqgzws)
  end
  def Divination.par_fu_sheng_ylqgzw(all_ylqgzws)
    return all_ylqgzws[Divination::Ylqgzw_FuSheng]
  end
  def Divination.par_all_yao_ylqgzws(all_ylqgzws)
    return all_ylqgzws[Divination::Ylqgzw_BenGua] + all_ylqgzws[Divination::Ylqgzw_BianGua] + all_ylqgzws[Divination::Ylqgzw_FuSheng]
  end
  def Divination.par_all_yao_di_zhis(all_ylqgzws)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    return Divination.par_ylqgzws_2_di_zhis(ylqgzws)
  end
  def Divination.par_all_gua_ylqgzws(all_ylqgzws)
    return all_ylqgzws[Divination::Ylqgzw_BenGua] + all_ylqgzws[Divination::Ylqgzw_BianGua] + all_ylqgzws[Divination::Ylqgzw_FuSheng] + all_ylqgzws[Divination::Ylqgzw_Date]
  end
  def Divination.par_benbian_gua_ylqgzws(all_ylqgzws)
    return all_ylqgzws[Divination::Ylqgzw_BenGua] + all_ylqgzws[Divination::Ylqgzw_BianGua]
  end
  def Divination.par_benbian_gua_di_zhis(all_ylqgzws)
    ylqgzws = Divination.par_benbian_gua_ylqgzws(all_ylqgzws)
    return Divination.par_ylqgzws_2_di_zhis(ylqgzws)
  end
  def Divination.par_dong_gua_ylqgzws(all_ylqgzws)
    return all_ylqgzws[Divination::Ylqgzw_BianGua] + all_ylqgzws[Divination::Ylqgzw_Date]
  end
  def Divination.par_dong_gua_di_zhis(all_ylqgzws)
    ylqgzws = Divination.par_dong_gua_ylqgzws(all_ylqgzws)
    return Divination.par_ylqgzws_2_di_zhis(ylqgzws)
  end
  def Divination.par_jing_yao_ylqgzws(all_ylqgzws)
    return all_ylqgzws[Divination::Ylqgzw_JingYao]
  end
  def Divination.par_jing_yao_di_zhis(all_ylqgzws)
    ylqgzws = Divination.par_jing_yao_ylqgzws(all_ylqgzws)
    return Divination.par_ylqgzws_2_di_zhis(ylqgzws)
  end

  def Divination.par_date_ylqgzw(all_ylqgzws)
    return all_ylqgzws[Divination::Ylqgzw_Date]
  end
  def Divination.par_find_ylqgzw_by_wu_xing(ylqgzws,wu_xing)
    ylqgzws.each_index do |i|
      a = ylqgzws[i]
      if (Divination.fu_shen_wu_xing(a) == wu_xing) then
        return a
      end
    end
    return nil
  end
  def Divination.par_find_ylqgzw_by_yao(ylqgzws,yao)
    ylqgzws.each_index do |i|
      a = ylqgzws[i]
      if (Divination.fu_shen_yao(a) == yao) then
        return a
      end
    end
    return nil
  end
  def Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,di_zhi)
    ylqgzws.each_index do |i|
      a = ylqgzws[i]
      if (Divination.fu_shen_di_zhi(a) == di_zhi) then
        return a
      end
    end
    return nil
  end
  def Divination.par_find_ylqgzw_by_liu_qin(ylqgzws,liu_qin)
    ylqgzws.each_index do |i|
      a = ylqgzws[i]
      if (Divination.fu_shen_liu_qin(a) == liu_qin) then
        return a
      end
    end
    return nil
  end
  def Divination.par_find_dong_yao_ylqgzws(all_ylqgzws)
    aOut = Array.new
    ben_ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_ylqgzws[i]
      yao = Divination.fu_shen_yao(bian_ylqgzw)
      a = Divination.par_find_ylqgzw_by_yao(ben_ylqgzws,yao)
      aOut.push(a)
    end
    return aOut
  end
  def Divination.par_make_yao_liu_qin_gan_zi_wu_xing(yao,liu_qin,gan,zi,wu_xing)
    return [yao,liu_qin,[gan,zi,wu_xing]]
  end

  def Divination.par_ganzhi_ylqgzw_s(nGanZhiYear,nGanZhiMonth,nGanZhiDay,yao_ylqgzws)
    aOut = Array.new
    aOut.push(Divination.par_ganzhi_ylqgzw(nGanZhiYear,yao_ylqgzws))
    aOut.push(Divination.par_ganzhi_ylqgzw(nGanZhiMonth,yao_ylqgzws))
    aOut.push(Divination.par_ganzhi_ylqgzw(nGanZhiDay,yao_ylqgzws))
    return aOut
  end
  def Divination.par_ganzhi_ylqgzw(nGanZhi,yao_ylqgzws)
    nEarth = Xdate.GetGanZhiEarth(nGanZhi)
    nSky = Xdate.GetGanZhiSky(nGanZhi)
    wu_xing = Divination.Earth_Five(nEarth)
    ylqgzws = Divination.par_all_yao_ylqgzws(yao_ylqgzws)
    a = Divination.par_find_ylqgzw_by_wu_xing(ylqgzws,wu_xing)  # 取第一個遇到的
    liu_qin = Divination.fu_shen_liu_qin(a)
    yao = Divination.fu_shen_yao(a)
    return Divination.par_make_yao_liu_qin_gan_zi_wu_xing(yao,liu_qin,nSky,nEarth,wu_xing)
  end

  # 天喜之用口訣為：「春戌夏丑為天喜，秋辰冬未世歡喜，世爻遇此屬吉利，百事得之皆有理」。
  # 立春 春天 立夏 夏 立秋 秋 立冬 冬
  def Divination.tianxi(nWYear, nWMonth, nWDate)
    nSeason = Xdate.GetSeasonBySegment(nWYear, nWMonth, nWDate)
    # dizhis = [戌，丑，辰，未]
    dizhis = [11,2,5,8]
    return dizhis[nSeason]
  end
  def Divination.tianxi_dizhi_str2(nWYear, nWMonth, nWDate)
    dizhi = Divination.tianxi(nWYear, nWMonth, nWDate)
    return Earth.GetName(dizhi)
  end
  def Divination.tianxi_dizhi_str(nSegMonth)
    nSeason = (Earth.ModifyEarth(nSegMonth - 2)-1) / 3
    dizhis = [11,2,5,8]
    dizhi = dizhis[nSeason]
    return Earth.GetName(dizhi)
  end

  # 劫煞，查法：以日支為標尺，逢申子辰日劫煞在巳，逢巳酉丑日劫煞在寅，逢寅午戌日劫煞在亥，逢亥卯未日劫煞在申。
  def Divination.jie_sha(nWYear, nWMonth, nWDate)
    ganzhi = Xdate.GetSegDateGanzhi(nWYear, nWMonth,nWDate, 22)
    nSky,nEarth = Xdate.GetGanZhiSkyEarth(ganzhi)
    # dizhis = [巳，寅，亥，申，巳，寅，亥，申，巳，寅，亥，申]
    dizhis = [6,3,12,9,6,3,12,9,6,3,12,9]
    return dizhis[Earth.Earth2EarthIndex(nEarth)]

  end
  def Divination.jiesha_dizhi_str2(nWYear, nWMonth, nWDate)
    dizhi = Divination.jie_sha(nWYear, nWMonth, nWDate)
    return Earth.GetName(dizhi)
  end
  def Divination.jiesha_dizhi_str(nEarth)
    # dizhis = [巳，寅，亥，申，巳，寅，亥，申，巳，寅，亥，申]
    dizhis = [6,3,12,9,6,3,12,9,6,3,12,9]
    dizhi = dizhis[Earth.Earth2EarthIndex(nEarth)]
    return Earth.GetName(dizhi)
  end
end
