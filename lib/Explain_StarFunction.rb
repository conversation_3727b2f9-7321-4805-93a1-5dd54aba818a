#require "#{ENV['RAILS_ROOT']}/config/environment.rb"
require 'active_record'
require("PanWeb.rb")
require("Star.rb")
#require "#{ENV['RAILS_ROOT']}/app/models/tz_main.rb"
#require "D:/Pro58_2010/Ruby/projects/vip/app/models/tz_main.rb"
#require("D:/Pro58_2010/Ruby/projects/vip/app/models/four_out.rb")
#require("D:/Pro58_2010/Ruby/projects/vip/app/models/four_k.rb")
# require "tz_main.rb"
# require "four_out.rb"
# require "four_k.rb"
require "TzMainData.rb"
require "FourKData.rb"
require "FourOutData.rb"

class Explain
  # constant
  EX_PANTYPE = "ex_pantype"
  EX_HOUSE = "ex_house"
  EX_SKY = "ex_sky"
  EX_EARTH = "ex_earth"
  EX_FIVE = "ex_five"
  EX_STAR = "ex_star"
  EX_STAR_A = "ex_star_a"
  EX_STAR_B = "ex_star_b"
  EX_STAR_C = "ex_star_c"
  EX_FOURHUA = "ex_fourhua"

  @StarInfo
  @PanInfo
  @m_nPanType
  @AllPanInfo
  @UserAskData
  @UserInfo
  @Star
  # @FourK
  # @FourOut
  # @TzMain
  def ex_Star_getExplain(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @lang = sLang
    @m_nPanType = nPanType
    @UserInfo = hUserInfo
    @ApFunc = hApFunc
    @ParAll = hParAll
    @Star = Star.new
    @StarInfo = @Star.g_GetStarPanInfo(nPanType,hUserInfo,hUserAskData)
    @AllPanInfo = @StarInfo[Star::U_PAN_INFO]
    @PanInfo = @AllPanInfo[nPanType]
    @UserAskData = hUserAskData
    @ExplainHouse = @UserAskData[Star::HOUSE_NAME]
    if (@ExplainHouse == nil) then
      @ExplainHouse = Star::EX_HOUSE_ALL
    end

    # @FourK = FourKData.new
    # @FourOut = FourOutData.new
    # @TzMain = TzMainData.new
    return ex_Star_Create()
  end

  def ex_Star_Create()
    sBuf = ""
    sBuf += PanWeb.pw_GetCenter()
    #sBuf += PanWeb.pw_GetPan_table()
    sBuf += "<TABLE BORDER=1 WIDTH=680 ALIGN=CENTER>"
    sBuf += PanWeb.pw_GetPan_table_tr()
    sBuf += PanWeb.pw_GetPan_table_td()
    case @m_nPanType
    when Cfate::PAN_NORMAL then
      sBuf += ex_Star_Create_AllHouse(@m_nPanType)
    else
      if (@ExplainHouse == Star::EX_HOUSE_ALL) then
        sBuf += ex_Star_Create_AllHouse(@m_nPanType)
      else
        sBuf += ex_Star_Create_OneHouse(@m_nPanType,@ExplainHouse)
      end
    end
    sBuf += PanWeb.pw_GetPan_table_td_end()
    sBuf += PanWeb.pw_GetPan_table_tr_end()
    sBuf += PanWeb.pw_GetPan_table_end()
    return sBuf
  end

  # 本命盤：則各宮顯示說明(十二宮分析)
  def ex_Star_Create_AllHouse(nPanType)
    sBuf = ""

    nEarth = @Star.gHouse_GetFirstHouseEarth(nPanType)
    # 1..12 表示子宮到亥宮
    (1..12).each do |nHouse|
      if (ex_house_check(nHouse)) then
        sHouseName = @Star.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
        sBuf += sHouseName
        sBuf += PanWeb.pw_GetPan_br()

        # 說明部分
        sBuf += ex_Star_Create_HouseInfo_Explain_AllHouse(@Star,nPanType,nEarth)
      end

      # 尋找下一個宮名
      nEarth = @Star.gHouse_GetNextHouseEarth(nEarth)
    end
    return sBuf
  end

  def ex_Star_Create_HouseInfo_Explain_AllHouse(oStar,nPanType,nEarth)
    sBuf = ""

    # 四化部份：
    sBuf += ex_Star_Create_HouseInfo_Explain_FourHua(oStar,nPanType,nEarth)
    sBuf += PanWeb.pw_GetPan_br()

    # 星性顯示部份：
    sBuf += ex_Star_Create_HouseInfo_Explain_Star(oStar,nPanType,nEarth)

    return sBuf
  end

  def ex_house_check(nHouse)
    nHouseKey = 1 << (nHouse - 1)

    explain_house = 0
    if (@ApFunc["n_explain_house"] != nil) then
      explain_house = explain_house | @ApFunc["n_explain_house"].to_i
    end
    if (@ApFunc["n_explain_free_house"] != nil) then
      explain_house = explain_house | @ApFunc["n_explain_free_house"].to_i
    end

  # Pm.saveTestDb("ex_house_check2:#{nHouseKey},#{nHouseKey & explain_house}","#{explain_house}")
    return (nHouseKey & explain_house) == nHouseKey
  end

  def ex_content()
    content = 0
    if (@ApFunc["n_explain_content"] != nil) then
      content = content | @ApFunc["n_explain_content"].to_i
    end
    if (@ApFunc["n_explain_free_content"] != nil) then
      content = content | @ApFunc["n_explain_free_content"].to_i
    end
    return content
  end

  # 四化部份：
    # 機會點(化祿)
    # 擴展點(化權)
    # 轉折點(化科)
    # 注意點(化忌)
  def ex_Star_Create_HouseInfo_Explain_FourHua(oStar,nPanType,nEarth)
    sBuf = ""
    if (ex_content() & Star::EX_C_FOURHUA == Star::EX_C_FOURHUA) then
      (Star::FH_LU..Star::FH_GI).each do |nFourHuaIndex|
        # sBuf += Pm.GetStr("IDS_S_EX_FOURHUA_#{nFourHuaIndex}")
        # sBuf += PanWeb.pw_GetPan_br()

        # FourOut(文字)
        sBuf += ex_Star_Create_HouseInfo_Explain_FourHua_FourOut(oStar,nPanType,nEarth,nFourHuaIndex)

        # FourK(文字)
        sBuf += ex_Star_Create_HouseInfo_Explain_FourHua_FourK(oStar,nPanType,nEarth,nFourHuaIndex)

        sBuf += PanWeb.pw_GetPan_br()
        sBuf += PanWeb.pw_GetPan_br()
      end
    end
    return sBuf
  end

  # FourOut(文字)
#   def ex_Star_Create_HouseInfo_Explain_FourHua_FourOut(oStar,nPanType,nEarth,nFourHuaIndex)
#     sFh = Star.GetFourHuaStr(nFourHuaIndex)
#     sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)
#     sFourOutHouseName = oStar.gHouse_GetFourOutHouseName(nPanType,nEarth,nFourHuaIndex)

#   #puts(":HouseStay => #{sHouseName}, :HouseAccept => #{sFourOutHouseName}, :HuaType => #{sFh}")
#     oFourOut = FourOut.where(:lang => @lang, :PanType => nPanType, :HouseStay => sHouseName, :HouseAccept => sFourOutHouseName, :HuaType => sFh)
# #   oFourOut = FourOut.where(:HouseStay => sHouseName)
#     if (oFourOut[0] == nil) then
#       return ""
#     end
#     if (oFourOut[0].Descrip == nil) then
#       return ""
#     end
#     return oFourOut[0].Descrip
#   end

  def ex_Star_Create_HouseInfo_Explain_FourHua_FourOut(oStar,nPanType,nEarth,nFourHuaIndex)
    sFh = Star.GetFourHuaStr(nFourHuaIndex)
    sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)
    sFourOutHouseName = oStar.gHouse_GetFourOutHouseName(nPanType,nEarth,nFourHuaIndex)

    return FourOutData.getDesc(nPanType,sHouseName,sFourOutHouseName,sFh)
  end

  # FourK(文字)
#   def ex_Star_Create_HouseInfo_Explain_FourHua_FourK(oStar,nPanType,nEarth,nFourHuaIndex)
#     sFh = Star.GetFourHuaStr(nFourHuaIndex)
#     sHouseSky = oStar.gHouse_GetHouseSkyName(nPanType,nEarth)
#     nStar = oStar.gHouse_GetFourOutStar(nPanType,nEarth,nFourHuaIndex)
#     nFourOutHouseEarth = Star.HouseIndex2Earth(oStar.gHouse_GetAStar_HouseIndex(nPanType,nStar))
#     sHouseName = oStar.gHouse_GetHouseName(nPanType,nFourOutHouseEarth)

#     oFourK = FourK.where(:lang => @lang, :PanType => nPanType, :HouseStaySky => sHouseSky , :HouseStay => sHouseName, :HuaType => sFh)
#     if (oFourK[0] == nil) then
#       return ""
#     end
#     if (oFourK[0].Descrip == nil) then
#       return ""
#     end
# # Pm.saveTestDb("FourHua","#{nPanType},#{nEarth},#{sHouseSky},#{sHouseName},#{oFourK[0].Descrip}")
#     return oFourK[0].Descrip
#   end

  def ex_Star_Create_HouseInfo_Explain_FourHua_FourK(oStar,nPanType,nEarth,nFourHuaIndex)
    sFh = Star.GetFourHuaStr(nFourHuaIndex)
    sHouseSky = oStar.gHouse_GetHouseSkyName(nPanType,nEarth)
    nStar = oStar.gHouse_GetFourOutStar(nPanType,nEarth,nFourHuaIndex)
    nFourOutHouseEarth = Star.HouseIndex2Earth(oStar.gHouse_GetAStar_HouseIndex(nPanType,nStar))
    sHouseName = oStar.gHouse_GetHouseName(nPanType,nFourOutHouseEarth)
    # sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)

    return FourKData.getDesc(nPanType,sHouseSky,sHouseName,sFh)
  end

  # 星性顯示部份：
  def ex_Star_Create_HouseInfo_Explain_Star(oStar,nPanType,nEarth)
    sBuf = ""
    # 主要特徵現象
    sBuf += Pm.GetStr("IDS_S_A_STAR_EXPLAIN")
    sBuf += PanWeb.pw_GetPan_br()
    # 主星解釋
    sBuf += ex_Star_Create_HouseInfo_Explain_Star_Main(oStar,nPanType,nEarth)
    sBuf += PanWeb.pw_GetPan_br()
    sBuf += PanWeb.pw_GetPan_br()
    # 次要特徵現象
    sBuf += Pm.GetStr("IDS_S_B_STAR_EXPLAIN")
    sBuf += PanWeb.pw_GetPan_br()
    # 其他星解釋
    sBuf += ex_Star_Create_HouseInfo_Explain_Star_Sub(oStar,nPanType,nEarth)

    sBuf += PanWeb.pw_GetPan_br()
    sBuf += PanWeb.pw_GetPan_br()

    return sBuf
  end

  def ex_Star_Create_HouseInfo_Explain_Star_Main(oStar,nPanType,nEarth)
    sBuf = ""
    if (ex_content() & Star::EX_C_STAR_A == Star::EX_C_STAR_A) then
      sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)
      aStar,bUseOpp = oStar.gHouse_GetAStars(nPanType,nEarth)
      aStarName = oStar.gHouse_GetAStarNames(nPanType,nEarth)

      (0...aStar.length).each do |nIndex|
        # sBuf += Cfate.GetSpace(2)
        sSanKur = oStar.gExHouse_GetAStarSanKurName(nPanType,nEarth,aStar[nIndex])
        sBuf += ex_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseName,aStarName[nIndex],sSanKur)
        sBuf += PanWeb.pw_GetPan_br()
      end
    end
    return sBuf
  end

  # def ex_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseStay,sStar,sSanKur)
  #   # C.saveTestDb("Star_GetDescript",":lang => #{@lang}, :PanType => #{nPanType}, :HouseStay => #{sHouseStay} , :Star => #{sStar}, :SanKur => #{sSanKur}")
  #   oTaMain = TzMain.where(:lang => @lang, :PanType => nPanType, :HouseStay => sHouseStay , :Star => sStar, :SanKur => sSanKur)
  #   if (oTaMain[0] == nil) then
  #     return ""
  #   end

  #   if (oTaMain[0].Descrip == nil) then
  #     return ""
  #   end
  #   return oTaMain[0].Descrip
  # end

  def ex_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseStay,sStar,sSanKur)
    return TzMainData.getDesc(nPanType,sHouseStay,sStar,sSanKur)
  end

  def ex_Star_Create_HouseInfo_Explain_Star_Sub(oStar,nPanType,nEarth)
    sBuf = ""
    if (ex_content() & Star::EX_C_STAR_B == Star::EX_C_STAR_B) then
      sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)
      aStar = oStar.gHouse_GetBStars(nPanType,nEarth)
      aStarName = oStar.gHouse_GetBStarNames(nPanType,nEarth)

      (0...aStar.length).each do |nIndex|
        # sBuf += Cfate.GetSpace(2)
        sSanKur = oStar.gExHouse_GetBStarSanKurName(nPanType,nEarth,aStar[nIndex])
        sBuf += ex_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseName,aStarName[nIndex],sSanKur)
        sBuf += PanWeb.pw_GetPan_br()
      # Pm.saveTestDb("BStar","#{sHouseName},#{sSanKur},#{ex_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseName,aStarName[nIndex],sSanKur)}")
      end
    end
    return sBuf
  end

  def ex_Star_Create_HouseInfo_Explain_Star_C(oStar,nPanType,nEarth)
    sBuf = ""
    starTypes = [Star::DOCTOR,Star::YEARSTAR,Star::YEARGOD,Star::GOD]
    if (ex_content() & Star::EX_C_STAR_C == Star::EX_C_STAR_C) then
      sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)

      (0...starTypes.length).each do |nIndex|
        # sBuf += Cfate.GetSpace(2)
        nStar = oStar.gHouse_GetCStar(nPanType,nEarth,starTypes[nIndex])
        sStarName = oStar.gHouse_GetCStarName(nPanType,nEarth,starTypes[nIndex])
        sSanKur = oStar.gExHouse_GetCStarSanKurName(nPanType,nEarth,nStar,starTypes[nIndex])
        sExplain = ex_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseName,sStarName,sSanKur)
        if (sExplain.length > 0) then
          sBuf += sExplain
          sBuf += PanWeb.pw_GetPan_br()
        end
      # Pm.saveTestDb("BStar","#{sHouseName},#{sSanKur},#{ex_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseName,aStarName[nIndex],sSanKur)}")
      end
    end
    return sBuf
  end

  # 十年盤以下：則各宮顯示說明(十二宮分析)
  # 六個選項：(十年動盤以下適用)
  # 1、本命顯示該項目十二宮分析。
  # 2、事業(官祿宮)
  # 3、感情(夫妻宮)
  # 4、健康(疾厄宮)
  # 5、財務(財帛宮)
  # 6、外出(遷移宮)
  def ex_Star_Create_OneHouse(nPanType,nExplainHouse)
    sHouseName = Star.GetHouseName(nExplainHouse.to_s)
    sBuf = ""
    if (ex_house_check(nExplainHouse)) then
      nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
      sBuf += @Star.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
      sBuf += PanWeb.pw_GetPan_br()

      #nHouseId = Star.g_House_FindId(sHouseName)
      #if (nHouseId == 1) then
      case nPanType
      when Cfate::PAN_TENYEAR then
        sBuf += ex_Star_Create_TenYear(nPanType,sHouseName)
      when Cfate::PAN_FLOWYEAR then
        sBuf += ex_Star_Create_FlowYear(nPanType,sHouseName)
      when Cfate::PAN_FLOWMONTH then
        sBuf += ex_Star_Create_FlowMonth(nPanType,sHouseName)
      when Cfate::PAN_FLOWDATE then
        sBuf += ex_Star_Create_FlowDate(nPanType,sHouseName)
      when Cfate::PAN_FLOWTIME then
        sBuf += ex_Star_Create_FlowTime(nPanType,sHouseName)
      when Cfate::PAN_FLOWMIN then
        sBuf += ex_Star_Create_FlowMin(nPanType,sHouseName)
      else
        sBuf += ""
      end
    end

    return sBuf
  end

  # 十年部份：問某十年之事業問題(有各大運項目)。
  def ex_Star_Create_TenYear(nPanType,sHouseName)
    sBuf = ""
    # 1、顯示該流運十年之事業宮之資料顯示，如「頁面顯示說明」。
    nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
    sBuf += ex_Star_Create_HouseInfo_Explain(@Star,nPanType,nEarth)

    nNextPanType = nPanType + 1
    # 2、再顯示該十年流運之十年之事業宮資料。
    #nStarYear = @UserAskData[Cfate::EYear] # 十年大運的起始年
    nStarYear = @Star.g_getLargeSanStartYear()

    oStar = Star.new
    hAskData = Hash.new
    (0..9).each do |i|
      hAskData[Cfate::EYear] = nStarYear + i
      starInfo = oStar.g_GetStarPanInfo(nPanType,@UserInfo,hAskData)
      nEarth = oStar.g_House_FindEarth(nNextPanType,sHouseName)

      sBuf += hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
      sBuf += PanWeb.pw_GetPan_br()
      sBuf += ex_Star_Create_HouseInfo_Explain(oStar,nNextPanType,nEarth)
      sBuf += PanWeb.pw_GetPan_br()

    end


    return sBuf
  end


  def ex_Star_Create_HouseInfo_Explain(oStar,nPanType,nEarth)
    sBuf = ""
    # 四化部份：
    sBuf += ex_Star_Create_HouseInfo_Explain_FourHua(oStar,nPanType,nEarth)

    # 星性顯示部份：
    sBuf += ex_Star_Create_HouseInfo_Explain_Star(oStar,nPanType,nEarth)

    return sBuf
  end

  def ex_Star_Create_FlowYear(nPanType,sHouseName)
    sBuf = ""
    # 1、顯示該流運年之事業宮之資料顯示，如「頁面顯示說明」。
    nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
    sBuf += ex_Star_Create_HouseInfo_Explain(@Star,nPanType,nEarth)

    nNextPanType = nPanType + 1

    # 2、再顯示該年流運之十二月之事業宮資料。
    oStar = Star.new
    hAskData = @UserAskData
    (1..12).each do |i|
      hAskData[Cfate::EMonth] = i
      starInfo = oStar.g_GetStarPanInfo(nPanType,@UserInfo,hAskData)
      nEarth = oStar.g_House_FindEarth(nNextPanType,sHouseName)

      sBuf += hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
      sBuf += hAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")
      sBuf += PanWeb.pw_GetPan_br()
      sBuf += ex_Star_Create_HouseInfo_Explain(oStar,nNextPanType,nEarth)
      sBuf += PanWeb.pw_GetPan_br()

    end

    return sBuf
  end


  def ex_Star_Create_FlowMonth(nPanType,sHouseName)
    sBuf = ""
    # 1、顯示該流運年之事業宮之資料顯示，如「頁面顯示說明」。
    nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
    sBuf += ex_Star_Create_HouseInfo_Explain(@Star,nPanType,nEarth)

    return sBuf
  end


  def ex_Star_Create_FlowDate(nPanType,sHouseName)
    sBuf = ""
    # 1、顯示該流運年之事業宮之資料顯示，如「頁面顯示說明」。
    nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
    sBuf += ex_Star_Create_HouseInfo_Explain(@Star,nPanType,nEarth)

    return sBuf
  end

  def ex_Star_Create_FlowTime(nPanType,sHouseName)
    sBuf = ""
    # 1、顯示該流運時之事業宮之資料顯示，如「頁面顯示說明」。
    nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
    sBuf += ex_Star_Create_HouseInfo_Explain(@Star,nPanType,nEarth)

    return sBuf
  end

  def ex_Star_Create_FlowMin(nPanType,sHouseName)
    sBuf = ""
    # 1、顯示該流運分之事業宮之資料顯示，如「頁面顯示說明」。
    nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
    sBuf += ex_Star_Create_HouseInfo_Explain(@Star,nPanType,nEarth)

    return sBuf
  end

  def ex_Star_getSimpleExplain(sLang,hUserInfo,hUserAskData,hApFunc,hParAll)
    @lang = sLang
    @m_nPanType = Cfate::PAN_NORMAL
    @UserInfo = hUserInfo
    @Star = Star.new
    @StarInfo = @Star.g_GetStarPanInfo(@m_nPanType,hUserInfo,hUserAskData)
    @AllPanInfo = @StarInfo[Star::U_PAN_INFO]
    @PanInfo = @AllPanInfo[@m_nPanType]
    @UserAskData = hUserAskData
    @ExplainHouse = @UserAskData[Star::HOUSE_NAME]
    @ApFunc = hApFunc
    @ParAll = hParAll

    # @TzMain = TzMainData.new
    return ex_Star_Create_Simple()
  end

  def ex_Star_Create_Simple()
    if ((@ExplainHouse == nil) || (@ExplainHouse == Star::EX_HOUSE_NONE)) then
      return ""
    end
    sBuf = ""
    sBuf += PanWeb.pw_GetCenter()
    #sBuf += PanWeb.pw_GetPan_table()
    sBuf += "<TABLE BORDER=0 WIDTH=680 ALIGN=CENTER>"
    sBuf += PanWeb.pw_GetPan_table_tr()
    sBuf += PanWeb.pw_GetPan_table_td()
      sBuf += PanWeb.pw_Star_GetSpan(14,2,20)
      if (@ExplainHouse == Star::EX_HOUSE_ALL) then
        sBuf += ex_Star_Create_AllHouse_Simple(@m_nPanType)
      else
        sBuf += ex_Star_Create_OneHouse_Simple(@m_nPanType,@ExplainHouse)
      end
      sBuf += PanWeb.pw_Star_GetSpanEnd()

    sBuf += PanWeb.pw_GetPan_table_td_end()
    sBuf += PanWeb.pw_GetPan_table_tr_end()
    sBuf += PanWeb.pw_GetPan_table_end()

    return sBuf
  end

  # 本命盤：則各宮顯示說明(十二宮分析)
  def ex_Star_Create_AllHouse_Simple(nPanType)
    sBuf = ""

    nEarth = @Star.gHouse_GetFirstHouseEarth(nPanType)
    # 1..12 表示子宮到亥宮
    (1..12).each do |nHouse|
      if (ex_house_check(nHouse)) then
        sHouseName = @Star.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
        sBuf += sHouseName
        sBuf += PanWeb.pw_GetPan_br()

        # 說明部分
        sBuf += ex_Star_Create_HouseInfo_Explain_AllHouse_Simple(@Star,nPanType,nEarth)
      end
      # 尋找下一個宮名
      nEarth = @Star.gHouse_GetNextHouseEarth(nEarth)
    end
    return sBuf
  end

  def ex_Star_Create_HouseInfo_Explain_AllHouse_Simple(oStar,nPanType,nEarth)
    sBuf = ""

    # 星性顯示部份：
    sBuf += ex_Star_Create_HouseInfo_Explain_Star_Simple(oStar,nPanType,nEarth)

    return sBuf
  end

  def ex_Star_Create_OneHouse_Simple(nPanType,nExplainHouse)
    sBuf = ""
    if (ex_house_check(nExplainHouse)) then
      sHouseName = Star.GetHouseName(nExplainHouse.to_s)
      nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
      sBuf += @Star.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
      sBuf += PanWeb.pw_GetPan_br()

      sBuf += ex_Star_Create_Normal_Simple(nPanType,sHouseName)
    end

    return sBuf
  end

  def ex_Star_Create_Normal_Simple(nPanType,sHouseName)
    sBuf = ""
    nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
    sBuf += ex_Star_Create_HouseInfo_Explain_Simple(@Star,nPanType,nEarth)

    return sBuf
  end


  def ex_Star_Create_HouseInfo_Explain_Simple(oStar,nPanType,nEarth)
    sBuf = ""

    # 星性顯示部份：
    sBuf += ex_Star_Create_HouseInfo_Explain_Star_Simple(oStar,nPanType,nEarth)

    return sBuf
  end

  def ex_Star_Create_HouseInfo_Explain_Star_Simple(oStar,nPanType,nEarth)
    sBuf = ""
    # 主要特徵現象
    # sBuf += Pm.GetStr("IDS_S_A_STAR_EXPLAIN")
    # sBuf += PanWeb.pw_GetPan_br()
    # 主星解釋
    sBuf += ex_Star_Create_HouseInfo_Explain_Star_Main(oStar,nPanType,nEarth)
    sBuf += PanWeb.pw_GetPan_br()
    sBuf += PanWeb.pw_GetPan_br()

    return sBuf
  end

  # 宮位論斷說明
  def ex_Star_getHouseExplain(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @lang = sLang
    @m_nPanType = nPanType
    @UserInfo = hUserInfo
    @Star = Star.new
    @StarInfo = @Star.g_GetStarPanInfo(@m_nPanType,hUserInfo,hUserAskData,hParAll)
    @AllPanInfo = @StarInfo[Star::U_PAN_INFO]
    @PanInfo = @AllPanInfo[@m_nPanType]
    @UserAskData = hUserAskData
    @HouseEarth = @UserAskData[Star::HOUSE_EARTH]
    @ExplainHouse = @Star.g_House_GetHouseId(@m_nPanType,@HouseEarth)

    @ApFunc = hApFunc
    @ParAll = hParAll

    # @TzMain = TzMainData.new
    hExplain = Hash.new

    hExplain[Explain::EX_PANTYPE] = Cfate.GetPanFullName(@m_nPanType)
    hExplain[Explain::EX_HOUSE] = @Star.gHouse_GetHouseNameWithQuota(@m_nPanType,@HouseEarth)
    hExplain[Explain::EX_SKY] = Sky.GetName(@Star.cp_getHouseSky(@m_nPanType,@HouseEarth))
    hExplain[Explain::EX_EARTH] = Earth.GetName(@HouseEarth)
    hExplain[Explain::EX_FIVE] = Five.GetFiveStr(@Star.cp_getHouseFive(@m_nPanType,@HouseEarth))
    if (ex_house_check(@ExplainHouse)) then
      # hExplain[Explain::EX_STAR] = ex_Star_OneHouse_Star(@Star,@m_nPanType,@HouseEarth)
      hExplain[Explain::EX_STAR_A] = ex_Star_Create_HouseInfo_Explain_Star_Main(@Star,@m_nPanType,@HouseEarth)
      hExplain[Explain::EX_STAR_B] = ex_Star_Create_HouseInfo_Explain_Star_Sub(@Star,@m_nPanType,@HouseEarth)
      hExplain[Explain::EX_STAR_C] = ex_Star_Create_HouseInfo_Explain_Star_C(@Star,@m_nPanType,@HouseEarth)
      hExplain[Explain::EX_FOURHUA] = ex_Star_Create_HouseInfo_Explain_FourHua(@Star,@m_nPanType,@HouseEarth)
    else
      hExplain[Explain::EX_STAR_A] = ""
      hExplain[Explain::EX_STAR_B] = ""
      hExplain[Explain::EX_STAR_C] = ""
      hExplain[Explain::EX_FOURHUA] = ""
    end

    return hExplain
  end

  def ex_Star_OneHouse_Star(oStar,nPanType,nHouseEarth)
    if ((@ExplainHouse == nil) || (@ExplainHouse == Star::EX_HOUSE_NONE)) then
      return ""
    end
    sBuf = ""
    sBuf += PanWeb.pw_GetCenter()
    #sBuf += PanWeb.pw_GetPan_table()
    sBuf += "<TABLE BORDER=0 WIDTH=100% ALIGN=CENTER>"

    sBuf += PanWeb.pw_GetPan_table_tr()
    sBuf += PanWeb.pw_GetPan_table_td()
    sBuf += ex_Star_OneHouse_Star_A(oStar,nPanType,nHouseEarth)
    sBuf += PanWeb.pw_GetPan_table_td_end()
    sBuf += PanWeb.pw_GetPan_table_tr_end()

    sBuf += PanWeb.pw_GetPan_table_tr()
    sBuf += PanWeb.pw_GetPan_table_td()
    sBuf += ex_Star_OneHouse_Star_B(oStar,nPanType,nHouseEarth)
    sBuf += PanWeb.pw_GetPan_table_td_end()
    sBuf += PanWeb.pw_GetPan_table_tr_end()

    sBuf += PanWeb.pw_GetPan_table_end()

    return sBuf
  end

  def ex_Star_OneHouse_Star_A(oStar,nPanType,nEarth)
    sBuf = ""
    # 主要特徵現象
    sBuf += Pm.GetStr("IDS_S_A_STAR_EXPLAIN")
    sBuf += PanWeb.pw_GetPan_br()
    # 主星解釋
    sBuf += ex_Star_Create_HouseInfo_Explain_Star_Main(oStar,nPanType,nEarth)
    sBuf += PanWeb.pw_GetPan_br()
    sBuf += PanWeb.pw_GetPan_br()

    return sBuf
  end
  def ex_Star_OneHouse_Star_B(oStar,nPanType,nEarth)
    sBuf = ""
    # 主要特徵現象
    sBuf += Pm.GetStr("IDS_S_B_STAR_EXPLAIN")
    sBuf += PanWeb.pw_GetPan_br()
    # 主星解釋
    sBuf += ex_Star_Create_HouseInfo_Explain_Star_Sub(oStar,nPanType,nEarth)
    sBuf += PanWeb.pw_GetPan_br()
    sBuf += PanWeb.pw_GetPan_br()

    return sBuf
  end

end
