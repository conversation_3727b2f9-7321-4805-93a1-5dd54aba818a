# 求財程式
class Divination
  def explain_qiucai()
    h_biao_tou = Hash.new
    h_jie_guo = Hash.new
    hOut = Hash.new

    h = explain_qiucai_jie_guo()

    # 表頭
    h_biao_tou["xiang_mu_1"] = explain_qiucai_biao_tou(1)
    h_biao_tou["xiang_mu_2"] = explain_qiucai_biao_tou(2)
    h_biao_tou["xiang_mu_3"] = explain_qiucai_biao_tou(3)
    h_biao_tou["xiang_mu_4"] = explain_qiucai_biao_tou(4)
    h_biao_tou["xiang_mu_5"] = explain_qiucai_biao_tou(5)
    h_biao_tou["xiang_mu_6"] = explain_qiucai_biao_tou(6)
    h_biao_tou["xiang_mu_7"] = explain_qiucai_biao_tou(7)
    hOut["biao_tou"] = h_biao_tou
    # 結果
    h_jie_guo["xiang_mu_1"] = explain_qiucai_jie_guo_xiang_mu_1(h)
    h_jie_guo["xiang_mu_2"] = explain_qiucai_jie_guo_xiang_mu_2(h)
    h_jie_guo["xiang_mu_3"] = explain_qiucai_jie_guo_xiang_mu_3(h)
    h_jie_guo["xiang_mu_4"] = explain_qiucai_jie_guo_xiang_mu_4(h)
    h_jie_guo["xiang_mu_5"] = explain_qiucai_jie_guo_xiang_mu_5(h)
    h_jie_guo["xiang_mu_6"] = explain_qiucai_jie_guo_xiang_mu_6(h)
    h_jie_guo["xiang_mu_7"] = explain_qiucai_jie_guo_xiang_mu_7(h)
    hOut["jie_guo"] = h_jie_guo

    hOut["original"] = h

    return hOut
  end
  def explain_qiucai_biao_tou(xiang_mu)
    return Divination.liuyaogua_str("qiucai.title.xiang_mu_#{xiang_mu}")
  end
  # 項目一：本年度內之求財狀況
  def explain_qiucai_jie_guo_xiang_mu_1(h)
    # 1 年之關係
    a = h["nian"].values

    # 7 三合刑(以年支判斷)
    a.push(h["san_he_xing"]["san_he"]["nian"])
    a.push(h["san_he_xing"]["san_xing"]["nian"])
    a.push(h["san_he_xing"]["hu_xing"]["nian"])

    return Divination.explain_merge_content(a)
  end
   # 項目二：本月內之求財狀況
  def explain_qiucai_jie_guo_xiang_mu_2(h)
    # 2 月之關係-1
    # 3 月之關係-2
    a = h["yue"].values

    # 7 三合刑(以月支判斷)
    a.push(h["san_he_xing"]["san_he"]["yue"])
    a.push(h["san_he_xing"]["san_xing"]["yue"])
    a.push(h["san_he_xing"]["hu_xing"]["yue"])

    return Divination.explain_merge_content(a)
  end
   # 項目三：你的人格特性，能力與目前之運勢
  def explain_qiucai_jie_guo_xiang_mu_3(h)
    # 4 日之關係-1
    # 5 日之關係-2日合
    # 6 日之關係-3日破
    a = h["ri"].values

    # 8 世爻之人格特性new
    a += h["shi_yao"].values

    return Divination.explain_merge_content(a)
  end
   # 項目四：  目前之問題及狀況
  def explain_qiucai_jie_guo_xiang_mu_4(h)
    # 9 動爻與變爻之關係
    a = h["dong_yao_bian_yao"].values

    return Divination.explain_merge_content(a)
  end
   # 項目五：你目前之各種心理感受之問題與狀況
  def explain_qiucai_jie_guo_xiang_mu_5(h)
    # 10 貴人驛馬
    a = h["gui_ren_yi_ma"].values
    # 12 參數對照與說明
    a += h["kong_wang_yang_ren"].values

    return Divination.explain_merge_content(a)
  end
   # 項目六：  求財之環境與市場狀況
  def explain_qiucai_jie_guo_xiang_mu_6(h)
    # 13 應爻
    a = h["yin_yao"].values

    return Divination.explain_merge_content(a)
  end
   # 項目七：  特殊問題之說明
  def explain_qiucai_jie_guo_xiang_mu_7(h)
    # 11 本卦變卦之卦象說明
    a = h["gua_xiang"].values

    return Divination.explain_merge_content(a)
  end

  def explain_qiucai_jie_guo()
    h = Hash.new

    h["nian"] = ex_qiucai_year()
    h["yue"] = ex_qiucai_month()
    h["ri"] = ex_qiucai_day()
    h["san_he_xing"] = ex_qiucai_san_he_xing()
    h["shi_yao"] = ex_qiucai_shi_yao()
    h["gui_ren_yi_ma"] = ex_qiucai_gui_ren_yi_ma()
    h["dong_yao_bian_yao"] = ex_qiucai_dong_yao_yu_bian_yao()
    h["gua_xiang"] = ex_qiucai_gua_image()
    h["kong_wang_yang_ren"] = ex_qiucai_kong_wang_yang_ren()
    h["yin_yao"] = ex_qiucai_yin_yao()

    return h
  end

  def Divination.ex_qiucai_str(key)
    return Divination.liuyaogua_str("qiucai.#{key}")
  end

  # 1 年之關係
  def ex_qiucai_year()
    h = Hash.new
    year_earth = Xdate.GetGanZhiEarth(@nGanZhiYear)
    shi_yao_di_zhi = [@shi_earth]
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(@all_ylqgzws)

    # 前置詞
    h["qian_zhi_ci"] = Divination.ex_qc_year_pre_str()

    # 六合  年 世爻
    h["liu_he"] = Divination.ex_qc_y_liu_he(year_earth,ben_gua_di_zhi)
    # 六沖  年 世爻(全部一樣)
    h["liu_chong"] = Divination.ex_qc_y_liu_chong(year_earth,ben_gua_di_zhi)
    # 互刑  年 爻支(全部一樣)
    h["hu_xing"] = Divination.ex_qc_y_hu_xing(year_earth,ben_gua_di_zhi)
    # 三刑  年   爻支1 爻支2(全部一樣)
    h["san_xing"] = Divination.ex_qc_y_san_xing(year_earth,ben_gua_di_zhi)
    # 入年墓 年 爻支1 爻支2 爻支3 爻支4 爻支5 爻支6
    h["ru_nian_mu"] = Divination.ex_qc_y_ru_nian_mu(year_earth,ben_gua_di_zhi)

    return h
  end
  def Divination.ex_qc_year_str(key)
    return Divination.ex_qiucai_str("year.#{key}")
  end
  def Divination.ex_qc_year_pre_str()
    return Divination.ex_qc_year_str("pre")
  end
    # 六合  年 世爻
  def Divination.ex_qc_year_liu_he_str(dz1,dz2)
  	key = Divination.dizhi_keys(dz1,dz2)
    return Divination.ex_qc_year_str("liu_he.#{key}")
  end
  def Divination.ex_qc_y_liu_he(year_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_liu_he?(year_earth,a[i])) then
        return Divination.ex_qc_year_liu_he_str(year_earth,a[i])
      end
    end
    return ""
  end
    # 六沖  年 世爻(全部一樣)
  def Divination.ex_qc_year_liu_chong_str()
    return Divination.ex_qc_year_str("liu_chong.desc")
  end
  def Divination.ex_qc_y_liu_chong(year_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_liu_chong?(year_earth,a[i])) then
        return Divination.ex_qc_year_liu_chong_str()
      end
    end
    return ""
  end
    # 互刑  年 爻支(全部一樣)
  def Divination.ex_qc_year_hu_xing_str()
    return Divination.ex_qc_year_str("hu_xing.desc")
  end
  def Divination.ex_qc_y_hu_xing(year_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_hu_xing?(year_earth,a[i])) then
        return Divination.ex_qc_year_hu_xing_str()
      end
    end
    return ""
  end
    # 三刑  年   爻支1 爻支2(全部一樣)
  def Divination.ex_qc_year_san_xing_str()
    return Divination.ex_qc_year_str("san_xing.desc")
  end
  def Divination.ex_qc_y_san_xing(year_earth,ben_gua_di_zhi)
    a1 = Divination.San_Xing([year_earth],ben_gua_di_zhi)
    a1.each_index do |i|
      a = a1[i]
      if (a.index(year_earth) != nil) then
        return Divination.ex_qc_year_san_xing_str()
      end
    end
    return ""
  end
    # 入年墓 年 爻支1 爻支2 爻支3 爻支4 爻支5 爻支6
  def Divination.ex_qc_year_ru_nian_mu_str()
    return Divination.ex_qc_year_str("ru_nian_mu.desc")
  end
  def Divination.ex_qc_y_ru_nian_mu(year_earth,ben_gua_di_zhi)
    a = Divination.ru_mu_array(year_earth,ben_gua_di_zhi)
    if (a.length > 0) then
      return Divination.ex_qc_year_ru_nian_mu_str()
    end
    return ""
  end

  # 2 月之關係-1
  def ex_qiucai_month()
    h = Hash.new
    month_earth = Xdate.GetGanZhiEarth(@nGanZhiMonth)
    shi_yao_di_zhi = [@shi_earth]
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(@all_ylqgzws)

    # 前置詞
    # h["qian_zhi_ci"] = Divination.ex_qc_month_pre_str()

    # 2 月之關係-1
    h["liu_he"] = Divination.ex_qc_m_liu_he(month_earth,ben_gua_di_zhi)
    h["liu_chong"] = Divination.ex_qc_m_liu_chong(month_earth,ben_gua_di_zhi)
    h["hu_xing"] = Divination.ex_qc_m_hu_xing(month_earth,ben_gua_di_zhi)
    h["san_xing"] = Divination.ex_qc_m_san_xing(month_earth,ben_gua_di_zhi)
    h["ru_yue_mu"] = Divination.ex_qc_m_ru_yue_mu(month_earth,ben_gua_di_zhi)

    # 3 月之關係-2
    # 旺 相 休 囚 死
    wang_xiang_xiu_qiu_si = @par_five_levels.index(@shi_five) + 1
    h["wang_xiang_xiu_qiu_si"] = Divination.ex_qc_m_wang_xiang_xiu_qiu_si(month_earth,@shi_earth,wang_xiang_xiu_qiu_si)

    return h
  end
  def Divination.ex_qc_month_str(key)
    return Divination.ex_qiucai_str("month.#{key}")
  end
  def Divination.ex_qc_month_pre_str()
    return Divination.ex_qc_month_str("pre")
  end
    # 月合（六合） 月 世爻
  def Divination.ex_qc_month_liu_he_str(dz1,dz2)
  	key = Divination.dizhi_keys(dz1,dz2)
    return Divination.ex_qc_month_str("liu_he.#{key}")
  end
  def Divination.ex_qc_m_liu_he(month_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_liu_he?(month_earth,a[i])) then
        return Divination.ex_qc_month_liu_he_str(month_earth,a[i])
      end
    end
    return ""
  end
    # 月破(六沖) 月 世爻
  def Divination.ex_qc_month_liu_chong_str(dz1,dz2)
  	key = Divination.dizhi_keys(dz1,dz2)
    return Divination.ex_qc_month_str("liu_chong.#{key}")
  end
  def Divination.ex_qc_m_liu_chong(month_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_liu_chong?(month_earth,a[i])) then
        return Divination.ex_qc_month_liu_chong_str(month_earth,a[i])
      end
    end
    return ""
  end
    # 月互刑 月 世爻
  def Divination.ex_qc_month_hu_xing_str()
    return Divination.ex_qc_month_str("hu_xing.desc")
  end
  def Divination.ex_qc_m_hu_xing(month_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_hu_xing?(month_earth,a[i])) then
        return Divination.ex_qc_month_hu_xing_str()
      end
    end
    return ""
  end
    # 月三刑 月 世爻 爻支2
  def Divination.ex_qc_month_san_xing_str()
    return Divination.ex_qc_month_str("san_xing.desc")
  end
  def Divination.ex_qc_m_san_xing(month_earth,ben_gua_di_zhi)
    a1 = Divination.San_Xing([month_earth],ben_gua_di_zhi)
    a1.each_index do |i|
      a = a1[i]
      if (a.index(month_earth) != nil) then
        return Divination.ex_qc_month_san_xing_str()
      end
    end
    return ""
  end
    # 入月墓   月   爻支1 爻支2 爻支3 爻支4 爻支5 爻支6
  def Divination.ex_qc_year_ru_yue_mu_str()
    return Divination.ex_qc_month_str("ru_yue_mu.desc")
  end
  def Divination.ex_qc_m_ru_yue_mu(month_earth,ben_gua_di_zhi)
    a = Divination.ru_mu_array(month_earth,ben_gua_di_zhi)
    if (a.length > 0) then
      return Divination.ex_qc_year_ru_yue_mu_str()
    end
    return ""
  end

  # 3 月之關係-2
    # 月令 爻支 旺 相 休 囚 死
      # 月令 子
  def Divination.ex_qc_month_wang_xiang_xiu_qiu_si_str(month_dz1,yao_dz2)
    # 月令 子
  	key = Divination.dizhi_keys(month_dz1)
    s = Divination.ex_qc_month_str("wang_xiang_xiu_qiu_si.#{key}")

    # 爻支 子 丑 。。⋯⋯ 亥共12個
    return Divination.extract_str(s,yao_dz2)
  end
  def Divination.ex_qc_m_wang_xiang_xiu_qiu_si(month_earth,shi_earth,wang_xiang_xiu_qiu_si)
    if (Divination.ex_jk_m_wang_xiang_xiu_qiu_si_has_words?(month_earth,shi_earth,wang_xiang_xiu_qiu_si)) then
      return Divination.ex_qc_month_wang_xiang_xiu_qiu_si_str(month_earth,shi_earth)
    end
    return ""
  end

  # 4 日之關係-1
  def ex_qiucai_day()
    h = Hash.new
    day_earth = Xdate.GetGanZhiEarth(@nGanZhiDay)
    month_earth = Xdate.GetGanZhiEarth(@nGanZhiMonth)
    chang_sheng = Divination.chang_sheng(@shi_five,day_earth)
    liu_shou = Divination.liu_shou(@liu_shous,@shi_yao)
    ben_gua_liu_qins = Divination.par_ben_gua_liu_qins(@all_ylqgzws)
    liu_qin = Divination.gua_yao_liu_qin(ben_gua_liu_qins,@shi_yao)

    # 前置詞
    # h["qian_zhi_ci"] = Divination.ex_qc_day_pre_str()

    # 4 日之關係-1
    h["chang_sheng"] = Divination.ex_qc_day_shi_chang_sheng(day_earth,@shi_earth,chang_sheng)

    # 5 日之關係-2日合
    h["ri_he"] = Divination.ex_qc_day_2_he(day_earth,month_earth,@shi_earth,@par_five_levels,@par_chang_sheng,liu_shou,liu_qin)

    # 6 日之關係-3日破
    h["an_dong"] = Divination.ex_qc_day_an_dong(day_earth,@shi_earth,liu_shou,liu_qin)

    return h
  end
  def Divination.ex_qc_day_str(key)
    return Divination.ex_qiucai_str("day.#{key}")
  end
  def Divination.ex_qc_day_pre_str()
    return Divination.ex_qc_day_str("pre")
  end
      # 日之 12 長生 世爻
  def Divination.ex_qc_day_shi_str(day_dz1,shi_dz2)
    # 月令 子
  	key = Divination.dizhi_keys(day_dz1)
    s = Divination.ex_qc_day_str("shi.#{key}")

    # 世爻爻支 子 丑 。。⋯⋯ 亥共12個
    return Divination.extract_str(s,shi_dz2)
  end
  # @@Qiucai_Day_DiZhi_ChangSheng = [
  #   [帝旺,帝旺,沐浴,沐浴,帝旺,胎,胎,帝旺,死,死,帝旺,帝旺],   # 子
  #   [衰,衰,冠帶,冠帶,衰,養,養,衰,墓,墓,衰,衰],   # 丑
  #   [病,病,臨官,臨官,病,長生,長生,病,絕,胎,病,病],   # 寅
  #   [死,死,帝旺,帝旺,死,沐浴,沐浴,死,絕,胎,死,死],   # 卯
  #   [墓,墓,衰,衰,墓,冠帶,冠帶,墓,養,養,墓,墓],   # 辰
  #   [絕,絕,病,病,絕,臨官,臨官,絕,長生,長生,絕,絕],   # 巳
  #   [胎,胎,死,死,胎,帝旺,帝旺,胎,沐浴,沐浴,胎,胎],   # 午
  #   [養,養,墓,墓,養,衰,衰,養,冠帶,冠帶,養,養],   # 未
  #   [長生,長生,絕,絕,長生,病,病,長生,臨官,臨官,長生,長生],   # 申
  #   [沐浴,沐浴,胎,胎,沐浴,死,死,沐浴,帝旺,帝旺,沐浴,沐浴],   # 酉
  #   [冠帶,冠帶,養,養,冠帶,墓,墓,冠帶,衰,衰,冠帶,冠帶],   # 戌
  #   [臨官,臨官,長生,長生,臨官,絕,絕,臨官,病,病,臨官,臨官]   # 亥
  #                                 ]
  @@Qiucai_Day_DiZhi_ChangSheng = [
  	[5,5,2,2,5,11,11,5,8,8,5,5],
    [6,6,3,3,6,12,12,6,9,9,6,6],
    [7,7,4,4,7,1,1,7,10,11,7,7],
    [8,8,5,5,8,2,2,8,10,11,8,8],
    [9,9,6,6,9,3,3,9,12,12,9,9],
    [10,10,7,7,10,4,4,10,1,1,10,10],
    [11,11,8,8,11,5,5,11,2,2,11,11],
    [12,12,9,9,12,6,6,12,3,3,12,12],
    [1,1,10,10,1,7,7,1,4,4,1,1],
    [2,2,11,11,2,8,8,2,5,5,2,2],
    [3,3,12,12,3,9,9,3,6,6,3,3],
    [4,4,1,1,4,10,10,4,7,7,4,4]
                                  ]
  def Divination.ex_qc_day_shi_chang_sheng(day_dz1,shi_dz2,chang_sheng)
  	a = @@Qiucai_Day_DiZhi_ChangSheng[day_dz1 - 1]
  	if (a[shi_dz2 - 1] == chang_sheng) then
  	  return Divination.ex_qc_day_shi_str(day_dz1,shi_dz2)
    else
      return ""
    end
  end
  # 5 日之關係-2日合
  def Divination.ex_qc_day_2_he(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    # 日合生
    s = Divination.ex_qc_day_he_san(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    if (s != "") then
      return [Divination.ex_qc_day_he_san_title(),s]
    end
    # 日合
    s = Divination.ex_qc_day_he(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    if (s != "") then
      return [Divination.ex_qc_day_he_title(),s]
    end
    # 日合剋
    s = Divination.ex_qc_day_he_ke(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    if (s != "") then
      return [Divination.ex_qc_day_he_ke_title(),s]
    end
    return ["",""]
  end
    # 日合生
    # ※除外：當世爻逢空亡(1。 於月令為死 2。 日辰為絕)時下列六合將不成立
    # 日之關係 日辰 爻支 爻支-六獸 爻支-六親
    # 父母 兄弟 子孫 妻財 官鬼
  def Divination.ex_qc_day_he_san_title()
    return Divination.ex_qc_day_str("he_san.title")
  end
  def Divination.ex_qc_day_he_san_str(day_dz1,yao_dz2,liu_shou,liu_qin)
    # 日辰 爻支
  	key = Divination.dizhi_keys(day_dz1,yao_dz2)
  	key2 = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_qc_day_str("he_san.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_qc_day_he_san(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    tf = Divination.ex_jk_ky_kong_wang_true_false(day_earth,month_earth,par_five_levels,par_chang_sheng)
    if (!tf) then
      # 不為空亡時，才看六合生
      if (Divination.par_is_he_san?(day_earth,shi_earth)) then
      	return Divination.ex_qc_day_he_san_str(day_earth,shi_earth,liu_shou,liu_qin)
      end
    end
    return ""
  end

    # 日合
  def Divination.ex_qc_day_he_title()
    return Divination.ex_qc_day_str("he.title")
  end
  def Divination.ex_qc_day_he_str(day_dz1,yao_dz2,liu_shou,liu_qin)
    # 日辰 爻支
  	key = Divination.dizhi_keys(day_dz1,yao_dz2)
  	key2 = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_qc_day_str("he.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_qc_day_he(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    tf = Divination.ex_jk_ky_kong_wang_true_false(day_earth,month_earth,par_five_levels,par_chang_sheng)
    if (!tf) then
      # 不為空亡時，才看六合
      if (Divination.par_is_he?(day_earth,shi_earth)) then
      	return Divination.ex_qc_day_he_str(day_earth,shi_earth,liu_shou,liu_qin)
      end
    end
    return ""
  end
    # 日合剋
  def Divination.ex_qc_day_he_ke_title()
    return Divination.ex_qc_day_str("he_ke.title")
  end
  def Divination.ex_qc_day_he_ke_str(day_dz1,yao_dz2,liu_shou,liu_qin)
    # 日辰 爻支
  	key = Divination.dizhi_keys(day_dz1,yao_dz2)
  	key2 = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_qc_day_str("he_ke.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_qc_day_he_ke(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    tf = Divination.ex_jk_ky_kong_wang_true_false(day_earth,month_earth,par_five_levels,par_chang_sheng)
    if (!tf) then
      # 不為空亡時，才看六合
      if (Divination.par_is_he_ke?(day_earth,shi_earth)) then
      	return Divination.ex_qc_day_he_ke_str(day_earth,shi_earth,liu_shou,liu_qin)
      end
    end
    return ""
  end

  # 6 日之關係-3日破
    # 暗動 日破
  def Divination.ex_qc_day_an_dong_str(day_dz1,yao_dz2,liu_shou,liu_qin)
    # 日辰 爻支
  	key = Divination.dizhi_keys(day_dz1,yao_dz2)
  	key2 = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_qc_day_str("an_dong.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_qc_day_an_dong(day_earth,shi_earth,liu_shou,liu_qin)
    if (Divination.par_is_liu_chong?(day_earth,shi_earth)) then
      return Divination.ex_qc_day_an_dong_str(day_earth,shi_earth,liu_shou,liu_qin)
    else
      return ""
    end
  end
  # 7 三合刑
  def ex_qiucai_san_he_xing()
    h = Hash.new

    year_dizhi = Xdate.GetGanZhiEarth(@nGanZhiYear)
    month_dizhi = Xdate.GetGanZhiEarth(@nGanZhiMonth)
    day_dizhi = Xdate.GetGanZhiEarth(@nGanZhiDay)
    tf = Divination.ex_jk_ky_kong_wang_true_false(day_dizhi,month_dizhi,@par_five_levels,@par_chang_sheng)

    # 前置詞
    h["qian_zhi_ci"] = Divination.ex_qc_san_he_xing_pre_str()

    # 三合
    h["san_he"] = Divination.ex_qc_san_he_xing_san_he(tf,year_dizhi,month_dizhi,day_dizhi,@all_ylqgzws,@liu_shous)

    # 三刑
    h["san_xing"] = Divination.ex_qc_san_he_xing_san_xing(tf,year_dizhi,month_dizhi,day_dizhi,@all_ylqgzws)

    # 互刑
    h["hu_xing"] = Divination.ex_qc_san_he_xing_hu_xing(tf,year_dizhi,month_dizhi,day_dizhi,@all_ylqgzws)

    return h
  end
  def Divination.ex_qc_san_he_xing_str(key)
    return Divination.ex_qiucai_str("san_he_xing.#{key}")
  end
  def Divination.ex_qc_san_he_xing_pre_str()
    return Divination.ex_qc_san_he_xing_str("pre")
  end
    # 三合
  @@Qiucai_San_He = [
                [9,1,5],
                [6,10,2],
                [3,7,11],
                [12,4,8]
              ]
  def Divination.ex_qc_san_he(year_dizhi,month_dizhi,day_dizhi,all_ylqgzws,liu_shous)
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(all_ylqgzws)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)

  	date_dizhis = [year_dizhi,month_dizhi,day_dizhi]
  	all_dizhi = date_dizhis + ben_gua_di_zhi
    a2 = all_dizhi.uniq
    @@Qiucai_San_He.each_index do |i|
      a = @@Qiucai_San_He[i]
      a3 = a.select { |n| a2.index(n) != nil  }
      if (a3 == a) then
        san_he_middle = a[1]
        ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,san_he_middle)
        if (ylqgzw == nil) then
          wu_xing = Divination.Earth_Five(san_he_middle)
          ylqgzw = Divination.par_find_ylqgzw_by_wu_xing(ylqgzws,wu_xing)
        end
        if (ylqgzw != nil) then
          yao = Divination.fu_shen_yao(ylqgzw)
          liu_shou = Divination.liu_shou(liu_shous,yao)
          liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
          return a,liu_shou,liu_qin
        end
      end
    end
    return nil,0,0
  end
    # 日之關係  年干/月干/日辰/爻支     爻支-六獸 爻支-六親 說明
  def Divination.ex_qc_san_he_xing_san_he_str(dz1,dz2,dz3,liu_shou,liu_qin)
    # 年干/月干/日辰/爻支
  	key = Divination.dizhi_keys(dz1,dz2,dz3)
  	# 六獸
  	key2 = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_qc_san_he_xing_str("san_he.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_qc_san_he_xing_san_he(tf,year_dizhi,month_dizhi,day_dizhi,all_ylqgzws,liu_shous)
    h = {"nian" => "", "yue" => "", "ri" => ""}
  	if (tf) then
      return h
  	end
  	san_he,liu_shou,liu_qin = Divination.explain_san_he([year_dizhi],all_ylqgzws,liu_shous)
    if (san_he != nil) then
      dz1,dz2,dz3 = san_he[0],san_he[1],san_he[2]
      h["nian"] = Divination.ex_qc_san_he_xing_san_he_str(dz1,dz2,dz3,liu_shou,liu_qin)
    end
    san_he,liu_shou,liu_qin = Divination.explain_san_he([month_dizhi],all_ylqgzws,liu_shous)
    if (san_he != nil) then
      dz1,dz2,dz3 = san_he[0],san_he[1],san_he[2]
      h["yue"] = Divination.ex_qc_san_he_xing_san_he_str(dz1,dz2,dz3,liu_shou,liu_qin)
    end
    san_he,liu_shou,liu_qin = Divination.explain_san_he([day_dizhi],all_ylqgzws,liu_shous)
    if (san_he != nil) then
      dz1,dz2,dz3 = san_he[0],san_he[1],san_he[2]
      h["ri"] = Divination.ex_qc_san_he_xing_san_he_str(dz1,dz2,dz3,liu_shou,liu_qin)
    end
    return h
  end
    # 三刑
    # 年干/月干/日辰/爻支  年干六親
  def Divination.ex_qc_san_he_xing_san_xing_str(dz1,dz2,dz3,liu_qin)
    # 年干/月干/日辰/爻支
    key = Divination.dizhi_keys(dz1,dz2,dz3)
    s = Divination.ex_qc_san_he_xing_str("san_xing.#{key}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
    # 還沒做好,各種三刑狀況的處理未做
  @@Qiucai_San_Xing = [
                [3,6,9],
                [2,11,8]
              ]
  # 沒有三刑
  QC_San_Xing_None = 0
  # 三刑在年（不在月），寅年年度三刑,年度過後三刑自動消失        巳年年度三刑,年度過後三刑自動消失       申年年度三刑,年度過後三刑自動消失
  QC_San_Xing_Type_Year = 1
  # 三刑在月，巳月份三刑,過巳月後三刑自動消失        申月份三刑,過申月後三刑自動消失        寅月份三刑,過寅月後三刑自動消失
  QC_San_Xing_Type_Month = 2
  # 三刑只在日，永久性三刑
  QC_San_Xing_Type_Day = 3
  # 三刑皆在爻，永久性三刑
  QC_San_Xing_Type_Yao = 4
  def Divination.ex_qc_san_he_xing_san_xing_check(year_dizhi,month_dizhi,day_dizhi,ben_gua_di_zhi)
    date_dizhis = [year_dizhi,month_dizhi,day_dizhi]
    aOut = Array.new
    dizhis = date_dizhis + ben_gua_di_zhi
    a2 = dizhis.uniq
    @@Qiucai_San_Xing.each_index do |i|
      a = @@Qiucai_San_Xing[i]
      a3 = a.select { |n| a2.index(n) != nil  }
      if (a3 == a) then
        # 靜爻三刑不計算,其他動爻或是動與靜爻其中有即是。
        if (Divination.has_same_item([month_dizhi],a)) then
          aOut.push([a,QC_San_Xing_Type_Month])
        elsif (Divination.has_same_item([year_dizhi],a)) then
          aOut.push([a,QC_San_Xing_Type_Year])
        elsif (Divination.has_same_item([day_dizhi],a)) then
          aOut.push([a,QC_San_Xing_Type_Day])
        else
          aOut.push([a,QC_San_Xing_Type_Yao])
        end
      end
    end
    return aOut
  end
  # 參數-三刑自刑六合六沖程式說明 未處理
  def Divination.ex_qc_san_he_xing_san_xing_alldate(tf,year_dizhi,month_dizhi,day_dizhi,all_ylqgzws)
    if (tf) then
      return ""
    end
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(all_ylqgzws)
    a_san_xing = Divination.ex_qc_san_he_xing_san_xing_check(year_dizhi,month_dizhi,day_dizhi,ben_gua_di_zhi)
    if (a_san_xing.length == 0) then
      return [""]
    end

    aOut = Array.new
    a_san_xing.each_index do |i|
      a = a_san_xing[i][0]
      # 參數-三刑自刑六合六沖程式說明 未做
      type = a_san_xing[i][1]
      # 以第一個地支之六親為主
      ylqgzw = Divination.par_find_ylqgzw_in_all_gua_ylqgzws_by_di_zhi(a[0],all_ylqgzws)
      liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      dz1,dz2,dz3 = a[0],a[1],a[2]
      aOut.push(Divination.ex_qc_san_he_xing_san_xing_str(dz1,dz2,dz3,liu_qin))
    end

    return aOut.uniq
  end
  def Divination.ex_qc_san_he_xing_san_xing(tf,year_dizhi,month_dizhi,day_dizhi,all_ylqgzws)
    h = {"nian" => "", "yue" => "", "ri" => ""}
    if (tf) then
      return h
    end
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(all_ylqgzws)

    san_xing = Divination.explain_san_xing_check([year_dizhi],all_ylqgzws)
    if (san_xing != nil) then
      ylqgzw = Divination.par_find_ylqgzw_in_all_gua_ylqgzws_by_di_zhi(san_xing[0],all_ylqgzws)
      liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      dz1,dz2,dz3 = san_xing[0],san_xing[1],san_xing[2]
      h["nian"] = Divination.ex_qc_san_he_xing_san_xing_str(dz1,dz2,dz3,liu_qin)
    end

    san_xing = Divination.explain_san_xing_check([month_dizhi],all_ylqgzws)
    if (san_xing != nil) then
      ylqgzw = Divination.par_find_ylqgzw_in_all_gua_ylqgzws_by_di_zhi(san_xing[0],all_ylqgzws)
      liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      dz1,dz2,dz3 = san_xing[0],san_xing[1],san_xing[2]
      h["yue"] = Divination.ex_qc_san_he_xing_san_xing_str(dz1,dz2,dz3,liu_qin)
    end
    san_xing = Divination.explain_san_xing_check([day_dizhi],all_ylqgzws)
    if (san_xing != nil) then
      ylqgzw = Divination.par_find_ylqgzw_in_all_gua_ylqgzws_by_di_zhi(san_xing[0],all_ylqgzws)
      liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      dz1,dz2,dz3 = san_xing[0],san_xing[1],san_xing[2]
      h["ri"] = Divination.ex_qc_san_he_xing_san_xing_str(dz1,dz2,dz3,liu_qin)
    end
    return h
  end

    # 互刑
  def Divination.ex_qc_san_he_xing_hu_xing_str(dz1,dz2,liu_qin)
    # 年干/月干/日辰/爻支
  	key = Divination.dizhi_keys(dz1,dz2)
    s = Divination.ex_qc_san_he_xing_str("hu_xing.#{key}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  @@Qiucai_Hu_Xing = [
                [1,4],
                [4,1],
                [5,5],
                [7,7],
                [10,10],
                [12,12]
              ]
  # 沒有互刑
  QC_Hu_Xing_None = 0
  # 互刑在年（不在月），子年年度互刑,年度過後互刑自動消失       卯年年度互刑,年度過後互刑自動消失
  QC_Hu_Xing_Type_Year = 1
  # 互刑在月，子月互刑,子月過後互刑自動消失        卯月互刑,卯月過後互刑自動消失
  QC_Hu_Xing_Type_Month = 2
  # 互刑只在日，永久性互刑
  QC_Hu_Xing_Type_Day = 3
  # 互刑皆在爻，永久性互刑
  QC_Hu_Xing_Type_Yao = 4
  # 辰，午，酉，亥互刑
  QC_Hu_Xing_Type_5_7_10_12 = 5
  def Divination.ex_qc_san_he_xing_hu_xing_check_alldate(year_dizhi,month_dizhi,day_dizhi,ben_gua_di_zhi)
    date_dizhis = [year_dizhi,month_dizhi,day_dizhi]
    aOut = Array.new
    @@Qiucai_Hu_Xing.each_index do |i|
      a = @@Qiucai_Hu_Xing[i]
      if ((date_dizhis.index(a[0]) != nil) && (ben_gua_di_zhi.index(a[1]) != nil)) then
        # 子卯互刑
        if (a == [1,4] || a == [4,1]) then
          if (month_dizhi == a[0]) then
            aOut.push([a,QC_Hu_Xing_Type_Month])
          elsif (year_dizhi == a[0]) then
            aOut.push([a,QC_Hu_Xing_Type_Year])
          elsif (day_dizhi == a[0]) then
            aOut.push([a,QC_Hu_Xing_Type_Day])
          else
            aOut.push([a,QC_Hu_Xing_Type_Yao])
          end
        else
          aOut.push([a,QC_Hu_Xing_Type_5_7_10_12])
        end
      end
    end
    return aOut
  end
  def Divination.ex_qc_san_he_xing_hu_xing_alldate(tf,year_dizhi,month_dizhi,day_dizhi,all_ylqgzws)
    if (tf) then
      return ""
    end
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(all_ylqgzws)
    a_hu_xing = Divination.ex_qc_san_he_xing_hu_xing_check_alldate(year_dizhi,month_dizhi,day_dizhi,ben_gua_di_zhi)
    if (a_hu_xing.length == 0) then
      return [""]
    end
    aOut = Array.new

    a_hu_xing.each_index do |i|
      a = a_hu_xing[i][0]
      # 子卯 參數-三刑自刑六合六沖程式說明 未做
      type = a_hu_xing[i][1]
      ylqgzw = Divination.par_find_ylqgzw_in_all_gua_ylqgzws_by_di_zhi(a[0],all_ylqgzws)
      liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      dz1,dz2 = a[0],a[1]
      aOut.push(Divination.ex_qc_san_he_xing_hu_xing_str(dz1,dz2,liu_qin))
    end
    return aOut.uniq
  end
  def Divination.ex_qc_san_he_xing_hu_xing_check(date_dizhi,all_ylqgzws)
    par_all_yao_di_zhis = Divination.par_all_yao_di_zhis(all_ylqgzws)
    date_dizhis = [date_dizhi]
    aOut = Array.new
    @@Qiucai_Hu_Xing.each_index do |i|
      a = @@Qiucai_Hu_Xing[i]
      if ((date_dizhis.index(a[0]) != nil) && (par_all_yao_di_zhis.index(a[1]) != nil)) then
        return a
      end
    end
    return nil
  end
  def Divination.ex_qc_san_he_xing_hu_xing(tf,year_dizhi,month_dizhi,day_dizhi,all_ylqgzws)
    h = {"nian" => "", "yue" => "", "ri" => ""}
    if (tf) then
      return h
    end

    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(all_ylqgzws)

    a = Divination.ex_qc_san_he_xing_hu_xing_check(year_dizhi,all_ylqgzws)
    if (a != nil) then
      ylqgzw = Divination.par_find_ylqgzw_in_all_gua_ylqgzws_by_di_zhi(a[0],all_ylqgzws)
      liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      dz1,dz2 = a[0],a[1]
      h["nian"] = Divination.ex_qc_san_he_xing_hu_xing_str(dz1,dz2,liu_qin)
    end

    a = Divination.ex_qc_san_he_xing_hu_xing_check(month_dizhi,all_ylqgzws)
    if (a != nil) then
      ylqgzw = Divination.par_find_ylqgzw_in_all_gua_ylqgzws_by_di_zhi(a[0],all_ylqgzws)
      liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      dz1,dz2 = a[0],a[1]
      h["yue"] = Divination.ex_qc_san_he_xing_hu_xing_str(dz1,dz2,liu_qin)
    end

    a = Divination.ex_qc_san_he_xing_hu_xing_check(day_dizhi,all_ylqgzws)
    if (a != nil) then
      ylqgzw = Divination.par_find_ylqgzw_in_all_gua_ylqgzws_by_di_zhi(a[0],all_ylqgzws)
      liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      dz1,dz2 = a[0],a[1]
      h["ri"] = Divination.ex_qc_san_he_xing_hu_xing_str(dz1,dz2,liu_qin)
    end
    return h
  end

  # 8 世爻之人格特性new
  def ex_qiucai_shi_yao()
    h = Hash.new

    liu_shou = Divination.liu_shou(@liu_shous,@shi_yao)
    di_zhi = @shi_earth
    wang_xiang_xiu_qiu_si = Divination.par_find_five_level(@par_five_levels,@shi_five)

    # 人格特性
    h["ren_ge"] = Divination.ex_qiucai_shi_yao_ren_ge(liu_shou,di_zhi,wang_xiang_xiu_qiu_si,@all_ylqgzws)

    return h
  end
  def Divination.ex_qc_shi_yao_ren_ge_str(liu_shou,wang_xiang_xiu_qiu_si,liu_qin)
    # 世爻六獸  世爻六親  旺     相     休     囚     死
  	key = Divination.liu_shou_key(liu_shou)
  	key2 = Divination.wang_xiang_xiu_qiu_si_key(wang_xiang_xiu_qiu_si)
    s = Divination.ex_qiucai_str("shi_yao_ren_ge.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_qiucai_shi_yao_ren_ge(liu_shou,di_zhi,wang_xiang_xiu_qiu_si,all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_in_all_yao_ylqgzws_by_di_zhi(di_zhi,all_ylqgzws)
    liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
    return Divination.ex_qc_shi_yao_ren_ge_str(liu_shou,wang_xiang_xiu_qiu_si,liu_qin)
  end

  # 9 動爻與變爻之關係
    # 動爻  變爻  動爻六親  變爻六親
  def ex_qiucai_dong_yao_yu_bian_yao()
    h = Hash.new

    ben_gua_ylqgzws = Divination.par_ben_gua_ylqgzws(@all_ylqgzws)
    bian_gua_ylqgzws = Divination.par_bian_gua_ylqgzws(@all_ylqgzws)

    # 動爻  變爻  動爻六親  變爻六親
    h["liu_qin"] = Divination.ex_qiucai_dong_yao_yu_bian_yao_liu_qin(ben_gua_ylqgzws,bian_gua_ylqgzws)

    # ※爻之反吟:
    h["yao_fan_yin"] = Divination.ex_qiucai_dong_yao_yu_bian_yao_yao_fan_yin(ben_gua_ylqgzws,bian_gua_ylqgzws)

    # 化退神
    h["hua_tui_shen"] = Divination.ex_qiucai_dong_yao_yu_bian_yao_hua_tui_shen(ben_gua_ylqgzws,bian_gua_ylqgzws)

    # 化進神
    h["hua_jin_shen"] = Divination.ex_qiucai_dong_yao_yu_bian_yao_hua_jin_shen(ben_gua_ylqgzws,bian_gua_ylqgzws)

    return h
  end
  def Divination.ex_qc_dong_yao_yu_bian_yao_str(key)
    return Divination.ex_qiucai_str("dong_yao_yu_bian_yao.#{key}")
  end
  def Divination.ex_qc_dong_yao_yu_bian_yao_liu_qin_str(dz1,dz2,liu_qin)
    # 世爻六獸  世爻六親  旺     相     休     囚     死
  	key = Divination.dizhi_key(dz1)
  	key2 = Divination.dizhi_key(dz2)
    s = Divination.ex_qiucai_str("dong_yao_yu_bian_yao.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_qiucai_dong_yao_yu_bian_yao_liu_qin(ben_gua_ylqgzws,bian_gua_ylqgzws)
    aOut = Array.new
    bian_gua_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_gua_ylqgzws[i]
      bian_yao = Divination.fu_shen_yao(bian_ylqgzw)
      bian_di_zhi = Divination.fu_shen_di_zhi(bian_ylqgzw)
      ben_ylqgzw = Divination.par_find_ylqgzw_by_yao(ben_gua_ylqgzws,bian_yao)
      ben_di_zhi = Divination.fu_shen_di_zhi(ben_ylqgzw)
      ben_liu_qin = Divination.fu_shen_liu_qin(ben_ylqgzw)
      s = Divination.ex_qc_dong_yao_yu_bian_yao_liu_qin_str(ben_di_zhi,bian_di_zhi,ben_liu_qin)
      aOut.push(s)
    end
    return aOut.uniq
  end
  def Divination.ex_qiucai_dong_yao_yu_bian_yao_dizhis_check(a2,ben_di_zhi,bian_di_zhi)
    a2.each_index do |i|
      a = a2[i]
      if (a[0] == ben_di_zhi && a[1] == bian_di_zhi) then
        return true
      end
    end
    return false
  end
  # ※爻之反吟:
  # 動爻  變爻
  # 午 子
  # 丑 未
  # 寅 申
  # 卯 酉
  # 辰 戌
  # 巳 亥
  @@Qiucai_DB_Yao_Fan_Yin = [[7,1],[2,8],[3,9],[4,10],[5,11],[6,12]]
  def Divination.ex_qiucai_dong_yao_yu_bian_yao_yao_fan_yin_check(ben_di_zhi,bian_di_zhi)
    return Divination.ex_qiucai_dong_yao_yu_bian_yao_dizhis_check(@@Qiucai_DB_Yao_Fan_Yin,ben_di_zhi,bian_di_zhi)
  end
  def Divination.ex_qiucai_dong_yao_yu_bian_yao_yao_fan_yin(ben_gua_ylqgzws,bian_gua_ylqgzws)
    bian_gua_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_gua_ylqgzws[i]
      bian_yao = Divination.fu_shen_yao(bian_ylqgzw)
      bian_di_zhi = Divination.fu_shen_di_zhi(bian_ylqgzw)
      ben_ylqgzw = Divination.par_find_ylqgzw_by_yao(ben_gua_ylqgzws,bian_yao)
      ben_di_zhi = Divination.fu_shen_di_zhi(ben_ylqgzw)

      if (Divination.ex_qiucai_dong_yao_yu_bian_yao_yao_fan_yin_check(ben_di_zhi,bian_di_zhi)) then
        return Divination.ex_qc_dong_yao_yu_bian_yao_str("yao_fan_yin")
      end
    end
    return ""
  end
    # 化退神
  # 動爻  變爻
  # 子 亥
  # 辰 丑
  # 戌 未
  # 午 巳
  # 酉 申
  # 未 辰
  # 卯 寅
  # 丑 戌
  @@Qiucai_DB_Hua_Tui_Shen = [[1,12],[5,2],[11,8],[7,6],[10,9],[8,5],[4,3],[2,11]]
  def Divination.ex_qiucai_dong_yao_yu_bian_yao_hua_tui_shen_check(ben_di_zhi,bian_di_zhi)
    return Divination.ex_qiucai_dong_yao_yu_bian_yao_dizhis_check(@@Qiucai_DB_Hua_Tui_Shen,ben_di_zhi,bian_di_zhi)
  end
  def Divination.ex_qiucai_dong_yao_yu_bian_yao_hua_tui_shen(ben_gua_ylqgzws,bian_gua_ylqgzws)
    bian_gua_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_gua_ylqgzws[i]
      bian_yao = Divination.fu_shen_yao(bian_ylqgzw)
      bian_di_zhi = Divination.fu_shen_di_zhi(bian_ylqgzw)
      ben_ylqgzw = Divination.par_find_ylqgzw_by_yao(ben_gua_ylqgzws,bian_yao)
      ben_di_zhi = Divination.fu_shen_di_zhi(ben_ylqgzw)

      if (Divination.ex_qiucai_dong_yao_yu_bian_yao_hua_tui_shen_check(ben_di_zhi,bian_di_zhi)) then
        return Divination.ex_qc_dong_yao_yu_bian_yao_str("hua_tui_shen")
      end
    end
    return ""
  end
  # 化進神:
  # 動爻  變爻
  # 亥 子
  # 丑 辰
  # 未 戌
  # 巳 午
  # 申 酉
  # 辰 未
  # 寅 卯
  # 戌 丑
  @@Qiucai_DB_Hua_Jin_Shen = [[12,1],[2,5],[8,11],[6,7],[9,10],[5,8],[3,4],[11,2]]
  def Divination.ex_qiucai_dong_yao_yu_bian_yao_hua_jin_shen_check(ben_di_zhi,bian_di_zhi)
    return Divination.ex_qiucai_dong_yao_yu_bian_yao_dizhis_check(@@Qiucai_DB_Hua_Jin_Shen,ben_di_zhi,bian_di_zhi)
  end
  def Divination.ex_qiucai_dong_yao_yu_bian_yao_hua_jin_shen(ben_gua_ylqgzws,bian_gua_ylqgzws)
    bian_gua_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_gua_ylqgzws[i]
      bian_yao = Divination.fu_shen_yao(bian_ylqgzw)
      bian_di_zhi = Divination.fu_shen_di_zhi(bian_ylqgzw)
      ben_ylqgzw = Divination.par_find_ylqgzw_by_yao(ben_gua_ylqgzws,bian_yao)
      ben_di_zhi = Divination.fu_shen_di_zhi(ben_ylqgzw)

      if (Divination.ex_qiucai_dong_yao_yu_bian_yao_hua_jin_shen_check(ben_di_zhi,bian_di_zhi)) then
        return Divination.ex_qc_dong_yao_yu_bian_yao_str("hua_jin_shen")
      end
    end
    return ""
  end
  # 10 貴人驛馬
  def ex_qiucai_gui_ren_yi_ma()
    h = Hash.new

    day_tian_gan = Xdate.GetGanZhiSky(@nGanZhiDay)
    day_dizhi = Xdate.GetGanZhiEarth(@nGanZhiDay)

    # 貴人1
    h["gui_ren_1"] = Divination.ex_qiucai_gui_ren_yi_ma_gui_ren_1(day_tian_gan,@all_ylqgzws,@liu_shous,@par_five_levels,@shi_earth)

    # 貴人2
    h["gui_ren_2"] = Divination.ex_qiucai_gui_ren_yi_ma_gui_ren_2(day_tian_gan,@shi_earth)

    # 驛馬
    h["yi_ma"] = Divination.ex_qiucai_gui_ren_yi_ma_yi_ma(day_dizhi,@shi_earth,@all_ylqgzws)

    return h
  end
  def Divination.ex_qc_gui_ren_yi_ma_str(key)
    return Divination.ex_qiucai_str("gui_ren_yi_ma.#{key}")
  end
  def Divination.ex_qc_gui_ren_yi_ma_shi_yao_zi_chi_gui_ren_str()
    return Divination.ex_qc_gui_ren_yi_ma_str("shi_yao_zi_chi_gui_ren")
  end
    # 貴人1
    # 日辰  甲 乙 丙 丁 戊 己 庚 辛 壬 癸
    # 貴人  丑未  子申  酉亥  酉亥  丑未  子申  寅午  寅午  卯巳  卯巳
  def Divination.ex_qc_gui_ren_yi_ma_gui_ren_1_str(tiangan,liu_shou,wang_xiang_xiu_qiu_si,liu_qin)
  	key = Divination.tiangan_key(tiangan)
  	key2 = Divination.liu_shou_key(liu_shou)
  	key3 = Divination.wang_xiang_xiu_qiu_si_key(wang_xiang_xiu_qiu_si)
    s = Divination.ex_qc_gui_ren_yi_ma_str("gui_ren_1.#{key}.#{key2}.#{key3}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_qc_gui_ren_yi_ma_is_shi_hold_gui_ren(dizhis,shi_yao_di_zhi)
    return dizhis.index(shi_yao_di_zhi) != nil
  end
  def Divination.ex_qiucai_gui_ren_yi_ma_gui_ren_1(day_tian_gan,all_ylqgzws,liu_shous,par_five_levels,shi_yao_di_zhi)
    dizhis = Divination.gui_ren(day_tian_gan,1)
    if (Divination.ex_qc_gui_ren_yi_ma_is_shi_hold_gui_ren(dizhis,shi_yao_di_zhi)) then
      aOut = Array.new
      aOut.push(Divination.ex_qc_gui_ren_yi_ma_shi_yao_zi_chi_gui_ren_str())
      return aOut.uniq
    else
      return Divination.ex_qiucai_gui_ren_yi_ma_gui_ren_1_normal(day_tian_gan,all_ylqgzws,liu_shous,par_five_levels,dizhis)
    end
  end
  def Divination.ex_qiucai_gui_ren_yi_ma_gui_ren_1_normal(day_tian_gan,all_ylqgzws,liu_shous,par_five_levels,dizhis)
    aOut = Array.new
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    dizhis.each_index do |i|
      ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,dizhis[i])
      if (ylqgzw != nil) then
        yao = Divination.fu_shen_yao(ylqgzw)
        liu_shou = Divination.liu_shou(liu_shous,yao)
        liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
        wu_xing = Divination.fu_shen_wu_xing(ylqgzw)
        wang_xiang_xiu_qiu_si = Divination.par_find_five_level(par_five_levels,wu_xing)
        aOut.push(Divination.ex_qc_gui_ren_yi_ma_gui_ren_1_str(day_tian_gan,liu_shou,wang_xiang_xiu_qiu_si,liu_qin))
      end
    end

    return aOut.uniq
  end
    # 貴人2
    # (2)貴人地支與世爻地支判斷關係：
    # 貴人      世爻                      說明
  def Divination.ex_qc_gui_ren_yi_ma_gui_ren_2_str(dz1,dz2)
  	key = Divination.dizhi_key(dz1)
  	key2 = Divination.dizhi_key(dz2)
    return Divination.ex_qc_gui_ren_yi_ma_str("gui_ren_2.#{key}.#{key2}")
  end
  def Divination.ex_qiucai_gui_ren_yi_ma_gui_ren_2(day_tian_gan,shi_yao_di_zhi)
    dizhis = Divination.gui_ren(day_tian_gan,1)
    if (Divination.ex_qc_gui_ren_yi_ma_is_shi_hold_gui_ren(dizhis,shi_yao_di_zhi)) then
      # 貴人1已傳回，不必再回
      return [""]
    else
      return Divination.ex_qiucai_gui_ren_yi_ma_gui_ren_2_normal(dizhis,shi_yao_di_zhi)
    end
  end
  @@Qiucai_Gui_Ren_2 = [
    [1,2,4,6,7],  # 貴人為 子
    [1,2,4,8,12], # 丑
    [2,3,5,8,9,11,12], # 寅
    [1,2,4,5,8,10,11], # 卯
    [1,5,9,10,11], # 辰
    [6,9,10,12], # 巳
    [1,7,8,9,10], # 午
    [1,2,7,8,12], # 未
    [3,4,6,9], # 申
    [3,4,5,10], # 酉
    [1,4,5,11,12], # 戌
    [3,6,7,12] # 亥
  ]
  def Divination.ex_qiucai_gui_ren_yi_ma_gui_ren_2_normal_check(gui_ren_dizhi,shi_yao_di_zhi)
    a = @@Qiucai_Gui_Ren_2[gui_ren_dizhi - 1]
    return a.index(shi_yao_di_zhi) != nil
  end
  def Divination.ex_qiucai_gui_ren_yi_ma_gui_ren_2_normal(dizhis,shi_yao_di_zhi)
    aOut = Array.new
    dizhis.each_index do |i|
      if (Divination.ex_qiucai_gui_ren_yi_ma_gui_ren_2_normal_check(dizhis[i],shi_yao_di_zhi)) then
        aOut.push(Divination.ex_qc_gui_ren_yi_ma_gui_ren_2_str(dizhis[i],shi_yao_di_zhi))
      end
    end

    return aOut.uniq
  end
    # 驛馬
    # 占卜日之日地支決定。
    # 日支  子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
    # 驛馬  寅 亥 申 巳 寅 亥 申 巳 寅 亥 申 巳
  @@Qiucai_Yi_Ma = [3,12,9,6,3,12,9,6,3,12,9,6]
  def Divination.ex_qiucai_yi_ma_dizhi(day_dizhi)
    return @@Qiucai_Yi_Ma[day_dizhi - 1]
  end
  def Divination.ex_qc_gui_ren_yi_ma_shi_chi_yi_ma_str()
    return Divination.ex_qc_gui_ren_yi_ma_str("yi_ma.shi_chi_yi_ma")
  end
  def Divination.ex_qc_gui_ren_yi_ma_yi_ma_str(dz1,liu_qin)
  	key = Divination.dizhi_key(dz1)
    s = Divination.ex_qc_gui_ren_yi_ma_str("yi_ma.#{key}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_qiucai_gui_ren_yi_ma_yi_ma(day_dizhi,shi_yao_di_zhi,all_ylqgzws)
    yi_ma_dizhi = Divination.ex_qiucai_yi_ma_dizhi(day_dizhi)
    if (yi_ma_dizhi == shi_yao_di_zhi) then
      return Divination.ex_qc_gui_ren_yi_ma_shi_chi_yi_ma_str()
    else
      return Divination.ex_qiucai_gui_ren_yi_ma_yi_ma_normal(day_dizhi,all_ylqgzws)
    end
  end
  def Divination.ex_qiucai_gui_ren_yi_ma_yi_ma_normal(day_dizhi,all_ylqgzws)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    yi_ma_dizhi = Divination.ex_qiucai_yi_ma_dizhi(day_dizhi)
    ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,yi_ma_dizhi)
    if (ylqgzw == nil) then
      return ""
    end
    liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
    return Divination.ex_qc_gui_ren_yi_ma_yi_ma_str(day_dizhi,liu_qin)
  end

  # 11 本卦變卦之卦象說明
  def ex_qiucai_gua_image()
    h = Hash.new
    gua_he_chong = Divination.Gua_64_he_chong(@wai,@nei)
    special_no = Divination.Gua_64_info_special_no(@wai,@nei)
    gua_fan_yin = Divination.Cr_find_gua_fan_yin(@wai,@nei,@bian_wai,@bian_nei)
    ingua_fu_yin = Divination.Cr_find_ingua_fu_yin(@wai,@nei,@bian_wai,@bian_nei)
    outgua_fu_yin = Divination.Cr_find_outgua_fu_yin(@wai,@nei,@bian_wai,@bian_nei)
    inoutgua_fu_yin = Divination.Cr_find_inoutgua_fu_yin(@wai,@nei,@bian_wai,@bian_nei)

    # (1)六沖卦:
    h["liu_chong_gua"] = Divination.ex_qiucai_gua_image_liu_chong_gua(gua_he_chong)
    # (2)游魂卦:
    h["you_hun_gua"] = Divination.ex_qiucai_gua_image_you_hun_gua(special_no)
    # (3)反吟卦
    h["fan_yin_gua"] = Divination.ex_qiucai_gua_image_fan_yin_gua(gua_fan_yin)
    # (4)伏吟卦…內卦伏吟
    h["fu_yin_gua_nei_gua_fu_yin"] = Divination.ex_qiucai_gua_image_fu_yin_gua_nei_gua_fu_yin(ingua_fu_yin)
    # (5)伏吟卦…外卦伏吟
    h["fu_yin_gua_wai_gua_fu_yin"] = Divination.ex_qiucai_gua_image_fu_yin_gua_wai_gua_fu_yin(outgua_fu_yin)
    # (6)伏吟卦…內外卦伏吟
    h["fu_yin_gua_nei_wai_gua_fu_yin"] = Divination.ex_qiucai_gua_image_fu_yin_gua_nei_wai_gua_fu_yin(inoutgua_fu_yin)

    return h
  end
  def Divination.ex_qc_ben_gua_bian_gua_gua_xiang_str(key)
    return Divination.ex_qiucai_str("ben_gua_bian_gua_gua_xiang.#{key}")
  end
    # (1)六沖卦:
  def Divination.ex_qc_ben_gua_bian_gua_gua_xiang_liu_chong_gua_str()
    return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_str("liu_chong_gua")
  end
  def Divination.ex_qiucai_gua_image_liu_chong_gua(gua_he_chong)
    if (gua_he_chong == HeChong_2) then
      return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_liu_chong_gua_str()
    else
      return ""
    end
  end
    # (2)游魂卦:
  def Divination.ex_qc_ben_gua_bian_gua_gua_xiang_you_hun_gua_str()
    return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_str("you_hun_gua")
  end
  def Divination.ex_qiucai_gua_image_you_hun_gua(special_no)
    if (special_no == Te_Shu_Gua_7) then
      return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_you_hun_gua_str()
    else
      return ""
    end
  end
    # (3)反吟卦
  def Divination.ex_qc_ben_gua_bian_gua_gua_xiang_fan_yin_gua_str()
    return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_str("fan_yin_gua")
  end
  def Divination.ex_qiucai_gua_image_fan_yin_gua(gua_fan_yin)
    if (gua_fan_yin) then
      return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_fan_yin_gua_str()
    else
      return ""
    end
  end
    # (4)伏吟卦…內卦伏吟
  def Divination.ex_qc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_nei_gua_fu_yin_str()
    return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_str("fu_yin_gua_nei_gua_fu_yin")
  end
  def Divination.ex_qiucai_gua_image_fu_yin_gua_nei_gua_fu_yin(ingua_fu_yin)
    if (ingua_fu_yin) then
      return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_nei_gua_fu_yin_str()
    else
      return ""
    end
  end
    # (5)伏吟卦…外卦伏吟
  def Divination.ex_qc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_wai_gua_fu_yin_str()
    return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_str("fu_yin_gua_wai_gua_fu_yin")
  end
  def Divination.ex_qiucai_gua_image_fu_yin_gua_wai_gua_fu_yin(outgua_fu_yin)
    if (outgua_fu_yin) then
      return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_wai_gua_fu_yin_str()
    else
      return ""
    end
  end
    # (6)伏吟卦…內外卦伏吟
  def Divination.ex_qc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_nei_wai_gua_fu_yin_str()
    return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_str("fu_yin_gua_nei_wai_gua_fu_yin")
  end
  def Divination.ex_qiucai_gua_image_fu_yin_gua_nei_wai_gua_fu_yin(inoutgua_fu_yin)
    if (inoutgua_fu_yin) then
      return Divination.ex_qc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_nei_wai_gua_fu_yin_str()
    else
      return ""
    end
  end

  # 12 參數對照與說明
  def ex_qiucai_kong_wang_yang_ren()
    h = Hash.new

    day_tian_gan = Xdate.GetGanZhiSky(@nGanZhiDay)
    day_dizhi = Xdate.GetGanZhiEarth(@nGanZhiDay)
    month_dizhi = Xdate.GetGanZhiEarth(@nGanZhiMonth)

    # 空亡
    h["kong_wang"] = Divination.ex_qc_kong_wang_yang_ren_kong_wang(day_tian_gan,day_dizhi,month_dizhi,@all_ylqgzws,@shi_yao,@par_five_levels,@par_chang_sheng)
    # 羊刄
    h["yang_ren"] = Divination.ex_qc_kong_wang_yang_ren_yang_ren(day_tian_gan,@all_ylqgzws)

    return h
  end
    # 空亡說明
  def Divination.ex_qc_kong_wang_yang_ren_str(key)
    return Divination.ex_qiucai_str("kong_wang_yang_ren.#{key}")
  end
  def Divination.ex_qc_kong_wang_yang_ren_kong_wang_shi_yao_str(tf)
    tf_key = Divination.bool_key(tf)
    return Divination.ex_qc_kong_wang_yang_ren_str("kong_wang.#{tf_key}_kong_wang.shi_yao")
  end
  def Divination.ex_qc_kong_wang_yang_ren_kong_wang_str(tf,liu_qin)
    tf_key = Divination.bool_key(tf)
    s = Divination.ex_qc_kong_wang_yang_ren_str("kong_wang.#{tf_key}_kong_wang.liu_qin")
    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_qc_kong_wang_yang_ren_kong_wang(day_tian_gan,day_dizhi,month_dizhi,all_ylqgzws,shi_yao,par_five_levels,par_chang_sheng)
    aOut = Array.new
    tf = Divination.ex_jk_ky_kong_wang_true_false(day_dizhi,month_dizhi,par_five_levels,par_chang_sheng)
    dizhis = Divination.Kong_Wang(day_tian_gan,day_dizhi)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)

    dizhis.each_index do |i|
      ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,dizhis[i])
      if (ylqgzw != nil) then
        yao = Divination.fu_shen_yao(ylqgzw)
        if (yao == shi_yao) then
          aOut.push(Divination.ex_qc_kong_wang_yang_ren_kong_wang_shi_yao_str(tf))
        else
          liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
          s = Divination.ex_qc_kong_wang_yang_ren_kong_wang_str(tf,liu_qin)
          aOut.push(s)
        end
      end
    end
    return aOut.uniq
  end
    # 羊刃
  def Divination.ex_qc_kong_wang_yang_ren_yang_ren_str(liu_qin)
    s = Divination.ex_qc_kong_wang_yang_ren_str("yang_ren.liu_qin")
    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_qc_kong_wang_yang_ren_yang_ren(day_tian_gan,all_ylqgzws)
    di_zhi = Divination.Yang_Ren(day_tian_gan)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,di_zhi)

    if (ylqgzw != nil) then
      liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      return Divination.ex_qc_kong_wang_yang_ren_yang_ren_str(liu_qin)
    else
      return ""
    end
  end
  # 13 應爻
  def ex_qiucai_yin_yao()
    h = Hash.new

    liu_shou = Divination.liu_shou(@liu_shous,@yin_yao)
    di_zhi = @yin_earth
    wang_xiang_xiu_qiu_si = Divination.par_find_five_level(@par_five_levels,@yin_five)

    # 應爻
    h["yin_yao"] = Divination.ex_qiucai_yin_yao(liu_shou,di_zhi,wang_xiang_xiu_qiu_si,@all_ylqgzws)

    return h
  end
  def Divination.ex_qc_yin_yao_str(liu_shou,wang_xiang_xiu_qiu_si,liu_qin)
    # 應爻六獸  應爻六親  旺     相     休     囚     死
    key = Divination.liu_shou_key(liu_shou)
    key2 = Divination.liu_qin_key(liu_qin)
    s = Divination.ex_qiucai_str("yin_yao.#{key}.#{key2}")

    # 旺 相 休 囚 死，共五個
    return Divination.extract_str(s,wang_xiang_xiu_qiu_si)
  end
  def Divination.ex_qiucai_yin_yao(liu_shou,di_zhi,wang_xiang_xiu_qiu_si,all_ylqgzws)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,di_zhi)
    liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
    return Divination.ex_qc_yin_yao_str(liu_shou,wang_xiang_xiu_qiu_si,liu_qin)
  end
end
