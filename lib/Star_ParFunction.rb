require("ifate_par.rb")

class Star
  # 個別參數
    # 參數設定
  attr_accessor :par_YearDisplay  # 年代顯示
  attr_accessor :par_LeapType  #起盤排法
  attr_accessor :par_HorseType    #天馬星排法
  attr_accessor :par_12GodType     #十二長生排法
  attr_accessor :par_DocType        #博士星排法
  attr_accessor :par_LuType    #祿存動盤方式
  attr_accessor :par_GSType          #將前、歲前各星排法
  attr_accessor :par_FlowSkyType         # 流盤干支方式
  attr_accessor :par_SmallSanType   #起小限規則
  attr_accessor :par_GodType      #斗君排法,預設流年斗君
  attr_accessor :par_FlowLifeHouseType    #流年命宮排法
  attr_accessor :par_GiaKungType      # 截空排法
  attr_accessor :par_FourHuaType     #四化星排法
  attr_accessor :par_FlowYearHua    #流盤四化顯示方式
  attr_accessor :par_SsnType                # 歲前各星名稱
  attr_accessor :par_b3132Type                # 旬中旬空(空亡)
  attr_accessor :par_yuekui   # 天鉞天魁排法

  attr_accessor :par_PanBase  #排盤方式
  attr_accessor :par_PanTimeBase    #流盤取時間的方法
  attr_accessor :par_FixSky                     # 尋找宮五行的天干是否要隨流盤變動

  # Shortcut   Effect
  # attr_reader :v   def v; @v; end
  # attr_writer :v   def v=(value); @v=value; end # attr_accessor :v   attr_reader :v; attr_writer :v
  # 介面參數
  attr_accessor :int_34Color               # 三方四正色彩顯示  @int_34Color
  attr_accessor :int_MioWongStyle          # 顯示廟旺的方式,一般的文字或林老師的數字或不顯示
  attr_accessor :int_HuaOutDisplay         # 顯示對宮四化
  attr_accessor :int_SelHuaDisplay         # 顯示自化
  attr_accessor :int_HouseFiveDisplay      # 顯示各宮五行
  attr_accessor :int_FlowTimeDisplay       # 顯示命盤時間
  attr_accessor :int_TenDisplay            # 顯示十年大運
  attr_accessor :int_SmallDisplay          # 顯示小限時間
  attr_accessor :int_8WordsDisplay         # 顯示八字時間
  attr_accessor :int_LifeLine              # 標示命宮三方四正
  attr_accessor :int_SmallYearRevert       # 小限應期顯示


  # 星曜顯示設定
  attr_accessor :dis_AStar      # 甲級星曜設定  Array A_STAR_COUNT
  attr_accessor :dis_BStar      # 乙、丙級星曜設定 Array B_STAR_COUNT
  attr_accessor :dis_FlowLYT     # 流祿，流羊，流陀
  attr_accessor :dis_FlowMa    # 流馬
  attr_accessor :dis_FlowLuan      # 流鸞
  attr_accessor :dis_Doctor      # 顯示博士十二星
  attr_accessor :dis_God      # 顯示長生十二星
  attr_accessor :dis_YearStar      # 顯示將星諸星
  attr_accessor :dis_YearGod      # 顯示歲前諸星
  attr_accessor :dis_FlowChan     # 顯示流昌
  attr_accessor :dis_SkyCook      # 顯示天廚星
  attr_accessor :dis_LYYG      # 顯示來因業障
  attr_accessor :dis_7Star      # 七星

    # 自訂四化
  attr_accessor :pfh_AStar      # 自訂四化

  # 堂號
  attr_accessor :hall_number1      # 中宮堂號一
  attr_accessor :hall_number2      # 中宮堂號二
  attr_accessor :hall_number3      # 中宮堂號三

  # 列印設定
  attr_accessor :print_time_page      # 是否列印時間報表
  attr_accessor :print_tp_type      # 列印時間報表方式，1張或3張
  attr_accessor :print_tp_three_1      # 若3張，第1張是否列印
  attr_accessor :print_tp_three_2      # 若3張，第2張是否列印
  attr_accessor :print_tp_three_3      # 若3張，第3張是否列印
  attr_accessor :print_pan_title      # 排盤表頭
  attr_accessor :print_time_footer      # 時間頁頁尾說明

# 免費解釋之星曜：
# 適用在免費會員及租用主版本(專家版)

# 紫微、天機、太陽、武曲、天同、廉貞、天府、太陰、貪狼、巨門、天相、天梁、七殺、破軍、羊刃、陀羅、火星、鈴星、天空、地劫、吉星：文昌、文曲、天魁、天鉞、左輔、右弼。

# 1、未註冊之會員使用論斷解釋：
# 1.1命宮、財帛宮、官祿宮 三個宮位。
# 1.2使用盤面：本命盤。

# 2、已註冊之會員使用論斷解釋：
# 2.1命宮、財帛宮、官祿宮、夫妻、遷移 等宮位
# 2.2使用盤面：本命盤。

# 3、租用專家版本使用論斷解釋：
# 3.1開放所有宮位、
# 3.2使用盤面：各流盤解釋。

# ===========================================================
# 租用論斷解釋：
# 1、所有星曜解釋。
# 2、四化解釋。
# 3、所有盤別(本命、十年、流年、流月、流日)
# 4、列印解釋。(在彈出解釋，可選擇本宮位或全部解釋，直接列印，下方註記為：王文華老師解釋，僅供參考，公司名稱 網站url  版權所有)，暫時先不做。

# 若同時租用論斷及商務：(列印時再一起做)
# 則在列印地方可以控制可印出解釋頁，並且可以修改論斷文字內容及版權。

  EX_C_STAR_A = 0x01
  EX_C_STAR_B = 0x02
  EX_C_STAR_C = 0x04
  EX_C_FOURHUA = 0x08
  EX_C_DEFAULT = EX_C_STAR_A# | EX_C_STAR_B
  EX_C_UNREGISTER = EX_C_STAR_A #EX_C_STAR_A

  EX_C_HOUSE_1 = 0x001  # 命宮
  EX_C_HOUSE_2 = 0x002  # 兄弟
  EX_C_HOUSE_3 = 0x004  # 夫妻
  EX_C_HOUSE_4 = 0x008  # 子女
  EX_C_HOUSE_5 = 0x010  # 財帛
  EX_C_HOUSE_6 = 0x020  # 疾厄
  EX_C_HOUSE_7 = 0x040  # 遷移
  EX_C_HOUSE_8 = 0x080  # 朋友
  EX_C_HOUSE_9 = 0x100  # 官祿
  EX_C_HOUSE_10 = 0x200 # 田宅
  EX_C_HOUSE_11 = 0x400 # 福德
  EX_C_HOUSE_12 = 0x800 # 父母
  EX_C_HOUSE_DEFAULT = EX_C_HOUSE_1 | EX_C_HOUSE_3 | EX_C_HOUSE_5 | EX_C_HOUSE_7 | EX_C_HOUSE_9 #341
  EX_C_HOUSE_UNREGISTER = EX_C_HOUSE_1 | EX_C_HOUSE_5 |EX_C_HOUSE_9 # 273

  def gHouse_Check34House(nHouse1Earth,nEarth)
    if (@int_34Color == Star::INT_34_COLOR) then
      n34Color = g_Check34House(nHouse1Earth,nEarth)
    else
      n34Color = Star::HOUSE_34_NONE
    end
    return n34Color
  end

  def gHouse_GetHouseOutFourHuaStr(nPanType,nEarth)
    aFourHua = Array.new(4,"")
    if (@int_HuaOutDisplay == Cfate::PAR_TRUE) then
      aStar,bUseOpp = gHouse_GetAStars(nPanType,Earth.ModifyEarth(nEarth+6),false,false)
      (Star::FH_LU..Star::FH_GI).each do |nFourHuaIndex|
        nStar = gHouse_GetFourOutStar(nPanType,nEarth,nFourHuaIndex)
        if (aStar.index(nStar) != nil) then
          aFourHua[nFourHuaIndex] = Star.GetFourHuaStr(nFourHuaIndex)
        end
      end
    end
    return aFourHua
  end

  def gHouse_GetAStarsSelfFourHua(nPanType,nEarth)
    aFourHua = Array.new
    nFourHuaCount = 0

    aStarInfos = get_HouseAStarInfos(nPanType,nEarth,false)
    aStarInfos.each do |starInfo|
      nFourHua = Star.Get_AStarInfo_SelfFourHua(starInfo)

      aFH = Array.new
      aFH.push(nFourHua == nil ? 99 : nFourHua)
      aFH.push(getFourHuaStr(nFourHua))

      if (nFourHua != nil) then
        nFourHuaCount += 1
      end

      aFourHua.push(aFH)
    end

    return aFourHua,nFourHuaCount
  end
  def gHouse_GetAStarsSelfFourHuaDisplay()
    bSelHuaDisplay = (@int_SelHuaDisplay == Cfate::PAR_TRUE)
  end

  def gHouse_FiveStr(nPanType,nEarth)
    sFive = ""
    if (@int_HouseFiveDisplay == Cfate::PAR_TRUE) then
      nFive = cp_getHouseFive(nPanType,nEarth)
      sFive = Five.GetFiveStr(nFive)
    end
    return sFive
  end

  def gHouse_GetFlowTimeStr(nPanType)
    sFlowTime1 = sFlowTime2 = sFlowTime3 = ""
    if (int_FlowTimeDisplay == Cfate::PAR_TRUE) then
      sFlowTime1,sFlowTime2,sFlowTime3 = gHouse_GetFlowTimeStr2(nPanType)
    end

    return sFlowTime1,sFlowTime2,sFlowTime3
  end
  def gHouse_GetFlowTimeStr2(nPanType)
    sFlowTime1 = sFlowTime2 = sFlowTime3 = ""
    # 顯示流年等訊息
    if (nPanType == Cfate::PAN_NORMAL) then
      sFlowTime1,sFlowTime2,sFlowTime3 = gHouse_GetFlowTimeStr_Normal(nPanType)
    elsif (nPanType == Cfate::PAN_TENYEAR)
      sFlowTime1,sFlowTime2,sFlowTime3 = gHouse_GetFlowTimeStr_TenYear(nPanType)
    elsif (nPanType >= Cfate::PAN_FLOWYEAR)
      sFlowTime1,sFlowTime2,sFlowTime3 = gHouse_GetFlowTimeStr_FlowYear(nPanType)
    end
    sFlowTime3 = gHouse_GetTodayStr()
    return sFlowTime1,sFlowTime2,sFlowTime3
  end

  def gHouse_GetFlowTimeStr_Normal(nPanType)
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(@m_UserInfo["remote_ip"])
    sFlowTime1 = Pm.GetStr("IDS_X_NOW")
    sFlowTime1 += Pm.GetStrWithQuote2("IDS_S_UI_SOLAR_CAL")
    sFlowTime1 += Pm.GetColon()
    sFlowTime1 += Xdate.GetSolarDateStr(nWYear,nWMonth,nWDate,@par_YearDisplay)

    sFlowTime2 = Pm.GetStr("IDS_X_NOW")
    sFlowTime2 += Pm.GetStrWithQuote2("IDS_S_UI_MOON_CAL")
    sFlowTime2 += Pm.GetColon()
    sFlowTime2 += Xdate.GetMoonDateStr(nEYear,nEMonth,nEDate,bLeapMonth,@par_YearDisplay)

        sFlowTime3 = gHouse_GetTodayStr()

        return sFlowTime1,sFlowTime2,sFlowTime3
    end

  def gHouse_GetFlowTimeStr_TenYear(nPanType)
    nStartYear = g_getLargeSanStartYear()
    nStarYearOld = gNormal_GetYearOld(uig_EY(),nStartYear)

      sFlowTime1 = Cfate.GetPanFullNameWithColon(nPanType)
    sFlowTime1 += "#{nStarYearOld} -- #{nStarYearOld + 9}#{Pm.GetStr("IDS_S_YEAR_OLD")}"

      sFlowTime2 = Pm.GetStrWithColon("IDS_S_UI_LUNAR_CAL")
    sFlowTime2 += "#{nStartYear} -- #{nStartYear + 9}#{Pm.GetStr("IDS_X_YEAR")}"

        sFlowTime3 = gHouse_GetTodayStr()

        return sFlowTime1,sFlowTime2,sFlowTime3
  end

  def gHouse_GetFlowTimeStr_FlowYear(nPanType)
    sFlowTime1 =gHouse_GetFlowTimeStr_PanInfo(nPanType)

    sFlowTime2 = Pm.GetStrWithColon("IDS_S_YEAR_OLD_LUNAR")
    sFlowTime2 += Xdate.GetYearOldStr(gNormal_GetYearOld(uig_EY(),fdg_EY(nPanType)))
    sFlowTime2 += " "
      if (nPanType >= Cfate::PAN_FLOWYEAR) then
          sFlowTime2 += Xdate.GetLunarYearGanZhiStr(fdg_EY(nPanType))
      end
      if (nPanType >= Cfate::PAN_FLOWMONTH) then
          sFlowTime2 += Xdate.GetLunarMonthGanZhiStr(fdg_EY(nPanType),fdg_EM(nPanType))
      end
      if (nPanType >= Cfate::PAN_FLOWDATE) then
        sFlowTime2 += Xdate.GetLunarDateGanZhiStr(fdg_EY(nPanType),fdg_EM(nPanType),fdg_ED(nPanType),fdg_ET(nPanType),fdg_EL(nPanType))
        end
      if (nPanType >= Cfate::PAN_FLOWTIME) then
        sFlowTime2 += Xdate.GetLunarTimeGanZhiStr(fdg_EY(nPanType),fdg_EM(nPanType),fdg_ED(nPanType),fdg_ET(nPanType),fdg_EL(nPanType))
        end

        sFlowTime3 = gHouse_GetTodayStr()

        return sFlowTime1,sFlowTime2,sFlowTime3
    end

    def gHouse_GetFlowTimeStr_PanInfo(nPanType)
      sFlowTime = Cfate.GetPanFullNameWithColon(nPanType)
      sFlowTime1 = Pm.GetStrWithColon("IDS_S_UI_SOLAR_CAL")
      sFlowTime2 = Pm.GetStrWithColon("IDS_S_UI_LUNAR_CAL")
      if (nPanType == Cfate::PAN_FLOWYEAR) then
        sFlowTime1 += Xdate.YearDisplayOnlyNumber(fdg_WY(nPanType),@par_YearDisplay)
        #sFlowTime2 += Xdate.YearDisplayOnlyNumber(fdg_EY(),@par_YearDisplay)
      sFlowTime2 += Xdate.GetFullEDateTimeNumStr(fdg_EY(nPanType),0,0,-1,false,@par_YearDisplay)
      elsif (nPanType == Cfate::PAN_FLOWMONTH) then
        sFlowTime1 += Xdate.GetFullWDateStr(fdg_WY(nPanType),fdg_WM(nPanType),0,@par_YearDisplay)
        #sFlowTime2 += Xdate.GetFullWDateStr(fdg_EY(nPanType),fdg_EM(nPanType),0,@par_YearDisplay)
      sFlowTime2 += Xdate.GetFullEDateTimeNumStr(fdg_EY(nPanType),fdg_EM(nPanType),0,-1,fdg_EL(nPanType),@par_YearDisplay)
      elsif (nPanType >= Cfate::PAN_FLOWDATE) then
        sFlowTime1 += Xdate.GetFullWDateStr(fdg_WY(nPanType),fdg_WM(nPanType),fdg_WD(nPanType),@par_YearDisplay)
        #sFlowTime2 += Xdate.GetFullWDateStr(fdg_EY(nPanType),fdg_EM(nPanType),fdg_ED(nPanType),@par_YearDisplay)
      sFlowTime2 += Xdate.GetFullEDateTimeNumStr(fdg_EY(nPanType),fdg_EM(nPanType),fdg_ED(nPanType),-1,fdg_EL(nPanType),@par_YearDisplay)
      end

      return sFlowTime + sFlowTime1 + " " + sFlowTime2
   end

  def gHouse_CheckMiddle34House(nHouse1Earth,nEarth)
    if (@int_LifeLine == Cfate::PAR_TRUE) then
      n34Color = g_Check34House(nHouse1Earth,nEarth)
    else
      n34Color = Star::HOUSE_34_NONE
    end
    return n34Color
  end

  def gHouse_GetTodayStr()
    # 今日
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(@m_UserInfo["remote_ip"])
      sFlowTime = Pm.GetStrWithColon("IDS_S_TODAY")
      sFlowTime += Pm.GetStrWithColon("IDS_S_UI_SOLAR_CAL")
      sFlowTime += Xdate.GetFullWDateStr(nWYear,nWMonth,nWDate,@par_YearDisplay)
      sFlowTime += " "
      sFlowTime += Pm.GetStrWithColon("IDS_S_UI_LUNAR_CAL")
      sFlowTime += Xdate.GetNumberEDateTimeStr(nEYear,nEMonth,nEDate,bLeapMonth,@par_YearDisplay)

      return sFlowTime
  end

  def gHouse_GetLarge(nEarth=nil)
    if (nEarth == nil) then
      aLarge = Array.new(12)
      (1..12).each do |nEarth|
        nEarthIndex = Earth.Earth2EarthIndex(nEarth)
        aLarge[nEarthIndex] = cp_getHouseLarge(nEarth)
      end
      return aLarge
    else
      return cp_getHouseLarge(nEarth)
    end
  end

  def gHouse_GetLargeDisplay()
    return (@int_TenDisplay == Cfate::PAR_TRUE)
  end

  def gHouse_GetSmallDisplay()
    return (@int_SmallDisplay == Cfate::PAR_TRUE)
  end

  def gHouse_Get8WordsDisplay()
    return (@int_8WordsDisplay == Cfate::PAR_TRUE)
  end

  def gHouse_GetSmallYearRevertDisplay()
    return (@int_SmallYearRevert == Cfate::PAR_TRUE)
  end

  def gHouse_GetAStarDisplay(nPanType,nStar)
    # 祿存疊四層 流時，流分盤不顯示羊刃 祿存 陀羅 只顯示流日以上的疊宮
    # if (nPanType > Cfate::PAN_FLOWDATE && [19,20,21].include?(nStar) && (@dis_FlowLYT == Star::STAR_DIS_FLOW_DIE)) then
    # 增加時羊，時陀，時祿
    if (nPanType > Cfate::PAN_FLOWTIME && [19,20,21].include?(nStar) && (@dis_FlowLYT == Star::STAR_DIS_FLOW_DIE)) then
      # 1、設本命時，各流盤之祿存不顯示:作法是將各流盤的名稱改成本命的名稱，並只顯示各流盤的羊刃 祿存 陀羅
      # LU_CALD，LU_FLOW時，流時，流分盤不顯示羊刃 祿存 陀羅。
      if ([Star::PAN_LU_LIFE].include?(@par_LuType)) then
        return true
      else
        return false
      end
    else
      return (@dis_AStar["#{Star::DIS_ASTAR}_#{nStar}"] == Cfate::PAR_TRUE)
    end
  end

  def gHouse_GetBStarDisplay(nStar)
    return (@dis_BStar["#{Star::DIS_BSTAR}_#{nStar}"] == Cfate::PAR_TRUE)
  end

  def gHouse_GetFlowLYTDisplay()
    return (@dis_FlowLYT == Star::STAR_DIS_FLOW_ORG)
  end

  # 顯示博士十二星
  def gHouse_GetDoctorDisplay()
    return (@dis_Doctor == Cfate::PAR_TRUE)
  end

  # 顯示長生十二星
  def gHouse_GetGodDisplay()
    return (@dis_God == Cfate::PAR_TRUE)
  end

  # 顯示將星諸星
  def gHouse_GetYearStarDisplay()
    return (@dis_YearStar == Cfate::PAR_TRUE)
  end

  # 顯示歲前諸星
  def gHouse_GetYearGodDisplay()
    return (@dis_YearGod == Cfate::PAR_TRUE)
  end

  def gHouse_GetSkyCookDisplay()
    return true
    # always true ,using b_star_35 to control display
    # return (@dis_SkyCook == Cfate::PAR_TRUE)
  end

  def gHouse_GetFlowChanDisplay()
    return (@dis_FlowChan == Cfate::PAR_TRUE)
  end

  def gHouse_GetLyygDisplay()
    return (@dis_LYYG == Cfate::PAR_TRUE)
  end

  def gHouse_Get7StarDisplay()
    return (@dis_7Star == Cfate::PAR_TRUE)
  end


  # 七星暗盤
  @@SevenStarUnderPan = [19,16,6,4,14,4,6,16,19,10,9,10]
  def gHouse_Get7Star(nEarth)
    nHouseIndex = Star.Earth2HouseIndex(nEarth)
    aStar = Array.new
    nStar = @@SevenStarUnderPan[nHouseIndex]
    aStar.push(nStar)
    aStar.push(Star.GetAStarName(nStar))
    return aStar
  end

  def Star.gHouse_Get7StarName(a7StarInfo)
    return a7StarInfo[1]
  end

  def Star.get_pan_pars(userId,bCanUseIfatePars=false)
    if (bCanUseIfatePars) then
      panPar = IfatePar.check_userid_star(userId).first
      if (panPar == nil) then
        hUserType = Star.pan_par_assign(nil,userId)
      else
        hUserType = Star.pan_par_assign(panPar.hPars,userId)
      end
    else
      hUserType = Star.pan_par_assign(nil,userId)
    end

    return hUserType
  end

  def Star.pan_par_assign(hIn,userId)
    hUserType = Hash.new
    if (hIn == nil) then
      hInput = nil #Hash.new
    else
      hInput = hIn
    end
    hUserType = Star.pan_par_assign_parameter(hUserType,hInput)
    hUserType = Star.pan_par_assign_interface(hUserType,hInput)
    hUserType = Star.pan_par_assign_display(hUserType,hInput)
    hUserType = Star.pan_par_assign_privatefourhua(hUserType,hInput)
    hUserType = Star.pan_par_assign_hallnumber(hUserType,hInput)
    hUserType = Star.pan_par_assign_print(hUserType,hInput)

    return hUserType
  end

  def Star.pan_par_assign_each(nPanPar,hUserType,hInput)
    if (nPanPar == Star::PanPar_Type) then
      hUserType = Star.pan_par_assign_parameter(hUserType,hInput)
    elsif (nPanPar == Star::PanPar_Interface) then
      hUserType = Star.pan_par_assign_interface(hUserType,hInput)
    elsif (nPanPar == Star::PanPar_Display) then
      hUserType = Star.pan_par_assign_display(hUserType,hInput)
    elsif (nPanPar == Star::PanPar_PrivateFourHua) then
      hUserType = Star.pan_par_assign_privatefourhua(hUserType,hInput)
    elsif (nPanPar == Star::PanPar_HallNumber) then
      hUserType = Star.pan_par_assign_hallnumber(hUserType,hInput)
    elsif (nPanPar == Star::PanPar_Print) then
      hUserType = Star.pan_par_assign_print(hUserType,hInput)
    end
    return hUserType
  end
  def Star.pan_par_keys(nPanPar)
    hUserType = Star.pan_par_init_each(nPanPar,{})
    return hUserType.keys
  end
  def Star.pan_par_key_str(key,hParAll)
    h = {
      # 參數設定
      Cfate::PAR_PAN_YEAR_DISPLAY => "IDS_CAPTION_YEAR",
      Star::PAR_LEAP_TYPE => "IDS_CAPTION_LEAP",
      Star::PAR_HORSE_TYPE => "IDS_CAPTION_HORSE",
      Star::PAR_GS_NAME_TYPE => "IDS_CAPTION_SSN",
      Star::PAR_SMALLSAN_TYPE => "IDS_CAPTION_SSAN",
      Star::PAR_GOD_TYPE => "IDS_CAPTION_GOD",
      Star::PAR_FLOWLIFE_HOUSETYPE => "IDS_CAPTION_FLH",
      Star::PAR_GIAKUNG_TYPE => "IDS_CAPTION_GK",
      Star::PAR_FOURHUA_TYPE => "IDS_CAPTION_FH",
      Star::PAR_YUEKUI_TYPE => "IDS_CAPTION_YUEKUI",
      Star::PAR_ZI_SHI_TYPE => "IDS_CAPTION_ZI_SHI",
      Star::PAR_GOD_TYPE12 => "IDS_CAPTION_12GOD",
      Star::PAR_DOC_TYPE => "IDS_CAPTION_DOC",
      Star::PAR_LU_TYPE => "IDS_CAPTION_LU",
      Star::PAR_GS_TYPE => "IDS_CAPTION_GS",
      Star::PAR_FLOWYEAR_HUA => "IDS_CAPTION_YEAR_HUA",
      Star::PAR_FLOWSKY_TYPE => "IDS_CAPTION_FS",
      Star::PAR_FLOWSKY_TYPE_CALD_FLOWYEAR => "IDS_CAPTION_FS_CALD_FLOWYEAR",
      Star::PAR_B3132_TYPE => "IDS_CAPTION_B_STAR_31_32_PAN_TYPE",
      Star::PAR_B30_TYPE => "IDS_CAPTION_B_STAR_30_PAN_TYPE",
      Star::PAR_B2425_TYPE => "IDS_CAPTION_B_STAR_24_25_PAN_TYPE",
      Star::PAR_LIUSHI_MINGGONG => "IDS_CAPTION_LIUSHI_MINGGONG"
    }
    h2 = {
      # 介面參數
      Star::INT_34COLOR => "IDS_INTERFACE_CAPTION_34Color",
      Star::INT_MIOWONG_STYLE => "IDS_INTERFACE_CAPTION_MW",
      Star::INT_HUAOUT_DISPLAY => "IDS_INTERFACE_CHECK_HUAOUT",
      Star::INT_SELHUA_DISPLAY => "IDS_INTERFACE_CHECK_SELF_HUA",
      Star::INT_HOUSEFIVE_DISPLAY => "IDS_INTERFACE_CHECK_HOUSE_FIVE",
      Star::INT_FLOWTIME_DISPLAY => "IDS_INTERFACE_CHECK_FLOW_TIME",
      Star::INT_TEN_DISPLAY => "IDS_INTERFACE_CHECK_TEN_YEAR",
      Star::INT_SMALL_DISPLAY => "IDS_INTERFACE_CHECK_SMALL_YEAR",
      Star::INT_8WORDS_DISPLAY => "IDS_INTERFACE_CHECK_8WORDS",
      Star::INT_LIFELINE_DISPLAY => "IDS_INTERFACE_CHECK_LIFE_LINE",
      Star::INT_SMALLYEAR_REVERT_DISPLAY => "IDS_INTERFACE_CHECK_SMALL_YEAR_REVERT"
    }
    h.merge!(h2)

  # 星曜顯示設定
    h3 = Star.HashAStarKeyStr()
    h3.merge!(Star.HashBStarKeyStr(hParAll[Star::PAR_FOURHUA_TYPE]))
    h3[Star::DIS_ASTAR] = "IDS_DISPLAY_CAPTION_ASTAR"
    h3[Star::DIS_BSTAR] = "IDS_DISPLAY_CAPTION_BSTAR"
    h3[Star::DIS_FLOWLYT] = "IDS_DISPLAY_CAPTION_STAR_FLOWNAME_LYT"
    h3[Star::DIS_FLOWMA]  = "IDS_DISPLAY_CAPTION_STAR_FLOWNAME_MA"
    h3[Star::DIS_FLOWLUAN] = "IDS_DISPLAY_CAPTION_STAR_FLOWNAME_LUAN"
    h3[Star::DIS_DOCTOR] = "IDS_DISPLAY_CHECK_DOCTOR"
    h3[Star::DIS_LIVE] = "IDS_DISPLAY_CHECK_LIVE"
    h3[Star::DIS_YEARSTAR] = "IDS_DISPLAY_CHECK_YEARSTAR"
    h3[Star::DIS_YEARGOD] = "IDS_DISPLAY_CHECK_YEARGOD"
    h3[Star::DIS_FLOWCHAN] = "IDS_DISPLAY_CHECK_FLOWCHAN"
    # h3[Star::DIS_SKYCOOK] = "IDS_DISPLAY_CHECK_SKYCOOK"
    h3[Star::DIS_LYYG] = "IDS_DISPLAY_CHECK_LYYG"
    h3[Star::DIS_7STAR] = "IDS_DISPLAY_CHECK_7STAR"
    h.merge!(h3)

    h[Cfate::HALL_NUMBER1] = "IDS_S_MIDDLE_HALLNUMBER_1"
    h[Cfate::HALL_NUMBER2] = "IDS_S_MIDDLE_HALLNUMBER_2"
    h[Cfate::HALL_NUMBER3] = "IDS_S_MIDDLE_HALLNUMBER_3"

    return h[key]
  end
  def Star.HashAStarKeyStr()
    h = {}
    (1..Star::A_STAR_COUNT).each do |nStar|
      h["#{Star::DIS_ASTAR}_#{nStar}"] = Star.GetNameKey(Star.GetAStarNameKey(nStar))
    end
    return h
  end
  def Star.HashBStarKeyStr(par_FourHuaType)
    h = {}
    (1..Star::B_STAR_COUNT).each do |nStar|
      h["#{Star::DIS_BSTAR}_#{nStar}"] = Star.GetNameKey(Star.GetBStarNameKey(nStar,par_FourHuaType))
    end
    return h
  end

  def Star.pan_par_init_each(nPanPar,hUserType)
    Star.pan_par_assign_each(nPanPar,hUserType,nil)

    return hUserType
  end

  # 參數設定
  def Star.pan_par_assign_parameter(hUserType,hInput)
    #hUserType.merge(hInput)
    hUserType[Cfate::PAR_PAN_YEAR_DISPLAY] = Cfate.HashOrDefault(hInput,Cfate::PAR_PAN_YEAR_DISPLAY,Star::PAR_YEARDISPLAY_DEFAULT)  # 年代顯示
    hUserType[Star::PAR_ZI_SHI_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_ZI_SHI_TYPE,Star::PAR_ZI_SHI_DEFAULT) # 子時排盤
    hUserType[Star::PAR_LEAP_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_LEAP_TYPE,Star::PAR_LEAPTYPE_DEFAULT) # 閏月排法
    hUserType[Star::PAR_FOURHUA_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_FOURHUA_TYPE,Star::PAR_FOURHUATYPE_DEFAULT) # 四化星排法
    hUserType[Star::PAR_FLOWLIFE_HOUSETYPE] = Cfate.HashOrDefault(hInput,Star::PAR_FLOWLIFE_HOUSETYPE,Star::PAR_FLOWLIFEHOUSETYPE_DEFAULT) # 流年命宮排法
    hUserType[Star::PAR_SMALLSAN_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_SMALLSAN_TYPE,Star::PAR_SMALLSANTYPE_DEFAULT) # 起小限規則
    hUserType[Star::PAR_GOD_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_GOD_TYPE,Star::PAR_GODTYPE_DEFAULT) # 斗君排法
    hUserType[Star::PAR_LIUSHI_MINGGONG] = Cfate.HashOrDefault(hInput,Star::PAR_LIUSHI_MINGGONG,Star::PAR_LIUSHI_MINGGONG_DEFAULT) # 現在時間起命宮
    hUserType[Star::PAR_HORSE_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_HORSE_TYPE,Star::PAR_HORSETYPE_DEFAULT) # 天馬星排法
    hUserType[Star::PAR_GIAKUNG_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_GIAKUNG_TYPE,Star::PAR_GIAKUNGTYPE_DEFAULT) # 截空排法
    hUserType[Star::PAR_YUEKUI_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_YUEKUI_TYPE,Star::PAR_YUEKUI_DEFAULT) # 天鉞天魁排法
    hUserType[Star::PAR_GS_NAME_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_GS_NAME_TYPE,Star::PAR_SSNTYPE_DEFAULT) # 歲前各星名稱
    hUserType[Star::PAR_FLOWYEAR_HUA] = Cfate.HashOrDefault(hInput,Star::PAR_FLOWYEAR_HUA,Star::PAR_FLOWYEARHUA_DEFAULT) # 流盤四化顯示
    hUserType[Star::PAR_FLOWSKY_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_FLOWSKY_TYPE,Star::PAR_FLOWSKYTYPE_DEFAULT) # 流盤干支排法
    hUserType[Star::PAR_FLOWSKY_TYPE_CALD_FLOWYEAR] = Cfate.HashOrDefault(hInput,Star::PAR_FLOWSKY_TYPE_CALD_FLOWYEAR,Star::PAR_FLOWSKYTYPE_CALD_FLOWYEAR_DEFAULT) # 流盤干支排法 流年干支
    hUserType[Star::PAR_LU_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_LU_TYPE,Star::PAR_LUTYPE_DEFAULT) # 祿存星排法顯示
    hUserType[Star::PAR_B30_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_B30_TYPE,Star::PAN_B30_DEFAULT) # 截空動盤顯示
    hUserType[Star::PAR_B3132_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_B3132_TYPE,Star::PAN_B3132_DEFAULT) # 旬中旬空(空亡)
    hUserType[Star::PAR_B2425_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_B2425_TYPE,Star::PAN_B2425_DEFAULT) # 魁鉞動盤顯示
    hUserType[Star::PAR_GOD_TYPE12] = Cfate.HashOrDefault(hInput,Star::PAR_GOD_TYPE12,Star::PAR_12GODTYPE_DEFAULT) # 十二長生排法
    hUserType[Star::PAR_DOC_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_DOC_TYPE,Star::PAR_DOCTYPE_DEFAULT) # 博士星排法
    hUserType[Star::PAR_GS_TYPE] = Cfate.HashOrDefault(hInput,Star::PAR_GS_TYPE,Star::PAR_GSTYPE_DEFAULT) # 將前、歲前各星排法

    return hUserType
  end

  # 介面參數
  def Star.pan_par_assign_interface(hUserType,hInput)
    hUserType[Star::INT_34COLOR]    = Cfate.HashOrDefault(hInput,Star::INT_34COLOR,Star::INT_34COLOR_DEFAULT)      # 三方四正色彩顯示
    hUserType[Star::INT_MIOWONG_STYLE]    = Cfate.HashOrDefault(hInput,Star::INT_MIOWONG_STYLE,Star::INT_MIOWONGSTYLE_DEFAULT)      # 顯示廟旺的方式,一般的文字或林老師的數字或不顯示
    hUserType[Star::INT_HUAOUT_DISPLAY]    = Cfate.HashOrDefault2(hInput,Star::INT_HUAOUT_DISPLAY,Star::INT_HUAOUT_DISPLAY_DEFAULT)      # 顯示對宮四化
    hUserType[Star::INT_SELHUA_DISPLAY]    = Cfate.HashOrDefault2(hInput,Star::INT_SELHUA_DISPLAY,Star::INT_SELHUA_DISPLAY_DEFAULT)      # 顯示自化
    hUserType[Star::INT_HOUSEFIVE_DISPLAY]    = Cfate.HashOrDefault2(hInput,Star::INT_HOUSEFIVE_DISPLAY,Star::INT_HOUSEFIVE_DISPLAY_DEFAULT)      # 顯示各宮五行
    hUserType[Star::INT_FLOWTIME_DISPLAY]    = Cfate.HashOrDefault2(hInput,Star::INT_FLOWTIME_DISPLAY,Star::INT_FLOWTIME_DISPLAY_DEFAULT)      # 顯示命盤時間
    hUserType[Star::INT_TEN_DISPLAY]    = Cfate.HashOrDefault2(hInput,Star::INT_TEN_DISPLAY,Star::INT_TEN_DISPLAY_DEFAULT)      # 顯示十年大運
    hUserType[Star::INT_SMALL_DISPLAY]    = Cfate.HashOrDefault2(hInput,Star::INT_SMALL_DISPLAY,Star::INT_SMALL_DISPLAY_DEFAULT)      # 顯示小限時間
    hUserType[Star::INT_8WORDS_DISPLAY]    = Cfate.HashOrDefault2(hInput,Star::INT_8WORDS_DISPLAY,Star::INT_8WORDS_DISPLAY_DEFAULT)      # 顯示八字時間
    hUserType[Star::INT_LIFELINE_DISPLAY]    = Cfate.HashOrDefault2(hInput,Star::INT_LIFELINE_DISPLAY,Star::INT_LIFELINE_DISPLAY_DEFAULT)      # 標示命宮三方四正
    hUserType[Star::INT_SMALLYEAR_REVERT_DISPLAY]    = Cfate.HashOrDefault2(hInput,Star::INT_SMALLYEAR_REVERT_DISPLAY,Star::INT_SMALLYEAR_REVERT_DISPLAY_DEFAULT)    # 小限應期顯示

    return hUserType
  end

  # 星曜顯示設定
  def Star.pan_par_assign_display(hUserType,hInput)
    hUserType2 = Star.HashStarsAssign2(Star::DIS_ASTAR,hUserType,hInput,Star::A_STAR_COUNT,Cfate::PAR_TRUE)   # 甲級星曜設定
    hUserType.merge!(hUserType2) { |key, v1, v2| v2 }
    hUserType2 = Star.HashStarsAssign2(Star::DIS_BSTAR,hUserType,hInput,Star::B_STAR_COUNT,Cfate::PAR_TRUE)   # 乙、丙級星曜設定
    hUserType.merge!(hUserType2) { |key, v1, v2| v2 }
    hUserType[Star::DIS_FLOWLYT] = Cfate.HashOrDefault(hInput,Star::DIS_FLOWLYT,Star::DIS_FLOWLYT_DEFAULT)   # 流祿，流羊，流陀
    hUserType[Star::DIS_FLOWMA]  = Cfate.HashOrDefault(hInput,Star::DIS_FLOWMA,Star::DIS_FLOWMA_DEFAULT)   # 流馬
    hUserType[Star::DIS_FLOWLUAN] = Cfate.HashOrDefault(hInput,Star::DIS_FLOWLUAN,Star::DIS_FLOWLUAN_DEFAULT)   # 流鸞
    hUserType[Star::DIS_DOCTOR] = Cfate.HashOrDefault2(hInput,Star::DIS_DOCTOR,Star::DIS_DOCTOR_DEFAULT)  # 顯示博士十二星
    hUserType[Star::DIS_LIVE] = Cfate.HashOrDefault2(hInput,Star::DIS_LIVE,Star::DIS_LIVE_DEFAULT)   # 顯示長生十二星
    hUserType[Star::DIS_YEARSTAR] = Cfate.HashOrDefault2(hInput,Star::DIS_YEARSTAR,Star::DIS_YEARSTAR_DEFAULT)   # 顯示將星諸星
    hUserType[Star::DIS_YEARGOD] = Cfate.HashOrDefault2(hInput,Star::DIS_YEARGOD,Star::DIS_YEARGOD_DEFAULT)   # 顯示歲前諸星
    hUserType[Star::DIS_FLOWCHAN]  = Cfate.HashOrDefault2(hInput,Star::DIS_FLOWCHAN,Star::DIS_FLOWCHAN_DEFAULT)      # 顯示流昌
    hUserType[Star::DIS_SKYCOOK]   = Cfate.HashOrDefault2(hInput,Star::DIS_SKYCOOK,Star::DIS_SKYCOOK_DEFAULT)      # 顯示天廚星
    hUserType[Star::DIS_LYYG]    = Cfate.HashOrDefault2(hInput,Star::DIS_LYYG,Star::DIS_LYYG_DEFAULT)      # 顯示來因業障
    hUserType[Star::DIS_7STAR] = Cfate.HashOrDefault2(hInput,Star::DIS_7STAR,Star::DIS_7STAR_DEFAULT)   # 七星

    return hUserType
  end

  def Star.pan_par_assign_privatefourhua(hUserType,hInput)
    (Sky::SKY_FIRST..Sky::SKY_LAST).each do |nSky|
      (Star::FH_LU..Star::FH_GI).each do |nFourHuaIndex|
        hUserType[Star.PrivateFourHua_HashName(nSky,nFourHuaIndex)] = Cfate.HashOrDefault(hInput,Star.PrivateFourHua_HashName(nSky,nFourHuaIndex),Star.GetDefaultFourHuaStarValue(Star::PAN_FH_STEVEN,nSky,nFourHuaIndex))
      end
    end
    return hUserType
  end

  # 堂號設定
  def Star.pan_par_assign_hallnumber(hUserType,hInput)
    hUserType[Cfate::HALL_NUMBER1] = Cfate.Hash2Str(hInput,Cfate::HALL_NUMBER1,"") #Star.gPan_CI_Data1())
    hUserType[Cfate::HALL_NUMBER2] = Cfate.Hash2Str(hInput,Cfate::HALL_NUMBER2,"") #Star.gPan_CI_Data2())
    hUserType[Cfate::HALL_NUMBER3] = Cfate.Hash2Str(hInput,Cfate::HALL_NUMBER3,"") #Star.gPan_CI_Data3())
    # 老師堂號介紹
    hUserType[Cfate::ADV_HALL_NUMBER_1] = Cfate.Hash2Str(hInput,Cfate::ADV_HALL_NUMBER_1,"")
    hUserType[Cfate::ADV_HALL_NUMBER_2] = Cfate.Hash2Str(hInput,Cfate::ADV_HALL_NUMBER_2,"")
    hUserType[Cfate::ADV_HALL_NUMBER_3] = Cfate.Hash2Str(hInput,Cfate::ADV_HALL_NUMBER_3,"")
    return hUserType
  end

  # 列印設定
  def Star.pan_par_assign_print(hUserType,hInput)
    hUserType[Cfate::PAR_PRINT_TIME_PAGE] = Cfate.HashBoolCheck(hInput,Cfate::PAR_PRINT_TIME_PAGE,false)
    hUserType[Cfate::PAR_PRINT_TIME_PAGE_TYPE] = Cfate.HashOrDefault(hInput,Cfate::PAR_PRINT_TIME_PAGE_TYPE,Cfate::PRINT_TP_INONE)
    hUserType[Cfate::PAR_PRINT_TIME_PAGE_THREE_1] = Cfate.HashBoolCheck(hInput,Cfate::PAR_PRINT_TIME_PAGE_THREE_1,false)
    hUserType[Cfate::PAR_PRINT_TIME_PAGE_THREE_2] = Cfate.HashBoolCheck(hInput,Cfate::PAR_PRINT_TIME_PAGE_THREE_2,false)
    hUserType[Cfate::PAR_PRINT_TIME_PAGE_THREE_3] = Cfate.HashBoolCheck(hInput,Cfate::PAR_PRINT_TIME_PAGE_THREE_3,false)
    hUserType[Cfate::PAR_PRINT_PAN_HEADER] = Cfate.Hash2Str(hInput,Cfate::PAR_PRINT_PAN_HEADER,"")
    hUserType[Cfate::PAR_PRINT_SET_FOOTER] = Cfate.Hash2Str(hInput,Cfate::PAR_PRINT_SET_FOOTER,"")

    return hUserType
  end

  def pan_par_hash2var_all(hInput)
    pan_par_hash2var_parameter(hInput)
    pan_par_hash2var_interface(hInput)
    pan_par_hash2var_display(hInput)
    pan_par_hash2var_privatefourhua(hInput)
    pan_par_hash2var_hallnumber(hInput)
    pan_par_hash2var_print(hInput)
  end

  def par_check_LeapType()
    if (!uig_EL() || uig_EM() >= 12) then
      return false
    end
    if (@par_LeapType == Star::PAN_LEAP_THIS) then
      return false
    end

    if (@par_LeapType == Star::PAN_LEAP_MID) then
      if (uig_ED() <= 15) then
        return false
      else
        return true
      end
    end

    if (@par_LeapType == Star::PAN_LEAP_NEXT) then
      return true
    end

    if (@par_LeapType == Star::PAN_LEAP_SEGMENT) then
      wy,wm,wd,h = uig_W_YMDH()
      min = uig_WMI()
      # puts "y,m,d,h,min,segment check: #{wy},#{wm},#{wd},#{h},#{min},#{Xdate.leap_date_before_segment_date_check(wy, wm,wd, h,min)}"
      return Xdate.leap_date_before_segment_date_check(wy, wm,wd, h,min)
    end
    return false
  end

  # 子時排盤判斷 PAR_ZI_SHI
  def par_check_ZiShi_Type(ey,em,ed,el,eh)
    if (@par_zi_shi == Star::PAN_ZI_SHI_NORMAL) then
      # 一般子時
      et = Xdate.Hour2ETime(eh)
      ey,em,ed,el,et = Xdate.NextEDateFromTime(ey,em,ed,el,et)
    elsif (@par_zi_shi == Star::PAN_ZI_SHI_DIFF) then
      # 分早晚子時
      et = Xdate.Hour2ETime(eh)
      if (et == 12) then
        et = 0
      end      
    end
    return ey,em,ed,el,et
  end

  def getType(nUserType,nDefType)
    if (nUserType == nil) then
      return nDefType
    end
    return nUserType
  end

  def pan_par_hash2var_parameter(hInput)
    @par_YearDisplay = Cfate.HashOrDefault(hInput,Cfate::PAR_PAN_YEAR_DISPLAY,Star::PAR_YEARDISPLAY_DEFAULT)  # 年代顯示
    @par_LeapType = Cfate.HashOrDefault(hInput,Star::PAR_LEAP_TYPE,Star::PAR_LEAPTYPE_DEFAULT) # 閏月排法
    @par_HorseType = Cfate.HashOrDefault(hInput,Star::PAR_HORSE_TYPE,Star::PAR_HORSETYPE_DEFAULT) # 天馬星排法
    @par_12GodType = Cfate.HashOrDefault(hInput,Star::PAR_GOD_TYPE12,Star::PAR_12GODTYPE_DEFAULT) # 十二長生排法
    @par_DocType = Cfate.HashOrDefault(hInput,Star::PAR_DOC_TYPE,Star::PAR_DOCTYPE_DEFAULT) # 博士星排法
    @par_LuType = Cfate.HashOrDefault(hInput,Star::PAR_LU_TYPE,Star::PAR_LUTYPE_DEFAULT) # 祿存星排法顯示
    @par_GSType = Cfate.HashOrDefault(hInput,Star::PAR_GS_TYPE,Star::PAR_GSTYPE_DEFAULT) # 將前、歲前各星排法
    @par_FlowSkyType = Cfate.HashOrDefault(hInput,Star::PAR_FLOWSKY_TYPE,Star::PAR_FLOWSKYTYPE_DEFAULT) # 流盤干支排法
    @par_FlowSkyType_Cald_Flowyear = Cfate.HashOrDefault(hInput,Star::PAR_FLOWSKY_TYPE_CALD_FLOWYEAR,Star::PAR_FLOWSKYTYPE_CALD_FLOWYEAR_DEFAULT) # 流盤干支排法
    @par_SmallSanType = Cfate.HashOrDefault(hInput,Star::PAR_SMALLSAN_TYPE,Star::PAR_SMALLSANTYPE_DEFAULT) # 起小限規則
    @par_GodType = Cfate.HashOrDefault(hInput,Star::PAR_GOD_TYPE,Star::PAR_GODTYPE_DEFAULT) # 斗君排法
    @par_FlowLifeHouseType = Cfate.HashOrDefault(hInput,Star::PAR_FLOWLIFE_HOUSETYPE,Star::PAR_FLOWLIFEHOUSETYPE_DEFAULT) # 流年命宮排法
    @par_GiaKungType = Cfate.HashOrDefault(hInput,Star::PAR_GIAKUNG_TYPE,Star::PAR_GIAKUNGTYPE_DEFAULT) # 截空排法
    @par_FourHuaType = Cfate.HashOrDefault(hInput,Star::PAR_FOURHUA_TYPE,Star::PAR_FOURHUATYPE_DEFAULT) # 四化星排法
    @par_FlowYearHua = Cfate.HashOrDefault(hInput,Star::PAR_FLOWYEAR_HUA,Star::PAR_FLOWYEARHUA_DEFAULT) # 流盤四化顯示
    @par_SsnType = Cfate.HashOrDefault(hInput,Star::PAR_GS_NAME_TYPE,Star::PAR_SSNTYPE_DEFAULT) # 歲前各星名稱
    @par_b3132Type = Cfate.HashOrDefault(hInput,Star::PAR_B3132_TYPE,Star::PAN_B3132_DEFAULT) # 旬中旬空(空亡)
    @par_yuekui = Cfate.HashOrDefault(hInput,Star::PAR_YUEKUI_TYPE,Star::PAR_YUEKUI_DEFAULT) # 天鉞天魁排法
    @par_zi_shi = Cfate.HashOrDefault(hInput,Star::PAR_ZI_SHI_TYPE,Star::PAR_ZI_SHI_DEFAULT) # 子時排盤
    @par_b30Type = Cfate.HashOrDefault(hInput,Star::PAR_B30_TYPE,Star::PAN_B30_DEFAULT) # 截空動盤顯示
    @par_b2425Type = Cfate.HashOrDefault(hInput,Star::PAR_B2425_TYPE,Star::PAN_B2425_DEFAULT) # 魁鉞動盤顯示
    @par_liushi_minggong = Cfate.HashOrDefault(hInput,Star::PAR_LIUSHI_MINGGONG,Star::PAR_LIUSHI_MINGGONG_DEFAULT) # 流時命宮排法

    @par_PanBase  = Cfate.HashOrDefault(hInput,Star::PAR_PAN_BASE,Star::PAR_PANBASE_DEFAULT) #排盤方式
      @par_PanTimeBase  = Cfate.HashOrDefault(hInput,Star::PAR_PAN_TIMEBASE,Star::PAR_PANTIMEBASE_DEFAULT) #流盤取時間的方法
      #@par_FixSky       = getType(hUserType[Star::PAR_FIX_SKY],Star::PAR_FIXSKY_DEFAULT)              # 尋找宮五行的天干是否要隨流盤變動
  end

  def pan_par_hash2var_interface(hInput)
    @int_34Color    = Cfate.HashOrDefault(hInput,Star::INT_34COLOR,Star::INT_34COLOR_DEFAULT)      # 三方四正色彩顯示
    @int_MioWongStyle    = Cfate.HashOrDefault(hInput,Star::INT_MIOWONG_STYLE,Star::INT_MIOWONGSTYLE_DEFAULT)      # 顯示廟旺的方式,一般的文字或林老師的數字或不顯示
    @int_HuaOutDisplay    = Cfate.HashOrDefault(hInput,Star::INT_HUAOUT_DISPLAY,Star::INT_HUAOUT_DISPLAY_DEFAULT)      # 顯示對宮四化
    @int_SelHuaDisplay    = Cfate.HashOrDefault(hInput,Star::INT_SELHUA_DISPLAY,Star::INT_SELHUA_DISPLAY_DEFAULT)      # 顯示自化
    @int_HouseFiveDisplay    = Cfate.HashOrDefault(hInput,Star::INT_HOUSEFIVE_DISPLAY,Star::INT_HOUSEFIVE_DISPLAY_DEFAULT)      # 顯示各宮五行
    @int_FlowTimeDisplay    = Cfate.HashOrDefault(hInput,Star::INT_FLOWTIME_DISPLAY,Star::INT_FLOWTIME_DISPLAY_DEFAULT)      # 顯示命盤時間
    @int_TenDisplay    = Cfate.HashOrDefault(hInput,Star::INT_TEN_DISPLAY,Star::INT_TEN_DISPLAY_DEFAULT)      # 顯示十年大運
    @int_SmallDisplay    = Cfate.HashOrDefault(hInput,Star::INT_SMALL_DISPLAY,Star::INT_SMALL_DISPLAY_DEFAULT)      # 顯示小限時間
    @int_8WordsDisplay    = Cfate.HashOrDefault(hInput,Star::INT_8WORDS_DISPLAY,Star::INT_8WORDS_DISPLAY_DEFAULT)      # 顯示八字時間
    @int_LifeLine    = Cfate.HashOrDefault(hInput,Star::INT_LIFELINE_DISPLAY,Star::INT_LIFELINE_DISPLAY_DEFAULT)      # 標示命宮三方四正
    @int_SmallYearRevert    = Cfate.HashOrDefault(hInput,Star::INT_SMALLYEAR_REVERT_DISPLAY,Star::INT_SMALLYEAR_REVERT_DISPLAY_DEFAULT)    # 小限應期顯示
  end

  def pan_par_hash2var_display(hInput)
    @dis_AStar = Star.HashStarsAssign(Star::DIS_ASTAR,Hash.new,hInput,Star::A_STAR_COUNT,Cfate::PAR_TRUE)   # 甲級星曜設定
    @dis_BStar = Star.HashStarsAssign(Star::DIS_BSTAR,Hash.new,hInput,Star::B_STAR_COUNT,Cfate::PAR_TRUE)   # 乙、丙級星曜設定
    @dis_FlowLYT = Cfate.HashOrDefault(hInput,Star::DIS_FLOWLYT,Star::DIS_FLOWLYT_DEFAULT)   # 流祿，流羊，流陀
    @dis_FlowMa  = Cfate.HashOrDefault(hInput,Star::DIS_FLOWMA,Star::DIS_FLOWMA_DEFAULT)   # 流馬
    @dis_FlowLuan = Cfate.HashOrDefault(hInput,Star::DIS_FLOWLUAN,Star::DIS_FLOWLUAN_DEFAULT)   # 流鸞
    @dis_Doctor = Cfate.HashOrDefault(hInput,Star::DIS_DOCTOR,Star::DIS_DOCTOR_DEFAULT)  # 顯示博士十二星
    @dis_God = Cfate.HashOrDefault(hInput,Star::DIS_LIVE,Star::DIS_LIVE_DEFAULT)   # 顯示長生十二星
    @dis_YearStar = Cfate.HashOrDefault(hInput,Star::DIS_YEARSTAR,Star::DIS_YEARSTAR_DEFAULT)   # 顯示將星諸星
    @dis_YearGod = Cfate.HashOrDefault(hInput,Star::DIS_YEARGOD,Star::DIS_YEARGOD_DEFAULT)   # 顯示歲前諸星
    @dis_FlowChan  = Cfate.HashOrDefault(hInput,Star::DIS_FLOWCHAN,Star::DIS_FLOWCHAN_DEFAULT)      # 顯示流昌
    @dis_SkyCook   = Cfate.HashOrDefault(hInput,Star::DIS_SKYCOOK,Star::DIS_SKYCOOK_DEFAULT)      # 顯示天廚星
    @dis_LYYG    = Cfate.HashOrDefault(hInput,Star::DIS_LYYG,Star::DIS_LYYG_DEFAULT)      # 顯示來因業障
    @dis_7Star = Cfate.HashOrDefault(hInput,Star::DIS_7STAR,Star::DIS_7STAR_DEFAULT)   # 七星
  end

  def Star.HashStarsAssign(sKey,hUserType,hInput,nCount,bDefault)
    (1..nCount).each do |nStar|
      sFunc = "hUserType['#{sKey}_#{nStar}'] = Cfate.HashOrDefault(hInput,'#{sKey}_#{nStar}',#{bDefault})"
      eval(sFunc)
    end
    return hUserType
  end
  def Star.HashStarsAssign2(sKey,hUserType,hInput,nCount,bDefault)
    (1..nCount).each do |nStar|
      sFunc = "hUserType['#{sKey}_#{nStar}'] = Cfate.HashOrDefault2(hInput,'#{sKey}_#{nStar}',#{bDefault})"
      eval(sFunc)
    end
    return hUserType
  end

  def pan_par_hash2var_privatefourhua(hInput)
    @m_dwHua_User = Array.new(10) {Array.new(4,nil)}
    # 客戶自訂四化，預設用欽天派
    (Sky::SKY_FIRST..Sky::SKY_LAST).each do |nSky|
      (Star::FH_LU..Star::FH_GI).each do |nFourHuaIndex|
        nSkyIndex = Sky.Sky2SkyIndex(nSky)
        @m_dwHua_User[nSkyIndex][nFourHuaIndex] = Cfate.HashOrDefault(hInput,Star.PrivateFourHua_HashName(nSky,nFourHuaIndex),Star.GetDefaultFourHuaStarValue(Star::PAN_FH_STEVEN,nSky,nFourHuaIndex))
      end
    end
  end

  def pan_par_hash2var_hallnumber(hInput)
    @hall_number1 = Cfate.Hash2Str(hInput,Cfate::HALL_NUMBER1,"") #Star.gPan_CI_Data1())
    @hall_number2 = Cfate.Hash2Str(hInput,Cfate::HALL_NUMBER2,"") #Star.gPan_CI_Data2())
    @hall_number3 = Cfate.Hash2Str(hInput,Cfate::HALL_NUMBER3,"") #Star.gPan_CI_Data3())

    @adv_hall_number1 = Cfate.Hash2Str(hInput,Cfate::ADV_HALL_NUMBER_1,"")
    @adv_hall_number2 = Cfate.Hash2Str(hInput,Cfate::ADV_HALL_NUMBER_2,"")
    @adv_hall_number3 = Cfate.Hash2Str(hInput,Cfate::ADV_HALL_NUMBER_3,"")
  end

  def pan_par_hash2var_print(hInput)
    @print_time_page = Cfate.HashBoolCheck(hInput,Cfate::PAR_PRINT_TIME_PAGE,false)
    @print_tp_type = Cfate.HashOrDefault(hInput,Cfate::PAR_PRINT_TIME_PAGE_TYPE,Cfate::PRINT_TP_INONE)
    @print_tp_three_1 = Cfate.HashBoolCheck(hInput,Cfate::PAR_PRINT_TIME_PAGE_THREE_1,false)
    @print_tp_three_2 = Cfate.HashBoolCheck(hInput,Cfate::PAR_PRINT_TIME_PAGE_THREE_2,false)
    @print_tp_three_3 = Cfate.HashBoolCheck(hInput,Cfate::PAR_PRINT_TIME_PAGE_THREE_3,false)
    @print_pan_title = Cfate.Hash2Str(hInput,Cfate::PAR_PRINT_PAN_HEADER,"")
    @print_time_footer = Cfate.Hash2Str(hInput,Cfate::PAR_PRINT_SET_FOOTER,"")
  end

  # def Star.GetStarVersion(sApVer)
  #   hUserApInfo = Hash.new
  #   if (sApVer == nil) then
  #     sVer = "A0"
  #   elsif (sApVer == "") then
  #     sVer = "A0"
  #   else
  #     sVer = sApVer
  #   end
  #   hUserApInfo["StarVersion"] = sVer
  #   return hUserApInfo
  # end
  FUNC_SET_PARS = "SetStarPars"
    @@hApFuncDefault = { "n_SkyFindStar_house" => 273, #    沒註冊免費版本，紫微干找星及論斷，僅提供 命，財，官
               "n_StarFindSky_house" => 0,
               "GiSuongEnable" => false, # 論斷
               Cfate::FUNC_HALL_NUMBER => false, # 論斷
               "explain" => false, # 論斷
               "n_explain_pan" => Cfate::PAN_NORMAL, # 沒註冊可看本命之論斷
               "n_explain_content" => Star::EX_C_DEFAULT, # 1
               "n_explain_house" => Star::EX_C_HOUSE_UNREGISTER, # 273
               "n_explain_free_pan" => Cfate::PAN_NORMAL, # 沒註冊可看本命之論斷
               "n_explain_free_content" => Star::EX_C_DEFAULT, # 1
               "n_explain_free_house" => Star::EX_C_HOUSE_UNREGISTER, # 273
               "n_gift_days" => 0 # 優惠天數
              }

  def Star.product_func_par_assign(hdbInput,hDefault)
    hApFunc = Hash.new
    (Cfate::PAN_NORMAL..Cfate::PAN_FLOWMIN).each do |nPanType|
      hApFunc[Cfate.gGetPanTypeHashKey(nPanType)] = Cfate.HashBoolCheck(hdbInput,Cfate.gGetPanTypeHashKey(nPanType),hDefault[Cfate.gGetPanTypeHashKey(nPanType)])
    end
    hApFunc["LYYGDisplay"] = Cfate.HashBoolCheck(hdbInput,"LYYGDisplay",hDefault["LYYGDisplay"])
    hApFunc["Star7Display"] = Cfate.HashBoolCheck(hdbInput,"Star7Display",hDefault["Star7Display"])
    hApFunc[Star::FUNC_SET_PARS] = Cfate.HashBoolCheck(hdbInput,Star::FUNC_SET_PARS,hDefault[Star::FUNC_SET_PARS])
    hApFunc["GanZhiBirthDisplay"] = Cfate.HashBoolCheck(hdbInput,"GanZhiBirthDisplay",hDefault["GanZhiBirthDisplay"])
    hApFunc["SmallYearRevertDisplay"] = Cfate.HashBoolCheck(hdbInput,"SmallYearRevertDisplay",hDefault["SmallYearRevertDisplay"])
    hApFunc["n_StarFindSky_house"] = Cfate.ParInHashValueCheck(hdbInput,"n_StarFindSky_house",@@hApFuncDefault)
    hApFunc["n_SkyFindStar_house"] = Cfate.ParInHashValueCheck(hdbInput,"n_SkyFindStar_house",@@hApFuncDefault)
    hApFunc["SelfFourHua"] = Cfate.HashBoolCheck(hdbInput,"SelfFourHua",hDefault["SelfFourHua"])
    hApFunc["FlyoutFourHua"] = Cfate.HashBoolCheck(hdbInput,"FlyoutFourHua",hDefault["FlyoutFourHua"])
    hApFunc["LastPanType"] = Cfate.HashOrDefault(hdbInput,"LastPanType",hDefault["LastPanType"])
    hApFunc["LastXdatePanType"] = Cfate.HashOrDefault(hdbInput,"LastXdatePanType",hDefault["LastXdatePanType"])
    hApFunc["showXdate"] = Cfate.HashBoolCheck(hdbInput,"showXdate",hDefault["showXdate"])
    hApFunc["GiSuongEnable"] = Cfate.HashBoolCheck(hdbInput,"GiSuongEnable",@@hApFuncDefault["GiSuongEnable"])
    hApFunc[Cfate::FUNC_HALL_NUMBER] = Cfate.HashBoolCheck(hdbInput,Cfate::FUNC_HALL_NUMBER,@@hApFuncDefault[Cfate::FUNC_HALL_NUMBER])
    hApFunc["explain"] = Cfate.HashBoolCheck(hdbInput,"explain",@@hApFuncDefault["explain"])
    hApFunc["n_explain_pan"] = Cfate.ParInHashValueCheck(hdbInput,"n_explain_pan",@@hApFuncDefault)
    hApFunc["n_explain_content"] = Cfate.ParInHashValueCheck(hdbInput,"n_explain_content",@@hApFuncDefault)
    hApFunc["n_explain_house"] = Cfate.ParInHashValueCheck(hdbInput,"n_explain_house",@@hApFuncDefault)
    hApFunc["n_explain_free_pan"] = Cfate.ParInHashValueCheck(hdbInput,"n_explain_free_pan",@@hApFuncDefault)
    hApFunc["n_explain_free_content"] = Cfate.ParInHashValueCheck(hdbInput,"n_explain_free_content",@@hApFuncDefault)
    hApFunc["n_explain_free_house"] = Cfate.ParInHashValueCheck(hdbInput,"n_explain_free_house",@@hApFuncDefault)
    hApFunc["n_gift_days"] = Cfate.ParInHashValueCheck(hdbInput,"n_gift_days",@@hApFuncDefault)

    return hApFunc
  end

  def Star.func_assign(hInput)
    hOutput = Hash.new
    hInput.each_pair {|key, value| hOutput[key] = Cfate.ValueBoolCheck(value) }
    hOutput["n_explain_pan"] = Cfate.ParInHashValueCheck(hInput,"n_explain_pan",@@hApFuncDefault)
    hOutput["n_explain_content"] = Cfate.ParInHashValueCheck(hInput,"n_explain_content",@@hApFuncDefault)
    hOutput["n_explain_house"] = Cfate.ParInHashValueCheck(hInput,"n_explain_house",@@hApFuncDefault)
    hOutput["n_explain_free_pan"] = Cfate.ParInHashValueCheck(hInput,"n_explain_free_pan",@@hApFuncDefault)
    hOutput["n_explain_free_content"] = Cfate.ParInHashValueCheck(hInput,"n_explain_free_content",@@hApFuncDefault)
    hOutput["n_explain_free_house"] = Cfate.ParInHashValueCheck(hInput,"n_explain_free_house",@@hApFuncDefault)
    hOutput["n_gift_days"] = Cfate.ParInHashValueCheck(hInput,"n_gift_days",@@hApFuncDefault)
    return hOutput
  end

  def IsSelFourHuaStack?()
    return (@par_FlowYearHua == Star::PAN_YEAR_HUA_STACK)
  end
  def IsMioWongNum?()
    return (int_MioWongStyle == Star::INT_MW_NUM)
  end

end
