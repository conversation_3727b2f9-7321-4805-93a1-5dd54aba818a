require("Divination_explain_jiankang.rb")
require("Divination_explain_qiucai.rb")
require("Divination_explain_ganqing.rb")
require("Divination_explain_zhichang.rb")

class Divination
  Jiankang = "jiankang" # 健康
  Qiucai = "qiucai"  # 求財
  Nuweihun = "nuweihun"  # 女未婚
  Nanweihun = "nanweihun" # 男未婚 
  Nuyihun = "nuyihun" # 女已婚 
  Nanyihun = "nanyihun" # 男已婚 
  ZhiChang = "zhichang" # 職場
  # explain
  # gua_bie 卦別
  def explain(gua_bie)
    if (gua_bie == Divination::Jiankang) then
      return explain_jiankang()
    elsif (gua_bie == Divination::Qiucai) then
      return explain_qiucai()
    elsif (gua_bie == Divination::<PERSON><PERSON><PERSON><PERSON>) then
      return explain_zhichang()
    else
      if ([Divination::Nuweihun,Divination::Nanwei<PERSON>,Divination::<PERSON>uy<PERSON><PERSON>,Divination::<PERSON><PERSON><PERSON>].index(gua_bie) != nil) then
        return explain_ganqing(gua_bie)
      else
        return explain_jiankang()
      end
    end
  end

  def Divination.semicolon()
    return Divination.liuyaogua_str("semicolon")
  end
  def Divination.tiangan_key(tiangan)
    return "s#{tiangan}"
  end
  def Divination.dizhi_key(dizhi)
    return "e#{dizhi}"
  end
  def Divination.dizhi_keys(dz1,dz2=nil,dz3=nil)
    s = Divination.dizhi_key(dz1)
    if (dz2 == nil) then
      return s
    end
    s += "_" 
    s += Divination.dizhi_key(dz2)
    if (dz3 == nil) then
      return s
    end
    s += "_" 
    s += Divination.dizhi_key(dz3)
    return s
  end
  def Divination.extract_str(s,n,sep="***")
    a = s.split(sep)
    return a[n - 1]
  end
  # 六親
  # Liu_Qin_1 = 1 # 父母
  # Liu_Qin_2 = 2 # 兄弟
  # Liu_Qin_3 = 3 # 官鬼
  # Liu_Qin_4 = 4 # 妻財
  # Liu_Qin_5 = 5 # 子孫
  # 但是傳入的字串s的擺放順序是根據高先生的Excel的順序，父母，兄弟，子孫，妻財，官鬼
  # 所以要改變存取位置
  def Divination.extract_liuqin_str(s,liu_qin,sep="***")
    liu_qin_pos = [-1,1,2,5,4,3].index(liu_qin)
    return Divination.extract_str(s,liu_qin_pos,sep)
  end
  def Divination.liu_shou_key(liu_shou)
    return "b#{liu_shou}"
  end
  def Divination.liu_qin_key(liuqin)
    return "r#{liuqin}"
  end
  def Divination.wang_xiang_xiu_qiu_si_key(wang_xiang_xiu_qiu_si)
    return "fl#{wang_xiang_xiu_qiu_si}"
  end
  def Divination.yao_key(yao)
    return "y#{yao}"
  end
  def Divination.bool_key(tf)
    return tf ? "true" : "false"
  end

  @@Explain_San_He = [
                [9,1,5],
                [6,10,2],
                [3,7,11],
                [12,4,8]
              ]
  def Divination.explain_san_he(date_dizhi,all_ylqgzws,liu_shous)
    # ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(all_ylqgzws)
    par_all_yao_di_zhis = Divination.par_all_yao_di_zhis(all_ylqgzws)

    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)

    all_dizhi = date_dizhi + par_all_yao_di_zhis
    a2 = all_dizhi.uniq
    @@Explain_San_He.each_index do |i|
      a = @@Explain_San_He[i]
      a3 = a.select { |n| a2.index(n) != nil  } 
      if (a3 == a) then
        # 以三合中間的地支來判斷在六爻中的位置
        san_he_middle = a[1]
        ylqgzw = Divination.explain_find_ylqgzw_by_dizhi(san_he_middle,ylqgzws)
        if (ylqgzw != nil) then
          yao = Divination.fu_shen_yao(ylqgzw)
          liu_shou = Divination.liu_shou(liu_shous,yao)
          liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
          return a,liu_shou,liu_qin
        end
      end
    end
    return nil,0,0
  end

  def Divination.explain_merge_content(a)
    a.flatten! 
    a.delete("")
    return a.join("\n")
  end

  @@Explain_San_Xing = [
                [3,6,9],
                [2,11,8]
              ]
  # def Divination.explain_san_xing_check(date_dizhi,ben_gua_di_zhi)
  def Divination.explain_san_xing_check(date_dizhi,all_ylqgzws)
    par_all_yao_di_zhis = Divination.par_all_yao_di_zhis(all_ylqgzws)

    # dizhis = date_dizhi + ben_gua_di_zhi
    dizhis = date_dizhi + par_all_yao_di_zhis
    a2 = dizhis.uniq
    @@Explain_San_Xing.each_index do |i|
      a = @@Explain_San_Xing[i]
      a3 = a.select { |n| a2.index(n) != nil  } 
      if (a3 == a) then
        # 靜爻三刑不計算,其他動爻或是動與靜爻其中有即是。
        if (Divination.has_same_item(date_dizhi,a)) then
          return a
        end
      end
    end
    return nil
  end
  def Divination.explain_find_ylqgzw_by_dizhi(di_zhi,ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,di_zhi)
    if (ylqgzw == nil) then
      wu_xing = Divination.Earth_Five(di_zhi)
      ylqgzw = Divination.par_find_ylqgzw_by_wu_xing(ylqgzws,wu_xing)
    end
    return ylqgzw
  end

end
