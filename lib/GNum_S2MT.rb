# coding: UTF-8

class GNum

# 簡化字：傳統漢字：字義辨析：例詞
  @@s_2_t_map = {
	"划" => "劃,畫,划",
	"卤" => "滷,鹵",
	"历" => "歷,曆",
	"发" => "發,髮",
	"只" => "只,隻",
	"台" => "臺,檯,颱,台",
	"后" => "後,后",
	"坛" => "壇,罈",
	"复" => "復,複,覆",
	"尽" => "盡,儘",
	"干" => "幹,乾,干,榦",
	"并" => "並,併,并",
	"当" => "當,噹",
	"志" => "志,誌",
	"汇" => "匯,彙",
	"系" => "系,係,繫",
	"脏" => "髒,臟",
	"荡" => "蕩,盪",
	"获" => "獲,穫",
	"采" => "採,采",
	"里" => "裏,里",
	"钟" => "鍾,鐘",
	"饥" => "飢,饑",
	"丰" => "豐,丰",
	"丑" => "醜,丑",
	"了" => "了,瞭",
	"借" => "借,藉",
	"克" => "克,剋",
	"准" => "準,准",
	"刮" => "刮,颳",
	"制" => "制,製",
	"吁" => "籲,吁",
	"吊" => "吊,弔",
	"团" => "團,糰",
	"困" => "困,睏",
	"布" => "佈,布",
	"御" => "御,禦",
	"斗" => "鬭,斗",
	"曲" => "曲,麯",
	"松" => "鬆,松",
	"淀" => "澱,淀",
	"纤" => "纖,縴",
	"致" => "致,緻",
	"蔑" => "蔑,衊",
	"仇" => "仇,讎",
	"冬" => "冬,鼕",
	"咸" => "咸,鹹",
	"云" => "雲,云",
	"仆" => "僕,仆",
	"舍" => "舍,捨",
	"签" => "籤,簽",
	"折" => "折,摺",
	"谷" => "谷,穀",
	"几" => "幾,几",
	"辟" => "闢,辟",
	"奸" => "奸,姦",
	"游" => "遊,游",
	"佣" => "傭,佣",
	"苏" => "蘇,囌,甦",
	"回" => "回,迴",
	"面" => "面,麪",
	"向" => "向,嚮,曏",
	"伙" => "夥,伙",
	"郁" => "鬱,郁",
	"朴" => "樸,朴",
	"才" => "才,纔",
	"朱" => "朱,硃",
	"别" => "別,彆",
	"卷" => "捲,卷",
	"蒙" => "蒙,矇,濛,懞",
	"征" => "徵,征",
	"症" => "症,癥",
	"恶" => "惡,噁",
	"注" => "注,註",
	"哄" => "哄,鬨",
	"参" => "參,蔘",
	"腌" => "醃,腌",
	"彩" => "彩,綵",
	"占" => "佔,占",
	"欲" => "欲,慾",
	"扎" => "扎,紮",
	"熏" => "熏,燻",
	"赞" => "贊,讚",
	"尝" => "嘗,嚐",
	"烟" => "煙,菸",
	"周" => "周,週,賙",
	"柜" => "櫃,柜",
	"喂" => "餵,喂",
	"幸" => "幸,倖",
	"凶" => "兇,凶",
	"杰" => "傑,杰",
	"针" => "針,鍼",
	"戚" => "戚,慼,鏚",
	"托" => "托,託",
	"挨" => "挨,捱",
	"挽" => "挽,輓",
	"栗" => "慄,栗",
	"炼" => "煉,鍊",
	"链" => "鏈,鍊",
	"穗" => "穗,繐",
	"雕" => "彫,鵰",
	"梁" => "樑,梁",
	"升" => "升,昇",
	"摆" => "擺,襬",
	"岩" => "巖,岩",
	"娘" => "娘,孃",
	"僵" => "僵,殭",
	"药" => "藥,葯",
	"余" => "餘,余",
	"蜡" => "蠟,蜡",
	"出" => "出,齣",
	"卜" => "卜,蔔",
	"同" => "同,衕",
	"板" => "板,闆",
	"漓" => "漓,灕",
	"术" => "術,朮",
	"仑" => "侖,崙",
	"秋" => "秋,鞦",
	"千" => "千,韆",
	"帘" => "簾,帘",
	"庵" => "庵,菴",
	"尸" => "屍,尸",
	"胡" => "胡,衚,鬍",
	"须" => "須,鬚",
	"据" => "據,据",
	"筑" => "築,筑",
	"夸" => "誇,夸",
	"苹" => "蘋,苹",
	"袅" => "裊,嫋",
	"暗" => "暗,闇",
	"冲" => "衝,沖,冲",
	"表" => "表,錶",
	"杆" => "杆,桿",
	"鉴" => "鑒,鑑",
	"搜" => "搜,蒐",
	"杯" => "杯,盃",
	"铲" => "剷,鏟",
	"扣" => "扣,釦",
	"念" => "念,唸",
	"杠" => "杠,槓",
	"泛" => "泛,氾",
	"核" => "核,覈",
	"巨" => "巨,鉅",
	"叹" => "嘆,歎",
	"价" => "價,价",
	"私" => "私,俬",
	"局" => "局,侷",
	"拐" => "拐,柺",
	"弦" => "弦,絃",
	"哗" => "譁,嘩",
	"凄" => "悽,淒",
	"家" => "家,傢",
	"席" => "席,蓆",
	"酸" => "酸,痠",
	"噪" => "噪,譟",
	"咽" => "咽,嚥",
	"愈" => "愈,癒",
	"凌" => "凌,淩",
	"毁" => "毀,譭,燬",
	"苔" => "苔,薹",
	"糊" => "糊,餬",
	"抵" => "抵,牴",
	"恤" => "恤,卹",
	"荫" => "蔭,廕",
	"皂" => "皁,皂",
	"芸" => "芸,蕓",
	"背" => "背,揹",
	"夫" => "夫,伕",
	"迹" => "蹟,跡",
	"录" => "錄,彔",
	"极" => "極,极",
	"愿" => "願,愿",
	"胜" => "勝,胜",
	"确" => "確,确",
	"叶" => "葉,叶",
	"虫" => "蟲,虫",
	"厂" => "廠,厂",
	"修" => "修,脩",
	"合" => "合,閤",
	"适" => "適,适",
	"弥" => "彌,瀰",
	"厘" => "釐,厘",
	"涂" => "塗,涂",
	"个" => "個,箇,个",
	"于" => "於,于",
	"党" => "黨,党",
	"种" => "種,种",
	"万" => "萬,万",
	"范" => "範,范",
	"沈" => "瀋,沈",
	"姜" => "姜,薑",
	"闲" => "閒,閑",
	"证" => "證,証",
	"佑" => "佑,祐",
	"谥" => "諡,謚",

	# add by peter
	"涌" => "涌,湧",
	"淼" => "淼,渺",
	"菰" => "菰,菇",
	"愣" => "愣,楞",
	"琅" => "琅,瑯",
	"溷" => "溷,混",
	"噘" => "噘,撅",
	"澹" => "澹,淡",
	"凋" => "凋,雕",
	"硅" => "硅,矽",
	"铺" => "舖,鋪",
	"荐" => "荐,薦",
	"洒" => "洒,灑",
	"晒" => "晒,曬"
  }

  def GNum.simple_2_traditional(word)
  	a = []
  	a_words = @@s_2_t_map[word]
  	if (a_words != nil) then
  		a = a_words.split(",")
  		# a_word.each do |w|
  		# 	p = GNum.find_people(w)
  		# 	a.push(w) if p != nil
  		# end
  	# else
  	# 	p = GNum.find_people(word)
  	# 	a.push(word) if p != nil
  	end
  	a = GNum.find_people_local_array(a)

  	return a
  end
  def GNum.s2t_test()
  	a = GNum.simple_not_in_traditional_test()
  	b = GNum.s2mt_test()
  	c = a + b
  	c.uniq!
  	puts "all: #{c}"
  	puts "length : #{c.length}"
  end
  def GNum.simple_not_in_traditional_test()
  	a = []
  	@@people_simple.keys.each do |word|
  		s = GNum.find_people_local(word)
  		a.push(word) if s == nil
  	end
  	puts "simple_not_in_traditional : #{a}"
  	return a
  end
  def GNum.s2mt_test()
  	b = []
  	@@s_2_t_map.each_pair do |word,m_word|
  		a_word = GNum.simple_2_traditional(word)
  		b = b + (m_word.split(",") - a_word)
  		# puts "#{word} : #{a_word} : #{a_word - a}" if a.length != a_word.length
  	end
  	puts "simple multi tradional not in trad. : #{b}"
  	return b
  end

end
