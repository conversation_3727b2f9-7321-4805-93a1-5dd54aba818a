class Score
  def wa_Result(nPanType,hUserInfo,hUserAskData,hUserType)
    @m_nPanType = nPanType
    @Star = Star.new(hUserInfo,hUserAskData,hUserType)
    # @Star.Score_GetAllScore(nPanType,hUserInfo,hUserAskData)
    # @Star.Score_CalcScore(nPanType,hUserInfo,hUserAskData,nScoreHouse)
    @UserAskData = hUserAskData.clone
    @UserAskData[Cfate::ETime] = @Star.ddg_ET()
    @UserInfo = hUserInfo.clone
    @ScoreHouse = @UserAskData[Star::HOUSE_NAME]
    if (@ScoreHouse == nil) then
      @ScoreHouse = Star::EX_HOUSE_ALL
    end
    @Score = Hash.new
    if (@ScoreHouse == Star::EX_HOUSE_ALL) then
      @Score["score_housename"] = "all"
    else
      nEarth = @Star.g_House_GetEarth(nPanType,@ScoreHouse)
      sHouseName = @Star.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
      @Score["score_housename"] = sHouseName
    end

  	if (nPanType == Cfate::PAN_NORMAL) then
  		return wa_Result_Normal(nPanType,hUserInfo,@UserAskData,hUserType)
  	else
  		return wa_Result_Others(nPanType,hUserInfo,@UserAskData,hUserType)
  	end
  end

  def wa_Result_Normal(nPanType,hUserInfo,hAskData,hUserType)
    @ScoreHouse = Star::EX_HOUSE_ALL

	  scores,title = wa_Star_Create_AllHouse(nPanType)
    
    hScore = Hash.new
    hScore["keys"] = scores.keys
    hScore["values"] = scores.values

    @Score["score"] = hScore
    @Score["score_title"] = title

    return @Score
  end
  def wa_Star_Create_AllHouse(nPanType)
    nEarth = @Star.gHouse_GetFirstHouseEarth(nPanType)

    # 各宮分數總表
    hHouseTotal = Hash.new
    @Star.Score_CalcScore(nPanType,@UserInfo,@UserAskData)

    # 1..12 表示子宮到亥宮
    (1..12).each do |i|
      sHouseName = @Star.gHouse_GetHouseName(nPanType,nEarth)
      sHouseName = sHouseName

      # 指數顯示部份
      nHouseScore = @Star.Score_GetHouseScore(nPanType,nEarth).round(1)
      hHouseTotal[sHouseName] = nHouseScore.round(1).to_s

      # 尋找下一個宮名
      nEarth = @Star.gHouse_GetNextHouseEarth(nEarth)
    end

    return hHouseTotal,"title"
  end


  def wa_Result_Others(nPanType,hUserInfo,hAskData,hUserType)
    scores,title = wa_Star_createScore(nPanType,hUserInfo,hAskData,hUserType)

    hScore = Hash.new
    hScore["keys"] = scores.keys
    hScore["values"] = scores.values

    @Score["scores"] = hScore
    @Score["title"] = title

    return @Score
  end

  def wa_Star_createScore(nPanType,hUserInfo,hAskData,hUserType)
  	if (@ScoreHouse == Star::EX_HOUSE_ALL) then
  	  return wa_Star_Create_AllHouse(nPanType)
  	else
  	  return wa_Star_Create_OneHouse(nPanType,@ScoreHouse,hUserInfo,hAskData,hUserType)
  	end
  end

  def wa_Star_Create_OneHouse(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    case (nPanType)
      when (Cfate::PAN_NORMAL) then
        scores,flow_title = wa_Star_Create_OneHouse_Normal(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)  # PAN_NORMAL
      when (Cfate::PAN_TENYEAR) then
        scores,flow_title = wa_Star_Create_OneHouse_TenYear(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        scores,flow_title = wa_Star_Create_OneHouse_FlowYear(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        scores,flow_title = wa_Star_Create_OneHouse_FlowMonth(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        scores,flow_title = wa_Star_Create_OneHouse_FlowDate(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        scores,flow_title = wa_Star_Create_OneHouse_FlowTime(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        scores,flow_title = Hash.new,""    # PAN_FLOWMIN
    end
    return scores,flow_title
  end

  def wa_Star_Create_OneHouse_Normal(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone
    hUserAskData[Cfate::WHour] = 0

    oStar = @Star

    nNextPanType = nPanType + 1
    # 2、再顯示該本命之各十年流運之該宮資料。
    nStarYear = oStar.gEx_GetLargeYear(0)

    (0..11).each do |i|
      hUserAskData[Cfate::EYear] = nStarYear
      oStar.Score_CalcScore(nNextPanType,hUserInfo,hUserAskData,nScoreHouse)
      nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

      # 指數顯示部份
      nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
      key = "#{nStarYear}~#{nStarYear+9}#{Pm.GetStr("IDS_X_YEAR")}"
      hFlow[key] = nHouseScore.round(1).to_s

      nStarYear += 10
    end
    return hFlow,"Normal"
  end

  def wa_Star_Create_OneHouse_TenYear(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone
    hUserAskData[Cfate::WHour] = 0

    oStar = @Star

    nNextPanType = nPanType + 1
    # 2、再顯示該十年流運之十年之事業宮資料。
    nStarYear = oStar.g_getLargeSanStartYear()

    (0..9).each do |i|
      hUserAskData[Cfate::EYear] = nStarYear + i
      oStar.Score_CalcScore(nNextPanType,hUserInfo,hUserAskData,nScoreHouse)
      nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

      # 指數顯示部份
      nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
      key = Xdate.GetYearStr(hUserAskData[Cfate::EYear])
      hFlow[key] = nHouseScore.round(1).to_s
    end
    return hFlow,"#{nStarYear}~#{nStarYear + 9}"
  end

  def wa_Star_Create_OneHouse_FlowYear(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone
    hUserAskData[Cfate::WHour] = 0

    nNextPanType = nPanType + 1

    oStar = @Star
    mArray = Xdate.GetEastMonthArray(hUserAskData[Cfate::EYear])
    mArray.each do |aMonth|
      hUserAskData[Cfate::EMonth] = aMonth[0]
      hUserAskData[Cfate::LeapMonth] = aMonth[2]

      oStar.Score_CalcScore(nNextPanType,hUserInfo,hUserAskData)
      nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

      key = Xdate.GetMonthStr(hUserAskData[Cfate::EMonth],hUserAskData[Cfate::LeapMonth])

      # 指數顯示部份
      nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
      hFlow[key] = nHouseScore.round(1).to_s
    end
    # return hFlow,Xdate.GetYearStr(@Star.ddg_EY())
    return hFlow,Xdate.GetYearStr(oStar.fdg_EY(nPanType))
  end


  def wa_Star_Create_OneHouse_FlowMonth(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone
    hUserAskData[Cfate::WHour] = 0

    nNextPanType = nPanType + 1

    oStar = @Star
    nEYear = hUserAskData[Cfate::EYear]
    nEMonth = hUserAskData[Cfate::EMonth]
    bLeapMonth = hUserAskData[Cfate::LeapMonth]
    nDays = Xdate.GetEastMonthDays(nEYear, nEMonth, bLeapMonth)
    # flow_title = Xdate.GetYearStr(@Star.ddg_EY()) + Xdate.GetMonthStr(@Star.ddg_EM(),@Star.ddg_EL())
    flow_title = Xdate.GetYearStr(oStar.fdg_EY(nPanType)) + Xdate.GetMonthStr(oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType))

    nDays = hUserAskData["CurrentDate"] ? 1 : nDays

    (1..nDays).each do |i|
      hUserAskData[Cfate::EDate] = i if nDays != 1

      oStar.Score_CalcScore(nNextPanType,hUserInfo,hUserAskData,nScoreHouse)
      nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

      key = Xdate.GetDateStr(hUserAskData[Cfate::EDate])

      # 指數顯示部份
      nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
      hFlow[key] = nHouseScore.round(1).to_s
    end
    return hFlow,flow_title
  end

  def wa_Star_Create_OneHouse_FlowDate(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone

    nNextPanType = nPanType + 1

    oStar = @Star

    # flow_title = Xdate.GetYearStr(@Star.ddg_EY()) + Xdate.GetMonthStr(@Star.ddg_EM(),@Star.ddg_EL())
    # flow_title += Xdate.GetDateStr(@Star.ddg_ED())
    flow_title = Xdate.GetYearStr(oStar.fdg_EY(nPanType)) + Xdate.GetMonthStr(oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType))
    flow_title += Xdate.GetDateStr(oStar.fdg_ED(nPanType))

    (1..12).each do |i|
      hUserAskData[Cfate::ETime] = i - 1
      nWHour = Xdate.ETime2PanHour(i - 1)
      hUserAskData[Cfate::WHour] = nWHour

      oStar.Score_CalcScore(nNextPanType,hUserInfo,hUserAskData,nScoreHouse)
      nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

      key = Xdate.GetETimeStr(hUserAskData[Cfate::ETime])
      # 指數顯示部份
      nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
      hFlow[key] = nHouseScore.round(1).to_s
    end

    return hFlow,flow_title
  end

  def wa_Star_Create_OneHouse_FlowTime(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone

    nNextPanType = nPanType + 1

    oStar = @Star
    # flow_title = Xdate.GetYearStr(@Star.ddg_EY()) + Xdate.GetMonthStr(@Star.ddg_EM(),@Star.ddg_EL())
    # flow_title += Xdate.GetDateStr(@Star.ddg_ED())
    # flow_title += Xdate.GetETimeStr(@Star.ddg_ET())
    flow_title = Xdate.GetYearStr(oStar.fdg_EY(nPanType)) + Xdate.GetMonthStr(oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType))
    flow_title += Xdate.GetDateStr(oStar.fdg_ED(nPanType))
    flow_title += Xdate.GetETimeStr(oStar.fdg_ET(nPanType))

    nETime = hUserAskData[Cfate::ETime] # oStar.ddg_ET() 
    hUserAskData[Cfate::WHour] = Xdate.ETimeFirstHour(nETime)
    # nEYear,nEMonth,nEDate,bLeapMonth = hUserAskData[Cfate::EYear],hUserAskData[Cfate::EMonth],hUserAskData[Cfate::EDate],hUserAskData[Cfate::LeapMonth]
    # if (hUserAskData[Cfate::WHour] == 23) then
    #   nEYear,nEMonth,nEDate,bLeapMonth = Xdate.PreEDate(nEYear,nEMonth,nEDate,bLeapMonth)
    # end
    # hUserAskData[Cfate::EYear],hUserAskData[Cfate::EMonth],hUserAskData[Cfate::EDate],hUserAskData[Cfate::LeapMonth] = nEYear,nEMonth,nEDate,bLeapMonth
    (1..12).each do |i|
      hUserAskData[Cfate::EMinute] = i - 1
      hUserAskData[Cfate::WMinute] = ((i - 1) * 10) % 60
      hUserAskData[Cfate::WHour] += 1 if i == 7

      nEYear,nEMonth,nEDate,bLeapMonth = hUserAskData[Cfate::EYear],hUserAskData[Cfate::EMonth],hUserAskData[Cfate::EDate],hUserAskData[Cfate::LeapMonth]
      if (hUserAskData[Cfate::WHour] == 24) then
        hUserAskData[Cfate::WHour] = 0
        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      end
      hUserAskData[Cfate::EYear],hUserAskData[Cfate::EMonth],hUserAskData[Cfate::EDate],hUserAskData[Cfate::LeapMonth] = nEYear,nEMonth,nEDate,bLeapMonth

      oStar.Score_CalcScore(nNextPanType,hUserInfo,hUserAskData,nScoreHouse)
      nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

      key = Xdate.GetEMinRangeStr(oStar.fdg_ET(nNextPanType),oStar.fdg_EMI(nNextPanType))

      # 指數顯示部份
      nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)

      hFlow[key] = nHouseScore.round(1).to_s
    end
    return hFlow,flow_title
  end

  # explode 調整＋-8
  def wa_explode(nPanType,hUserInfo,hUserAskData,hUserType)
    @m_nPanType = nPanType
    @Star = Star.new(hUserInfo,hUserAskData,hUserType)
    # @Star.Score_GetAllScore(nPanType,hUserInfo,hUserAskData)
    # @Star.Score_CalcScore(nPanType,hUserInfo,hUserAskData)
    
    @UserAskData = hUserAskData.clone
    @UserAskData[Cfate::ETime] = @Star.ddg_ET()
    @UserInfo = hUserInfo.clone
    @ScoreHouse = @UserAskData[Star::HOUSE_NAME]
    if (@ScoreHouse == Star::EX_HOUSE_ALL || @ScoreHouse == nil) then
      return Hash.new
    end
    @Score = Hash.new
    nEarth = @Star.g_House_GetEarth(nPanType,@ScoreHouse)
    sHouseName = @Star.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
    @Score["base8_housename"] = sHouseName

    return wa_explode_Others(nPanType,@UserInfo,@UserAskData,hUserType)
  end
  def wa_explode_Others(nPanType,hUserInfo,hAskData,hUserType)
    scores,title = wa_Star_explode_OneHouse(nPanType,@ScoreHouse,hUserInfo,hAskData,hUserType)

    h = Hash.new
    scores["keys"].each_index do |i|
      h[scores["keys"][i]] = scores["values"][i]
    end
    scores.delete("keys")
    scores.delete("values")
    @Score["base8_title"] = title
    @Score["base8"] = h
    @Score["base8_scores"] = scores

    return @Score
  end
  def wa_Star_explode_OneHouse(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    case (nPanType)
      when (Cfate::PAN_NORMAL) then
        scores,flow_title = wa_Star_explode_OneHouse_Normal(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)  # PAN_NORMAL
      when (Cfate::PAN_TENYEAR) then
        scores,flow_title = wa_Star_explode_OneHouse_TenYear(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        scores,flow_title = wa_Star_explode_OneHouse_FlowYear(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        scores,flow_title = wa_Star_explode_OneHouse_FlowMonth(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        scores,flow_title = wa_Star_explode_OneHouse_FlowDate(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        scores,flow_title = wa_Star_explode_OneHouse_FlowTime(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        scores,flow_title = Hash.new,""    # PAN_FLOWMIN
    end
    h = Hash.new
    h2 = Hash.new
    scores["keys"].each_index do |i|
      h[scores["keys"][i]] = scores["flow_scores_after_formulas"][i]
    end
    scores.delete("flow_scores_after_formulas")
    h2.merge!(scores)
    h2["flow_scores_after_formulas"] = h
    return h2,flow_title
  end
  def wa_Star_explode_OneHouse_Normal(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone

    # 找出本命該宮的分數
    all_scores,all_title = wa_Star_Create_AllHouse(nPanType)
    score = all_scores.values[nScoreHouse - 1].to_f
    
    # 找出各十年該宮的分數
    flow_scores,flow_title = wa_Star_Create_OneHouse_Normal(nPanType,nScoreHouse,hUserInfo,hUserAskData,hUserType)  # PAN_TENYEAR
    hFlow["keys"] = flow_scores.keys

    scores_2 = flow_scores.values

    # 算出最大3個平均值g
    g = wa_Star_explode_find_large3_avg(scores_2)

    # 各流年盤該宮的修正分數
    scores_3 = wa_Star_explode_formulas(scores_2,g) 

    # 本命盤該宮的修正分數
    score_1 = wa_Star_explode_formula(score,g)  

    # 依比例算出最後的分數
    hFlow["values"] = scores_3.collect { |x| x = wa_Star_explode_formula2(score_1,0.3,x,0.7) } 
 
    hFlow["nScoreHouse"] = nScoreHouse
    hFlow["all_scores"] = all_scores
    hFlow["all_title"] = all_title
    hFlow["score"] = score
    hFlow["flow_scores"] = flow_scores
    hFlow["g"] = g
    hFlow["flow_scores_after_formulas"] = scores_3
    hFlow["score_after_formulas"] = score_1
    return hFlow,flow_title
  end  
  def wa_Star_explode_OneHouse_TenYear(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone

    # 找出十年該宮的分數
    all_scores,all_title = wa_Star_Create_AllHouse(nPanType)
    score = all_scores.values[nScoreHouse - 1].to_f
    
    # 找出各流年該宮的分數
    flow_scores,flow_title = wa_Star_Create_OneHouse_TenYear(nPanType,nScoreHouse,hUserInfo,hUserAskData,hUserType)  # PAN_TENYEAR
    hFlow["keys"] = flow_scores.keys

    scores_2 = flow_scores.values

    # 算出最大3個平均值g
    g = wa_Star_explode_find_large3_avg(scores_2)

    # 各流年盤該宮的修正分數
    scores_3 = wa_Star_explode_formulas(scores_2,g) 

    # 十年盤該宮的修正分數
    score_1 = wa_Star_explode_formula(score,g)  

    # 依比例算出最後的分數
    hFlow["values"] = scores_3.collect { |x| x = wa_Star_explode_formula2(score_1,0.3,x,0.7) } 
 
    hFlow["nScoreHouse"] = nScoreHouse
    hFlow["all_scores"] = all_scores
    hFlow["all_title"] = all_title
    hFlow["score"] = score
    hFlow["flow_scores"] = flow_scores
    hFlow["g"] = g
    hFlow["flow_scores_after_formulas"] = scores_3
    hFlow["score_after_formulas"] = score_1
    return hFlow,flow_title
  end
  def wa_Star_explode_OneHouse_FlowYear(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone

    # 找出流年該宮的分數
    all_scores,all_title = wa_Star_Create_AllHouse(nPanType)
    score = all_scores.values[nScoreHouse - 1].to_f
    
    # 找出各流月該宮的分數
    flow_scores,flow_title = wa_Star_Create_OneHouse_FlowYear(nPanType,nScoreHouse,hUserInfo,hUserAskData,hUserType)  # PAN_TENYEAR
    hFlow["keys"] = flow_scores.keys

    scores_2 = flow_scores.values

    # 算出最大3個平均值g
    g = wa_Star_explode_find_large3_avg(scores_2)

    # 各流年盤該宮的修正分數
    scores_3 = wa_Star_explode_formulas(scores_2,g) 

    # 十年盤該宮的修正分數
    score_1 = wa_Star_explode_formula(score,g)  

    # 依比例算出最後的分數
    hFlow["values"] = scores_3.collect { |x| x = wa_Star_explode_formula2(score_1,0.3,x,0.7) } 
 
    hFlow["nScoreHouse"] = nScoreHouse
    hFlow["all_scores"] = all_scores
    hFlow["all_title"] = all_title
    hFlow["score"] = score
    hFlow["flow_scores"] = flow_scores
    hFlow["g"] = g
    hFlow["flow_scores_after_formulas"] = scores_3
    hFlow["score_after_formulas"] = score_1
    return hFlow,flow_title
  end
  def wa_Star_explode_OneHouse_FlowMonth(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone

    # 找出流年該宮的分數
    all_scores,all_title = wa_Star_Create_AllHouse(nPanType)
    score = all_scores.values[nScoreHouse - 1].to_f
    
    # 找出各流月該宮的分數
    flow_scores,flow_title = wa_Star_Create_OneHouse_FlowMonth(nPanType,nScoreHouse,hUserInfo,hUserAskData,hUserType)  # PAN_TENYEAR
    hFlow["keys"] = flow_scores.keys

    scores_2 = flow_scores.values

    # 算出最大3個平均值g
    g = wa_Star_explode_find_large3_avg(scores_2)

    # 各流年盤該宮的修正分數
    scores_3 = wa_Star_explode_formulas(scores_2,g) 

    # 十年盤該宮的修正分數
    score_1 = wa_Star_explode_formula(score,g)  

    # 依比例算出最後的分數
    hFlow["values"] = scores_3.collect { |x| x = wa_Star_explode_formula2(score_1,0.3,x,0.7) } 
 
    hFlow["nScoreHouse"] = nScoreHouse
    hFlow["all_scores"] = all_scores
    hFlow["all_title"] = all_title
    hFlow["score"] = score
    hFlow["flow_scores"] = flow_scores
    hFlow["g"] = g
    hFlow["flow_scores_after_formulas"] = scores_3
    hFlow["score_after_formulas"] = score_1
    return hFlow,flow_title
  end
  def wa_Star_explode_OneHouse_FlowDate(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone

    # 找出流年該宮的分數
    all_scores,all_title = wa_Star_Create_AllHouse(nPanType)
    score = all_scores.values[nScoreHouse - 1].to_f
    
    # 找出各流月該宮的分數
    flow_scores,flow_title = wa_Star_Create_OneHouse_FlowDate(nPanType,nScoreHouse,hUserInfo,hUserAskData,hUserType)  # PAN_TENYEAR
    hFlow["keys"] = flow_scores.keys

    scores_2 = flow_scores.values

    # 算出最大3個平均值g
    g = wa_Star_explode_find_large3_avg(scores_2)

    # 各流年盤該宮的修正分數
    scores_3 = wa_Star_explode_formulas(scores_2,g) 

    # 十年盤該宮的修正分數
    score_1 = wa_Star_explode_formula(score,g)  

    # 依比例算出最後的分數
    hFlow["values"] = scores_3.collect { |x| x = wa_Star_explode_formula2(score_1,0.3,x,0.7) } 
 
    hFlow["nScoreHouse"] = nScoreHouse
    hFlow["all_scores"] = all_scores
    hFlow["all_title"] = all_title
    hFlow["score"] = score
    hFlow["flow_scores"] = flow_scores
    hFlow["g"] = g
    hFlow["flow_scores_after_formulas"] = scores_3
    hFlow["score_after_formulas"] = score_1
    return hFlow,flow_title
  end
  def wa_Star_explode_OneHouse_FlowTime(nPanType,nScoreHouse,hUserInfo,hAskData,hUserType)
    hFlow = Hash.new
    hUserAskData = hAskData.clone

    # 找出流年該宮的分數
    all_scores,all_title = wa_Star_Create_AllHouse(nPanType)
    score = all_scores.values[nScoreHouse - 1].to_f
    
    # 找出各流月該宮的分數
    flow_scores,flow_title = wa_Star_Create_OneHouse_FlowTime(nPanType,nScoreHouse,hUserInfo,hUserAskData,hUserType)  # PAN_TENYEAR
    hFlow["keys"] = flow_scores.keys

    scores_2 = flow_scores.values

    # 算出最大3個平均值g
    g = wa_Star_explode_find_large3_avg(scores_2)

    # 各流年盤該宮的修正分數
    scores_3 = wa_Star_explode_formulas(scores_2,g) 

    # 十年盤該宮的修正分數
    score_1 = wa_Star_explode_formula(score,g)  

    # 依比例算出最後的分數
    hFlow["values"] = scores_3.collect { |x| x = wa_Star_explode_formula2(score_1,0.3,x,0.7) } 
 
    hFlow["nScoreHouse"] = nScoreHouse
    hFlow["all_scores"] = all_scores
    hFlow["all_title"] = all_title
    hFlow["score"] = score
    hFlow["flow_scores"] = flow_scores
    hFlow["g"] = g
    hFlow["flow_scores_after_formulas"] = scores_3
    hFlow["score_after_formulas"] = score_1
    return hFlow,flow_title
  end

  def wa_Star_explode_find_large3_avg(scores)
    a = scores.collect { |x| x.to_f.abs } 

    a.sort! { |x,y| y <=> x }

    avg = 0.0
    if (a.length >= 3) then
      a[0,3].each {|x| avg += x }
    else
      avg = a[0] * 3
    end

    return (avg / 3).round(1)
  end
  def wa_Star_explode_formulas(scores,g)
    return scores.collect { |x| x = wa_Star_explode_formula(x,g) } 
  end
  def wa_Star_explode_formula(y,g,max=8)
    n = (y.to_f / g.to_f) * max.to_f
    n = wa_Star_explode_max_check(n,max)
    return n
  end
  def wa_Star_explode_max_check(n,max=8)
    if (n.abs > max) then
      n = n / n.abs * max
    end
    return n.round(1)
  end
  def wa_Star_explode_formula2(n1,r1,n2,r2)
    return wa_Star_explode_max_check(n1 * r1 + n2 * r2)
  end
end
