
 # 付款方式。
class Allpay_ChoosePayment
    NONE = 'NONE'
    # 不指定付款方式。
    ALL = 'ALL'

    # 信用卡付費。
    Credit = 'Credit'
    UnionPay = 'UnionPay'
    # 信用卡 定期定額
    Credit_Period = 'Credit_Period'
     # 網路 ATM。
    WebATM = 'WebATM'

    # 自動櫃員機。
    ATM = 'ATM'

    # 超商代碼。
    CVS = 'CVS'

    # 超商條碼。
    BARCODE = 'BARCODE'

    # 支付寶。
    Alipay = 'Alipay'

    # 財付通。
    Tenpay = 'Tenpay'

    # 儲值消費。
    TopUpUsed = 'TopUpUsed'

    AndroidPay = 'AndroidPay'

    ALLPAY_CP = [Credit,Credit_Period,WebATM,ATM,CVS,BARCODE,UnionPay,Alipay,Tenpay,TopUpUsed,AndroidPay]
    Support_CP = [Credit,WebATM,ATM,CVS,BARCODE,UnionPay,AndroidPay]
    Support_CP_mobile = [Credit,ATM,CVS,UnionPay,AndroidPay]
    def Allpay_ChoosePayment.check_choosepayment(choosepayment)
    	if (ALLPAY_CP.include?(choosepayment)) then
    		return choosepayment
    	else
    		return ALL
    	end
    end
    def Allpay_ChoosePayment.supported_paymenttype(user_agent_string="")
      # ua = AgentOrange::UserAgent.new(user_agent_string)
      # if (!ua.device.is_mobile?) then
        cp = Allpay_ChoosePayment::Support_CP
      # else
        # cp = Allpay_ChoosePayment::Support_CP_mobile
      # end
      a = Array.new
      cp.each do |key|
        a.push({ "Key" => key,"desc" => Controller_Api_Allpay.choosepayment_desc(key)})
      end
      h = {"supported_paymenttype" => a}
      return h
    end
    def Allpay_ChoosePayment.check_choosesubpayment(choosepayment,choosesubpayment)
      if ([ALL,Credit,Credit_Period,UnionPay,Tenpay,Alipay].include?(choosepayment)) then
      	return Allpay_ChooseSubPayment::None
      end
      if (choosepayment == WebATM) then
      	return Allpay_ChooseSubPayment.check_webatm(choosesubpayment)
      end
      if (choosepayment == ATM) then
      	return Allpay_ChooseSubPayment.check_atm(choosesubpayment)
      end
      if (choosepayment == CVS) then
      	return Allpay_ChooseSubPayment.check_cvs(choosesubpayment)
      end
      if (choosepayment == BARCODE) then
      	return Allpay_ChooseSubPayment.check_barcode(choosesubpayment)
      end
      if (choosepayment == TopUpUsed) then
      	return Allpay_ChooseSubPayment.check_topupused(choosesubpayment)
      end
      if (choosepayment == AndroidPay) then
        return Allpay_ChooseSubPayment.check_AndroidPay(choosesubpayment)
      end
      return Allpay_ChooseSubPayment::None
    end
    def Allpay_ChoosePayment.all_choosepayment()
      return ALLPAY_CP
    end
    def Allpay_ChoosePayment.desc(key)
    	return Pm.GetStr("allpay.desc.#{key}")
    end
    def Allpay_ChoosePayment.desc_choosepayment(key)
    	return Allpay_ChoosePayment.desc("choosepayment.#{key}")
    end
    def Allpay_ChoosePayment.all_choosepayment_desc()
      return [
      	       Allpay_ChoosePayment.desc_choosepayment("Credit"),
               Allpay_ChoosePayment.desc_choosepayment("Credit_Period"),
               Allpay_ChoosePayment.desc_choosepayment("WebATM"),
               Allpay_ChoosePayment.desc_choosepayment("ATM"),
               Allpay_ChoosePayment.desc_choosepayment("CVS"),
               Allpay_ChoosePayment.desc_choosepayment("BARCODE"),
               Allpay_ChoosePayment.desc_choosepayment("UnionPay"),
               Allpay_ChoosePayment.desc_choosepayment("Alipay"),
               Allpay_ChoosePayment.desc_choosepayment("Tenpay"),
               Allpay_ChoosePayment.desc_choosepayment("TopUpUsed"),
               Allpay_ChoosePayment.desc_choosepayment("AndroidPay")
             ]
    end
end

class Allpay_date
	def Allpay_date.datetime_now()
      t = Time.now
      s = "%04d/%02d/%02d %02d:%02d:%02d" % [t.year,t.month,t.day,t.hour,t.min,t.sec]
      return s
	end
end

# 付款方式子項目。
class Allpay_ChooseSubPayment

    # 不指定。
    None = ''

    # WebATM 類(001~100)
    # 台新銀行。
    WebATM_TAISHIN = 'TAISHIN'
    # 玉山銀行。
    WebATM_ESUN = 'ESUN'
    # 華南銀行。
    WebATM_HUANAN = 'HUANAN'
    # 台灣銀行。
    WebATM_BOT = 'BOT'
    # 台北富邦。
    WebATM_FUBON = 'FUBON'
    # 中國信託。
    WebATM_CHINATRUST = 'CHINATRUST'
    # 第一銀行。
    WebATM_FIRST = 'FIRST'
    # 國泰世華。
    WebATM_CATHAY = 'CATHAY'
    # 兆豐銀行。
    WebATM_MEGA = 'MEGA'
    # 元大銀行。
    WebATM_YUANTA = 'YUANTA'
    # 土地銀行。
    WebATM_LAND = 'LAND'

    WebATM_ALL = [WebATM_TAISHIN,WebATM_ESUN,WebATM_HUANAN,WebATM_BOT,WebATM_FUBON,WebATM_CHINATRUST,WebATM_FIRST,WebATM_CATHAY,WebATM_MEGA,WebATM_YUANTA,WebATM_LAND]
    def Allpay_ChooseSubPayment.check_webatm(choosesubpayment)
      if (WebATM_ALL.include?(choosesubpayment)) then
      	return choosesubpayment
      end
      return WebATM_ALL[0]
    end

    # ATM 類(101~200)
    # 台新銀行。
    ATM_TAISHIN = 'TAISHIN'
    # 玉山銀行。
    ATM_ESUN = 'ESUN'
    # 華南銀行。
    ATM_HUANAN = 'HUANAN'
    # 台灣銀行。
    ATM_BOT = 'BOT'
    # 台北富邦。
    ATM_FUBON = 'FUBON'
    # 中國信託。
    ATM_CHINATRUST = 'CHINATRUST'
    # 第一銀行。
    ATM_FIRST = 'FIRST'
    ATM_ALL = [ATM_TAISHIN,ATM_ESUN,ATM_HUANAN,ATM_BOT,ATM_FUBON,ATM_CHINATRUST,ATM_FIRST]
    def Allpay_ChooseSubPayment.check_atm(choosesubpayment)
      if (ATM_ALL.include?(choosesubpayment)) then
      	return choosesubpayment
      end
      return ATM_ALL[0]
    end
    def Allpay_ChooseSubPayment.check_is_atm(choosesubpayment)
      if (ATM_ALL.include?(choosesubpayment)) then
        return true
      end
      return false
    end
    # 超商類(201~300)
    # 超商代碼繳款。
    CVS = 'CVS'
    # OK超商代碼繳款。
    CVS_OK = 'OK'
    # 全家超商代碼繳款。
    CVS_FAMILY = 'FAMILY'
    # 萊爾富超商代碼繳款。
    CVS_HILIFE = 'HILIFE'
    # 7-11 ibon代碼繳款。
    CVS_IBON = 'IBON'
    CVS_ALL = [CVS,CVS_OK,CVS_FAMILY,CVS_HILIFE,CVS_IBON]
    def Allpay_ChooseSubPayment.check_cvs(choosesubpayment)
      if (CVS_ALL.include?(choosesubpayment)) then
      	return choosesubpayment
      end
      return CVS_ALL[0]
    end
    def Allpay_ChooseSubPayment.check_is_cvs(choosesubpayment)
      if (CVS_ALL.include?(choosesubpayment)) then
        return true
      end
      return false
    end
    # 其他第三方支付類(301~400)
    # 支付寶。
    Alipay = 'Alipay'
    # 財付通。
    Tenpay = 'Tenpay'
    # 儲值/餘額消費類(401~500)
    # 儲值/餘額消費(歐付寶)
    TopUpUsed_AllPay = 'AllPay'
    # 儲值/餘額消費(玉山)
    TopUpUsed_ESUN = 'ESUN'
    TopUpUsed_ALL = [TopUpUsed_AllPay,TopUpUsed_ESUN]
    def Allpay_ChooseSubPayment.check_topupused(choosesubpayment)
      if (TopUpUsed_ALL.include?(choosesubpayment)) then
      	return choosesubpayment
      end
      return TopUpUsed_ALL[0]
    end
    # 其他類(901~999)
    # 超商條碼繳款。
    BARCODE = 'BARCODE'
    BARCODE_ALL = [BARCODE]
    def Allpay_ChooseSubPayment.check_barcode(choosesubpayment)
    	return BARCODE
    end
    def Allpay_ChooseSubPayment.check_is_barcode(choosesubpayment)
      if (BARCODE_ALL.include?(choosesubpayment)) then
        return true
      end
      return false
    end
    # 信用卡(MasterCard/JCB/VISA)。
    Credit = 'Credit'
    # 貨到付款。
    COD = 'COD'

    AndroidPay_AndroidPay = 'AndroidPay'
    AndroidPay_ALL = [AndroidPay_AndroidPay]
    def Allpay_ChooseSubPayment.check_AndroidPay(choosesubpayment)
      if (AndroidPay_ALL.include?(choosesubpayment)) then
        return choosesubpayment
      end
      return AndroidPay_ALL[0]
    end

    def Allpay_ChooseSubPayment.check_choosepayment(choosesubpayment)
      if (Allpay_ChooseSubPayment.check_is_atm(choosesubpayment)) then
        return Allpay_ChoosePayment::ATM
      elsif (Allpay_ChooseSubPayment.check_is_cvs(choosesubpayment)) then
        return Allpay_ChoosePayment::CVS
      elsif (Allpay_ChooseSubPayment.check_is_barcode(choosesubpayment)) then
        return Allpay_ChoosePayment::BARCODE
      end
      return Allpay_ChoosePayment::NONE
    end
end
# 回覆付款方式( PaymentType)
    # WebATM_TAISHIN 台新銀行 WebATM
    # WebATM_ESUN 玉山銀行 WebATM
    # WebATM_HUANAN 華南銀行 WebATM
    # ￼
    # WebATM_BOT 台灣銀行 WebATM
    # WebATM_FUBON 台北富邦 WebATM
    # WebATM_CHINATRUST 中國信託 WebATM
    # WebATM_FIRST 第一銀行 WebATM
    # ￼
    # WebATM_CATHAY 國泰世華 WebATM
    # ￼
    # WebATM_MEGA 兆豐銀行 WebATM
    # ￼
    # WebATM_YUANTA 元大銀行 WebATM
    # WebATM_LAND 土地銀行 WebATM
    # ATM_TAISHIN 台新銀行 ATM
    # ATM_ESUN 玉山銀行 ATM
    # ATM_HUANAN 華南銀行 ATM
    # ATM_BOT 台灣銀行 ATM
    # ATM_FUBON 台北富邦 ATM
    # ATM_CHINATRUST 中國信託 ATM
    # ATM_FIRST 第一銀行 ATM
    # CVS_CVS 超商代碼繳款
    # CVS_OK OK 超商代碼繳款
    # CVS_FAMILY 全家超商代碼繳款
    # CVS_HILIFE 萊爾富超商代碼繳款
    # CVS_IBON 7-11 ibon 代碼繳款
    # BARCODE_BARCODE 超商條碼繳款
    # Alipay_Alipay 支付寶
    # Tenpay_Tenpay 財付通
    # Credit_CreditCard 信用卡
    # TopUpUsed_AllPay 儲值/餘額消費_歐付寶
    # TopUpUsed_ESUN 儲值/餘額消費_玉山銀行
    # UnionPay
    # AndroidPay
class Allpay_PaymentType
    def Allpay_PaymentType.check_paymenttype(paymenttype)
    puts "paymenttype => #{paymenttype}"
      if (paymenttype == nil) then
        return false,nil,nil
      end
      a = paymenttype.split('_')
      if (a.length == 2) then
        return true,a[0],a[1]
      elsif (a.length == 1) then
        return true,a[0],nil
      else
        return false,nil,nil
      end
    end
end

# 額外付款資訊。
class Allpay_ExtraPaymentInfo
    # 需要額外付款資訊。
    Yes = 'Y'
    # 不需要額外付款資訊。
    No = 'N'
end

# 額外付款資訊。
class Allpay_DeviceSource
    # 桌機版付費頁面。
    PC = 'P'
    # 行動裝置版付費頁面。
    Mobile = 'M'
    def Allpay_DeviceSource.check(ds)
      if (ds == Mobile) then
      	return Mobile
      end
      return PC
    end
end

# 信用卡訂單處理動作資訊。
class Allpay_ActionType
    # 關帳
    C = 'C'
    # 退刷
    R = 'R'
    # 取消
    E = 'E'
    # 放棄
    N = 'N'

end

# 定期定額的週期種類。
class Allpay_PeriodType
    # 無
    None = ''
    # 年
    Year = 'Y'
    # 月
    Month = 'M'
    # 日
    Day = 'D'

end

# 電子發票開立註記。
class Allpay_InvoiceState
  # 需要開立電子發票。
  Yes = 'Y'
  # 不需要開立電子發票。
  No = ''
end
class Allpay_EncryptType
  # 需要開立電子發票。
  MD5 = 0
  # 不需要開立電子發票。
  SHA256 = 1
end
class Allpay_UnionPay
	Yes = 1
	No = 0
end
class Allpay_Redeem
	Yes = 'Y'
	No = ''
end
