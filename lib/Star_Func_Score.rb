
class Star
  @HouseScore
  @HouseOriginalScore
  @HousePower

  @HouseFourHuaInfo

  @HouseFourHuaScore
  @HouseFourHuaScore_Org
  @HouseFourHuaScore_Self

  @HouseStarScoreFive
  @HouseScore_AStarFive
  @HouseScore_BStarFive
  @HouseScore_DoctorFive
  @HouseScore_YearStarFive
  @HouseScore_YearGodFive
  @HouseScore_GodFive

  @HouseStarScoreNoFive
  @HouseScore_AStarNoFive
  @HouseScore_BStarNoFive
  @HouseScore_DoctorNoFive
  @HouseScore_YearStarNoFive
  @HouseScore_YearGodNoFive
  @HouseScore_GodNoFive

  @HouseStarInfo
  def Score_InitData()
    @HouseScore = Array.new(Cfate::PAN_CNT) {Array.new(12,nil)}
    @HouseOriginalScore = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HousePower = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}

    # 四化的訊息(測試用)
    @HouseFourHuaInfo = Array.new(Cfate::PAN_CNT) {Array.new(12) {Array.new}}

    @HouseFourHuaScore = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseFourHuaScore_Org = Array.new(Cfate::PAN_CNT) {Array.new(12) {Array.new(4,0)}}
    @HouseFourHuaScore_Self = Array.new(Cfate::PAN_CNT) {Array.new(12) {Array.new(4,0)}}

    @HouseStarScoreFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_AStarFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_BStarFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_DoctorFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_YearStarFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_YearGodFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_GodFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}

    @HouseStarScoreNoFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_AStarNoFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_BStarNoFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_DoctorNoFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_YearStarNoFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_YearGodNoFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}
    @HouseScore_GodNoFive = Array.new(Cfate::PAN_CNT) {Array.new(12,0)}

    @HouseStarInfo = Array.new(Cfate::PAN_CNT) {Array.new(12) {Array.new}}
  end

  def Score_FourHua_AddHouseInfo(nPanType,nEarth,nFlyOutStar,nFlyOutFourHua,nFlyOutEarth,nColStar,nColFourHua,nFourHuaType,nScore)
    sHouse =  gHouse_GetHouseName(nPanType,nEarth)
    sFlyOutStar = Star.GetAStarName(nFlyOutStar)
    sFlyOutFourHua = Star.GetFourHuaStr(nFlyOutFourHua)
    sFlyOutHouse = gHouse_GetHouseName(nPanType,nFlyOutEarth)

    sColStar = Star.GetAStarName(nColStar)
    sColFourHua = Star.GetFourHuaStr(nColFourHua)
    sFourHuaType = Star.GetFourHuaTypeStr(nFourHuaType)
    n34House = g_Check34House(nEarth,nFlyOutEarth)
    nHousePower = Score_GetFlyOutPower(n34House)
    nOppHousePower = (gHouse_WithoutMainAStar(nPanType,nFlyOutEarth) ? 0.5 : 1.0)

    sInfo = "#{sFourHuaType} #{sHouse} ==> FlyOutStar:#{sFlyOutStar} #{sFlyOutFourHua} in #{sFlyOutHouse} Col #{sColStar} #{sColFourHua} Score :#{nScore} 34Power: #{nHousePower} nOppHousePower: #{nOppHousePower}"
    Score_FourHua_AddHouseInfoData(nPanType,nEarth,sInfo)
    Score_FourHua_AddHouseInfoData(nPanType,nEarth,"")
  end

  def Score_FourHua_AddHouseStarInfo(nPanType,nEarth)
    sFourHuaInfo = Score_FourHua_HouseSkyStarInfo(nPanType,nEarth)
    Score_FourHua_AddHouseInfoData(nPanType,nEarth,sFourHuaInfo)
  end

  def Score_FourHua_AddHouseInfoData(nPanType,nEarth,sInfo)
    @HouseFourHuaInfo[nPanType][Earth.Earth2EarthIndex(nEarth)].push(sInfo)
  end

  def Score_FourHua_HouseSkyStarInfo(nPanType,nEarth)
    nSky = gHouse_GetSky(nPanType,nEarth)
    sSkyName = Sky.GetName(nSky)
    sFourHuaInfo = sSkyName + "  ==> " + Score_FourHua_GetSkyStarNames(nSky)
  end

  def Score_FourHua_GetSkyStarNames(nSky)
    nStarNames = ""
    nSkyIndex = Sky.Sky2SkyIndex(nSky)
    (Star::FH_LU..Star::FH_GI).each do |nFourHuaIndex|
      nStar = getFourHuaStarValue(nSkyIndex,nFourHuaIndex)
      nStarNames += "#{Star.GetFourHuaStr(nFourHuaIndex)}: #{Star.GetStarName(Star::A_STAR,nStar)}  ,"
    end
    return nStarNames
  end

  def Score_multiply_10(nScore)
    @multiply_10 = true
    if (@multiply_10 == true) then
      return (nScore.round(1) * 10).round()
    else
      return (nScore.round(1) * 1).round(1)
    end
  end

  def Score_FourHua_GetHouseInfo(nPanType,nEarth)
    return @HouseFourHuaInfo[nPanType][Earth.Earth2EarthIndex(nEarth)]
  end

  def Score_FourHua_SetScore_Original(nPanType,nEarth,nFourHua,nScore)
    @HouseFourHuaScore_Org[nPanType][Earth.Earth2EarthIndex(nEarth)][nFourHua] += Score_multiply_10(nScore)
  end

  def Score_FourHua_GetScore_Original(nPanType,nEarth,nFourHua)
    return @HouseFourHuaScore_Org[nPanType][Earth.Earth2EarthIndex(nEarth)][nFourHua]
  end

  def Score_FourHua_SetScore_Self(nPanType,nEarth,nFourHua,nScore)
    @HouseFourHuaScore_Self[nPanType][Earth.Earth2EarthIndex(nEarth)][nFourHua] += Score_multiply_10(nScore)
  end

  def Score_FourHua_GetScore_Self(nPanType,nEarth,nFourHua)
    return @HouseFourHuaScore_Self[nPanType][Earth.Earth2EarthIndex(nEarth)][nFourHua]
  end

  def Score_FourHua_SetHouseScore(nPanType,nEarth,nScore)
    @HouseFourHuaScore[nPanType][Earth.Earth2EarthIndex(nEarth)] = Score_multiply_10(nScore)
  end

  def Score_FourHua_GetHouseScore(nPanType,nEarth)
    return @HouseFourHuaScore[nPanType][Earth.Earth2EarthIndex(nEarth)]
  end

  def Score_Star_AddHouseScore(nPanType,nEarth,nScoreFive,nScoreNoFive)
    @HouseStarScoreFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreFive)
    @HouseStarScoreNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreNoFive)
  end

  def Score_Star_GetHouseScore(nPanType,nEarth)
    nScoreFive = Score_Star_GetHouseScoreFive(nPanType,nEarth)
    nScoreNoFive = Score_Star_GetHouseScoreNoFive(nPanType,nEarth)
    return nScoreFive,nScoreNoFive
  end

  def Score_Star_GetHouseScoreFive(nPanType,nEarth)
    return @HouseStarScoreFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
  end

  def Score_Star_GetHouseScoreNoFive(nPanType,nEarth)
    return @HouseStarScoreNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
  end

  def Score_Star_AddHouseScore_AStar(nPanType,nEarth,nScoreFive,nScoreNoFive)
    @HouseScore_AStarFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreFive)
    @HouseScore_AStarNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreNoFive)
    Score_Star_AddHouseScore(nPanType,nEarth,nScoreFive,nScoreNoFive)
  end

  def Score_Star_GetHouseScore_AStar(nPanType,nEarth)
    nScoreFive = @HouseScore_AStarFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    nScoreNoFive = @HouseScore_AStarFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    return nScoreFive,nScoreNoFive
  end

  def Score_Star_AddHouseScore_BStar(nPanType,nEarth,nScoreFive,nScoreNoFive)
    @HouseScore_BStarFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreFive)
    @HouseScore_BStarNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreNoFive)
    Score_Star_AddHouseScore(nPanType,nEarth,nScoreFive,nScoreNoFive)
  end

  def Score_Star_GetHouseScore_BStar(nPanType,nEarth)
    nScoreFive = @HouseScore_BStarFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    nScoreNoFive = @HouseScore_BStarNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    return nScoreFive,nScoreNoFive
  end

  def Score_Star_AddHouseScore_Doctor(nPanType,nEarth,nScoreFive,nScoreNoFive)
    @HouseScore_DoctorFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreFive)
    @HouseScore_DoctorNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreNoFive)
    Score_Star_AddHouseScore(nPanType,nEarth,nScoreFive,nScoreNoFive)
  end

  def Score_Star_GetHouseScore_Doctor(nPanType,nEarth)
    nScoreFive = @HouseScore_DoctorFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    nScoreNoFive = @HouseScore_DoctorNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    return nScoreFive,nScoreNoFive
  end

  def Score_Star_AddHouseScore_YearStar(nPanType,nEarth,nScoreFive,nScoreNoFive)
    @HouseScore_YearStarFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreFive)
    @HouseScore_YearStarNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreNoFive)
    Score_Star_AddHouseScore(nPanType,nEarth,nScoreFive,nScoreNoFive)
  end

  def Score_Star_GetHouseScore_YearStar(nPanType,nEarth)
    nScoreFive = @HouseScore_YearStarFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    nScoreNoFive = @HouseScore_YearStarNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    return nScoreFive,nScoreNoFive
  end

  def Score_Star_AddHouseScore_YearGod(nPanType,nEarth,nScoreFive,nScoreNoFive)
    @HouseScore_YearGodFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreFive)
    @HouseScore_YearGodNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreNoFive)
    Score_Star_AddHouseScore(nPanType,nEarth,nScoreFive,nScoreNoFive)
  end

  def Score_Star_GetHouseScore_YearGod(nPanType,nEarth)
    nScoreFive = @HouseScore_YearGodFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    nScoreNoFive = @HouseScore_YearGodNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    return nScoreFive,nScoreNoFive
  end

  def Score_Star_AddHouseScore_God(nPanType,nEarth,nScoreFive,nScoreNoFive)
    @HouseScore_GodFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreFive)
    @HouseScore_GodNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)] += Score_multiply_10(nScoreNoFive)
    Score_Star_AddHouseScore(nPanType,nEarth,nScoreFive,nScoreNoFive)
  end

  def Score_Star_GetHouseScore_God(nPanType,nEarth)
    nScoreFive = @HouseScore_GodFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    nScoreNoFive = @HouseScore_GodNoFive[nPanType][Earth.Earth2EarthIndex(nEarth)]
    return nScoreFive,nScoreNoFive
  end

  # Score計算分數的排盤方式與原先排盤不同，例四化的先天四化的天干取法不同
  def Score_GetAllScore(nPanType,hUserInfo,hUserAskData)
    hUserType = Hash.new
    hUserType[Star::PAR_FLOWYEAR_HUA] = Star::PAN_TYPE_SCORE
    # CreateAllPan(nPanType,hUserInfo,hUserAskData,hUserType)
    initAll(nPanType,hUserInfo,hUserAskData,hUserType)

    Score_InitData()
    #(Cfate::PAN_NORMAL...Cfate::PAN_FLOWMIN).each do |nPanType|
    (Cfate::PAN_NORMAL..nPanType).each do |panType|
      # 星的分數
      Score_Star_CalcHousesScore(panType)
      # 四化的分數
      Score_FourHua_CalcHousesScore(panType)
      # 2.2.2 全部以標準方式計算分開計算及「星的總分」計算。
      Score_SumScore(panType)
    end
  end

  def Score_CalcScore(nPanType,hUserInfo,hUserAskData,nScoreHouse=nil)
    # hUserType = {:user_id=>5, "n_par_pan_year_display"=>0, "leap_type"=>1, "horse_type"=>1, "god_type12"=>2, "doc_type"=>2, "lu_type"=>2, "gs_type"=>1, "flowsky_type"=>1, "smallsan_type"=>0, "god_type"=>1, "flowlife_housetype"=>0, "giakung_type"=>0, "fourhua_type"=>0, "flowyear_hua"=>1, "gs_name_type"=>0, "par_b3132_type"=>1, "int_34color"=>0, "int_miowong_style"=>0, "int_huaout_display"=>1, "int_selhua_display"=>1, "int_housefive_display"=>1, "int_flowtime_display"=>0, "int_ten_display"=>1, "int_small_display"=>1, "int_8words_display"=>1, "int_lifeline_display"=>1, "int_smallyear_revert_display"=>1, "dis_astar_1"=>1, "dis_astar_2"=>1, "dis_astar_3"=>1, "dis_astar_4"=>1, "dis_astar_5"=>1, "dis_astar_6"=>1, "dis_astar_7"=>1, "dis_astar_8"=>1, "dis_astar_9"=>1, "dis_astar_10"=>1, "dis_astar_11"=>1, "dis_astar_12"=>1, "dis_astar_13"=>1, "dis_astar_14"=>1, "dis_astar_15"=>1, "dis_astar_16"=>1, "dis_astar_17"=>1, "dis_astar_18"=>1, "dis_astar_19"=>1, "dis_astar_20"=>1, "dis_astar_21"=>1, "dis_astar_22"=>1, "dis_astar_23"=>1, "dis_astar_24"=>1, "dis_astar_25"=>1, "dis_bstar_1"=>1, "dis_bstar_2"=>1, "dis_bstar_3"=>1, "dis_bstar_4"=>1, "dis_bstar_5"=>1, "dis_bstar_6"=>1, "dis_bstar_7"=>1, "dis_bstar_8"=>1, "dis_bstar_9"=>1, "dis_bstar_10"=>1, "dis_bstar_11"=>1, "dis_bstar_12"=>1, "dis_bstar_13"=>1, "dis_bstar_14"=>1, "dis_bstar_15"=>1, "dis_bstar_16"=>1, "dis_bstar_17"=>1, "dis_bstar_18"=>1, "dis_bstar_19"=>1, "dis_bstar_20"=>1, "dis_bstar_21"=>1, "dis_bstar_22"=>1, "dis_bstar_23"=>1, "dis_bstar_24"=>1, "dis_bstar_25"=>1, "dis_bstar_26"=>1, "dis_bstar_27"=>1, "dis_bstar_28"=>1, "dis_bstar_29"=>1, "dis_bstar_30"=>1, "dis_bstar_31"=>1, "dis_bstar_32"=>1, "dis_bstar_33"=>1, "dis_bstar_34"=>1, "dis_bstar_35"=>1, "dis_flowlyt"=>0, "dis_flowma"=>0, "dis_flowluan"=>0, "dis_doctor"=>1, "dis_live"=>1, "dis_yearstar"=>1, "dis_yeargod"=>1, "dis_flowchan"=>1, "dis_skycook"=>1, "dis_lyyg"=>1, "dis_7star"=>0, "pfh_1_0"=>6, "pfh_1_1"=>14, "pfh_1_2"=>4, "pfh_1_3"=>3, "pfh_2_0"=>2, "pfh_2_1"=>12, "pfh_2_2"=>1, "pfh_2_3"=>8, "pfh_3_0"=>5, "pfh_3_1"=>2, "pfh_3_2"=>15, "pfh_3_3"=>6, "pfh_4_0"=>8, "pfh_4_1"=>5, "pfh_4_2"=>2, "pfh_4_3"=>10, "pfh_5_0"=>9, "pfh_5_1"=>8, "pfh_5_2"=>18, "pfh_5_3"=>2, "pfh_6_0"=>4, "pfh_6_1"=>9, "pfh_6_2"=>12, "pfh_6_3"=>16, "pfh_7_0"=>3, "pfh_7_1"=>4, "pfh_7_2"=>8, "pfh_7_3"=>5, "pfh_8_0"=>10, "pfh_8_1"=>3, "pfh_8_2"=>16, "pfh_8_3"=>15, "pfh_9_0"=>12, "pfh_9_1"=>1, "pfh_9_2"=>17, "pfh_9_3"=>4, "pfh_10_0"=>14, "pfh_10_1"=>10, "pfh_10_2"=>8, "pfh_10_3"=>9, "hall_number1"=>"", "hall_number2"=>"", "hall_number3"=>"", "par_print_time_page"=>false, "par_print_time_page_type"=>0, "par_print_time_page_three_1"=>false, "par_print_time_page_three_2"=>false, "par_print_time_page_three_3"=>false, "par_print_pan_header"=>"", "par_print_set_footer"=>"meen"}
    hUserType = Hash.new
    hUserType[Star::PAR_FLOWYEAR_HUA] = Star::PAN_TYPE_SCORE
    # CreateAllPan(nPanType,hUserInfo,hUserAskData,hUserType)
    initAll(nPanType,hUserInfo,hUserAskData,hUserType)

    if (nScoreHouse == nil) then
      nEarth = nil
    else
      nEarth = g_House_GetEarth(nPanType,nScoreHouse)
    end

    Score_InitData()
    # 星的分數
    Score_Star_CalcHousesScore(nPanType,nEarth)

    # 四化的分數
    Score_FourHua_CalcHousesScore(nPanType,nEarth)
    # 2.2.2 全部以標準方式計算分開計算及「星的總分」計算。
    Score_SumScore(nPanType,nEarth)
  end
  def Score_CalcHouseEarthScore(nPanType,hUserInfo,hUserAskData,nEarth=nil)
    # hUserType = {:user_id=>5, "n_par_pan_year_display"=>0, "leap_type"=>1, "horse_type"=>1, "god_type12"=>2, "doc_type"=>2, "lu_type"=>2, "gs_type"=>1, "flowsky_type"=>1, "smallsan_type"=>0, "god_type"=>1, "flowlife_housetype"=>0, "giakung_type"=>0, "fourhua_type"=>0, "flowyear_hua"=>1, "gs_name_type"=>0, "par_b3132_type"=>1, "int_34color"=>0, "int_miowong_style"=>0, "int_huaout_display"=>1, "int_selhua_display"=>1, "int_housefive_display"=>1, "int_flowtime_display"=>0, "int_ten_display"=>1, "int_small_display"=>1, "int_8words_display"=>1, "int_lifeline_display"=>1, "int_smallyear_revert_display"=>1, "dis_astar_1"=>1, "dis_astar_2"=>1, "dis_astar_3"=>1, "dis_astar_4"=>1, "dis_astar_5"=>1, "dis_astar_6"=>1, "dis_astar_7"=>1, "dis_astar_8"=>1, "dis_astar_9"=>1, "dis_astar_10"=>1, "dis_astar_11"=>1, "dis_astar_12"=>1, "dis_astar_13"=>1, "dis_astar_14"=>1, "dis_astar_15"=>1, "dis_astar_16"=>1, "dis_astar_17"=>1, "dis_astar_18"=>1, "dis_astar_19"=>1, "dis_astar_20"=>1, "dis_astar_21"=>1, "dis_astar_22"=>1, "dis_astar_23"=>1, "dis_astar_24"=>1, "dis_astar_25"=>1, "dis_bstar_1"=>1, "dis_bstar_2"=>1, "dis_bstar_3"=>1, "dis_bstar_4"=>1, "dis_bstar_5"=>1, "dis_bstar_6"=>1, "dis_bstar_7"=>1, "dis_bstar_8"=>1, "dis_bstar_9"=>1, "dis_bstar_10"=>1, "dis_bstar_11"=>1, "dis_bstar_12"=>1, "dis_bstar_13"=>1, "dis_bstar_14"=>1, "dis_bstar_15"=>1, "dis_bstar_16"=>1, "dis_bstar_17"=>1, "dis_bstar_18"=>1, "dis_bstar_19"=>1, "dis_bstar_20"=>1, "dis_bstar_21"=>1, "dis_bstar_22"=>1, "dis_bstar_23"=>1, "dis_bstar_24"=>1, "dis_bstar_25"=>1, "dis_bstar_26"=>1, "dis_bstar_27"=>1, "dis_bstar_28"=>1, "dis_bstar_29"=>1, "dis_bstar_30"=>1, "dis_bstar_31"=>1, "dis_bstar_32"=>1, "dis_bstar_33"=>1, "dis_bstar_34"=>1, "dis_bstar_35"=>1, "dis_flowlyt"=>0, "dis_flowma"=>0, "dis_flowluan"=>0, "dis_doctor"=>1, "dis_live"=>1, "dis_yearstar"=>1, "dis_yeargod"=>1, "dis_flowchan"=>1, "dis_skycook"=>1, "dis_lyyg"=>1, "dis_7star"=>0, "pfh_1_0"=>6, "pfh_1_1"=>14, "pfh_1_2"=>4, "pfh_1_3"=>3, "pfh_2_0"=>2, "pfh_2_1"=>12, "pfh_2_2"=>1, "pfh_2_3"=>8, "pfh_3_0"=>5, "pfh_3_1"=>2, "pfh_3_2"=>15, "pfh_3_3"=>6, "pfh_4_0"=>8, "pfh_4_1"=>5, "pfh_4_2"=>2, "pfh_4_3"=>10, "pfh_5_0"=>9, "pfh_5_1"=>8, "pfh_5_2"=>18, "pfh_5_3"=>2, "pfh_6_0"=>4, "pfh_6_1"=>9, "pfh_6_2"=>12, "pfh_6_3"=>16, "pfh_7_0"=>3, "pfh_7_1"=>4, "pfh_7_2"=>8, "pfh_7_3"=>5, "pfh_8_0"=>10, "pfh_8_1"=>3, "pfh_8_2"=>16, "pfh_8_3"=>15, "pfh_9_0"=>12, "pfh_9_1"=>1, "pfh_9_2"=>17, "pfh_9_3"=>4, "pfh_10_0"=>14, "pfh_10_1"=>10, "pfh_10_2"=>8, "pfh_10_3"=>9, "hall_number1"=>"", "hall_number2"=>"", "hall_number3"=>"", "par_print_time_page"=>false, "par_print_time_page_type"=>0, "par_print_time_page_three_1"=>false, "par_print_time_page_three_2"=>false, "par_print_time_page_three_3"=>false, "par_print_pan_header"=>"", "par_print_set_footer"=>"meen"}
    hUserType = Hash.new
    hUserType[Star::PAR_FLOWYEAR_HUA] = Star::PAN_TYPE_SCORE
    if (hUserAskData["ParAll"] != nil) then
      hUserType.merge!(hUserAskData["ParAll"])
    end
    # CreateAllPan(nPanType,hUserInfo,hUserAskData,hUserType)
    initAll(nPanType,hUserInfo,hUserAskData,hUserType)

    Score_InitData()
    # 星的分數
    Score_Star_CalcHousesScore(nPanType,nEarth)
    # 四化的分數
    Score_FourHua_CalcHousesScore(nPanType,nEarth)
    # 2.2.2 全部以標準方式計算分開計算及「星的總分」計算。
    Score_SumScore(nPanType,nEarth)

  end

                # 四化星 星性  非五行星性
@@Score_Proportion =  [[70,30,0],          #  本命盤
               [70,30,0],          #  十年盤
               [50,30,20],         #  流年盤
               [50,25,25],         #  流月盤
               [30,35,35],         #  流日盤
               [30,35,35],         #  流時盤
               [30,35,35]]         #  流分盤
  def Score_SumScore(nPanType,nEarthIn=nil)
    nHouseScore = 0
    if (nEarthIn == nil || nEarthIn == Star::EX_HOUSE_ALL) then
      (1..12).each do |nEarth|
        Score_SumScore_Each(nPanType,nEarth)
      end
    elsif (nEarthIn.class == Array) then       
      nEarthIn.each do |nEarth|
        Score_SumScore_Each(nPanType,nEarth)
      end
    else
      Score_SumScore_Each(nPanType,nEarthIn)
    end
  end
  def Score_SumScore_Each(nPanType,nEarth)
    nHouseScore = 0

    # 計算宮位原始分數
    nFourHuaScore = Score_FourHua_GetHouseScore(nPanType,nEarth)
    nStarScoreFive,nStarScoreNoFive = Score_Star_GetHouseScore(nPanType,nEarth)
    nHouseScore = 0
    nHouseScore += (Score_GetScoreTypePower_FourHua(nPanType) * nFourHuaScore)
    nHouseScore += (Score_GetScoreTypePower_Five(nPanType) * nStarScoreFive)
    nHouseScore += (Score_GetScoreTypePower_NoFive(nPanType) * nStarScoreNoFive)

    Score_SetHouseOriginalScore(nPanType,nEarth,nHouseScore)

    # 十二長生加權
    nHousePower = Score_FindHousePower(nPanType,nEarth)
    Score_SetHousePower(nPanType,nEarth,nHousePower)
    nHousePower = Score_CalcHousePower(nHousePower)

    # 計算宮位最後分數
    nHouseScore = nHouseScore + (nHousePower * nHouseScore.abs)
    Score_SetHouseScore(nPanType,nEarth,nHouseScore)
  end

  def Score_GetScoreTypePower_FourHua(nPanType)
    return Score_GetScoreTypePower(nPanType,0)
  end

  def Score_GetScoreTypePower_Five(nPanType)
    return Score_GetScoreTypePower(nPanType,1)
  end

  def Score_GetScoreTypePower_NoFive(nPanType)
    return Score_GetScoreTypePower(nPanType,2)
  end

  def Score_GetScoreTypePower(nPanType,nScoreType)
    if (@@Score_Proportion[nPanType] == nil) then
      return 0.0
    end
    return (@@Score_Proportion[nPanType][nScoreType].to_f / 100)
  end

  # 1.6、十二長生加權表
  def Score_FindHousePower(nPanType,nEarth)
    nGodIndex = gHouse_GetGodIndex(nPanType,nEarth)
    nHouseIndex = g_House_GetHouseIndex(nPanType,nEarth)
    return (@@HouseGodPower[nGodIndex][nHouseIndex])
  end

  def Score_CalcHousePower(nHousePower)
    return nHousePower.to_f / 100.0
  end

  def Score_SetHousePower(nPanType,nEarth,nPower)
    @HousePower[nPanType][Earth.Earth2EarthIndex(nEarth)] = nPower
  end

  def Score_GetHousePower(nPanType,nEarth)
    return @HousePower[nPanType][Earth.Earth2EarthIndex(nEarth)]
  end
  def Score_multiply_1(nScore)
    if (@multiply_10 == true) then
      return nScore.round()
    else
      return nScore.round(1)
    end
  end
  def Score_SetHouseOriginalScore(nPanType,nEarth,nHouseScore)
    @HouseOriginalScore[nPanType][Earth.Earth2EarthIndex(nEarth)] = Score_multiply_1(nHouseScore)
  end

  def Score_GetHouseOriginalScore(nPanType,nEarth)
    return @HouseOriginalScore[nPanType][Earth.Earth2EarthIndex(nEarth)]
  end

  def Score_SetHouseScore(nPanType,nEarth,nHouseScore)
    @HouseScore[nPanType][Earth.Earth2EarthIndex(nEarth)] = Score_multiply_1(nHouseScore)
  end

  def Score_GetHouseScore(nPanType,nEarth)
    return @HouseScore[nPanType][Earth.Earth2EarthIndex(nEarth)]
  end

  # 星性分數計算
  # 2.2.1 純單星分數計算，不列入加權分數
  # 2.2.2 全部以標準方式計算分開計算及「星的總分」計算。
  # 計算方式：
  #     「宮氣」之五行與宮內之各星逐一比對及加權分數，
  #        將所有的數值加起來為該宮之星的總分。
  #        無五行之星曜，則以定義在各宮位的數值為準。
  def Score_Star_CalcHousesScore(nPanType,nEarthIn=nil)
    if (nEarthIn == nil || nEarthIn == Star::EX_HOUSE_ALL) then
      (1..12).each do |nEarth|
        # 單星分數列入加權計算
        Score_Star_CalcHouseScore(nPanType,nEarth)
      end
    elsif (nEarthIn.class == Array) then
      nEarthIn.each do |nEarth|
        # 單星分數列入加權計算
        Score_Star_CalcHouseScore(nPanType,nEarth)
      end
    else
      Score_Star_CalcHouseScore(nPanType,nEarthIn)
    end
  end

  # 2.2.1 純單星分數計算，不列入加權分數
  def Score_Star_CalcHouseScore(nPanType,nEarth)
    # A星分數
    nScoreFive,nScoreNoFive = Score_Star_CalcHouseScore_AStar(nPanType,nEarth)
    Score_Star_AddHouseScore_AStar(nPanType,nEarth,nScoreFive,nScoreNoFive)

    # B星分數
    nScoreFive,nScoreNoFive = Score_Star_CalcHouseScore_BStar(nPanType,nEarth)
    Score_Star_AddHouseScore_BStar(nPanType,nEarth,nScoreFive,nScoreNoFive)

    # 博士分數
    nScoreFive,nScoreNoFive = Score_Star_CalcHouseScore_Doctor(nPanType,nEarth)
    Score_Star_AddHouseScore_Doctor(nPanType,nEarth,nScoreFive,nScoreNoFive)

    # 將星分數
    nScoreFive,nScoreNoFive = Score_Star_CalcHouseScore_YearStar(nPanType,nEarth)
    Score_Star_AddHouseScore_YearStar(nPanType,nEarth,nScoreFive,nScoreNoFive)

    # 歲建星分數
    nScoreFive,nScoreNoFive = Score_Star_CalcHouseScore_YearGod(nPanType,nEarth)
    Score_Star_AddHouseScore_YearGod(nPanType,nEarth,nScoreFive,nScoreNoFive)

    # 十二長生分數，原本沒有此項目
    nScoreFive,nScoreNoFive = Score_Star_CalcHouseScore_God(nPanType,nEarth)
    Score_Star_AddHouseScore_God(nPanType,nEarth,nScoreFive,nScoreNoFive)
  end

  def Score_Star_CalcHouseScore_AStar(nPanType,nEarth)
    aOrgStar,aOppStar,bUseOpp = gHouse_GetAStars_Sep(nPanType,nEarth)

    nHouseScoreFive1,nHouseScoreNoFive1 = Score_Star_CalcStarsScore(nPanType,nEarth,Star::A_STAR,aOrgStar,1,nil)

    # 1.11 本宮無主星計算方式,分數以50%計算之。
    nHouseScoreFive2,nHouseScoreNoFive2 = Score_Star_CalcStarsScore(nPanType,nEarth,Star::A_STAR,aOppStar,0.5,nil)

    nHouseScoreFive = nHouseScoreFive1 + nHouseScoreFive2
    nHouseScoreNoFive = nHouseScoreNoFive1 + nHouseScoreNoFive2

    return nHouseScoreFive,nHouseScoreNoFive
  end

  def Score_Star_CalcStarsScore(nPanType,nEarth,sStarType,aStar,nOtherPower,aExceptStar)
    nHouseScoreFive = 0
    nHouseScoreNoFive = 0
    if (aExceptStar == nil) then
      aExceptStar = Array.new
    end
    aStar.each do |nStar|
      nScoreFive,nScoreNoFive = 0,0
      if (aExceptStar.index(nStar) == nil)
        nScoreFive,nScoreNoFive,nStarFive,nHouseFive = Score_Star(nPanType,nEarth,nStar,sStarType)
      end

      # 對宮的星先除以2
      nScoreFive *= nOtherPower
      nScoreNoFive *= nOtherPower

      # 星的加權分,包含例外
      nStarPower,nOrgPower,nSihPower,nSign = Score_GetStarPower(nPanType,nEarth,nStar,sStarType)

      # 加權計算
      nStarScoreFive = Score_Score_formula(nScoreFive,nStarPower)
      nStarScoreNoFive = Score_Score_formula(nScoreNoFive,nStarPower)

      # 計入宮分數
      nHouseScoreFive += nStarScoreFive
      nHouseScoreNoFive += nStarScoreNoFive

      Score_Star_AddHouseStarInfo(nPanType,nEarth,sStarType,nStar,nStarScoreFive,nStarScoreNoFive,nScoreFive,nScoreNoFive,nOtherPower,nStarPower,nOrgPower,nSihPower,nSign,nStarFive,nHouseFive)
    end

    return nHouseScoreFive,nHouseScoreNoFive
  end

  def Score_Star_CalcHouseScore_BStar(nPanType,nEarth)
    aStar = gHouse_GetBStars(nPanType,nEarth)

    # 天使天傷不算分
    aExceptStar = Array.new
    aExceptStar.push(33)
    aExceptStar.push(34)
    nHouseScoreFive,nHouseScoreNoFive = Score_Star_CalcStarsScore(nPanType,nEarth,Star::B_STAR,aStar,1,aExceptStar)

    return nHouseScoreFive,nHouseScoreNoFive
  end

  def Score_Star_CalcHouseScore_Doctor(nPanType,nEarth)
    nStar = gHouse_GetDoctor(nPanType,nEarth)
    nHouseScoreFive,nHouseScoreNoFive = Score_Star_CalcStarScore(nPanType,nEarth,Star::DOCTOR,nStar)

    return nHouseScoreFive,nHouseScoreNoFive
  end

  def Score_Star_CalcStarScore(nPanType,nEarth,sStarType,nStar)
    aStar = Array.new
    aStar.push(nStar)
    nHouseScoreFive,nHouseScoreNoFive = Score_Star_CalcStarsScore(nPanType,nEarth,sStarType,aStar,1,nil)

    return nHouseScoreFive,nHouseScoreNoFive
  end

  def Score_Star_CalcHouseScore_YearStar(nPanType,nEarth)
    nStar = gHouse_GetYearStar(nPanType,nEarth)
    nHouseScoreFive,nHouseScoreNoFive = Score_Star_CalcStarScore(nPanType,nEarth,Star::YEARSTAR,nStar)

    return nHouseScoreFive,nHouseScoreNoFive
  end

  def Score_Star_CalcHouseScore_YearGod(nPanType,nEarth)
    nStar = gHouse_GetYearGod(nPanType,nEarth)
    nHouseScoreFive,nHouseScoreNoFive = Score_Star_CalcStarScore(nPanType,nEarth,Star::YEARGOD,nStar)

    return nHouseScoreFive,nHouseScoreNoFive
  end

  def Score_Star_CalcHouseScore_God(nPanType,nEarth)
    nStar = gHouse_GetGod(nPanType,nEarth)
    nHouseScoreFive,nHouseScoreNoFive = Score_Star_CalcStarScore(nPanType,nEarth,Star::GOD,nStar)

    return nHouseScoreFive,nHouseScoreNoFive
  end

  # 此函數已沒有使用 2018/7/5 Peter
  def Score_AddHouseStarInfo(nPanType,nEarth,sStarType,nStar,nStarScoreFive,nStarScoreNoFive,nScoreFive,nScoreNoFive,nStarPower,nHouseScoreFive,nHouseScoreNoFive)
    sInfo = Array.new
    sInfo.push(sStarType)          # 1
    sInfo.push(nStar)              # 2
    sInfo.push(nStarScoreFive.round(1))         # 3
    sInfo.push(nStarScoreNoFive.round(1))       # 4
    sInfo.push(nScoreFive.round(1))          # 5
    sInfo.push(nScoreNoFive.round(1))          # 6
    sInfo.push(nStarPower.round(1))          # 7
    sInfo.push(nHouseScoreFive.round(1))        # 8
    sInfo.push(nHouseScoreNoFive.round(1))      # 9

    @HouseStarInfo[nPanType][Earth.Earth2EarthIndex(nEarth)].push(sInfo)
  end

  def Score_Star_AddHouseStarInfo(nPanType,nEarth,sStarType,nStar,nStarScoreFive,nStarScoreNoFive,nScoreFive,nScoreNoFive,nOtherPower,nStarPower,nOrgPower,nSihPower,nSign,nStarFive,nHouseFive)
    sInfo = Array.new
    sInfo.push(sStarType)          # 1
    sInfo.push(nStar)              # 2
    sInfo.push(Score_multiply_10(nStarScoreFive))         # 3
    sInfo.push(Score_multiply_10(nStarScoreNoFive))       # 4
    sInfo.push(Score_multiply_10(nScoreFive))          # 5
    sInfo.push(Score_multiply_10(nScoreNoFive))          # 6
    sInfo.push(nOtherPower.round(1))           # 7
    sInfo.push(nStarPower.round(1))        # 8
    sInfo.push(nOrgPower.round(1))      # 9
    sInfo.push(nSihPower.round(1))      # 10
    sInfo.push(nSign.round(1))      # 11
    sInfo.push(nStarFive)              # 12
    sInfo.push(nHouseFive)               # 13

    @HouseStarInfo[nPanType][Earth.Earth2EarthIndex(nEarth)].push(sInfo)
  end

  def Score_Star_GetHouseInfo(nPanType,nEarth)
    return @HouseStarInfo[nPanType][Earth.Earth2EarthIndex(nEarth)]
  end

  def Score_Score_formula(nScore,nPower)
    nFNum = 0.0
    nFNum = (nPower * nScore)
    return nFNum
  end

  # 1.3、各級星的加權分表(一般通則),含例外,各宮加權及正負值
  # return Float
  def Score_GetStarPower(nPanType, nEarth, nStar, sStarType)
      nStarPower = 1.0
      if (!nPanType.between?(Cfate::PAN_NORMAL,Cfate::PAN_FLOWTIME)) then
        return nStarPower,1.0,1.0,0
      end

      # 各級星的加權分
        nOrgPower = Score_GetStarDefaultPower(nPanType,nStar,sStarType)

        aArray = Array.new(2)
        aArray[0] = sStarType
        aArray[1] = nStar
        # 例外情形不依通則計算
    if (@@ByStarLevelPower_Except[nPanType].include?(aArray)) then
      nOrgPower = 1.0
    end

    # 待加權後再來計算  10 * 1 * (1.2) * nSign
    # 一顆星的分數 : 原分數 * 各級星加權(包含例外) * 1.9各宮加權 * nSign
    #1.9 各宮加權
    nSihPower = Score_GetStarInHousePower(nPanType,nEarth,nStar,sStarType)

    nStarPower = (nOrgPower * nSihPower)

    # 正負數值比例表
    nSign = Score_GetStarInHouseSign(nPanType,nEarth,nStar,sStarType)
    # nStarPower = nStarPower * nSign
    # 2020/12/12修改
    nStarPower = Score_ChangeSign(nStarPower,nSign)

    return nStarPower,nOrgPower,nSihPower,nSign
  end
  def Score_ChangeSign(nStarPower,nSign)
    return nStarPower if nSign == 0
    return nStarPower.abs if nSign == 1
    return (nStarPower.abs * -1) if nSign == -1
    return nStarPower
  end
  def Score_GetStarDefaultPower(nPanType,nStar,sStarType)
    nStarLevel = Score_FindStarLevel(nStar,sStarType)
    return (@@ByStarLevelPower[nPanType][nStarLevel].to_f / 100)
  end

  # 判定各星的等級
  def Score_FindStarLevel(nStar,sStarType)
    nStarLevel = -1
    if (sStarType == Star::A_STAR) then  # A
      # 甲級星
      nStarLevel = Star::STAR_LEVEL_1
    elsif (sStarType == Star::B_STAR) then  # B
      if (nStar <= 29) then
        nStarLevel = Star::STAR_LEVEL_2  # 乙級星
      else
        # 截空,旬中,旬空,天傷,天使,天廚
        nStarLevel = Star::STAR_LEVEL_3  # 丙級星
      end
    elsif (sStarType == Star::DOCTOR) then   # 博士星
      # 丙級星
      nStarLevel = Star::STAR_LEVEL_3
    elsif (sStarType == Star::GOD) then  # 十二長生
      # 丙級星
      nStarLevel = Star::STAR_LEVEL_3
    elsif (sStarType == Star::YEARSTAR) then  # 將星
      if (@@YearStarLevel4.include?(nStar)) then
        nStarLevel = Star::STAR_LEVEL_4  # 丁級星
      else
        nStarLevel = Star::STAR_LEVEL_5  # 戊級星
      end
    elsif (sStarType == Star::YEARGOD) then  # 歲建星
      if (@@YearGodLevel4.include?(nStar)) then
        nStarLevel = Star::STAR_LEVEL_4  # 丁級星
      else
        nStarLevel = Star::STAR_LEVEL_5  # 戊級星
      end
    end
    return nStarLevel
  end

  # 1.9 各宮加權或正負數值比例表
  def Score_GetStarInHousePower(nPanType,nEarth,nStar,sStarType)
    sHashKey = "#{sStarType}_#{nStar}"
    aPowers = Star.GetStarInHousePowers()
    aPower = aPowers[sHashKey]
    if (aPower == nil) then
      return 1.0
    end
    nHouseIndex = g_House_GetHouseIndex(nPanType,nEarth)
    if (aPower[nHouseIndex] == nil) then
      return 1.0
    end
    return (aPower[nHouseIndex].to_f / 100)
  end

  # nSign 0 : 原值 1 : 絕對正值 -1 : 紹對負值
  def Score_GetStarInHouseSign(nPanType,nEarth,nStar,sStarType)
    sHashKey = "#{sStarType}_#{nStar}"
    aSign = @@StarInHouseSign[sHashKey]
    if (aSign == nil) then
      # nSign = 1
      # 沒有正負值表示使用 原值
      nSign = 0
    else
      sHouseId = g_House_GetHouseId(nPanType,nEarth)
      sHouseIndex = Earth.Earth2EarthIndex(sHouseId)
      nSign = aSign[sHouseIndex]
    end

    # 註1：天魁、天鉞二星在40歲以後正負交換。
    if ((sHashKey == A_STAR_24) || (sHashKey == A_STAR_25)) then
      # 2021/7/19 先取消此機制
      # if (cp_GetFlowYearOld(nPanType) >= 40) then
      #   nSign = (-1) * nSign
      # end
    end
    return nSign
  end

  # 四化分數計算
  # 2.3.1 四化星定義：
  #   2.3.1.1 垂象四化：先天四化星(本命出生年干)垂象、流運四化垂象星。
  #   2.3.1.2 宮干四化：以所在的宮干(本宮)為四化星的依據。
  #   2.3.1.3 自化：所在的宮位的宮干，飛出四化星所在的宮位自化的現象。
  # 2.3.2 以純單星分數計算，不例入加權分數。
  # 2.3.3 各四化星的分數以各宮單獨計算。
  # 2.3.4 比例分數依第1.2點四化星各盤加權表
  # 2.3.5 四化星的計算方：以所在的宮位稱「本宮」，以本宮的宮干四化有飛入三方四正位時列入計算。
  def Score_FourHua_CalcHousesScore(nPanType,nEarthIn=nil)
    nHouseScore = 0.0
    if (nEarthIn == nil || nEarthIn == Star::EX_HOUSE_ALL) then
      (1..12).each do |nEarth|
        nHouseScore = Score_FourHua_CalcHouseScore(nPanType,nEarth)
        Score_FourHua_SetHouseScore(nPanType,nEarth,nHouseScore)
      end
    elsif (nEarthIn.class == Array) then
      nEarthIn.each do |nEarth|
        nHouseScore = Score_FourHua_CalcHouseScore(nPanType,nEarth)
        Score_FourHua_SetHouseScore(nPanType,nEarth,nHouseScore)
      end
    else
      nHouseScore = Score_FourHua_CalcHouseScore(nPanType,nEarthIn)
      Score_FourHua_SetHouseScore(nPanType,nEarthIn,nHouseScore)
    end
  end

  def Score_FourHua_CalcHouseScore(nPanType,nEarth)
    # 測試驗證用
    Score_FourHua_AddHouseStarInfo(nPanType,nEarth)

    nHouseScore = 0.0
    (0..3).each do |nFourHua|
      # 取得四化的星
      nStar = gHouse_GetFourOutStar(nPanType,nEarth,nFourHua)

      # 找出四化飛出的宮
      aFlyOutEarth = gHouse_GetFourOutHouse(nPanType,nEarth,nFourHua)

      aFlyOutEarth.each do |nFlyOutEarth|
        # 與先天四化相碰的分數
        nHouseScore += Score_FourHua_CalcHouseScore_Original(nPanType,nEarth,nStar,nFourHua,nFlyOutEarth)
        # 與宮干四化相碰的分數
        nHouseScore += Score_FourHua_CalcHouseScore_Self(nPanType,nEarth,nStar,nFourHua,nFlyOutEarth)
      end
    end
    return nHouseScore
  end

  def Score_FourHua_CalcHouseScore_Original(nPanType,nEarth,nStar,nFourHua,nFlyOutEarth)
    # 判斷是否在三方四正內
    n34House = g_Check34House(nEarth,nFlyOutEarth)
    if (n34House == Star::HOUSE_34_NONE) then
      return 0
    end

    # 三方四正的比例
    nHousePower = Score_GetFlyOutPower(n34House)

    # 取得該四化的分數
    nScore = Score_CalcFourHuaScore(nFourHua)
    # 判斷先天四化
    nOrgFourHua = Score_CheckOrgFourHua(nPanType,nFlyOutEarth)
    nOrgFourHuaScore = Score_GetOrgFourHuaScore(nScore,nOrgFourHua)

    # 三方四正的比例
    nOrgFourHuaHouseScore = (nOrgFourHuaScore * nHousePower)

    # 取對宮之主星者加權50%
    nOppHousePower = (gHouse_WithoutMainAStar(nPanType,nFlyOutEarth) ? 0.5 : 1.0)
    nOrgFourHuaHouseScore = (nOrgFourHuaHouseScore * nOppHousePower)

    Score_FourHua_SetScore_Original(nPanType,nEarth,nFourHua,nOrgFourHuaHouseScore)

    # 測試用四化
    if (nOrgFourHuaScore != 0.0)
      nColStar = cp_get_OriginalFourHua_Star(nPanType,nOrgFourHua)
      Score_FourHua_AddHouseInfo(nPanType,nEarth,nStar,nFourHua,nFlyOutEarth,nColStar,nOrgFourHua,Star::FH_TYPE_0,nOrgFourHuaScore)
    end

    return (nOrgFourHuaHouseScore)
  end

  def Score_FourHua_CalcHouseScore_Self(nPanType,nEarth,nStar,nFourHua,nFlyOutEarth)
    # 判斷是否在三方四正內
    n34House = g_Check34House(nEarth,nFlyOutEarth)
    if (n34House == Star::HOUSE_34_NONE) then
      return 0
    end

    # 三方四正的比例
    nHousePower = Score_GetFlyOutPower(n34House)

    # 判斷宮自化
    aFourHua = Score_CheckSelfFourHua(nPanType,nFlyOutEarth)
    nSelfFourHuaScore = Score_GetSelfFourHuaScore(nPanType,nFlyOutEarth,aFourHua)
    nSelfFourHuaScore = (nSelfFourHuaScore * nHousePower)

    Score_FourHua_SetScore_Self(nPanType,nEarth,nFourHua,nSelfFourHuaScore)
    # 測試用
    if (nSelfFourHuaScore != 0.0)
      Score_FourHua_AddSelfInfo(nPanType,nEarth,nStar,nFourHua,nFlyOutEarth)
    end
    return nSelfFourHuaScore
  end

  # 三方四正比例
  def Score_GetFlyOutPower(n34House)
    nPower = 0
    if (n34House == Star::HOUSE_34_SELF) then
      nPower = 100
    elsif (n34House == Star::HOUSE_34_1) then
      nPower = 50
    elsif  (n34House == Star::HOUSE_34_2) then
      nPower = 50
    elsif  (n34House == Star::HOUSE_34_OPP) then
      nPower = 50
    end
    return nPower.to_f / 100
  end

  # 遇到先天四化時的分數，有多個只算一個
  def Score_CheckOrgFourHua(nPanType,nFlyOutEarth)
    (Star::FH_LU..Star::FH_GI).each do |nFourHuaIndex|
      aOrgFourOutEarth = Score_GetOriginalFourHua_HouseEarth(nPanType,nFourHuaIndex)

#     if ((nEarth == nFlyOutEarth) || (Earth.ModifyEarth(nEarth + 6) == nFlyOutEarth)) then
      if ((aOrgFourOutEarth.index(nFlyOutEarth) != nil)) then
        return nFourHuaIndex
      end
    end
    return Star::FH_NONE
  end

  def Score_GetOriginalFourHua_HouseEarth(nPanType,nFourHuaIndex)
    aOrgFourOutEarth = Array.new
    nOrgFourHuaEarth = cp_get_OriginalFourHua_HouseEarth(nPanType,nFourHuaIndex)
    aOrgFourOutEarth.push(nOrgFourHuaEarth)

    # 若對宮無主星，則因借星到對宮，所以先天四化也到對宮去
    nOrgFourHuaOppEarth = Earth.ModifyEarth(nOrgFourHuaEarth + 6)
    if (gHouse_WithoutMainAStar(nPanType,nOrgFourHuaOppEarth)) then
      aOrgFourOutEarth.push(nOrgFourHuaOppEarth)
    end

    return aOrgFourOutEarth
  end

  def Score_GetOrgFourHuaScore(nScore,nFourHua)
    if (nFourHua == Star::FH_NONE) then
      return 0
    end
    return (nScore * 2)
  end

  def Score_CheckSelfFourHua(nPanType,nFlyOutEarth)
    #aFhStar = cp_get_SelfFourHua_FourHua(nPanType,nFlyOutEarth)
    aFhStar = gHouse_FindSelfFourHua_FourHua(nPanType,nFlyOutEarth)

    #if (aFhStar.length == 0) then
    # return Star::FH_NONE
    #end
    # 若有多個,全部都要算
    return aFhStar
  end

  def Score_FourHua_AddSelfInfo(nPanType,nEarth,nStar,nFourHua,nFlyOutEarth)
    # ahFHHouseStar = cp_get_HouseSelfFourHua(nPanType,nFlyOutEarth)
    ahFHHouseStar = gHouse_FindSelfFourHua(nPanType,nFlyOutEarth)
    Score_FourHua_AddHouseInfoData(nPanType,nEarth,Score_FourHua_HouseSkyStarInfo(nPanType,nFlyOutEarth))
    ahFHHouseStar.each do |hFHHouseStar|
      nSelfStar = g_get_SelfFourHuaHash_Star(hFHHouseStar)
      nSelfFourHua = g_get_SelfFourHuaHash_FourHua(hFHHouseStar)
      nScore = Score_GetSelfFourHuaScore_Each(nSelfFourHua)
      Score_FourHua_AddHouseInfo(nPanType,nEarth,nStar,nFourHua,nFlyOutEarth,nSelfStar,nSelfFourHua,Star::FH_TYPE_1,nScore)
    end
  end

  # 遇到宮干自化時的分數，每個都要算，因為自化影響比較大
  def Score_GetSelfFourHuaScore(nPanType,nFlyOutEarth,aFourHua)
    nTotalScore = 0
    # 取對宮之主星者加權50%
    nOppHousePower = (gHouse_WithoutMainAStar(nPanType,nFlyOutEarth) ? 0.5 : 1.0)

    aFourHua.each do |nFourHua|
      nTotalScore += (Score_GetSelfFourHuaScore_Each(nFourHua) * nOppHousePower)
    end
    return nTotalScore
  end

  def Score_GetSelfFourHuaScore_Each(nFourHua)
    nScore = Score_CalcFourHuaScore(nFourHua)
    if (nFourHua == Star::FH_GI) then
      return ((0 - nScore.abs) * 1.5).to_f
    end
    return (nScore.abs * 1.5).to_f
  end

  # 計算四化的分數，包含加權
  # @@FuaFix = [8, 5, 2, -10]
  def Score_CalcFourHuaScore(nFourHua)
    nFHScore = @@FuaFix[nFourHua]
  end

  # 宮位定義
  # 2.1.1 宮分陰陽，陰宮宮位丑、卯、己、未、酉、亥六宮。陽宮宮位子、寅、辰、午、申、戍六宮。
  def Score_HouseInYang(nEarth)
    nInYang = nEarth % 2
    return nInYang
  end

  # 2.1.2 宮氣：每個宮之天干與地干合化之納音五行稱之。例如：在寅宮天干為壬，此宮為壬寅，納音五行為「金」。
  def Score_HouseFive(nPanType,nEarth)
    return gHouse_Five(nPanType,nEarth)
  end

  def Score_StarFive(nPanType,nEarth,nStar,sStarType)
    nFive = -1
    if (sStarType == Star::A_STAR) then
      nFive = gHouse_GetAStarFive(nPanType,nEarth,nStar)
    elsif (sStarType == Star::B_STAR) then
      nFive = gHouse_GetBStarFive(nPanType,nEarth,nStar)
    elsif (sStarType == Star::DOCTOR) then
      nFive = gHouse_GetDoctorFive(nStar)
    elsif (sStarType == Star::YEARSTAR) then
      nFive = gHouse_GetYearStarFive(nStar)
    elsif (sStarType == Star::YEARGOD) then
      nFive = gHouse_GetYearGodFive(nStar)
    elsif (sStarType == Star::GOD) then
      nFive = gHouse_GetGodFive(nStar)
    end
    return nFive
  end

  def Score_Star(nPanType,nEarth,nStar,sStarType)
    nScoreFive = 0
    nScoreNoFive = 0
    nStarFive = Score_StarFive(nPanType,nEarth,nStar,sStarType)
    nHouseFive = Score_HouseFive(nPanType,nEarth)

    if (nStarFive > 0) then
      nInYang = Score_HouseInYang(nEarth)
      nScoreFive = Score_StarScore_Five(nInYang,nHouseFive,nStarFive)
    else
      nScoreNoFive = Score_StarScore_NoFive(nPanType,nEarth,nStar,sStarType)
    end
    return nScoreFive,nScoreNoFive,nStarFive,nHouseFive
  end

  def Score_StarScore_Five2(nInYang,nHouseFive,nStarFive)
    nHouseFiveIndex = nHouseFive - 1
    nStarFiveIndex = nStarFive - 1
        if (nInYang == 1) then # Yang
            nScore = @@ByYangScore[nStarFiveIndex][nHouseFiveIndex]
        else
            nScore = @@ByInScore[nStarFiveIndex][nHouseFiveIndex]
        end
        return nScore
  end
  def Score_StarScore_Five(nInYang,nHouseFive,nStarFive)
    nHouseFiveIndex = nHouseFive - 1
    nStarFiveIndex = nStarFive - 1
    nScore = @@ByFiveScore[nStarFiveIndex][nHouseFiveIndex]

        return nScore
  end

  # 沒有五行的星指數由宮位來決定
  def Score_StarScore_NoFive(nPanType,nEarth,nStar,sStarType)
    nScore = 0
        if (sStarType == Star::B_STAR) then
          nScore = Score_StarScore_NoFive_BStar(nPanType,nEarth,nStar)
        elsif (sStarType == Star::GOD) then # 長生
            nScore = Score_StarScore_NoFive_God(nStar)
        elsif (sStarType == Star::YEARSTAR) then # 將星
            nScore = Score_StarScore_NoFive_YearStar(nPanType,nStar)
        elsif (sStarType == Star::YEARGOD) # 歲建
            nScore = Score_StarScore_NoFive_YearGod(nPanType,nStar)
        end
        return nScore
  end

  # 沒有五行的乙級星指數由宮位來決定
  def Score_StarScore_NoFive_BStar(nPanType,nEarth,nStar)
    nScore = 0
    nNoFiveBStarIndex = @@NoFiveBStar.index(nStar)
    if (nNoFiveBStarIndex != nil)
      nHouseIndex = gHouse_GetBStar_HouseIndex(nPanType,nStar)
      nHouseIdIndex = g_House_HouseIndex2HouseIdIndex(nPanType,nHouseIndex)
            nScore = @@NoFive_BStar[nNoFiveBStarIndex][nHouseIdIndex]
        end
        return nScore
  end

  def Score_StarScore_NoFive_God(nStar)
        return 0
  end

  # @@ByYearStarNoFive = [0, 1, 2, 3, 5, 7, 8, 9, 10, 11]
  @@ByYearStarNoFive = [1, 2, 3, 4, 6, 8, 9, 10, 11,12]
  # 沒有五行的將星指數由宮位來決定
  def Score_StarScore_NoFive_YearStar(nPanType,nStar)
    nHouseIndex = gHouse_GetYearStar_HouseIndex(nPanType,nStar)
    nHouseIdIndex = g_House_HouseIndex2HouseIdIndex(nPanType,nHouseIndex)
    nNoFiveIndex = @@ByYearStarNoFive.index(nStar)
        nScore = @@NoFive_YearStar[nNoFiveIndex][nHouseIdIndex]

        return nScore
  end

  @@ByYearGod = [1, 2, 7, 9, 10]
  @@ByYearGodNoFive = [2, 4, 8, 10]
  # 沒有五行的歲建指數由宮位來決定
  def Score_StarScore_NoFive_YearGod(nPanType,nStar)
    nHouseIndex = gHouse_GetYearGod_HouseIndex(nPanType,nStar)
    nHouseIdIndex = g_House_HouseIndex2HouseIdIndex(nPanType,nHouseIndex)
    nNoFiveIndex = @@ByYearGodNoFive.index(nStar)

        nScore = @@NoFive_YearGod[nNoFiveIndex][nHouseIdIndex]

        return nScore
  end


end
