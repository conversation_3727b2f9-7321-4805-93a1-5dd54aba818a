# coding: UTF-8
require("Pukwa_Ask.rb")
require("Pukwa_Main.rb")
require("Pukwa_Solution.rb")

class Pukwa
  def Pukwa.create(po_no,ask_no,lang)
  	ask = Pukwa.find_ask(ask_no,lang)
  	main = Pukwa.find_main(po_no,lang)
  	solution = Pukwa.find_solution(po_no,ask_no,lang)

  	hOut = {}
  	hOut["ask"] = ask
  	hOut["main"] = main
  	hOut["solution"] = solution
    # ask_no = Pm.TestNo21(ask_no.to_i)
    hOut["ask_no"] = ask_no
    # po_no = Pm.TestNo64(po_no.to_i)
    hOut["po_no"] = po_no
  	# Peter測試用
  	# hOut["sky"] = sky
  	# hOut["land"] = land
  	# hOut["landcount"] = GNum.landcount(land)
  	# hOut["people"] = people
  	# hOut["count"] = count

  	return hOut
  end	
end