# coding: UTF-8

require("PanXml_StarFunction.rb")
require("Pm.rb")

class PanXml
	PX_STAR = "Star"
	PX_EIGHT = "Eight"
	
	def px_getPan(pxType,nPanType,hUserInfo,hUserAskData,hUserType)
		return eval("px_#{pxType}_getPan(nPanType,hUserInfo,hUserAskData,hUserType)")
	end
	
	def px_AddTagValue(tagName,value)
		sBuf = "<#{tagName}>#{value}</#{tagName}>"
	end
	
	def px_AddTag(tagName)
		sBuf = "<#{tagName}>"
	end

	def px_AddTagEnd(tagName)
		sBuf = "</#{tagName}>"
	end
	
end
