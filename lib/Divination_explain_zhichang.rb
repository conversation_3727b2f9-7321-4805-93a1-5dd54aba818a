# 求財程式
class Divination
  def explain_zhichang()
    h_biao_tou = Hash.new
    h_jie_guo = Hash.new
    hOut = Hash.new

    h = explain_zhichang_jie_guo()

    # 表頭
    h_biao_tou["xiang_mu_1"] = explain_zhichang_biao_tou(1)
    h_biao_tou["xiang_mu_2"] = explain_zhichang_biao_tou(2)
    h_biao_tou["xiang_mu_3"] = explain_zhichang_biao_tou(3)
    h_biao_tou["xiang_mu_4"] = explain_zhichang_biao_tou(4)
    h_biao_tou["xiang_mu_5"] = explain_zhichang_biao_tou(5)
    h_biao_tou["xiang_mu_6"] = explain_zhichang_biao_tou(6)
    h_biao_tou["xiang_mu_7"] = explain_zhichang_biao_tou(7)
    hOut["biao_tou"] = h_biao_tou
    # 結果
    h_jie_guo["xiang_mu_1"] = explain_zhichang_jie_guo_xiang_mu_1(h)
    h_jie_guo["xiang_mu_2"] = explain_zhichang_jie_guo_xiang_mu_2(h)
    h_jie_guo["xiang_mu_3"] = explain_zhichang_jie_guo_xiang_mu_3(h)
    h_jie_guo["xiang_mu_4"] = explain_zhichang_jie_guo_xiang_mu_4(h)
    h_jie_guo["xiang_mu_5"] = explain_zhichang_jie_guo_xiang_mu_5(h)
    h_jie_guo["xiang_mu_6"] = explain_zhichang_jie_guo_xiang_mu_6(h)
    h_jie_guo["xiang_mu_7"] = explain_zhichang_jie_guo_xiang_mu_7(h)
    hOut["jie_guo"] = h_jie_guo

    hOut["original"] = h

    return hOut
  end
  def explain_zhichang_biao_tou(xiang_mu)
    return Divination.liuyaogua_str("zhichang.title.xiang_mu_#{xiang_mu}")
  end
  # 項目一: 本年度內之職場狀況 使用 sheet:
  def explain_zhichang_jie_guo_xiang_mu_1(h)
    # 1 年之關係
    a = h["nian"].values

    return Divination.explain_merge_content(a)
  end
  # 項目二: 本月內之職場狀況 使用 sheet:(月之關係)
  def explain_zhichang_jie_guo_xiang_mu_2(h)
    # 2 月之關係
    a = h["yue"].values

    return Divination.explain_merge_content(a)
  end
  # 項目三: 人格特性及能力與目前之運勢 使用 sheet:()
  def explain_zhichang_jie_guo_xiang_mu_3(h)
    # 3 日之關係(12 長生) 3.1 日之關係(合生) 3.2 日之關係(暗動)
    a = h["ri"].values

    # 4 世爻人格特性
    a += h["shi_yao"].values
    return Divination.explain_merge_content(a)
  end
  # 項目四: 目前之問題及狀況 使用 sheet:(世爻)
  def explain_zhichang_jie_guo_xiang_mu_4(h)
    # 5 動變爻之關係
    a = h["dong_yao_bian_yao"].values

    return Divination.explain_merge_content(a)
  end
  # 項目五: 勞資關係及職場大環境狀況與直屬長官之關係 使用 sheet:(應爻)
  def explain_zhichang_jie_guo_xiang_mu_5(h)
    # 6 世應關係
    a = h["shi_yin_guan_xi"].values

    return Divination.explain_merge_content(a)
  end
  # 項目六: 你目前之各種心理感受之問題與狀況 使用 sheet:(應爻)
  def explain_zhichang_jie_guo_xiang_mu_6(h)
    # 7 貴人驛馬
    a = h["gui_ren_yi_ma"].values
    # 8 空亡羊刃
    a += h["kong_wang_yang_ren"].values
    return Divination.explain_merge_content(a)
  end
  # 項目七: 特殊問題之說明 使用 sheet:(特殊卦)
  def explain_zhichang_jie_guo_xiang_mu_7(h)
    # 9 卦象說明
    a = h["gua_xiang"].values

    return Divination.explain_merge_content(a)
  end

  def explain_zhichang_jie_guo()
    h = Hash.new

    # 1年之關係
    h["nian"] = ex_zhichang_year()
    # 2月之關係
    h["yue"] = ex_zhichang_month()
    # 日之關係(12長生) 日之關係(合生) 日之關係(暗動)
    h["ri"] = ex_zhichang_day()
    # 世爻人格特性
    h["shi_yao"] = ex_zhichang_shi_yao()
    # 動變爻之關係
    h["dong_yao_bian_yao"] = ex_zhichang_dong_yao_yu_bian_yao()
    # 世應關係
    h["shi_yin_guan_xi"] = ex_zhichang_shi_yin_guan_xi()
    # 貴人驛馬
    h["gui_ren_yi_ma"] = ex_zhichang_gui_ren_yi_ma()
    # 空亡羊刃
    h["kong_wang_yang_ren"] = ex_zhichang_kong_wang_yang_ren()
    # 本卦變卦之卦象說明
    h["gua_xiang"] = ex_zhichang_gua_image()

    return h
  end
  def Divination.ex_zhichang_str(key)
    return Divination.liuyaogua_str("zhichang.#{key}")
  end

  # 1 年之關係
  def ex_zhichang_year()
    h = Hash.new
    year_earth = Xdate.GetGanZhiEarth(@nGanZhiYear)
    month_earth = Xdate.GetGanZhiEarth(@nGanZhiMonth)
    day_earth = Xdate.GetGanZhiEarth(@nGanZhiDay)
    shi_yao_di_zhi = [@shi_earth]
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(@all_ylqgzws)

    # 六合  年 世爻
    h["liu_he"] = Divination.ex_zc_y_liu_he(year_earth,ben_gua_di_zhi)
    # 六沖  年 世爻(全部一樣)
    h["liu_chong"] = Divination.ex_zc_y_liu_chong(year_earth,ben_gua_di_zhi)
    # 互刑  年 爻支(全部一樣)
    h["hu_xing"] = Divination.ex_zc_y_hu_xing(year_earth,ben_gua_di_zhi)
    # 三刑  年   爻支1 爻支2(全部一樣)
    h["san_xing"] = Divination.ex_zc_y_san_xing(year_earth,ben_gua_di_zhi)
    # 入年墓 年 爻支1 爻支2 爻支3 爻支4 爻支5 爻支6
    h["ru_nian_mu"] = Divination.ex_zc_y_ru_nian_mu(year_earth,ben_gua_di_zhi)
    # 三合  年干/月干/日辰/爻支     爻支-六獸 爻支-六親
    h["san_he"] = Divination.ex_zc_y_san_he(year_earth,month_earth,day_earth,@all_ylqgzws,@liu_shous)

    return h
  end
  def Divination.ex_zc_year_str(key)
    return Divination.ex_zhichang_str("nian.#{key}")
  end
    # 六合  年 世爻
  def Divination.ex_zc_year_liu_he_str(dz1,dz2)
    key = Divination.dizhi_keys(dz1,dz2)
    return Divination.ex_zc_year_str("liu_he.#{key}")
  end
  def Divination.ex_zc_y_liu_he(year_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_liu_he?(year_earth,a[i])) then
        return Divination.ex_zc_year_liu_he_str(year_earth,a[i])
      end
    end
    return ""
  end
    # 六沖  年 世爻
  def Divination.ex_zc_year_liu_chong_str(dz1,dz2)
    key = Divination.dizhi_keys(dz1,dz2)
    return Divination.ex_zc_year_str("liu_chong.#{key}")
  end
  def Divination.ex_zc_y_liu_chong(year_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_liu_chong?(year_earth,a[i])) then
        return Divination.ex_zc_year_liu_chong_str(year_earth,a[i])
      end
    end
    return ""
  end
    # 互刑  年 爻支
  def Divination.ex_zc_year_hu_xing_str(dz1,dz2)
    key = Divination.dizhi_keys(dz1,dz2)
    return Divination.ex_zc_year_str("hu_xing.#{key}")
  end
  def Divination.ex_zc_y_hu_xing(year_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_hu_xing?(year_earth,a[i])) then
        return Divination.ex_zc_year_hu_xing_str(year_earth,a[i])
      end
    end
    return ""
  end
    # 三刑  年   爻支1 爻支2(全部一樣)
  def Divination.ex_zc_year_san_xing_str()
    return Divination.ex_zc_year_str("san_xing.desc")
  end
  def Divination.ex_zc_y_san_xing(year_earth,ben_gua_di_zhi)
    a1 = Divination.San_Xing([year_earth],ben_gua_di_zhi)
    a1.each_index do |i|
      a = a1[i]
      if (a.index(year_earth) != nil) then
        return Divination.ex_zc_year_san_xing_str()
      end
    end
    return ""
  end
    # 入年墓 年 爻支1 爻支2 爻支3 爻支4 爻支5 爻支6
  def Divination.ex_zc_year_ru_nian_mu_str()
    return Divination.ex_zc_year_str("ru_nian_mu.desc")
  end
  def Divination.ex_zc_y_ru_nian_mu(year_earth,ben_gua_di_zhi)
    a = Divination.ru_mu_array(year_earth,ben_gua_di_zhi)
    if (a.length > 0) then
      return Divination.ex_zc_year_ru_nian_mu_str()
    end
    return ""
  end
    # 三合  年干/月干/日辰/爻支     爻支-六獸 爻支-六親
  def Divination.ex_zc_year_san_he_str(dz1,dz2,dz3,liu_shou,liu_qin)
    key = Divination.dizhi_keys(dz1,dz2,dz3)
    liu_shou_key = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_zc_year_str("san_he.#{key}.#{liu_shou_key}")
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_y_san_he(year_dizhi,month_dizhi,day_dizhi,all_ylqgzws,liu_shous)
    a,liu_shou,liu_qin = Divination.explain_san_he([year_dizhi],all_ylqgzws,liu_shous)
    # a,liu_shou,liu_qin = Divination.ex_zc_san_he(year_dizhi,month_dizhi,day_dizhi,all_ylqgzws,liu_shous)
    if (a != nil) then
      return Divination.ex_zc_year_san_he_str(a[0],a[1],a[2],liu_shou,liu_qin)
    end
    return ""
  end
    # 三合
  @@Zhichang_San_He = [
                [9,1,5],
                [6,10,2],
                [3,7,11],
                [12,4,8]
              ]
  def Divination.ex_zc_san_he(year_dizhi,month_dizhi,day_dizhi,all_ylqgzws,liu_shous)
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(all_ylqgzws)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)

    date_dizhi = [year_dizhi,month_dizhi,day_dizhi]
    all_dizhi = date_dizhi + ben_gua_di_zhi
    a2 = all_dizhi.uniq
    @@Zhichang_San_He.each_index do |i|
      a = @@Zhichang_San_He[i]
      a3 = a.select { |n| a2.index(n) != nil  }
      if (a3 == a) then
        san_he_middle = a[1]
        ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,san_he_middle)
        if (ylqgzw == nil) then
          wu_xing = Divination.Earth_Five(san_he_middle)
          ylqgzw = Divination.par_find_ylqgzw_by_wu_xing(ylqgzws,wu_xing)
        end
        yao = Divination.fu_shen_yao(ylqgzw)
        liu_shou = Divination.liu_shou(liu_shous,yao)
        liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
        return a,liu_shou,liu_qin
      end
    end
    return nil,0,0
  end

  # 2 月之關係
  def ex_zhichang_month()
    h = Hash.new
    year_earth = Xdate.GetGanZhiEarth(@nGanZhiYear)
    month_earth = Xdate.GetGanZhiEarth(@nGanZhiMonth)
    day_earth = Xdate.GetGanZhiEarth(@nGanZhiDay)
    shi_yao_di_zhi = [@shi_earth]
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(@all_ylqgzws)

    h["liu_he"] = Divination.ex_zc_m_liu_he(month_earth,ben_gua_di_zhi)
    h["liu_chong"] = Divination.ex_zc_m_liu_chong(month_earth,ben_gua_di_zhi)
    h["hu_xing"] = Divination.ex_zc_m_hu_xing(month_earth,ben_gua_di_zhi)
    h["san_xing"] = Divination.ex_zc_m_san_xing(month_earth,@all_ylqgzws)
    h["ru_yue_mu"] = Divination.ex_zc_m_ru_yue_mu(month_earth,ben_gua_di_zhi)
    # 三合  年干/月干/日辰/爻支     爻支-六獸 爻支-六親
    h["san_he"] = Divination.ex_zc_m_san_he(year_earth,month_earth,day_earth,@all_ylqgzws,@liu_shous)

    return h
  end
  def Divination.ex_zc_month_str(key)
    return Divination.ex_zhichang_str("yue.#{key}")
  end
    # 月合（六合） 月 世爻
  def Divination.ex_zc_month_liu_he_str(dz1,dz2)
    key = Divination.dizhi_keys(dz1,dz2)
    return Divination.ex_zc_month_str("liu_he.#{key}")
  end
  def Divination.ex_zc_m_liu_he(month_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_liu_he?(month_earth,a[i])) then
        return Divination.ex_zc_month_liu_he_str(month_earth,a[i])
      end
    end
    return ""
  end
    # 月破(六沖) 月 世爻
  def Divination.ex_zc_month_liu_chong_str(dz1,dz2)
    key = Divination.dizhi_keys(dz1,dz2)
    return Divination.ex_zc_month_str("liu_chong.#{key}")
  end
  # 六沖
  # 年 衝  月 衝  日 衝
  # 地支 子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
  @@Zhichang_Chung6 = [7,-1,9,10,11,12,1,2,3,4,-1,6]
  # 沒有 丑未  戌辰
  def Divination.ex_zc_liu_chong(nEarth)
    return @@Zhichang_Chung6[nEarth - 1]
  end
  def Divination.ex_zc_is_liu_chong?(nEarth,nEarth2)
    return Divination.ex_zc_liu_chong(nEarth) == nEarth2
  end
  def Divination.ex_zc_m_liu_chong(month_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.ex_zc_is_liu_chong?(month_earth,a[i])) then
        return Divination.ex_zc_month_liu_chong_str(month_earth,a[i])
      end
    end
    return ""
  end
    # 月互刑 月 世爻
  def Divination.ex_zc_month_hu_xing_str(yue_dz,shi_dz)
    yue_dz_key = Divination.dizhi_key(yue_dz)
    shi_dz_key = Divination.dizhi_key(shi_dz)
    return Divination.ex_zc_month_str("hu_xing.yue_#{yue_dz_key}_shi_#{shi_dz_key}")
  end
  def Divination.ex_zc_m_hu_xing(month_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_hu_xing?(month_earth,a[i])) then
        return Divination.ex_zc_month_hu_xing_str(month_earth,a[i])
      end
    end
    return ""
  end
    # 月三刑 月 世爻 爻支2
  def Divination.ex_zc_month_san_xing_str(dz1,dz2,dz3)
    key = Divination.dizhi_keys(dz1,dz2,dz3)
    return Divination.ex_zc_month_str("san_xing.#{key}")
  end
  def Divination.ex_zc_m_san_xing(month_dizhi,all_ylqgzws)
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(all_ylqgzws)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)

    a1 = Divination.San_Xing([month_dizhi],ben_gua_di_zhi)
    a1.each_index do |i|
      a = a1[i]
      if (a.index(month_dizhi) != nil) then
        return Divination.ex_zc_month_san_xing_str(a[0],a[1],a[2])
      end
    end
    return ""
  end
    # 入月墓   月   爻支1 爻支2 爻支3 爻支4 爻支5 爻支6
  def Divination.ex_zc_m_ru_yue_mu_str()
    return Divination.ex_zc_month_str("ru_yue_mu.desc")
  end
  @@Zhichang_Yue_ru_yue_mu = {
    # 月支   世爻地支， [其他爻支]
    "e2" => [9,10],
    "e5" => [1,12,5,11,2,8],
    "e8" => [3,4],
    "e11" => [6,7]
  }
  def Divination.ex_zc_m_ru_mu_array(nEarth,ben_gua_di_zhi)
    a = @@Zhichang_Yue_ru_yue_mu["e#{nEarth}"]
    if (a != nil) then
      a2 = a & ben_gua_di_zhi
      return a2
    end
    return []
  end
  def Divination.ex_zc_m_ru_yue_mu(month_earth,ben_gua_di_zhi)
    a = Divination.ex_zc_m_ru_mu_array(month_earth,ben_gua_di_zhi)
    if (a.length > 0) then
      return Divination.ex_zc_m_ru_yue_mu_str()
    end
    return ""
  end
    # 三合  年干/月干/日辰/爻支     爻支-六獸 爻支-六親
  def Divination.ex_zc_month_san_he_str(dz1,dz2,dz3,liu_shou,liu_qin)
    key = Divination.dizhi_keys(dz1,dz2,dz3)
    liu_shou_key = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_zc_month_str("san_he.#{key}.#{liu_shou_key}")
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_m_san_he(year_dizhi,month_dizhi,day_dizhi,all_ylqgzws,liu_shous)
    a,liu_shou,liu_qin = Divination.explain_san_he([month_dizhi],all_ylqgzws,liu_shous)
    # a,liu_shou,liu_qin = Divination.ex_zc_san_he(year_dizhi,month_dizhi,day_dizhi,all_ylqgzws,liu_shous)
    if (a != nil) then
      return Divination.ex_zc_month_san_he_str(a[0],a[1],a[2],liu_shou,liu_qin)
    end
    return ""
  end

    # 日之關係(12長生) 日之關係(合生) 日之關係(暗動)
  def ex_zhichang_day()
    h = Hash.new
    day_earth = Xdate.GetGanZhiEarth(@nGanZhiDay)
    month_earth = Xdate.GetGanZhiEarth(@nGanZhiMonth)
    chang_sheng = Divination.chang_sheng(@shi_five,day_earth)
    liu_shou = Divination.liu_shou(@liu_shous,@shi_yao)
    ben_gua_liu_qins = Divination.par_ben_gua_liu_qins(@all_ylqgzws)
    liu_qin = Divination.gua_yao_liu_qin(ben_gua_liu_qins,@shi_yao)

    # 前置詞
    h["qian_zhi_ci"] = Divination.ex_zc_day_pre_str()

    # 日之關係(12長生）
    h["chang_sheng"] = Divination.ex_zc_day_shi_chang_sheng(day_earth,@shi_earth,chang_sheng)

    # 日之關係(合生)
    h["ri_he"] = Divination.ex_zc_day_2_he(day_earth,month_earth,@shi_earth,@par_five_levels,@par_chang_sheng,liu_shou,liu_qin)

    # 日之關係(暗動)
    h["an_dong"] = Divination.ex_zc_day_an_dong(day_earth,@shi_earth,liu_shou,liu_qin)

    return h
  end
  def Divination.ex_zc_day_str(key)
    return Divination.ex_zhichang_str("ri.#{key}")
  end
  def Divination.ex_zc_day_pre_str()
    return Divination.ex_zc_day_str("pre")
  end
      # 日之 12 長生 世爻
  def Divination.ex_zc_day_shi_str(day_dz1,shi_dz2)
    # 日辰
    key = Divination.dizhi_keys(day_dz1)
    s = Divination.ex_zc_day_str("shi.#{key}")

    # 世爻爻支 子 丑 。。⋯⋯ 亥共12個
    return Divination.extract_str(s,shi_dz2)
  end
  # @@Zhichang_Day_DiZhi_ChangSheng = [
  #   [帝旺,帝旺,沐浴,沐浴,帝旺,胎,胎,帝旺,死,死,帝旺,帝旺],   # 子
  #   [衰,衰,冠帶,冠帶,衰,養,養,衰,墓,墓,衰,衰],   # 丑
  #   [病,病,臨官,臨官,病,長生,長生,病,絕,胎,病,病],   # 寅
  #   [死,死,帝旺,帝旺,死,沐浴,沐浴,死,絕,胎,死,死],   # 卯
  #   [墓,墓,衰,衰,墓,冠帶,冠帶,墓,養,養,墓,墓],   # 辰
  #   [絕,絕,病,病,絕,臨官,臨官,絕,長生,長生,絕,絕],   # 巳
  #   [胎,胎,死,死,胎,帝旺,帝旺,胎,沐浴,沐浴,胎,胎],   # 午
  #   [養,養,墓,墓,養,衰,衰,養,冠帶,冠帶,養,養],   # 未
  #   [長生,長生,絕,絕,長生,病,病,長生,臨官,臨官,長生,長生],   # 申
  #   [沐浴,沐浴,胎,胎,沐浴,死,死,沐浴,帝旺,帝旺,沐浴,沐浴],   # 酉
  #   [冠帶,冠帶,養,養,冠帶,墓,墓,冠帶,衰,衰,冠帶,冠帶],   # 戌
  #   [臨官,臨官,長生,長生,臨官,絕,絕,臨官,病,病,臨官,臨官]   # 亥
  #                                 ]
  @@Zhichang_Day_DiZhi_ChangSheng = [
    [5,5,2,2,5,11,11,5,8,8,5,5],
    [6,6,3,3,6,12,12,6,9,9,6,6],
    [7,7,4,4,7,1,1,7,10,11,7,7],
    [8,8,5,5,8,2,2,8,10,11,8,8],
    [9,9,6,6,9,3,3,9,12,12,9,9],
    [10,10,7,7,10,4,4,10,1,1,10,10],
    [11,11,8,8,11,5,5,11,2,2,11,11],
    [12,12,9,9,12,6,6,12,3,3,12,12],
    [1,1,10,10,1,7,7,1,4,4,1,1],
    [2,2,11,11,2,8,8,2,5,5,2,2],
    [3,3,12,12,3,9,9,3,6,6,3,3],
    [4,4,1,1,4,10,10,4,7,7,4,4]
                                  ]
  def Divination.ex_zc_day_shi_chang_sheng(day_dz1,shi_dz2,chang_sheng)
    a = @@Zhichang_Day_DiZhi_ChangSheng[day_dz1 - 1]
    if (a[shi_dz2 - 1] == chang_sheng) then
      return Divination.ex_zc_day_shi_str(day_dz1,shi_dz2)
    else
      return ""
    end
  end
  # 日之關係(合生)
  def Divination.ex_zc_day_2_he(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    # 日合生
    s = Divination.ex_zc_day_he_san(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    if (s != "") then
      return [Divination.ex_zc_day_he_san_title(),s]
    end
    # 日合
    s = Divination.ex_zc_day_he(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    if (s != "") then
      return [Divination.ex_zc_day_he_title(),s]
    end
    # 日合剋
    s = Divination.ex_zc_day_he_ke(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    if (s != "") then
      return [Divination.ex_zc_day_he_ke_title(),s]
    end
    return ["",""]
  end
    # 日合生
    # ※除外：當世爻逢空亡(1。 於月令為死 2。 日辰為絕)時下列六合將不成立
    # 日之關係 日辰 爻支 爻支-六獸 爻支-六親
    # 父母 兄弟 子孫 妻財 官鬼
  def Divination.ex_zc_day_he_san_title()
    return Divination.ex_zc_day_str("he_san.title")
  end
  def Divination.ex_zc_day_he_san_str(day_dz1,yao_dz2,liu_shou,liu_qin)
    # 日辰 爻支
    key = Divination.dizhi_keys(day_dz1,yao_dz2)
    key2 = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_zc_day_str("he_san.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_day_he_san(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    tf = Divination.ex_jk_ky_kong_wang_true_false(day_earth,month_earth,par_five_levels,par_chang_sheng)
    if (!tf) then
      # 不為空亡時，才看六合生
      if (Divination.par_is_he_san?(day_earth,shi_earth)) then
        return Divination.ex_zc_day_he_san_str(day_earth,shi_earth,liu_shou,liu_qin)
      end
    end
    return ""
  end

    # 日合
  def Divination.ex_zc_day_he_title()
    return Divination.ex_zc_day_str("he.title")
  end
  def Divination.ex_zc_day_he_str(day_dz1,yao_dz2,liu_shou,liu_qin)
    # 日辰 爻支
    key = Divination.dizhi_keys(day_dz1,yao_dz2)
    key2 = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_zc_day_str("he.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_day_he(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    tf = Divination.ex_jk_ky_kong_wang_true_false(day_earth,month_earth,par_five_levels,par_chang_sheng)
    if (!tf) then
      # 不為空亡時，才看六合
      if (Divination.par_is_he?(day_earth,shi_earth)) then
        return Divination.ex_zc_day_he_str(day_earth,shi_earth,liu_shou,liu_qin)
      end
    end
    return ""
  end
    # 日合剋
  def Divination.ex_zc_day_he_ke_title()
    return Divination.ex_zc_day_str("he_ke.title")
  end
  def Divination.ex_zc_day_he_ke_str(day_dz1,yao_dz2,liu_shou,liu_qin)
    # 日辰 爻支
    key = Divination.dizhi_keys(day_dz1,yao_dz2)
    key2 = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_zc_day_str("he_ke.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_day_he_ke(day_earth,month_earth,shi_earth,par_five_levels,par_chang_sheng,liu_shou,liu_qin)
    tf = Divination.ex_jk_ky_kong_wang_true_false(day_earth,month_earth,par_five_levels,par_chang_sheng)
    if (!tf) then
      # 不為空亡時，才看六合
      if (Divination.par_is_he_ke?(day_earth,shi_earth)) then
        return Divination.ex_zc_day_he_ke_str(day_earth,shi_earth,liu_shou,liu_qin)
      end
    end
    return ""
  end
  # 日之關係(暗動)
    # 暗動 日破
  def Divination.ex_zc_day_an_dong_str(day_dz1,yao_dz2,liu_shou,liu_qin)
    # 日辰 爻支
    key = Divination.dizhi_keys(day_dz1,yao_dz2)
    key2 = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_zc_day_str("an_dong.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_day_an_dong(day_earth,shi_earth,liu_shou,liu_qin)
    if (Divination.par_is_liu_chong?(day_earth,shi_earth)) then
      return Divination.ex_zc_day_an_dong_str(day_earth,shi_earth,liu_shou,liu_qin)
    else
      return ""
    end
  end

    # 世爻人格特性
  def ex_zhichang_shi_yao()
    h = Hash.new

    liu_shou = Divination.liu_shou(@liu_shous,@shi_yao)
    di_zhi = @shi_earth
    wang_xiang_xiu_qiu_si = Divination.par_find_five_level(@par_five_levels,@shi_five)

    # 世持官鬼需抓此資料：
    h["shi_chi_guan_gui"] = Divination.ex_zc_shi_yao_shi_chi_guan_gui(di_zhi,wang_xiang_xiu_qiu_si,@all_ylqgzws)
    # 人格特性
    h["ren_ge"] = Divination.ex_zc_shi_yao_ren_ge(liu_shou,di_zhi,wang_xiang_xiu_qiu_si,@all_ylqgzws)

    return h
  end
  def Divination.ex_zc_shi_yao_shi_chi_guan_gui_str(wang_xiang_xiu_qiu_si)
    key2 = Divination.wang_xiang_xiu_qiu_si_key(wang_xiang_xiu_qiu_si)
    return Divination.ex_zhichang_str("shi_yao.shi_chi_guan_gui.#{key2}")
  end
  def Divination.ex_zc_shi_yao_ren_ge_str(liu_shou,wang_xiang_xiu_qiu_si,liu_qin)
    # 世爻六獸  世爻六親  旺     相     休     囚     死
    key = Divination.liu_shou_key(liu_shou)
    key2 = Divination.wang_xiang_xiu_qiu_si_key(wang_xiang_xiu_qiu_si)
    s = Divination.ex_zhichang_str("shi_yao.#{key2}.#{key}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_shi_yao_ren_ge(liu_shou,di_zhi,wang_xiang_xiu_qiu_si,all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_in_all_yao_ylqgzws_by_di_zhi(di_zhi,all_ylqgzws)
    liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
    return Divination.ex_zc_shi_yao_ren_ge_str(liu_shou,wang_xiang_xiu_qiu_si,liu_qin)
  end
  # 世持官鬼需抓此資料：
  def Divination.ex_zc_shi_yao_shi_chi_guan_gui(di_zhi,wang_xiang_xiu_qiu_si,all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_in_all_yao_ylqgzws_by_di_zhi(di_zhi,all_ylqgzws)
    liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
    if (liu_qin == Liu_Qin_3) then
      return Divination.ex_zc_shi_yao_shi_chi_guan_gui_str(wang_xiang_xiu_qiu_si)
    end
    return ""
  end

  # 動變爻之關係
    # 動爻  變爻  動爻六親  變爻六親
  def ex_zhichang_dong_yao_yu_bian_yao()
    h = Hash.new

    ben_gua_ylqgzws = Divination.par_ben_gua_ylqgzws(@all_ylqgzws)
    bian_gua_ylqgzws = Divination.par_bian_gua_ylqgzws(@all_ylqgzws)

    # 動爻  變爻  動爻六親  變爻六親
    h["liu_qin"] = Divination.ex_zc_dong_yao_yu_bian_yao_liu_qin(ben_gua_ylqgzws,bian_gua_ylqgzws)

    return h
  end
  def Divination.ex_zc_dong_yao_yu_bian_yao_str(key)
    return Divination.ex_zhichang_str("dong_yao_yu_bian_yao.#{key}")
  end
  def Divination.ex_zc_dong_yao_yu_bian_yao_liu_qin_str(dz1,dz2,liu_qin)
    # 世爻六獸  世爻六親  旺     相     休     囚     死
    key = Divination.dizhi_key(dz1)
    key2 = Divination.dizhi_key(dz2)
    s = Divination.ex_zhichang_str("dong_yao_yu_bian_yao.#{key}.#{key2}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_dong_yao_yu_bian_yao_liu_qin(ben_gua_ylqgzws,bian_gua_ylqgzws)
    aOut = Array.new
    bian_gua_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_gua_ylqgzws[i]
      bian_yao = Divination.fu_shen_yao(bian_ylqgzw)
      bian_di_zhi = Divination.fu_shen_di_zhi(bian_ylqgzw)
      bian_liu_qin = Divination.fu_shen_liu_qin(bian_ylqgzw)
      ben_ylqgzw = Divination.par_find_ylqgzw_by_yao(ben_gua_ylqgzws,bian_yao)
      ben_di_zhi = Divination.fu_shen_di_zhi(ben_ylqgzw)
      ben_liu_qin = Divination.fu_shen_liu_qin(ben_ylqgzw)
      s = Divination.ex_zc_dong_yao_yu_bian_yao_liu_qin_str(ben_di_zhi,bian_di_zhi,ben_liu_qin)
      aOut.push(s)
    end
    return aOut.uniq
  end

  # 世應關係
  def ex_zhichang_shi_yin_guan_xi()
    h = Hash.new

    liu_shou = Divination.liu_shou(@liu_shous,@yin_yao)
    wang_xiang_xiu_qiu_si = Divination.par_find_five_level(@par_five_levels,@yin_five)

    h["shi_yao_yu_yin_yao"] = Divination.ex_zc_shi_yin_guan_xi_sheng_ke(liu_shou,wang_xiang_xiu_qiu_si,@yin_earth,@shi_earth,@all_ylqgzws)

    h["dong_yao_yu_shi_yao"] = Divination.ex_zc_dong_yao_yu_shi_yao_sheng_ke(@all_ylqgzws,@shi_earth)

    return h
  end
  def Divination.ex_zc_shi_yin_guan_xi_sheng_ke(liu_shou,wang_xiang_xiu_qiu_si,yin_earth,shi_earth,all_ylqgzws)
    # 世應比和
    s = Divination.ex_zc_shi_yin_bi_he(liu_shou,wang_xiang_xiu_qiu_si,yin_earth,shi_earth,all_ylqgzws)
    if (s != "") then
      return [Divination.ex_zc_shi_yin_guan_xi_shi_yin_bi_he_title(),s]
    end
    # 應爻剋世爻
    s = Divination.ex_zc_shi_yin_guan_xi_yin_ke_shi(liu_shou,yin_earth,shi_earth)
    if (s != "") then
      return [Divination.ex_zc_shi_yin_guan_xi_yin_ke_shi_title(),s]
    end
    # 應爻生世爻
    s = Divination.ex_zc_shi_yin_guan_xi_yin_sheng_shi(liu_shou,yin_earth,shi_earth)
    if (s != "") then
      return [Divination.ex_zc_shi_yin_guan_xi_yin_sheng_shi_title(),s]
    end
    return ["",""]
  end
    # 世應比和
  def Divination.ex_zc_shi_yin_guan_xi_shi_yin_bi_he_title()
    return Divination.ex_zhichang_str("shi_yin_guan_xi.shi_yin_bi_he.title")
  end
  def Divination.ex_zc_shi_yin_bi_he_str(liu_shou,wang_xiang_xiu_qiu_si,liu_qin)
    # 應爻六親  旺     相     休     囚     死
    liu_qin_key = Divination.liu_qin_key(liu_qin)
    wang_xiang_xiu_qiu_si_key = Divination.wang_xiang_xiu_qiu_si_key(wang_xiang_xiu_qiu_si)
    s = Divination.ex_zhichang_str("shi_yin_guan_xi.shi_yin_bi_he.#{wang_xiang_xiu_qiu_si_key}.#{liu_qin_key}")

    # 應爻六獸  ，共六個
    return Divination.extract_str(s,liu_shou)
  end
  def Divination.ex_zc_shi_yin_bi_he(liu_shou,wang_xiang_xiu_qiu_si,yin_dz,shi_dz,all_ylqgzws)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    yin_ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,yin_dz)
    yin_liu_qin = Divination.fu_shen_liu_qin(yin_ylqgzw)
    shi_ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,shi_dz)
    shi_liu_qin = Divination.fu_shen_liu_qin(shi_ylqgzw)

    if (yin_liu_qin == shi_liu_qin) then
      return Divination.ex_zc_shi_yin_bi_he_str(liu_shou,wang_xiang_xiu_qiu_si,yin_liu_qin)
    else
      return ""
    end
  end

  # 應爻剋世爻
  def Divination.ex_zc_shi_yin_guan_xi_yin_ke_shi_str(liu_shou,dz1,dz2)
    # 應爻六親  旺     相     休     囚     死
    dz1_key = Divination.dizhi_key(dz1)
    dz2_key = Divination.dizhi_key(dz2)
    s = Divination.ex_zhichang_str("shi_yin_guan_xi.yin_ke_shi.#{dz1_key}_ke_#{dz2_key}")

    # 應爻六獸  ，共六個
    return Divination.extract_str(s,liu_shou)
  end
  def Divination.ex_zc_shi_yin_guan_xi_yin_ke_shi_title()
    return Divination.ex_zhichang_str("shi_yin_guan_xi.yin_ke_shi.title")
  end
  # 應爻地支,辰,戌,丑,未  剋 世爻地支 子 亥
  # 應爻地支,寅 卯  剋 世爻地支 辰戌丑未
  # 應爻地支,申 酉  剋 世爻地支 寅卯
  # 應爻地支,子 亥  剋 世爻地支 巳午
  # 應爻地支,巳 午  剋 世爻地支 申酉
  @@Zhichang_Sygx_Yin_ke_Shi_Data = [
                           [[[5,11,2,8],[1,12]],[5,1]],
                           [[[3,4],[5,11,2,8]],[3,5]],
                           [[[9,10],[3,4]],[9,3]],
                           [[[1,12],[6,7]],[1,6]],
                           [[[6,7],[9,10]],[6,9]]
                           ]
  def Divination.ex_zc_shi_yin_guan_xi_yin_ke_shi_check(yin_dz,shi_dz)
    @@Zhichang_Sygx_Yin_ke_Shi_Data.each_index do |i|
      a = @@Zhichang_Sygx_Yin_ke_Shi_Data[i]
      ke = a[0]
      yin = ke[0]
      shi = ke[1]
      if (yin.index(yin_dz) != nil && shi.index(shi_dz) != nil) then
        return true,a[1][0],a[1][1]
      end
    end
    return false,nil
  end
  def Divination.ex_zc_shi_yin_guan_xi_yin_ke_shi(liu_shou,yin_dz,shi_dz)
    is_ke,dz1,dz2 = Divination.ex_zc_shi_yin_guan_xi_yin_ke_shi_check(yin_dz,shi_dz)
    if (is_ke) then
      return Divination.ex_zc_shi_yin_guan_xi_yin_ke_shi_str(liu_shou,dz1,dz2)
    else
      return ""
    end
  end

  # 應爻生世爻
  def Divination.ex_zc_shi_yin_guan_xi_yin_sheng_shi_str(liu_shou,dz1,dz2)
    # 應爻六親  旺     相     休     囚     死
    dz1_key = Divination.dizhi_key(dz1)
    dz2_key = Divination.dizhi_key(dz2)
    s = Divination.ex_zhichang_str("shi_yin_guan_xi.yin_sheng_shi.#{dz1_key}_sheng_#{dz2_key}")

    # 應爻六獸  ，共六個
    return Divination.extract_str(s,liu_shou)
  end
  def Divination.ex_zc_shi_yin_guan_xi_yin_sheng_shi_title()
    return Divination.ex_zhichang_str("shi_yin_guan_xi.yin_sheng_shi.title")
  end
  # 應爻地支,辰,戌,丑,未  生 世爻地支 申酉
  # 應爻地支,寅 卯  生 世爻地支 巳午
  # 應爻地支,申 酉  生 世爻地支 子  亥
  # 應爻地支,子 亥  生 世爻地支 寅卯
  # 應爻地支,巳 午  生 世爻地支 辰戌丑未
  @@Zhichang_Sygx_Yin_Sheng_Shi_Data = [
                           [[[5,11,2,8],[9,10]],[5,9]],
                           [[[3,4],[6,7]],[3,6]],
                           [[[9,10],[1,12]],[9,1]],
                           [[[1,12],[3,4]],[1,3]],
                           [[[6,7],[5,11,2,8]],[6,5]]
                           ]
  def Divination.ex_zc_shi_yin_guan_xi_yin_sheng_shi_check(yin_dz,shi_dz)
    @@Zhichang_Sygx_Yin_Sheng_Shi_Data.each_index do |i|
      a = @@Zhichang_Sygx_Yin_Sheng_Shi_Data[i]
      ke = a[0]
      yin = ke[0]
      shi = ke[1]
      if (yin.index(yin_dz) != nil && shi.index(shi_dz) != nil) then
        return true,a[1][0],a[1][1]
      end
    end
    return false,nil,nil
  end
  def Divination.ex_zc_shi_yin_guan_xi_yin_sheng_shi(liu_shou,yin_dz,shi_dz)
    is_sheng,dz1,dz2 = Divination.ex_zc_shi_yin_guan_xi_yin_sheng_shi_check(yin_dz,shi_dz)
    if (is_sheng) then
      return Divination.ex_zc_shi_yin_guan_xi_yin_sheng_shi_str(liu_shou,dz1,dz2)
    else
      return ""
    end
  end
  # 動爻與世爻
  def Divination.ex_zc_dong_yao_yu_shi_yao_sheng_ke(all_ylqgzws,shi_earth)
    # 動爻剋世爻
    s = Divination.ex_zc_dong_yao_yu_shi_yao_dong_sheng_shi(all_ylqgzws,shi_earth)
    if (s != "") then
      return [Divination.ex_zc_dong_yao_yu_shi_yao_dong_sheng_shi_title(),s]
    end
    # 動爻生世爻
    s = Divination.ex_zc_dong_yao_yu_shi_yao_dong_ke_shi(all_ylqgzws,shi_earth)
    if (s != "") then
      return [Divination.ex_zc_dong_yao_yu_shi_yao_dong_ke_shi_title(),s]
    end
    return ["",""]
  end
  def Divination.ex_zc_dong_yao_yu_shi_yao_dong_sheng_shi_str(lq1,lq2)
    # 應爻六親  旺     相     休     囚     死
    lq1_key = Divination.liu_qin_key(lq1)
    lq2_key = Divination.liu_qin_key(lq2)
    return Divination.ex_zhichang_str("shi_yin_guan_xi.dong_yao_sheng_shi_yao.#{lq1_key}_#{lq2_key}")
  end
  def Divination.ex_zc_dong_yao_yu_shi_yao_dong_sheng_shi_title()
    return Divination.ex_zhichang_str("shi_yin_guan_xi.dong_yao_sheng_shi_yao.title")
  end
  # 動爻生世爻
  # 判斷動爻與世爻的六親
  @@Zhichang_Sygx_Dong_Sheng_Shi_Data = [
                           [1,2], # 父母 生 兄弟
                           [2,5], # 兄弟 生 子孫
                           [5,4], # 子孫 生 妻財
                           [4,3], # 妻財 生 官鬼
                           [3,1]  # 官鬼 生 父母
                           ]
  def Divination.ex_zc_dong_yao_yu_shi_yao_dong_sheng_shi_check(dong_dz,shi_dz,dong_liu_qin,shi_liu_qin)
    # 辰,戌,丑,未  生 申酉
    # di_zhis = @@Sygx_Yin_Sheng_Shi_Data[0][0]
    @@Zhichang_Sygx_Dong_Sheng_Shi_Data.each_index do |i|
      a = @@Zhichang_Sygx_Dong_Sheng_Shi_Data[i]
      # if ((a == [dong_liu_qin,shi_liu_qin]) && (di_zhis[0].index(dong_dz) != nil && di_zhis[1].index(shi_dz))) then
      if (a == [dong_liu_qin,shi_liu_qin]) then
        return true,a[0],a[1]
      end
    end
    return false,nil,nil
  end
  def Divination.ex_zc_dong_yao_yu_shi_yao_dong_sheng_shi(all_ylqgzws,shi_dz)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    shi_ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,shi_dz)
    shi_liu_qin = Divination.fu_shen_liu_qin(shi_ylqgzw)

    dong_ylqgzws = Divination.par_find_dong_yao_ylqgzws(all_ylqgzws)
    dong_ylqgzws.each_index do |i|
      ylqgzw = dong_ylqgzws[i]
      dong_liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      dong_dz = Divination.fu_shen_di_zhi(ylqgzw)
      is_sheng,lq1,lq2 = Divination.ex_zc_dong_yao_yu_shi_yao_dong_sheng_shi_check(dong_dz,shi_dz,dong_liu_qin,shi_liu_qin)
      if (is_sheng) then
        return Divination.ex_zc_dong_yao_yu_shi_yao_dong_sheng_shi_str(lq1,lq2)
      end
    end
    return ""
  end

  # 動爻剋世爻
  def Divination.ex_zc_dong_yao_yu_shi_yao_dong_ke_shi_str(lq1,lq2)
    # 應爻六親  旺     相     休     囚     死
    lq1_key = Divination.liu_qin_key(lq1)
    lq2_key = Divination.liu_qin_key(lq2)
    return Divination.ex_zhichang_str("shi_yin_guan_xi.dong_yao_ke_shi_yao.#{lq1_key}_#{lq2_key}")
  end
  def Divination.ex_zc_dong_yao_yu_shi_yao_dong_ke_shi_title()
    return Divination.ex_zhichang_str("shi_yin_guan_xi.dong_yao_ke_shi_yao.title")
  end
  # 判斷動爻與世爻的六親
  @@Zhichang_Sygx_Dong_Ke_Shi_Data = [
                           [1,5], # 父母 剋 子孫
                           [2,4], # 兄弟 剋 妻財
                           [5,3], # 子孫 剋 官鬼
                           [4,1], # 妻財 剋 父母
                           [3,2]  # 官鬼 剋 兄弟
                           ]
  def Divination.ex_zc_dong_yao_yu_shi_yao_dong_ke_shi_check(dong_dz,shi_dz,dong_liu_qin,shi_liu_qin)
    # 辰,戌,丑,未  剋 申酉
    # di_zhis = @@Sygx_Yin_Sheng_Shi_Data[0][0]
    @@Zhichang_Sygx_Dong_Ke_Shi_Data.each_index do |i|
      a = @@Zhichang_Sygx_Dong_Ke_Shi_Data[i]
      # if ((a == [dong_liu_qin,shi_liu_qin]) && (di_zhis[0].index(dong_dz) != nil && di_zhis[1].index(shi_dz))) then
      if (a == [dong_liu_qin,shi_liu_qin]) then
        return true,a[0],a[1]
      end
    end
    return false,nil,nil
  end
  # 動爻剋世爻
  def Divination.ex_zc_dong_yao_yu_shi_yao_dong_ke_shi(all_ylqgzws,shi_earth)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    shi_ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,shi_earth)
    shi_liu_qin = Divination.fu_shen_liu_qin(shi_ylqgzw)

    dong_ylqgzws = Divination.par_find_dong_yao_ylqgzws(all_ylqgzws)
    dong_ylqgzws.each_index do |i|
      ylqgzw = dong_ylqgzws[i]
      dong_liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      dong_dz = Divination.fu_shen_di_zhi(ylqgzw)
      is_ke,lq1,lq2 = Divination.ex_zc_dong_yao_yu_shi_yao_dong_ke_shi_check(dong_dz,shi_earth,dong_liu_qin,shi_liu_qin)
      if (is_ke) then
        return Divination.ex_zc_dong_yao_yu_shi_yao_dong_ke_shi_str(lq1,lq2)
      end
    end
    return ""
  end
  # 貴人驛馬
  def ex_zhichang_gui_ren_yi_ma()
    h = Hash.new

    day_tian_gan = Xdate.GetGanZhiSky(@nGanZhiDay)
    day_dizhi = Xdate.GetGanZhiEarth(@nGanZhiDay)

    # 貴人剋世爻
    # h["gui_ren_ke_shi"] = Divination.ex_zc_gui_ren_yi_ma_gui_ren_ke_shi(day_tian_gan,@all_ylqgzws,@liu_shous,@par_five_levels,@shi_earth)
    # 貴人1
    h["gui_ren_1"] = Divination.ex_zc_gui_ren_yi_ma_gui_ren_1(day_tian_gan,@all_ylqgzws,@liu_shous,@par_five_levels,@shi_earth)

    # 貴人2
    h["gui_ren_2"] = Divination.ex_zc_gui_ren_yi_ma_gui_ren_2(day_tian_gan,@shi_earth)

    # 驛馬
    h["yi_ma"] = Divination.ex_zc_gui_ren_yi_ma_yi_ma(day_dizhi,@shi_earth,@all_ylqgzws)

    return h
  end
  def Divination.ex_zc_gui_ren_yi_ma_str(key)
    return Divination.ex_zhichang_str("gui_ren_yi_ma.#{key}")
  end
  # 貴人剋世爻
  def Divination.ex_zc_gui_ren_yi_ma_gui_ren_ke_shi_str()
    return Divination.ex_zc_gui_ren_yi_ma_str("gui_ren_ke_shi")
  end
  def Divination.ex_zc_gui_ren_yi_ma_shi_yao_zi_chi_gui_ren_str()
    return Divination.ex_zc_gui_ren_yi_ma_str("shi_yao_zi_chi_gui_ren")
  end
    # 貴人1
    # 日辰  甲 乙 丙 丁 戊 己 庚 辛 壬 癸
    # 貴人  丑未  子申  酉亥  酉亥  丑未  子申  寅午  寅午  卯巳  卯巳
  def Divination.ex_zc_gui_ren_yi_ma_gui_ren_1_str(tiangan,liu_shou,wang_xiang_xiu_qiu_si,liu_qin)
    key = Divination.tiangan_key(tiangan)
    key2 = Divination.wang_xiang_xiu_qiu_si_key(wang_xiang_xiu_qiu_si)
    key3 = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_zc_gui_ren_yi_ma_str("gui_ren_1.#{key}.#{key2}.#{key3}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_gui_ren_yi_ma_is_shi_hold_gui_ren(dizhis,shi_yao_di_zhi)
    return dizhis.index(shi_yao_di_zhi) != nil
  end
  def Divination.ex_zc_gui_ren_yi_ma_gui_ren_1(day_tian_gan,all_ylqgzws,liu_shous,par_five_levels,shi_yao_di_zhi)
    dizhis = Divination.gui_ren(day_tian_gan,1)
    if (Divination.ex_zc_gui_ren_yi_ma_is_shi_hold_gui_ren(dizhis,shi_yao_di_zhi)) then
      aOut = Array.new
      aOut.push(Divination.ex_zc_gui_ren_yi_ma_shi_yao_zi_chi_gui_ren_str())
      return aOut.uniq
    else
      return Divination.ex_zc_gui_ren_yi_ma_gui_ren_1_normal(day_tian_gan,all_ylqgzws,liu_shous,par_five_levels,dizhis)
    end
  end
  def Divination.ex_zc_gui_ren_yi_ma_gui_ren_1_normal(day_tian_gan,all_ylqgzws,liu_shous,par_five_levels,dizhis)
    aOut = Array.new
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    dizhis.each_index do |i|
      ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,dizhis[i])
      if (ylqgzw != nil) then
        yao = Divination.fu_shen_yao(ylqgzw)
        liu_shou = Divination.liu_shou(liu_shous,yao)
        liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
        wu_xing = Divination.fu_shen_wu_xing(ylqgzw)
        wang_xiang_xiu_qiu_si = Divination.par_find_five_level(par_five_levels,wu_xing)
        aOut.push(Divination.ex_zc_gui_ren_yi_ma_gui_ren_1_str(day_tian_gan,liu_shou,wang_xiang_xiu_qiu_si,liu_qin))
      end
    end

    return aOut.uniq
  end
    # 貴人2
    # (2)貴人地支與世爻地支判斷關係：
    # 貴人      世爻                      說明
  def Divination.ex_zc_gui_ren_yi_ma_gui_ren_2_str(dz1,shi_yao_di_zhi)
    key = Divination.dizhi_key(dz1)
    s = Divination.ex_zc_gui_ren_yi_ma_str("gui_ren_2.#{key}")
    return Divination.extract_str(s,shi_yao_di_zhi)
  end
  def Divination.ex_zc_gui_ren_yi_ma_gui_ren_2(day_tian_gan,shi_yao_di_zhi)
    dizhis = Divination.gui_ren(day_tian_gan,1)
    if (Divination.ex_zc_gui_ren_yi_ma_is_shi_hold_gui_ren(dizhis,shi_yao_di_zhi)) then
      # 貴人1已傳回，不必再回
      return [""]
    else
      return Divination.ex_zc_gui_ren_yi_ma_gui_ren_2_normal(dizhis,shi_yao_di_zhi)
    end
  end
  @@Zhichang_Gui_Ren_2 = [
    [1,2,3,4,5,6,7,8,9,10,11,12],  # 貴人為 子
    [1,2,3,4,5,6,7,8,9,10,11,12], # 丑
    [1,2,3,4,5,6,7,8,9,10,11,12], # 寅
    [1,2,3,4,5,6,7,8,9,10,11,12], # 卯
    [1,2,3,4,5,6,7,8,9,10,11,12], # 辰
    [1,2,3,4,5,6,7,8,9,10,11,12], # 巳
    [1,2,3,4,5,6,7,8,9,10,11,12], # 午
    [1,2,3,4,5,6,7,8,9,10,11,12], # 未
    [1,2,3,4,5,6,7,8,9,10,11,12], # 申
    [1,2,3,4,5,6,7,8,9,10,11,12], # 酉
    [1,2,3,4,5,6,7,8,9,10,11,12], # 戌
    [1,2,3,4,5,6,7,8,9,10,11,12] # 亥
  ]
  def Divination.ex_zc_gui_ren_yi_ma_gui_ren_2_normal_check(gui_ren_dizhi,shi_yao_di_zhi)
    a = @@Zhichang_Gui_Ren_2[gui_ren_dizhi - 1]
    return a.index(shi_yao_di_zhi) != nil
  end
  def Divination.ex_zc_gui_ren_yi_ma_gui_ren_2_normal(dizhis,shi_yao_di_zhi)
    aOut = Array.new
    dizhis.each_index do |i|
      if (Divination.ex_zc_gui_ren_yi_ma_gui_ren_2_normal_check(dizhis[i],shi_yao_di_zhi)) then
        aOut.push(Divination.ex_zc_gui_ren_yi_ma_gui_ren_2_str(dizhis[i],shi_yao_di_zhi))
      end
    end

    return aOut.uniq
  end
    # 驛馬
    # 占卜日之日地支決定。
    # 日支  子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
    # 驛馬  寅 亥 申 巳 寅 亥 申 巳 寅 亥 申 巳
  @@Zhichang_Yi_Ma = [3,12,9,6,3,12,9,6,3,12,9,6]
  def Divination.ex_zc_yi_ma_dizhi(day_dizhi)
    return @@Zhichang_Yi_Ma[day_dizhi - 1]
  end
  def Divination.ex_zc_gui_ren_yi_ma_shi_chi_yi_ma_str()
    return Divination.ex_zc_gui_ren_yi_ma_str("yi_ma.shi_chi_yi_ma")
  end
  def Divination.ex_zc_gui_ren_yi_ma_yi_ma_str(dz1,liu_qin)
    key = Divination.dizhi_key(dz1)
    s = Divination.ex_zc_gui_ren_yi_ma_str("yi_ma.#{key}")

    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_gui_ren_yi_ma_yi_ma(day_dizhi,shi_yao_di_zhi,all_ylqgzws)
    yi_ma_dizhi = Divination.ex_zc_yi_ma_dizhi(day_dizhi)
    if (yi_ma_dizhi == shi_yao_di_zhi) then
      return Divination.ex_zc_gui_ren_yi_ma_shi_chi_yi_ma_str()
    else
      return Divination.ex_zc_gui_ren_yi_ma_yi_ma_normal(day_dizhi,all_ylqgzws)
    end
  end
  def Divination.ex_zc_gui_ren_yi_ma_yi_ma_normal(day_dizhi,all_ylqgzws)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    yi_ma_dizhi = Divination.ex_zc_yi_ma_dizhi(day_dizhi)
    ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,yi_ma_dizhi)
    if (ylqgzw == nil) then
      return ""
    end
    liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
    return Divination.ex_zc_gui_ren_yi_ma_yi_ma_str(day_dizhi,liu_qin)
  end

    # 空亡羊刃
  def ex_zhichang_kong_wang_yang_ren()
    h = Hash.new

    day_tian_gan = Xdate.GetGanZhiSky(@nGanZhiDay)
    day_dizhi = Xdate.GetGanZhiEarth(@nGanZhiDay)
    month_dizhi = Xdate.GetGanZhiEarth(@nGanZhiMonth)

    # 空亡
    h["kong_wang"] = Divination.ex_zc_kong_wang_yang_ren_kong_wang(day_tian_gan,day_dizhi,month_dizhi,@all_ylqgzws,@shi_yao,@par_five_levels,@par_chang_sheng)
    # 羊刄
    h["yang_ren"] = Divination.ex_zc_kong_wang_yang_ren_yang_ren(day_tian_gan,@all_ylqgzws)

    return h
  end
    # 空亡說明
  def Divination.ex_zc_kong_wang_yang_ren_str(key)
    return Divination.ex_zhichang_str("kong_wang_yang_ren.#{key}")
  end
  def Divination.ex_zc_kong_wang_yang_ren_kong_wang_shi_yao_str(tf)
    tf_key = Divination.bool_key(tf)
    return Divination.ex_zc_kong_wang_yang_ren_str("kong_wang.#{tf_key}_kong_wang.shi_yao")
  end
  def Divination.ex_zc_kong_wang_yang_ren_kong_wang_str(tf,liu_qin)
    tf_key = Divination.bool_key(tf)
    s = Divination.ex_zc_kong_wang_yang_ren_str("kong_wang.#{tf_key}_kong_wang.liu_qin")
    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_kong_wang_yang_ren_kong_wang(day_tian_gan,day_dizhi,month_dizhi,all_ylqgzws,shi_yao,par_five_levels,par_chang_sheng)
    aOut = Array.new
    tf = Divination.ex_jk_ky_kong_wang_true_false(day_dizhi,month_dizhi,par_five_levels,par_chang_sheng)
    dizhis = Divination.Kong_Wang(day_tian_gan,day_dizhi)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)

    dizhis.each_index do |i|
      ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,dizhis[i])
      if (ylqgzw != nil) then
        yao = Divination.fu_shen_yao(ylqgzw)
        if (yao == shi_yao) then
          aOut.push(Divination.ex_zc_kong_wang_yang_ren_kong_wang_shi_yao_str(tf))
        else
          liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
          s = Divination.ex_zc_kong_wang_yang_ren_kong_wang_str(tf,liu_qin)
          aOut.push(s)
        end
      end
    end
    return aOut.uniq
  end
    # 羊刃
  def Divination.ex_zc_kong_wang_yang_ren_yang_ren_str(liu_qin)
    s = Divination.ex_zc_kong_wang_yang_ren_str("yang_ren.liu_qin")
    # 六親，共五個
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_zc_kong_wang_yang_ren_yang_ren(day_tian_gan,all_ylqgzws)
    di_zhi = Divination.Yang_Ren(day_tian_gan)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,di_zhi)

    if (ylqgzw != nil) then
      liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      return Divination.ex_zc_kong_wang_yang_ren_yang_ren_str(liu_qin)
    else
      return ""
    end
  end

    # 本卦變卦之卦象說明
  def ex_zhichang_gua_image()
    h = Hash.new
    gua_he_chong = Divination.Gua_64_he_chong(@wai,@nei)
    special_no = Divination.Gua_64_info_special_no(@wai,@nei)
    gua_fan_yin = Divination.Cr_find_gua_fan_yin(@wai,@nei,@bian_wai,@bian_nei)
    ingua_fu_yin = Divination.Cr_find_ingua_fu_yin(@wai,@nei,@bian_wai,@bian_nei)
    outgua_fu_yin = Divination.Cr_find_outgua_fu_yin(@wai,@nei,@bian_wai,@bian_nei)
    inoutgua_fu_yin = Divination.Cr_find_inoutgua_fu_yin(@wai,@nei,@bian_wai,@bian_nei)
    bian_gua_he_chong = Divination.Gua_64_he_chong(@bian_wai,@bian_nei)

    # (1)六沖卦:
    h["liu_chong_gua"] = Divination.ex_zc_gua_image_liu_chong_gua(gua_he_chong)
    # (2)游魂卦:
    h["you_hun_gua"] = Divination.ex_zc_gua_image_you_hun_gua(special_no)
    # (3)反吟卦
    h["fan_yin_gua"] = Divination.ex_zc_gua_image_fan_yin_gua(gua_fan_yin)
    # (4)伏吟卦…內卦伏吟
    h["fu_yin_gua_nei_gua_fu_yin"] = Divination.ex_zc_gua_image_fu_yin_gua_nei_gua_fu_yin(ingua_fu_yin)
    # (5)伏吟卦…外卦伏吟
    h["fu_yin_gua_wai_gua_fu_yin"] = Divination.ex_zc_gua_image_fu_yin_gua_wai_gua_fu_yin(outgua_fu_yin)
    # (6)伏吟卦…內外卦伏吟
    h["fu_yin_gua_nei_wai_gua_fu_yin"] = Divination.ex_zc_gua_image_fu_yin_gua_nei_wai_gua_fu_yin(inoutgua_fu_yin)

    # (7 )六合卦:
    h["liu_he_gua"] = Divination.ex_zc_gua_image_liu_he_gua(gua_he_chong)
    # 六合卦化六沖掛:
    h["liu_he_gua_hua_liu_chong_gua"] = Divination.ex_zc_gua_image_liu_he_gua_hua_liu_chong_gua(gua_he_chong,bian_gua_he_chong)
    # 六合卦化六合掛:
    h["liu_he_gua_hua_liu_he_gua"] = Divination.ex_zc_gua_image_liu_he_gua_hua_liu_he_gua(gua_he_chong,bian_gua_he_chong)
    # 六沖卦化六沖掛:
    h["liu_chong_gua_hua_liu_chong_gua"] = Divination.ex_zc_gua_image_liu_chong_gua_hua_liu_chong_gua(gua_he_chong,bian_gua_he_chong)

    return h
  end
  def Divination.ex_zc_ben_gua_bian_gua_gua_xiang_str(key)
    return Divination.ex_zhichang_str("ben_gua_bian_gua_gua_xiang.#{key}")
  end
    # (1)六沖卦:
  def Divination.ex_zc_ben_gua_bian_gua_gua_xiang_liu_chong_gua_str()
    return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_str("liu_chong_gua")
  end
  def Divination.ex_zc_gua_image_liu_chong_gua(gua_he_chong)
    if (gua_he_chong == HeChong_2) then
      return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_liu_chong_gua_str()
    else
      return ""
    end
  end
    # (2)游魂卦:
  def Divination.ex_zc_ben_gua_bian_gua_gua_xiang_you_hun_gua_str()
    return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_str("you_hun_gua")
  end
  def Divination.ex_zc_gua_image_you_hun_gua(special_no)
    if (special_no == Te_Shu_Gua_7) then
      return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_you_hun_gua_str()
    else
      return ""
    end
  end
    # (3)反吟卦
  def Divination.ex_zc_ben_gua_bian_gua_gua_xiang_fan_yin_gua_str()
    return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_str("fan_yin_gua")
  end
  def Divination.ex_zc_gua_image_fan_yin_gua(gua_fan_yin)
    if (gua_fan_yin) then
      return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_fan_yin_gua_str()
    else
      return ""
    end
  end
    # (4)伏吟卦…內卦伏吟
  def Divination.ex_zc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_nei_gua_fu_yin_str()
    return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_str("fu_yin_gua_nei_gua_fu_yin")
  end
  def Divination.ex_zc_gua_image_fu_yin_gua_nei_gua_fu_yin(ingua_fu_yin)
    if (ingua_fu_yin) then
      return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_nei_gua_fu_yin_str()
    else
      return ""
    end
  end
    # (5)伏吟卦…外卦伏吟
  def Divination.ex_zc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_wai_gua_fu_yin_str()
    return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_str("fu_yin_gua_wai_gua_fu_yin")
  end
  def Divination.ex_zc_gua_image_fu_yin_gua_wai_gua_fu_yin(outgua_fu_yin)
    if (outgua_fu_yin) then
      return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_wai_gua_fu_yin_str()
    else
      return ""
    end
  end
    # (6)伏吟卦…內外卦伏吟
  def Divination.ex_zc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_nei_wai_gua_fu_yin_str()
    return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_str("fu_yin_gua_nei_wai_gua_fu_yin")
  end
  def Divination.ex_zc_gua_image_fu_yin_gua_nei_wai_gua_fu_yin(inoutgua_fu_yin)
    if (inoutgua_fu_yin) then
      return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_fu_yin_gua_nei_wai_gua_fu_yin_str()
    else
      return ""
    end
  end
    # (7 )六合卦:
  def Divination.ex_zc_ben_gua_bian_gua_gua_xiang_liu_he_gua_str()
    return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_str("liu_he_gua")
  end
  def Divination.ex_zc_gua_image_liu_he_gua(gua_he_chong)
    if (gua_he_chong == HeChong_1) then
      return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_liu_he_gua_str()
    else
      return ""
    end
  end

    # 六合卦化六沖掛:
  def Divination.ex_zc_ben_gua_bian_gua_gua_xiang_liu_he_gua_hua_liu_chong_gua_str()
    return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_str("liu_he_gua_hua_liu_chong_gua")
  end
  def Divination.ex_zc_gua_image_liu_he_gua_hua_liu_chong_gua(gua_he_chong,bian_gua_he_chong)
    if (gua_he_chong == HeChong_1 && bian_gua_he_chong == HeChong_2) then
      return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_liu_he_gua_hua_liu_chong_gua_str()
    else
      return ""
    end
  end

    # 六合卦化六合掛:
  def Divination.ex_zc_ben_gua_bian_gua_gua_xiang_liu_he_gua_hua_liu_he_gua_str()
    return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_str("liu_he_gua_hua_liu_he_gua")
  end
  def Divination.ex_zc_gua_image_liu_he_gua_hua_liu_he_gua(gua_he_chong,bian_gua_he_chong)
    if (gua_he_chong == HeChong_1 && bian_gua_he_chong == HeChong_1) then
      return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_liu_he_gua_hua_liu_he_gua_str()
    else
      return ""
    end
  end

    # 六沖卦化六沖掛:
  def Divination.ex_zc_ben_gua_bian_gua_gua_xiang_liu_chong_gua_hua_liu_chong_gua_str()
    return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_str("liu_chong_gua_hua_liu_chong_gua")
  end
  def Divination.ex_zc_gua_image_liu_chong_gua_hua_liu_chong_gua(gua_he_chong,bian_gua_he_chong)
    if (gua_he_chong == HeChong_2 && bian_gua_he_chong == HeChong_2) then
      return Divination.ex_zc_ben_gua_bian_gua_gua_xiang_liu_chong_gua_hua_liu_chong_gua_str()
    else
      return ""
    end
  end
end


