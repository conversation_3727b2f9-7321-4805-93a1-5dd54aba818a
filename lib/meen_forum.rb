# redis-server /usr/local/etc/redis.conf
# bundle exec sidekiq
module MeenForum
  class BgWorker
    include Sidekiq::Worker
    sidekiq_options retry: 7, dead: true, queue: 'api'

    sidekiq_retry_in { |count| count + 86_400 }

    sidekiq_retries_exhausted do |msg, _ex|
      subject = "[MeenForum::BgWorker]Out of retries! #{msg['class']} with #{msg['args']}"
      _message = "error: #{msg['error_message']}"
      FileLog.logger('log/background_worker.log').error(subject)
      # ...
    end

    # def perform(user_id)
    #   logger.info "====== start ====== user_id: #{user_id}"
    #   puts 'hello world'
    #   logger.info '====== done ======'
    # rescue StandardError => e
    #   logger.error "[MeenForum::BgWorker] ERROR:\n #{e.inspect}\n #{e.backtrace}"
    # end
  def perform(*args)
    # Do something later
    if (args[0] == "get_score_statistic") then
      obj_talent = Talent.new
      category_id = args[1]
      talent = args[2]
      raw_exist_check = args[3]
      category_level = args[4]
      obj_talent.get_score_statistic(category_id,talent,raw_exist_check,category_level)
    elsif (args[0] == "get_pr_value_and_save") then
      obj_talent = Talent.new
      category_id = args[1]
      talent = args[2]
      pr_exist_check = args[3]
      timestamp_user_1 = args[4]
      timestamp_user_2 = args[5]
      category_level = args[6]
      obj_talent.get_pr_value_and_save(category_id,talent,pr_exist_check,timestamp_user_1,timestamp_user_2,category_level)
    elsif (args[0] == "getUsersScore") then
      obj_talent = Talent.new
      nPanType = args[1]
      hUserInfo = args[2]
      hUserDefData = args[3]
      # logger.info "====== start ====== time: #{Time.now}"
      # logger.info "======  ====== nPanType: #{nPanType}"
      # logger.info "======  ====== hUserInfo: #{hUserInfo}"
      # logger.info "======  ====== hUserDefData: #{hUserDefData}"
      obj_talent.getUsersScore(nPanType,hUserInfo,hUserDefData)      
      # logger.info "====== start ====== time: #{Time.now}"
    elsif (args[0] == "jianggong_api_all") then
      oJg = Jianggong.new
      hUserInfo = args[1]
      hUserDefData = args[2]
      oJg.jianggong_api_all2(hUserInfo,hUserDefData)      
    else
      obj_talent = Talent.new
      obj_talent.sti_api_check_all() 
    end 
  end

    private

    def logger
      # FileLog.logger('log/background_worker.log')
    end
  end
end
