class Eightword
	@@SkyFiveTable = [1, 1, 2, 2, 3, 3, 4, 4, 0, 0]
	@@EarthFiveTable = [0, 3, 1, 1, 3, 2, 2, 3, 4, 4, 3, 0]
	@@ByChungSky = [[10,  9,  0], # 子
							[6,  8, 10], # 丑   
							[1,  3,  5],
							[2,  1,  0],
							[5,  2, 10],
							[3,  5,  7],
							[4,  6,  0],
							[6,  2,  4],
							[7,  9,  5],
							[8,  7,  0],
							[5,  8,  4],
							[9,  1,  0]]
	@@ByChungSky_old_fail_order = [[9,  10,  0], # 子
							[6,  8, 10], # 丑   
							[1,  3,  5],
							[1,  2,  0],
							[2,  5, 10],
							[3,  5,  7],
							[4,  6,  0],
							[2,  4,  6],
							[5,  7,  9],
							[7,  8,  0],
							[4,  5,  8],
							[1,  9,  0]]
	@@ByChungSkyWin = [[5,  3,  1], # 一月
							  [1,  2,  0],
							  [2, 10,  5],
							  [5,  7,  3],
							  [3,  6,  4],
							  [4,  2,  6],
							  [5,  9,  7],
							  [7,  8,  0],
							  [8,  4,  5],
							  [5,  1,  9],
                              [9, 10,  0],
							  [10,  8,  6]]
	@@ByChungSkyDays = [[7, 14],  # 一月  
							   [10,  0],
							   [9, 12],
							   [7, 14],
							   [10, 19],
							   [9, 12],
							   [7, 14],
							   [10,  0],
							   [9, 12],
							   [7, 14],
                               [10,  0],
							   [9, 12]]
    @@FLevel = [0.5, 0.3, 0.2]
    @@NLevel = [8, 5, 3]

	@@BetweenLevel = [7, -7, 9, -4, -2]

	@@By12Win = [1, 6, 10, 9, 10, 9, 7, 0, 4, 3]
	@@ByFu = [2, 1, 0, 9, 8, 7, 6, 5, 4, 3, 2, 1]

	@@BySchool = [5, 2, 4, 7, 0]
	@@BySchool1 = [11, 2, 8, 5, 8]

	@@ByTenYiGod = [[1, 7], [0, 8], [11, 9], [11, 9], 
		                   [1, 7], [0, 8], [1, 7],[2, 6], [6, 5], [3, 5]]
	@@ByWenChung = [5, 6, 8, 9, 8, 9, 11, 0, 2, 3]
	@@BySheep = [3, 2, 6, 5, 6, 5, 9, 8, 0, 11]
	@@ByFly = [9, 8, 0, 11, 0, 11, 3, 2, 6, 5]
	@@ByLu = [2, 1, 5, 6, 5, 6, 8, 9, 11, 0]
	@@ByRed = [6, 6, 2, 7, 4, 4, 10, 9, 0, 8]

	@@ByJohnStar = [0, 9, 6, 3]
	@@ByHuaGai = [4, 1, 10, 7]
	@@ByHorse = [2, 11, 8, 5]
	@@ByGieKill = [5, 2, 11, 8]
	@@ByDieGod = [11, 8, 5, 2]
	@@ByFlower = [9, 6, 3, 0]

	@@BySkyDe = [3, -1, 8, 7, -1, 0, 9, -1, 2, 1, -1, 6]
	@@ByMonthDe = [2, 0, 8, 6]
	@@ByTenLin = [4, 11, 4, 9, 6, 10, 2, 11, 2, 7]

	@@BySkyDie = [10, 0, 2, 4, 6, 8]
	@@ByGuChen = [2, 5, 8, 11]
	@@ByGuaSu = [10, 1, 4, 7]

	@@ByFive = [4, 2, 1, 3, 4, 2, 0, 3, 4, 1, 0, 3, 2, 1, 0]

	def Eightword.GetPanMinute(nHour,nMin)
		return ((nMin / 10) + (((nHour + 1) % 2) * 6))    #分鐘以10分鐘為一單位，一個時辰共有12個單位
	end

	def Eightword.GetPanWMinute(nEMin)
		return ((nEMin % 6) * 10)    #分鐘以10分鐘為一單位，一個時辰共有12個單位
	end

	@@SkyValue = [3, 1, 7, 5, 1, -1, -1, -3, -5, -7]
	@@EarthValue = [-7, -6, 3, 1, 2, 7, 5, 6, -1, -3, -2, -5]
	@@ByChangeFive = [[11,0],[2,3],[5,6],[7,1],[8,9]]

	# Array [10][12]
	@@ByMidUse = [[2, 6, 6, 9, 9, 9, 6, 6, 6, 6, 3, 3], # 1
                         [2, 2, 9, 9, 9, 9, 2, 9, 9, 2, 2, 2], # 2
                         [8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 8, 8], # 3
                         [0, 6, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0], # 4
                         [2, 2, 0, 0, 8, 9, 2, 2, 0, 0, 2, 2], # 5
                         [2, 0, 2, 9, 9, 9, 2, 2, 0, 2, 2, 2], # 6
                         [4, 3, 0, 8, 8, 3, 3, 3, 0, 3, 3, 2], # 7
                         [5, 8, 8, 8, 8, 8, 8, 8, 8, 8, 2, 2], # 8
                         [6, 4, 0, 8, 9, 7, 4, 0, 0, 4, 4, 2], # 9
                         [7, 6, 2, 7, 6, 6, 3, 7, 7, 6, 2, 2]] # 10
# Array [10][12][4]                   
	@@ByMidLike =   [[[9,-1,-1,-1], [2, 3, 4, 5], [3, 8,-1,-1], [3, 6,-1,-1],  # 1
	                  [3, 6,-1,-1], [3, 6,-1,-1], [3, 8,-1,-1], [2, 3,-1,-1],
	                  [0, 3, 8, 9], [2, 3, 4,-1], [2, 6,-1,-1], [2, 6,-1,-1]], 
	                 [[9,-1,-1,-1], [9,-1,-1,-1], [2, 4,-1,-1],[-1,-1,-1,-1],  # 2
	                  [2,-1,-1,-1], [2,-1,-1,-1], [5, 9,-1,-1], [2, 3,-1,-1],
	                  [7,-1,-1,-1], [4,-1,-1,-1],[-1,-1,-1,-1],[-1,-1,-1,-1]], 
	                 [[6,-1,-1,-1], [5,-1,-1,-1], [0,-1,-1,-1], [6, 9,-1,-1], # 3
	                  [6,-1,-1,-1], [6,-1,-1,-1], [4,-1,-1,-1], [9,-1,-1,-1], 
	                  [8,-1,-1,-1], [4, 6, 8,-1], [4, 5,-1,-1], [0,-1,-1,-1]],  
	                 [[6,-1,-1,-1], [0,-1,-1,-1], [6,-1,-1,-1], [6,-1,-1,-1], # 4
	                  [6, 9,-1,-1], [6, 8,-1,-1], [2, 4, 6,-1], [2, 4, 6,-1],
	                  [4, 6,-1,-1], [6,-1,-1,-1], [6,-1,-1,-1], [6,-1,-1,-1]],
	                 [[0, 9,-1,-1], [0, 9,-1,-1], [2, 9,-1,-1], [2, 9,-1,-1], # 5
	                  [0, 2,-1,-1], [0, 2,-1,-1], [0, 9,-1,-1], [9,-1,-1,-1], 
	                  [2, 9,-1,-1], [2,-1,-1,-1], [0,-1,-1,-1], [0,-1,-1,-1]],
	                 [[0, 6,-1,-1], [2, 9,-1,-1], [0, 9,-1,-1], [2,-1,-1,-1], # 6
	                  [2,-1,-1,-1], [2,-1,-1,-1], [9,-1,-1,-1], [9,-1,-1,-1], 
	                  [2, 9,-1,-1], [4,-1,-1,-1], [4,-1,-1,-1], [4,-1,-1,-1]],
	                 [[0, 2, 3, 8], [0, 2, 6,-1], [3, 8, 9,-1], [2, 3, 4,-1], # 7
	                  [9,-1,-1,-1], [0,-1,-1,-1], [0,-1,-1,-1], [0, 2,-1,-1],  
	                  [8,-1,-1,-1], [2,-1,-1,-1], [0, 2,-1,-1], [0, 3,-1,-1]],
	                 [[6, 8,-1,-1], [0,-1,-1,-1], [0,-1,-1,-1], [0, 9,-1,-1], # 8
	                  [5, 9,-1,-1], [0, 6,-1,-1], [0, 4,-1,-1], [0,-1,-1,-1], 
	                  [0,-1,-1,-1], [2,-1,-1,-1], [0, 4, 8,-1], [4, 5, 8,-1]],
	                 [[2, 4,-1,-1], [6, 7,-1,-1], [6,-1,-1,-1], [6, 7, 9,-1], # 9
	                  [6, 7,-1,-1], [0,-1,-1,-1], [3,-1,-1,-1], [6,-1,-1,-1],  
	                  [2,-1,-1,-1], [2, 6,-1,-1], [2,-1,-1,-1], [0, 3,-1,-1]], 
	                 [[2,-1,-1,-1], [7,-1,-1,-1], [0, 7,-1,-1],[-1,-1,-1,-1], # 10
	                  [8, 9,-1,-1], [7, 8, 9,-1],[-1,-1,-1,-1], [2,-1,-1,-1], 
	                  [0, 8, 9,-1], [3, 4, 7,-1], [7,-1,-1,-1], [3,-1,-1,-1]]]

	# 命宮地支速見表
	def Eightword.LifeHouse_EarthIndex(nSegmentIndex,nHourEarthIndex)
		nLHSeg = Pm.TestNo24(nSegmentIndex - 1)
		nLHSegKey = nLHSeg / 2
		# return @@LifeHouseEarthIndex["SEG_#{nLHSegKey}"][nHourEarthIndex]
		return Earth.Modify(3 - nLHSegKey - nHourEarthIndex)
	end
	@@LifeHouseEarthIndex = {
		'SEG_0' => [3,2,1,0,11,10,9,8,7,6,5,4], # 大寒後雨水前
		'SEG_1' => [2,1,0,11,10,9,8,7,6,5,4,3], # 雨水後春分前
		'SEG_2' => [1,0,11,10,9,8,7,6,5,4,3,2], # 春分後穀雨前
		'SEG_3' => [0,11,10,9,8,7,6,5,4,3,2,1], # 穀雨後小滿前
		'SEG_4' => [11,10,9,8,7,6,5,4,3,2,1,0], # 小滿後夏至前
		'SEG_5' => [10,9,8,7,6,5,4,3,2,1,0,11], # 夏至後大暑前
		'SEG_6' => [9,8,7,6,5,4,3,2,1,0,11,10], # 大暑後處暑前
		'SEG_7' => [8,7,6,5,4,3,2,1,0,11,10,9], # 處暑後秋分前
		'SEG_8' => [7,6,5,4,3,2,1,0,11,10,9,8], # 秋分後霜降前
		'SEG_9' => [6,5,4,3,2,1,0,11,10,9,8,7], # 霜降後小雪前
		'SEG_10' => [5,4,3,2,1,0,11,10,9,8,7,6], # 小雪後冬至前
		'SEG_11' => [4,3,2,1,0,11,10,9,8,7,6,5] # 冬至後大寒前
	}

	def Eightword.LifeStyleRule(nDaySkyIndex,nMonthEarthIndex)
		aLifeStyle = @@LifeStyleRule["S#{nDaySkyIndex}"]
		nLifeStyle = aLifeStyle[nMonthEarthIndex]
		return nLifeStyle
	end

    @@LifeStyleRule =  {
		'S0'   =>  [3,5,10,11,6,8,9,7,4,5,6,2],  # 甲 正印,正官,建祿,羊刃,偏財,食神,傷官,正財,七殺,正官,偏財,偏印
		'S1'   =>  [2,6,9,10,7,9,8,6,5,4,7,3],  # 乙 偏印,偏財,傷官,建祿,正財,傷官,食神,偏財,正官,七殺,正財,正印
		'S2'   =>  [5,9,2,3,8,10,11,9,6,7,8,4],  # 丙 正官,傷官,偏印,正印,食神,建祿,羊刃,傷官,偏財,正財,食神,七殺
		'S3'   =>  [4,8,3,2,9,9,10,8,7,6,9,5],  # 丁 七殺,食神,正印,偏印,傷官,傷官,建祿,食神,正財,偏財,傷官,正官
		'S4'   =>  [7,9,4,5,5,10,11,5,8,9,9,4],  # 戊 正財,傷官,七殺,正官,正官,建祿,羊刃,正官,食神,傷官,傷官,七殺
		'S5'   =>  [6,8,5,4,6,3,10,4,9,8,2,7],  # 己 偏財,食神,正官,七殺,偏財,正印,建祿,七殺,傷官,食神,偏印,正財
		'S6'   =>  [9,3,6,7,2,4,5,3,10,11,2,8],  # 庚 傷官,正印,偏財,正財,偏印,七殺,正官,正印,建祿,羊刃,偏印,食神
		'S7'   =>  [8,2,7,6,3,5,12,2,9,10,3,9],  # 辛 食神,偏印,正財,偏財,正印,正官,偏官,偏印,傷官,建祿,正印,傷官
		'S8'   =>  [11,13,8,9,4,6,13,7,2,3,4,10],  # 壬 羊刃,財官,食神,傷官,七殺,偏財,財官,正財,偏印,正印,七殺,建祿
		'S9'   =>  [10,4,9,8,5,7,6,4,3,2,13,9]  # 癸 建祿,七殺,傷官,食神,正官,正財,偏財,七殺,正印,偏印,財官,傷官
	}

	def Eightword.TaiUanSkyIndex(nSkyIndex)
		return Sky.Modify(nSkyIndex + 1)
	end
	def Eightword.TaiUanEarthIndex(nEarthIndex)
		return Earth.Modify(nEarthIndex + 3)
	end
	def Eightword.TaiHsiSkyIndex(nSkyIndex)
		return Sky.Modify(nSkyIndex + 5)
	end
	def Eightword.TaiHsiEarthIndex(nEarthIndex)
		return @@TaiHsiEarth[nEarthIndex]
	end
	@@TaiHsiEarth = [1,0,11,10,9,8,7,6,5,4,3,2]

	@@MDay = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30]
	def Eightword.UsePeople(nMonthEarthIndex,nDay)
		a = @@UsePeople["E#{nMonthEarthIndex}"]

		a.each do |aItem|
			# if (@@MDay[aItem[0][0]].include?(nDay)) then
			if (aItem[0][0].include?(nDay)) then
				return aItem[1]
			end
		end
		return -1
	end
	# 甲乙丙 丁 戊 已 庚 辛 壬 癸
	# 0 1 2 3  4  5  6 7  8  9
	@@UsePeople = {
		"E0" =>	[[[1..10],8],[[11..30],9]],  # 子月 ～，1～10壬，11～30癸
		"E1" =>	[[[1..9],9],[[10..12],7],[[13..30],5]],  # 丑月 1～9癸，10～12辛，13～30己
		"E2" =>	[[[1..7],4],[[8..14],2],[[15..30],0]],  # 寅月 1～7戊，8～14丙，15～30甲
		"E3" =>	[[[1..10],0],[[11..30],1]],  # 卯月 ～，1～10甲，11～30乙
		"E4" =>	[[[1..9],1],[[10..12],9],[[13..30],4]],  # 辰月 1～9乙，10～12癸，13～30戊
		"E5" =>	[[[1..5],4],[[6..14],6],[[15..30],2]],  # 巳月 1～5戊，6～14庚，15～30丙
		"E6" =>	[[[1..10],2],[[11..19],5],[[20..30],3]],  # 午月 1～10丙，11～19己，20～30丁
		"E7" =>	[[[1..9],3],[[10..12],1],[[13..30],5]],  # 未月 1～9丁，10～12乙，13～30己
		"E8" =>	[[[1..10],4],[[11..13],8],[[14..30],6]],  # 申月 1～10戊，11～13壬，14～30庚
		"E9" =>	[[[1..10],6],[[11..30],7]],  # 酉月 ～，1～10庚，11～30辛
		"E10" =>	[[[1..9],7],[[10..12],3],[[13..30],4]],  # 戌月 1～9辛，10～12丁，13～30戊
		"E11" =>	[[[1..7],4],[[8..12],0],[[13..30],8]]  # 亥月 1～7戊，8～12甲，13～30壬

	}
	@@UsePeople_old = {
		"E0" =>	[[[],-1],[[1..10],8],[[11..30],9]],  # 子月 ～，1～10壬，11～30癸
		"E1" =>	[[[1..9],9],[[10..12],7],[[13..30],5]],  # 丑月 1～9癸，10～12辛，13～30己
		"E2" =>	[[[1..7],4],[[8..14],2],[[15..30],0]],  # 寅月 1～7戊，8～14丙，15～30甲
		"E3" =>	[[[],-1],[[1..10],0],[[11..30],1]],  # 卯月 ～，1～10甲，11～30乙
		"E4" =>	[[[1..9],1],[[10..12],9],[[13..30],4]],  # 辰月 1～9乙，10～12癸，13～30戊
		"E5" =>	[[[1..5],4],[[6..14],6],[[15..30],2]],  # 巳月 1～5戊，6～14庚，15～30丙
		"E6" =>	[[[1..10],2],[[11..19],5],[[20..30],3]],  # 午月 1～10丙，11～19己，20～30丁
		"E7" =>	[[[1..9],3],[[10..12],1],[[13..30],5]],  # 未月 1～9丁，10～12乙，13～30己
		"E8" =>	[[[1..10],4],[[11..13],8],[[14..30],6]],  # 申月 1～10戊，11～13壬，14～30庚
		"E9" =>	[[[],-1],[[1..10],6],[[11..30],7]],  # 酉月 ～，1～10庚，11～30辛
		"E10" =>	[[[1..9],7],[[10..12],3],[[13..30],4]],  # 戌月 1～9辛，10～12丁，13～30戊
		"E11" =>	[[[1..7],4],[[8..12],0],[[13..30],8]]  # 亥月 1～7戊，8～12甲，13～30壬

	}

	# IDS_E_MS_0      :         比肩
	# IDS_E_MS_1      :         劫財
	# IDS_E_MS_2      :         偏印
	# IDS_E_MS_3      :         正印
	# IDS_E_MS_4      :         七殺
	# IDS_E_MS_5      :         正官
	# IDS_E_MS_6      :         偏財
	# IDS_E_MS_7      :         正財
	# IDS_E_MS_8      :         食神
	# IDS_E_MS_9     :         傷官
	def Eightword.MainStar(nDaySkyIndex,nSkyIndex)
		return @@MainStar[nDaySkyIndex][nSkyIndex]
	end
	@@MainStar = [
		[0,1,8,9,6,7,4,5,2,3],  # nDaySkyIndex為0，甲時
		[1,0,9,8,7,6,5,4,3,2],  # nDaySkyIndex為1，乙時
		[2,3,0,1,8,9,6,7,4,5],  # nDaySkyIndex為2，丙時
		[3,2,1,0,9,8,7,6,5,4],  # nDaySkyIndex為3，丁時
		[4,5,2,3,0,1,8,9,6,7],  # nDaySkyIndex為4，戊時
		[5,4,3,2,1,0,9,8,7,6],  # nDaySkyIndex為5，己時
		[6,7,4,5,2,3,0,1,8,9],  # nDaySkyIndex為6，庚時
		[7,6,5,4,3,2,1,0,9,8],  # nDaySkyIndex為7，辛時
		[8,9,6,7,4,5,2,3,0,1],  # nDaySkyIndex為8，壬時
		[9,8,7,6,5,4,3,2,1,0]  # nDaySkyIndex為9，癸時		
	]

	def Eightword.EmptyEarthIndex(nSkyIndex,nEarthIndex)
		nDiff = Earth.Modify(nEarthIndex - nSkyIndex)
		nDiff /= 2
		return @@EmptyEarths[nDiff]
	end
	@@EmptyEarths = [
		[10,11], # 戌亥 甲子 ...
		[0,1],   # 子丑 甲寅 ...
		[2,3],   # 寅卯 甲辰 ...
		[4,5],   # 辰巳 甲午 ...
		[6,7],   # 午未 甲申 ...
		[8,9]    # 申酉 甲戌 ...
	]

	# 天干合化：甲己合化土(黃色)，乙庚合化金(白色)，丙辛合化水(黑色)，丁壬合化木(綠色)，戊癸合化火(紅色)
	# 水 1 ,木 2 ,金 3 ,土 4,火 5
	def Eightword.SkyHerHua(nSkyIndex,isMouseOverEvent)
		if (isMouseOverEvent) then
			@@SkyHerHuas.each do |oSkyHerHua|
				if (oSkyHerHua[0].include?(nSkyIndex)) then
					return oSkyHerHua
				end
			end
		else
			return [[],0]
		end
	end
	@@SkyHerHuas = [
		[[0,5],4], # 甲己合化土(黃色)
		[[1,6],3], # 乙庚合化金(白色)
		[[2,7],1],
		[[3,8],2],
		[[4,9],5]
	]

	# 地支三合：申子辰合化水、巳酉丑合化金、寅午戌合化火、亥卯未合化木 
	def Eightword.Earth3Her(nEarthIndex,nEvent)
		if (Eightword.cp_isEarthMouseOverEvent(Eightword::EVENT_EARTH_HER3,nEvent)) then
			@@Earth3Hers.each do |oEarthHua|
				if (oEarthHua[0].include?(nEarthIndex)) then
					return oEarthHua
				end
			end
		else
			return [[],0]
		end
	end
	@@Earth3Hers = [
		[[8,0,4],1],  # 申子辰合化水
		[[5,9,1],3],  # 巳酉丑合化金
		[[2,6,10],5],
		[[11,3,7],2]
	]


	# 地支	六合	六沖	刑	害
	# 寅		亥	申	巳	巳
	# 卯		戌	酉	子	辰
	# 辰		酉	戌	辰	卯
	# 巳		申	亥	申	寅
	# 午		未	子	午	丑
	# 未		午	丑	丑	子
	# 申		巳	寅	寅	亥
	# 酉		辰	卯	酉	戌
	# 戌		卯	辰	未	酉
	# 亥		寅	巳	亥	申
	# 子		丑	午	卯	未
	# 丑		子	未	戌	午

	# 六合
	def Eightword.Earth6Her(nEarthIndex,nEvent)
		if (Eightword.cp_isEarthMouseOverEvent(Eightword::EVENT_EARTH_HER6,nEvent)) then
			return [[@@Earth6Hers[nEarthIndex]],Eightword::HER6]
		else
			return [[],""]
		end
	end
	@@Earth6Hers = [1,0,11,10,9,8,7,6,5,4,3,2]

	# 六沖
	def Eightword.Earth6Chuon(nEarthIndex,nEvent)
		if (Eightword.cp_isEarthMouseOverEvent(Eightword::EVENT_EARTH_CHUON,nEvent)) then
			return [[@@Earth6Chuons[nEarthIndex]],Eightword::CHUON]
		else
			return [[],""]
		end
	end
	@@Earth6Chuons = [6,7,8,9,10,11,0,1,2,3,4,5]

	# 刑
	def Eightword.EarthHsin(nEarthIndex,nEvent)
		if (Eightword.cp_isEarthMouseOverEvent(Eightword::EVENT_EARTH_HSIN,nEvent)) then
			return [[@@EarthHsins[nEarthIndex]],Eightword::HSIN]
		else
			return [[],""]
		end
	end
	@@EarthHsins = [3,10,5,0,4,8,6,1,2,9,7,11]
	
	# 害
	def Eightword.EarthHai(nEarthIndex,nEvent)
		if (Eightword.cp_isEarthMouseOverEvent(Eightword::EVENT_EARTH_HAI,nEvent)) then
			return [[@@EarthHais[nEarthIndex]],Eightword::HAI]
		else
			return [[],""]
		end
	end
	@@EarthHais = [7,6,5,4,3,2,1,0,11,10,9,8]
end
