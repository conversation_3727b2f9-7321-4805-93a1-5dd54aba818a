class Sky
	#天干
 	SKY = "SKY"
  SKY_KEY       =      "SKY"
  SKY_KEY_PRE       =      "SKY_"

  SKY_FIRST = 1
  SKY_LAST = 10


	def Sky.Exist?(nSkyIndex)
		if (nSkyIndex == nil) then
			return false
		end
		if (nSkyIndex.between?(0,9)) then
			return true
		end
		return false
	end

	def Sky.Modify(nSkyIndex)
		return Pm.TestNo10(nSkyIndex)
	end

	def Sky.ModifySky(nSky)
		nSkyIndex = nSky - 1
		nSkyIndex = Sky.Modify(nSkyIndex)
		return Sky.SkyIndex2Sky(nSkyIndex)
	end
	def Sky.GetIndexName(nSkyIndex)
		nSky = Sky.SkyIndex2Sky(nSkyIndex)
		return Sky.GetName(nSky)
	end
	def Sky.GetName(nSky)
		return Pm.GetStr("IDS_S_SKY_" + nSky.to_s)
	end
  def Sky.GetNameKey(nSky)
    return "IDS_S_SKY_" + nSky.to_s
  end
	def Sky.GetKeyNameByIndex(nSkyIndex)
		if (Sky.Exist?(nSkyIndex)) then
			return "#{Sky::SKY_KEY_PRE}#{Sky.SkyIndex2Sky(nSkyIndex)}"
		else
			return (Sky::SKY_KEY)
		end
	end

	def Sky.GetKeyName(nSky)
		if (Sky.Exist?(Sky.Sky2SkyIndex(nSky))) then
			return "#{Sky::SKY_KEY_PRE}#{nSky}"
		else
			return (Sky::SKY_KEY)
		end
	end

	def Sky.GetYearSkyIndex(nYear)
			return ((nYear + 6) % 10)
	end

	def Sky.GetSkyIndex(nValue)
		return (nValue % 10)
	end

	def Sky.SkyIndex2Sky(nSkyIndex)
		return (Sky.Modify(nSkyIndex) + 1)
	end

	def Sky.Sky2SkyIndex(nSky)
		return Sky.Modify(nSky - 1)
	end

  # 天干合化：甲己合化土(黃色)，乙庚合化金(白色)，丙辛合化水(黑色)，丁壬合化木(綠色)，戊癸合化火(紅色)
  # 水 1 ,木 2 ,金 3 ,土 4,火 5
  @@SkyHerHuas = [
    [[1,6],4], # 甲己合化土(黃色)
    [[2,7],3], # 乙庚合化金(白色)
    [[3,8],1],
    [[4,9],2],
    [[5,10],5]
  ]
  def Sky.SkyHerHua(nSky)
    @@SkyHerHuas.each do |oSkyHerHua|
      if (oSkyHerHua[0].include?(nSky)) then
        return oSkyHerHua[0] - [nSky]
      end
    end
    return []
  end
end

class Earth
	#地支
  EARTH     =      "EARTH"
  EARTH_1     =      "EARTH_1"
  EARTH_2     =      "EARTH_2"
  EARTH_3     =      "EARTH_3"
  EARTH_4     =      "EARTH_4"
  EARTH_5     =      "EARTH_5"
  EARTH_6     =      "EARTH_6"
  EARTH_7     =      "EARTH_7"
  EARTH_8     =      "EARTH_8"
  EARTH_9     =      "EARTH_9"
  EARTH_10    =      "EARTH_10"
  EARTH_11    =      "EARTH_11"
  EARTH_12    =      "EARTH_12"
	EARTH_COUNT = 12

	def Earth.Exist?(nEarthIndex)
		if (nEarthIndex == nil) then
			return false
		end
		if (nEarthIndex.between?(0,11)) then
			return true
		end
		return false
	end

	def Earth.Modify(nEarthIndex)
		return Pm.TestNo12(nEarthIndex)
	end

	def Earth.ModifyEarth(nEarth)
		nEarthIndex = nEarth - 1
		nEarthIndex = Earth.Modify(nEarthIndex)
		return Earth.EarthIndex2Earth(nEarthIndex)
	end

	def Earth.GetIndexName(nEarthIndex)
		nEarth = Earth.EarthIndex2Earth(nEarthIndex)
		return Earth.GetName(nEarth)
	end
	def Earth.GetName(nEarth)
		return Pm.GetStr("IDS_S_EARTH_" + nEarth.to_s)
	end
	def Earth.GetKeyName(nEarth)
		if (Earth.Exist?(Earth.Earth2EarthIndex(nEarth))) then
			return ("EARTH_" + nEarth.to_s)
		else
			return ("EARTH")
		end
	end
	def Earth.GetKeyNameByIndex(nEarthIndex)
		if (Earth.Exist?(nEarthIndex)) then
			return ("EARTH_" + Earth.EarthIndex2Earth(nEarthIndex).to_s)
		else
			return ("EARTH")
		end
	end

	def Earth.GetYearEarthIndex(nYear)
		return ((nYear - 1900) % 12)
	end

	def Earth.GetYearEarth(nYear)
		 nEarthIndex = Earth.GetYearEarthIndex(nYear)
		 return Earth.EarthIndex2Earth(nEarthIndex)
	end

	def Earth.GetEarthIndex(nValue)
		return (nValue % 12)
	end

	def Earth.EarthIndex2Earth(nEarthIndex)
		return (Earth.Modify(nEarthIndex) + 1)
	end

	def Earth.Earth2EarthIndex(nEarth)
		return Earth.Modify(nEarth - 1)
	end

  # 三 合
      # 地 支 三 合 : 申子辰合化水、巳酉丑合化金、寅午戌合化火、亥卯未合化木。
      # 只有三組地支同時存在,在參數區中顯示三合之訊息。
  @@San_Hes = [
                [[9,1,5],5],
                [[6,10,2],4],
                [[3,7,11],2],
                [[12,4,8],1]
              ]
  def Earth.san_he(nEarth)
    @@San_Hes.each do |san_he|
      if (san_he[0].include?(nEarth)) then
        return san_he[0]
      end
    end
    return []
  end
  # 六合
  # 年 合  月 合  日 合
  # 地支 子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
  #     丑 子 亥 戌 酉 申 未 午 巳 辰 卯 寅
  @@Par_Her6 = [2,1,12,11,10,9,8,7,6,5,4,3]
  def Earth.liu_he(nEarth)
    if (nEarth == 0) then
      return []
    end
    return [@@Par_Her6[nEarth - 1]]
  end
  # 六沖
  # 年 衝  月 衝  日 衝
  # 地支 子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
  @@Par_Chung6 = [7,8,9,10,11,12,1,2,3,4,5,6]
  def Earth.liu_chong(nEarth)
    if (nEarth == 0) then
      return []
    end
    return [@@Par_Chung6[nEarth - 1]]
  end
  @@Par_Hu_Xings = [4,0,0,1,5,0,7,0,0,10,0,12]
  def Earth.hu_xing(nEarth)
    if (nEarth == 0) then
      return []
    end
    xing = @@Par_Hu_Xings[nEarth - 1]
    if (xing == 0) then
      return []
    end
    return [xing]
  end
  @@Zi_Xings = [5,7,10,12]
  def Earth.zi_xing(nEarth)
    if (@@Zi_Xings.include?(nEarth)) then
      return [nEarth]
    else
      return []
    end
  end
  # 刑
  # 卯戌巳子辰申午丑寅酉未亥
  @@Par_Xings = [4,11,6,1,5,9,7,2,3,10,8,12]
  def Earth.xing(nEarth)
    if (nEarth == 0) then
      return []
    end
    xing = @@Par_Xings[nEarth - 1]
    return [xing]
  end

  # 害
  # 巳辰卯寅丑子亥戌酉申未午
  # 6 5 4 3 2 1 12 11 10 9 8 7
  @@Par_Hais = [6,5,4,3,2,1,12,11,10,9,8,7]
  def Earth.hai(nEarth)
    if (nEarth == 0) then
      return []
    end
    hai = @@Par_Hais[nEarth - 1]
    return [hai]
  end
  def Earth.jia?(jia_1,jia_2,bei_jia)
    if (jia_1 == nil) then return false end
    if (jia_2 == nil) then return false end
    if (bei_jia == nil) then return false end
    if (jia_1 == []) then return false end
    if (jia_2 == []) then return false end
    if (bei_jia == nil) then return false end
    
    jia_1.each do |j1|
      jia_2.each do |j2|
        if (Earth.jia2?(j1,j2,bei_jia)) then
          return true
        end
      end
    end

    return false
  end
  def Earth.jia2?(jia_1,jia_2,bei_jia)
    if (Earth.ModifyEarth(jia_1 - 1) == bei_jia) then
      if (Earth.ModifyEarth(jia_2 + 1) == bei_jia) then
        return true
      end
    end
    if (Earth.ModifyEarth(jia_1 + 1) == bei_jia) then
      if (Earth.ModifyEarth(jia_2 - 1) == bei_jia) then
        return true
      end
    end
    return false
  end
end


class Five
	def Five.Exist?(nFive)
		if (nFive == nil) then
			return false
		end
		if (nFive.between?(0,4)) then
			return true
		end
		return false
	end

	def Five.Modify(nFive)
		return Pm.TestNo5(nFive)
	end

	def Five.GetName(nFive)
		nFive += 1
		return Five.GetFiveStr(nFive)
	end

	def Five.GetFiveStr(nFive)
		sFive = ""
		if (nFive == nil) then
		  return sFive
		end
		if (nFive > 0) then
		  sFive = Pm.GetStr("IDS_S_FIVE_#{nFive}#{nFive}")
		end
		return sFive
	end

end
