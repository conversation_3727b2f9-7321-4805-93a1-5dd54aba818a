
# 用 meen.tw/ifate/star/panscore 呼叫的 api 來計算原始分數
class Talent
  @WC_PanType
  def wc_one_item_score(category_id,item_id,nPanType,h_ParSetting)
    @WC_PanType = nPanType
    # @Category_Statistic_L1 = {}
    # @Category_Statistic_L2 = {}
    # @Category_Statistic_L3 = {}
    # @Category_Statistic_MP = {}
    h = {}
    hOrg = {}
    h[category_id] = {}
    hOrg[category_id] = {}
    hItem = h_ParSetting[category_id][item_id]
    h[category_id][item_id],hOrg[category_id][item_id] = wc_item_score(hItem,category_id,item_id)
    return h,hOrg
  end
  def wc_one_trait_score(category_id,item_id,trait_id,nPanType,h_ParSetting)
    @WC_PanType = nPanType
    # @Category_Statistic_L1 = {}
    # @Category_Statistic_L2 = {}
    # @Category_Statistic_L3 = {}
    # @Category_Statistic_MP = {}
    h = {}
    hOrg = {}
    h[category_id] = {}
    hOrg[category_id] = {}
    h[category_id][item_id] = {}
    hOrg[category_id][item_id] = {}
    hTrait = h_ParSetting[category_id][item_id][trait_id]
    h[category_id][item_id][trait_id],hOrg[category_id][item_id][trait_id] = wc_trait_score(hTrait,category_id,item_id,trait_id)
    return h,hOrg
  end
  def wc_all_category_score(nPanType,h_ParSetting)
    return wc_categorys_score(h_ParSetting.keys,nPanType,h_ParSetting)
  end
  def wc_categorys_score(category_ids,nPanType,h_ParSetting)
    @WC_PanType = nPanType
    # @Category_Statistic_L1 = {}
    # @Category_Statistic_L2 = {}
    # @Category_Statistic_L3 = {}
    # @Category_Statistic_MP = {}
    h = {}
    hOrg = {}
    if (category_ids == nil) then
      return h,hOrg
    end
    category_ids.each do |category_id|
      h[category_id],hOrg[category_id] = wc_category_score(h_ParSetting[category_id],category_id)
    end
    # hOrg["Category_Statistic"] = @Category_Statistic
    return h,hOrg
  end
  def wc_category_score(hCategory,category_id)
    h = {}
    hOrg = {}
    if (hCategory == nil) then
      return h,hOrg
    end
    if (hCategory.keys.length == 0) then
      return h,hOrg
    end
    hCategory.keys.each do |item_id|
      h[item_id],hOrg[item_id] = wc_item_score(hCategory[item_id],category_id,item_id)
    end
    return h,hOrg
  end
  def wc_item_score(hItem,category_id,item_id)
    h = {}
    hOrg = {}
    if (hItem == nil) then
      return h,hOrg
    end
    if (hItem.keys.length == 0) then
      return h,hOrg
    end
    hItem.keys.each do |trait_id|
      h[trait_id],hOrg[trait_id] = wc_trait_score(hItem[trait_id],category_id,item_id,trait_id)
    end
    return h,hOrg
  end
  def wc_trait_score(hTrait,category_id,item_id,trait_id)
    if (@category_level == "L1") then
      return wc_trait_score_L1(hTrait,category_id,item_id,trait_id)
    elsif (@category_level == "L2") then
      return rl_trait_score_L2(hTrait,category_id,item_id,trait_id)
    elsif (@category_level == "L3") then
      return rl_trait_score_L3(hTrait,category_id,item_id,trait_id)
    elsif (@category_level == "MP") then
      return rl_trait_score_MP(hTrait,category_id,item_id,trait_id)
    end
  end
  def wc_trait_score_L1(hTrait,category_id,item_id,trait_id)
    h = {}
    hOrg = {}
    score = 0
    hTrait.keys.each do |star_for_rule|
    # puts "wc_trait_score_L1 : #{category_id}/#{item_id}/#{trait_id}"
      hSfr = wc_one_star_for_rule(hTrait[star_for_rule])
      star_for_rule_score,hOrg[star_for_rule] = wc_star_for_rule_score(hSfr)
      score += star_for_rule_score
    end
    # h["score"] = score.round # 取整數
    h["score"] = score.round(2) # 取小數位後兩位數
    pr_value = wc_get_pr(category_id,@category_level)
    h["pr"] = wc_find_score_pr(@par_setting_L1,@par_setting_L2,@par_setting_L3,@par_setting_MP,@category_level,category_id,item_id,trait_id,pr_value,h["score"])
    return h,hOrg
  end
  def rl_trait_score_L2(hTrait,category_id,item_id,trait_id)
    h = {}
    hOrg = {}
    score_all = 0.0
    pr_all = 0.0
    weight_all = 0
    a = []
    hR = hTrait["Rule"]
    return h,hOrg if hR == nil
    # rule_L2s = Array.new(10) {{"rl_use" => false, "rl_trait_L1" => "category_1/item_1/trait_1", "rl_weight" => 100}}

    hR.each do |r|
      if (r["rl_use"]) then
        score_each,pr_each,rl_weight,h_detail = rl2_rule_score(r["rl_trait_L1"],r["rl_weight"].to_i,r["rl_reverse"])
        a.push(h_detail)
        score_all += score_each
        pr_all += pr_each
        weight_all += rl_weight
      end
    end

    h["score"] = (score_all / weight_all * 100).round(2) # 取小數位後兩位數
    # L2,L3,MP不用大數據算PR，直接用設定的參數換算PR值
    # 從統計中取得
    # pr_value = wc_get_pr(category_id,@category_level)
    # h["pr"] = wc_find_score_pr(@par_setting_L1,@par_setting_L2,@par_setting_L3,@par_setting_MP,@category_level,category_id,item_id,trait_id,pr_value,h["score"])
    # h["pr"] = (pr_all / weight_all * 100).round(2) if h["pr"] == nil # 取小數位後兩位數
    h["pr"] = (pr_all / weight_all * 100).round(2) # 取小數位後兩位數
    h["pr"] = 99.99 if h["pr"] >= 99.99
    h["pr"] = 1.0 if h["pr"] <= 1.0

    hOrg["Detail"] = a

    return h,hOrg
  end
  def rl2_rule_score(rl_trait_L1,rl_weight,rl_reverse)
    category_level,category_id,item_id,trait_id = split_rl_trait(rl_trait_L1)
    score_org,pr_org_1 = wc_get_trait_score(@Score_L1,@Score_L2,@Score_L3,category_level,category_id,item_id,trait_id)

    # L2,L3,MP不用大數據算PR，直接用設定的參數換算PR值
    # pr_value = wc_get_pr(category_id,category_level)
    # pr_org = wc_find_score_pr(@par_setting_L1,@par_setting_L2,@par_setting_L3,@par_setting_MP,category_level,category_id,item_id,trait_id,pr_value,score_org)
    # 若沒有統計數值，就用原先算的
    pr_org = pr_org_1 #if pr_org == nil
    pr_org = 1.0 if pr_org == nil

    if (rl_reverse) then
      pr = (100.0 - pr_org).round(2)
      # L2,L3,MP不用大數據算PR，直接用設定的參數換算PR值
      # score = wc_find_score_from_pr(@par_setting_L1,@par_setting_L2,@par_setting_L3,@par_setting_MP,category_level,category_id,item_id,trait_id,pr_value,pr)
      # score = ((score_org * pr) / pr_org).round(2) if score == nil
      score = ((score_org * pr) / pr_org).round(2)
    else
      pr = pr_org
      score = score_org
    end
    score_each_after_reverse = score
    pr_each_after_reverse = pr

    use_score = true
    score_each = 0
    pr_each = 0
    # if (use_score) then
    #   score_each = (score * rl_weight / 100.0).round(2)
    #   # pr_each = (pr * rl_weight / 100.0).round(2)
    #   # 用分數換算回PR值
    #   pr_each = wc_find_score_pr(@par_setting_L1,@par_setting_L2,@par_setting_L3,@par_setting_MP,category_level,category_id,item_id,trait_id,pr_value,score_each)
    #   # 若沒有統計數值，就用算的
    #   pr_each = (pr * rl_weight / 100.0).round(2) if pr_each == nil
    # else
    #   pr_each = (pr * rl_weight / 100.0).round(2)
    #   # 用PR值換算回分數
    #   score_each = wc_find_score_from_pr(@par_setting_L1,@par_setting_L2,@par_setting_L3,@par_setting_MP,category_level,category_id,item_id,trait_id,pr_value,pr_each)
    #   # 若沒有統計數值，就用算的
    #   score_each = (score * rl_weight / 100.0).round(2) if score_each == nil
    # end
    # 2021/3/30 改成各自計算
    score_each = (score * rl_weight / 100.0).round(2)
    pr_each = (pr * rl_weight / 100.0).round(2)

    h_talent = wc_get_talent(@my_current_lang,category_level)
    trait_code = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_code"]
    desc = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["desc"]
    trait_desc = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_desc"]
    trait_score_desc = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_score_desc"]
    trait_def = Talent.trait_def_init_check(h_talent["trait_def"][category_id][item_id][trait_id])

    h = {"trait_code" => trait_code,"desc" => desc,"trait_desc" => trait_desc,"trait_score_desc" => trait_score_desc, "trait_def" => trait_def, "trait_id" => "#{category_level}:#{category_id}/#{item_id}/#{trait_id}", "org_score" => score_org,"org_pr" => pr_org,"rl_weight" => rl_weight,"score_each" => score_each,"pr_each" => pr_each,"score_each_after_reverse" => score_each_after_reverse,"pr_each_after_reverse" => pr_each_after_reverse, "rl_reverse" => rl_reverse}
    return score_each,pr_each,rl_weight,h
  end
  def rl_trait_score_L3(hTrait,category_id,item_id,trait_id)
    return rl_trait_score_L2(hTrait,category_id,item_id,trait_id)
  end
  # def rl_trait_score_MP(hTrait,category_id,item_id,trait_id)
  #   return rl_trait_score_L2(hTrait,category_id,item_id,trait_id)
  # end
  def rl_trait_score_MP(hTrait,category_id,item_id,trait_id)
    h = {}
    hOrg = {}
    score_all = 0.0
    pr_all = 0.0
    weight_all = 0
    a = []
    hR = hTrait["Rule"]
    return h,hOrg if hR == nil
    # rule_L2s = Array.new(10) {{"rl_use" => false, "rl_trait_L1" => "category_1/item_1/trait_1", "rl_weight" => 100}}

    all_100 = true
    hr_count = 0
    hR.each do |r|
      if (r["rl_use"]) then
        score_each,pr_each,rl_weight,h_detail = rl2_rule_score(r["rl_trait_L1"],r["rl_weight"].to_i,r["rl_reverse"])
        a.push(h_detail)
        score_all += score_each
        pr_all += pr_each
        weight_all += rl_weight
        all_100 = false if r["rl_weight"].to_i != 100
        hr_count += 1
      end
    end

    h["score"] = (score_all / weight_all * 100).round(2) # 取小數位後兩位數
    # 從統計中取得
    # pr_value = wc_get_pr(category_id,@category_level)
    # h["pr"] = wc_find_score_pr(@par_setting_L1,@par_setting_L2,@par_setting_L3,@par_setting_MP,@category_level,category_id,item_id,trait_id,pr_value,h["score"])
    # h["pr"] = (pr_all / weight_all * 100).round(2) if h["pr"] == nil # 取小數位後兩位數

    # 直接計算
    h["pr"] = (pr_all / weight_all * 100).round(2)

    # 從總pr_all計算得來，若rl_weight都是100，則平均，若不是，直接累加
    # if all_100 then
    #   h["pr"] = (pr_all / hr_count).round(2)
    # else
    #   h["pr"] = pr_all.round(2)
    #   # h["pr"] = (pr_all / weight_all * 100).round(2)
    # end
    h["pr"] = 99.99 if h["pr"] >= 99.99
    h["pr"] = 1.0 if h["pr"] <= 1.0

    hOrg["Detail"] = a

    return h,hOrg
  end

  def wc_get_pr(category_id,category_level)
    # hCategory_Statistic = wc_get_one_hash(category_level,@Category_Statistic_L1,@Category_Statistic_L2,@Category_Statistic_L3,@Category_Statistic_MP)
    
    # hCategory_Statistic = Talent.get_category_statistic(category_id,category_level) if hCategory_Statistic[category_id] == nil
    hCategory_Statistic = wc_get_category_statistic(category_id,category_level)

    return nil if (hCategory_Statistic == nil || hCategory_Statistic == {})
    return nil if (hCategory_Statistic[category_id] == nil || hCategory_Statistic[category_id] == {})
    pr_value = hCategory_Statistic[category_id]["pr"]
# puts "category_level:hCategory_Statistic[category_id] : #{category_level}:#{hCategory_Statistic[category_id].keys}"

    # if (category_level == "L1") then
    #   @Category_Statistic_L1[category_id] = hCategory_Statistic[category_id]
    # elsif (category_level == "L2") then
    #   @Category_Statistic_L2[category_id] = hCategory_Statistic[category_id]
    # elsif (category_level == "L3") then
    #   @Category_Statistic_L3[category_id] = hCategory_Statistic[category_id]
    # end
    return pr_value
  end
  def wc_get_statistic(category_id,category_level)
    # hCategory_Statistic = Talent.get_category_statistic(category_id,category_level)
    hCategory_Statistic = wc_get_category_statistic(category_id,category_level)
    return hCategory_Statistic[category_id]
  end
  def wc_get_pr2(category_id,category_level)
    # hCategory_Statistic = Talent.get_category_statistic(category_id,category_level)
    hCategory_Statistic = wc_get_category_statistic(category_id,category_level)
    pr_value = hCategory_Statistic[category_id]["pr"]
    return pr_value
  end
  def wc_find_score_pr_one_category_level(par_setting,category_id,item_id,trait_id,pr_value,score)
    c_idx,i_idx,t_idx = wc_find_cit_index2(par_setting,category_id,item_id,trait_id)
    return wc_find_score_pr2(c_idx,i_idx,t_idx,pr_value,score)
  end
  def wc_find_score_pr(par_setting_L1,par_setting_L2,par_setting_L3,par_setting_MP,category_level,category_id,item_id,trait_id,pr_value,score)
    c_idx,i_idx,t_idx = wc_find_cit_index(par_setting_L1,par_setting_L2,par_setting_L3,par_setting_MP,category_level,category_id,item_id,trait_id)
# puts "par_setting_L1 : #{par_setting_L1.keys}"
# puts "c_idx,i_idx,t_idx : #{c_idx},#{i_idx},#{t_idx}"
    pr_out = wc_find_score_pr2(c_idx,i_idx,t_idx,pr_value,score)
    return pr_out if pr_out == nil

    pr_out = 99.9 if pr_out >= 99.9
    pr_out = 1.0 if pr_out <= 1.0
    return pr_out
  end
  def wc_find_score_pr2(c_idx,i_idx,t_idx,pr_value,score)
    return nil if pr_value == nil
    return nil if i_idx == nil
    return nil if t_idx == nil
    return nil if pr_value[i_idx] == nil
# puts "category_level : #{category_level} ; #{category_id} : #{c_idx};  #{item_id} : #{i_idx} ;  #{trait_id} : #{t_idx}"
    hPr = pr_value[i_idx][t_idx]
# puts "pr_value : #{pr_value[i_idx][t_idx].keys[0]}"
    return nil if hPr == nil

    i = hPr.keys.index("#{score}")
    return hPr["#{score}"].to_f if i != nil

    score = score.to_f
    quickly = true
    if (quickly) then
      pr_out = wc_pr_find_between_value(hPr.keys,"#{score}",0,hPr.keys.length - 1,hPr.values)
      return pr_out.round(2)
    else
      return hPr[hPr.keys[0]].to_f if score >= hPr.keys[0].to_f
    # puts "hPr.keys : #{hPr.keys}"
      return hPr[hPr.keys[hPr.keys.length - 1]].to_f if score <= hPr.keys[hPr.keys.length - 1].to_f
      hPr.keys.each_with_index do |sc,i|
        m = sc
        n = hPr.keys[i + 1]
        return hPr[hPr.keys[hPr.keys.length - 1]].to_f if m == nil || n == nil
        m = m.to_f
        n = n.to_f
        # puts "score : v 4 : #{score}:#{score.class}:#{m}:#{m.class}:#{n}:#{n.class}:#{score.between?(m, n)}" if score == 86.97 && m > 86 && m < 87
        if score.between?(n,m) then
          m_pr = hPr[sc].to_f
          n_pr = hPr[hPr.keys[i + 1]].to_f
    # puts "score : m_pr : n_pr : #{score}:#{m_pr}:#{n_pr}:#{((m_pr + n_pr) / 2).round(2)}" if score.to_f == 86.97
          return ((m_pr + n_pr) / 2).round(2)
        end
      end
    end
    return nil
  end
  # pr 的值是由大而小的排序
  def wc_pr_find_between_value(a_in,v,head_idx,tail_idx,a_out)
    m = (tail_idx - head_idx) / 2 + head_idx

    # puts "v : head_idx : tail_idx : #{v}:#{head_idx}:#{tail_idx}" if v.to_f == 86.97
    return nil if a_in == nil || a_in.length == 0
    # 如果比最大的還大，直接回
    return a_out[head_idx].to_f if v.to_f >= a_in[head_idx].to_f && head_idx == 0
    # 如果比最小的還小，直接回
    return a_out[tail_idx].to_f if v.to_f <= a_in[tail_idx].to_f && tail_idx == a_in.length - 1

    # 有值，直接回
    return a_out[m].to_f if (v.to_f == a_in[m].to_f)
    return a_out[m + 1].to_f if (v.to_f == a_in[m + 1].to_f)
    # 頭 尾 指引相同或差1，表示要結束了
    return ((a_out[head_idx].to_f + a_out[head_idx + 1].to_f) / 2).round(2) if (tail_idx - head_idx) <= 1

    # puts "v : m : #{v}:#{m}:#{a_in[m]}:#{a_out[m]}" if v.to_f == 86.97
    if (v.to_f > a_in[m].to_f) then
      tail_idx = m
      return wc_pr_find_between_value(a_in,v,head_idx,tail_idx,a_out)
    elsif (v.to_f < a_in[m].to_f) then
      head_idx = m
      return wc_pr_find_between_value(a_in,v,head_idx,tail_idx,a_out) 
    else
      return nil
    end
  end
  def wc_find_score_from_pr(par_setting_L1,par_setting_L2,par_setting_L3,par_setting_MP,category_level,category_id,item_id,trait_id,pr_value,pr_each)
    c_idx,i_idx,t_idx = wc_find_cit_index(par_setting_L1,par_setting_L2,par_setting_L3,par_setting_MP,category_level,category_id,item_id,trait_id)
    return nil if pr_value == nil || pr_value == {}
    return nil if i_idx == nil || t_idx == nil || pr_value[i_idx] == nil || pr_value[i_idx] == {}

    hPr = pr_value[i_idx][t_idx]
    return nil if hPr == nil || hPr == {}

    i = hPr.values.index(pr_each)
    return hPr.key(pr_each).to_f if i != nil

    quickly = true
    if (quickly) then
      return wc_pr_find_between_value(hPr.values,pr_each,0,hPr.values.length - 1,hPr.keys)
    else
      return hPr.keys[0].to_f if pr_each >= hPr.values[0].to_f
    # puts "category_level : #{category_level}"
      return hPr.keys[hPr.keys.length - 1].to_f if pr_each <= hPr.values[hPr.values.length - 1].to_f
    # puts "pr_each : #{pr_each}"
  
      hPr.values.each_with_index do |v,i|
        m = v
        n = hPr.values[i + 1]
        return hPr.keys[hPr.keys.length - 1].to_f if m == nil || n == nil
        # between?(小，大)才會正確，between?(大，小)永遠false
        if pr_each.between?(n,m) then
          m_value = hPr.keys[i].to_f
          n_value = hPr.keys[i + 1].to_f
          return ((m_value + n_value) / 2).round(2)
        end
      end
    end

    return nil
  end

  def split_rl_trait(rl_trait)
    b = rl_trait.split(":")
    if (b.length == 2) then
      category_level = b[0]
      a = b[1].split("/")
    elsif (b.length == 1) then
      category_level = "L1"
      a = b[0].split("/")
    else
      return "","","",""
    end
    # a = rl_trait.split("/")
    category_id = a[0]
    item_id = a[1]
    trait_id = a[2]
    return category_level,category_id,item_id,trait_id
  end
  def wc_get_trait_score_db(hScore_L1_db,hScore_L2_db,hScore_L3_db,category_level,category_id,item_id,trait_id,par_setting)
    # score = @Score_L1[category_id]["score"]
    # c_idx,i_idx,t_idx = wc_find_cit_index(@par_setting,category_id,item_id,trait_id)
    # puts "category_id : #{category_id}"
    # puts "item_id : #{item_id}"
    # puts "trait_id : #{trait_id}"
    # puts "category_level : #{category_level}"
    # puts "hScore_L1 : #{hScore_L1}"
    # puts "hScore_L1[category_id][item_id][trait_id] : #{hScore_L1[category_id][item_id][trait_id]}"
            # @Score_L1_db[category_id]["score"] = b
            # @Score_L1_db[category_id]["version"] = talent_version[category_id]
    hScore_db = wc_get_one_hash(category_level,hScore_L1_db,hScore_L2_db,hScore_L3_db)
    return 0.0,nil if hScore_db == nil
    return 0.0,nil if hScore_db[category_id] == nil
    return 0.0,nil if hScore_db[category_id]["score"] == nil
    
    c_idx,i_idx,t_idx = wc_find_cit_index(par_setting,category_id,item_id,trait_id)
    score = hScore_db[category_id]["score"][i_idx][t_idx]
    pr = hScore_db[category_id]["pr"][i_idx][t_idx]

    return score,pr
  end
  def wc_get_trait_score(hScore_L1,hScore_L2,hScore_L3,category_level,category_id,item_id,trait_id)
    # score = @Score_L1[category_id]["score"]
    # c_idx,i_idx,t_idx = wc_find_cit_index(@par_setting,category_id,item_id,trait_id)
    # puts "category_id : #{category_id}"
    # puts "item_id : #{item_id}"
    # puts "trait_id : #{trait_id}"
    # puts "category_level : #{category_level}"
    # puts "hScore_L1 : #{hScore_L1}"
    # puts "hScore_L1[category_id][item_id][trait_id] : #{hScore_L1[category_id][item_id][trait_id]}"
    hScore = wc_get_one_hash(category_level,hScore_L1,hScore_L2,hScore_L3)
    return 0.0,nil if hScore == nil
    return 0.0,nil if hScore[category_id][item_id][trait_id] == nil
    return 0.0,nil if hScore[category_id][item_id][trait_id]["score"] == nil
    
    score = hScore[category_id][item_id][trait_id]["score"]
    pr = hScore[category_id][item_id][trait_id]["pr"]

    return score,pr
  end
  def wc_get_one_hash(category_level,hL1,hL2,hL3,hMP=nil)
    if (category_level == "L1") then
      return hL1
    elsif (category_level == "L2") then
      return hL2
    elsif (category_level == "L3") then
      return hL3
    elsif (category_level == "MP") then
      return hMP
    else
      return nil
    end
  end
  def wc_find_cit_index(par_setting_L1,par_setting_L2,par_setting_L3,par_setting_MP,category_level,category_id,item_id,trait_id)
    # c_idx = talent["category"].keys.index(category_id)
    # i_idx = talent["category"][category_id]["item"].keys.index(item_id)
    # t_idx = talent["category"][category_id]["item"][item_id]["trait"].keys.index(trait_id)
    par_setting = wc_get_one_hash(category_level,par_setting_L1,par_setting_L2,par_setting_L3,par_setting_MP)
    return 0,0,0 if par_setting == nil
    
    return wc_find_cit_index2(par_setting,category_id,item_id,trait_id)
  end
  def wc_find_cit_index2(par_setting,category_id,item_id,trait_id)
    # c_idx = talent["category"].keys.index(category_id)
    # i_idx = talent["category"][category_id]["item"].keys.index(item_id)
    # t_idx = talent["category"][category_id]["item"][item_id]["trait"].keys.index(trait_id)

    c_idx = par_setting.keys.index(category_id)
    i_idx = par_setting[category_id].keys.index(item_id)
    t_idx = par_setting[category_id][item_id].keys.index(trait_id)
    
    return c_idx,i_idx,t_idx
  end

  # 計算PR值
  def Talent.cal_pr_value(raw)
    n = raw.length
    raw_sort = raw.sort.reverse # 由大到小
    pr = 99.0
    r1 = -99999
    i_pr = 99.0
    h_pr = {}
    raw_sort.each_with_index do |r,i|
      if (r1 != r) then
        r1 = r
        pr = (n - (i + 1)) * 100.0 / n
        i_pr = pr.round(2)
        i_pr = 99.99 if i_pr >= 100
        i_pr = 1.0 if i_pr <= 1.0
        i_pr = 99.99 if i == 0
      end
      h_pr["#{r}"] = i_pr
    end
    return h_pr
  end
  def Talent.save_category_statistic(category_id,h,category_level="L1")
    cl = "_#{category_level}"
    cl = "" if category_level == "L1"
    key = "Talent_Statistic#{cl}_#{category_id}"
    h2 = Pm.getSystemValue_hash(key)

    h2.merge!(h) { |key, v1, v2| v2 }
    Pm.setSystemValue_hash(key,h2)
  end
  def wc_get_pars_from_trait_code(my_current_lang,trait_code)
    @my_current_lang = my_current_lang
    category_levels = ["L1","L2","L3","MP"]
    category_levels.each do |category_level|
      bFind,category_id,item_id,trait_id,trait_desc,trait_def = wc_get_pars_from_trait_code_by_category_level(my_current_lang,category_level,trait_code)
      return bFind,category_level,category_id,item_id,trait_id,trait_desc,trait_def if bFind
    end
    return false,"",0,0,0,"",Talent.trait_def_empty()
  end
  def wc_get_pars_from_trait_code_by_category_level(my_current_lang,category_level,trait_code)
      h_ParSetting,h_Version = wc_get_pars_setting(my_current_lang,category_level)
      h_talent = wc_get_talent(my_current_lang,category_level)
      h_ParSetting.keys.each do |category_id|
          h_ParSetting[category_id].keys.each do |item_id|
              h_ParSetting[category_id][item_id].keys.each do |trait_id|    
                trait_code_find = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_code"]
                trait_desc = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["desc"]
                trait_def = Talent.trait_def_init_check(h_talent["trait_def"][category_id][item_id][trait_id])
                return true,category_id,item_id,trait_id,trait_desc,trait_def if trait_code_find == trait_code
              end
          end
      end
      return false,0,0,0,"",Talent.trait_def_empty()
  end
  def Talent.trait_def_empty()
    h = {}
    (1..4).each do |i|
      h["score_drop_#{i}"] = {}
      h["score_drop_#{i}"]["up"] = nil
      h["score_drop_#{i}"]["down"] = nil
      h["score_drop_#{i}"]["trait_content"] = ""
      h["score_drop_#{i}"]["proposal"] = ""
    end
    h = Talent.trait_def_init_check(h)
    return h
  end
  def Talent.trait_def_init_check(trait_def)
    (1..4).each do |i|
      trait_def["score_drop_#{i}"]["up"] = (i - 1) * 25 if trait_def["score_drop_#{i}"]["up"] == nil || trait_def["score_drop_#{i}"]["up"] == ""
      trait_def["score_drop_#{i}"]["down"] = i * 25 - 1 if trait_def["score_drop_#{i}"]["down"] == nil || trait_def["score_drop_#{i}"]["down"] == ""
    end
    return trait_def
  end
  def Talent.trait_def_pr_check(trait_def,pr,category_level)
    td = Talent.trait_def_init_check(trait_def)
    (1..4).each do |i|
      bFind,h = Talent.trait_def_pr_desc(td,i,pr,category_level)
      return h if bFind
    end
    return {"taitou" => "", "trait_content" => ""}
  end
  def Talent.trait_def_pr_desc(trait_def,i,pr,category_level)
    h = {}
    up,down,trait_content,proposal = Talent.get_one_trait_def(trait_def,i)
    if (pr.to_f.between?(up.to_f,down.to_f + 0.99)) then
      trait_def_area = Pm.t("talent.big_nos")[i - 1]

      h["taitou"] = Pm.t("talent.#{category_level}.trait_def_taitou",:trait_def_area => trait_def_area,:pr => "#{(up.to_f + 1.0).to_i}")
      h["trait_content"] = trait_content

      return true,h
    end
    return false,{"taitou" => "", "trait_content" => ""}
  end
  def Talent.get_one_trait_def(trait_def,i)
      up = trait_def["score_drop_#{i}"]["up"]
      down = trait_def["score_drop_#{i}"]["down"]
      trait_content = trait_def["score_drop_#{i}"]["trait_content"]
      proposal = trait_def["score_drop_#{i}"]["proposal"]

      return up,down,trait_content,proposal
  end

  def init_pars_setting()
    @par_setting_L1 = nil
    @par_setting_L2 = nil
    @par_setting_L3 = nil
    @par_setting_MP = nil
    @par_Version_L1 = nil
    @par_Version_L2 = nil
    @par_Version_L3 = nil
    @par_Version_MP = nil
  end
  def wc_get_pars_setting(my_current_lang,category_level="L1")
    if (category_level == "L1") then
      @par_setting_L1,@par_Version_L1 = Talent.get_pars_setting(category_level,my_current_lang) if @par_setting_L1 == nil
      return @par_setting_L1,@par_Version_L1
    elsif (category_level == "L2") then
      @par_setting_L2,@par_Version_L2 = Talent.get_pars_setting(category_level,my_current_lang) if @par_setting_L2 == nil
      return @par_setting_L2,@par_Version_L2
    elsif (category_level == "L3") then
      @par_setting_L3,@par_Version_L3 = Talent.get_pars_setting(category_level,my_current_lang) if @par_setting_L3 == nil
      return @par_setting_L3,@par_Version_L3
    elsif (category_level == "MP") then
      @par_setting_MP,@par_Version_MP = Talent.get_pars_setting(category_level,my_current_lang) if @par_setting_MP == nil
      return @par_setting_MP,@par_Version_MP
    end
    return {},{}
  end
  def Talent.get_pars_setting(category_level,my_current_lang)
    key = Talent.get_talent_key(category_level,my_current_lang) 
    hTalent = Pm.getSystemValue_hash(key)
    h_ParSetting = hTalent["par_setting"]
    h_Version = hTalent["version"]
    return h_ParSetting,h_Version
  end
  def init_talent()
    @talent_L1 = nil
    @talent_L2 = nil
    @talent_L3 = nil
    @talent_MP = nil
  end
  def wc_get_talent(my_current_lang,category_level="L1")
    if (category_level == "L1") then
      @talent_L1 = Talent.get_talent(category_level,my_current_lang) if @talent_L1 == nil
      return @talent_L1
    elsif (category_level == "L2") then
      @talent_L2 = Talent.get_talent(category_level,my_current_lang) if @talent_L2 == nil
      return @talent_L2
    elsif (category_level == "L3") then
      @talent_L3 = Talent.get_talent(category_level,my_current_lang) if @talent_L3 == nil
      return @talent_L3
    elsif (category_level == "MP") then
      @talent_MP = Talent.get_talent(category_level,my_current_lang) if @talent_MP == nil
      return @talent_MP
    end
    return {}
  end
  def Talent.get_talent(category_level,my_current_lang)
    key = Talent.get_talent_key(category_level,my_current_lang) 
    hTalent = Pm.getSystemValue_hash(key)
    return hTalent
  end
  def init_category_statistic()
    @Category_Statistic_L1 = nil
    @Category_Statistic_L2 = nil
    @Category_Statistic_L3 = nil
    @Category_Statistic_MP = nil
  end
  def wc_get_category_statistic(category_id,category_level="L1")
    # puts "caller : #{caller[0]} : #{category_id} : #{category_level}"
    @Category_Statistic_L1 = {} if @Category_Statistic_L1 == nil
    @Category_Statistic_L2 = {} if @Category_Statistic_L2 == nil
    @Category_Statistic_L3 = {} if @Category_Statistic_L3 == nil
    @Category_Statistic_MP = {} if @Category_Statistic_MP == nil
    if (category_level == "L1") then
      hCategory_Statistic = Talent.get_category_statistic(category_id,category_level) if @Category_Statistic_L1[category_id] == nil
      @Category_Statistic_L1[category_id] = hCategory_Statistic[category_id] == nil ? {} : hCategory_Statistic[category_id] if @Category_Statistic_L1[category_id] == nil
      return @Category_Statistic_L1
    elsif (category_level == "L2") then
      hCategory_Statistic = Talent.get_category_statistic(category_id,category_level) if @Category_Statistic_L2[category_id] == nil
      @Category_Statistic_L2[category_id] = hCategory_Statistic[category_id] == nil ? {} : hCategory_Statistic[category_id] if @Category_Statistic_L2[category_id] == nil
      return @Category_Statistic_L2
    elsif (category_level == "L3") then
      hCategory_Statistic = Talent.get_category_statistic(category_id,category_level) if @Category_Statistic_L3[category_id] == nil
      @Category_Statistic_L3[category_id] = hCategory_Statistic[category_id] == nil ? {} : hCategory_Statistic[category_id] if @Category_Statistic_L3[category_id] == nil
      return @Category_Statistic_L3
    elsif (category_level == "MP") then
      hCategory_Statistic = Talent.get_category_statistic(category_id,category_level) if @Category_Statistic_MP[category_id] == nil
      @Category_Statistic_MP[category_id] = hCategory_Statistic[category_id] == nil ? {} : hCategory_Statistic[category_id] if @Category_Statistic_MP[category_id] == nil
      return @Category_Statistic_MP
    end
    return {}
  end
  def Talent.get_category_statistic(category_id,category_level="L1")
    cl = "_#{category_level}"
    cl = "" if category_level == "L1"
    key = "Talent_Statistic#{cl}_#{category_id}"
    h2 = Pm.getSystemValue_hash(key)
    return h2
  end
  def wc_star_for_rule_score(hSfr)
    case hSfr["star_for_rule_type"]
    when Talent::AStarKey
      score,hOrg = wc_sfr_AStar_score(hSfr)
    when Talent::BStarKey
      score,hOrg = wc_sfr_BStar_score(hSfr)
    when Talent::Doctor12Key
      score,hOrg = wc_sfr_Doctor12_score(hSfr)
    when Talent::God12Key
      score,hOrg = wc_sfr_God12_score(hSfr)
    when Talent::YearStar12Key
      score,hOrg = wc_sfr_YearStar12_score(hSfr)
    when Talent::YearGod12Key
      score,hOrg = wc_sfr_YearGod12_score(hSfr)
    when Talent::HouseIdKey
      score,hOrg = wc_sfr_HouseId_score(hSfr)
    when Talent::BodyHouseKey
      score,hOrg = wc_sfr_BodyHouse_score(hSfr)
    end
    # 此score已經是依 「星曜比重」 調整的最後分數
    hOrg["score"] = score.round(2)

    hOrg["star_weight"] = hSfr["star_weight"]

    # 身宮獨立增加的分數(依身宮比重)，不列入 「星曜比重」 的計算，是算完後，再額外再加上去
    # 改到算到星曜分數裡
    # hOrg["shenggong_score"] = {}
    # score_sheng,hOrg["shenggong_score"]["detail"] = wc_sfr_shenggong_scores(score,hSfr,hOrg)
    # hOrg["shenggong_score"]["score"] = score_sheng
    # score += score_sheng
    # hOrg["score"] = score

    return score,hOrg
  end
    # 身宮獨立增加的分數(依身宮比重)
  def wc_sfr_shenggong_scores(score_final,hSfr,hOrg,isSameHouseId)
    shenggong_weights = hSfr["shenggong_weights"]
    score_sheng = 0
    shenggong_score_org = []
    if (shenggong_weights == nil) then
      return score_sheng,shenggong_score_org
    end
    shenggong_weights.each do |sg_w|
      score = wc_sfr_shenggong_score(score_final,sg_w,hOrg,isSameHouseId)
      score_sheng += score
      shenggong_score_org.push(score)
    end

    return score_sheng.round(2),shenggong_score_org
  end
  def wc_sfr_shenggong_score(score_final,sg_w,hOrg,isSameHouseId)
    gong_score = score_final # 宮總分
    xing_score = hOrg["star"]["star_score"] # 星曜總分
    sihua_score = hOrg["sihua"]["score_sihua"] # 四化總分
    sihua_0_score = hOrg["sihua"]["score_sihuas"][0] # 祿 分數
    sihua_1_score = hOrg["sihua"]["score_sihuas"][1] # 權 分數
    sihua_2_score = hOrg["sihua"]["score_sihuas"][2] # 科 分數
    sihua_3_score = hOrg["sihua"]["score_sihuas"][3] # 忌 分數

    return 0 if !sg_w["use_shenggong"] || !isSameHouseId

    score_sheng = sg_w["weight"] * xing_score / 100.0 if sg_w["condition"] == "sw_star_sum"
    score_sheng = sg_w["weight"] * sihua_0_score / 100.0 if sg_w["condition"] == "sw_sihua_0"
    score_sheng = sg_w["weight"] * sihua_1_score / 100.0 if sg_w["condition"] == "sw_sihua_1"
    score_sheng = sg_w["weight"] * sihua_2_score / 100.0 if sg_w["condition"] == "sw_sihua_2"
    score_sheng = sg_w["weight"] * sihua_3_score / 100.0 if sg_w["condition"] == "sw_sihua_3"
    score_sheng = sg_w["weight"] * gong_score / 100.0 if sg_w["condition"] == "sw_gong_sum"
    score_sheng = sg_w["weight"] * sihua_score / 100.0 if sg_w["condition"] == "sw_sihua_sum"

    return score_sheng
  end
  # sfr : star for rule
  def wc_sfr_AStar_score(hSfr)
    nStar = hSfr["nStar"]
    nHouseId = @Obj_Star.gHouse_GetAStar_HouseId(@WC_PanType,nStar)

    hOrg = {}
    score_star,hOrg["star"] = wc_sfr_AStar_score_star(hSfr,nHouseId)
    score_sihua,hOrg["sihua"] = wc_sfr_AStar_score_sihua(hSfr,nHouseId)

    # 加上身宮分數判斷
    score_sheng,hOrg["star"]["shenggong_score"] = wc_sfr_get_shenggong_scores(score_star,hSfr,hOrg,nHouseId)
    score_star += score_sheng
    hOrg["star"]["star_score_sheng"] = score_star.round(2)

    score = wc_sfr_final_score(score_star,score_sihua,hSfr)

    return score,hOrg
  end
  def wc_sfr_AStar_score_star(hSfr,nHouseId)
    nStar = hSfr["nStar"]
    star_type = Talent.get_star_type_from_star_type_key(Talent::AStarKey)
    star_score = @Obj_Score.get_star_score(star_type,nStar,nHouseId)

    return wc_sfr_Star_score_star(hSfr,nStar,nHouseId,star_score)
  end
  # nStar : 表示 單星曜， nHouseId 表示 單星曜 所在宮位
  def wc_sfr_Star_score_star(hSfr,nStar,nHouseId,org_star_score)
    score = 0.0
    hOrg = {}
    hOrg["star_score"] = score
    hOrg["org_score"] = org_star_score
    hOrg["star_house_weight"] = 100.0
    hOrg["HouseId"] = "house_id_#{nHouseId}"
    return score,hOrg if nHouseId == nil

    a = []
    b = []
    hOrg = {}
    weight_conditions = hSfr["weight_conditions"]
    weight = 0
    weight_conditions.each do |hWc|
      # w 120 代表 1.2倍 ； 原始分數 100變成 120; 若w = 80, 則變成 80; 若 0, 則 0
      bOk,w = wc_one_wc_weight(hWc,nStar,nHouseId)
      a.push(w)
      b.push(bOk)
      weight += wc_weight_cal(w)
    end
    hOrg["weight_condition_score"] = a
    hOrg["is_weight_condition_score_ok"] = b
    special_weight = wc_weight_cal_final(weight)
    hOrg["special_weight"] = special_weight

    star_house_weight = hSfr["star_house_weight"]
    score = wc_star_score(org_star_score,star_house_weight[nHouseId - 1],special_weight)

    stars_weight = hSfr["stars_weight"]
    hOrg["stars_weight"] = stars_weight

    # 星曜權重
    score = score * stars_weight / 100.0
    hOrg["star_score"] = score.round(2)
    hOrg["org_score"] = org_star_score.round(2)
    hOrg["star_house_weight"] = star_house_weight[nHouseId - 1]
    hOrg["HouseId"] = "house_id_#{nHouseId}"

    return score,hOrg
  end
  # 【星曜分數】X【宮位權重】X【特殊加權】aaa
  def wc_star_score(score,house_weight,special_weight)
    # return score * house_weight * special_weight / 10000.0
    score1 = score + ((special_weight - 100) * score.abs) / 100.0
    score2 = score1 * house_weight / 100.0
    return score2.round(2)
  end
  def wc_weight_cal(weight)
    return weight - 100.0
  end
  def wc_weight_cal_final(weight)
    return weight + 100.0
  end

  def wc_sfr_AStar_score_sihua(hSfr,nHouseId)

    return wc_sfr_score_sihua(nHouseId,hSfr)
  end
  def wc_sihua_star_weights(hSfr,nStarHouseId)
    a = []
    aW = []
    weight_conditions = hSfr["sihua_star_weight"]
    weight = 100.0
    weight_conditions = Talent.sihua_star_weight_par_setting_new() if weight_conditions == nil
    weight_conditions.each do |hWc|
      b,wc_weight,aSelfCount,aShengCount,n,hAll = wc_sihua_one_wc_weight(hWc,nStarHouseId)
      a.push([hWc,b,wc_weight,aSelfCount,aShengCount,n,hAll]) if b != nil
      aW.push([wc_weight,aSelfCount,aShengCount,n]) if b
    end

    return aW,a
  end
  # def wc_sihua_star_weight(hSfr,nStarHouseId)
  #   a = []
  #   weight_conditions = hSfr["sihua_star_weight"]
  #   weight = 100.0
  #   weight_conditions = Talent.sihua_star_weight_par_setting_new() if weight_conditions == nil
  #   weight_conditions.each do |hWc|
  #     b,wc_weight,aSelf,aSheng,n,hAll = wc_sihua_one_wc_weight(hWc,nStarHouseId)
  #     a.push([hWc,b,wc_weight]) if b != nil
  #     if (b) then
  #       weight = wc_weight
  #     end
  #   end

  #   return weight,a
  # end
  def Talent.sihua_star_weight_par_setting_new()
    # return Array.new(5,{"sihua_wc_star" => "", "sihua_wc_weight" => -100, "sihua_star_condition" => "sihua_same_house", "sihua_wc_condition" => []})
    # 2020/1/5 從 5 改成 8
    return Talent.sihua_star_weight_par_setting_new2(8)
  end
  def Talent.sihua_star_weight_par_setting_new2(n)
    return Array.new(n,{"sihua_wc_star" => "", "sihua_wc_weight" => -100, "sihua_star_condition" => "sihua_same_house", "sihua_wc_condition" => []})
  end
  def Talent.sihua_sign_new()
    sihua_sign = ["sihua_sign_original","sihua_sign_original","sihua_sign_original","sihua_sign_original"]
    return sihua_sign
  end
  def Talent.sihua_sign_exception_house_new()
    sihua_sign_exception_house = Array.new(4) { Array.new(4,nil)}
    return sihua_sign_exception_house
  end
  def wc_sihua_one_wc_weight(hWc,nStarHouseId)
    wc_star,wc_weight,star_condition,aOwc = wc_star_sihua_get_weight_condition_content(hWc)
    # puts "wc_sihua_one_wc_weight wc_star: #{wc_star}" if wc_star == Talent::SihuaFlyoutKey
    a = Talent.T_Star_Split(wc_star)
    t = Talent.ParseStarsByKey(a)

    aSelfCount,aShengCount = [0,0,0,0],[0,0,0,0]
    if (a.length == 0) then
      return nil,100.0,aSelfCount,aShengCount,0,{}
    end

    b = false
    n = 0
    hAll = {}
    # nStar = t[0][1]
    # nSihuaHouseId = @Obj_Star.gHouse_GetAStar_HouseId(@WC_PanType,nStar)
    # b = wc_sihua_one_wc_check_star(star_condition,nStar,nStarHouseId,aOwc,nSihuaHouseId)
    case t[0][0]
    when Talent::AStarKey
      nStar = t[0][1]
      nSihuaHouseId = @Obj_Star.gHouse_GetAStar_HouseId(@WC_PanType,nStar)
      b,n,aSelfCount,aShengCount,hAll = wc_sihua_one_wc_check_star(star_condition,nStar,nStarHouseId,aOwc,nSihuaHouseId)
    when Talent::SihuaKey
      aSihua = [0,1,2,3]
      aStar_HouseId = gTalent_FindSihuaFlyoutHouse(nStarHouseId,@WC_PanType,aSihua)
      b,n,aSelfCount,aShengCount,hAll = wc_sihua_one_wc_check_sihua(t,star_condition,0,nStarHouseId,aOwc,aStar_HouseId)
    when Talent::SihuaFlyoutKey
      aSihua = [0,1,2,3]
      b,n,aSelfCount,aShengCount,hAll = wc_sihua_one_wc_check_sihua_flyout(t,star_condition,0,nStarHouseId,aOwc,aSihua)
    when Talent::SihuaFlyoutKey_012
      aSihua = [0,1,2]
      b,n,aSelfCount,aShengCount,hAll = wc_sihua_one_wc_check_sihua_flyout(t,star_condition,0,nStarHouseId,aOwc,aSihua)
    when Talent::SihuaFlyoutKey_3
      aSihua = [3]
      b,n,aSelfCount,aShengCount,hAll = wc_sihua_one_wc_check_sihua_flyout(t,star_condition,0,nStarHouseId,aOwc,aSihua)
    when Talent::BodyHouseKey
      nHouseId = @Obj_Star.g_getBodyHouseId(@WC_PanType)
      b,n,aSelfCount,aShengCount,hAll = wc_sihua_one_wc_check_star(star_condition,0,nStarHouseId,aOwc,nHouseId)
    when Talent::SkyKey
      nSky = t[0][1]
      b,n,aSelfCount,aShengCount,hAll = wc_sihua_one_wc_check_sky(star_condition,nSky,nStarHouseId,aOwc)
    end

    return b,wc_weight / 1.0,aSelfCount,aShengCount,n,hAll
  end
  def wc_sihua_one_wc_check_star(star_condition,nStar,nStarHouseId,aOwc,nSihuaHouseId)
    return false,0,[0,0,0,0],[0,0,0,0],{} if nStarHouseId == nil
    return false,0,[0,0,0,0],[0,0,0,0],{} if nSihuaHouseId == nil

    # 先判斷四化選項的宮位是否符合條件
    b,n,a = wc_sihua_one_wc_check_house_condition(star_condition,nStarHouseId,[nSihuaHouseId])
    if (!b) then
      return false,0,[0,0,0,0],[0,0,0,0],{}
    end

    # # 身宮 只要同宮或對宮的判斷即可
    # return true if nStar == 0

    aOwc_Self_Sihua = ["zihua_lu_quan_ke","zihua_lu","zihua_quan","zihua_ke","zihua_ji"]
    aOwc_Sheng_Sihua = ["sheng_lu_quan_ke","sheng_lu","sheng_quan","sheng_ke","sheng_ji"]

    # 設定條件中的四化
    aSelf = wc_get_sihua_ids(aOwc_Self_Sihua & aOwc)
    aSheng = wc_get_sihua_ids(aOwc_Sheng_Sihua & aOwc)

    # 取得命盤中符合條件的四化
    aSelfSihua,aOrgSihua = wc_sihua_one_wc_check_get_sihuas(star_condition,nStar,nStarHouseId,[nSihuaHouseId])

    # # 符合條件
    # aSelf &= aSelfSihua
    # aSheng &= aOrgSihua
    # aAll = aSelf + aSheng

    m,aSelfCount = wc_one_wc_score_wc_check_houses_sihua_count(aSelf,aSelfSihua)
    n,aShengCount = wc_one_wc_score_wc_check_houses_sihua_count(aSheng,aOrgSihua)
    nOut = m + n

    hAll = {}
    hAll["Self_def"] = aSelf
    hAll["Sheng_def"] = aSheng
    hAll["Self_star"] = aSelfSihua
    hAll["Sheng_star"] = aOrgSihua
    hAll["Self_count"] = aSelfCount
    hAll["Sheng_count"] = aShengCount

    return nOut > 0,nOut,aSelfCount,aShengCount,hAll
  end
  def wc_sihua_one_wc_check_get_sihuas(star_condition,nStar,nStarHouseId,aStar_HouseId)
    aSelfSihua = []
    aOrgSihua = []
    if (star_condition == "sihua_same_house") then
      # 同宮
      aSelfSihua,aOrgSihua = wc_sihua_one_wc_get_house_sihuas([nStarHouseId])
    elsif (star_condition == "sihua_chong") then
      # 對宮
      aSelfSihua,aOrgSihua = wc_sihua_one_wc_get_house_sihuas([Earth.ModifyEarth(nStarHouseId + 6)])
    elsif (star_condition == "sihua_star_self") then
      # 本身
      nSelfFourHua,nOrgFourHua = wc_sihua_one_wc_get_star_sihua(nStar) if nStar != 0
      aSelfSihua = [nSelfFourHua] if nSelfFourHua != nil
      aOrgSihua = [nOrgFourHua] if nOrgFourHua != nil
    elsif (star_condition == "sihua_san_fang") then
      # 三方
      a = []
      a.push(Earth.ModifyEarth(nStarHouseId + 2))
      a.push(Earth.ModifyEarth(nStarHouseId - 2))
      # a.push(Earth.ModifyEarth(nStarHouseId + 6))
      a = a & aStar_HouseId
      aSelfSihua,aOrgSihua = wc_sihua_one_wc_get_house_sihuas_2(a)
    end

    return aSelfSihua,aOrgSihua
  end
  def wc_sihua_one_wc_get_star_sihua(nStar)
    starInfo = @Obj_Star.get_A_Star_Info_by_star(@WC_PanType,nStar)
    nSelfFourHua = Star.Get_AStarInfo_SelfFourHua(starInfo)
    hFourHua = Star.Get_AStarInfo_OrgFourHua(starInfo)
    nOrgFourHua = @Obj_Star.getFourHuaHashValue(hFourHua,@WC_PanType)
    return nSelfFourHua,nOrgFourHua
  end
  def wc_sihua_one_wc_get_house_sihuas(aHouseId)
    aSelfSihua,aOrgSihua = wc_sihua_one_wc_get_house_sihuas_2(aHouseId)
    aSelfSihua.uniq!
    aOrgSihua.uniq!
    return aSelfSihua,aOrgSihua
  end
  def wc_sihua_one_wc_get_house_sihuas_2(aHouseId)
    nPanType = @WC_PanType
    aSelfSihua = []
    aOrgSihua = []
    aHouseId.each do |nHouseId|
      nEarth = @Obj_Star.g_House_GetEarth(nPanType,nHouseId)
      aSelfSihua += @Obj_Star.gHouse_FindSelfFourHua_FourHua(nPanType,nEarth)
      aOrgSihua += @Obj_Star.gHouse_GetOrgFourHua_FourHua(nPanType,nEarth)
    end

    return aSelfSihua,aOrgSihua
  end
  def wc_sihua_one_wc_check_sihua(t,star_condition,nStar,nStarHouseId,aOwc,aStar_HouseId)
    aOwc_Self_Sihua = ["zihua_lu_quan_ke","zihua_lu","zihua_quan","zihua_ke","zihua_ji"]
    aOwc_Sheng_Sihua = ["sheng_lu_quan_ke","sheng_lu","sheng_quan","sheng_ke","sheng_ji"]

    aSelf = wc_get_sihua_ids(aOwc_Self_Sihua & aOwc)
    aSheng = wc_get_sihua_ids(aOwc_Sheng_Sihua & aOwc)

    aSelfSihua,aOrgSihua = wc_sihua_one_wc_check_get_sihuas(star_condition,nStar,nStarHouseId,aStar_HouseId)
    # a1 = aSelf & aSelfSihua
    # a2 = aSheng & aOrgSihua
    # aAll = aSelf + aSheng

    m,aSelfCount = wc_one_wc_score_wc_check_houses_sihua_count(aSelf,aSelfSihua)
    n,aShengCount = wc_one_wc_score_wc_check_houses_sihua_count(aSheng,aOrgSihua)
    nOut = m + n

    hAll = {}
    hAll["Self_def"] = aSelf
    hAll["Sheng_def"] = aSheng
    hAll["Self_star"] = aSelfSihua
    hAll["Sheng_star"] = aOrgSihua
    hAll["Self_count"] = aSelfCount
    hAll["Sheng_count"] = aShengCount

    return nOut > 0,nOut,aSelfCount,aShengCount,hAll
  end
  def wc_sihua_one_wc_check_sihua_flyout(t,star_condition,nStar,nStarHouseId,aOwc,aSihua_in)
    nPanType = @WC_PanType

    aOwc_Self_Sihua = ["zihua_lu_quan_ke","zihua_lu","zihua_quan","zihua_ke","zihua_ji"]
    aSelf = wc_get_sihua_ids(aOwc_Self_Sihua & aOwc)
    aSelf = [0,1,2,3] if aOwc_Self_Sihua & aOwc == []

    aSihua = aSihua_in & aSelf
    aHouseId = gTalent_FindSihuaFlyoutHouse(nStarHouseId,nPanType,aSihua)
    b,n,a = wc_sihua_one_wc_check_house_condition(star_condition,nStarHouseId,aHouseId)

    aSelfCount = [0,0,0,0]
    aHouseId.each_with_index do |house_id,i|
      aSelfCount[aSihua[i]] += 1 if a.include?(house_id)
    end

    hAll = {}
    hAll["Self_def"] = aSelf
    hAll["Sheng_def"] = []
    hAll["Self_star"] = aSihua
    hAll["Sheng_star"] = []

    if (!b) then
      return false,0,[0,0,0,0],[0,0,0,0],{}
    end
    return true,n,aSelfCount,[0,0,0,0],hAll
  end

  def wc_sihua_one_wc_check_sky(star_condition,nSky,nStarHouseId,aOwc)
    aHouseId = @Obj_Star.gHouse_GetHouseIdFromTiangan(@WC_PanType,nSky)
    return false,1,[],[],{} if !aHouseId.include?(nStarHouseId)

    # 單星曜所在宮位天干為nSky
    aOwc_Self_Sihua = ["zihua_lu_quan_ke","zihua_lu","zihua_quan","zihua_ke","zihua_ji"]
    aOwc_Sheng_Sihua = ["sheng_lu_quan_ke","sheng_lu","sheng_quan","sheng_ke","sheng_ji"]

    aSelf = wc_get_sihua_ids(aOwc_Self_Sihua & aOwc)
    aSheng = wc_get_sihua_ids(aOwc_Sheng_Sihua & aOwc)

    aSelfSihua,aOrgSihua = wc_sihua_one_wc_check_get_sihuas(star_condition,0,nStarHouseId,[1,2,3,4,5,6,7,8,9,10,11,12])
    # aSelf &= aSelfSihua
    # aSheng &= aOrgSihua
    # aAll = aSelf + aSheng

    m,aSelfCount = wc_one_wc_score_wc_check_houses_sihua_count(aSelf,aSelfSihua)
    n,aShengCount = wc_one_wc_score_wc_check_houses_sihua_count(aSheng,aOrgSihua)
    nOut = m + n

    hAll = {}
    hAll["Self_def"] = aSelf
    hAll["Sheng_def"] = aSheng
    hAll["Self_star"] = aSelfSihua
    hAll["Sheng_star"] = aOrgSihua
    hAll["Self_count"] = aSelfCount
    hAll["Sheng_count"] = aShengCount

    return nOut > 0,nOut,aSelfCount,aShengCount,hAll
  end
  def wc_sihua_one_wc_check_house_condition(star_condition,nStarHouseId,aSihuaHouseId)
    if (star_condition == "sihua_same_house") then
      # 是否同宮？
      if (aSihuaHouseId.include?(nStarHouseId)) then
        return true,1,[nStarHouseId]
      end
    elsif (star_condition == "sihua_chong") then
      # 是否對宮？
      if (aSihuaHouseId.include?(Earth.ModifyEarth(nStarHouseId + 6))) then
        return true,1,[Earth.ModifyEarth(nStarHouseId + 6)]
      end
    elsif (star_condition == "sihua_star_self") then
      # 本身
      if (aSihuaHouseId.include?(nStarHouseId)) then
        return true,1,[nStarHouseId]
      end
    elsif (star_condition == "sihua_san_fang") then
      # 三方
      a = []
      a.push(Earth.ModifyEarth(nStarHouseId + 4))
      a.push(Earth.ModifyEarth(nStarHouseId - 4))
      a.push(Earth.ModifyEarth(nStarHouseId + 6))
      a &= aSihuaHouseId
      return a.length > 0,a.length,a
    end

    return false,0,[]
  end
  def wc_get_sihua_ids(aOwc)
    a = []
    aOwc.each do |owc|
      a.push(0) if owc.include?("_lu")
      a.push(1) if owc.include?("_quan")
      a.push(2) if owc.include?("_ke")
      a.push(3) if owc.include?("_ji")
    end
    a.uniq!

    return a
  end
  def wc_star_sihua_get_weight_condition_content(hWc)
    wc_star = wc_star_sihua_wc_get_wc_star(hWc)
    wc_weight = wc_star_sihua_wc_get_wc_weight(hWc)
    sihua_star_condition = wc_star_sihua_wc_get_star_condition(hWc)
    weight_condition = wc_star_sihua_wc_get_weight_condition(hWc)
    return wc_star,wc_weight,sihua_star_condition,weight_condition
  end
  def wc_star_sihua_wc_get_wc_star(hWc)
    wc_star = hWc["sihua_wc_star"]
    return wc_star
  end
  def wc_star_sihua_wc_get_wc_weight(hWc)
    wc_weight = hWc["sihua_wc_weight"]
    return wc_weight.to_i
  end
  def wc_star_sihua_wc_get_star_condition(hWc)
    sihua_star_condition = hWc["sihua_star_condition"]
    return sihua_star_condition
  end
  def wc_star_sihua_wc_get_weight_condition(hWc)
    weight_condition = hWc["sihua_wc_condition"]
    # 沒有設定表示同宮
    weight_condition.push("zihua_lu_quan_ke") if weight_condition == []
    return weight_condition
  end

  def wc_sfr_score_sihua(nStarHouseId,hSfr)
    score_sihua = 0.0
    hOrg = {}
    hOrg["HouseId"] = nStarHouseId
    hOrg["org_house_sihua_score"] = 0
    hOrg["sihua_one_house_weight"] = 0
    hOrg["sihuas_weight"] = 0
    hOrg["score_sihuas"] = 0
    hOrg["score_sihua_before_weight"] = 0
    hOrg["sihua_star_weight_detail"] = []
    hOrg["sihua_star_weight"] = 0
    hOrg["score_sihua"] = 0
    return score_sihua,hOrg if nStarHouseId == nil

    # 取得原始四化分數 [4]
    org_house_sihua_score = @Obj_Score.get_house_sihua_score(nStarHouseId)

    # 四化在各宮的加權
    sihua_house_weight = hSfr["sihua_house_weight"]
    sihua_one_house_weight = [0,0,0,0]
    score_sihuas = org_house_sihua_score.clone

    # 計算符合的加權條件
    aSsw,hOrg["sihua_star_weight_detail_all"] = wc_sihua_star_weights(hSfr,nStarHouseId)

    sihua_signs = hSfr["sihua_sign"]
    sihua_signs = Talent.sihua_sign_new() if sihua_signs == nil
    sihua_sign_exception_houses = hSfr["sihua_sign_exception_house"]
    sihua_sign_exception_houses = Talent.sihua_sign_exception_house_new() if sihua_sign_exception_houses == nil
    sihua_star_weights = [100.0,100.0,100.0,100.0]
    sihua_star_weight_details = [[],[],[],[]]
    score = 0
    aSihua_def = [70,70,70,-70]
    (0..3).each do |nFourHua|
      # 取得四化在此宮的加權
      sihua_one_house_weight[nFourHua] = sihua_house_weight[nFourHua][nStarHouseId - 1]

      # 四化修件加權
      sihua_weight,sihua_star_weight_detail = wc_sihua_get_weight(aSsw,nFourHua)

      # 2020/1/5 steven add when score_sihua is 0 but has weight to prevent 0
      score_sihuas[nFourHua] = aSihua_def[nFourHua] if score_sihuas[nFourHua] == 0 && sihua_weight != 100.0

      # 四化分數依宮位加權
      score_sihuas[nFourHua] = score_sihuas[nFourHua] * sihua_one_house_weight[nFourHua] / 100.0

      score_sihuas[nFourHua] = score_sihuas[nFourHua] * sihua_weight / 100.0
      sihua_star_weights[nFourHua] = sihua_weight
      sihua_star_weight_details[nFourHua] = sihua_star_weight_detail

      # 正負值判斷
      score_sihuas[nFourHua] = wc_sihua_sign_score(score_sihuas[nFourHua],sihua_signs,nFourHua,sihua_sign_exception_houses,nStarHouseId)

      score += score_sihuas[nFourHua]
    end
    sihuas_weight = hSfr["sihuas_weight"]
    score_sihua = score * sihuas_weight / 100.0

    # hOrg = {}
    hOrg["HouseId"] = nStarHouseId
    hOrg["org_house_sihua_score"] = org_house_sihua_score
    hOrg["sihua_one_house_weight"] = sihua_one_house_weight
    hOrg["sihuas_weight"] = sihuas_weight
    hOrg["score_sihuas"] = score_sihuas
    hOrg["score_sihua_before_weight"] = score
    hOrg["sihua_signs"] = sihua_signs
    hOrg["sihua_sign_exception_house"] = sihua_sign_exception_houses

    hOrg["sihua_star_weight_detail"] = sihua_star_weight_details
    hOrg["sihua_star_weight"] = sihua_star_weights
    hOrg["score_sihua"] = score_sihua

    return score_sihua,hOrg
  end
  def wc_sihua_sign_score(score,sihua_signs,nFourHua,sihua_sign_exception_houses,nStarHouseId)
    return score if sihua_sign_exception_houses[nFourHua].include?(nStarHouseId) && nStarHouseId != 0
    return score if sihua_signs[nFourHua] == "sihua_sign_original"
    return score.abs if sihua_signs[nFourHua] == "sihua_sign_positive"
    return (score.abs * -1) if sihua_signs[nFourHua] == "sihua_sign_negative"
    return score
  end
  # 多個相同的四化時，累積乘 W1 * W2 * ...
  def wc_sihua_get_weight(sihua_star_weights,nFourHua)
    weight = 100.0
    w2 = 0.0
    sihua_star_weight_detail = []
    sihua_star_weights.each do |sihua_star_weight|
      w = sihua_star_weight[0]
      aSelfCount = sihua_star_weight[1]
      aShengCount = sihua_star_weight[2]
      # n = sihua_star_weight[3]
      aAll = [aSelfCount[nFourHua]] + [aShengCount[nFourHua]]
      n = aSelfCount[nFourHua] + aShengCount[nFourHua]
      # weight = weight * w * n / 100.0 if n > 0
      w2 = w2 + (w * n) if n > 0
      sihua_star_weight_detail.push(aAll) if n > 0
    end
    weight = weight * w2 / 100.0 if w2 != 0.0
    return weight,sihua_star_weight_detail
  end
  # def wc_sfr_score_sihua_old(nStarHouseId,hSfr)
  #   score_sihua = 0.0
  #   hOrg = {}
  #   hOrg["HouseId"] = nStarHouseId
  #   hOrg["org_house_sihua_score"] = 0
  #   hOrg["sihua_one_house_weight"] = 0
  #   hOrg["sihuas_weight"] = 0
  #   hOrg["score_sihuas"] = 0
  #   hOrg["score_sihua_before_weight"] = 0
  #   hOrg["sihua_star_weight_detail"] = []
  #   hOrg["sihua_star_weight"] = 0
  #   hOrg["score_sihua"] = 0
  #   return score_sihua,hOrg if nStarHouseId == nil

  #   # 取得原始四化分數 [4]
  #   org_house_sihua_score = @Obj_Score.get_house_sihua_score(nStarHouseId)

  #   # 四化在各宮的加權
  #   sihua_house_weight = hSfr["sihua_house_weight"]
  #   sihua_one_house_weight = [0,0,0,0]
  #   score_sihuas = org_house_sihua_score.clone
  #   score = 0
  #   (0..3).each do |i|
  #     # 取得四化在此宮的加權
  #     sihua_one_house_weight[i] = sihua_house_weight[i][nStarHouseId - 1]
  #     # 四化分數依宮位加權
  #     score_sihuas[i] = score_sihuas[i] * sihua_one_house_weight[i] / 100.0
  #     score += score_sihuas[i]
  #   end
  #   sihuas_weight = hSfr["sihuas_weight"]
  #   score_sihua = score * sihuas_weight / 100.0

  #   hOrg = {}
  #   hOrg["HouseId"] = nStarHouseId
  #   hOrg["org_house_sihua_score"] = org_house_sihua_score
  #   hOrg["sihua_one_house_weight"] = sihua_one_house_weight
  #   hOrg["sihuas_weight"] = sihuas_weight
  #   hOrg["score_sihuas"] = score_sihuas
  #   hOrg["score_sihua_before_weight"] = score_sihua

  #   sihua_star_weight,hOrg["sihua_star_weight_detail"] = wc_sihua_star_weight(hSfr,nStarHouseId)
  #   hOrg["sihua_star_weight"] = sihua_star_weight
  #   score_sihua = score_sihua * sihua_star_weight / 100.0
  #   hOrg["score_sihua"] = score_sihua

  #   return score_sihua,hOrg
  # end
  def wc_sfr_final_score(score_star,score_sihua,hSfr)
    star_weight = hSfr["star_weight"]

    score = (score_star + score_sihua) * star_weight / 100.0
    return score.round(2)
  end
  def wc_sfr_get_shenggong_scores(score_star,hSfr,hOrg,nStarHouseId)
    # 身宮獨立增加的分數(依身宮比重)，不列入 「星曜比重」 的計算，是算完後，再額外再加上去
    # 加上身宮分數之後，再加上四化分數，才乘以星垣總權重
    nShengGongHouseId = @Obj_Star.g_getBodyHouseId(@WC_PanType)
    hOrg_sheng = {}
    score_sheng,hOrg_sheng["detail"] = wc_sfr_shenggong_scores(score_star,hSfr,hOrg,nStarHouseId == nShengGongHouseId)
    hOrg_sheng["score"] = score_sheng.round(2)

    return score_sheng,hOrg_sheng
  end

  def wc_sfr_BStar_score(hSfr)
    nStar = hSfr["nStar"]
    nHouseId = @Obj_Star.gHouse_GetBStar_HouseId(@WC_PanType,nStar)

    hOrg = {}
    score_star,hOrg["star"] = wc_sfr_BStar_score_star(hSfr,nHouseId)
    score_sihua,hOrg["sihua"] = wc_sfr_BStar_score_sihua(hSfr,nHouseId)

    # 加上身宮分數判斷
    score_sheng,hOrg["star"]["shenggong_score"] = wc_sfr_get_shenggong_scores(score_star,hSfr,hOrg,nHouseId)
    score_star += score_sheng
    hOrg["star"]["star_score_sheng"] = score_star.round(2)

    score = wc_sfr_final_score(score_star,score_sihua,hSfr)
    hOrg["score"] = score
    return score,hOrg
  end
  def wc_sfr_BStar_score_star(hSfr,nHouseId)
    nStar = hSfr["nStar"]
    star_type = Talent.get_star_type_from_star_type_key(Talent::BStarKey)
  # s = "Talent_Star line 798 :star_type,nStar,nHouseId : #{star_type},#{nStar},#{nHouseId}"
  # Pm.saveTestDb("logger_info Talent_Star line 798",s)
    star_score = @Obj_Score.get_star_score(star_type,nStar,nHouseId)
    return wc_sfr_Star_score_star(hSfr,nStar,nHouseId,star_score)
  end
  def wc_sfr_BStar_score_sihua(hSfr,nHouseId)

    return wc_sfr_score_sihua(nHouseId,hSfr)
  end

  def wc_sfr_Doctor12_score(hSfr)
    nStar = hSfr["nStar"]
    nHouseId = @Obj_Star.get_Doctor_HouseId(@WC_PanType,nStar)

    hOrg = {}
    score_star,hOrg["star"] = wc_sfr_Doctor12_score_star(hSfr,nHouseId)
    score_sihua,hOrg["sihua"] = wc_sfr_Doctor12_score_sihua(hSfr,nHouseId)

    # 加上身宮分數判斷
    score_sheng,hOrg["star"]["shenggong_score"] = wc_sfr_get_shenggong_scores(score_star,hSfr,hOrg,nHouseId)
    score_star += score_sheng
    hOrg["star"]["star_score_sheng"] = score_star.round(2)

    score = wc_sfr_final_score(score_star,score_sihua,hSfr)
    hOrg["score"] = score
    return score,hOrg
  end
  def wc_sfr_Doctor12_score_star(hSfr,nHouseId)
    nStar = hSfr["nStar"]
    star_type = Talent.get_star_type_from_star_type_key(Talent::Doctor12Key)
    star_score = @Obj_Score.get_star_score(star_type,nStar,nHouseId)

    return wc_sfr_Star_score_star(hSfr,nStar,nHouseId,star_score)
  end
  def wc_sfr_Doctor12_score_sihua(hSfr,nHouseId)

    return wc_sfr_score_sihua(nHouseId,hSfr)
  end

  def wc_sfr_God12_score(hSfr)
    nStar = hSfr["nStar"]
    nHouseId = @Obj_Star.get_God_HouseId(@WC_PanType,nStar)

    hOrg = {}
    score_star,hOrg["star"] = wc_sfr_God12_score_star(hSfr,nHouseId)
    score_sihua,hOrg["sihua"] = wc_sfr_God12_score_sihua(hSfr,nHouseId)

    # 加上身宮分數判斷
    score_sheng,hOrg["star"]["shenggong_score"] = wc_sfr_get_shenggong_scores(score_star,hSfr,hOrg,nHouseId)
    score_star += score_sheng
    hOrg["star"]["star_score_sheng"] = score_star.round(2)

    score = wc_sfr_final_score(score_star,score_sihua,hSfr)
    hOrg["score"] = score
    return score,hOrg
  end
  def wc_sfr_God12_score_star(hSfr,nHouseId)
    nStar = hSfr["nStar"]
    star_type = Talent.get_star_type_from_star_type_key(Talent::God12Key)
    star_score = @Obj_Score.get_star_score(star_type,nStar,nHouseId)
    return wc_sfr_Star_score_star(hSfr,nStar,nHouseId,star_score)
  end
  def wc_sfr_God12_score_sihua(hSfr,nHouseId)

    return wc_sfr_score_sihua(nHouseId,hSfr)
  end

  def wc_sfr_YearStar12_score(hSfr)
    nStar = hSfr["nStar"]
    nHouseId = @Obj_Star.get_YearStar_HouseId(@WC_PanType,nStar)

    hOrg = {}
    score_star,hOrg["star"] = wc_sfr_YearStar12_score_star(hSfr,nHouseId)
    score_sihua,hOrg["sihua"] = wc_sfr_YearStar12_score_sihua(hSfr,nHouseId)

    # 加上身宮分數判斷
    score_sheng,hOrg["star"]["shenggong_score"] = wc_sfr_get_shenggong_scores(score_star,hSfr,hOrg,nHouseId)
    score_star += score_sheng
    hOrg["star"]["star_score_sheng"] = score_star.round(2)

    score = wc_sfr_final_score(score_star,score_sihua,hSfr)
    hOrg["score"] = score
    return score,hOrg
  end
  def wc_sfr_YearStar12_score_star(hSfr,nHouseId)
    nStar = hSfr["nStar"]
    star_type = Talent.get_star_type_from_star_type_key(Talent::YearStar12Key)
    star_score = @Obj_Score.get_star_score(star_type,nStar,nHouseId)
    return wc_sfr_Star_score_star(hSfr,nStar,nHouseId,star_score)
  end
  def wc_sfr_YearStar12_score_sihua(hSfr,nHouseId)

    return wc_sfr_score_sihua(nHouseId,hSfr)
  end

  def wc_sfr_YearGod12_score(hSfr)
    nStar = hSfr["nStar"]
    nHouseId = @Obj_Star.get_YearGod_HouseId(@WC_PanType,nStar)

    hOrg = {}
    score_star,hOrg["star"] = wc_sfr_YearGod12_score_star(hSfr,nHouseId)
    score_sihua,hOrg["sihua"] = wc_sfr_YearGod12_score_sihua(hSfr,nHouseId)

    # 加上身宮分數判斷
    score_sheng,hOrg["star"]["shenggong_score"] = wc_sfr_get_shenggong_scores(score_star,hSfr,hOrg,nHouseId)
    score_star += score_sheng
    hOrg["star"]["star_score_sheng"] = score_star.round(2)

    score = wc_sfr_final_score(score_star,score_sihua,hSfr)
    hOrg["score"] = score
    return score,hOrg
  end
  def wc_sfr_YearGod12_score_star(hSfr,nHouseId)
    nStar = hSfr["nStar"]
    star_type = Talent.get_star_type_from_star_type_key(Talent::YearGod12Key)
    star_score = @Obj_Score.get_star_score(star_type,nStar,nHouseId)
    return wc_sfr_Star_score_star(hSfr,nStar,nHouseId,star_score)
  end
  def wc_sfr_YearGod12_score_sihua(hSfr,nHouseId)

    return wc_sfr_score_sihua(nHouseId,hSfr)
  end

  def wc_sfr_HouseId_score(hSfr)
    nStar = hSfr["nStar"]
    nHouseId = nStar

    hOrg = {}
    score_star,hOrg["star"] = wc_sfr_HouseId_score_star(hSfr,nHouseId)
    score_sihua,hOrg["sihua"] = wc_sfr_HouseId_score_sihua(hSfr,nHouseId)

    # 加上身宮分數判斷
    score_sheng,hOrg["star"]["shenggong_score"] = wc_sfr_get_shenggong_scores(score_star,hSfr,hOrg,nHouseId)
    score_star += score_sheng
    hOrg["star"]["star_score_sheng"] = score_star.round(2)

    score = wc_sfr_final_score(score_star,score_sihua,hSfr)
    hOrg["score"] = score
    return score,hOrg
  end
  def wc_sfr_HouseId_score_star(hSfr,nHouseId)
    star_score = @Obj_Score.get_house_score(nHouseId)
    return wc_sfr_Star_score_star(hSfr,0,nHouseId,star_score)
  end
  def wc_sfr_HouseId_score_sihua(hSfr,nHouseId)
    return wc_sfr_score_sihua(nHouseId,hSfr)
  end
  # Peter add for 身宮 in setup_star_rule 單星曜
  # 身宮的 nStar 是 0 ( "".to_i)
  def wc_sfr_BodyHouse_score(hSfr)
    nHouseId = @Obj_Star.g_getBodyHouseId(@WC_PanType)

    hOrg = {}
    score_star,hOrg["star"] = wc_sfr_BodyHouse_score_star(hSfr,nHouseId)
    score_sihua,hOrg["sihua"] = wc_sfr_BodyHouse_score_sihua(hSfr,nHouseId)

    # 加上身宮分數判斷
    score_sheng,hOrg["star"]["shenggong_score"] = wc_sfr_get_shenggong_scores(score_star,hSfr,hOrg,nHouseId)
    score_star += score_sheng
    hOrg["star"]["star_score_sheng"] = score_star.round(2)

    score = wc_sfr_final_score(score_star,score_sihua,hSfr)
    hOrg["score"] = score
    return score,hOrg
  end
  def wc_sfr_BodyHouse_score_star(hSfr,nHouseId)

    star_score = @Obj_Score.get_house_score(nHouseId)
    return wc_sfr_Star_score_star(hSfr,0,nHouseId,star_score)
  end
  def wc_sfr_BodyHouse_score_sihua(hSfr,nHouseId)
    return wc_sfr_score_sihua(nHouseId,hSfr)
  end

# 甲級星：同宮  對宮  三方（OR）, 另外，可以只選 （自祿、權、科  自祿   自權  自科  自忌  生祿、權、科  生祿   生權  生科  生忌）代表本星四化
# 乙級星：同宮  對宮  三方（OR）
# 長生十二星：同宮  對宮  三方（OR）
# 博士星：同宮  對宮  三方（OR）
# 將前星：同宮  對宮  三方（OR）
# 歲前星：同宮  對宮  三方（OR）
# 雙星：同宮  對宮  三方（OR）  夾
# 祿權科：同宮  對宮  三方（OR）  夾  自祿、權、科  自祿   自權  自科  自忌  生祿、權、科  生祿   生權  生科  生忌（AND）
# 四化：同宮  對宮  三方（OR）  夾  自祿、權、科  自祿   自權  自科  自忌  生祿、權、科  生祿   生權  生科  生忌（OR）
# 宮位：無主星
# 身宮：同宮
# nStar : 表示 單星曜， nHouseId 表示 單星曜 所在宮位
  def wc_one_wc_weight(hWc,nStar,nHouseId)
    wc_star,wc_weight,aOwc = wc_star_get_weight_condition_content(hWc)
    a = Talent.T_Star_Split(wc_star)
    t = Talent.ParseStarsByKey(a)
    if (a.length == 0) then
      return false,100.0
    end
    # 祿 權 科 
    if (a.length == 3) then
      b = wc_one_wc_score_sihua3(t,aOwc,nStar,nHouseId)
      return wc_one_wc_final(b,wc_weight)
    end

    # 雙星
    if (a.length == 2) then
      b = wc_one_wc_score_ShuangXing(t,aOwc,nStar,nHouseId)
      return wc_one_wc_final(b,wc_weight)
    end

    # puts "t : #{t[0]}"
    case t[0][0]
    when Talent::AStarKey
      b = wc_one_wc_score_AStar(t,aOwc,nStar,nHouseId)
      return wc_one_wc_final(b,wc_weight)
    when Talent::BStarKey
      b = wc_one_wc_score_Star(t,aOwc,nStar,nHouseId)
      return wc_one_wc_final(b,wc_weight)
    when Talent::Doctor12Key
      b = wc_one_wc_score_Star(t,aOwc,nStar,nHouseId)
      return wc_one_wc_final(b,wc_weight)
    when Talent::God12Key
      b = wc_one_wc_score_Star(t,aOwc,nStar,nHouseId)
      return wc_one_wc_final(b,wc_weight)
    when Talent::YearStar12Key
      b = wc_one_wc_score_Star(t,aOwc,nStar,nHouseId)
      return wc_one_wc_final(b,wc_weight)
    when Talent::YearGod12Key
      b = wc_one_wc_score_Star(t,aOwc,nStar,nHouseId)
      return wc_one_wc_final(b,wc_weight)
    when Talent::SihuaKey
      # b = wc_one_wc_score_Sihua(t,aOwc,nStar,nHouseId)
      b,n = wc_one_wc_score_SihuaFlyout(t,aOwc,nStar,nHouseId,[0,1,2,3])
      w = wc_weight_mult_n(wc_weight,n)
      return wc_one_wc_final(b,w)
    when Talent::SihuaFlyoutKey
      b,n = wc_one_wc_score_SihuaFlyout(t,aOwc,nStar,nHouseId,[0,1,2,3])
      w = wc_weight_mult_n(wc_weight,n)
      return wc_one_wc_final(b,w)
    when Talent::SihuaFlyoutKey_012
      b,n = wc_one_wc_score_SihuaFlyout(t,aOwc,nStar,nHouseId,[0,1,2])
      w = wc_weight_mult_n(wc_weight,n)
      return wc_one_wc_final(b,w)
    when Talent::SihuaFlyoutKey_3
      b,n = wc_one_wc_score_SihuaFlyout(t,aOwc,nStar,nHouseId,[3])
      w = wc_weight_mult_n(wc_weight,n)
      return wc_one_wc_final(b,w)
    when Talent::SihuaFlyinKey_OnlyStar_012
      b,n = wc_one_wc_score_SihuaFlyin_OnlyStar(t,aOwc,nStar,nHouseId,[0,1,2])
      w = wc_weight_mult_n(wc_weight,n)
      return wc_one_wc_final(b,w)
    when Talent::SihuaFlyinKey_OnlyStar_3
      b,n = wc_one_wc_score_SihuaFlyin_OnlyStar(t,aOwc,nStar,nHouseId,[3])
      w = wc_weight_mult_n(wc_weight,n)
      return wc_one_wc_final(b,w)
    when Talent::SihuaFlyinKey_SameHouse_012
      b,n = wc_one_wc_score_SihuaFlyin_SameHouse(t,aOwc,nStar,nHouseId,[0,1,2])
      w = wc_weight_mult_n(wc_weight,n)
      return wc_one_wc_final(b,w)
    when Talent::SihuaFlyinKey_SameHouse_3
      b,n = wc_one_wc_score_SihuaFlyin_SameHouse(t,aOwc,nStar,nHouseId,[3])
      w = wc_weight_mult_n(wc_weight,n)
      return wc_one_wc_final(b,w)
    when Talent::HouseIdKey
      b = wc_one_wc_score_HouseId(t,aOwc,nStar,nHouseId)
      return wc_one_wc_final(b,wc_weight)
    when Talent::BodyHouseKey
      b = wc_one_wc_score_BodyHouse(t,aOwc,nStar,nHouseId)
      return wc_one_wc_final(b,wc_weight)
    end
    return false,100.0
  end
  def wc_weight_mult_n(wc_weight,n)
    w = wc_weight_cal(wc_weight)
    return wc_weight_cal_final(w * n)
  end
  # 祿 權 科 同宮 有自化或生年四化
  def wc_one_wc_score_sihua3(t,aOwc,nStar,nHouseId)
    b = true
    aHouse,n = wc_one_wc_score_wc_check_houses_1(t,aOwc,nStar,nHouseId)

    aOwc_Self_Sihua = ["zihua_lu_quan_ke","zihua_lu","zihua_quan","zihua_ke"]
    aOwc_Sheng_Sihua = ["sheng_lu_quan_ke","sheng_lu","sheng_quan","sheng_ke"]

    # 設定條件中的四化
    aSelf = wc_get_sihua_ids(aOwc_Self_Sihua & aOwc)
    aSheng = wc_get_sihua_ids(aOwc_Sheng_Sihua & aOwc)
    aSelf = [0,1,2] if aOwc_Self_Sihua & aOwc == [] && aOwc_Sheng_Sihua & aOwc == []
    aSheng = [0,1,2] if aOwc_Self_Sihua & aOwc == [] && aOwc_Sheng_Sihua & aOwc == []
    # aSihua = [t[0][1],t[1][1],t[2][1]]
    # aSihua = [0,1,2]
    a = gTalent_FindSihuaHouse(t,nHouseId,@WC_PanType,aSelf)
    a += gTalent_FindFlowSihuaHouse(t,nHouseId,@WC_PanType,aSheng)
    a = a & aHouse

    return aHouse.length >= n
  end
  def wc_one_wc_score_ShuangXing(t,aOwc,nStar,nHouseId)
    b = true
    aHouse,n = wc_one_wc_score_wc_check_houses_1(t,aOwc,nStar,nHouseId)

    a = []
    aStarType = [t[0][0],t[1][0]]
    aStar = [t[0][1],t[1][1]]
    # aSihua = [0,1,2]
    aStarType.each_with_index do |st,i|
      ns = aStar[i]
      if (st == Talent::AStarKey) then
        nHouseId = @Obj_Star.gHouse_GetAStar_HouseId(@WC_PanType,ns)
      elsif (st == Talent::BStarKey) then
        nHouseId = @Obj_Star.gHouse_GetBStar_HouseId(@WC_PanType,ns)
      end
      a.push(nHouseId)
    end
    a = a & aHouse
    return a.length >= 2
  end
  def wc_one_wc_score_AStar(t,aOwc,nStar,nHouseId)
    aOwc_Sihua = ["zihua_lu_quan_ke","zihua_lu","zihua_quan","zihua_ke","zihua_ji",
          "sheng_lu_quan_ke","sheng_lu","sheng_quan","sheng_ke","sheng_ji"]
    a = aOwc_Sihua & aOwc
    a2 = aOwc - a
    bOnlySihua = true
    bOnlySihua = false if (a2.length > 0)
    t1 = t[0]

    b = true
    if (bOnlySihua) then
        aHouse,n = wc_one_wc_score_wc_check_sihuas(t,aOwc,nStar,nHouseId)
        # 只要該星有符合四化即可
        return aHouse.length >= n
    else
      aHouse,n = wc_one_wc_score_wc_check_houses_1(t,aOwc,nStar,nHouseId)
  
      a = []
      aStarType = [t1[0]]
      aStar = [t1[1]]
      # aSihua = [0,1,2]
      aStarType.each_with_index do |st,i|
        ns = aStar[i]
        house_id = @Obj_Star.gHouse_GetAStar_HouseId(@WC_PanType,ns)
        a.push(house_id)
      end
      a = a & aHouse

      return a.length >= 1
    end
  end
  def wc_one_wc_score_wc_check_sihuas(t,aOwc,nStar,nHouseId)
    starInfo = @Obj_Star.get_A_Star_Info_by_star(@WC_PanType,nStar)
    hFourHua = Star.Get_AStarInfo_OrgFourHua(starInfo)
    nOrgFourHua = @Obj_Star.getFourHuaHashValue(hFourHua,@WC_PanType)
    nSelfFourHua = Star.Get_AStarInfo_SelfFourHua(starInfo)
    nHouseId_star = @Obj_Star.gHouse_GetAStar_HouseId(@WC_PanType,nStar)

    aHouse = []
    aOwc_Self = ["zihua_lu_quan_ke","zihua_lu","zihua_quan","zihua_ke","zihua_ji"]
    aOwc_Self_idx = [[0,1,2],[0],[1],[2],[3]]
    aOwc_Org = ["sheng_lu_quan_ke","sheng_lu","sheng_quan","sheng_ke","sheng_ji"]
    aOwc_Org_idx = [[0,1,2],[0],[1],[2],[3]]
    aOwc.each do |owc|
      n = aOwc_Self.index(owc)
      if (n != nil) then
        aHouse.push(nHouseId_star) if aOwc_Self_idx[n].include?(nSelfFourHua)
      end
      n = aOwc_Org.index(owc)
      if (n != nil) then
        aHouse.push(nHouseId_star) if aOwc_Org_idx[n].include?(nOrgFourHua)
      end
    end
    return aHouse,1
  end
  def wc_one_wc_score_Star(t,aOwc,nStar,nHouseId)
    b = true
    aHouse,n = wc_one_wc_score_wc_check_houses_1(t,aOwc,nStar,nHouseId)
    t1 = t[0]

    a = []
    aStarType = [t1[0]]
    aStar = [t1[1]]
    # aSihua = [0,1,2]
    aStarType.each_with_index do |st,i|
      ns = aStar[i]
      if (st == Talent::BStarKey) then
        nHouseId_star = @Obj_Star.gHouse_GetBStar_HouseId(@WC_PanType,ns)
      elsif (st == Talent::Doctor12Key) then
        nHouseId_star = @Obj_Star.get_Doctor_HouseId(@WC_PanType,ns)
      elsif (st == Talent::God12Key) then
        nHouseId_star = @Obj_Star.get_God_HouseId(@WC_PanType,ns)
      elsif (st == Talent::YearStar12Key) then
        nHouseId_star = @Obj_Star.get_YearStar_HouseId(@WC_PanType,ns)
      elsif (st == Talent::YearGod12Key) then
        nHouseId_star = @Obj_Star.get_YearGod_HouseId(@WC_PanType,ns)
      end
      a.push(nHouseId_star) if nHouseId_star != nil
    end
    a = a & aHouse
    return a.length >= 1
  end
  # 四化用 OR 自化 或 生年四化 用 OR 集合
  # 例如： 同宮 而且 自化祿權科或生年祿權科
  # def wc_one_wc_score_Sihua(t,aOwc,nStar,nHouseId)
  #   b = true
  #   aHouse,n = wc_one_wc_score_wc_check_houses_or(t,aOwc,nStar,nHouseId)

  #   # aSihua = [t[0][1],t[1][1],t[2][1]]
  #   # aSihua = [nStar] #[0,1,2,3]
  #   # a = gTalent_FindSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
  #   # a += gTalent_FindFlowSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
  #   # a = a & aHouse

  #   return aHouse.length >= n
  # end
  def wc_one_wc_score_SihuaFlyout(t,aOwc,nStar,nHouseId,aSihua)
    b = true
    aHouseId,n1 = wc_one_wc_score_wc_check_houses_2(t,aOwc,nStar,nHouseId,aSihua)

    n = wc_one_wc_score_wc_check_house_sihua(aOwc,aHouseId)
    return n > 0,n
  end

  def wc_one_wc_score_SihuaFlyin_OnlyStar(t,aOwc,nStar,nHouseId,aSihua)
    aHouseId,n = wc_one_wc_score_wc_check_houses_4(t,aOwc,nStar,nHouseId,aSihua,true)

    return n > 0,n
  end
  def wc_one_wc_score_SihuaFlyin_SameHouse(t,aOwc,nStar,nHouseId,aSihua)
    aHouseId,n = wc_one_wc_score_wc_check_houses_4(t,aOwc,nStar,nHouseId,aSihua,false)

    return n > 0,n
  end

  def wc_one_wc_score_HouseId(t,aOwc,nStar,nHouseId)
    b = true
    aHouse,n = wc_one_wc_score_wc_check_houses_3(t,aOwc,nStar,nHouseId)

    # 修件的星曜（此時為宮位）本身所在的宮位 ID
    a = []
    n = t[0][1]
    a.push(n)
    a = a & aHouse
    
    return a.length >= 1
  end
  def wc_one_wc_score_BodyHouse(t,aOwc,nStar,nHouseId)
    b = true
    aHouse,n = wc_one_wc_score_wc_check_houses_3(t,aOwc,nStar,nHouseId)

    a = []
    # n = Earth.EarthIndex2Earth(@Obj_Star.cp_getBody())
    n = @Obj_Star.g_getBodyHouseId(@WC_PanType)
    a.push(n)
    a = a & aHouse
    
    return a.length >= 1
  end
  # def wc_one_wc_score_wc_check_houses_or(t,aOwc,nStar,nHouseId)
  #   aOwc_House = ["same_house","opposite_house","three_house","jia"]
  #   aOwc_Sihua = ["zihua_lu_quan_ke","zihua_lu","zihua_quan","zihua_ke","zihua_ji",
  #         "sheng_lu_quan_ke","sheng_lu","sheng_quan","sheng_ke","sheng_ji"]
  #   aHouse = [1,2,3,4,5,6,7,8,9,10,11,12]
  #   n = 1
  #   # 宮位狀況 同宮，對宮，三方，夾
  #   a = aOwc_House & aOwc
  #   aH = []
  #   a.each do |owc|
  #     aH = wc_one_wc_score_wc_check_house(t,owc,nStar,nHouseId,aH)
  #     aHouse = aHouse & aH
  #     n = 2 if owc == "jia"
  #   end
  #   # 用 OR
  #   # 祿權科忌所在宮位
  #   a = aOwc_Sihua & aOwc
  #   aHouse2 = []
  #   aH = []
  #   a.each do |owc|
  #     aH = wc_one_wc_score_wc_check_house(t,owc,nStar,nHouseId,aH)
  #     aHouse2 += aH
  #   end
  #   aHouse = aHouse & aHouse2
  #   return aHouse,n
  # end
  # 星類別_星曜中屬於 甲級星，乙級星，丙級星，雙星的使用這個函式
  # 同宮  對宮  三方  夾
  def wc_one_wc_score_wc_check_houses_1(t,aOwc,nStar,nHouseId)
    # 沒有設代表同宮
    return [nHouseId] if aOwc == []

    # aHouse = [1,2,3,4,5,6,7,8,9,10,11,12]
    n = 1
    aOwc_House = ["same_house","opposite_house","three_house","jia"]

    a = aOwc & aOwc_House
    return [nHouseId] if a == []
    # a = ["same_house"] if a == [] # 完全沒有設定的話，取星曜本身的宮位
    aH_House = []
    a.each do |owc|
      aH = wc_one_wc_score_wc_check_house(t,owc,nStar,nHouseId,[])
      aH_House += aH
      n = 2 if owc == "jia"
    end

    return aH_House,n
  end
  # 星類別_星曜中屬於 祿權科飛出，四化，自化飛出 的使用這個函式
  # 同宮  對宮  三方  夾 自祿、權、科  自祿   自權  自科  自忌  生祿、權、科  生祿   生權  生科  生忌
  def wc_one_wc_score_wc_check_houses_2(t,aOwc,nStar,nHouseId,aSihua)
    # 同宮  對宮  三方  夾
    aHouse,n = wc_one_wc_score_wc_check_houses_1(t,aOwc,nStar,nHouseId)

    # 四化，祿權科飛出，化忌飛出都先找出星曜宮的天干飛出的宮位
    a_Flyout_House = gTalent_FindSihuaFlyoutHouse(nHouseId,@WC_PanType,aSihua)

    # 找出需要判斷是否有四化的宮位
    aOut = a_Flyout_House & aHouse

    return aOut,1
  end
  def wc_one_wc_score_wc_check_houses_4(t,aOwc,nStar,nHouseId,aSihua,bOnlyStar)
    # 同宮  對宮  三方  夾
    aHouseId,n = wc_one_wc_score_wc_check_houses_1(t,aOwc,nStar,nHouseId)
    nEarth = @Obj_Star.g_House_GetEarth(@WC_PanType,nHouseId)

    a_Flyin_Stars = [nStar] if bOnlyStar
    a_Flyin_Stars,bUseOpp = @Obj_Star.gHouse_GetAStars(@WC_PanType,nEarth,true,false) if !bOnlyStar

    # 找出四化飛入的宮位
    aFindHouseStarSihuas = gTalent_FindSihuaFlyinHouse(@WC_PanType,aSihua,aHouseId,a_Flyin_Stars)

    return aFindHouseStarSihuas,aFindHouseStarSihuas.length
  end
  def gTalent_FindSihuaFlyinHouse(nPanType,aSihua,aHouseId,a_Flyin_Stars)
    aOut = []
    aHouseId.each do |nHouseId|
      nSky = @Obj_Star.gHouse_GetSky_ByHouseId(nPanType,nHouseId)
      aSkyFourHuaStars = @Obj_Star.gNormal_FindSkyFourHuaStar(nSky)
      aFindStarSihuas = gTalent_CheckSihuaStars(aSihua,a_Flyin_Stars,aSkyFourHuaStars)
      aOut.push([nHouseId,aFindStarSihuas]) if aFindStarSihuas != []
    end
    return aOut
  end

  # 取出四化星符合四化條件的
  def gTalent_CheckSihuaStars(aSihua,a_Flyin_Stars,aSkyFourHuaStars)
    aOut = []
    aSkyFourHuaStars.each do |aSihuaStar|
      aOut.push(aSihuaStar) if aSihua.include?(aSihuaStar[1]) && a_Flyin_Stars.include?(aSihuaStar[0])
    end
    return aOut
  end
  def wc_one_wc_score_wc_check_house_sihua(aOwc,aHouseId)
    # 自祿、權、科  自祿   自權  自科  自忌  生祿、權、科  生祿   生權  生科  生忌
    aOwc_Sihua = ["zihua_lu_quan_ke","zihua_lu","zihua_quan","zihua_ke","zihua_ji",
          "sheng_lu_quan_ke","sheng_lu","sheng_quan","sheng_ke","sheng_ji"]

    a = aOwc & aOwc_Sihua
    return aHouseId.length if a == []  # 沒有設定四化，幾個宮位滿足，就乘以幾倍

    aSelfSihua_2,aOrgSihua_2 = wc_sihua_one_wc_get_house_sihuas_2(aHouseId)

    aSelfSihua_1 = []
    aOrgSihua_1 = []
    a.each do |owc|
      aS1,aO1 = wc_one_wc_score_wc_get_sihuas(owc)
      aSelfSihua_1 = aSelfSihua_1 + aS1
      aOrgSihua_1 = aOrgSihua_1 + aO1
    end
    aSelfSihua_1.uniq!
    aOrgSihua_1.uniq!
      
    m,aSelfCount = wc_one_wc_score_wc_check_houses_sihua_count(aSelfSihua_1,aSelfSihua_2)
    n,aShengCount = wc_one_wc_score_wc_check_houses_sihua_count(aOrgSihua_1,aOrgSihua_2)
    nOut = m + n

    return nOut
  end
  def wc_one_wc_score_wc_check_houses_sihua_count(aSihua_1,aSihua_2)
    # aSihua_2 = [1,2,1,3] for example
    # aSihua_1 = [0,1,2] for example
    # n == 3
    a = Array.new(4)
    nOut = 0
    [0,1,2,3].each_with_index do |sihua,i|
      n = aSihua_2.count {|x| aSihua_1.include?(x) && sihua == x}
      a[i] = n
      nOut += n
    end
    return nOut,a
  end
  def wc_one_wc_score_wc_get_sihuas(owc)
    aSelfSihua = []
    aOrgSihua = []
    case owc
    when "zihua_lu_quan_ke"
      aSelfSihua = [0,1,2]
    when "zihua_lu"
      aSelfSihua = [0]
    when "zihua_quan"
      aSelfSihua = [1]
    when "zihua_ke"
      aSelfSihua = [2]
    when "zihua_ji"
      aSelfSihua = [3]
    when "sheng_lu_quan_ke"
      aOrgSihua = [0,1,2]
    when "sheng_lu"
      aOrgSihua = [0]
    when "sheng_quan"
      aOrgSihua = [1]
    when "sheng_ke"
      aOrgSihua = [2]
    when "sheng_ji"
      aOrgSihua = [3]
    end
    return aSelfSihua,aOrgSihua
  end
  # 星類別_星曜中屬於 宮位，身宮 的使用這個函式
  # 同宮  對宮  三方  夾 無主星
  def wc_one_wc_score_wc_check_houses_3(t,aOwc,nStar,nHouseId)
    n = 1
    aOwc_no_main_star = ["no_main_star"]

    owc = aOwc_no_main_star[0]
    if (aOwc.include?(owc)) then # 無主星
      house_id = t[0][1] # Talent::HouseIdKey
      house_id = @Obj_Star.g_getBodyHouseId(@WC_PanType) if t[0][0] == Talent::BodyHouseKey
      aHouse = wc_one_wc_score_wc_check_house(t,owc,nStar,nHouseId,[house_id])
    else
      aHouse,n = wc_one_wc_score_wc_check_houses_1(t,aOwc,nStar,nHouseId)
    end

    return aHouse,n
  end
  def wc_one_wc_score_wc_check_house(t,owc,nStar,nHouseId,aH)
    a = []
    return a if nHouseId == nil

    case owc
    when "same_house"
      a.push(nHouseId)
    when "opposite_house"
      a.push(Earth.ModifyEarth(nHouseId + 6))
    when "three_house"
      n = 4  # 原本是 2
      a.push(Earth.ModifyEarth(nHouseId + n))
      a.push(Earth.ModifyEarth(nHouseId - n))
      # aH.push(Earth.ModifyEarth(nHouseId + 6))
    when "jia"
      a.push(Earth.ModifyEarth(nHouseId + 1))
      a.push(Earth.ModifyEarth(nHouseId - 1))
    # when "zihua_lu_quan_ke"
    #   aSihua = [0,1,2]
    #   # 四化，祿權科飛出，化忌飛出都先找出星曜宮的天干飛出的宮位
    #   a = gTalent_FindSihuaFlyoutHouse(nHouseId,@WC_PanType,aSihua)
    #   # a = gTalent_FindSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
    # when "zihua_lu"
    #   aSihua = [0]
    #   a = gTalent_FindSihuaFlyoutHouse(nHouseId,@WC_PanType,aSihua)
    #   # a = gTalent_FindSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
    # when "zihua_quan"
    #   aSihua = [1]
    #   a = gTalent_FindSihuaFlyoutHouse(nHouseId,@WC_PanType,aSihua)
    #   # a = gTalent_FindSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
    # when "zihua_ke"
    #   aSihua = [2]
    #   a = gTalent_FindSihuaFlyoutHouse(nHouseId,@WC_PanType,aSihua)
    #   # a = gTalent_FindSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
    # when "zihua_ji"
    #   aSihua = [3]
    #   a = gTalent_FindSihuaFlyoutHouse(nHouseId,@WC_PanType,aSihua)
    #   # a = gTalent_FindSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
    # when "sheng_lu_quan_ke"
    #   aSihua = [0,1,2]
    #   a = gTalent_FindSihuaFlyoutHouse(nHouseId,@WC_PanType,aSihua)
    #   # a = gTalent_FindFlowSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
    # when "sheng_lu"
    #   aSihua = [0]
    #   a = gTalent_FindSihuaFlyoutHouse(nHouseId,@WC_PanType,aSihua)
    #   # a = gTalent_FindFlowSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
    # when "sheng_quan"
    #   aSihua = [1]
    #   a = gTalent_FindSihuaFlyoutHouse(nHouseId,@WC_PanType,aSihua)
    #   # a = gTalent_FindFlowSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
    # when "sheng_ke"
    #   aSihua = [2]
    #   a = gTalent_FindSihuaFlyoutHouse(nHouseId,@WC_PanType,aSihua)
    #   # a = gTalent_FindFlowSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
    # when "sheng_ji"
    #   aSihua = [3]
    #   a = gTalent_FindSihuaFlyoutHouse(nHouseId,@WC_PanType,aSihua)
    #   # a = gTalent_FindFlowSihuaHouse(t,nHouseId,@WC_PanType,aSihua)
    when "no_main_star"
      aH.each do |house_id|
        nEarth = @Obj_Star.g_House_GetEarth(@WC_PanType,house_id)
        if (@Obj_Star.gHouse_WithoutMainAStar(@WC_PanType,nEarth)) then
          a.push(house_id)
        end
      end
    end
    return a
  end
  def gTalent_GetSihuaStarValue(nSky,nFourHua)
    nSkyIndex = Sky.Sky2SkyIndex(nSky)
    nStar = @Obj_Star.getFourHuaStarValue(nSkyIndex,nFourHua)
    return nStar
  end
  # 自化  四化
  def gTalent_FindSihuaHouse(t,nHouseId,nPanType,aSihua)
    # 四化飛出
    # if (t[0][0] == Talent::SihuaFlyoutKey) then
    #   return gTalent_FindSihuaFlyoutHouse(nHouseId,nPanType,aSihua)
    # end
    # 宮內四化 Talent::SihuaKey
    a = Array.new
    (1..12).each do |nSky|
      aSihua.each do |nFourHua|
        nStar = gTalent_GetSihuaStarValue(nSky,nFourHua)
        nHouseId = @Obj_Star.gHouse_GetAStar_HouseId(nPanType,nStar)
        a.push(nHouseId)
      end
    end
    return a
  end
  def gTalent_FindFlowSihuaHouse(t,nHouseId,nPanType,aSihua)
    # 四化飛出
    if (t[0][0] == Talent::SihuaFlyoutKey) then
      return gTalent_FindSihuaFlyoutHouse(nHouseId,nPanType,aSihua)
    end
    a = Array.new
    nSkyIndex = @Obj_Star.get_FlowFourHua_SkyIndex(nPanType)
    nSky = Sky.SkyIndex2Sky(nSkyIndex)
    aSihua.each do |nFourHua|
      nStar = gTalent_GetSihuaStarValue(nSky,nFourHua)
      nHouseId2 = @Obj_Star.gHouse_GetAStar_HouseId(nPanType,nStar)
      a.push(nHouseId2)
    end
    return a
  end
  def gTalent_FindSihuaFlyoutHouse(nHouseId,nPanType,aSihua)
    nSky = @Obj_Star.gHouse_GetSky_ByHouseId(nPanType,nHouseId)
    return Array.new if nHouseId == nil
    a = gTalent_FindSkyFlyoutHouse(nSky,nPanType,aSihua)
    return a
  end
  def gTalent_FindSkyFlyoutHouse(nSky,nPanType,aSihua)
    a = Array.new
    aSihua.each do |nFourHua|
      nStar = gTalent_GetSihuaStarValue(nSky,nFourHua)
      nHouseId = @Obj_Star.gHouse_GetAStar_HouseId(nPanType,nStar)
      a.push(nHouseId)
    end
    return a
  end
  def wc_one_wc_final(b,weight)
      if (b) then
        return b,weight / 1.0
      else
        return b,100.0
      end
  end
  def wc_one_star_for_rule(hSfr)
    h = {}
    star_for_rule_type,nStar = wc_get_star_for_rule(hSfr)
    star_weight = wc_get_star_weight(hSfr)
    star_house_weight,stars_weight,weight_conditions = wc_get_star(hSfr)
    sihua_house_weight,sihuas_weight,sihua_star_weight,sihua_sign,sihua_sign_exception_house = wc_get_sihua(hSfr)
    h["star_for_rule_type"] = star_for_rule_type
    h["nStar"] = nStar
    h["star_weight"] = star_weight
    h["star_house_weight"] = star_house_weight
    h["stars_weight"] = stars_weight
    h["weight_conditions"] = weight_conditions
    h["sihua_house_weight"] = sihua_house_weight
    h["sihuas_weight"] = sihuas_weight
    h["sihua_star_weight"] = sihua_star_weight
    h["sihua_sign"] = sihua_sign
    h["sihua_sign_exception_house"] = sihua_sign_exception_house
    h["shenggong_weights"] = wc_get_shenggong_condition(hSfr)

    return h
  end
  def wc_get_star_for_rule(hSfr)
    star_for_rule = hSfr["star_for_rule"]
    return wc_parse_star_for_rule(star_for_rule)
  end
  def wc_parse_star_for_rule(star_for_rule)
    a = Talent.T_Star_Split(star_for_rule)
    t = Talent.ParseStarsByKey(a)
    return t[0][0],t[0][1]
  end
  def wc_get_star_weight(hSfr)
    star_weight = hSfr["star_weight"]
    return star_weight.to_i
  end
  def wc_get_shenggong_condition(hSfr)
    shenggong_weights = hSfr["shenggong_weight"]
    return shenggong_weights
  end
  def wc_get_star(hSfr)
    hStar = hSfr["star"]
    house_weight = wc_star_get_house_weight(hStar)
    stars_weight = wc_star_get_stars_weight(hStar)
    weight_conditions = wc_star_get_weight_conditions(hStar)
    return house_weight,stars_weight,weight_conditions
  end
  def wc_star_get_house_weight(hStar)
    house_weight = hStar["house_weight"]
    a2 = house_weight.map {|x| x.to_i}
    return a2
  end
  def wc_star_get_stars_weight(hStar)
    stars_weight = hStar["stars_weight"]
    return stars_weight.to_i
  end
  def wc_star_get_weight_conditions(hStar)
    hWc = hStar["weight_condition"]
    return hWc
  end
  def wc_star_get_weight_condition_content(hWc)
    wc_star = wc_star_wc_get_wc_star(hWc)
    wc_weight = wc_star_wc_get_wc_weight(hWc)
    weight_condition = wc_star_wc_get_weight_condition(hWc)
    return wc_star,wc_weight,weight_condition
  end
  def wc_star_wc_get_wc_star(hWc)
    wc_star = hWc["wc_star"]
    return wc_star
  end
  def wc_star_wc_get_wc_weight(hWc)
    wc_weight = hWc["wc_weight"]
    return wc_weight.to_i
  end
  def wc_star_wc_get_weight_condition(hWc)
    weight_condition = hWc["weight_condition"]
    # 沒有設定表示同宮
    weight_condition.push("same_house") if weight_condition == []
    return weight_condition
  end
  def wc_get_sihua(hCond)
    hSihua = hCond["sihua"]
    house_weight = wc_sihua_get_house_weight(hSihua)
    sihuas_weight = wc_sihua_get_sihuas_weight(hSihua)
    sihua_star_weight = wc_sihua_get_sihua_star_weight(hSihua)
    sihua_sign = wc_sihua_get_sihua_sign(hSihua)
    sihua_sign_exception_house = wc_sihua_get_sihua_sign_exception_house(hSihua)
    return house_weight,sihuas_weight,sihua_star_weight,sihua_sign,sihua_sign_exception_house
  end
  def wc_sihua_get_house_weight(hSihua)
    h = hSihua["house_weight"]
    a = []
    ["hua_lu","hua_quan","hua_ke","hua_ji"].each do |k|
      a2 = h[k].map {|x| x.to_i}
      a.push(a2)
    end
    return a
  end
  def wc_sihua_get_sihuas_weight(hSihua)
    sihuas_weight = hSihua["sihuas_weight"]
    return sihuas_weight.to_i
  end
  def wc_sihua_get_sihua_star_weight(hSihua)
    sihua_star_weight = hSihua["sihua_star_weight"]
    return sihua_star_weight
  end
  def wc_sihua_get_sihua_sign(hSihua)
    sihua_sign = hSihua["sihua_sign"]
    return sihua_sign
  end
  def wc_sihua_get_sihua_sign_exception_house(hSihua)
    sihua_sign_exception_house = hSihua["sihua_sign_exception_house"]
    a = Talent.sihua_sign_exception_house_new()
    return a if sihua_sign_exception_house == nil
    (0..3).each do |i|
      (0..3).each do |j|
        t = Talent.ParseStarByKey(sihua_sign_exception_house[i][j])
        a[i][j] = t[1]
      end
    end
    return a
  end

  def Talent.GetDescByKey(k)
    a = Talent.T_Star_Split(k)
    t = Talent.ParseStarsByKey(a)
    a2 = Array.new()
    t.each do |u|
      a2.push(u[2])
    end
    return a2.join("、")
  end
  def Talent.ParseStarsByKey(a)
    t = Array.new(a.length)

    a.each_with_index do |k,i|
      t[i] = Talent.ParseStarByKey(k)
    end
    return t
  end
  def Talent.ParseStarByKey(k)
    t,p,n,s = Talent.AStarKey(k)
    if (t) then
      return [p,n,s]
    end

    t,p,n,s = Talent.BStarKey(k)
    if (t) then
      return [p,n,s]
    end

    t,p,n,s = Talent.YearGod12Key(k)
    if (t) then
      return [p,n,s]
    end

    t,p,n,s = Talent.God12Key(k)
    if (t) then
      return [p,n,s]
    end
    t,p,n,s = Talent.Doctor12Key(k)
    if (t) then
      return [p,n,s]
    end
    t,p,n,s = Talent.YearStar12Key(k)
    if (t) then
      return [p,n,s]
    end

    t,p,n,s = Talent.Sihua3Key(k)
    if (t) then
      return [p,n,s]
    end

    t,p,n,s = Talent.SihuaFlyinKey_OnlyStar_012(k)
    if (t) then
      return [p,n,s]
    end
    t,p,n,s = Talent.SihuaFlyinKey_OnlyStar_3(k)
    if (t) then
      return [p,n,s]
    end
    t,p,n,s = Talent.SihuaFlyinKey_SameHouse_012(k)
    if (t) then
      return [p,n,s]
    end
    t,p,n,s = Talent.SihuaFlyinKey_SameHouse_3(k)
    if (t) then
      return [p,n,s]
    end

    t,p,n,s = Talent.SihuaFlyoutKey_012(k)
    if (t) then
      return [p,n,s]
    end

    t,p,n,s = Talent.SihuaFlyoutKey_3(k)
    if (t) then
      return [p,n,s]
    end

    # flyout 要比 sihua先跑，否則會辨別錯誤
    t,p,n,s = Talent.SihuaFlyoutKey(k)
    if (t) then
      return [p,n,s]
    end

    t,p,n,s = Talent.SihuaKey(k)
    if (t) then
      return [p,n,s]
    end

    t,p,n,s = Talent.HouseIdKey(k)
    if (t) then
      return [p,n,s]
    end

    t,p,n,s = Talent.BodyHouseKey(k)
    if (t) then
      return [p,n,s]
    end

    t,p,n,s = Talent.SkyKey(k)
    if (t) then
      return [p,n,s]
    end

    return [nil,0,""] 
  end
  def Talent.get_star_type_from_star_type_key(star_type_key)
    case star_type_key
    when Talent::AStarKey
        return Star::A_STAR
    when Talent::BStarKey
        return Star::B_STAR
    when Talent::Doctor12Key
        return Star::DOCTOR
    when Talent::God12Key
        return Star::GOD
    when Talent::YearStar12Key
        return Star::YEARSTAR
    when Talent::YearGod12Key
        return Star::YEARGOD
    end
    return 0
  end
  AStarKey = Star::A_STAR_
  def Talent.AStarKey(k)
    t,p,n = Talent.Parse_nStar(k,Talent::AStarKey)
    s = Star.GetAStarName(n)
    return t,p,n,s
  end
  def Talent.isAStar(k)
    return k == Talent::AStarKey
  end
  BStarKey = Star::B_STAR_
  def Talent.BStarKey(k)
    t,p,n = Talent.Parse_nStar(k,Talent::BStarKey)
    s = Star.GetBStarName(n)
    return t,p,n,s
  end
  def Talent.isBStar(k)
    return k == Talent::BStarKey
  end
  God12Key = Star::GOD_
  def Talent.God12Key(k)
    t,p,n = Talent.Parse_nStar(k,Talent::God12Key)
    s = Star.GetGodName(n)
    return t,p,n,s
  end
  def Talent.isGod12(k)
    return k == Talent::God12Key
  end
  Doctor12Key = Star::DOCTOR_
  def Talent.Doctor12Key(k)
    t,p,n = Talent.Parse_nStar(k,Talent::Doctor12Key)
    s = Star.GetDoctorName(n)
    return t,p,n,s
  end
  def Talent.isDoctor12(k)
    return k == Talent::Doctor12Key
  end
  YearStar12Key = Star::YEARSTAR_
  def Talent.YearStar12Key(k)
    t,p,n = Talent.Parse_nStar(k,Talent::YearStar12Key)
    s = Star.GetYearStarName(n)
    return t,p,n,s
  end
  def Talent.isYearStar12(k)
    return k == Talent::YearStar12Key
  end
  YearGod12Key = Star::YEARGOD_
  def Talent.YearGod12Key(k)
    t,p,n = Talent.Parse_nStar(k,Talent::YearGod12Key)
    s = Star.GetYearGodName2(n)
    return t,p,n,s
  end
  def Talent.isYearGod12(k)
    return k == Talent::YearGod12Key
  end
  Sihua3Key = Star::FOUR_HUA_
  def Talent.Sihua3Key(k)
    t,p,n = Talent.Parse_nStar(k,Talent::Sihua3Key)
    s = Star.GetFourHuaStr(n)
    return t,p,n,s
  end
  def Talent.isSihua3(k)
    return k == Talent::Sihua3Key
  end
  SihuaKey = "sihua"
  def Talent.SihuaKey(k)
    t,p,n = Talent.Parse_nStar(k,Talent::SihuaKey)
    s = Pm.GetStr("talent.L1.sihua")
    return t,p,n,s
  end
  def Talent.isSihua(k)
    return k == Talent::SihuaKey
  end
  SihuaFlyoutKey = "sihua_flyout"
  def Talent.SihuaFlyoutKey(k)
    t,p,n = Talent.Parse_nStar(k,Talent::SihuaFlyoutKey)
    s = Pm.GetStr("talent.L1.sihua_flyout")
    return t,p,n,s
  end
  SihuaFlyoutKey_012 = "sihua_flyout_012"
  def Talent.SihuaFlyoutKey_012(k)
    t,p,n = Talent.Parse_nStar(k,Talent::SihuaFlyoutKey_012)
    s = Pm.GetStr("talent.L1.sihua_flyout_012")
    return t,p,n,s
  end
  SihuaFlyoutKey_3 = "sihua_flyout_3"
  def Talent.SihuaFlyoutKey_3(k)
    t,p,n = Talent.Parse_nStar(k,Talent::SihuaFlyoutKey_3)
    s = Pm.GetStr("talent.L1.sihua_flyout_3")
    return t,p,n,s
  end
  SihuaFlyinKey_OnlyStar_012 = "sihua_flyin_onlystar_012"
  def Talent.SihuaFlyinKey_OnlyStar_012(k)
    t,p,n = Talent.Parse_nStar(k,Talent::SihuaFlyinKey_OnlyStar_012)
    s = Pm.GetStr("talent.L1.sihua_flyin_OnlyStar_012")
    return t,p,n,s
  end
  SihuaFlyinKey_OnlyStar_3 = "sihua_flyin_onlystar_3"
  def Talent.SihuaFlyinKey_OnlyStar_3(k)
    t,p,n = Talent.Parse_nStar(k,Talent::SihuaFlyinKey_OnlyStar_3)
    s = Pm.GetStr("talent.L1.sihua_flyin_OnlyStar_3")
    return t,p,n,s
  end
  SihuaFlyinKey_SameHouse_012 = "sihua_flyin_samehouse_012"
  def Talent.SihuaFlyinKey_SameHouse_012(k)
    t,p,n = Talent.Parse_nStar(k,Talent::SihuaFlyinKey_SameHouse_012)
    s = Pm.GetStr("talent.L1.sihua_flyin_SameHouse_012")
    return t,p,n,s
  end
  SihuaFlyinKey_SameHouse_3 = "sihua_flyin_samehouse_3"
  def Talent.SihuaFlyinKey_SameHouse_3(k)
    t,p,n = Talent.Parse_nStar(k,Talent::SihuaFlyinKey_SameHouse_3)
    s = Pm.GetStr("talent.L1.sihua_flyin_SameHouse_3")
    return t,p,n,s
  end
  HouseIdKey = Star::HOUSE_NAME_
  def Talent.HouseIdKey(k)
    t,p,n = Talent.Parse_nStar(k,Talent::HouseIdKey)
    s = Star.GetHouseName(n)
    return t,p,n,s
  end
  def Talent.isHouseId(k)
    return k == Talent::HouseIdKey
  end
  BodyHouseKey = Star::BODY_HOUSE
  def Talent.BodyHouseKey(k)
    t,p,n = Talent.Parse_nStar(k,Talent::BodyHouseKey)
    s = Pm.GetStr("IDS_S_BODY_HOUSE")
    return t,p,n,s
  end
  def Talent.isBodyHouse(k)
    return k == Talent::BodyHouseKey
  end
  SkyKey = Sky::SKY_KEY_PRE
  def Talent.SkyKey(k)
    t,p,n = Talent.Parse_nStar(k,Talent::SkyKey)
    s = Sky.GetName(n)
    return t,p,n,s
  end
  def Talent.Parse_nStar(k,p)
    if (k == nil) then
      return false,p,0
    end
    n = k.index(p)
    if (n == nil) then
      return false,p,0
    else
      s = k.gsub(p,"")
      return true,p,s.to_i
    end
  end
  def Talent.AStarForSelect(s="")
    h = {}
    (1..Star::A_STAR_COUNT).each do |nStar|
      h["#{s}#{Star.GetAStarName(nStar)}"] = Star.GetAStarNameKey(nStar)
    end
    return h
  end
  def Talent.BStarForSelect(par_FourHuaType,s="")
    h = {}
    (1..Star::B_STAR_COUNT).each do |nStar|
      h["#{s}#{Star.GetBStarName(nStar,par_FourHuaType)}"] = Star.GetBStarNameKey(nStar)
    end
    return h
  end
  def Talent.God12ForSelect(s="")
    h = {}
    (1..12).each do |nStar|
      h["#{s}#{Star.GetGodName(nStar)}"] = Star.GetGodNameKey(nStar)
    end
    return h
  end
  def Talent.Doctor12ForSelect(s="")
    h = {}
    (1..12).each do |nStar|
      h["#{s}#{Star.GetDoctorName(nStar)}"] = Star.GetDoctorNameKey(nStar)
    end
    return h
  end
  def Talent.YearStar12ForSelect(s="")
    h = {}
    (1..12).each do |nStar|
      h["#{s}#{Star.GetYearStarName(nStar)}"] = Star.GetYearStarNameKey(nStar)
    end
    return h
  end
  def Talent.YearGod12ForSelect(s="")
    h = {}
    (1..12).each do |nStar|
      h["#{s}#{Star.GetYearGodName2(nStar)}"] = Star.GetYearGodNameKey2(nStar,Star::PAN_SSN_NORMAL)
    end
    return h
  end
  # 「太陽、太陰」，「天魁、天鉞」，「左輔、右弼」，
  # 「文昌、文曲」，「火星、鈴星」，「地劫、天空」，
  # 「龍池、鳳閣」，「三台、八座」，
  @@ShuangXing_Jia = [["A_STAR_3","A_STAR_8"],["A_STAR_24","A_STAR_25"],["A_STAR_17","A_STAR_18"],
					  ["A_STAR_15","A_STAR_16"],["A_STAR_22","A_STAR_23"],["B_STAR_1","B_STAR_2"],
					  ["B_STAR_13","B_STAR_14"],["B_STAR_24","B_STAR_25"]]
  def Talent.ShuangXing_Jia(s="")
    h = {}
  	@@ShuangXing_Jia.each do |sxj|
      h["#{s}#{Pm.GetStr(Star.GetNameKey(sxj[0]))}、#{Pm.GetStr(Star.GetNameKey(sxj[1]))}"] = sxj.join("、")
  	end
    return h
  end
  def Talent.T_Star_Split(s)
    return [] if s == nil
  	return s.split("、")
  end
  # 「祿、權、科」
  def Talent.Sihua3ForSelect()
  	sh = ["FOUR_HUA_0","FOUR_HUA_1","FOUR_HUA_2"]
  	a = []
  	sh.each do |s|
  	  a.push(Pm.GetStr(Star.GetNameKey(s)))
  	end
    h = {}
    h["#{a.join("、")}"] = sh.join("、")

    return h
  end
  # 四化
  def Talent.get_sihua_star()
    hAStars = Hash.new
    hAStars[Pm.GetStr("talent.L1.sihua_star")] = "sihua_star"
    return hAStars     
  end
  def Talent.SihuaForSelect()
  	s = Pm.GetStr("talent.L1.sihua")
    h = {}
    h[s] = "sihua"

    return h
  end
  # 四化飛出
  def Talent.SihuaFlyoutForSelect()
    s = Pm.GetStr("talent.L1.sihua_flyout")
    h = {}
    h[s] = Talent::SihuaFlyoutKey

    return h
  end
  def Talent.SihuaFlyout_012_ForSelect()
    s = Pm.GetStr("talent.L1.sihua_flyout_012")
    h = {}
    h[s] = Talent::SihuaFlyoutKey_012

    return h
  end
  def Talent.SihuaFlyout_3_ForSelect()
    s = Pm.GetStr("talent.L1.sihua_flyout_3")
    h = {}
    h[s] = Talent::SihuaFlyoutKey_3

    return h
  end

  def Talent.SihuaFlyin_OnlyStar_012_ForSelect()
    s = Pm.GetStr("talent.L1.sihua_flyin_OnlyStar_012")
    h = {}
    h[s] = Talent::SihuaFlyinKey_OnlyStar_012

    return h
  end
  def Talent.SihuaFlyin_OnlyStar_3_ForSelect()
    s = Pm.GetStr("talent.L1.sihua_flyin_OnlyStar_3")
    h = {}
    h[s] = Talent::SihuaFlyinKey_OnlyStar_3

    return h
  end
  def Talent.SihuaFlyin_SameHouse_012_ForSelect()
    s = Pm.GetStr("talent.L1.sihua_flyin_SameHouse_012")
    h = {}
    h[s] = Talent::SihuaFlyinKey_SameHouse_012

    return h
  end
  def Talent.SihuaFlyin_SameHouse_3_ForSelect()
    s = Pm.GetStr("talent.L1.sihua_flyin_SameHouse_3")
    h = {}
    h[s] = Talent::SihuaFlyinKey_SameHouse_3

    return h
  end

  def Talent.HouseIdForSelect(s="")
    h = {}
    (1..12).each do |nHouseId|
      h["#{s}#{Star.GetHouseName(nHouseId)}"] = Star.GetHouseNameKey(nHouseId)
    end
    return h
  end
  def Talent.BodyHouseForSelect()
    h = {}

    h["#{Pm.GetStr("IDS_S_BODY_HOUSE")}"] = Star::BODY_HOUSE

    return h
  end
  def Talent.TianganForSelect()
    h = {}
    (1..10).each do |nSky|
      nSkyIndex = Sky.Sky2SkyIndex(nSky)
      h[Sky.GetName(nSky)] = Sky.GetKeyNameByIndex(nSkyIndex)
    end
    return h
  end

  def Talent.AllStarsForSelect()
  	stars_all = Talent.AStarForSelect("#{Pm.GetStr("IDS_S_T_ASTAR")}_")
  	stars_all.merge!(Talent.BStarForSelect(Star::PAR_FOURHUATYPE_DEFAULT,"#{Pm.GetStr("IDS_S_T_BSTAR")}_"))
  	stars_all.merge!(Talent.God12ForSelect("#{Pm.GetStr("IDS_S_T_CSTAR_GOD")}_"))
  	stars_all.merge!(Talent.Doctor12ForSelect("#{Pm.GetStr("IDS_S_T_CSTAR_DOCTOR")}_"))
  	stars_all.merge!(Talent.YearStar12ForSelect("#{Pm.GetStr("IDS_S_T_CSTAR_YEARSTAR")}_"))
  	stars_all.merge!(Talent.YearGod12ForSelect("#{Pm.GetStr("IDS_S_T_CSTAR_YEARGOD")}_"))
  	# stars_all.merge!(Talent.ShuangXing_Jia())
  	# stars_all.merge!(Talent.Sihua3ForSelect())
  	stars_all.merge!(Talent.HouseIdForSelect())
    stars_all.merge!(Talent.BodyHouseForSelect())

  	return stars_all
  end
  def Talent.AllConditionForSelect()
  	stars_all = Talent.AStarForSelect("#{Pm.GetStr("IDS_S_T_ASTAR")}_")
  	# stars_all.merge!(Talent.AStarForSelect_flowpan())
  	stars_all.merge!(Talent.BStarForSelect(Star::PAR_FOURHUATYPE_DEFAULT,"#{Pm.GetStr("IDS_S_T_BSTAR")}_"))
  	stars_all.merge!(Talent.God12ForSelect("#{Pm.GetStr("IDS_S_T_CSTAR_GOD")}_"))
  	stars_all.merge!(Talent.Doctor12ForSelect("#{Pm.GetStr("IDS_S_T_CSTAR_DOCTOR")}_"))
  	stars_all.merge!(Talent.YearStar12ForSelect("#{Pm.GetStr("IDS_S_T_CSTAR_YEARSTAR")}_"))
  	stars_all.merge!(Talent.YearGod12ForSelect("#{Pm.GetStr("IDS_S_T_CSTAR_YEARGOD")}_"))
  	stars_all.merge!(Talent.ShuangXing_Jia())
  	stars_all.merge!(Talent.Sihua3ForSelect())
    stars_all.merge!(Talent.SihuaForSelect())
    # stars_all.merge!(Talent.SihuaFlyoutForSelect())
    stars_all.merge!(Talent.SihuaFlyout_012_ForSelect())
  	stars_all.merge!(Talent.SihuaFlyout_3_ForSelect())
    stars_all.merge!(Talent.SihuaFlyin_OnlyStar_012_ForSelect())
    stars_all.merge!(Talent.SihuaFlyin_OnlyStar_3_ForSelect())
    stars_all.merge!(Talent.SihuaFlyin_SameHouse_012_ForSelect())
    stars_all.merge!(Talent.SihuaFlyin_SameHouse_3_ForSelect())
    stars_all.merge!(Talent.HouseIdForSelect())
  	stars_all.merge!(Talent.BodyHouseForSelect())

  	return stars_all
  end
  def Talent.AllSihuaStarsForSelect()
    stars_all = Talent.getFourHuaAllStars()
    # stars_all.merge!(Talent.get_sihua_star())
    stars_all.merge!(Talent.SihuaForSelect())
    # stars_all.merge!(Talent.SihuaFlyoutForSelect())
    stars_all.merge!(Talent.SihuaFlyout_012_ForSelect())
    stars_all.merge!(Talent.SihuaFlyout_3_ForSelect())
    stars_all.merge!(Talent.BodyHouseForSelect())
    stars_all.merge!(Talent.TianganForSelect())

    return stars_all
  end
  def Talent.getFourHuaAllStars()
    hAStars = Hash.new
    (Star.PrivateFourHua_FirstAStar()..Star.PrivateFourHua_LastAStar()).each do |nStar|
      hAStars[Star.GetAStarName(nStar)] = Star.GetAStarNameKey(nStar)
    end
    return hAStars
  end
  # 流昌、大祿、年祿、月祿、日祿、時祿、大羊、年羊、月羊、日羊、時羊、大陀、年陀、月陀、日陀、時陀
  def Talent.AStarForSelect_flowpan()
  	xings = ["astar_19","astar_20","astar_21"]
  	ids = ["A_STAR_19","A_STAR_20","A_STAR_21"]
  	pts = ["pt_1","pt_2","pt_3","pt_4","pt_5"]
  	h = {}
  	# 流昌
  	h["#{Pm.GetStr("IDS_S_T_ASTAR")}_#{Pm.GetStr("IDS_S_F_STAR_6")}"] = "F_STAR_6"
    # 大祿、年祿、月祿、日祿、時祿、大羊、年羊、月羊、日羊、時羊、大陀、年陀、月陀、日陀、時陀
  	xings.each_with_index do |xing,i|
  		pts.each do |pt|
  			h["#{Pm.GetStr("IDS_S_T_ASTAR")}_#{Pm.GetStr("star.xing.#{xing}.#{pt}")}"] = "#{ids[i]}__#{pt.upcase}"
  		end
  	end
  	return h
  end

  def Talent.save_raw_to_testdb(talent_key,category_id,talent,category_level,raw_exist_check=true)
    if (raw_exist_check) then
      item_id = talent["category"][category_id]["item"].keys[0]
      trait_id = talent["category"][category_id]["item"][item_id]["trait"].keys[0]
      # k = "raw_#{category_id}_#{item_id}_#{trait_id}"
      k = Talent.get_trait_scores_key(category_id,item_id,trait_id,category_level)
      h = Pm.findTestDbHashValue_key(k)
      if !(h == {} || h == nil) then
        return
      end
    end
    # 抓出所有的 raw
    # 只取 525960 筆，19600101 - 20191231
    # Talent.save_raw_to_testdb 無法使用global variable @category_level，因為程式是類別函式
    timestamp_user_1 = "1960010100f"
    timestamp_user_2 = "2019123122m"
    raw = Talent.category_raw_array(category_id,talent)
    # 儲存 timestamp_user 提供給輸出統計用
    ts_user = Array.new
    TalentScore.check_talent_key(talent_key).timestamp_user_between(timestamp_user_1,timestamp_user_2).find_each do |ts|
      score = ts.hScores[category_id]["score"]
      score.each_with_index do |ai,i|
        ai.each_with_index do |aj,j|
          raw[i][j].push(aj)
        end
      end
      ts_user.push(ts.timestamp_user)
    end
    # 存到 testdb裡面
    talent["category"][category_id]["item"].keys.each_with_index do |item_id,i|
      talent["category"][category_id]["item"][item_id]["trait"].keys.each_with_index do |trait_id,j|
        h = {}
        # k = "raw_#{category_id}_#{item_id}_#{trait_id}"
        k = Talent.get_trait_scores_key(category_id,item_id,trait_id,category_level)
        h[k] = raw[i][j]
        Pm.updateTestDbHashValue_key(k,h)
      end
    end
    Pm.updateTestDbHashValue_key("raw_#{category_level}_timestamp_user",ts_user)
  end
  def Talent.get_trait_scores_key(category_id,item_id,trait_id,category_level="L1")
    s = "_#{category_level}"
    s = "" if category_level == "L1"
    s = "" if category_level == nil
    k = "raw#{s}_#{category_id}_#{item_id}_#{trait_id}"

    return k
  end
  def Talent.category_raw_array(category_id,talent)
    # a = []
    a = Array.new(talent["category"][category_id]["item"].keys.length) {[]}
    talent["category"][category_id]["item"].keys.each_with_index do |item_id,i|
      a[i] = Array.new(talent["category"][category_id]["item"][item_id]["trait"].keys.length) {[]}
    end
    return a
  end
  def Talent.get_raw_from_testdb(talent_key,category_id,talent,category_level="L1")
    raw = Talent.category_raw_array(category_id,talent)
    talent["category"][category_id]["item"].keys.each_with_index do |item_id,i|
      talent["category"][category_id]["item"][item_id]["trait"].keys.each_with_index do |trait_id,j|
        # k = "raw_#{category_id}_#{item_id}_#{trait_id}"
        k = Talent.get_trait_scores_key(category_id,item_id,trait_id,category_level)
        h = Pm.findTestDbHashValue_key(k)
  # puts "get_raw_from_testdb : #{i} / #{j} / #{h[k].length}"
        raw[i][j] = h[k]
      end
    end
    return raw
  end
  def Talent.get_score_new_array(score,c)
    b = score.map(&:clone)
    a = b.collect {|x| x.fill(c)}
    return a.clone
  end
  def Talent.get_talent_scores(h,category_level)
    @category_level = category_level
    count = h["count"] == nil ? 10 : h["count"].to_i
    talent_key = Talent.get_talent_score_key(@category_level)
    # talent_scores = TalentScore.check_talent_key(talent_key).order("timestamp_user DESC").limit(count)
    talent_scores = TalentScore.check_talent_key(talent_key).order("id DESC").limit(count)
    return talent_scores
  end
  def get_score_statistic(category_id,talent,raw_exist_check,category_level)
    category_ids = talent["category"].keys if category_id == nil || category_id == ""
    category_ids = [category_id] if category_ids == nil
    final = {}
    category_ids.each do |c_id|
      final1 = get_score_statistic2(c_id,talent,raw_exist_check,category_level)
      final.merge!(final1) { |key, v1, v2| v2 }
    end
    return final
  end
  def get_score_statistic2(category_id,talent,raw_exist_check,category_level)
    @category_level = category_level

    talent_key = Talent.get_talent_score_key(category_level)
    max = Talent.category_raw_array(category_id,talent)
    min = Talent.category_raw_array(category_id,talent)
    sum = Talent.category_raw_array(category_id,talent)
    avg = Talent.category_raw_array(category_id,talent)
    mid = Talent.category_raw_array(category_id,talent)
    mid_25 = Talent.category_raw_array(category_id,talent)
    mid_75 = Talent.category_raw_array(category_id,talent)
    raw = nil
    pr = Talent.category_raw_array(category_id,talent)

    Talent.save_raw_to_testdb(talent_key,category_id,talent,category_level,raw_exist_check)

    # final = Talent.get_category_statistic(category_id,@category_level)
    final = wc_get_category_statistic(category_id,category_level)
    if (!raw_exist_check || final == {} || final == nil) then
      final = {}
      raw = Talent.get_raw_from_testdb(talent_key,category_id,talent,category_level)
      count = raw[0][0].length
      raw.each_with_index do |ai,i|
        ai.each_with_index do |aj,j|
          max[i][j] = aj.max
          min[i][j] = aj.min
          sum[i][j] = aj.sum
          avg[i][j] = (sum[i][j] / count).round(2)
          a = aj.sort
          if (a.length / 2 * 2 == a.length) then
            n1,n2 = a[aj.length / 2 - 1],a[aj.length / 2]
          else
            n1 = a[aj.length / 2]
            n2 = n1
          end
          n1 = 0.0 if n1 == nil
          n2 = 0.0 if n2 == nil
          mid[i][j] = ((n1 + n2) / 2).round(2)
  
          n1 = a[aj.length / 4]
          n1 = 0.0 if n1 == nil
          mid_25[i][j] = n1.round(2)
  
          n1 = a[aj.length / 4 * 3]
          n1 = 0.0 if n1 == nil
          mid_75[i][j] = n1.round(2)
  
          pr[i][j] = Talent.cal_pr_value(aj)
        end
      end
  
      final[category_id] = {}
      final[category_id]["max"] = max
      final[category_id]["min"] = min
      final[category_id]["avg"] = avg
      final[category_id]["mid_25"] = mid_25
      final[category_id]["mid"] = mid
      final[category_id]["mid_75"] = mid_75
      final[category_id]["pr"] = pr
      Talent.save_category_statistic(category_id,final,category_level)
  
      # 不存raw的所有值了，改為每次從 raw_category_n_item_n_trait_n 裡面取出
      # final[category_id]["raw"] = raw
      # Pm.updateTestDbHashValue_key("talent_score_statistic",final)

      # 為了滅少顯示的內容，只取第一筆出來顯示
      final[category_id]["pr"] = pr[0][0]
      final[category_id]["raw"] = raw[0][0].length
    else
      raw = Talent.get_raw_from_testdb(talent_key,category_id,talent,category_level)
      # h2 = Pm.findTestDbHashValue_key("talent_score_statistic")
      pr = final[category_id]["pr"]
      # raw = h2[category_id]["raw"]
      final[category_id]["pr"] = pr[0][0]
      final[category_id]["raw"] = raw[0][0].length
    end
    return final
  end
  def get_pr_value_and_save(category_id,talent,pr_exist_check=false,timestamp_user_1=nil,timestamp_user_2=nil,category_level="L1")
    category_ids = talent["category"].keys if category_id == nil || category_id == ""
    category_ids = [category_id] if category_ids == nil
    final = {}
    category_ids.each do |c_id|
      final = get_pr_value_and_save2(c_id,talent,pr_exist_check,timestamp_user_1,timestamp_user_2,category_level)
    end
    return final
  end
  def get_pr_value_and_save2(category_id,talent,pr_exist_check=false,timestamp_user_1=nil,timestamp_user_2=nil,category_level="L1")
    @category_level = category_level

    # h = Talent.get_category_statistic(category_id,category_level)
    h = wc_get_category_statistic(category_id,category_level)
    pr = h[category_id]["pr"]
    h_score = {}

    talent_key = Talent.get_talent_score_key(category_level)
    if (timestamp_user_1 == nil) then
      TalentScore.check_talent_key(talent_key).find_each do |ts|
        h_score = ts.hScores
        if (!pr_exist_check || h_score[category_id]["pr"] == nil) then
          score = h_score[category_id]["score"]
          pr_score = Talent.get_score_new_array(score.map(&:clone),0)
          score.each_with_index do |ai,i|
            ai.each_with_index do |aj,j|
              pr_score[i][j] = pr[i][j]["#{aj}"]
            end
          end
          h_score[category_id]["pr"] = pr_score
          ts.hScores = h_score
          ts.save!
        end
      end
    else
      TalentScore.check_talent_key(talent_key).timestamp_user_between(timestamp_user_1,timestamp_user_2).find_each do |ts|
        h_score = ts.hScores
        if (!pr_exist_check || h_score[category_id]["pr"] == nil) then
          score = h_score[category_id]["score"]
          pr_score = Talent.get_score_new_array(score.map(&:clone),0)
          score.each_with_index do |ai,i|
            ai.each_with_index do |aj,j|
              pr_score[i][j] = pr[i][j]["#{aj}"]
            end
          end
          h_score[category_id]["pr"] = pr_score
          ts.hScores = h_score
          ts.save!
        end
      end
    end
    final = {}
    final[category_id] = {}
    final[category_id]["max"] = pr[0][0]

    return final
  end
end

# 2021/1/8 modified
# 1, 條件改為 25 跟 sihua 8 個 ok
# 2， 四化 ，祿權科飛出，化忌飛出 符合者倍數 OK
# 3，若原始分數為0 ，有權重，分數改成 70 , -70 OK
