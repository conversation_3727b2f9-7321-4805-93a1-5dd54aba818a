class Xdate
# 檔名要大寫開頭

# 真太陽時
# 作為時空影響人體的自然變化，應以當地時間為準。
# 正確的時辰，是太陽相對於地球此處的視角，即天文學的真太陽時，不能簡單地沿用當地時間。真太陽時＝平太陽時＋真平太陽時差。
# 1884年國際會議制定劃分時區的方法，規定每隔經度15˚算一個時區，全球分24個時區，把通過英國倫敦格林威治天文台原址那條經線定為0˚經線，作為0˚中央經線，從西經7.5˚至東經7.5˚為中時區，向東劃分12個時區，向西劃分12個時區。地理經度和時間有特定的關係。因地球每24小時自轉1周（360˚），則每小時自轉360˚÷24=15˚，每經度1˚時刻差為60÷15=4分鐘，此為地區時差計算的基礎。
# 簡單算法是：真太陽時＝平太陽時－15分鐘。因為推後15分鐘（一年之中的2月10日是推後最多的一天，-14'15"），即解決了繁雜的推算問題，保證不出錯（或可多按幾次，總有一次是對的，一天之中只要有一次有效即可）。你所在地的經緯度，可用搜索引擎查找，如google，在對話方塊輸入“ｘｘ城市+經緯度”。
# 步驟：①將當地時間推算成當地平太陽時，②將平太陽時換算成當地真太陽時，③對照五鼠遁日起時表查時干。
# 以中國為例
# 中國當地的平太陽時＝北京時間＋４分鐘ｘ（當地經度－120）
# 中國按當地之經度推算平太陽時，以東經120度為基準，每減少1度則減4分鐘，每增加1度則加4分鐘。如某地位於東經105度，105-120=-15度，-15度×4分鐘=-60分鐘，則當地的平太陽時是用北京時間減去60分鐘；又如某地位於東經130度，130-120=10度，10度×4分鐘=40分鐘，則當地的平太陽時用北京時間加上40分鐘，餘類推。如不能確定某地的經度，粗略的辦法是根據下表找最鄰近城市的時差值。
# 中國各主要城市之平太陽時與北京時間對照表：
# 地區	經度	時差	　	地區	經度	時差	　	地區	經度	時差	　	地區	經度	時差
# 臺北	121˚31'	+6'04"	　	呼和浩特	111˚38'	-33'28"	包頭	110˚00'	-40'00"	　	海拉爾	119˚43'	-1'08"
# 太原	112˚33'	-29'48"	　	臨汾	111˚31'	-33'56"	　	大同	113˚13'	-27'08"	　	長治	113˚13'	-27'08"
# 石家莊 114˚26'	-22'16"	唐山	118˚09'	-7'24"	　	秦皇島	119˚37'	-1'32"	承德	117˚52'	-8'32"
# 保定	115˚28'	-18'08"	　	張家口	114˚55'	-20'20"	北京	116˚28'	-14'08"	　	瀋陽	123˚23'	+13'32"
# 鞍山	123˚00'	+12'00"	　	錦州	123˚09'	+12'36"	　	大連	121˚38'	+6'32"	　	長春	125˚18'	+21'12"
# 吉林	126˚36'	+26'24"	　	哈爾濱	126˚38'	+26'32"	牡丹江	129˚36'	+38'24"	齊齊哈爾	123˚55'	+15'40"
# 上海	121˚26'	+5'44"	　	南京	118˚46'	-4'56"	　	無錫	120˚18'	+1'12"	　	蘇州	120˚39'	+2'36"
# 徐州	117˚12'	-11'12"	　	合肥	117˚16'	-10'56"	　	蕪湖	118˚20'	-6'40"	　	安慶	117˚02'	-11'52"
# 濟南	117˚02'	-11'52"	　	煙臺	121˚22'	+5'28"	　	青島	120˚19'	+1'16"	　	天津	117˚10'	-11'20"
# 杭州	120˚10'	+0'40"	　	紹興	120˚40'	+2'40"	　	寧波	121˚34'	+6'16"	　	金華	119˚49'	+0'44"
# 溫州	120˚38'	+2'32"	　	南昌	115˚53'	-16'28"	　	九江	115˚59'	-16'04"	　	贛州	114˚56'	-20'16"
# 福州	119˚19'	-2'44"	　	廈門	118˚04'	-7'44"	　	泉州	118˚37'	-5'32"	　	長沙	112˚55'	-28'20"
# 湘潭	112˚51'	-28'36"	　	常德	111˚39'	-33'24"	　	衡陽	112˚34'	-29'44"	　	武漢	114˚20'	-22'40"
# 監利	112˚53'	-28'28"	　	沙市	112˚17'	-30'52"	　	宜昌	111˚15'	-35'00"	　	鄭州	113˚42'	-25'12"
# 新鄉	113˚54'	-24'24"	　	許昌	113˚48'	-24'48"	　	洛陽	112˚26'	-30'16"	　	開封	114˚23'	-22'28"
# 廣州	113˚18'	-26'48"	　	珠海	113˚18'	-26'48"	　	澳門	113˚18'	-26'48"	　	韶關	113˚33'	-25'48"
# 汕頭	116˚40'	-13'20"	　	深圳	113˚33'	-25'48"	　	香港	113˚33'	-25'48"	　	海口	110˚19'	-38'44"
# 南寧	108˚21'	-46'36"	　	桂林	110˚10'	-39'20"	　	梧州	111˚18'	-34'48"	　	柳州	109˚19'	-42'44"
# 貴陽	106˚43'	-53'08"	　	遵義	106˚53'	-52'28"	　	成都	104˚10'	-63'06"	　	重慶	106˚33'	-53'48"
# 萬縣	108˚22'	-46'32"	　	內江	105˚03'	-59'48"	　	瀘州	105˚27'	-58'12"	　	昆明	102˚42'	-69'12"
# 西安	108˚55'	-44'20"	　	延安	109˚26'	-42'16"	　	寶雞	107˚09'	-51'24"	　	蘭州	103˚50'	-64'40"
# 天水	105˚33'	-57'48"	　	烏魯木齊	87˚36'	-129'36"銀川	106˚13'	-55'08"	　	西寧	101˚49'	-72'44"
# 酒泉	98˚30'	-86'00"	　	克拉瑪依	84˚51'	-140'36"哈密	93˚29'	-106'04"	和田	79˚55'	-160'20"
# 拉薩	91˚02'	-115'52"	達孜	91˚23'	-114'28"	林芝	94˚28'	-102'08"	曲水	90˚44'	-117'04"
# 貢嘎	90˚58'	-116'08"	林周	91˚17'	-114'52"	那曲	92˚03'	-111'48"	　	　	　	　
# 注：根據本表推算出來的時間乃是平太陽時，還須進一步推算出真太陽時才能論定時辰。
# 以上根據某地經度推算出來的當地時間稱為平太陽時，還不是真正的太陽時，因為地球繞日運行的軌道是橢圓的，
# 所以地球相對於太陽的自轉週期並不十分均勻，一年之內，有的時期快些，有的時期慢些，平均起來，
# 折合一天24小時，稱為平太陽時，一年365天，每一天的平太陽時與真太陽時或多或少都有些差異，未可忽視，
# 下表將真平兩種時間的差值列出，以供換算之需。
# 真平太陽時差（用平時加減差值得真時）
@@Real_sun_time_by_date = {
"01/01" => "-3'09","01/02" => "-3'38","01/03" => "-4'06","01/04" => "-4'33","01/05" => "-5'01","01/06" => "-5'27","01/07" => "-5'54","01/08" => "-6'20","01/09" => "-6'45","01/10" => "-7'10","01/11" => "-7'35","01/12" => "-7'59","01/13" => "-8'22","01/14" => "-8'45","01/15" => "-9'07","01/16" => "-9'28","01/17" => "-9'49","01/18" => "-10'09","01/19" => "-10'28","01/20" => "-10'47","01/21" => "-11'05","01/22" => "-11'22","01/23" => "-11'38","01/24" => "-11'54","01/25" => "-12'08","01/26" => "-12'22","01/27" => "-12'35","01/28" => "-12'59","01/29" => "-13'10","01/30" => "-13'19","01/31" => "-13'37",
"02/01" => "-13'44","02/02" => "-13'50","02/03" => "-13'56","02/04" => "-14'01","02/05" => "-14'05","02/06" => "-14'09","02/07" => "-14'11","02/08" => "-14'13","02/09" => "-14'14","02/10" => "-14'15","02/11" => "-14'14","02/12" => "-14'13","02/13" => "-14'11","02/14" => "-14'08","02/15" => "-14'05","02/16" => "-14'01","02/17" => "-13'56","02/18" => "-13'51","02/19" => "-13'44","02/20" => "-13'38","02/21" => "-13'30","02/22" => "-13'22","02/23" => "-13'13","02/24" => "-11'04","02/25" => "-12'54","02/26" => "-12'43","02/27" => "-12'32","02/28" => "-12'21","02/29" => "-12'08",
"03/01" => "-11'56","03/02" => "-11'43","03/03" => "-11'29","03/04" => "-11'15","03/05" => "-11'01","03/06" => "-10'47","03/07" => "-10'32","03/08" => "-10'16","03/09" => "-10'01","03/10" => "-9'45","03/11" => "-9'28","03/12" => "-9'12","03/13" => "-8'55","03/14" => "-8'38","03/15" => "-8'21","03/16" => "-8'04","03/17" => "-7'46","03/18" => "-7'29","03/19" => "-7'11","03/20" => "-6'53","03/21" => "-6'35","03/22" => "-6'17","03/23" => "-5'58","03/24" => "-5'40","03/25" => "-5'22","03/26" => "-5'04","03/27" => "-4'45","03/28" => "-4'27","03/29" => "-4'09","03/30" => "-3'51","03/31" => "-3'33",
"04/01" => "-3'16","04/02" => "-2'58","04/03" => "-2'41","04/04" => "-2'24","04/05" => "-2'07","04/06" => "-1'50","04/07" => "-1'33","04/08" => "-1'17","04/09" => "-1'01","04/10" => "+0'46","04/11" => "+0'30","04/12" => "+0'16","04/13" => "+0'01","04/14" => "+0'13","04/15" => "+0'27","04/16" => "+0'41","04/17" => "+0'54","04/18" => "+1'06","04/19" => "+1'19","04/20" => "+1'31","04/21" => "+1'42","04/22" => "+1'53","04/23" => "+2'04","04/24" => "+2'14","04/25" => "+2'23","04/26" => "+2'33","04/27" => "+2'41","04/28" => "+2'49","04/29" => "+2'57","04/30" => "+3'04",
"05/01" => "+1'10","05/02" => "+3'16","05/03" => "+3'21","05/04" => "+3'26","05/05" => "+3'30","05/06" => "+3'37","05/07" => "+3'36","05/08" => "+3'39","05/09" => "+3'40","05/10" => "+3'42","05/11" => "+3'42","05/12" => "+3'42","05/13" => "+3'42","05/14" => "+3'41","05/15" => "+3'39","05/16" => "+3'37","05/17" => "+3'34","05/18" => "+3'31","05/19" => "+3'27","05/20" => "+3'23","05/21" => "+3'18","05/22" => "+3'13","05/23" => "+3'07","05/24" => "+3'01","05/25" => "+2'54","05/26" => "+2'47","05/27" => "+2'39","05/28" => "+2'31","05/29" => "+2'22","05/30" => "+2'13","05/31" => "+2'04",
"06/01" => "+1'54","06/02" => "+1'44","06/03" => "+1'34","06/04" => "+1'23","06/05" => "+1'12","06/06" => "+1'00","06/07" => "+0'48","06/08" => "+0'36","06/09" => "+0'24","06/10" => "+0'12","06/11" => "+0'01","06/12" => "+0'14","06/13" => "+0'39","06/14" => "+0'52","06/15" => "-1'05","06/16" => "-1'18","06/17" => "-1'31","06/18" => "-1'45","06/19" => "-1'57","06/20" => "-2'10","06/21" => "-2'23","06/22" => "-2'36","06/23" => "-2'48","06/24" => "-3'01","06/25" => "-3'13","06/26" => "-3'25","06/27" => "-3'37","06/28" => "-3'49","06/29" => "-4'00","06/30" => "-4'11",
"07/01" => "-4'22","07/02" => "-4'33","07/03" => "-4'43","07/04" => "-4'53","07/05" => "-5'02","07/06" => "-5'11","07/07" => "-5'20","07/08" => "-5'28","07/09" => "-5'36","07/10" => "-5'43","07/11" => "-5'50","07/12" => "-5'56","07/13" => "-6'02","07/14" => "-6'08","07/15" => "-6'12","07/16" => "-6'16","07/17" => "-6'20","07/18" => "-6'23","07/19" => "-6'25","07/20" => "-6'27","07/21" => "-6'29","07/22" => "-6'29","07/23" => "-6'29","07/24" => "-6'29","07/25" => "-6'28","07/26" => "-6'26","07/27" => "-6'24","07/28" => "-6'21","07/29" => "-6'17","07/30" => "-6'13","07/31" => "-6'08",
"08/01" => "-6'03","08/02" => "-5'57","08/03" => "-5'51","08/04" => "-5'44","08/05" => "-5'36","08/06" => "-5'28","08/07" => "-5'19","08/08" => "-5'10","08/09" => "-5'00","08/10" => "-4'50","08/11" => "-4'39","08/12" => "-4'27","08/13" => "-4'15","08/14" => "-4'02","08/15" => "-3'49","08/16" => "-3'36","08/17" => "-3'21","08/18" => "-3'07","08/19" => "-2'51","08/20" => "-2'36","08/21" => "-2'20","08/22" => "-2'03","08/23" => "-1'47","08/24" => "-1'29","08/25" => "-1'12","08/26" => "+0'54","08/27" => "+0'35","08/28" => "+0'17","08/29" => "+0'02","08/30" => "+0'21","08/31" => "+0'41",
"09/01" => "+1'00","09/02" => "+1'20","09/03" => "+1'40","09/04" => "+2'01","09/05" => "+2'21","09/06" => "+2'42","09/07" => "+3'03","09/08" => "+3'03","09/09" => "+3'24","09/10" => "+3'45","09/11" => "+4'06","09/12" => "+4'27","09/13" => "+4'48","09/14" => "+5'10","09/15" => "+5'31","09/16" => "+5'53","09/17" => "+6'14","09/18" => "+6'35","09/19" => "+6'57","09/20" => "+7'18","09/21" => "+7'39","09/22" => "+8'00","09/23" => "+8'21","09/24" => "+8'42","09/25" => "+9'02","09/26" => "+9'22","09/27" => "+9'42","09/28" => "+10'02","09/29" => "+10'21","09/30" => "+10'40",
"10/01" => "+10'59","10/02" => "+11'18","10/03" => "+11'36","10/04" => "+11'36","10/05" => "+11'53","10/06" => "+12'11","10/07" => "+12'28","10/08" => "+12'44","10/09" => "+12'60","10/10" => "+13'16","10/11" => "+13'16","10/12" => "+13'31","10/13" => "+13'45","10/14" => "+13'59","10/15" => "+14'13","10/16" => "+14'26","10/17" => "+14'38","10/18" => "+14'50","10/19" => "+15'01","10/20" => "+15'12","10/21" => "+15'21","10/22" => "+15'31","10/23" => "+15'40","10/24" => "+15'48","10/25" => "+15'55","10/26" => "+16'01","10/27" => "+16'07","10/28" => "+16'12","10/29" => "+16'16","10/30" => "+16'20","10/31" => "+16'22",
"11/01" => "+16'24","11/02" => "+16'25","11/03" => "+16'25","11/04" => "+16'24","11/05" => "+16'23","11/06" => "+16'21","11/07" => "+16'17","11/08" => "+16'13","11/09" => "+16'09","11/10" => "+16'03","11/11" => "+15'56","11/12" => "+15'49","11/13" => "+15'41","11/14" => "+15'32","11/15" => "+15'22","11/16" => "+15'11","11/17" => "+14'60","11/18" => "+14'47","11/19" => "+14'34","11/20" => "+14'20","11/21" => "+14'06","11/22" => "+13'50","11/23" => "+13'34","11/24" => "+13'17","11/25" => "+12'59","11/26" => "+12'40","11/27" => "+12'21","11/28" => "+12'01","11/29" => "+11'40","11/30" => "+11'18",
"12/01" => "+10'56","12/02" => "+10'33","12/03" => "+10'09","12/04" => "+9'45","12/05" => "+9'21","12/06" => "+8'55","12/07" => "+8'29","12/08" => "+8'03","12/09" => "+7'36","12/10" => "+7'09","12/11" => "+6'42","12/12" => "+6'14","12/13" => "+5'46","12/14" => "+5'17","12/15" => "+4'48","12/16" => "+4'19","12/17" => "+3'50","12/18" => "+3'21","12/19" => "+2'51","12/20" => "+2'22","12/21" => "+1'52","12/22" => "+1'22","12/23" => "+0'52","12/24" => "+0'23","12/25" => "+0'07","12/26" => "+0'37","12/27" => "-1'06","12/28" => "-1'36","12/29" => "-2'05","12/30" => "-2'34","12/31" => "-3'03"
}
# 注：1.如某年2月25日，某地平太陽時為7:05'，查表得2月25日時差為-12'54"，即7:05'-12'54"，那麼此刻的真太陽時即為6:52'06"
# 　    2. 由於西曆年與回歸年和閏年與平年的差異，實際的時差可能是前一日或後一日的資料，其誤差可忽略不計
# 　    3. 用於中醫時，要求不高，一律後推15分鐘即可，免除了計算
# 步驟：①將當地時間推算成當地平太陽時，②將平太陽時換算成當地真太陽時
# local time:nWYear, nWMonth,nWDate, nWHour,nWMin
# longitude:經度
# latitude:緯度
# 輸出 平太陽時
# sample :
# 重慶 Xdate.api_rst_localtime_to_localsuntime(2020,3,5,0,35,106.97357,29.63438) [2020, 3, 4, 23, 31]
# 台北 Xdate.api_rst_localtime_to_localsuntime(2020,3,5,0,25,121.52184,25.03459) [2020, 3, 5, 0, 20]
  def Xdate.api_rst_localtime_to_localsuntime(nWYear, nWMonth,nWDate, nWHour,nWMin,longitude,latitude)
  	nWsYear,nWsMonth,nWsDate,nWsHour,nWsMin = nWYear, nWMonth,nWDate,nWHour,nWMin
    if (longitude == nil || latitude == nil) then
      return nWsYear,nWsMonth,nWsDate,nWsHour,nWsMin
    end
  	# ①將當地時間推算成當地平太陽時
  	if Xdate.api_rst_IsInsideChina(latitude,longitude) then
  		nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin = Xdate.api_rst_localtime_to_localsuntime_inChina(nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin,longitude,latitude)
  	else
  		nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin = Xdate.api_rst_localtime_to_localsuntime_outChina(nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin,longitude,latitude)
  	end

  	# ②將平太陽時換算成當地真太陽時
  	nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin = Xdate.api_rst_localsuntime_to_realsuntime(nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin)

  	return nWsYear,nWsMonth,nWsDate,nWsHour,nWsMin
  end
  # ①將中國當地時間推算成當地平太陽時
  # 中國當地的平太陽時＝北京時間＋４分鐘ｘ（當地經度－120）
  # 北京時間，又名中國標準時間，是中國大陸的標準時間，比世界協調時快八小時（即UTC+8），
  # 與香港、澳門、台北、吉隆坡、新加坡等地的標準時間相同。 北京時間並不是北京市的地方平太陽時間（東經116°23'），
  # 而是東經120°的地方平太陽時間，二者相差約14分鐘28秒。
  # 112.5-127.5 北京時區 以120為標準
  def Xdate.api_rst_localtime_to_localsuntime_inChina(nWYear, nWMonth,nWDate, nWHour,nWMin,longitude,latitude)
    if (longitude == nil || latitude == nil) then
      return nWYear, nWMonth,nWDate,nWHour,nWMin
    end
  	nStandLongitude = 120
  	longitude_diff = longitude - nStandLongitude

  	nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin = Xdate.api_rst_localtime_to_localsuntime_byLongitudeDiff(nWYear, nWMonth,nWDate, nWHour,nWMin,longitude_diff)
  	# 平太陽時＝ 當地時間＋４分鐘ｘ（當地經度－120）

  	return nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin
  end
  # ①將中國以外的當地時間推算成當地平太陽時
  # 全球以英國格林威治天文台為標準劃分。全球劃為24時區，每個時區範圍為15個經度，即相差15度時差1小時。
  # 以經過格林威治天文台的零度經線為標準，從西經7.5度到東經7.5度為中區，稱為0時區。東西各有12個時區。
  # 北京位於東經116.19度，劃在東八區。該區的中央經線為東經120度，因此北京時間是以東經120度的地方時作為標準時間，
  # 也叫北京時間。北京地方實際時間比北京時間晚14分44秒。因為東經度每差一度的時差是4分鐘，
  # 因此北京地方時的平太陽時的計算公式是
  # 原文網址：https://kknews.cc/news/l6zj2r9.html	
  def Xdate.api_rst_localtime_to_localsuntime_outChina(nWYear, nWMonth,nWDate, nWHour,nWMin,longitude,latitude)
    if (longitude == nil || latitude == nil) then
      return nWYear, nWMonth,nWDate,nWHour,nWMin
    end
  	# 計算該經度時區的標準經度 127.5 -> 120; 112.5 => 105
  	nStandLongitude = ((longitude + 7.4) / 15).floor * 15
  	longitude_diff = longitude - nStandLongitude
  	nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin = Xdate.api_rst_localtime_to_localsuntime_byLongitudeDiff(nWYear, nWMonth,nWDate, nWHour,nWMin,longitude_diff)

  	return nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin
  end
  def Xdate.api_rst_localtime_to_localsuntime_byLongitudeDiff(nWYear, nWMonth,nWDate, nWHour,nWMin,longitude_diff)
  	# 平太陽時＝ 當地時間＋４分鐘ｘ 當地經度與該時區標準經度之差
  	nMinDiff = (4 * longitude_diff).floor
  	nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin = Xdate.NextNMinute(nWYear, nWMonth,nWDate, nWHour,nWMin,nMinDiff)

  	return nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin
  end

  # ②將平太陽時換算成當地真太陽時
  def Xdate.api_rst_localsuntime_to_realsuntime(nWYear, nWMonth,nWDate, nWHour,nWMin)
  	s = "%02d/%02d" % [ nWMonth, nWDate]
  	diff = @@Real_sun_time_by_date[s]
  	nDMin = Xdate.api_rst_parse_difftime(diff)
  	nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin = Xdate.NextNMinute(nWYear, nWMonth,nWDate, nWHour,nWMin,nDMin)

  	return nWsYear, nWsMonth,nWsDate, nWsHour,nWsMin
  end
  def Xdate.api_rst_parse_difftime(diff)
  	nDMin = 0
  	if (diff != nil) then
  		a = diff.split("'")
  		nDMin = a[0].to_i
  		sec = a[1].to_i
  		if (sec >= 30) then
  			if nDMin >=0 then
  				nDMin += 1
  			else
  				nDMin -= 1
  			end
  		end
  	end
  	return nDMin
  end
# 實行夏令時期間，其實際時間應為當時的時間減去１小時，即：將調快的1小時減回來。
# 例如：夏令時西曆1991年5月2日0點10分，實際北京時間為：1991年5月1日23點10分（已經將夏令時換算為正常時間）
# ③五鼠遁日起時表（以時地支對照日幹查時干）
# 時間	日上起時
# 1甲  6己日	2乙  7庚日	3丙  8辛日	4丁  9壬日	5戊  10癸日
# 半夜 11=>00∼ 1=>00	甲子	丙子	戊子	庚子	壬子
# 凌晨  1=>00∼ 3=>00	乙丑	丁丑	己丑	辛丑	癸丑
# 　　  3=>00∼ 5=>00	丙寅	戊寅	庚寅	壬寅	甲寅
# 　　  5=>00∼ 7=>00	丁卯	己卯	辛卯	癸卯	乙卯
# 　　  7=>00∼ 9=>00	戊辰	庚辰	壬辰	甲辰	丙辰
# 　　  9=>00∼11=>00	己巳	辛巳	癸巳	乙巳	丁巳
# 　　 11=>00∼ 1=>00	庚午	壬午	甲午	丙午	戊午
# 下午  1=>00∼ 3=>00	辛未	癸未	乙未	丁未	己未
# 　　  3=>00∼ 5=>00	壬申	甲申	丙申	戊申	庚申
# 　　  5=>00∼ 7=>00	癸酉	乙酉	丁酉	己酉	辛酉
# 　　  7=>00∼ 9=>00	甲戌	丙戌	戊戌	庚戌	壬戌
# 　　  9=>00∼11=>00	乙亥	丁亥	己亥	辛亥	癸亥
# 注：表中所列時間以真太陽時為准
# 例：北京地區 1974年2月6日上午5時整：北京地區上午5時為平太陽時4=>45'52''，查表得知2月6日的真平時差為-14'09"，用平太陽時減14'09"，得到真太陽時為4=>31'43''，定為寅時，查表得到時干支為甲寅。
  # 判斷經緯度有沒有在中國領地內
# http://www.zrwulian.com/nd.jsp?id=53
# 基本思路是：把整个行政区域划分为几个小的矩形，然后再排除掉一些矩形区域。只要一个点在限定的区域内，并且不在排除的区域内，则判定成功，否则失败。下图中蓝色区域为限定区域，红色区域为排除区域。这种快速判定的方法是从Nokia map中挖掘出来的。
# >> 函数功能：判断经纬度是否在中国
# >> 函数名称：IsInsideChina
# >> 输入参数： dbLatitude 查询纬度 dbLongitude 查询经度
# >> 返回值：true 在中国，false不在中国
# [latitude1,longitude1,latitude2,longitude2] 矩形
  @@InChina = [[49.220400,79.446200,42.889900,96.330000],
    [54.141500,109.687200,39.374200,135.000200],
	[42.889900,73.124600,29.529700,124.143255],
	[29.529700,82.968400,26.718600,97.035200],
	[29.529700,97.025300,20.414096,124.367395],
	[20.414096,107.975793,17.871542,111.744104]]
  @@OutChina = [[25.398623,119.921265,21.785006,122.497559],
    [22.284000,101.865200,20.098800,106.665000],
	[21.542200,106.452500,20.487800,108.051000],
	[55.817500,109.032300,50.325700,119.127000],
	[55.817500,127.456800,49.557400,137.022700],
	[44.892200,131.266200,42.569200,137.022700]]
  def Xdate.api_rst_IsInsideChina(dbLatitude,dbLongitude)
    if (dbLongitude == nil || dbLatitude == nil) then
      return false
    end
  	@@InChina.each_with_index do |in_lalo, index|
  		if (dbLatitude.between?(in_lalo[2],in_lalo[0]) && dbLongitude.between?(in_lalo[1],in_lalo[3])) then
  			@@OutChina.each_with_index do |out_lalo, index|
  				if (dbLatitude.between?(out_lalo[2],out_lalo[0]) && dbLongitude.between?(out_lalo[1],out_lalo[3])) then
  					return false
  				end
  			end
  			return true
  		end
  	end
  	return false
  end
end