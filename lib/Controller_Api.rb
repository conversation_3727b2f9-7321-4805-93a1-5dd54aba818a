# 1.紫微斗數論斷產品
#   1､紫微十年事業發展衝刺年分析
#   2､紫微十年進財年分析
#   3､紫微十年婚姻感情緣年分析
#   4､紫微流年事業發展衝刺月份分析
#   5､紫微流年進財運月份分析
#   6､紫微流年感情運月份分析
#   7､紫微流月工作好運日分析
#   8､紫微流月進財日分析
#   9､紫微流月感情最佳互動日分析
#   10､紫微流日工作好運時辰分析
#   11､紫微流日進財時辰分析
#   12､紫微流日感情最佳互動時辰分析
#   13､紫微本命總體分析（十二宮）分析
#   14､紫微十年總體分析（十二宮）分析
#   15､紫微流年總體分析（十二宮）分析
#   16､紫微流月總體分析（十二宮）分析
#   17､本命盤命宮的詳細解說
#   18､紫微斗數剖腹擇日（七天）分析
# 2.測字
# 3.姓名學
# 4.姓名學 配對
# 5.六爻卦 健康 求財 女未婚 女已婚 男未婚 男已婚 職場
# 6.農民曆
# 7.萬年曆

require("Xdate.rb")
require("WeenApi.rb")
require("Name.rb")
require("GNum.rb")
require("Pukwa.rb")
require("Flower.rb")

module Controller_Api
  # below for profate_events by Peter
  # EX_HOUSE_1 = 1      # 命宮
  # EX_HOUSE_2 = 2          # 兄弟
  # EX_HOUSE_3 = 3          # 夫妻
  # EX_HOUSE_4 = 4          # 子女
  # EX_HOUSE_5 = 5          # 財帛
  # EX_HOUSE_6 = 6          # 疾厄
  # EX_HOUSE_7 = 7          # 遷移
  # EX_HOUSE_8 = 8          # 朋友
  # EX_HOUSE_9 = 9          # 官祿
  # EX_HOUSE_10 = 10         # 田宅
  # EX_HOUSE_11 = 11         # 福德
  # EX_HOUSE_12 = 12         # 父母
  # 紫微斗數產品：（每次）
  def check_gpc_ziwei_api_legal(api_name)
    return true if api_name == "gpc_ziwei_lunduan_shinian_guanlu"
    return true if api_name == "gpc_ziwei_lunduan_shinian_caibo"
    return true if api_name == "gpc_ziwei_lunduan_shinian_fugi"
    return true if api_name == "gpc_ziwei_lunduan_liunian_guanlu"
    return true if api_name == "gpc_ziwei_lunduan_liunian_caibo"
    return true if api_name == "gpc_ziwei_lunduan_liunian_fugi"
    return true if api_name == "gpc_ziwei_lunduan_liuyue_guanlu"
    return true if api_name == "gpc_ziwei_lunduan_liuyue_caibo"
    return true if api_name == "gpc_ziwei_lunduan_liuyue_fugi"
    return true if api_name == "gpc_ziwei_lunduan_liuri_guanlu"
    return true if api_name == "gpc_ziwei_lunduan_liuri_caibo"
    return true if api_name == "gpc_ziwei_lunduan_liuri_fugi"

    return true if api_name == "gpc_ziwei_lunduan_liushi_guanlu"
    return true if api_name == "gpc_ziwei_lunduan_liushi_caibo"
    return true if api_name == "gpc_ziwei_lunduan_liushi_fugi"

    return true if api_name == "gpc_ziwei_lunduan_benming"
    return true if api_name == "gpc_ziwei_lunduan_shinian"
    return true if api_name == "gpc_ziwei_lunduan_liunian"
    return true if api_name == "gpc_ziwei_lunduan_liuyue"
    return true if api_name == "gpc_ziwei_lunduan_liuri"
    return true if api_name == "gpc_ziwei_lunduan_liushi"

    return true if api_name == "gpc_ziwei_lunduan_benming_minggong"
    return true if api_name == "gpc_ziwei_poufuzeri"
    return false
  end
  # 1､紫微十年事業發展衝刺年分析＝＝》180元
  # 官祿
  def api_ziwei_lunduan_shinian_guanlu(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_TENYEAR
    h_par_org["house"] = Star::EX_HOUSE_9
    return api_ziwei_lunduan(h_par_org,h_result,remote_ip)
  end
  # 2､紫微十年進財年分析＝＝》180元
  # 財帛
  def api_ziwei_lunduan_shinian_caibo(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_TENYEAR
    h_par_org["house"] = Star::EX_HOUSE_5
    return api_ziwei_lunduan(h_par_org,h_result,remote_ip)
  end
  # 3､紫微十年婚姻感情緣年分析＝＝》180元
  # 夫妻
  def api_ziwei_lunduan_shinian_fugi(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_TENYEAR
    h_par_org["house"] = Star::EX_HOUSE_3
    return api_ziwei_lunduan(h_par_org,h_result,remote_ip)
  end
  # 4､紫微流年事業發展衝刺月份分析＝＝》150元
  def api_ziwei_lunduan_liunian_guanlu(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWYEAR
    h_par_org["house"] = Star::EX_HOUSE_9
    return api_ziwei_lunduan(h_par_org,h_result,remote_ip)
  end
  # 5､紫微流年進財運月份分析＝＝》150元
  def api_ziwei_lunduan_liunian_caibo(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWYEAR
    h_par_org["house"] = Star::EX_HOUSE_5
    return api_ziwei_lunduan(h_par_org,h_result,remote_ip)
  end
  # 6､紫微流年感情運月份分析＝＝》150元
  def api_ziwei_lunduan_liunian_fugi(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWYEAR
    h_par_org["house"] = Star::EX_HOUSE_3
    return api_ziwei_lunduan(h_par_org,h_result,remote_ip)
  end
  # 7､紫微流月工作好運日分析＝＝》120元
  def api_ziwei_lunduan_liuyue_guanlu(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWMONTH
    h_par_org["house"] = Star::EX_HOUSE_9
    return api_ziwei_lunduan(h_par_org,h_result,remote_ip)
  end
  # 8､紫微流月進財日分析＝＝》120元
  def api_ziwei_lunduan_liuyue_caibo(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWMONTH
    h_par_org["house"] = Star::EX_HOUSE_5
    return api_ziwei_lunduan(h_par_org,h_result,remote_ip)
  end
  # 9､紫微流月感情最佳互動日分析＝＝》120元
  def api_ziwei_lunduan_liuyue_fugi(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWMONTH
    h_par_org["house"] = Star::EX_HOUSE_3
    return api_ziwei_lunduan(h_par_org,h_result,remote_ip)
  end
  # 五行論斷，有雙星
  def api_ziwei_lunduan(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    hUserDefData = get_cur_date_info(h_par,remote_ip)
    hUserInfo = get_user_info(h_par,remote_ip)

    oWeenApi = WeenApi.new
    nPanType = h_par["pt"] == nil ? Cfate::PAN_TENYEAR : h_par["pt"].to_i
    if (nPanType == Cfate::PAN_NORMAL) then
      nPanType = Cfate::PAN_TENYEAR
    end
    hApFunc = nil
    hParAll = nil
    hParAll = {Star::DIS_SKYCOOK => Cfate::PAR_FALSE}
    result = oWeenApi.g_profate_lunduan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)

    h_par_org["p"] = -1
    return result,result,h_par_org
    # respond_with result
    # JSONP format
    # respond_with(result) do |format|
    #   format.any(:js, :json) { render :json => result, :callback => params[:callback] }
    # end
  end
  # 10､紫微流日工作好運時辰分析（七天）＝＝》120元
  def api_ziwei_lunduan_liuri_guanlu(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWDATE
    h_par_org["house"] = Star::EX_HOUSE_9
    return api_ziwei_lunduan_liuri_onegong(h_par_org,h_result,remote_ip)
  end
  # 11､紫微流日進財時辰分析（七天）＝＝》120元
  def api_ziwei_lunduan_liuri_caibo(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWDATE
    h_par_org["house"] = Star::EX_HOUSE_5
    return api_ziwei_lunduan_liuri_onegong(h_par_org,h_result,remote_ip)
  end
  # 12､紫微流日感情最佳互動時辰分析（七天）＝＝》120元
  def api_ziwei_lunduan_liuri_fugi(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWDATE
    h_par_org["house"] = Star::EX_HOUSE_3
    return api_ziwei_lunduan_liuri_onegong(h_par_org,h_result,remote_ip)
  end
  @@max_liuri_day = 7
  def api_ziwei_lunduan_liuri_onegong(h_par_org,h_result,remote_ip)
    pan_type = Cfate::PAN_FLOWDATE
    h_par = h_par_org.clone
    h_par["pt"] = pan_type
    hDate = get_cur_date_info(h_par,remote_ip)

    # get_cur_date_info 之後，全轉成陰曆
    # h_par["udt"] = 1 # 陰曆
    # nEYear,nEMonth,nEDate,bLeapMonth = hDate[Cfate::EYear],hDate[Cfate::EMonth],hDate[Cfate::EDate],hDate[Cfate::LeapMonth]

    # 從輸入的日期之後幾天開始
    after_days = h_par["p"].to_i
    # nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextNEDate(nEYear,nEMonth,nEDate,bLeapMonth,after_days)
    h_par["timestamp_udt"] = Xdate.make_timestamp_udt_next_days(pan_type,hDate["timestamp_udt"],after_days)

    # h_par["uy"],h_par["um"],h_par["ud"] = nEYear,nEMonth,nEDate
    # h_par["ul"] = bLeapMonth ? 1 : 0

    days = h_par["days"].to_i
    if (days <= 0) then
      days = 1
    end
    days = @@max_liuri_day if days > @@max_liuri_day
    hOut = Hash.new
    (0...days).each do |i|  # 七天 改 一天
      hOneOut = Hash.new

      hOld_result,hOne,hOld_par = api_ziwei_lunduan(h_par.clone,h_result,remote_ip)

      if (i + after_days == 0) then
        hOut["pan"] = hOne["pan"]
        hOut["jingyu"] = hOne["jingyu"]
      end
      hOneOut["timestamp"] = hOne["timestamp"]
      hOneOut["qushitu"] = hOne["qushitu"]
      hOneOut["lunduan"] = hOne["lunduan"] #[0]

      # nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      # h_par["uy"],h_par["um"],h_par["ud"] = nEYear,nEMonth,nEDate
      # h_par["ul"] = bLeapMonth ? 1 : 0
      h_par["timestamp_udt"] = Xdate.make_timestamp_udt_next_days(pan_type,h_par["timestamp_udt"],1)

      hOut["pd#{i + after_days}"] = hOneOut
    end

    h_result.merge!(hOut) { |key, v1, v2| v2 }

    if (check_par_p_if_neeed_new(h_par_org["p"],days,@@max_liuri_day)) then
      h_par_org["p"] += days
    else
      h_par_org["p"] = -1
    end
    return h_result,hOut,h_par_org
  end
  # 13､紫微本命總體分析（十二宮）分析＝＝》50元
  def api_ziwei_lunduan_benming(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_NORMAL
    h_par_org["house"] = Star::EX_HOUSE_ALL
    return api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
  end
  # 14､紫微十年總體分析（十二宮）分析＝＝》100元
  def api_ziwei_lunduan_shinian(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_TENYEAR
    h_par_org["house"] = Star::EX_HOUSE_ALL
    return api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
  end
  # 15､紫微流年總體分析（十二宮）分析＝＝》100元
  def api_ziwei_lunduan_liunian(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWYEAR
    h_par_org["house"] = Star::EX_HOUSE_ALL
    return api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
  end
  # 流月流日的主要現象與次要現象對調
  # 16､紫微流月總體分析（十二宮）分析＝＝》50元
  def api_ziwei_lunduan_liuyue(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWMONTH
    h_par_org["house"] = Star::EX_HOUSE_ALL
    return api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
  end
  # 17､紫微流日總體分析（十二宮）分析＝＝》50元
  def api_ziwei_lunduan_liuri(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWDATE
    h_par_org["house"] = Star::EX_HOUSE_ALL
    return api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
  end
  # 本命盤命宮的詳細解說要0元
  def api_ziwei_lunduan_benming_minggong(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_NORMAL
    h_par_org["house"] = Star::EX_HOUSE_1
    return api_ziwei_lunduan_meen_benming_minggong(h_par_org,h_result,remote_ip)
  end
  # 生剋論斷，沒有雙星
  def api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    hUserDefData = get_cur_date_info(h_par,remote_ip)
    hUserInfo = get_user_info(h_par,remote_ip)

    oWeenApi = WeenApi.new
    nPanType = h_par["pt"] == nil ? Cfate::PAN_NORMAL : h_par["pt"].to_i
    hApFunc = nil
    # hParAll = nil
    hParAll = {Star::DIS_SKYCOOK => Cfate::PAR_FALSE}
    result = oWeenApi.g_meen_lunduan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)

    h_par_org["p"] = -1
    return result,result,h_par_org
  end
  def api_ziwei_lunduan_meen_benming_minggong(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    h_par["house"] = Star::EX_HOUSE_1
    hOld_result,h,hOld_par = api_ziwei_lunduan_meen(h_par,h_result,remote_ip)
    # 趨勢圖 （感受等）
    h["qushitu"] = api_ziwei_lunduan_meen_benming_minggong_qushitu_erase()

    h_lunduan = h["lunduan"]
    #
    # 命宮保留完整資料，其餘宮位只剩主星
    #
    # 用Star::EX_HOUSE_1 就不必移除其他的宮
    # (1..11).each do |i|
    #   h_gong = h_lunduan[i]
    #   h_gong["xing"]["ciyao"] = api_ziwei_lunduan_meen_benming_minggong_ciyao_erase(h_gong["xing"]["ciyao"])
    #   h_gong["xing"]["huanjingtezheng"] = api_ziwei_lunduan_meen_benming_minggong_ciyao_erase(h_gong["xing"]["huanjingtezheng"])
    #   h_gong["sihua"] = api_ziwei_lunduan_meen_benming_minggong_sihua_erase(h_gong["sihua"])
    # end

    h_par_org["p"] = -1
    return h,h,h_par_org
  end
  def api_ziwei_lunduan_meen_benming_minggong_ciyao_erase(h)
    h["data"] = []
    # h["data"]["name"] = []
    # h["data"]["explain"] = []
    return h
  end
  def api_ziwei_lunduan_meen_benming_minggong_sihua_erase(h_sihua)
    (0..3).each do |nFourHuaIndex|
      h_sihua[Ziwei.sihua_nick(nFourHuaIndex)] = api_ziwei_lunduan_meen_benming_minggong_sihua_empty()
    end
    return h_sihua
  end
  def api_ziwei_lunduan_meen_benming_minggong_sihua_empty()
    return {"jilu": {"data": {"zihua": ["",[],""]},"taitou": ""},"shijian": {"gi": "","xiong": "","taitou": ""},"taitou": "","xianxiang": {"data": {"diegong": "","tiangan": ""},"taitou": ""}}
  end
  def api_ziwei_lunduan_meen_benming_minggong_qushitu_erase()
    return {"series": [{"name": "","data": []}],"xAxis": {"categories": []},"timestamp": "","shijian": {"gi": "","xiong": "","taitou": ""}}
  end
  @@max_poufuzeri_day = 7
  # 18､紫微斗數剖腹擇日（七天）分析 ＝＝520元
  def api_ziwei_poufuzeri(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_NORMAL
    h_par_org["house"] = Star::EX_HOUSE_ALL
    if (h_par_org["timestamp_zeri"] == nil || h_par_org["timestamp_zeri"] == "") then
      return api_ziwei_poufuzeri_zeri(h_par_org,h_result,remote_ip)
    else
      return api_ziwei_poufuzeri_pan(h_par_org,h_result,remote_ip)
    end
  end
  def api_ziwei_poufuzeri_zeri(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    h_par_org.delete("timestamp_zeri")
    hUserDefData = get_cur_date_info(h_par,remote_ip)
    hUserDefData[Cfate::WHour] = 0
    hUserDefData[Cfate::WMinute] = 0
    hUserInfo = get_user_info(h_par,remote_ip)
    hUserDefData[Cfate::PickDays] = h_par["pickdays"].to_i
    hUserDefData[Cfate::PickDays] = 1 if hUserDefData[Cfate::PickDays] == 0
    hUserDefData["after_days"] = h_par["p"].to_i

    # 分開運算時，判斷從哪一個 pd開始放資料
    # a = h_result.keys
    # a.map! {|x| x[2,x.length - 2] } # 把 pd 拿掉
    # a.map! {|x| x.to_i }
    # a.sort! { |x,y| y <=> x } # 從大排到小
    # hUserDefData["pd_start"] = a[0] + 1 if a.length > 0
    # pd_start = hUserDefData["after_days"] < 0 || hUserDefData["after_days"] >= @@max_poufuzeri_day ? 1 : hUserDefData["after_days"] * 12 + 1
    # hUserDefData["pd_start"] = pd_start

    oWeenApi = WeenApi.new
    hApFunc = nil
    hParAll = nil
    result = oWeenApi.g_pick_days(hUserInfo,hUserDefData,hApFunc,hParAll,false)
    # 整合新舊算出來的
    h = h_result.clone
    h["title"] = result["title"] if h["title"] == nil
    h["score"] = [] if h["score"] == nil
    h["lunduan"] = {} #if h["lunduan"] == nil
    h["score"] += result["score"]

    if (check_par_p_if_neeed_new(h_par_org["p"],hUserDefData[Cfate::PickDays],@@max_poufuzeri_day)) then
      h_par_org["p"] += hUserDefData[Cfate::PickDays]
    else
      h_par_org["p"] = -1
    end

    return h,result,h_par_org
  end
  def api_ziwei_poufuzeri_pan(h_par_org,h_result,remote_ip)
    # 判斷 timestamp是否為此次合法之值？
    h_par = h_par_org.clone
    h_par_org.delete("timestamp_zeri")
    h_par_org["timestamp_zeri_input"] = h_par["timestamp_zeri"]
    if (h_result == nil || h_result == {}) then
      h_par["timestamp"] = h_par["timestamp_zeri"]
      h_result = find_gpd_db(h_par,remote_ip)
  # puts "h_result : #{h_result}"
      return {},{},h_par_org if h_result == nil
      h_par["pt"] = Cfate::PAN_NORMAL
      h_par["house"] = 99
      result_1,result_2,h_par = api_ziwei_lunduan_meen(h_par,{},remote_ip)
      return h_result,result_2,h_par_org
    else
      h_result["score"].each do |res|
        if (res["timestamp"] == h_par["timestamp_zeri"]) then
          if (h_result["lunduan"][h_par["timestamp_zeri"]] == nil) then
            h_par["timestamp"] = h_par["timestamp_zeri"]
            h_par["pt"] = Cfate::PAN_NORMAL
            h_par["house"] = 99
            result_1,result_2,h_par = api_ziwei_lunduan_meen(h_par,{},remote_ip)
            # lunduan結果不儲存
            # h_result["lunduan"][h_par["timestamp_zeri"]] = result_2
          else
            result_2 = h_result["lunduan"][h_par["timestamp_zeri"]]
          end
          return h_result,result_2,h_par_org
        end
      end
    end
    return h_result,{},h_par_org
  end
  def find_gpd_db(h_par_org,remote_ip)
    hUserInfo = get_user_info(h_par_org,remote_ip)
    nWYear = hUserInfo[Cfate::WYear]
    nWMonth = hUserInfo[Cfate::WMonth]
    nWDate = hUserInfo[Cfate::WDate]
    nWHour = hUserInfo[Cfate::WHour]
    nSex = hUserInfo[Cfate::Sex]
    sSex = nSex == false ? "male" : "female" #性別 true 是女性,false是男性
    oWeenApi = WeenApi.new
    return oWeenApi.gpd_db_find(nWYear,nWMonth,nWDate,nWHour,sSex)
  end
  def check_par_p_if_neeed_new(p,new_p,max_p)
    if (p == nil) then
      return false
    end
    if (p < 0) then
      return false
    end
    if ((p + new_p).between?(0,max_p - 1)) then
      return true
    end
    return false
  end
  # 測字
  def api_cezi(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    word = h_par["word"]
    land = h_par["land"]
    if (land == "-1") then
      land = nil
    end
    m = h_par["minute"]
    if (m == "-1") then
      m = nil
    end
    thing = h_par["thing"]

    oWeenApi = WeenApi.new
    result = oWeenApi.g_gnum(word,land,m,thing)

    h_par_org["p"] = -1
    return result,result,h_par_org
  end
  # 姓名學
  def api_xingmingxue(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    lang = h_par["my_current_lang"]
    s = Pm.GetStrWithColon("explain.star.title.house")
    surname = s[0,2]
    txtname = s[2,2]
    sex = "M"
    surname = h_par["sname"]
    txtname = h_par["tname"]
    sex = sex_check(h_par)
    s=nil
    t=nil

    oWeenApi = WeenApi.new
    result = oWeenApi.g_name(surname,txtname,sex,s,t,lang)

    h_par_org["p"] = -1
    return result,result,h_par_org
  end

  # 姓名學 配對
  def api_xingming_peidui(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    lang = h_par["my_current_lang"]
    s = Pm.GetStrWithColon("explain.star.title.house")
    surname = s[0,1]
    txtname = s[2,1]
    s = Pm.GetStrWithColon("explain.star.title.main")
    psurname = s[0,1]
    ptxtname = s[2,1]
    sex = "man"
    surname = h_par["sname"]
    txtname = h_par["tname"]
    sex = sex_check(h_par)
    psurname = h_par["psname"]
    ptxtname = h_par["ptname"]
    s=nil
    t=nil

    oWeenApi = WeenApi.new
    result = oWeenApi.g_namepair(surname,txtname,sex,psurname,ptxtname,s,t,lang)

    h_par_org["p"] = -1
    return result,result,h_par_org
  end
  # 六爻卦 健康
  def api_liuyaogua_jiankang(h_par,h_result,remote_ip)
    liuyaogua(h_par,Divination::Jiankang,remote_ip)
  end
  # 六爻卦 求財
  def api_liuyaogua_qiucai(h_par,h_result,remote_ip)
    liuyaogua(h_par,Divination::Qiucai,remote_ip)
  end
  # 六爻卦 女未婚
  def api_liuyaogua_nuweihun(h_par,h_result,remote_ip)
    liuyaogua(h_par,Divination::Nuweihun,remote_ip)
  end
  # 六爻卦 女已婚
  def api_liuyaogua_nuyihun(h_par,h_result,remote_ip)
    liuyaogua(h_par,Divination::Nuyihun,remote_ip)
  end
  # 六爻卦 男未婚
  def api_liuyaogua_nanweihun(h_par,h_result,remote_ip)
    liuyaogua(h_par,Divination::Nanweihun,remote_ip)
  end
  # 六爻卦 男已婚
  def api_liuyaogua_nanyihun(h_par,h_result,remote_ip)
    liuyaogua(h_par,Divination::Nanyihun,remote_ip)
  end
  # 六爻卦 職場
  def api_liuyaogua_zhichang(h_par,h_result,remote_ip)
    liuyaogua(h_par,Divination::ZhiChang,remote_ip)
  end

  def liuyaogua(h_par_org,gua_bie,remote_ip)
    h_par = h_par_org.clone
    hData = get_user_info(h_par,remote_ip)
    nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip())
    nWYear,nWMonth,nWDate,nWHour,nWMin = hData[Cfate::WYear],hData[Cfate::WMonth],hData[Cfate::WDate],hData[Cfate::WHour],hData[Cfate::WMinute]
    year = h_par["uy"] != nil ? h_par["uy"].to_i : nWYear
    month = h_par["um"] != nil ? h_par["um"].to_i : nWMonth
    day = h_par["ud"] != nil ? h_par["ud"].to_i : nWDate
    hour = h_par["uh"] != nil ? h_par["uh"].to_i : nWHour
    name = h_par["name"]
    question = h_par["question"]
    if (h_par["wai"] != nil) then
      wai = h_par["wai"].to_i
    else
      wai = h_par["up"].to_i
    end
    if (h_par["nei"] != nil) then
      nei = h_par["nei"].to_i
    else
      nei = h_par["down"].to_i
    end
    if (h_par["bian"] != nil) then
      bian = h_par["bian"].to_i
    else
      bian = h_par["change"].to_i
    end
    oWeenApi = WeenApi.new
    result = oWeenApi.g_pugua(wai,nei,bian,year,month,day,hour,name,question,gua_bie)

    h_par_org["p"] = -1
    return result,result,h_par_org
  end

  def sex_check(h_par)
    if (h_par["sex"] != nil) then
      return h_par["sex"]
    elsif (h_par["gender"] != nil) then
      return h_par["gender"]
    else
      return "M"
    end
  end
  def get_cur_date_info(h,remote_ip)
    y,m,d,hour,min,l,dt = h["uy"],h["um"],h["ud"],h["uh"],h["umin"],h["ul"],h["udt"]
    hUserDefData = Hash.new
    if (y != nil) then
      if (dt.to_i == 0) then # solar
        y,m,d,l = Xdate.West2East(y.to_i, m.to_i, d.to_i)
      else
        l = (l.to_i != 0)
      end
      hUserDefData[Cfate::EYear] = y.to_i
      hUserDefData[Cfate::EMonth] = m.to_i
      hUserDefData[Cfate::EDate] = d.to_i
      hUserDefData[Cfate::LeapMonth] = l
      hUserDefData[Cfate::WHour] = hour.to_i
      hUserDefData[Cfate::WMinute] = min.to_i
    else
      nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip)
      hUserDefData[Cfate::EYear] = nEYear
      hUserDefData[Cfate::EMonth] = nEMonth
      hUserDefData[Cfate::EDate] = nEDate
      hUserDefData[Cfate::LeapMonth] = bLeapMonth
      hUserDefData[Cfate::WHour] = nWHour
      hUserDefData[Cfate::WMinute] = nWMin
    end
    # hUserDefData[Star::HOUSE_NAME] = h["house"] == nil ? Star::EX_HOUSE_ALL : h["house"].to_i
    hUserDefData[Star::HOUSE_NAME] = h["house"] == nil ? Star::EX_HOUSE_3 : h["house"].to_i
    
    timestamp = h["timestamp_udt"]
    if (timestamp != nil) then
      y,m,d,hour,sex,min,mb = Xdate.parse_timestamp(timestamp)

      hUserDefData[Cfate::WYear] = y
      hUserDefData[Cfate::WMonth] = m
      hUserDefData[Cfate::WDate] = d
      hUserDefData[Cfate::WHour] = hour
      hUserDefData[Cfate::WMinute] = min

      ey,em,ed,ebl = Xdate.West2East(y, m, d)
      hUserDefData[Cfate::EYear] = ey
      hUserDefData[Cfate::EMonth] = em
      hUserDefData[Cfate::EDate] = ed
      hUserDefData[Cfate::LeapMonth] = ebl

      hUserDefData["timestamp_udt"] = h["timestamp_udt"]
    else
      timestamp_udt = Xdate.make_timestamp_hash(hUserDefData)
      pan_type = h["pt"].to_i
      hUserDefData["timestamp_udt"] = Xdate.make_timestamp_udt(pan_type,timestamp_udt)
    end
    hUserDefData["my_current_lang"] = h["my_current_lang"]
    hUserDefData["my_current_lang"] = "zh-CN" if hUserDefData["my_current_lang"] == nil
    return hUserDefData
  end
  def get_user_info(h,remote_ip)
    # y,m,d,hour,min,l,dt = h[:y],h[:m],h[:d],h[:h],h[:min],h[:l],h[:dt]
    y,m,d,hour,min,l,dt = h["y"],h["m"],h["d"],h["h"],h["min"],h["l"],h["dt"]
    hUserInfo = Hash.new
    if (y != nil) then
      if (dt.to_i == 0) then # solar
        hUserInfo[Cfate::WYear] = y.to_i
        hUserInfo[Cfate::WMonth] = m.to_i
        hUserInfo[Cfate::WDate] = d.to_i
      else # lunar
        nWYear,nWMonth,nWDate = Xdate.East2West(y.to_i, m.to_i, d.to_i, l.to_i != 0)
        hUserInfo[Cfate::WYear] = nWYear
        hUserInfo[Cfate::WMonth] = nWMonth
        hUserInfo[Cfate::WDate] = nWDate
      end
      hUserInfo[Cfate::WHour] = hour.to_i
      hUserInfo[Cfate::WMinute] = min.to_i
      # hUserInfo[Cfate::Sex] = h["sex"] == "1" || h["gender"] == "1" || h["gender"].upcase == "M" ? false : true  # sex : 1 ±íÊ¾ÄÐÉú
      hUserInfo[Cfate::Sex] = !Name.isMale(sex_check(h))
      hUserInfo[Cfate::Name] = h["name"]
    else
      nWYear,nWMonth,nWDate,nWHour,nWMin,nEYear,nEMonth,nEDate,bLeapMonth,nETime,nEMin = Xdate.gHouse_GetNow(remote_ip)
      hUserInfo[Cfate::WYear] = nWYear
      hUserInfo[Cfate::WMonth] = nWMonth
      hUserInfo[Cfate::WDate] = nWDate
      hUserInfo[Cfate::WHour] = nWHour
      hUserInfo[Cfate::WMinute] = nWMin
      hUserInfo[Cfate::Sex] = false
      hUserInfo[Cfate::Name] = "name"
    end
    hUserInfo[Cfate::Name] = h["name"] if (h["name"] != nil)
    timestamp = h["timestamp"]
    if (timestamp != nil) then
      y,m,d,hour,sex,min,mb = Xdate.parse_timestamp(timestamp)

      hUserInfo[Cfate::WYear] = y
      hUserInfo[Cfate::WMonth] = m
      hUserInfo[Cfate::WDate] = d
      hUserInfo[Cfate::WHour] = hour
      hUserInfo[Cfate::WMinute] = min
      hUserInfo[Cfate::Sex] = !Name.isMale(sex)
      hUserInfo[Cfate::Multiple_births] = mb
    end
    hUserInfo["longitude"] = h["longitude"] == nil ? nil : h["longitude"].to_f
    hUserInfo["latitude"] = h["latitude"] == nil ? nil : h["latitude"].to_f

    return hUserInfo
  end
  def coupon_issued(uuid_coupon,need_point)
    if (need_point == 0) then
      return nil,false
    end
    if (uuid_coupon == nil) then
      return nil,false
    end
    coupon = ProfateCoupon.check_uuid(uuid_coupon).issued().last
    if (coupon != nil) then
      return coupon,true
    end
    return nil,false
  end
  def check_profateevent_uuid_exist(uuid)
    count = ProfateCoupon.check_uuid(uuid).count
    if (count > 0) then
      return true
    end
    return false
  end
  def get_profateevent_coupon()
    # loop do
    20.times do |i|
      uuid = SecureRandom.hex(6).upcase
      is_exist = check_profateevent_uuid_exist(uuid)
      if (!is_exist) then
        return uuid
      end
      puts "get_profateevent_coupon 1: uuid : not found"
    end
    20.times do |i|
      uuid = SecureRandom.uuid.gsub('-','').upcase[0,12]
      is_exist = check_profateevent_uuid_exist(uuid)
      if (!is_exist) then
        return uuid
      end
      puts "get_profateevent_coupon 2: uuid : not found"
    end
    20.times do |i|
      uuid = SecureRandom.hex(12).upcase[0,12]
      is_exist = check_profateevent_uuid_exist(uuid)
      if (!is_exist) then
        return uuid
      end
      puts "get_profateevent_coupon 3: uuid : not found"
    end
    return ""
  end
  def get_profateevent_unique_uuid()
      uuid = SecureRandom.uuid.gsub('-','').upcase
      uuid += SecureRandom.uuid.gsub('-','').upcase
      return uuid
  end

  # free api below
  def api_ziwei_pan(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    # h_par = params
    hUserDefData = get_cur_date_info(h_par,remote_ip)
    hUserInfo = get_user_info(h_par,remote_ip)
    # h3 = hUserInfo.clone
    # h3.merge!(hUserDefData)
    # api_v1_events.hParam = h3
    # api_v1_events.save!
    oWeenApi = WeenApi.new
    nPanType = h_par["pt"] == nil ? Cfate::PAN_NORMAL : h_par["pt"].to_i
    # if (nPanType == Cfate::PAN_NORMAL) then
    #   nPanType = Cfate::PAN_TENYEAR
    # end
    hApFunc = nil
    hParAll = nil
    result = Hash.new
    result["pan"] = oWeenApi.ziwei_pan(Cfate::PAN_NORMAL,hUserInfo,hUserDefData,hApFunc,hParAll)
    # render json: h
    h_par_org["p"] = -1
    return result,result,h_par_org
    # respond_with result
    # JSONP format
    # respond_with(result) do |format|
    #   format.any(:js, :json) { render :json => result, :callback => params[:callback] }
    # end
  end

  def api_ziwei_shinian_dayun(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    # h_par = params
    hUserDefData = get_cur_date_info(h_par,remote_ip)
    hUserInfo = get_user_info(h_par,remote_ip)

    oStar = Star.new(hUserInfo,hUserDefData,nil)
    result = {"dayun" => oStar.cp_GetLargePos()}

    h_par_org["p"] = -1
    return result,result,h_par_org
  end

  # 八字盤
  def api_bazhi_pan(h_par_org,h_result,remote_ip,par_all=nil)
    h_par = h_par_org.clone
    # h_par = params
    hUserDefData = get_cur_date_info(h_par,remote_ip)
    hUserInfo = get_user_info(h_par,remote_ip)

    oWeenApi = WeenApi.new
    # nPanType = params["pt"] == nil ? Cfate::PAN_NORMAL : params["pt"].to_i
    nPanType = Cfate::PAN_NORMAL
    hApFunc = nil
    hParAll = par_all
    result = oWeenApi.g_bazhi_info(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)

    return result,result,h_par_org
  end

  # api_v1_events.param 的資料放在 params[:param] 的hash 來傳
  def api_v1_events_params_create_check(user_id,client_ip,h)
    # h = api_v1_events_params
# Pm.saveTestDb("api_v1_events_params_create_check","#{params}")
    # 從profate_event_points取得相關資料
    if (h[:event_point_id] == nil || h[:event_point_id].to_i == 0) then
      return nil,nil,nil
    end
    event_point = ProfateEventPoint.find(h[:event_point_id].to_i)
    # event_point = ProfateEventPoint.where(:event_name => h[:event_name])
    if (event_point == nil) then
      return nil,nil,nil
    end

    uuid = h[:coupon]
    need_point = h[:need_point].to_i
    # 用coupon(一個uuid) => 先判斷是否合法，依此uuid建一個event，給結果，記錄coupon的使用資訊
    coupon,has_coupon = coupon_issued(uuid,need_point)
    if (has_coupon) then
      # 記錄coupon的使用資訊
      coupon.flag = 2 # 已使用
    # for test only ，先不管是否有issued，上線前要拿掉
      if (coupon.uuid == "sw2293sw2293") then
        coupon.flag = 1 # 永遠未使用
      end
      coupon.use_date = Xdate.GetNowRails()
      coupon.remote_ip = client_ip
      coupon.use_user_id = user_id
      need_point = 0
      user_point = nil
    else
      # 輸入資料驗證
      if (event_point.need_point != need_point) then
        return nil,nil,nil
      end
      if (event_point.event_name != h[:event_name]) then
        return nil,nil,nil
      end
      uuid = get_profateevent_unique_uuid()
      if (uuid == "") then
        return nil,nil,nil
      end
      if (user_id != nil) then
        # 客戶點數確認及扣除
        user_point = ProfateUserPoint.find(user_id)
        if (user_point == nil) then
          user_point1 = ProfateUserPoint.new
          user_point1.user_id = user_id
          user_point1.point = 0
          user_point1.save!
        else
          # 點數不足 => client 會先判斷自行轉到購買點數；點數不足強行進入的是客戶亂試的結果，回應錯誤即可。
          if (user_point.point < need_point) then
            user_point = nil
          end
        end
      else
        user_point = nil
      end
    end


    # 建立profate_events表格物件
    if (uuid == "sw2293sw2293") then
      api_v1_events = ProfateEvent.check_uuid(uuid).last
    end
    api_v1_events = ProfateEvent.new() if api_v1_events == nil
    api_v1_events.user_id = user_id
    api_v1_events.uuid = uuid
    api_v1_events.event_point_id = event_point.id
    api_v1_events.point_used = need_point
    api_v1_events.event_name = event_point.event_name
    api_v1_events.api_name = event_point.api_name
    api_v1_events.can_update_count = event_point.can_update_count
    api_v1_events.param = nil
    api_v1_events.result = nil
    if (h[:param] != nil) then
      # create時就傳param時
      api_v1_events.can_update_count -= 1
      h_par = combine_user_and_server_param({},h[:param],event_point.id)
      # 有新參數，需重算 result
      h_par["need_new_result"] = true
      api_v1_events.hParam = h_par
    elsif (h[:ref_uuid] != nil) then
      oEvent = ProfateEvent.check_uuid(h[:ref_uuid])
      if (oEvent != nil) then
        api_v1_events.can_update_count -= 1
        h_par = combine_user_and_server_param(oEvent.hParam,{},0)
        # 有新參數，需重算 result
        h_par["need_new_result"] = true
        api_v1_events.hParam = h_par
      end
    end
    # 已付款 2，未付款 1，使用coupon 3，免費 4
    if (has_coupon) then
      api_v1_events.flag = 3 # 使用coupon
    else
      if (need_point == 0) then
        api_v1_events.flag = 4 # 免費
      else
        if (user_point != nil) then
          # 點數足 －》直接扣點，產生event，回結果及event
          user_point.point -= need_point
          api_v1_events.flag = 2 # 已付款
        else
          api_v1_events.flag = 1 # 未付款
        end
      end
    end
    return api_v1_events,user_point,coupon
  end
  def combine_user_and_server_param(h_event_param,h_param,event_point_id)
    h = h_event_param.clone
    h.merge!(h_param) { |key, v1, v2| v2 }
    event_point = ProfateEventPoint.find_by_id(event_point_id)
    if (event_point == nil) then
      return h
    end
    h.merge!(event_point.hParam["server"]) { |key, v1, v2| v2 }
    return h
  end

  # 夫妻 house 3，財運 5 ，工作 9
  def api_ziwei_lunduan_liuri_now(h_par_org,remote_ip)
    h = Hash.new
    h["timestamp"] = h_par_org["timestamp"]
    h["pt"] = Cfate::PAN_FLOWDATE
    house = h_par_org["house"].to_i
    house = 3 if ![3,5,9].include?(house)
    h["house"] = house
    h["name"] = h_par_org["name"]
    return api_ziwei_lunduan(h,{},remote_ip)
  end

  # for customer list search
  def api_find_search_customers(user_id,sSearchData,nLastId,nCount,par_YearDisplay)
    sSearchDataRet = sSearchData

    if ((sSearchData == nil) || (sSearchData.length == 0)) then
#     all_user_customer = UserCustomer.find(:all, :order => "id DESC", :limit=>nCount.abs.to_s)
      # order by id
      # all_user_customer = UserCustomer.check_user_id(user_id).order("id DESC")#.limit(nCount.abs)

      # order by updated_at
      all_user_customer = UserCustomer.check_user_id(user_id).order("updated_at DESC")#.limit(nCount.abs)
    else
      sps,sSearchDataRet = SearchParser.ParseSearchData(sSearchData,par_YearDisplay)

      # aDataAfterCheck = UserCustomer.check_user_id(user_id).check_year(nWYear).check_month(nWMonth).check_day(nWDate).check_hour(nWHour).check_sex(nSex).check_name(sName)
      aDataAfterCheck = UserCustomer.check_user_id(user_id)
      aDataAfterCheck = aDataAfterCheck.check_sps(sps)
      # if (nType == 1) then
      #     # aDataAfterCheck = aDataAfterCheck.check_year(nWYear).check_month(nWMonth).check_day(nWDate)
      #     aDataAfterCheck = aDataAfterCheck.check_year_month_day(nWYear,nWMonth,nWDate)
      # elsif (nType == 2) then
      #     # aDataAfterCheck = aDataAfterCheck.check_ymd_and(nWYear,nWMonth,nWDate)
      #     aDataAfterCheck = aDataAfterCheck.check_years_months_days(nWYear,nWMonth,nWDate)
      # else
      #     aDataAfterCheck = aDataAfterCheck.check_ymd_or(nWYear,nWMonth,nWDate)
      # end
      # aDataAfterCheck = aDataAfterCheck.check_hour(nWHour)

      # aDataAfterCheck = aDataAfterCheck.check_sex(nSex)
      # if (sName == nil) then
      #   sName = ""
      # end
      # aDataAfterCheck = aDataAfterCheck.check_name(sName)
      if (nCount > 0) then
        # all_user_customer = aDataAfterCheck.next(nLastId).reverse #.limit(nCount).reverse
        all_user_customer = aDataAfterCheck.order("updated_at DESC") #.limit(nCount).reverse
      elsif (nCount == 0) then
        all_user_customer = aDataAfterCheck.reverse #.limit(nCount).reverse
      else
        all_user_customer = aDataAfterCheck.prev(nLastId)#.limit(nCount.abs)
      end
    end
    list_count = nCount.abs
    return sSearchDataRet,all_user_customer,list_count
  end

  def api_set_user_customer_updated_at(customer_id)
    uc = UserCustomer.find_by_id(customer_id)
    if (uc != nil) then
      uc.updated_at = Xdate.GetNowRails()
      uc.save!
    end
  end

  # 火錘
  # 五行論斷，有雙星
  def huochui_ziwei_lunduan(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    hUserDefData = get_cur_date_info(h_par,remote_ip)
    hUserInfo = get_user_info(h_par,remote_ip)

    oWeenApi = WeenApi.new
    nPanType = h_par["pt"] == nil ? Cfate::PAN_NORMAL : h_par["pt"].to_i
    # if (nPanType == Cfate::PAN_NORMAL) then
    #   nPanType = Cfate::PAN_TENYEAR
    # end
    hApFunc = nil
    hParAll = nil
    hParAll = {Star::DIS_SKYCOOK => Cfate::PAR_FALSE}
    result = oWeenApi.g_profate_lunduan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
    result.delete("pan")

    h_par_org["p"] = -1
    return result,result,h_par_org
    # respond_with result
    # JSONP format
    # respond_with(result) do |format|
    #   format.any(:js, :json) { render :json => result, :callback => params[:callback] }
    # end
  end
  # 生剋論斷，沒有雙星
  def huochui_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    hUserDefData = get_cur_date_info(h_par,remote_ip)
    hUserInfo = get_user_info(h_par,remote_ip)

    oWeenApi = WeenApi.new
    nPanType = h_par["pt"] == nil ? Cfate::PAN_NORMAL : h_par["pt"].to_i
    hApFunc = nil
    # hParAll = nil
    hParAll = {Star::DIS_SKYCOOK => Cfate::PAR_FALSE}
    result = oWeenApi.g_meen_lunduan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
    result.delete("pan")

    h_par_org["p"] = -1
    return result,result,h_par_org
  end
  # 火錘 排盤
  def huochui_ziwei_pan(h_par_org,h_result,remote_ip)
    h_par = h_par_org.clone
    # h_par = params
    hUserDefData = get_cur_date_info(h_par,remote_ip)
    hUserInfo = get_user_info(h_par,remote_ip)
    # h3 = hUserInfo.clone
    # h3.merge!(hUserDefData)
    # api_v1_events.hParam = h3
    # api_v1_events.save!
    oWeenApi = WeenApi.new
    nPanType = h_par["pt"] == nil ? Cfate::PAN_NORMAL : h_par["pt"].to_i
    # if (nPanType == Cfate::PAN_NORMAL) then
    #   nPanType = Cfate::PAN_TENYEAR
    # end
    hApFunc = nil
    hParAll = nil
    result = Hash.new
    result["pan"] = oWeenApi.huochui_ziwei_pan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
    # render json: h
    h_par_org["p"] = -1
    return result,result,h_par_org
    # respond_with result
    # JSONP format
    # respond_with(result) do |format|
    #   format.any(:js, :json) { render :json => result, :callback => params[:callback] }
    # end
  end


  # gpc api
  # 測字
  def gpc_cezi_simple(h_par_org)
    word = h_par_org["word"]
    word.strip! if word != nil
    land = h_par_org["land"]
    if (land == "-1") then
      land = nil
    end
    m = h_par_org["minute"]
    if (m == "-1") then
      m = nil
    end
    thing = h_par_org["thing"]

    oWeenApi = WeenApi.new
    # result = oWeenApi.g_gnum(word,land,m,thing)
    result = oWeenApi.g_gnum_simple(word,land,m,thing)
    result["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],1)
    # result["function_name"] = "gpc_cezi_simple"
    return result
  end
  # 姓名學
  def gpc_xingmingxue_simple(h_par_org)
    h_par = h_par_org.clone
    lang = h_par["my_current_lang"]
    # s_d = Pm.GetStrWithColon("explain.star.title.house")
    # surname = s_d[0,2]
    # txtname = s_d[2,2]
    sex = "M"
    surname = h_par["sname"]
    txtname = h_par["tname"]
    sex = sex_check(h_par)
    s = []
    t = []
    s=h_par_org["s"].split(",") if h_par_org["s"] != nil
    t=h_par_org["t"].split(",") if h_par_org["t"] != nil

    oWeenApi = WeenApi.new
    # result = oWeenApi.g_name_simple(surname,txtname,sex,s,t)
    result = oWeenApi.gpc_name(surname,txtname,sex,s,t,lang)
    result["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],1)
    # result["function_name"] = "gpc_xingmingxue_simple"

    return result
  end
  # 姓名學 配對
  def api_xingming_peidui_simple(h_par_org)
    h_par = h_par_org.clone
    lang = h_par["my_current_lang"]
    # s = Pm.GetStrWithColon("explain.star.title.house")
    # surname = s[0,1]
    # txtname = s[2,1]
    # s = Pm.GetStrWithColon("explain.star.title.main")
    # psurname = s[0,1]
    # ptxtname = s[2,1]
    sex = "man"
    surname = h_par["sname"]
    txtname = h_par["tname"]
    sex = sex_check(h_par)
    psurname = h_par["psname"]
    ptxtname = h_par["ptname"]

    s = []
    t = []
    s = h_par_org["s"].split(",") if h_par_org["s"] != nil
    t = h_par_org["t"].split(",") if h_par_org["t"] != nil
    ps = []
    pt = []
    ps = h_par_org["ps"].split(",") if h_par_org["ps"] != nil
    pt = h_par_org["pt"].split(",") if h_par_org["pt"] != nil

    oWeenApi = WeenApi.new
    result = oWeenApi.gpc_namepair(surname,txtname,sex,psurname,ptxtname,s,t,ps,pt,lang)
    # result = g_namepair(surname,txtname,sex,pairfirstn,pairlastn,s=nil,t=nil,ps=nil,pt=nil)
    result["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],1)
    # result["function_name"] = "api_xingming_peidui_simple"

    return result
  end

  # 紫微論斷
  # 1､紫微十年事業發展衝刺年分析＝＝》180元
  # 官祿
  def gpc_ziwei_lunduan_shinian_guanlu(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_TENYEAR
    h_par_org["house"] = Star::EX_HOUSE_9
    r,r2,h = api_ziwei_lunduan(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    # r_out["qushitu"] = r["qushitu"]
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],1)
    # r_out["function_name"] = "gpc_ziwei_lunduan_shinian_guanlu"
    return r_out
  end
  # 2､紫微十年進財年分析＝＝》180元
  # 財帛
  def gpc_ziwei_lunduan_shinian_caibo(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_TENYEAR
    h_par_org["house"] = Star::EX_HOUSE_5
    r,r2,h = api_ziwei_lunduan(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    # r_out["qushitu"] = r["qushitu"]
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],2)
    # r_out["function_name"] = "gpc_ziwei_lunduan_shinian_caibo"
    return r_out
  end
  # 3､紫微十年婚姻感情緣年分析＝＝》180元
  # 夫妻
  def gpc_ziwei_lunduan_shinian_fugi(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_TENYEAR
    h_par_org["house"] = Star::EX_HOUSE_3
    r,r2,h = api_ziwei_lunduan(h_par_org,h_result,remote_ip)
    # r.delete("pan")
    # r["lunduan"][0].delete("xing")
    # return r
    r_out = gpc_remove_gong_data(r)
    # r_out["qushitu"] = r["qushitu"]
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],3)
    # r_out["function_name"] = "gpc_ziwei_lunduan_shinian_fugi"
    return r_out
  end
  # 4､紫微流年事業發展衝刺月份分析＝＝》150元
  def gpc_ziwei_lunduan_liunian_guanlu(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWYEAR
    h_par_org["house"] = Star::EX_HOUSE_9
    r,r2,h = api_ziwei_lunduan(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    # r_out["qushitu"] = r["qushitu"]
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],4)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liunian_guanlu"
    return r_out
  end
  # 5､紫微流年進財運月份分析＝＝》150元
  def gpc_ziwei_lunduan_liunian_caibo(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWYEAR
    h_par_org["house"] = Star::EX_HOUSE_5
    r,r2,h = api_ziwei_lunduan(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    # r_out["qushitu"] = r["qushitu"]
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],5)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liunian_caibo"
    return r_out
  end
  # 6､紫微流年感情運月份分析＝＝》150元
  def gpc_ziwei_lunduan_liunian_fugi(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWYEAR
    h_par_org["house"] = Star::EX_HOUSE_3
    r,r2,h = api_ziwei_lunduan(h_par_org,h_result,remote_ip)
    # r.delete("pan")
    # r["lunduan"][0].delete("xing")
    # return r
    r_out = gpc_remove_gong_data(r)
    # r_out["qushitu"] = r["qushitu"]
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],6)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liunian_fugi"
    return r_out
  end
  # 7､紫微流月工作好運日分析＝＝》120元
  def gpc_ziwei_lunduan_liuyue_guanlu(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWMONTH
    h_par_org["house"] = Star::EX_HOUSE_9
    r,r2,h = api_ziwei_lunduan(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    # r_out["qushitu"] = r["qushitu"]
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],7)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liuyue_guanlu"
    return r_out
  end
  # 8､紫微流月進財日分析＝＝》120元
  def gpc_ziwei_lunduan_liuyue_caibo(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWMONTH
    h_par_org["house"] = Star::EX_HOUSE_5
    r,r2,h = api_ziwei_lunduan(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    # r_out["qushitu"] = r["qushitu"]
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],8)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liuyue_caibo"
    return r_out
  end
  # 9､紫微流月感情最佳互動日分析＝＝》120元
  def gpc_ziwei_lunduan_liuyue_fugi(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWMONTH
    h_par_org["house"] = Star::EX_HOUSE_3
    r,r2,h = api_ziwei_lunduan(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    # r_out["qushitu"] = r["qushitu"]
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],9)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liuyue_fugi"
    return r_out
  end
  # 10､紫微流日工作好運時辰分析（七天）＝＝》120元
  def gpc_ziwei_lunduan_liuri_guanlu(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWDATE
    h_par_org["house"] = Star::EX_HOUSE_9
    r,r2,h = api_ziwei_lunduan_liuri_onegong(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_pd_data(r)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],10)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liuri_guanlu"

    return r_out
  end
  # 流日疾厄 2021/1/23 尚未提供
  def gpc_ziwei_lunduan_liuri_jie(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWDATE
    h_par_org["house"] = Star::EX_HOUSE_6
    r,r2,h = api_ziwei_lunduan_liuri_onegong(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_pd_data(r)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],22)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liuri_jie"

    return r_out
  end
  def gpc_remove_pd_data(r,bLiushi=false)
    r_out = {}
    jingyu = r["jingyu"]
    # r.delete("jingyu")
    # r.delete("pan")
    r_out["jingyu"] = jingyu

    r.keys.each do |k|
      if (k.include?("pd")) then
        r_k = {}
        r_k["timestamp"] = r[k]["timestamp"]
        # r_k["qushitu"] = r[k]["qushitu"]

        # r_k["lunduan"] = {}
        # r_k_h = {}
        # r_k_h["title"] = r[k]["lunduan"]["title"]
        # r_k_h["gongming"] = r[k]["lunduan"]["gongming"]
        # r_k_h["xing"] = gpc_remove_lunduan_gong_data_xing(r[k]["lunduan"]["xing"])

        # rsh = {}
        # r[k]["lunduan"]["sihua"].keys.each do |sh|
        #   h = r[k]["lunduan"]["sihua"][sh]
        #   rsh[sh] = gpc_remove_liuri_data_sihua(h.clone)
        # end
        # r_k_h["sihua"] = rsh

        # r_k["lunduan"] = [r_k_h]
        r_k["lunduan"] = gpc_remove_lunduan_data(r[k]["lunduan"],bLiushi)

        r_out[k] = r_k
      end
    end
    return r_out
  end
  def gpc_remove_liuri_data_sihua(h)
    r = {}
    r["taitou"] = gpc_remove_sihua_word(h["taitou"])

    s = ""
    s += gpc_remove_lunduan_gong_data_xing_each_explain_final(h["xianxiang"]["data"]["diegong"])
    s += gpc_remove_lunduan_gong_data_xing_each_explain_final(h["xianxiang"]["data"]["tiangan"])
    r["xianxiang"] = {}
    r["xianxiang"]["taitou"] = Ziwei.shuoming_sihua_taitou("xianxiang_meiyougixiong")
    r["xianxiang"]["data"] = s
    
    s = ""
    s = h["jilu"]["data"]["zihua"]
    r["jilu"] = {}
    r["jilu"]["taitou"] = Ziwei.shuoming_sihua_taitou("ganso")
    r["jilu"]["data"] = s
    return r
  end
  # 11､紫微流日進財時辰分析（七天）＝＝》120元
  def gpc_ziwei_lunduan_liuri_caibo(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWDATE
    h_par_org["house"] = Star::EX_HOUSE_5
    r,r2,h = api_ziwei_lunduan_liuri_onegong(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_pd_data(r)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],11)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liuri_caibo"
    return r_out
  end
  # 12､紫微流日感情最佳互動時辰分析（七天）＝＝》120元
  def gpc_ziwei_lunduan_liuri_fugi(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWDATE
    h_par_org["house"] = Star::EX_HOUSE_3
    r,r2,h = api_ziwei_lunduan_liuri_onegong(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_pd_data(r)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],12)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liuri_fugi"
    return r_out
  end

  # 13､紫微流時工作好運時辰分析
  def gpc_ziwei_lunduan_liushi_guanlu(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWTIME
    h_par_org["house"] = Star::EX_HOUSE_9
    r,r2,h = api_ziwei_lunduan_liushi_onegong(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_pd_data(r,true)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],13)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liushi_guanlu"
    return r_out
  end
  # 14､紫微流時進財時辰分析
  def gpc_ziwei_lunduan_liushi_caibo(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWTIME
    h_par_org["house"] = Star::EX_HOUSE_5
    r,r2,h = api_ziwei_lunduan_liushi_onegong(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_pd_data(r,true)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],14)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liushi_caibo"
    return r_out
  end
  # 15､紫微流時感情最佳互動時辰分析
  def gpc_ziwei_lunduan_liushi_fugi(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWTIME
    h_par_org["house"] = Star::EX_HOUSE_3
    r,r2,h = api_ziwei_lunduan_liushi_onegong(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_pd_data(r,true)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],15)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liushi_fugi"
    return r_out
  end
  
  @@max_liushi_hour = 12
  def api_ziwei_lunduan_liushi_onegong(h_par_org,h_result,remote_ip)
    pan_type = Cfate::PAN_FLOWTIME
    h_par = h_par_org.clone
    h_par["pt"] = pan_type
    hDate = get_cur_date_info(h_par,remote_ip)

    h_par["timestamp_udt"] = hDate["timestamp_udt"]

    hours = h_par["hours"].to_i
    if (hours <= 0) then
      hours = 1
    end
    hours = @@max_liushi_hour if hours > @@max_liushi_hour
    hOut = Hash.new
    (0...hours).each do |i|  # 七天 改 一天
      hOneOut = Hash.new

      hOld_result,hOne,hOld_par = api_ziwei_lunduan(h_par.clone,h_result,remote_ip)

      if (i == 0) then
        hOut["pan"] = hOne["pan"]
        hOut["jingyu"] = hOne["jingyu"]
      end
      hOneOut["timestamp"] = hOne["timestamp"]
      # hOneOut["qushitu"] = hOne["qushitu"]
      hOneOut["lunduan"] = hOne["lunduan"] #[0]

      # nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      # h_par["uy"],h_par["um"],h_par["ud"] = nEYear,nEMonth,nEDate
      # h_par["ul"] = bLeapMonth ? 1 : 0
      h_par["timestamp_udt"] = Xdate.make_timestamp_udt_next_hours(pan_type,h_par["timestamp_udt"],2)

      hOut["pd#{i}"] = hOneOut
    end

    h_result.merge!(hOut) { |key, v1, v2| v2 }

    return h_result,hOut,h_par_org
  end
  def gpc_remove_liushi_lunduan_data(r)
    r_out = {}
    r_out["title"] = r["title"]
    r_out["gongming"] = r["gongming"]

    rsh = {}
    r["sihua"].keys.each do |sh|
      h = r["sihua"][sh]
      rsh[sh] = gpc_remove_liushi_data_sihua(h.clone)
    end
    r_out["sihua"] = rsh

    return r_out
  end
  def gpc_remove_liushi_data_sihua(h)
    s = ""
    s = h["jilu"]["data"]["zihua"] if h["jilu"]["data"].class == Hash
    s = h["jilu"]["data"] if s = h["jilu"]["data"].class != Hash
    r = {}
    r["taitou"] = gpc_remove_sihua_word(h["taitou"])
    r["jilu"] = {}
    r["jilu"]["taitou"] = Ziwei.shuoming_sihua_taitou("ganso")
    r["jilu"]["data"] = s
    return r
  end
  def gpc_remove_sihua_word(s)
    s = s[0..s.index(Pm.t("left_bracket")) - 1] if s.index(Pm.t("left_bracket")) != nil
    return s
  end
  # 16､紫微本命總體分析（十二宮）分析＝＝》50元
  def gpc_ziwei_lunduan_benming(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_NORMAL
    h_par_org["house"] = Star::EX_HOUSE_ALL
    r,r2,h = api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],16)
    # r_out["function_name"] = "gpc_ziwei_lunduan_benming"
    return r_out
  end
  # 17､紫微十年總體分析（十二宮）分析＝＝》100元
  def gpc_ziwei_lunduan_shinian(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_TENYEAR
    h_par_org["house"] = Star::EX_HOUSE_ALL
    r,r2,h = api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],17)
    # r_out["function_name"] = "gpc_ziwei_lunduan_shinian"
    return r_out
  end
  # 18､紫微流年總體分析（十二宮）分析＝＝》100元
  def gpc_ziwei_lunduan_liunian(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWYEAR
    h_par_org["house"] = Star::EX_HOUSE_ALL
    r,r2,h = api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],18)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liunian"
    return r_out
  end
  # 流月流日的主要現象與次要現象對調
  # 19､紫微流月總體分析（十二宮）分析＝＝》50元
  def gpc_ziwei_lunduan_liuyue(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWMONTH
    h_par_org["house"] = Star::EX_HOUSE_ALL
    r,r2,h = api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],19)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liuyue"
    return r_out
  end
  # 20､紫微流日總體分析（十二宮）分析＝＝》50元
  def gpc_ziwei_lunduan_liuri(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWDATE
    h_par_org["house"] = Star::EX_HOUSE_ALL
    r,r2,h = api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],20)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liuri"
    return r_out
  end
  # 21､紫微流時總體分析（十二宮）分析＝＝》50元
  def gpc_ziwei_lunduan_liushi(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_FLOWTIME
    h_par_org["house"] = Star::EX_HOUSE_ALL
    r,r2,h = api_ziwei_lunduan_meen(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r,true)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],21)
    # r_out["function_name"] = "gpc_ziwei_lunduan_liushi"
    return r_out
  end
  # 22､本命盤命宮的詳細解說要0元
  def gpc_ziwei_lunduan_benming_minggong(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_NORMAL
    h_par_org["house"] = Star::EX_HOUSE_1
    # h_par_org["house"] = Star::EX_HOUSE_ALL
    r,r2,h = api_ziwei_lunduan_meen_benming_minggong(h_par_org,h_result,remote_ip)
    r_out = gpc_remove_gong_data(r)
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],22)
    # r_out["function_name"] = "gpc_ziwei_lunduan_benming_minggong"
    return r_out
  end
  def gpc_remove_gong_data(r,bLiushi=false)
    r_out = {}
    jingyu = r["jingyu"]

    r_out["jingyu"] = jingyu
    r_out["timestamp"] = r["timestamp"]
    r_out["name"] = r["name"]

    r_out["lunduan"] = gpc_remove_lunduan_data(r["lunduan"],bLiushi)
    return r_out
  end
  def gpc_remove_lunduan_data(r_l,bLiushi=false)
    a = []
    r_l.each do |g|
      r_k = gpc_remove_lunduan_gong_data(g)
      r_k = gpc_remove_liushi_lunduan_data(r_k) if bLiushi
      a.push(r_k)
    end
    return a
  end
  def gpc_remove_lunduan_gong_data(g)
    r_out = {}

    r_out["title"] = g["title"] if g["title"].class == Hash
    r_out["title"] = g["taitou"] if g["title"].class != Hash
    r_out["gongming"] = g["gongming"]

    r_out["xing"] = gpc_remove_lunduan_gong_data_xing(g["xing"])
    r_out["sihua"] = gpc_remove_lunduan_gong_data_sihua(g["sihua"])

    return r_out
  end
  def gpc_remove_lunduan_gong_data_xing(x)
    r_out = {}
    r_out["zhuyao"] = gpc_remove_lunduan_gong_data_xing_each(x["zhuyao"]["data"])
    r_out["ciyao"] = gpc_remove_lunduan_gong_data_xing_each(x["ciyao"]["data"])
    r_out["huanjingtezheng"] = gpc_remove_lunduan_gong_data_xing_each(x["huanjingtezheng"]["data"])

    return r_out
  end
  def gpc_remove_lunduan_gong_data_xing_each(d)
    s = ""
    d.each do |h|
      s += gpc_remove_lunduan_gong_data_xing_each_explain(h)
    end
    return s
  end
  def gpc_remove_lunduan_gong_data_xing_each_explain(h)
    s = h["explain"]
    return gpc_remove_lunduan_gong_data_xing_each_explain_final(s)
  end
  def gpc_remove_lunduan_gong_data_xing_each_explain_final(s)
    n = s.index(Pm.t("right_arrow"))
    return s if n == nil

    s1 = s[0..n]
    s.gsub!(s1,"")
    return s
  end
  def gpc_remove_lunduan_gong_data_sihua(h)
    r_out = {}
    h.keys.each do |sihua|
      r_out[sihua] = gpc_remove_liuri_data_sihua(h[sihua])
    end
    return r_out
  end
  # 23､紫微斗數剖腹擇日（七天）分析
  def gpc_ziwei_poufuzeri(h_par_org,h_result,remote_ip)
    h_par_org["pt"] = Cfate::PAN_NORMAL
    h_par_org["house"] = Star::EX_HOUSE_ALL
    hUserInfo = get_user_info(h_par_org,remote_ip)

    r,r2,h2 = api_ziwei_poufuzeri_zeri(h_par_org,h_result,remote_ip)
    r_l = {}
    r["title"].push(Pm.t("IDS_S_UI_SEX"))
    r["score"].each do |h|
      timestamp = h["timestamp"]
      r_l[timestamp] = gpc_ziwei_poufuzeri_lunduan(timestamp,remote_ip)
      h["gender"] = Star.sex_word(hUserInfo[Cfate::Sex])  #r_l[timestamp]["lunduan"][0]["title"]["gender"]
    end
    r["lunduan"] = r_l

    r_out = {}
    r_out["lunduan"] = r["lunduan"]
    r_out["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],23)
    # r_out["function_name"] = "gpc_ziwei_poufuzeri"

    return r_out
  end
  def gpc_ziwei_poufuzeri_lunduan(timestamp,remote_ip)
    h_par = {}
    h_par["timestamp"] = timestamp
    h_par["pt"] = Cfate::PAN_NORMAL
    h_par["house"] = 99
    result_1,result_2,h_par = api_ziwei_lunduan_meen(h_par,{},remote_ip)
    r_out = gpc_remove_gong_data(result_1)
    # r_out["qushitu"] = result_1["qushitu"]
    return r_out
  end

  # 六爻卦 健康
  def gpc_liuyaogua_jiankang(h_par,h_result,remote_ip)
    r,r2,h_par_org = gpc_liuyaogua(h_par,Divination::Jiankang,remote_ip)
    return gpc_remove_liuyaogua(r)
  end
  # 六爻卦 求財
  def gpc_liuyaogua_qiucai(h_par,h_result,remote_ip)
    r,r2,h_par_org = gpc_liuyaogua(h_par,Divination::Qiucai,remote_ip)
    return gpc_remove_liuyaogua(r)
  end

  # 六爻卦 女未婚
  def gpc_liuyaogua_nuweihun(h_par,h_result,remote_ip)
    r,r2,h_par_org = gpc_liuyaogua(h_par,Divination::Nuweihun,remote_ip)
    return gpc_remove_liuyaogua(r)
  end
  # 六爻卦 女已婚
  def gpc_liuyaogua_nuyihun(h_par,h_result,remote_ip)
    r,r2,h_par_org = gpc_liuyaogua(h_par,Divination::Nuyihun,remote_ip)
    return gpc_remove_liuyaogua(r)
  end
  # 六爻卦 男未婚
  def gpc_liuyaogua_nanweihun(h_par,h_result,remote_ip)
    r,r2,h_par_org = gpc_liuyaogua(h_par,Divination::Nanweihun,remote_ip)
    return gpc_remove_liuyaogua(r)
  end
  # 六爻卦 男已婚
  def gpc_liuyaogua_nanyihun(h_par,h_result,remote_ip)
    r,r2,h_par_org = gpc_liuyaogua(h_par,Divination::Nanyihun,remote_ip)
    return gpc_remove_liuyaogua(r)
  end
  # 六爻卦 職場
  def gpc_liuyaogua_zhichang(h_par,h_result,remote_ip)
    r,r2,h_par_org = gpc_liuyaogua(h_par,Divination::ZhiChang,remote_ip)
    return gpc_remove_liuyaogua(r)
  end
  def gpc_remove_liuyaogua(h)
    r_out = {}
    r_out["pugua_pan"] = {}
    r_out["pugua_pan"]["explain"] = {}
    r_out["pugua_pan"]["explain"]["biao_tou"] = h["pugua_pan"]["explain"]["biao_tou"]
    r_out["pugua_pan"]["explain"]["jie_guo"] = h["pugua_pan"]["explain"]["jie_guo"]

    return r_out
  end

  def gpc_liuyaogua(h_par_org,gua_bie,remote_ip)
    h_par = h_par_org.clone
    hData = get_cur_date_info(h_par,remote_ip)
    hUserData = get_user_info(h_par,remote_ip)
    nWYear,nWMonth,nWDate,nWHour,nWMin = hData[Cfate::WYear],hData[Cfate::WMonth],hData[Cfate::WDate],hData[Cfate::WHour],hData[Cfate::WMinute]
    s_name = hUserData[Cfate::Name]
    question = h_par["question"]
    if (h_par["wai"] != nil) then
      wai = h_par["wai"].to_i
    else
      wai = h_par["up"].to_i
    end
    if (h_par["nei"] != nil) then
      nei = h_par["nei"].to_i
    else
      nei = h_par["down"].to_i
    end
    if (h_par["bian"] != nil) then
      bian = h_par["bian"].to_i
    else
      bian = h_par["change"].to_i
    end
    oWeenApi = WeenApi.new
    result = oWeenApi.g_pugua(wai,nei,bian,nWYear,nWMonth,nWDate,nWHour,s_name,question,gua_bie)

    h_par_org["p"] = -1
    return result,result,h_par_org
  end

  def gpc_function_name(company,api_id,code)
    Pm.t("ziwei.api.#{company}.#{api_id}.code_#{code}")
  end

  # 易經卜卦
  def gpc_pukwa(h_par_org)
    h_par = h_par_org.clone

    po_no = h_par["po_no"]
    ask_no = h_par["ask_no"]
    lang = h_par["my_current_lang"]

    result = Pukwa.create(po_no,ask_no,lang)
    result["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],1)
    result["final_success"] = result["solution"] != ""

    return result
  end

  # 梅花易數
  def gpc_flower(h_par_org)
    h_par = h_par_org.clone

    ask_no = h_par["ask_no"]
    any_val = h_par["any_val"]
    dir_val = h_par["dir_val"]
    time_val = h_par["time_val"]
    lang = h_par["my_current_lang"]

    result = Flower.create(ask_no,any_val,dir_val,time_val,lang)
    result["name"] = h_par["name"]
    result["function_desc"] = gpc_function_name("gpc",h_par_org["api_id"],1)
    result["final_success"] = result["process_explain"]["process_0"] != ""

    return result
  end
  def gpc_flower_gua(h_par_org)
    h_par = h_par_org.clone
    up = h_par["up"]
    down = h_par["down"]
    bian = h_par["bian"]
    lang = h_par["my_current_lang"]
    result = Flower.gua(up,down,bian,lang="zh-CN")
    return result
  end


  def g_star_pan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
    oWeenApi = WeenApi.new
    return oWeenApi.gw_star_pan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
  end

  # 八字盤
  def g_eightword_pan(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
    oWeenApi = WeenApi.new
    return oWeenApi.g_bazhi_info(nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
  end

  def g_get_user_customer(user_id,customer_id)
      # oCustomer = UserCustomer.check_option_user_id(user_id).find(customer_id) if customer_id != 0
      oCustomer = UserCustomer.check_option_user_id(user_id).check_id(customer_id).last if customer_id != 0
      hUserInfo = Hash.new
      if (oCustomer != nil) then
        hUserInfo[Cfate::WYear] = oCustomer.wyear
        hUserInfo[Cfate::WMonth] = oCustomer.wmonth
        hUserInfo[Cfate::WDate] = oCustomer.wday
        hUserInfo[Cfate::WHour] = oCustomer.hour
        hUserInfo[Cfate::WMinute] = oCustomer.minute
        hUserInfo[Cfate::Sex] = oCustomer.sex == 1 ? false : true
        hUserInfo[Cfate::Name] = oCustomer.name
        hAllInfo = oCustomer.hAll
        hUserInfo["longitude"] = hAllInfo["longitude"] == "" || hAllInfo["longitude"] == nil ? nil : hAllInfo["longitude"].to_f
        hUserInfo["latitude"] = hAllInfo["latitude"] == "" || hAllInfo["latitude"] == nil ? nil : hAllInfo["latitude"].to_f
      else
        t = time_now_local()
        hUserInfo[Cfate::WYear] = t.year
        hUserInfo[Cfate::WMonth] = t.month
        hUserInfo[Cfate::WDate] = t.day
        hUserInfo[Cfate::WHour] = t.hour
        hUserInfo[Cfate::Sex] = false
        hUserInfo[Cfate::Name] = "Profate"
      end
      return hUserInfo
  end
  def g_get_user_customers(user_id)
    # ucs = UserCustomer.check_option_user_id(user_id).all
    ucs = UserCustomer.check_user_id(user_id).order("updated_at DESC").all
    sp_customer_ids = {}
    if (ucs != nil) then
      ucs.each_with_index do |customer,nIndex|
        sp_customer_ids[customer.name] = customer.id
      end
    end
    return sp_customer_ids
  end

end
