# 闖關
class Xuexi_Z<PERSON>wei
  def Xuexi_Ziwei.chuangguan_data(hPars,xuexi_class,xuexi_item,client_ip,oStar,nPanType)
    dijiti = hPars["dijiti"]
    # 出題目
    if (dijiti == nil) then
      return Xuexi_Ziwei.chuangguan_new_data(hPars,xuexi_class,xuexi_item,client_ip,oStar,nPanType)
    else
      chuangguan_id = hPars["chuangguan_id"].to_i
      a = Pm.findTestDbValue(chuangguan_id)
      h = a[dijiti.to_i] # 下一題
      h["chuangguan_id"] = chuangguan_id
      hPars["timestamp"] = h["timestamp"]
      return h
    end
  end
  def Xuexi_Ziwei.chuangguan_timusu(xuexi_class,xuexi_item)
    if (Xuexi_Ziwei.last_class_itemcount(xuexi_class,xuexi_item)) then
      return Xuexi_Ziwei.itemcount_in_class(xuexi_class) - 1
      # return 5
    else
      return 5
    end
  end
  def Xuexi_Ziwei.chuangguan_new_data(hPars,xuexi_class,xuexi_item,client_ip,oStar,nPanType)
    jiti = Xuexi_Ziwei.chuangguan_timusu(xuexi_class,xuexi_item)
    a = []
    (1..jiti).each do |dijiti|
      h = Xuexi_Ziwei.chuangguan_one_data(dijiti,hPars,xuexi_class,xuexi_item,client_ip,oStar,nPanType)
      h["dijiti"] = dijiti
      h["jiti"] = Xuexi_Ziwei.chuangguan_timusu(xuexi_class,xuexi_item)
      a.push(h)
    end
    h2 = {}
    # 存到暫時資料庫，待回頭取出來判斷
    h2 = a[0]
    h2["chuangguan_id"] = Pm.saveTestDb2("Xuexi_Ziwei.chuangguan_data",a)
    hPars["timestamp"] = h2["timestamp"]
    return h2
  end
  def Xuexi_Ziwei.chuangguan_one_data(dijiti,hPars,xuexi_class,xuexi_item,client_ip,oStar,nPanType)
    hPars["timestamp"] = "#{oStar.uig_W_timestamp()}#{oStar.uig_bS_Ui_Val()}"
    if (!Xuexi_Ziwei.last_class_itemcount(xuexi_class,xuexi_item)) then
      s = "Xuexi_Ziwei.timu_data_#{xuexi_class}_#{xuexi_item}(hPars,xuexi_class,xuexi_item,oStar,nPanType)"
      h = eval(s)
      return h
    else
      c = xuexi_class
      i = dijiti
      s = "Xuexi_Ziwei.timu_data_#{c}_#{i}(hPars,c,i,oStar,nPanType)"
      h = eval(s)
      return h
    end
  end

  def Xuexi_Ziwei.chuangguan_jieguo(hPars,ap_name,xuexi_class,xuexi_item)
    dijiti = hPars["dijiti"].to_i
    pass,shuoming = Xuexi_Ziwei.chuangguan_jieguo_one(hPars)
    if (dijiti == Xuexi_Ziwei.chuangguan_timusu(xuexi_class,xuexi_item)) then
      pass,shuoming_all = Xuexi_Ziwei.chuangguan_jieguo_all(hPars)
      nextmsg = pass ? "nextitem" : "onemore"
      if (pass) then
        Xuexi_Ziwei.next_class_item(hPars["user_id"],ap_name)
      end
      if (Xuexi_Ziwei.last_class_itemcount(xuexi_class,xuexi_item)) then
        nextmsg = pass ? "finish" : "onemore"
      end
      return shuoming,shuoming_all,nextmsg
    else
      return shuoming,"",""
    end
  end
  def Xuexi_Ziwei.chuangguan_jieguo_all(hPars)
    chuangguan_id = hPars["chuangguan_id"].to_i
    a = Pm.findTestDbValue(chuangguan_id)
    right = 0
    notright = 0
    a.each do |h|
      right += 1 if h["result"]
      notright += 1 if !h["result"]
    end
    return Xuexi_Ziwei.chuangguan_final_jieguo_response(right,notright)
  end
  def Xuexi_Ziwei.chuangguan_jieguo_one(hPars)
    dijiti = hPars["dijiti"]
    dijiti = dijiti.to_i
    chuangguan_id = hPars["chuangguan_id"].to_i
    answers = Xuexi_Ziwei.parse_answers(hPars["answer"])
    a = Pm.findTestDbValue(chuangguan_id)
    if (a == nil) then
      return false,"error"
    else
      h = a[dijiti -  1]
      timu = h["timu_api"]
      func = "Xuexi_Ziwei.#{timu}_jieguo(h,answers)"
      h["answer"] = answers

      result,ans,right_xuaxion = eval(func)
      h["result"] = result
      Pm.updateTestDbValue(chuangguan_id,a)

      return Xuexi_Ziwei.chuangguan_jieguo_response(result,ans,right_xuaxion)
    end
  end
  def Xuexi_Ziwei.chuangguan_const_data(xuexi_class,xuexi_item,*args)
    s = "Xuexi_Ziwei.chuangguan_const_data_#{xuexi_class}(*args)"
    return eval(s)
  end
  def Xuexi_Ziwei.chuangguan_const_data_1(*args)
    return {}
  end

  def Xuexi_Ziwei.chuangguan_jieguo_response(result,ans,right_xuaxions)
    if (result) then
      # 答對
      return result,Pm.t("xuexi_ziwei.jieguo.dadui")
    else
      # 答錯
      return result,Pm.t("xuexi_ziwei.jieguo.dacuo",:q1 => ans.sort, :q2 => right_xuaxions.sort)
    end
  end
  def Xuexi_Ziwei.chuangguan_final_jieguo_response(right,notright)
    pass = false
    if ((right.to_f / (right + notright).to_f) > 0.6) then
      pass = true
    end
    pass_str = Pm.t("xuexi_ziwei.jieguo.pass") if (pass)
    pass_str = Pm.t("xuexi_ziwei.jieguo.nopass") if (!pass)
    s = Pm.t("xuexi_ziwei.jieguo.chuangguan",:q1 => right, :q2 => notright,:q3 => pass_str)
    return pass,s
  end

end
