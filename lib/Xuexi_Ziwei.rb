require("Cfate.rb")
require("Xdate.rb")
require("Star.rb")
require("Wannianli.rb")
require("Zeri.rb")
require("Xuexi_Ziwei_xuexi.rb")
require("Xuexi_Ziwei_lianxi.rb")
require("<PERSON><PERSON>i_Ziwei_chuangguan.rb")
require("Xuexi_Ziwei_timu.rb")
require("Xuexi_Ziwei_api.rb")

class Xuexi_Ziwei

  @@hApFuncDefault = { "n_SkyFindStar_house" => 273, #  4095 all  沒註冊免費版本，紫微干找星及論斷，僅提供 命，財，官
               "n_StarFindSky_house" => 0,
               "b_GiSuongEnable" => false, # 論斷
               Cfate::FUNC_HALL_NUMBER => false, # 論斷
               "b_explain" => true, # 論斷
               "n_explain_pan" => Cfate::PAN_NORMAL, # 沒註冊可看本命之論斷
               "n_explain_content" => Star::EX_C_DEFAULT, # 1
               "n_explain_house" => Star::EX_C_HOUSE_UNREGISTER, # 273
               "n_explain_free_pan" => Cfate::PAN_NORMAL, # 沒註冊可看本命之論斷
               "n_explain_free_content" => Star::EX_C_DEFAULT, # 1
               "n_explain_free_house" => Star::EX_C_HOUSE_UNREGISTER, # 273
               "n_gift_days" => 0, # 優惠天數
               "b_LYYGDisplay" => false,
               "b_Star7Display" => false,
               Cfate::FUNC_SET_PARS => false,
               "b_GanZhiBirthDisplay" => true,
               "b_SmallYearRevertDisplay" => true,
               "b_SelfFourHua" => true,
               "b_FlyoutFourHua" => true,
               "n_LastPanType" => Cfate::PAN_FLOWMIN,
               "n_LastXdatePanType" => Cfate::PAN_FLOWMIN,
               "b_showXdate" => true,
               Cfate::FUNC_HALL_NUMBER => false,
               Cfate.gGetPanTypeHashKey(Cfate::PAN_NORMAL) => true,
               Cfate.gGetPanTypeHashKey(Cfate::PAN_TENYEAR) => true,
               Cfate.gGetPanTypeHashKey(Cfate::PAN_FLOWYEAR) => true,
               Cfate.gGetPanTypeHashKey(Cfate::PAN_FLOWMONTH) => true,
               Cfate.gGetPanTypeHashKey(Cfate::PAN_FLOWDATE) => true,
               Cfate.gGetPanTypeHashKey(Cfate::PAN_FLOWTIME) => true,
               Cfate.gGetPanTypeHashKey(Cfate::PAN_FLOWMIN) => true
              }

  def Xuexi_Ziwei.apfunc_default_str()
    return Cfate.pan_par_hash2db(@@hApFuncDefault)
  end
  def Xuexi_Ziwei.func_assign(hApFunc)
    hOutput = Hash.new
    @@hApFuncDefault.each {|key,value|
      hOutput[key] = Cfate.ParInHashValueCheck(hApFunc,key,@@hApFuncDefault)
    }
    return hOutput
  end

  def Xuexi_Ziwei.product_func_par_assign(hInput,hDefault)
    if (hDefault == nil || hDefault == {}) then
      hDefault = @@hApFuncDefault
    end
    hOutput = Hash.new
    hDefault.each {|key,value|
      hOutput[key] = Cfate.ParInHashValueCheck(hInput,key,hDefault)
      if (key[1] == "_") then
        hOutput[key[2,key.length - 2]] = hOutput[key]
      end
    }
    hOutput[Star::FUNC_SET_PARS] = hOutput[Cfate::FUNC_SET_PARS]
    return hOutput
  end

  def Xuexi_Ziwei.get_pan_pars(userId,bCanUseIfatePars=false)
    if (bCanUseIfatePars) then
      panPar = IfatePar.check_userid_star(userId).first
      if (panPar == nil) then
        hUserType = Xuexi_Ziwei.pan_par_assign(nil,userId)
      else
        hUserType = Xuexi_Ziwei.pan_par_assign(panPar.hPars,userId)
      end
    else
      hUserType = Xuexi_Ziwei.pan_par_assign(nil,userId)
    end

    return hUserType
  end

  @@ApParDefault = {
                # 參數設定
                Cfate::PAR_PAN_YEAR_DISPLAY => Star::PAR_YEARDISPLAY_DEFAULT,  # 年代顯示
               "n_leap_type" => Star::PAR_LEAPTYPE_DEFAULT, #起盤排法
               "n_horse_type" => Star::PAR_HORSETYPE_DEFAULT, # 天馬星排法
               "n_god_type12" => Star::PAR_12GODTYPE_DEFAULT, # 十二長生排法
               "n_doc_type" => Star::PAR_DOCTYPE_DEFAULT, # 博士星排法
               "n_lu_type" => Star::PAR_LUTYPE_DEFAULT, # 祿存動盤方式
               "n_gs_type" => Star::PAR_GSTYPE_DEFAULT, # 將前、歲前各星排法
               "n_flowsky_type" => Star::PAR_FLOWSKYTYPE_DEFAULT, # 流盤干支方式
               "n_flowsky_type_cald_flowyear" => Star::PAR_FLOWSKYTYPE_CALD_FLOWYEAR_DEFAULT, # 流盤干支方式 流年干支
               "n_smallsan_type" => Star::PAR_SMALLSANTYPE_DEFAULT, # 起小限規則
               "n_god_type" => Star::PAR_GODTYPE_DEFAULT, # 斗君排法,預設流年斗君
               "n_flowlife_housetype" => Star::PAR_FLOWLIFEHOUSETYPE_DEFAULT, # 流年命宮排法
               "n_giakung_type" => Star::PAR_GIAKUNGTYPE_DEFAULT, # 截空排法
               "n_fourhua_type" => Star::PAR_FOURHUATYPE_DEFAULT, # 四化星排法
               "n_flowyear_hua" => Star::PAR_FLOWYEARHUA_DEFAULT, # 流盤四化顯示方式
               "n_gs_name_type" => Star::PAR_SSNTYPE_DEFAULT, # 歲前各星名稱
               "n_par_b3132_type" => Star::PAN_B3132_DEFAULT, # 旬中旬空(空亡)
               "n_par_b30_type" => Star::PAN_B30_DEFAULT, # 截空動盤顯示 
               "n_par_b2425_type" => Star::PAN_B2425_DEFAULT, # 魁鉞動盤顯示
               "n_yuekui_type" => Star::PAR_YUEKUI_DEFAULT, # 天鉞天魁排法
               "n_par_pan_base" => Star::PAN_BASE_LIFE, # 排盤方式
               "n_par_pan_timebase" => Star::PAR_PANTIMEBASE_DEFAULT, # Star::PAN_TIME_BASE_BIRTH #流盤取時間的方法
               # 介面參數
               "n_int_34color" => Star::INT_34COLOR_DEFAULT, # 三方四正色彩顯示
               "n_int_miowong_style" => Star::INT_MIOWONGSTYLE_DEFAULT, # 顯示廟旺的方式,一般的文字或林老師的數字或不顯示
               "b_int_huaout_display" => Star::INT_HUAOUT_DISPLAY_DEFAULT, # 顯示對宮四化
               "b_int_selhua_display" => Star::INT_SELHUA_DISPLAY_DEFAULT, # 顯示自化
               "b_int_housefive_display" => Star::INT_HOUSEFIVE_DISPLAY_DEFAULT, # 顯示各宮五行
               "b_int_flowtime_display" => Star::INT_FLOWTIME_DISPLAY_DEFAULT, # 顯示命盤時間
               "b_int_ten_display" => Star::INT_TEN_DISPLAY_DEFAULT, # 顯示十年大運
               "b_int_small_display" => Star::INT_SMALL_DISPLAY_DEFAULT, # 顯示小限時間
               "b_int_8words_display" => Star::INT_8WORDS_DISPLAY_DEFAULT, # 顯示八字時間
               "b_int_lifeline_display" => Star::INT_LIFELINE_DISPLAY_DEFAULT, # 標示命宮三方四正
               "b_int_smallyear_revert_display" => Star::INT_SMALLYEAR_REVERT_DISPLAY_DEFAULT, # 小限應期顯示
               # 星曜顯示設定
               "a_dis_astar" => [1..Star::A_STAR_COUNT], # 甲級星曜設定
               "a_dis_bstar" => [1..Star::B_STAR_COUNT], # 乙、丙級星曜設定
               "n_dis_flowlyt" => Star::DIS_FLOWLYT_DEFAULT, # 流祿，流羊，流陀
               "n_dis_flowma" => Star::DIS_FLOWMA_DEFAULT, # 流馬
               "n_dis_flowluan" => Star::DIS_FLOWLUAN_DEFAULT, # 流鸞
               "b_dis_doctor" => Star::DIS_DOCTOR_DEFAULT, # 顯示博士十二星
               "b_dis_live" => Star::DIS_LIVE_DEFAULT, # 顯示長生十二星
               "b_dis_yearstar" => Star::DIS_YEARSTAR_DEFAULT, # 顯示將星諸星
               "b_dis_yeargod" => Star::DIS_YEARGOD_DEFAULT, # 顯示歲前諸星
               "b_dis_flowchan" => Star::DIS_FLOWCHAN_DEFAULT, # 顯示流昌
               "b_dis_skycook" => Star::DIS_SKYCOOK_DEFAULT, # 顯示天廚星
               "b_dis_lyyg" => Star::DIS_LYYG_DEFAULT, # 顯示來因業障
               "b_dis_7star" => Star::DIS_7STAR_DEFAULT, # 七星

               "b_end" => 0 # ending
             }

  def Xuexi_Ziwei.pan_par_assign(hIn,userId,hUserTypeIn=nil)
    hOutput = Hash.new
    hUserType = hUserTypeIn
    if (hUserTypeIn == nil) then
      hUserType = @@ApParDefault
    end
    if (hIn == nil) then
      hInput = nil #Hash.new
    else
      hInput = hIn
    end

    hUserType.each {|key,value|
      hOutput[key] = Cfate.ParInHashValueCheck(hInput,key,hUserType)
      if (key == "a_dis_astar") then
        hOutput = Xuexi_Ziwei.HashStarsAssign(Star::DIS_ASTAR,hOutput,hOutput[key],Star::A_STAR_COUNT)
      elsif (key == "a_dis_bstar") then
        hOutput = Xuexi_Ziwei.HashStarsAssign(Star::DIS_BSTAR,hOutput,hOutput[key],Star::B_STAR_COUNT)
      elsif (key[1] == "_") then
        hOutput[key[2,key.length - 2]] = hOutput[key]
      end
    }

    return hOutput
  end
  def Xuexi_Ziwei.pan_par_assign_each(nPanPar,hUserType,hInput)
    return Xuexi_Ziwei.pan_par_assign(hInput,nil,hUserType)
  end

  def Xuexi_Ziwei.pan_par_init_each(nPanPar,hUserType)
    return Xuexi_Ziwei.pan_par_assign_each(nPanPar,hUserType,nil)
  end
  def Xuexi_Ziwei.HashStarsAssign(sKey,hUserType,star_arr,nCount)
    (1..nCount).each do |nStar|
      hUserType['#{sKey}_#{nStar}'] = star_arr.include?(nStar)
    end
    return hUserType
  end
end
