# 練習
class Xuex<PERSON>_Ziwei
  def Xuexi_Ziwei.lianxi_data(hPars,xuexi_class,xuexi_item,client_ip,oStar,nPanType)
    hPars["timestamp"] = "#{oStar.uig_W_timestamp()}#{oStar.uig_bS_Ui_Val()}"
    s = "Xuexi_Ziwei.timu_data_#{xuexi_class}_#{xuexi_item}(hPars,xuexi_class,xuexi_item,oStar,nPanType)"
    h = eval(s)
    # h["lianxi_timestamp"] = Xdate.make_now_timestamp()

    # 存到暫時資料庫，待回頭取出來判斷
    h["lianxi_id"] = Pm.saveTestDb2("Xuexi_Ziwei.lianxi_data",h)
    return h
  end
  def Xuexi_Ziwei.lianxi_jieguo(hPars)
    lianxi_id = hPars["lianxi_id"].to_i
    answers = Xuexi_Ziwei.parse_answers(hPars["answer"])
    h = Pm.findTestDbValue(lianxi_id)

    if (h == nil) then
      return "error"
    else
      timu = h["timu_api"]
      func = "Xuexi_Ziwei.#{timu}_jieguo(h,answers)"

      result,ans,right_xuaxion = eval(func)
      return Xuexi_Ziwei.lianxi_jieguo_response(result,ans,right_xuaxion)
    end
  end

  def Xuexi_Ziwei.lianxi_const_data(xuexi_class,xuexi_item,oStar,nPanType)
    s = "Xuexi_Ziwei.lianxi_const_data_#{xuexi_class}(oStar,nPanType)"
    return eval(s)
  end
  def Xuexi_Ziwei.lianxi_const_data_1(oStar,nPanType)
    return {}
  end

  def Xuexi_Ziwei.lianxi_jieguo_response(result,ans,right_xuaxions)
    if (result) then
      # 答對
      return Pm.t("xuexi_ziwei.jieguo.dadui")
    else
      # 答錯
      return Pm.t("xuexi_ziwei.jieguo.dacuo",:q1 => ans.sort, :q2 => right_xuaxions.sort)
    end
  end

end
