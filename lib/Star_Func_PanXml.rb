# Class for PanXml
class Star
  def gHouse_GetAStarCount(nPanType,nEarth)
    starArray = get_HouseFullAStarInfos(nPanType,nEarth)
    return starArray.length
  end

  def gHouse_GetAStarName_Each(nPanType,nEarth,nIndex)
    starArray = get_HouseFullAStarInfos(nPanType,nEarth)
    sBuf = starArray[nIndex][0]
    return sBuf
  end

  def gHouse_GetBStarCount(nPanType,nEarth)
    return get_HouseBStarLength(nPanType,nEarth)
  end

  def gHouse_GetBStarName_Each(nPanType,nEarth,nIndex)
    return get_HouseBStarName(nPanType,nEarth,nIndex)
  end

end
