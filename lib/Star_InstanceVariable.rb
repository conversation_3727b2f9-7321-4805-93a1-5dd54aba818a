
class Star
  def yearDisplay(nYear)
    return Xdate.YearDisplay(nYear,@par_YearDisplay)
  end
  def yearDisplayOnlyNumber(nYear)
    return Xdate.YearDisplayOnlyNumber(nYear,@par_YearDisplay)
  end

  def GetWYearMonthDateStr(nWYear,nWMonth,nWDate)
    return Xdate.GetWYearMonthDateStr_WC(nWYear,nWMonth,nWDate,@par_YearDisplay)
  end

  def GetYearStr(nYear)
    return Xdate.GetYearStr_WC(nYear,@par_YearDisplay)
  end


#排盤部分
  @m_nPanType         #排盤類別

  @m_nUserDefinedDate
  def ddg_all()
    return @m_nUserDefinedDate.clone
  end
  def dds_E_YMDLT(y,m,d,l,t)
    dds_EY(y)
    dds_EM(m)
    dds_ED(d)
    dds_EL(l)
    dds_ET(t)
  end
  def ddg_E_YMDLT()
    return ddg_EY(),ddg_EM(),ddg_ED(),ddg_EL(),ddg_ET()
  end
  def dds_EY(nVal) return @m_nUserDefinedDate[Cfate::EYear] = nVal end
  def dds_EM(nVal) return @m_nUserDefinedDate[Cfate::EMonth] = nVal end
  def dds_ED(nVal) return @m_nUserDefinedDate[Cfate::EDate] = nVal end
  def dds_ET(nVal) return @m_nUserDefinedDate[Cfate::ETime] = nVal end
  def dds_EMI(nVal) return @m_nUserDefinedDate[Cfate::EMinute] = nVal end
  def dds_EL(nVal) return @m_nUserDefinedDate[Cfate::LeapMonth] = nVal end

  def ddg_EY() return @m_nUserDefinedDate[Cfate::EYear] end
  def ddg_EM() return @m_nUserDefinedDate[Cfate::EMonth] end
  def ddg_ED() return @m_nUserDefinedDate[Cfate::EDate] end
  def ddg_ET() return @m_nUserDefinedDate[Cfate::ETime] == nil ? 0 : @m_nUserDefinedDate[Cfate::ETime] end
  def ddg_EMI() return @m_nUserDefinedDate[Cfate::EMinute] == nil ? 0 : @m_nUserDefinedDate[Cfate::EMinute] end
  def ddg_EL() return @m_nUserDefinedDate[Cfate::LeapMonth] end

  def dds_WY(nVal) return @m_nUserDefinedDate[Cfate::WYear] = nVal end
  def dds_WM(nVal) return @m_nUserDefinedDate[Cfate::WMonth] = nVal end
  def dds_WD(nVal) return @m_nUserDefinedDate[Cfate::WDate] = nVal end
  def dds_WH(nVal) return @m_nUserDefinedDate[Cfate::WHour] = nVal end
  def dds_WMI(nVal) return @m_nUserDefinedDate[Cfate::WMinute] = nVal end

  def ddg_WY() return @m_nUserDefinedDate[Cfate::WYear] end
  def ddg_WM() return @m_nUserDefinedDate[Cfate::WMonth] end
  def ddg_WD() return @m_nUserDefinedDate[Cfate::WDate] end
  def ddg_WH() return @m_nUserDefinedDate[Cfate::WHour] == nil ? 0 : @m_nUserDefinedDate[Cfate::WHour] end
  def ddg_WMI() return @m_nUserDefinedDate[Cfate::WMinute] == nil ? 0 : @m_nUserDefinedDate[Cfate::WMinute] end
  def ddg_CurrentDate() return @m_nUserDefinedDate["CurrentDate"] end

  # @m_nFlowDate[Cfate::PAN_CNT]        #流年時間Hash
  def fds_E_YMDL(nPanType,y,m,d,l)
    fds_EY(nPanType,y)
    fds_EM(nPanType,m)
    fds_ED(nPanType,d)
    fds_EL(nPanType,l)
  end
  def fds_E_YMDLT(nPanType,y,m,d,l,t)
    fds_EY(nPanType,y)
    fds_EM(nPanType,m)
    fds_ED(nPanType,d)
    fds_EL(nPanType,l)
    fds_ET(nPanType,t)
  end
  def fdg_E_YMDLT(nPanType)
    return fdg_EY(nPanType),fdg_EM(nPanType),fdg_ED(nPanType),fdg_EL(nPanType),fdg_ET(nPanType)
  end
  def fdg_E_YMDLT2(nPanType)
    return fdg_EY2(nPanType),fdg_EM2(nPanType),fdg_ED2(nPanType),fdg_EL2(nPanType),fdg_ET2(nPanType)
  end
  def fdg_E_YMDL(nPanType)
    return fdg_EY(nPanType),fdg_EM(nPanType),fdg_ED(nPanType),fdg_EL(nPanType)
  end
  def fdg_E_YMDL2(nPanType)
    return fdg_EY2(nPanType),fdg_EM2(nPanType),fdg_ED2(nPanType),fdg_EL2(nPanType)
  end
  #@m_nFlowEYear       #流年農曆年
  def check_fd_exist(nPanType)
    if (@m_nFlowDate[nPanType][Cfate::EYear] == nil) then
      setFlowTimeInfo(nPanType)
    end
  end
  def fdg_EY(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::EYear]
  end
  def fdg_EY2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::EYear]
  end
  def fds_EY(nPanType,nVal) @m_nFlowDate[nPanType][Cfate::EYear] = nVal end
  #@m_nFlowEMonth      #流年農曆月
  def fdg_EM(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::EMonth]
  end
  def fdg_EM2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::EMonth]
  end
  def fds_EM(nPanType,nVal) @m_nFlowDate[nPanType][Cfate::EMonth] = nVal end
  #䦌月依䦌月排法調整之月份，影響右弼，左輔之值
  def fdg_ELM(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::ELeapMonth]
  end
  def fdg_ELM2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::ELeapMonth]
  end
  def fds_ELM(nPanType,nVal) @m_nFlowDate[nPanType][Cfate::ELeapMonth] = nVal end
  #@m_nFlowEDate       #流年農曆日
  def fdg_ED(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::EDate]
  end
  def fdg_ED2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::EDate]
  end
  def fds_ED(nPanType,nVal) @m_nFlowDate[nPanType][Cfate::EDate] = nVal end
  #@m_nFlowETime        #流年時
  def fdg_ET(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::ETime]
  end
  def fdg_ET2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::ETime]
  end
  def fds_ET(nPanType,nVal) @m_nFlowDate[nPanType][Cfate::ETime] = nVal end
  #@m_nFlowEMinute        #流年分
  def fdg_EMI(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::EMinute]
  end
  def fdg_EMI2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::EMinute]
  end
  def fds_EMI(nPanType,nVal) @m_nFlowDate[nPanType][Cfate::EMinute] = nVal end
  #@m_bFlowLeap        #流年是否潤月
  def fdg_EL(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::LeapMonth]
  end
  def fdg_EL2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::LeapMonth]
  end
  def fds_EL(nPanType,nVal) @m_nFlowDate[nPanType][Cfate::LeapMonth] = nVal end

  def fds_W_YMDHm(nPanType,y,m,d,h,min)
    fds_WY(nPanType,y)
    fds_WM(nPanType,m)
    fds_WD(nPanType,d)
    fds_WH(nPanType,h)
    fds_WMI(nPanType,min)
  end
  def fdg_W_YMDH(nPanType)
    return fdg_WY(nPanType),fdg_WM(nPanType),fdg_WD(nPanType),fdg_WH(nPanType)
  end
  def fdg_W_YMDHm(nPanType)
    return fdg_WY(nPanType),fdg_WM(nPanType),fdg_WD(nPanType),fdg_WH(nPanType),fdg_WMI(nPanType)
  end
  def fdg_W_YMDHm2(nPanType)
    return fdg_WY2(nPanType),fdg_WM2(nPanType),fdg_WD2(nPanType),fdg_WH2(nPanType),fdg_WMI2(nPanType)
  end
  def fds_W_YMD(nPanType,y,m,d)
    fds_WY(nPanType,y)
    fds_WM(nPanType,m)
    fds_WD(nPanType,d)
  end
  def fdg_W_YMDT(nPanType)
    return fdg_WY(nPanType),fdg_WM(nPanType),fdg_WD(nPanType),fdg_ET(nPanType)
  end
  def fdg_W_YMDT2(nPanType)
    return fdg_WY2(nPanType),fdg_WM2(nPanType),fdg_WD2(nPanType),fdg_ET2(nPanType)
  end
  def fdg_W_YMD(nPanType)
    return fdg_WY(nPanType),fdg_WM(nPanType),fdg_WD(nPanType)
  end
  def fdg_W_YMD2(nPanType)
    return fdg_WY2(nPanType),fdg_WM2(nPanType),fdg_WD2(nPanType)
  end
  #@m_nFlowWYear       #流年西曆年
  def fdg_WY(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::WYear]
  end
  def fdg_WY2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::WYear]
  end
  def fds_WY(nPanType,nVal) @m_nFlowDate[nPanType][Cfate::WYear] = nVal end
  #@m_nFlowWMonth      #流年西曆月
  def fdg_WM(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::WMonth]
  end
  def fdg_WM2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::WMonth]
  end
  def fds_WM(nPanType,nVal) @m_nFlowDate[nPanType][Cfate::WMonth] = nVal end
  #@m_nFlowWDate       #流年西曆日
  def fdg_WD(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::WDate]
  end
  def fdg_WD2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::WDate]
  end
  def fds_WD(nPanType,nVal) @m_nFlowDate[nPanType][Cfate::WDate] = nVal end

  def fdg_WT(nPanType)
    check_fd_exist(nPanType)
    return (Xdate.Hour2WTime(fdg_WH(nPanType)))
  end
  def fdg_WT2(nPanType)
    check_fd_exist(nPanType)
    return (Xdate.Hour2WTime(fdg_WH(nPanType)))
  end
  def fds_WH(nPanType,nVal) return @m_nFlowDate[nPanType][Cfate::WHour] = nVal end
  def fdg_WH(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::WHour]
  end
  def fdg_WH2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::WHour]
  end
  #@m_nFlowWMin         #流年分
  def fdg_WMI(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::WMinute]
  end
  def fdg_WMI2(nPanType)
    check_fd_exist(nPanType)
    return @m_nFlowDate[nPanType][Cfate::WMinute]
  end
  def fds_WMI(nPanType,nVal) @m_nFlowDate[nPanType][Cfate::WMinute] = nVal end

  #目前時間
  @m_nCurDate
  def cds_W_YMDH(y,m,d,h)
    cds_WY(y)
    cds_WM(m)
    cds_WD(d)
    cds_WH(h)
  end
  def cds_E_YMDL(y,m,d,l)
    cds_EY(y)
    cds_EM(m)
    cds_ED(d)
    cds_EL(l)
  end
  def cds_E_YMDLT(y,m,d,l,t)
    cds_E_YMDL(y,m,d,l)
    cds_ET(t)
  end
  def cdg_E_YMDLT()
    return cdg_EY(),cdg_EM(),cdg_ED(),cdg_EL(),cdg_ET()
  end
  def cdg_W_YMDH()
    return cdg_WY(),cdg_WM(),cdg_WD(),cdg_WH()
  end
  # @m_nCurEYear        #目前農曆年
  def cdg_EY() return @m_nCurDate[Cfate::EYear] end
  def cds_EY(nVal) @m_nCurDate[Cfate::EYear] = nVal end
  # @m_nCurEMonth       #目前農曆月
  def cdg_EM() return @m_nCurDate[Cfate::EMonth] end
  def cds_EM(nVal) @m_nCurDate[Cfate::EMonth] = nVal end
  # @m_nCurEDate        #目前農曆日
  def cdg_ED() return @m_nCurDate[Cfate::EDate] end
  def cds_ED(nVal) @m_nCurDate[Cfate::EDate] = nVal end
  # @m_nCurWYear        #目前西曆年
  def cdg_WY() return @m_nCurDate[Cfate::WYear] end
  def cds_WY(nVal) @m_nCurDate[Cfate::WYear] = nVal end
  # @m_nCurWMonth       #目前西曆月
  def cdg_WM() return @m_nCurDate[Cfate::WMonth] end
  def cds_WM(nVal) @m_nCurDate[Cfate::WMonth] = nVal end
  # @m_nCurWDate        #目前西曆日
  def cdg_WD() return @m_nCurDate[Cfate::WDate] end
  def cds_WD(nVal) @m_nCurDate[Cfate::WDate] = nVal end
  # @m_nCurETime        #目前時
  def cdg_ET() return @m_nCurDate[Cfate::ETime] end
  def cds_ET(nVal) @m_nCurDate[Cfate::ETime] = nVal end
  # @m_nCurHour         #24小時制
  def cdg_WH() return @m_nCurDate[Cfate::WHour] end
  def cds_WH(nVal) @m_nCurDate[Cfate::WHour] = nVal end
  def cdg_WMI() return @m_nCurDate[Cfate::WMinute] end
  def cds_WMI(nVal) @m_nCurDate[Cfate::WMinute] = nVal end
  # @m_nCurMinute       #目前分
  def cdg_EMI() return @m_nCurDate[Cfate::EMinute] end
  def cds_EMI(nVal) @m_nCurDate[Cfate::EMinute] = nVal end
  # @m_bCurLeapMonth    #目前是否潤月
  def cdg_EL() return @m_nCurDate[Cfate::LeapMonth] end
  def cds_EL(nVal) @m_nCurDate[Cfate::LeapMonth] = nVal end

  #流盤使用者設定時間 Hash
  @m_nPanDate
  def pdg_E_YMDLT()
    return pdg_EY(),pdg_EM(),pdg_ED(),pdg_EL(),pdg_ET()
  end
  def pdg_W_YMDH()
    return pdg_WY(),pdg_WM(),pdg_WD(),pdg_WH()
  end
  # @m_nPanEYear        #目前農曆年
  def pdg_EY() return @m_nPanDate[Cfate::EYear] end
  def pds_EY(nVal) @m_nPanDate[Cfate::EYear] = nVal end
  # @m_nPanEMonth       #目前農曆月
  def pdg_EM() return @m_nPanDate[Cfate::EMonth] end
  def pds_EM(nVal) @m_nPanDate[Cfate::EMonth] = nVal end
  # @m_nPanEDate        #目前農曆日
  def pdg_ED() return @m_nPanDate[Cfate::EDate] end
  def pds_ED(nVal) @m_nPanDate[Cfate::EDate] = nVal end
  # @m_nPanWYear        #目前西曆年
  def pdg_WY() return @m_nPanDate[Cfate::WYear] end
  def pds_WY(nVal) @m_nPanDate[Cfate::WYear] = nVal end
  # @m_nPanWMonth       #目前西曆月
  def pdg_WM() return @m_nPanDate[Cfate::WMonth] end
  def pds_WM(nVal) @m_nPanDate[Cfate::WMonth] = nVal end
  # @m_nPanWDate        #目前西曆日
  def pdg_WD() return @m_nPanDate[Cfate::WDate] end
  def pds_WD(nVal) @m_nPanDate[Cfate::WDate] = nVal end
  # @m_nPanETime        #目前時
  def pdg_ET() return @m_nPanDate[Cfate::ETime] end
  def pds_ET(nVal) @m_nPanDate[Cfate::ETime] = nVal end
  # @m_nPanHour         #24小時制
  def pdg_WH() return @m_nPanDate[Cfate::WHour] end
  def pds_WH(nVal) @m_nPanDate[Cfate::WHour] = nVal end
  # @m_nPanMinute       #目前分
  def pdg_WMI() return @m_nPanDate[Cfate::WMinute] end
  def pds_WMI(nVal) @m_nPanDate[Cfate::WMinute] = nVal end
  # @m_bPanLeapMonth    #目前是否潤月
  def pdg_EL() return @m_nPanDate[Cfate::LeapMonth] end
  def pds_EL(nVal) @m_nPanDate[Cfate::LeapMonth] = nVal end

  #個人資訊
  @m_UserInfo #個人資訊 hash
  def uig_all()
    return @m_UserInfo.clone
  end
  def uig_FlowAge(nPanType=nil) fdg_EY(nPanType) - uig_EY() + 1 end
  def uig_Name()
    return @m_UserInfo[Cfate::Name]
  end

  def uis_E_YMDL(y,m,d,l)
    uis_EY(y)
    uis_EM(m)
    uis_ED(d)
    uis_EL(l)
  end
  def uis_E_YMDLT(y,m,d,l,t)
    uis_E_YMDL(y,m,d,l)
    uis_ET(t)
  end

  def uig_E_YMDLT()
    return uig_EY(),uig_EM(),uig_ED(),uig_EL(),uig_ET()
  end
  def uig_E_timestamp()
    return "%04d%02d%02d%02d%01d" % [uig_EY(),uig_EM(),uig_ED(),uig_ET(),uig_EL()]
  end

  #    1963年　1月　30日　寅時
  def uig_E_BirthDate_Str()
      y,m,d,l = Xdate.West2East(uig_WY_B(),uig_WM_B(),uig_WD_B())
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(y)
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += Cfate.GetSpace(1)
    if (l) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += m.to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += Cfate.GetSpace(1)
    sBuf += d.to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET_B()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_E_BirthDate_ganzhi_Str()
      y,m,d,l = Xdate.West2East(uig_WY_B(),uig_WM_B(),uig_WD_B())
    # sBuf = Cfate.GetSpace(1)
    sBuf = yearDisplay(y)
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    # sBuf += Cfate.GetSpace(1)
    if (l) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += m.to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    # sBuf += Cfate.GetSpace(1)
    sBuf += d.to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET_B()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_E_BirthDate_Str_old()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_EY_B())
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += Cfate.GetSpace(1)
    if (uig_EL_B()) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += uig_EM_B().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_ED_B().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET_B()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end

  def uig_E_BirthDate_Str2_old()
      y,m,d,l = Xdate.West2East(uig_WY_B(),uig_WM_B(),uig_WD_B())
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(y)
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    if (l) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += m.to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += d.to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET_B()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_E_BirthDate_Str2()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_EY_B())
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    if (uig_EL_B()) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += uig_EM_B().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_ED_B().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET_B()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_E_BirthDate_Str3()
    sBuf = yearDisplay(uig_EY_B())
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    if (uig_EL_B()) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += uig_EM_B().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_ED_B().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
  end

  # 真太陽時
  def uig_E_RealSunTime_Str()
      y,m,d,l = Xdate.West2East(uig_WY(),uig_WM(),uig_WD())
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(y)
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += Cfate.GetSpace(1)
    if (l) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += m.to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += Cfate.GetSpace(1)
    sBuf += d.to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_E_RealSunTime_ganzhi_Str()
      y,m,d,l = Xdate.West2East(uig_WY(),uig_WM(),uig_WD())
    # sBuf = Cfate.GetSpace(1)
    sBuf = yearDisplay(y)
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    # sBuf += Cfate.GetSpace(1)
    if (l) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += m.to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    # sBuf += Cfate.GetSpace(1)
    sBuf += d.to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_E_RealSunTime_Str_old()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_EY())
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += Cfate.GetSpace(1)
    if (uig_EL()) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += uig_EM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_ED().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end

  def uig_E_RealSunTime_Str2_old()
      y,m,d,l = Xdate.West2East(uig_WY(),uig_WM(),uig_WD())
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(y)
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    if (l) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += m.to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += d.to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_E_RealSunTime_Str2()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_EY())
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    if (uig_EL()) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += uig_EM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_ED().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Earth.GetName(Earth.EarthIndex2Earth(uig_ET()))
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_E_RealSunTime_Str3()
    sBuf = yearDisplay(uig_EY())
    #sBuf += uig_EY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    if (uig_EL()) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += uig_EM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_ED().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
  end

  #出生年(農曆) 真太陽時
  def uig_EY()
    return @m_UserInfo[Cfate::EYear]
  end
  def uis_EY(nVal)
    @m_UserInfo[Cfate::EYear] = nVal
  end
  #出生月(農曆)
  def uig_EM() return @m_UserInfo[Cfate::EMonth] end
  def uis_EM(nVal) @m_UserInfo[Cfate::EMonth] = nVal end
  #出生日(農曆)
  def uig_ED() return @m_UserInfo[Cfate::EDate] end
  def uis_ED(nVal) @m_UserInfo[Cfate::EDate] = nVal end
  #出生時(地支時，十二個時辰)
  def uig_ET() return @m_UserInfo[Cfate::ETime] end
  def uis_ET(nVal) @m_UserInfo[Cfate::ETime] = nVal end
  #出生分(農曆)
  def uig_EMI() return @m_UserInfo[Cfate::EMinute] end
  def uis_EMI(nVal) @m_UserInfo[Cfate::EMinute] = nVal end
  #出生月是否為潤月
  def uig_EL() return @m_UserInfo[Cfate::LeapMonth] end
  def uis_EL(nVal) @m_UserInfo[Cfate::LeapMonth] = nVal end

  #䦌月依䦌月排法調整之月份，影響右弼，左輔之值
  def uig_ELM()
    return cp_GetBirthMonth()
  end

  def uis_W_YMD(y,m,d)
    uis_WY(y)
    uis_WM(m)
    uis_WD(d)
  end
  def uig_W_YMDH()
    return uig_WY(),uig_WM(),uig_WD(),uig_WH()
  end
  def uig_W_timestamp()
    return Xdate.make_timestamp(uig_WY(),uig_WM(),uig_WD(),uig_WH())
  end

  def uig_GanZhi_Birth_Str()
    return Xdate.GetLunar8Words_Str(uig_WY_B(), uig_WM_B(),uig_WD_B(), Xdate.Hour2ETime(uig_WH_B())) #uig_ET())
  end
  # 真太陽時
  def uig_GanZhi_RealSunTime_Str()
    return Xdate.GetLunar8Words_Str(uig_WY(), uig_WM(),uig_WD(), Xdate.Hour2ETime(uig_WH())) #uig_ET())
  end

  def uig_Seg8Words_Str(bSegment=false,nFirstTimeType=Xdate::FIRST_NEXT,nFirstSegment=Xdate::SEGMENT_SPRING,bSpace=true,nSegmentMove=Xdate::SegmentNow)
    return Xdate.GetSeg8Words_Str(uig_WY(), uig_WM(),uig_WD(), uig_WH(),uig_WMI(),bSegment,nFirstTimeType,nFirstSegment,bSpace,nSegmentMove)
  end

  #　　　1963年　2月　23日　4時
  def uig_W_BirthDate_Str()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_WY_B())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_WM_B().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_WD_B().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_WH_B().to_s
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end

  def uig_W_BirthDate_Str2()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_WY_B())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += uig_WM_B().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_WD_B().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += uig_WH_B().to_s
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_W_BirthDate_Str3()
    sBuf = yearDisplay(uig_WY_B())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += uig_WM_B().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_WD_B().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
  end

  def uig_W_BirthDate_ganzhishi_Str()
    # sBuf = Cfate.GetSpace(1)
    sBuf = yearDisplay(uig_WY_B())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    # sBuf += Cfate.GetSpace(1)
    sBuf += uig_WM_B().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    # sBuf += Cfate.GetSpace(1)
    sBuf += uig_WD_B().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += Xdate.GetETimeStr(uig_ET_B())
  end

  # 真太陽時
  def uig_W_RealSunTime_Str()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_WY())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_WM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_WD().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += uig_WH().to_s
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end

  def uig_W_RealSunTime_Str2()
    sBuf = Cfate.GetSpace(1)
    sBuf += yearDisplay(uig_WY())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += uig_WM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_WD().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += uig_WH().to_s
    sBuf += Pm.GetStr("IDS_X_HOUR")
  end
  def uig_W_RealSunTime_Str3()
    sBuf = yearDisplay(uig_WY())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += uig_WM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += uig_WD().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
  end

  def uig_W_RealSunTime_ganzhishi_Str()
    # sBuf = Cfate.GetSpace(1)
    sBuf = yearDisplay(uig_WY())
    #sBuf += uig_WY().to_s
    sBuf += Pm.GetStr("IDS_X_YEAR")
    # sBuf += Cfate.GetSpace(1)
    sBuf += uig_WM().to_s
    sBuf += Pm.GetStr("IDS_X_MONTH")
    # sBuf += Cfate.GetSpace(1)
    sBuf += uig_WD().to_s
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Cfate.GetSpace(1)
    sBuf += Xdate.GetETimeStr(uig_ET())
  end

  #出生年(西曆) 真太陽時
  def uig_WY() return @m_UserInfo[Cfate::WYear] end
  def uis_WY(nVal) @m_UserInfo[Cfate::WYear] = nVal end
  #出生月(西曆)
  def uig_WM() return @m_UserInfo[Cfate::WMonth] end
  def uis_WM(nVal) @m_UserInfo[Cfate::WMonth] = nVal end
  #出生日(西曆)
  def uig_WD() return @m_UserInfo[Cfate::WDate] end
  def uis_WD(nVal) @m_UserInfo[Cfate::WDate] = nVal end
  #出生時(西曆)
  def uig_WH() return @m_UserInfo[Cfate::WHour] end
  def uis_WH(nVal) @m_UserInfo[Cfate::WHour] = nVal end
  #出生分(西曆)
  def uig_WMI()
    nMinute = @m_UserInfo[Cfate::WMinute] == nil ? 0 : @m_UserInfo[Cfate::WMinute]
    return  nMinute
  end
  def uis_WMI(nVal) @m_UserInfo[Cfate::WMinute] = nVal end
  #性別 true 是女性,false是男性
  def uig_bS_Val() return (uig_bS() ? 1 : 0) end
  def uig_bS() return @m_UserInfo[Cfate::Sex] end
  def uis_bS(nVal) @m_UserInfo[Cfate::Sex] = nVal end
    # 在網頁中，1代表男性（在此為false)，0代表女性(在此為true)
  def uig_bS_Ui_Val() return (uig_bS() ? 0 : 1) end
  def uig_bS_str() return (Pm.GetStr("IDS_S_CUST_MF_#{uig_bS_Ui_Val()}")) end
  #0為陽,1為陰(陰男,陽男,陰女,陽女用)
  def uig_nSIY() return @m_UserInfo[Cfate::SexInYang] end
  def uis_nSIY(nVal) @m_UserInfo[Cfate::SexInYang] = nVal end

  # 原始出生日，uis_WY等已經改成真太陽時
  def uig_EY_B()
    return @m_UserInfo[Cfate::EYear_B]
  end
  def uis_EY_B(nVal) @m_UserInfo[Cfate::EYear_B] = nVal end
  #出生月(農曆)
  def uig_EM_B() return @m_UserInfo[Cfate::EMonth_B] end
  def uis_EM_B(nVal) @m_UserInfo[Cfate::EMonth_B] = nVal end
  #出生日(農曆)
  def uig_ED_B() return @m_UserInfo[Cfate::EDate_B] end
  def uis_ED_B(nVal) @m_UserInfo[Cfate::EDate_B] = nVal end
  #出生時(地支時，十二個時辰)
  def uig_ET_B() return @m_UserInfo[Cfate::ETime_B] end
  def uis_ET_B(nVal) @m_UserInfo[Cfate::ETime_B] = nVal end
  #出生分(農曆)
  def uig_EMI_B() return @m_UserInfo[Cfate::EMinute_B] end
  def uis_EMI_B(nVal) @m_UserInfo[Cfate::EMinute_B] = nVal end
  #出生月是否為潤月
  def uig_EL_B() return @m_UserInfo[Cfate::LeapMonth_B] end
  def uis_EL_B(nVal) @m_UserInfo[Cfate::LeapMonth_B] = nVal end
  def uig_WY_B() return @m_UserInfo[Cfate::WYear_B] end
  def uis_WY_B(nVal) @m_UserInfo[Cfate::WYear_B] = nVal end
  #出生月(西曆)
  def uig_WM_B() return @m_UserInfo[Cfate::WMonth_B] end
  def uis_WM_B(nVal) @m_UserInfo[Cfate::WMonth_B] = nVal end
  #出生日(西曆)
  def uig_WD_B() return @m_UserInfo[Cfate::WDate_B] end
  def uis_WD_B(nVal) @m_UserInfo[Cfate::WDate_B] = nVal end
  #出生時(西曆)
  def uig_WH_B() return @m_UserInfo[Cfate::WHour_B] end
  def uis_WH_B(nVal) @m_UserInfo[Cfate::WHour_B] = nVal end
  def uig_WMI_B()
    nMinute = @m_UserInfo[Cfate::WMinute_B] == nil ? 0 : @m_UserInfo[Cfate::WMinute_B]
    return  nMinute
  end
  def uis_WMI_B(nVal) @m_UserInfo[Cfate::WMinute_B] = nVal end

  def uig_Multiple_births() return @m_UserInfo[Cfate::Multiple_births] end
  def uis_Multiple_births(nVal) @m_UserInfo[Cfate::Multiple_births] = nVal end
  # 多胞胎分二大類，一是同卵 S，二是異卵 D
  # 同時間，同卵雙胞胎老大依命宮為主，老二則以福德宮為命宮，老三則是身宮為命宮。
  # 同時間異卵，老大命宮為主，老二兄弟宮為命宮，老三夫妻宮為命宮，老四子女宮為命宮，老五財帛宮為命宮。
  # 計算時按原命盤取不同之宮為命宮，再計算其STI分數。
  def uig_Multiple_births_Check() 
    b = uig_Multiple_births()
    if (b == nil || b == "") then
      return false 
    end
    if (["S","s","D","d"].include?(b[0])) then
      return true 
    end
    return false
  end
  def uig_mb_change_houseid(nHouseId,nBody_HouseId)
    # 判斷UserDefData中的參數，是否啟用多胞胎更換，只有計算分數時才用
    b = uig_Multiple_births_Check()
    return nHouseId if !b

    mb = uig_Multiple_births()
    n = mb[1].to_i
    if (mb[0] == "S" || mb[0] == "s") then
      return nHouseId if n == nil || n == 0
      return nHouseId if n == 1
      return Earth.ModifyEarth(nHouseId - 11 + 1) if n == 2  # 老二
      return Earth.ModifyEarth(nHouseId - nBody_HouseId + 1) if n == 3 # 老三
    elsif (mb[0] == "D" || mb[0] == "d") then
      # D 異卵
      return nHouseId if n == nil || n == 0
      return nHouseId if n == 1
      return Earth.ModifyEarth(nHouseId - 2 + 1) if n == 2  # 老二
      return Earth.ModifyEarth(nHouseId - 3 + 1) if n == 3 # 老三
      return Earth.ModifyEarth(nHouseId - 4 + 1) if n == 4 # 老四
      return Earth.ModifyEarth(nHouseId - 5 + 1) if n == 5 # 老五
      return Earth.ModifyEarth(nHouseId - n + 1) if n > 5 # 老六以後
    end
    return nHouseId
  end

  @m_nLargeSan #目前的大限


  #四化資訊
  @m_dwHua_User   # 客戶自訂四化

  # 某人的紫微資訊
  @StarInfo     # 包含 以下及@AllPanInfo
    #本命資訊
    #h[Star::U_LIFE]    @m_nLife    #本命命宮所在地支位置(以地支表示)
    #h[Star::U_GOD]    @m_nGod     #本命子年斗君所在地支位置(以地支表示)
    #h[Star::U_BODY]    @m_nBody    #本命身宮所在地支位置(以地支表示)
    #h[Star::U_SKY]    @m_nSky     #本命(出生年)天干
    #h[Star::U_EARTH]    @m_nEarth   #本命(出生年)地支
    #h[Star::U_FIVE]   @m_nFive    #本命五行局(2~6)
    #h[Star::U_SMALL]  @m_nSmall   #小限Array
    #h[Star::U_LARGE]       @m_nLarge   #大限Array
    # 各盤資訊
    #h[Star::U_PAN_INFO]       @AllPanInfo   #各盤資訊

  # 各盤資訊
  # A = @AllPanInfo[Cfate::PAN_CNT] of @PanInfo
  @AllPanInfo

    # @PanInfo  #各宮資訊  @PanInfo == @AllPanInfo[@m_nPanType]中
    #身宮位置,sEarthIndex = @PanInfo[Star::BODY],
    #各宮資訊
      #用Hash來存放各宮資料,以地支為索引,例寅宮為 h = @PanInfo[Star::EARTH_3]
        #每一個宮位傳回的資料也是Hash
        #宮名,aHouse = h[Star::HOUSE] 取得一個Arry of house [宮名,宮索引名]
        #甲級星,hAStar = h[Star::A_STAR] 取得一個Array of AStars Array[星名,星索引名]
        #乙級星,hBStar = h[Star::B_STAR] 取得一個Array of BStars Array[星名,星索引名]
        #十二長生,hCStar = h[Star::GOD] 取得一個 CStars
        #博士星,sDoctor = h[Star::DOCTOR] 取得一個Doctor Star
        #歲建星,sYearGods = h[Star::YEARGOD] 取得一個 YEARGOD Star
        #將星,sYearStars = h[Star::YEARSTAR] 取得一個 YEARSTAR Star
        #宮天干,sSky = h[Sky::SKY_KEY] 取得一個 Sky
        #宮五行,sFive = h[Star::FIVE] 取得一個 Five
        #小限,aSmallSam = h[Star::SMALLSAM] 取得小限 Array
        #大限,aLargeSam = h[Star::LARGESAM] 取得大限 Array ([起始年,結束年,起始年-結束年字串"]
        #h[Star::A_STAR_POS]              @m_A_Star_Pos       #甲級星存放位置  寅宮為0
        #h[Star::B_STAR_POS]              @m_B_Star_Pos       #乙級星存放位置  寅宮為0
        #h[Star::F_STAR_POS]              @m_F_Star_Pos       #流年星存放位置  寅宮為0
        #h[Star::S_STAR_POS]              @m_S_Star_Pos       #特殊星存放位置  寅宮為0
        #h[Star::DOCTOR_POS]              @m_Doctor_Pos       #博士12星存放位置  寅宮為0
        #h[Star::YEARGOD_POS]             @m_YearGod_Pos      #歲建星存放位置  寅宮為0
        #h[Star::YEARSTAR_POS]            @m_YearStar_Pos     #將星星存放位置  寅宮為0
        #h[Star::GOD_POS]                 @m_God_Pos          #十二長生存放位置  寅宮為0
        #h[Star::FLOW_HOUSE_HEAD]           @m_nFlowHouseHead      #各流盤宮位開始的起始位置(地支表示)Array[Cfate::PAN_CNT]

        #四化資訊
        #先天四化
  # A = @OriginalFourHua[Cfate::PAN_CNT] of OrighnalFourHua
        @OriginalFourHua
        #宮干自化
        @SelfFourHua
        #四化飛出

    #廟旺落平陷

  #論斷部分


end
