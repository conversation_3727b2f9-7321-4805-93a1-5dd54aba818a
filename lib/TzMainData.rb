# coding: UTF-8
require "Pm.rb"

class TzMainData

  def TzMainData.get_lunduan_data(nPanType)
    return Pm.GetStr("lun_duan.tzmaindata.tmstr#{nPanType}")
  end
  def TzMainData.getDesc_byid(nPanType,housestay,star,shengke)
    sHouseStay = Pm.GetStr("IDS_S_HOUSE_NAME_#{housestay}")
    sStarName = Pm.GetStr("IDS_S_A_STAR_#{star}")
    sSanKur = Pm.GetStr("IDS_S_SANKUR_#{shengke}")
    return TzMainData.getDesc(nPanType,sHouseStay,sStarName,sSanKur)
  end
  def TzMainData.getDesc(nPanType,sHouseStay,sStarName,sSanKur)
    sToFind = TzMainData.get_lunduan_data(nPanType)

    xing_taitou = Pm.t("lun_duan.xing_taitou",:xing => sStarName)
    sStarName = TzMainData.changeStarName(sStarName)
    sFind = "#{nPanType},,#{sHouseStay},,#{sStarName},,#{sSanKur},,"

    sFind2 = " " #"\n"
    foStr = "sToFind.index('#{sFind}')"
    nStartPos = eval(foStr)
    if (nStartPos == nil) then
      return xing_taitou + ""
    end
    nStartPos += sFind.length
    foStr = "sToFind.index('#{sFind2}',#{nStartPos})"
    nStopPos = eval(foStr)
    if (nStopPos == nil) then
      return xing_taitou + ""
    end
    foStr = "sToFind[#{nStartPos}..#{nStopPos - 1}]"
    sOut = eval(foStr)
    return xing_taitou + sOut
  end
  def TzMainData.changeStarName(sStarName)
    (19..21).each do |nStar|
      (0..4).each do |pt|
        if (sStarName == Pm.GetStr("star.xing.astar_#{nStar}.pt_#{pt}")) then
          return Pm.GetStr("IDS_S_A_STAR_#{nStar}")
        end
      end
    end
    return sStarName
  end
end
