require("Cfate.rb")

class Star
	def Star.Get3ParPanType(nParType,nPanType)
		if (nParType == PAN_TYPE_LIFE) then
			# 本命盤固定顯示
			return Cfate::PAN_NORMAL
		elsif (nParType == PAN_TYPE_CALD) then
			# 依動盤顯示
			return nPanType
		elsif (nParType == PAN_TYPE_3) then
			# 第四種顯示方式
			return nPanType
		elsif (nParType == PAN_TYPE_SCORE) then
			if (nPanType <= Cfate::PAN_TENYEAR) then
				return Cfate::PAN_NORMAL
			elsif (nPanType.between?(Cfate::PAN_FLOWYEAR,Cfate::PAN_FLOWMONTH)) then
				return Cfate::PAN_FLOWYEAR
			elsif (nPanType >= Cfate::PAN_FLOWDATE) then
				return Cfate::PAN_FLOWDATE
			end
		else   #if (nParType == PAN_TYPE_FLOW) then
			# 本命及流年變化顯示 ，本命及十年依本命；流年以下依流年;流日以下依流日
			# if (nPanType <= Cfate::PAN_TENYEAR) then
			# 	return Cfate::PAN_NORMAL
			# else
			# 	return Cfate::PAN_FLOWYEAR
			# end
			if (nPanType <= Cfate::PAN_TENYEAR) then
				return Cfate::PAN_NORMAL
			elsif (nPanType.between?(Cfate::PAN_FLOWYEAR,Cfate::PAN_FLOWMONTH)) then
				return Cfate::PAN_FLOWYEAR
			elsif (nPanType >= Cfate::PAN_FLOWDATE) then
				return Cfate::PAN_FLOWDATE
			end
		end
	end
  def Star.Get2ParPanType(nParType,nPanType)
    if (nParType == PAN_TYPE_LIFE) then
      # 本命盤固定顯示
      return Cfate::PAN_NORMAL
    else   #if (nParType == PAN_TYPE_FLOW) then
      # 本命及流年,流日變化顯示 ，本命及十年依本命；流年以下依流年;流日以下依流日
      if (nPanType <= Cfate::PAN_TENYEAR) then
        return Cfate::PAN_NORMAL
      elsif (nPanType.between?(Cfate::PAN_FLOWYEAR,Cfate::PAN_FLOWMONTH)) then
        return Cfate::PAN_FLOWYEAR
      elsif (nPanType >= Cfate::PAN_FLOWDATE) then
        return Cfate::PAN_FLOWDATE
      end
    end
  end

  def Star.LegalPanType(nPanType)
    return Cfate::PAN_NORMAL if (nPanType <= Cfate::PAN_NORMAL)
    # return Cfate::PAN_FLOWTIME if (nPanType >= Cfate::PAN_FLOWTIME)
    return Cfate::PAN_FLOWMIN if (nPanType >= Cfate::PAN_FLOWMIN)
    return nPanType
  end

	STAR_LEVEL_1  = 0   # 甲級星
	STAR_LEVEL_2  = 1   # 乙級星
	STAR_LEVEL_3  = 2   # 丙級星
	STAR_LEVEL_4  = 3   # 丁級星
	STAR_LEVEL_5  = 4   # 戊級星

	#起盤排法,閏月排法
	PAN_LEAP_THIS			=		0  #閏月以該月排
	PAN_LEAP_MID			=		1  #閏月前半月屬本月，下半月歸次月
  PAN_LEAP_NEXT     =   2  #閏月以次月排
	PAN_LEAP_SEGMENT			=	 3  #閏月以節氣區分

	#排盤方式
	PAN_BASE_LIFE		=			0  # 一般排盤法
	PAN_BASE_BODY		=			1  # 地盤排盤法 身宮

	#天馬星排法
	PAN_HORSE_YEAR			=		0   #以年排法
	PAN_HORSE_MONTH			=		1   #以月排法

	#十二長生排法
	PAN_12GOD_LIFE     =             0    #本命盤固定顯示
	PAN_12GOD_CALD     =             1    #依動盤顯示
	PAN_12GOD_FLOW     =             2    #本命及流年變化顯示

	#博士星排法
	PAN_DOC_LIFE         =         0   #本命盤固定顯示
	PAN_DOC_CALD         =         1   #依動盤顯示
	PAN_DOC_FLOW         =         2   #本命及流年變化顯示

	#祿存星排法顯示
	PAN_LU_LIFE          =        0    #本命盤固定顯示
	PAN_LU_CALD          =        1    #依動盤顯示
	PAN_LU_FLOW          =        2    #本命及流年變化顯示

	#將前、歲前各星排法
	PAN_GS_LIFE          =        0    #本命盤固定顯示
	PAN_GS_FLOW          =        1    #本命及流年變化顯示

	#流盤干支排法
	PAN_FS_LIFE          =        0    #本命盤固定顯示
	PAN_FS_CALD          =        1    #依動盤顯示
	PAN_FS_FLOW          =        2    #本命及流年變化顯示

	PAN_FS_CALD_FLOWYEAR_NIANXUN         =        0    #年旬排法
	PAN_FS_CALD_FLOWYEAR_TAISUI          =        1    #太歲年順排

	#起小限規則
	PAN_SSAN_TRAD          =        0  #傳統排法
	PAN_SSAN_BIRTH_EARTH   =        1  #生年地支起小限

	#斗君排法
	PAN_GOD_LIFE		=			0        #子年斗君
	PAN_GOD_YEAR		=			1        #流年斗君
	PAN_GOD_SMALL		=			2        #小限斗君

	#流年命宮排法
	PAN_FLH_BIRTH          =        0  #生肖年為命宮
	PAN_FLH_SMALL_SAN      =        1  #小限為命宮
  PAN_FLH_SMALL_SAN_FLOW_SKY      =        2  #小限為命宮流年天干

	#截空排法
	PAN_GK_TRAD           =   0      #傳統排法
	PAN_GK_DIFF           =   1      #戊癸年不同

	#四化星排法
	PAN_FH_STEVEN        =    0    #欽天派(一般傳統)
	PAN_FH_HSU           =    1    #占驗門四化
	PAN_FH_18FLY_STAR    =    2    #十八飛星四化
	PAN_FH_USER          =    3    #自訂四化星

	FH_LU 				= 0    # 祿
	FH_CHUAN			= 1    # 權
	FH_KER				= 2    # 科
	FH_GI					= 3    # 忌
	FH_NONE             = -1
	def Star.GetFourHuaStr(nFourHuaIndex)
		if (nFourHuaIndex == nil) then
			return Pm.GetStr("IDS_A_KEY_SPACE")
		end
		return Pm.GetStr("IDS_S_FOUR_HUA_#{nFourHuaIndex}")
	end
	def Star.GetFourHuaIdStr(nFourHua)
		if (nFourHua == nil) then
			return Pm.GetStr("IDS_A_KEY_SPACE")
		end
		nFourHuaIndex = nFourHua - 1
		return Star.GetFourHuaStr(nFourHuaIndex)
	end


	FH_TYPE_0           = 0  # 先天四化
	FH_TYPE_1           = 1  # 宮干四化
	FH_TYPE_2           = 2  # 四化飛出
	def Star.GetFourHuaTypeStr(nFourHuaType)
		return Pm.GetStr("IDS_S_FOUR_HUA_TYPE_#{nFourHuaType}")
	end

	#動盤排法
	PAN_TYPE_LIFE         =         0  #依本命宮排法 */
	PAN_TYPE_CALD         =         1  #依動盤排法 */
	PAN_TYPE_FLOW         =         2  #本命及流年變化顯示 */
	PAN_TYPE_3            =         3  #第四種顯示方式 */
	PAN_TYPE_SCORE        =         99 # 給紫微計算四化分數用

	#流盤取時間的方法
	PAN_TIME_BASE_BIRTH     =        0
	PAN_TIME_BASE_NOW       =        1
	PAN_TIME_BASE_DEFINE		=				 2


	#流盤四化顯示
	PAN_YEAR_HUA_LIFE			=	0    #本命盤固定顯示
	PAN_YEAR_HUA_CALD			=	1    #依動盤顯示
	PAN_YEAR_HUA_FLOW			=	2    #本命及流年變化顯示
	PAN_YEAR_HUA_STACK		=	3    #重疊顯示

	#歲前各星名稱
	PAN_SSN_NORMAL       =    0    #一般使用名稱
	PAN_SSN_LIN          =    1    #特別名稱  =>內湖林老師名稱

	# 旬中旬空(空亡)
	PAN_B3132_LIFE       =  0
	PAN_B3132_CALD       =  1
	PAN_B3132_FLOW       =  2

  # 天鉞天魁排法
  PAN_YUEKUI_NORMAL = 0 # 一般排法：（現有排法）
  PAN_YUEKUI_SU = 1 # 勸學齋主排法：

  # 子時排盤
  PAN_ZI_SHI_NORMAL = 0 # 一般子時
  PAN_ZI_SHI_DIFF = 1 # 分早晚子時

  # 截空動盤顯示
  PAN_B30_LIFE       =  0
  PAN_B30_CALD       =  1
  PAN_B30_FLOW       =  2

  # 魁鉞動盤顯示
  PAN_B2425_LIFE       =  0
  PAN_B2425_CALD       =  1
  PAN_B2425_FLOW       =  2

  #流年星顯示模式
  STAR_DIS_FLOW_ORG   =   0		# 流盤顯示原名
  STAR_DIS_FLOW_FLOW  =   1   # 流盤顯示流X
  STAR_DIS_FLOW_DIE  =   2		# 各流運重疊顯示

  # 三方四正色彩顯示
  INT_34_COLOR  = 0
  INT_34_MONO   = 1

  # 顯示廟旺平落陷
  INT_MW_CHAR        =   0
  INT_MW_NUM         =   1
  INT_MW_NO_DISPLAY  =   2

  # 流時命宮排法
  PAN_LIUSHI_MINGGONG_1 = 0 # 子宮起子時
  PAN_LIUSHI_MINGGONG_2 = 1 # 流日命宮起子時

  #宮名
  def Star.GetHouseName(nHouseId)
	return Star.GetName(Star.GetHouseNameKey(nHouseId))
  end
  def Star.GetHouseNameWithQuota(nHouseId)
    return Star.GetName(Star.GetHouseNameKey(nHouseId))
  end
	def Star.GetHouseNameKey(nHouseId)
		return ("HOUSE_NAME_" + nHouseId.to_s)
	end
  HOUSE_NAME_   =   "HOUSE_NAME_"
  HOUSE_NAME   =   "HOUSE_NAME"
  HOUSE_NAME_1   =   "HOUSE_NAME_1"   # 命宮
  HOUSE_NAME_2   =   "HOUSE_NAME_2"   # 兄弟
  HOUSE_NAME_3   =   "HOUSE_NAME_3"   # 夫妻
  HOUSE_NAME_4   =   "HOUSE_NAME_4"   # 子女
  HOUSE_NAME_5   =   "HOUSE_NAME_5"   # 財帛
  HOUSE_NAME_6   =   "HOUSE_NAME_6"   # 疾厄
  HOUSE_NAME_7   =   "HOUSE_NAME_7"   # 遷移
  HOUSE_NAME_8   =   "HOUSE_NAME_8"   # 朋友
  HOUSE_NAME_9   =   "HOUSE_NAME_9"   # 官祿
  HOUSE_NAME_10  =   "HOUSE_NAME_10"   # 田宅
  HOUSE_NAME_11  =   "HOUSE_NAME_11"   # 福德
  HOUSE_NAME_12  =   "HOUSE_NAME_12"   # 父母

	EX_HOUSE_ALL = 99
	EX_HOUSE_NONE = 100
	EX_HOUSE_1 = 1			# 命宮
	EX_HOUSE_2 = 2          # 兄弟
	EX_HOUSE_3 = 3          # 夫妻
	EX_HOUSE_4 = 4          # 子女
	EX_HOUSE_5 = 5          # 財帛
	EX_HOUSE_6 = 6          # 疾厄
	EX_HOUSE_7 = 7          # 遷移
	EX_HOUSE_8 = 8          # 朋友
	EX_HOUSE_9 = 9          # 官祿
	EX_HOUSE_10 = 10         # 田宅
	EX_HOUSE_11 = 11         # 福德
	EX_HOUSE_12 = 12         # 父母

	BODY_HOUSE = "BODY_HOUSE"

	#星名
	def Star.GetAStarName(nStar)
		return Star.GetName(GetAStarNameKey(nStar))
	end
	def Star.GetAStarNameKey(nStar)
		return ("A_STAR_" + nStar.to_s)
	end
  #甲級星
  A_STAR_     =     "A_STAR_"
  A_STAR     =     "A_STAR"
  A_STAR_POS     =     "A_STAR_POS"
  A_STAR_1     =     "A_STAR_1"             #"紫微"
  A_STAR_2     =     "A_STAR_2"             #"天機"
  A_STAR_3     =     "A_STAR_3"             #"太陽"
  A_STAR_4     =     "A_STAR_4"             #"武曲"
  A_STAR_5     =     "A_STAR_5"             #"天同"
  A_STAR_6     =     "A_STAR_6"             #"廉貞"
  A_STAR_7     =     "A_STAR_7"             #"天府"
  A_STAR_8     =     "A_STAR_8"             #"太陰"
  A_STAR_9     =     "A_STAR_9"             #"貪狼"
  A_STAR_10    =     "A_STAR_10"            #"巨門"
  A_STAR_11    =     "A_STAR_11"            #"天相"
  A_STAR_12    =     "A_STAR_12"            #"天梁"
  A_STAR_13    =     "A_STAR_13"            #"七殺"
  A_STAR_14    =     "A_STAR_14"            #"破軍"
  A_STAR_15    =     "A_STAR_15"            #"文昌"
  A_STAR_16    =     "A_STAR_16"            #"文曲"
  A_STAR_17    =     "A_STAR_17"            #"左輔"
  A_STAR_18    =     "A_STAR_18"            #"右弼"
  A_STAR_19    =     "A_STAR_19"            #"祿存"
  A_STAR_20    =     "A_STAR_20"            #"擎羊"
  A_STAR_21    =     "A_STAR_21"            #"陀羅"
  A_STAR_22    =     "A_STAR_22"            #"火星"
  A_STAR_23    =     "A_STAR_23"            #"鈴星"
  A_STAR_24    =     "A_STAR_24"            #"天魁"
  A_STAR_25    =     "A_STAR_25"            #"天鉞"
  A_STAR_COUNT = 25

  #乙級星 hParAll[Star::PAR_FOURHUA_TYPE] == Star::PAN_FH_HSU
	def Star.GetBStarName(nStar,nManPie=Star::PAN_FH_STEVEN)
		return Star.GetName(GetBStarNameKey(nStar,nManPie))
	end
	def Star.GetBStarNameKey(nStar,nManPie=Star::PAN_FH_STEVEN)
		if (nManPie == Star::PAN_FH_HSU) then
			return Star.GetBStarNameKey_Hsu(nStar,nManPie)
		end
		return ("B_STAR_" + nStar.to_s)
	end
	def Star.GetBStarNameKey_Hsu(nStar,nManPie)
		sKey = "B_STAR_" + nStar.to_s
		if (nStar == 2) then  # 天空
			sKey = "#{sKey}_#{nManPie}"
		end
		return sKey
	end
  B_STAR_     =     "B_STAR_"
  B_STAR     =     "B_STAR"
  B_STAR_POS     =     "B_STAR_POS"
  B_STAR_1     =     "B_STAR_1"     # 地劫
  B_STAR_2     =     "B_STAR_2"     # 天空
  B_STAR_3     =     "B_STAR_3"     # 天刑
  B_STAR_4     =     "B_STAR_4"     # 天姚
  B_STAR_5     =     "B_STAR_5"     # 天馬
  B_STAR_6     =     "B_STAR_6"     # 解神
  B_STAR_7     =     "B_STAR_7"     # 陰煞
  B_STAR_8     =     "B_STAR_8"     # 天喜
  B_STAR_9     =     "B_STAR_9"     # 天官
  B_STAR_10    =     "B_STAR_10"    # 天福
  B_STAR_11    =     "B_STAR_11"    # 天哭
  B_STAR_12    =     "B_STAR_12"    # 天虛
  B_STAR_13    =     "B_STAR_13"    # 龍池
  B_STAR_14    =     "B_STAR_14"    # 鳳閣
  B_STAR_15    =     "B_STAR_15"    # 紅鸞
  B_STAR_16    =     "B_STAR_16"    # 孤辰
  B_STAR_17    =     "B_STAR_17"    # 寡宿
  B_STAR_18    =     "B_STAR_18"    # 蜚廉
  B_STAR_19    =     "B_STAR_19"    # 破碎
  B_STAR_20    =     "B_STAR_20"    # 台輔
  B_STAR_21    =     "B_STAR_21"    # 封誥
  B_STAR_22    =     "B_STAR_22"    # 天巫
  B_STAR_23    =     "B_STAR_23"    # 天月
  B_STAR_24    =     "B_STAR_24"    # 三台
  B_STAR_25    =     "B_STAR_25"    # 八座
  B_STAR_26    =     "B_STAR_26"    # 恩光
  B_STAR_27    =     "B_STAR_27"    # 天貴
  B_STAR_28    =     "B_STAR_28"    # 天才
  B_STAR_29    =     "B_STAR_29"    # 天壽  # 以上乙級
  B_STAR_30    =     "B_STAR_30"    # 截空  #  丙級
  B_STAR_31    =     "B_STAR_31"    # 旬中  #  丙級
  B_STAR_32    =     "B_STAR_32"    # 旬空  #  丙級
  B_STAR_33    =     "B_STAR_33"    # 天傷  #  丙級
  B_STAR_34    =     "B_STAR_34"    # 天使  #  丙級
  B_STAR_35    =     "B_STAR_35"    # 天廚
  B_STAR_COUNT = 35

	#博士星
	def Star.GetDoctorName(nDoctor)
		return Star.GetName(GetDoctorNameKey(nDoctor))
	end
	def Star.GetDoctorNameKey(nDoctor)
		return ("DOCTOR_" + nDoctor.to_s)
	end
  DOCTOR_     =     "DOCTOR_"
  DOCTOR     =     "DOCTOR"
  DOCTOR_POS     =     "DOCTOR_POS"
  DOCTOR_1     =     "DOCTOR_1"
  DOCTOR_2     =     "DOCTOR_2"
  DOCTOR_3     =     "DOCTOR_3"
  DOCTOR_4     =     "DOCTOR_4"
  DOCTOR_5     =     "DOCTOR_5"
  DOCTOR_6     =     "DOCTOR_6"
  DOCTOR_7     =     "DOCTOR_7"
  DOCTOR_8     =     "DOCTOR_8"
  DOCTOR_9     =     "DOCTOR_9"
  DOCTOR_10    =     "DOCTOR_10"
  DOCTOR_11    =     "DOCTOR_11"
  DOCTOR_12    =     "DOCTOR_12"
  DOCTOR_COUNT = 12

	#歲建星
	def Star.GetYearGodName2(nYearGod)
		return Star.GetName(Star.GetYearGodNameKey2(nYearGod,Star::PAN_SSN_NORMAL))
	end
	def GetYearGodName(nYearGod)
		return Star.GetName(GetYearGodNameKey(nYearGod))
	end
	def GetYearGodNameKey(nYearGod)
		return Star.GetYearGodNameKey2(nYearGod,@par_SsnType)
	end
	def Star.GetYearGodNameKey2(nYearGod,par_SsnType)
		if (par_SsnType == Star::PAN_SSN_NORMAL) then
			return ("YEARGOD_" + nYearGod.to_s)
		else
			return ("YEARGOD_#{par_SsnType}_" + nYearGod.to_s)
		end
	end

  YEARGOD_    =     "YEARGOD_"
  YEARGOD    =     "YEARGOD"
  YEARGOD_POS    =     "YEARGOD_POS"
  YEARGOD_1    =     "YEARGOD_1"
  YEARGOD_2    =     "YEARGOD_2"
  YEARGOD_3    =     "YEARGOD_3"
  YEARGOD_4    =     "YEARGOD_4"
  YEARGOD_5    =     "YEARGOD_5"
  YEARGOD_6    =     "YEARGOD_6"
  YEARGOD_7    =     "YEARGOD_7"
  YEARGOD_8    =     "YEARGOD_8"
  YEARGOD_9    =     "YEARGOD_9"
  YEARGOD_10   =     "YEARGOD_10"
  YEARGOD_11   =     "YEARGOD_11"
  YEARGOD_12   =     "YEARGOD_12"
  YEARGOD_COUNT = 12

	#將星
	def Star.GetYearStarName(nYearStar)
		return Star.GetName(GetYearStarNameKey(nYearStar))
	end
	def Star.GetYearStarNameKey(nYearStar)
		return ("YEARSTAR_" + nYearStar.to_s)
	end
  YEARSTAR_    =    "YEARSTAR_"
  YEARSTAR    =    "YEARSTAR"
  YEARSTAR_POS    =    "YEARSTAR_POS"
  YEARSTAR_1    =    "YEARSTAR_1"
  YEARSTAR_2    =    "YEARSTAR_2"
  YEARSTAR_3    =    "YEARSTAR_3"
  YEARSTAR_4    =    "YEARSTAR_4"
  YEARSTAR_5    =    "YEARSTAR_5"
  YEARSTAR_6    =    "YEARSTAR_6"
  YEARSTAR_7    =    "YEARSTAR_7"
  YEARSTAR_8    =    "YEARSTAR_8"
  YEARSTAR_9    =    "YEARSTAR_9"
  YEARSTAR_10   =    "YEARSTAR_10"
  YEARSTAR_11   =    "YEARSTAR_11"
  YEARSTAR_12   =    "YEARSTAR_12"
  YEARSTAR_COUNT = 12

	def Star.GetGodName(nGod)
		return Star.GetName(GetGodNameKey(nGod))
	end
	def Star.GetGodNameKey(nGod)
		return ("GOD_" + nGod.to_s)
	end
	#十二長生
  GOD_     =     "GOD_"
  GOD     =     "GOD"
  GOD_POS     =     "GOD_POS"
  GOD_1     =     "GOD_1"
  GOD_2     =     "GOD_2"
  GOD_3     =     "GOD_3"
  GOD_4     =     "GOD_4"
  GOD_5     =     "GOD_5"
  GOD_6     =     "GOD_6"
  GOD_7     =     "GOD_7"
  GOD_8     =     "GOD_8"
  GOD_9     =     "GOD_9"
  GOD_10    =     "GOD_10"
  GOD_11    =     "GOD_11"
	GOD_12    =     "GOD_12"
  GOD_COUNT = 12

	#流年星
	def Star.GetFStarName(nStar)
		return Star.GetName(GetFStarNameKey(nStar))
	end
	def Star.GetFStarNameKey(nStar)
		return ("F_STAR_" + nStar.to_s)
	end
  F_STAR       =    "F_STAR"
  FA_STAR       =    "FA_STAR"
  FB_STAR       =    "FB_STAR"
  F_STAR_POS       =    "F_STAR_POS"
  F_STAR_1       =    "F_STAR_1"
  F_STAR_2       =    "F_STAR_2"
  F_STAR_3       =    "F_STAR_3"
  F_STAR_4       =    "F_STAR_4"
  F_STAR_5       =    "F_STAR_5"
  F_STAR_6       =    "F_STAR_6"
  F_STAR_COUNT = 6

  #特殊星
	def Star.GetSStarName(nStar)
		return Star.GetName(GetSStarNameKey(nStar))
	end
	def Star.GetSStarNameKey(nStar)
		return ("S_STAR_" + nStar.to_s)
	end
  S_STAR       =  	"S_STAR"
  S_STAR_POS       =  	"S_STAR_POS"
  S_STAR_1       =  	"S_STAR_1"
  S_STAR_2       =  	"S_STAR_2"
  S_STAR_COUNT = 2

	FLOW_LIFE_HEAD = "FLOW_LIFE_HEAD"
	FLOW_HOUSE_HEAD = "FLOW_HOUSE_HEAD"
	U_LIFE            =    "U_LIFE"
	U_GOD	            =    "U_GOD"
	U_BODY	          =    "U_BODY"
	U_SKY	            =    "U_SKY"
	U_EARTH	          =    "U_EARTH"
	U_FIVE	          =    "U_FIVE"
	U_SMALL	          =    "U_SMALL"
	U_LARGE	          =    "U_LARGE"
	U_PAN_INFO	      =    "U_PAN_INFO"
	HOUSE_SKY         =    "HOUSE_SKY"
	ORIGINAL_FOUR_HUA =    "ORIGINAL_FOUR_HUA"
	SELF_FOUR_HUA     =    "SELF_FOUR_HUA"
	FOUR_HUA          =    "FOUR_HUA"
	FOUR_HUA_          =    "FOUR_HUA_"
	HOUSE_EARTH       =    "HOUSE_EARTH"

	# 三方四正
	HOUSE_34_SELF = 0
	HOUSE_34_1 = 1
	HOUSE_34_2 = 2
	HOUSE_34_OPP = 3
	HOUSE_34_NONE = 99

	def Star.GetStarName(sStarType,nStar)
		return Star.GetName("#{sStarType}_#{nStar}")
	end

	#參數設定
  PanPar_Copy = 0
	PanPar_Type = 1
	PanPar_Interface = 2
	PanPar_Display = 3
	PanPar_PrivateFourHua = 4
	PanPar_HallNumber = 5
	PanPar_Print = 6

	#可設定參數預設值
	PAR_YEARDISPLAY_DEFAULT = Cfate::PAN_DISPLAY_WEST  # 年代顯示
	PAR_LEAPTYPE_DEFAULT = Star::PAN_LEAP_MID  #起盤排法
	PAR_HORSETYPE_DEFAULT = Star::PAN_HORSE_MONTH  #天馬星排法
	PAR_12GODTYPE_DEFAULT = Star::PAN_12GOD_FLOW   #十二長生排法
	PAR_DOCTYPE_DEFAULT = Star::PAN_DOC_FLOW       #博士星排法
	PAR_LUTYPE_DEFAULT =  Star::PAN_LU_FLOW  #祿存動盤方式
	PAR_GSTYPE_DEFAULT = Star::PAN_GS_FLOW         #將前、歲前各星排法
	PAR_FLOWSKYTYPE_DEFAULT = Star::PAN_FS_CALD        # 流盤干支方式
	PAR_FLOWSKYTYPE_CALD_FLOWYEAR_DEFAULT = Star::PAN_FS_CALD_FLOWYEAR_TAISUI # 太歲年順排
	PAR_SMALLSANTYPE_DEFAULT = Star::PAN_SSAN_TRAD #起小限規則
	PAR_GODTYPE_DEFAULT = Star::PAN_GOD_LIFE    #斗君排法,預設流年斗君
	PAR_FLOWLIFEHOUSETYPE_DEFAULT = Star::PAN_FLH_BIRTH   #流年命宮排法
	PAR_GIAKUNGTYPE_DEFAULT = Star::PAN_GK_TRAD    # 截空排法
	PAR_FOURHUATYPE_DEFAULT = Star::PAN_FH_STEVEN   #四化星排法
	PAR_FLOWYEARHUA_DEFAULT = Star::PAN_YEAR_HUA_FLOW  #流盤四化顯示方式 old:PAN_YEAR_HUA_CALD
	PAR_SSNTYPE_DEFAULT = Star::PAN_SSN_NORMAL   # 歲前各星名稱
	PAN_B3132_DEFAULT  = Star::PAN_B3132_CALD  # 旬中旬空(空亡)
    PAR_YUEKUI_DEFAULT = Star::PAN_YUEKUI_NORMAL  # 天鉞天魁排法
    PAR_ZI_SHI_DEFAULT = Star::PAN_ZI_SHI_NORMAL  # 子時排盤
    PAN_B30_DEFAULT  = Star::PAN_B30_CALD  # 截空動盤顯示
    PAN_B2425_DEFAULT  = Star::PAN_B2425_CALD  # 魁鉞動盤顯示
    PAR_LIUSHI_MINGGONG_DEFAULT  = Star::PAN_LIUSHI_MINGGONG_1  # 流時命宮排法

	# 沒有用到的參數
	PAR_PANBASE_DEFAULT  = Star::PAN_BASE_LIFE  #排盤方式
	PAR_PANTIMEBASE_DEFAULT = Star::PAN_TIME_BASE_BIRTH #Star::PAN_TIME_BASE_BIRTH #流盤取時間的方法
	PAR_FIXSKY_DEFAULT = Cfate::PAR_FALSE                   # 尋找宮五行的天干是否要隨流盤變動

	# 介面參數
	INT_34COLOR_DEFAULT = Star::INT_34_COLOR       # 三方四正色彩顯示
	INT_MIOWONGSTYLE_DEFAULT = Star::INT_MW_CHAR       #顯示廟旺的方式,一般的文字或林老師的數字或不顯示
	INT_HUAOUT_DISPLAY_DEFAULT   = Cfate::PAR_TRUE      # 顯示對宮四化
	INT_SELHUA_DISPLAY_DEFAULT   = Cfate::PAR_TRUE      # 顯示自化
	INT_HOUSEFIVE_DISPLAY_DEFAULT   = Cfate::PAR_TRUE      # 顯示各宮五行
	INT_FLOWTIME_DISPLAY_DEFAULT   = Cfate::PAR_FALSE      # 顯示命盤時間
	INT_TEN_DISPLAY_DEFAULT   = Cfate::PAR_TRUE      # 顯示十年大運
	INT_SMALL_DISPLAY_DEFAULT   = Cfate::PAR_TRUE      # 顯示小限時間
	INT_8WORDS_DISPLAY_DEFAULT   = Cfate::PAR_TRUE      # 顯示八字時間
	INT_LIFELINE_DISPLAY_DEFAULT   = Cfate::PAR_TRUE      # 標示命宮三方四正
	INT_SMALLYEAR_REVERT_DISPLAY_DEFAULT   = Cfate::PAR_TRUE      # 小限應期顯示

	# 星曜顯示設定
	#DIS_ASTAR_DEFAULT =     # 甲級星曜設定
	#DIS_BSTAR_DEFAULT =  Array.new()   # 乙、丙級星曜設定
	DIS_FLOWLYT_DEFAULT =  Star::STAR_DIS_FLOW_ORG   # 流祿，流羊，流陀
	DIS_FLOWMA_DEFAULT =  Star::STAR_DIS_FLOW_ORG   # 流馬
	DIS_FLOWLUAN_DEFAULT =  Star::STAR_DIS_FLOW_ORG   # 流鸞
	DIS_DOCTOR_DEFAULT =  Cfate::PAR_TRUE   # 顯示博士十二星
	DIS_LIVE_DEFAULT =  Cfate::PAR_TRUE   # 顯示長生十二星
	DIS_YEARSTAR_DEFAULT =  Cfate::PAR_TRUE   # 顯示將星諸星
	DIS_YEARGOD_DEFAULT =  Cfate::PAR_TRUE   # 顯示歲前諸星
	DIS_FLOWCHAN_DEFAULT = Cfate::PAR_TRUE    # 顯示流昌
	DIS_SKYCOOK_DEFAULT =  Cfate::PAR_TRUE   # 顯示天廚星
	DIS_LYYG_DEFAULT =   Cfate::PAR_TRUE  # 顯示來因業障
	DIS_7STAR_DEFAULT =  Cfate::PAR_FALSE   # 七星

  	# 參數設定
	PAR_PAR = "par_par"
	STAR_PAN_PAR_TYPE = "star_pan_par_type"  # 各門派排盤的參數
	PAR_LEAP_TYPE = "leap_type"
	PAR_HORSE_TYPE  = "horse_type"
	PAR_GOD_TYPE12 = "god_type12"
	PAR_DOC_TYPE  = "doc_type"
	PAR_LU_TYPE  = "lu_type"
	PAR_GS_TYPE  = "gs_type"
	PAR_FLOWSKY_TYPE  = "flowsky_type"
	PAR_FLOWSKY_TYPE_CALD_FLOWYEAR  = "flowsky_type_cald_flowyear"
	PAR_SMALLSAN_TYPE = "smallsan_type"
	PAR_GOD_TYPE  = "god_type"
	PAR_FLOWLIFE_HOUSETYPE = "flowlife_housetype"
	PAR_GIAKUNG_TYPE = "giakung_type"
	PAR_FOURHUA_TYPE = "fourhua_type"
	PAR_FLOWYEAR_HUA  = "flowyear_hua"
	PAR_GS_NAME_TYPE  = "gs_name_type"
	PAR_B3132_TYPE  = "par_b3132_type"
    PAR_YUEKUI_TYPE  = "yuekui_type"
    PAR_ZI_SHI_TYPE  = "zi_shi_type"
    PAR_B30_TYPE  = "par_b30_type"
    PAR_B2425_TYPE  = "par_b2425_type"
	PAR_LIUSHI_MINGGONG  = "liushi_minggong"

	PAR_PAN_BASE  = "par_pan_base"
	PAR_PAN_TIMEBASE  = "par_pan_timebase"
	PAR_FIX_SKY  = "par_fix_sky"

	# 介面參數
	PAR_INT = "par_int"
	INT_34COLOR  = "int_34color"  # 三方四正色彩顯示
	INT_MIOWONG_STYLE  = "int_miowong_style"    # 顯示廟旺的方式,一般的文字或林老師的數字或不顯示
	INT_HUAOUT_DISPLAY   = "int_huaout_display"      # 顯示對宮四化
	INT_SELHUA_DISPLAY   = "int_selhua_display"      # 顯示自化
	INT_HOUSEFIVE_DISPLAY   = "int_housefive_display"      # 顯示各宮五行
	INT_FLOWTIME_DISPLAY   = "int_flowtime_display"      # 顯示命盤時間
	INT_TEN_DISPLAY   = "int_ten_display"      # 顯示十年大運
	INT_SMALL_DISPLAY   = "int_small_display"      # 顯示小限時間
	INT_8WORDS_DISPLAY   = "int_8words_display"      # 顯示八字時間
	INT_LIFELINE_DISPLAY   = "int_lifeline_display"      # 標示命宮三方四正
	INT_SMALLYEAR_REVERT_DISPLAY   = "int_smallyear_revert_display"      # 小限應期顯示

 	# 星曜顯示設定
 	PAR_ALL = "par_all"
 	PAR_ALL_A = "par_all_a"
 	PAR_ALL_B = "par_all_b"
	PAR_DIS = "par_dis"
	DIS_ASTAR =   "dis_astar"  # 甲級星曜設定
	DIS_BSTAR =  "dis_bstar"   # 乙、丙級星曜設定
	DIS_FLOWLYT =  "dis_flowlyt"   # 流祿，流羊，流陀
	DIS_FLOWMA =  "dis_flowma"   # 流馬
	DIS_FLOWLUAN =  "dis_flowluan"   # 流鸞
	DIS_DOCTOR =  "dis_doctor"   # 顯示博士十二星
	DIS_LIVE =  "dis_live"   # 顯示長生十二星
	DIS_YEARSTAR =  "dis_yearstar"   # 顯示將星諸星
	DIS_YEARGOD =  "dis_yeargod"   # 顯示歲前諸星
	DIS_FLOWCHAN = "dis_flowchan"    # 顯示流昌
	DIS_SKYCOOK =  "dis_skycook"   # 顯示天廚星
	DIS_LYYG =   "dis_lyyg"  # 顯示來因業障
	DIS_7STAR =  "dis_7star"   # 七星

  # 自訂四化
  	def Star.PrivateFourHua_HashName(nSky,nFourHuaIndex)
  		return "#{PFH_PRE}_#{nSky}_#{nFourHuaIndex}"
  	end
  	def Star.PrivateFourHua_FirstAStar()
  		return 1
  	end
  	def Star.PrivateFourHua_LastAStar()
  		return 25
  	end

  	PFH_PRE = "pfh"
  	STAR_PAN_PAR_PFH = "star_pan_par_pfh"   # 自訂四化

    SKY_EARTH_INDEX = "sky_earth_index"

  def Star.Sex(sex)
    return Cfate.Sex(!sex)
  end
end
