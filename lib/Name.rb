require("Name_dk.rb")
require("Name_mk.rb")
require("Name_mtod.rb")
require("Name_mtoo.rb")
require("Name_mtot.rb")
require("Name_ok.rb")
require("Name_word.rb")
require("Name_pair.rb")

class Name
  Id = 0
  No = 1
  No1 = 2

  def Name.gpc_name(surname,txtname,sex,s=nil,t=nil,lang="zh-CN")
    h = Name.name(surname,txtname,sex,s,t,lang)
    return h if h["nameerr"]

    male = Name.isMale(sex)

    # mk
    # 人格描繪
    h["Mk_Personal"] = Name.findmk_Personal(h["mk"]).join("")

    # ok
    # 人際關係
    h["Ok_InterPersonal"] = Name.findok_InterPersonal(h["ok"]).join("")

    # mtoo 
    # 人際關係
    h["Mtoo_InterPersonal"] = Name.findmtoo_InterPersonal(h["mtoo"]).join("")
    # 事業財運
    h["Mtoo_Substance"] = Name.findmtoo_Substance(h["mtoo"]).join("")
    # 交友態度
    h["Mtoo_Social"] = Name.findmtoo_Social(h["mtoo"]).join("")
    # 適合行業
    h["Mtoo_Industry"] = Name.findmtoo_Industry(h["mtoo"])
    # 意志力
    h["Mtoo_Will"] = Name.findmtoo_Will(h["mtoo"]).join("")
    # 婚姻機緣
    h["Mtoo_Marriage"] = Name.findmtoo_Marriage(h["mtoo"]).join("")

    # dk
    # 配偶個性，子女性格
    h["Dk_SpousePersonal"] = Name.finddk_male(h["dk"],male).join("")

    # mtod
    # 配偶人際關係
    h["Mtod_InterPersonal"] = Name.findmtod_male(h["mtod"],male).join("")
    # 婚姻機緣
    h["Mtod_Couples"] = Name.findmtod_Couples(h["mtod"]).join("")
    # 部屬朋友
    h["Mtod_Under"] = Name.findmtod_Under(h["mtod"]).join("")
    # 兒女
    h["Mtod_Child"] = Name.findmtod_Child(h["mtod"]).join("")

    # mtot
    # 上司父母
    h["Mtot_Parent"] = Name.findmtot_parent(h["mtot"]).join("")

    h.delete("ppeople")
    h.delete("pout")
    h.delete("pland")
    h.delete("psky")
    h.delete("mtoo")
    h.delete("mtod")
    h.delete("mtot")
    h.delete("mk")
    h.delete("ok")
    h.delete("dk")
    h.delete("param")
    h.delete("pstr")
    h.delete("usimage")

    return h
  end

  def Name.name(surname,txtname,sex,s=nil,t=nil,lang="zh-CN")
  	male = Name.isMale(sex)
    h = Name.pannos(surname,txtname,s,t)
    h["sex"] = sex
    return h if h["nameerr"]

    h = Name.strokes(surname.length,txtname.length,h)
    h["ppeople"] = Name.ppeople(h["people"])
    h["pout"] = Name.pout(h["out"])
    h["pland"] = Name.pland(h["land"])
    h["psky"] = Name.psky(h["sky"])
# puts h
    h["mtoo"] = Name.findmtoo(h["ppeople"],h["pout"],male,lang)
    h["mtod"] = Name.findmtod(h["ppeople"],h["pland"],male,lang)
    h["mtot"] = Name.findmtot(h["ppeople"],h["psky"],male,lang)
    h["mk"] = Name.findmk(h["people"],male,lang)
    h["ok"] = Name.findok(h["out"],male,lang)
    h["dk"] = Name.finddk(h["land"],male,lang)
    a = Name.finddk(h["pland"],male,lang)
    h["dk"][Name::Dk_Main] = a[Name::Dk_Main]

    h["param"] = Name.param(h,male)
    h["pstr"] = h["people"] % 10
    h["nameerr"] = false

    return h
  end

  def Name.param(h,male)
  	param = Array.new(29)
    param[0] = h["mk"][2]
    param[1] = h["ok"][2]
    param[2] = h["mtoo"][3]
    param[3] = h["mtoo"][4]
    param[4] = h["mtoo"][5]
    param[5] = h["mtoo"][6]
    param[6] = h["mtoo"][7]
    param[7] = h["mtoo"][8]
    param[8] = h["mtoo"][9]
    param[9] = h["mtoo"][10]
    param[10] = h["mtoo"][11]
    param[11] = h["mtoo"][12]
    param[12] = h["sky"]
    param[13] = h["people"]
    param[14] = h["land"]
    param[15] = h["out"]
    param[16] = h["total"]
    param[17] = h["dk"][4]
    if male then
      param[18] = h["dk"][2]
    else
      param[18] = h["dk"][3]
    end
    param[19] = h["mtod"][7]
    if male then
      param[20] = h["mtod"][3]
    else
      param[20] = h["mtod"][5]
    end
    param[21] = h["mtoo"][13]
    param[22] = h["mtod"][8]
    if male then
      param[23] = h["mtod"][4]
    else
      param[23] = h["mtod"][6]
    end
    param[24] = h["mtot"][3]
    param[25] = h["mtod"][9]
    param[26] = h["mtod"][10]
    param[27] = h["mtod"][11]
    param[28] = h["mtod"][12]

    return param
  end

  def Name.pannos(surname,txtname,s=nil,t=nil)
    hOut = Hash.new
    h = Name.findpannos(surname,s,"s")
    nameerr1 = h["nameerr"]
    hOut.merge!(h)
    h = Name.findpannos(txtname,t,"t")
    nameerr2 = h["nameerr"]
    hOut.merge!(h)
    hOut["nameerr"] = nameerr1 || nameerr2
    
    return hOut
  end

  def Name.findpannos(name,pannos,preword)
  	h = Hash.new

    if (name == nil || name == "") && (pannos == nil || pannos == []) then
      h["nameerr"] = true
      return h
    end

  	if (pannos == nil || pannos == []) then
	    (0...name.length).each do |i|
	      panno = Name.findpanno(name[i])
	      if (panno == nil) then
	        h["nameerr"] = true
          panno = 0
	      end
	      h["#{preword}#{i + 1}"] = panno
	      h["user_#{preword}#{i + 1}"] = name[i]
	    end
	  else
      n = [pannos.length,name.length].max
      pannos += Array.new(n - pannos.length) if n > pannos.length
      name += Array.new(n - name.length) if n > name.length
	    (0...pannos.length).each do |i|
	      panno = pannos[i].to_i
	      if (panno == nil || panno == 0) then
          panno = Name.findpanno(name[i])
          if (panno == nil || panno == 0) then
	         h["nameerr"] = true
            panno = 0
          end
	      end
        if (name[i] == nil || name[i] == "") then
          h["nameerr"] = true
          name[i] = ""
        end
	      h["#{preword}#{i + 1}"] = panno
	      h["user_#{preword}#{i + 1}"] = name[i]
	    end
	  end
  	return h
  end

  def Name.isMale(sex)
    return ["man","m","M","MAN",1,"1","male","MALE"].include?(sex)
  end

  def Name.strokes(slen,tlen,h)
  	if ((slen == 2) and (tlen == 2)) then
  	  h["sky"] = h["s1"] + h["s2"]
  	  h["people"] = h["s2"] + h["t1"]
  	  h["land"] = h["t1"] + h["t2"]
  	  h["out"] = h["s1"] + h["t2"]
  	  h["total"] = h["sky"] + h["land"]
  	  h["usimage"] = "4"
    elsif ((slen == 1) and (tlen == 2)) then
  	  h["sky"] = h["s1"] + 1
  	  h["people"] = h["s1"] + h["t1"]
  	  h["land"] = h["t1"] + h["t2"]
  	  h["out"] = h["t2"] + 1
  	  h["total"] = h["s1"] + h["land"]
  	  h["usimage"] = "3B"
    elsif ((slen == 2) and (tlen == 1)) then
  	  h["sky"] = h["s1"] + h["s2"]
  	  h["people"] = h["s2"] + h["t1"]
  	  h["land"] = h["t1"] + 1
  	  h["out"] = h["s1"] + 1
  	  h["total"] = h["s1"] + h["s2"] + h["t1"]
  	  h["usimage"] = "3A"
    elsif ((slen == 1) and (tlen == 1)) then
  	  h["sky"] = h["s1"] + 1
  	  h["people"] = h["s1"] + h["t1"]
  	  h["land"] = h["t1"] + 1
  	  h["out"] = 2
  	  h["total"] = h["people"]
  	  h["usimage"] = "2"
    end

    return h.clone
  end

  def Name.ppeople(people)
  	return Name.test10(people)
  end

  def Name.pout(out)
  	return Name.test10(out)
  end

  def Name.pland(land)
  	return Name.test10(land)
  end

  def Name.psky(sky)
  	return Name.test10(sky)
  end

  def Name.test10(m)
  	n = Pm.TestNo10(m % 10)
  	if (n == 0) then
  		n = 10
  	end
  	return n
  end

end
