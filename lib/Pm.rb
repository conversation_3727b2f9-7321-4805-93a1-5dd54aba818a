#require("Pm_Constant_Big5.rb")
#require("Pm_Constant_Eng.rb")
 require 'rubygems'
 require 'i18n'
 require 'iconv'

class Pm
  Lang_Big5 = "String_Big5"
  Lang_Cn = "String_Cn"
  Lang_Eng = "String_Eng"
  @@Lang_Out = "String_Big5"

  AP_STAR = "Star"
  AP_EIGHT = "Eight"

  def Pm.SetLang(newLang)
    @@Lang_Out = newLang
  end

  def Pm.IntToStr (m)
    return m.to_s
  end

  # i18n
  def Pm.GetStr(strToFind)
    # sId = "I18n.translate(:#{strToFind})"
    # # sId = "I18n.t('.#{strToFind}')"
    # return eval(sId)
    return Pm.t(strToFind)
  end

  def Pm.t_old(strToFind,hData=nil)
    sId = "I18n.translate('#{strToFind}'"
    #sId = "I18n.t('.#{strToFind}'"
    if (hData != nil) then
      hData.each {|key,value|
        sId += ",:#{key} => '#{value}'"
      }
    end
    sId += ")"
    return eval(sId)
  end
  def Pm.t(strToFind,hData=nil)
    # sId = "I18n.translate('#{strToFind}'"
    # #sId = "I18n.t('.#{strToFind}'"
    # if (hData != nil) then
    #   hData.each {|key,value|
    #     sId += ",:#{key} => #{value}"
    #   }
    # end
    # sId += ")"
    # return eval(sId)
    return I18n.translate("#{strToFind}",hData)
  end
    # Pm.saveTestDb("i18n",Pm.GetStr("datetime.distance_in_words.1_months"))
  def Pm.td(strToFind,ws_dates)
    a = ws_dates.split
    sDate = a[1]
    nFind = sDate.rindex('s')
    if (nFind == nil) then
      sDate = "#{sDate}s"
    elsif (nFind != sDate.length - 1)
      sDate = "#{sDate}s"
    end
    sId = "#{strToFind}.x_#{sDate}"
    hData = Hash.new
    hData[:count] = a[0].to_i
    return Pm.t(sId,hData)
  end

  def Pm.GreetUser(username)
    #sId = "I18n.translate(:greet_username ,:message => '#{Pm.GetStr("IDS_S_GREET")}',:name => #{username})"
    #return eval(sId)
    hData = Hash.new
    hData[:message] = Pm.GetStr("IDS_S_GREET")
    hData[:user] = username
    Pm.t("greet_username",hData)
  end
  def Pm.ApVersion(ap_version)
    #sId = "I18n.translate(:greet_username ,:message => '#{Pm.GetStr("IDS_S_GREET")}',:name => #{username})"
    #return eval(sId)
    Pm.GetStr("IDS_PRODUCT_STAR_#{ap_version}")
  end

  def Pm.GetStr2(strToFind)
    newStr = @@Lang_Out
    newStr += "::"
    newStr += strToFind
#   puts(newStr)
    return eval(newStr)
  end

  def Pm.GetStrWithColon(strToFind)
    sBuf = Pm.GetStr(strToFind)
    sBuf += Pm.GetStr("IDS_S_COLON")
    return sBuf
  end

  def Pm.GetColon()
    sBuf = Pm.GetStr("IDS_S_COLON")
  end

  def Pm.GetStrWithQuote(strToFind)
    sBuf = Pm.GetStr("IDS_A_LEFT_QUOTE")
    sBuf += Pm.GetStr(strToFind)
    sBuf += Pm.GetStr("IDS_A_RIGHT_QUOTE")
  end

  def Pm.GetStrWithQuote2(strToFind)
    sBuf = Pm.GetStr("IDS_X_QUOTE_LEFT")
    sBuf += Pm.GetStr(strToFind)
    sBuf += Pm.GetStr("IDS_X_QUOTE_RIGHT")
  end
  def Pm.GetStrWithQuote3(str)
    sBuf = Pm.GetStr("IDS_X_QUOTE_LEFT")
    sBuf += str
    sBuf += Pm.GetStr("IDS_X_QUOTE_RIGHT")
  end

  def Pm.TestNum(nNoToTest,nMin,nRange)
    nNo = nNoToTest
      if (nNo < nMin) then
        while (nNo < nMin)
            nNo += nRange
          end
      elsif (nNo >= nRange) then
        while (nNo >= nRange)
            nNo -= nRange
          end
    end

    return nNo
  end
  def Pm.TestNo60(nNoToTest)
    return Pm.TestNum(nNoToTest,0,60)
  end
  def Pm.TestNo24(nNoToTest)
    return Pm.TestNum(nNoToTest,0,24)
	end
	def Pm.TestNo28(nNoToTest)
		return Pm.TestNum(nNoToTest,0,28)
  end

  def Pm.TestNo12(nNoToTest)
    return Pm.TestNum(nNoToTest,0,12)
  end

  def Pm.TestNo10(nNoToTest)
    return Pm.TestNum(nNoToTest,0,10)
  end

  def Pm.TestNo5(nNoToTest)
    return Pm.TestNum(nNoToTest,0,5)
  end

	def Pm.TestNo6(nNoToTest)
		return Pm.TestNum(nNoToTest,0,6)
	end

	def Pm.TestNo8(nNoToTest)
		return Pm.TestNum(nNoToTest,0,8)
	end

  def Pm.TestNum2(nNoToTest,nMin,nRange)
    nNo = nNoToTest
      if (nNo < nMin) then
        while (nNo < nMin)
            nNo += nRange
          end
      elsif (nNo > nRange) then
        while (nNo > nRange)
            nNo -= nRange
          end
    end

    return nNo
  end
  def Pm.TestNo64(nNoToTest)
    return Pm.TestNum2(nNoToTest,1,64)
  end
  def Pm.TestNo21(nNoToTest)
    return Pm.TestNum2(nNoToTest,1,21)
  end
  def Pm.TestNo19(nNoToTest)
    return Pm.TestNum2(nNoToTest,1,19)
  end
  def Pm.TestNoN(nNoToTest,n)
    return Pm.TestNum2(nNoToTest,1,n)
  end

  def Pm.setSystemValue(key,value)
    oSystemPar = SystemPar.available.find_by_key(key)
    if (oSystemPar == nil) then
      oSystemPar = SystemPar.new
      oSystemPar.key = key
    end
    oSystemPar.value = value
    oSystemPar.status = 0
    oSystemPar.save!
  end
  def Pm.getSystemValue(key)
    # oSystemPar = SystemPar.available.check_key(key).first
    oSystemPar = SystemPar.available.find_by_key(key)
    # if (oSystemPar == nil) then
    #   return getSystemValue_local(key)
    # else
    if (oSystemPar == nil) then
      # Pm.saveTestDb("getSystemValue",key)
      return ""
    else
      return oSystemPar.value
    end
  end
  def Pm.setSystemValue_hash(key,h)
    oSystemPar = SystemPar.available.find_by_key(key)
    if (oSystemPar == nil) then
      oSystemPar = SystemPar.new
      oSystemPar.key = key
    end
    oSystemPar.hValue = h
    oSystemPar.status = 0
    oSystemPar.save!
  end
  def Pm.getSystemValue_hash(key)
    # oSystemPar = SystemPar.available.check_key(key).first
    oSystemPar = SystemPar.available.find_by_key(key)
    # if (oSystemPar == nil) then
    #   return getSystemValue_local(key)
    # else
    if (oSystemPar == nil) then
      # Pm.saveTestDb("getSystemValue",key)
      return {}
    else
      return oSystemPar.hValue
    end
  end
  def Pm.changeSystemValue_key(key_old,key_new)
    oSystemPar = SystemPar.available.find_by_key(key_old)
    if (oSystemPar != nil) then
      oSystemPar.key = key_new
      oSystemPar.save!
    end
  end

  def Pm.getSvUrl(key)
    url = Pm.getSystemValue(key)
    return Pm.getHostUrl(url)
  end

  def Pm.getHostUrl(url)
    sHost = Pm.getSystemValue("MEEN_URL")
    # return "#{sHost}#{"/"}#{I18n.locale.to_s}#{url}"
    return "#{sHost}#{url}"
  end

  def Pm.getBillingServer(url="")
    # return @@BillingServer
    sBillingServer = Pm.getSystemValue("BillingServer")
    # return "#{sBillingServer}#{"/"}#{I18n.locale.to_s}#{url}"
    return "#{sBillingServer}#{url}"
  end
  def Pm.getWeenBillingServer(url="")
    # return @@BillingServer
    sBillingServer = Pm.getSystemValue("WeenBillingServer")
    # return "#{sBillingServer}#{"/"}#{I18n.locale.to_s}#{url}"
    return "#{sBillingServer}#{"/"}#{url}"
  end
  def Pm.GetWeenServer(url="")
    sServer = Pm.getSystemValue("WeenServer")
    # return "#{sServer}#{"/"}#{I18n.locale.to_s}#{url}"
    return "#{sServer}#{url}"
  end

  def Pm.makeDouble(a)
    return "#{'%.02f' % a}"
  end

  def Pm.GetPeStr(sId)
    sId_full = "IDS_" + sId.upcase
    return Pm.GetStr(sId_full)
  end
  def Pm.saveTestDb(key,value)
      tdb = Testdb.new
      tdb.key = key
      # if (!value.include?("utf8")) then
      #   value = Iconv.conv('utf-8', 'big5', value)
      # end
      tdb.value = value
      tdb.save!
  end
  def Pm.saveTestDb2(key,value)
      tdb = Testdb.new
      tdb.key = key
      # if (!value.include?("utf8")) then
      #   value = Iconv.conv('utf-8', 'big5', value)
      # end
      tdb.hValue = value
      tdb.save!
      return tdb.id
  end
  def Pm.findTestDbValue(id)
    tdb = Testdb.find_by_id(id)
    if (tdb == nil) then
      return nil
    end
    return tdb.hValue
  end
  def Pm.updateTestDbValue(id,value)
    tdb = Testdb.find_by_id(id)
    if (tdb != nil) then
      tdb.hValue = value
      tdb.save!
    end
  end
  def Pm.changeTestDbKeys(oldKey,old_post,new_post,count=10)
    tdbs = Testdb.check_key_like(oldKey).limit(count)
    if (tdbs != nil) then
      tdbs.each do |tdb|
        tdb.key = tdb.key.sub(old_post,new_post)
        tdb.save!
      end
    end
  end
  def Pm.changeTestDbKey(oldKey,newKey)
    tdb = Testdb.check_key(oldKey).last
    if (tdb != nil) then
      tdb.key = newKey
      tdb.save!
    end
  end
  def Pm.findTestDbHashValue_key(key)
    tdb = Testdb.check_key(key).last
    if (tdb == nil) then
      return {}
    end
    return tdb.hValue.clone
  end
  def Pm.updateTestDbHashValue_key(key,value)
    tdb = Testdb.check_key(key).last
    if (tdb == nil) then
      tdb = Testdb.new
      tdb.key = key
    end
    tdb.hValue = value
    tdb.save!
  end
  def Pm.get_session_data_key(user_id,ap_name)
    # return "session_data_#{ap_name}_#{user_id}"
    return "session_data_#{user_id}"
  end
  def Pm.get_session_data_from_db(user_id,ap_name)
    if (user_id == nil) then
      return {}
    end
    key = Pm.get_session_data_key(user_id,ap_name)
    tdb = Testdb.check_key(key).last
    if (tdb == nil) then
      return {}
    end
    return tdb.hValue
  end
  def Pm.save_session_data_to_db(user_id,ap_name,session_data)
    if (user_id != nil) then
      key = Pm.get_session_data_key(user_id,ap_name)
      tdb = Testdb.check_key(key).last
      if (tdb == nil) then
        tdb = Testdb.new
        tdb.key = key
      end
      tdb.hValue = session_data
      tdb.save!
    end
  end

  def Pm.GetStr_N(strToFind,n)
    sBuf = Pm.GetStr(strToFind)
    if (sBuf != nil) then
      return sBuf[0,n]
    else
      return ""
    end
  end

  def Pm.logger_debug(s)
    logger = Logger.new(STDOUT)
    logger.debug s
  end
  def Pm.logger_info(s)
    logger = ActiveSupport::Logger.new(STDOUT)
    # logger = Logger.new(STDOUT)
    logger.info s
    # logger.fatal s
  end
end
