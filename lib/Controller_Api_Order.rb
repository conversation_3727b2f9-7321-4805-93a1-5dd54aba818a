require("Xdate.rb")
require("WeenApi.rb")
require("AllpayConstant.rb")

module Controller_Api_Order
  # for orders
  def ween_billing_get_allpay_checkout_data(hPar,client_ip)
    # 共用的orders
    h_for_allpay = create_order_info(hPar,client_ip)
    return allpay_get_checkout_data(h_for_allpay)
  end
  def create_order_info(hPar,client_ip)
    oOrder = save_order(hPar,client_ip)

    h_order = oOrder.serializable_hash

    return orderinfo_2_allpayinfo(h_order,hPar)
  end
  def save_order(hPar,client_ip)
    oOrder = Order.new
    oOrder.server_name = hPar["server_name"]
    oOrder.user_id = hPar["user_id"]
    oOrder.orderno = hPar["orderno"]
    oOrder.product_id = hPar["product_id"]
    oOrder.original_amount = hPar["original_amount"]
    oOrder.discount_amount = hPar["discount_amount"]
    oOrder.paid_amount = hPar["paid_amount"]
    # oOrder.amount = hPar["amount"]
    oOrder.payment_type = hPar["paymenttype"]
    oOrder.feedback_url = hPar["feedback_url"]
    oOrder.receive_url = hPar["receive_url"]
    oOrder.client_ip = client_ip
    oOrder.status = -1
    oOrder.save!
    return oOrder
  end
  def orderinfo_2_allpayinfo(h_order,hPar)
    h = Hash.new

    h["order_id"] = h_order["id"]
    h["user_id"] = h_order["user_id"]
    h["server_name"] = hPar["server_name"]
    h["product_id"] = hPar["product_id"]
    h["original_amount"] = hPar["original_amount"]
    h["discount_amount"] = hPar["discount_amount"]
    h["paid_amount"] = hPar["paid_amount"]
    h["paytitle"] = hPar["paytitle"]
    h["paymemo"] = hPar["paymemo"]
    h["receive_url"] = hPar["receive_url"]
    h["ItemName"] = hPar["paytitle"]
    h["TradeDesc"] = hPar["paymemo"]
    h["TotalAmount"] = hPar["paid_amount"]
    h["DeviceSource"] = hPar["DeviceSource"]

    h["email"] = hPar["email"]
    h["phoneno"] = hPar["phoneno"]
    h["username"] = hPar["username"]

    h["paymenttype"] = hPar["paymenttype"]
    if (h["paymenttype"] == Allpay_ChoosePayment::UnionPay) then
      h["ChoosePayment"] = Allpay_ChoosePayment::Credit
      h["UnionPay"] = "1"
    else
      h["ChoosePayment"] = hPar["paymenttype"]
      h["UnionPay"] = ""
    end
    h["ChooseSubPayment"] = Allpay_ChoosePayment.check_choosesubpayment(h["ChoosePayment"],hPar["ChooseSubPayment"])

    return h
  end
  def makeNewOrderno()
    t = Xdate.GetNowRails()
    sOrderno = "%04d%02d%02d%02d%02d%02d" % [t.year,t.month,t.day,t.hour,t.min,t.sec]
    if (Order.find_by_orderno(sOrderno) != nil) then
      return makeNewOrderno()
    end
    return sOrderno
  end
  # only allpay_paymentinfo call
  def update_order_when_server_created_order(order_id,is_RtnCode_valid,hPars)
  # Pm.saveTestDb("update_order_when_server_created_order","#{hPars}")
    h = allpayinfo_2_orderinfo_when_server_created_order(order_id,hPars)
    update_order_paymenttype(order_id,h["paymenttype"])
    feedback_url,receive_url,bSuccess = update_order_status_when_server_created_order(order_id,is_RtnCode_valid,h["rc"])
    if (bSuccess) then
      save_payment_when_server_created_order(order_id,h)
    end
    return bSuccess
  end
  def allpayinfo_2_orderinfo_when_server_created_order(order_id,hPars)
    h = Hash.new

    h["MerchantID"] = hPars["MerchantID"]
    # 廠商交易編號
    h["order_id"] = order_id
    # 交易狀態 1:付款成功。 800:貨到付款訂單建立成功。 ATM:2 取號成功 CVS：10100073 取號成功 其餘代碼為失敗。
    h["rc"] = hPars["RtnCode"]
    # 交易訊息
    h["RtnMsg"] = hPars["RtnMsg"]
    # allpay 的交易 編號  請保􏰁 allpay 的交易編號與 MerchantTradeNo 的關連。
    h["TradeNo"] = hPars["TradeNo"]
    # 交易金額
    h["paid_amount"] = hPars["TradeAmt"]
    # 付款時間
    h["paid_date"] = hPars["PaymentDate"]
    # 會員選擇的付 款方式
    h["paymenttype"] = hPars["PaymentType"] # Credit_CreditCard
    # 通路費
    h["PaymentTypeChargeFee"] = hPars["PaymentTypeChargeFee"]
    # 訂單成立時間
    h["TradeDate"] = hPars["TradeDate"]

    return h
  end

  def update_order_paymenttype(order_id,paymenttype)
    oOrder = Order.find_by_id(order_id)
    if (oOrder != nil) then
      oOrder.payment_type = paymenttype
      oOrder.save!
    end
  end
  # only allpay_paymentinfo call
  def update_order_status_when_server_created_order(order_id,is_RtnCode_valid,rc)
    # saveTestDb("#{order_id}update_order_status","order_id,#{order_id},rc,#{rc}")
    if (order_id == nil) then
      return "","",false
    end

    oOrder = Order.find_by_id(order_id)
    if (oOrder != nil) then
      if (oOrder.status != -1) then
        return "","",false
      end

    # saveTestDb("#{order_id}update_order_status","order_id,#{order_id},rc,#{rc},is_RtnCode_valid,#{is_RtnCode_valid}")
      if (is_RtnCode_valid) then
        # oOrder.status = 0 created 時 order尚未完成，不改status。改了會造成 finish時無法作用
        oOrder.status = rc
        bSuccess = true
      else
        oOrder.status = rc
        bSuccess = false
      end
      feedback_url = oOrder.feedback_url
      receive_url = oOrder.receive_url
      oOrder.save!
      return feedback_url,receive_url,bSuccess
    else
      return "","",false
    end
  end
  def save_payment_when_server_created_order(order_id,hPar)
    oOrder = Order.find_by_id(order_id)
    oPayment = Payment.where(:order_id => order_id).last
    if (oPayment == nil) then
      oPayment = Payment.new
    end
    h = oPayment.vsd
    h["paymentinfo"] = hPar
    oPayment.order_id = order_id
    oPayment.server_name = oOrder.server_name
    oPayment.user_id = oOrder.user_id
    oPayment.orderno = oOrder.orderno
    oPayment.original_amount = oOrder.original_amount
    oPayment.discount_amount = oOrder.discount_amount
    oPayment.paid_amount = hPar["paid_amount"]
    oPayment.payment_type = oOrder.payment_type # ChoosePayment
    oPayment.paid_date = nil
    oPayment.vsd = h
    oPayment.vendor_srv_status = ""
    oPayment.save!
  end

  # only allpay_return call
  def finish_order(order_id,hPars,is_RtnCode_valid)
    h = allpayinfo_2_orderinfo_when_finish_order(order_id,hPars)
    update_order_paymenttype(order_id,h["paymenttype"])
    feedback_url,receive_url,bSuccess = update_order_status_when_finish_order(order_id,is_RtnCode_valid,h["rc"])
    if (bSuccess) then
      save_payment_when_finish_order(order_id,h,hPars)
    end
    return bSuccess,h,feedback_url
  end
  def allpayinfo_2_orderinfo_when_finish_order(order_id,hPars)
    h = Hash.new

    h["MerchantID"] = hPars["MerchantID"]
    # 廠商交易編號
    h["order_id"] = order_id
    # 交易狀態 1:付款成功。 800:貨到付款訂單建立成功。 其餘代碼為失敗。
    h["rc"] = hPars["RtnCode"]
    # 交易訊息
    h["RtnMsg"] = hPars["RtnMsg"]
    # allpay 的交易 編號  請保􏰁 allpay 的交易編號與 MerchantTradeNo 的關連。
    h["TradeNo"] = hPars["TradeNo"]
    # 交易金額
    h["paid_amount"] = hPars["TradeAmt"]
    # 付款時間
    h["paid_date"] = hPars["PaymentDate"]
    # 會員選擇的付 款方式
    h["paymenttype"] = hPars["PaymentType"] # Credit_CreditCard
    # 通路費
    h["PaymentTypeChargeFee"] = hPars["PaymentTypeChargeFee"]
    # 訂單成立時間
    h["TradeDate"] = hPars["TradeDate"]

    return h
  end
  # only allpay_return call
 # 0：已建立（僅建立訂單）1：已付款； 2:已銷帳（消費者已繳款，並與收款業者銷帳）
  def update_order_status_when_finish_order(order_id,is_RtnCode_valid,rc)
    saveTestDb("#{order_id}update_order_status","order_id,#{order_id},rc,#{rc}")
    if (order_id == nil) then
      return "","",false
    end

    oOrder = Order.find_by_id(order_id)
    if (oOrder != nil) then
      if (is_RtnCode_valid) then
        # if (oOrder.status != -1) then
        if !([10100073,2,-1].include?(oOrder.status)) then
          return "","",false
        end
        oOrder.status = 0
        bSuccess = true
      else
        oOrder.status = rc
        bSuccess = false
      end
      feedback_url = oOrder.feedback_url
      receive_url = oOrder.receive_url
      oOrder.save!
      return feedback_url,receive_url,bSuccess
    else
      return "","",false
    end
  end

  def save_payment_when_finish_order(order_id,hPar_local,hPar_server)
    oOrder = Order.find_by_id(order_id)
    oPayment = Payment.where(:order_id => order_id).last
    if (oPayment == nil) then
      oPayment = Payment.new
    end
    h = oPayment.vsd
    h["local"] = hPar_local
    h["server"] = hPar_server
    oPayment.order_id = order_id
    oPayment.server_name = oOrder.server_name
    oPayment.user_id = oOrder.user_id
    oPayment.orderno = oOrder.orderno
    oPayment.original_amount = oOrder.original_amount
    oPayment.discount_amount = oOrder.discount_amount
    oPayment.paid_amount = hPar_local["paid_amount"]
    oPayment.payment_type = oOrder.payment_type # ChoosePayment
    oPayment.paid_date = hPar_local["paid_date"]
    oPayment.vsd = h
    oPayment.vendor_srv_status = hPar_local["rc"]
    oPayment.save!
  end
  def delete_payment_when_order_failed(order_id)
    oPayment = Payment.where(:order_id => order_id).last
    if (oPayment != nil) then
      Payment.destroy(oPayment.id)
    end
  end
  def my_post(sHttp,data)
    if (sHttp.length > 0) then
      uri = URI.parse(sHttp)
      if (sHttp.include?("https://")) then
        res = post_https(uri,data)
      else
        res = post_http(uri,data)
      end
    else
      # render :layout => false
      res = nil
    end
    return res
  end
  def post_http(uri,data)
    res = Net::HTTP.post_form(uri,data)
    # puts "res.header => #{res.header}"
    # puts "res.body => #{res.body}"
    return res
  end
  def post_https(uri,data)
    # uri = URI.parse("https://secure.com/")
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    http.verify_mode = OpenSSL::SSL::VERIFY_NONE
    # http.verify_mode = OpenSSL::SSL::VERIFY_PEER

    # request = Net::HTTP::Get.new(uri.request_uri)
    request = Net::HTTP::Post.new(uri.request_uri)
    request.set_form_data(data)

    response = http.request(request)
    # response.body
    # response.status
    # response["header-here"] # All headers are lowercase
    return response
  end
  def send_back_to_feedback_url(order_id,is_RtnCode_valid,hPars,feedback_url)
    # for meen.tw
    oOrder = Order.find_by_id(order_id)
    if (oOrder != nil) then
      hPars["orderme_id"] = oOrder.orderno
    else
      hPars["orderme_id"] = 0
    end
    hPars["rc"] = is_RtnCode_valid ? 0 : -1
    hPars["order_id"] = order_id
    # for meen.tw end
    # Pm.saveTestDb("send_back_to_feedback_url",hPars)

    # Pm.saveTestDb("send_back_to_feedback_url",feedback_url)
    res = my_post(feedback_url,hPars)
    # Pm.saveTestDb("res.body",res.body)
    # Pm.saveTestDb("send_back_to_feedback_url res",res.body)
    if (res.body == "1|OK") then
      return true
    else
      return false
    end
  end
end
