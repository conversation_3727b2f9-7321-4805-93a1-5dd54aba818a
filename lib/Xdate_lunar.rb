require("Xdate_Constant.rb")
require("Xdate_Variable.rb")
require("Pm.rb")
require("SkyEarthFive_Function.rb")
require("Cfate.rb")

class Xdate
  #---------------------------------------------------------------------
  #return mArray contains [month,days,bLeapMonth]
  def Xdate.GetEastMonthArray(nEYear)
    mArray = Array.new
    if (!Xdate.IsYearLegal?(nEYear)) then
      return mArray
    end

    nLeapMonth = (@@LunarInfo[nEYear - 1900] & 0xF)
    bLeapMonth = nLeapMonth > 0 ? true : false

    (0..11).each do |i|
          if ((@@LunarInfo[nEYear - 1900] & @@MonthMask[i]) != 0) then
              mArray.push([i+1,30,false])
          else
              mArray.push([i+1,29,false])
          end
          if (bLeapMonth && nLeapMonth == i+1) then
              if ((@@LunarInfo[nEYear - 1900] & 0x10000) != 0) then
                  mArray.push([nLeapMonth,30,true])
              else
                  mArray.push([nLeapMonth,29,true])
              end
          end
    end
    return mArray
  end

  #---------------------------------------------------------------------
  # [a][b]
  # a : 表示月份
  # b : 0 表示月份,1表日數,2表潤月
  def Xdate.GetEastMonthDays(nEYear, nEMonth, bLeapMonth)
    mArray = Xdate.GetEastMonthArray(nEYear)

    nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,mArray)
    if (nMonthIndex != nil) then
      return mArray[nMonthIndex][1]
    else
      return 0
    end
  end

  #---------------------------------------------------------------------
  def Xdate.GetEastYearDays(nEYear)
    mArray = Xdate.GetEastMonthArray(nEYear)
    nDays = 0
    mArray.each { |aMonth| nDays += aMonth[1]}
    return nDays
  end

  #---------------------------------------------------------------------
  def Xdate.GetLegalEMonthDay(nEYear,nEMonth,nEDate,bLeapMonth)
    if (nEDate < 1) then
      return 1
    end

    mArray = Xdate.GetEastMonthArray(nEYear)
    nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,mArray)
    if (nMonthIndex == nil) then
      return 1
    end

    if (nEDate < mArray[nMonthIndex][1]) then
      return nEDate
    else
      return mArray[nMonthIndex][1]
    end
  end

  #---------------------------------------------------------------------
  def Xdate.GetEastYearDaysFrom1900(nEYear)
    nDays = 0
    #... means 1900 to nYear - 1
    (1900...nEYear).each do |i|
      nDays += Xdate.GetEastYearDays(i)
    end
    return nDays
  end

  #---------------------------------------------------------------------
  def Xdate.GetEastMonthDaysFrom11(nEYear,nEMonth,bLeapMonth)
    mArray = Xdate.GetEastMonthArray(nEYear)
    nDays = 0
    nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,mArray)
    if (nMonthIndex == nil) then
      return nDays
    end

      (0...nMonthIndex).each do |i|
        nDays += mArray[i][1]
      end

      return nDays
  end

  #---------------------------------------------------------------------
  def Xdate.GetEDaysThisYear(nEYear, nEMonth, nEDate, bLeapMonth)
    #month days
    nDays = Xdate.GetEastMonthDaysFrom11(nEYear, nEMonth, bLeapMonth)

      #date days
      nDays += nEDate

    return nDays
  end

  #---------------------------------------------------------------------
  def Xdate.GetTotalEastDaysFrom190011(nEYear, nEMonth, nEDate, bLeapMonth)
#puts("nEYear:#{nEYear}, nEMonth:#{nEMonth}, nEDate:#{nEDate}, bLeapMonth:#{bLeapMonth}")
    if (!(Xdate.IsEDateLegal?(nEYear,nEMonth,nEDate,bLeapMonth))) then
      return 0
    end
    #year days before nEYear
    nDays = Xdate.GetEastYearDaysFrom1900(nEYear)

    #DaysThisYear
    nDays += Xdate.GetEDaysThisYear(nEYear,nEMonth, nEDate, bLeapMonth)

    return nDays
  end

  #---------------------------------------------------------------------
  #return WYear,WMonth,WDate
  def Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
    nDays = Xdate.GetTotalEastDaysFrom190011(nEYear, nEMonth,nEDate,bLeapMonth)
      nDays += 30 #農曆1900/1/1是從國曆1900/1/31開始,所以先加回30天

    iY = 1900
    while (nDays > 0)
      niYDays = Xdate.GetWYearDays(iY)
      if (nDays > niYDays) then
        nDays -= niYDays
      else
        nWYear = iY
        mArray = Xdate.GetWMonthArray(nWYear)
        iM = 0
        while (nDays > 0)
          if (nDays > mArray[iM]) then
            nDays -= mArray[iM]
          else
            nWMonth = iM + 1
            nWDate = nDays
            nDays = 0
          end
          iM += 1
        end
      end
      iY += 1
    end

    return nWYear,nWMonth,nWDate
  end

  #---------------------------------------------------------------------
  #return nLeapMonth and bIsLeapMonth
  def Xdate.IsLeapEYear?(nEYear)
      nLeapMonth = @@LunarInfo[nEYear - 1900] & 0xF
      bLeapMonth = (nLeapMonth >= 1 && nLeapMonth <= 12) ? true : false
      return nLeapMonth,bLeapMonth
  end
  #---------------------------------------------------------------------
  #check if Lunar Year
  def Xdate.IsELunarYear?(nEYear)
    mArray = Xdate.GetEastMonthArray(nEYear)
    return (mArray.length == 13) ? true : false
  end


  def Xdate.GetLegalEDate(nEYear,nEMonth,nEDate,bLeapMonth)
    if (!Xdate.IsEDateLegal?(nEYear,nEMonth,nEDate,bLeapMonth)) then
      if (!Xdate.IsYearLegal?(nEYear)) then
        nEYear = Xdate.GetNowRails().year
      end
      if (!Xdate.IsEMonthLegal?(nEYear,nEMonth,bLeapMonth)) then
        nEMonth = 6 #Xdate.GetNowRails().month
        bLeapMonth = false
      end
      if (!Xdate.IsEDateLegal?(nEYear,nEMonth,nEDate,bLeapMonth)) then
        nEDate = 1 #Xdate.GetEMonthDays(nEYear,nEMonth,bLeapMonth)
      end
      #nEYear,nEMonth,nEDate,bLeapMonth = Xdate.PreEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      #return Xdate.GetLegalEDate(nEYear,nEMonth,nEDate,bLeapMonth)
    end
    return nEYear,nEMonth,nEDate,bLeapMonth
  end

  #---------------------------------------------------------------------
  #input nEYear,nEMonth,nEDate,bLeapMonth,nETime
  #return next nEYear,nEMonth,nEDate,bLeapMonth,nETime
  def Xdate.NextEDateFromTime(nEYear,nEMonth,nEDate,bLeapMonth,nETime)
    if (!Xdate.IsEDateLegal?(nEYear,nEMonth,nEDate,bLeapMonth)) then
      return 1900,1,1,false,0
    end

    if (nETime != 12) then
      return nEYear,nEMonth,nEDate,bLeapMonth,nETime
    end

    #when nETime == 12,return next day
    nETime = 0
    mArray = Xdate.GetEastMonthArray(nEYear)
    nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,mArray)
    if (nMonthIndex == nil) then
      return 1900,1,1,false,0
    end

    #normal add one day
    if (nEDate < mArray[nMonthIndex][1]) then
      nEDate += 1
    else
      nEDate = 1
      #to next month
      nMonthIndex += 1

      #to next year if end of year
      if (nMonthIndex >= mArray.length) then
        nEMonth = 1
        nEYear += 1
      else
        nEMonth = mArray[nMonthIndex][0]
        bLeapMonth = mArray[nMonthIndex][2]
      end
    end

    return nEYear,nEMonth,nEDate,bLeapMonth,nETime
  end

  def Xdate.NextNEDate(nEYear,nEMonth,nEDate,bLeapMonth,nDays)
    (0...nDays).each do |i|
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
    end
    return nEYear,nEMonth,nEDate,bLeapMonth
  end

  # 只能加小於30天
  def Xdate.NextNEDates(nEYear,nEMonth,nEDate,bLeapMonth,nDays)
    if (!Xdate.IsEDateLegal?(nEYear,nEMonth,nEDate,bLeapMonth)) then
      return 1900,1,1,false
    end

    mArray = Xdate.GetEastMonthArray(nEYear)
    nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,mArray)
    if (nMonthIndex == nil) then
      return 1900,1,1,false,0
    end

    #normal add one day
    if (nEDate + nDays <= mArray[nMonthIndex][1]) then
      nEDate += nDays
    else
      nEDate = nEDate + nDays - mArray[nMonthIndex][1]
      #to next month
      nMonthIndex += 1

      #to next year if end of year
      if (nMonthIndex >= mArray.length) then
        nEMonth = 1
        nEYear += 1
      else
        nEMonth = mArray[nMonthIndex][0]
        bLeapMonth = mArray[nMonthIndex][2]
      end
    end

    return nEYear,nEMonth,nEDate,bLeapMonth
  end

  def Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
    if (!Xdate.IsEDateLegal?(nEYear,nEMonth,nEDate,bLeapMonth)) then
      return 1900,1,1,false
    end

    mArray = Xdate.GetEastMonthArray(nEYear)
    nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,mArray)
    if (nMonthIndex == nil) then
      return 1900,1,1,false,0
    end

    #normal add one day
    if (nEDate < mArray[nMonthIndex][1]) then
      nEDate += 1
    else
      nEDate = 1
      #to next month
      nMonthIndex += 1

      #to next year if end of year
      if (nMonthIndex >= mArray.length) then
        nEMonth = 1
        nEYear += 1
      else
        nEMonth = mArray[nMonthIndex][0]
        bLeapMonth = mArray[nMonthIndex][2]
      end
    end

    return nEYear,nEMonth,nEDate,bLeapMonth
  end

  def Xdate.PreNEDate(nEYear,nEMonth,nEDate,bLeapMonth,nDays)
    (0...nDays).each do |i|
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.PreEDate(nEYear,nEMonth,nEDate,bLeapMonth)
    end
    return nEYear,nEMonth,nEDate,bLeapMonth
  end
  def Xdate.PreEDate(nEYear,nEMonth,nEDate,bLeapMonth)
    mArray = Xdate.GetEastMonthArray(nEYear)
    nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,mArray)
    if (nMonthIndex == nil) then
      return 1900,1,1,false,0
    end

    #normal minus one day
    if (nEDate > 1) then
      nEDate -= 1
    else
      if (nMonthIndex == 0) then
        nEYear -= 1
        mArray = Xdate.GetEastMonthArray(nEYear)
        if (mArray.length == 0) then
          return 1900,1,1,false,0
        end
        nMonthIndex = mArray.length - 1
      else
        nMonthIndex -= 1
      end
      nEMonth = mArray[nMonthIndex][0]
      bLeapMonth = mArray[nMonthIndex][2]
      nEDate = mArray[nMonthIndex][1]
    end

    return nEYear,nEMonth,nEDate,bLeapMonth
  end
  def Xdate.NextEMonth(nEYear,nEMonth,bLeapMonth)
    mArray = Xdate.GetEastMonthArray(nEYear)
    nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,mArray)
    if (nMonthIndex == nil) then
      return 1900,1,29,false
    end
    if (nMonthIndex == mArray.length - 1) then
      nEYear += 1
      mArray = Xdate.GetEastMonthArray(nEYear)
      if (mArray.length == 0) then
        return 1900,1,29,false
      end
      nMonthIndex = 0
    else
      nMonthIndex += 1
    end
    nEMonth = mArray[nMonthIndex][0]
    bLeapMonth = mArray[nMonthIndex][2]
    nEDays = mArray[nMonthIndex][1]

    return nEYear,nEMonth,nEDays,bLeapMonth
  end

  #---------------------------------------------------------------------
  def Xdate.IsEMonthLegal?(nEYear,nEMonth,bLeapMonth)
    if (!Xdate.IsYearLegal?(nEYear)) then
      return false
    end
    if (!Xdate.IsWMonthLegal?(nEMonth)) then
      return false
    end
    if ([true,false].index(bLeapMonth) == nil) then
      return false
    end

    mArray = Xdate.GetEastMonthArray(nEYear)
    nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,mArray)
    return (nMonthIndex != nil) ? true : false
  end

  def Xdate.GetEMonthIndex(nEMonth,bLeapMonth,mArray)
    (0...mArray.length).each do |nMonthIndex|
      if ((mArray[nMonthIndex][0] == nEMonth) && (bLeapMonth == mArray[nMonthIndex][2])) then
        return nMonthIndex
      end
    end
    return nil
  end

  #---------------------------------------------------------------------
  def Xdate.GetEMonthDays(nEYear,nEMonth,bLeapMonth)
    mArray = Xdate.GetEastMonthArray(nEYear)
    nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,mArray)
    return (nMonthIndex != nil) ? mArray[nMonthIndex][1] : 0
  end

  #---------------------------------------------------------------------
  def Xdate.IsEDateLegal?(nEYear,nEMonth,nEDate,bLeapMonth,*args)
    if (!Xdate.IsYearLegal?(nEYear)) then
      return false
    end
    if (!Xdate.IsEMonthLegal?(nEYear,nEMonth,bLeapMonth)) then
      return false
    end
    nDays = Xdate.GetEMonthDays(nEYear,nEMonth,bLeapMonth)

    if (nDays == 0) then
      return false
    end

    if (nEDate == nil) then
      return false
    end

    bRet = nEDate.between?(1,nDays)
    if ((args[0] == nil) || (bRet == false)) then
        return bRet
      end

      bRet = args[0].between?(0,23) #hours
      if (args.length > 1) then
        bRet = bRet && args[1].between?(0,59) #minutes
      end
      return bRet
  end

  #---------------------------------------------------------------------
  # Input the east(農曆)  "Year", and then return the GanZhi of year.
  def Xdate.GetLunarYearGanZhi(nEYear, nEMonth=1, nEDate=1, bLeapMonth=false, nETime=1)
    return Xdate.GetYearGanZhi(nEYear)
  end

  def Xdate.GetLunarYearGanZhiStr(nEYear, nEMonth=1, nEDate=1, bLeapMonth=false, nETime=1)
      nGanZhiYear = Xdate.GetLunarYearGanZhi(nEYear, nEMonth, nEDate, bLeapMonth, nETime)
    sBuf = Xdate.GetGanZhiYear_Str(nGanZhiYear)
    return sBuf
  end

  #---------------------------------------------------------------------
  # Input the west "Year" and "Month", and then return the GanZhi of month.
  # nHour is from "0" to "24".
  def Xdate.GetLunarMonthGanZhi(nEYear, nEMonth, nEDate=1, bLeapMonth=false, nETime=1)
    return Xdate.GetMonthGanZhi(nEYear, nEMonth)
  end
  def Xdate.GetLunarMonthGanZhiStr(nEYear, nEMonth, nEDate=1, bLeapMonth=false, nETime=1)
      nGanZhiMonth = Xdate.GetLunarMonthGanZhi(nEYear, nEMonth, nEDate, bLeapMonth, nETime)
    sBuf = Xdate.GetGanZhiMonth_Str(nGanZhiMonth)
    return sBuf
  end

  #---------------------------------------------------------------------
  # Input the east(農曆)  "Year" and "Month", and then return the GanZhi of month.
  def Xdate.GetLunarMonthGanZhi_old(nEYear, nEMonth)
      nYearCount = nEYear - Xdate::BASE_YEAR
      nMonthCount = nYearCount * 12 + nEMonth - 1
      nGanZhiMonth = (nMonthCount + Xdate::BASE_LUNAR_GANZHI_MONTH) % 60;

      return nGanZhiMonth
  end


  def Xdate.GetLunarDateGanZhi(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType=nil)
      nDays = Xdate.GetLunarDateDays(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType)

      nGanZhiDay = Xdate.GetDateGanZhi(nDays)
      return nGanZhiDay
  end
  def Xdate.GetLunarDateGanZhiStr(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType=nil)
      nGanZhiDay = Xdate.GetLunarDateGanZhi(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType)
    sBuf = Xdate.GetGanZhiDate_Str(nGanZhiDay)
    return sBuf
  end

  def Xdate.GetLunarYearGanZhiSkyEarth(nEYear)
      nGanZhi = Xdate.GetLunarYearGanZhi(nEYear)
      return Xdate.GetGanZhiSkyEarth(nGanZhi)
  end

  def Xdate.GetLunarMonthGanZhiSkyEarth(nEYear,nEMonth)
      nGanZhi = Xdate.GetLunarMonthGanZhi(nEYear, nEMonth)
      return Xdate.GetGanZhiSkyEarth(nGanZhi)
  end

  def Xdate.GetLunarDateGanZhiSkyEarth(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType=nil)
      nGanZhi = Xdate.GetLunarDateGanZhi(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType)
      return Xdate.GetGanZhiSkyEarth(nGanZhi)
  end

  def Xdate.GetLunarDateDays(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType=nil)
    # 若為23點nETime = 12,則日期加一天
    nEYear, nEMonth, nEDate, bLeapMonth, nETime1 = Xdate.NextEDateFromTime(nEYear,nEMonth,nEDate,bLeapMonth,nETime)
    nDays = Xdate.GetTotalEDaysFromW180111(nEYear,nEMonth,nEDate,bLeapMonth)
    if ((nFirstTimeType != nil) && (nETime >= 12)) then
      nDays -= 1  # 因1801年1月1日之干支調整
      # nDays 為明天之值，因為nDays += nAddDay
      if (nFirstTimeType == Xdate::FIRST_CURRENT) then
        # 分早子晚子時，今天日子明天時辰 陳明德老師版本夜子時
        nDays = nDays - 1
      else
        # 不分早子晚子時，明天日子明天時辰
        # nDays = nDays
      end
    else
      # 紫微用的不管子時的狀況
      nDays -= 1
    end

      return nDays
  end

  def Xdate.GetLunarTimeGanZhi(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType=nil)
      nChinaHour = nETime
    if (nChinaHour == 12)  then # It is tomorrow After the PM 11:00.
      nChinaHour = 0
      end
      nDays = Xdate.GetLunarTimeDays(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType)
      return Xdate.GetTimeGanZhi(nDays,nChinaHour)
  end
  def Xdate.GetLunarTimeDays(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType=nil)
    # 若為23點nETime = 12,則日期加一天
    nEYear, nEMonth, nEDate, bLeapMonth, nETime1 = Xdate.NextEDateFromTime(nEYear,nEMonth,nEDate,bLeapMonth,nETime)
    nDays = Xdate.GetTotalEDaysFromW180111(nEYear,nEMonth,nEDate,bLeapMonth)
    if ((nFirstTimeType != nil) && (nETime >= 12)) then
      nDays -= 1  # 因1801年1月1日之干支調整
      # nDays 為明天之值，因為nDays += nAddDay
      if (nFirstTimeType == Xdate::FIRST_CURRENT) then
        # 分早子晚子時，今天日子明天時辰 陳明德老師版本夜子時
        # nDays = nDays
      else
        # 不分早子晚子時，明天日子明天時辰
        # nDays = nDays
      end
    else
      # 紫微用的不管子時的狀況
        nDays -= 1
    end

      return nDays
  end

  def Xdate.GetLunarTimeGanZhiStr(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType=nil)
    nGanZhiHour = Xdate.GetLunarTimeGanZhi(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType)
    sBuf = Xdate.GetGanZhiTime_Str(nGanZhiHour)
    return sBuf
  end
  def Xdate.GetLunarTimeGanZhiStr2(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType=nil)
    nGanZhiHour = Xdate.GetLunarTimeGanZhi(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType)
    sBuf = Xdate.GetGanZhiTime_Str2(nGanZhiHour)
    return sBuf
  end

  def Xdate.GetLunarTimeGanZhiSkyEarth(nEYear,nEMonth,nEDate,nETime,bLeapMonth)
    nGanZhi = Xdate.GetLunarTimeGanZhiValue(nEYear,nEMonth,nEDate,nETime,bLeapMonth)
      return Xdate.GetGanZhiSkyEarth(nGanZhi)
  end

  def Xdate.GetLunarTimeGanZhiValue(nEYear,nEMonth,nEDate,nETime,bLeapMonth)
      nDays = Xdate.GetTotalEDaysFromW180111(nEYear,nEMonth,nEDate,bLeapMonth)

    if (nETime == 12)  then # It is tomorrow After the PM 11:00.
      nETime = 0
        nDays += 1
      end

      nDays -= 1
      nGanZhiHour = Xdate.GetTimeGanZhi(nDays,nETime)
      return nGanZhiHour
  end

  # 取得農曆干支曆的八字
  def Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, nETime,nFirstTimeType=nil)
    nHour = Xdate.ETime2Hour(nETime)
    if (!Xdate.IsWDateLegal?(nWYear,nWMonth,nWDate,nHour)) then
      return 0,0,0,0
    end
      nEYear, nEMonth, nEDate, bLeapMonth = Xdate.West2East(nWYear, nWMonth, nWDate)

      nGanZhiYear = Xdate.GetLunarYearGanZhi(nEYear, nEMonth, nEDate, bLeapMonth, nETime)
      nGanZhiMonth = Xdate.GetLunarMonthGanZhi(nEYear, nEMonth, nEDate, bLeapMonth, nETime)
      nGanZhiDay = Xdate.GetLunarDateGanZhi(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType)
      nGanZhiHour = Xdate.GetLunarTimeGanZhi(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType)

      return nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour
  end
  # 求出 nWYear, nWMonth, nWDate 之後的第一個 ganzhi 的日
  def Xdate.GetLunarGanZhiDate(ganzhi,nWYear, nWMonth, nWDate,nFirstTimeType=nil)
    nETime = 0
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, nETime,nFirstTimeType)

    n = ganzhi - nGanZhiDay
    if (n < 0) then
      n += 60
    end
    nWYear,nWMonth,nWDate = Xdate.NextNWDate(nWYear,nWMonth,nWDate,n)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, nETime,nFirstTimeType)
    return nWYear,nWMonth,nWDate,nGanZhiYear,nGanZhiMonth,nGanZhiDay
  end
  def Xdate.GetLunarGanZhiHour(ganzhi,nWYear, nWMonth, nWDate,nHour,nFirstTimeType=nil)
    nETime = Xdate.Hour2ETime(nHour)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, nETime,nFirstTimeType)

    n = ganzhi - nGanZhiHour
    if (n < 0) then
      n += 60
    end
    n *= 2

    nWYear,nWMonth,nWDate,nHour = Xdate.NextNHour(nWYear,nWMonth,nWDate,nHour,n)
    nETime = Xdate.Hour2ETime(nHour)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, nETime,nFirstTimeType)
    return nWYear,nWMonth,nWDate,nHour,nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour
  end

  def Xdate.GetLunar8Words_Str(nWYear, nWMonth,nWDate, nETime,nFirstTimeType=nil,bSpace=true)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, nETime,nFirstTimeType)
    sBuf = Xdate.GetGanZhi_Str(nGanZhiYear)
    sBuf += Pm.GetStr("IDS_X_YEAR")
    sBuf += " " if bSpace

    sBuf += Xdate.GetGanZhi_Str(nGanZhiMonth)
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += " " if bSpace

    sBuf += Xdate.GetGanZhi_Str(nGanZhiDay)
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += " " if bSpace

    sBuf += Xdate.GetGanZhi_Str(nGanZhiHour)
    sBuf += Pm.GetStr("IDS_X_HOUR")
    return sBuf
  end
  def Xdate.GetLunar8Words_Str2(nWYear, nWMonth,nWDate, nETime,nFirstTimeType=nil)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, nETime,nFirstTimeType)
    sBuf = Xdate.GetGanZhi_Str(nGanZhiYear)
    sBuf += Pm.GetStr("IDS_X_YEAR")
    nEYear, nEMonth, nEDate, bLeapMonth = Xdate.West2East(nWYear, nWMonth, nWDate)
    sBuf += Pm.GetStr("IDS_X_LEAP") if bLeapMonth
    sBuf += "#{nEMonth}"
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += "#{nEDate}"
    sBuf += Pm.GetStr("IDS_X_DAY")
    return sBuf
  end
  def Xdate.GetLunar8Words_Str3(nWYear, nWMonth,nWDate, nWHour,nFirstTimeType=nil)
    nETime = Xdate.Hour2ETime(nWHour)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, nETime,nFirstTimeType)
    nEYear, nEMonth, nEDate, bLeapMonth = Xdate.West2East(nWYear, nWMonth, nWDate)
    s = ""
    s = Pm.GetStr("IDS_X_LEAP") if bLeapMonth
    sBuf = "#{Xdate.GetGanZhi_Str(nGanZhiYear)} #{s}#{nEMonth} #{nEDate}"
    return sBuf
  end
  # 農曆 辛亥 年 11 月 12 日 未 時
  def Xdate.GetLunar8Words_Str4(nWYear, nWMonth,nWDate, nETime,nFirstTimeType=nil)
    sBuf = Pm.GetStr("IDS_S_CAL_LUNAR2")
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, nETime,nFirstTimeType)
    sBuf += Xdate.GetGanZhi_Str(nGanZhiYear)
    sBuf += Pm.GetStr("IDS_X_YEAR")
    nEYear, nEMonth, nEDate, bLeapMonth = Xdate.West2East(nWYear, nWMonth, nWDate)
    sBuf += Pm.GetStr("IDS_X_LEAP") if bLeapMonth
    sBuf += "#{nEMonth}"
    sBuf += Pm.GetStr("IDS_X_MONTH")
    sBuf += "#{nEDate}"
    sBuf += Pm.GetStr("IDS_X_DAY")
    sBuf += Xdate.GetETimeStr(nETime)
    return sBuf
  end
  def Xdate.GetLunar8Words_Str5(nWYear, nWMonth,nWDate, nWHour,nFirstTimeType=nil)
    nETime = Xdate.Hour2ETime(nWHour)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, nETime,nFirstTimeType)
    nEYear, nEMonth, nEDate, bLeapMonth = Xdate.West2East(nWYear, nWMonth, nWDate)
    s = ""
    s = Pm.GetStr("IDS_X_LEAP") if bLeapMonth
    sBuf = "#{Xdate.GetGanZhi_Str(nGanZhiYear)}#{s}#{nEMonth}/#{nEDate}"
    sBuf += "(#{Xdate.GetGanZhi_Str(nGanZhiDay)}#{Pm.GetStr("IDS_X_DAY")})"
    return sBuf
  end


  def Xdate.GetTotalEDaysFromW180111(nEYear,nEMonth,nEDate,bLeapMonth)
    nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,nEMonth,nEDate,bLeapMonth)
      nDays = Xdate.GetTotalWDaysFrom180111(nWYear,nWMonth,nWDate)
      return nDays
  end

  def Xdate.GetEMonthStr(nEMonth,bLeapMonth)
    sBuf = ""
    if (bLeapMonth) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += Pm.GetStr("IDS_S_CHINESE_MONTH_#{nEMonth}")
    sBuf += Pm.GetStr("IDS_X_MONTH")
    return sBuf
  end
  def Xdate.GetEDayStr(nEDate)
    sBuf = ""
    sBuf += Pm.GetStr("IDS_S_CHINESE_DAY_#{nEDate}")
    sBuf += Pm.GetStr("IDS_X_DAY")
    return sBuf
  end
  def Xdate.GetEMonthDateStr(nEMonth,nEDate,bLeapMonth)
    sBuf = Pm.GetStr("nongminli.nong")
    sBuf += " "
    if (bLeapMonth) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += "#{nEMonth}/#{nEDate}"
    return sBuf
  end
  def Xdate.GetSimpleEMonthDateStr(nEYear,nEMonth,nEDate,bLeapMonth)
    sBuf = "#{nEYear}/"
    if (bLeapMonth) then
      sBuf += Pm.GetStr("IDS_X_LEAP")
    end
    sBuf += "#{nEMonth}/#{nEDate}"
    return sBuf
  end

  def Xdate.makeLunarDateStr(nEYear,nEMonth,nEDate,bLeapMonth,nWHour,par_YearDisplay=Cfate::PAN_DISPLAY_WEST)
    nETime = Xdate.Hour2ETime(nWHour)
    # sOut = Pm.GetStrWithQuote("IDS_S_UI_LUNAR_CAL")
    sOut = Xdate.GetFullEDateTimeStr(nEYear,nEMonth,nEDate,nETime,bLeapMonth,par_YearDisplay)
    return sOut
  end

  def Xdate.makeLunarGanZhiStr(nWYear,nWMonth,nWDate,nWHour,nWMinute,nFirstTimeType=nil)
    nETime = Xdate.Hour2ETime(nWHour)
    # sOut = Pm.GetStrWithQuote("IDS_S_UI_MOON_CAL")
    sOut = Xdate.GetLunar8Words_Str(nWYear, nWMonth,nWDate, nETime,nFirstTimeType,false)
    return sOut
  end

  def Xdate.same_month(eY1,eM1,bL1,eY2,eM2,bL2)
    return false if (eY1 != eY2)
    return false if (eM1 != eM2)
    return false if (bL1 != bL2)
    return true
  end
end
