require("SkyEarthFive_Function.rb")

class Eightword
 	# Flow Info for score
	def cp_fi_getInfo(key,sFunc,byWhatIdx=nil,arrayIndex=nil)
		if (byWhatIdx != nil) then
			if (arrayIndex == nil) then
				if (@EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key][byWhatIdx] == nil) then
					eval(sFunc)
				end
				return @EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key][byWhatIdx]
			else
				if (@EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key][byWhatIdx][arrayIndex] == nil) then
					eval(sFunc)
				end
				return @EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key][byWhatIdx][arrayIndex]
			end
		else
			if (arrayIndex == nil) then
				if (@EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key][Eightword::ByYearIdx] == nil) then
					eval(sFunc)
				end
				return @EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key]
			else
				if (@EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key][arrayIndex] == nil) then
					eval(sFunc)
				end
				return @EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key][arrayIndex]
			end
		end
	end
	def cp_fi_setInfo(key,byWhatIdx,byValue,arrayIndex=nil)
		byValue = cloneValue(byValue)
		if (byWhatIdx != nil) then
			if (arrayIndex == nil) then
				@EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key][byWhatIdx] = byValue
			else
				@EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key][byWhatIdx][arrayIndex] = byValue
			end
		else
			if (arrayIndex == nil) then
				@EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key] = byValue
			else
				@EightwordInfo[Eightword::FLOW_INFO][Eightword::PAN_FOUR_COL][key][arrayIndex] = byValue
			end
		end
	end
	def cp_fi_get8Words(byWhatIdx=nil)
		mainKey = Eightword::FLOW_INFO
		sFunc = "cp_fi_GanZhi8Words(#{@m_nPanType})"
		cp_get8Words(mainKey,sFunc,byWhatIdx=nil)
	end
	def cp_fi_set8Words(byValue,byWhatIdx=nil)
		mainKey = Eightword::FLOW_INFO
		cp_set8Words(mainKey,byValue,byWhatIdx)
	end
	# 取得各個流盤的八字
	def cp_fi_GanZhi8Words(nPanType)
	# Pm.saveTestDb("cp_fi_GanZhi8Words",caller[0])
		# nWYear,nWMonth,nWDate,nHour,nMinute = cp_fi_getFlowDates(nPanType)
		# h8Words = ew_get8Words(nWYear,nWMonth,nWDate,nHour,nMinute)
		h8Words = cp_fp_CurrentGanZhi8Words(nPanType)
		cp_fi_set8Words(h8Words)
	end
	def cp_fi_getFlowDates(nPanType)
		nWYear,nWMonth,nWDate,nHour,nMinute = uig_W_YMDHm()
		if (nPanType == Cfate::PAN_TENYEAR) then
			nWYear = fdg_WY()
		elsif (nPanType == Cfate::PAN_FLOWYEAR) then
			nWYear = fdg_WY()
		elsif (nPanType == Cfate::PAN_FLOWMONTH) then
			nWYear = fdg_WY()
			nWMonth = fdg_WM()
		elsif (nPanType == Cfate::PAN_FLOWDATE) then
			nWYear = fdg_WY()
			nWMonth = fdg_WM()
			nWDate = fdg_WD()
		elsif (nPanType == Cfate::PAN_FLOWTIME) then
			nWYear = fdg_WY()
			nWMonth = fdg_WM()
			nWDate = fdg_WD()
			nWHour = fdg_WH()
		end	
		return nWYear,nWMonth,nWDate,nHour,nMinute
	end

	def cp_fi_getCurSkyIndex()
		return cp_fi_getBySkyIndex(cp_fp_4c_getDateByWhatIdx(@m_nPanType))
	end
	def cp_fi_getBySkyIndex(byWhatIdx=nil)
		return cp_fi_getInfo(Eightword::BySky_4,"cp_fi_BySkyIndex_4()",byWhatIdx)
	end
	def cp_fi_setBySkyIndex(byValue,byWhatIdx=nil)
		cp_fi_setInfo(Eightword::BySky_4,byWhatIdx,byValue)
	end
	def cp_fi_BySkyIndex_4()
		h8Words = cp_fi_get8Words()
		hSkyIndex = cp_BySkyIndex_4(h8Words)
		cp_fi_setBySkyIndex(hSkyIndex)
	end

	def cp_fi_getCurEarthIndex()
		return cp_fi_getByEarthIndex(cp_fp_4c_getDateByWhatIdx(@m_nPanType))
	end
	def cp_fi_getByEarthIndex(byWhatIdx=nil)
		return cp_fi_getInfo(Eightword::ByEarth_4,"cp_fi_ByEarthIndex_4()",byWhatIdx)
	end
	def cp_fi_setByEarthIndex(byValue,byWhatIdx=nil)
		cp_fi_setInfo(Eightword::ByEarth_4,byWhatIdx,byValue)
	end
	def cp_fi_ByEarthIndex_4()
		h8Words = cp_fi_get8Words()
		hEarthIndex = cp_ByEarthIndex_4(h8Words)
		cp_fi_setByEarthIndex(hEarthIndex)
	end

	# 變異天干
	def cp_fi_getByChungSky(byWhatIdx=nil,arrayIndex=nil)
		return cp_fi_getInfo(Eightword::ByChungSky_4_3,"cp_fi_ByChungSky_4_3()",byWhatIdx,arrayIndex)
	end
	def cp_fi_setByChungSky(byValue,byWhatIdx=nil,arrayIndex=nil)
		cp_fi_setInfo(Eightword::ByChungSky_4_3,byWhatIdx,byValue,arrayIndex)
	end
	def cp_fi_ByChungSky_4_3()
		hEarthIndex = cp_fi_getByEarthIndex()
		hChungSky = cp_ByChungSky_4_3(hEarthIndex)
		cp_fi_setByChungSky(hChungSky)
	end

	# 天干之五行
	def cp_fi_getBySkyFive(byWhatIdx=nil)
		return cp_fi_getInfo(Eightword::BySkyFive_4,"cp_fi_BySkyFive_4()",byWhatIdx)
	end
	def cp_fi_setBySkyFive(byValue,byWhatIdx=nil)
		cp_fi_setInfo(Eightword::BySkyFive_4,byWhatIdx,byValue)
	end
	def cp_fi_BySkyFive_4()
		hSkyIndex = cp_fi_getBySkyIndex()
		aSkyFive = cp_BySkyFive_4(hSkyIndex)
		cp_fi_setBySkyFive(aSkyFive)
	end

	# 地支五行
	def cp_fi_getByEarthFive(byWhatIdx=nil)
		return cp_fi_getInfo(Eightword::ByEarthFive_4,"cp_fi_ByEarthFive_4()",byWhatIdx)
	end
	def cp_fi_setByEarthFive(byValue,byWhatIdx=nil)
		cp_fi_setInfo(Eightword::ByEarthFive_4,byWhatIdx,byValue)
	end
	def cp_fi_ByEarthFive_4()
	    hEarthIndex = cp_fi_getByEarthIndex()
	    aEarthFive = cp_ByEarthFive_4(hEarthIndex)
	    cp_fi_setByEarthFive(aEarthFive)
	end

	# 變異五行
	def cp_fi_getByChungFive(byWhatIdx=nil,arrayIndex=nil)
		return cp_fi_getInfo(Eightword::ByChungFive_4_3,"cp_fi_ByChungFive_4_3()",byWhatIdx,arrayIndex)
	end
	def cp_fi_setByChungFive(byValue,byWhatIdx=nil,arrayIndex=nil)
		cp_fi_setInfo(Eightword::ByChungFive_4_3,byWhatIdx,byValue,arrayIndex)
	end
	def cp_fi_ByChungFive_4_3()
		hEarthIndex = cp_fi_getByEarthIndex()
		aChungFive = cp_ByChungFive_4_3(hEarthIndex)
		cp_fi_setByChungFive(aChungFive)
	end

	# 四柱的主星
	def cp_fi_getByMainStar(byWhatIdx=nil)
		return cp_fi_getInfo(Eightword::ByMainStar_4,"cp_fi_ByMainStar_4()",byWhatIdx)
	end
	def cp_fi_setByMainStar(byValue,byWhatIdx=nil)
		cp_fi_setInfo(Eightword::ByMainStar_4,byWhatIdx,byValue)
	end
	# 所有的天干與日天干比較。甲為陽，乙為陰，以日天干為基準，
	# 我同者，陰陽相同者為比肩，不同者為劫財；
	# 生我者，陰陽相同者為偏印；不同者為正印
	# 剋我者，陰陽相同者為七殺；不同者為正官；
	# 我剋者，陰陽相同者為偏財；不同者為正財；
	# 我生者，陰陽相同者為食神；不同者為傷官；
	def cp_fi_ByMainStar_4()
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		hSkyIndex = cp_fi_getBySkyIndex()
		hMainStars = cp_ByMainStar_4(nDaySkyIndex,hSkyIndex,true)
		cp_fi_setByMainStar(hMainStars)
	end

	# 四柱的副星
	def cp_fi_getBySubStar(byWhatIdx=nil,arrayIndex=nil)
		return cp_fi_getInfo(Eightword::BySubStar_4_3,"cp_fi_BySubStar_4_3()",byWhatIdx,arrayIndex)
	end
	def cp_fi_setBySubStar(byValue,byWhatIdx=nil,arrayIndex=nil)
		cp_fi_setInfo(Eightword::BySubStar_4_3,byWhatIdx,byValue,arrayIndex)
	end
	def cp_fi_BySubStar_4_3()
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		aSubStar = cp_BySubStar_4_3(nDaySkyIndex,hEarthIndex)
		cp_fi_setBySubStar(aSubStar)
	end


	# 四柱的十二運
	def cp_fi_getBy12Win(byWhatIdx=nil)
		return cp_fi_getInfo(Eightword::By12Win_4,"cp_fi_By12Win_4()",byWhatIdx)
	end
	def cp_fi_setBy12Win(byValue,byWhatIdx=nil)
		cp_fi_setInfo(Eightword::By12Win_4,byWhatIdx,byValue)
	end
	def cp_fi_By12Win_4()
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		a12Win = cp_By12Win_4(nDaySkyIndex,hEarthIndex)
		cp_fi_setBy12Win(a12Win)
	end

	def cp_fi_getDwStarType(byWhatIdx=nil)
		return cp_fi_getInfo(Eightword::DwStarType_4,"cp_fi_DwStarType_4()",byWhatIdx)
	end
	def cp_fi_setDwStarType(byWhatIdx,byValue)
		cp_fi_setInfo(Eightword::DwStarType_4,byWhatIdx,byValue)
	end
	def cp_fi_DwStarType_4()
		dwStarType = cp_init8words_array(0,nil)
		(1..22).each do |nStar|
			sFunc = "cp_fi_DwStarType_God_#{nStar}(dwStarType)"
			dwStarType = eval(sFunc)
		end
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
			cp_fi_setDwStarType(byWhatIdx,dwStarType[byWhatIdx].uniq)
		end
	end
	# 三奇貴人
	def cp_fi_DwStarType_God_1(dwStarType)
		nYearSkyIndex = cp_fi_getBySkyIndex(Eightword::ByYearIdx)
		nMonthSkyIndex = cp_fi_getBySkyIndex(Eightword::ByMonthIdx)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		return cp_DwStarType_God_1(dwStarType,nYearSkyIndex,nMonthSkyIndex,nDaySkyIndex)
	end	

	# 魁罡貴人
	def cp_fi_DwStarType_God_2(dwStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		return cp_DwStarType_God_2(dwStarType,nDaySkyIndex,nDayEarthIndex)
	end

	# 金神
	def cp_fi_DwStarType_God_3(dwStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		nHourSkyIndex = cp_fi_getBySkyIndex(Eightword::ByHourIdx)
		nHourEarthIndex = cp_fi_getByEarthIndex(Eightword::ByHourIdx)
		return cp_DwStarType_God_3(dwStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex,nHourSkyIndex,nHourEarthIndex)
	end

	# 福星
	def cp_fi_DwStarType_God_4(dwStarType)
		nMonthSkyIndex = cp_fi_getBySkyIndex(Eightword::ByMonthIdx)
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		return cp_DwStarType_God_4(dwStarType,nMonthSkyIndex,nMonthEarthIndex)
	end
	# 天乙貴人
	def cp_fi_DwStarType_God_5(dwStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwStarType_God_5(dwStarType,nDaySkyIndex,hEarthIndex)
	end

	# 文昌
	def cp_fi_DwStarType_God_6(dwStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwStarType_God_6(dwStarType,nDaySkyIndex,hEarthIndex)
	end

	# 天德
	def cp_fi_DwStarType_God_7(dwStarType)
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		hSkyIndex = cp_fi_getBySkyIndex()
		return cp_DwStarType_God_7(dwStarType,nMonthEarthIndex,hSkyIndex)
	end

	# 月德
	def cp_fi_DwStarType_God_8(dwStarType)
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		hSkyIndex = cp_fi_getBySkyIndex()
		return cp_DwStarType_God_8(dwStarType,nMonthEarthIndex,hSkyIndex)
	end

	# 月將
	def cp_fi_DwStarType_God_9(dwStarType)
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		nCurSegment = cp_fi_get8Words(Eightword::Segment)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwStarType_God_9(dwStarType,nMonthEarthIndex,nCurSegment,hEarthIndex)
	end

	# 將星 日柱地支 年柱地支
	def cp_fi_DwStarType_God_10(dwStarType)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		nYearEarthIndex = cp_fi_getByEarthIndex(Eightword::ByYearIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwStarType_God_10(dwStarType,nDayEarthIndex,nYearEarthIndex,hEarthIndex)
	end

	# 華蓋 日柱地支 年柱地支
	def cp_fi_DwStarType_God_11(dwStarType)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		nYearEarthIndex = cp_fi_getByEarthIndex(Eightword::ByYearIdx)
		return cp_DwStarType_God_11(dwStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
	end

	# 學堂
	def cp_fi_DwStarType_God_12(dwStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		hSkyIndex = cp_fi_getBySkyIndex()
		return cp_DwStarType_God_12(dwStarType,nDaySkyIndex,hSkyIndex)
	end

	# 詞館
	def cp_fi_DwStarType_God_13(dwStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwStarType_God_13(dwStarType,nDaySkyIndex,hEarthIndex)
	end

	# 驛馬 日柱地支 年柱地支
	def cp_fi_DwStarType_God_14(dwStarType)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		nYearEarthIndex = cp_fi_getByEarthIndex(Eightword::ByYearIdx)
		return cp_DwStarType_God_14(dwStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
	end

	# 紅鸞 年柱地支
	def cp_fi_DwStarType_God_15(dwStarType)
		nYearEarthIndex = cp_fi_getByEarthIndex(Eightword::ByYearIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwStarType_God_15(dwStarType,nYearEarthIndex,hEarthIndex)
	end

	# 天喜 年柱地支
	def cp_fi_DwStarType_God_16(dwStarType)
		nYearEarthIndex = cp_fi_getByEarthIndex(Eightword::ByYearIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwStarType_God_16(dwStarType,nYearEarthIndex,hEarthIndex)
	end

	# 六秀
	def cp_fi_DwStarType_God_17(dwStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		return cp_DwStarType_God_17(dwStarType,nDaySkyIndex,nDayEarthIndex)
	end

	# 十靈
	def cp_fi_DwStarType_God_18(dwStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		return cp_DwStarType_God_18(dwStarType,nDaySkyIndex,nDayEarthIndex)
	end

	# 天醫 月柱地支
	def cp_fi_DwStarType_God_19(dwStarType)
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwStarType_God_19(dwStarType,nMonthEarthIndex,hEarthIndex)
	end

	# 祿神
	def cp_fi_DwStarType_God_20(dwStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwStarType_God_20(dwStarType,nDaySkyIndex,hEarthIndex)
	end

	# 天德合
	def cp_fi_DwStarType_God_21(dwStarType)
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		hSkyIndex = cp_fi_getBySkyIndex()
		return cp_DwStarType_God_21(dwStarType,nMonthEarthIndex,hSkyIndex)
	end

	# 月德合 月柱地支
	def cp_fi_DwStarType_God_22(dwStarType)
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		hSkyIndex = cp_fi_getBySkyIndex()
		return cp_DwStarType_God_22(dwStarType,nMonthEarthIndex,hSkyIndex)
	end

	# 四柱中神煞之煞
	def cp_fi_getDwBStarType(byWhatIdx=nil)
		return cp_fi_getInfo(Eightword::DwBStarType_4,"cp_fi_DwBStarType_4()",byWhatIdx)
	end
	def cp_fi_setDwBStarType(byWhatIdx,byValue)
		cp_fi_setInfo(Eightword::DwBStarType_4,byWhatIdx,byValue)
	end
	def cp_fi_DwBStarType_4()
		dwBStarType = cp_init8words_array(0,nil)
		(1..18).each do |nStar|
			sFunc = "cp_fi_DwBStarType_Kill_#{nStar}(dwBStarType)"
			dwBStarType = eval(sFunc)
		end
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
			cp_fi_setDwBStarType(byWhatIdx,dwBStarType[byWhatIdx].uniq)
		end
	end

	# 羊刃
	def cp_fi_DwBStarType_Kill_1(dwBStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwBStarType_Kill_1(dwBStarType,nDaySkyIndex,hEarthIndex)
	end

	# 飛刃
	def cp_fi_DwBStarType_Kill_2(dwBStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwBStarType_Kill_2(dwBStarType,nDaySkyIndex,hEarthIndex)
	end

	# 劫煞 日柱地支 年柱地支
	def cp_fi_DwBStarType_Kill_3(dwBStarType)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		nYearEarthIndex = cp_fi_getByEarthIndex(Eightword::ByYearIdx)
		return cp_DwBStarType_Kill_3(dwBStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
	end

	# 亡神 日柱地支 年柱地支
	def cp_fi_DwBStarType_Kill_4(dwBStarType)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		nYearEarthIndex = cp_fi_getByEarthIndex(Eightword::ByYearIdx)
		return cp_DwBStarType_Kill_4(dwBStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
	end

	# 咸池 年柱地支 月柱地支
	def cp_fi_DwBStarType_Kill_5(dwBStarType)
		nYearEarthIndex = cp_fi_getByEarthIndex(Eightword::ByYearIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		return cp_DwBStarType_Kill_5(dwBStarType,nYearEarthIndex,hEarthIndex,nMonthEarthIndex)
	end

	# 紅艷
	def cp_fi_DwBStarType_Kill_6(dwBStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwBStarType_Kill_6(dwBStarType,nDaySkyIndex,hEarthIndex)
	end

	# 外桃 日柱地支
	def cp_fi_DwBStarType_Kill_7(dwBStarType)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		nHourEarthIndex = cp_fi_getByEarthIndex(Eightword::ByHourIdx)
		return cp_DwBStarType_Kill_7(dwBStarType,nDayEarthIndex,nHourEarthIndex)
	end

	# 六甲空亡 日柱天干
	def cp_fi_DwBStarType_Kill_8(dwBStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwBStarType_Kill_8(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
	end

	# 孤虛 日柱天干
	def cp_fi_DwBStarType_Kill_9(dwBStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwBStarType_Kill_9(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
	end

	# 四大空亡 日柱天干
	def cp_fi_DwBStarType_Kill_10(dwBStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwBStarType_Kill_10(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
	end

	# 孤辰 年柱地支
	def cp_fi_DwBStarType_Kill_11(dwBStarType)
		nYearEarthIndex = cp_fi_getByEarthIndex(Eightword::ByYearIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwBStarType_Kill_11(dwBStarType,nYearEarthIndex,hEarthIndex)
	end

	# 寡宿 年柱地支
	def cp_fi_DwBStarType_Kill_12(dwBStarType)
		nYearEarthIndex = cp_fi_getByEarthIndex(Eightword::ByYearIdx)
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwBStarType_Kill_12(dwBStarType,nYearEarthIndex,hEarthIndex)
	end

	# 天轉
	def cp_fi_DwBStarType_Kill_13(dwBStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		return cp_DwBStarType_Kill_13(dwBStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex)
	end

	# 地轉 月柱地支
	def cp_fi_DwBStarType_Kill_14(dwBStarType)
		nDaySkyIndex = cp_fi_getBySkyIndex(Eightword::ByDayIdx)
		nDayEarthIndex = cp_fi_getByEarthIndex(Eightword::ByDayIdx)
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		return cp_DwBStarType_Kill_14(dwBStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex)
	end

	# 天羅
	def cp_fi_DwBStarType_Kill_15(dwBStarType)
		hNaIn = cp_fi_getNaIn()
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwBStarType_Kill_15(dwBStarType,hNaIn,hEarthIndex)
	end

	# 地網
	def cp_fi_DwBStarType_Kill_16(dwBStarType)
		hNaIn = cp_fi_getNaIn()
		hEarthIndex = cp_fi_getByEarthIndex()
		return cp_DwBStarType_Kill_16(dwBStarType,hNaIn,hEarthIndex)
	end

	# 十惡大敗
	def cp_fi_DwBStarType_Kill_17(dwBStarType)
		h8Words = cp_fi_get8Words()
		return cp_DwBStarType_Kill_17(dwBStarType,h8Words)
	end

	# 四廢
	def cp_fi_DwBStarType_Kill_18(dwBStarType)
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		h8Words = cp_fi_get8Words()	
		return cp_DwBStarType_Kill_18(dwBStarType,nMonthEarthIndex,h8Words)
	end


	def cp_fi_getNaIn(byWhatIdx=nil)
		return cp_fi_getInfo(Eightword::ByNaIn_4,"cp_fi_ByNaIn_4()",byWhatIdx)
	end
	def cp_fi_setNaIn(byValue,byWhatIdx=nil)
		cp_fi_setInfo(Eightword::ByNaIn_4,byWhatIdx,byValue)
	end
	def cp_fi_ByNaIn_4()
		h8Words = cp_fi_get8Words()
		hNaIn = cp_ByNaIn_4(h8Words)
		cp_fi_setNaIn(hNaIn)
	end

	# 旺相休囚死
	def cp_fi_getByFiveLevel(arrayIndex=nil)
		return cp_fi_getInfo(Eightword::ByFiveLevel_5,"cp_fi_ByFiveLevel_5()",nil,arrayIndex)
	end
	def cp_fi_setByFiveLevel(byValue,arrayIndex=nil)
		cp_fi_setInfo(Eightword::ByFiveLevel_5,nil,byValue,arrayIndex)
	end
	def cp_fi_ByFiveLevel_5()
		nMonthEarthIndex = cp_fi_getByEarthIndex(Eightword::ByMonthIdx)
		byValue = cp_ByFiveLevel_5(nMonthEarthIndex)
		cp_fi_setByFiveLevel(byValue)
	end

    def cp_fi_SkyScore()
    	if (ddg_SkyEarth() == Sky::SKY) then
    		return cp_fi_getSkyScore_Sky()
    	else
    		return cp_fi_getSkyScore_Earth()
    	end
    end
    def cp_fi_getSkyScore_Sky(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::BySkyScore_4,"cp_BigWin_Sky()",arrayIndex)
    end
    def cp_fi_getSkyScore_Earth(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::BySkyScore_4,"cp_fi_SkyScore_Earth()",arrayIndex)
    end
    def cp_fi_setSkyScore(byValue,arrayIndex=nil)
		return cp_fi_setScoreInfo(Eightword::BySkyScore_4,byValue,arrayIndex)
    end

    def cp_fi_nChungSky_Sky()
    end
    def cp_fi_nBetweenChungSky_Sky()
    end

   	def cp_fi_isBigWin?()
   		return @m_nPanType != Cfate::PAN_NORMAL
   	end
   	def cp_fi_needCurSeg?()
   		return @m_nPanType == Cfate::PAN_FLOWMONTH
   	end
   	def cp_fi_BigWin_isFlowDateWinIn()
    	bWin = cp_fi_isBigWin?()
    	bNeedSeg = cp_fi_needCurSeg?()
    	return bWin && bNeedSeg
   	end
    def cp_fi_nChung_Earth()
		nCurSeg,nOffDays,nOffHour = cp_mp_getBySegmentOffsetTime()
    	nCurSeg = nCurSeg >> 1
     	m = Earth.Modify(nCurSeg - 1)
    	n = @@ByChungSkyDays[m][0]
    	if (nOffDays < n || (nOffDays == n && nOffHour <= 0)) then
        	nChung = @@ByChungSkyWin[m][0]
    	else
            n = @@ByChungSkyDays[m][1]
        end
        if (n == 0) then
            nChung = @@ByChungSkyWin[m][1]
        else
            if (nOffDays < n || (nOffDays == n && nOffHour <= 0)) then
                nChung = @@ByChungSkyWin[m][1]
            else
                nChung = @@ByChungSkyWin[m][2]
        	end
    	end
    	return nChung
    end
    def cp_fi_nChungSky_Earth()
    	bFlowDateWinIn = cp_fi_BigWin_isFlowDateWinIn()
		nChungSky = Array.new(3,0)
		nEarthIndex = cp_fi_getCurEarthIndex()

		(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |i|
			if (bFlowDateWinIn) then
				nChung = cp_fi_nChung_Earth()
	            if (i == 0) then
	                nChungSky[i] = nChung
	            else
	                nChungSky[i] = 0
	            end	        
	        else
	            nChungSky[i] = @@ByChungSky[nEarthIndex][i]
	        end
	    end
	    return nChungSky
    end
    def cp_fi_byChungSkyFlag_Earth()
    	bFlowDateWinIn = cp_fi_BigWin_isFlowDateWinIn()
    	nChungSky = cp_fi_nChungSky_Earth()
		nEarthIndex = cp_fi_getCurEarthIndex()
		byChungSkyFlag = Array.new(3,0)

		(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |i|
			if (bFlowDateWinIn) then
	            if (i == 0) then
	                byChungSkyFlag[i] = 0
	            else
	                byChungSkyFlag[i] = 0xFF
	            end	        
	        else
	            if (nEarthIndex == 0 || nEarthIndex == 3 || nEarthIndex == 9) then
				    if (@par_ChungSkyType == Eightword::PAN_SKY_1) then
	                    if (i == 0) then
					        byChungSkyFlag[i] = 0
				        else
					        byChungSkyFlag[i] = 0xFF
				        end
	                else
				        byChungSkyFlag[i] = 0
	                end
			    elsif (nChungSky[i] > 0) then
				    byChungSkyFlag[i] = 0
			    else
				    byChungSkyFlag[i] = 0xFF
			    end
	        end
	    end
	    return byChungSkyFlag
    end
    def cp_fi_nWinFive_4nBetweenChungSky_Earth(i)
    	bFlowDateWinIn = cp_fi_BigWin_isFlowDateWinIn()
    	nChungSky = cp_fi_nChungSky_Earth()
		nWinFive = 0

		if (bFlowDateWinIn) then
			nChung = cp_fi_nChung_Earth()
            if (i == 0) then
                nWinFive = @@SkyFiveTable[nChung - 1]
            else
                nWinFive = 0
            end	        
        else
		    nWinFive = @@SkyFiveTable[nChungSky[i] - 1]
        end

	    return nWinFive
    end

    def cp_fi_nBetween_Sky()
    	nBetween = Array.new(5,0)

    	nWinFive = cp_fi_nWinFive_4nBetween_Sky()
		# 生 		
		if (nWinFive == 4) then
			nBetween[0] = 0
		else
			nBetween[0] = nWinFive + 1
		end

		# 我剋
		m = Five.Modify(nWinFive + 2)
		nBetween[1] = m

		# 比助
		nBetween[2] = nWinFive
		
		# 洩 		
		if (nWinFive == 0) then
			nBetween[3] = 4
		else
			nBetween[3] = nWinFive - 1
		end

		# 剋我
		m = Five.Modify(nWinFive - 2)
		nBetween[4] = m

		return nBetween
	end

    def cp_fi_nWinFive_4nBetween_Sky()
		nSkyIndex = cp_fi_getCurSkyIndex()
    	nWinFive = @@SkyFiveTable[nSkyIndex]
    	return nWinFive
    end

    # 八字程式沒有到這個
    def cp_fi_nWinFive_4nBetween_Earth()
		nEarthIndex = cp_fi_getCurEarthIndex()
    	nWinFive = @@EarthFiveTable[nEarthIndex]
    	return nWinFive
    end
    def cp_fi_nBetweenChungSky_Earth()
    	nBetweenChungSky = Array.new(3) {Array.new(5,-1)} # 預設值 -1
    	byChungSkyFlag = cp_fi_byChungSkyFlag_Earth()

		(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |i|
	    	nWinFive = cp_fi_nWinFive_4nBetweenChungSky_Earth(i)
			if (byChungSkyFlag[i] == 0) then
				if (nWinFive == 4) then
					nBetweenChungSky[i][0] = 0
				else
					nBetweenChungSky[i][0] = nWinFive + 1
				end
				# 我剋
				m = Five.Modify(nWinFive + 2)

				nBetweenChungSky[i][1] = m
				# 比助
				nBetweenChungSky[i][2] = nWinFive
				# 洩
				if (nWinFive == 0) then
					nBetweenChungSky[i][3] = 4
				else
					nBetweenChungSky[i][3] = nWinFive - 1
				end
				# 剋我
				m = Five.Modify(nWinFive - 2)
				nBetweenChungSky[i][4] = m
			end
		end
		return nBetweenChungSky
    end

    def cp_fi_fEarthSkyLevel_Earth()
    	bFlowDateWinIn = cp_fi_BigWin_isFlowDateWinIn()
    	nChungSky = cp_fi_nChungSky_Earth()
		nEarthIndex = cp_fi_getCurEarthIndex()

		(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |i|
			if (bFlowDateWinIn) then
	            if (i == 0) then
	                fEarthSkyLevel[i] = 1.0
	            else
	                fEarthSkyLevel[i] = 0
	            end	        
	        else
	            if (nEarthIndex == 0 || nEarthIndex == 3 || nEarthIndex == 9) then
				    if (@par_ChungSkyType == Eightword::PAN_SKY_1) then
	                    if (i == 0) then
					        fEarthSkyLevel[i] = 1.0
				        else
					        fEarthSkyLevel[i] = 0
				        end
	                else
	                    fEarthSkyLevel[i] = @@FLevel[i]
	                end
			    elsif (nChungSky[i] > 0) then
				    fEarthSkyLevel[i] = @@FLevel[i]
			    else
				    fEarthSkyLevel[i] = 0
				    fEarthSkyLevel[0] += @@FLevel[i]
			    end
	        end
	    end
	    return fEarthSkyLevel
    end
    def cp_fi_fBetweenLevel()
    	fBetweenLevel = Array.new(5,0.0)
    	(0..4).each do |i|
	        n = @@BetweenLevel[i]
	        dValue = n.to_f / 9
	        fBetweenLevel[i] = dValue
    	end
    	return fBetweenLevel
    end

    def cp_fi_fInChungPow_Earth()
		fInChungPow = Array.new(3,0.0)
		nInEarthLevel = [11.0, 3.0, 2.0]

		(0..2).each do |i|
	        fInChungPow[i] = nInEarthLevel[i] / 16.0
	    end

    	bFlowDateWinIn = cp_fi_BigWin_isFlowDateWinIn()
		nEarthIndex = cp_fi_getCurEarthIndex()
    	nChungSky = cp_fi_nChungSky_Earth()

		(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |i|
			if (bFlowDateWinIn) then
	            if (i == 0) then
	                fInChungPow[0] = 1.0
	            else
	                fInChungPow[i] = 0
	            end	        
	        else
	            if (nEarthIndex == 0 || nEarthIndex == 3 || nEarthIndex == 9) then
				    if (@par_ChungSkyType == Eightword::PAN_SKY_1) then
	                    if (i == 0) then
					        fInChungPow[i] = 1.0
				        else
					        fInChungPow[i] = 0
				        end
	                end
		    	elsif (nChungSky[i] > 0)
			    else
	                fInChungPow[0] = fInChungPow[i]
	                fInChungPow[i] = 0
			    end
	        end
	    end

	# nil[1]
	    return fInChungPow
    end
    def cp_fi_fEarthSky_Earth()
    	bFlowDateWinIn = cp_fi_BigWin_isFlowDateWinIn()
    	nChungSky = cp_fi_nChungSky_Earth()
		nEarthIndex = cp_fi_getCurEarthIndex()

		(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |i|
			if (bFlowDateWinIn) then
	            if (i == 0) then
	                fEarthSky[i] = 1.0
	            else
	                fEarthSky[i] = 0
	            end	        
	        else
	            if (nEarthIndex == 0 || nEarthIndex == 3 || nEarthIndex == 9) then
				    if (@par_ChungSkyType == Eightword::PAN_SKY_1) then
	                    if (i == 0) then
					        fEarthSky[i] = 1.0
				        else
					        fEarthSky[i] = 0
				        end
	                else
	                    fEarthSky[i] = @@FLevel[i]
	                end
			    elsif (nChungSky[i] > 0) then
				    fEarthSky[i] = @@FLevel[i]
			    else
				    fEarthSky[i] = 0
	                fEarthSky[0] += @@FLevel[i]
			    end
	        end
	    end
	    return fEarthSky
    end
    def cp_fi_nHRYearSkyNum_Earth()
		nHRYearSkyNum = 0

    	bFlowDateWinIn = cp_fi_BigWin_isFlowDateWinIn()
    	nChungSky = cp_fi_nChungSky_Earth()
		nEarthIndex = cp_fi_getCurEarthIndex()

		(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |i|
			if (bFlowDateWinIn) then
	            if (i == 0) then
	                nHRYearSkyNum = 1
	            end	        
	        else
	            if (nEarthIndex == 0 || nEarthIndex == 3 || nEarthIndex == 9) then
				    if (@par_ChungSkyType == Eightword::PAN_SKY_1) then
	                    if (i == 0) then
					        nHRYearSkyNum = 1
				        end
	                else
				        nHRYearSkyNum += 1
	                end
			    elsif (nChungSky[i] > 0) then
				    nHRYearSkyNum += 1
			    end
	        end
	    end
	    return nHRYearSkyNum
    end

    def cp_fi_SkyScore_Earth()
    	fSkyScore = Array.new(5,16.0)

    	nChungSky = cp_fi_nChungSky_Earth()
    	nBetweenChungSky = cp_fi_nBetweenChungSky_Earth()
    	fBetweenLevel = cp_fi_fBetweenLevel()
    	fInChungPow = cp_fi_fInChungPow_Earth()
		# 大運進入
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
			n = cp_mp_getBySkyIndex(byWhatIdx)
	        l = @@SkyFiveTable[n]
			(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |j|
				m = nChungSky[j]
				if (m > 0) then
			        bStop = false
					(0..4).each do |k|
						if (!bStop) then
							if (l == nBetweenChungSky[j][k]) then
		                        fSkyScore[byWhatIdx] += 9.0 * fInChungPow[j] * fBetweenLevel[k]
		                        bStop = true # 結束跳出
		                    end
						end
		            end
		        end
			end
		end
	# nil[1]
		cp_fi_setSkyScore(fSkyScore)
	end

    def cp_fi_SkyScore1()
    	if (ddg_SkyEarth() == Sky::SKY) then
    		return cp_fi_SkyScore1_Sky()
    	else
    		return cp_fi_SkyScore1_Earth()
    	end
    end
    def cp_fi_SkyScore1_Sky()
    end
    def cp_fi_SkyScore1_Earth()
    	fSkyScore1 = Array.new(5,1.0)
    	nChungSky = cp_fi_nChungSky_Earth()
    	nBetweenChungSky = cp_fi_nBetweenChungSky_Earth()
    	fEarthSkyLevel = cp_fi_fEarthSkyLevel_Earth()
    	fBetweenLevel = cp_fi_fBetweenLevel()

		# 大運進入
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
			n = cp_mp_getBySkyIndex(byWhatIdx)
	        l = @@SkyFiveTable[n]
			(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |j|
				m = nChungSky[j]
				if (m > 0) then
					(0..4).each do |k|
						if (l == nBetweenChungSky[j][k]) then
							fSkyScore1[i] += fEarthSkyLevel[j] * fBetweenLevel[k]
	                        l = nil # 結束跳出
	                    end
					end
	            end
			end
		end
		return fSkyScore1
	end

	def cp_fi_getScoreInfo(key,sFunc,arrayIndex=nil)
		if (arrayIndex == nil) then
			if ((@EightwordInfo[Eightword::FLOW_INFO][Eightword::Scores][key] == nil) || 
				(@EightwordInfo[Eightword::FLOW_INFO][Eightword::Scores][key][0] == nil)) then
				eval(sFunc)
			end
			return @EightwordInfo[Eightword::FLOW_INFO][Eightword::Scores][key]
		else
			if (@EightwordInfo[Eightword::FLOW_INFO][Eightword::Scores][key][arrayIndex] == nil) then
				eval(sFunc)
			end
			return @EightwordInfo[Eightword::FLOW_INFO][Eightword::Scores][key][arrayIndex]
		end
	end
	def cp_fi_setScoreInfo(key,byValue,arrayIndex=nil)
		byValue = cloneValue(byValue)
		if (arrayIndex == nil) then
			@EightwordInfo[Eightword::FLOW_INFO][Eightword::Scores][key] = byValue
		else
			@EightwordInfo[Eightword::FLOW_INFO][Eightword::Scores][key][arrayIndex] = byValue
		end
	end
	def cp_fi_EarthType_5()
		nEarthType = Array.new(5,-1)
		cp_fi_setEarthType(nEarthType)
	end
	def cp_fi_getEarthType(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::NEarthType_5,"cp_fi_EarthType_5()",arrayIndex)
	end
	def cp_fi_setEarthType(byValue,arrayIndex=nil)
		cp_fi_setScoreInfo(Eightword::NEarthType_5,byValue,arrayIndex)
	end

    def cp_fi_fChungSkyScore()
    	if (ddg_SkyEarth() == Sky::SKY) then
    		return cp_fi_fChungSkyScore_Sky()
    	else
    		return cp_fi_fChungSkyScore_Earth()
    	end
	end
    def cp_fi_fChungSkyScore_Sky()
    	fChungSkyScore = Array.new(4) {Array.new(3,0)}
	end

    def cp_fi_fChungSkyScore_Earth_All()
    	fChungSkyScore = Array.new(4) {Array.new(3,0)}
    	fOrgChungSkyScore = Array.new(4) {Array.new(3,0)}
    	fChungSkyScore,fOrgChungSkyScore = cp_fi_fChungSkyScore_Earth1(fChungSkyScore,fOrgChungSkyScore)
    	fChungSkyScore,fOrgChungSkyScore = cp_fi_fChungSkyScore_Earth2(fChungSkyScore,fOrgChungSkyScore)
    	return fChungSkyScore,fOrgChungSkyScore
    end
    def cp_fi_fOrgChungSkyScore_Earth()
    	fChungSkyScore,fOrgChungSkyScore = cp_fi_fChungSkyScore_Earth_All()
    	return fOrgChungSkyScore
    end
    def cp_fi_fChungSkyScore_Earth()
    	fChungSkyScore,fOrgChungSkyScore = cp_fi_fChungSkyScore_Earth_All()
    	return fChungSkyScore
    end
    def cp_fi_fChungSkyScore_Earth1(fChungSkyScore,fOrgChungSkyScore)
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
			m = cp_mp_getByEarthIndex(i)
			bEscape = false
			(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |j|
				if (!bEscape) then
					n = cp_mp_getByChungSky(i,j)
					if (m == 0 || m == 3 || m == 9) then
						if (j == 0) then
							fOrgChungSkyScore[i][j] = fChungSkyScore[i][j] = 1.0
							bEscape = true # 跳出
						end
					elsif (n > 0) then
						fOrgChungSkyScore[i][j] = fChungSkyScore[i][j] = @@FLevel[j]
					else
						fChungSkyScore[i][0] += @@FLevel[j]
						fOrgChungSkyScore[i][0] += @@FLevel[j]
		                fOrgChungSkyScore[i][j] = fChungSkyScore[i][j] = 0
					end
				end
			end
		end
	
		return fChungSkyScore,fOrgChungSkyScore
    end
    def cp_fi_fChungSkyScore_Earth2(fChungSkyScore,fOrgChungSkyScore)
		byXChungSky = cp_fi_byXChungSky_Earth()
    	nChungSky = cp_fi_nChungSky_Earth()
    	nBetweenChungSky = cp_fi_nBetweenChungSky_Earth()
    	fInChungPow = cp_fi_fInChungPow_Earth()
    	fBetweenLevel = cp_fi_fBetweenLevel()
    	nHRYearSkyNum = cp_fi_nHRYearSkyNum_Earth()
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
			(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |j|
				(0...nHRYearSkyNum).each do |n1|
					m = nChungSky[n1]
					n = byXChungSky[i][j]
					if (m > 0 && n > 0) then
						n -= 1
						m = @@SkyFiveTable[n]
						bEscape = false
						(0...5).each do |l|
							if (!bEscape) then
								if (m == nBetweenChungSky[n1][l]) then
									fValue = fInChungPow[n1] * fOrgChungSkyScore[i][j] * fBetweenLevel[l]
									fChungSkyScore[i][j] += fValue
									bEscape = true
								end
							end
						end
					end
				end
			end
		end

		return fChungSkyScore,fOrgChungSkyScore
    end
    def cp_fi_fChungSkyScore1()
    	if (ddg_SkyEarth() == Sky::SKY) then
    		return cp_fi_fChungSkyScore1_Sky()
    	else
    		return cp_fi_fChungSkyScore1_Earth()
    	end
	end
    def cp_fi_fChungSkyScore1_Sky()
	end
    def cp_fi_fChungSkyScore1_Earth()
    	fChungSkyScore1 = Array.new(4) {Array.new(3,0)}
    	fOrgChungSkyScore1 = Array.new(4) {Array.new(3,0)}
    	fOrgChungSkyScore = cp_fi_fOrgChungSkyScore_Earth()

		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
			m = cp_mp_getByEarthIndex(i)
			bEscape = false
			(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |j|
				if (!bEscape) then
					n = cp_mp_getByChungSky(i,j)
					if (m == 0 || m == 3 || m == 9) then
						if (j == 0) then
		                    fOrgChungSkyScore1[i][j] = fChungSkyScore1[i][j] = 16.0
							bEscape = true # 跳出
						end
					elsif (n > 0) then
		                fOrgChungSkyScore1[i][j] = fChungSkyScore1[i][j] = @@NLevel[j].to_f
					else
		                fChungSkyScore1[i][0] += @@NLevel[j].to_f
		                fOrgChungSkyScore1[i][0] += @@NLevel[j].to_f
		                fOrgChungSkyScore1[i][j] = fChungSkyScore1[i][j] = 0
					end
				end
			end
		end
		byXChungSky = cp_fi_byXChungSky_Earth()
    	nChungSky = cp_fi_nChungSky_Earth()
    	nBetweenChungSky = cp_fi_nBetweenChungSky_Earth()
    	fInChungPow = cp_fi_fInChungPow_Earth()
    	fBetweenLevel = cp_fi_fBetweenLevel()
    	nHRYearSkyNum = cp_fi_nHRYearSkyNum_Earth()
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
			(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |j|
				(0...nHRYearSkyNum).each do |n1|
					m = nChungSky[n1]
					n = byXChungSky[i][j]
					if (m > 0 && n > 0) then
						n -= 1
						m = @@SkyFiveTable[n]
						bEscape = false
						(0...5).each do |l|
							if (!bEscape) then
								if (m == nBetweenChungSky[n1][l]) then
									# 原始版本底下這行用 fOrgChungSkyScore 而不是 fOrgChungSkyScore1
									# fValue = fInChungPow[n1] * fOrgChungSkyScore1[i][j] * fBetweenLevel[l]
									fValue = fInChungPow[n1] * fOrgChungSkyScore[i][j] * fBetweenLevel[l]
		                            fChungSkyScore1[i][j] += fValue
									bEscape = true
								end
							end
						end
					end
				end
			end
		end
		return fChungSkyScore1
    end
    def cp_fi_byXChungSky_Earth()
    	byXChungSky = Array.new(4) {Array.new(3,0)}

		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
			m = cp_mp_getByEarthIndex(i)
			bEscape = false
			(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |j|
				if (!bEscape) then
					n = cp_mp_getByChungSky(i,j)
					if (m == 0 || m == 3 || m == 9) then
						if (j == 0) then
							byXChungSky[i][0] = n
							bEscape = true # 跳出
						end
					elsif (n > 0) then
						byXChungSky[i][j] = n
					else
						byXChungSky[i][j] = 0
					end
				end
			end
		end
		return byXChungSky
    end

    def cp_fi_getfGodScore()
    	if (ddg_SkyEarth() == Sky::SKY) then
    		return cp_fi_getfGodScore_Sky()
    	else
    		return cp_fi_getfGodScore_Earth()
    	end
    end
    def cp_fi_getfGodScore_Sky(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::NGodScore_10,"cp_fi_fGodScore_Sky()",arrayIndex)
    end
    def cp_fi_getfGodScore_Earth(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::NGodScore_10,"cp_fi_fGodScore_Earth()",arrayIndex)
    end
    def cp_fi_setfGodScore(byValue,arrayIndex=nil)
		return cp_fi_setScoreInfo(Eightword::NGodScore_10,byValue,arrayIndex)
    end
    def cp_fi_fGodScore_Sky()
		fGodScore = Array.new(10,0.0)

   		nfiSkyIndex = cp_fi_getCurSkyIndex()
    	nfiEarthIndex = cp_fi_getCurEarthIndex()
		fSkyScore = cp_fi_getSkyScore_Sky()
    	byfiSkyIndex = cp_sfi_getBySkyIndex()
    	byfiChungSky = cp_sfi_getByChungSky()

		fGodScore[nfiSkyIndex] += fSkyScore[4]
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
			n = byfiSkyIndex[i]
			fGodScore[n] += fSkyScore[i]
		end

		fChungSkyScore = Array.new(4) {Array.new(3,0.0)}
		fChungSkyScore1 = Array.new(4) {Array.new(3,0.0)}
		fOrgChungSkyScore = Array.new(4) {Array.new(3,0.0)}
		fOrgChungSkyScore1 = Array.new(4) {Array.new(3,0.0)}
		fChungSkyScore,fOrgChungSkyScore,fChungSkyScore1,fOrgChungSkyScore1,bysfiChungSky = cp_bws_fSkyScore_init_fChungSkyScore(fChungSkyScore,fOrgChungSkyScore,fChungSkyScore1,fOrgChungSkyScore1)
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
			(0..2).each do |j|
				n = byfiChungSky[i][j]
	            if (n > 0 && n <= 10) then
	                n -= 1
	                fGodScore[n] += fChungSkyScore[i][j]
				end
			end
		end

	    (0..9).each do |i|
			if (fGodScore[i] < 0) then
	            fGodScore[i] = 0
	        end
		end

		return fGodScore
	end
    def cp_fi_fGodScore_Earth()
    	fGodScore = Array.new(10,0)

    	nSky = cp_fi_getCurSkyIndex()
    	fSkyScore = cp_fi_getSkyScore_Earth()
    	fGodScore[nSky] += fSkyScore[4]
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
			n = cp_fi_getBySkyIndex(i)
			fGodScore[n] += fSkyScore[i]
		end
		return fGodScore
	end

    def cp_fi_getnGodValue(arrayIndex=nil)
    	if (ddg_SkyEarth() == Sky::SKY) then
    		return cp_fi_getnGodValue_Sky(arrayIndex)
    	else
    		return cp_fi_getnGodValue_Earth(arrayIndex)
    	end
    end
    def cp_fi_getnGodValue_Sky(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::NGodValue_10,"cp_BigWin_Sky()",arrayIndex)
    end
    def cp_fi_getnGodValue_Earth(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::NGodValue_10,"cp_fi_fGodValue_Earth()",arrayIndex)
    end
    def cp_fi_setnGodValue(byValue,arrayIndex=nil)
		return cp_fi_setScoreInfo(Eightword::NGodValue_10,byValue,arrayIndex)
    end

    def cp_fi_fGodValue_Earth()
    	nGodValue = Array.new(10,0)

    	nSick = cp_fi_getnSick()

    	m = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
		n = m % 2
		if (n == 1) then
			(0..9).each do |i|
				nGodValue[i] = nSick[m]
				m = Sky.Modify(m - 1)
			end
		else
			j = 0
			(0..4).each do |i|
				nGodValue[j] = nSick[m]
				nGodValue[j + 1] = nSick[m + 1]
				m = Sky.Modify(m - 2)

				j += 2
			end
		end

		cp_fi_setnGodValue(nGodValue)
	end


    def cp_fi_getfHRScore()
    	if (ddg_SkyEarth() == Sky::SKY) then
    		return cp_fi_getfHRScore_Sky()
    	else
    		return cp_fi_getfHRScore_Earth()
    	end
    end
    def cp_fi_getfHRScore_Sky(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::FHRScore_10,"cp_fi_fHRScore_Sky()",arrayIndex)
    end
    def cp_fi_getfHRScore_Earth(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::FHRScore_10,"cp_fi_fHRScore_Earth()",arrayIndex)
    end
    def cp_fi_setfHRScore(byValue,arrayIndex=nil)
		return cp_fi_setScoreInfo(Eightword::FHRScore_10,byValue,arrayIndex)
    end
    def cp_fi_fHRScore_Sky()
    	cp_BigWin_Sky()
	end
    def cp_fi_fHRScore_Earth()
    	fHRScore = Array.new(10,0)

    	nSky = cp_fi_getCurSkyIndex()
    	fSkyScore1 = cp_fi_SkyScore1_Earth()
		fHRScore[nSky] += fSkyScore1[4] 
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
			n = cp_fi_getBySkyIndex(i)
			fHRScore[n] += fSkyScore1[i]
		end

    	nChungSky = cp_fi_nChungSky_Earth()
    	fEarthSky = cp_fi_fEarthSky_Earth()
		(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |i|
			m = nChungSky[i]
			if (m > 0) then
				fHRScore[m - 1] += fEarthSky[i]
			end
		end

		byXChungSky = cp_fi_byXChungSky_Earth()
		fChungSkyScore = cp_fi_fChungSkyScore_Earth()
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
			(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |j|
				n = byXChungSky[i][j]
				if (n > 0 && n <= 10) then
					n -= 1
					fHRScore[n] += fChungSkyScore[i][j]
				end
			end
		end
	
		(0...10).each do |i|
			if (fHRScore[i] < 0) then
				fHRScore[i] = 0
			end
		end

		cp_fi_setfHRScore(fHRScore)
	end

    def cp_fi_getfSkyArray()
    	if (ddg_SkyEarth() == Sky::SKY) then
    		return cp_fi_getfSkyArray_Sky()
    	else
    		return cp_fi_getfSkyArray_Earth()
    	end
	end
    def cp_fi_getfSkyArray_Sky()
    	# nothing
    	return Array.new(10,0)
	end
    def cp_fi_getfSkyArray_Earth(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::FSkyArray,"cp_fi_fSkyArray_Earth()",arrayIndex)
    end
    def cp_fi_setfSkyArray(byValue,arrayIndex=nil)
		return cp_fi_setScoreInfo(Eightword::FSkyArray,byValue,arrayIndex)
    end
    def cp_fi_fSkyArray_Earth()
    	nFiveSum,fSkyArray,nEarthType,nEarthArray = cp_ChangeFlowPan()

    	nSkyIndex = cp_fi_getCurSkyIndex()
    	fSkyScore = cp_fi_getSkyScore_Earth()
    	byXChungSky = cp_fi_byXChungSky_Earth()
    	fChungSkyScore1 = cp_fi_fChungSkyScore1_Earth()

    	(0..4).each do |i|
	        if (i < 4) then
	        	n = cp_sfi_getBySkyIndex(i)
	            fSkyArray[n] += fSkyScore[i]
	        else
	            fSkyArray[nSkyIndex] += fSkyScore[4]
	        end

	        if (i < 4) then
		        if (nEarthArray[i] > 0) then
		        	(0...3).each do |j|
		                n = byXChungSky[i][j]
					    if (n > 0 && n <= 10) then
						    n -= 1
		                    fSkyArray[n] += fChungSkyScore1[i][j]
		                end
		            end
		        end
		    end
	    end

	    cp_fi_setfSkyArray(fSkyArray)
	end

    def cp_fi_getnSick(arrayIndex=nil)
    	if (ddg_SkyEarth() == Sky::SKY) then
    		return cp_fi_getnSick_Sky(arrayIndex)
    	else
    		return cp_fi_getnSick_Earth(arrayIndex)
    	end
	end
    def cp_fi_getnSick_Sky(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::NSick_10,"cp_fi_nSick_Sky()",arrayIndex)
	end
    def cp_fi_getnSick_Earth(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::NSick_10,"cp_fi_nSick_Earth()",arrayIndex)
    end
    def cp_fi_setnSick(byValue,arrayIndex=nil)
		return cp_fi_setScoreInfo(Eightword::NSick_10,byValue,arrayIndex)
    end
	def cp_fi_nSick_Sky()
		nSick = Array.new(10,0)

		fGodScore = cp_fi_getfGodScore_Sky()
		(0..9).each do |i|
			if (fGodScore[i] < 0) then
    	        fGodScore[i] = 0
    	    end
        	n = (fGodScore[i] + 0.5).to_i
			nSick[i] = n
		end
		cp_fi_setnSick(nSick)
	end
	def cp_fi_nSick_Earth()
		nSick = Array.new(10,0)
		fSkyArray = cp_fi_getfSkyArray_Earth()
		(0..9).each do |i|
			nSick[i] = (fSkyArray[i] + 0.5).to_i
		end

		cp_fi_setnSick(nSick)
	end

    def cp_fi_NFiveSum_5()
    	if (ddg_SkyEarth() == Sky::SKY) then
    		return cp_fi_NFiveSum_5_Sky()
    	else
    		return cp_fi_NFiveSum_5_Earth()
    	end
    end
    def cp_fi_NFiveSum_5_Sky()
    	cp_BigWin_Sky()
    end
	# 五行分數
    def cp_fi_NFiveSum_5_Earth()
    	nFiveSum,fSkyArray,nEarthType,nEarthArray = cp_ChangeFlowPan()

    	fSkyScore = cp_fi_SkyScore()
    	fChungSkyScore = cp_fi_fChungSkyScore()
# nil[1]

		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
			nFiveSum = cp_fi_NFiveSum_5_Each(byWhatIdx,nFiveSum,fSkyScore,nEarthType,fChungSkyScore)
		end

# nil[1]
		cp_fi_setEarthType(nEarthType)
    	cp_fi_setFiveSum(nFiveSum)
    end
    def cp_fi_NFiveSum_5_Each(byWhatIdx,nFiveSum,fSkyScore=nil,nEarthType=nil,fChungSkyScore=nil)
    	fSkyScore = cp_fi_SkyScore() if fSkyScore == nil 
    	nEarthType = cp_fi_getEarthType() if nEarthType == nil
    	fChungSkyScore = cp_fi_fChungSkyScore() if fChungSkyScore == nil

	    m = cp_sfi_getBySkyFive(byWhatIdx)
		n = (fSkyScore[byWhatIdx] + 0.5).to_i
		nFiveSum[m] += n
		m = cp_sfi_getByEarthFive(byWhatIdx)
        n = 0
        dSum = 0.0
        if (nEarthType[byWhatIdx] == -1) then
			(Eightword::ByYearIdx..Eightword::ByDayIdx).each do |j|
               dSum += fChungSkyScore[byWhatIdx][j]
            end
            if (dSum > 0) then
                n = (dSum + 0.5).to_i
            elsif (dSum < 0) then
                n = (dSum - 0.5).to_i
            end
        end
		nFiveSum[m] = nFiveSum[m] + 16 + n

		return nFiveSum
	end

    def cp_fi_getFiveSum(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::NFiveSum_5,"cp_fi_NFiveSum_5()",arrayIndex)
    end
    def cp_fi_setFiveSum(byValue,arrayIndex=nil)
    	cp_fi_setScoreInfo(Eightword::NFiveSum_5,byValue,arrayIndex)
    end

    def cp_fi_NFiveValue_5()
    	cp_fi_setnFiveValue(cp_fi_getFiveSum().clone)
    end
    def cp_fi_getnFiveValue(arrayIndex=nil)
		return cp_fi_getScoreInfo(Eightword::NFiveValue_5,"cp_fi_NFiveValue_5()",arrayIndex)
    end
    def cp_fi_setnFiveValue(byValue,arrayIndex=nil)
    	cp_fi_setScoreInfo(Eightword::NFiveValue_5,byValue,arrayIndex)
    end


	# 日主旺度 A90 > 30B
	def cp_fi_NA()
		bStrongLife = cp_mp_getStrongLife()

		byUseGod = cp_mp_getByUseGod()
		if (!bStrongLife) then
		    m = byUseGod[0]
			n = byUseGod[1]
		else
		    m = byUseGod[3]
			n = byUseGod[4]
		end
		nFiveSum = cp_fi_getFiveSum()
		nA = nFiveSum[m] + nFiveSum[n] # for bigWinEarth
        # nA = nFive[m] + nFive[n]; for bigWinSky
# nil[1]

		cp_fi_setNA(nA)
	end
	def cp_fi_getNA()
		return cp_fi_getScoreInfo(Eightword::NA,"cp_fi_NA()")
	end
	def cp_fi_setNA(byValue)
    	cp_fi_setScoreInfo(Eightword::NA,byValue)
	end

	def cp_fi_NB()
		bStrongLife = cp_mp_getStrongLife()

		byUseGod = cp_mp_getByUseGod()
		if (bStrongLife) then
		    m = byUseGod[0]
			n = byUseGod[1]
		else
		    m = byUseGod[3]
			n = byUseGod[4]
		end
		nFiveSum = cp_fi_getFiveSum()
		nB = nFiveSum[m] + nFiveSum[n]
        # nB = nFive[m] + nFive[n]; for bigWinSky
# nil[1]
		cp_fi_setNB(nB)
	end
	def cp_fi_getNB()
		return cp_fi_getScoreInfo(Eightword::NB,"cp_fi_NB()")
	end
	def cp_fi_setNB(byValue)
    	cp_fi_setScoreInfo(Eightword::NB,byValue)
	end

	def cp_fi_ByDateFiveLevel()
		byDateFiveLevel = Array.new(5,0)

		n = cp_fi_getBySkyFive(Eightword::ByDayIdx)
		if (n == 0) then  # 水
			byDateFiveLevel[0] = 0  # 水
			byDateFiveLevel[1] = 1  # 水生木水相生
			byDateFiveLevel[2] = 4  # 金生水金休
			byDateFiveLevel[3] = 3  # 土剋水囚
			byDateFiveLevel[4] = 2  # 水剋火死
		elsif (n == 1) then  # 木
			byDateFiveLevel[0] = 1  # 木
			byDateFiveLevel[1] = 2  # 木生火木相生
			byDateFiveLevel[2] = 0  # 水生木水休
			byDateFiveLevel[3] = 4  # 金剋木囚
			byDateFiveLevel[4] = 3  # 木剋土死
		elsif (n == 2) then  # 火
			byDateFiveLevel[0] = 2  # 火
			byDateFiveLevel[1] = 3  # 火生土火相生
			byDateFiveLevel[2] = 1  # 木生火木休
			byDateFiveLevel[3] = 0  # 水剋火囚
			byDateFiveLevel[4] = 4  # 火剋金死
		elsif (n == 3) then  # 土
			byDateFiveLevel[0] = 3  # 土
			byDateFiveLevel[1] = 4  # 土生金土相生
			byDateFiveLevel[2] = 2  # 火生土火休
			byDateFiveLevel[3] = 1  # 木剋土囚
			byDateFiveLevel[4] = 0  # 土剋水土
		elsif (n == 4) then  # 金
			byDateFiveLevel[0] = 4  # 金
			byDateFiveLevel[1] = 0  # 金生水金相生
			byDateFiveLevel[2] = 3  # 土生金土休
			byDateFiveLevel[3] = 2  # 火剋金囚
			byDateFiveLevel[4] = 1  # 金剋木死
		end
	end
	def cp_fi_getByDateFiveLevel(arrayIndex=nil)
		return cp_fi_getInfo(Eightword::ByDateFiveLevel_5,"cp_fi_ByDateFiveLevel()",arrayIndex)
	end
	def cp_fi_setByDateFiveLevel(byValue,arrayIndex=nil)
    	cp_fi_setInfo(Eightword::ByDateFiveLevel_5,byValue,arrayIndex)
	end

    def cp_fi_getBBase()
    	if (@par_BType == Eightword::B_TYPE_MONTH) then
    		nMonthSkyFive = cp_fi_getBySkyFive(Eightword::ByMonthIdx)
    		nMonthEarthFive = cp_fi_getByEarthFive(Eightword::ByMonthIdx)
			if (nMonthSkyFive == nMonthEarthFive) then
				b = @par_BBase / 100.0
			else
				b = 0
			end
		else
    		nDaySkyFive = cp_fi_getBySkyFive(Eightword::ByDayIdx)
    		nDayEarthFive = cp_fi_getByEarthFive(Eightword::ByDayIdx)
			if (nDaySkyFive == nDayEarthFive) then
				b = @par_BBase / 100.0
			else
				b = 0
			end
		end
		return b
    end
    def cp_fi_getBType()
		nbType = -1
    	if (@par_BType == Eightword::B_TYPE_MONTH) then
    		nMonthSkyFive = cp_fi_getBySkyFive(Eightword::ByMonthIdx)
    		nMonthEarthFive = cp_fi_getByEarthFive(Eightword::ByMonthIdx)
			if (nMonthSkyFive == nMonthEarthFive) then
				nbType = nMonthSkyFive
			end
		else
    		nDaySkyFive = cp_fi_getBySkyFive(Eightword::ByDayIdx)
    		nDayEarthFive = cp_fi_getByEarthFive(Eightword::ByDayIdx)
			if (nDaySkyFive == nDayEarthFive) then
				nbType = nDaySkyFive
			end
		end
		return nbType
    end
end