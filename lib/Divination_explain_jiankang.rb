class Divination
  def explain_jiankang()
    h_biao_tou = Hash.new
    h_jie_guo = Hash.new
    hOut = Hash.new

    h = explain_jiankang_jie_guo()

    # 表頭
    h_biao_tou["xiang_mu_1"] = explain_jiankang_biao_tou(1)
    h_biao_tou["xiang_mu_2"] = explain_jiankang_biao_tou(2)
    h_biao_tou["xiang_mu_3"] = explain_jiankang_biao_tou(3)
    h_biao_tou["xiang_mu_4"] = explain_jiankang_biao_tou(4)
    h_biao_tou["xiang_mu_5"] = explain_jiankang_biao_tou(5)
    h_biao_tou["xiang_mu_6"] = explain_jiankang_biao_tou(6)
    h_biao_tou["xiang_mu_7"] = explain_jiankang_biao_tou(7)
    h_biao_tou["xiang_mu_8"] = explain_jiankang_biao_tou(8)
    h_biao_tou["xiang_mu_9"] = explain_jiankang_biao_tou(9)
    hOut["biao_tou"] = h_biao_tou
    # 結果
    h_jie_guo["xiang_mu_1"] = explain_jiankang_jie_guo_xiang_mu_1(h)
    h_jie_guo["xiang_mu_2"] = explain_jiankang_jie_guo_xiang_mu_2(h)
    h_jie_guo["xiang_mu_3"] = explain_jiankang_jie_guo_xiang_mu_3(h)
    h_jie_guo["xiang_mu_4"] = explain_jiankang_jie_guo_xiang_mu_4(h)
    h_jie_guo["xiang_mu_5"] = explain_jiankang_jie_guo_xiang_mu_5(h)
    h_jie_guo["xiang_mu_6"] = explain_jiankang_jie_guo_xiang_mu_6(h)
    h_jie_guo["xiang_mu_7"] = explain_jiankang_jie_guo_xiang_mu_7(h)
    h_jie_guo["xiang_mu_8"] = explain_jiankang_jie_guo_xiang_mu_8(h)
    h_jie_guo["xiang_mu_9"] = explain_jiankang_jie_guo_xiang_mu_9(h)
    hOut["jie_guo"] = h_jie_guo

    hOut["original"] = h

    return hOut
  end
  def explain_jiankang_biao_tou(xiang_mu)
    return Divination.liuyaogua_str("jiankang.title.xiang_mu_#{xiang_mu}")
  end
  # 項目一：本年度內之健康狀況
  def explain_jiankang_jie_guo_xiang_mu_1(h)
    # 1 年之關係
    a = h["nian"].values

    # 6 三合刑(以年支判斷)
    a.push(h["san_he_xing"]["san_he"]["nian"])
    a.push(h["san_he_xing"]["san_xing"]["nian"])
    a.push(h["san_he_xing"]["hu_xing"]["nian"])

    # 11 世爻官伏
    a.push(h["lq3_in_fusheng"]["lq3_in_fusheng"])

    return Divination.explain_merge_content(a)
  end
    # 項目二：本月內健康之狀況
  def explain_jiankang_jie_guo_xiang_mu_2(h)
    # 2 月之關係
    a = h["yue"].values

    # 6 三合刑(以月支判斷)
    a.push(h["san_he_xing"]["san_he"]["yue"])
    a.push(h["san_he_xing"]["san_xing"]["yue"])
    a.push(h["san_he_xing"]["hu_xing"]["yue"])

    return Divination.explain_merge_content(a)
  end
  # 項目三：病症部位與問題
  def explain_jiankang_jie_guo_xiang_mu_3(h)
    # 3 日之12長生
    a = h["ri"].values

    # 4 病症-1官爻
    a.push(h["ji_bing_guan_yao"]["guan_yao"])
    # 4.1 病症-2 剋世
    a.push(h["ji_bing_ke_shi"]["ke_shi"])

    # 6 三合刑(以日支判斷)
    a.push(h["san_he_xing"]["san_he"]["ri"])
    a.push(h["san_he_xing"]["san_xing"]["ri"])
    a.push(h["san_he_xing"]["hu_xing"]["ri"])

    # 11 世爻官伏
    a.push(h["lq3_in_fusheng"]["lq3_in_fusheng"])

    return Divination.explain_merge_content(a)
  end
  # 項目四：  你目前之精神與身體根基之狀況
  def explain_jiankang_jie_guo_xiang_mu_4(h)
    # 5 世爻
    a = h["shi_yao"].values

    return Divination.explain_merge_content(a)
  end
  # 項目五：就醫及醫師之狀況
  def explain_jiankang_jie_guo_xiang_mu_5(h)
    # 5.1 應爻
    a = h["yin_yao"].values

    # 6 三合刑
    a.push(h["san_he_xing"]["san_he"]["yin_yao"])
    a.push(h["san_he_xing"]["san_xing"]["yin_yao"])
    a.push(h["san_he_xing"]["hu_xing"]["yin_yao"])

    # 10 應爻空亡
    a += h["yin_yao_kong_wang"].values

    return Divination.explain_merge_content(a)
  end
  # 項目六：  目前之問題及狀況
  def explain_jiankang_jie_guo_xiang_mu_6(h)
    # 8 動爻變爻之關係
    a = h["dong_yao_bian_yao"].values

    return Divination.explain_merge_content(a)
  end
  # 項目七：  你目前之各種問題與狀況
  def explain_jiankang_jie_guo_xiang_mu_7(h)
    # 7 參數-貴人驛馬
    a = h["gui_ren_tian_ma"].values

    # 7.1 參數2-空亡羊刃
    a += h["kong_wang_yang_ren"].values

    return Divination.explain_merge_content(a)
  end
  # 項目八：  特殊問題之說明
  def explain_jiankang_jie_guo_xiang_mu_8(h)
    # 9 本卦變卦之卦象說明
    a = h["gua_xiang"].values

    return Divination.explain_merge_content(a)
  end
  # 項目九：  用藥狀況
  def explain_jiankang_jie_guo_xiang_mu_9(h)
    # 5.2 子孫爻
    a = h["zi_sun_yao"].values

    return Divination.explain_merge_content(a)
  end

  def explain_jiankang_jie_guo()
    h = Hash.new

    h["nian"] = ex_jiankang_year()
    h["yue"] = ex_jiankang_month()
    h["ri"] = ex_jiankang_day()
    h["ji_bing_guan_yao"] = ex_jiankang_ji_bing_guan_yao()
    h["ji_bing_ke_shi"] = ex_jiankang_ji_bing_ke_shi()
    h["shi_yao"] = ex_jiankang_shi_yao()
    h["yin_yao"] = ex_jiankang_yin_yao()
    h["zi_sun_yao"] = ex_jiankang_sons_yao()
    h["san_he_xing"] = ex_jiankang_he_san_xing()
    h["gui_ren_tian_ma"] = ex_jiankang_gui_ren_ten_ma()
    h["kong_wang_yang_ren"] = ex_jiankang_kong_wang_yang_ren()
    h["dong_yao_bian_yao"] = ex_jiankang_move_dong_yao()
    h["gua_xiang"] = ex_jiankang_gua_image()

    h["yin_yao_kong_wang"] = ex_jiankang_yin_yao_kong_wang()
    h["lq3_in_fusheng"] = ex_jiankang_lq3_infusheng()

    return h
  end
  def ex_jiankang_year()
    h = Hash.new
    nYearEarth = Xdate.GetGanZhiEarth(@nGanZhiYear)
    shi_yao_di_zhi = [@shi_earth]
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(@all_ylqgzws)

    h["liu_he"] = Divination.ex_jk_y_liu_he(nYearEarth,ben_gua_di_zhi)
    h["liu_chong"] = Divination.ex_jk_y_liu_chong(nYearEarth,ben_gua_di_zhi)
    h["hu_xing"] = Divination.ex_jk_y_hu_xing(nYearEarth,ben_gua_di_zhi)
    h["san_xing"] = Divination.ex_jk_y_san_xing(nYearEarth,ben_gua_di_zhi)
    h["ru_nian_mu"] = Divination.ex_jk_y_ru_nian_mu(nYearEarth,ben_gua_di_zhi)

    return h
  end
  def ex_jiankang_month()
    h = Hash.new
    nMonthEarth = Xdate.GetGanZhiEarth(@nGanZhiMonth)
    shi_yao_di_zhi = [@shi_earth]
    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(@all_ylqgzws)

    # 旺 相 休 囚 死
    wang_xiang_xiu_qiu_si = @par_five_levels.index(@shi_five) + 1
    h["wang_xiang_xiu_qiu_si"] = Divination.ex_jk_m_wang_xiang_xiu_qiu_si(nMonthEarth,@shi_earth,wang_xiang_xiu_qiu_si)

    h["liu_he"] = Divination.ex_jk_m_liu_he(nMonthEarth,ben_gua_di_zhi)
    h["liu_chong"] = Divination.ex_jk_m_liu_chong(nMonthEarth,ben_gua_di_zhi)
    h["san_he"] = Divination.ex_jk_m_san_he(nMonthEarth,ben_gua_di_zhi)
    h["hu_xing"] = Divination.ex_jk_m_hu_xing(nMonthEarth,ben_gua_di_zhi)
    h["san_xing"] = Divination.ex_jk_m_san_xing(nMonthEarth,ben_gua_di_zhi)
    h["ru_yue_mu"] = Divination.ex_jk_m_ru_yue_mu(nMonthEarth,ben_gua_di_zhi)

    return h
  end
  def ex_jiankang_day()
    h = Hash.new
    nDayEarth = Xdate.GetGanZhiEarth(@nGanZhiDay)
    chang_sheng = Divination.chang_sheng(@shi_five,nDayEarth)

    h["chang_sheng"] = Divination.ex_jk_day_shi_chang_sheng(nDayEarth,@shi_earth,chang_sheng)

    return h
  end

  def ex_jiankang_ji_bing_guan_yao()
    h = Hash.new

    h["guan_yao"] = Divination.ex_jk_ji_bing_guan_yao(@all_ylqgzws,@liu_shous,@par_five_levels)

    return h
  end
  def ex_jiankang_ji_bing_ke_shi()
    h = Hash.new

    h["ke_shi"] = Divination.ex_jk_ji_bing_ke_shi(@nGanZhiYear,@nGanZhiMonth,@nGanZhiDay,@all_ylqgzws,@shi_yao,@liu_shous,@par_five_levels)

    return h
  end
  def ex_jiankang_shi_yao()
    h = Hash.new

    h["shi_yao"] = Divination.ex_jk_shi_yao(@all_ylqgzws,@shi_yao,@liu_shous,@par_five_levels)

    return h
  end
  def ex_jiankang_yin_yao()
    h = Hash.new

    h["yin_yao"] = Divination.ex_jk_yin_yao(@all_ylqgzws,@yin_yao,@liu_shous,@par_five_levels)
    h["shi_yin_san_ke"] = Divination.ex_jk_shi_yin_san_ke(@all_ylqgzws,@shi_yao,@yin_yao,@liu_shous,@par_five_levels)

    return h
  end
  def ex_jiankang_sons_yao()
    h = Hash.new

    h["zi_sun_shi"] = Divination.ex_jk_sons_vs_shi(@all_ylqgzws,@shi_yao)
    h["zi_sun_yao"] = Divination.ex_jk_sons_yao(@all_ylqgzws,@liu_shous,@par_five_levels)

    return h
  end
  def ex_jiankang_he_san_xing()
    h = Hash.new

    nYearEarth = Xdate.GetGanZhiEarth(@nGanZhiYear)
    nMonthEarth = Xdate.GetGanZhiEarth(@nGanZhiMonth)
    nDayEarth = Xdate.GetGanZhiEarth(@nGanZhiDay)

    h["san_he"] = Divination.ex_jk_he_san_xing_san_he(nYearEarth,nMonthEarth,nDayEarth,@all_ylqgzws,@liu_shous,@yin_earth)
    h["san_xing"] = Divination.ex_jk_he_san_xing_san_xing(nYearEarth,nMonthEarth,nDayEarth,@all_ylqgzws,@liu_shous,@yin_earth)
    h["hu_xing"] = Divination.ex_jk_hh3_hu_xing(nYearEarth,nMonthEarth,nDayEarth,@all_ylqgzws,@yin_earth)

    # h["nian_san_he"] = Divination.ex_jk_he_san_xing_san_he(nYearEarth,@all_ylqgzws,@liu_shous)
    # h["yue_san_he"] = Divination.ex_jk_he_san_xing_san_he(nMonthEarth,@all_ylqgzws,@liu_shous)
    # h["ri_san_he"] = Divination.ex_jk_he_san_xing_san_he(nDayEarth,@all_ylqgzws,@liu_shous)

    # h["nian_san_xing"] = Divination.ex_jk_he_san_xing_san_xing(nYearEarth,@all_ylqgzws,@liu_shous)
    # h["yue_san_xing"] = Divination.ex_jk_he_san_xing_san_xing(nMonthEarth,@all_ylqgzws,@liu_shous)
    # h["ri_san_xing"] = Divination.ex_jk_he_san_xing_san_xing(nDayEarth,@all_ylqgzws,@liu_shous)

    # h["nian_hu_xing"] = Divination.ex_jk_hh3_hu_xing(nYearEarth,@all_ylqgzws)
    # h["yue_hu_xing"] = Divination.ex_jk_hh3_hu_xing(nMonthEarth,@all_ylqgzws)
    # h["ri_hu_xing"] = Divination.ex_jk_hh3_hu_xing(nDayEarth,@all_ylqgzws)

    return h
  end
  def ex_jiankang_gui_ren_ten_ma()
    h = Hash.new

    nDaySky = Xdate.GetGanZhiSky(@nGanZhiDay)
    nDayEarth = Xdate.GetGanZhiEarth(@nGanZhiDay)

    h["gui_ren"] = Divination.ex_jk_gt_gui_ren(nDaySky,@all_ylqgzws,@liu_shous,@par_five_levels,@shi_yao)
    h["tian_ma"] = Divination.ex_jk_gt_ten_ma(nDayEarth,@all_ylqgzws,@shi_yao)

    return h
  end

  def ex_jiankang_kong_wang_yang_ren()
    h = Hash.new

    nDaySky = Xdate.GetGanZhiSky(@nGanZhiDay)
    nDayEarth = Xdate.GetGanZhiEarth(@nGanZhiDay)
    nMonthEarth = Xdate.GetGanZhiEarth(@nGanZhiMonth)

    h["kong_wang"] = Divination.ex_jk_ky_kong_wang(nDaySky,nDayEarth,nMonthEarth,@all_ylqgzws,@shi_yao,@par_five_levels,@par_chang_sheng)
    h["yang_ren"] = Divination.ex_jk_ky_yang_ren(nDaySky,@all_ylqgzws)

    h["yao_fan_yin"] = Divination.ex_jk_ky_yao_fan_yin(@all_ylqgzws)

    h["te_shu_zi_sun_dong"] = Divination.ex_jk_ky_te_shu_chong(nDayEarth,@all_ylqgzws)
    h["te_shu_zi_sun_an"] = Divination.ex_jk_ky_te_shu_no_sons(@all_ylqgzws)

    return h
  end

  def ex_jiankang_move_dong_yao()
    h = Hash.new

    h["dong_yao"] = Divination.ex_jk_move_dong_yao(@all_ylqgzws)

    return h
  end

  def ex_jiankang_gua_image()
    h = Hash.new
    gua_he_chong = Divination.Gua_64_he_chong(@wai,@nei)
    special_no = Divination.Gua_64_info_special_no(@wai,@nei)
    gua_fan_yin = Divination.Cr_find_gua_fan_yin(@wai,@nei,@bian_wai,@bian_nei)
    ingua_fu_yin = Divination.Cr_find_ingua_fu_yin(@wai,@nei,@bian_wai,@bian_nei)
    outgua_fu_yin = Divination.Cr_find_outgua_fu_yin(@wai,@nei,@bian_wai,@bian_nei)
    inoutgua_fu_yin = Divination.Cr_find_inoutgua_fu_yin(@wai,@nei,@bian_wai,@bian_nei)

    h["te_shu"] = Divination.ex_jk_gi_te_shu(gua_he_chong,special_no)
    h["fu_yin"] = Divination.ex_jk_gi_fu_yin(gua_fan_yin,ingua_fu_yin,outgua_fu_yin,inoutgua_fu_yin)

    return h
  end
  # 10.應爻空亡
  def ex_jiankang_yin_yao_kong_wang()
    h = Hash.new

    nDaySky = Xdate.GetGanZhiSky(@nGanZhiDay)
    nDayEarth = Xdate.GetGanZhiEarth(@nGanZhiDay)
    nMonthEarth = Xdate.GetGanZhiEarth(@nGanZhiMonth)

    h["yin_yao_kong_wang"] = Divination.ex_jk_yinyao_kong_wang(nDaySky,nDayEarth,nMonthEarth,@all_ylqgzws,@yin_yao,@yin_five,@par_five_levels)

    return h
  end
  # 11.本卦無官，看官鬼伏於第幾爻
  def ex_jiankang_lq3_infusheng()
    h = Hash.new

    h["lq3_in_fusheng"] = Divination.ex_jk_lq3_in_fusheng(@all_ylqgzws)

    return h
  end
  # 以上為說明項目

  def Divination.ex_jiankang_str(key)
    return Divination.liuyaogua_str("jiankang.#{key}")
  end
  def Divination.ex_jk_year_str(key)
    return Divination.ex_jiankang_str("nian.#{key}")
  end
  # 1 年之關係
  # 六合
  def Divination.ex_jk_y_liu_he_str(year_earth,yao_earth)
    key = Divination.dizhi_keys(year_earth,yao_earth)
    return Divination.ex_jk_year_str("liu_he.#{key}")
  end
  def Divination.ex_jk_y_liu_he(year_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_liu_he?(year_earth,a[i])) then
        return Divination.ex_jk_y_liu_he_str(year_earth,a[i])
      end
    end
    return ""
  end
  # 六沖
  def Divination.ex_jk_y_liu_chong_str(year_earth,yao_earth)
    key = Divination.dizhi_keys(year_earth,yao_earth)
    return Divination.ex_jk_year_str("liu_chong.#{key}")
  end
  def Divination.ex_jk_y_liu_chong(year_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_liu_chong?(year_earth,a[i])) then
        return Divination.ex_jk_y_liu_chong_str(year_earth,a[i])
      end
    end
    return ""
  end
  # 互刑
  def Divination.ex_jk_y_hu_xing_str(year_earth,yao_earth)
    key = Divination.dizhi_keys(year_earth,yao_earth)
    return Divination.ex_jk_year_str("hu_xing.#{key}")
  end
  def Divination.ex_jk_y_hu_xing(year_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_hu_xing?(year_earth,a[i])) then
        return Divination.ex_jk_y_hu_xing_str(year_earth,a[i])
      end
    end
    return ""
  end
  # 三刑
  def Divination.ex_jk_y_san_xing_str(year_earth,yao_earth1,yao_earth2)
    return Divination.ex_jk_year_str("san_xing.desc")
  end
  def Divination.ex_jk_y_san_xing(year_earth,ben_gua_di_zhi)
    a = Divination.San_Xing([year_earth],ben_gua_di_zhi)
    if (a.length > 0) then
      a2 = a[0] - [year_earth]
      return Divination.ex_jk_y_san_xing_str(year_earth,a2[0],a2[1])
    end
    return ""
  end
  # 入年墓
  @@Into_mu = {
    "e2" => [9,10],
    "e5" => [1,12,5,11,2,8],
    "e8" => [3,4],
    "e11" => [6,7]
  }
  def Divination.ru_mu_array(nEarth,ben_gua_di_zhi)
    a = @@Into_mu["e#{nEarth}"]
    if (a != nil) then
      a2 = a & ben_gua_di_zhi
      return a2
    end
    return []
  end
  def Divination.ex_jk_y_ru_nian_mu_str(year_earth,yao_earth)
    return Divination.ex_jk_year_str("ru_nian_mu.desc")
  end
  def Divination.ex_jk_y_ru_nian_mu(year_earth,jing_yao_di_zhi)
    a = Divination.ru_mu_array(year_earth,jing_yao_di_zhi)
    if (a.length > 0) then
      return Divination.ex_jk_y_ru_nian_mu_str(year_earth,a[0])
    end
    return ""
  end

  # 2 月之關係
  def Divination.ex_jk_month_str(key)
    return Divination.ex_jiankang_str("yue.#{key}")
  end
  # 六合
  def Divination.ex_jk_m_liu_he_str(month_earth,yao_earth)
    key = Divination.dizhi_keys(month_earth,yao_earth)
    return Divination.ex_jk_month_str("liu_he.#{key}")
  end
  def Divination.ex_jk_m_liu_he(month_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_liu_he?(month_earth,a[i])) then
        return Divination.ex_jk_m_liu_he_str(month_earth,a[i])
      end
    end
    return ""
  end
  # 六沖
  def Divination.ex_jk_m_liu_chong_str(month_earth,yao_earth)
    key = Divination.dizhi_keys(month_earth,yao_earth)
    return Divination.ex_jk_month_str("liu_chong.#{key}")
  end
  def Divination.ex_jk_m_liu_chong(month_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_liu_chong?(month_earth,a[i])) then
        return Divination.ex_jk_m_liu_chong_str(month_earth,a[i])
      end
    end
    return ""
  end
  # 三合
  def Divination.ex_jk_m_san_he_str(month_earth,yao_earth1,yao_earth2)
    return Divination.ex_jk_month_str("san_he.desc")
  end
  def Divination.ex_jk_m_san_he(month_earth,ben_gua_di_zhi)
    a = Divination.san_he_check([month_earth],ben_gua_di_zhi)
    if (a.length > 0) then
      a2 = a[0] - [month_earth]
      return Divination.ex_jk_m_san_he_str(month_earth,a2[0],a2[1])
    end
    return ""
  end
  # 互刑
  def Divination.ex_jk_m_hu_xing_str(month_earth,yao_earth)
    key = Divination.dizhi_keys(month_earth,yao_earth)
    return Divination.ex_jk_month_str("hu_xing.#{key}")
  end
  def Divination.ex_jk_m_hu_xing(month_earth,ben_gua_di_zhi)
    a = ben_gua_di_zhi.uniq
    a.each_index do |i|
      if (Divination.par_is_hu_xing?(month_earth,a[i])) then
        return Divination.ex_jk_m_hu_xing_str(month_earth,a[i])
      end
    end
    return ""
  end
  # 三刑
  def Divination.ex_jk_m_san_xing_str(dz1,dz2,dz3)
    key = Divination.dizhi_keys(dz1,dz2,dz3)
    return Divination.ex_jk_month_str("san_xing.#{key}")
  end
  def Divination.ex_jk_m_san_xing(month_earth,ben_gua_di_zhi)
    a = Divination.San_Xing([month_earth],ben_gua_di_zhi)
    if (a.length > 0) then
      a2 = a[0] #- [month_earth]
      # return Divination.ex_jk_m_san_xing_str(month_earth,a2[0],a2[1])
      return Divination.ex_jk_m_san_xing_str(a2[0],a2[1],a2[2])
    end
    return ""
  end
  # 入月墓
  def Divination.ex_jk_m_ru_yue_mu_str(month_earth,yao_earth)
    return Divination.ex_jk_month_str("ru_yue_mu.desc")
  end
  def Divination.ex_jk_m_ru_yue_mu(month_earth,ben_gua_di_zhi)
    a = Divination.ru_mu_array(month_earth,ben_gua_di_zhi)
    if (a.length > 0) then
      return Divination.ex_jk_m_ru_yue_mu_str(month_earth,a[0])
    end
    return ""
  end
  # 月支與世爻支之旺相休囚死
  def Divination.ex_jk_m_wang_xiang_xiu_qiu_si_str(month_earth,yao_earth)
    key = Divination.dizhi_key(month_earth)
    s = Divination.ex_jk_month_str("wang_xiang_xiu_qiu_si.#{key}")
    return Divination.extract_str(s,yao_earth)
  end
  def Divination.ex_jk_m_wang_xiang_xiu_qiu_si(month_earth,shi_earth,wang_xiang_xiu_qiu_si)
    if (Divination.ex_jk_m_wang_xiang_xiu_qiu_si_has_words?(month_earth,shi_earth,wang_xiang_xiu_qiu_si)) then
      return Divination.ex_jk_m_wang_xiang_xiu_qiu_si_str(month_earth,shi_earth)
    end
    return ""
  end
   # 世爻 子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥
      # [旺,囚,相,相,囚,死,死,囚,休,休,囚,旺],  # 月令 子
      # [死,旺,囚,囚,旺,休,休,旺,相,相,旺,死],  # 丑
      # [休,死,旺,旺,死,相,相,死,囚,囚,死,休],  # 寅
      # [休,死,旺,旺,死,相,相,死,囚,囚,死,休],  # 卯
      # [死,旺,囚,囚,旺,休,休,旺,相,相,旺,死],  # 辰
      # [囚,相,休,休,相,旺,旺,相,死,囚,相,囚],  # 巳
      # [囚,相,休,休,相,旺,旺,相,死,死,相,囚],  # 午
      # [死,旺,囚,囚,旺,休,休,旺,相,相,旺,死],  # 未
      # [相,休,死,死,休,囚,囚,休,旺,旺,休,相],  # 申
      # [相,休,死,死,休,囚,囚,休,旺,旺,休,相],  # 酉
      # [死,旺,囚,囚,旺,休,休,旺,相,相,旺,死],  # 戌
      # [旺,囚,相,相,囚,死,死,囚,休,休,囚,旺]  # 亥
  @@Month_Shi_WXXQS = [
      [1,4,2,2,4,5,5,4,3,3,4,1],  # 月令 子
      [5,1,4,4,1,3,3,1,2,2,1,5],  # 丑
      [3,5,1,1,5,2,2,5,4,4,5,3],  # 寅
      [3,5,1,1,5,2,2,5,4,4,5,3],  # 卯
      [5,1,4,4,1,3,3,1,2,2,1,5],  # 辰
      [4,2,3,3,2,1,1,2,5,5,2,4],  # 巳
      [4,2,3,3,2,1,1,2,5,5,2,4],  # 午
      [5,1,4,4,1,3,3,1,2,2,1,5],  # 未
      [2,3,5,5,3,4,4,3,1,1,3,2],  # 申
      [2,3,5,5,3,4,4,3,1,1,3,2],  # 酉
      [5,1,4,4,1,3,3,1,2,2,1,5],  # 戌
      [1,4,2,2,4,5,5,4,3,3,4,1]  # 亥
  ]
  def Divination.ex_jk_m_wang_xiang_xiu_qiu_si_has_words?(month_earth,shi_earth,wang_xiang_xiu_qiu_si)
    a = @@Month_Shi_WXXQS[month_earth - 1]
    return (wang_xiang_xiu_qiu_si == a[shi_earth - 1])
  end

  # 3 日之12長生
  def Divination.ex_jk_day_str(key)
    return Divination.ex_jiankang_str("ri.#{key}")
  end
  def Divination.ex_jk_day_shi_str(day_dz1,shi_dz2)
    # 日辰
    key = Divination.dizhi_keys(day_dz1)
    s = Divination.ex_jk_day_str("shi.#{key}")

    # 世爻爻支 子 丑 。。⋯⋯ 亥共12個
    return Divination.extract_str(s,shi_dz2)
  end
  # @@Zhichang_Day_DiZhi_ChangSheng = [
  #   [帝旺,帝旺,沐浴,沐浴,帝旺,胎,胎,帝旺,死,死,帝旺,帝旺],   # 子
  #   [衰,衰,冠帶,冠帶,衰,養,養,衰,墓,墓,衰,衰],   # 丑
  #   [病,病,臨官,臨官,病,長生,長生,病,絕,胎,病,病],   # 寅
  #   [死,死,帝旺,帝旺,死,沐浴,沐浴,死,絕,胎,死,死],   # 卯
  #   [墓,墓,衰,衰,墓,冠帶,冠帶,墓,養,養,墓,墓],   # 辰
  #   [絕,絕,病,病,絕,臨官,臨官,絕,長生,長生,絕,絕],   # 巳
  #   [胎,胎,死,死,胎,帝旺,帝旺,胎,沐浴,沐浴,胎,胎],   # 午
  #   [養,養,墓,墓,養,衰,衰,養,冠帶,冠帶,養,養],   # 未
  #   [長生,長生,絕,絕,長生,病,病,長生,臨官,臨官,長生,長生],   # 申
  #   [沐浴,沐浴,胎,胎,沐浴,死,死,沐浴,帝旺,帝旺,沐浴,沐浴],   # 酉
  #   [冠帶,冠帶,養,養,冠帶,墓,墓,冠帶,衰,衰,冠帶,冠帶],   # 戌
  #   [臨官,臨官,長生,長生,臨官,絕,絕,臨官,病,病,臨官,臨官]   # 亥
  #                                 ]
  @@Zhichang_Day_DiZhi_ChangSheng = [
    [5,5,2,2,5,11,11,5,8,8,5,5],
    [6,6,3,3,6,12,12,6,9,9,6,6],
    [7,7,4,4,7,1,1,7,10,11,7,7],
    [8,8,5,5,8,2,2,8,10,11,8,8],
    [9,9,6,6,9,3,3,9,12,12,9,9],
    [10,10,7,7,10,4,4,10,1,1,10,10],
    [11,11,8,8,11,5,5,11,2,2,11,11],
    [12,12,9,9,12,6,6,12,3,3,12,12],
    [1,1,10,10,1,7,7,1,4,4,1,1],
    [2,2,11,11,2,8,8,2,5,5,2,2],
    [3,3,12,12,3,9,9,3,6,6,3,3],
    [4,4,1,1,4,10,10,4,7,7,4,4]
                                  ]
  def Divination.ex_jk_day_shi_chang_sheng(day_dz1,shi_dz2,chang_sheng)
    a = @@Zhichang_Day_DiZhi_ChangSheng[day_dz1 - 1]
    if (a[shi_dz2 - 1] == chang_sheng) then
      return Divination.ex_jk_day_shi_str(day_dz1,shi_dz2)
    else
      return ""
    end
  end

  # 4 病症-1官爻
  def Divination.ex_jk_ji_bing_guan_yao_str(earth,liu_shou,yao,five_level)
    dz_key = Divination.dizhi_key(earth)
    liu_shou_key = Divination.liu_shou_key(liu_shou)
    yao_key = Divination.yao_key(yao)
    s = Divination.ex_jiankang_str("ji_bing_guan_yao.#{dz_key}.#{liu_shou_key}.#{yao_key}")
    return Divination.extract_str(s,five_level)
  end
  def Divination.ex_jk_ji_bing_guan_yao_fu_shen_str()
    return Divination.ex_jiankang_str("ji_bing_guan_yao.fu_shen")
  end
  def Divination.ex_jk_ji_bing_guan_yao(all_ylqgzws,liu_shous,par_five_levels)
    # 要伏神的官鬼,小高說的2014/9/24
    ylqgzws = all_ylqgzws
    # 官鬼 Liu_Qin_3
    aOut = Array.new
    ylqgzws.each_index do |i|
      ylqgzw = ylqgzws[i]
      liu_qin = Divination.fu_shen_liu_qin(ylqgzw)
      if (liu_qin == Liu_Qin_3) then
        yao = Divination.fu_shen_yao(ylqgzw)
        five = Divination.fu_shen_wu_xing(ylqgzw)
        earth = Divination.fu_shen_di_zhi(ylqgzw)
        liu_shou = Divination.liu_shou(liu_shous,yao)
        five_level = Divination.par_find_five_level(par_five_levels,five)
        aOut.push(Divination.ex_jk_ji_bing_guan_yao_str(earth,liu_shou,yao,five_level))
      end
    end
    return aOut.uniq
  end

  # 4.1 病症-2 剋世
  # 以年月日有剋到世爻才能抓
  # 本卦動之爻五行剋到世爻切也算。
  # 世爻為變爻則變卦之剋到世爻也算。
  def Divination.ex_jk_ji_bing_ke_shi_str(earth,liu_shou,r1,five_level,ke_shi_liu_qin)
    r_ke = ke_shi_liu_qin  # Divination.liu_qins_ke(r1)
    dz_key = Divination.dizhi_key(earth)
    liu_shou_key = Divination.liu_shou_key(liu_shou)
    liu_qin_key = Divination.liu_qin_key(r1)
    # return Divination.ex_jiankang_str("ji_bing_ke_shi.#{dz_key}.#{liu_shou_key}.#{liu_qin_key}.r#{r_ke}.fl#{five_level}")
    s = Divination.ex_jiankang_str("ji_bing_ke_shi.#{dz_key}.#{liu_shou_key}.#{liu_qin_key}")
    return Divination.extract_str(s,five_level)
  end
  def Divination.ex_jk_ji_bing_ke_shi(nGanZhiYear,nGanZhiMonth,nGanZhiDay,all_ylqgzws,shi_yao,liu_shous,par_five_levels)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    ben_gua_liu_qins = Divination.par_ben_gua_liu_qins(all_ylqgzws)

    shi_wu_xing = Divination.par_yao_five(all_ylqgzws,shi_yao)  # 世爻五行
    earth = Divination.par_yao_earth(all_ylqgzws,shi_yao)  # 世爻地支
    liu_shou = Divination.liu_shou(liu_shous,shi_yao)  # 世爻六獸
    liu_qin = Divination.gua_yao_liu_qin(ben_gua_liu_qins,shi_yao)  # 世爻六親
    five_level = Divination.par_find_five_level(par_five_levels,shi_wu_xing)  # 世旺旺休囚死
    # 剋世六親
    # 以年月日有剋到世爻才能抓
    lq_gzfs_date = Divination.par_ganzhi_ylqgzw_s(nGanZhiYear,nGanZhiMonth,nGanZhiDay,all_ylqgzws)
    ke_shi_liu_qin = Divination.ex_jk_ji_bing_ke_shi_liu_qin(shi_wu_xing,lq_gzfs_date)
    if (ke_shi_liu_qin != nil) then
      return Divination.ex_jk_ji_bing_ke_shi_str(earth,liu_shou,liu_qin,five_level,ke_shi_liu_qin)
    end

    # 本卦動之爻五行剋到世爻切也算。
    lq_gzfs_dong_yao = Divination.par_ben_gua_dong_yao_ylqgzws(all_ylqgzws)
    ke_shi_liu_qin = Divination.ex_jk_ji_bing_ke_shi_liu_qin(shi_wu_xing,lq_gzfs_dong_yao)
    if (ke_shi_liu_qin != nil) then
      return Divination.ex_jk_ji_bing_ke_shi_str(earth,liu_shou,liu_qin,five_level,ke_shi_liu_qin)
    end

    # 世爻為變爻則變卦之剋到世爻也算。
    is_dong_yao,n = Divination.is_shi_yao_dong_yao?(all_ylqgzws,shi_yao)
    if (is_dong_yao) then
      lq_gzfs_bian_yao = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
      ke_shi_liu_qin = Divination.ex_jk_ji_bing_ke_shi_liu_qin(shi_wu_xing,[lq_gzfs_bian_yao[n]])
      if (ke_shi_liu_qin != nil) then
        return Divination.ex_jk_ji_bing_ke_shi_str(earth,liu_shou,liu_qin,five_level,ke_shi_liu_qin)
      end
    end
    # # 世爻為變爻則變卦之剋到世爻也算。
    # is_dong_yao = Divination.is_shi_yao_dong_yao?(all_ylqgzws,shi_yao)
    # if (is_dong_yao) then
    #   lq_gzfs_bian_yao = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    #   ylqgzw = Divination.par_find_ylqgzw_by_yao(lq_gzfs_bian_yao,shi_yao)
    #   ylqgzws = [ylqgzw]
    #   ke_shi_liu_qin = Divination.ex_jk_ji_bing_ke_shi_liu_qin(shi_wu_xing,ylqgzws)
    #   if (ke_shi_liu_qin != nil) then
    #     return Divination.ex_jk_ji_bing_ke_shi_str(earth,liu_shou,liu_qin,five_level,ke_shi_liu_qin)
    #   end
    # end
    return ""
  end
  # lq_gzfs ： [[爻，六親，[天干，地支，五行]],...]
  def Divination.ex_jk_ji_bing_ke_shi_liu_qin(shi_wu_xing,lq_gzfs)
    lq_gzfs.each_index do |i|
      a = lq_gzfs[i]
      lq_wu_xing = Divination.fu_shen_wu_xing(a)
      if (Divination.Five_SanKe(shi_wu_xing,lq_wu_xing) == Five_SK_Ke) then
        return Divination.fu_shen_liu_qin(a)
      end
    end
    return nil
  end
  def Divination.is_shi_yao_dong_yao?(all_ylqgzws,shi_yao)
    ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    ylqgzws.each_index do |i|
      if (Divination.fu_shen_yao(ylqgzws[i]) == shi_yao) then
        return true,i
      end
    end
    return false,nil
  end
  def Divination.par_ben_gua_dong_yao_ylqgzws(all_ylqgzws)
    aOut = Array.new
    ben_gua_ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    bian_gua_ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)

    bian_gua_ylqgzws.each_index do |i|
      bian_yao = Divination.fu_shen_yao(bian_gua_ylqgzws[i])
      aOut.push(Divination.par_find_ylqgzw_by_yao(ben_gua_ylqgzws,bian_yao))
    end
    return aOut
  end
  # 5 世爻
  def Divination.ex_jk_shi_yao_str(earth,liu_shou,liu_qin,five_level)
    dz_key = Divination.dizhi_key(earth)
    liu_shou_key = Divination.liu_shou_key(liu_shou)
    liu_qin_key = Divination.liu_qin_key(liu_qin)
    s = Divination.ex_jiankang_str("shi_yao.#{dz_key}.#{liu_shou_key}.#{liu_qin_key}")
    return Divination.extract_str(s,five_level)
  end
  def Divination.ex_jk_shi_yao(all_ylqgzws,shi_yao,liu_shous,par_five_levels)
    ben_gua_liu_qins = Divination.par_ben_gua_liu_qins(all_ylqgzws)

    five = Divination.par_yao_five(all_ylqgzws,shi_yao)
    earth = Divination.par_yao_earth(all_ylqgzws,shi_yao)
    liu_shou = Divination.liu_shou(liu_shous,shi_yao)
    liu_qin = Divination.gua_yao_liu_qin(ben_gua_liu_qins,shi_yao)
    five_level = Divination.par_find_five_level(par_five_levels,five)
    return Divination.ex_jk_shi_yao_str(earth,liu_shou,liu_qin,five_level)
  end

  # 5.1 應爻
  def Divination.ex_jk_yin_yao_str(earth,liu_shou,liu_qin,five_level)
    dz_key = Divination.dizhi_key(earth)
    liu_shou_key = Divination.liu_shou_key(liu_shou)
    liu_qin_key = Divination.liu_qin_key(liu_qin)
    s = Divination.ex_jiankang_str("yin_yao.#{dz_key}.#{liu_shou_key}.#{liu_qin_key}")
    return Divination.extract_str(s,five_level)
  end
  def Divination.ex_jk_yin_yao(all_ylqgzws,yin_yao,liu_shous,par_five_levels)
    ben_gua_liu_qins = Divination.par_ben_gua_liu_qins(all_ylqgzws)

    five = Divination.par_yao_five(all_ylqgzws,yin_yao)
    earth = Divination.par_yao_earth(all_ylqgzws,yin_yao)
    liu_shou = Divination.liu_shou(liu_shous,yin_yao)
    liu_qin = Divination.gua_yao_liu_qin(ben_gua_liu_qins,yin_yao)
    five_level = Divination.par_find_five_level(par_five_levels,five)
    return Divination.ex_jk_yin_yao_str(earth,liu_shou,liu_qin,five_level)
  end
  def Divination.ex_jk_shi_yin_san_ke_str2(earth,liu_shou,san_ke_key,five_level)
    dz_key = Divination.dizhi_key(earth)
    liu_shou_key = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_jiankang_str("yin_yao.#{dz_key}.#{liu_shou_key}.#{san_ke_key}")
    return Divination.extract_str(s,five_level)
  end
  def Divination.ex_jk_shi_yin_san_ke_str(earth,liu_shou,shi_five,yin_five,five_level)
    san_ke_key = Divination.ex_jk_make_shi_yin_san_ke_key(shi_five,yin_five)
    if (san_ke_key != "") then
      return Divination.ex_jk_shi_yin_san_ke_str2(earth,liu_shou,san_ke_key,five_level)
    else
      return ""
    end
  end
  def Divination.ex_jk_make_shi_yin_san_ke_key(shi_five,yin_five)
    n = Divination.Five_San_Ke(shi_five,yin_five)
    if (n == Five_San) then
      key = "shi_san_yin"
    elsif (n == Five_Ke) then
      key = "shi_ke_yin"
    elsif (n == Five_Saned) then
      key = "yin_san_shi"
    elsif (n == Five_Keed) then
      key = "yin_ke_shi"
    else
      key = ""
    end
    return key
  end
  def Divination.ex_jk_shi_yin_san_ke(all_ylqgzws,shi_yao,yin_yao,liu_shous,par_five_levels)
    ben_gua_liu_qins = Divination.par_ben_gua_liu_qins(all_ylqgzws)

    shi_five = Divination.par_yao_five(all_ylqgzws,shi_yao)
    yin_five = Divination.par_yao_five(all_ylqgzws,yin_yao)
    earth = Divination.par_yao_earth(all_ylqgzws,yin_yao)
    liu_shou = Divination.liu_shou(liu_shous,yin_yao)
    five_level = Divination.par_find_five_level(par_five_levels,yin_five)
    return Divination.ex_jk_shi_yin_san_ke_str(earth,liu_shou,shi_five,yin_five,five_level)
  end

  # 5.2 子孫爻
  def Divination.ex_jk_sons_yao_str(earth,liu_shou,five_level)
    dz_key = Divination.dizhi_key(earth)
    liu_shou_key = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_jiankang_str("sons_yao.#{dz_key}.#{liu_shou_key}")
    return Divination.extract_str(s,five_level)
  end
  def Divination.ex_jk_sons_vs_shi_str(san_ke)
    if (san_ke == Five_SK_San) then
      return Divination.ex_jiankang_str("sons_yao.sons_san_shi")
    elsif (san_ke == Five_SK_Ke) then
      return Divination.ex_jiankang_str("sons_yao.sons_ke_shi")
    else
      return ""
    end
  end
  def Divination.ex_jk_sons_vs_shi(all_ylqgzws,shi_yao)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)

    five_shi = Divination.par_yao_five(all_ylqgzws,shi_yao)
    ylqgzws.each_index do |i|
      a = ylqgzws[i]
      liu_qin = Divination.fu_shen_liu_qin(a)
      if (liu_qin == Liu_Qin_5) then
        five_sons = Divination.fu_shen_wu_xing(a)
        san_ke = Divination.Five_SanKe(five_shi,five_sons)
        return Divination.ex_jk_sons_vs_shi_str(san_ke)
      end
    end
    return ""
  end
  def Divination.ex_jk_sons_yao(all_ylqgzws,liu_shous,par_five_levels)
    ylqgzws = Divination.par_benbian_gua_ylqgzws(all_ylqgzws)

    aOut = Array.new
    ylqgzws.each_index do |i|
      a = ylqgzws[i]
      liu_qin = Divination.fu_shen_liu_qin(a)
      if (liu_qin == Liu_Qin_5) then
        five = Divination.fu_shen_wu_xing(a)
        earth = Divination.fu_shen_di_zhi(a)
        yao = Divination.fu_shen_yao(a)
        liu_shou = Divination.liu_shou(liu_shous,yao)
        five_level = Divination.par_find_five_level(par_five_levels,five)
        aOut.push(Divination.ex_jk_sons_yao_str(earth,liu_shou,five_level))
      end
    end
    return aOut.uniq
  end

  # 6 三合刑
  # 三合
  def Divination.ex_jk_he_san_xing_str(key)
    return Divination.ex_jiankang_str("he_san_xing.#{key}")
  end
  def Divination.ex_jk_he_san_xing_san_he_str(dz1,dz2,dz3,liu_shou,liu_qin)
    dz_key = Divination.dizhi_keys(dz1,dz2,dz3)
    liu_shou_key = Divination.liu_shou_key(liu_shou)
    s = Divination.ex_jk_he_san_xing_str("san_he.#{dz_key}.#{liu_shou_key}")
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_jk_he_san_xing_san_he_alldate(all_ylqgzws,liu_shous)
    # dong_di_zhis = [date_earth]
    # jing_yao_di_zhis = Divination.par_benbian_gua_di_zhis(all_ylqgzws)
    dong_di_zhis = Divination.par_dong_gua_di_zhis(all_ylqgzws)
    jing_yao_di_zhis = Divination.par_ben_gua_di_zhis(all_ylqgzws)
    a = Divination.san_he_check(dong_di_zhis,jing_yao_di_zhis)
    if (a.length > 0) then
      a2 = a[0]
      # 以三合中間的地支來判斷在六爻中的位置
      yao_index,liu_shou,liu_qin = Divination.ex_jk_earth_info(a2[1],all_ylqgzws,liu_shous)
      if (liu_qin == nil) then
        return ""
      end
      return Divination.ex_jk_he_san_xing_san_he_str(a2[0],a2[1],a2[2],liu_shou,liu_qin)
    end
    return ""
  end
  def Divination.ex_jk_he_san_xing_san_he(year_dizhi,month_dizhi,day_dizhi,all_ylqgzws,liu_shous,yin_earth)
    h = {"nian" => "", "yue" => "", "ri" => "", "yin_yao" => ""}

    san_he,liu_shou,liu_qin = Divination.explain_san_he([year_dizhi],all_ylqgzws,liu_shous)
    if (san_he != nil) then
      dz1,dz2,dz3 = san_he[0],san_he[1],san_he[2]
      h["nian"] = Divination.ex_jk_he_san_xing_san_he_str(dz1,dz2,dz3,liu_shou,liu_qin)
    end

    san_he,liu_shou,liu_qin = Divination.explain_san_he([month_dizhi],all_ylqgzws,liu_shous)
    if (san_he != nil) then
      dz1,dz2,dz3 = san_he[0],san_he[1],san_he[2]
      h["yue"] = Divination.ex_jk_he_san_xing_san_he_str(dz1,dz2,dz3,liu_shou,liu_qin)
    end
    san_he,liu_shou,liu_qin = Divination.explain_san_he([day_dizhi],all_ylqgzws,liu_shous)
    if (san_he != nil) then
      dz1,dz2,dz3 = san_he[0],san_he[1],san_he[2]
      h["ri"] = Divination.ex_jk_he_san_xing_san_he_str(dz1,dz2,dz3,liu_shou,liu_qin)
    end

    san_he,liu_shou,liu_qin = Divination.explain_san_he([yin_earth],all_ylqgzws,liu_shous)
    if (san_he != nil) then
      dz1,dz2,dz3 = san_he[0],san_he[1],san_he[2]
      h["yin_yao"] = Divination.ex_jk_he_san_xing_san_he_str(dz1,dz2,dz3,liu_shou,liu_qin)
    end

    return h
  end
  # 三刑
  def Divination.ex_jk_he_san_xing_san_xing_str(dz1,dz2,dz3,liu_qin,r2,r3)
    dz_key = Divination.dizhi_keys(dz1,dz2,dz3)
    s = Divination.ex_jk_he_san_xing_str("san_xing.#{dz_key}")
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_jk_he_san_xing_san_xing_alldate(all_ylqgzws,liu_shous)
    # dong_di_zhis = [date_earth]
    # jing_yao_di_zhis = Divination.par_benbian_gua_di_zhis(all_ylqgzws)
    dong_di_zhis = Divination.par_dong_gua_di_zhis(all_ylqgzws)
    jing_yao_di_zhis = Divination.par_ben_gua_di_zhis(all_ylqgzws)
    a = Divination.San_Xing(dong_di_zhis,jing_yao_di_zhis)
    if (a.length > 0) then
      a2 = a[0]
      yao_index1,liu_shou1,r1 = Divination.ex_jk_earth_info(a2[0],all_ylqgzws,liu_shous)
      yao_index2,liu_shou2,r2 = Divination.ex_jk_earth_info(a2[1],all_ylqgzws,liu_shous)
      yao_index3,liu_shou3,r3 = Divination.ex_jk_earth_info(a2[2],all_ylqgzws,liu_shous)
      if (r1 == nil) then
        return ""
      end
      return Divination.ex_jk_he_san_xing_san_xing_str(a2[0],a2[1],a2[2],r1,r2,r3)
    end
    return ""
  end
  def Divination.ex_jk_he_san_xing_san_xing(year_dizhi,month_dizhi,day_dizhi,all_ylqgzws,liu_shous,yin_earth)
    h = {"nian" => "", "yue" => "", "ri" => "", "yin_yao" => ""}

    ben_gua_di_zhi = Divination.par_ben_gua_di_zhis(all_ylqgzws)

    san_xing = Divination.explain_san_xing_check([year_dizhi],all_ylqgzws)
    if (san_xing != nil) then
      dz1,dz2,dz3 = san_xing[0],san_xing[1],san_xing[2]
      yao_index1,liu_shou1,r1 = Divination.ex_jk_earth_info(dz1,all_ylqgzws,liu_shous)
      yao_index2,liu_shou2,r2 = Divination.ex_jk_earth_info(dz2,all_ylqgzws,liu_shous)
      yao_index3,liu_shou3,r3 = Divination.ex_jk_earth_info(dz3,all_ylqgzws,liu_shous)
      if (r1 != nil) then
        h["nian"] = Divination.ex_jk_he_san_xing_san_xing_str(dz1,dz2,dz3,r1,r2,r3)
      end
    end

    san_xing = Divination.explain_san_xing_check([month_dizhi],all_ylqgzws)
    if (san_xing != nil) then
      dz1,dz2,dz3 = san_xing[0],san_xing[1],san_xing[2]
      yao_index1,liu_shou1,r1 = Divination.ex_jk_earth_info(dz1,all_ylqgzws,liu_shous)
      yao_index2,liu_shou2,r2 = Divination.ex_jk_earth_info(dz2,all_ylqgzws,liu_shous)
      yao_index3,liu_shou3,r3 = Divination.ex_jk_earth_info(dz3,all_ylqgzws,liu_shous)
      if (r1 != nil) then
        h["yue"] = Divination.ex_jk_he_san_xing_san_xing_str(dz1,dz2,dz3,r1,r2,r3)
      end
    end
    san_xing = Divination.explain_san_xing_check([day_dizhi],all_ylqgzws)
    if (san_xing != nil) then
      dz1,dz2,dz3 = san_xing[0],san_xing[1],san_xing[2]
      yao_index1,liu_shou1,r1 = Divination.ex_jk_earth_info(dz1,all_ylqgzws,liu_shous)
      yao_index2,liu_shou2,r2 = Divination.ex_jk_earth_info(dz2,all_ylqgzws,liu_shous)
      yao_index3,liu_shou3,r3 = Divination.ex_jk_earth_info(dz3,all_ylqgzws,liu_shous)
      if (r1 != nil) then
        h["ri"] = Divination.ex_jk_he_san_xing_san_xing_str(dz1,dz2,dz3,r1,r2,r3)
      end
    end
    san_xing = Divination.explain_san_xing_check([yin_earth],all_ylqgzws)
    if (san_xing != nil) then
      dz1,dz2,dz3 = san_xing[0],san_xing[1],san_xing[2]
      yao_index1,liu_shou1,r1 = Divination.ex_jk_earth_info(dz1,all_ylqgzws,liu_shous)
      yao_index2,liu_shou2,r2 = Divination.ex_jk_earth_info(dz2,all_ylqgzws,liu_shous)
      yao_index3,liu_shou3,r3 = Divination.ex_jk_earth_info(dz3,all_ylqgzws,liu_shous)
      if (r1 != nil) then
        h["yin_yao"] = Divination.ex_jk_he_san_xing_san_xing_str(dz1,dz2,dz3,r1,r2,r3)
      end
    end
    return h
  end
  def Divination.ex_jk_earth_info(di_zhi,all_ylqgzws,liu_shous)
    ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,di_zhi)

    if (ylqgzw == nil) then
      ylqgzws = Divination.par_all_gua_ylqgzws(all_ylqgzws)
      wu_xing = Divination.Earth_Five(di_zhi)
      ylqgzw = Divination.par_find_ylqgzw_by_wu_xing(ylqgzws,wu_xing)
    end
    if (ylqgzw == nil) then
      return nil,nil,nil
    end
    yao = Divination.fu_shen_yao(ylqgzw)
    yao_index = Divination.Yao2Index(yao)
    liu_shou = nil
    if (liu_shous != nil) then
      liu_shou = liu_shous[yao_index]
    end
    liu_qin = Divination.fu_shen_liu_qin(ylqgzw)

    return yao_index,liu_shou,liu_qin
  end
  def Divination.par_find_ylqgzw_in_all_gua_ylqgzws_by_di_zhi(di_zhi,all_ylqgzws)
    ylqgzws = Divination.par_all_gua_ylqgzws(all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,di_zhi)

    return ylqgzw
  end
  def Divination.par_find_ylqgzw_in_all_yao_ylqgzws_by_di_zhi(di_zhi,all_ylqgzws)
    ylqgzws = Divination.par_all_yao_ylqgzws(all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,di_zhi)

    return ylqgzw
  end
  def Divination.par_find_ylqgzw_in_fusheng_ylqgzws_by_di_zhi(di_zhi,all_ylqgzws)
    ylqgzws = Divination.par_fu_sheng_ylqgzw(all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_by_di_zhi(ylqgzws,di_zhi)

    return ylqgzw
  end

  # 互刑
  def Divination.ex_jk_he_san_xing_hu_xing_str(dz1,dz2,liu_qin,r2)
    dz_key = Divination.dizhi_keys(dz1,dz2)
    s = Divination.ex_jk_he_san_xing_str("hu_xing.#{dz_key}")
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_jk_hh3_hu_xing_alldate(all_ylqgzws)
    # jing_yao_di_zhis = Divination.par_benbian_gua_di_zhis(all_ylqgzws)
    dong_di_zhis = Divination.par_dong_gua_di_zhis(all_ylqgzws)
    jing_yao_di_zhis = Divination.par_ben_gua_di_zhis(all_ylqgzws)

    a = Divination.ex_jk_hh3_hu_xing_check(dong_di_zhis,all_ylqgzws)
    aOut = Array.new
    a.each_index do |i|
      yao_index1,liu_shou1,r1 = Divination.ex_jk_earth_info(a[i][0],all_ylqgzws,nil)
      yao_index2,liu_shou2,r2 = Divination.ex_jk_earth_info(a[i][1],all_ylqgzws,nil)
      if (r1 != nil && r2 != nil) then
        r1,r2 = Divination.ex_jk_hh3_hu_xing_liu_qins_check(r1,r2)
        aOut.push(Divination.ex_jk_he_san_xing_hu_xing_str(a[i][0],a[i][1],r1,r2))
      end
    end
    return aOut.uniq
  end
  def Divination.ex_jk_hh3_hu_xing_check_alldate(dong_di_zhis,jing_yao_di_zhis)
    aOut = Array.new
    dong_di_zhis.each_index do |i|
      jing_yao_di_zhis.each_index do |j|
        if (Divination.par_is_hu_xing?(dong_di_zhis[i],jing_yao_di_zhis[j])) then
          a = [dong_di_zhis[i],jing_yao_di_zhis[j]]
          b = [jing_yao_di_zhis[j],dong_di_zhis[i]]
          if !(aOut.include?(a) || aOut.include?(b)) then
            aOut.push(a)
          end
        end
      end
    end
    return aOut.uniq
  end
  def Divination.ex_jk_hh3_hu_xing(year_dizhi,month_dizhi,day_dizhi,all_ylqgzws,yin_earth)
    h = {"nian" => "", "yue" => "", "ri" => "", "yin_yao" => ""}

    # jing_yao_di_zhis = Divination.par_ben_gua_di_zhis(all_ylqgzws)

    a = Divination.ex_jk_hh3_hu_xing_check(year_dizhi,all_ylqgzws)
    if (a != nil) then
      yao_index1,liu_shou1,r1 = Divination.ex_jk_earth_info(a[0],all_ylqgzws,nil)
      yao_index2,liu_shou2,r2 = Divination.ex_jk_earth_info(a[1],all_ylqgzws,nil)
      if (r1 != nil && r2 != nil) then
        r1,r2 = Divination.ex_jk_hh3_hu_xing_liu_qins_check(r1,r2)
        h["nian"] = Divination.ex_jk_he_san_xing_hu_xing_str(a[0],a[1],r1,r2)
      end
    end

    a = Divination.ex_jk_hh3_hu_xing_check(month_dizhi,all_ylqgzws)
    if (a != nil) then
      yao_index1,liu_shou1,r1 = Divination.ex_jk_earth_info(a[0],all_ylqgzws,nil)
      yao_index2,liu_shou2,r2 = Divination.ex_jk_earth_info(a[1],all_ylqgzws,nil)
      if (r1 != nil && r2 != nil) then
        r1,r2 = Divination.ex_jk_hh3_hu_xing_liu_qins_check(r1,r2)
        h["yue"] = Divination.ex_jk_he_san_xing_hu_xing_str(a[0],a[1],r1,r2)
      end
    end

    a = Divination.ex_jk_hh3_hu_xing_check(day_dizhi,all_ylqgzws)
    if (a != nil) then
      yao_index1,liu_shou1,r1 = Divination.ex_jk_earth_info(a[0],all_ylqgzws,nil)
      yao_index2,liu_shou2,r2 = Divination.ex_jk_earth_info(a[1],all_ylqgzws,nil)
      if (r1 != nil && r2 != nil) then
        r1,r2 = Divination.ex_jk_hh3_hu_xing_liu_qins_check(r1,r2)
        h["ri"] = Divination.ex_jk_he_san_xing_hu_xing_str(a[0],a[1],r1,r2)
      end
    end

    a = Divination.ex_jk_hh3_hu_xing_check(yin_earth,all_ylqgzws)
    if (a != nil) then
      yao_index1,liu_shou1,r1 = Divination.ex_jk_earth_info(a[0],all_ylqgzws,nil)
      yao_index2,liu_shou2,r2 = Divination.ex_jk_earth_info(a[1],all_ylqgzws,nil)
      if (r1 != nil && r2 != nil) then
        r1,r2 = Divination.ex_jk_hh3_hu_xing_liu_qins_check(r1,r2)
        h["yin_yao"] = Divination.ex_jk_he_san_xing_hu_xing_str(a[0],a[1],r1,r2)
      end
    end
    return h
  end
  # def Divination.ex_jk_hh3_hu_xing_check(date_di_zhi,jing_yao_di_zhis)
  def Divination.ex_jk_hh3_hu_xing_check(date_di_zhi,all_ylqgzws)
    par_all_yao_di_zhis = Divination.par_all_yao_di_zhis(all_ylqgzws)
    # jing_yao_di_zhis.each_index do |j|
    par_all_yao_di_zhis.each_index do |j|
      if (Divination.par_is_hu_xing?(date_di_zhi,par_all_yao_di_zhis[j])) then
        return [date_di_zhi,par_all_yao_di_zhis[j]]
      end
    end
    return nil
  end
  def Divination.ex_jk_hh3_hu_xing_liu_qins_check(r1,r2)
    a = [r1,r2]
    if (a & [1,2] == a) then
      a2 = [1,2]
    elsif (a & [2,5] == a) then
      a2 = [2,5]
    elsif (a & [5,4] == a) then
      a2 = [5,4]
    elsif (a & [4,3] == a) then
      a2 = [4,3]
    elsif (a & [3,1] == a) then
      a2 = [3,1]
    else
      a2 = a
    end
    return a2[0],a2[1]
  end

  # 7 參數-貴人驛馬
  # ※貴人1
  def Divination.ex_jk_gui_ren_ten_ma_str(key)
    return Divination.ex_jiankang_str("gui_ren_ten_ma.#{key}")
  end
  def Divination.ex_jk_gt_shi_hold_gui_ren_str()
    return Divination.ex_jk_gui_ren_ten_ma_str("gui_ren.shi_hold_gui_ren")
  end
  def Divination.ex_jk_gt_gui_ren_str(s1,dz1,dz2,b1,r1,five_level)
    # return Divination.ex_jk_gui_ren_ten_ma_str("gui_ren.s#{s1}_e#{e1}_e#{e2}.b#{b1}.r#{r1}.fl#{five_level}")
    tg_key = Divination.tiangan_key(s1)
    liu_shou_key = Divination.liu_shou_key(b1)
    liu_qin_key = Divination.liu_qin_key(r1)
    s = Divination.ex_jk_gui_ren_ten_ma_str("gui_ren.#{tg_key}.#{liu_shou_key}.#{liu_qin_key}")
    return Divination.extract_str(s,five_level)
  end

  def Divination.ex_jk_gt_gui_ren(nDaySky,all_ylqgzws,liu_shous,par_five_levels,shi_yao)
    aEarth = Divination.gui_ren(nDaySky,1)
    if (Divination.ex_jk_gt_is_shi_hold_gui_ren(aEarth,all_ylqgzws,shi_yao)) then
      return Divination.ex_jk_gt_shi_hold_gui_ren_str()
    else
      return Divination.ex_jk_gt_gui_ren_normal(nDaySky,all_ylqgzws,liu_shous,par_five_levels,aEarth)
    end
  end
  def Divination.ex_jk_gt_is_shi_hold_gui_ren(aEarth,all_ylqgzws,shi_yao)
    aEarth.each_index do |i|
      ylqgzw = Divination.par_find_ylqgzw_in_all_yao_ylqgzws_by_di_zhi(aEarth[i],all_ylqgzws)
      yao = nil
      if (ylqgzw != nil) then
        yao = Divination.fu_shen_yao(ylqgzw)
      end
      if (yao == shi_yao) then
        return true
      end
    end

    return false
  end
  def Divination.ex_jk_gt_gui_ren_normal(nDaySky,all_ylqgzws,liu_shous,par_five_levels,aEarth)
    aOut = Array.new
    aEarth.each_index do |i|
      ylqgzw = Divination.par_find_ylqgzw_in_all_yao_ylqgzws_by_di_zhi(aEarth[i],all_ylqgzws)
      if (ylqgzw != nil) then
        yao = Divination.fu_shen_yao(ylqgzw)
        e1,e2 = aEarth[0],aEarth[1]
        b1 = Divination.liu_shou(liu_shous,yao)
        r1 = Divination.fu_shen_liu_qin(ylqgzw)
        five = Divination.fu_shen_wu_xing(ylqgzw)
        five_level = Divination.par_find_five_level(par_five_levels,five)
        aOut.push(Divination.ex_jk_gt_gui_ren_str(nDaySky,e1,e2,b1,r1,five_level))
      end
    end

    return aOut.uniq
  end

  # ※驛馬
  def Divination.ex_jk_gt_shi_hold_ten_ma_str(e1,e2,r1)
    return Divination.ex_jk_gui_ren_ten_ma_str("ten_ma.shi_hold_ten_ma")
  end
  def Divination.ex_jk_gt_ten_ma_str(dz1,dz2,liu_qin)
    dz_key = Divination.dizhi_key(dz1)
    s = Divination.ex_jk_gui_ren_ten_ma_str("ten_ma.#{dz_key}")
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_jk_gt_yao_ten_ma_str(dz1,dz2,liu_qin)
    dz_key = Divination.dizhi_key(dz1)
    s = Divination.ex_jk_gui_ren_ten_ma_str("yao_ten_ma.#{dz_key}")
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_jk_gt_ten_ma(nDayEarth,all_ylqgzws,shi_yao)
    nEarth = Divination.Ten_ma(nDayEarth)

    ylqgzw = Divination.par_find_ylqgzw_in_all_yao_ylqgzws_by_di_zhi(nEarth,all_ylqgzws)
    if (ylqgzw == nil) then
      return ""
    end
    yao = Divination.fu_shen_yao(ylqgzw)
    r1 = Divination.fu_shen_liu_qin(ylqgzw)
    if (yao == shi_yao) then
      return Divination.ex_jk_gt_yao_ten_ma_str(nDayEarth,nEarth,r1)
    else
      return Divination.ex_jk_gt_ten_ma_str(nDayEarth,nEarth,r1)
    end
  end


  # 7.1 參數2-空亡羊刃
  # 空亡
  def Divination.ex_jk_gt_kong_wang_yang_ren_str(key)
    return Divination.ex_jiankang_str("kong_wang_yang_ren.#{key}")
  end
  def Divination.ex_jk_ky_kong_wang_shi_str(tf)
    tf_key = Divination.bool_key(tf)
    return Divination.ex_jk_gt_kong_wang_yang_ren_str("kong_wang.#{tf_key}_kong_wang.shi_yao")
  end
  def Divination.ex_jk_ky_kong_wang_str(tf,liu_qin)
    tf_key = Divination.bool_key(tf)
    s = Divination.ex_jk_gt_kong_wang_yang_ren_str("kong_wang.#{tf_key}_kong_wang.liu_qin")
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_jk_ky_kong_wang_lq3_str(tf,yao)
    tf_key = Divination.bool_key(tf)
    s = Divination.ex_jk_gt_kong_wang_yang_ren_str("kong_wang.#{tf_key}_kong_wang.r3_post")
    return Divination.extract_str(s,yao)
  end
  def Divination.ex_jk_ky_kong_wang(nDaySky,nDayEarth,nMonthEarth,all_ylqgzws,shi_yao,par_five_levels,par_chang_sheng)
    aOut = Array.new
    tf = Divination.ex_jk_ky_kong_wang_true_false(nDayEarth,nMonthEarth,par_five_levels,par_chang_sheng)
    aEarth = Divination.Kong_Wang(nDaySky,nDayEarth)

    aEarth.each_index do |i|
      ylqgzw = Divination.par_find_ylqgzw_in_all_yao_ylqgzws_by_di_zhi(aEarth[i],all_ylqgzws)
      if (ylqgzw != nil) then
        yao = Divination.fu_shen_yao(ylqgzw)
        if (yao == shi_yao) then
          aOut.push(Divination.ex_jk_ky_kong_wang_shi_str(tf))
        else
          r1 = Divination.fu_shen_liu_qin(ylqgzw)
          s = Divination.ex_jk_ky_kong_wang_str(tf,r1)
          if (r1 == Liu_Qin_3) then # 官鬼
            s += Divination.ex_jk_ky_kong_wang_lq3_str(tf,yao)
          end
          aOut.push(s)
        end
      end
    end
    return aOut.uniq
  end
  # 10.應爻空亡
  def Divination.ex_jk_gt_yin_yao_kong_wang_str(key)
    return Divination.ex_jiankang_str("yin_yao_kong_wang.#{key}")
  end
  def Divination.ex_jk_kong_wang_yin_str(wang_xiang_xiu_qiu_si)
    key = Divination.wang_xiang_xiu_qiu_si_key(wang_xiang_xiu_qiu_si)
    return Divination.ex_jk_gt_yin_yao_kong_wang_str("#{key}")
  end
  def Divination.ex_jk_yinyao_kong_wang(nDaySky,nDayEarth,nMonthEarth,all_ylqgzws,yin_yao,yin_five,par_five_levels)
    aOut = Array.new
    aEarth = Divination.Kong_Wang(nDaySky,nDayEarth)
    wang_xiang_xiu_qiu_si = par_five_levels.index(yin_five) + 1

    aEarth.each_index do |i|
      ylqgzw = Divination.par_find_ylqgzw_in_all_yao_ylqgzws_by_di_zhi(aEarth[i],all_ylqgzws)
      if (ylqgzw != nil) then
        yao = Divination.fu_shen_yao(ylqgzw)
        if (yao == yin_yao) then
          aOut.push(Divination.ex_jk_kong_wang_yin_str(wang_xiang_xiu_qiu_si))
        end
      end
    end
    return aOut.uniq
  end
  # 11.本卦無官，看官鬼伏於第幾爻
  def Divination.ex_jk_gt_lq3_infusheng_str(key)
    return Divination.ex_jiankang_str("lq3_infusheng.#{key}")
  end
  def Divination.ex_jk_lq3_in_fusheng_str(yao)
    key = Divination.yao_key(yao)
    return Divination.ex_jk_gt_lq3_infusheng_str("#{key}")
  end
  def Divination.ex_jk_lq3_in_fusheng(all_ylqgzws)
    aOut = Array.new
    ylqgzws = Divination.par_fu_sheng_ylqgzw(all_ylqgzws)

    ylqgzws.each_index do |i|
      ylqgzw = ylqgzws[i]
      yao = Divination.fu_shen_yao(ylqgzw)
      r1 = Divination.fu_shen_liu_qin(ylqgzw)
      if (r1 == Liu_Qin_3) then # 官鬼
        aOut.push(Divination.ex_jk_lq3_in_fusheng_str(yao))
      end
    end
    return aOut.uniq
  end
  # 地支爻於月令不為死,日辰不為絕,逢空亡時均為假空亡
  def Divination.ex_jk_ky_kong_wang_true_false(nDayEarth,nMonthEarth,par_five_levels,par_chang_sheng)
    month_five = Divination.Earth_Five(nMonthEarth)
    if (par_five_levels.index(month_five) != Five_level_5) then
      return false
    end
    day_five = Divination.Earth_Five(nDayEarth)
    index = par_five_levels.index(day_five)
    if (par_chang_sheng[index] != Chang_Sheng_10) then
      return false
    end
    return true
  end
  # 羊刃
  def Divination.ex_jk_ky_yang_ren_str(r1,yao)
    s1 = Divination.ex_jk_gt_kong_wang_yang_ren_str("yang_ren.liu_qin")
    s = Divination.extract_liuqin_str(s1,r1)
    s1 = Divination.ex_jk_gt_kong_wang_yang_ren_str("yang_ren.yao")
    s += Divination.extract_str(s1,yao)
    return s
  end
  def Divination.ex_jk_ky_yang_ren(nDaySky,all_ylqgzws)
    nEarth = Divination.Yang_Ren(nDaySky)

    ylqgzw = Divination.par_find_ylqgzw_in_all_yao_ylqgzws_by_di_zhi(nEarth,all_ylqgzws)
    if (ylqgzw != nil) then
      r1 = Divination.fu_shen_liu_qin(ylqgzw)
      yao = Divination.fu_shen_yao(ylqgzw)
      return Divination.ex_jk_ky_yang_ren_str(r1,yao)
    else
      return ""
    end
  end
  # ※爻之反吟:
  def Divination.ex_jk_ky_yao_fan_yin_str()
    return Divination.ex_jk_gt_kong_wang_yang_ren_str("yao_fan_yin")
  end
  def Divination.ex_jk_ky_yao_fan_yin(all_ylqgzws)
    dong_yao,bAntiYin = Divination.Cr_find_yao_fan_yin(all_ylqgzws)
    if (bAntiYin) then
      return Divination.ex_jk_ky_yao_fan_yin_str()
    else
      return ""
    end
  end
  # ※特殊卦:
  def Divination.ex_jk_ky_te_shu_chong_str()
    return Divination.ex_jk_gt_kong_wang_yang_ren_str("te_shu_chong")
  end
  def Divination.ex_jk_ky_te_shu_no_sons_str()
    return Divination.ex_jk_gt_kong_wang_yang_ren_str("special_no_sons")
  end
  # 子孫與官鬼同時為動爻(一為明動,一為暗動皆算),建議您除正常之醫療動作外,亦可輔以傳統之民俗療法
  # ＝》​​指動爻為「子孫」時，再找本卦之「官鬼」之五行，是否與占卜日之地支相衝，若有顯示。
  def Divination.ex_jk_ky_te_shu_chong(nDayEarth,all_ylqgzws)
    # dong_yao_ylqgzws = Divination.par_find_dong_yao_ylqgzws(all_ylqgzws)
    all_ylqgzws = Divination.par_all_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_ylqgzws[i]
      bian_liu_qin = Divination.fu_shen_liu_qin(bian_ylqgzw)
      if (bian_liu_qin == Liu_Qin_5) then
        bian_yao = Divination.fu_shen_yao(bian_ylqgzw)
        ben_ylqgzw = Divination.par_find_ylqgzw_by_liu_qin(all_ylqgzws,Liu_Qin_3)

        ben_wu_xing = Divination.fu_shen_wu_xing(ben_ylqgzw)  # 本卦五行
        day_five = Divination.Earth_Five(nDayEarth)
        san_ke = Divination.Five_SanKe(ben_wu_xing,day_five)
        if ([Five_SK_Ke,Five_SK_BeKe].index(san_ke) != nil) then
          return Divination.ex_jk_ky_te_shu_chong_str()
        end
      end
    end

    return ""
  end
  def Divination.ex_fu_shen_liu_qin(fu_shens,liu_qin)
    fu_shens.each_index do |i|
      fs_liu_qin = Divination.fu_shen_liu_qin(fu_shens[i])
      if (liu_qin == fs_liu_qin) then
        return a
      end
    end
    return nil
  end

  # 子孫與官鬼同時暗伏,不上六爻內時,建議您除正常之醫療動作外,亦可輔以傳統之民俗療法
  # ＝》​ ​在本卦中均無「子孫」、「官鬼」時，顯示此資料。 ​​
  def Divination.ex_jk_ky_te_shu_no_sons(all_ylqgzws)
    ben_ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    ylqgzw = Divination.par_find_ylqgzw_by_liu_qin(ben_ylqgzws,Liu_Qin_3)
    ylqgzw2 = Divination.par_find_ylqgzw_by_liu_qin(ben_ylqgzws,Liu_Qin_5)
    if (ylqgzw == nil && ylqgzw2 == nil) then
      return Divination.ex_jk_ky_te_shu_no_sons_str()
    end
    return ""
  end

  # 8 動爻變爻之關係
  def Divination.ex_jk_move_dong_yao_str(dz1,dz2,liu_qin,r2)
    dz1_key = Divination.dizhi_key(dz1)
    dz2_key = Divination.dizhi_key(dz2)
    s = Divination.ex_jiankang_str("move_dong_yao.#{dz1_key}.#{dz2_key}")
    return Divination.extract_liuqin_str(s,liu_qin)
  end
  def Divination.ex_jk_move_dong_yao(all_ylqgzws)
    ben_ylqgzws = Divination.par_ben_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws = Divination.par_bian_gua_ylqgzws(all_ylqgzws)
    bian_ylqgzws.each_index do |i|
      bian_ylqgzw = bian_ylqgzws[i]
      yao = Divination.fu_shen_yao(bian_ylqgzw)
      ben_ylqgzw = Divination.par_find_ylqgzw_by_yao(ben_ylqgzws,yao)

      e1 = Divination.fu_shen_di_zhi(ben_ylqgzw)
      e2 = Divination.fu_shen_di_zhi(bian_ylqgzw)
      r1 = Divination.fu_shen_liu_qin(ben_ylqgzw)
      r2 = Divination.fu_shen_liu_qin(bian_ylqgzw)
      s = Divination.ex_jk_move_dong_yao_str(e1,e2,r1,r2)
      if (s.index("translation missing") != nil) then
        return ""
      end
      return s
    end
    return ""
  end

  # 9 本卦變卦之卦象說明
  def Divination.ex_jk_gua_image_str(key)
    return Divination.ex_jiankang_str("gua_image.#{key}")
  end
  def Divination.ex_jk_gi_te_shu(gua_he_chong,special_no)
    if (gua_he_chong == HeChong_1) then
      # 六合卦
      return Divination.ex_jk_gua_image_str("liu_he")
    elsif (gua_he_chong == HeChong_2) then
      # 六沖卦
      return Divination.ex_jk_gua_image_str("liu_chong")
    elsif (special_no == Te_Shu_Gua_7) then
      # 游魂卦
      return Divination.ex_jk_gua_image_str("special7")
    elsif (gua_he_chong == Te_Shu_Gua_8) then
      # 歸魂卦
      return Divination.ex_jk_gua_image_str("special8")
    else
      return ""
    end

  end
  def Divination.ex_jk_gi_fu_yin(gua_fan_yin,ingua_fu_yin,outgua_fu_yin,inoutgua_fu_yin)
    if (gua_fan_yin) then
      # 反吟卦
      return Divination.ex_jk_gua_image_str("fan_yin")
    elsif (ingua_fu_yin) then
      # 伏吟卦…內卦伏吟
      return Divination.ex_jk_gua_image_str("ingua_fu_yin")
    elsif (outgua_fu_yin) then
      # 伏吟卦…外卦伏吟
      return Divination.ex_jk_gua_image_str("outgua_fu_yin")
    elsif (inoutgua_fu_yin) then
      # 伏吟卦…內外卦伏吟
      return Divination.ex_jk_gua_image_str("inoutgua_fu_yin")
    else
      return ""
    end

  end

end
