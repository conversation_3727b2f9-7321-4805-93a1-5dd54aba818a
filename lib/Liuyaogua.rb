require("Cfate.rb")
require("Xdate.rb")
require("Pm.rb")
require("SkyEarthFive_Function.rb")

class Liuyaogua
  #參數設定
  PanPar_Copy = 0
  PanPar_Type = 1
  PanPar_Interface = 2
  PanPar_Display = 3
  PanPar_HallNumber = 4
  PanPar_Print = 5

  # 起卦方式
  PAR_QIGUAFANXI_NO  = 0  # 數字起卦
  PAR_QIGUAFANXI_YAO   = 1 # 卦爻起卦

  # 爻的表示方式
  PAR_YAODISPLAYTYPE_NORMAL  = 0  # 標準爻
  PAR_YAODISPLAYTYPE_SEP   = 1 # 陰爻分左右

  PAR_QIGUAFANXI_DEFAULT = Liuyaogua::PAR_QIGUAFANXI_NO       # 起卦方式
  PAR_YAODISPLAYTYPE_DEFAULT = Liuyaogua::PAR_YAODISPLAYTYPE_SEP       # 起卦方式

  PAR_QIGUAFANXI  = "n_par_QiGuaFanXi"  # 起卦方式
  PAR_YAODISPLAYTYPE  = "n_par_YaoDisplayType"  # 陰爻分左右

    # 客戶設定的排盤參數
  def Liuyaogua.get_pan_pars(userId,bCanUseIfatePars=false)
    if (bCanUseIfatePars) then
      panPar = IfatePar.check_userid_liuyaogua(userId).first
      if (panPar == nil) then
        hUserType = Liuyaogua.pan_par_assign(nil,userId)
      else
        hUserType = Liuyaogua.pan_par_assign(panPar.hPars,userId)
      end
    else
      hUserType = Liuyaogua.pan_par_assign(nil,userId)
    end
    return hUserType
  end
  def Liuyaogua.pan_par_assign(hInput,userId)
    hUserType = Hash.new
    if (hInput == nil) then
      hUserInput = nil #Hash.new
    else
      hUserInput = hInput
    end

    (Liuyaogua::PanPar_Type..Liuyaogua::PanPar_Print).each do |nPanPar|
      hUserType = Liuyaogua.pan_par_assign_each(nPanPar,hUserType,hUserInput)
    end

    return hUserType
  end
  @@hUserParDefault = { 
      Cfate::PAR_PAN_YEAR_DISPLAY => Cfate::PAN_DISPLAY_WEST,
      Liuyaogua::PAR_QIGUAFANXI => Liuyaogua::PAR_QIGUAFANXI_DEFAULT,  # 起卦方式
      Liuyaogua::PAR_YAODISPLAYTYPE => Liuyaogua::PAR_YAODISPLAYTYPE_DEFAULT  # 爻的表示方式
  }

  @@hUserIntDefault = {
  }
  @@hUserDisDefault = {  
  }
  HN_HALL_NUMBER1 = "s_hn_hall_number1"
  @@hUserHnDefault = {   #
                 Liuyaogua::HN_HALL_NUMBER1 => Pm.t("liuyaogua_pan.hallnumber_default") # 堂號 
  }
  PAR_PRINT_TIME_PAGE = "b_par_print_time_page"
  PAR_PRINT_PAN_HEADER = "s_par_print_pan_header"  # 排盤表頭
  PAR_PRINT_SET_FOOTER = "s_par_print_set_footer"  # 時間頁頁尾說明
    @@hUserPrnDefault = {   #
                 Liuyaogua::PAR_PRINT_TIME_PAGE => false, #
                 Liuyaogua::PAR_PRINT_PAN_HEADER => Pm.GetStr("liuyaogua_pan.app_title"), #
                 Liuyaogua::PAR_PRINT_SET_FOOTER => Pm.GetStr("liuyaogua_pan.app_footer") #
  }
  def Liuyaogua.pan_par_assign_each(nPanPar,hParAll,hUserInput)
    if (nPanPar == Liuyaogua::PanPar_Type) then
      hOutput = Liuyaogua.pan_par_assign_parameter(hUserInput,hParAll,@@hUserParDefault)
    elsif (nPanPar == Liuyaogua::PanPar_Interface) then
      hOutput = Liuyaogua.pan_par_assign_parameter(hUserInput,hParAll,@@hUserIntDefault)
    elsif (nPanPar == Liuyaogua::PanPar_Display) then
      hOutput = Liuyaogua.pan_par_assign_parameter(hUserInput,hParAll,@@hUserDisDefault)
    elsif (nPanPar == Liuyaogua::PanPar_HallNumber) then
      hOutput = Liuyaogua.pan_par_assign_hallnumber(hUserInput,hParAll)
    elsif (nPanPar == Liuyaogua::PanPar_Print) then
      hOutput = Liuyaogua.pan_par_assign_print(hUserInput,hParAll)
    else
      hOutput = hParAll.clone
    end
    return hOutput
  end
  # 參數設定
  def Liuyaogua.pan_par_assign_parameter(hUserInput,hParAll,hDefault)
    hOutput = hParAll.clone
    if (hUserInput == nil) then
      hUserInput = Hash.new
    end

    hDefault.each {|key,value|
      hOutput[key] = Cfate.ParValueCheck(key,hUserInput[key],value)
    }

    return hOutput
  end
  def Liuyaogua.pan_par_assign_hallnumber(hUserInput,hParAll)
    # @@hUserHnDefault[Liuyaogua::HN_HALL_NUMBER1] = Pm.GetStr("liuyaogua_pan.app_owner") # 堂號
    hOutput = Liuyaogua.pan_par_assign_parameter(hUserInput,hParAll,@@hUserHnDefault)
    return hOutput
  end
  def Liuyaogua.pan_par_assign_print(hUserInput,hParAll)
    @@hUserPrnDefault[Liuyaogua::PAR_PRINT_PAN_HEADER] = Pm.GetStr("liuyaogua_pan.app_title")
    @@hUserPrnDefault[Liuyaogua::PAR_PRINT_SET_FOOTER] = Pm.GetStr("liuyaogua_pan.app_footer")
    hOutput = Liuyaogua.pan_par_assign_parameter(hUserInput,hParAll,@@hUserPrnDefault)
    return hOutput
  end

  def Liuyaogua.pan_par_init_each(nPanPar,hParAll)
    hUserType = Liuyaogua.pan_par_assign_each(nPanPar,hParAll,nil)

    return hUserType
  end
  def Liuyaogua.product_func_par_assign(hdbInput,hDefault)
    if (hdbInput == nil) then
      hdbInput = Hash.new
    end
    bhasDefault = true
    if (hDefault == nil) then
      bhasDefault = false
      hDefault = Hash.new
    end
    hApFunc = Hash.new
    hDemo = Liuyaogua.getDemoApFuncHash()
    if (hDemo == nil) then
      if (bhasDefault) then
        hDefault.each {|key,value|
          hApFunc[key] = Cfate.ParValueCheck(key,hdbInput[key],hDefault[key])
        }
      else
        hdbInput.each {|key,value|
          hApFunc[key] = Cfate.ParValueCheck(key,hdbInput[key],hDefault[key])
        }
      end
    else
      hDemo.each {|key,value|
        hApFunc[key] = Cfate.ParValueCheck(key,hdbInput[key],hDefault[key])
      }
    end
    return hApFunc
  end
  def Liuyaogua.func_assign(hInput)
    hOutput = Hash.new
    hInput.each {|key,value|
      hOutput[key] = Cfate.ParValueCheck(key,hInput[key],hInput[key])
    }
    return hOutput
  end

  def Liuyaogua.getDemoApFuncHash()
    oProduct = Product.check_demo.check_name("liuyaogua").last
    if (oProduct == nil) then
      return nil
    end
    return Cfate.pan_par_dbfield2hash(oProduct.func_par)
  end

  def prn_GetPrintHeader(hParAll={})
    return hParAll[Liuyaogua::PAR_PRINT_PAN_HEADER]
  end
  def prn_GetPrintFooter(hParAll={})
    return hParAll[Liuyaogua::PAR_PRINT_SET_FOOTER]
  end
end
