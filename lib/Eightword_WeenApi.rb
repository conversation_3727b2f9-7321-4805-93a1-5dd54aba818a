class Eightword
  def wa_Pan(nPanType,hUserInfo,hUserDefDate,hUserType)
  	g_GetPanInfo(nPanType,hUserInfo,hUserDefDate,hUserType)

    hOut = Hash.new

  	# 主要盤
    hOut["zhu_yao_pan"] = wap_zhu_yao_pan()

    # 大運
    hOut["da_yun"] = wap_da_yun(nPanType)

    # 客戶資訊
    hOut["ke_hu_zi_xun"] = wap_ke_hu_zi_xun()

    # 命宮命格
    hOut["ming_gong_zi_xun"] = wap_ming_gong_zi_xun(nPanType)

    # 喜用神
    hOut["shi_yong_shen"] = wap_shi_yong_shen()

    # 旺相休囚死
    hOut["wang_xiang_xiu_qiu_si"] = wap_wang_xiang_xiu_qiu_si()

    # 疾病檢核表
    hOut["ji_bing_jian_he_biao"] = wap_ji_bing_jian_he_biao()

    # 十神數值
    hOut["shi_shen_shu_zhi"] = wap_shi_shen_shu_zhi()

    # 五行數值
    hOut["wu_xing_shu_zhi"] = wap_wu_xing_shu_zhi()

    return hOut
  end
  def wap_zhu_yao_pan()
  	d = ["nian","yue","ri","shi"]
    h = Hash.new
	(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      h2 = Hash.new

      # 主星 及 其 旺相休囚死
      h2["zhu_xing"] = cp_mp_4c_getByMainStar_Name(byWhatIdx)
      h2["wang_xiang_xiu_qiu_si"] = cp_mp_4c_get4CFiveLevel_Name(byWhatIdx)

      # 天干 及其五行
      h2["tian_gan"] = cp_mp_4c_getSky_Name(byWhatIdx)
	  h2["tian_gan_wu_xing"] = cp_mp_4c_getSkyFive_Name(byWhatIdx)

      # 地支 及其五行
      h2["di_zhi"] = cp_mp_4c_getEarth_Name(byWhatIdx)
	  h2["di_zhi_wu_xing"] = cp_mp_4c_getEarthFive_Name(byWhatIdx)

      # 藏干
      a = Array.new
	  (0..2).each do |i|
        h3 = Hash.new
        h3["tian_gan"] = cp_mp_4c_getChungSky_Name(byWhatIdx,i)
        h3["tian_gan_wu_xing"] = cp_mp_4c_getChungSkyFive_Name(byWhatIdx,i)
        a.push(h3)
	  end
	  h2["cang_gan"] = a
      
      # 副星
      a = Array.new
	  (0..2).each do |i|
		a.push(cp_mp_4c_getBySubStar_Name(byWhatIdx,i))
	  end
	  h2["fu_xing"] = a

      # 12運
	  h2["shi_er_yun"] = cp_mp_4c_getBy12Win_Name(byWhatIdx)

      # 神煞
	  aGodKill = cp_mp_4c_getGodKill_NameArray(byWhatIdx)
	  h2["shen_sha"] = aGodKill

      # 納音
	  h2["na_yin"] = cp_mp_4c_getNaIn_Name(byWhatIdx)

      h[d[byWhatIdx]] = h2
    end
    return h
  end
  def wap_da_yun(nPanType)
    h = Hash.new
	(0...da_yun_pfwbw_col_no(nPanType)).each do |nCol|
      h2 = Hash.new
	  h2["ri_qi_1"] = cp_fp_4c_getByDateValue(nCol,nPanType)
	  h2["zhu_xing"] = cp_fp_4c_getByMainStar_Name(nCol,nPanType)
	  h2["tian_gan"] = cp_fp_4c_getSky_Name(nCol,nPanType)
	  h2["di_zhi"] = cp_fp_4c_getEarth_Name(nCol,nPanType)
	  h2["ri_qi_2"] = cp_fp_4c_getByDateValue2(nCol,nPanType)
      # 藏干
      a = Array.new
	  (0..2).each do |i|
        h3 = Hash.new
        h3["tian_gan"] = cp_fp_4c_getChungSky_Name(nCol,nPanType,i)
        a.push(h3)
	  end
	  h2["cang_gan"] = a

      # 副星
      a = Array.new
	  (0..2).each do |i|
		a.push(cp_fp_4c_getBySubStar_Name(nCol,nPanType,i))
	  end
	  h2["fu_xing"] = a

      # 12運
	  h2["shi_er_yun"] = cp_fp_4c_getBy12Win_Name(nCol,nPanType)

      # 納音
	  h2["na_yin"] = cp_fp_4c_getNaIn_Name(nCol,nPanType)

      h["#{nCol + 1}"] = h2
	end
	return h
  end
  def da_yun_pfwbw_col_no(nPanType)
	  if (nPanType == Cfate::PAN_FLOWDATE) then
		   return cp_fp_getMaxCol_FlowDate()
	  else
		   return 12
	  end
  end
  # 客戶資訊
  def wap_ke_hu_zi_xun()
    h = Hash.new

    h["xing_ming"] = uig_Name()
    h["ming_zao"] = cp_mp_ui_IYMF()
    h["sheng_xiao"] = cp_mp_ui_Animal()
    h["sui_shu"] = cp_mp_ui_YearOld()
    h["chu_sheng_yang_li"] = cp_mp_ui_GetSolor_BirthDate3()
    h["chu_sheng_yin_li"] = uig_E_BirthDate_Array3()
    h["jiao_da_yun"] = cp_mp_ui_GetMatchYear2()
    h["chu_sheng_hou_jiao_da_yun"] = cp_mp_ui_GetBirthAfter2()

    return h
  end
  def cp_mp_ui_GetSolor_BirthDate3()
    a = cp_mp_ui_GetSolor_BirthDate()
    return a.join
  end
  def uig_E_BirthDate_Array3()
    a = uig_E_BirthDate_Array2()
    a2 = []
    a2.push(a[0,a.length - 2].join)
    a2.push(a[a.length - 2])
    a2.push(a[a.length - 1])

    return a2
  end

  # 命宮資訊
  def wap_ming_gong_zi_xun(nPanType)
    h = Hash.new

    h["ming_gong"] = cp_mp_ui_GetLifeHouse2()
    h["ming_ge"] = cp_mp_ui_GetLifeStyle2()
    h["tai_yuan"] = cp_mp_ui_GetTaiUan2()
    h["tai_xi"] = cp_mp_ui_GetTaiHsi2()
    h["nian_kong"] = cp_mp_ui_GetYearEmpty2()
    h["ri_kong"] = cp_mp_ui_GetDayEmpty2()
    # 人元用事
    h["ren_yuan_yong_shi"] = cp_mp_ui_UsePeople2()
    # 日主旺度
    h["ri_zhu_wang_du"] = cp_fi_ui_GetNAvsNB2(nPanType)
    # 陰陽氣合
    h["yin_yang_qi_he"] = cp_mp_ui_NInYangValue2()
    # 中用神
    h["zhong_yong_shen"] = cp_fi_ui_GetMidGod2()
    # 格局用神
    h["ge_ju_yong_shen"] = cp_fi_ui_GetSpecialGod2()

    return h
  end
  def wap_shi_yong_shen()
    h = Hash.new

    h["yong_shen"] = cp_fi_ui_GetByUseGodOrg2(0)
    h["shi_shen"] = cp_fi_ui_GetByUseGodOrg2(1)
    h["xian_shen"] = cp_fi_ui_GetByUseGodOrg2(2)
    h["ji_shen"] = cp_fi_ui_GetByUseGodOrg2(3)
    h["chou_shen"] = cp_fi_ui_GetByUseGodOrg2(4)

    return h
  end

  def wap_wang_xiang_xiu_qiu_si()
    h = Hash.new

    h["wang"] = cp_mp_ui_GetFiveLevel2(0)
    h["xiang"] = cp_mp_ui_GetFiveLevel2(1)
    h["xiu"] = cp_mp_ui_GetFiveLevel2(2)
    h["qiu"] = cp_mp_ui_GetFiveLevel2(3)
    h["si"] = cp_mp_ui_GetFiveLevel2(4)

    return h
  end

  # 疾病檢核表
  def wap_ji_bing_jian_he_biao()
    a = Array.new

    (0..9).each do |i|    
      a[i] = cp_fi_ui_GetnSick2(i)
    end
    return a
  end
  # 十神數值
  def wap_shi_shen_shu_zhi()
    a = Array.new

    (0..9).each do |i|    
      a[i] = cp_fi_ui_GetnGodValue2(i)
    end
    return a
  end    

  # 五行數值
  def wap_wu_xing_shu_zhi()
    a = Array.new

    (0..4).each do |i|    
      a[i] = cp_fi_ui_GetnFiveValue2(i)
    end
    return a
  end    

end