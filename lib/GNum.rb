require("GNum_mean.rb")
require("GNum_turn.rb")
require("GNum_way.rb")
require("GNum_word.rb")
require("GNum_JiXiong.rb")
require("GNum_S2MT.rb")
require("GNum_S2T.rb")
require("GNum_word_simple.rb")

class GNum
  def t(strToFind,hData=nil)
	  return I18n.translate("#{strToFind}",hData)
  end

  def GNum.init()
    hMean = Hash.new
    # Peter測試用
    # hMean["turn"] = turn
    # hMean["meanno"] = aMean[GNum::Mean_NO]
    hMean["mean"] = ''
    hMean["meaning"] = ''
    hMean["key"] = ''
    hMean["hard"] = ''
    hMean["easy"] = 0
    hMean["success"] = 0
    hMean["fail"] = 0
    hMean["level"] = 0
    hMean["luck"] = ''
    hMean["forecast"] = ''
    hMean["option1"] = ''

    hMean["meanthing"] = ''
    hMean["levelId"] = ''
    hMean["luckId"] = ''

    hMean["landway"] = ''

    return hMean
  end

  def GNum.create(word,land_from_client=nil,minute=nil)
  	sky = GNum.find_sky(minute)
  	land = GNum.find_land(land_from_client)
  	people = GNum.find_people(word)

  	count = GNum.getTurnCount(sky,people,land)

  	hMean = GNum.result(count)
  	hMean["landway"] = GNum.find_Way(land)
  	# Peter測試用
  	# hMean["sky"] = sky
  	# hMean["land"] = land
  	# hMean["landcount"] = GNum.landcount(land)
  	# hMean["people"] = people
  	# hMean["count"] = count

  	return hMean
  end
  def GNum.create_simple(word,land_from_client=nil,minute=nil)
    sky = GNum.find_sky(minute)
    land = GNum.find_land(land_from_client)
    people,words = GNum.find_people_simple(word)

    aMean = []

    people.each_with_index do |p,i|
      h = GNum.result2(sky,p,land,words[i])
      aMean.push(h)
    end

    return aMean
  end
  def GNum.result2(sky,people,land,word)
    count = GNum.getTurnCount(sky,people,land)

    hMean = GNum.result(count)
    hMean["landway"] = GNum.find_Way(land)
    hMean["people"] = people
    hMean["word"] = word

    hMean.delete("levelId")
    hMean.delete("luckId")
    hMean.delete("JiXiong")
    hMean.delete("landway")
    hMean.delete("people")
    
    return hMean
  end


  def GNum.result(count)
  	turn = GNum.find_Turn(count)
  	aMean = GNum.find_Mean(turn)

  	hMean = Hash.new
  	# Peter測試用
    # hMean["turn"] = turn
    # hMean["meanno"] = aMean[GNum::Mean_NO]
    hMean["mean"] = aMean[GNum::Mean_MEAN]
    hMean["meaning"] = aMean[GNum::Mean_MEANING]
    hMean["key"] = aMean[GNum::Mean_KEY]
    hMean["hard"] = aMean[GNum::Mean_HARD].to_i
    hMean["easy"] = (100 - hMean["hard"])
    hMean["success"] = aMean[GNum::Mean_SUCCESS].to_i
    hMean["fail"] = (100 - hMean["success"])
    hMean["level"] = aMean[GNum::Mean_LEVEL]
    hMean["luck"] = aMean[GNum::Mean_LUCK]
    hMean["forecast"] = aMean[GNum::Mean_FORECAST]
    hMean["option1"] = aMean[GNum::Mean_OPTION]

    # hMean["meanthing"] = GNum.getMeanThing(hMean["hard"],hMean["success"])
    hMean["meanthing"] = GNum.getMeanThing(hMean["easy"],hMean["success"])
    hMean["levelId"] = GNum.getLevelId(hMean["level"])
    hMean["luckId"] = GNum.getLuckId(hMean["luck"])

    hMean["JiXiong"] = GNum.find_JiXiong(turn)

    return hMean
  end

  def GNum.test(land_from_client=nil,minute=nil)
  	GNum.show(I18n.t("gnum.level.l_7"),land_from_client,minute)
  end

end
