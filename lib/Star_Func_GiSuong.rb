require("SkyEarthFive_Function.rb")

class Star
  def gGiSuong_getFlowSkyIndex(nPanType)
    return cp_getFlowSkyIndex_PanType(nPanType)
  end

  def gGiSuong_FindFourHuaHouse(nPanType,nSky)
    aFourHua = Array.new
    (0..3).each do |nFourHua|
      nStar = gGiSuong_GetFourHuaStarValue(nSky,nFourHua)
      nEarth = gHouse_GetAStar_HouseEarth(nPanType,nStar)
      aFourHua.push(nEarth)
    end
    return aFourHua
  end

  def gGiSuong_GetFourHuaStarValue(nSky,nFourHua)
    nSkyIndex = Sky.Sky2SkyIndex(nSky)
    nStar = getFourHuaStarValue(nSkyIndex,nFourHua)
    return nStar
  end

  def gGiSuong_getLargeSanStartYearOld(nLargeSan)
    nFlowAge = cp_getHouseIndexLarge(nLargeSan)
    return nFlowAge
  end

  def gGiSuong_getLargeSanStartYear(nLargeSan)
    nFlowAge = gGiSuong_getLargeSanStartYearOld(nLargeSan)
    return cp_YearOld2FlowYear(nFlowAge)
  end

  def gGiSuong_GetHouseEarth(nPanType,nHouse)
    g_House_GetEarth(nPanType,nHouse)
  end

  def gGiSuong_GetSmallSan(nEarth)
    nHouseIndex = Star.Earth2HouseIndex(nEarth)
    return cp_GetHouseSmallSan(nHouseIndex)
  end

  def gGiSuong_CheckClockWise()
    return cp_CheckDirectionClock()
  end

  def gGiSuong_GetGiSuong_Rule1_OrgSky(nPanType)
    nSky = -1
    if (nPanType == Cfate::PAN_TENYEAR) then
      nSky = gNormal_GetSky()
    elsif ((nPanType == Cfate::PAN_FLOWYEAR)) then
      nSky = Sky.SkyIndex2Sky(getFlowYearSkyIndex(nPanType))
    elsif (nPanType == Cfate::PAN_FLOWMONTH) then
      nSky,nEarth = Xdate.GetLunarMonthGanZhiSkyEarth(fdg_EY(nPanType),fdg_EM(nPanType))
    elsif (nPanType == Cfate::PAN_FLOWDATE) then
      nSky,nEarth = Xdate.GetLunarDateGanZhiSkyEarth(fdg_EY(nPanType),fdg_EM(nPanType),fdg_ED(nPanType),fdg_ET(nPanType),fdg_EL(nPanType))
    elsif (nPanType == Cfate::PAN_FLOWTIME) then
      nSky,nEarth = Xdate.GetLunarTimeGanZhiSkyEarth(fdg_EY(nPanType),fdg_EM(nPanType),fdg_ED(nPanType),fdg_ET(nPanType),fdg_EL(nPanType))
    end
    return nSky
  end

end
