<%% content_for :sitemap do %>
  <span class="current"><%%= t("activerecord.models.<%= file_name %>") %></span>
<%% end %>

<%%= render 'base' %>
<h1><%%= t("activerecord.models.<%= file_name %>") %></h1>

<div class="toolbar">
  <a href="<%%= new_<%= singular_table_name %>_path %>" class="btn btn-sm btn-success">新建</a>
</div>

<div id="<%= plural_table_name %>">
  <table class="table table-bordered table-striped table-condensed">
    <tr class="head">
      <td>編號</td>
<% attributes.each do |attr| -%>
      <td><%%= t("activerecord.attributes.<%= file_name %>.<%= attr.name %>") %></td>
<% end -%>
      <td>創建時間</td>
      <td class="opts"></td>
    </tr>
    <%% @<%= plural_file_name %>.each do |item| %>
    <tr class="<%%= cycle("","even") %>">
      <td><%%= item.id %></td>
<% attributes.each do |attr| -%>
      <td><%%= item.<%= attr.name %> %></td>
<% end -%>
      <td><%%= l(item.created_at, format: :long) %></td>
      <td>
        <%%= link_to "", edit_<%= singular_table_name %>_path(item.id), class: "fa fa-pencil" %>
        <%%= link_to "", <%= singular_table_name %>_path(item.id), method: :delete, 'data-confirm' => '確定要刪除嗎?', class: "fa fa-trash" %>
      </td>
    </tr>
    <%% end %>
  </table>
</div>
