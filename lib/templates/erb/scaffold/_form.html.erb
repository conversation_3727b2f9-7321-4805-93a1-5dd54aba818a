<%
  form_model_name = "@#{file_name}"
  if controller_class_name.include?('::')
    namespace = controller_class_name.split('::').first.downcase
    form_model_name = "[:#{namespace},@#{file_name}]"
  end
%>
<%%= simple_form_for(<%= form_model_name %>) do |f| %>
  <%%= render "/shared/error_messages", target: @<%= file_name %>  %>
  <% for attribute in attributes -%>
  <%%= f.<%= attribute.reference? ? :association : :input %> :<%= attribute.name %> %>
  <% end -%>
  <div class="form-group">
    <div class="col-md-10 col-sm-offset-2">
      <%%= f.submit t("common.save"), class: "btn btn-primary" %> or <%%= link_to t("common.cancel"), <%= index_helper %>_path %>
    </div>
  </div>
<%% end %>