require("Pm.rb")

class Ziwei
  # 命宮  minggong
  # 兄弟  xiongdi
  # 夫妻  fugi
  # 子女  zinu
  # 財帛  caibo
  # 疾厄  jie
  # 遷移  qianyi
  # 朋友  pengyou
  # 官祿  guanlu
  # 田宅  tianzhai
  # 福德  fude
  # 父母  fumu
  def <PERSON><PERSON><PERSON>.gong_nick(house_id)
    a = ["minggong","xiongdi","fugi","zinu","caibo","jie","qianyi","pengyou","guanlu","tianzhai","fude","fumu"]
    return a[house_id - 1]
  end

  # 本命  benming
  # 十年  shinian
  # 流年  liunian
  # 流月  liuyue
  # 流日  liuri
  def Ziwei.pantype_nick(panType)
    a = ["benming","shinian","liunian","liuyue","liuri","liushi"]
    return a[panType]
  end

  # 祿 lu
  # 權 quan
  # 科 ke
  # 忌 ji
  def Ziwei.sihua_nick(sihua)
    a = ["lu","quan","ke","ji"]
    return a[sihua]
  end

  # 甲級星 A
  # 紫微  astar1
  # 天機  astar2
  # 太陽  astar3
  # 武曲  astar4
  # 天同  astar5
  # 廉貞  astar6
  # 天府  astar7
  # 太陰  astar8
  # 貪狼  astar9
  # 巨門  astar10
  # 天相  astar11
  # 天梁  astar12
  # 七殺  astar13
  # 破軍  astar14
  # 文昌  astar15
  # 文曲  astar16
  # 左輔  astar17
  # 右弼  astar18
  # 祿存  astar19
  # 擎羊  astar20
  # 陀羅  astar21
  # 火星  astar22
  # 鈴星  astar23
  # 天魁  astar24
  # 天鉞  astar25
  def Ziwei.danxing_a_nick(n)
    a = ["astar1","astar2","astar3","astar4","astar5",
         "astar6","astar7","astar8","astar9","astar10",
         "astar11","astar12","astar13","astar14","astar15",
         "astar16","astar17","astar18","astar19","astar20",
         "astar21","astar22","astar23","astar24","astar25"
        ]
    return a[n - 1]
  end
  def Ziwei.danxing_a_nicks(stars)
    aOut = Array.new
    stars.each do |n|
      aOut.push(Ziwei.danxing_a_nick(n))
    end
    return aOut
  end

  # 乙級星 B
  # 地劫 bstar1
  # 天空 bstar2
  # 天刑 bstar3
  # 天姚 bstar4
  # 天馬 bstar5
  # 解神 bstar6
  # 陰煞 bstar7
  # 天喜 bstar8
  # 天官 bstar9
  # 天福 bstar10
  # 天哭 bstar11
  # 天虛 bstar12
  # 龍池 bstar13
  # 鳳閣 bstar14
  # 紅鸞 bstar15
  # 孤辰 bstar16
  # 寡宿 bstar17
  # 蜚廉 bstar18
  # 破碎 bstar19
  # 台輔 bstar20
  # 封誥 bstar21
  # 天巫 bstar22
  # 天月 bstar23
  # 三台 bstar24
  # 八座 bstar25
  # 恩光 bstar26
  # 天貴 bstar27
  # 天才 bstar28
  # 天壽 bstar29
  # 截空 bstar30
  # 旬中 bstar31
  # 旬空 bstar32
  # 天傷 bstar33
  # 天使 bstar34
  # 天廚 bstar35
  def Ziwei.danxing_b_nick(n)
    a = ["bstar1","bstar2","bstar3","bstar4","bstar5",
         "bstar6","bstar7","bstar8","bstar9","bstar10",
         "bstar11","bstar12","bstar13","bstar14","bstar15",
         "bstar16","bstar17","bstar18","bstar19","bstar20",
         "bstar21","bstar22","bstar23","bstar24","bstar25",
         "bstar26","bstar27","bstar28","bstar29","bstar30",
         "bstar31","bstar32","bstar33","bstar34","bstar35"]
    return a[n - 1]
  end
  def Ziwei.danxing_b_nicks(stars)
    aOut = Array.new
    stars.each do |n|
      aOut.push(Ziwei.danxing_b_nick(n))
    end
    return aOut
  end

  #博士星 D
  # 博士 dstar1
  # 力士 dstar2
  # 青龍 dstar3
  # 小耗 dstar4
  # 將軍 dstar5
  # 奏書 dstar6
  # 飛廉 dstar7
  # 喜神 dstar8
  # 病符 dstar9
  # 大耗 dstar10
  # 伏兵 dstar11
  # 官府 dstar12
  def Ziwei.danxing_d_nick(n)
    a = ["dstar1","dstar2","dstar3","dstar4","dstar5","dstar6",
         "dstar7","dstar8","dstar9","dstar10","dstar11","dstar12"]
    return a[n - 1]
  end

  #歲建星 Y
  # 歲建 ystar1
  # 晦氣 ystar2
  # 喪門 ystar3
  # 貫索 ystar4
  # 官符 ystar5
  # 小耗 ystar6
  # 大耗 ystar7
  # 龍德 ystar8
  # 白虎 ystar9
  # 天德 ystar10
  # 弔客 ystar11
  # 病符 ystar12
  def Ziwei.danxing_y_nick(n)
    a = ["ystar1","ystar2","ystar3","ystar4","ystar5","ystar6",
         "ystar7","ystar8","ystar9","ystar10","ystar11","ystar12"]
    return a[n - 1]
  end

  #將星 S
  # 將星 sstar1
  # 攀鞍 sstar2
  # 歲驛 sstar3
  # 息神 sstar4
  # 華蓋 sstar5
  # 劫煞 sstar6
  # 災煞 sstar7
  # 天煞 sstar8
  # 指背 sstar9
  # 咸池 sstar10
  # 月煞 sstar11
  # 亡神 sstar12
  def Ziwei.danxing_s_nick(n)
    a = ["sstar1","sstar2","sstar3","sstar4","sstar5","sstar6",
         "sstar7","sstar8","sstar9","sstar10","sstar11","sstar12"]
    return a[n - 1]
  end

  #十二長生 G
  # 長生 gstar1
  # 沐浴 gstar2
  # 冠帶 gstar3
  # 臨官 gstar4
  # 帝旺 gstar5
  # 衰 gstar6
  # 病 gstar7
  # 死 gstar8
  # 墓 gstar9
  # 絕 gstar10
  # 胎 gstar11
  # 養 gstar12
  def Ziwei.danxing_g_nick(n)
    a = ["gstar1","gstar2","gstar3","gstar4","gstar5","gstar6","gstar7","gstar8","gstar9","gstar10","gstar11","gstar12"]
    return a[n - 1]
  end

  # ziwei string
  def Ziwei.nick_name(key)
    return Pm.GetStr("ziwei.name.#{key}")
  end
  # ziwei xing string
  def Ziwei.nick_name_danxing(key)
    return Ziwei.nick_name("danxing.#{key}")
  end
  def Ziwei.nick_name_danxing_a(key)
    return Ziwei.nick_name_danxing("a.#{key}")
  end
  def Ziwei.id_str_danxing_a(star)
    key = Ziwei.danxing_a_nick(star)
    return Ziwei.nick_name_danxing_a(key)
  end
  def Ziwei.nick_name_danxing_b(key)
    return Ziwei.nick_name_danxing("b.#{key}")
  end
  def Ziwei.id_str_danxing_b(star)
    key = Ziwei.danxing_b_nick(star)
    return Ziwei.nick_name_danxing_b(key)
  end
  def Ziwei.nick_name_danxing_d(key)
    return Ziwei.nick_name_danxing("d.#{key}")
  end
  def Ziwei.id_str_danxing_d(star)
    key = Ziwei.danxing_d_nick(star)
    return Ziwei.nick_name_danxing_d(key)
  end
  def Ziwei.nick_name_danxing_y(key)
    return Ziwei.nick_name_danxing("y.#{key}")
  end
  def Ziwei.id_str_danxing_y(star)
    key = Ziwei.danxing_y_nick(star)
    return Ziwei.nick_name_danxing_y(key)
  end
  def Ziwei.nick_name_danxing_s(key)
    return Ziwei.nick_name_danxing("s.#{key}")
  end
  def Ziwei.id_str_danxing_s(star)
    key = Ziwei.danxing_s_nick(star)
    return Ziwei.nick_name_danxing_s(key)
  end
  def Ziwei.nick_name_danxing_g(key)
    return Ziwei.nick_name_danxing("g.#{key}")
  end
  def Ziwei.id_str_danxing_g(star)
    key = Ziwei.danxing_g_nick(star)
    return Ziwei.nick_name_danxing_g(key)
  end

  # wuxing string
  def Ziwei.nick_name_wuxing(key)
    return Ziwei.nick_name("wuxing.#{key}")
  end

  # gong string
  def Ziwei.id_name_gong(house_id)
    nick = Ziwei.gong_nick(house_id)
    return Ziwei.nick_name_gong(nick)
  end
  def Ziwei.nick_name_gong(key)
    return Ziwei.nick_name("gong.#{key}")
  end

  # pan string
  def Ziwei.nick_name_pan(key)
    return Ziwei.nick_name("pan.#{key}")
  end

  # sihua string
  def Ziwei.nick_name_sihua(key)
    return Ziwei.nick_name("sihua.#{key}")
  end

  # 雙星
  # 紫微天府  astar1_astar7
  # 紫微貪狼  astar1_astar9
  # 紫微天相  astar1_astar11
  # 紫微七殺  astar1_astar13
  # 紫微破軍  astar1_astar14
  # 天機太陰  astar2_astar8
  # 天機巨門  astar2_astar10
  # 天機天梁  astar2_astar12
  # 太陽太陰  astar3_astar8
  # 太陽巨門  astar3_astar10
  # 太陽天梁  astar3_astar12
  # 武曲天府    astar4_astar7
  # 武曲貪狼    astar4_astar9
  # 武曲天相    astar4_astar11
  # 武曲七殺    astar4_astar13
  # 武曲破軍    astar4_astar14
  # 天同太陰    astar5_astar8
  # 天同巨門    astar5_astar10
  # 天同天梁    astar5_astar12
  # 廉貞天府    astar6_astar7
  # 廉貞貪狼    astar6_astar9
  # 廉貞天相    astar6_astar11
  # 廉貞七殺    astar6_astar13
  # 廉貞破軍    astar6_astar14
  # 文昌文曲    astar15_astar16
  # 左輔右弼    astar17_astar18
  # 天空地劫    bstar2_bstar1
  # 龍池鳳閣    bstar13_bstar14
  # 三台八座    bstar24_bstar25
  # 大耗大耗    dstar10_ystar7
  # 大耗小耗    dstar10_ystar6
  # 大耗小耗    ystar7_dstar4
  # 小耗小耗    dstar4_ystar6
  # 貪狼火星    astar9_astar22
  # 貪狼鈴星    astar9_astar23
  # 貪狼擎羊    astar9_astar20
  # 廉貞擎羊    astar6_astar20
  # 太陰文曲    astar8_astar16
  # 祿存天馬    astar19_bstar5
  # 病符病符    dstar9_ystar12
  def Ziwei.shuangxing_check(star_nicks,panType,house_id,house_wu_xing)
    shuangxings = [["astar1","astar7"],["astar1","astar9"],["astar1","astar11"],["astar1","astar13"],
         ["astar1","astar14"],["astar2","astar8"],["astar2","astar10"],["astar2","astar12"],
         ["astar3","astar8"],["astar3","astar10"],["astar3","astar12"],["astar4","astar7"],
         ["astar4","astar9"],["astar4","astar11"],["astar4","astar13"],["astar4","astar14"],
         ["astar5","astar8"],["astar5","astar10"],["astar5","astar12"],["astar6","astar7"],
         ["astar6","astar9"],["astar6","astar11"],["astar6","astar13"],["astar6","astar14"],
         ["astar15","astar16"],["astar17","astar18"],["bstar2","bstar1"],["bstar13","bstar14"],
         ["bstar24","bstar25"],["dstar10","ystar7"],["dstar10","ystar6"],["ystar7","dstar4"],
         ["dstar4","ystar6"],["astar9","astar22"],["astar9","astar23"],["astar9","astar20"],
         ["astar6","astar20"],["astar8","astar16"],["astar19","bstar5"],["dstar9","ystar12"]
        ]
    shuangxingOut = Array.new
    nicks = star_nicks.clone
    shuangxings.each do |a|
      b = a & nicks
      if (b.sort == a.sort) then
        if (Ziwei.gong_shuangxing_lunduan(panType,house_id,a,house_wu_xing) != "") then
          shuangxingOut.push(a)
          nicks -= Ziwei.shuangxing_buyong(a)
        end
      end
    end
    danxingOut = star_nicks.clone
    shuangxingOut.each do |a|
      danxingOut -= Ziwei.shuangxing_bubaoliu_danxing(a)
    end
    return shuangxingOut,danxingOut
  end
  ShuangXing_MA = 1
  ShuangXing_A = 2
  ShuangXing_B = 3
  ShuangXing_DSYG = 4
  # 1 : 主星雙星如下：共二十四組。以紫微及天府星系組合稱為雙星。
  # 2 : 小星雙星組合如下：目前共有十四組。
  # 巨門、天機天梁、太陽太陰、太陽巨門、太陽天梁、武曲天府、武曲貪狼、武曲天相、武曲七殺、武曲破軍、天同太陰、天同巨門、天同天梁、廉貞天府、廉貞貪狼、廉貞天相、廉貞七殺、廉貞破軍。
  # ＝》以上雙星資料顯在主要現象中。
  # 小星雙星組合如下：目前共有十四組：
  # 文昌文曲、貪狼火星、貪狼鈴星、貪狼擎羊、廉貞擎羊、太陰文曲、祿存天馬、左輔右弼。
  # ＝》以上雙星資料顯在主要現象中。
  # 天空地劫、龍池鳳閣、三台八座
  # ＝》以上雙星資料顯在次要現象中。
  # 大耗大耗、大耗小耗、小耗小耗。
  # ＝》以上雙星資料顯在環境現象中。
  # 雙星不用第二次的，主要是甲級星

  # 貪狼擎羊、廉貞擎羊 時 只用擎羊一次 例 只選 貪狼擎羊
  def Ziwei.shuangxing_buyong(a)
    aOut = Array.new
    dengji = Ziwei.shuangxing_dengji(a)
    dengji.each_index do |i|
      if (dengji[i] >= 3) then
        aOut.push(a[i])
      end
    end
    return aOut
  end
  # 不保留的單星
  ShuangXing_bubaoliu_danxing = [[1,1],[2,2],[3,3],[4,4],[2,3]]
  def Ziwei.shuangxing_bubaoliu_danxing(a)
    aOut = Array.new
    dengji = Ziwei.shuangxing_dengji(a)
    if (ShuangXing_bubaoliu_danxing.index(dengji) != nil) then
      return a
    end
    # [1,2] 狀況
    # 貪狼火星    astar9_astar22
    # 貪狼鈴星    astar9_astar23
    # 貪狼擎羊    astar9_astar20
    # 廉貞擎羊    astar6_astar20
    # 太陰文曲    astar8_astar16
    aOut.push(a[1])
    return aOut
  end
  def Ziwei.shuangxing_dengji(shuangxing)
    shuangxings = [["astar1","astar7"],["astar1","astar9"],["astar1","astar11"],["astar1","astar13"],
         ["astar1","astar14"],["astar2","astar8"],["astar2","astar10"],["astar2","astar12"],
         ["astar3","astar8"],["astar3","astar10"],["astar3","astar12"],["astar4","astar7"],
         ["astar4","astar9"],["astar4","astar11"],["astar4","astar13"],["astar4","astar14"],
         ["astar5","astar8"],["astar5","astar10"],["astar5","astar12"],["astar6","astar7"],
         ["astar6","astar9"],["astar6","astar11"],["astar6","astar13"],["astar6","astar14"],
         ["astar15","astar16"],["astar17","astar18"],["bstar2","bstar1"],["bstar13","bstar14"],
         ["bstar24","bstar25"],["dstar10","ystar7"],["dstar10","ystar6"],["ystar7","dstar4"],
         ["dstar4","ystar6"],["astar9","astar22"],["astar9","astar23"],["astar9","astar20"],
         ["astar6","astar20"],["astar8","astar16"],["astar19","bstar5"],["dstar9","ystar12"]
        ]
    shuangxing_dengji = [[1,1],[1,1],[1,1],[1,1],
                         [1,1],[1,1],[1,1],[1,1],
                         [1,1],[1,1],[1,1],[1,1],
                         [1,1],[1,1],[1,1],[1,1],
                         [1,1],[1,1],[1,1],[1,1],
                         [1,1],[1,1],[1,1],[1,1],
                         [2,2],[2,2],[3,3],[3,3],
                         [3,3],[4,4],[4,4],[4,4],
                         [4,4],[1,2],[1,2],[1,2],
                         [1,2],[1,2],[2,3],[4,4]
                        ]
    index = shuangxings.index(shuangxing)
    return shuangxing_dengji[index]
  end

  def Ziwei.nick_shuangxing(a)
    return a.join("_")
  end
  def Ziwei.nick_name_shuangxing(a)
    s = Ziwei.nick_shuangxing(a)
    return Ziwei.nick_name("shuangxing.#{s}")
  end

  # 五行
  # 水 shu
  # 木 mu
  # 金 jin
  # 土 tu
  # 火 huo
  def Ziwei.wuxing_nick(wuxing)
    a = ["shu","mu","jin","tu","huo"]
    return a[wuxing - 1]
  end

  # 天干
  # jia: 甲
  # yi: 乙
  # bing: 丙
  # ding: 丁
  # wu: 戊
  # ji: 己
  # geng: 庚
  # xin: 辛
  # ren: 壬
  # gui: 癸
  def Ziwei.tiangan_nick(n)
    a = ["jia","yi","bing","ding","wu","ji","geng","xin","ren","gui"]
    return a[n - 1]
  end
  def Ziwei.nick_name_tiangan(key)
    return Ziwei.nick_name("tiangan.#{key}")
  end

  # 論斷說明
  def Ziwei.shuoming(key)
    return Pm.GetStr("ziwei.shuoming.#{key}")
  end
  # 主要現象
  def Ziwei.shuoming_zhuyao(key)
    return Ziwei.shuoming("zhuyao.#{key}")
  end
  def Ziwei.shuoming_zhuyao_taitou()
    return Ziwei.shuoming_zhuyao("taitou")
  end
  # 次要現象
  def Ziwei.shuoming_ciyao(key)
    return Ziwei.shuoming("ciyao.#{key}")
  end
  def Ziwei.shuoming_ciyao_taitou()
    return Ziwei.shuoming_ciyao("taitou")
  end
  # 環境特徵
  def Ziwei.shuoming_huanjingtezheng(key)
    return Ziwei.shuoming("huanjingtezheng.#{key}")
  end
  def Ziwei.shuoming_huanjingtezheng_taitou(key)
    return Ziwei.shuoming_huanjingtezheng("taitou.#{key}")
  end
  def Ziwei.shuoming_huanjingtezheng_taitou_gong(gong)
    return Ziwei.shuoming_huanjingtezheng_taitou("#{gong}")
  end
  # 四化
  def Ziwei.shuoming_sihua(key)
    return Ziwei.shuoming("sihua.#{key}")
  end

  def Ziwei.shuoming_sihua_taitou(key)
    return Ziwei.shuoming_sihua("taitou.#{key}")
  end

  # 應期
  def Ziwei.shuoming_yingqi(key)
    return Ziwei.shuoming("yingqi.#{key}")
  end
  def Ziwei.shuoming_yingqi_pt_gixiong_gong(pt,gixiong,gong)
    s = Ziwei.pantype_nick(pt)
    return Ziwei.shuoming_yingqi("#{s}.#{gixiong}.#{gong}")
  end

  # 符號
  def Ziwei.fuhao(key)
    return Pm.GetStr("ziwei.fuhao.#{key}")
  end
  def Ziwei.maohao()
    return Ziwei.fuhao("maohao")
  end
  def Ziwei.dunhao()
    return Ziwei.fuhao("dunhao")
  end

  # 論斷說明
  def Ziwei.lunduan(key)
    return Pm.GetStr("ziweilunduan.#{key}")
  end
  # 單星  danxing
  def Ziwei.danxing_lunduan(key)
    return Ziwei.lunduan("danxing.#{key}")
  end
  def Ziwei.gong_danxing_lunduan(nPanType,house_id,xing_nick,house_wu_xing)
    pt = Ziwei.pantype_nick(nPanType)
    house = Ziwei.gong_nick(house_id)
    wu = Ziwei.wuxing_nick(house_wu_xing)
    return Ziwei.danxing_lunduan("#{pt}.#{house}.#{xing_nick}.#{wu}")
  end
  # 雙星  shuangxing
  def Ziwei.shuangxing_lunduan(key)
    return Ziwei.lunduan("shuangxing.#{key}")
  end
  def Ziwei.gong_shuangxing_lunduan(nPanType,house_id,a,house_wu_xing)
    pt = Ziwei.pantype_nick(nPanType)
    house = Ziwei.gong_nick(house_id)
    s = Ziwei.nick_shuangxing(a)
    wu = Ziwei.wuxing_nick(house_wu_xing)
    return Ziwei.shuangxing_lunduan("#{pt}.#{house}.#{s}.#{wu}")
  end

  # 四化疊宮  sihua.diegong
  def Ziwei.sihua_diegong_lunduan(key)
    return Ziwei.lunduan("sihua.diegong.#{key}")
  end
  # sihua diegong(四化疊宮) 本命沒有疊宮
  # diegong + tiangan = xianxiang
  def Ziwei.gong_sihua_diegong_lunduan(nPanType,wenshigong_id,huarugong_id,benmin_gong_id,sihua_idx)
    pt = Ziwei.pantype_nick(nPanType)
    #  問事宮位    官祿
    wenshigong = Ziwei.gong_nick(wenshigong_id)
    #化入宮位  命宮
    huarugong = Ziwei.gong_nick(huarugong_id)
    #化入宮位之本命盤宮位名  命宮
    benmin_gong = Ziwei.gong_nick(benmin_gong_id)
    # 四化
    sihua = Ziwei.sihua_nick(sihua_idx)
    return Ziwei.sihua_diegong_lunduan("#{pt}.#{wenshigong}.#{huarugong}.#{benmin_gong}.#{sihua}")
  end
  # 四化天干  sihua.tiangan
  def Ziwei.sihua_tiangan_lunduan(key)
    return Ziwei.lunduan("sihua.tiangan.#{key}")
  end
  # sihua_tiangan(四化天干)
  def Ziwei.gong_sihua_tiangan_lunduan(nPanType,wenshigong_id,sihua_gong_gan_id,jieshou_sihua_gong_id,sihua_idx)
    #  問事宮位    官祿
    wenshigong = Ziwei.gong_nick(wenshigong_id)
    pt = Ziwei.pantype_nick(nPanType)
    #四化宮干    甲
    sihua_gong_gan = Ziwei.tiangan_nick(sihua_gong_gan_id)
    #接受四化宮位  命宮
    jieshou_sihua_gong = Ziwei.gong_nick(jieshou_sihua_gong_id)
    # 四化
    sihua = Ziwei.sihua_nick(sihua_idx)
    return Ziwei.sihua_tiangan_lunduan("#{wenshigong}.#{pt}.#{sihua_gong_gan}.#{jieshou_sihua_gong}.#{sihua}")
  end

  # 自化
  # 感受現象 zihua
  def Ziwei.sihua_zihua_lunduan(pantype,key)
    pan_nick = Ziwei.pantype_nick(pantype)
    return Ziwei.lunduan("sihuazuhe.#{pan_nick}.#{key}")
  end
  def Ziwei.sihua_zihua_lunduan_wusihuazuhe(pantype)
    return Ziwei.sihua_zihua_lunduan(pantype,"wusihuazuhe.wusihuazuhe")
  end

  def Ziwei.gong_sihua_zihua_lunduan(pantype,wenshigong_id,sihua_idx,xiantiansihua,jieshou_sihua_gong_zihua)
    s,sihuas = Ziwei.gong_sihua_zihua_sihua_panduan(sihua_idx,xiantiansihua,jieshou_sihua_gong_zihua)
    wenshigong = Ziwei.gong_nick(wenshigong_id)
    if (s == "") then
      return Ziwei.sihua_zihua_lunduan_wusihuazuhe(pantype),sihuas,wenshigong
    end
    return Ziwei.sihua_zihua_lunduan(pantype,"#{wenshigong}.#{s}"), sihuas, wenshigong
  end
  A = 0
  B = 1
  C = 2
  D = 3
  # 假設 A＝祿、B＝權、C＝科、D＝忌。
    # lu_quan:（A、B）或（A、A、B、B）或（A、A、A、B）或（B、B、A、B）或（C、C、A、B）或（D、D、A、B）
    # lu_ke: （A、C）或（A、A、C、C）或（A、A、A、C）或（B、B、A、C）或（C、C、A、C）或（D、D、A、C）
    # lu_ji: （A、D）或（A、A、D、D）或（A、A、A、D）或（B、B、A、D）或（C、C、A、D）或（D、D、A、D）
    # quan_ke:（B、C）或（A、A、B、C）或（B、B、C、C）或（B、B、B、C）或（C、C、B、C）或（D、D、B、C）
    # quan_ji: （B、D）或（B、B、D、D）或（A、A、B、D）或（B、B、B、D）或（C、C、B、D）或（D、D、B、D）
    # ke_ji: （C、D）或（C、C、D、D）或（A、A、C、D）或（B、B、C、D）或（C、C、C、D）或（D、D、C、D）
    # lu_lu: （A、A）或（A、A、A）或（B、B、A）或（C、C、A）或（D、D、A）
    # quan_quan: （B、B）或（B、B、B）或（A、A、B）或（C、C、B）或（D、D、B）
    # ke_ke: （C、C）或（C、C、C）或（A、A、C）或（B、B、C）或（D、D、C）
    # ji_ji: （D、D）或（D、D、D）或（A、A、D）或（B、B、D）或（C、C、D）
    # lu_quan_ke: （A、B、C）或（A、A、A、B、C）或（B、B、A、B、C）或（C、C、A、B、C）或（D、D、A、B、C）
    # lu_quan_ji: （A、B、D）或（A、A、A、B、D）或（B、B、A、B、D）或（C、C、A、B、D）或（D、D、A、B、D）
    # lu_ke_ji: （A、C、D）或（A、A、A、C、D）或（B、B、A、C、D）或（C、C、A、C、D）或（D、D、A、C、D）
    # quan_ke_ji: （B、C、D）或（A、A、B、C、D）或（B、B、B、C、D）或（C、C、B、C、D）或（D、D、B、C、D）
    # lu_quan_ke_ji: （A、B、C、D）
  Sihua_Zihua_panduan_data = [
    [[[A,B],[A,A,B,B],[A,A,A,B],[B,B,A,B],[C,C,A,B],[D,D,A,B]],"lu_quan"],
    [[[A,C],[A,A,C,C],[A,A,A,C],[B,B,A,C],[C,C,A,C],[D,D,A,C]],"lu_ke"],
    [[[A,D],[A,A,D,D],[A,A,A,D],[B,B,A,D],[C,C,A,D],[D,D,A,D]],"lu_ji"],
    [[[B,C],[A,A,B,C],[B,B,C,C],[B,B,B,C],[C,C,B,C],[D,D,B,C]],"quan_ke"],
    [[[B,D],[B,B,D,D],[A,A,B,D],[B,B,B,D],[C,C,B,D],[D,D,B,D]],"quan_ji"],
    [[[C,D],[C,C,D,D],[A,A,C,D],[B,B,C,D],[C,C,C,D],[D,D,C,D]],"ke_ji"],
    [[[A,A],[A,A,A],[B,B,A],[C,C,A],[D,D,A]],"lu_lu"],
    [[[B,B],[B,B,B],[A,A,B],[C,C,B],[D,D,B]],"quan_quan"],
    [[[C,C],[C,C,C],[A,A,C],[B,B,C],[D,D,C]],"ke_ke"],
    [[[D,D],[D,D,D],[A,A,D],[B,B,D],[C,C,D]],"ji_ji"],
    [[[A,B,C],[A,A,A,B,C],[B,B,A,B,C],[C,C,A,B,C],[D,D,A,B,C]],"lu_quan_ke"],
    [[[A,B,D],[A,A,A,B,D],[B,B,A,B,D],[C,C,A,B,D],[D,D,A,B,D]],"lu_quan_ji"],
    [[[A,C,D],[A,A,A,C,D],[B,B,A,C,D],[C,C,A,C,D],[D,D,A,C,D]],"lu_ke_ji"],
    [[[B,C,D],[A,A,B,C,D],[B,B,B,C,D],[C,C,B,C,D],[D,D,B,C,D]],"quan_ke_ji"],
    [[[A,B,C,D]],"lu_quan_ke_ji"]
                             ]
  def Ziwei.gong_sihua_zihua_sihua_panduan(sihua_idx,xiantiansihua,jieshou_sihua_gong_zihua)
    a = Array.new
    a.push(sihua_idx)
    a += xiantiansihua
    a += jieshou_sihua_gong_zihua

    if (a.length == 1) then
      return "",a # 有感受，實質發生機率低。
    end
    # 自化判斷
    Sihua_Zihua_panduan_data.each_index do |i|
      b = Sihua_Zihua_panduan_data[i][0]
      b.each do |c|
        if (c.sort == a.sort) then
          return Sihua_Zihua_panduan_data[i][1],a
        end
      end
    end
    return "",a
  end
  def Ziwei.nick_zihua(a)
    return a.join("_")
  end

  # 警語
  def Ziwei.shuoming_jingyu()
    return Ziwei.shuoming("jingyu")
  end

  # 排盤中間資訊
  def Ziwei.ziwei_pan(key)
    return Pm.GetStr("ziwei.pan.#{key}")
  end
  def Ziwei.pan_zhongjian(key)
    return Ziwei.ziwei_pan("zhongjian.#{key}")
  end
  def Ziwei.pan_zhongjian_bazhi_biaotou()
    return Ziwei.pan_zhongjian("bazhi_biaotou")
  end


  # 各宮宮性說明
  def Ziwei.shuoming_gongxing(pantype,gong_id)
    pan_nick = Ziwei.pantype_nick(pantype)
    gong_nick = Ziwei.gong_nick(gong_id)
    return Ziwei.shuoming("gongxing.#{pan_nick}.#{gong_nick}")
  end

end
