rails generate model FourOut HouseStay:string HouseAccept:string HuaType:string Descrip:string
rails generate model FourK HouseStaySky:string HouseStay:string HuaType:string Descrip:string
rails generate model TzMain HouseStay:string Star:string SanKur:string Descrip:string
rails generate model user name:string password:string level:string
rails generate model user_ap user_id:integer ap_name:string ap_version:string end_date:datetime
rails generate model user_customer user_id:integer name:string first_name:string last_name:string sex:integer birthday:datetime year:integer month:integer day:integer hour:integer blood:integer 
rails generate model star_pan_par user_id:integer par_parameter:text par_interface:text par_display:text

原先string只有255,不夠用
change_column :star_pan_pars, :par_parameter, :text
change_column :star_pan_pars, :par_interface, :text
change_column :star_pan_pars, :par_display, :text

 preserveAspectRatio="xMaxYMax meet"
viewBox="0 0 24em 24em" font-family="微軟正黑體","arial","sans serif" font-size="medium" font-size-adjust="inherit" position="absolute" top="0em" left="0em" width="24em" height="24em"

    <%= render :partial => "ifate/star/pan_middle_p34" , :locals => {:aPan34Pt => @Pan34Pt }%>
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" preserveAspectRatio="xMinYMin meet"
viewBox="0 0 24em 24em" class="pan34line">
    <line x1="<%=@Pan34Pt[Star::HOUSE_34_OPP][0]%>%" y1="<%=@Pan34Pt[Star::HOUSE_34_OPP][1]%>%" x2="<%=@Pan34Pt[Star::HOUSE_34_SELF][0]%>%" y2="<%=@Pan34Pt[Star::HOUSE_34_SELF][1]%>%"  style="stroke:rgb(255,0,0);stroke-width:1"/>
    <line x1="<%=@Pan34Pt[Star::HOUSE_34_SELF][0]%>%" y1="<%=@Pan34Pt[Star::HOUSE_34_SELF][1]%>%" x2="<%=@Pan34Pt[Star::HOUSE_34_1][0]%>%" y2="<%=@Pan34Pt[Star::HOUSE_34_1][1]%>%"  style="stroke:rgb(255,0,0);stroke-width:1"/>
    <line x1="<%=@Pan34Pt[Star::HOUSE_34_1][0]%>%" y1="<%=@Pan34Pt[Star::HOUSE_34_1][1]%>%" x2="<%=@Pan34Pt[Star::HOUSE_34_2][0]%>%" y2="<%=@Pan34Pt[Star::HOUSE_34_2][1]%>%"  style="stroke:rgb(255,0,0);stroke-width:1"/>
    <line x1="<%=@Pan34Pt[Star::HOUSE_34_2][0]%>%" y1="<%=@Pan34Pt[Star::HOUSE_34_2][1]%>%" x2="<%=@Pan34Pt[Star::HOUSE_34_SELF][0]%>%" y2="<%=@Pan34Pt[Star::HOUSE_34_SELF][1]%>%"  style="stroke:rgb(255,0,0);stroke-width:1"/>
    </svg>

iPhone 看普通網頁時會將屏幕像素闊度變成 980 像素，如果想將屏幕像素闊度變回原本大小，可以在 head 加入以下設定:
<meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1, user-scalable=no" />

width=device-width 是用 iPhone 屏幕像素來定義網頁闊度。
minimum-scale 是用戶可將畫面縮小的倍率。
maximum-scale 是用戶可將畫面放大的倍率。
user-scalable 是否給用戶可將畫面放大或縮小。

  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
用戶將網頁收藏為書並加到主畫面時，可加入自定義的 icon，在 head 加入以下設定:
<link rel="apple-touch-icon" href="/custom_icon.png"/>

用戶將網頁收藏為書並加到主畫面時，可以設定在載入網頁時的等待畫面，在 head 加入以下設定:
<link rel="apple-touch-startup-image" href="/startup.png">

用戶將網頁收藏為書並加到主畫面時，可以將最上的狀態列設定為其他顏色(發覺只能設定為黑色)，在 head 加入以下設定:
<meta name="apple-mobile-web-app-status-bar-style" content="black" />

想在網頁載入完成時隱藏最頂的網址列，可加入以下的 Javascript:
window.onload = function(){
    setTimeout(function(){
        window.scrollTo(0, 1);
    }, 100);
}

防止用戶拉動網頁，可加入以下的 Javascript:
document.addEventListener("touchmove", function(event){
    event.preventDefault();
}, false);

rails generate migration change_user_customer
remove_column :user_customer, :first_name
remove_column :user_customer, :last_name
remove_column :user_customer, :year
remove_column :user_customer, :month
remove_column :user_customer, :day
remove_column :user_customer, :hour
add_column :user_customer, :email, :string, :default => nil
add_column :user_customer, :cust_userid, :integer, :default => nil
add_column :user_customer, :flag, :integer, :default => 1

add_column :user_customer, :year, :integer, :default => nil
add_column :user_customer, :month, :integer, :default => nil
add_column :user_customer, :day, :integer, :default => nil
add_column :user_customer, :hour, :integer, :default => nil

# 將Hash轉成Array再用join(",")轉成String儲存
# 將String 用split(/\s*,\s*/)轉成Array,再用 Hash[*a]轉成Hash
rails generate model star_pan_par user_id:integer pan_par:string pan_interface:string pan_display:string


rails destroy model star_pan_par

# 以下不用
rails generate model star_pan_par_type user_id:integer pan_year_display:integer leap_type:integer horse_type:integer god_type12:integer doc_type:integer lu_type:integer gs_type:integer flowsky_type:integer smallsan_type:integer god_type:integer flowlife_housetype:integer giakung_type:integer fourhua_type:integer flowyear_hua:integer gs_name_type:integer


rails destroy model star_pan_par

# name value version
rails generate model star_pan_par user_id:integer user_ap_id:integer par_name:string par_value:string

pan_par_type"  # 各門派排盤的參數
rails generate model star_pan_par_type user_id:integer pan_year_display:integer leap_type:integer horse_type:integer god_type12:integer doc_type:integer lu_type:integer gs_type:integer flowsky_type:integer smallsan_type:integer god_type:integer flowlife_housetype:integer giakung_type:integer fourhua_type:integer flowyear_hua:integer gs_name_type:integer


"dis_astar,1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1,dis_bstar,1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-1-11,dis_flowlyt,0,dis_flowma,0,dis_flowluan,0,dis_doctor,1,dis_live,1,dis_yearstar,1,dis_yeargod,1,dis_flowchan,1,dis_skycook,1,dis_lyyg,1,ds_7star,1"


rails generate controller ifate/star panparedit -s
rails generate controller ifate/star panpartypeupdate -s
rails generate controller ifate/star panparinterfaceupdate -s
rails generate controller ifate/star panpardisplayupdate -s

scss,less
coffee
haml replace erb

正式上線時要執行
bundle install
rake db:create RAILS_ENV="production" 
rake db:reset RAILS_ENV="production" 
rake db:migrate RAILS_ENV="production" 
rake 
編譯CSS及JS
rake assets:precompile
產生出來的檔案在public/assets/下。

touch tmp/restart.txt

rake assets:clean
這樣就會刪除。

rake db:setup RAILS_ENV="production"
rake db:migrate RAILS_ENV="production" 

安裝
command line:
gem install rails
bundle update
bundle install

bundle update
gem install coffee-script-source -v '1.3.1'
gem install execjs -v '1.3.1'
gem install coffee-script -v '2.2.0'
gem install coffee-rails -v '3.2.2'
gem install jquery-rails -v '2.0.2'
gem install sass -v '3.1.16'
gem install sass-rails -v '3.2.5'
gem install uglifier -v '1.2.4'

http://rubyonrails.org/download
http://www.tmtm.org/en/mysql/ruby/

git clone git://github.com/oneclick/rubyinstaller.git
cd rubyinstaller
rake devkit sfx=1

@events = Event.page(params[:page]).per(5)

gem update --system 

or 

$ gem install rubygems-update  # again, might need to be admin/root
$ update_rubygems  

Download from above.
Unpack into a directory and cd there
Install with: ruby setup.rb (you may need admin/root privilege)

gem install rails


http://jashkenas.github.com/coffee-script/

http://sass-lang.com/

=========================
# SQLite version 3.x
#   gem install sqlite3
development:
  adapter: mysql2
  encoding: utf8
  reconnect: false
  database: ifate
  pool: 5
  username: root
  password: peter
  host: 127.0.0.1

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  adapter: mysql2
  encoding: utf8
  reconnect: false
  database: ifate
  pool: 5
  username: root
  password: peter
  host: 127.0.0.1

production:
  adapter: mysql2
  encoding: utf8
  reconnect: false
  database: ifate
  pool: 5
  username: root
  password: peter
  host: 127.0.0.1
=========================


installer:
ruby 1.9.3p194
DevKit

gemfile mysql2

gem search mysql2
gem install mysql2

<%= form_tag '/upload', :multipart => true do %>
  <label for="file">File to Upload</label> <%= file_field_tag "file" %>
  <%= submit_tag %>
<% end %>

$ rake db:migrate

			<%=select_minute(Time.now, :field_name => 'birth_minute') %>
			<%=Pm.GetStr("IDS_X_MINUTE")%>

	    <td><%= link_to 'Edit', edit_person_path(person) %></td>
	    <td><%= link_to 'Destroy', person, :confirm => 'Are you sure?', :method => :delete %></td>
		<td><%= link_to "star", {:controller => "ifate", :action => "star"}, {:Name => customer.name, :WYear => sBirthday.slice(0..3), :WMonth => sBirthday.slice(4..5), :WDate => sBirthday.slice(6..7), :WHour => sBirthday.slice(8..9)} , :Sex => customer.sex %></td>


				if (@password == "pro58") then
					if (@user_name == "A0") then
						# Sets a cookie that expires in 1 hour.
						# cookie.signed[:user_name] = { :value => @user_name, :expires => 1.minute.from_now }
					elsif (@user_name == "A1") then
					elsif (@user_name == "A2") then
					elsif (@user_name == "A3") then
					elsif (@user_name == "A4") then
					elsif (@user_name == "PRO58") then
						@user_name = "A4"
					else

class User < ActiveRecord::Base
  validates_uniqueness_of :username
  validates_presence_of :username, :password, :email
  validates_size_of :username, :password, :within => 5..15
  validates_format_of :email,
    :with => /^([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})$/i
  validates_format_of :username, :with => /^\w+$/i,
    :message => "can only contain letters and numbers."
  validates_confirmation_of :password
  validates_inclusion_of :gender, :in => %w(male female)
  validates_acceptance_of :eula
end

<%= date_select('range', 'start_date',:date_separator => '/', :order => [ :year, :month, :day])%>
<%= date_select("contact", "first_met",:start_year => 1901, :end_year => 2099, :use_month_numbers => true ,:prompt => {:year => Pm.GetStr("IDS_X_YEAR"), :month => 'month', :day => 'day'}) %>
<%= time_select("article", "start_time", :include_seconds => false ,:prompt => {:hour => Pm.GetStr("IDS_X_HOUR"), :minute => 'minute', :second => 'seconds'}, :minute_step => 15) %>


<% my_date_time = Time.now + 4.days %>
<%= select_datetime(my_date_time, :date_separator => '/', :time_separator => ':', :datetime_separator => ',') %><BR/>
<%= datetime_select("article", "written_on", :start_year => 1995) %>

$:<<"C:\\Sites\\ifate\\lib"
require("Pm.rb")
Pm.GetStr("IDS_S_COMPANY_NAME")


offset
offset 可以設定忽略前幾筆不取出，通常用於資料分頁：
c = Category.limit(2)
c.first.id # 1
Category.limit(2).offset(3)
c.first.id # 4

      nDisPanType = Star.Get3ParPanType(@nFlowSkyType,nPanType)

irb
$:<<"C:\\Sites\\ifate\\lib"
require("Star_Function.rb")
h = Hash.new
h[Star::WYear] = 2000
h[Star::WMonth] = 3
h[Star::WDate] = 13
h[Star::WHour] = 1
h[Star::Sex] = true
h2 = Hash.new
h2[Star::EYear] = 2012
h2[Star::EMonth] = 1
h2[Star::LeapMonth] = false
h2[Star::EDate] = 1
h2[Star::WHour] = 1
h2[Star::WMinute] = 1
h2[Star::HOUSE_NAME] = EX_HOUSE_ALL
h3 = Star.pan_par_init(nil,nil,nil,0)
a = Star.new
a.g_GetStarPanInfo(Star::PAN_TENYEAR,h,h2)
a.g_GetStarPanInfo(Star::PAN_TENYEAR,h,h2,h3)


d:
cd D:\Pro58_2010\Ruby\projects\vip\lib
irb
$:<<"D:\\Pro58_2010\\Ruby\\projects\\vip\\lib"
require("Xdate_Function.rb")
Xdate.LoadSegTbl()
Xdate.GetSegmentStrFromWest(2012, 4, 7, 21, 59)
Xdate.GetEMin(1)
 Xdate.GetSegmentFromWest(2012, 4, 20, 23, 0)
 
	   		<%= label_tag(:customer_search_label, "S:", :class => "customer_search_label") %>

		<line x1="<%=@Pan34Pt[Star::HOUSE_34_OPP][0]%>em" y1="<%=@Pan34Pt[Star::HOUSE_34_OPP][1]%>em" x2="<%=@Pan34Pt[Star::HOUSE_34_SELF][0]%>em" y2="<%=@Pan34Pt[Star::HOUSE_34_SELF][1]%>em"  style="stroke:rgb(255,0,0);stroke-width:1"/>
		<line x1="<%=@Pan34Pt[Star::HOUSE_34_SELF][0]%>em" y1="<%=@Pan34Pt[Star::HOUSE_34_SELF][1]%>em" x2="<%=@Pan34Pt[Star::HOUSE_34_1][0]%>em" y2="<%=@Pan34Pt[Star::HOUSE_34_1][1]%>em"  style="stroke:rgb(255,0,0);stroke-width:1"/>
		<line x1="<%=@Pan34Pt[Star::HOUSE_34_1][0]%>em" y1="<%=@Pan34Pt[Star::HOUSE_34_1][1]%>em" x2="<%=@Pan34Pt[Star::HOUSE_34_2][0]%>em" y2="<%=@Pan34Pt[Star::HOUSE_34_2][1]%>em"  style="stroke:rgb(255,0,0);stroke-width:1"/>
		<line x1="<%=@Pan34Pt[Star::HOUSE_34_2][0]%>em" y1="<%=@Pan34Pt[Star::HOUSE_34_2][1]%>em" x2="<%=@Pan34Pt[Star::HOUSE_34_SELF][0]%>em" y2="<%=@Pan34Pt[Star::HOUSE_34_SELF][1]%>em"  style="stroke:rgb(255,0,0);stroke-width:1"/>



					<div id="astar<%=Star.Get_AStarInfo_Star(sAStarInfo)%>" class="astar<%=i%>" onmouseover="AStarMouseOver1(i,<%=@AStarFourHuaSky[nEarthIndex]%>)" onmouseout="this.className='astar<%=i%>';">

					<div id="astar<%=Star.Get_AStarInfo_Star(sAStarInfo)%>" class="astar<%=i%>" onmouseover="this.className='astar_hover';" onmouseout="this.className='astar<%=i%>';">


<?xml version="1.0" standalone="no"?>

<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" 

"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">



<svg xmlns="http://www.w3.org/2000/svg" version="1.1">

  <circle cx="100" cy="50" r="40" stroke="black"

  stroke-width="2" fill="red" />


<line x1="100" y1="50" x2="100" y2="100" color="black" />
<line x1="300" y1="100" x2="300" y2="300"/>
<line x1="500" y1="100" x2="500" y2="300"/>
<line x1="700" y1="100" x2="700" y2="300"/>
<line x1="900" y1="100" x2="900" y2="300"/>

</svg>

		<svg xmlns="http://www.w3.org/2000/svg" version="1.1">
		<line x1="<%=@Pan34Pt[Star::HOUSE_34_OPP][0]%>em" y1="<%=@Pan34Pt[Star::HOUSE_34_OPP][1]%>em" x2="<%=@Pan34Pt[Star::HOUSE_34_SELF][0]%>em" y2="<%=@Pan34Pt[Star::HOUSE_34_SELF][1]%>em"  style="stroke:rgb(255,0,0);stroke-width:1"/>
		<line x1="<%=@Pan34Pt[Star::HOUSE_34_SELF][0]%>em" y1="<%=@Pan34Pt[Star::HOUSE_34_SELF][1]%>em" x2="<%=@Pan34Pt[Star::HOUSE_34_1][0]%>em" y2="<%=@Pan34Pt[Star::HOUSE_34_1][1]%>em"  style="stroke:rgb(255,0,0);stroke-width:1"/>
		<line x1="<%=@Pan34Pt[Star::HOUSE_34_1][0]%>em" y1="<%=@Pan34Pt[Star::HOUSE_34_1][1]%>em" x2="<%=@Pan34Pt[Star::HOUSE_34_2][0]%>em" y2="<%=@Pan34Pt[Star::HOUSE_34_2][1]%>em"  style="stroke:rgb(255,0,0);stroke-width:1"/>
		<line x1="<%=@Pan34Pt[Star::HOUSE_34_2][0]%>em" y1="<%=@Pan34Pt[Star::HOUSE_34_2][1]%>em" x2="<%=@Pan34Pt[Star::HOUSE_34_SELF][0]%>em" y2="<%=@Pan34Pt[Star::HOUSE_34_SELF][1]%>em"  style="stroke:rgb(255,0,0);stroke-width:1"/>
		</svg>


irb -E UTF-8:UTF-8
exit
d:
cd D:\Pro58_2010\Ruby\projects\demo\lib
irb
$:<<"D:\\Pro58_2010\\Ruby\\projects\\demo\\lib"
require("Xdate_Function.rb")
Xdate.GetWYearDaysFrom1900(1900)
Xdate.GetWMonthDaysFrom11(1900,2)
Xdate.GetTotalWDaysFrom190011(1900,1,1)
Xdate.GetWWeekDay(2011,12,20)
(0..4).each do |i|
 (0..2).each do |j|
   puts(i.to_s + "," + j.to_s + "," + Xdate.GetBy60Five(i,j).to_s)
  end
end
(0..9).each do |i|
   puts(i.to_s + "," + Xdate.GetSkyFive(i).to_s)
end
(0..11).each do |i|
   puts(i.to_s + "," + Xdate.GetEarthFive(i).to_s)
end
Xdate.EastYearToChina(1911)
Xdate.WestYearToChina(2011)
Xdate.GetEastMonthArray(1900)
Xdate.GetEastMonthDays(1900,8,true)
Xdate.GetEastYearDays(1900)
Xdate.West2East(2011, 12, 21)
Xdate.East2West(2012,1,1,false)
Xdate.West2East(2012,1,23)
Xdate.East2West(2011,11,27,false)
Xdate.West2East(2011,12,21)
Xdate.IsELeapYear?(2011)
Xdate.IsELeapYear?(2012)
Xdate.NextEDateFromTime(2011,12,29,false,0)
Xdate.NextEDateFromTime(2011,12,29,false,11)
Xdate.NextEDateFromTime(2011,12,29,false,12)
Xdate.NextEDateFromTime(1900,8,30,false,12)
Xdate.NextEDateFromTime(1900,8,30,true,12)
Xdate.NextEDateFromTime(1900,8,29,true,12)
Xdate.GetMonthGanZhi(2011, 3, 23,11, 40)
Xdate.GetMonthGanZhi(2012,3,23,11,40)
Xdate.GetSegmentFromWest(2012, 2, 23,11, 40)
Xdate.GetSegmentFromWest(2012, 2, 22,11, 40)
Xdate.GetSegmentFromWest(2012, 2, 3,11, 40)
Xdate.GetSegmentFromWest(2012, 2, 8,11, 40)

exit
d:
cd D:\Pro58_2010\Ruby\projects\demo\lib
irb
$:<<"D:\\Pro58_2010\\Ruby\\projects\\demo\\lib"
require("Xdate_Function.rb")
Xdate.GetLunarMonthGanZhi(2012,1)
Xdate.GetLunarMonthGanZhi(2011,10)
Xdate.GetLunarYearGanZhi(2012)
Xdate.GetLunarYearGanZhi(2011)

exit
d:
cd D:\Pro58_2010\Ruby\projects\demo\lib
irb
$:<<"D:\\Pro58_2010\\Ruby\\projects\\demo\\lib"
require("Star_Function.rb")
h = Hash.new
h[Star::WYear] = 2000
h[Star::WMonth] = 3
h[Star::WDate] = 13
h[Star::WHour] = 1
h[Star::Sex] = true
a = Star.new
p(a.GetPanInfo(Star::PAN_NORMAL,h))
p(a.GetPanInfo(Star::PAN_TENYEAR,h))
p(a.GetPanInfo(Star::PAN_FLOWYEAR,h))
p(a.GetPanInfo(Star::PAN_FLOWMONTH,h))
p(a.GetPanInfo(Star::PAN_FLOWDATE,h))
p(a.GetPanInfo(Star::PAN_FLOWTIME,h))
p(a.GetPanInfo(Star::PAN_FLOWMIN,h))


exit
d:
cd D:\Pro58_2010\Ruby\projects\demo\lib
irb
$:<<"D:\\Pro58_2010\\Ruby\\projects\\demo\\lib"
require("PanWeb.rb")
h = Hash.new
h[Star::WYear] = 2000
h[Star::WMonth] = 3
h[Star::WDate] = 13
h[Star::WHour] = 1
h[Star::Sex] = true
a = PanWeb.new
sWebPan = a.pw_getPan(PanWeb::PW_STAR,Star::PAN_NORMAL,h)
a.pw_Star_GetAStar(1,1)


exit
d:
cd D:\Pro58_2010\Ruby\projects\demo\lib
irb
$:<<"D:\\Pro58_2010\\Ruby\\projects\\demo\\lib"
require("test_functions.rb")
okTags="a href, b, br, i, p, table, tr, td, center, font"
d = sanitize(LibTest.TestStar())
d = sanitize(LibTest.TestStar(), okTags )

exit
d:
cd D:\Pro58_2010\Ruby\projects\demo\lib
irb
$:<<"D:\\Pro58_2010\\Ruby\\projects\\demo\\lib"
require("PanWeb.rb")
require("Pm.rb")
Pm.GetStr("IDS_A_KEY_FONT_MIN")


exit
d:
cd D:\Pro58_2010\Ruby\projects\demo\lib
irb
$:<<"D:\\Pro58_2010\\Ruby\\projects\\demo\\lib"
require("PanXml_Function.rb")
h = Hash.new
h[Star::WYear] = 2000
h[Star::WMonth] = 3
h[Star::WDate] = 13
h[Star::WHour] = 1
h[Star::Sex] = true
a = PanXml.new
sXmlPan = a.px_getPan(Pm::AP_STAR,Star::PAN_NORMAL,h)



exit
d:
cd D:\Pro58_2010\Ruby\projects\vip\lib
irb
$:<<"D:\\Pro58_2010\\Ruby\\projects\\vip\\lib"
require("PanWeb.rb")
h = Hash.new
h[Star::WYear] = 2000
h[Star::WMonth] = 3
h[Star::WDate] = 13
h[Star::WHour] = 1
h[Star::Sex] = true
a = PanWeb.new
sWebPan = a.pw_getPan(Pm::AP_STAR,Star::PAN_NORMAL,h)
a.pw_Star_GetAStar(1,1)


exit
d:
cd D:\Pro58_2010\Ruby\projects\vip
rails console
$:<<"D:\\Pro58_2010\\Ruby\\projects\\vip\\lib"
$:<<"D:\\Pro58_2010\\Ruby\\projects\\vip"
$:<<"D:\\Pro58_2010\\Ruby\\projects\\vip\\app\\models"
require("Explain_Function.rb")
h = Hash.new
h[Star::WYear] = 2000
h[Star::WMonth] = 3
h[Star::WDate] = 13
h[Star::WHour] = 1
h[Star::Sex] = true
h2 = Hash.new
h2[Star::EYear] = 2012
h2[Star::EMonth] = 1
h2[Star::EDate] = 1
h2[Star::ETime] = 1
h2[Star::EMinute] = 1
h2[Star::HOUSE_NAME] = EX_HOUSE_ALL
a = Explain.new
sBuf = a.ex_getExplain(Pm::AP_STAR,Star::PAN_NORMAL,h,h2)
h2[Star::HOUSE_NAME] = EX_HOUSE_1
sBuf = a.ex_getExplain(Pm::AP_STAR,Star::PAN_TENYEAR,h,h2)
h2[Star::HOUSE_NAME] = EX_HOUSE_2
sBuf = a.ex_getExplain(Pm::AP_STAR,Star::PAN_FLOWYEAR,h,h2)
sBuf = a.ex_getExplain(Pm::AP_STAR,Star::PAN_FLOWMONTH,h,h2)
sBuf = a.ex_getExplain(Pm::AP_STAR,Star::PAN_FLOWDATE,h,h2)

exit
d:
cd D:\Pro58_2010\Ruby\projects\vip
irb
$:<<"D:\\Pro58_2010\\Ruby\\projects\\vip\\lib"
$:<<"D:\\Pro58_2010\\Ruby\\projects\\vip"
require("TzMainStr.rb")
a = TzMainStr.new
a.parseStr()

require("FourOutStr.rb")
a = FourOutStr.new
a.parseStr()

require("FourKStr.rb")
a = FourKStr.new
a.parseStr()

d:
cd D:\Pro58_2010\Ruby\projects\vip
irb
$:<<"D:\\Pro58_2010\\Ruby\\projects\\vip\\lib"
require("TzMainData.rb")
sBuf = TzMainData.getDesc(0,"","","")


/***** Selector Hacks ******/

/* IE6 and below */
* html #uno  { color: red }

/* IE7 */
*:first-child+html #dos { color: red } 

/* IE7, FF, Saf, Opera  */
html>body #tres { color: red }

/* IE8, FF, Saf, Opera (Everything but IE 6,7) */
html>/**/body #cuatro { color: red }

/* Opera 9.27 and below, safari 2 */
html:first-child #cinco { color: red }

/* Safari 2-3 */
html[xmlns*=""] body:last-child #seis { color: red }

/* safari 3+, chrome 1+, opera9+, ff 3.5+ */
body:nth-of-type(1) #siete { color: red }

/* safari 3+, chrome 1+, opera9+, ff 3.5+ */
body:first-of-type #ocho {  color: red }

/* saf3+, chrome1+ */
@media screen and (-webkit-min-device-pixel-ratio:0) {
 #diez  { color: red  }
}

/* iPhone / mobile webkit */
@media screen and (max-device-width: 480px) {
 #veintiseis { color: red  }
}


/* Safari 2 - 3.1 */
html[xmlns*=""]:root #trece  { color: red  }

/* Safari 2 - 3.1, Opera 9.25 */
*|html[xmlns*=""] #catorce { color: red  }

/* Everything but IE6-8 */
:root *> #quince { color: red  }

/* IE7 */
*+html #dieciocho {  color: red }

/* Firefox only. 1+ */
#veinticuatro,  x:-moz-any-link  { color: red }

/* Firefox 3.0+ */
#veinticinco,  x:-moz-any-link, x:default  { color: red  }



/***** Attribute Hacks ******/

/* IE6 */
#once { _color: blue }

/* IE6, IE7 */
#doce { *color: blue; /* or #color: blue */ }

/* Everything but IE6 */
#diecisiete { color/**/: blue }

/* IE6, IE7, IE8 */
#diecinueve { color: blue\9; }

/* IE7, IE8 */
#veinte { color/*\**/: blue\9; }

/* IE6, IE7 -- acts as an !important */
#veintesiete { color: blue !ie; } /* string after ! can be anything */


【命宫】
　　您是一位聪明而有急智，个性好动外向，不耐静，欲望多，桃花多，比较注重物质上的享乐，好胜心强，喜爱高谈阔论，耐心不够，野心勃勃的，易追求社会地位，对哲学、神仙之学有研究的偏好，年轻的时候应多方学习，奠定日后的成功。 
　　您的个性比较好强，不服输，寻求刺激，急躁倔强，破坏力强，行事不计后果，会有报复的心态，易受伤害或会向法律挑战，身体会留下许多的伤痕。