class Talent
  MAX_RUNNING_COUNT = 5 # 每次執行5份報告 
  def sti_api_check_all()
    # Rails.logger.info "sti_api_check_all"
    sti_api_all_set_waiting() # 將資料的key的running拿掉，結束上一個執行程式
    sti_api_all_set_running(Talent::MAX_RUNNING_COUNT)
    hValue = Hash.new
    tdbs = sti_api_all_get_runnings()
    tdbs.each do |tdb|
      if tdb != nil then
        t_key = tdb.key
        hValue = tdb.hValue
        hValue["data"] = sti_api_all_each(hValue)
        sti_api_all_set_finished(tdb,t_key,hValue)
    
        # 清除記憶體空間 2022/7/2
        init_all() # 每次執行完都重設，避免有時是zh-CN，有時是zh-TW的問題
      end
    end
    # 清除記憶體空間
    init_all()
  end
  def sti_api_all_each(hPar)
    h_par_org = hPar["param"].clone
    nPanType = h_par_org["pan_type"].to_i
    nPanType = 2 if h_par_org["pan_type"] == nil
    hUserInfo = Jianggong.get_user_info(h_par_org)
    hUserDefData = Jianggong.get_cur_date_info(h_par_org)
    hUserDefData["my_current_lang"] = h_par_org["my_current_lang"]
    hUserDefData["my_current_lang"] = "zh-TW" if h_par_org["my_current_lang"] == nil || h_par_org["my_current_lang"] == ""
    I18n.locale = hUserDefData["my_current_lang"]
    hUserDefData["flow_rate"] = get_ts_rate(nPanType,h_par_org)
    # hUserDefData["uuid"] = h_par_org["uuid"]
    hUserDefData["uuid"] = hPar["uuid"]

    hResult = sti_api_all(nPanType,hUserInfo,hUserDefData)

    return hResult
  end
  STI_UUID_PRE = "STI_"
  STI_UUID_WAITING = "_WAITING"
  STI_UUID_RUNNING = "_RUNNING"
  STI_UUID_FINISHED = "_FINISHED"
  def sti_api_remove_s(s,s_to_r="_")
    return s.sub(s_to_r,"")
  end
  def sti_api_all_savedb_key(uuid)
    return "#{Talent::STI_UUID_PRE}#{uuid}"
  end
  def sti_api_all_empty_result(uuid,h_par_org)
    h = Hash.new
    hPar = h_par_org.clone
    hPar.delete("api_id")
    hPar.delete("format")
    hPar.delete("controller")
    hPar.delete("action")
    h["param"] = hPar
    h["data"] = {}
    h["uuid"] = uuid
    return h
  end
  def sti_api_all_savedb_waiting(uuid,h_par_org)
    k = "#{sti_api_all_savedb_key(uuid)}#{Talent::STI_UUID_WAITING}"
    h = sti_api_all_empty_result(uuid,h_par_org)
    id = Pm.saveTestDb2(k,h)

    return h
  end
  def sti_api_all_set_finished(tdb,t_key,hResult)
    k = t_key.sub(Talent::STI_UUID_RUNNING,Talent::STI_UUID_FINISHED)
    tdb.key = k
    tdb.hValue = hResult
    tdb.save!    
  end
  # waiting to running
  def sti_api_all_set_running(count=Talent::MAX_RUNNING_COUNT)
    oldKey = "#{sti_api_all_savedb_key("%")}#{Talent::STI_UUID_WAITING}"
    Pm.changeTestDbKeys(oldKey,Talent::STI_UUID_WAITING,Talent::STI_UUID_RUNNING,count)
  end
  # running to waiting 
  def sti_api_all_set_waiting()
    oldKey = "#{sti_api_all_savedb_key("%")}#{Talent::STI_UUID_RUNNING}"
    Pm.changeTestDbKeys(oldKey,Talent::STI_UUID_RUNNING,Talent::STI_UUID_WAITING)
  end
  def sti_api_all_get_runnings()
    oldKey = "#{sti_api_all_savedb_key("%")}#{Talent::STI_UUID_RUNNING}"
    tdbs = Testdb.check_key_like(oldKey).all
    return tdbs
  end
  def sti_api_all_get_running()
    oldKey = "#{sti_api_all_savedb_key("%")}#{Talent::STI_UUID_RUNNING}"
    tdb = Testdb.check_key_like(oldKey).first
    return tdb
  end
  def sti_api_all(nPanType,hUserInfo,hUserDefData)
    wc_init_Scores()

  	h = Hash.new

    # 2022/7/3 連續處理錯誤
    @bNeedDrop = false

  	h["api_1_1"] = sti_api_1_1(nPanType,hUserInfo,hUserDefData)
  	h["api_1_2"] = sti_api_1_2(nPanType,hUserInfo,hUserDefData)
  	h["api_1_3"] = sti_api_1_3(nPanType,hUserInfo,hUserDefData)
  	h["api_1_4"] = sti_api_1_4(nPanType,hUserInfo,hUserDefData)
  	h["api_1_5"] = sti_api_1_5(nPanType,hUserInfo,hUserDefData)
  	h["api_1_6"] = sti_api_1_6(nPanType,hUserInfo,hUserDefData)

    h["api_mp_1"] = sti_api_mp_1(nPanType,hUserInfo,hUserDefData,true)
    h["api_mp_2"] = sti_api_mp_2(nPanType,hUserInfo,hUserDefData,true)

    # h["uuid"] = hUserDefData["uuid"]
  	# id = Pm.saveTestDb2(hUserDefData["uuid"],h)

  	return h
  end
  def sti_api_all_check(uuid)
    oldKey = "#{sti_api_all_savedb_key("#{uuid}")}%"
    oldKey1 = "#{sti_api_all_savedb_key("#{uuid}")}"
    tdb = Testdb.check_key_like(oldKey).last
    h = {}
    if tdb == nil then
      h["status"] = "NULL"
      return h
    end
    h = tdb.hValue
    s = tdb.key.sub(oldKey1,"")
    s = sti_api_remove_s(s)
  	h["status"] = s

  	return h
  end

  def sti_api_1(nPanType,hUserInfo,hUdt)
  	hUserDefData = hUdt.clone
    hOut = sti_getdata(nPanType,hUserInfo,hUserDefData)
    hOut["final_success"] = true

    return hOut
  end
  # 主指標輸出
  def sti_api_1_1(nPanType,hUserInfo,hUdt)
  	hUserDefData = hUdt.clone
    trait_codes = ["A11","A12","A13","A16","A17","A18","A20","A22","A25","B21","B23","B24","B25","B26","B27","B28","C32","C33","C34","C36","C38","C39","C40","C41","D11","D13","D14","D15","D16","D51","D54","D55"]
    hUserDefData["trait_codes"] = trait_codes

    hOut = sti_getdata_1_1(nPanType,hUserInfo,hUserDefData)

    hOut["final_success"] = true

    return hOut
  end
  def get_ts_rate(pan_type,h)
    a = get_ts_default_rate(pan_type)
    h_rate_1,h_rate_2,h_rate_3 = a[0],a[1],a[2]
    h_rate_1 = h["rate_1"].to_i if h["rate_1"] != nil && h["rate_1"] != ""
    h_rate_2 = h["rate_2"].to_i if h["rate_2"] != nil && h["rate_2"] != ""
    h_rate_3 = h["rate_3"].to_i if h["rate_3"] != nil && h["rate_3"] != ""
    return [h_rate_1,h_rate_2,h_rate_3]
  end
  # 大五人格
  def sti_api_1_2(nPanType,hUserInfo,hUdt)
  	hUserDefData = hUdt.clone
    trait_codes = ["MO11","MO12","MO13","MO14","MO15"]
    hUserDefData["trait_codes"] = trait_codes

    hOut = sti_getdata_1_2(nPanType,hUserInfo,hUserDefData)

    hOut["final_success"] = true

    return hOut
  end
  # 1.3  C＋D霍蘭德六邊形
  def sti_api_1_3(nPanType,hUserInfo,hUdt)
  	hUserDefData = hUdt.clone
    trait_codes = ["MH01","MH02","MH03","MH04","MH05","MH06"]
    hUserDefData["trait_codes"] = trait_codes

    hOut = sti_getdata_1_3(nPanType,hUserInfo,hUserDefData)

    hOut["final_success"] = true

    return hOut
  end
  # 1.4  E十二門課堂指標
  def sti_api_1_4(nPanType,hUserInfo,hUdt)
  	hUserDefData = hUdt.clone
    trait_codes = ["MS01","MS02","MS03","MS04","MS05","MS06","MS07","MS08","MS09","MS10","MS11","MS12"]
    hUserDefData["trait_codes"] = trait_codes

    hOut = sti_getdata_1_4(nPanType,hUserInfo,hUserDefData)

    hOut["final_success"] = true

    return hOut
  end
  # 1.5  E十一門課堂指標
  def sti_api_1_5(nPanType,hUserInfo,hUdt)
  	hUserDefData = hUdt.clone
    trait_codes1 = ["L01","L02","L03","L04","L05","L06"]
    trait_codes2 = ["MA01","MA02","MA03","MA04","MA05","MA06","MA07","MA08","MA09","MA11","MA12","MA13"]
    hUserDefData["trait_codes1"] = trait_codes1
    hUserDefData["trait_codes2"] = trait_codes2

    hOut = sti_getdata_1_5(nPanType,hUserInfo,hUserDefData)

    hOut["final_success"] = true

    return hOut
  end
  # 1.6.1  G綜合應用指標
  # N71_業務型
  # N72_創業型
  # N73_幕僚型
  # N74_高管型
  # N75_研究型
  # N76_企劃型

  # 1.6.2  G綜合應用指標
  # MI01  心理、哲學
  # MI02  文學、史學
  # MI03  語言、文字
  # MI04  藝術、創作
  # MI05  教育、培訓
  # MI06  金融、經濟
  # MI07  經營、管理
  # MI08  司法、法學
  # MI09  科學、理學
  # MI10  製造、工業
  # MI11  畜牧、農林
  # MI12  行銷、商學
  # MI13  生物、醫學
  # MI14  營造、建築
  # MI15  社會、服務
  # MI16  休閒、觀光
  # MI17  影音、媒體
  # MI18  信息、通訊
  # MI19  政治、國際
  # MI20  物流、運輸  
  def sti_api_1_6(nPanType,hUserInfo,hUdt)
  	hUserDefData = hUdt.clone
    trait_codes1 = ["N71","N72","N73","N74","N75","N76"]
    trait_codes2 = ["MI01","MI02","MI03","MI04","MI05","MI06","MI07","MI08","MI09","MI10","MI11","MI12","MI13","MI14","MI15","MI16","MI17","MI18","MI19","MI20"]
    hUserDefData["trait_codes1"] = trait_codes1
    hUserDefData["trait_codes2"] = trait_codes2

    hOut = sti_getdata_1_6(nPanType,hUserInfo,hUserDefData)

    hOut["final_success"] = true

    return hOut
  end

    # for sti
  def get_ts_default_rate(pan_type)
    if pan_type <= Cfate::PAN_TENYEAR then
      return [40,60,0]
    else
      # return [30,50,20]
      # 2021/7/19 文華提出修改
      return [20,40,40]
    end
  end
  def sti_getdata_1_1(nPanType,hUserInfo,hUserDefData)
    hOut = sti_getdata_1_1_by_pantype2(nPanType,hUserInfo,hUserDefData)

    hOut = sti_getdata_1_1_4(hOut)
    return hOut
  end
  # 1.1.4天賦強弱計算：
  # （1）天賦強弱計算分為三部份，「高天賦區」、「最溫暖區」、「多關愛區」三個區數量總數，不在區內則不計算。
  def sti_getdata_1_1_4(a0)
      hOut = a0.clone
      a = Array.new(3,0)
      a0["Data"].keys.each do |trait_code|
          pr = a0["Data"][trait_code]["pr"]
          area = sti_getdata_1_1_4_area(pr)
          a[area] = a[area] + 1 if area != nil
      end
      hOut["talent_area"] = a
      return hOut
  end
  # （2）0「高天賦區」：PR值達到85以上(含)。
  # （2）1「最溫暖區」：PR值達到30(含)〜70(含)之間。
  # （2）2「多關愛區」：PR值達到15以下(含)
  def sti_getdata_1_1_4_area(pr)
      return 0 if pr >= 85.0
      return 1 if pr.between?(30.0,70.0)
      return 2 if pr <= 15.0
      return nil
  end

  def sti_getdata_1_1_out(a0,a1,a2,n0,n1,n2)
      hOut = a0.clone
      pr2 = 0.0
      a0["Data"].keys.each do |trait_code|
          pr0 = a0["Data"][trait_code]["pr"]
          pr1 = a1["Data"][trait_code]["pr"]
          pr2 = a2["Data"][trait_code]["pr"] if a2 != nil
          flow_pr = pr0 * n0 + pr1 * n1
          flow_pr = flow_pr + pr2 * n2 if a2 != nil
          flow_pr = flow_pr / 100
          flow_pr = flow_pr.round(2)

          # 各分數低於 15%含 以下，顯示規則修正 2021/8/27
          flow_pr = modify_for_low_pr(flow_pr)

          hOut["Data"][trait_code]["flow_pr"] = flow_pr
          hOut["Data"][trait_code]["flow_pr_stars"] = pr_stars(flow_pr)
      end
      return hOut
  end
  def sti_getdata_1_1_by_pantype(nPanType,hUserInfo,hUserDefData)
    hOut = {}

    bFinal_Sucess = true

    hUdt = hUserDefData.clone
    my_current_lang = hUdt["my_current_lang"]
    nPanType = Star.LegalPanType(nPanType)
    trait_codes = hUserDefData["trait_codes"]
    hData = {}
    pr_cmp = 0.0
    trait_code_max = ""
    trait_codes.each do |trait_code|
        bFind,category_level,category_id,item_id,trait_id,desc,trait_desc,trait_score_desc,trait_def = sti_get_pars_from_trait_code(my_current_lang,trait_code)
        if bFind then
            hUdt["category_level"] = category_level
            hScore,hDetail = getScore_trait(nPanType,hUserInfo,hUdt,category_id,item_id,trait_id)
            pr_value = wc_get_pr(category_id,category_level)
            hData[trait_code],pr = make_sti_trait_score_hash(my_current_lang,hScore,hDetail,pr_value,category_level,category_id,item_id,trait_id,trait_code,desc,trait_desc,trait_score_desc,trait_def)
    # puts "trait_code : #{trait_code} / #{pr} / #{pr_cmp}"
            if (pr > pr_cmp) then
                trait_code_max = trait_code
                pr_cmp = pr
            end
        else
            bFinal_Sucess = false
            hData[trait_code] = {"trait_code" => "","desc" => "","trait_desc" => "","trait_score_desc" => "","score" => 0, "pr" => 0, "pr_desc" => "", "trait_content" => "", "proposal" => "", "pr_score_drop" => 0}
        end
    end
    hOut["trait_code_max_pr"] = trait_code_max
    hOut["name"] = hUserInfo[Cfate::Name]
    hOut["timestamp"] = hUserInfo["timestamp"]
    hOut["timestamp_solar"] = Xdate.make_timestamp_hash(hUserInfo)
    hOut["timestamp_udt"] = hUserDefData["timestamp_udt"]
    hOut["longitude"] = hUserInfo["longitude"] if hUserInfo["longitude"] != nil
    hOut["latitude"] = hUserInfo["latitude"] if hUserInfo["latitude"] != nil
    hOut["Data"] = hData
    hOut["final_success"] = bFinal_Sucess

    return hOut
  end

  # 大五人格
  def sti_getdata_1_2(nPanType,hUserInfo,hUserDefData)
    # hOut = sti_getdata_1_1_by_pantype(0,hUserInfo,hUserDefData)
    hOut = sti_getdata_1_1_by_pantype2(nPanType,hUserInfo,hUserDefData)
    hOut = sti_getdata_1_2_top2(hOut)
    hOut = sti_getdata_1_2_desc(hOut)
    return hOut
  end
  def sti_getdata_1_2_top2(a0)
      hOut = a0.clone
      ary = Array.new()
      a0["Data"].keys.each do |trait_code|
          pr = a0["Data"][trait_code]["pr"]
          ary.push([trait_code,pr])
      end
      ary.sort! {|a, b| b[1] <=> a[1]} 
      a = ary[0,2]
      hOut["top2_trait_code"] = [a[0][0],a[1][0]]
      hOut["top2_pr"] = [a[0][1],a[1][1]]
      return hOut
  end
    # 效標定義：<標題內容>
    # 效標分數說明：<觀察要點內容>
    # 分數落點1：<高低特徵>
    # 項目名稱內容：<高分者特徵>
    # 建議方案：<低分者特徵>
    # 分數落點2：<小小叮嚀>
    # 項目名稱內容：<叮嚀1>
    # 分數落點3：<小小叮嚀>
    # 項目名稱內容：<叮嚀2>@@<叮嚀3>
    # 註：@@分段記號
    # 
    # 小小叮嚀  排列說明：
    # 我 <叮嚀1>（第一高分內容）
    # 我 <叮嚀2>（第一高分內容）
    # 我 <叮嚀2>（第二高分內容）
    # 我是個 <叮嚀3>（第一高分內容） 和 <叮嚀3>（第二高分內容）
  def sti_getdata_1_2_desc(a0)
      hOut = a0.clone

      a0["Data"].keys.each do |trait_code|
          trait_desc = a0["Data"][trait_code]["trait_desc"]
          a = trait_desc.split("@@")
          # 效標定義：<標題內容>
          # hOut["Data"][trait_code]["title_content"] = a
          # 效標分數說明：<觀察要點內容>
          # hOut["Data"][trait_code]["observed_content"] = a0["Data"][trait_code]["trait_score_desc"]
          # 項目名稱內容：<高分者特徵>
          # hOut["Data"][trait_code]["high_score_feature"] = a0["Data"][trait_code]["trait_def"]["score_drop_1"]["trait_content"]
          # 建議方案：<低分者特徵>
          # hOut["Data"][trait_code]["low_score_feature"] = a0["Data"][trait_code]["trait_def"]["score_drop_1"]["proposal"]
          # 分數落點2：<小小叮嚀>
          remind = a0["Data"][trait_code]["trait_def"]["score_drop_2"]["trait_content"]
          b = remind.split("@@")
          # b[0] = b[0].split(" ")
          # b[1] = b[1].split(" ")
          b[2].strip!
          hOut["Data"][trait_code]["remind"] = b

          hOut["Data"][trait_code].delete("trait_desc")
          hOut["Data"][trait_code].delete("trait_score_desc")
          hOut["Data"][trait_code].delete("trait_def")
      end

      return hOut
  end

  def sti_getdata_1_3(nPanType,hUserInfo,hUserDefData)
    # hOut = sti_getdata_1_1_by_pantype(0,hUserInfo,hUserDefData)
    hOut = sti_getdata_1_1_by_pantype2(nPanType,hUserInfo,hUserDefData)
    hOut = sti_getdata_1_3_desc(hOut)
    return hOut
  end
  def sti_getdata_1_3_desc(a0)
      hOut = a0.clone

      a0["Data"].keys.each do |trait_code|
          hOut["Data"][trait_code].delete("trait_desc")
          hOut["Data"][trait_code].delete("trait_score_desc")
          hOut["Data"][trait_code].delete("trait_def")
      end

      return hOut
  end

  def sti_getdata_1_1_by_pantype2(nPanType,hUserInfo,hUserDefData)
      a_rate = hUserDefData["flow_rate"]
      a_rate = get_ts_default_rate(nPanType) if a_rate == nil || a_rate == "" || a_rate == [] || a_rate.length != 3
      if nPanType == 0 then # 本命
          hOut = sti_getdata_1_1_by_pantype(nPanType,hUserInfo,hUserDefData)
      elsif nPanType == 1 then # 十年
          a0 = sti_getdata_1_1_by_pantype(0,hUserInfo,hUserDefData) 
          a1 = sti_getdata_1_1_by_pantype(1,hUserInfo,hUserDefData) 
          # hOut = sti_getdata_1_1_out(a0,a1,nil,40,60,0)
          hOut = sti_getdata_1_1_out(a0,a1,nil,a_rate[0],a_rate[1],a_rate[2])
      elsif (nPanType == 2) then # 流年
          a0 = sti_getdata_1_1_by_pantype(0,hUserInfo,hUserDefData) 
          a1 = sti_getdata_1_1_by_pantype(1,hUserInfo,hUserDefData) 
          a2 = sti_getdata_1_1_by_pantype(2,hUserInfo,hUserDefData) 
          # hOut = sti_getdata_1_1_out(a0,a1,a2,30,50,20)
          hOut = sti_getdata_1_1_out(a0,a1,a2,a_rate[0],a_rate[1],a_rate[2])
      elsif (nPanType == 3) then # 流月
          a0 = sti_getdata_1_1_by_pantype(0,hUserInfo,hUserDefData) 
          a1 = sti_getdata_1_1_by_pantype(2,hUserInfo,hUserDefData) 
          a2 = sti_getdata_1_1_by_pantype(3,hUserInfo,hUserDefData) 
          # hOut = sti_getdata_1_1_out(a0,a1,a2,30,50,20)
          hOut = sti_getdata_1_1_out(a0,a1,a2,a_rate[0],a_rate[1],a_rate[2])
      end
      return hOut
  end
  # 1.4  E十二門課堂指標
  def sti_getdata_1_4(nPanType,hUserInfo,hUserDefData)
    # hOut = sti_getdata_1_1_by_pantype(0,hUserInfo,hUserDefData)
    # 2021/4/27討論，先不用加權
    # pr_max = hOut["Data"][hOut["trait_code_max_pr"]]["pr"]
    # pr_base = sti_new_pr_base(pr_max)
    # hOut["Data"].keys.each do |tc|
    #     pr = hOut["Data"][tc]["pr"]
    #     hOut["Data"][tc]["pr"] = (pr / pr_base * 100).round(2)
    #     hOut["Data"][tc]["pr_org"] = pr
    # end
    hOut = sti_getdata_1_1_by_pantype2(nPanType,hUserInfo,hUserDefData)

    return hOut
  end
# （1）PR值達 98含以上，乘上指數 1.001
# （2）PR值達 96含～98不含，乘上指數 1.01
# （3）PR值達 90含～95不含，乘上指數 1.03
# （4）PR值達 81含～90不含，乘上指數 1.05
# （5）PR值達 61含～80不含，乘上指數 1.1
# （6）PR值達 60不含以下，乘上指數 1.15
  def sti_new_pr_base(pr)
    prs = [98.0,96.0,90.0,81.0,61.0,0.00]
    ws = [1.001,1.01,1.03,1.05,1.1,1.15]
    prs.each_with_index do |pr_1,i|
        return (ws[i] * pr).round(2) if pr >= pr_1
    end
    return (pr * ws[ws.length - 1]).round(2)
  end

  def sti_getdata_2_trait_codes(nPanType,hUserInfo,hUserDefData)
    hUserDefData["trait_codes"] = hUserDefData["trait_codes1"]
    # hOut = sti_getdata_1_1_by_pantype(0,hUserInfo,hUserDefData)
    hOut = sti_getdata_1_1_by_pantype2(nPanType,hUserInfo,hUserDefData)

    hUserDefData["trait_codes"] = hUserDefData["trait_codes2"]
    # hOut2 = sti_getdata_1_1_by_pantype(0,hUserInfo,hUserDefData)
    hOut2 = sti_getdata_1_1_by_pantype2(nPanType,hUserInfo,hUserDefData)
    hOut["Data2"] = hOut2["Data"]
    
    return hOut
  end
  def sti_getdata_1_5(nPanType,hUserInfo,hUserDefData)
    hOut = sti_getdata_2_trait_codes(nPanType,hUserInfo,hUserDefData)

    hOut = sti_getdata_1_5_sort(hOut)
    ary = hOut["trait_code_pr_sorted"].select {|a| a[1] >= 85.0} 
    # hOut["top3_pr_great_85"] = ary[0,3]
    a = ary[0,3]
    b = []
    a.each do |ar|
        b.push(hOut["Data"][ar[0]]["desc"])
    end
    hOut["top3_pr_great_85"] = b

    return hOut
  end
  def sti_getdata_1_6(nPanType,hUserInfo,hUserDefData)
    hOut = sti_getdata_2_trait_codes(nPanType,hUserInfo,hUserDefData)
    hOut = sti_getdata_1_5_sort(hOut)

    ary = hOut["trait_code_pr_sorted"]
    b = []
    ary.each do |ar|
        b.push(hOut["Data"][ar[0]]["desc"])
    end
    hOut["trait_code_pr_sorted"] = b

    return hOut
  end
  def sti_getdata_1_5_sort(a0)
      hOut = a0.clone
      ary = Array.new()
      a0["Data"].keys.each do |trait_code|
          pr = a0["Data"][trait_code]["pr"]
          ary.push([trait_code,pr])
      end
      ary.sort! {|a, b| b[1] <=> a[1]} 
      hOut["trait_code_pr_sorted"] = ary
      return hOut
  end

  def sti_getdata(nPanType,hUserInfo,hUserDefData)
      hOut = {}
      hUdt = hUserDefData.clone
      hUdt["category_level"] = "MP"
      hUdt["categorys"] = []
      wc_get_pars_setting_all(hUdt["my_current_lang"])
      wc_get_talent_all(hUdt["my_current_lang"])
      (0..nPanType).each do |pan_type|
          hOut["pan_type_#{pan_type}"] = sti_getdata_each_pantype(pan_type,hUserInfo,hUdt)
      end
      return hOut
  end
  def sti_getdata_each_pantype(pan_type,hUserInfo,hUserDefData)
      hOut = {}
      hOut["all"] = {}
      # wc_init_Scores()
      hScore,hDetail = getScore(pan_type,hUserInfo,hUserDefData)
      hOut["base"] = {}
      hOut["base"]["L1"] = sti_getdata_score(@Score_L1,@talent_L1,@par_setting_L1,"L1")
      hOut["all"].merge!(hOut["base"]["L1"]) { |key, v1, v2| v2 }
      hOut["base"]["L2"] = sti_getdata_score(@Score_L2,@talent_L2,@par_setting_L2,"L2")
      hOut["all"].merge!(hOut["base"]["L2"]) { |key, v1, v2| v2 }
      hOut["base"]["L3"] = sti_getdata_score(@Score_L3,@talent_L3,@par_setting_L3,"L3")
      hOut["all"].merge!(hOut["base"]["L3"]) { |key, v1, v2| v2 }

      hOut["business"] = {}
      hOut["business"]["MP"] = sti_getdata_score(@Score_MP,@talent_MP,@par_setting_MP,"MP")
      hOut["all"].merge!(hOut["business"]["MP"]) { |key, v1, v2| v2 }

      return hOut
  end
  def sti_getdata_score(hScore,h_talent,h_ParSetting,category_level)
      hOut = {}
      hOutDetail = {}
      @count = 1 if @count == nil
      number = 1
      h_ParSetting.keys.each do |category_id|
          h_ParSetting[category_id].keys.each do |item_id|
              h_ParSetting[category_id][item_id].keys.each do |trait_id|                    
                  trait_code = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["trait_code"]
                  trait_desc = h_talent["category"][category_id]["item"][item_id]["trait"][trait_id]["desc"]
                  hOut[trait_code] = {}
                  hOut[trait_code]["number"] = number
                  hOut[trait_code]["count"] = @count
                  hOut[trait_code]["trait_code"] = trait_code
                  hOut[trait_code]["score"] = hScore[category_id][item_id][trait_id]["score"]
                  hOut[trait_code]["pr"] = hScore[category_id][item_id][trait_id]["pr"]
                  hOut[trait_code]["trait_desc"] = trait_desc
                  hOut[trait_code]["category_level"] = category_level
                  @count += 1
                  number += 1
              end
          end
      end
      return hOut
  end

  # 1.主要內容
      # ATA17_配合度 (L原代碼 A17_配合度)
      # ATA18_耐心度 (L原代碼 A18_耐心度)
      # ATB21_抗壓能力 (L原代碼 B21_抗壓能力)
      # ATB27_企圖慾望 (L原代碼 B27_企圖慾望)
      # ATC32_學習效率 (L原代碼 C32_學習效率)
      # ATC34_記憶能力 (L原代碼 C34_記憶能力)
      # ATD15_開拓嘗新 (L原代碼 D15_開拓嘗新)
      # ATD54_領導才華 (L原代碼 D54_領導才華)
  # 
  # 
  # 2.本命基本數值及PR值及年度曲線PR值
      # 2.1基本數值及PR值
      # 2.2年度曲線PR值預設為今年(國曆1/1~12/31日購買) ，若國曆元月輸出次年之年度PR值。
  # 
  # 3.本質內容
      # 3.1 指標意義
      # 3.2 定義
      # 3.3 指標判讀說明
      # 3.4 引導建言及注意事項
  def sti_api_mp_1(nPanType,hUserInfo,hUdt,bNeedDrop=true)
    hUserDefData = hUdt.clone
    trait_codes = ["ATA17","ATA18","ATB21","ATB27","ATC32","ATC33","ATD15","ATD54"]
    hUserDefData["trait_codes"] = trait_codes
    @bNeedDrop = bNeedDrop
    # hOut = sti_getdata_1_1_by_pantype(nPanType,hUserInfo,hUserDefData)
    hOut = sti_getdata_1_1(nPanType,hUserInfo,hUserDefData)
    hOut["final_success"] = true

    return hOut
  end

  # 1.7  多元智能
  def sti_api_mp_2(nPanType,hUserInfo,hUdt,bNeedDrop=true)
    hUserDefData = hUdt.clone
    trait_codes = ["MM91","MM92","MM93","MM94","MM95","MM96","MM97","MM98","MM99"]
    hUserDefData["trait_codes"] = trait_codes
    @bNeedDrop = bNeedDrop
    # hOut = sti_getdata_1_1_by_pantype(nPanType,hUserInfo,hUserDefData)
    hOut = sti_getdata_1_1(nPanType,hUserInfo,hUserDefData)
    hOut["final_success"] = true

    return hOut
  end

end
