
class Xuexi_Ziwei
  # 正在練習的級別
  def Xuexi_Ziwei.next_class_item(user_id,ap_name)
    ifate_pars = Xuexi_Ziwei.find_from_ifatepar(user_id,ap_name)
    if (ifate_pars["xuexi_class_muqian"] == ifate_pars["xuexi_class"]) then
      if (ifate_pars["xuexi_item_muqian"] == ifate_pars["xuexi_item"]) then
        Xuexi_Ziwei.next_muqian_class_item(user_id,ap_name)
        ifate_pars = Xuexi_Ziwei.find_from_ifatepar(user_id,ap_name)
      end
    end

    if (ifate_pars["xuexi_item"] < Xuexi_Ziwei.itemcount_in_class(ifate_pars["xuexi_class"])) then
      ifate_pars["xuexi_item"] += 1
    else
      if (ifate_pars["xuexi_class"] < ifate_pars["xuexi_class_muqian"]) then
        ifate_pars["xuexi_class"] += 1
        ifate_pars["xuexi_item"] = 1
      end
    end
    Xuexi_Ziwei.save_to_ifatepar(user_id,ap_name,ifate_pars)
  end
  # 目前已到達的級別
  def Xuexi_Ziwei.next_muqian_class_item(user_id,ap_name)
    ifate_pars = Xuexi_Ziwei.find_from_ifatepar(user_id,ap_name)
    if (ifate_pars["xuexi_item_muqian"] < Xuexi_Ziwei.itemcount_in_class(ifate_pars["xuexi_class_muqian"])) then
      ifate_pars["xuexi_item_muqian"] += 1
    else
      if (ifate_pars["xuexi_class_muqian"] < Xuexi_Ziwei.all_class_count(ap_name)) then
        ifate_pars["xuexi_class_muqian"] += 1
        ifate_pars["xuexi_item_muqian"] = 1
      end
    end
    Xuexi_Ziwei.save_to_ifatepar(user_id,ap_name,ifate_pars)
  end
  def Xuexi_Ziwei.ap_name()
    return "xuexi_ziwei"
  end
  def Xuexi_Ziwei.lunduan_ap_name()
    return "xuexi_ziweilunduan"
  end
  def Xuexi_Ziwei.pan_ap_name()
    return "xuexi_ziweipan"
  end
  def Xuexi_Ziwei.pars_ap_name()
    return "xuexi_ziweipars"
  end
  def Xuexi_Ziwei.find_from_ifatepar(user_id,ap_name)
    if (user_id == nil) then 
      user_id = 0
    end
    ifatepar = IfatePar.check_userid(user_id).check_apname(ap_name).last
    if (ifatepar == nil) then
      ifatepar = IfatePar.new
      ifatepar.user_id = user_id
      ifatepar.ap_name = ap_name
      ifatepar.hPars = Xuexi_Ziwei.init_pars(ap_name)
    end
    return ifatepar.hPars
  end
  def Xuexi_Ziwei.init_pars(ap_name)
    if (ap_name == Xuexi_Ziwei.ap_name()) then
      return {"xuexi_class" => 1,"xuexi_item" => Xuexi_Ziwei.itemcount_in_class(1),"xuexi_class_muqian" => 1, "xuexi_item_muqian" => Xuexi_Ziwei.itemcount_in_class(1)}
    elsif (ap_name == Xuexi_Ziwei.pars_ap_name()) then
      return {"xuexi_class" => 2,"xuexi_item" => 1,"xuexi_class_muqian" => 2, "xuexi_item_muqian" => Xuexi_Ziwei.itemcount_in_class(2)}
    elsif (ap_name == Xuexi_Ziwei.pan_ap_name()) then
      return {"xuexi_class" => 0,"xuexi_item" => 1,"xuexi_class_muqian" => 0, "xuexi_item_muqian" => Xuexi_Ziwei.itemcount_in_class(0)}
    elsif (ap_name == Xuexi_Ziwei.lunduan_ap_name()) then
      return {"xuexi_class" => 3,"xuexi_item" => Xuexi_Ziwei.itemcount_in_class(3),"xuexi_class_muqian" => 3, "xuexi_item_muqian" => Xuexi_Ziwei.itemcount_in_class(3)}
    end
  end
  def Xuexi_Ziwei.save_to_ifatepar(user_id,ap_name,hParAll)
    if (user_id == nil) then 
      user_id = 0
    end
    ifatepar = IfatePar.check_userid(user_id).check_apname(ap_name).last
    if (ifatepar == nil) then
      ifatepar = IfatePar.new
      ifatepar.user_id = user_id
      ifatepar.ap_name = ap_name
    end
    ifatepar.hPars = hParAll
    ifatepar.save!
  end
  def Xuexi_Ziwei.rand_timu(timu_names)
    timus = Xuexi_Ziwei.make_timus(timu_names)
    i = timu_names.index("timu_panright")
    if (i == nil) then
      n = rand(timus.length)
      timu = timus[n]
    else
      # 2/3的機會給pan
      if (rand(3) < 2) then
        timu = timus[i]
      else
        n = rand(timus.length)
        timu = timus[n]
      end
    end
    return timu[0],timu[1]
  end
  def Xuexi_Ziwei.make_timus(timu_names)
    a = []
    timu_names.each_with_index do |timu_name,i|
      a.push(Xuexi_Ziwei.find_timu(timu_name))
    end
    return a
  end
  def Xuexi_Ziwei.find_timu(timu_name)
    all_timus = [["timu_is","radiobox"],["timu_notis","radiobox"],
              ["timu_in","checkbox"],["timu_notin","checkbox"],
              ["timu_right","radiobox"],["timu_wrong","radiobox"],
              ["timu_panright","radiobox"],["timu_panwrong","radiobox"]
            ]
    all_timus.each_with_index do |timu,i|
      if (timu[0] == timu_name) then
        return timu
      end
    end
    return ["timu_is","radiobox"]
  end
  def Xuexi_Ziwei.timu_is(q1,xuaxiongs,notxuaxiongs)
    h = {}
    h["timu_taitou"] = Pm.t("xuexi_ziwei.timu.is",:q1 => q1)
    h["xuaxiongs"],h["xuaxions_is"],h["xuaxions_notis"] = Xuexi_Ziwei.make_timu_xuaxiong(xuaxiongs,1,notxuaxiongs,3)
    h["all_xuaxiongs"] = xuaxiongs
    h["all_notxuaxiongs"] = notxuaxiongs
    h["right_xuaxions"] = h["xuaxions_is"]

    return h
  end
  def Xuexi_Ziwei.timu_jieguo(hPars,answers)
    all_xuaxiongs = hPars["all_xuaxiongs"]
    xuaxiongs = hPars["xuaxiongs"]
    # right_xuaxions = xuaxiongs & all_xuaxiongs
    right_xuaxions = hPars["right_xuaxions"]

    ans = answers
    # ans = []
    # answers.each_with_index do |answer,i|
    #   ans[i] = xuaxiongs[answer - 1]
    # end

    return ans.sort == right_xuaxions.sort,ans,right_xuaxions
  end
  def Xuexi_Ziwei.timu_notis(q1,xuaxiongs,notxuaxiongs)
    h = {}
    h["timu_taitou"] = Pm.t("xuexi_ziwei.timu.notis",:q1 => q1)
    h["xuaxiongs"],h["xuaxions_is"],h["xuaxions_notis"] = Xuexi_Ziwei.make_timu_xuaxiong(xuaxiongs,3,notxuaxiongs,1)
    h["all_xuaxiongs"] = xuaxiongs
    h["all_notxuaxiongs"] = notxuaxiongs
    h["right_xuaxions"] = h["xuaxions_notis"]

    return h
  end
  def Xuexi_Ziwei.timu_in(q1,xuaxiongs,notxuaxiongs)
    h = {}
    h["timu_taitou"] = Pm.t("xuexi_ziwei.timu.in",:q1 => q1)
    xuaxiongs_want = rand(4)
    xuaxiongs_want += 1 if xuaxiongs_want == 0
    xuaxiongs_want = xuaxiongs.length if xuaxiongs_want > xuaxiongs.length
    notxuaxiongs_want = 4 - xuaxiongs_want
    h["xuaxiongs"],h["xuaxions_is"],h["xuaxions_notis"] = Xuexi_Ziwei.make_timu_xuaxiong(xuaxiongs,xuaxiongs_want,notxuaxiongs,notxuaxiongs_want)
    h["all_xuaxiongs"] = xuaxiongs
    h["all_notxuaxiongs"] = notxuaxiongs
    h["right_xuaxions"] = h["xuaxions_is"]
    return h
  end
  def Xuexi_Ziwei.timu_notin(q1,xuaxiongs,notxuaxiongs)
    h = {}
    h["timu_taitou"] = Pm.t("xuexi_ziwei.timu.notin",:q1 => q1)
    xuaxiongs_want = rand(4)
    xuaxiongs_want += 1 if xuaxiongs_want == 0
    xuaxiongs_want = xuaxiongs.length if xuaxiongs_want > xuaxiongs.length
    notxuaxiongs_want = 4 - xuaxiongs_want
    h["xuaxiongs"],h["xuaxions_is"],h["xuaxions_notis"] = Xuexi_Ziwei.make_timu_xuaxiong(xuaxiongs,xuaxiongs_want,notxuaxiongs,notxuaxiongs_want)
    h["all_xuaxiongs"] = xuaxiongs
    h["all_notxuaxiongs"] = notxuaxiongs
    h["right_xuaxions"] = h["xuaxions_notis"]
    return h
  end
  def Xuexi_Ziwei.timu_right(q1,xuaxiongs,notxuaxiongs)
    h = {}
    h["timu_taitou"] = Pm.t("xuexi_ziwei.timu.right",:q1 => q1)
    xuaxiongs_want = 1
    notxuaxiongs_want = 4 - xuaxiongs_want
    h["xuaxiongs"],h["xuaxions_is"],h["xuaxions_notis"] = Xuexi_Ziwei.make_timu_xuaxiong(xuaxiongs,xuaxiongs_want,notxuaxiongs,notxuaxiongs_want)
    h["all_xuaxiongs"] = xuaxiongs
    h["all_notxuaxiongs"] = notxuaxiongs
    h["right_xuaxions"] = h["xuaxions_is"]
    return h
  end
  def Xuexi_Ziwei.timu_wrong(q1,xuaxiongs,notxuaxiongs)
    h = {}
    h["timu_taitou"] = Pm.t("xuexi_ziwei.timu.wrong",:q1 => q1)
    xuaxiongs_want = 3
    notxuaxiongs_want = 4 - xuaxiongs_want
    h["xuaxiongs"],h["xuaxions_is"],h["xuaxions_notis"] = Xuexi_Ziwei.make_timu_xuaxiong(xuaxiongs,xuaxiongs_want,notxuaxiongs,notxuaxiongs_want)
    h["all_xuaxiongs"] = xuaxiongs
    h["all_notxuaxiongs"] = notxuaxiongs
    h["right_xuaxions"] = h["xuaxions_notis"]
    return h
  end
  def Xuexi_Ziwei.timu_panright(q1,xuaxiongs,notxuaxiongs)
    h = {}
    h["timu_taitou"] = Pm.t("xuexi_ziwei.timu.panright",:q1 => q1)
    xuaxiongs_want = 1
    notxuaxiongs_want = 4 - xuaxiongs_want
    h["xuaxiongs"],h["xuaxions_is"],h["xuaxions_notis"] = Xuexi_Ziwei.make_timu_xuaxiong(xuaxiongs,xuaxiongs_want,notxuaxiongs,notxuaxiongs_want)
    h["all_xuaxiongs"] = xuaxiongs
    h["all_notxuaxiongs"] = notxuaxiongs
    h["right_xuaxions"] = h["xuaxions_is"]
    return h
  end
  def Xuexi_Ziwei.timu_panwrong(q1,xuaxiongs,notxuaxiongs)
    h = {}
    h["timu_taitou"] = Pm.t("xuexi_ziwei.timu.panwrong",:q1 => q1)
    xuaxiongs_want = 3
    notxuaxiongs_want = 4 - xuaxiongs_want
    h["xuaxiongs"],h["xuaxions_is"],h["xuaxions_notis"] = Xuexi_Ziwei.make_timu_xuaxiong(xuaxiongs,xuaxiongs_want,notxuaxiongs,notxuaxiongs_want)
    h["all_xuaxiongs"] = xuaxiongs
    h["all_notxuaxiongs"] = notxuaxiongs
    h["right_xuaxions"] = h["xuaxions_notis"]
    return h
  end
  def Xuexi_Ziwei.make_timu_xuaxiong(xuaxiongs,xuaxiongs_want,notxuaxiongs,notxuaxiongs_want)
    xuaxiong_nos,notxuaxiong_nos = Xuexi_Ziwei.make_random_nos(xuaxiongs.length,xuaxiongs_want,notxuaxiongs.length,notxuaxiongs_want)
    a = Array.new(xuaxiongs_want + notxuaxiongs_want)
    xuaxions_is = []
    xuaxiong_nos.each_with_index do |xuaxiong_no,i|
      if (xuaxiong_no[1] == nil) then
        a[xuaxiong_no[0]] = ""
      else
        a[xuaxiong_no[0]] = xuaxiongs[xuaxiong_no[1]]
      end
      xuaxions_is.push(xuaxiong_no[0] + 1)
    end
    xuaxions_notis = []
    notxuaxiong_nos.each_with_index do |notxuaxiong_no,i|
      if (notxuaxiong_no[1] == nil) then
        a[notxuaxiong_no[0]] = ""
      else
        a[notxuaxiong_no[0]] = notxuaxiongs[notxuaxiong_no[1]]
      end
      xuaxions_notis.push(notxuaxiong_no[0] + 1)
    end
    return a,xuaxions_is,xuaxions_notis
  end
  def Xuexi_Ziwei.make_random_nos(maxno1,no1want,maxno2,no2want)
    maxnowant = no1want + no2want
    # 預防 maxno1 小於 no1want，題目變少
    if (no1want > maxno1) then
      no1want = maxno1
    end
    no2want = maxnowant - no1want
    a1 = Xuexi_Ziwei.make_random_nos2(maxno1,no1want)
    a2 = Xuexi_Ziwei.make_random_nos2(maxno2,no2want)

    pos1 = Xuexi_Ziwei.make_random_nos2(maxnowant,no1want)
    pos2 = (0...maxnowant).to_a - pos1

    a1_out = []
    pos1.each_with_index do |pos,i|
      if (a1.length >= i + 1) then
        a1_out.push([pos,a1[i]])
      else
        a1_out.push([pos,nil])
      end
    end
    a2_out = []
    pos2.each_with_index do |pos,i|
      if (a2.length >= i + 1) then
        a2_out.push([pos,a2[i]])
      else
        a2_out.push([pos,nil])
      end
    end
    return a1_out,a2_out
  end
  def Xuexi_Ziwei.make_random_nos2(maxno,nowant)
    a = []
    while (a.length < nowant && a.length < maxno)
      newno = rand(maxno)
      if (!a.include?(newno)) then
        a.push(newno)
      end
    end
    return a
  end

  def Xuexi_Ziwei.parse_answers(answers)
    ans = eval("[#{answers}]")
    ans.map! {|n| n.to_i}
    return ans
  end

  def Xuexi_Ziwei.timu_data_timestamp(xuexi_class,xuexi_item)
    if ([xuexi_class,xuexi_item] == [1,23]) then
      return Xuexi_Ziwei.rand_xia_timestamp()
    elsif ([xuexi_class,xuexi_item] == [1,22]) then
      return Xuexi_Ziwei.rand_jia_timestamp()
    else
      return Xdate.make_now_timestamp_sex()
    end
  end
  # 紫微斗數教學互動單元
  #
  # 程式判說讀：星曜資料庫以生剋為主（不依五行為主）。
  #
  # 盤別：本命盤
  #
  # 級別1：基本術語及專有名詞介紹
  #
  # 學習目標：紫微斗數基本上之常用之專業名詞或術語，本單元主要以紫微斗數常用之術語，進入紫微斗數之第一步。
  #
  # 內容：
  # 各關題目
  # 天干：（學習效果：了解十天干在命盤位置，須背記。）
  def Xuexi_Ziwei.timu_data_1_1(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    # timu_names = ["timu_is","timu_notis","timu_in","timu_notin"]
    timu_names1 = ["timu_is","timu_in"]
    timu_names2 = ["timu_right","timu_wrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
      q1 = Pm.t("xuexi_ziwei.tiangan.taitou")
      xuaxiongs = Xuexi_Ziwei.tiangan_data()
      notxuaxiongs = Xuexi_Ziwei.dizhi_data()
    else
      q1,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.timu_data_1_1_xuaxiongs(hPars,oStar,nPanType)
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.tiangan.taitou")
    h["timu_api"] = "timu" # timu
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 地支：（學習效果：了解十二地支在命盤位置，須背記。）
  def Xuexi_Ziwei.timu_data_1_2(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = ["timu_is"]
    timu_names2 = ["timu_right","timu_wrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
      q1 = Pm.t("xuexi_ziwei.dizhi.taitou")
      xuaxiongs = Xuexi_Ziwei.dizhi_data()
      notxuaxiongs = Xuexi_Ziwei.tiangan_data()
    else
      q1,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.timu_data_1_2_xuaxiongs(hPars,oStar,nPanType)
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.dizhi.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 五行生剋：（學習效果：了解五行生剋，有助於星曜之在各宮位之強弱判斷依據，須背記。）
  def Xuexi_Ziwei.timu_data_1_3(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = ["timu_is","timu_notis"]
    timu_names2 = ["timu_right","timu_wrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
      q1 = Pm.t("xuexi_ziwei.wuxing.taitou")
      xuaxiongs = Xuexi_Ziwei.wuxing_data()
      notxuaxiongs = Xuexi_Ziwei.tiangan_data() + Xuexi_Ziwei.dizhi_data()
    else
      q1 = Pm.t("xuexi_ziwei.wuxing.shengke.taitou")
      xuaxiongs = Xuexi_Ziwei.wuxing_all_sheng_ke_strs()
      notxuaxiongs = Xuexi_Ziwei.wuxing_all_wrong_sheng_ke_strs()
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.wuxing.shengke.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end

  # 五行旺相休囚絕：（學習效果：了解五行之在各春夏秋冬時強弱。）
  def Xuexi_Ziwei.timu_data_1_4(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = []
    timu_names2 = ["timu_right","timu_wrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
    else
      q1 = Pm.t("xuexi_ziwei.wuxing.wang_xiang_xiu_qiu_si.taitou")
      xuaxiongs = Xuexi_Ziwei.wuxing_all_wang_xiang_xiu_qiu_si_strs()
      notxuaxiongs = Xuexi_Ziwei.wuxing_all_wrong_wang_xiang_xiu_qiu_si_strs()
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.wuxing.wang_xiang_xiu_qiu_si.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 六十花甲納音歌：（學習效果：了解天干與地支組合成六十花納甲之五行，須背記。）
  def Xuexi_Ziwei.timu_data_1_5(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = ["timu_is","timu_notis","timu_notin"]
    timu_names2 = ["timu_right","timu_wrong","timu_in"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
      q1 = Pm.t("xuexi_ziwei.liu_shi_na_yin.taitou") + Pm.t("xuexi_ziwei.ganzhi.taitou")
      xuaxiongs = Xuexi_Ziwei.all_ganzhi_strs()
      notxuaxiongs = Xuexi_Ziwei.all_wrong_ganzhi_strs()
    else
      q1 = Pm.t("xuexi_ziwei.liu_shi_na_yin.taitou")
      xuaxiongs = Xuexi_Ziwei.all_liu_shi_na_yin_strs()
      notxuaxiongs = Xuexi_Ziwei.all_wrong_liu_shi_na_yin_strs()
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.liu_shi_na_yin.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end

  # 主星：（學習效果：了解基本定義或說明。）
  # 紫微星系：紫微、天機、太陽、武曲、天同、廉貞共有六顆。
  # 天府星系：天府、太陰、貪狼、巨門、天相、天梁、七殺、破軍共有八顆。
  # 六煞星或煞星：羊刃（擎羊星）、陀羅、火星、鈴星、天空、地劫，對凶星的威力有加強的作用，對吉星有減弱吉星效果。
  # 六吉星或吉星：文昌、文曲、天魁、天鉞、左輔、右弼，對凶星的威力有減弱的作用，對吉星有增強吉星效果。
  def Xuexi_Ziwei.timu_data_1_6(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = ["timu_is","timu_notis","timu_in","timu_notin"]
    timu_names2 = []
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.zhuxingxi_xuaxiong()
      q1 = Pm.t("xuexi_ziwei.xing.#{q1key}")
    else
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.xing.zhuxing")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end

  # 星、曜：（學習效果：了解基本定義或說明。）
  # 北斗主星：（學習效果：了解基本定義或說明。）
  # 北斗助星：（學習效果：了解基本定義或說明。）
  # 南斗主星：（學習效果：了解基本定義或說明。）
  # 南斗助星：（學習效果：了解基本定義或說明。）
  # 中天主星：（學習效果：了解基本定義或說明。）
  # 中天助星：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_7(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = ["timu_is","timu_in","timu_notin"]
    timu_names2 = []
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.xingyao_xuaxiong()
      q1 = Pm.t("xuexi_ziwei.xing.#{q1key}")
    else
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.xing.xingyao")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end

  # 四化：（學習效果：了解四化星有那些，天干對應各四化星曜，須背記、熟記。）
  def Xuexi_Ziwei.timu_data_1_8(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = ["timu_is","timu_notis","timu_in","timu_notin"]
    timu_names2 = ["timu_right","timu_wrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.sihua_xuaxiong()
      q1 = Pm.t("xuexi_ziwei.sihua.#{q1key}")
    else
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.sihua_xuaxiong2()
      q1 = Pm.t("xuexi_ziwei.sihua.#{q1key}")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.sihua.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end

  # 十二宮：（學習效果：了解基本定義或說明。）
  # 本宮：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_9(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = ["timu_is","timu_notis","timu_in","timu_notin"]
    timu_names2 = ["timu_right","timu_wrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.shiergong_xuaxiong()
      q1 = Pm.t("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    else
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.shiergong_xuaxiong2("bengong")
      q1 = Pm.t("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 對宮：（學習效果：了解基本定義或說明，須背記。）
  def Xuexi_Ziwei.timu_data_1_10(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = []
    timu_names2 = ["timu_right","timu_wrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
    else
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.shiergong_xuaxiong2("duigong")
      q1 = Pm.t("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 鄰宮、兩鄰宮：（學習效：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_11(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = []
    timu_names2 = ["timu_right","timu_wrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
    else
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.shiergong_xuaxiong2("lingong")
      q1 = Pm.t("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end

  # 三合：（學習效果：了解基本定義或說明，須背記。）
  def Xuexi_Ziwei.timu_data_1_12(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = []
    timu_names2 = ["timu_right","timu_wrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
    else
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.shiergong_3634_xuaxiong2("sanhe",oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 六合：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_13(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = []
    timu_names2 = []
    timu_names3 = ["timu_panright","timu_panwrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2 + timu_names3)
    if (timu_names1.include?(timu)) then
    elsif (timu_names2.include?(timu)) then
    elsif (timu_names3.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.shiergong_3634_xuaxiong2("liuhe",oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 三方四正：（學習效果：了解基本定義或說明，須背記。）
  def Xuexi_Ziwei.timu_data_1_14(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = []
    timu_names2 = ["timu_right","timu_wrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2)
    if (timu_names1.include?(timu)) then
    else
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.shiergong_3634_xuaxiong2("sanfangsizheng",oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.shiergong.taitou.#{q1key}")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end

  # 宮干：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_15(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = []
    timu_names2 = []
    timu_names3 = ["timu_panright","timu_panwrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2 + timu_names3)
    if (timu_names1.include?(timu)) then
    elsif (timu_names2.include?(timu)) then
    elsif (timu_names3.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.gong_gdw_xuaxiong2("tiangan",oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.#{q1key}.taitou")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.#{q1key}.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 宮地支
  def Xuexi_Ziwei.timu_data_1_16(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = []
    timu_names2 = []
    timu_names3 = ["timu_panright","timu_panwrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2 + timu_names3)
    if (timu_names1.include?(timu)) then
    elsif (timu_names2.include?(timu)) then
    elsif (timu_names3.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.gong_gdw_xuaxiong2("dizhi",oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.#{q1key}.taitou")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.#{q1key}.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 宮五行
  def Xuexi_Ziwei.timu_data_1_17(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = []
    timu_names2 = []
    timu_names3 = ["timu_panright","timu_panwrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2 + timu_names3)
    if (timu_names1.include?(timu)) then
    elsif (timu_names2.include?(timu)) then
    elsif (timu_names3.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.gong_gdw_xuaxiong2("wuxing",oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.#{q1key}.taitou")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.#{q1key}.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end

  # 坐、守：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_18(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    return Xuexi_Ziwei.timu_data_1_18_21(hPars,xuexi_class,xuexi_item,oStar,nPanType,"zuoshou")
  end
  def Xuexi_Ziwei.timu_data_1_18_21(hPars,xuexi_class,xuexi_item,oStar,nPanType,which_func)
    timu_names1 = []
    timu_names2 = []
    timu_names3 = ["timu_panright","timu_panwrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2 + timu_names3)
    if (timu_names1.include?(timu)) then
    elsif (timu_names2.include?(timu)) then
    elsif (timu_names3.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.gong_xing_xuaxiong2(which_func,oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.xing.taitou.#{q1key}")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.xing.taitou.#{q1key}")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 會：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_19(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    return Xuexi_Ziwei.timu_data_1_18_21(hPars,xuexi_class,xuexi_item,oStar,nPanType,"hui")
  end
  # 照：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_20(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    return Xuexi_Ziwei.timu_data_1_18_21(hPars,xuexi_class,xuexi_item,oStar,nPanType,"zhao")
  end
  # 沖：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_21(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    return Xuexi_Ziwei.timu_data_1_18_21(hPars,xuexi_class,xuexi_item,oStar,nPanType,"chong")
  end
  # 同宮、同度、共守、同守：（學習效果：了解基本定義或說明。）
  # 不用
  def Xuexi_Ziwei.timu_data_1_22_no_use(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    return Xuexi_Ziwei.timu_data_1_18_21(hPars,xuexi_class,xuexi_item,oStar,nPanType,"gongshou")
  end
  # 夾：（學習效果：了解基本定義或說明。）
  # 太陰、太陽 ＝》日月夾   " 宮職 "
  # 羊刃、陀羅 ＝》羊陀夾   " 宮職 "
  # 天空、地劫 ＝》 空劫夾   " 宮職 "
  # 文昌、文曲 ＝》 昌曲夾   " 宮職 "
  # 左輔、右弼 ＝》 輔弼夾   " 宮職 "
  # 天魁、天鉞 ＝》 魁鉞夾   " 宮職 "
  # 化祿、化權 ＝》 祿權夾   " 宮職 "
  # 化祿、化科 ＝》 祿科夾   " 宮職 "
  # 化權、化科 ＝》 權科夾   " 宮職 "
  #
  # 特殊
  # 廉貞、羊刃 夾 天相 ＝》刑囚夾印  （不論何宮職）
  def Xuexi_Ziwei.timu_data_1_22(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = []
    timu_names2 = []
    timu_names3 = ["timu_panright"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2 + timu_names3)
    if (timu_names1.include?(timu)) then
    elsif (timu_names2.include?(timu)) then
    elsif (timu_names3.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs,hPars["timestamp"] = Xuexi_Ziwei.gong_xing_jia_xuaxiong2(oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.xing.taitou.#{q1key}")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.xing.taitou.#{q1key}")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end

  # 忌星包含範圍比較大，化忌、六煞、耗星（大小耗星、天刑）等。
  # 挾：忌星和煞星同宮或會照時，忌星便可挾煞星共同危害三方宮位。如流年忌煞相互會照時，謂之流年忌星「挾」煞攻入某宮；煞星組合若構成格局時，三方的主星便可「挾」成格的煞星共謀，該主星便因而「挾煞成格」也
  # 煞星（六煞、天刑、耗星）
  def Xuexi_Ziwei.timu_data_1_23(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = []
    timu_names2 = []
    timu_names3 = ["timu_panright"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2 + timu_names3)
    if (timu_names1.include?(timu)) then
    elsif (timu_names2.include?(timu)) then
    elsif (timu_names3.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs,hPars["timestamp"] = Xuexi_Ziwei.gong_xing_xia_xuaxiong2(oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.xing.taitou.#{q1key}")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.xing.taitou.#{q1key}")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 四生宮：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_24(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = ["timu_is","timu_notis","timu_in","timu_notin"]
    timu_names2 = ["timu_right"]
    timu_names3 = ["timu_panright","timu_panwrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2 + timu_names3)
    if (timu_names1.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.sishenggong_xuaxiong()
      q1 = Pm.t("xuexi_ziwei.gong.sishenggong.taitou")
    elsif (timu_names2.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.sishenggong_xuaxiong2(oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.sishenggong.taitou")
    elsif (timu_names3.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.sishenggong_xuaxiong3(oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.sishenggong.taitou")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.sishenggong.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 四墓宮：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_25(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = ["timu_is","timu_notis","timu_in","timu_notin"]
    timu_names2 = ["timu_right"]
    timu_names3 = ["timu_panright","timu_panwrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2 + timu_names3)
    if (timu_names1.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.simugong_xuaxiong()
      q1 = Pm.t("xuexi_ziwei.gong.simugong.taitou")
    elsif (timu_names2.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.simugong_xuaxiong2(oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.simugong.taitou")
    elsif (timu_names3.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.simugong_xuaxiong3(oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.simugong.taitou")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.simugong.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 四敗宮：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_26(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = ["timu_is","timu_notis","timu_in","timu_notin"]
    timu_names2 = ["timu_right"]
    timu_names3 = ["timu_panright","timu_panwrong"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2 + timu_names3)
    if (timu_names1.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.sibaigong_xuaxiong()
      q1 = Pm.t("xuexi_ziwei.gong.sibaigong.taitou")
    elsif (timu_names2.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.sibaigong_xuaxiong2(oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.sibaigong.taitou")
    elsif (timu_names3.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.sibaigong_xuaxiong3(oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.sibaigong.taitou")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.sibaigong.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 天羅地網：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.timu_data_1_27(hPars,xuexi_class,xuexi_item,oStar,nPanType)
    timu_names1 = ["timu_is"]
    timu_names2 = ["timu_right"]
    timu_names3 = ["timu_panright"]
    timu,object_type = Xuexi_Ziwei.rand_timu(timu_names1 + timu_names2 + timu_names3)
    if (timu_names1.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.tianluodiwang_xuaxiong()
      q1 = Pm.t("xuexi_ziwei.gong.tianluodiwang.taitou")
    elsif (timu_names2.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.tianluodiwang_xuaxiong2(oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.tianluodiwang.taitou")
    elsif (timu_names3.include?(timu)) then
      q1key,xuaxiongs,notxuaxiongs = Xuexi_Ziwei.tianluodiwang_xuaxiong3(oStar,nPanType)
      q1 = Pm.t("xuexi_ziwei.gong.tianluodiwang.taitou")
    end
    h = eval("Xuexi_Ziwei.#{timu}(q1,xuaxiongs,notxuaxiongs)")
    h["class_item_desc"] = Pm.GetStrWithQuote("xuexi_ziwei.gong.tianluodiwang.taitou")
    h["timu_api"] = "timu"
    h["object_type"] = object_type
    h["timestamp"] = hPars["timestamp"]
    return h
  end
  # 虛歲：（學習效果：了解基本定義或說明）
  # 太歲：（學習效果：了解基本定義或說明。）
  #
  # 紫微斗數排盤軟體編排架構介紹及使用：
  # （學習效果：了解排盤星曜位置，及排盤之功能，有良好之工具輔佐，會在論斷上能夠得心應用。）
  #
  # 級別2：主要單雙星介紹
  #
  # 學習目標：介紹紫微星系、天府星系、六吉星、六煞星、雙星、十二宮位基本定義等基本特性，及在十二宮之解說，主要引導學習星曜在十二宮之解釋及互動關係說明，了解紫微斗數運作觀念，進一步對星曜在各宮位之強弱判斷，做出最佳之解說。
  #
  # 內容：
  #
  # （十二宮定義說明）
  # 命宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 兄弟宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 夫妻宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 子女宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 財帛宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 疾厄宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 遷移宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 僕役宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 官祿宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 田宅宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 福德宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 父母宮：（學習效果：了解宮位之基本定義或說明，對於人事物方面如何區分，未來應用上很重要，須背記。）
  # 身宮：（學習效果：了解宮位之基本定義或說明。）
  # （主星星曜說明）資料庫以相生之說顯示，以十二宮說明。
  # 紫微星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 天機星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  #
  # 收費一：250元/月(以下收費項目開始)
  #
  # 太陽星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 武曲星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 天同星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 廉貞星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 天府星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 太陰星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 貪狼星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 巨門星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 天相星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 天梁星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 七殺星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 破軍星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 文昌星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 文曲星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 左輔星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 右弼星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 天魁星、天鉞星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 祿存星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 羊刃星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 陀羅星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 火星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 鈴星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  # 天空星、地劫星：（學習效果：了解星曜之基本定義或說明，對於人事物方面如何區分，須熟記、背記。）
  #
  # （宮氣運用說明）
  # 五行運作說明：（學習效果：了解五行之基本定義或說明及運用，須熟記、背記。）
  # 宮氣之說明：（學習效果：了解宮氣五行與星曜之間之關係說明，須熟記、背記。）
  # 星曜與宮氣互動說明：（學習效果：了解宮氣五行與星曜之間之關係說明及應用，須熟記、背記。）
  #
  # （星曜與體形介紹）
  # 星曜與體形介紹說明：（學習效果：了解星曜與人之長相、形體之間關係說明及應用，須熟記、背記。）
  #
  # （雙星在各宮介紹）
  #
  # 紫微天府：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 紫微貪狼：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 紫微天相：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 紫微七殺：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 紫微破軍：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 天機太陰：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 天機巨門：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 天機天梁：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 太陽太陰：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 太陽巨門：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 太陽天梁：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 武曲天府：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 武曲貪狼：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 武曲天相：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 武曲七殺：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 武曲破軍：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 天同太陰：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 天同巨門：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 天同天梁：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 廉貞天府：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 廉貞貪狼：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 廉貞天相：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 廉貞七殺：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  # 廉貞破軍：（學習效果：了解雙星曜解說及應用，須熟記、背記。）
  #
  # 收費二：680元／月
  # 
  #
  # 級別3：四化星介紹（以後再加）
  #
  # 學習目標：四化星基本之變化介紹，及疊宮解釋說明。
  #
  # 內容：
  #
  # 四化基本規則
  # 四化基本的意義
  # 各天干四化星性說明
  # 四化各宮表徵意涵


  def Xuexi_Ziwei.all_class_count(ap_name)
    if (ap_name == Xuexi_Ziwei.ap_name()) then
      return 1
    elsif (ap_name == Xuexi_Ziwei.pars_ap_name()) then
      return 2
    elsif (ap_name == Xuexi_Ziwei.pan_ap_name()) then
      return 0
    elsif (ap_name == Xuexi_Ziwei.lunduan_ap_name()) then
      return 3
    end
  end
  def Xuexi_Ziwei.itemcount_in_class(xuexi_class)
    if (xuexi_class == 0) then
      # ziwei pan
      return 20
    elsif (xuexi_class == 1) then
      return 28
      # 28 總測驗
    elsif (xuexi_class == 2) then
      # ziwei pars
      return 10
    elsif (xuexi_class == 3) then
      return 10
    end
  end
  def Xuexi_Ziwei.last_class_itemcount(xuexi_class,xuexi_item)
    if (Xuexi_Ziwei.itemcount_in_class(xuexi_class) == xuexi_item) then
      # 28 總測驗
      return true
    else
      return false
    end
  end

  # 一、天干
  # 甲、乙、丙、丁、戊、己、庚、辛、壬、癸，為十天干。
  # 1、陰陽：
  # 甲、丙、戊、庚、壬  屬陽。陽年生，男為陽男，女為陽女。
  # 乙、丁、己、辛、癸  屬陰。陰年生，男為陰男，女為陰女。
  # 2、天干五行方位：
  # 甲、乙屬木為東方木
  # 丙、丁屬火為南方火
  # 戊、己屬土為中央土
  # 庚、辛屬金為西方金
  # 壬、癸屬水為北方水
  # 3、天干化合、沖剋：
  # 甲己合化土，乙庚合化金，丙辛合化水，丁壬合化木，戊癸合化火。
  # 甲庚相沖，乙辛相沖，壬丙相沖，癸丁相沖，丙剋庚，丁剋辛。
  def Xuexi_Ziwei.timu_data_1_1_xuaxiongs(hPars,oStar,nPanType)
    n = rand(4)
    q1 = Pm.t("xuexi_ziwei.tiangan.taitou")
    if (n == 0) then
      # 陰陽
      xuaxiongs,notxuaxiongs = Xuexi_Ziwei.tiangan_timu_yinyang_data()
    elsif (n == 1) then
      xuaxiongs,notxuaxiongs = Xuexi_Ziwei.tiangan_timu_wuxing_data()
    elsif (n == 2) then
      xuaxiongs,notxuaxiongs = Xuexi_Ziwei.tiangan_timu_hehua_data()
    elsif (n == 3) then
      xuaxiongs1,notxuaxiongs1 = Xuexi_Ziwei.tiangan_timu_chong_data()
      xuaxiongs2,notxuaxiongs2 = Xuexi_Ziwei.tiangan_timu_ke_data()
      xuaxiongs = xuaxiongs1 + xuaxiongs2
      notxuaxiongs = notxuaxiongs1 + notxuaxiongs2
    end
    return q1,xuaxiongs.uniq,notxuaxiongs.uniq
  end
  def Xuexi_Ziwei.tiangan_timu_yinyang_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..10).each do |tiangan|
      xuaxiongs.push(Xuexi_Ziwei.tiangan_shuoming_yinyang(tiangan))
      notxuaxiongs.push(Xuexi_Ziwei.tiangan_shuoming_yinyang_not(tiangan))
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.tiangan_timu_wuxing_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..10).each do |tiangan|
      xuaxiongs.push(Xuexi_Ziwei.tiangan_shuoming_wuxing(tiangan))
      notxuaxiongs.push(Xuexi_Ziwei.tiangan_shuoming_wuxing_not(tiangan))
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.tiangan_timu_hehua_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..10).each do |tiangan|
      xuaxiongs.push(Xuexi_Ziwei.tiangan_shuoming_hehua(tiangan))
      notxuaxiongs.push(Xuexi_Ziwei.tiangan_shuoming_hehua_not(tiangan))
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.tiangan_timu_chong_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..10).each do |tiangan|
      s = Xuexi_Ziwei.tiangan_shuoming_chong(tiangan)
      xuaxiongs.push(s) if (s != "")
      s_not = Xuexi_Ziwei.tiangan_shuoming_chong_not(tiangan)
      notxuaxiongs.push(s_not) if (s_not != "")
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.tiangan_timu_ke_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..10).each do |tiangan|
      s = Xuexi_Ziwei.tiangan_shuoming_ke(tiangan)
      xuaxiongs.push(s) if (s != "")
      s_not = Xuexi_Ziwei.tiangan_shuoming_ke_not(tiangan)
      notxuaxiongs.push(s_not) if (s_not != "")
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.tiangan_shuoming(tiangan)
    a = []
    a.push(Xuexi_Ziwei.tiangan_shuoming_yinyang(tiangan))
    a.push(Xuexi_Ziwei.tiangan_shuoming_wuxing(tiangan))
    a.push(Xuexi_Ziwei.tiangan_shuoming_hehua(tiangan))
    s = Xuexi_Ziwei.tiangan_shuoming_chong(tiangan)
    a.push(s) if s != ""
    s = Xuexi_Ziwei.tiangan_shuoming_ke(tiangan)
    a.push(s) if s != ""

    return a
  end
  def Xuexi_Ziwei.tiangan_shuoming_yinyang(tiangan)
    # 甲、丙、戊、庚、壬  屬陽
    yang = [1,3,5,7,9]
    yin = [2,4,6,8,10]
    tiangan_str = Xuexi_Ziwei.tiangan_str(tiangan)
    if (yang.include?(tiangan)) then
      return Pm.t("xuexi_ziwei.shuoming.tiangan.yang",:q1 => tiangan_str)
    else
      return Pm.t("xuexi_ziwei.shuoming.tiangan.yin",:q1 => tiangan_str)
    end
  end
  def Xuexi_Ziwei.tiangan_shuoming_yinyang_not(tiangan)
    # 甲、丙、戊、庚、壬  屬陽
    yang = [1,3,5,7,9]
    yin = [2,4,6,8,10]
    tiangan_str = Xuexi_Ziwei.tiangan_str(tiangan)
    if (!yang.include?(tiangan)) then
      return Pm.t("xuexi_ziwei.shuoming.tiangan.yang",:q1 => tiangan_str)
    else
      return Pm.t("xuexi_ziwei.shuoming.tiangan.yin",:q1 => tiangan_str)
    end
  end
  def Xuexi_Ziwei.tiangan_shuoming_wuxing(tiangan)
    # wuxing_1: "壬、癸屬水為北方水"
    # wuxing_2: "甲、乙屬木為東方木"
    # wuxing_3: "庚、辛屬金為西方金"
    # wuxing_4: "戊、己屬土為中央土"
    # wuxing_5: "丙、丁屬火為南方火"
    tiangan_wuxings = [[9,10],[1,2],[7,8],[5,6],[3,4]]
    tiangan_str = Xuexi_Ziwei.tiangan_str(tiangan)
    tiangan_wuxings.each_with_index do |tiangan_wuxing,i|
      if (tiangan_wuxing.include?(tiangan)) then
        return Pm.t("xuexi_ziwei.shuoming.tiangan.wuxing_#{i + 1}",:q1 => tiangan_str)
      end
    end
    return ""
  end
  def Xuexi_Ziwei.tiangan_shuoming_wuxing_not(tiangan)
    # wuxing_1: "壬、癸屬水為北方水"
    # wuxing_2: "甲、乙屬木為東方木"
    # wuxing_3: "庚、辛屬金為西方金"
    # wuxing_4: "戊、己屬土為中央土"
    # wuxing_5: "丙、丁屬火為南方火"
    tiangan_wuxings = [[9,10],[1,2],[7,8],[5,6],[3,4]]
    tiangan_not = Xuexi_Ziwei.rand_tiangan_not(tiangan)
    tiangan_str = Xuexi_Ziwei.tiangan_str(tiangan_not)
    tiangan_wuxings.each_with_index do |tiangan_wuxing,i|
      if (tiangan_wuxing.include?(tiangan)) then
        return Pm.t("xuexi_ziwei.shuoming.tiangan.wuxing_#{i + 1}",:q1 => tiangan_str)
      end
    end
    return ""
  end
  def Xuexi_Ziwei.rand_tiangan()
    n = rand(10)
    n = 10 if (n == 0)
    return n
  end
  def Xuexi_Ziwei.rand_tiangan_not(tiangan)
    tg = Xuexi_Ziwei.rand_tiangan()
    tg += 1 if tg == tiangan
    return tg
  end
  def Xuexi_Ziwei.tiangan_shuoming_hehua(tiangan)
    # hehua_1_6: "甲己合化土"
    # hehua_2_7: "乙庚合化金"
    # hehua_3_8: "丙辛合化水"
    # hehua_4_9: "丁壬合化木"
    # hehua_5_10: "戊癸合化火"
    hehuas = [[1,6],[2,7],[3,8],[4,9],[5,10]]
    hehuas.each_with_index do |hehua,i|
      if (hehua.include?(tiangan)) then
        return Pm.t("xuexi_ziwei.shuoming.tiangan.hehua_#{hehua[0]}_#{hehua[1]}")
      end
    end
    return ""
  end
  def Xuexi_Ziwei.tiangan_shuoming_hehua_not(tiangan)
    # hehua_1_6: "甲己合化土"
    # hehua_2_7: "乙庚合化金"
    # hehua_3_8: "丙辛合化水"
    # hehua_4_9: "丁壬合化木"
    # hehua_5_10: "戊癸合化火"
      # wuxing_1: 水
      # wuxing_2: 木
      # wuxing_3: 金
      # wuxing_4: 土
      # wuxing_5: 火
    hehuas = [[1,6],[2,7],[3,8],[4,9],[5,10]]
    wuxings = [4,3,1,2,5]
    hehuas.each_with_index do |hehua,i|
      if (hehua.include?(tiangan)) then
        n = rand(2)
        if (n == 0) then
          tiangan_not = Xuexi_Ziwei.rand_tiangan_not(tiangan)
          q1 = Xuexi_Ziwei.tiangan_str(tiangan_not)
          tiangan2 = hehua - [tiangan]
          q2 = Xuexi_Ziwei.tiangan_str(tiangan2.sample)
          q3 = Xuexi_Ziwei.wuxing_str(wuxings[i])
          q1,q2 = q2,q1 if (hehua.index(tiangan) == 1)
        else
          q1 = Xuexi_Ziwei.tiangan_str(tiangan)
          tiangan2 = hehua - [tiangan]
          q2 = Xuexi_Ziwei.tiangan_str(tiangan2.sample)
          q1,q2 = q2,q1 if (hehua.index(tiangan) == 1)
          wuxing_not = (wuxings - [wuxings[i]]).sample
          q3 = Xuexi_Ziwei.wuxing_str(wuxing_not)
        end
        return Pm.t("xuexi_ziwei.shuoming.tiangan.hehua", :q1 => q1, :q2 => q2, :q3 => q3)
      end
    end
    return ""
  end
  def Xuexi_Ziwei.tiangan_shuoming_chong(tiangan)
    # chong_1_7: "甲庚相沖"
    # chong_2_8: "乙辛相沖"
    # chong_9_3: "壬丙相沖"
    # chong_10_4: "癸丁相沖"
    chongs = [[1,7],[2,8],[9,3],[10,4]]
    chongs.each_with_index do |chong,i|
      if (chong.include?(tiangan)) then
        return Pm.t("xuexi_ziwei.shuoming.tiangan.chong_#{chong[0]}_#{chong[1]}")
      end
    end
    return ""
  end
  def Xuexi_Ziwei.tiangan_shuoming_chong_not(tiangan)
    # chong_1_7: "甲庚相沖"
    # chong_2_8: "乙辛相沖"
    # chong_9_3: "壬丙相沖"
    # chong_10_4: "癸丁相沖"
    chongs = [[1,7],[2,8],[9,3],[10,4]]
    chongs.each_with_index do |chong,i|
      if (chong.include?(tiangan)) then
        tiangan_not = Xuexi_Ziwei.rand_tiangan_not(tiangan)
        q1 = Xuexi_Ziwei.tiangan_str(tiangan_not)
        tiangan2 = chong - [tiangan]
        q2 = Xuexi_Ziwei.tiangan_str(tiangan2.sample)
        q1,q2 = q2,q1 if (chong.index(tiangan) == 1)
        return Pm.t("xuexi_ziwei.shuoming.tiangan.chong", :q1 => q1, :q2 => q2)
      end
    end
    return ""
  end
  def Xuexi_Ziwei.tiangan_shuoming_ke(tiangan)
    # ke_3_7: "丙剋庚"
    # ke_4_8: "丁剋辛"
    kes = [[3,7],[4,8]]
    kes.each_with_index do |ke,i|
      if (ke.include?(tiangan)) then
        return Pm.t("xuexi_ziwei.shuoming.tiangan.ke_#{ke[0]}_#{ke[1]}")
      end
    end
    return ""
  end
  def Xuexi_Ziwei.tiangan_shuoming_ke_not(tiangan)
    # ke_3_7: "丙剋庚"
    # ke_4_8: "丁剋辛"
    kes = [[3,7],[4,8]]
    kes.each_with_index do |ke,i|
      if (ke.include?(tiangan)) then
        tiangan_not = Xuexi_Ziwei.rand_tiangan_not(tiangan)
        q1 = Xuexi_Ziwei.tiangan_str(tiangan_not)
        tiangan2 = ke - [tiangan]
        q2 = Xuexi_Ziwei.tiangan_str(tiangan2.sample)
        q1,q2 = q2,q1 if (ke.index(tiangan) == 1)
        return Pm.t("xuexi_ziwei.shuoming.tiangan.ke", :q1 => q1, :q2 => q2)
      end
    end
    return ""
  end
  def Xuexi_Ziwei.tiangan_data()
    a = []
    (1..10).each do |tiangan|
      a.push(Xuexi_Ziwei.tiangan_str(tiangan))
    end
    return a
  end
  def Xuexi_Ziwei.tiangan_str(tiangan)
    return Pm.t("xuexi_ziwei.tiangan.tiangan_#{tiangan}")
  end

  # 二、地支
  # 子、丑、寅、卯、辰、巳、午、未、申、酉、戌、亥，為十二地支。
  # 1、陰陽：
  # 子、寅、辰、午、申、戌 屬陽。
  # 丑、卯、巳、未、酉、亥 屬陰。
  # 2、地支生肖代表：
  # 子屬鼠、丑屬牛、寅屬虎、卯屬兔、辰屬龍、巳屬蛇
  # 午屬馬、未屬羊、申屬猴、酉屬雞、戌屬狗、亥屬豬。
  # 3、地支五行方位：
  # 子屬水、丑屬土、寅屬木、卯屬木、辰屬土、巳屬火
  # 午屬火、未屬土、申屬金、酉屬金、戌屬土、亥屬水。
  # 4、地支代表之月份：
  # 子為十一月、丑為十二月、寅為一月、卯為二月、辰為三月、巳為四月
  # 午為五月、未為六月、申為七月、酉為八月、戌為九月、亥為十月（以上均為農曆月份）。
  # 5、四時：
  # 寅、卯、辰，司春，為東方。
  # 巳、午、未，司夏，為南方。
  # 申、酉、戌，司秋，為西方。
  # 亥、子、丑，司冬，為北方。
  # 惟辰、戌、丑、未四個地支單位而言之，屬土，為四季，為中央。
  # 6、地支六合：
  # 子丑合化土、寅亥合化木、卯戌合化火、辰酉合化金、巳申合化水。
  # 午為陽、未為陰。如圖3-8十二地支圖以對稱方式記憶。
  # 7、地支三合：
  # 寅午戌合化火局。申子辰合水局。巳酉丑合金局。亥卯未合木局。以起始宮開始算，順時及逆時，第四宮位（中間空三個宮位）即為三合位置。
  # 8、地支六沖：
  # 子午相沖、丑未相沖、寅申相沖、卯酉相沖、辰戌相沖、巳亥相沖。正對面的宮稱為沖。
  def Xuexi_Ziwei.timu_data_1_2_xuaxiongs(hPars,oStar,nPanType)
    n = rand(6)
    q1 = Pm.t("xuexi_ziwei.dizhi.taitou")
    if (n == 0) then
      # 陰陽
      xuaxiongs,notxuaxiongs = Xuexi_Ziwei.dizhi_timu_yinyang_data()
    elsif (n == 1) then
      xuaxiongs,notxuaxiongs = Xuexi_Ziwei.dizhi_timu_shengxiao_data()
    elsif (n == 2) then
      xuaxiongs,notxuaxiongs = Xuexi_Ziwei.dizhi_timu_wuxing_data()
    elsif (n == 3) then
      xuaxiongs,notxuaxiongs = Xuexi_Ziwei.dizhi_timu_yue_data()
    elsif (n == 4) then
      xuaxiongs,notxuaxiongs = Xuexi_Ziwei.dizhi_timu_sishi_data()
    elsif (n == 5) then
      xuaxiongs1,notxuaxiongs1 = Xuexi_Ziwei.dizhi_timu_liuhe_data()
      xuaxiongs2,notxuaxiongs2 = Xuexi_Ziwei.dizhi_timu_sanhe_data()
      xuaxiongs3,notxuaxiongs3 = Xuexi_Ziwei.dizhi_timu_liuchong_data()
      xuaxiongs = xuaxiongs1 + xuaxiongs2 + xuaxiongs3
      notxuaxiongs = notxuaxiongs1 + notxuaxiongs2 + notxuaxiongs3
    end
    xuaxiongs -= [""]
    notxuaxiongs -= [""]
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.dizhi_timu_yinyang_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..12).each do |dizhi|
      xuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_yinyang(dizhi))
      notxuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_yinyang_not(dizhi))
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.dizhi_timu_shengxiao_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..12).each do |dizhi|
      xuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_shengxiao(dizhi))
      notxuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_shengxiao_not(dizhi))
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.dizhi_timu_wuxing_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..12).each do |dizhi|
      xuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_wuxing(dizhi))
      notxuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_wuxing_not(dizhi))
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.dizhi_timu_yue_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..12).each do |dizhi|
      xuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_yue(dizhi))
      notxuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_yue_not(dizhi))
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.dizhi_timu_sishi_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..12).each do |dizhi|
      xuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_sishi(dizhi))
      notxuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_sishi_not(dizhi))
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.dizhi_timu_liuhe_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..12).each do |dizhi|
      xuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_liuhe(dizhi))
      notxuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_liuhe_not(dizhi))
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.dizhi_timu_sanhe_data()
    xuaxiongs = []
    notxuaxiongs = []
    n = rand(2)
    if (n == 0) then
      (1..12).each do |dizhi|
        xuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_sanhe(dizhi))
        notxuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_sanhe_not(dizhi))
      end
    else
      xuaxiongs = Xuexi_Ziwei.dizhi_sanhe_strs()
      notxuaxiongs = Xuexi_Ziwei.not_dizhi_sanhe_strs()
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.dizhi_timu_liuchong_data()
    xuaxiongs = []
    notxuaxiongs = []
    (1..12).each do |dizhi|
      xuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_liuchong(dizhi))
      notxuaxiongs.push(Xuexi_Ziwei.dizhi_shuoming_liuchong_not(dizhi))
    end
    return xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.dizhi_shuoming(dizhi)
    a = []
    a.push(Xuexi_Ziwei.dizhi_shuoming_yinyang(dizhi))
    a.push(Xuexi_Ziwei.dizhi_shuoming_shengxiao(dizhi))
    a.push(Xuexi_Ziwei.dizhi_shuoming_wuxing(dizhi))
    a.push(Xuexi_Ziwei.dizhi_shuoming_yue(dizhi))
    a.push(Xuexi_Ziwei.dizhi_shuoming_sishi(dizhi))
    s = Xuexi_Ziwei.dizhi_shuoming_liuhe(dizhi)
    a.push(s) if s != ""
    a.push(Xuexi_Ziwei.dizhi_shuoming_sanhe(dizhi))
    a.push(Xuexi_Ziwei.dizhi_shuoming_liuchong(dizhi))

    return a
  end
  def Xuexi_Ziwei.dizhi_shuoming_yinyang(dizhi)
    # 子、寅、辰、午、申、戌 屬陽。
    yang = [1,3,5,7,9,11]
    dizhi_str = Xuexi_Ziwei.dizhi_str(dizhi)
    if (yang.include?(dizhi)) then
      return Pm.t("xuexi_ziwei.shuoming.dizhi.yang",:q1 => dizhi_str)
    else
      return Pm.t("xuexi_ziwei.shuoming.dizhi.yin",:q1 => dizhi_str)
    end
  end
  def Xuexi_Ziwei.dizhi_shuoming_yinyang_not(dizhi)
    # 子、寅、辰、午、申、戌 屬陽。
    yang = [1,3,5,7,9,11]
    dizhi_str = Xuexi_Ziwei.dizhi_str(dizhi)
    if (!yang.include?(dizhi)) then
      return Pm.t("xuexi_ziwei.shuoming.dizhi.yang",:q1 => dizhi_str)
    else
      return Pm.t("xuexi_ziwei.shuoming.dizhi.yin",:q1 => dizhi_str)
    end
  end
  def Xuexi_Ziwei.dizhi_shuoming_shengxiao(dizhi)
    # 2、地支生肖代表：
    # 子屬鼠、丑屬牛、寅屬虎、卯屬兔、辰屬龍、巳屬蛇
    # 午屬馬、未屬羊、申屬猴、酉屬雞、戌屬狗、亥屬豬。
    s = Pm.t("xuexi_ziwei.shuoming.dizhi.shengxiaos")
    a = s.split(",")
    return a[dizhi - 1]
  end
  def Xuexi_Ziwei.dizhi_shuoming_shengxiao_not(dizhi)
    wrong_dizhi = Xuexi_Ziwei.rand_not_dizhi(dizhi)
    q1 = Xuexi_Ziwei.dizhi_str(wrong_dizhi)
    q2 = Xuexi_Ziwei.shengxiao_str(dizhi)
    return Pm.t("xuexi_ziwei.shuoming.dizhi.shengxiao",:q1 => q1, :q2 => q2)
  end
  def Xuexi_Ziwei.shengxiao_str(dizhi)
    return Pm.t("xuexi_ziwei.shengxiao.shengxiao_#{dizhi}")
  end
  def Xuexi_Ziwei.dizhi_shuoming_wuxing(dizhi)
    # 3、地支五行方位：
    # 子屬水、丑屬土、寅屬木、卯屬木、辰屬土、巳屬火
    # 午屬火、未屬土、申屬金、酉屬金、戌屬土、亥屬水。
    s = Pm.t("xuexi_ziwei.shuoming.dizhi.wuxings")
    a = s.split(",")
    return a[dizhi - 1]
  end
  def Xuexi_Ziwei.dizhi_shuoming_wuxing_not(dizhi)
    # wuxing_1: 水
    # wuxing_2: 木
    # wuxing_3: 金
    # wuxing_4: 土
    # wuxing_5: 火
    wuxings = [1,4,2,2,4,5,5,4,3,3,4,1]
    q1 = Xuexi_Ziwei.dizhi_str(dizhi)
    wuxing = wuxings[dizhi-1]
    wrong_wuxing = (wuxings - [wuxing]).sample
    q2 = Xuexi_Ziwei.wuxing_str(wrong_wuxing)
    return Pm.t("xuexi_ziwei.shuoming.dizhi.wuxing",:q1 => q1, :q2 => q2)
  end
  def Xuexi_Ziwei.dizhi_shuoming_yue(dizhi)
    # 4、地支代表之月份：
    # 子為十一月、丑為十二月、寅為一月、卯為二月、辰為三月、巳為四月
    # 午為五月、未為六月、申為七月、酉為八月、戌為九月、亥為十月（以上均為農曆月份）。
    s = Pm.t("xuexi_ziwei.shuoming.dizhi.yues")
    a = s.split(",")
    return a[dizhi - 1]
  end
  def Xuexi_Ziwei.dizhi_shuoming_yue_not(dizhi)
    yues = [11,12,1,2,3,4,5,6,7,8,9,10]
    wrong_dizhi = Xuexi_Ziwei.rand_not_dizhi(dizhi)
    q1 = Xuexi_Ziwei.dizhi_str(wrong_dizhi)
    yue = yues[dizhi-1]
    q2 = Xdate.guo_zi_sus_str(yue)
    return Pm.t("xuexi_ziwei.shuoming.dizhi.wuxing",:q1 => q1, :q2 => q2)
  end
  def Xuexi_Ziwei.dizhi_shuoming_sishi(dizhi)
    # 5、四時：
    # 寅、卯、辰，司春，為東方。
    # 巳、午、未，司夏，為南方。
    # 申、酉、戌，司秋，為西方。
    # 亥、子、丑，司冬，為北方。
    # 惟辰、戌、丑、未四個地支單位而言之，屬土，為四季，為中央。 此項不用
    sishis = [[3,4,5],[6,7,8],[9,10,11],[12,1,2]] #,[5,11,2,8]]
    key = ""
    sishis.each do |sishi|
      if (sishi.include?(dizhi)) then
        key = "sishi_" + sishi.join("_")
      end
    end
    dizhi_str = Xuexi_Ziwei.dizhi_str(dizhi)
    return Pm.t("xuexi_ziwei.shuoming.dizhi.#{key}",:q1 => dizhi_str)
  end
  def Xuexi_Ziwei.dizhi_shuoming_sishi_not(dizhi)
    # 5、四時：
    # 寅、卯、辰，司春，為東方。
    # 巳、午、未，司夏，為南方。
    # 申、酉、戌，司秋，為西方。
    # 亥、子、丑，司冬，為北方。
    # 惟辰、戌、丑、未四個地支單位而言之，屬土，為四季，為中央。 此項不用
    sishis = [[3,4,5],[6,7,8],[9,10,11],[12,1,2]] #,[5,11,2,8]]
    key = ""
    sishis.each do |sishi|
      if (sishi.include?(dizhi)) then
        key = "sishi_" + sishi.join("_")
      end
    end
    wrong_dizhi = Xuexi_Ziwei.rand_not_dizhi(dizhi)
    dizhi_str = Xuexi_Ziwei.dizhi_str(wrong_dizhi)
    return Pm.t("xuexi_ziwei.shuoming.dizhi.#{key}",:q1 => dizhi_str)
  end
  def Xuexi_Ziwei.dizhi_shuoming_liuhe(dizhi)
    # 6、地支六合：
    # 子丑合化土、寅亥合化木、卯戌合化火、辰酉合化金、巳申合化水。
    # 午為陽、未為陰。如圖3-8十二地支圖以對稱方式記憶。
    liuhes = [[1,2],[3,12],[4,11],[5,10],[6,9],[7,8]]
    key = ""
    liuhes.each do |liuhe|
      if (liuhe.include?(dizhi)) then
        key = "liuhe_" + liuhe.join("_")
      end
    end
    return Pm.t("xuexi_ziwei.shuoming.dizhi.#{key}")
  end
  def Xuexi_Ziwei.dizhi_shuoming_liuhe_not(dizhi)
    # 6、地支六合：
    # 子丑合化土、寅亥合化木、卯戌合化火、辰酉合化金、巳申合化水、午未合化火。
    # 午為陽、未為陰。如圖3-8十二地支圖以對稱方式記憶。
    # wuxing_1: 水
    # wuxing_2: 木
    # wuxing_3: 金
    # wuxing_4: 土
    # wuxing_5: 火
    wuxings = [4,2,5,3,1,5]
    liuhes = [[1,2],[3,12],[4,11],[5,10],[6,9],[7,8]]
    key = ""
    liuhes.each_with_index do |liuhe,i|
      if (liuhe.include?(dizhi)) then
        dizhi1 = liuhe[0]
        dizhi2 = liuhe[1]
        wuxing = wuxings[i]
        wrong_wuxing = (wuxings - [wuxing]).sample
        q1 = Xuexi_Ziwei.dizhi_str(dizhi1)
        q2 = Xuexi_Ziwei.dizhi_str(dizhi2)
        q3 = Xuexi_Ziwei.wuxing_str(wrong_wuxing)
        return Pm.t("xuexi_ziwei.shuoming.dizhi.liuhe",:q1 => q1,:q2 => q2,:q3 => q3)
      end
    end
    return ""
  end
  def Xuexi_Ziwei.dizhi_shuoming_sanhe(dizhi)
    # 7、地支三合：
    # 寅午戌合化火局。申子辰合水局。巳酉丑合金局。亥卯未合木局。以起始宮開始算，順時及逆時，第四宮位（中間空三個宮位）即為三合位置。
    sanhe = Xuexi_Ziwei.find_dizhi_sanhe(dizhi)
    key = "sanhe_" + sanhe.join("_")
    return Pm.t("xuexi_ziwei.shuoming.dizhi.#{key}")
  end
  def Xuexi_Ziwei.dizhi_shuoming_sanhe_not(dizhi)
    # wuxing_1: 水
    # wuxing_2: 木
    # wuxing_3: 金
    # wuxing_4: 土
    # wuxing_5: 火
    wuxings = [1,3,5,2,1,3,5,2,1,3,5,2]
    # 7、地支三合：
    # 寅午戌合化火局。申子辰合水局。巳酉丑合金局。亥卯未合木局。以起始宮開始算，順時及逆時，第四宮位（中間空三個宮位）即為三合位置。
    sanhe = Xuexi_Ziwei.find_dizhi_sanhe(dizhi)
    wuxing = wuxings[dizhi - 1]
    wrong_wuxing = (wuxings - [wuxing]).sample
    q1 = Xuexi_Ziwei.dizhi_str(sanhe[0])
    q2 = Xuexi_Ziwei.dizhi_str(sanhe[1])
    q3 = Xuexi_Ziwei.dizhi_str(sanhe[2])
    q4 = Xuexi_Ziwei.wuxing_str(wrong_wuxing)
    return Pm.t("xuexi_ziwei.shuoming.dizhi.sanhe",:q1 => q1,:q2 => q2,:q3 => q3,:q4 => q4)
  end
  def Xuexi_Ziwei.dizhi_liuchong(dizhi)
    liuchongs = [[1,7],[2,8],[3,9],[4,10],[5,11],[6,12]]
    liuchongs.each do |liuchong|
      if (liuchong.include?(dizhi)) then
        return liuchong
      end
    end
    return []
  end
  def Xuexi_Ziwei.dizhi_shuoming_liuchong(dizhi)
    # 8、地支六沖：
    # 子午相沖、丑未相沖、寅申相沖、卯酉相沖、辰戌相沖、巳亥相沖。正對面的宮稱為沖。
    liuchong = Xuexi_Ziwei.dizhi_liuchong(dizhi)
    key = "liuchong_" + liuchong.join("_")
    return Pm.t("xuexi_ziwei.shuoming.dizhi.#{key}")
  end
  def Xuexi_Ziwei.dizhi_shuoming_liuchong_not(dizhi)
    # 8、地支六沖：
    # 子午相沖、丑未相沖、寅申相沖、卯酉相沖、辰戌相沖、巳亥相沖。正對面的宮稱為沖。
    liuchong = Xuexi_Ziwei.dizhi_liuchong(dizhi)
    q1 = Xuexi_Ziwei.dizhi_str(dizhi)
    dizhis = Xuexi_Ziwei.dizhis()
    wrong_chong_dizhi = (dizhis - liuchong).sample
    q2 = Xuexi_Ziwei.dizhi_str(wrong_chong_dizhi)
    return Pm.t("xuexi_ziwei.shuoming.dizhi.liuchong",:q1 => q1,:q2 => q2)
  end
  def Xuexi_Ziwei.dizhis()
    return [1,2,3,4,5,6,7,8,9,10,11,12]
  end
  def Xuexi_Ziwei.dizhi_data()
    a = []
    (1..12).each do |dizhi|
      a.push(Xuexi_Ziwei.dizhi_str(dizhi))
    end
    return a
  end
  def Xuexi_Ziwei.dizhi_str(dizhi)
    return Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi}")
  end
  def Xuexi_Ziwei.dizhi_sanhe_strs()
    a = []
    @@sanhe_xings.each do |sanhe|
      a.push(Xuexi_Ziwei.dizhi_sanhe_str(sanhe))
    end
    return a
  end
  def Xuexi_Ziwei.not_dizhi_sanhe_strs()
    a = []
    sanhes = Xuexi_Ziwei.not_sanhes()
    sanhes.each do |sanhe|
      a.push(Xuexi_Ziwei.dizhi_sanhe_str(sanhe))
    end
    return a
  end
  def Xuexi_Ziwei.not_sanhes(dizhi=nil)
    a = []
    while a.length < 10
      sanhe = Xuexi_Ziwei.not_sanhe(dizhi)
      if (!a.include?(sanhe)) then
        a.push(sanhe)
      end
    end
    return a
  end
  def Xuexi_Ziwei.check_sanhe(sanhe2)
    @@sanhe_xings.each do |sanhe1|
      if (sanhe1.sort == sanhe2.sort) then
        return true
      end
    end
    return false
  end
  def Xuexi_Ziwei.not_sanhe(dizhi=nil)
    sanhe = Xuexi_Ziwei.get_not_sanhe(dizhi)
    while (Xuexi_Ziwei.check_sanhe(sanhe))
      sanhe = Xuexi_Ziwei.get_not_sanhe(dizhi)
    end
    return sanhe
  end
  def Xuexi_Ziwei.get_not_sanhe(dizhi=nil)
    a = []
    if (dizhi != nil) then
      a.push(dizhi)
    end
    while a.length < 3
      dizhi = Xuexi_Ziwei.rand_dizhi()
      if (!a.include?(dizhi)) then
        a.push(dizhi)
      end
    end
    return a.sort
  end
  def Xuexi_Ziwei.rand_not_dizhi(dizhi)
    wrong_dizhi = Xuexi_Ziwei.rand_dizhi()
    wrong_dizhi = Xuexi_Ziwei.check_dizhi(wrong_dizhi + 1) if (wrong_dizhi == dizhi)
    return wrong_dizhi
  end
  def Xuexi_Ziwei.check_dizhi(dizhi)
    newdizhi = Pm.TestNo12(dizhi)
    newdizhi = 12 if newdizhi == 0
    return newdizhi
  end
  def Xuexi_Ziwei.rand_dizhi()
    n = Xuexi_Ziwei.check_dizhi(rand(12))
    return n
  end
  def Xuexi_Ziwei.dizhi_sanhe_str(sanhe)
    q1 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{sanhe[0]}")
    q2 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{sanhe[1]}")
    q3 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{sanhe[2]}")
    return Pm.t("xuexi_ziwei.dizhi.question",:q1 => q1,:q2 => q2,:q3 => q3)
  end
  # 三合：亦稱三合宮，由寅、午、戍三宮；申、子、辰三宮；巳、酉、丑三宮；亥、卯、未三宮。
  # 如命坐寅午戍中之一宮，其他兩宮必成財帛和官祿，命、財、官因而成為「三合」或「三合宮」，
  # 其吉凶引動息息相關；同理，若田宅宮坐於亥、卯、未之其中一宮，另兩宮必為疾厄與兄弟，該三宮的吉凶亦息息相關。
  # 可參考本章第三節之地支三合。
  @@sanhe_xings = [
                [9,1,5],
                [6,10,2],
                [3,7,11],
                [12,4,8]
              ]
  def Xuexi_Ziwei.find_dizhi_sanhe(dizhi)
    @@sanhe_xings.each do |sanhe|
      if (sanhe.include?(dizhi)) then
        return sanhe
      end
    end
    return nil
  end

  def Xuexi_Ziwei.wuxing_data()
    a = []
    (1..5).each do |wuxing|
      a.push(Xuexi_Ziwei.wuxing_str(wuxing))
    end
    return a
  end
  def Xuexi_Ziwei.wuxing_str(wuxing)
    return Pm.t("xuexi_ziwei.wuxing.wuxing_#{wuxing}")
  end
  #     水 木 金 土 火
  #     1  2  3 4  5
  # 我生 2  5  1 3  4
  # 我剋 5  4  2 1  3
  # 生我 3  1  4 5  2
  # 剋我 4  3  5 2  1
  def Xuexi_Ziwei.wuxing_sheng_ke(wuxing)
    wo_sheng = Xuexi_Ziwei.wuxing_wo_sheng(wuxing)
    wo_ke = Xuexi_Ziwei.wuxing_wo_ke(wuxing)
    sheng_wo = Xuexi_Ziwei.wuxing_sheng_wo(wuxing)
    ke_wo = Xuexi_Ziwei.wuxing_ke_wo(wuxing)
    return wo_sheng,wo_ke,sheng_wo,ke_wo
  end
  def Xuexi_Ziwei.wuxing_wo_sheng(wuxing)
    a = [2,5,1,3,4]
    return a[wuxing - 1]
  end
  def Xuexi_Ziwei.wuxing_wo_ke(wuxing)
    a = [5,4,2,1,3]
    return a[wuxing - 1]
  end
  def Xuexi_Ziwei.wuxing_sheng_wo(wuxing)
    a = [3,1,4,5,2]
    return a[wuxing - 1]
  end
  def Xuexi_Ziwei.wuxing_ke_wo(wuxing)
    a = [4,3,5,2,1]
    return a[wuxing - 1]
  end
  def Xuexi_Ziwei.wuxing_all_sheng_ke_strs()
    a = []
    (1..5).each do |wuxing|
      a += Xuexi_Ziwei.wuxing_sheng_ke_strs(wuxing)
    end
    return a
  end
  def Xuexi_Ziwei.wuxing_sheng_ke_strs(wuxing)
    wo_sheng,wo_ke,sheng_wo,ke_wo = Xuexi_Ziwei.wuxing_sheng_ke(wuxing)
    a = []
    a.push(Xuexi_Ziwei.wuxing_sheng_ke_str(wuxing,"sheng",wo_sheng))
    a.push(Xuexi_Ziwei.wuxing_sheng_ke_str(wuxing,"ke",wo_ke))
    a.push(Xuexi_Ziwei.wuxing_sheng_ke_str(sheng_wo,"sheng",wuxing))
    a.push(Xuexi_Ziwei.wuxing_sheng_ke_str(ke_wo,"ke",wuxing))
    return a
  end
  def Xuexi_Ziwei.wuxing_all_wrong_sheng_ke_strs()
    a = []
    (1..5).each do |wuxing|
      a += Xuexi_Ziwei.wuxing_wrong_sheng_ke_strs(wuxing)
    end
    return a
  end
  def Xuexi_Ziwei.wuxing_wrong_sheng_ke_strs(wuxing)
    wo_sheng,wo_ke,sheng_wo,ke_wo = Xuexi_Ziwei.wuxing_sheng_ke(wuxing)
    a = []
    a.push(Xuexi_Ziwei.wuxing_sheng_ke_str(wuxing,"sheng",wo_ke))
    a.push(Xuexi_Ziwei.wuxing_sheng_ke_str(wuxing,"ke",sheng_wo))
    a.push(Xuexi_Ziwei.wuxing_sheng_ke_str(sheng_wo,"sheng",ke_wo))
    a.push(Xuexi_Ziwei.wuxing_sheng_ke_str(ke_wo,"ke",wo_sheng))
    return a
  end
  def Xuexi_Ziwei.wuxing_sheng_ke_str(wuxing1,shengke,wuxing2)
    wx1 = Pm.GetStr("xuexi_ziwei.wuxing.wuxing_#{wuxing1}")
    shengke = Pm.GetStr("xuexi_ziwei.wuxing.shengke.#{shengke}")
    wx2 = Pm.GetStr("xuexi_ziwei.wuxing.wuxing_#{wuxing2}")
    return "#{wx1}#{shengke}#{wx2}"
  end
  #     水 木 金 土 火
  #     1  2  3 4  5
  # 春:寅、卯 木 火 水 金 土
  # 夏:巳、午 火 土 木 水 金
  # 秋:申、酉 金 水 土 火 木
  # 冬:亥、子 水 木 金 土 火
  # 四季:辰、 土 金 火 木 水
  # 未、戌、丑
  Season_Spring = 1
  Season_Summer = 2
  Season_Autumn = 3
  Season_Winter = 4
  Season_Four = 5
  @@Earth_season = [4,5,1,1,5,2,2,5,3,3,5,4]
  def Xuexi_Ziwei.Earth_season(nEarth)
    return @@Earth_season[nEarth - 1]
  end
  #                    旺 相 休 囚 死
  @@Seg_wuxing_wang_xiang_xiu_qiu_si = [
                       [2,5,1,3,4],  # 春
                       [5,4,2,1,3],  # 夏
                       [3,1,4,5,2],  # 秋
                       [1,2,3,4,5],  # 冬
                       [4,3,5,2,1]   # 四季
                     ]
  def Xuexi_Ziwei.par_wuxing_wang_xiang_xiu_qiu_si(nEarth)
    season = Xuexi_Ziwei.Earth_season(nEarth)
    return @@Seg_wuxing_wang_xiang_xiu_qiu_si[season - 1]
  end
  def Xuexi_Ziwei.par_find_wuxing_wang_xiang_xiu_qiu_si(nEarth,wuxing)
    par_wuxing_wang_xiang_xiu_qiu_si = Xuexi_Ziwei.par_wuxing_wang_xiang_xiu_qiu_si(nEarth)
    wuxing_wang_xiang_xiu_qiu_si_index = Xuexi_Ziwei.par_find_wuxing_wang_xiang_xiu_qiu_si_index(par_wuxing_wang_xiang_xiu_qiu_si,wuxing)
    return wuxing_wang_xiang_xiu_qiu_si_index + 1
  end
  def Xuexi_Ziwei.par_find_wuxing_wang_xiang_xiu_qiu_si_index(par_wuxing_wang_xiang_xiu_qiu_si,wuxing)
    return par_wuxing_wang_xiang_xiu_qiu_si.index(wuxing)
  end
  def Xuexi_Ziwei.wuxing_wang_xiang_xiu_qiu_si(wuxing)
    chun = Xuexi_Ziwei.par_find_wuxing_wang_xiang_xiu_qiu_si(3,wuxing)
    xia = Xuexi_Ziwei.par_find_wuxing_wang_xiang_xiu_qiu_si(6,wuxing)
    qiu = Xuexi_Ziwei.par_find_wuxing_wang_xiang_xiu_qiu_si(9,wuxing)
    dong = Xuexi_Ziwei.par_find_wuxing_wang_xiang_xiu_qiu_si(12,wuxing)
    siji = Xuexi_Ziwei.par_find_wuxing_wang_xiang_xiu_qiu_si(2,wuxing)
    return [chun,xia,qiu,dong,siji],[3,6,9,12,2]
  end
  def Xuexi_Ziwei.wuxing_all_wang_xiang_xiu_qiu_si_strs()
    a = []
    (1..5).each do |wuxing|
      a += Xuexi_Ziwei.wuxing_wang_xiang_xiu_qiu_si_strs(wuxing)
    end
    return a
  end
  def Xuexi_Ziwei.wuxing_wang_xiang_xiu_qiu_si_strs(wuxing)
    wang_xiang_xiu_qiu_si,aEarths = Xuexi_Ziwei.wuxing_wang_xiang_xiu_qiu_si(wuxing)
    q1 = Pm.t("xuexi_ziwei.wuxing.wuxing_#{wuxing}")
    a = []
    wang_xiang_xiu_qiu_si.each_with_index do |wxxqs,i|
      siji_key = Xuexi_Ziwei.siji_key(aEarths[i])
      siji_str = Pm.t("xuexi_ziwei.wuxing.siji.#{siji_key}")
      wxxqs_str = Xuexi_Ziwei.wang_xiang_xiu_qiu_si_str(wxxqs)
      s = Pm.t("xuexi_ziwei.wuxing.wang_xiang_xiu_qiu_si.question",:q1 => q1, :q2 => siji_str, :q3 => wxxqs_str)
      a.push(s)
    end
    return a
  end
  def Xuexi_Ziwei.wuxing_all_wrong_wang_xiang_xiu_qiu_si_strs()
    a = []
    (1..5).each do |wuxing|
      a += Xuexi_Ziwei.wuxing_wrong_wang_xiang_xiu_qiu_si_strs(wuxing)
    end
    return a
  end
  def Xuexi_Ziwei.wuxing_wrong_wang_xiang_xiu_qiu_si_strs(wuxing)
    wang_xiang_xiu_qiu_si,aEarths = Xuexi_Ziwei.wuxing_wang_xiang_xiu_qiu_si(wuxing)
    q1 = Pm.t("xuexi_ziwei.wuxing.wuxing_#{wuxing}")
    a = []
    wang_xiang_xiu_qiu_si.each_with_index do |wxxqs,i|
      siji_key = Xuexi_Ziwei.siji_key(aEarths[i])
      siji_str = Pm.t("xuexi_ziwei.wuxing.siji.#{siji_key}")
      wrong_i = i + 1
      wrong_i = 0 if wrong_i >= 5
      wxxqs_str = Xuexi_Ziwei.wang_xiang_xiu_qiu_si_str(wang_xiang_xiu_qiu_si[wrong_i])
      s = Pm.t("xuexi_ziwei.wuxing.wang_xiang_xiu_qiu_si.question",:q1 => q1, :q2 => siji_str, :q3 => wxxqs_str)
      a.push(s)
    end
    return a
  end
  def Xuexi_Ziwei.wang_xiang_xiu_qiu_si_str(wang_xiang_xiu_qiu_si)
    a = ["","wang","xiang","xiu","qiu","si"]
    return Pm.GetStr("xuexi_ziwei.wuxing.wang_xiang_xiu_qiu_si.#{a[wang_xiang_xiu_qiu_si]}")
  end
  def Xuexi_Ziwei.siji_str(nEarth)
    siji = Xuexi_Ziwei.siji_key(nEarth)
    return Pm.GetStr("xuexi_ziwei.wuxing.siji.#{siji}")
  end
  def Xuexi_Ziwei.siji_key(nEarth)
    a = ["","chun","xia","qiu","dong","siji"]
    siji = Xuexi_Ziwei.Earth_season(nEarth)
    return a[siji]
  end

  def Xuexi_Ziwei.all_ganzhi_strs()
    a = []
    (0...60).each do |ganzhi|
      ganzhi_str = Xuexi_Ziwei.ganzhi_str(ganzhi)
      a.push(ganzhi_str)
    end
    return a
  end
  def Xuexi_Ziwei.all_wrong_ganzhi_strs()
    a = []
    (0...60).each do |ganzhi|
      n = rand(60)
      n += 3 if n == 0
      n = 2 * n + 1
      wrong_ganzhi = Pm.TestNo60(n + ganzhi)
      ganzhi_str1 = Xuexi_Ziwei.ganzhi_str(ganzhi)
      ganzhi_str2 = Xuexi_Ziwei.ganzhi_str(wrong_ganzhi)
      a.push(ganzhi_str1[0] + ganzhi_str2[1])
    end
    return a
  end
  def Xuexi_Ziwei.ganzhi_str(ganzhi)
    tiangan,dizhi = Xdate.GetGanZhiSkyEarth(ganzhi)
    return Pm.t("xuexi_ziwei.tiangan.tiangan_#{tiangan}") + Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi}")
  end
  def Xuexi_Ziwei.all_liu_shi_na_yin_strs()
    a = []
    (0...60).each do |ganzhi|
      ganzhi_str,liu_shi_na_yin_str,wuxing = Xuexi_Ziwei.liu_shi_na_yin_str(ganzhi)
      a.push(ganzhi_str + Pm.t("xuexi_ziwei.liu_shi_na_yin.wuxing_su", :q1 => wuxing))
      # a.push(ganzhi_str + liu_shi_na_yin_str + Pm.t("xuexi_ziwei.liu_shi_na_yin.wuxing_su", :q1 => wuxing))
    end
    return a
  end
  def Xuexi_Ziwei.liu_shi_na_yin_str(ganzhi)
    ganzhi_str = Xuexi_Ziwei.ganzhi_str(ganzhi)
    liu_shi_na_yin_str = Xdate.wan_nian_li_liu_shi_na_yin_str(ganzhi)
    wuxing = liu_shi_na_yin_str[2]
    return ganzhi_str,liu_shi_na_yin_str,wuxing
  end
  def Xuexi_Ziwei.all_wrong_liu_shi_na_yin_strs()
    a = []
    (0...60).each do |ganzhi|
      ganzhi_str1,liu_shi_na_yin_str1,wuxing1 = Xuexi_Ziwei.liu_shi_na_yin_str(ganzhi)
      n = rand(60)
      n += 3 if n == 0
      wrong_ganzhi = Pm.TestNo60(n + ganzhi)
      ganzhi_str2,liu_shi_na_yin_str2,wuxing2 = Xuexi_Ziwei.liu_shi_na_yin_str(wrong_ganzhi)
      a.push(ganzhi_str1 + Pm.t("xuexi_ziwei.liu_shi_na_yin.wuxing_su", :q1 => wuxing2))
      # a.push(ganzhi_str1 + liu_shi_na_yin_str2 + Pm.t("xuexi_ziwei.liu_shi_na_yin.wuxing_su", :q1 => wuxing2))
    end
    return a
  end

  # 主星：紫微斗數主星共有十四顆，
  def Xuexi_Ziwei.zhuxingxi_xings_str(zhuxingxi)
    return  eval("Xuexi_Ziwei.#{zhuxingxi}_xings_str()")
  end
  def Xuexi_Ziwei.zhuxingxi_xings(zhuxingxi)
    return  eval("Xuexi_Ziwei.#{zhuxingxi}_xings()")
  end
  def Xuexi_Ziwei.xuazhuxingxi()
    a = ["ziweixingxi","tianfuxingxi","liujixing","liushaxing"]
    n = rand(a.length)
    return a[n]
  end
  def Xuexi_Ziwei.zhuxingxi_xuaxiong()
    a = ["ziweixingxi","tianfuxingxi","liujixing","liushaxing"]
    n = rand(a.length)
    zhuxingxi = a[n]
    notxuaxiong_zhuxingxis = a - [zhuxingxi]
    xuaxiongs = Xuexi_Ziwei.zhuxingxi_xings_str(zhuxingxi)
    notxuaxiongs = []
    notxuaxiong_zhuxingxis.each do |notxuaxiong_zhuxingxi|
      notxuaxiongs += Xuexi_Ziwei.zhuxingxi_xings_str(notxuaxiong_zhuxingxi)
    end
    return zhuxingxi,xuaxiongs,notxuaxiongs
  end

  # 紫微星系：紫微、天機、太陽、武曲、天同、廉貞共有六顆。
  # 天府星系：天府、太陰、貪狼、巨門、天相、天梁、七殺、破軍共有八顆。
  # 六煞星或煞星：羊刃（擎羊星）、陀羅、火星、鈴星、天空、地劫，對凶星的威力有加強的作用，對吉星有減弱吉星效果。
  # 六吉星或吉星：文昌、文曲、天魁、天鉞、左輔、右弼，對凶星的威力有減弱的作用，對吉星有增強吉星效果。
  # 紫微星系六顆，依序為紫微、天機、太陽、武曲、天同、廉貞；1-6
  def Xuexi_Ziwei.make_star_keys(which_star,xings)
    return eval("Xuexi_Ziwei.make_#{which_star}_keys(xings)")
  end
  def Xuexi_Ziwei.make_astar_keys(xings)
    xing_keys = []
    xings.each do |xing|
      xing_keys.push(Xuexi_Ziwei.make_astar_key(xing))
    end
    return xing_keys
  end
  def Xuexi_Ziwei.astar_str(xing)
    k = Xuexi_Ziwei.make_astar_key(xing)
    s = Pm.t("xuexi_ziwei.xing.#{k}")
    return Xuexi_Ziwei.str_with_xing(s)
  end
  def Xuexi_Ziwei.xing_str_with_xing(xing_key)
    s = Pm.t("xuexi_ziwei.xing.#{xing_key}")
    return Xuexi_Ziwei.str_with_xing(s)
  end
  def Xuexi_Ziwei.str_with_xing(s)
    xing = Pm.t("xuexi_ziwei.xing.xing")
    if (s[s.length - 1] != xing) then
      s += xing
    end
    return s
  end
  def Xuexi_Ziwei.str_without_xing(s)
    xing = Pm.t("xuexi_ziwei.xing.xing")
    if (s[s.length - 1] == xing) then
      s = s[0..s.length-1]
    end
    return s
  end
  def Xuexi_Ziwei.make_astar_key(xing)
    return "dis_astar.star_#{xing}"
  end
  def Xuexi_Ziwei.make_star_pan_ids(which_star,xings)
    return eval("Xuexi_Ziwei.make_#{which_star}_pan_ids(xings)")
  end
  def Xuexi_Ziwei.make_astar_pan_ids(xings)
    a = []
    xings.each do |xing|
      a.push(Xuexi_Ziwei.make_astar_pan_id(xing))
    end
    return a
  end
  def Xuexi_Ziwei.make_astar_pan_id(xing)
    return "astar#{xing}"
  end
  def Xuexi_Ziwei.make_bstar_keys(xings)
    xing_keys = []
    xings.each do |xing|
      xing_keys.push(Xuexi_Ziwei.make_bstar_key(xing))
    end
    return xing_keys
  end
  def Xuexi_Ziwei.make_bstar_key(xing)
    return "dis_bstar.star_#{xing}"
  end
  def Xuexi_Ziwei.make_bstar_pan_ids(xings)
    a = []
    xings.each do |xing|
      a.push(Xuexi_Ziwei.make_bstar_pan_id(xing))
    end
    return a
  end
  def Xuexi_Ziwei.make_bstar_pan_id(xing)
    return "bstar#{xing}"
  end
  def Xuexi_Ziwei.make_doctor_keys(xings)
    xing_keys = []
    xings.each do |xing|
      xing_keys.push(Xuexi_Ziwei.make_doctor_key(xing))
    end
    return xing_keys
  end
  def Xuexi_Ziwei.make_doctor_key(xing)
    return "dis_doctor.doctor_#{xing}"
  end
  def Xuexi_Ziwei.make_doctor_pan_ids(xings)
    a = []
    xings.each do |xing|
      a.push(Xuexi_Ziwei.make_doctor_pan_id(xing))
    end
    return a
  end
  def Xuexi_Ziwei.make_doctor_pan_id(xing)
    return "doctor#{xing}"
  end

  def Xuexi_Ziwei.make_yeargod_keys(xings)
    xing_keys = []
    xings.each do |xing|
      xing_keys.push(Xuexi_Ziwei.make_yeargod_key(xing))
    end
    return xing_keys
  end
  def Xuexi_Ziwei.make_yeargod_key(xing)
    return "dis_yeargod.yeargod_#{xing}"
  end
  def Xuexi_Ziwei.make_yeargod_pan_ids(xings)
    a = []
    xings.each do |xing|
      a.push(Xuexi_Ziwei.make_yeargod_pan_id(xing))
    end
    return a
  end
  def Xuexi_Ziwei.make_yeargod_pan_id(xing)
    return "yeargod#{xing}"
  end

  def Xuexi_Ziwei.make_yearstar_keys(xings)
    xing_keys = []
    xings.each do |xing|
      xing_keys.push(Xuexi_Ziwei.make_yearstar_key(xing))
    end
    return xing_keys
  end
  def Xuexi_Ziwei.make_yearstar_key(xing)
    return "dis_yearstar.yearstar_#{xing}"
  end
  def Xuexi_Ziwei.make_yearstar_pan_ids(xings)
    a = []
    xings.each do |xing|
      a.push(Xuexi_Ziwei.make_yearstar_pan_id(xing))
    end
    return a
  end
  def Xuexi_Ziwei.make_yearstar_pan_id(xing)
    return "yearstar#{xing}"
  end
  def Xuexi_Ziwei.make_god_keys(xings)
    xing_keys = []
    xings.each do |xing|
      xing_keys.push(Xuexi_Ziwei.make_god_key(xing))
    end
    return xing_keys
  end
  def Xuexi_Ziwei.make_god_key(xing)
    return "dis_god.god_#{xing}"
  end
  def Xuexi_Ziwei.make_god_pan_ids(xings)
    a = []
    xings.each do |xing|
      a.push(Xuexi_Ziwei.make_god_pan_id(xing))
    end
    return a
  end
  def Xuexi_Ziwei.make_god_pan_id(xing)
    return "god#{xing}"
  end

  def Xuexi_Ziwei.ziweixingxi_xings()
    return ["astar1","astar2","astar3","astar4","astar5","astar6"]
  end
  def Xuexi_Ziwei.ziweixingxi_xings_str()
    x = ["dis_astar.star_1","dis_astar.star_2","dis_astar.star_3",
      "dis_astar.star_4","dis_astar.star_5","dis_astar.star_6"]
    a = []
    x.each do |k|
      a.push(Pm.t("xuexi_ziwei.xing.#{k}"))
    end
    return a
  end
  # 天府星系則有八顆，依序為天府、太陰、貪狼、巨門、天相、天梁、七殺和破軍，7-14
  def Xuexi_Ziwei.tianfuxingxi_xings()
    return ["astar7","astar8","astar9","astar10","astar11","astar12","astar13","astar14"]
  end
  def Xuexi_Ziwei.tianfuxingxi_xings_str()
    x = ["dis_astar.star_7","dis_astar.star_8","dis_astar.star_9",
         "dis_astar.star_10","dis_astar.star_11","dis_astar.star_12",
         "dis_astar.star_13","dis_astar.star_14"]
    a = []
    x.each do |k|
      a.push(Pm.t("xuexi_ziwei.xing.#{k}"))
    end
    return a
  end
  # 主星也包含六吉星 15,16,17,18,24,25
  def Xuexi_Ziwei.liujixing_xings()
    return ["astar15","astar16","astar17","astar18","astar24","astar25"]
  end
  def Xuexi_Ziwei.liujixing_xings_str()
    x = ["dis_astar.star_15","dis_astar.star_16","dis_astar.star_17",
      "dis_astar.star_18","dis_astar.star_24","dis_astar.star_25"]
    a = []
    x.each do |k|
      a.push(Pm.t("xuexi_ziwei.xing.#{k}"))
    end
    return a
  end
  def Xuexi_Ziwei.check_liujixing_xings(xings)
    a = Xuexi_Ziwei.liujixing_xings()
    x = Xuexi_Ziwei.make_astar_pan_ids(xings)
    liujixings = a & x
    b = []
    liujixings.each do |liujixing|
      n = x.index(liujixing)
      b.push(xings[n])
    end
    return b
  end
  # 六凶星。20,21,22,23,b1,b2
  def Xuexi_Ziwei.liushaxing_xings()
    return ["astar20","astar21","astar22","astar23","bstar1","bstar2"]
  end
  def Xuexi_Ziwei.liushaxing_xings_str()
    x = ["dis_astar.star_20","dis_astar.star_21","dis_astar.star_22",
      "dis_astar.star_23","dis_bstar.star_1","dis_bstar.star_2"]
    a = []
    x.each do |k|
      a.push(Pm.t("xuexi_ziwei.xing.#{k}"))
    end
    return a
  end
  def Xuexi_Ziwei.check_liushaxing_xings(which_star,xings)
    a = Xuexi_Ziwei.liushaxing_xings()
    x = Xuexi_Ziwei.make_star_pan_ids(which_star,xings)
    liushaxings = a & x
    b = []
    liushaxings.each do |liushaxing|
      n = x.index(liushaxing)
      b.push(xings[n])
    end
    return b
  end

  # 星、曜：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xingyao_xuaxiong()
    a = ["beidouzhu3xing","beidouzhu4xing","nandouzhu3xing","nandouzhu4xing","zhongtianzhu3xing","zhongtianzhu4xing"]
    n = rand(a.length)
    xingyao = a[n]
    notxuaxiong_xingyaos = a - [xingyao]
    xuaxiongs = Xuexi_Ziwei.xingyao_xings_str(xingyao)
    notxuaxiongs = []
    notxuaxiong_xingyaos.each do |notxuaxiong_xingyao|
      notxuaxiongs += Xuexi_Ziwei.xingyao_xings_str(notxuaxiong_xingyao)
    end
    return xingyao,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.xingyao_xings(xingyao)
    return  eval("Xuexi_Ziwei.#{xingyao}_xings()")
  end
  def Xuexi_Ziwei.xingyao_xings_str(xingyao)
    return  eval("Xuexi_Ziwei.#{xingyao}_xings_str()")
  end
  # 北斗主星：（學習效果：了解基本定義或說明。）
  # 紫微、武曲、巨門、貪狼、廉貞、破軍
  def Xuexi_Ziwei.beidouzhu3xing_xings()
    return ["astar1","astar4","astar10","astar9","astar6","astar14"]
  end
  def Xuexi_Ziwei.beidouzhu3xing_xings_str()
    x = ["dis_astar.star_1","dis_astar.star_4","dis_astar.star_10",
      "dis_astar.star_9","dis_astar.star_6","dis_astar.star_14"]
    a = []
    x.each do |k|
      a.push(Pm.t("xuexi_ziwei.xing.#{k}"))
    end
    return a
  end
  # 北斗助星：（學習效果：了解基本定義或說明。）
  # 祿存、羊刃（擎羊）、陀羅、左輔、右弼、文曲
  def Xuexi_Ziwei.beidouzhu4xing_xings()
    return ["astar19","astar20","astar21","astar17","astar18","astar16"]
  end
  def Xuexi_Ziwei.beidouzhu4xing_xings_str()
    x = ["dis_astar.star_19","dis_astar.star_20","dis_astar.star_21",
      "dis_astar.star_17","dis_astar.star_18","dis_astar.star_16"]
    a = []
    x.each do |k|
      a.push(Pm.t("xuexi_ziwei.xing.#{k}"))
    end
    return a
  end
  # 南斗主星：（學習效果：了解基本定義或說明。）
  # 天府、天機、天相、天梁、天同、七殺
  def Xuexi_Ziwei.nandouzhu3xing_xings()
    return ["astar7","astar2","astar11","astar12","astar5","astar13"]
  end
  def Xuexi_Ziwei.nandouzhu3xing_xings_str()
    x = ["dis_astar.star_7","dis_astar.star_2","dis_astar.star_11",
      "dis_astar.star_12","dis_astar.star_5","dis_astar.star_13"]
    a = []
    x.each do |k|
      a.push(Pm.t("xuexi_ziwei.xing.#{k}"))
    end
    return a
  end
  # 南斗助星：（學習效果：了解基本定義或說明。）
  # 火星、鈴星、天魁、天鉞、文昌
  def Xuexi_Ziwei.nandouzhu4xing_xings()
    return ["astar22","astar23","astar24","astar25","astar15"]
  end
  def Xuexi_Ziwei.nandouzhu4xing_xings_str()
    x = ["dis_astar.star_22","dis_astar.star_23","dis_astar.star_24",
      "dis_astar.star_25","dis_astar.star_15"]
    a = []
    x.each do |k|
      a.push(Pm.t("xuexi_ziwei.xing.#{k}"))
    end
    return a
  end
  # 中天主星：（學習效果：了解基本定義或說明。）
  # 太陽、太陰
  def Xuexi_Ziwei.zhongtianzhu3xing_xings()
    return ["astar3","astar8"]
  end
  def Xuexi_Ziwei.zhongtianzhu3xing_xings_str()
    x = ["dis_astar.star_3","dis_astar.star_8"]
    a = []
    x.each do |k|
      a.push(Pm.t("xuexi_ziwei.xing.#{k}"))
    end
    return a
  end
  # 中天助星：（學習效果：了解基本定義或說明。）
  # 天空、地劫
  def Xuexi_Ziwei.zhongtianzhu4xing_xings()
    return ["bstar2","bstar1"]
  end
  def Xuexi_Ziwei.zhongtianzhu4xing_xings_str()
    x = ["dis_bstar.star_2","dis_bstar.star_1"]
    a = []
    x.each do |k|
      a.push(Pm.t("xuexi_ziwei.xing.#{k}"))
    end
    return a
  end

  # 四化：（學習效果：了解四化星有那些，天干對應各四化星曜，須背記、熟記。）
  def Xuexi_Ziwei.sihuas()
    a = []
    (0...4).each do |sihua|
      a.push(Pm.t("xuexi_ziwei.sihua.sihua_#{sihua}"))
    end
    return a
  end
  def Xuexi_Ziwei.notsihuas()
    a = Pm.t("xuexi_ziwei.sihua.notsihua").split(//)
    return a
  end
  def Xuexi_Ziwei.sihua_xings_str(tiangan)
    a = []
    (0...4).each do |sihua|
      xing = Star.GetDefaultFourHuaStarValue(Star::PAN_FH_STEVEN,tiangan,sihua)
      a.push(Pm.t("xuexi_ziwei.xing.dis_astar.star_#{xing}"))
    end
    return a
  end
  def Xuexi_Ziwei.sihua_xings()
    a = []
    (1..10).each do |tiangan|
      a.push(Xuexi_Ziwei.tiangan_sihua_xings(tiangan))
    end
    return a
  end
  def Xuexi_Ziwei.tiangan_sihua_xings(tiangan)
    a = []
    (0...4).each do |sihua|
      xing = Star.GetDefaultFourHuaStarValue(Star::PAN_FH_STEVEN,tiangan,sihua)
      a.push([xing,sihua])
    end
    return a
  end
  def Xuexi_Ziwei.sihua_xuaxiong()
    q1key = "taitou"
    xuaxiongs = Xuexi_Ziwei.sihuas()
    notxuaxiongs = Xuexi_Ziwei.notsihuas()
    return q1key,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.sihua_xuaxiong2()
    q1key = "taitou"
    tiangan_strs = Xuexi_Ziwei.tiangan_data()
    sihuas = Xuexi_Ziwei.sihuas()
    xuaxiongs = []
    notxuaxiongs = []
    (1..10).each do |tiangan|
      sihua_xings_str = Xuexi_Ziwei.sihua_xings_str(tiangan)
      xuaxiongs += Xuexi_Ziwei.sihua_xuaxiong2_strs(tiangan_strs[tiangan - 1],sihua_xings_str,sihuas)
      wrong_tiangan = rand(10)
      wrong_tiangan += 1 if (wrong_tiangan == 0)
      wrong_tiangan += 1 if (wrong_tiangan == tiangan)
      wrong_sihua_xings_str = Xuexi_Ziwei.sihua_xings_str(wrong_tiangan)
      notxuaxiongs += Xuexi_Ziwei.sihua_xuaxiong2_strs(tiangan_strs[tiangan - 1],wrong_sihua_xings_str,sihuas)
    end
    notxuaxiongs.uniq!
    return q1key,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.sihua_xuaxiong2_strs(tiangan_str,sihua_xings_str,sihuas)
    a = []
    sihua_xings_str.each_with_index do |sihua_xing_str,i|
      a.push(Xuexi_Ziwei.tiangan_sihua_xings_question(tiangan_str,sihuas[i],sihua_xing_str))
    end
    return a
  end
  def Xuexi_Ziwei.tiangan_sihua_xings_str(tiangan)
    sihuas = Xuexi_Ziwei.sihuas()
    sihua_xings_str = Xuexi_Ziwei.sihua_xings_str(tiangan)
    tiangan_str = Xuexi_Ziwei.tiangan_str(tiangan)
    return Xuexi_Ziwei.sihua_xuaxiong2_strs(tiangan_str,sihua_xings_str,sihuas)
  end
  def Xuexi_Ziwei.tiangan_sihua_xings_question(tiangan_str,sihua_str,sihua_xing_str)
    return Pm.t("xuexi_ziwei.sihua.question",:q1 => tiangan_str,:q2 =>sihua_str,:q3 => sihua_xing_str)
  end

  # 十二宮
  def Xuexi_Ziwei.shiergong_xuaxiong()
    q1key = "shiergong"
    xuaxiongs = Xuexi_Ziwei.shiergongs()
    notxuaxiongs = Xuexi_Ziwei.notshiergongs()
    return q1key,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.shiergongs()
    a = []
    (1..12).each do |gong|
      a.push(Xuexi_Ziwei.gong_str(gong))
    end
    return a
  end
  def Xuexi_Ziwei.notshiergongs()
    a = Pm.t("xuexi_ziwei.gong.shiergong.not_gongs").split(" ")
    return a
  end
  def Xuexi_Ziwei.shiergong_xuaxiong2(whichgong)
    q1key = whichgong
    xuaxiongs = []
    notxuaxiongs = []
    (1..12).each do |gong|
      xuaxiongs += Xuexi_Ziwei.shiergong_strs(gong,whichgong)
      notxuaxiongs += Xuexi_Ziwei.not_shiergong_strs(gong,whichgong)
    end
    notxuaxiongs.uniq!
    return q1key,xuaxiongs,notxuaxiongs
  end

  def Xuexi_Ziwei.shiergong_strs(gong,whichgong)
    return Xuexi_Ziwei.find_shiergong_strs(gong,whichgong)
  end
  def Xuexi_Ziwei.not_shiergong_strs(gong,whichgong)
    return Xuexi_Ziwei.find_shiergong_not_strs(gong,whichgong)
  end
  def Xuexi_Ziwei.find_shiergong_strs(gong,whichgong)
    a = []
    if (whichgong == "bengong") then
      a.push(Xuexi_Ziwei.shiergong_ben_gong_strs(gong,Xuexi_Ziwei.ben_gong(gong)))
    elsif (whichgong == "duigong") then
      a.push(Xuexi_Ziwei.shiergong_dui_gong_strs(gong,Xuexi_Ziwei.dui_gong(gong)))
    elsif (whichgong == "lingong") then
      a.push(Xuexi_Ziwei.shiergong_lin_gong_strs(gong,Xuexi_Ziwei.lin_gong(gong,1)))
      a.push(Xuexi_Ziwei.shiergong_lin_gong_strs(gong,Xuexi_Ziwei.lin_gong(gong,-1)))
    end
    return a
  end
  def Xuexi_Ziwei.find_shiergong_not_strs(gong,whichgong)
    wrong_gong = Xuexi_Ziwei.rand_not_gong(gong)
    a = []
    if (whichgong == "bengong") then
      a.push(Xuexi_Ziwei.shiergong_ben_gong_strs(gong,Xuexi_Ziwei.ben_gong(wrong_gong)))
    elsif (whichgong == "duigong") then
      a.push(Xuexi_Ziwei.shiergong_dui_gong_strs(gong,Xuexi_Ziwei.dui_gong(wrong_gong)))
    elsif (whichgong == "lingong") then
      n = rand(10)
      n = 2 if n == 1
      a.push(Xuexi_Ziwei.shiergong_lin_gong_strs(gong,Xuexi_Ziwei.lin_gong(gong,n)))
      a.push(Xuexi_Ziwei.shiergong_lin_gong_strs(gong,Xuexi_Ziwei.lin_gong(gong,-n)))
    end
    return a
  end
  def Xuexi_Ziwei.rand_not_gong(gong)
    wrong_gong = Xuexi_Ziwei.check_gong(rand(12))
    wrong_gong = Xuexi_Ziwei.check_gong(wrong_gong + 1) if (wrong_gong == gong)
    return wrong_gong
  end
  def Xuexi_Ziwei.ben_gong(gong)
    return gong
  end
  def Xuexi_Ziwei.check_gong(gong)
    newgong = Pm.TestNo12(gong)
    newgong = 12 if newgong == 0
    return newgong
  end
  def Xuexi_Ziwei.dui_gong(gong)
    return Xuexi_Ziwei.check_gong(gong + 6)
  end
  def Xuexi_Ziwei.lin_gong(gong,lin_count)
    return Xuexi_Ziwei.check_gong(gong + lin_count)
  end
  def Xuexi_Ziwei.shiergong_ben_gong_strs(gong,bengong)
    return Xuexi_Ziwei.shiergong_question(bengong,gong,"bengong")
  end
  def Xuexi_Ziwei.shiergong_dui_gong_strs(gong,duigong)
    return Xuexi_Ziwei.shiergong_question(duigong,gong,"duigong")
  end
  def Xuexi_Ziwei.shiergong_lin_gong_strs(gong,lingong)
    return Xuexi_Ziwei.shiergong_question(lingong,gong,"lingong")
  end
  def Xuexi_Ziwei.gong_str(gong)
    return Pm.t("xuexi_ziwei.gong.gong_#{gong}")
  end
  def Xuexi_Ziwei.gong_str_with_gong(gong)
    s = Pm.t("xuexi_ziwei.gong.gong_#{gong}")
    return Xuexi_Ziwei.str_with_gong(s)
  end
  def Xuexi_Ziwei.str_with_gong(s)
    gong = Pm.t("xuexi_ziwei.gong.gong")
    if (s[s.length - 1] != gong) then
      s += gong
    end
    return s
  end
  def Xuexi_Ziwei.shiergong_str(str)
    return Pm.t("xuexi_ziwei.gong.shiergong.#{str}")
  end
  def Xuexi_Ziwei.shiergong_question(gong1,gong2,shiergong_key)
    gong1 = Xuexi_Ziwei.gong_str(gong1)
    gong2 = Xuexi_Ziwei.gong_str(gong2)
    shiergong_str = Xuexi_Ziwei.shiergong_str(shiergong_key)
    return Pm.t("xuexi_ziwei.gong.shiergong.question.#{shiergong_key}",:q1 => gong1,:q2 =>gong2,:q3 => shiergong_str)
  end

  # 三合：（學習效果：了解基本定義或說明，須背記。）
  # 六合：（學習效果：了解基本定義或說明。）
  # 三方四正：（學習效果：了解基本定義或說明，須背記。）
  def Xuexi_Ziwei.rand_3634()
    a = ["sanhe","liuhe","sanfangsizheng"]
    n = rand(a.length)
    return a[n]
  end
  def Xuexi_Ziwei.shiergong_3634_xuaxiong2(which3634,oStar,nPanType)
    q1key = which3634
    xuaxiongs = []
    notxuaxiongs = []
    xuaxiongs += eval("Xuexi_Ziwei.shiergong_3634_strs(which3634,oStar,nPanType)")
    notxuaxiongs += eval("Xuexi_Ziwei.shiergong_not_3634_strs(which3634,oStar,nPanType)")

    notxuaxiongs.uniq!
    return q1key,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.shiergong_3634_strs(which3634,oStar,nPanType)
    if (which3634 == "sanhe") then
      return Xuexi_Ziwei.shiergong_dizhi_sanhe_gong_strs(oStar,nPanType)
    elsif (which3634 == "liuhe") then
      return Xuexi_Ziwei.shiergong_liuhe_strs(oStar,nPanType)
    elsif (which3634 == "sanfangsizheng") then
      return Xuexi_Ziwei.shiergong_gong_sanfangsizheng_strs(oStar,nPanType)
    end
  end
  def Xuexi_Ziwei.shiergong_not_3634_strs(which3634,oStar,nPanType)
    if (which3634 == "sanhe") then
      return Xuexi_Ziwei.shiergong_not_dizhi_sanhe_strs(oStar,nPanType)
    elsif (which3634 == "liuhe") then
      return Xuexi_Ziwei.shiergong_not_liuhe_strs(oStar,nPanType)
    elsif (which3634 == "sanfangsizheng") then
      return Xuexi_Ziwei.shiergong_not_sanfangsizheng_strs(oStar,nPanType)
    end
  end
  def Xuexi_Ziwei.shiergong3634_str(gong,which3634,oStar,nPanType)
    return Xuexi_Ziwei.find_shiergong3634_str(gong,which3634,oStar,nPanType)
  end
  def Xuexi_Ziwei.find_shiergong3634_str(gong,which3634,oStar,nPanType)
    a = []
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    if (which3634 == "sanhe") then
      a.push(Xuexi_Ziwei.shiergong_find_dizhi_sanhe_gong_str(dizhi,oStar,nPanType))
    elsif (which3634 == "liuhe") then
      a.push(Xuexi_Ziwei.shiergong_find_dizhi_liuhe_gong_str(dizhi,oStar,nPanType))
    elsif (which3634 == "sanfangsizheng") then
      a.push(Xuexi_Ziwei.shiergong_find_dizhi_sanfangsizheng_str(dizhi,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.find_dizhi_sanhe(dizhi)
    @@sanhe_xings.each do |sanhe|
      if (sanhe.include?(dizhi)) then
        return sanhe
      end
    end
    return nil
  end
  def Xuexi_Ziwei.find_shiergong3634_not_sanhe(dizhi)
    sanhes = Xuexi_Ziwei.not_sanhes(dizhi)
    sanhes.each do |sanhe|
      if (sanhe.include?(dizhi)) then
        return sanhe
      end
    end
    return nil
  end
  def Xuexi_Ziwei.shiergong_find_dizhi_sanhe_gong_str(dizhi,oStar,nPanType)
    a = []
    sanhe = Xuexi_Ziwei.find_dizhi_sanhe(dizhi)
    a.push(Xuexi_Ziwei.shiergong_dizhi_sanhe_gong_str(sanhe,oStar,nPanType))

    return a
  end
  def Xuexi_Ziwei.shiergong_dizhi_sanhe_gong_strs(oStar,nPanType)
    a = []
    @@sanhe_xings.each do |sanhe|
      a.push(Xuexi_Ziwei.shiergong_dizhi_sanhe_gong_str(sanhe,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.shiergong_not_dizhi_sanhe_strs(oStar,nPanType)
    a = []
    sanhes = Xuexi_Ziwei.not_sanhes()
    sanhes.each do |sanhe|
      a.push(Xuexi_Ziwei.shiergong_dizhi_sanhe_gong_str(sanhe,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.shiergong_dizhi_sanhe_gong_str(sanhe_dizhi,oStar,nPanType)
    gong1 = oStar.g_House_GetHouseId(nPanType,sanhe_dizhi[0])
    gong2 = oStar.g_House_GetHouseId(nPanType,sanhe_dizhi[1])
    gong3 = oStar.g_House_GetHouseId(nPanType,sanhe_dizhi[2])
    return Xuexi_Ziwei.shiergong_gong_sanhe_str([gong1,gong2,gong3])
  end
  def Xuexi_Ziwei.shiergong_gong_sanhe_str(sanhe_gong)
    q1 = Pm.t("xuexi_ziwei.gong.gong_#{sanhe_gong[0]}")
    q2 = Pm.t("xuexi_ziwei.gong.gong_#{sanhe_gong[1]}")
    q3 = Pm.t("xuexi_ziwei.gong.gong_#{sanhe_gong[2]}")
    return Pm.t("xuexi_ziwei.gong.shiergong.question.sanhe",:q1 => q1,:q2 => q2,:q3 => q3)
  end

  @@liu_hes = [
                [1,2], # 子丑合
                [3,12],  # 寅亥合
                [4,11],  # 卯戌合
                [5,10],  # 辰酉合
                [6,9],  # 巳申合
                [7,8]  # 午未合
              ]
  def Xuexi_Ziwei.find_dizhi_liuhe(dizhi)
    @@liu_hes.each do |liuhe|
      if (liuhe.include?(dizhi)) then
        return liuhe
      end
    end
    return nil
  end
  def Xuexi_Ziwei.shiergong_find_dizhi_liuhe_gong_str(dizhi,oStar,nPanType)
    a = []
    liuhe = Xuexi_Ziwei.find_dizhi_liuhe(dizhi)
    a.push(Xuexi_Ziwei.shiergong_dizhi_liuhe_gong_str(liuhe,oStar,nPanType))

    return a
  end
  def Xuexi_Ziwei.shiergong_liuhe_strs(oStar,nPanType)
    a = []
    @@liu_hes.each do |liuhe|
      a.push(Xuexi_Ziwei.shiergong_dizhi_liuhe_gong_str(liuhe,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.shiergong_not_liuhe_strs(oStar,nPanType)
    a = []
    liuhes = Xuexi_Ziwei.not_liuhes()
    liuhes.each do |liuhe|
      a.push(Xuexi_Ziwei.shiergong_dizhi_liuhe_gong_str(liuhe,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.not_liuhes()
    a = []
    while a.length < 10
      liuhe = Xuexi_Ziwei.not_liuhe()
      if (!a.include?(liuhe)) then
        a.push(liuhe)
      end
    end
    return a
  end
  def Xuexi_Ziwei.check_liuhe(liuhe2)
    @@liu_hes.each do |liuhe1|
      if (liuhe1.sort == liuhe2.sort) then
        return true
      end
    end
    return false
  end
  def Xuexi_Ziwei.not_liuhe()
    liuhe = Xuexi_Ziwei.get_not_liuhe()
    while (Xuexi_Ziwei.check_liuhe(liuhe))
      liuhe = Xuexi_Ziwei.get_not_liuhe()
    end
    return liuhe
  end
  def Xuexi_Ziwei.get_not_liuhe()
    a = []
    while a.length < 2
      dizhi = Xuexi_Ziwei.rand_dizhi()
      if (!a.include?(dizhi)) then
        a.push(dizhi)
      end
    end
    return a.sort
  end
  def Xuexi_Ziwei.shiergong_find_gong_liuhe(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    liuhe = Xuexi_Ziwei.find_dizhi_liuhe(dizhi)
    liuhe -= [dizhi]
    gong_he = oStar.g_House_GetHouseId(nPanType,liuhe[0])
    return [gong,gong_he]
  end
  def Xuexi_Ziwei.shiergong_dizhi_liuhe_gong_str(liuhe_dizhi,oStar,nPanType)
    gong1 = oStar.g_House_GetHouseId(nPanType,liuhe_dizhi[0])
    gong2 = oStar.g_House_GetHouseId(nPanType,liuhe_dizhi[1])
    return Xuexi_Ziwei.shiergong_gong_liuhe_str([gong1,gong2])
  end
  def Xuexi_Ziwei.shiergong_gong_liuhe_str(liuhe_gong)
    q1 = Pm.t("xuexi_ziwei.gong.gong_#{liuhe_gong[0]}")
    q2 = Pm.t("xuexi_ziwei.gong.gong_#{liuhe_gong[1]}")
    return Pm.t("xuexi_ziwei.gong.shiergong.question.liuhe",:q1 => q1,:q2 => q2)
  end

  # sanfangsizheng
  def Xuexi_Ziwei.shiergong_find_dizhi_sanfangsizheng_str(dizhi,oStar,nPanType)
    a = []
    sanfangsizheng = Xuexi_Ziwei.find_dizhi_sanfangsizheng(dizhi,oStar,nPanType)
    a.push(Xuexi_Ziwei.shiergong_dizhi_sanfangsizheng_str(sanfangsizheng))

    return a
  end
  def Xuexi_Ziwei.shiergong_gong_sanfangsizheng_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      sanfangsizheng = Xuexi_Ziwei.find_gong_sanfangsizheng(gong,oStar,nPanType)
      a.push(Xuexi_Ziwei.shiergong_gong_sanfangsizheng_str(sanfangsizheng))
    end
    return a
  end
  def Xuexi_Ziwei.shiergong_not_sanfangsizheng_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      sanfangsizheng = Xuexi_Ziwei.shiergong_find_gong_not_sanfangsizheng(gong,oStar,nPanType)
      a.push(Xuexi_Ziwei.shiergong_gong_sanfangsizheng_str(sanfangsizheng))
    end
    return a
  end
  def Xuexi_Ziwei.shiergong_find_gong_not_sanfangsizheng(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    sanhe = Xuexi_Ziwei.find_shiergong3634_not_sanhe(dizhi)
    sanhe -= [dizhi]
    gong2 = oStar.g_House_GetHouseId(nPanType,sanhe[0])
    gong3 = oStar.g_House_GetHouseId(nPanType,sanhe[1])

    duigong = oStar.g_House_GetHouseId(nPanType,Xuexi_Ziwei.dui_gong(Xuexi_Ziwei.check_gong(rand(12))))
    return [gong,gong2,gong3,duigong]
  end
  def Xuexi_Ziwei.find_dizhi_sanfangsizheng(dizhi,oStar,nPanType)
    sanhe = Xuexi_Ziwei.find_dizhi_sanhe(dizhi)
    sanhe -= [dizhi]
    duigong_dizhi = Xuexi_Ziwei.dui_gong(dizhi)
    return [dizhi,sanhe[0],sanhe[1],duigong_dizhi]
  end
  def Xuexi_Ziwei.find_gong_sanfangsizheng(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    sanhe = Xuexi_Ziwei.find_dizhi_sanhe(dizhi)
    sanhe -= [dizhi]
    gong2 = oStar.g_House_GetHouseId(nPanType,sanhe[0])
    gong3 = oStar.g_House_GetHouseId(nPanType,sanhe[1])

    duigong = Xuexi_Ziwei.dui_gong(gong)
    return [gong,gong2,gong3,duigong]
  end
  def Xuexi_Ziwei.shiergong_dizhi_sanfangsizheng_str(sanfangsizheng_dizhi,oStar,nPanType)
    gong0 = oStar.g_House_GetHouseId(nPanType,sanfangsizheng_dizhi[0])
    gong1 = oStar.g_House_GetHouseId(nPanType,sanfangsizheng_dizhi[1])
    gong2 = oStar.g_House_GetHouseId(nPanType,sanfangsizheng_dizhi[2])
    gong3 = oStar.g_House_GetHouseId(nPanType,sanfangsizheng_dizhi[3])
    sanfangsizheng_gong = [gong0,gong1,gong2,gong3]
    Xuexi_Ziwei.shiergong_gong_sanfangsizheng_str(sanfangsizheng_gong)
  end
  def Xuexi_Ziwei.shiergong_gong_sanfangsizheng_str(sanfangsizheng)
    q1 = Pm.t("xuexi_ziwei.gong.gong_#{sanfangsizheng[0]}")
    q2 = Pm.t("xuexi_ziwei.gong.gong_#{sanfangsizheng[1]}")
    q3 = Pm.t("xuexi_ziwei.gong.gong_#{sanfangsizheng[2]}")
    q4 = Pm.t("xuexi_ziwei.gong.gong_#{sanfangsizheng[3]}")
    return Pm.t("xuexi_ziwei.gong.shiergong.question.sanfangsizheng",:q1 => q1,:q2 => q2,:q3 => q3,:q4 => q4)
  end

  # 宮干 宮支 宮五行
  def Xuexi_Ziwei.gong_gdw_xuaxiong2(which_gdw,oStar,nPanType)
    q1key = which_gdw
    xuaxiongs = []
    notxuaxiongs = []
    xuaxiongs += eval("Xuexi_Ziwei.gong_gdw_strs(which_gdw,oStar,nPanType)")
    notxuaxiongs += eval("Xuexi_Ziwei.gong_not_gdw_strs(which_gdw,oStar,nPanType)")

    notxuaxiongs.uniq!
    return q1key,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.gong_gdw_strs(which_gdw,oStar,nPanType)
    if (which_gdw == "tiangan") then
      return Xuexi_Ziwei.pan_gong_tiangan_strs(oStar,nPanType)
    elsif (which_gdw == "dizhi") then
      return Xuexi_Ziwei.pan_gong_dizhi_strs(oStar,nPanType)
    elsif (which_gdw == "wuxing") then
      return Xuexi_Ziwei.pan_gong_wuxing_strs(oStar,nPanType)
    end
  end
  def Xuexi_Ziwei.pan_gong_tiangan_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a.push(Xuexi_Ziwei.pan_gong_tiangan_str(gong,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_tiangan_str(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    tiangan = oStar.gHouse_GetSky(nPanType,dizhi)
    return Xuexi_Ziwei.gong_tiangan_str(gong,tiangan)
  end
  def Xuexi_Ziwei.gong_tiangan_str(gong,tiangan)
    q1 = Xuexi_Ziwei.gong_str_with_gong(gong)
    q2 = Pm.t("xuexi_ziwei.tiangan.tiangan_#{tiangan}")
    return Pm.t("xuexi_ziwei.gong.tiangan.question",:q1 => q1,:q2 => q2)
  end
  def Xuexi_Ziwei.pan_gong_dizhi_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a.push(Xuexi_Ziwei.pan_gong_dizhi_str(gong,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_dizhi_str(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    return Xuexi_Ziwei.gong_dizhi_str(gong,dizhi)
  end
  def Xuexi_Ziwei.gong_dizhi_str(gong,dizhi)
    q1 = Xuexi_Ziwei.gong_str_with_gong(gong)
    q2 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi}")
    return Pm.t("xuexi_ziwei.gong.dizhi.question",:q1 => q1,:q2 => q2)
  end
  def Xuexi_Ziwei.pan_gong_wuxing_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a.push(Xuexi_Ziwei.pan_gong_wuxing_str(gong,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_wuxing_str(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    wuxing = oStar.gHouse_Five(nPanType,dizhi)
    return Xuexi_Ziwei.gong_wuxing_str(gong,wuxing)
  end
  def Xuexi_Ziwei.gong_wuxing_str(gong,wuxing)
    q1 = Xuexi_Ziwei.gong_str_with_gong(gong)
    q2 = Pm.t("xuexi_ziwei.wuxing.wuxing_#{wuxing}")
    return Pm.t("xuexi_ziwei.gong.wuxing.question",:q1 => q1,:q2 => q2)
  end
  def Xuexi_Ziwei.gong_not_gdw_strs(which_gdw,oStar,nPanType)
    if (which_gdw == "tiangan") then
      return Xuexi_Ziwei.pan_not_gong_tiangan_strs(oStar,nPanType)
    elsif (which_gdw == "dizhi") then
      return Xuexi_Ziwei.pan_not_gong_dizhi_strs(oStar,nPanType)
    elsif (which_gdw == "wuxing") then
      return Xuexi_Ziwei.pan_not_gong_wuxing_strs(oStar,nPanType)
    end
  end
  def Xuexi_Ziwei.pan_not_gong_tiangan_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a.push(Xuexi_Ziwei.pan_not_gong_tiangan_str(gong,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.pan_not_gong_tiangan_str(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    tiangan = oStar.gHouse_GetSky(nPanType,dizhi)
    n = Xuexi_Ziwei.rand_not_in(10,tiangan)
    return Xuexi_Ziwei.gong_tiangan_str(gong,n)
  end
  def Xuexi_Ziwei.pan_not_gong_dizhi_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a.push(Xuexi_Ziwei.pan_not_gong_dizhi_str(gong,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.pan_not_gong_dizhi_str(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    n = Xuexi_Ziwei.rand_not_in(12,dizhi)
    return Xuexi_Ziwei.gong_dizhi_str(gong,n)
  end
  def Xuexi_Ziwei.pan_not_gong_wuxing_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a.push(Xuexi_Ziwei.pan_not_gong_wuxing_str(gong,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.pan_not_gong_wuxing_str(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    wuxing = oStar.gHouse_Five(nPanType,dizhi)
    n = Xuexi_Ziwei.rand_not_in(5,wuxing)
    return Xuexi_Ziwei.gong_wuxing_str(gong,n)
  end
  def Xuexi_Ziwei.rand_not_in(count,not_in_n)
    a = (1..count).to_a - [not_in_n]
    n = rand(a.length)
    return a[n]
  end

  # 坐、守：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.gong_xing_xuaxiong2(which_xing,oStar,nPanType)
    q1key = which_xing
    xuaxiongs = []
    notxuaxiongs = []
    xuaxiongs += Xuexi_Ziwei.pan_gong_xing_strs(which_xing,oStar,nPanType)
    notxuaxiongs += Xuexi_Ziwei.pan_gong_not_xing_strs(which_xing,oStar,nPanType)

    notxuaxiongs.uniq!
    return q1key,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.pan_gong_xing_strs(which_xing,oStar,nPanType)
    if (which_xing == "zuoshou") then
      return Xuexi_Ziwei.pan_gong_zuoshou_strs(oStar,nPanType)
    elsif (which_xing == "hui") then
      return Xuexi_Ziwei.pan_gong_hui_strs(oStar,nPanType)
    elsif (which_xing == "zhao") then
      return Xuexi_Ziwei.pan_gong_zhao_strs(oStar,nPanType)
    elsif (which_xing == "chong") then
      return Xuexi_Ziwei.pan_gong_chong_strs(oStar,nPanType)
    elsif (which_xing == "gongshou") then
      return Xuexi_Ziwei.pan_gong_gongshou_strs(oStar,nPanType)
    end
  end
  def Xuexi_Ziwei.pan_gong_zuoshou_xings(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    bOnlyMain = true
    bOppIfNoMain = false
    xings,bUseOpp = oStar.gHouse_GetAStars(nPanType,dizhi,bOnlyMain,bOppIfNoMain)
    return xings
  end
  def Xuexi_Ziwei.pan_gong_zuoshou_xings_pan_id(gong,oStar,nPanType)
    xings = Xuexi_Ziwei.pan_gong_zuoshou_xings(gong,oStar,nPanType)
    return Xuexi_Ziwei.make_astar_pan_ids(xings)
  end
  def Xuexi_Ziwei.pan_gong_zuoshou_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a += Xuexi_Ziwei.pan_gong_zuoshou_str(gong,oStar,nPanType)
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_zuoshou_str(gong,oStar,nPanType)
    xings = Xuexi_Ziwei.pan_gong_zuoshou_xings(gong,oStar,nPanType)
    return Xuexi_Ziwei.astar_zuoshou_gong_strs(xings,gong)
  end
  def Xuexi_Ziwei.astar_zuoshou_gong_strs(xings,gong)
    xing_keys = Xuexi_Ziwei.make_astar_keys(xings)
    a = []
    xing_keys.each do |xing_key|
      a.push(Xuexi_Ziwei.astar_zuoshou_gong_str(xing_key,gong))
    end
    if (a.length == 0) then
      q1 = Xuexi_Ziwei.gong_str_with_gong(gong)
      a.push(Pm.t("xuexi_ziwei.gong.xing.question.zuoshou_wuzhuxing",:q1 => q1))
    end
    return a
  end
  def Xuexi_Ziwei.astar_zuoshou_gong_str(xing_key,gong)
    q1 = Xuexi_Ziwei.xing_str_with_xing(xing_key)
    q2 = Xuexi_Ziwei.gong_str_with_gong(gong)
    return Pm.t("xuexi_ziwei.gong.xing.question.zuoshou",:q1 => q1,:q2 => q2)
  end

  def Xuexi_Ziwei.pan_gong_hui_xings(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    sanfangsizheng_dizhi = Xuexi_Ziwei.find_dizhi_sanfangsizheng(dizhi,oStar,nPanType)
    bOnlyMain = false
    bOppIfNoMain = false
    dizhi1 = sanfangsizheng_dizhi[1]
    dizhi2 = sanfangsizheng_dizhi[2]
    gong1 = oStar.g_House_GetHouseId(nPanType,dizhi1)
    gong2 = oStar.g_House_GetHouseId(nPanType,dizhi2)
    xings1,bUseOpp = oStar.gHouse_GetAStars(nPanType,dizhi1,bOnlyMain,bOppIfNoMain)
    xings2,bUseOpp = oStar.gHouse_GetAStars(nPanType,dizhi2,bOnlyMain,bOppIfNoMain)
    return dizhi1,xings1,dizhi2,xings2
  end
  def Xuexi_Ziwei.pan_gong_hui_xings_pan_id(gong,oStar,nPanType)
    dizhi1,xings1,dizhi2,xings2 = Xuexi_Ziwei.pan_gong_hui_xings(gong,oStar,nPanType)
    a = Xuexi_Ziwei.make_astar_pan_ids(xings1)
    a += Xuexi_Ziwei.make_astar_pan_ids(xings2)
    return a
  end
  def Xuexi_Ziwei.pan_gong_hui_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a += Xuexi_Ziwei.pan_gong_hui_str(gong,oStar,nPanType)
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_hui_str(gong,oStar,nPanType)
    dizhi1,xings1,dizhi2,xings2 = Xuexi_Ziwei.pan_gong_hui_xings(gong,oStar,nPanType)
    a = Xuexi_Ziwei.astar_hui_gong_strs(xings1,dizhi1,gong)
    a += Xuexi_Ziwei.astar_hui_gong_strs(xings2,dizhi2,gong)
    return a
  end
  def Xuexi_Ziwei.astar_hui_gong_strs(xings,dizhi_hui,gong_zhu)
    xing_keys = Xuexi_Ziwei.make_astar_keys(xings)
    a = []
    xing_keys.each do |xing_key|
      a.push(Xuexi_Ziwei.astar_hui_gong_str(xing_key,dizhi_hui,gong_zhu))
    end
    return a
  end
  def Xuexi_Ziwei.astar_hui_gong_str(xing_key,dizhi_hui,gong_zhu)
    q1 = Xuexi_Ziwei.xing_str_with_xing(xing_key)
    q2 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi_hui}")
    q3 = Xuexi_Ziwei.gong_str_with_gong(gong_zhu)
    return Pm.t("xuexi_ziwei.gong.xing.question.hui",:q1 => q1,:q2 => q2,:q3 => q3)
  end

  def Xuexi_Ziwei.pan_gong_zhao_xings(gong,oStar,nPanType)
    duigong = Xuexi_Ziwei.dui_gong(gong)
    dizhi = oStar.g_House_GetEarth(nPanType,duigong)
    bOnlyMain = false
    bOppIfNoMain = false
    xings,bUseOpp = oStar.gHouse_GetAStars(nPanType,dizhi,bOnlyMain,bOppIfNoMain)
    jixings = Xuexi_Ziwei.check_liujixing_xings(xings)
    return jixings,duigong
  end
  def Xuexi_Ziwei.pan_gong_zhao_xings_pan_id(gong,oStar,nPanType)
    jixings,duigong = Xuexi_Ziwei.pan_gong_zhao_xings(gong,oStar,nPanType)
    return Xuexi_Ziwei.make_astar_pan_ids(jixings)
  end
  def Xuexi_Ziwei.pan_gong_zhao_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a += Xuexi_Ziwei.pan_gong_zhao_str(gong,oStar,nPanType)
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_zhao_str(gong,oStar,nPanType)
    jixings,duigong = Xuexi_Ziwei.pan_gong_zhao_xings(gong,oStar,nPanType)
    return Xuexi_Ziwei.star_zhao_gong_strs(jixings,duigong,gong)
  end
  def Xuexi_Ziwei.star_zhao_gong_strs(xings,gong_zhao,gong_zhu)
    xing_keys = Xuexi_Ziwei.make_astar_keys(xings)
    a = []
    xing_keys.each do |xing_key|
      a.push(Xuexi_Ziwei.star_zhao_gong_str(xing_key,gong_zhao,gong_zhu))
    end
    if (a.length == 0) then
      a.push(Xuexi_Ziwei.star_zhaowu_gong_str(gong_zhu))
    end
    return a
  end
  def Xuexi_Ziwei.star_zhao_gong_str(xing_key,gong_zhao,gong_zhu)
    q1 = Xuexi_Ziwei.gong_str_with_gong(gong_zhao)
    q2 = Xuexi_Ziwei.xing_str_with_xing(xing_key)
    q3 = Xuexi_Ziwei.gong_str_with_gong(gong_zhu)
    return Pm.t("xuexi_ziwei.gong.xing.question.zhao", :q1 => q1, :q2 => q2, :q3 => q3)
  end
  def Xuexi_Ziwei.star_zhaowu_gong_str(gong_zhu)
    q1 = Xuexi_Ziwei.gong_str_with_gong(gong_zhu)
    return Pm.t("xuexi_ziwei.gong.xing.question.zhao_wu", :q1 => q1)
  end

  def Xuexi_Ziwei.pan_gong_chong_xings(gong,oStar,nPanType)
    duigong = Xuexi_Ziwei.dui_gong(gong)
    dizhi = oStar.g_House_GetEarth(nPanType,duigong)
    bOnlyMain = false
    bOppIfNoMain = false
    xings_a,bUseOpp = oStar.gHouse_GetAStars(nPanType,dizhi,bOnlyMain,bOppIfNoMain)
    xings_b = oStar.gHouse_GetBStars(nPanType,dizhi)
    return xings_a,xings_b,duigong
  end
  def Xuexi_Ziwei.pan_gong_chong_xings_pan_id(gong,oStar,nPanType)
    xings_a,xings_b,duigong = Xuexi_Ziwei.pan_gong_chong_xings(gong,oStar,nPanType)
    shaxings_a = Xuexi_Ziwei.check_liushaxing_xings("astar",xings_a)
    shaxings_b = Xuexi_Ziwei.check_liushaxing_xings("bstar",xings_b)
    axing_keys = Xuexi_Ziwei.make_star_pan_ids("astar",shaxings_a)
    bxing_keys = Xuexi_Ziwei.make_star_pan_ids("bstar",shaxings_b)
    return axing_keys + bxing_keys
  end
  def Xuexi_Ziwei.pan_gong_chong_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a += Xuexi_Ziwei.pan_gong_chong_str(gong,oStar,nPanType)
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_chong_str(gong,oStar,nPanType)
    xings_a,xings_b,duigong = Xuexi_Ziwei.pan_gong_chong_xings(gong,oStar,nPanType)
    a = Xuexi_Ziwei.star_chong_gong_strs("astar",xings_a,duigong,gong)
    a += Xuexi_Ziwei.star_chong_gong_strs("bstar",xings_b,duigong,gong)
    if (a.length == 0) then
      a.push(Xuexi_Ziwei.star_chongwu_gong_str(gong))
    end
    return a
  end
  def Xuexi_Ziwei.star_chongwu_gong_str(gong)
    q1 = Xuexi_Ziwei.gong_str_with_gong(gong)
    return Pm.t("xuexi_ziwei.gong.xing.question.chong_wu", :q1 => q1)
  end
  def Xuexi_Ziwei.star_chong_gong_strs(which_star,xings,gong_chong,gong_zhu)
    shaxings = Xuexi_Ziwei.check_liushaxing_xings(which_star,xings)
    xing_keys = Xuexi_Ziwei.make_star_keys(which_star,shaxings)
    a = []
    xing_keys.each do |xing_key|
      a.push(Xuexi_Ziwei.star_chong_gong_str(xing_key,gong_chong,gong_zhu))
    end
    return a
  end
  def Xuexi_Ziwei.star_chong_gong_str(xing_key,gong_chong,gong_zhu)
    q1 = Xuexi_Ziwei.gong_str_with_gong(gong_chong)
    q2 = Xuexi_Ziwei.xing_str_with_xing(xing_key)
    q3 = Xuexi_Ziwei.gong_str_with_gong(gong_zhu)
    return Pm.t("xuexi_ziwei.gong.xing.question.chong", :q1 => q1, :q2 => q2, :q3 => q3)
  end

  def Xuexi_Ziwei.pan_gong_gongshou_xings(gong,oStar,nPanType)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    bOnlyMain = false
    bOppIfNoMain = false
    xings,bUseOpp = oStar.gHouse_GetAStars(nPanType,dizhi,bOnlyMain,bOppIfNoMain)
    return xings
  end
  def Xuexi_Ziwei.pan_gong_gongshou_xings_pan_id(gong,oStar,nPanType)
    xings = Xuexi_Ziwei.pan_gong_gongshou_xings(gong,oStar,nPanType)
    return Xuexi_Ziwei.make_astar_pan_ids(xings)
  end
  def Xuexi_Ziwei.pan_gong_gongshou_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a += Xuexi_Ziwei.pan_gong_gongshou_str(gong,oStar,nPanType)
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_gongshou_str(gong,oStar,nPanType)
    xings = Xuexi_Ziwei.pan_gong_gongshou_xings(gong,oStar,nPanType)
    return Xuexi_Ziwei.star_gongshou_gong_strs(xings,gong)
  end
  def Xuexi_Ziwei.star_gongshou_gong_strs(xings,gong_zhu)
    xing_keys = Xuexi_Ziwei.make_astar_keys(xings)
    if (xing_keys.length < 2) then
      return []
    end
    a = []
    a.push(Xuexi_Ziwei.star_gongshou_gong_str(xing_keys[0],xing_keys[1],gong_zhu))
    return a
  end
  def Xuexi_Ziwei.star_gongshou_gong_str(xing_key1,xing_key2,gong_zhu)
    q1 = Xuexi_Ziwei.xing_str_with_xing(xing_key1)
    q2 = Xuexi_Ziwei.xing_str_with_xing(xing_key2)
    q3 = Xuexi_Ziwei.gong_str_with_gong(gong_zhu)
    return Pm.t("xuexi_ziwei.gong.xing.question.gongshou", :q1 => q1, :q2 => q2, :q3 => q3)
  end

  def Xuexi_Ziwei.pan_gong_not_xing_strs(which_xing,oStar,nPanType)
    if (which_xing == "zuoshou") then
      return Xuexi_Ziwei.pan_gong_not_zuoshou_strs(oStar,nPanType)
    elsif (which_xing == "hui") then
      return Xuexi_Ziwei.pan_gong_not_hui_strs(oStar,nPanType)
    elsif (which_xing == "zhao") then
      return Xuexi_Ziwei.pan_gong_not_zhao_strs(oStar,nPanType)
    elsif (which_xing == "chong") then
      return Xuexi_Ziwei.pan_gong_not_chong_strs(oStar,nPanType)
    elsif (which_xing == "gongshou") then
      return Xuexi_Ziwei.pan_gong_not_gongshou_strs(oStar,nPanType)
    end
  end
  def Xuexi_Ziwei.pan_gong_not_zuoshou_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a += Xuexi_Ziwei.pan_gong_not_zuoshou_str(gong,oStar,nPanType)
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_not_zuoshou_str(gong,oStar,nPanType)
    wrong_gong = Xuexi_Ziwei.rand_not_gong(gong)
    xings = Xuexi_Ziwei.pan_gong_zuoshou_xings(wrong_gong,oStar,nPanType)
    return Xuexi_Ziwei.astar_zuoshou_gong_strs(xings,gong)
  end

  def Xuexi_Ziwei.pan_gong_not_hui_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a += Xuexi_Ziwei.pan_gong_not_hui_str(gong,oStar,nPanType)
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_not_hui_str(gong,oStar,nPanType)
    wrong_gong = Xuexi_Ziwei.check_gong(gong + 1)
    dizhi1,xings1,dizhi2,xings2 = Xuexi_Ziwei.pan_gong_hui_xings(wrong_gong,oStar,nPanType)
    a = []
    a += Xuexi_Ziwei.astar_hui_gong_strs(xings1,dizhi1,gong)
    a += Xuexi_Ziwei.astar_hui_gong_strs(xings2,dizhi2,gong)
    return a
  end
  def Xuexi_Ziwei.pan_gong_not_zhao_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a += Xuexi_Ziwei.pan_gong_not_zhao_str(gong,oStar,nPanType)
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_not_zhao_str(gong,oStar,nPanType)
    jixings = []
    while (jixings.length == 0)
      wrong_gong = Xuexi_Ziwei.rand_not_gong(gong)
      jixings,duigong = Xuexi_Ziwei.pan_gong_zhao_xings(wrong_gong,oStar,nPanType)
    end
    return Xuexi_Ziwei.star_zhao_gong_strs(jixings,duigong,gong)
  end

  def Xuexi_Ziwei.pan_gong_not_chong_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a += Xuexi_Ziwei.pan_gong_not_chong_str(gong,oStar,nPanType)
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_not_chong_str(gong,oStar,nPanType)
    shaxings = []
    while (shaxings.length == 0)
      wrong_gong = Xuexi_Ziwei.rand_not_gong(gong)
      xings_a,xings_b,duigong = Xuexi_Ziwei.pan_gong_chong_xings(wrong_gong,oStar,nPanType)
      shaxings = xings_a + xings_b
    end
    a = Xuexi_Ziwei.star_chong_gong_strs("astar",xings_a,duigong,gong)
    a += Xuexi_Ziwei.star_chong_gong_strs("bstar",xings_b,duigong,gong)
    return a
  end

  def Xuexi_Ziwei.pan_gong_not_gongshou_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a += Xuexi_Ziwei.pan_gong_not_gongshou_str(gong,oStar,nPanType)
    end
    return a
  end
  def Xuexi_Ziwei.pan_gong_not_gongshou_str(gong,oStar,nPanType)
    wrong_gong = Xuexi_Ziwei.rand_not_gong(gong)
    xings = Xuexi_Ziwei.pan_gong_gongshou_xings(wrong_gong,oStar,nPanType)
    return Xuexi_Ziwei.star_gongshou_gong_strs(xings,gong)
  end

  # 夾：（學習效果：了解基本定義或說明。）
  # 太陰、太陽 ＝》日月夾   " 宮職 "
  # 羊刃、陀羅 ＝》羊陀夾   " 宮職 "
  # 天空、地劫 ＝》 空劫夾   " 宮職 "
  # 文昌、文曲 ＝》 昌曲夾   " 宮職 "
  # 左輔、右弼 ＝》 輔弼夾   " 宮職 "
  # 天魁、天鉞 ＝》 魁鉞夾   " 宮職 "
  # 化祿、化權 ＝》 祿權夾   " 宮職 "
  # 化祿、化科 ＝》 祿科夾   " 宮職 "
  # 化權、化科 ＝》 權科夾   " 宮職 "
  #
  # 特殊
  # 廉貞、羊刃 夾 天相 ＝》刑囚夾印  （不論何宮職）
  def Xuexi_Ziwei.gong_xing_jia_xuaxiong2(oStar,nPanType)
    q1key = "jia"

    xuaxiongs,notxuaxiongs,timestamp = Xuexi_Ziwei.jieguo_gong_xing_jia_strs(oStar,nPanType)

    notxuaxiongs.uniq!
    a = xuaxiongs & notxuaxiongs
    b = notxuaxiongs - a
    return q1key,xuaxiongs,b,timestamp
  end
  def Xuexi_Ziwei.jieguo_gong_xing_jia_strs(oStar,nPanType)
    a = []
    gong = Xuexi_Ziwei.check_gong(rand(12))
    gong_jia_xing = Xuexi_Ziwei.rand_gong_jia_xing()
    key = Xuexi_Ziwei.make_gong_jia_key(gong_jia_xing)
    timestamp = Xuexi_Ziwei.jia_timestamp(gong,key)
    hUserInfo = Xuexi_Ziwei.set_pan_user_info(timestamp)
    oStar.resetUserInfo(nPanType,hUserInfo)
    a.push(Xuexi_Ziwei.jieguo_gong_xing_jia_str(gong,gong_jia_xing,oStar,nPanType))

    a2 = []
    (1..12).each do |gong|
      a2 += Xuexi_Ziwei.jieguo_gong_not_xing_jia_str(gong,timestamp,oStar,nPanType)
    end

    return a,a2,timestamp
  end
  def Xuexi_Ziwei.rand_jia_timestamp()
    gong = Xuexi_Ziwei.check_gong(rand(12))
    gong_jia_xing = Xuexi_Ziwei.rand_gong_jia_xing()
    key = Xuexi_Ziwei.make_gong_jia_key(gong_jia_xing)
    return Xuexi_Ziwei.jia_timestamp(gong,key)
  end
  def Xuexi_Ziwei.jia_timestamp(gong,key)
    return @@gong_jia_xing_timestamp[gong - 1][key].sample
  end
  def Xuexi_Ziwei.rand_gong_jia_xing()
    n = rand(3)
    if (n == 0) then
      m = rand(@@gong_jia_xings.length)
      return @@gong_jia_xings[m]
    elsif (n == 1) then
      m = rand(@@gong_jia_sihuas.length)
      return @@gong_jia_sihuas[m]
    else
      m = rand(@@gong_jia_specials.length)
      return @@gong_jia_specials[m]
    end
    return @@gong_jia_xings[0]
  end
  def Xuexi_Ziwei.jieguo_gong_xing_jia_str(gong,gong_jia_xing,oStar,nPanType)
    # gong_jia_xing = Xuexi_Ziwei.gong_jia_xing(gong,oStar,nPanType)
    return Xuexi_Ziwei.gong_jia_xing_str(gong_jia_xing,gong)
  end
  def Xuexi_Ziwei.jieguo_gong_not_xing_jia_strs(oStar,nPanType)
    a = []
    (1..12).each do |gong|
      a += Xuexi_Ziwei.jieguo_gong_not_xing_jia_str(gong,oStar,nPanType)
    end
    return a
  end
  def Xuexi_Ziwei.jieguo_gong_not_xing_jia_str(gong,timestamp,oStar,nPanType)
    a = []
    hUserInfo = Xuexi_Ziwei.set_pan_user_info(timestamp)
    hUserDefData = oStar.get_UserDefData()
    hParAll = oStar.get_ParAll()
    @@gong_jia_xings.each do |gong_jia_xing|
      legal = Xuexi_Ziwei.check_gong_jia_xing_timestamp_legal(gong,gong_jia_xing,nPanType,hUserInfo,hUserDefData,hParAll)
      if (!legal) then
        a.push(Xuexi_Ziwei.gong_jia_xing_str(gong_jia_xing,gong))
      end
    end
    @@gong_jia_sihuas.each do |gong_jia_sihua|
      legal = Xuexi_Ziwei.check_gong_jia_xing_timestamp_legal(gong,gong_jia_sihua,nPanType,hUserInfo,hUserDefData,hParAll)
      if (!legal) then
        a.push(Xuexi_Ziwei.gong_jia_xing_str(gong_jia_sihua,gong))
      end
    end
    @@gong_jia_specials.each do |gong_jia_special|
      legal = Xuexi_Ziwei.check_gong_jia_xing_timestamp_legal(gong,gong_jia_special,nPanType,hUserInfo,hUserDefData,hParAll)
      if (!legal) then
        a.push(Xuexi_Ziwei.gong_jia_xing_str(gong_jia_special,gong))
      end
    end
    return a
  end
  @@gong_jia_xings = [
                      ["dis_astar.star_3","dis_astar.star_8"],
                      ["dis_astar.star_20","dis_astar.star_21"],
                      ["dis_bstar.star_1","dis_bstar.star_2"],
                      ["dis_astar.star_15","dis_astar.star_16"],
                      ["dis_astar.star_17","dis_astar.star_18"],
                      ["dis_astar.star_24","dis_astar.star_25"]
                      ]
  @@gong_jia_sihuas = [
                      ["sihua.sihua_0","sihua.sihua_1"],
                      ["sihua.sihua_0","sihua.sihua_2"],
                      ["sihua.sihua_1","sihua.sihua_2"]
                     ]
  @@gong_jia_specials = [
                          ["dis_astar.star_6","dis_astar.star_20","dis_astar.star_11"]
                        ]

  #  gong,timestamp
@@gong_jia_xing_timestamp1 =   [
                                {"dis_astar_star_3_dis_astar_star_8"=>["19701023060", "19701025060", "19701026180", "19701110200", "19701114080", "19701115200", "19840921040", "19840926060", "19841002180", "19841003180", "19841020060", "19841025080", "19720220021", "19720222021", "19720301141", "19720302141", "19720320041", "19720322041", "20040306160", "20111109200", "20110224020", "19880207121", "19880208121", "19880219141"], "dis_astar_star_20_dis_astar_star_21"=>["19540910140", "19540911140", "19540912140", "19540913140", "19540914140", "19540915140", "19680720041", "19680721041", "19680722041", "19680723041", "19680724041", "19680725061"], "dis_bstar_star_1_dis_bstar_star_2"=>["20121015100", "20121015220", "20121016100", "20121016220", "20121017100", "20121017220", "20121018100", "20121018220", "20121019100", "20121019220", "20121020100", "20081128020", "20081128140", "20081129020", "20081129140", "20081130020", "20081130140", "20081201020", "20081201140", "20081202020", "20081202140", "20081203020", "20161001101", "20161001221", "20161002101", "20161002221", "20161003101", "20161003221", "20161004101", "20161004221", "20161005101", "20161005221", "20161006101", "20201215021", "20201215141", "20201216021", "20201216141", "20201217021", "20201217141", "20201218021", "20201218141", "20201219021", "20201219141", "20201220021"], "dis_astar_star_15_dis_astar_star_16"=>["20101106080", "20101106200", "20101107080", "20101107200", "20101108080", "20101108200", "20101109080", "20101109200", "20101110080", "20101110200", "20101111080", "20000829040", "20000829160", "20000830040", "20000830160", "20000831040", "20000831160", "20000901040", "20000901160", "20000902040", "20000902160", "20000903040", "20111027081", "20111027201", "20111028081", "20111028201", "20111029081", "20111029201", "20111030081", "20111030201", "20111031081", "20111031201", "20111101081", "20030828041", "20030828161", "20030829041", "20030829161", "20030830041", "20030830161", "20030831041", "20030831161", "20030901041", "20030901161", "20030902041"], "dis_astar_star_17_dis_astar_star_18"=>["20070417180", "20070418180", "20070419180", "20070420180", "20070421180", "20070422180", "20070423180", "20070424180", "20070425180", "20070426180", "20070427180", "20190603220", "20190604220", "20190605220", "20190606220", "20190607220", "20190608220", "20190609220", "20190610220", "20190611220", "20190612220", "20190613220", "20190929181", "20190930181", "20191001181", "20191002181", "20191003181", "20191004181", "20191005181", "20191006181", "20191007181", "20191008181", "20191009181", "20080604221", "20080605221", "20080606221", "20080607221", "20080608221", "20080609221", "20080610221", "20080611221", "20080612221", "20080613221", "20080614221"], "dis_astar_star_24_dis_astar_star_25"=>["20020212200", "20020213200", "20020214200", "20020215200", "20020216200", "20020217200", "20020218200", "20020219200", "20020220200", "20020221200", "20020222200", "20160827200", "20160828200", "20160829200", "20160830200", "20160831200", "20160901220", "20160902220", "20160903220", "20160904220", "20160905220", "20160906220", "20120123201", "20120124201", "20120125201", "20120126201", "20120127201", "20120128201", "20120129201", "20120130201", "20120131201", "20120201201", "20120202201", "20060129081", "20060130081", "20060131081", "20060201081", "20060202081", "20060203081", "20060204081", "20060205081", "20060206081", "20060207081", "20060208081"], "sihua_sihua_0_sihua_sihua_1"=>["20050220200", "20050223200", "20050301080", "20050301200", "20050311220", "20050321220", "20050324220", "20050330100", "20050330220", "20190210020", "20150105060", "20170128040", "20170205040", "20170207040", "20170214160", "20170215160", "20170226060", "20170306060", "20170308060", "20170315180", "20170316180", "20170328080", "20070621121", "20070629121", "20070701121", "20070224040", "20040123100", "20070720141", "20070728141", "20070730141", "20070804021", "20070805021", "20070819161", "20111027181", "20111102061", "20111104181", "20111106181", "20111121061", "20111125201", "20111201081", "20111203201", "20111205201", "20111220081", "20111225221"], "sihua_sihua_0_sihua_sihua_2"=>["20100225020", "20100301140", "20100302020", "20100309140", "20100311140", "20100312020", "20100327040", "20100331160", "20100401040", "20100408160", "20100410160", "20180909100", "20160918120", "20110701140", "20110705020", "20110713140", "20110725140", "20110729140", "20110829040", "20110905160", "20110917160", "20110921160", "20180909101", "20180917141", "20180918141", "20181018181", "20181028181", "20181031181", "20181106181", "20181121221", "20181201221", "20181204221", "20181220021", "20120907181", "20120928181", "20121006181", "20121008181", "20121024181", "20121101181", "20121103181", "20121123181", "20121203181", "20121206181", "20121212181"], "sihua_sihua_1_sihua_sihua_2"=>["20040125060", "20040208060", "20040209180", "20040213060", "20040217180", "20040219180", "20040223080", "20040308080", "20040309200", "20040313080", "20040317200", "20010308220", "20010309220", "20010428120", "20010430120", "20031119040", "20080828100", "20010528120", "20010530120", "20010723140", "20010804140", "20010808140", "20080222101", "20080301101", "20080303101", "20080326141", "20080403141", "20080405141", "20080408181", "20080418181", "20080513221", "20080523221", "20080607021", "20120709181", "20120719181", "20120821181", "20120917181", "20121013181", "20121017181", "20121019181", "20121121181", "20121124181", "20121130181", "20121214181"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20190409200", "20190413200", "20190421200", "20190509220", "20190513220", "20190521220", "20130227040", "20090130160", "20090709020", "20190707020", "20190711020", "20190707020", "20190711020", "20190719020", "20190805040", "20190809040", "20190817040", "20190903060", "20190907060", "20190915060", "20191003080", "20191007080", "20070219161", "20070220161", "20070315161", "20070316161", "20070320181", "20070321181", "20070413181", "20070414181", "20070418201", "20070419201", "20070512201", "20070219161", "20070220161", "20070315161", "20070316161", "20070320181", "20070321181", "20070413181", "20070414181", "20070418201", "20070419201", "20070512201"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19860414040", "19860416040", "19860428160", "19860509180", "19860514060", "19860516060", "19800916140", "19800928020", "19800928140", "19800929020", "19801002140", "19801016160", "19520603081", "19520608201", "19520609081", "19520620201", "19520629081", "19520702081", "20051010161", "20051022041", "20051022161", "20051023041", "20051026161", "20051109181"], "dis_astar_star_20_dis_astar_star_21"=>["19900526180", "19900527180", "19900528180", "19900529180", "19900530180", "19900531180", "19330921161", "19330922161", "19330923161", "19330924161", "19330925161", "19330926161"], "dis_bstar_star_1_dis_bstar_star_2"=>["20061121100", "20061121220", "20061122100", "20061122220", "20061123100", "20061123220", "20061124100", "20061124220", "20061125100", "20061125220", "20061126100", "20100115020", "20100115140", "20100116020", "20100116140", "20100117020", "20100117140", "20100118020", "20100118140", "20100119020", "20100119140", "20100120020", "20181108101", "20181108221", "20181109101", "20181109221", "20181110101", "20181110221", "20181111101", "20181111221", "20181112101", "20181112221", "20181113101", "20070119021", "20070119141", "20070120021", "20070120141", "20070121021", "20070121141", "20070122021", "20070122141", "20070123021", "20070123141", "20070124021"], "dis_astar_star_15_dis_astar_star_16"=>["20171218080", "20171218200", "20171219080", "20171219200", "20171220080", "20171220200", "20171221080", "20171221200", "20171222080", "20171222200", "20171223080", "20091018040", "20091018160", "20091019040", "20091019160", "20091020040", "20091020160", "20091021040", "20091021160", "20091022040", "20091022160", "20091023040", "20101206081", "20101206201", "20101207081", "20101207201", "20101208081", "20101208201", "20101209081", "20101209201", "20101210081", "20101210201", "20101211081", "20151013041", "20151013161", "20151014041", "20151014161", "20151015041", "20151015161", "20151016041", "20151016161", "20151017041", "20151017161", "20151018041"], "dis_astar_star_17_dis_astar_star_18"=>["20170328160", "20170329160", "20170330160", "20170331160", "20170401160", "20170402160", "20170403160", "20170404160", "20170405160", "20170406160", "20170407160", "20130608200", "20130609200", "20130610200", "20130611200", "20130612200", "20130613200", "20130614200", "20130615200", "20130616200", "20130617200", "20130618200", "20191017161", "20191018161", "20191019161", "20191020161", "20191021161", "20191022161", "20191023161", "20191024161", "20191025161", "20191026161", "20191027161", "20071210201", "20071211201", "20071212201", "20071213201", "20071214201", "20071215201", "20071216201", "20071217201", "20071218201", "20071219201", "20071220201"], "dis_astar_star_24_dis_astar_star_25"=>["19220128180", "19220129180", "19220130180", "19220131180", "19220201180", "19220202180", "19220203180", "19220204180", "19220205180", "19220206180", "19220207180", "19260213060", "19260214060", "19260215060", "19260216060", "19260217060", "19260218060", "19260219060", "19260220060", "19260221060", "19260222060", "19260223060", "19220128181", "19220129181", "19220130181", "19220131181", "19220201181", "19220202181", "19220203181", "19220204181", "19220205181", "19220206181", "19220207181", "19260213061", "19260214061", "19260215061", "19260216061", "19260217061", "19260218061", "19260219061", "19260220061", "19260221061", "19260222061", "19260223061"], "sihua_sihua_0_sihua_sihua_1"=>["20040125080", "20040208080", "20040209200", "20040213080", "20040217200", "20040219200", "20040223100", "20040308100", "20040309220", "20040313100", "20040317220", "20150625020", "20150705020", "20150708020", "20150714020", "20150722160", "20150725040", "20150804040", "20150807040", "20150813040", "20150820180", "20150823060", "20171224221", "20180101221", "20180103221", "20180108101", "20180109101", "20040306100", "20140203080", "20100201220", "20180207121", "20180208121", "20011221180", "20010124221", "20010130101", "20010201221", "20010203221", "20010218101", "20140203080", "20010301121", "20150815060", "20010130220", "20010320121", "20010325021"], "sihua_sihua_0_sihua_sihua_2"=>["20020624200", "20020702200", "20020714200", "20020718200", "20020726200", "20020811200", "20020817200", "20020911200", "20021027200", "20021104200", "20021123200", "20061220020", "20061220120", "20061220220", "20061224080", "20061224100", "20061225040", "20061225060", "20001105180", "20061227040", "20061228020", "20061229200", "20110117220", "20061024041", "20061024181", "20061024221", "20061025161", "20061026221", "20061028141", "20061030021", "20061031201", "20061102121", "20061102161", "20161129021", "20161129121", "20161129221", "20161203081", "20161203101", "20161204041", "20161204061", "20070728080", "20161206041", "20161207021", "20161208201"], "sihua_sihua_1_sihua_sihua_2"=>["20080222080", "20180527160", "20180717040", "20180721040", "20180729040", "20180820080", "20180824080", "20180901080", "20180915120", "20180916120", "20181016160", "20030930080", "20030930100", "20031001140", "20031002140", "20031003160", "20031004060", "20031004100", "20031004160", "20031005120", "20031005180", "20031009120", "20140205041", "20140206041", "20140220161", "20140306061", "20140307061", "20140321181", "20140330061", "20140405081", "20140406081", "20140420201", "20140504101", "20180709201", "20040127040", "20040313180", "20180813041", "20180823041", "20180916081", "20181012121", "20181020121", "20181109161", "20181110161", "20181203161"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20070219140", "20070220140", "20070315140", "20070316140", "20070320160", "20070321160", "20070413160", "20070414160", "20070418180", "20070419180", "20070512180", "20170916020", "20170917020", "20170921040", "20170922040", "20171015040", "20171016040", "20171021060", "20171022060", "20171114060", "20171115060", "20171119080", "20190805021", "20190809021", "20190817021", "20190903041", "20190907041", "20190915041", "20191003061", "20191007061", "20191015061", "20191101081", "20191105081", "20090511201", "20090528221", "20090601221", "20090609221", "20090627221", "20090701221", "20090627220", "20190209140", "20030320040", "20030218020", "20090824021"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19880215080", "19880220220", "19880221220", "19880303100", "19880311100", "19880313100", "19750716080", "19750728080", "19750728200", "19750729200", "19750801080", "19750814100", "20140116080", "19610915121", "19610917121", "20110805100", "19611010021", "19611015141", "20180716080", "19750929121", "19751012141", "19751024021", "19751024141", "19751025021"], "dis_astar_star_20_dis_astar_star_21"=>["20051214140", "20051215140", "20051216140", "20051217140", "20051218140", "20051219140", "19340522021", "19340523021", "19340524021", "19340525021", "19340526021", "19340527021"], "dis_bstar_star_1_dis_bstar_star_2"=>["20111125100", "20111125220", "20111126100", "20111126220", "20111127100", "20111127220", "20111128100", "20111128220", "20111129100", "20111129220", "20111130100", "20100214020", "20100214140", "20100215020", "20100215140", "20100216020", "20100216140", "20100217020", "20100217140", "20100218020", "20100218140", "20100219020", "20011215101", "20011215221", "20011216101", "20011216221", "20011217101", "20011217221", "20011218101", "20011218221", "20011219101", "20011219221", "20011220101", "20040122021", "20040122141", "20040123021", "20040123141", "20040124021", "20040124141", "20040125021", "20040125141", "20040126021", "20040126141", "20040127021"], "dis_astar_star_15_dis_astar_star_16"=>["20020113080", "20020113200", "20020114080", "20020114200", "20020115080", "20020115200", "20020116080", "20020116200", "20020117080", "20020117200", "20020118080", "20001027040", "20001027160", "20001028040", "20001028160", "20001029040", "20001029160", "20001030040", "20001030160", "20001031040", "20001031160", "20001101040", "20190106081", "20190106201", "20190107081", "20190107201", "20190108081", "20190108201", "20190109081", "20190109201", "20190110081", "20190110201", "20190111081", "20191028041", "20191028161", "20191029041", "20191029161", "20191030041", "20191030161", "20191031041", "20191031161", "20191101041", "20191101161", "20191102041"], "dis_astar_star_17_dis_astar_star_18"=>["20080406140", "20080407140", "20080408140", "20080409140", "20080410140", "20080411140", "20080412140", "20080413140", "20080414140", "20080415140", "20080416140", "20181207180", "20181208180", "20181209180", "20181210180", "20181211180", "20181212180", "20181213180", "20181214180", "20181215180", "20181216180", "20181217180", "20030926141", "20030927141", "20030928141", "20030929141", "20030930141", "20031001141", "20031002141", "20031003141", "20031004141", "20031005141", "20031006141", "20051218181", "20051219181", "20051220181", "20051221181", "20051222181", "20051223181", "20051224181", "20051225181", "20051226181", "20051227181", "20051228181"], "dis_astar_star_24_dis_astar_star_25"=>["19220128160", "19220129160", "19220130160", "19220131160", "19220201160", "19220202160", "19220203160", "19220204160", "19220205160", "19220206160", "19220207160", "19260213040", "19260214040", "19260215040", "19260216040", "19260217040", "19260218040", "19260219040", "19260220040", "19260221040", "19260222040", "19260223040", "19220128161", "19220129161", "19220130161", "19220131161", "19220201161", "19220202161", "19220203161", "19220204161", "19220205161", "19220206161", "19220207161", "19260213041", "19260214041", "19260215041", "19260216041", "19260217041", "19260218041", "19260219041", "19260220041", "19260221041", "19260222041", "19260223041"], "sihua_sihua_0_sihua_sihua_1"=>["20170203120", "20110824200", "20090823100", "20170203120", "20140203060", "20170304140", "20170307020", "20170317020", "20170320020", "20170326020", "20170403160", "20051215120", "20051217120", "20010522020", "20060106140", "20060108020", "20060114140", "20060116140", "20060118020", "20190522040", "20010926100", "20090411020", "20040714021", "20040716021", "20040720161", "20040803161", "20040804041", "20040808161", "20040812041", "20040814041", "20040819181", "20040902181", "20040903061", "20190206221", "20190209101", "20190220221", "20190306221", "20151013080", "20190311121", "20170203120", "20190406021", "20190409141", "20190420021", "20190504021"], "sihua_sihua_0_sihua_sihua_2"=>["20100223220", "20100227220", "20100301100", "20100302100", "20100307220", "20000517040", "20110702020", "20100331120", "20100401120", "20061121180", "20100423020", "20070324220", "20070329220", "20070405100", "20070408220", "20070415100", "20100223221", "20061121180", "20070504120", "20200203220", "20070514120", "20070522020", "20061121181", "20100802080", "20061123041", "20061124161", "20061125221", "20061127061", "20061129181", "20061129221", "20061130141", "20061130201", "20061201141", "20070119121", "20070121021", "20070121061", "20070123061", "20070123081", "20070124041", "20070125221", "20081203180", "20070126041", "20070127101", "20070128201"], "sihua_sihua_1_sihua_sihua_2"=>["20110310220", "20110311220", "20110323100", "20110331100", "20110402100", "20050120120", "20080222060", "20110524120", "20110601120", "20110707140", "20110710020", "20161229080", "20161231220", "20040905020", "20170102220", "20170103180", "20170103200", "20170104020", "20170104180", "20170105160", "20170105200", "20170106160", "20040822141", "20040905021", "20040919161", "20040920161", "20041004041", "20041013161", "20041019181", "20041020181", "20041103061", "20041117201", "20041118201", "20010520221", "20010527101", "20010531101", "20010730121", "20010803121", "20020805180", "20010811121", "20010917141", "20010923021", "20010925141", "20010927141"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20170201120", "20170205120", "20170213120", "20170302140", "20170306140", "20170314140", "20170401160", "20170405160", "20170413160", "20170430180", "20170504180", "20130721100", "20130722100", "20130820120", "20130821120", "20130918140", "20130919140", "20131018160", "20131019160", "20131116180", "20131117180", "20131216200", "20030613081", "20030614081", "20030713101", "20030714101", "20030811121", "20030812121", "20030910141", "20030911141", "20031009161", "20031010161", "20031107181", "20070222121", "20070226121", "20070306121", "20070323141", "20070327141", "20070404141", "20070421161", "20070425161", "20070503161", "20070521181", "20070525181"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["20100719060", "20100722060", "20100727180", "20100728060", "20100808180", "20100817080", "19181226160", "19190109180", "19190121060", "19190121180", "19190122060", "19190125180", "19160120061", "19160121181", "19160201061", "19160207201", "19160208201", "19160219081", "19510922221", "19510930221", "19511008121", "19511009121", "20141122020", "19511106141"], "dis_astar_star_20_dis_astar_star_21"=>["19770614160", "19770615160", "19770616160", "19770617180", "19770618180", "19770619180", "19581211081", "19581212081", "19581213081", "19581214081", "19581215081", "19581216081"], "dis_bstar_star_1_dis_bstar_star_2"=>["19200121100", "19200121220", "19200122100", "19200122220", "19200123100", "19200123220", "19200124100", "19200124220", "19200125100", "19200125220", "19200126100", "19200320020", "19200320140", "19200321020", "19200321140", "19200322020", "19200322140", "19200323020", "19200323140", "19200324020", "19200324140", "19200325020", "19200121101", "19200121221", "19200122101", "19200122221", "19200123101", "19200123221", "19200124101", "19200124221", "19200125101", "19200125221", "19200126101", "19200320021", "19200320141", "19200321021", "19200321141", "19200322021", "19200322141", "19200323021", "19200323141", "19200324021", "19200324141", "19200325021"], "dis_astar_star_15_dis_astar_star_16"=>["20070218080", "20070218200", "20070219080", "20070219200", "20070220080", "20070220200", "20070221080", "20070221200", "20070222080", "20070222200", "20070223080", "20011215040", "20011215160", "20011216040", "20011216160", "20011217040", "20011217160", "20011218040", "20011218160", "20011219040", "20011219160", "20011220040", "20150219081", "20150219201", "20150220081", "20150220201", "20150221081", "20150221201", "20150222081", "20150222201", "20150223081", "20150223201", "20150224081", "20141222041", "20141222161", "20141223041", "20141223161", "20141224041", "20141224161", "20141225041", "20141225161", "20141226041", "20141226161", "20141227041"], "dis_astar_star_17_dis_astar_star_18"=>["20110927120", "20110928120", "20110929120", "20110930120", "20111001120", "20111002120", "20111003120", "20111004120", "20111005120", "20111006120", "20111007120", "20051201160", "20051202160", "20051203160", "20051204160", "20051205160", "20051206160", "20051207160", "20051208160", "20051209160", "20051210160", "20051211160", "20121015121", "20121016121", "20121017121", "20121018121", "20121019121", "20121020121", "20121021121", "20121022121", "20121023121", "20121024121", "20121025121", "20151211161", "20151212161", "20151213161", "20151214161", "20151215161", "20151216161", "20151217161", "20151218161", "20151219161", "20151220161", "20151221161"], "dis_astar_star_24_dis_astar_star_25"=>["20120123140", "20120124140", "20120125140", "20120126140", "20120127140", "20120128140", "20120129140", "20120130140", "20120131140", "20120201140", "20120202140", "20180103220", "20180104220", "20180105220", "20180106220", "20180107220", "20180108220", "20180109220", "20180110220", "20180111220", "20180112220", "20180113220", "20220201141", "20220202141", "20220203141", "20220204141", "20220205141", "20220206141", "20220207141", "20220208141", "20220209141", "20220210141", "20220211141", "20060129021", "20060130021", "20060131021", "20060201021", "20060202021", "20060203021", "20060204021", "20060205021", "20060206021", "20060207021", "20060208021"], "sihua_sihua_0_sihua_sihua_1"=>["20040127040", "20040128040", "20040211160", "20040225060", "20040226060", "20040311180", "20040320060", "20040326060", "20040327060", "20040410200", "20040424080", "20150622220", "20150624100", "20150630220", "20150702220", "20150704100", "20170203100", "20150724120", "20140205040", "20170203100", "20150803120", "20150820020", "20190205201", "20190211081", "20190213201", "20190215201", "20190302081", "20190307221", "20190313101", "20190315221", "20190317221", "20190401101", "20150820140", "20051108081", "20051110201", "20051116081", "20051118081", "20051120201", "20051207101", "20051209221", "20051215101", "20051217101", "20051219221", "20060106121"], "sihua_sihua_0_sihua_sihua_2"=>["20200201200", "20200204200", "20200209080", "20200210200", "20200221080", "20200301220", "20200304220", "20200309100", "20200310220", "20200321100", "20080125160", "20170901060", "20170908180", "20170911060", "20170918180", "20170925080", "20170930080", "20171007200", "20171010080", "20171017200", "20171025100", "20171030100", "20200201201", "20200204201", "20200209081", "20200210201", "20200221081", "20200301221", "20200304221", "20200309101", "20200310221", "20200321101", "20110122180", "20081130161", "20081212161", "20081216161", "20081224161", "20090103201", "20090104201", "20100215081", "20100219201", "20100301201", "20100304201", "20100310201"], "sihua_sihua_1_sihua_sihua_2"=>["20140615080", "20140620080", "20140623200", "20140630100", "20140714100", "20140719100", "20140722220", "20140730120", "20140813120", "20140818120", "20080529160", "20080701200", "20060130040", "20060131220", "20060201200", "20060202200", "20060202220", "20060203160", "20060203180", "20060204020", "20060204180", "20060205120", "20080228041", "20080325081", "20080326081", "20080425121", "20080426121", "20080529161", "20080605201", "20080701201", "20080228040", "20080228040", "20161129160", "20110212181", "20110213181", "20110227061", "20110406081", "20110414081", "20110416201", "20110426201", "20110429201", "20110606101", "20110610101", "20110618101"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20071015020", "20071019020", "20071027020", "20071114040", "20071118040", "20071126040", "20071214060", "20071218060", "20071226060", "20080112080", "20080116080", "20170201100", "20170205100", "20170213100", "20170302120", "20170306120", "20170314120", "20170401140", "20170405140", "20170413140", "20170430160", "20170504160", "20030214221", "20030215221", "20031207180", "20170628200", "20030415021", "20030416021", "20030514041", "20030515041", "20030613061", "20030614061", "20030713081", "20130223221", "20130224221", "20090129100", "20170201100", "20130423021", "20130424021", "20130523041", "20130524041", "20130621061", "20130622061", "20130721081"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19221002080", "19221007080", "19221012200", "19221017080", "19221031100", "19221105100", "19180628020", "19180628140", "19180629140", "19180702020", "19180715040", "19180727040", "20140913201", "20140924221", "20140929101", "20141001101", "20141013221", "20141024221", "19350606021", "19350616021", "19350619021", "19350625021", "19350626141", "19350702161"], "dis_astar_star_20_dis_astar_star_21"=>["19611018180", "19611019180", "19611020180", "19611021180", "19611022180", "19611023180", "19311114201", "19311115201", "19311116201", "19311117201", "19311118201", "19311119201"], "dis_bstar_star_1_dis_bstar_star_2"=>["20110203100", "20110203220", "20110204100", "20110204220", "20110205100", "20110205220", "20110206100", "20110206220", "20110207100", "20110207220", "20110208100", "20080406020", "20080406140", "20080407020", "20080407140", "20080408020", "20080408140", "20080409020", "20080409140", "20080410020", "20080410140", "20080411020", "20100214101", "20100214221", "20100215101", "20100215221", "20100216101", "20100216221", "20100217101", "20100217221", "20100218101", "20100218221", "20100219101", "20090327021", "20090327141", "20090328021", "20090328141", "20090329021", "20090329141", "20090330021", "20090330141", "20090331021", "20090331141", "20090401021"], "dis_astar_star_15_dis_astar_star_16"=>["20170226080", "20170226200", "20170227080", "20170227200", "20170228080", "20170228200", "20170301080", "20170301200", "20170302080", "20170302200", "20170303080", "20111225040", "20111225160", "20111226040", "20111226160", "20111227040", "20111227160", "20111228040", "20111228160", "20111229040", "20111229160", "20111230040", "20120222081", "20120222201", "20120223081", "20120223201", "20120224081", "20120224201", "20120225081", "20120225201", "20120226081", "20120226201", "20120227081", "20050110041", "20050110161", "20050111041", "20050111161", "20050112041", "20050112161", "20050113041", "20050113161", "20050114041", "20050114161", "20050115041"], "dis_astar_star_17_dis_astar_star_18"=>["20011017100", "20011018100", "20011019100", "20011020100", "20011021100", "20011022100", "20011023100", "20011024100", "20011025100", "20011026100", "20011027100", "20040618140", "20040619140", "20040620140", "20040621140", "20040622140", "20040623140", "20040624140", "20040625140", "20040626140", "20040627140", "20040628140", "20200324101", "20200325101", "20200326101", "20200327101", "20200328101", "20200329101", "20200330101", "20200331101", "20200401101", "20200402101", "20200403101", "20100612141", "20100613141", "20100614141", "20100615141", "20100616141", "20100617141", "20100618141", "20100619141", "20100620141", "20100621141", "20100622141"], "dis_astar_star_24_dis_astar_star_25"=>["20120123120", "20120124120", "20120125120", "20120126120", "20120127120", "20120128120", "20120129120", "20120130120", "20120131120", "20120201120", "20120202120", "20030601200", "20031007040", "20060913141", "20171024160", "20120802221", "20020807221", "20061227200", "20120921021", "20060129001", "20020923020", "20120221120", "20220201121", "20220202121", "20220203121", "20220204121", "20220205121", "20220206121", "20220207121", "20220208121", "20220209121", "20220210121", "20220211121", "20060129000", "20070815120", "20180111201", "20060723100", "20160212001", "20120123121", "20060219001", "20070606060", "20020722220", "20120325160", "20020212121"], "sihua_sihua_0_sihua_sihua_1"=>["20041103060", "20041117200", "20041118200", "20041202080", "20041211200", "20041217220", "20041218220", "20050101100", "20150903120", "20151102160", "20050130120", "20190211180", "20190213060", "20190219180", "20190221180", "20190223060", "20190313200", "20190315080", "20190321200", "20190323200", "20190325080", "20190411220", "20140205021", "20140206021", "20140220141", "20140306041", "20140307041", "20140321161", "20140330041", "20140405061", "20140406061", "20140420181", "20140504081", "20140206141", "20140209021", "20140210021", "20140307161", "20140310041", "20140311041", "20140406181", "20140409061", "20140410061", "20140505201", "20140508081"], "sihua_sihua_0_sihua_sihua_2"=>["20100221180", "20100224180", "20100301060", "20100302180", "20100313060", "20100323200", "20100326200", "20100331080", "20100401200", "20100412080", "20100421220", "20001211140", "20001214140", "20001220140", "20001221020", "20001227040", "20001231160", "20010110160", "20010113160", "20010119160", "20010120040", "20010224180", "20130101141", "20130125141", "20130126141", "20161229081", "20161231221", "20111107040", "20170102221", "20170103181", "20170103201", "20170104021", "20170104181", "20060228021", "20060301081", "20060302121", "20060228020", "20060304061", "20060306141", "20060308021", "20060308061", "20060309041", "20060309221", "20060310021"], "sihua_sihua_1_sihua_sihua_2"=>["20040803080", "20040808080", "20040811200", "20040819100", "20040902100", "20040907100", "20040910220", "20040917120", "20041001120", "20041006120", "20111117040", "20120920100", "20120930100", "20121010100", "20121020100", "20121025100", "20121104100", "20121117100", "20121118100", "20121211100", "20121212100", "20121218100", "20110320081", "20110328081", "20110330081", "20110401201", "20110402201", "20110504221", "20110514221", "20110517221", "20110523101", "20110523221", "20110702121", "20130111101", "20130119101", "20130131101", "20130204101", "20130210201", "20130211101", "20130212121", "20130213161", "20130214121", "20130217181", "20130221141"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20090131080", "20090205080", "20090215080", "20090302100", "20090307100", "20090317100", "20090401120", "20090406120", "20090416120", "20090430140", "20090505140", "20170131080", "20170203080", "20170209080", "20170301100", "20170304100", "20170310100", "20170331120", "20170403120", "20170409120", "20170429140", "20170502140", "20130220201", "20130304201", "20130322221", "20130403221", "20030523021", "20030211201", "20130520021", "20130601021", "20130618041", "20130630041", "20130718061", "20030211201", "20030223201", "20030313221", "20030325221", "20130927100", "20030211200", "20030511021", "20030523021", "20030610041", "20030622041", "20030710061"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["20060205160", "20060208160", "20060213040", "20060214160", "20060225040", "20060307180", "20061029080", "20000504100", "19430702140", "19430711020", "19430725020", "19430730020", "19170605101", "19170613101", "19170615101", "19170617221", "19170618221", "20140112140", "19700606121", "20170923060", "20160928180", "19700616121", "20080516220", "19700705141"], "dis_astar_star_20_dis_astar_star_21"=>["19500806120", "19500807120", "19500808120", "19500809120", "19500810120", "19500811120", "20040601200", "20061126020", "20050703200", "20000724120", "20010703080", "20030706040"], "dis_bstar_star_1_dis_bstar_star_2"=>["20020314100", "20020314220", "20020315100", "20020315220", "20020316100", "20020316220", "20020317100", "20020317220", "20020318100", "20020318220", "20020319100", "20180515020", "20180515140", "20180516020", "20180516140", "20180517020", "20180517140", "20180518020", "20180518140", "20180519020", "20180519140", "20180520020", "20170226101", "20170226221", "20170227101", "20170227221", "20170228101", "20170228221", "20170301101", "20170301221", "20170302101", "20170302221", "20170303101", "20060428021", "20060428141", "20060429021", "20060429141", "20060430021", "20060430141", "20060501021", "20060501141", "20060502021", "20060502141", "20060503021"], "dis_astar_star_15_dis_astar_star_16"=>["20200324080", "20200324200", "20200325080", "20200325200", "20200326080", "20200326200", "20200327080", "20200327200", "20200328080", "20200328200", "20200329080", "20050209040", "20050209160", "20050210040", "20050210160", "20050211040", "20050211160", "20050212040", "20050212160", "20050213040", "20050213160", "20050214040", "20030402081", "20030402201", "20030403081", "20030403201", "20030404081", "20030404201", "20030405081", "20030405201", "20030406081", "20030406201", "20030407081", "20010124041", "20010124161", "20010125041", "20010125161", "20010126041", "20010126161", "20010127041", "20010127161", "20010128041", "20010128161", "20010129041"], "dis_astar_star_17_dis_astar_star_18"=>["20160407080", "20160408080", "20160409080", "20160410080", "20160411080", "20160412080", "20160413080", "20160414080", "20160415080", "20160416080", "20160417080", "20111125120", "20111126120", "20111127120", "20111128120", "20111129120", "20111130120", "20111201120", "20111202120", "20111203120", "20111204120", "20111205120", "20050409081", "20050410081", "20050411081", "20050412081", "20050413081", "20050414081", "20050415081", "20050416081", "20050417081", "20050418081", "20050419081", "20010607121", "20010608121", "20010609121", "20010610121", "20010611121", "20010612121", "20010613121", "20010614121", "20010615121", "20010616121", "20010617121"], "dis_astar_star_24_dis_astar_star_25"=>["20020212100", "20020213100", "20020214100", "20020215100", "20020216100", "20020217100", "20020218100", "20020219100", "20020220100", "20020221100", "20020222100", "20260217220", "20260218220", "20260219220", "20260220220", "20260221220", "20260222220", "20260223220", "20260224220", "20260225220", "20260226220", "20260227220", "20220201101", "20220202101", "20220203101", "20220204101", "20220205101", "20220206101", "20220207101", "20220208101", "20220209101", "20220210101", "20220211101", "20060129221", "20060130221", "20060131221", "20060201221", "20060202221", "20060203221", "20060204221", "20060205221", "20060206221", "20060207221", "20060208221"], "sihua_sihua_0_sihua_sihua_1"=>["20060120200", "20070219180", "20070222060", "20070305180", "20070320200", "20070323080", "20070403200", "20070418220", "20070421100", "20070502220", "20070516220", "20140811120", "20140826140", "20140829020", "20140909140", "20140923140", "20140925160", "20140928040", "20141009160", "20141023160", "20141025160", "20141028040", "20170129181", "20170201061", "20170212181", "20170227201", "20170302081", "20170313201", "20170327201", "20170329221", "20170401101", "20170412221", "20070812040", "20010130020", "20140204121", "20090126160", "20140302021", "20140305141", "20140316021", "20140330021", "20140401041", "20140404161", "20140415041", "20140430061"], "sihua_sihua_0_sihua_sihua_2"=>["20101111100", "20060130040", "20060131220", "20060201200", "20060202200", "20060202220", "20060203160", "20060203180", "20060204020", "20060204180", "20060205120", "20200127040", "20200128160", "20200205160", "20200206040", "20200207160", "20200225060", "20200226180", "20200305180", "20200306060", "20200307180", "20200326080", "20200125041", "20200130161", "20200201161", "20200213041", "20200223061", "20200228181", "20200301181", "20200313061", "20200324081", "20200329201", "20200331201", "20180919040", "20081001041", "20081003041", "20081109081", "20081114081", "20081124081", "20081201121", "20081215121", "20081220121", "20090103161", "20090115161"], "sihua_sihua_1_sihua_sihua_2"=>["20040122200", "20040128080", "20040130200", "20040201200", "20040216080", "20040220220", "20040226100", "20040228220", "20040301220", "20040316100", "20040321220", "20180307200", "20180311200", "20180303000", "20080917040", "20180427040", "20180428040", "20180601080", "20180611080", "20180705120", "20180730160", "20180731160", "20140608041", "20140623161", "20140627061", "20140703181", "20140705061", "20140707061", "20140722181", "20140727081", "20140802201", "20140804081", "20140806081", "20040715161", "20040719061", "20040721061", "20040730181", "20040813181", "20040818081", "20040820081", "20040829201", "20040912201", "20040916101", "20040918101"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20030412220", "20030424220", "20190210060", "20130927080", "20030610020", "20030622020", "20030710040", "20030722040", "20030808060", "20030820060", "20030907080", "20090515120", "20090529140", "20090603140", "20090613140", "20090628140", "20090703140", "20090713160", "20090727160", "20090801160", "20090811160", "20090825180", "20070221061", "20070224061", "20070302061", "20070322081", "20070325081", "20070331081", "20070420101", "20070423101", "20070429101", "20070520121", "20070523121", "20170131061", "20170203061", "20170209061", "20170301081", "20170304081", "20170310081", "20170331101", "20170403101", "20170409101", "20170429121", "20170502121"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["20140322160", "20140409180", "20140413180", "20140415060", "20140416060", "20140421180", "19841015180", "19841018060", "19841031080", "19841112080", "19841112200", "19841113200", "19431208101", "19431213101", "19431218221", "19431223101", "19440107121", "19440112121", "19870922141", "19870930041", "19871001041", "19871014161", "19871022161", "19871030061"], "dis_astar_star_20_dis_astar_star_21"=>["20030818040", "20030819040", "20030820040", "20030821040", "20030822040", "20030823040", "19470808141", "19470809141", "19470810141", "19470811141", "19470812141", "19470813141"], "dis_bstar_star_1_dis_bstar_star_2"=>["20020413100", "20020413220", "20020414100", "20020414220", "20020415100", "20020415220", "20020416100", "20020416220", "20020417100", "20020417220", "20020418100", "20060527020", "20060527140", "20060528020", "20060528140", "20060529020", "20060529140", "20060530020", "20060530140", "20060531020", "20060531140", "20060601020", "20130410101", "20130410221", "20130411101", "20130411221", "20130412101", "20130412221", "20130413101", "20130413221", "20130414101", "20130414221", "20130415101", "20150616021", "20150616141", "20150617021", "20150617141", "20150618021", "20150618141", "20150619021", "20150619141", "20150620021", "20150620141", "20150621021"], "dis_astar_star_15_dis_astar_star_16"=>["20150518080", "20150518200", "20150519080", "20150519200", "20150520080", "20150520200", "20150521080", "20150521200", "20150522080", "20150522200", "20150523080", "20010223040", "20010223160", "20010224040", "20010224160", "20010225040", "20010225160", "20010226040", "20010226160", "20010227040", "20010227160", "20010228040", "20190505081", "20190505201", "20190506081", "20190506201", "20190507081", "20190507201", "20190508081", "20190508201", "20190509081", "20190509201", "20190510081", "20050310041", "20050310161", "20050311041", "20050311161", "20050312041", "20050312161", "20050313041", "20050313161", "20050314041", "20050314161", "20050315041"], "dis_astar_star_17_dis_astar_star_18"=>["20061118060", "20061119060", "20061120060", "20070417060", "20070418060", "20070419060", "20070420060", "20070421060", "20070422060", "20070423060", "20070424060", "20171218100", "20171219100", "20171220100", "20171221100", "20171222100", "20171223100", "20171224100", "20171225100", "20171226100", "20171227100", "20171228100", "20150419061", "20150420061", "20150421061", "20150422061", "20150423061", "20150424061", "20150425061", "20150426061", "20150427061", "20150428061", "20150429061", "20190603101", "20190604101", "20190605101", "20190606101", "20190607101", "20190608101", "20190609101", "20190610101", "20190611101", "20190612101", "20190613101"], "dis_astar_star_24_dis_astar_star_25"=>["20120123080", "20120124080", "20120125080", "20120126080", "20120127080", "20120128080", "20120129080", "20120130080", "20120131080", "20120201080", "20120202080", "20060129200", "20060130200", "20060131200", "20060201200", "20060202200", "20060203200", "20060204200", "20060205200", "20060206200", "20060207200", "20060208200", "20130112061", "20130113061", "20130114061", "20130115061", "20130116061", "20130117061", "20130118061", "20130119061", "20130120061", "20130121061", "20130122061", "20160208201", "20160209201", "20160210201", "20160211201", "20160212201", "20160213201", "20160214201", "20160215201", "20160216201", "20160217201", "20160218201"], "sihua_sihua_0_sihua_sihua_1"=>["20010715080", "20010720200", "20010722220", "20010805220", "20010814100", "20090128140", "20170206160", "20010912120", "20010918020", "20011002020", "20011011140", "20100117120", "20171029080", "20150316200", "20110204120", "20090812120", "20090813120", "20191030080", "20090128140", "20090820020", "20090901020", "20090910140", "20110204121", "20110218121", "20190207140", "20110304121", "20110306141", "20110320141", "20110329021", "20110404161", "20110418161", "20110427041", "20110502161", "20140201221", "20140204101", "20140215221", "20190207140", "20140305121", "20150630160", "20041115160", "20140401021", "20140404141", "20140415021", "20140430041"], "sihua_sihua_0_sihua_sihua_2"=>["20000306040", "20160310040", "20160310140", "20160310180", "20160310220", "20160311220", "20160312200", "20160313200", "20160314160", "20160316120", "20160320180", "20060428160", "20060430060", "20060501080", "20060502060", "20060503020", "20060503040", "20060504020", "20060504100", "20100908160", "20060505040", "20160507160", "20100214021", "20100219141", "20100221141", "20100305021", "20100316041", "20100321161", "20100323161", "20100404041", "20100414061", "20100419181", "20100421181", "20060428161", "20060430061", "20060501081", "20060502061", "20060503021", "20060503041", "20060504021", "20060504101", "20080605140", "20060505041", "20100503060"], "sihua_sihua_1_sihua_sihua_2"=>["20140703160", "20140705040", "20140707040", "20140722160", "20140727060", "20140802180", "20140804060", "20140806060", "20140821180", "20140825080", "20140831200", "20210314040", "20210318160", "20210328160", "20210331160", "20210406160", "20210407040", "20210513180", "20210527180", "20210605060", "20210711080", "20210717200", "20010226181", "20010301181", "20010305061", "20010307181", "20010317061", "20010430201", "20010503201", "20010508081", "20010509201", "20010520081", "20010530201", "20210314041", "20210318161", "20210328161", "20210331161", "20210406161", "20210407041", "20210513181", "20210527181", "20210605061", "20210711081", "20210717201"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20170330080", "20000108020", "20000109020", "20000201020", "20000202020", "20030214160", "20030228160", "20030316180", "20030330180", "20030415200", "20030429200", "20070220040", "20070222040", "20070321060", "20070323060", "20070419080", "20070421080", "20070519100", "20070521100", "20070617120", "20070619120", "20070716140", "20080110021", "20080112021", "20090127041", "20090128041", "20090220041", "20090221041", "20090226061", "20090227061", "20090322061", "20090323061", "20090328081", "20190826161", "20190827161", "20190831181", "20190901181", "20190924181", "20190925181", "20190930201", "20191001201", "20191024201", "20191025201", "20191029221"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19850417020", "19850417140", "19850418140", "19850423160", "19850424160", "19850505040", "19971119060", "19971123060", "19971129180", "19971130200", "19971207080", "19971219080", "19250322021", "19250322141", "19250323141", "19250327161", "19250328161", "19250408041", "19351227221", "19360102101", "19360103101", "19360120221", "19360202121", "20140617080"], "dis_astar_star_20_dis_astar_star_21"=>["20060827160", "20060828160", "20060829160", "20060830160", "20060831160", "20060901160", "20090904141", "20090905141", "20090906141", "20090907141", "20090908141", "20090909141"], "dis_bstar_star_1_dis_bstar_star_2"=>["20100514100", "20100514220", "20100515100", "20100515220", "20100516100", "20100516220", "20100517100", "20100517220", "20100518100", "20100518220", "20100519100", "20160704020", "20160704140", "20160705020", "20160705140", "20160706020", "20160706140", "20160707020", "20160707140", "20160708020", "20160708140", "20160709020", "20100514101", "20100514221", "20100515101", "20100515221", "20100516101", "20100516221", "20100517101", "20100517221", "20100518101", "20100518221", "20100519101", "20000702021", "20000702141", "20000703021", "20000703141", "20000704021", "20000704141", "20000705021", "20000705141", "20000706021", "20000706141", "20000707021"], "dis_astar_star_15_dis_astar_star_16"=>["20060527080", "20060527200", "20060528080", "20060528200", "20060529080", "20060529200", "20060530080", "20060530200", "20060531080", "20060531200", "20060601080", "20160407040", "20160407160", "20160408040", "20160408160", "20160409040", "20160409160", "20160410040", "20160410160", "20160411040", "20160411160", "20160412040", "20180614081", "20180614201", "20180615081", "20180615201", "20180616081", "20180616201", "20180617081", "20180617201", "20180618081", "20180618201", "20180619081", "20060329041", "20060329161", "20060330041", "20060330161", "20060331041", "20060331161", "20060401041", "20060401161", "20060402041", "20060402161", "20060403041"], "dis_astar_star_17_dis_astar_star_18"=>["20180416040", "20180417040", "20180418040", "20180419040", "20180420040", "20180421040", "20180422040", "20180423040", "20180424040", "20180425040", "20180426040", "20070617080", "20070618080", "20070619080", "20070620080", "20070621080", "20070622080", "20070623080", "20070624080", "20070625080", "20070626080", "20070627080", "20130410041", "20130411041", "20130412041", "20130413041", "20130414041", "20130415041", "20130416041", "20130417041", "20130418041", "20130419041", "20130420041", "20090524081", "20090525081", "20090526081", "20090527081", "20090528081", "20090529081", "20090530081", "20090531081", "20090601081", "20090602081", "20090603081"], "dis_astar_star_24_dis_astar_star_25"=>["20220201060", "20220202060", "20220203060", "20220204060", "20220205060", "20220206060", "20220207060", "20220208060", "20220209060", "20220210060", "20220211060", "20170416220", "20170417220", "20170418220", "20170419220", "20170420220", "20170421220", "20170422220", "20170423220", "20170424220", "20170425220", "20020218060", "20220201061", "20220202061", "20220203061", "20220204061", "20220205061", "20220206061", "20220207061", "20220208061", "20220209061", "20220210061", "20220211061", "20171104101", "20171105101", "20171106101", "20171107101", "20171108101", "20171109101", "20171110101", "20171111101", "20171112101", "20171113101", "20171114101"], "sihua_sihua_0_sihua_sihua_1"=>["20070412040", "20070426180", "20070427180", "20070511060", "20070526200", "20070527200", "20070610080", "20070624220", "20070625220", "20070709100", "20140425120", "20190413040", "20190414160", "20190423040", "20190424160", "20190427160", "20190503160", "20190513060", "20190514180", "20190523060", "20190524180", "20190527180", "20190206121", "20090930020", "20190216121", "20190219121", "20190225121", "20090127120", "20190308141", "20190313021", "20190318141", "20190321141", "20190327141", "20190721101", "20190722221", "20190725221", "20190731221", "20190809121", "20190206120", "20190819121", "20110304100", "20070227140", "20041118140", "20190907141"], "sihua_sihua_0_sihua_sihua_2"=>["20200128120", "20200129120", "20070512020", "20000706220", "20200221120", "20200222120", "20200226140", "20200227140", "20200309020", "20200321020", "20200321140", "20160606080", "20160606120", "20160607060", "20160608040", "20160609040", "20160609060", "20170428160", "20160610020", "20160611020", "20160611100", "20160612200", "20170130101", "20170211101", "20170215101", "20170222221", "20170223101", "20170228121", "20170312121", "20170316121", "20170806200", "20170324121", "20170330141", "20070701181", "20070702061", "20070712061", "20070718201", "20070722201", "20070730201", "20070731081", "20070810081", "20070817221", "20070821221", "20070829221"], "sihua_sihua_1_sihua_sihua_2"=>["20160407060", "20160407100", "20160407200", "20160411160", "20160411180", "20160412120", "20160412140", "20160414080", "20160414120", "20160415100", "20160416040", "20210213140", "20210223140", "20210226140", "20210304020", "20210304140", "20210421160", "20210503040", "20210505160", "20210510160", "20210511040", "20210611180", "20040124161", "20040205161", "20040208041", "20040209041", "20040209161", "20040217161", "20040222181", "20040305181", "20040308061", "20040309061", "20040309181", "20180225161", "20180311161", "20180316161", "20180317201", "20180329201", "20180410201", "20180414201", "20141119100", "20040928061", "20180528041", "20180529041"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20190206020", "20190207020", "20190302020", "20190303020", "20190308040", "20190309040", "20190401040", "20190402040", "20190406060", "20190407060", "20190430060", "20130223140", "20130309140", "20130325160", "20130408160", "20130423180", "20130507180", "20130523200", "20130606200", "20130621220", "20130705220", "20170528100", "20070220021", "20070222021", "20070321041", "20070323041", "20070419061", "20070421061", "20070519081", "20070521081", "20070617101", "20070619101", "20070716121", "20130223141", "20130309141", "20130325161", "20130408161", "20130423181", "20130507181", "20130523201", "20130606201", "20130621221", "20130705221", "20090127020"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19800710060", "19800710180", "19800711180", "19800715200", "19800716200", "19800727080", "19590410020", "19590413140", "19590420020", "19590423140", "19590426140", "19590502140", "19251102141", "19251114021", "19251114141", "19251115021", "19251119041", "19251120041", "19730802221", "19730810221", "19730812221", "19730818101", "19730819101", "20051216180"], "dis_astar_star_20_dis_astar_star_21"=>["19951019220", "19951020220", "19951021220", "19951022220", "19951023220", "19951024220", "19400919101", "19400920101", "19400921101", "19400922101", "19400923101", "19400924101"], "dis_bstar_star_1_dis_bstar_star_2"=>["20150616100", "20150616220", "20150617100", "20150617220", "20150618100", "20150618220", "20150619100", "20150619220", "20150620100", "20150620220", "20150621100", "20090820020", "20090820140", "20090821020", "20090821140", "20090822020", "20090822140", "20090823020", "20090823140", "20090824020", "20090824140", "20090825020", "20190603101", "20190603221", "20190604101", "20190604221", "20190605101", "20190605221", "20190606101", "20190606221", "20190607101", "20190607221", "20190608101", "20020809021", "20020809141", "20020810021", "20020810141", "20020811021", "20020811141", "20020812021", "20020812141", "20020813021", "20020813141", "20020814021"], "dis_astar_star_15_dis_astar_star_16"=>["20110701080", "20110701200", "20110702080", "20110702200", "20110703080", "20110703200", "20110704080", "20110704200", "20110705080", "20110705200", "20110706080", "20050508040", "20050508160", "20050509040", "20050509160", "20050510040", "20050510160", "20050511040", "20050511160", "20050512040", "20050512160", "20050513040", "20120719081", "20120719201", "20120720081", "20120720201", "20120721081", "20120721201", "20120722081", "20120722201", "20120723081", "20120723201", "20120724081", "20050508041", "20050508161", "20050509041", "20050509161", "20050510041", "20050510161", "20050511041", "20050511161", "20050512041", "20050512161", "20050513041"], "dis_astar_star_17_dis_astar_star_18"=>["20180416020", "20180417020", "20180418020", "20180419020", "20180420020", "20180421020", "20180422020", "20180423020", "20180424020", "20180425020", "20180426020", "20120605060", "20120606060", "20120607060", "20120608060", "20120609060", "20120610060", "20120611060", "20120612060", "20120613060", "20120614060", "20120615060", "20130410021", "20130411021", "20130412021", "20130413021", "20130414021", "20130415021", "20130416021", "20130417021", "20130418021", "20130419021", "20130420021", "20010607061", "20010608061", "20010609061", "20010610061", "20010611061", "20010612061", "20010613061", "20010614061", "20010615061", "20010616061", "20010617061"], "dis_astar_star_24_dis_astar_star_25"=>["20120123040", "20120124040", "20120125040", "20120126040", "20120127040", "20120128040", "20120129040", "20120130040", "20120131040", "20120201040", "20120202040", "20060129160", "20060130160", "20060131160", "20060201160", "20060202160", "20060203160", "20060204160", "20060205160", "20060206160", "20060207160", "20060208160", "20120123041", "20120124041", "20120125041", "20120126041", "20120127041", "20120128041", "20120129041", "20120130041", "20120131041", "20120201041", "20120202041", "20070726021", "20070727021", "20070728021", "20070729021", "20070730021", "20070731021", "20070801021", "20070802021", "20070803021", "20070804021", "20070805021"], "sihua_sihua_0_sihua_sihua_1"=>["20010514020", "20010515020", "20010529140", "20010606140", "20010608160", "20010613040", "20010614040", "20010627160", "20010705160", "20010707160", "20010712040", "20190823220", "20190829220", "20190907120", "20191210060", "20190917120", "20000113200", "20070218120", "20160127020", "20191007140", "20191008020", "20191017140", "20140131181", "20140206061", "20140208181", "20140210181", "20140225061", "20140301201", "20140307081", "20140309201", "20140311201", "20140326081", "20140331221", "20170130121", "20170211121", "20170215121", "20040122180", "20170223121", "20170228141", "20170312141", "20170316141", "20170323021", "20170324141", "20170330161"], "sihua_sihua_0_sihua_sihua_2"=>["20070219080", "20160510180", "20160510220", "20160513140", "20160513220", "20160514200", "20160516120", "20160516160", "20160517100", "20160517200", "20160519220", "20060627020", "20060627060", "20060627080", "20060627120", "20060627220", "20060628060", "20060629040", "20060630040", "20100217100", "20060703200", "20060707020", "20180411221", "20180507021", "20180508021", "20180515061", "20180607061", "20180608061", "20180617101", "20180620101", "20180626101", "20180720141", "20180723141", "20070219080", "20180720141", "20120107121", "20120117121", "20120120121", "20120128021", "20120130021", "20120225021", "20120228021", "20120305021", "20120324021"], "sihua_sihua_1_sihua_sihua_2"=>["20180605060", "20180606060", "20180616100", "20180622100", "20180716140", "20180719140", "20180725140", "20180814180", "20180815180", "20180907180", "20180908180", "20080830140", "20080904180", "20080929220", "20081022220", "20081023220", "20081030020", "20081031020", "20081123020", "20081124020", "20081203060", "20081205060", "20120628061", "20120708061", "20120711061", "20120717061", "20120726061", "20120809061", "20120830061", "20120913061", "20121001061", "20121002061", "20121101061", "20030515181", "20140202140", "20030517181", "20030520041", "20030521021", "20030521041", "20030522061", "20030522201", "20030523061", "20110307160", "20030528121"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20170409040", "20170429060", "20170502060", "20170508060", "20170529080", "20170601080", "20170607080", "20170627100", "20170630100", "20170706100", "20170726100", "20090401040", "20090406040", "20090416040", "20090430060", "20090505060", "20090515060", "20090529080", "20090603080", "20090613080", "20090628080", "20090703080", "20030213121", "20030917021", "20070429040", "20090302021", "20090307021", "20090317021", "20090401041", "20090406041", "20090416041", "20090430061", "20090505061", "20091107160", "20130222121", "20070618080", "20170301021", "20170304021", "20170310021", "20170331041", "20170403041", "20170409041", "20170429061", "20170502061"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19460209080", "19460217200", "19460218200", "19460309100", "19460311100", "19460319220", "19340703040", "19340705160", "19340710160", "19340711040", "19340721180", "19340802060", "19890123181", "19890204061", "19890204181", "19890205061", "19890217081", "19890221201", "20000424121", "20000428121", "20000504021", "20000511141", "20000523141", "20000527141"], "dis_astar_star_20_dis_astar_star_21"=>["19540620140", "19540621140", "19540622140", "19540623140", "19540624140", "19540625140", "19360410041", "19360411041", "19360412041", "19360413041", "19360414041", "19360415041"], "dis_bstar_star_1_dis_bstar_star_2"=>["20180713100", "20180713220", "20180714100", "20180714220", "20180715100", "20180715220", "20180716100", "20180716220", "20180717100", "20180717220", "20180718100", "20160901020", "20160901140", "20160902020", "20160902140", "20160903020", "20160903140", "20160904020", "20160904140", "20160905020", "20160905140", "20160906020", "20170624101", "20170624221", "20170625101", "20170625221", "20170626101", "20170626221", "20170627101", "20170627221", "20170628101", "20170628221", "20170629101", "20090919021", "20090919141", "20090920021", "20090920141", "20090921021", "20090921141", "20090922021", "20090922141", "20090923021", "20090923141", "20090924021"], "dis_astar_star_15_dis_astar_star_16"=>["20030729080", "20030729200", "20030730080", "20030730200", "20030731080", "20030731200", "20030801080", "20030801200", "20030802080", "20030802200", "20030803080", "20070615040", "20070615160", "20070616040", "20070616160", "20070617040", "20070617160", "20070618040", "20070618160", "20070619040", "20070619160", "20070620040", "20130807081", "20130807201", "20130808081", "20130808201", "20130809081", "20130809201", "20130810081", "20130810201", "20130811081", "20130811201", "20130812081", "20060527041", "20060527161", "20060528041", "20060528161", "20060529041", "20060529161", "20060530041", "20060530161", "20060531041", "20060531161", "20060601041"], "dis_astar_star_17_dis_astar_star_18"=>["20140924000", "20061022000", "20101011000", "20140924000", "20030415000", "20031016000", "20140331000", "20191021000", "20060329000", "20081004000", "20190929000", "20180416000", "20020505000", "20060329000", "20161001000", "20050409000", "20181009000", "20020413000", "20151013000", "20040405000", "20120322000", "20021006000", "20110403000", "20030926000", "20190929000", "20190929000", "20181009000", "20150419000", "20121015000", "20130410000", "20180416000", "20190411000", "20080929000", "20061022000", "20110403000", "20151013000", "20130410000", "20171020000", "20030402000", "20090327000", "20080406000", "20180416000", "20021006000", "20030425000"], "dis_astar_star_24_dis_astar_star_25"=>["20220201020", "20220202020", "20220203020", "20220204020", "20220205020", "20220206020", "20220207020", "20220208020", "20220209020", "20220210020", "20220211020", "20060129140", "20060130140", "20060131140", "20060201140", "20060202140", "20060203140", "20060204140", "20060205140", "20060206140", "20060207140", "20060208140", "20120123021", "20120124021", "20120125021", "20120126021", "20120127021", "20120128021", "20120129021", "20120130021", "20120131021", "20120201021", "20120202021", "20260217141", "20260218141", "20260219141", "20260220141", "20260221141", "20260222141", "20260223141", "20260224141", "20260225141", "20260226141", "20260227141"], "sihua_sihua_0_sihua_sihua_1"=>["20140202160", "20140214160", "20140217040", "20140218040", "20140218160", "20140226160", "20140303180", "20140315180", "20140318060", "20140319060", "20140319180", "20110203060", "20110211060", "20110213060", "20110220180", "20110221180", "20110305080", "20110313080", "20110315080", "20110322200", "20110323200", "20110403100", "20110209061", "20110217061", "20110219061", "20110224181", "20110225181", "20110311081", "20110319081", "20110321081", "20110326201", "20110327201", "20110409101", "20190206081", "20190220081", "20190301201", "20190306081", "20190308101", "20190322101", "20190331221", "20190406121", "20190420121", "20091204020", "20190504121"], "sihua_sihua_0_sihua_sihua_2"=>["20120201040", "20120209040", "20120211040", "20120228040", "20120307040", "20120309040", "20120327040", "20120406040", "20120409040", "20120415040", "20120422040", "20060725080", "20060726020", "20060726060", "20060726220", "20060727060", "20060801040", "20070922200", "20060803200", "20060804040", "20060805020", "20060806180", "20070219061", "20070224181", "20070301061", "20070304061", "20070310061", "20070315181", "20070320081", "20070325201", "20070330081", "20070402081", "20070408081", "20000819201", "20000823201", "20000829101", "20000905221", "20000917221", "20000921221", "20000927101", "20000928121", "20011219220", "20080213120", "20200203080"], "sihua_sihua_1_sihua_sihua_2"=>["20140406040", "20140411160", "20140414160", "20140420160", "20140425040", "20140430180", "20140505060", "20140510180", "20140513180", "20140519180", "20140524060", "20210223120", "20181016000", "20210228120", "20020731040", "20120125040", "20210310120", "20210415140", "20210429140", "20210430020", "20210504140", "20210508020", "20080207161", "20080226161", "20080328201", "20131117040", "20010624160", "20080526041", "20080527041", "20080604081", "20080627081", "20080628081", "20080706121", "20180716120", "20080207160", "20120323040", "20081207080", "20130114040", "20010612060", "20040123120", "20020214040", "20041108160", "20140201120", "20180216160"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20130620180", "20130628180", "20130630180", "20130720200", "20130728200", "20130730200", "20130819220", "20130827220", "20130829220", "20100120200", "20190210220", "20030523160", "20030612180", "20030620180", "20030622180", "20030712200", "20030720200", "20030722200", "20030810220", "20030818220", "20030820220", "20070221220", "20100120201", "20100125201", "20100204201", "20130222101", "20130302101", "20130304101", "20130324121", "20130401121", "20130403121", "20130422141", "20130430141", "20070221221", "20070224221", "20070302221", "20091009120", "20131017020", "20070221220", "20070420021", "20070423021", "20070429021", "20070520041", "20070523041"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19410926200", "19410928200", "19411006080", "19411007080", "19411025220", "19411027220", "19310803040", "19310804040", "19310817180", "19310825180", "19310827180", "19310902060", "19470323201", "19470330081", "19470402081", "19470408101", "19470411221", "19470421221", "20160522000", "19570515121", "19570518121", "19570524121", "19570531021", "19570603141"], "dis_astar_star_20_dis_astar_star_21"=>["19960910100", "19960911100", "19960912100", "19960913120", "19960914120", "19960915120", "19930222101", "19930223101", "19930224101", "19930225101", "19930226101", "19930227101"], "dis_bstar_star_1_dis_bstar_star_2"=>["20020809100", "20020809220", "20020810100", "20020810220", "20020811100", "20020811220", "20020812100", "20020812220", "20020813100", "20020813220", "20020814100", "20171020020", "20171020140", "20171021020", "20171021140", "20171022020", "20171022140", "20171023020", "20171023140", "20171024020", "20171024140", "20171025020", "20090820101", "20090820221", "20090821101", "20090821221", "20090822101", "20090822221", "20090823101", "20090823221", "20090824101", "20090824221", "20090825101", "20051003021", "20051003141", "20051004021", "20051004141", "20051005021", "20051005141", "20051006021", "20051006141", "20051007021", "20051007141", "20051008021"], "dis_astar_star_15_dis_astar_star_16"=>["20130905080", "20130905200", "20130906080", "20130906200", "20130907080", "20130907200", "20130908080", "20130908200", "20130909080", "20130909200", "20130910080", "20130708040", "20130708160", "20130709040", "20130709160", "20130710040", "20130710160", "20130711040", "20130711160", "20130712040", "20130712160", "20130713040", "20030828081", "20030828201", "20030829081", "20030829201", "20030830081", "20030830201", "20030831081", "20030831201", "20030901081", "20030901201", "20030902081", "20160704041", "20160704161", "20160705041", "20160705161", "20160706041", "20160706161", "20160707041", "20160707161", "20160708041", "20160708161", "20160709041"], "dis_astar_star_17_dis_astar_star_18"=>["20190929220", "20190930220", "20191001220", "20191002220", "20191003220", "20191004220", "20191005220", "20191006220", "20191007220", "20191008220", "20191009220", "20111125020", "20111126020", "20111127020", "20111128020", "20111129020", "20111130020", "20111201020", "20111202020", "20111203020", "20111204020", "20111205020", "20110927221", "20110928221", "20110929221", "20110930221", "20111001221", "20111002221", "20111003221", "20111004221", "20111005221", "20111006221", "20111007221", "20061220021", "20061221021", "20061222021", "20061223021", "20061224021", "20061225021", "20061226021", "20061227021", "20061228021", "20061229021", "20061230021"], "dis_astar_star_24_dis_astar_star_25"=>["20121227200", "20121228200", "20121229200", "20121230200", "20121231200", "20130101200", "20130102200", "20130103200", "20130104200", "20130105200", "20130106200", "20060129120", "20060130120", "20060131120", "20060201120", "20060202120", "20060203120", "20060204120", "20060205120", "20060206120", "20060207120", "20060208120", "20130719101", "20130720101", "20130721101", "20130722101", "20130723101", "20130724101", "20130725101", "20130726101", "20130727101", "20130728101", "20130729101", "20160208121", "20160209121", "20160210121", "20160211121", "20160212121", "20160213121", "20160214121", "20160215121", "20160216121", "20160217121", "20160218121"], "sihua_sihua_0_sihua_sihua_1"=>["20040515180", "20040521200", "20040602200", "20040605080", "20040606080", "20040606200", "20040614200", "20040620220", "20040702220", "20040705100", "20040706100", "20140820020", "20140824020", "20140825040", "20140906040", "20140915160", "20140916160", "20140918040", "20140922040", "20140924060", "20141006060", "20141015180", "20040521201", "20040602201", "20040605081", "20040606081", "20040606201", "20040614201", "20040620221", "20040702221", "20040705101", "20040706101", "20040706221", "20010126041", "20010207041", "20010211041", "20010218161", "20010219041", "20010225061", "20010309061", "20010313061", "20010320181", "20010321061", "20010327081"], "sihua_sihua_0_sihua_sihua_2"=>["20021007020", "20021031020", "20021130020", "20021225020", "20030115020", "20030123020", "20030125020", "20060626140", "20060626180", "20010423160", "20060629160", "20160901080", "20160904020", "20160904060", "20160907060", "20160907220", "20160908040", "20100223060", "20160910200", "20160911040", "20160911180", "20160913060", "20020707021", "20020711021", "20020712021", "20020804021", "20020805021", "20020809021", "20020901021", "20020902021", "20020913021", "20021007021", "20021031021", "20170228061", "20170302061", "20170311181", "20170312181", "20170330081", "20170401081", "20170410201", "20170411201", "20170428101", "20170430101", "20170509221"], "sihua_sihua_1_sihua_sihua_2"=>["20080207140", "20080226140", "20080314180", "20080402180", "20080407220", "20080501220", "20080511020", "20080604060", "20080627060", "20080628060", "20080704100", "20160901040", "20160903180", "20160903220", "20110311020", "20160905220", "20160906200", "20160907140", "20160908160", "20160908200", "20160909020", "20160910120", "20020922021", "20021004021", "20021026021", "20021105021", "20021208021", "20030104021", "20030130021", "20040123101", "20040128221", "20040202101", "20040205101", "20121119221", "20121121221", "20121213221", "20121221221", "20121223221", "20130117221", "20130127221", "20130130221", "20130205221", "20130210141", "20130210221"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20070223200", "20070228200", "20070310200", "20070324220", "20070329220", "20070408220", "20130621160", "20090128200", "20191228181", "20070522020", "20070527020", "20070223200", "20070228200", "20070310200", "20070324220", "20070329220", "20070408220", "20130523140", "20090128201", "20030214080", "20070522020", "20070527020", "20070223201", "20070228201", "20070310201", "20070324221", "20070329221", "20070408221", "20190507021", "20190207200", "20130223080", "20070522021", "20070527021", "20130223081", "20130224081", "20130325101", "20130326101", "20130423121", "20130424121", "20130523141", "20130524141", "20130621161", "20130622161", "20130721181"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19210319060", "19210323060", "19210331060", "19210331180", "19210417080", "19210421080", "19550531100", "19550612220", "19550614100", "19550619100", "19550629120", "20050325180", "19280322181", "19280329061", "19280401061", "19280407081", "19280410201", "19280420201", "19951004181", "19951016081", "19951018201", "19951023201", "19951102201", "19951114081"], "dis_astar_star_20_dis_astar_star_21"=>["19240616100", "19240617100", "19240618100", "19240619100", "19240620100", "19240621100", "20020516140", "20040617080", "20170409220", "19670522001", "19670523001", "19670524001"], "dis_bstar_star_1_dis_bstar_star_2"=>["20120916100", "20120916220", "20120917100", "20120917220", "20120918100", "20120918220", "20120919100", "20120919220", "20120920100", "20120920220", "20120921100", "20141108020", "20141108140", "20141109020", "20141109140", "20141110020", "20141110140", "20141111020", "20141111140", "20141112020", "20141112140", "20141113020", "20030828101", "20030828221", "20030829101", "20030829221", "20030830101", "20030830221", "20030831101", "20030831221", "20030901101", "20030901221", "20030902101", "20081029021", "20081029141", "20081030021", "20081030141", "20081031021", "20081031141", "20081101021", "20081101141", "20081102021", "20081102141", "20081103021"], "dis_astar_star_15_dis_astar_star_16"=>["20190929080", "20190929200", "20190930080", "20190930200", "20191001080", "20191001200", "20191002080", "20191002200", "20191003080", "20191003200", "20191004080", "20030729040", "20030729160", "20030730040", "20030730160", "20030731040", "20030731160", "20030801040", "20030801160", "20030802040", "20030802160", "20030803040", "20190929081", "20190929201", "20190930081", "20190930201", "20191001081", "20191001201", "20191002081", "20191002201", "20191003081", "20191003201", "20191004081", "20170807041", "20170807161", "20170808041", "20170808161", "20170809041", "20170809161", "20170810041", "20170810161", "20170811041", "20170811161", "20170812041"], "dis_astar_star_17_dis_astar_star_18"=>["20010325200", "20010326200", "20010327200", "20010328200", "20010329200", "20010330200", "20010331200", "20010401200", "20010402200", "20010403200", "20010404200", "20061220000", "20061221000", "20061222000", "20061223000", "20061224000", "20061225000", "20061226000", "20061227000", "20061228000", "20061229000", "20061230000", "20011114201", "20020413201", "20020414201", "20020415201", "20020416201", "20020417201", "20020418201", "20020419201", "20020420201", "20020421201", "20020422201", "20081128001", "20081129001", "20081130001", "20081201001", "20081202001", "20081203001", "20081204001", "20081205001", "20081206001", "20081207001", "20081208001"], "dis_astar_star_24_dis_astar_star_25"=>["20220201220", "20220202220", "20220203220", "20220204220", "20220205220", "20220206220", "20220207220", "20220208220", "20220209220", "20220210220", "20220211220", "20160208100", "20160209100", "20160210100", "20160211100", "20160212100", "20160213100", "20160214100", "20160215100", "20160216100", "20160217100", "20160218100", "20120123221", "20120124221", "20120125221", "20120126221", "20120127221", "20120128221", "20120129221", "20120130221", "20120131221", "20120201221", "20120202221", "20160208101", "20160209101", "20160210101", "20160211101", "20160212101", "20160213101", "20160214101", "20160215101", "20160216101", "20160217101", "20160218101"], "sihua_sihua_0_sihua_sihua_1"=>["20171012200", "20171018200", "20171028100", "20171029220", "20171107100", "20171108220", "20171111220", "20171117220", "20171126120", "20050805100", "20171206120", "20040928020", "20190612120", "20190613120", "20190709020", "20190712140", "20190713140", "20190807040", "20190810160", "20190811160", "20190905060", "20190908180", "20010124021", "20010128141", "20010205021", "20010217021", "20010221021", "20010223041", "20010227161", "20010307041", "20010319041", "20010323041", "20010325061", "20190211161", "20190214041", "20190215041", "20190313181", "20190316061", "20190317061", "20190411201", "20190414081", "20190415081", "20190511221", "20190514101"], "sihua_sihua_0_sihua_sihua_2"=>["20160803040", "20160803140", "20160803180", "20170128020", "20160807020", "20160808200", "20160808220", "20160810160", "20160810200", "20160811180", "20160812120", "20061024080", "20200203040", "20061025020", "20061025060", "20061027200", "20061028060", "20061028220", "20061030080", "20061101180", "20160803040", "20061102040", "20160803041", "20160803141", "20160803181", "20110608140", "20160807021", "20160808201", "20160808221", "20160810161", "20160810201", "20160811181", "20160812121", "20071013181", "20071015181", "20071024061", "20071025061", "20071112201", "20071114201", "20071123081", "20071124081", "20071212221", "20071214221", "20071223101"], "sihua_sihua_1_sihua_sihua_2"=>["20010619160", "20010622160", "20010702040", "20010703040", "20010718160", "20010821180", "20010823180", "20010901060", "20010902060", "20011026200", "20011030200", "20080214080", "20080215080", "20080314120", "20080322120", "20080324120", "20080415160", "20080423160", "20080425160", "20080518200", "20080601200", "20010701140", "20081209041", "20081210041", "20090108081", "20090116081", "20090118081", "20110205121", "20140203080", "20010126120", "20110215121", "20040125080", "20110409021", "20120314201", "20120404201", "20120405201", "20120506201", "20120507201", "20120610201", "20120709201", "20120720201", "20120813201", "20120821201", "20120918201"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20070223180", "20070228180", "20070310180", "20070324200", "20070329200", "20070408200", "20070422220", "20070427220", "20070507220", "20030214060", "20031207021", "20130223060", "20130224060", "20130325080", "20130326080", "20130423100", "20130424100", "20130523120", "20130524120", "20130621140", "20130622140", "20130721160", "20170202181", "20170207181", "20170217181", "20170303201", "20170308201", "20170318201", "20170402221", "20170407221", "20170417221", "20190207180", "20071215140", "20030214061", "20030215061", "20030316081", "20030317081", "20030415101", "20030416101", "20030514121", "20030515121", "20030613141", "20030614141", "20030713161"]}
                              ]
@@gong_jia_xing_timestamp =   [
                                {"dis_astar_star_3_dis_astar_star_8"=>["19701023060", "19701025060", "19701026180", "19701110200", "19701114080", "19701115200", "19840921040", "19840926060", "19841002180", "19841003180", "19841020060", "19841025080", "19720220021", "19720222021", "19720301141", "19720302141", "19720320041", "19720322041", "20040306160", "20111109200", "20110224020", "19880207121", "19880208121", "19880219141"], "dis_astar_star_20_dis_astar_star_21"=>["19540910140", "19540911140", "19540912140", "19540913140", "19540914140", "19540915140", "19680720041", "19680721041", "19680722041", "19680723041", "19680724041", "19680725061"], "dis_bstar_star_1_dis_bstar_star_2"=>["20121015100", "20121015220", "20121016100", "20121016220", "20121017100", "20121017220", "20121018100", "20121018220", "20121019100", "20121019220", "20121020100", "20081128020", "20081128140", "20081129020", "20081129140", "20081130020", "20081130140", "20081201020", "20081201140", "20081202020", "20081202140", "20081203020", "20161001101", "20161001221", "20161002101", "20161002221", "20161003101", "20161003221", "20161004101", "20161004221", "20161005101", "20161005221", "20161006101", "20201215021", "20201215141", "20201216021", "20201216141", "20201217021", "20201217141", "20201218021", "20201218141", "20201219021", "20201219141", "20201220021"], "dis_astar_star_15_dis_astar_star_16"=>["20101106080", "20101106200", "20101107080", "20101107200", "20101108080", "20101108200", "20101109080", "20101109200", "20101110080", "20101110200", "20101111080", "20000829040", "20000829160", "20000830040", "20000830160", "20000831040", "20000831160", "20000901040", "20000901160", "20000902040", "20000902160", "20000903040", "20111027081", "20111027201", "20111028081", "20111028201", "20111029081", "20111029201", "20111030081", "20111030201", "20111031081", "20111031201", "20111101081", "20030828041", "20030828161", "20030829041", "20030829161", "20030830041", "20030830161", "20030831041", "20030831161", "20030901041", "20030901161", "20030902041"], "dis_astar_star_17_dis_astar_star_18"=>["20070417180", "20070418180", "20070419180", "20070420180", "20070421180", "20070422180", "20070423180", "20070424180", "20070425180", "20070426180", "20070427180", "20190603220", "20190604220", "20190605220", "20190606220", "20190607220", "20190608220", "20190609220", "20190610220", "20190611220", "20190612220", "20190613220", "20190929181", "20190930181", "20191001181", "20191002181", "20191003181", "20191004181", "20191005181", "20191006181", "20191007181", "20191008181", "20191009181", "20080604221", "20080605221", "20080606221", "20080607221", "20080608221", "20080609221", "20080610221", "20080611221", "20080612221", "20080613221", "20080614221"], "dis_astar_star_24_dis_astar_star_25"=>["20020212200", "20020213200", "20020214200", "20020215200", "20020216200", "20020217200", "20020218200", "20020219200", "20020220200", "20020221200", "20020222200", "20160827200", "20160828200", "20160829200", "20160830200", "20160831200", "20160901220", "20160902220", "20160903220", "20160904220", "20160905220", "20160906220", "20120123201", "20120124201", "20120125201", "20120126201", "20120127201", "20120128201", "20120129201", "20120130201", "20120131201", "20120201201", "20120202201", "20060129081", "20060130081", "20060131081", "20060201081", "20060202081", "20060203081", "20060204081", "20060205081", "20060206081", "20060207081", "20060208081"], "sihua_sihua_0_sihua_sihua_1"=>["20050220200", "20050223200", "20050301080", "20050301200", "20050311220", "20050321220", "20050324220", "20050330100", "20050330220", "20190210020", "20150105060", "20170128040", "20170205040", "20170207040", "20170214160", "20170215160", "20170226060", "20170306060", "20170308060", "20170315180", "20170316180", "20170328080", "20070621121", "20070629121", "20070701121", "20070224040", "20040123100", "20070720141", "20070728141", "20070730141", "20070804021", "20070805021", "20070819161", "20111027181", "20111102061", "20111104181", "20111106181", "20111121061", "20111125201", "20111201081", "20111203201", "20111205201", "20111220081", "20111225221"], "sihua_sihua_0_sihua_sihua_2"=>["20100225020", "20100301140", "20100302020", "20100309140", "20100311140", "20100312020", "20100327040", "20100331160", "20100401040", "20100408160", "20100410160", "20180909100", "20160918120", "20110701140", "20110705020", "20110713140", "20110725140", "20110729140", "20110829040", "20110905160", "20110917160", "20110921160", "20180909101", "20180917141", "20180918141", "20181018181", "20181028181", "20181031181", "20181106181", "20181121221", "20181201221", "20181204221", "20181220021", "20120907181", "20120928181", "20121006181", "20121008181", "20121024181", "20121101181", "20121103181", "20121123181", "20121203181", "20121206181", "20121212181"], "sihua_sihua_1_sihua_sihua_2"=>["20040125060", "20040208060", "20040209180", "20040213060", "20040217180", "20040219180", "20040223080", "20040308080", "20040309200", "20040313080", "20040317200", "20010308220", "20010309220", "20010428120", "20010430120", "20031119040", "20080828100", "20010528120", "20010530120", "20010723140", "20010804140", "20010808140", "20080222101", "20080301101", "20080303101", "20080326141", "20080403141", "20080405141", "20080408181", "20080418181", "20080513221", "20080523221", "20080607021", "20120709181", "20120719181", "20120821181", "20120917181", "20121013181", "20121017181", "20121019181", "20121121181", "20121124181", "20121130181", "20121214181"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20180221160", "20121006180", "20160211160", "20120204040", "20080212160", "20020224040"]}, # dis_astar_star_6_dis_astar_star_20_dis_astar_star_11 , "20060827040", "20020224040", "20020524100", "20060401200", "20020425080", "20160211160", "20181212120", "20020601100", "20060201160", "20120204040", "20060201160", "20080212161", "20060201161", "20021018200", "20120602100", "20120204040", "20021018200", "20080328180", "20061025080", "20160211160", "20120212040", "20161211120", "20121204220", "20120204040", "20120204040", "20060501220", "20120204040", "20020224040", "20180221160", "20060629020", "20020224040", "20080212160", "20180221160", "20060201161", "20061025080", "20080212160", "20160211161"
                                {"dis_astar_star_3_dis_astar_star_8"=>["19860414040", "19860416040", "19860428160", "19860509180", "19860514060", "19860516060", "19800916140", "19800928020", "19800928140", "19800929020", "19801002140", "19801016160", "19520603081", "19520608201", "19520609081", "19520620201", "19520629081", "19520702081", "20051010161", "20051022041", "20051022161", "20051023041", "20051026161", "20051109181"], "dis_astar_star_20_dis_astar_star_21"=>["19900526180", "19900527180", "19900528180", "19900529180", "19900530180", "19900531180", "19330921161", "19330922161", "19330923161", "19330924161", "19330925161", "19330926161"], "dis_bstar_star_1_dis_bstar_star_2"=>["20061121100", "20061121220", "20061122100", "20061122220", "20061123100", "20061123220", "20061124100", "20061124220", "20061125100", "20061125220", "20061126100", "20100115020", "20100115140", "20100116020", "20100116140", "20100117020", "20100117140", "20100118020", "20100118140", "20100119020", "20100119140", "20100120020", "20181108101", "20181108221", "20181109101", "20181109221", "20181110101", "20181110221", "20181111101", "20181111221", "20181112101", "20181112221", "20181113101", "20070119021", "20070119141", "20070120021", "20070120141", "20070121021", "20070121141", "20070122021", "20070122141", "20070123021", "20070123141", "20070124021"], "dis_astar_star_15_dis_astar_star_16"=>["20171218080", "20171218200", "20171219080", "20171219200", "20171220080", "20171220200", "20171221080", "20171221200", "20171222080", "20171222200", "20171223080", "20091018040", "20091018160", "20091019040", "20091019160", "20091020040", "20091020160", "20091021040", "20091021160", "20091022040", "20091022160", "20091023040", "20101206081", "20101206201", "20101207081", "20101207201", "20101208081", "20101208201", "20101209081", "20101209201", "20101210081", "20101210201", "20101211081", "20151013041", "20151013161", "20151014041", "20151014161", "20151015041", "20151015161", "20151016041", "20151016161", "20151017041", "20151017161", "20151018041"], "dis_astar_star_17_dis_astar_star_18"=>["20170328160", "20170329160", "20170330160", "20170331160", "20170401160", "20170402160", "20170403160", "20170404160", "20170405160", "20170406160", "20170407160", "20130608200", "20130609200", "20130610200", "20130611200", "20130612200", "20130613200", "20130614200", "20130615200", "20130616200", "20130617200", "20130618200", "20191017161", "20191018161", "20191019161", "20191020161", "20191021161", "20191022161", "20191023161", "20191024161", "20191025161", "20191026161", "20191027161", "20071210201", "20071211201", "20071212201", "20071213201", "20071214201", "20071215201", "20071216201", "20071217201", "20071218201", "20071219201", "20071220201"], "dis_astar_star_24_dis_astar_star_25"=>["19220128180", "19220129180", "19220130180", "19220131180", "19220201180", "19220202180", "19220203180", "19220204180", "19220205180", "19220206180", "19220207180", "19260213060", "19260214060", "19260215060", "19260216060", "19260217060", "19260218060", "19260219060", "19260220060", "19260221060", "19260222060", "19260223060", "19220128181", "19220129181", "19220130181", "19220131181", "19220201181", "19220202181", "19220203181", "19220204181", "19220205181", "19220206181", "19220207181", "19260213061", "19260214061", "19260215061", "19260216061", "19260217061", "19260218061", "19260219061", "19260220061", "19260221061", "19260222061", "19260223061"], "sihua_sihua_0_sihua_sihua_1"=>["20040125080", "20040208080", "20040209200", "20040213080", "20040217200", "20040219200", "20040223100", "20040308100", "20040309220", "20040313100", "20040317220", "20150625020", "20150705020", "20150708020", "20150714020", "20150722160", "20150725040", "20150804040", "20150807040", "20150813040", "20150820180", "20150823060", "20171224221", "20180101221", "20180103221", "20180108101", "20180109101", "20040306100", "20140203080", "20100201220", "20180207121", "20180208121", "20011221180", "20010124221", "20010130101", "20010201221", "20010203221", "20010218101", "20140203080", "20010301121", "20150815060", "20010130220", "20010320121", "20010325021"], "sihua_sihua_0_sihua_sihua_2"=>["20020624200", "20020702200", "20020714200", "20020718200", "20020726200", "20020811200", "20020817200", "20020911200", "20021027200", "20021104200", "20021123200", "20061220020", "20061220120", "20061220220", "20061224080", "20061224100", "20061225040", "20061225060", "20001105180", "20061227040", "20061228020", "20061229200", "20110117220", "20061024041", "20061024181", "20061024221", "20061025161", "20061026221", "20061028141", "20061030021", "20061031201", "20061102121", "20061102161", "20161129021", "20161129121", "20161129221", "20161203081", "20161203101", "20161204041", "20161204061", "20070728080", "20161206041", "20161207021", "20161208201"], "sihua_sihua_1_sihua_sihua_2"=>["20080222080", "20180527160", "20180717040", "20180721040", "20180729040", "20180820080", "20180824080", "20180901080", "20180915120", "20180916120", "20181016160", "20030930080", "20030930100", "20031001140", "20031002140", "20031003160", "20031004060", "20031004100", "20031004160", "20031005120", "20031005180", "20031009120", "20140205041", "20140206041", "20140220161", "20140306061", "20140307061", "20140321181", "20140330061", "20140405081", "20140406081", "20140420201", "20140504101", "20180709201", "20040127040", "20040313180", "20180813041", "20180823041", "20180916081", "20181012121", "20181020121", "20181109161", "20181110161", "20181203161"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20080212140", "20060201140", "20120204020", "20120403060", "20020224020", "20120403060"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19880215080", "19880220220", "19880221220", "19880303100", "19880311100", "19880313100", "19750716080", "19750728080", "19750728200", "19750729200", "19750801080", "19750814100", "20140116080", "19610915121", "19610917121", "20110805100", "19611010021", "19611015141", "20180716080", "19750929121", "19751012141", "19751024021", "19751024141", "19751025021"], "dis_astar_star_20_dis_astar_star_21"=>["20051214140", "20051215140", "20051216140", "20051217140", "20051218140", "20051219140", "19340522021", "19340523021", "19340524021", "19340525021", "19340526021", "19340527021"], "dis_bstar_star_1_dis_bstar_star_2"=>["20111125100", "20111125220", "20111126100", "20111126220", "20111127100", "20111127220", "20111128100", "20111128220", "20111129100", "20111129220", "20111130100", "20100214020", "20100214140", "20100215020", "20100215140", "20100216020", "20100216140", "20100217020", "20100217140", "20100218020", "20100218140", "20100219020", "20011215101", "20011215221", "20011216101", "20011216221", "20011217101", "20011217221", "20011218101", "20011218221", "20011219101", "20011219221", "20011220101", "20040122021", "20040122141", "20040123021", "20040123141", "20040124021", "20040124141", "20040125021", "20040125141", "20040126021", "20040126141", "20040127021"], "dis_astar_star_15_dis_astar_star_16"=>["20020113080", "20020113200", "20020114080", "20020114200", "20020115080", "20020115200", "20020116080", "20020116200", "20020117080", "20020117200", "20020118080", "20001027040", "20001027160", "20001028040", "20001028160", "20001029040", "20001029160", "20001030040", "20001030160", "20001031040", "20001031160", "20001101040", "20190106081", "20190106201", "20190107081", "20190107201", "20190108081", "20190108201", "20190109081", "20190109201", "20190110081", "20190110201", "20190111081", "20191028041", "20191028161", "20191029041", "20191029161", "20191030041", "20191030161", "20191031041", "20191031161", "20191101041", "20191101161", "20191102041"], "dis_astar_star_17_dis_astar_star_18"=>["20080406140", "20080407140", "20080408140", "20080409140", "20080410140", "20080411140", "20080412140", "20080413140", "20080414140", "20080415140", "20080416140", "20181207180", "20181208180", "20181209180", "20181210180", "20181211180", "20181212180", "20181213180", "20181214180", "20181215180", "20181216180", "20181217180", "20030926141", "20030927141", "20030928141", "20030929141", "20030930141", "20031001141", "20031002141", "20031003141", "20031004141", "20031005141", "20031006141", "20051218181", "20051219181", "20051220181", "20051221181", "20051222181", "20051223181", "20051224181", "20051225181", "20051226181", "20051227181", "20051228181"], "dis_astar_star_24_dis_astar_star_25"=>["19220128160", "19220129160", "19220130160", "19220131160", "19220201160", "19220202160", "19220203160", "19220204160", "19220205160", "19220206160", "19220207160", "19260213040", "19260214040", "19260215040", "19260216040", "19260217040", "19260218040", "19260219040", "19260220040", "19260221040", "19260222040", "19260223040", "19220128161", "19220129161", "19220130161", "19220131161", "19220201161", "19220202161", "19220203161", "19220204161", "19220205161", "19220206161", "19220207161", "19260213041", "19260214041", "19260215041", "19260216041", "19260217041", "19260218041", "19260219041", "19260220041", "19260221041", "19260222041", "19260223041"], "sihua_sihua_0_sihua_sihua_1"=>["20170203120", "20110824200", "20090823100", "20170203120", "20140203060", "20170304140", "20170307020", "20170317020", "20170320020", "20170326020", "20170403160", "20051215120", "20051217120", "20010522020", "20060106140", "20060108020", "20060114140", "20060116140", "20060118020", "20190522040", "20010926100", "20090411020", "20040714021", "20040716021", "20040720161", "20040803161", "20040804041", "20040808161", "20040812041", "20040814041", "20040819181", "20040902181", "20040903061", "20190206221", "20190209101", "20190220221", "20190306221", "20151013080", "20190311121", "20170203120", "20190406021", "20190409141", "20190420021", "20190504021"], "sihua_sihua_0_sihua_sihua_2"=>["20100223220", "20100227220", "20100301100", "20100302100", "20100307220", "20000517040", "20110702020", "20100331120", "20100401120", "20061121180", "20100423020", "20070324220", "20070329220", "20070405100", "20070408220", "20070415100", "20100223221", "20061121180", "20070504120", "20200203220", "20070514120", "20070522020", "20061121181", "20100802080", "20061123041", "20061124161", "20061125221", "20061127061", "20061129181", "20061129221", "20061130141", "20061130201", "20061201141", "20070119121", "20070121021", "20070121061", "20070123061", "20070123081", "20070124041", "20070125221", "20081203180", "20070126041", "20070127101", "20070128201"], "sihua_sihua_1_sihua_sihua_2"=>["20110310220", "20110311220", "20110323100", "20110331100", "20110402100", "20050120120", "20080222060", "20110524120", "20110601120", "20110707140", "20110710020", "20161229080", "20161231220", "20040905020", "20170102220", "20170103180", "20170103200", "20170104020", "20170104180", "20170105160", "20170105200", "20170106160", "20040822141", "20040905021", "20040919161", "20040920161", "20041004041", "20041013161", "20041019181", "20041020181", "20041103061", "20041117201", "20041118201", "20010520221", "20010527101", "20010531101", "20010730121", "20010803121", "20020805180", "20010811121", "20010917141", "20010923021", "20010925141", "20010927141"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20021023160", "20020826120", "20120607080", "20070503161", "20020301000", "20070525181"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["20100719060", "20100722060", "20100727180", "20100728060", "20100808180", "20100817080", "19181226160", "19190109180", "19190121060", "19190121180", "19190122060", "19190125180", "19160120061", "19160121181", "19160201061", "19160207201", "19160208201", "19160219081", "19510922221", "19510930221", "19511008121", "19511009121", "20141122020", "19511106141"], "dis_astar_star_20_dis_astar_star_21"=>["19770614160", "19770615160", "19770616160", "19770617180", "19770618180", "19770619180", "19581211081", "19581212081", "19581213081", "19581214081", "19581215081", "19581216081"], "dis_bstar_star_1_dis_bstar_star_2"=>["19200121100", "19200121220", "19200122100", "19200122220", "19200123100", "19200123220", "19200124100", "19200124220", "19200125100", "19200125220", "19200126100", "19200320020", "19200320140", "19200321020", "19200321140", "19200322020", "19200322140", "19200323020", "19200323140", "19200324020", "19200324140", "19200325020", "19200121101", "19200121221", "19200122101", "19200122221", "19200123101", "19200123221", "19200124101", "19200124221", "19200125101", "19200125221", "19200126101", "19200320021", "19200320141", "19200321021", "19200321141", "19200322021", "19200322141", "19200323021", "19200323141", "19200324021", "19200324141", "19200325021"], "dis_astar_star_15_dis_astar_star_16"=>["20070218080", "20070218200", "20070219080", "20070219200", "20070220080", "20070220200", "20070221080", "20070221200", "20070222080", "20070222200", "20070223080", "20011215040", "20011215160", "20011216040", "20011216160", "20011217040", "20011217160", "20011218040", "20011218160", "20011219040", "20011219160", "20011220040", "20150219081", "20150219201", "20150220081", "20150220201", "20150221081", "20150221201", "20150222081", "20150222201", "20150223081", "20150223201", "20150224081", "20141222041", "20141222161", "20141223041", "20141223161", "20141224041", "20141224161", "20141225041", "20141225161", "20141226041", "20141226161", "20141227041"], "dis_astar_star_17_dis_astar_star_18"=>["20110927120", "20110928120", "20110929120", "20110930120", "20111001120", "20111002120", "20111003120", "20111004120", "20111005120", "20111006120", "20111007120", "20051201160", "20051202160", "20051203160", "20051204160", "20051205160", "20051206160", "20051207160", "20051208160", "20051209160", "20051210160", "20051211160", "20121015121", "20121016121", "20121017121", "20121018121", "20121019121", "20121020121", "20121021121", "20121022121", "20121023121", "20121024121", "20121025121", "20151211161", "20151212161", "20151213161", "20151214161", "20151215161", "20151216161", "20151217161", "20151218161", "20151219161", "20151220161", "20151221161"], "dis_astar_star_24_dis_astar_star_25"=>["20120123140", "20120124140", "20120125140", "20120126140", "20120127140", "20120128140", "20120129140", "20120130140", "20120131140", "20120201140", "20120202140", "20180103220", "20180104220", "20180105220", "20180106220", "20180107220", "20180108220", "20180109220", "20180110220", "20180111220", "20180112220", "20180113220", "20220201141", "20220202141", "20220203141", "20220204141", "20220205141", "20220206141", "20220207141", "20220208141", "20220209141", "20220210141", "20220211141", "20060129021", "20060130021", "20060131021", "20060201021", "20060202021", "20060203021", "20060204021", "20060205021", "20060206021", "20060207021", "20060208021"], "sihua_sihua_0_sihua_sihua_1"=>["20040127040", "20040128040", "20040211160", "20040225060", "20040226060", "20040311180", "20040320060", "20040326060", "20040327060", "20040410200", "20040424080", "20150622220", "20150624100", "20150630220", "20150702220", "20150704100", "20170203100", "20150724120", "20140205040", "20170203100", "20150803120", "20150820020", "20190205201", "20190211081", "20190213201", "20190215201", "20190302081", "20190307221", "20190313101", "20190315221", "20190317221", "20190401101", "20150820140", "20051108081", "20051110201", "20051116081", "20051118081", "20051120201", "20051207101", "20051209221", "20051215101", "20051217101", "20051219221", "20060106121"], "sihua_sihua_0_sihua_sihua_2"=>["20200201200", "20200204200", "20200209080", "20200210200", "20200221080", "20200301220", "20200304220", "20200309100", "20200310220", "20200321100", "20080125160", "20170901060", "20170908180", "20170911060", "20170918180", "20170925080", "20170930080", "20171007200", "20171010080", "20171017200", "20171025100", "20171030100", "20200201201", "20200204201", "20200209081", "20200210201", "20200221081", "20200301221", "20200304221", "20200309101", "20200310221", "20200321101", "20110122180", "20081130161", "20081212161", "20081216161", "20081224161", "20090103201", "20090104201", "20100215081", "20100219201", "20100301201", "20100304201", "20100310201"], "sihua_sihua_1_sihua_sihua_2"=>["20140615080", "20140620080", "20140623200", "20140630100", "20140714100", "20140719100", "20140722220", "20140730120", "20140813120", "20140818120", "20080529160", "20080701200", "20060130040", "20060131220", "20060201200", "20060202200", "20060202220", "20060203160", "20060203180", "20060204020", "20060204180", "20060205120", "20080228041", "20080325081", "20080326081", "20080425121", "20080426121", "20080529161", "20080605201", "20080701201", "20080228040", "20080228040", "20161129160", "20110212181", "20110213181", "20110227061", "20110406081", "20110414081", "20110416201", "20110426201", "20110429201", "20110606101", "20110610101", "20110618101"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20160213100", "20120209220", "20060913000", "20081031040", "20120408020", "20021023140"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19221002080", "19221007080", "19221012200", "19221017080", "19221031100", "19221105100", "19180628020", "19180628140", "19180629140", "19180702020", "19180715040", "19180727040", "20140913201", "20140924221", "20140929101", "20141001101", "20141013221", "20141024221", "19350606021", "19350616021", "19350619021", "19350625021", "19350626141", "19350702161"], "dis_astar_star_20_dis_astar_star_21"=>["19611018180", "19611019180", "19611020180", "19611021180", "19611022180", "19611023180", "19311114201", "19311115201", "19311116201", "19311117201", "19311118201", "19311119201"], "dis_bstar_star_1_dis_bstar_star_2"=>["20110203100", "20110203220", "20110204100", "20110204220", "20110205100", "20110205220", "20110206100", "20110206220", "20110207100", "20110207220", "20110208100", "20080406020", "20080406140", "20080407020", "20080407140", "20080408020", "20080408140", "20080409020", "20080409140", "20080410020", "20080410140", "20080411020", "20100214101", "20100214221", "20100215101", "20100215221", "20100216101", "20100216221", "20100217101", "20100217221", "20100218101", "20100218221", "20100219101", "20090327021", "20090327141", "20090328021", "20090328141", "20090329021", "20090329141", "20090330021", "20090330141", "20090331021", "20090331141", "20090401021"], "dis_astar_star_15_dis_astar_star_16"=>["20170226080", "20170226200", "20170227080", "20170227200", "20170228080", "20170228200", "20170301080", "20170301200", "20170302080", "20170302200", "20170303080", "20111225040", "20111225160", "20111226040", "20111226160", "20111227040", "20111227160", "20111228040", "20111228160", "20111229040", "20111229160", "20111230040", "20120222081", "20120222201", "20120223081", "20120223201", "20120224081", "20120224201", "20120225081", "20120225201", "20120226081", "20120226201", "20120227081", "20050110041", "20050110161", "20050111041", "20050111161", "20050112041", "20050112161", "20050113041", "20050113161", "20050114041", "20050114161", "20050115041"], "dis_astar_star_17_dis_astar_star_18"=>["20011017100", "20011018100", "20011019100", "20011020100", "20011021100", "20011022100", "20011023100", "20011024100", "20011025100", "20011026100", "20011027100", "20040618140", "20040619140", "20040620140", "20040621140", "20040622140", "20040623140", "20040624140", "20040625140", "20040626140", "20040627140", "20040628140", "20200324101", "20200325101", "20200326101", "20200327101", "20200328101", "20200329101", "20200330101", "20200331101", "20200401101", "20200402101", "20200403101", "20100612141", "20100613141", "20100614141", "20100615141", "20100616141", "20100617141", "20100618141", "20100619141", "20100620141", "20100621141", "20100622141"], "dis_astar_star_24_dis_astar_star_25"=>["20120123120", "20120124120", "20120125120", "20120126120", "20120127120", "20120128120", "20120129120", "20120130120", "20120131120", "20120201120", "20120202120", "20030601200", "20031007040", "20060913141", "20171024160", "20120802221", "20020807221", "20061227200", "20120921021", "20060129001", "20020923020", "20120221120", "20220201121", "20220202121", "20220203121", "20220204121", "20220205121", "20220206121", "20220207121", "20220208121", "20220209121", "20220210121", "20220211121", "20060129000", "20070815120", "20180111201", "20060723100", "20160212001", "20120123121", "20060219001", "20070606060", "20020722220", "20120325160", "20020212121"], "sihua_sihua_0_sihua_sihua_1"=>["20041103060", "20041117200", "20041118200", "20041202080", "20041211200", "20041217220", "20041218220", "20050101100", "20150903120", "20151102160", "20050130120", "20190211180", "20190213060", "20190219180", "20190221180", "20190223060", "20190313200", "20190315080", "20190321200", "20190323200", "20190325080", "20190411220", "20140205021", "20140206021", "20140220141", "20140306041", "20140307041", "20140321161", "20140330041", "20140405061", "20140406061", "20140420181", "20140504081", "20140206141", "20140209021", "20140210021", "20140307161", "20140310041", "20140311041", "20140406181", "20140409061", "20140410061", "20140505201", "20140508081"], "sihua_sihua_0_sihua_sihua_2"=>["20100221180", "20100224180", "20100301060", "20100302180", "20100313060", "20100323200", "20100326200", "20100331080", "20100401200", "20100412080", "20100421220", "20001211140", "20001214140", "20001220140", "20001221020", "20001227040", "20001231160", "20010110160", "20010113160", "20010119160", "20010120040", "20010224180", "20130101141", "20130125141", "20130126141", "20161229081", "20161231221", "20111107040", "20170102221", "20170103181", "20170103201", "20170104021", "20170104181", "20060228021", "20060301081", "20060302121", "20060228020", "20060304061", "20060306141", "20060308021", "20060308061", "20060309041", "20060309221", "20060310021"], "sihua_sihua_1_sihua_sihua_2"=>["20040803080", "20040808080", "20040811200", "20040819100", "20040902100", "20040907100", "20040910220", "20040917120", "20041001120", "20041006120", "20111117040", "20120920100", "20120930100", "20121010100", "20121020100", "20121025100", "20121104100", "20121117100", "20121118100", "20121211100", "20121212100", "20121218100", "20110320081", "20110328081", "20110330081", "20110401201", "20110402201", "20110504221", "20110514221", "20110517221", "20110523101", "20110523221", "20110702121", "20130111101", "20130119101", "20130131101", "20130204101", "20130210201", "20130211101", "20130212121", "20130213161", "20130214121", "20130217181", "20130221141"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20160210080", "20030511021", "20120404000", "20180714180", "20080728181", "20121226160"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["20060205160", "20060208160", "20060213040", "20060214160", "20060225040", "20060307180", "20061029080", "20000504100", "19430702140", "19430711020", "19430725020", "19430730020", "19170605101", "19170613101", "19170615101", "19170617221", "19170618221", "20140112140", "19700606121", "20170923060", "20160928180", "19700616121", "20080516220", "19700705141"], "dis_astar_star_20_dis_astar_star_21"=>["19500806120", "19500807120", "19500808120", "19500809120", "19500810120", "19500811120", "20040601200", "20061126020", "20050703200", "20000724120", "20010703080", "20030706040"], "dis_bstar_star_1_dis_bstar_star_2"=>["20020314100", "20020314220", "20020315100", "20020315220", "20020316100", "20020316220", "20020317100", "20020317220", "20020318100", "20020318220", "20020319100", "20180515020", "20180515140", "20180516020", "20180516140", "20180517020", "20180517140", "20180518020", "20180518140", "20180519020", "20180519140", "20180520020", "20170226101", "20170226221", "20170227101", "20170227221", "20170228101", "20170228221", "20170301101", "20170301221", "20170302101", "20170302221", "20170303101", "20060428021", "20060428141", "20060429021", "20060429141", "20060430021", "20060430141", "20060501021", "20060501141", "20060502021", "20060502141", "20060503021"], "dis_astar_star_15_dis_astar_star_16"=>["20200324080", "20200324200", "20200325080", "20200325200", "20200326080", "20200326200", "20200327080", "20200327200", "20200328080", "20200328200", "20200329080", "20050209040", "20050209160", "20050210040", "20050210160", "20050211040", "20050211160", "20050212040", "20050212160", "20050213040", "20050213160", "20050214040", "20030402081", "20030402201", "20030403081", "20030403201", "20030404081", "20030404201", "20030405081", "20030405201", "20030406081", "20030406201", "20030407081", "20010124041", "20010124161", "20010125041", "20010125161", "20010126041", "20010126161", "20010127041", "20010127161", "20010128041", "20010128161", "20010129041"], "dis_astar_star_17_dis_astar_star_18"=>["20160407080", "20160408080", "20160409080", "20160410080", "20160411080", "20160412080", "20160413080", "20160414080", "20160415080", "20160416080", "20160417080", "20111125120", "20111126120", "20111127120", "20111128120", "20111129120", "20111130120", "20111201120", "20111202120", "20111203120", "20111204120", "20111205120", "20050409081", "20050410081", "20050411081", "20050412081", "20050413081", "20050414081", "20050415081", "20050416081", "20050417081", "20050418081", "20050419081", "20010607121", "20010608121", "20010609121", "20010610121", "20010611121", "20010612121", "20010613121", "20010614121", "20010615121", "20010616121", "20010617121"], "dis_astar_star_24_dis_astar_star_25"=>["20020212100", "20020213100", "20020214100", "20020215100", "20020216100", "20020217100", "20020218100", "20020219100", "20020220100", "20020221100", "20020222100", "20260217220", "20260218220", "20260219220", "20260220220", "20260221220", "20260222220", "20260223220", "20260224220", "20260225220", "20260226220", "20260227220", "20220201101", "20220202101", "20220203101", "20220204101", "20220205101", "20220206101", "20220207101", "20220208101", "20220209101", "20220210101", "20220211101", "20060129221", "20060130221", "20060131221", "20060201221", "20060202221", "20060203221", "20060204221", "20060205221", "20060206221", "20060207221", "20060208221"], "sihua_sihua_0_sihua_sihua_1"=>["20060120200", "20070219180", "20070222060", "20070305180", "20070320200", "20070323080", "20070403200", "20070418220", "20070421100", "20070502220", "20070516220", "20140811120", "20140826140", "20140829020", "20140909140", "20140923140", "20140925160", "20140928040", "20141009160", "20141023160", "20141025160", "20141028040", "20170129181", "20170201061", "20170212181", "20170227201", "20170302081", "20170313201", "20170327201", "20170329221", "20170401101", "20170412221", "20070812040", "20010130020", "20140204121", "20090126160", "20140302021", "20140305141", "20140316021", "20140330021", "20140401041", "20140404161", "20140415041", "20140430061"], "sihua_sihua_0_sihua_sihua_2"=>["20101111100", "20060130040", "20060131220", "20060201200", "20060202200", "20060202220", "20060203160", "20060203180", "20060204020", "20060204180", "20060205120", "20200127040", "20200128160", "20200205160", "20200206040", "20200207160", "20200225060", "20200226180", "20200305180", "20200306060", "20200307180", "20200326080", "20200125041", "20200130161", "20200201161", "20200213041", "20200223061", "20200228181", "20200301181", "20200313061", "20200324081", "20200329201", "20200331201", "20180919040", "20081001041", "20081003041", "20081109081", "20081114081", "20081124081", "20081201121", "20081215121", "20081220121", "20090103161", "20090115161"], "sihua_sihua_1_sihua_sihua_2"=>["20040122200", "20040128080", "20040130200", "20040201200", "20040216080", "20040220220", "20040226100", "20040228220", "20040301220", "20040316100", "20040321220", "20180307200", "20180311200", "20180303000", "20080917040", "20180427040", "20180428040", "20180601080", "20180611080", "20180705120", "20180730160", "20180731160", "20140608041", "20140623161", "20140627061", "20140703181", "20140705061", "20140707061", "20140722181", "20140727081", "20140802201", "20140804081", "20140806081", "20040715161", "20040719061", "20040721061", "20040730181", "20040813181", "20040818081", "20040820081", "20040829201", "20040912201", "20040916101", "20040918101"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20120205180", "20130109140", "20120205181", "20070121040", "20061123000", "20120205180"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["20140322160", "20140409180", "20140413180", "20140415060", "20140416060", "20140421180", "19841015180", "19841018060", "19841031080", "19841112080", "19841112200", "19841113200", "19431208101", "19431213101", "19431218221", "19431223101", "19440107121", "19440112121", "19870922141", "19870930041", "19871001041", "19871014161", "19871022161", "19871030061"], "dis_astar_star_20_dis_astar_star_21"=>["20030818040", "20030819040", "20030820040", "20030821040", "20030822040", "20030823040", "19470808141", "19470809141", "19470810141", "19470811141", "19470812141", "19470813141"], "dis_bstar_star_1_dis_bstar_star_2"=>["20020413100", "20020413220", "20020414100", "20020414220", "20020415100", "20020415220", "20020416100", "20020416220", "20020417100", "20020417220", "20020418100", "20060527020", "20060527140", "20060528020", "20060528140", "20060529020", "20060529140", "20060530020", "20060530140", "20060531020", "20060531140", "20060601020", "20130410101", "20130410221", "20130411101", "20130411221", "20130412101", "20130412221", "20130413101", "20130413221", "20130414101", "20130414221", "20130415101", "20150616021", "20150616141", "20150617021", "20150617141", "20150618021", "20150618141", "20150619021", "20150619141", "20150620021", "20150620141", "20150621021"], "dis_astar_star_15_dis_astar_star_16"=>["20150518080", "20150518200", "20150519080", "20150519200", "20150520080", "20150520200", "20150521080", "20150521200", "20150522080", "20150522200", "20150523080", "20010223040", "20010223160", "20010224040", "20010224160", "20010225040", "20010225160", "20010226040", "20010226160", "20010227040", "20010227160", "20010228040", "20190505081", "20190505201", "20190506081", "20190506201", "20190507081", "20190507201", "20190508081", "20190508201", "20190509081", "20190509201", "20190510081", "20050310041", "20050310161", "20050311041", "20050311161", "20050312041", "20050312161", "20050313041", "20050313161", "20050314041", "20050314161", "20050315041"], "dis_astar_star_17_dis_astar_star_18"=>["20061118060", "20061119060", "20061120060", "20070417060", "20070418060", "20070419060", "20070420060", "20070421060", "20070422060", "20070423060", "20070424060", "20171218100", "20171219100", "20171220100", "20171221100", "20171222100", "20171223100", "20171224100", "20171225100", "20171226100", "20171227100", "20171228100", "20150419061", "20150420061", "20150421061", "20150422061", "20150423061", "20150424061", "20150425061", "20150426061", "20150427061", "20150428061", "20150429061", "20190603101", "20190604101", "20190605101", "20190606101", "20190607101", "20190608101", "20190609101", "20190610101", "20190611101", "20190612101", "20190613101"], "dis_astar_star_24_dis_astar_star_25"=>["20120123080", "20120124080", "20120125080", "20120126080", "20120127080", "20120128080", "20120129080", "20120130080", "20120131080", "20120201080", "20120202080", "20060129200", "20060130200", "20060131200", "20060201200", "20060202200", "20060203200", "20060204200", "20060205200", "20060206200", "20060207200", "20060208200", "20130112061", "20130113061", "20130114061", "20130115061", "20130116061", "20130117061", "20130118061", "20130119061", "20130120061", "20130121061", "20130122061", "20160208201", "20160209201", "20160210201", "20160211201", "20160212201", "20160213201", "20160214201", "20160215201", "20160216201", "20160217201", "20160218201"], "sihua_sihua_0_sihua_sihua_1"=>["20010715080", "20010720200", "20010722220", "20010805220", "20010814100", "20090128140", "20170206160", "20010912120", "20010918020", "20011002020", "20011011140", "20100117120", "20171029080", "20150316200", "20110204120", "20090812120", "20090813120", "20191030080", "20090128140", "20090820020", "20090901020", "20090910140", "20110204121", "20110218121", "20190207140", "20110304121", "20110306141", "20110320141", "20110329021", "20110404161", "20110418161", "20110427041", "20110502161", "20140201221", "20140204101", "20140215221", "20190207140", "20140305121", "20150630160", "20041115160", "20140401021", "20140404141", "20140415021", "20140430041"], "sihua_sihua_0_sihua_sihua_2"=>["20000306040", "20160310040", "20160310140", "20160310180", "20160310220", "20160311220", "20160312200", "20160313200", "20160314160", "20160316120", "20160320180", "20060428160", "20060430060", "20060501080", "20060502060", "20060503020", "20060503040", "20060504020", "20060504100", "20100908160", "20060505040", "20160507160", "20100214021", "20100219141", "20100221141", "20100305021", "20100316041", "20100321161", "20100323161", "20100404041", "20100414061", "20100419181", "20100421181", "20060428161", "20060430061", "20060501081", "20060502061", "20060503021", "20060503041", "20060504021", "20060504101", "20080605140", "20060505041", "20100503060"], "sihua_sihua_1_sihua_sihua_2"=>["20140703160", "20140705040", "20140707040", "20140722160", "20140727060", "20140802180", "20140804060", "20140806060", "20140821180", "20140825080", "20140831200", "20210314040", "20210318160", "20210328160", "20210331160", "20210406160", "20210407040", "20210513180", "20210527180", "20210605060", "20210711080", "20210717200", "20010226181", "20010301181", "20010305061", "20010307181", "20010317061", "20010430201", "20010503201", "20010508081", "20010509201", "20010520081", "20010530201", "20210314041", "20210318161", "20210328161", "20210331161", "20210406161", "20210407041", "20210513181", "20210527181", "20210605061", "20210711081", "20210717201"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20020225160", "20080210040", "20060729161", "20120205161", "20160212040", "20120830040"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19850417020", "19850417140", "19850418140", "19850423160", "19850424160", "19850505040", "19971119060", "19971123060", "19971129180", "19971130200", "19971207080", "19971219080", "19250322021", "19250322141", "19250323141", "19250327161", "19250328161", "19250408041", "19351227221", "19360102101", "19360103101", "19360120221", "19360202121", "20140617080"], "dis_astar_star_20_dis_astar_star_21"=>["20060827160", "20060828160", "20060829160", "20060830160", "20060831160", "20060901160", "20090904141", "20090905141", "20090906141", "20090907141", "20090908141", "20090909141"], "dis_bstar_star_1_dis_bstar_star_2"=>["20100514100", "20100514220", "20100515100", "20100515220", "20100516100", "20100516220", "20100517100", "20100517220", "20100518100", "20100518220", "20100519100", "20160704020", "20160704140", "20160705020", "20160705140", "20160706020", "20160706140", "20160707020", "20160707140", "20160708020", "20160708140", "20160709020", "20100514101", "20100514221", "20100515101", "20100515221", "20100516101", "20100516221", "20100517101", "20100517221", "20100518101", "20100518221", "20100519101", "20000702021", "20000702141", "20000703021", "20000703141", "20000704021", "20000704141", "20000705021", "20000705141", "20000706021", "20000706141", "20000707021"], "dis_astar_star_15_dis_astar_star_16"=>["20060527080", "20060527200", "20060528080", "20060528200", "20060529080", "20060529200", "20060530080", "20060530200", "20060531080", "20060531200", "20060601080", "20160407040", "20160407160", "20160408040", "20160408160", "20160409040", "20160409160", "20160410040", "20160410160", "20160411040", "20160411160", "20160412040", "20180614081", "20180614201", "20180615081", "20180615201", "20180616081", "20180616201", "20180617081", "20180617201", "20180618081", "20180618201", "20180619081", "20060329041", "20060329161", "20060330041", "20060330161", "20060331041", "20060331161", "20060401041", "20060401161", "20060402041", "20060402161", "20060403041"], "dis_astar_star_17_dis_astar_star_18"=>["20180416040", "20180417040", "20180418040", "20180419040", "20180420040", "20180421040", "20180422040", "20180423040", "20180424040", "20180425040", "20180426040", "20070617080", "20070618080", "20070619080", "20070620080", "20070621080", "20070622080", "20070623080", "20070624080", "20070625080", "20070626080", "20070627080", "20130410041", "20130411041", "20130412041", "20130413041", "20130414041", "20130415041", "20130416041", "20130417041", "20130418041", "20130419041", "20130420041", "20090524081", "20090525081", "20090526081", "20090527081", "20090528081", "20090529081", "20090530081", "20090531081", "20090601081", "20090602081", "20090603081"], "dis_astar_star_24_dis_astar_star_25"=>["20220201060", "20220202060", "20220203060", "20220204060", "20220205060", "20220206060", "20220207060", "20220208060", "20220209060", "20220210060", "20220211060", "20170416220", "20170417220", "20170418220", "20170419220", "20170420220", "20170421220", "20170422220", "20170423220", "20170424220", "20170425220", "20020218060", "20220201061", "20220202061", "20220203061", "20220204061", "20220205061", "20220206061", "20220207061", "20220208061", "20220209061", "20220210061", "20220211061", "20171104101", "20171105101", "20171106101", "20171107101", "20171108101", "20171109101", "20171110101", "20171111101", "20171112101", "20171113101", "20171114101"], "sihua_sihua_0_sihua_sihua_1"=>["20070412040", "20070426180", "20070427180", "20070511060", "20070526200", "20070527200", "20070610080", "20070624220", "20070625220", "20070709100", "20140425120", "20190413040", "20190414160", "20190423040", "20190424160", "20190427160", "20190503160", "20190513060", "20190514180", "20190523060", "20190524180", "20190527180", "20190206121", "20090930020", "20190216121", "20190219121", "20190225121", "20090127120", "20190308141", "20190313021", "20190318141", "20190321141", "20190327141", "20190721101", "20190722221", "20190725221", "20190731221", "20190809121", "20190206120", "20190819121", "20110304100", "20070227140", "20041118140", "20190907141"], "sihua_sihua_0_sihua_sihua_2"=>["20200128120", "20200129120", "20070512020", "20000706220", "20200221120", "20200222120", "20200226140", "20200227140", "20200309020", "20200321020", "20200321140", "20160606080", "20160606120", "20160607060", "20160608040", "20160609040", "20160609060", "20170428160", "20160610020", "20160611020", "20160611100", "20160612200", "20170130101", "20170211101", "20170215101", "20170222221", "20170223101", "20170228121", "20170312121", "20170316121", "20170806200", "20170324121", "20170330141", "20070701181", "20070702061", "20070712061", "20070718201", "20070722201", "20070730201", "20070731081", "20070810081", "20070817221", "20070821221", "20070829221"], "sihua_sihua_1_sihua_sihua_2"=>["20160407060", "20160407100", "20160407200", "20160411160", "20160411180", "20160412120", "20160412140", "20160414080", "20160414120", "20160415100", "20160416040", "20210213140", "20210223140", "20210226140", "20210304020", "20210304140", "20210421160", "20210503040", "20210505160", "20210510160", "20210511040", "20210611180", "20040124161", "20040205161", "20040208041", "20040209041", "20040209161", "20040217161", "20040222181", "20040305181", "20040308061", "20040309061", "20040309181", "20180225161", "20180311161", "20180316161", "20180317201", "20180329201", "20180410201", "20180414201", "20141119100", "20040928061", "20180528041", "20180529041"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20180219020", "20180219020", "20060828141", "20180219020", "20020225141", "20060202020"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19800710060", "19800710180", "19800711180", "19800715200", "19800716200", "19800727080", "19590410020", "19590413140", "19590420020", "19590423140", "19590426140", "19590502140", "19251102141", "19251114021", "19251114141", "19251115021", "19251119041", "19251120041", "19730802221", "19730810221", "19730812221", "19730818101", "19730819101", "20051216180"], "dis_astar_star_20_dis_astar_star_21"=>["19951019220", "19951020220", "19951021220", "19951022220", "19951023220", "19951024220", "19400919101", "19400920101", "19400921101", "19400922101", "19400923101", "19400924101"], "dis_bstar_star_1_dis_bstar_star_2"=>["20150616100", "20150616220", "20150617100", "20150617220", "20150618100", "20150618220", "20150619100", "20150619220", "20150620100", "20150620220", "20150621100", "20090820020", "20090820140", "20090821020", "20090821140", "20090822020", "20090822140", "20090823020", "20090823140", "20090824020", "20090824140", "20090825020", "20190603101", "20190603221", "20190604101", "20190604221", "20190605101", "20190605221", "20190606101", "20190606221", "20190607101", "20190607221", "20190608101", "20020809021", "20020809141", "20020810021", "20020810141", "20020811021", "20020811141", "20020812021", "20020812141", "20020813021", "20020813141", "20020814021"], "dis_astar_star_15_dis_astar_star_16"=>["20110701080", "20110701200", "20110702080", "20110702200", "20110703080", "20110703200", "20110704080", "20110704200", "20110705080", "20110705200", "20110706080", "20050508040", "20050508160", "20050509040", "20050509160", "20050510040", "20050510160", "20050511040", "20050511160", "20050512040", "20050512160", "20050513040", "20120719081", "20120719201", "20120720081", "20120720201", "20120721081", "20120721201", "20120722081", "20120722201", "20120723081", "20120723201", "20120724081", "20050508041", "20050508161", "20050509041", "20050509161", "20050510041", "20050510161", "20050511041", "20050511161", "20050512041", "20050512161", "20050513041"], "dis_astar_star_17_dis_astar_star_18"=>["20180416020", "20180417020", "20180418020", "20180419020", "20180420020", "20180421020", "20180422020", "20180423020", "20180424020", "20180425020", "20180426020", "20120605060", "20120606060", "20120607060", "20120608060", "20120609060", "20120610060", "20120611060", "20120612060", "20120613060", "20120614060", "20120615060", "20130410021", "20130411021", "20130412021", "20130413021", "20130414021", "20130415021", "20130416021", "20130417021", "20130418021", "20130419021", "20130420021", "20010607061", "20010608061", "20010609061", "20010610061", "20010611061", "20010612061", "20010613061", "20010614061", "20010615061", "20010616061", "20010617061"], "dis_astar_star_24_dis_astar_star_25"=>["20120123040", "20120124040", "20120125040", "20120126040", "20120127040", "20120128040", "20120129040", "20120130040", "20120131040", "20120201040", "20120202040", "20060129160", "20060130160", "20060131160", "20060201160", "20060202160", "20060203160", "20060204160", "20060205160", "20060206160", "20060207160", "20060208160", "20120123041", "20120124041", "20120125041", "20120126041", "20120127041", "20120128041", "20120129041", "20120130041", "20120131041", "20120201041", "20120202041", "20070726021", "20070727021", "20070728021", "20070729021", "20070730021", "20070731021", "20070801021", "20070802021", "20070803021", "20070804021", "20070805021"], "sihua_sihua_0_sihua_sihua_1"=>["20010514020", "20010515020", "20010529140", "20010606140", "20010608160", "20010613040", "20010614040", "20010627160", "20010705160", "20010707160", "20010712040", "20190823220", "20190829220", "20190907120", "20191210060", "20190917120", "20000113200", "20070218120", "20160127020", "20191007140", "20191008020", "20191017140", "20140131181", "20140206061", "20140208181", "20140210181", "20140225061", "20140301201", "20140307081", "20140309201", "20140311201", "20140326081", "20140331221", "20170130121", "20170211121", "20170215121", "20040122180", "20170223121", "20170228141", "20170312141", "20170316141", "20170323021", "20170324141", "20170330161"], "sihua_sihua_0_sihua_sihua_2"=>["20070219080", "20160510180", "20160510220", "20160513140", "20160513220", "20160514200", "20160516120", "20160516160", "20160517100", "20160517200", "20160519220", "20060627020", "20060627060", "20060627080", "20060627120", "20060627220", "20060628060", "20060629040", "20060630040", "20100217100", "20060703200", "20060707020", "20180411221", "20180507021", "20180508021", "20180515061", "20180607061", "20180608061", "20180617101", "20180620101", "20180626101", "20180720141", "20180723141", "20070219080", "20180720141", "20120107121", "20120117121", "20120120121", "20120128021", "20120130021", "20120225021", "20120228021", "20120305021", "20120324021"], "sihua_sihua_1_sihua_sihua_2"=>["20180605060", "20180606060", "20180616100", "20180622100", "20180716140", "20180719140", "20180725140", "20180814180", "20180815180", "20180907180", "20180908180", "20080830140", "20080904180", "20080929220", "20081022220", "20081023220", "20081030020", "20081031020", "20081123020", "20081124020", "20081203060", "20081205060", "20120628061", "20120708061", "20120711061", "20120717061", "20120726061", "20120809061", "20120830061", "20120913061", "20121001061", "20121002061", "20121101061", "20030515181", "20140202140", "20030517181", "20030520041", "20030521021", "20030521041", "20030522061", "20030522201", "20030523061", "20110307160", "20030528121"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20020222120", "20180905120", "20060628101", "20021016041", "20020222120", "20020917020"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19460209080", "19460217200", "19460218200", "19460309100", "19460311100", "19460319220", "19340703040", "19340705160", "19340710160", "19340711040", "19340721180", "19340802060", "19890123181", "19890204061", "19890204181", "19890205061", "19890217081", "19890221201", "20000424121", "20000428121", "20000504021", "20000511141", "20000523141", "20000527141"], "dis_astar_star_20_dis_astar_star_21"=>["19540620140", "19540621140", "19540622140", "19540623140", "19540624140", "19540625140", "19360410041", "19360411041", "19360412041", "19360413041", "19360414041", "19360415041"], "dis_bstar_star_1_dis_bstar_star_2"=>["20180713100", "20180713220", "20180714100", "20180714220", "20180715100", "20180715220", "20180716100", "20180716220", "20180717100", "20180717220", "20180718100", "20160901020", "20160901140", "20160902020", "20160902140", "20160903020", "20160903140", "20160904020", "20160904140", "20160905020", "20160905140", "20160906020", "20170624101", "20170624221", "20170625101", "20170625221", "20170626101", "20170626221", "20170627101", "20170627221", "20170628101", "20170628221", "20170629101", "20090919021", "20090919141", "20090920021", "20090920141", "20090921021", "20090921141", "20090922021", "20090922141", "20090923021", "20090923141", "20090924021"], "dis_astar_star_15_dis_astar_star_16"=>["20030729080", "20030729200", "20030730080", "20030730200", "20030731080", "20030731200", "20030801080", "20030801200", "20030802080", "20030802200", "20030803080", "20070615040", "20070615160", "20070616040", "20070616160", "20070617040", "20070617160", "20070618040", "20070618160", "20070619040", "20070619160", "20070620040", "20130807081", "20130807201", "20130808081", "20130808201", "20130809081", "20130809201", "20130810081", "20130810201", "20130811081", "20130811201", "20130812081", "20060527041", "20060527161", "20060528041", "20060528161", "20060529041", "20060529161", "20060530041", "20060530161", "20060531041", "20060531161", "20060601041"], "dis_astar_star_17_dis_astar_star_18"=>["20140924000", "20061022000", "20101011000", "20140924000", "20030415000", "20031016000", "20140331000", "20191021000", "20060329000", "20081004000", "20190929000", "20180416000", "20020505000", "20060329000", "20161001000", "20050409000", "20181009000", "20020413000", "20151013000", "20040405000", "20120322000", "20021006000", "20110403000", "20030926000", "20190929000", "20190929000", "20181009000", "20150419000", "20121015000", "20130410000", "20180416000", "20190411000", "20080929000", "20061022000", "20110403000", "20151013000", "20130410000", "20171020000", "20030402000", "20090327000", "20080406000", "20180416000", "20021006000", "20030425000"], "dis_astar_star_24_dis_astar_star_25"=>["20220201020", "20220202020", "20220203020", "20220204020", "20220205020", "20220206020", "20220207020", "20220208020", "20220209020", "20220210020", "20220211020", "20060129140", "20060130140", "20060131140", "20060201140", "20060202140", "20060203140", "20060204140", "20060205140", "20060206140", "20060207140", "20060208140", "20120123021", "20120124021", "20120125021", "20120126021", "20120127021", "20120128021", "20120129021", "20120130021", "20120131021", "20120201021", "20120202021", "20260217141", "20260218141", "20260219141", "20260220141", "20260221141", "20260222141", "20260223141", "20260224141", "20260225141", "20260226141", "20260227141"], "sihua_sihua_0_sihua_sihua_1"=>["20140202160", "20140214160", "20140217040", "20140218040", "20140218160", "20140226160", "20140303180", "20140315180", "20140318060", "20140319060", "20140319180", "20110203060", "20110211060", "20110213060", "20110220180", "20110221180", "20110305080", "20110313080", "20110315080", "20110322200", "20110323200", "20110403100", "20110209061", "20110217061", "20110219061", "20110224181", "20110225181", "20110311081", "20110319081", "20110321081", "20110326201", "20110327201", "20110409101", "20190206081", "20190220081", "20190301201", "20190306081", "20190308101", "20190322101", "20190331221", "20190406121", "20190420121", "20091204020", "20190504121"], "sihua_sihua_0_sihua_sihua_2"=>["20120201040", "20120209040", "20120211040", "20120228040", "20120307040", "20120309040", "20120327040", "20120406040", "20120409040", "20120415040", "20120422040", "20060725080", "20060726020", "20060726060", "20060726220", "20060727060", "20060801040", "20070922200", "20060803200", "20060804040", "20060805020", "20060806180", "20070219061", "20070224181", "20070301061", "20070304061", "20070310061", "20070315181", "20070320081", "20070325201", "20070330081", "20070402081", "20070408081", "20000819201", "20000823201", "20000829101", "20000905221", "20000917221", "20000921221", "20000927101", "20000928121", "20011219220", "20080213120", "20200203080"], "sihua_sihua_1_sihua_sihua_2"=>["20140406040", "20140411160", "20140414160", "20140420160", "20140425040", "20140430180", "20140505060", "20140510180", "20140513180", "20140519180", "20140524060", "20210223120", "20181016000", "20210228120", "20020731040", "20120125040", "20210310120", "20210415140", "20210429140", "20210430020", "20210504140", "20210508020", "20080207161", "20080226161", "20080328201", "20131117040", "20010624160", "20080526041", "20080527041", "20080604081", "20080627081", "20080628081", "20080706121", "20180716120", "20080207160", "20120323040", "20081207080", "20130114040", "20010612060", "20040123120", "20020214040", "20041108160", "20140201120", "20180216160"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20080209220", "20020222100", "20060131220", "20160210220", "20020423141", "20080208220"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19410926200", "19410928200", "19411006080", "19411007080", "19411025220", "19411027220", "19310803040", "19310804040", "19310817180", "19310825180", "19310827180", "19310902060", "19470323201", "19470330081", "19470402081", "19470408101", "19470411221", "19470421221", "20160522000", "19570515121", "19570518121", "19570524121", "19570531021", "19570603141"], "dis_astar_star_20_dis_astar_star_21"=>["19960910100", "19960911100", "19960912100", "19960913120", "19960914120", "19960915120", "19930222101", "19930223101", "19930224101", "19930225101", "19930226101", "19930227101"], "dis_bstar_star_1_dis_bstar_star_2"=>["20020809100", "20020809220", "20020810100", "20020810220", "20020811100", "20020811220", "20020812100", "20020812220", "20020813100", "20020813220", "20020814100", "20171020020", "20171020140", "20171021020", "20171021140", "20171022020", "20171022140", "20171023020", "20171023140", "20171024020", "20171024140", "20171025020", "20090820101", "20090820221", "20090821101", "20090821221", "20090822101", "20090822221", "20090823101", "20090823221", "20090824101", "20090824221", "20090825101", "20051003021", "20051003141", "20051004021", "20051004141", "20051005021", "20051005141", "20051006021", "20051006141", "20051007021", "20051007141", "20051008021"], "dis_astar_star_15_dis_astar_star_16"=>["20130905080", "20130905200", "20130906080", "20130906200", "20130907080", "20130907200", "20130908080", "20130908200", "20130909080", "20130909200", "20130910080", "20130708040", "20130708160", "20130709040", "20130709160", "20130710040", "20130710160", "20130711040", "20130711160", "20130712040", "20130712160", "20130713040", "20030828081", "20030828201", "20030829081", "20030829201", "20030830081", "20030830201", "20030831081", "20030831201", "20030901081", "20030901201", "20030902081", "20160704041", "20160704161", "20160705041", "20160705161", "20160706041", "20160706161", "20160707041", "20160707161", "20160708041", "20160708161", "20160709041"], "dis_astar_star_17_dis_astar_star_18"=>["20190929220", "20190930220", "20191001220", "20191002220", "20191003220", "20191004220", "20191005220", "20191006220", "20191007220", "20191008220", "20191009220", "20111125020", "20111126020", "20111127020", "20111128020", "20111129020", "20111130020", "20111201020", "20111202020", "20111203020", "20111204020", "20111205020", "20110927221", "20110928221", "20110929221", "20110930221", "20111001221", "20111002221", "20111003221", "20111004221", "20111005221", "20111006221", "20111007221", "20061220021", "20061221021", "20061222021", "20061223021", "20061224021", "20061225021", "20061226021", "20061227021", "20061228021", "20061229021", "20061230021"], "dis_astar_star_24_dis_astar_star_25"=>["20121227200", "20121228200", "20121229200", "20121230200", "20121231200", "20130101200", "20130102200", "20130103200", "20130104200", "20130105200", "20130106200", "20060129120", "20060130120", "20060131120", "20060201120", "20060202120", "20060203120", "20060204120", "20060205120", "20060206120", "20060207120", "20060208120", "20130719101", "20130720101", "20130721101", "20130722101", "20130723101", "20130724101", "20130725101", "20130726101", "20130727101", "20130728101", "20130729101", "20160208121", "20160209121", "20160210121", "20160211121", "20160212121", "20160213121", "20160214121", "20160215121", "20160216121", "20160217121", "20160218121"], "sihua_sihua_0_sihua_sihua_1"=>["20040515180", "20040521200", "20040602200", "20040605080", "20040606080", "20040606200", "20040614200", "20040620220", "20040702220", "20040705100", "20040706100", "20140820020", "20140824020", "20140825040", "20140906040", "20140915160", "20140916160", "20140918040", "20140922040", "20140924060", "20141006060", "20141015180", "20040521201", "20040602201", "20040605081", "20040606081", "20040606201", "20040614201", "20040620221", "20040702221", "20040705101", "20040706101", "20040706221", "20010126041", "20010207041", "20010211041", "20010218161", "20010219041", "20010225061", "20010309061", "20010313061", "20010320181", "20010321061", "20010327081"], "sihua_sihua_0_sihua_sihua_2"=>["20021007020", "20021031020", "20021130020", "20021225020", "20030115020", "20030123020", "20030125020", "20060626140", "20060626180", "20010423160", "20060629160", "20160901080", "20160904020", "20160904060", "20160907060", "20160907220", "20160908040", "20100223060", "20160910200", "20160911040", "20160911180", "20160913060", "20020707021", "20020711021", "20020712021", "20020804021", "20020805021", "20020809021", "20020901021", "20020902021", "20020913021", "20021007021", "20021031021", "20170228061", "20170302061", "20170311181", "20170312181", "20170330081", "20170401081", "20170410201", "20170411201", "20170428101", "20170430101", "20170509221"], "sihua_sihua_1_sihua_sihua_2"=>["20080207140", "20080226140", "20080314180", "20080402180", "20080407220", "20080501220", "20080511020", "20080604060", "20080627060", "20080628060", "20080704100", "20160901040", "20160903180", "20160903220", "20110311020", "20160905220", "20160906200", "20160907140", "20160908160", "20160908200", "20160909020", "20160910120", "20020922021", "20021004021", "20021026021", "20021105021", "20021208021", "20030104021", "20030130021", "20040123101", "20040128221", "20040202101", "20040205101", "20121119221", "20121121221", "20121213221", "20121221221", "20121223221", "20130117221", "20130127221", "20130130221", "20130205221", "20130210141", "20130210221"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20080211200", "20121013220", "20180220201", "20160403220", "20160209201", "20060721060"]},
                                {"dis_astar_star_3_dis_astar_star_8"=>["19210319060", "19210323060", "19210331060", "19210331180", "19210417080", "19210421080", "19550531100", "19550612220", "19550614100", "19550619100", "19550629120", "20050325180", "19280322181", "19280329061", "19280401061", "19280407081", "19280410201", "19280420201", "19951004181", "19951016081", "19951018201", "19951023201", "19951102201", "19951114081"], "dis_astar_star_20_dis_astar_star_21"=>["19240616100", "19240617100", "19240618100", "19240619100", "19240620100", "19240621100", "20020516140", "20040617080", "20170409220", "19670522001", "19670523001", "19670524001"], "dis_bstar_star_1_dis_bstar_star_2"=>["20120916100", "20120916220", "20120917100", "20120917220", "20120918100", "20120918220", "20120919100", "20120919220", "20120920100", "20120920220", "20120921100", "20141108020", "20141108140", "20141109020", "20141109140", "20141110020", "20141110140", "20141111020", "20141111140", "20141112020", "20141112140", "20141113020", "20030828101", "20030828221", "20030829101", "20030829221", "20030830101", "20030830221", "20030831101", "20030831221", "20030901101", "20030901221", "20030902101", "20081029021", "20081029141", "20081030021", "20081030141", "20081031021", "20081031141", "20081101021", "20081101141", "20081102021", "20081102141", "20081103021"], "dis_astar_star_15_dis_astar_star_16"=>["20190929080", "20190929200", "20190930080", "20190930200", "20191001080", "20191001200", "20191002080", "20191002200", "20191003080", "20191003200", "20191004080", "20030729040", "20030729160", "20030730040", "20030730160", "20030731040", "20030731160", "20030801040", "20030801160", "20030802040", "20030802160", "20030803040", "20190929081", "20190929201", "20190930081", "20190930201", "20191001081", "20191001201", "20191002081", "20191002201", "20191003081", "20191003201", "20191004081", "20170807041", "20170807161", "20170808041", "20170808161", "20170809041", "20170809161", "20170810041", "20170810161", "20170811041", "20170811161", "20170812041"], "dis_astar_star_17_dis_astar_star_18"=>["20010325200", "20010326200", "20010327200", "20010328200", "20010329200", "20010330200", "20010331200", "20010401200", "20010402200", "20010403200", "20010404200", "20061220000", "20061221000", "20061222000", "20061223000", "20061224000", "20061225000", "20061226000", "20061227000", "20061228000", "20061229000", "20061230000", "20011114201", "20020413201", "20020414201", "20020415201", "20020416201", "20020417201", "20020418201", "20020419201", "20020420201", "20020421201", "20020422201", "20081128001", "20081129001", "20081130001", "20081201001", "20081202001", "20081203001", "20081204001", "20081205001", "20081206001", "20081207001", "20081208001"], "dis_astar_star_24_dis_astar_star_25"=>["20220201220", "20220202220", "20220203220", "20220204220", "20220205220", "20220206220", "20220207220", "20220208220", "20220209220", "20220210220", "20220211220", "20160208100", "20160209100", "20160210100", "20160211100", "20160212100", "20160213100", "20160214100", "20160215100", "20160216100", "20160217100", "20160218100", "20120123221", "20120124221", "20120125221", "20120126221", "20120127221", "20120128221", "20120129221", "20120130221", "20120131221", "20120201221", "20120202221", "20160208101", "20160209101", "20160210101", "20160211101", "20160212101", "20160213101", "20160214101", "20160215101", "20160216101", "20160217101", "20160218101"], "sihua_sihua_0_sihua_sihua_1"=>["20171012200", "20171018200", "20171028100", "20171029220", "20171107100", "20171108220", "20171111220", "20171117220", "20171126120", "20050805100", "20171206120", "20040928020", "20190612120", "20190613120", "20190709020", "20190712140", "20190713140", "20190807040", "20190810160", "20190811160", "20190905060", "20190908180", "20010124021", "20010128141", "20010205021", "20010217021", "20010221021", "20010223041", "20010227161", "20010307041", "20010319041", "20010323041", "20010325061", "20190211161", "20190214041", "20190215041", "20190313181", "20190316061", "20190317061", "20190411201", "20190414081", "20190415081", "20190511221", "20190514101"], "sihua_sihua_0_sihua_sihua_2"=>["20160803040", "20160803140", "20160803180", "20170128020", "20160807020", "20160808200", "20160808220", "20160810160", "20160810200", "20160811180", "20160812120", "20061024080", "20200203040", "20061025020", "20061025060", "20061027200", "20061028060", "20061028220", "20061030080", "20061101180", "20160803040", "20061102040", "20160803041", "20160803141", "20160803181", "20110608140", "20160807021", "20160808201", "20160808221", "20160810161", "20160810201", "20160811181", "20160812121", "20071013181", "20071015181", "20071024061", "20071025061", "20071112201", "20071114201", "20071123081", "20071124081", "20071212221", "20071214221", "20071223101"], "sihua_sihua_1_sihua_sihua_2"=>["20010619160", "20010622160", "20010702040", "20010703040", "20010718160", "20010821180", "20010823180", "20010901060", "20010902060", "20011026200", "20011030200", "20080214080", "20080215080", "20080314120", "20080322120", "20080324120", "20080415160", "20080423160", "20080425160", "20080518200", "20080601200", "20010701140", "20081209041", "20081210041", "20090108081", "20090116081", "20090118081", "20110205121", "20140203080", "20010126120", "20110215121", "20040125080", "20110409021", "20120314201", "20120404201", "20120405201", "20120506201", "20120507201", "20120610201", "20120709201", "20120720201", "20120813201", "20120821201", "20120918201"], "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"=>["20060423220", "20080211180", "20121211000", "20030130040", "20060621020", "20180827060"]}
                              ]
  def Xuexi_Ziwei.gong_jia_xings_for_select(gong,oStar,nPanType)
    a = []
    zeri = Zeri.new
    @@gong_jia_xings.each do |gong_jia_xing|
      a.push(Xuexi_Ziwei.gong_jia_xing_str_timestamp(gong_jia_xing,gong,oStar,nPanType))
    end
    @@gong_jia_sihuas.each do |gong_jia_sihua|
      a.push(Xuexi_Ziwei.gong_jia_xing_str_timestamp(gong_jia_sihua,gong,oStar,nPanType))
    end
    @@gong_jia_specials.each do |gong_jia_special|
      a.push(Xuexi_Ziwei.gong_jia_xing_str_timestamp(gong_jia_special,gong,oStar,nPanType))
    end
    return a
  end
  def Xuexi_Ziwei.gong_jia_xing_str_timestamp(gong_jia_xing,gong,oStar,nPanType)
    h = {}
    h["string"] = Xuexi_Ziwei.gong_jia_xing_str(gong_jia_xing,gong)
    h["key"] = Xuexi_Ziwei.make_gong_jia_key(gong_jia_xing)
    h["timestamp"] = @@gong_jia_xing_timestamp[gong - 1][h["key"]].sample
    h["gong"] = gong
    hUserInfo = Xuexi_Ziwei.set_pan_user_info(h["timestamp"])
    oStar.resetUserInfo(nPanType,hUserInfo)
    h["gong_dizhi"] = oStar.g_House_GetEarth(nPanType,gong)
    h["xings"] = Xuexi_Ziwei.gong_jia_xing_to_pan_id(gong,oStar,nPanType,gong_jia_xing)
    return h
  end
  def Xuexi_Ziwei.set_pan_user_info(timestamp)
    hUserInfo = Hash.new
    # hUserInfo["remote_ip"] = remote_ip()
    y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp)
    hUserInfo[Cfate::WYear] = y
    hUserInfo[Cfate::WMonth] = m
    hUserInfo[Cfate::WDate] = d
    hUserInfo[Cfate::WHour] = h
    hUserInfo[Cfate::WMinute] = min
    hUserInfo[Cfate::Sex] = sex == 1 ? false : true
    hUserInfo[Cfate::Name] = timestamp
    hUserInfo[Cfate::Multiple_births] = mb
    return hUserInfo
  end

  def Xuexi_Ziwei.pan_gong_jia_xings_pan_id(gong,oStar,nPanType)
    gong_jia_xing = Xuexi_Ziwei.gong_jia_xing(gong,oStar,nPanType)
    xings = Xuexi_Ziwei.gong_jia_xing_to_pan_id(gong,oStar,nPanType,gong_jia_xing)
    return xings
  end
  def Xuexi_Ziwei.gong_jia_xing_to_pan_id(gong,oStar,nPanType,gong_jia_xing)
    if (gong_jia_xing == nil) then
      return []
    end
    if (!gong_jia_xing[0].include?("sihua")) then
      return Xuexi_Ziwei.gong_jia_xing_abstar_to_pan_id(gong_jia_xing)
    else
      sihua_xings = Xuexi_Ziwei.gong_jia_xing_sihua_to_xings(gong,oStar,nPanType,gong_jia_xing)
      return Xuexi_Ziwei.make_astar_pan_ids(sihua_xings)
    end
  end
  def Xuexi_Ziwei.gong_jia_xing_abstar_to_pan_id(gong_jia_xing)
    a = gong_jia_xing.clone
    a.map! {|x| x.sub("dis_astar.","a")}
    a.map! {|x| x.sub("dis_bstar.","b")}
    a.map! {|x| x.sub("_","")}
    return a
  end
  def Xuexi_Ziwei.gong_jia_xing_sihua_to_xings(gong,oStar,nPanType,gong_jia_xing)
    lingong1 = Xuexi_Ziwei.lin_gong(gong,1)
    lingong2 = Xuexi_Ziwei.lin_gong(gong,-1)
    xing1 = Xuexi_Ziwei.find_gong_jia_xing_sihua_xing(lingong1,oStar,nPanType,gong_jia_xing)
    xing2 = Xuexi_Ziwei.find_gong_jia_xing_sihua_xing(lingong2,oStar,nPanType,gong_jia_xing)

    return [xing1,xing2]
  end
  def Xuexi_Ziwei.find_gong_jia_xing_sihua_xing(gong,oStar,nPanType,gong_jia_xing)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    xings_a,bUseOpp = oStar.gHouse_GetAStars(nPanType,dizhi,false,false)
    sihuas = oStar.gHouse_GetAStarsFourHua(nPanType,dizhi)
    a = Xuexi_Ziwei.gong_jia_xing_to_xing(gong_jia_xing)
    sihuas.each_with_index do |sihua,i|
      if (a.include?(sihua[0])) then
        return xings_a[i]
      end
    end
    return nil
  end
  def Xuexi_Ziwei.gong_jia_xing_to_xing(gong_jia_xing)
    a = gong_jia_xing.clone
    a.map! {|x| x.sub("dis_astar.star_","")}
    a.map! {|x| x.sub("dis_bstar.star_","")}
    a.map! {|x| x.sub("sihua.sihua_","")}
    a.map! {|x| x.to_i}
    return a
  end
  def Xuexi_Ziwei.gong_jia_xing(gong,oStar,nPanType)
    lingong1 = Xuexi_Ziwei.lin_gong(gong,1)
    lingong2 = Xuexi_Ziwei.lin_gong(gong,-1)
    gong_xings = Xuexi_Ziwei.gong_abs_stars(gong,oStar,nPanType,false,false)
    lingong_xings1 = Xuexi_Ziwei.gong_abs_stars(lingong1,oStar,nPanType,false,false)
    lingong_xings2 = Xuexi_Ziwei.gong_abs_stars(lingong2,oStar,nPanType,false,false)

    gong_jia_xing = Xuexi_Ziwei.find_gong_jia_xing(gong_xings,lingong_xings1,lingong_xings2)
    return gong_jia_xing
  end
  # a star ,b star ,sihua
  def Xuexi_Ziwei.gong_abs_stars(gong,oStar,nPanType,bOnlyMain,bOppIfNoMain)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    xings_a,bUseOpp = oStar.gHouse_GetAStars(nPanType,dizhi,bOnlyMain,bOppIfNoMain)
    xings_b = oStar.gHouse_GetBStars(nPanType,dizhi)
    sihuas = oStar.gHouse_GetAStarsFourHua(nPanType,dizhi)
    a = Xuexi_Ziwei.gong_sihua_stars(sihuas)
    return [xings_a,xings_b,a]
  end
  # 祿權科忌都有
  def Xuexi_Ziwei.gong_sihua_stars(sihuas)
    a = []
    sihuas.each do |sihua|
      a.push(sihua[0]) # 包含99
    end
    return a
  end
  def Xuexi_Ziwei.find_gong_jia_xing(gong_xings,lingong_xings1,lingong_xings2)
    gong_abstar_keys = Xuexi_Ziwei.make_jia_abs_keys(gong_xings)
    lingong_abstar_keys1 = Xuexi_Ziwei.make_jia_abs_keys(lingong_xings1)
    lingong_abstar_keys2 = Xuexi_Ziwei.make_jia_abs_keys(lingong_xings2)

    @@gong_jia_xings.each do |gong_jia_xing|
      a1 = gong_jia_xing & lingong_abstar_keys1
      a2 = gong_jia_xing & lingong_abstar_keys2
      if (a1 != [] && a2 != []) then
        return gong_jia_xing.clone
      end
    end
    @@gong_jia_sihuas.each do |gong_jia_sihua|
      a1 = gong_jia_sihua & lingong_abstar_keys1
      a2 = gong_jia_sihua & lingong_abstar_keys2
      if (a1 != [] && a2 != []) then
        return gong_jia_sihua.clone
      end
    end
    @@gong_jia_specials.each do |gong_jia_special|
      a = [gong_jia_special[2]] & gong_abstar_keys
      a1 = [gong_jia_special[0]] & gong_abstar_keys
      # a2 = [gong_jia_special[1]] & lingong_abstar_keys2
      # a3 = [gong_jia_special[1]] & lingong_abstar_keys1
      a2 = [gong_jia_special[1]] & gong_abstar_keys
      a3 = [gong_jia_special[1]] & gong_abstar_keys
      if (a == gong_jia_special[2] && a1 == gong_jia_special[0] && (a2 == gong_jia_special[1] || a3 == gong_jia_special[1])) then
        return gong_jia_special.clone
      end
    end
    return nil
  end
  def Xuexi_Ziwei.gong_jia_xing_str(gong_jia_xing,gong)
    if (gong_jia_xing == nil) then
      return ""
    end
    q1 = ""
    if (gong_jia_xing.length == 2) then
      q1 = Pm.t("xuexi_ziwei.gong.gong_#{gong}")
    end
    key = Xuexi_Ziwei.make_gong_jia_key(gong_jia_xing)
    return Pm.t("xuexi_ziwei.gong.xing.question.jia.#{key}", :q1 => q1)
  end
  def Xuexi_Ziwei.make_jia_abs_keys(xings)
    xings_a_keys = Xuexi_Ziwei.make_astar_keys(xings[0])
    xings_b_keys = Xuexi_Ziwei.make_bstar_keys(xings[1])
    xings_sihua_keys = Xuexi_Ziwei.make_sihua_keys(xings[2],[3,99]) # remove 忌
    return xings_a_keys + xings_b_keys + xings_sihua_keys
  end
  def Xuexi_Ziwei.make_sihua_keys(sihuas_all,no_need_sihuas)
    sihuas = sihuas_all - no_need_sihuas
    a = []
    sihuas.each do |sihua|
      a.push(Xuexi_Ziwei.make_sihua_key(sihua))
    end
    return a
  end
  def Xuexi_Ziwei.make_sihua_key(sihua)
    return "sihua.sihua_#{sihua}"
  end
  def Xuexi_Ziwei.make_gong_jia_key(xing_key)
    a = xing_key.join("_")
    a.gsub!(".","_")
    return a
  end
  def Xuexi_Ziwei.find_gongs_gong_jia_xings_timestamp(nPanType,hUserDefData,hParAll)
    a = []
    (1..12).each do |gong|
      a.push(Xuexi_Ziwei.find_gong_gong_jia_xings_timestamp(gong,nPanType,hUserDefData,hParAll))
    end
    return a
  end
  def Xuexi_Ziwei.find_gong_gong_jia_xings_timestamp(gong,nPanType,hUserDefData,hParAll)
    h = {}
    @@gong_jia_xings.each do |gong_jia_xing|
      a1 = Xuexi_Ziwei.find_gong_gong_jia_xing_timestamps(gong,gong_jia_xing,nPanType,hUserDefData,hParAll)
      h["#{Xuexi_Ziwei.make_gong_jia_key(gong_jia_xing)}"] = a1
    end
    @@gong_jia_sihuas.each do |gong_jia_sihua|
      a1 = Xuexi_Ziwei.find_gong_gong_jia_xing_timestamps(gong,gong_jia_sihua,nPanType,hUserDefData,hParAll)
      h["#{Xuexi_Ziwei.make_gong_jia_key(gong_jia_sihua)}"] = a1
    end
    @@gong_jia_specials.each do |gong_jia_special|
      a1 = Xuexi_Ziwei.find_gong_gong_jia_xing_timestamps(gong,gong_jia_special,nPanType,hUserDefData,hParAll)
      h["#{Xuexi_Ziwei.make_gong_jia_key(gong_jia_special)}"] = a1
    end
    puts "a #{gong} : #{h}"
    return h
  end
  def Xuexi_Ziwei.check_gongs_gong_jia_xings_timestamp(nPanType,hUserDefData,hParAll)
    gjx_timestamp = @@gong_jia_xing_timestamp.clone
    puts "dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"
    (1..12).each do |gong|
      gjx_timestamp = Xuexi_Ziwei.check_gong_gong_jia_xings_timestamp(gong,nPanType,hUserDefData,hParAll,gjx_timestamp)

      puts "gjx_timestamp gong #{gong} : #{gjx_timestamp[gong - 1]["dis_astar_star_6_dis_astar_star_20_dis_astar_star_11"]}"
    end
    # puts "gjx_timestamp : #{gjx_timestamp}"
  end
  def Xuexi_Ziwei.check_gong_gong_jia_xings_timestamp(gong,nPanType,hUserDefData,hParAll,gjx_timestamp)
    @@gong_jia_xings.each do |gong_jia_xing|
      gjx_timestamp = Xuexi_Ziwei.check_gong_gong_jia_xing_timestamps(gong,gong_jia_xing,nPanType,hUserDefData,hParAll,gjx_timestamp)
    end
    @@gong_jia_sihuas.each do |gong_jia_sihua|
      gjx_timestamp = Xuexi_Ziwei.check_gong_gong_jia_xing_timestamps(gong,gong_jia_sihua,nPanType,hUserDefData,hParAll,gjx_timestamp)
    end
    @@gong_jia_specials.each do |gong_jia_special|
      gjx_timestamp = Xuexi_Ziwei.check_gong_gong_jia_xing_timestamps(gong,gong_jia_special,nPanType,hUserDefData,hParAll,gjx_timestamp)
    end
    return gjx_timestamp
  end
  def Xuexi_Ziwei.check_gong_gong_jia_xing_timestamps(gong,gong_jia_xing,nPanType,hUserDefData,hParAll,gjx_timestamp)
    key = Xuexi_Ziwei.make_gong_jia_key(gong_jia_xing)
    oStar = Star.new
    @@gong_jia_xing_timestamp[gong - 1][key].each_with_index do |timestamp,i|
      hUserInfo = Xuexi_Ziwei.set_pan_user_info(timestamp)
      legal = Xuexi_Ziwei.check_gong_jia_xing_timestamp_legal(gong,gong_jia_xing,nPanType,hUserInfo,hUserDefData,hParAll)
      if (!legal) then
        a = Xuexi_Ziwei.find_gong_gong_jia_xing_timestamps(gong,gong_jia_xing,nPanType,hUserDefData,hParAll)
        gjx_timestamp[gong - 1][key][i] = a[0] if a[0] != nil
        puts "illegal #{gong}:#{key} : #{timestamp} ; new : #{a[0]}"
      end
    end
    return gjx_timestamp
  end
  def Xuexi_Ziwei.check_gong_jia_xing_timestamp_legal(gong,gong_jia_xing,nPanType,hUserInfo,hUserDefData,hParAll)
    oStar = Star.new
    oStar.g_GetPanInfo(nPanType,hUserInfo,hUserDefData,hParAll)
    gong_dizhi = oStar.g_House_GetEarth(nPanType,gong)
    dizhis = Xuexi_Ziwei.get_gong_jia_xing_dizhis(oStar,nPanType,gong_jia_xing)
    return Xuexi_Ziwei.gong_jia_xing_match(dizhis,gong_dizhi,gong_jia_xing)
  end
  def Xuexi_Ziwei.gong_jia_xing_match(dizhis,gong_dizhi,gong_jia_xing)
    len  = gong_jia_xing.length
    if (len == 2) then
      n = Xuexi_Ziwei.dizhi_distance(dizhis[0],dizhis[1])
      return false if (n != 2)
      n = Xuexi_Ziwei.dizhi_distance(gong_dizhi,dizhis[0])
      return false if (n != 1)
      n = Xuexi_Ziwei.dizhi_distance(gong_dizhi,dizhis[1])
      return false if (n != 1)
    elsif (len == 3) then
      n = Xuexi_Ziwei.dizhi_distance(dizhis[0],dizhis[2])
      return false if (n != 0)
      n = Xuexi_Ziwei.dizhi_distance(gong_dizhi,dizhis[0])
      return false if (n != 0)
      n = Xuexi_Ziwei.dizhi_distance(dizhis[0],dizhis[1])
      return false if (n != 0)
      # return false if (n != 1)
    end
    return true
  end
  def Xuexi_Ziwei.dizhi_distance(dizhi1,dizhi2)
    n = dizhi1 - dizhi2
    n = n.abs
    n = 12 - n if (n > 6)
    return n
  end
  def Xuexi_Ziwei.get_gong_jia_xing_dizhis(oStar,nPanType,gong_jia_xing)
    xings = Xuexi_Ziwei.gong_jia_xing_to_xing(gong_jia_xing)
    len  = gong_jia_xing.length
    dizhis = Array.new(len,0)
    gong_jia_xing.each_with_index do |xing_key,i|
      dizhis[i] = Xuexi_Ziwei.get_xing_dizhi(oStar,nPanType,xing_key,xings[i])
    end
    return dizhis
  end
  def Xuexi_Ziwei.get_xing_dizhi(oStar,nPanType,xing_key,xing)
    if (xing_key.include?("astar")) then
      return oStar.get_A_Star_HouseEarth(nPanType,xing)
    elsif (xing_key.include?("bstar")) then
      return oStar.get_B_Star_HouseEarth(nPanType,xing)
    elsif (xing_key.include?("sihua")) then
      hFHHouseStar = oStar.cp_get_OriginalFourHua(nPanType,xing)
      return hFHHouseStar[Star::HOUSE_EARTH]
    end
    return 0
  end
  def Xuexi_Ziwei.find_gong_gong_jia_xing_timestamps(gong,gong_jia_xing,nPanType,hUserDefData,hParAll)
    a = []
    [true,false].each do |sex|
      [1,-1].each do |lingong_dist|
        pars = Xuexi_Ziwei.make_gong_jia_xing_zeri_pars(gong,gong_jia_xing,lingong_dist)
        zeri_pars = Zeri.set_xuexi_zeri_pars(pars,sex)
        a += Xuexi_Ziwei.zeri_pan_answer_xuexi_ziewi(Cfate::PAN_NORMAL,zeri_pars,hUserDefData,hParAll)
      end
    end
    return a
  end
  def Xuexi_Ziwei.zeri_pan_answer_xuexi_ziewi(nPanType,zeri_pars,hUserDefData,hParAll)
    a = Array.new

    days = zeri_pars["days"]
    hUserInfo = zeri_pars["UserInfo"]
    days = 365
    nWYear,nWMonth,nWDate = hUserInfo[Cfate::WYear],hUserInfo[Cfate::WMonth],hUserInfo[Cfate::WDate]
    (0...days).each do |n_day|
      (0...12).each do |nTime|
        hUserInfo[Cfate::WYear] = nWYear
        hUserInfo[Cfate::WMonth] = nWMonth
        hUserInfo[Cfate::WDate] = nWDate
        hUserInfo[Cfate::WHour] = Xdate.ETime2Hour(nTime)
        bFind,answer = Xuexi_Ziwei.find_zeri_pan_answer_xuexi_ziwei(nPanType,zeri_pars,hUserDefData,hParAll)
        if (bFind) then
          a.push(answer)
          if (a.length > zeri_pars["need_count"]) then
            return a
          end
        end
      end
      nWYear,nWMonth,nWDate = Xdate.NextWDate(nWYear,nWMonth,nWDate)
    end

    return a
  end
  def Xuexi_Ziwei.zeri_pan_answer_xuexi_ziewi_6_11_20(nPanType,hUserDefData,hParAll)
    a = Array.new

    days = 365 * 100
    hUserInfo = {}
    nWYear,nWMonth,nWDate = 1920,1,1

    (0...days).each do |n_day|
      (0...12).each do |nTime|
        hUserInfo[Cfate::WYear] = nWYear
        hUserInfo[Cfate::WMonth] = nWMonth
        hUserInfo[Cfate::WDate] = nWDate
        hUserInfo[Cfate::WHour] = Xdate.ETime2Hour(nTime)

        bFind,answer = Xuexi_Ziwei.find_zeri_pan_answer_xuexi_ziwei_6_11_20(nPanType,hUserInfo,hUserDefData,hParAll)
        if (bFind) then
          a.push(answer)
          if (a.length >= 5) then
            return a
          end
        end
      end
      nWYear,nWMonth,nWDate = Xdate.NextWDate(nWYear,nWMonth,nWDate)
    end

    return a
  end
  def Xuexi_Ziwei.find_zeri_pan_answer_xuexi_ziwei_6_11_20(nPanType,hUserInfo,hUserDefData,hParAll)
    oStar = Star.new
    oStar.g_GetPanInfo(nPanType,hUserInfo,hUserDefData,hParAll)

    bFind = false
    timestamp = ""
    n6 = oStar.cp_get_A_Star_6(nPanType,6)
    n11 = oStar.cp_get_A_Star_11(nPanType,11)
    if ((n6 - n11).abs == 0) then
      n20 = oStar.cp_get_A_Star_20(nPanType,20)
      if ((n20 - n11).abs == 1) then
        timestamp = "#{oStar.uig_W_timestamp()}#{oStar.uig_bS_Ui_Val()}"
        bFind = true
      end
    end

    return bFind,timestamp
  end

  def Xuexi_Ziwei.find_zeri_pan_answer_xuexi_ziwei(nPanType,zeri_pars,hUserDefData,hParAll)
    hUserInfo = zeri_pars["UserInfo"]
    oStar = Star.new
    oStar.g_GetPanInfo(nPanType,hUserInfo,hUserDefData,hParAll)
    hOut = Hash.new
    bFind = false
    timestamp = ""
    if (oStar.match_zeri_pars(zeri_pars)) then
      timestamp = "#{oStar.uig_W_timestamp()}#{oStar.uig_bS_Ui_Val()}"
      bFind = true
    end

    return bFind,timestamp
  end

  def Xuexi_Ziwei.gong_jia_xing_to_str(gong_jia_xing)
    a = gong_jia_xing.join("___")
    a.gsub!(".","__")
    return a
  end
  def Xuexi_Ziwei.str_to_gong_jia_xing(str)
    a = str.split("___")
    a.gsub!("__",".")
    return a
  end
  def Xuexi_Ziwei.make_gong_jia_xing_str_zeri_pars(gong,str)
    gong_jia_xing = Xuexi_Ziwei.str_to_gong_jia_xing(str)
    return Xuexi_Ziwei.make_gong_jia_xing_zeri_pars(gong,gong_jia_xing,1)
  end
  def Xuexi_Ziwei.make_gong_jia_xing_zeri_pars(gong,gong_jia_xing,lingong_dist)
    xings = Xuexi_Ziwei.gong_jia_xing_to_xing(gong_jia_xing)

    lingong1 = Xuexi_Ziwei.lin_gong(gong,lingong_dist)
    lingong2 = Xuexi_Ziwei.lin_gong(gong,0 - lingong_dist)
    aOut = []

    if (gong_jia_xing.length == 2) then
      par = Xuexi_Ziwei.make_gong_jia_xing_zeri_par([lingong1],gong_jia_xing[0],xings[0])
      aOut.push(par)
      par = Xuexi_Ziwei.make_gong_jia_xing_zeri_par([lingong2],gong_jia_xing[1],xings[1])
      aOut.push(par)
    else
      par = Xuexi_Ziwei.make_gong_jia_xing_zeri_par([gong],gong_jia_xing[0],xings[0])
      aOut.push(par)
      par = Xuexi_Ziwei.make_gong_jia_xing_zeri_par([gong],gong_jia_xing[1],xings[1])
      # par = Xuexi_Ziwei.make_gong_jia_xing_zeri_par([lingong1,lingong2],gong_jia_xing[1],xings[1])
      aOut.push(par)
      par = Xuexi_Ziwei.make_gong_jia_xing_zeri_par([gong],gong_jia_xing[2],xings[2])
      aOut.push(par)
    end

    return aOut
  end
  def Xuexi_Ziwei.make_gong_jia_xing_zeri_par(gongs,gong_jia_xing,xing)
    a = gong_jia_xing.split(".")
    xingyao1_jiaxing,xingyao1_yixing,xingyao2_jiaxing,xingyao2_yixing,sihua = Xuexi_Ziwei.check_xing_in_xingyao(a[0],xing)
    gongwei = [0,0,0,0,0]
    gongs.each_with_index do |gong,i|
      gongwei[i] = gong
    end
    return Zeri.make_xuexi_ziwei_par(gongwei,xingyao1_jiaxing,xingyao1_yixing,xingyao2_jiaxing,xingyao2_yixing,sihua,[])
  end
  def Xuexi_Ziwei.check_xing_in_xingyao(xingji,xing)
    if (xingji == "dis_astar") then
      a = Star.xingyao1_jiaxing_select()
      if (a.include?(xing)) then
        return [xing],[],[],[],[]
      end
      a = Star.xingyao2_jiaxing_select()
      if (a.include?(xing)) then
        return [],[],[xing],[],[]
      end
    end
    if (xingji == "dis_bstar") then
      a = Star.xingyao1_yixing_select()
      if (a.include?(xing)) then
        return [],[xing],[],[],[]
      end
      a = Star.xingyao2_yixing_select()
      if (a.include?(xing)) then
        return [],[],[],[xing],[]
      end
    end
    if (xingji == "sihua") then
      a = [0,1,2,3]
      if (a.include?(xing)) then
        return [],[],[],[],[xing]
      end
    end
    return [],[],[],[],[]
  end

  # 煞星（六煞、天刑、耗星）
  def Xuexi_Ziwei.gong_xing_xia_xuaxiong2(oStar,nPanType)
    q1key = "xia"

    xuaxiongs,notxuaxiongs,timestamp = Xuexi_Ziwei.jieguo_gong_xing_xia_strs(oStar,nPanType)

    notxuaxiongs.uniq!
    a = xuaxiongs & notxuaxiongs
    b = notxuaxiongs - a
    return q1key,xuaxiongs,b,timestamp
  end
  @@gong_xia_xing_timestamp = [["20161023041", "20161025000", "20161022121", "20161022121", "20131026060", "20061121180"], ["20161023060", "20161025081", "20111027000", "20121021060", "20141025001", "20051026021"], ["20161023000", "20161025060", "19701024140", "19701025001", "19701025181", "19701031121"], ["20161023060", "20161025041", "19701023061", "19701025041", "19701024000", "19701025040"], ["20161023001", "20161025020", "19701024061", "19701025021", "19701026141", "19701030000"], ["20161023060", "20161025081", "19701023000", "19701025040", "19701024001", "19701025040"], ["20161023001", "20161025060", "19701024061", "19701025021", "19701025100", "19701030000"], ["20161023021", "20161025041", "19701023000", "19701025040", "19701024080", "19701025041"], ["20161023041", "20161025020", "19701024060", "19701025001", "19701024061", "19701030081"], ["20161023060", "20161025080", "19701023000", "19701025061", "19701024001", "19701026080"], ["20161023000", "20161025060", "19701024141", "19701026061", "19701029021", "19701102200"], ["20161023141", "20161025040", "19701023061", "19701025040", "19701024080", "19701025040"]]
  def Xuexi_Ziwei.jieguo_gong_xing_xia_strs(oStar,nPanType)
    a = []
    gong,str,timestamp = Xuexi_Ziwei.find_jieguo_gong_xing_xia_str(oStar,nPanType)
    a.push(str)

    a2 = []
    (1..12).each do |gong|
      a2.push(Xuexi_Ziwei.jieguo_gong_not_xing_xia_str(gong,oStar,nPanType))
    end

    return a,a2,timestamp
  end
  def Xuexi_Ziwei.find_jieguo_gong_xing_xia_str(oStar,nPanType)
    (1..12).each do |gong|
      timestamp = Xuexi_Ziwei.xia_timestamp(gong)
      hUserInfo = Xuexi_Ziwei.set_pan_user_info(timestamp)
      oStar.resetUserInfo(nPanType,hUserInfo)
      find,sha_xing_keys = Xuexi_Ziwei.gong_sha_xing_keys(1,gong,oStar,nPanType)
      if (find) then
        str = Xuexi_Ziwei.gong_xing_xia_str(gong,find,sha_xing_keys)
        return gong,str,timestamp
      end
    end
    return 1,Xuexi_Ziwei.gong_xing_xia_str(1,false,sha_xing_keys),timestamp
  end
  def Xuexi_Ziwei.jieguo_gong_xing_xia_str(gong,oStar,nPanType)
    find,sha_xing_keys = Xuexi_Ziwei.gong_sha_xing_keys(1,gong,oStar,nPanType)
    return Xuexi_Ziwei.gong_xing_xia_str(gong,find,sha_xing_keys)
  end
  def Xuexi_Ziwei.gong_xing_xia_str(gong,find,sha_xing_keys)
    if (find) then
      jixing_key = sha_xing_keys[0]
      q1 = Xuexi_Ziwei.xing_str_with_xing(jixing_key)
      q2 = Xuexi_Ziwei.xia_sha_xings_str(sha_xing_keys)
      q3 = Xuexi_Ziwei.gong_str_with_gong(gong)
      return Pm.t("xuexi_ziwei.gong.xing.question.xia.you",:q2 => q2,:q3 => q3)
    else
      q1 = Xuexi_Ziwei.gong_str_with_gong(gong)
      return Pm.t("xuexi_ziwei.gong.xing.question.xia.wu",:q1 => q1)
    end
  end
  def Xuexi_Ziwei.jieguo_gong_not_xing_xia_str(gong,oStar,nPanType)
    find,sha_xing_keys = Xuexi_Ziwei.gong_sha_xing_keys(1,gong,oStar,nPanType)
    all_shaxing_keys = Xuexi_Ziwei.shaxing_keys()
    wrong_gong = Xuexi_Ziwei.rand_not_gong(gong)
    if (find) then
      if (rand(2) == 1) then
        return Xuexi_Ziwei.gong_xing_xia_str(wrong_gong,find,sha_xing_keys)
      else
        return Xuexi_Ziwei.gong_xing_xia_str(gong,find,[sha_xing_keys[0]] + all_shaxing_keys - sha_xing_keys)
      end
    else
      return Xuexi_Ziwei.gong_xing_xia_str(gong,true,[Xuexi_Ziwei.make_astar_key(rand(10) + 1)] + Xuexi_Ziwei.rand_shaxing_keys(3))
    end
  end
  def Xuexi_Ziwei.xia_sha_xings_str(sha_xing_keys)
    a = []
    sha_xing_keys.each_with_index do |sha_xing_key,i|
      if (i > 0) then
        a.push(Pm.t("xuexi_ziwei.xing.#{sha_xing_key}"))
      end
    end
    return a.join(",")
  end
  # 煞星（六煞、天刑、耗星）
  # 六煞星或煞星：羊刃（擎羊星）、陀羅、火星、鈴星、天空、地劫，對凶星的威力有加強的作用，對吉星有減弱吉星效果。
  def Xuexi_Ziwei.shaxing_pan_ids()
    a = []
    # 六煞 羊刃（擎羊星）、陀羅、火星、鈴星
    a.push(["astar20","astar21","astar22","astar23"])
    # 天空、地劫,天刑
    a.push(["bstar1","bstar2","bstar3"])
    a.push([]) # 四化
    # 耗星 博士 大小耗
    a.push(["doctor4","doctor10"])
    # 耗星 歲建 大小耗
    a.push(["yeargod6","yeargod7"])

    a.push([]) # 將星
    a.push([]) # 十二長生
    return a
  end
  def Xuexi_Ziwei.shaxing_keys()
    return [["dis_astar.star_20","dis_astar.star_21","dis_astar.star_22","dis_astar.star_23"],
            ["dis_bstar.star_1","dis_bstar.star_2","dis_bstar.star_3"],
            [],
            ["dis_doctor.doctor_4","dis_doctor.doctor_10"],
            ["dis_yeargod.yeargod_6","dis_yeargod.yeargod_7"],
            [],
            []
        ].flatten
  end
  def Xuexi_Ziwei.rand_shaxing_keys(n)
    all_shaxing_keys = Xuexi_Ziwei.shaxing_keys()
    a = []
    (1..n).each do |i|
      a.push(all_shaxing_keys[rand(all_shaxing_keys.length)])
    end
    return a
  end
  # 忌星
  def Xuexi_Ziwei.ji_xing_key()
    return ["sihua.sihua_3"]
  end

  def Xuexi_Ziwei.shaxing_xings_str()
    x = Xuexi_Ziwei.shaxing_keys().flatten
    a = []
    x.each do |k|
      a.push(Pm.t("xuexi_ziwei.xing.#{k}"))
    end
    return a
  end
  def Xuexi_Ziwei.rand_xia_timestamp()
    gong = Xuexi_Ziwei.check_gong(rand(12))
    return Xuexi_Ziwei.xia_timestamp(gong)
  end
  def Xuexi_Ziwei.xia_timestamp(gong)
    return @@gong_xia_xing_timestamp[gong - 1].sample
  end
  def Xuexi_Ziwei.pan_gong_xia_xings_pan_id(gong,oStar,nPanType)
    find,sha_xing_keys = Xuexi_Ziwei.gong_sha_xing_pan_ids(1,gong,oStar,nPanType)
    return Xuexi_Ziwei.gong_xia_xing_to_pan_id(sha_xing_keys)
  end
  def Xuexi_Ziwei.gong_sha_xing_pan_ids(sha_count,gong,oStar,nPanType)
    find,sha_xing_keys = Xuexi_Ziwei.gong_sha_xing_keys(sha_count,gong,oStar,nPanType)
    return find,Xuexi_Ziwei.gong_xia_xing_to_pan_id(sha_xing_keys)
  end
  def Xuexi_Ziwei.gong_xia_xing_to_pan_id(sha_xing_keys)
    a = sha_xing_keys.clone
    a.map! {|x| x.sub("dis_astar.","a")}
    a.map! {|x| x.sub("dis_bstar.","b")}
    a.map! {|x| x.sub("dis_doctor.","")}
    a.map! {|x| x.sub("dis_yeargod.","")}
    a.map! {|x| x.sub("dis_yearstar.","")}
    a.map! {|x| x.sub("dis_god.","")}
    a.map! {|x| x.sub("_","")}
    return a
  end
  def Xuexi_Ziwei.gong_xia_xing_to_xing(gong_xia_xing)
    a = gong_xia_xing.clone
    a.map! {|x| x.sub("dis_astar.star_","")}
    a.map! {|x| x.sub("dis_bstar.star_","")}
    a.map! {|x| x.sub("sihua.sihua_","")}
    a.map! {|x| x.sub("dis_doctor.doctor_","")}
    a.map! {|x| x.sub("dis_yeargod._yeargod","")}
    a.map! {|x| x.sub("dis_yearstar._yearstar","")}
    a.map! {|x| x.sub("dis_god.god_","")}
    a.map! {|x| x.to_i}
    return a
  end
  def Xuexi_Ziwei.gong_sha_xing_keys(sha_count,gong,oStar,nPanType)
    sanfangsizhengs = Xuexi_Ziwei.find_gong_sanfangsizheng(gong,oStar,nPanType)
    jixing = Xuexi_Ziwei.find_sanfangsizheng_jixing(sanfangsizhengs,oStar,nPanType)
    find = false
    if (jixing == nil) then
      return find,[]
    end
    gong_xingss = []
    sanfangsizhengs.each do |sanfangsizheng|
      gong_xingss.push(Xuexi_Ziwei.gong_all_stars(sanfangsizheng,oStar,nPanType,false,false))
    end

    a = nil
    gong_xingss.each do |gong_xings|
      a = Xuexi_Ziwei.gong_xings_add(a,gong_xings)
    end
    jixing_key = Xuexi_Ziwei.make_astar_key(jixing)
    gong_sha_xing_keys = Xuexi_Ziwei.find_gong_sha_xing(a)
    find = true if gong_sha_xing_keys.length >= sha_count
    return find,[jixing_key] + gong_sha_xing_keys
  end
  def Xuexi_Ziwei.gong_xings_add(gong_xings1,gong_xings2)
    if (gong_xings1 == nil) then
      return gong_xings2.clone
    end
    a = Array.new(gong_xings1.length)
    gong_xings1.each_with_index do |gong_xings,i|
      a[i] = gong_xings1[i] + gong_xings2[i]
    end
    return a
  end
  def Xuexi_Ziwei.find_gong_sha_xing(gong_xings)
    xing_keys = Xuexi_Ziwei.make_xia_xing_keys(gong_xings)
    all_shaxing_keys = Xuexi_Ziwei.shaxing_keys()
    return all_shaxing_keys & xing_keys
  end
  def Xuexi_Ziwei.make_xia_xing_keys(gong_xings)
    xings_a_keys = Xuexi_Ziwei.make_astar_keys(gong_xings[0])
    xings_b_keys = Xuexi_Ziwei.make_bstar_keys(gong_xings[1])
    doctor_keys = Xuexi_Ziwei.make_doctor_keys(gong_xings[3])
    yeargod_keys = Xuexi_Ziwei.make_yeargod_keys(gong_xings[4])
    yearstar_keys = Xuexi_Ziwei.make_yearstar_keys(gong_xings[5])
    god_keys = Xuexi_Ziwei.make_god_keys(gong_xings[6])
    return xings_a_keys + xings_b_keys + doctor_keys + yeargod_keys + yearstar_keys + god_keys
  end
  def Xuexi_Ziwei.make_xia_jixing_keys(gong_xings)
    xings_sihua_keys = Xuexi_Ziwei.make_sihua_keys(gong_xings[2],[0,1,2,99]) # remove 祿權科
    return xings_sihua_keys
  end

  # [xings_a,xings_b,sihuas,doctor,yeargod,yearstar,god]
  def Xuexi_Ziwei.gong_all_stars(gong,oStar,nPanType,bOnlyMain,bOppIfNoMain)
    abs_stars = Xuexi_Ziwei.gong_abs_stars(gong,oStar,nPanType,bOnlyMain,bOppIfNoMain)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    doctor = oStar.get_Doctor(nPanType,dizhi)
    abs_stars.push([doctor])
    yeargod = oStar.get_YearGod(nPanType,dizhi)
    abs_stars.push([yeargod])
    yearstar = oStar.get_YearStar(nPanType,dizhi)
    abs_stars.push([yearstar])
    god = oStar.get_God(nPanType,dizhi)
    abs_stars.push([god])

    return abs_stars
  end
  def Xuexi_Ziwei.check_gong_xia_xing_jixing(gong_xings)
    return gong_xings[2].include?(Star::FH_GI)
  end
  def Xuexi_Ziwei.find_sanfangsizheng_jixing(sanfangsizhengs,oStar,nPanType)
    sanfangsizhengs.each do |sanfangsizheng|
      xing = Xuexi_Ziwei.find_gong_sihua_xing(sanfangsizheng,oStar,nPanType,Star::FH_GI)
      if (xing != nil) then
        return xing
      end
    end
    return nil
  end
  def Xuexi_Ziwei.find_gong_sihua_xing(gong,oStar,nPanType,sihua_in)
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    xings_a,bUseOpp = oStar.gHouse_GetAStars(nPanType,dizhi,false,false)
    sihuas = oStar.gHouse_GetAStarsFourHua(nPanType,dizhi)
    sihuas.each_with_index do |sihua,i|
      if (sihua_in == sihua[0]) then
        return xings_a[i]
      end
    end
    return nil
  end

  def Xuexi_Ziwei.check_gongs_gong_xia_xings_timestamp(nPanType,hUserDefData,hParAll)
    gxx_timestamp = @@gong_xia_xing_timestamp.clone
    (1..12).each do |gong|
      gxx_timestamp = Xuexi_Ziwei.check_gong_gong_xia_xings_timestamp(gong,nPanType,hUserDefData,hParAll,gxx_timestamp)
    end
    puts "gxx_timestamp : #{gxx_timestamp}"
  end
  def Xuexi_Ziwei.check_gong_gong_xia_xings_timestamp(gong,nPanType,hUserDefData,hParAll,gxx_timestamp)
    oStar = Star.new
    @@gong_xia_xing_timestamp[gong - 1].each_with_index do |timestamp,i|
      hUserInfo = Xuexi_Ziwei.set_pan_user_info(timestamp)
      legal,sha_xing_keys,t1 = Xuexi_Ziwei.check_gong_xia_xing_timestamp_legal(i + 1,gong,nPanType,hUserInfo,hUserDefData,hParAll)
      if (!legal) then
        a = Xuexi_Ziwei.find_gong_xia_xing_timestamp(i + 1,gong,nPanType,hUserInfo,hUserDefData,hParAll)
        gxx_timestamp[gong - 1][i] = a if a != nil
        puts "illegal #{gong}: : #{timestamp} ; new : #{a}"
      end
    end
    return gxx_timestamp
  end
  def Xuexi_Ziwei.check_gong_xia_xing_timestamp_legal(sha_count,gong,nPanType,hUserInfo,hUserDefData,hParAll)
    oStar = Star.new
    oStar.g_GetPanInfo(nPanType,hUserInfo,hUserDefData,hParAll)
    legal,sha_xing_keys = Xuexi_Ziwei.gong_sha_xing_keys(sha_count,gong,oStar,nPanType)
    timestamp = "#{oStar.uig_W_timestamp()}#{oStar.uig_bS_Ui_Val()}"
    return legal,sha_xing_keys,timestamp
  end
  def Xuexi_Ziwei.find_gong_xia_xing_timestamp(sha_count,gong,nPanType,hUserInfo,hUserDefData,hParAll)
    sex = [true,false]
    hUserInfo[Cfate::Sex] = sex[rand(2)]
    return Xuexi_Ziwei.find_next_gong_xia_xing_timestamp(sha_count,gong,nPanType,hUserInfo,hUserDefData,hParAll)
  end
  def Xuexi_Ziwei.find_next_gong_xia_xing_timestamp(sha_count,gong,nPanType,hUserInfo,hUserDefData,hParAll)
    # max_sha 7 include ji
    days = 365
    nWYear,nWMonth,nWDate = hUserInfo[Cfate::WYear],hUserInfo[Cfate::WMonth],hUserInfo[Cfate::WDate]
    (0...days).each do |n_day|
      (0...12).each do |nTime|
        hUserInfo[Cfate::WYear] = nWYear
        hUserInfo[Cfate::WMonth] = nWMonth
        hUserInfo[Cfate::WDate] = nWDate
        hUserInfo[Cfate::WHour] = Xdate.ETime2Hour(nTime)
        legal,sha_xing_keys,timestamp = Xuexi_Ziwei.check_gong_xia_xing_timestamp_legal(sha_count,gong,nPanType,hUserInfo,hUserDefData,hParAll)
        if (legal) then
          return timestamp
        end
      end
      nWYear,nWMonth,nWDate = Xdate.NextWDate(nWYear,nWMonth,nWDate)
    end

    return ""
  end

  # 四生宮
  @@Sishenggong = [3,6,9,12]
  def Xuexi_Ziwei.sishenggong_dizhis()
    return @@Sishenggong
  end
  def Xuexi_Ziwei.jieguo_sishenggong_right_str()
    return Xuexi_Ziwei.jieguo_sishenggong_normal_str(@@Sishenggong)
  end
  def Xuexi_Ziwei.jieguo_sishenggong_normal_str(dizhis)
    q1 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[0]}")
    q2 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[1]}")
    q3 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[2]}")
    q4 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[3]}")
    return Pm.t("xuexi_ziwei.gong.sishenggong.question.normal", :q1 => q1, :q2 => q2, :q3 => q3, :q4 => q4)
  end
  # is notis
  def Xuexi_Ziwei.sishenggong_xuaxiong()
    q1 = Pm.t("xuexi_ziwei.gong.sishenggong.taitou")
    xuaxiongs = Xuexi_Ziwei.sishenggong_xuaxiong_dizhi_data()
    notxuaxiongs = Xuexi_Ziwei.sishenggong_notxuaxiong_dizhi_data()
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.sishenggong_xuaxiong_dizhi_data()
    dizhis = @@Sishenggong
    a = []
    dizhis.each do |dizhi|
      a.push(Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi}"))
    end
    return a
  end
  def Xuexi_Ziwei.sishenggong_notxuaxiong_dizhi_data()
    dizhis = (1..12).to_a - @@Sishenggong
    a = []
    dizhis.each do |dizhi|
      a.push(Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi}"))
    end
    return a
  end
  # right wrong
  def Xuexi_Ziwei.sishenggong_xuaxiong2(oStar,nPanType)
    q1 = Pm.t("xuexi_ziwei.gong.sishenggong.taitou")
    xuaxiongs = Xuexi_Ziwei.sishenggong_xuaxiong2_strs(oStar,nPanType)
    notxuaxiongs = Xuexi_Ziwei.sishenggong_notxuaxiong2_strs(oStar,nPanType)
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.sishenggong_xuaxiong2_strs(oStar,nPanType)
    a = []
    a.push(Xuexi_Ziwei.jieguo_sishenggong_right_str())

    return a
  end
  def Xuexi_Ziwei.sishenggong_notxuaxiong2_strs(oStar,nPanType)
    dizhiss = Xuexi_Ziwei.rand_not_sishenggong_dizhis(10)
    a = Xuexi_Ziwei.jieguo_sishenggong_normal_strs(dizhiss,oStar,nPanType)
    return a
  end
  def Xuexi_Ziwei.jieguo_sishenggong_normal_strs(dizhiss,oStar,nPanType)
    a = []
    dizhiss.each do |dizhis|
      a.push(Xuexi_Ziwei.jieguo_sishenggong_normal_str(dizhis))
    end
    return a
  end
  def Xuexi_Ziwei.rand_not_sishenggong_dizhis(n)
    a = []
    while (a.length < n)
      dizhis = Xuexi_Ziwei.rand_dizhis(4)
      if (!a.include?(dizhis) && dizhis.sort != @@Sishenggong) then
        a.push(dizhis.sort)
      end
    end
    return a
  end
  def Xuexi_Ziwei.rand_dizhis(n)
    a = []
    while (a.length < n)
      dizhi = Xuexi_Ziwei.rand_dizhi()
      if (!a.include?(dizhi)) then
        a.push(dizhi)
      end
    end
    return a
  end
  def Xuexi_Ziwei.sishenggong_xuaxiong3(oStar,nPanType)
    q1 = Pm.t("xuexi_ziwei.gong.sishenggong.taitou")
    xuaxiongs = Xuexi_Ziwei.sishenggong_xuaxiong3_strs(oStar,nPanType)
    notxuaxiongs = Xuexi_Ziwei.sishenggong_notxuaxiong3_strs(oStar,nPanType)
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.sishenggong_xuaxiong3_strs(oStar,nPanType)
    a = []
    @@Sishenggong.each do |dizhi|
      gong = oStar.g_House_GetHouseId(nPanType,dizhi)
      a.push(Xuexi_Ziwei.jieguo_sishenggong_pan_str(gong))
    end
    return a
  end
  def Xuexi_Ziwei.jieguo_sishenggong_pan_str(gong)
    q1 = Xuexi_Ziwei.gong_str_with_gong(gong)
    return Pm.t("xuexi_ziwei.gong.sishenggong.question.pan", :q1 => q1)
  end
  def Xuexi_Ziwei.sishenggong_notxuaxiong3_strs(oStar,nPanType)
    a = []
    dizhis = (1..12).to_a - @@Sishenggong
    dizhis.each do |dizhi|
      gong = oStar.g_House_GetHouseId(nPanType,dizhi)
      a.push(Xuexi_Ziwei.jieguo_sishenggong_pan_str(gong))
    end
    return a
  end

  # 四墓宮
  # 辰、戍、丑、未四宮稱為「四墓宮」
  @@Simugong = [2,5,8,11]
  def Xuexi_Ziwei.simugong_dizhis()
    return @@Simugong
  end
  def Xuexi_Ziwei.jieguo_simugong_right_str()
    return Xuexi_Ziwei.jieguo_simugong_normal_str(@@Simugong)
  end
  def Xuexi_Ziwei.jieguo_simugong_normal_str(dizhis)
    q1 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[0]}")
    q2 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[1]}")
    q3 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[2]}")
    q4 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[3]}")
    return Pm.t("xuexi_ziwei.gong.simugong.question.normal", :q1 => q1, :q2 => q2, :q3 => q3, :q4 => q4)
  end
  # is notis
  def Xuexi_Ziwei.simugong_xuaxiong()
    q1 = Pm.t("xuexi_ziwei.gong.simugong.taitou")
    xuaxiongs = Xuexi_Ziwei.simugong_xuaxiong_dizhi_data()
    notxuaxiongs = Xuexi_Ziwei.simugong_notxuaxiong_dizhi_data()
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.simugong_xuaxiong_dizhi_data()
    dizhis = @@Simugong
    a = []
    dizhis.each do |dizhi|
      a.push(Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi}"))
    end
    return a
  end
  def Xuexi_Ziwei.simugong_notxuaxiong_dizhi_data()
    dizhis = (1..12).to_a - @@Simugong
    a = []
    dizhis.each do |dizhi|
      a.push(Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi}"))
    end
    return a
  end
  # right wrong
  def Xuexi_Ziwei.simugong_xuaxiong2(oStar,nPanType)
    q1 = Pm.t("xuexi_ziwei.gong.simugong.taitou")
    xuaxiongs = Xuexi_Ziwei.simugong_xuaxiong2_strs(oStar,nPanType)
    notxuaxiongs = Xuexi_Ziwei.simugong_notxuaxiong2_strs(oStar,nPanType)
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.simugong_xuaxiong2_strs(oStar,nPanType)
    a = []
    a.push(Xuexi_Ziwei.jieguo_simugong_right_str())

    return a
  end
  def Xuexi_Ziwei.simugong_notxuaxiong2_strs(oStar,nPanType)
    dizhiss = Xuexi_Ziwei.rand_not_simugong_dizhis(10)
    a = Xuexi_Ziwei.jieguo_simugong_normal_strs(dizhiss,oStar,nPanType)
    return a
  end
  def Xuexi_Ziwei.jieguo_simugong_normal_strs(dizhiss,oStar,nPanType)
    a = []
    dizhiss.each do |dizhis|
      a.push(Xuexi_Ziwei.jieguo_simugong_normal_str(dizhis))
    end
    return a
  end
  def Xuexi_Ziwei.rand_not_simugong_dizhis(n)
    a = []
    while (a.length < n)
      dizhis = Xuexi_Ziwei.rand_dizhis(4)
      if (!a.include?(dizhis) && dizhis.sort != @@Simugong) then
        a.push(dizhis.sort)
      end
    end
    return a
  end
  def Xuexi_Ziwei.simugong_xuaxiong3(oStar,nPanType)
    q1 = Pm.t("xuexi_ziwei.gong.simugong.taitou")
    xuaxiongs = Xuexi_Ziwei.simugong_xuaxiong3_strs(oStar,nPanType)
    notxuaxiongs = Xuexi_Ziwei.simugong_notxuaxiong3_strs(oStar,nPanType)
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.simugong_xuaxiong3_strs(oStar,nPanType)
    a = []
    @@Simugong.each do |dizhi|
      gong = oStar.g_House_GetHouseId(nPanType,dizhi)
      a.push(Xuexi_Ziwei.jieguo_simugong_pan_str(gong))
    end
    return a
  end
  def Xuexi_Ziwei.jieguo_simugong_pan_str(gong)
    q1 = Xuexi_Ziwei.gong_str_with_gong(gong)
    return Pm.t("xuexi_ziwei.gong.simugong.question.pan", :q1 => q1)
  end
  def Xuexi_Ziwei.simugong_notxuaxiong3_strs(oStar,nPanType)
    a = []
    dizhis = (1..12).to_a - @@Simugong
    dizhis.each do |dizhi|
      gong = oStar.g_House_GetHouseId(nPanType,dizhi)
      a.push(Xuexi_Ziwei.jieguo_simugong_pan_str(gong))
    end
    return a
  end

  # 四敗宮：（學習效果：了解基本定義或說明。）
  # 子、午、卯、酉四宮稱為「四敗宮」
  @@Sibaigong = [1,4,7,10]
  def Xuexi_Ziwei.sibaigong_dizhis()
    return @@Sibaigong
  end
  def Xuexi_Ziwei.jieguo_sibaigong_right_str()
    return Xuexi_Ziwei.jieguo_sibaigong_normal_str(@@Sibaigong)
  end
  def Xuexi_Ziwei.jieguo_sibaigong_normal_str(dizhis)
    q1 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[0]}")
    q2 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[1]}")
    q3 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[2]}")
    q4 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[3]}")
    return Pm.t("xuexi_ziwei.gong.sibaigong.question.normal", :q1 => q1, :q2 => q2, :q3 => q3, :q4 => q4)
  end
  # is notis
  def Xuexi_Ziwei.sibaigong_xuaxiong()
    q1 = Pm.t("xuexi_ziwei.gong.sibaigong.taitou")
    xuaxiongs = Xuexi_Ziwei.sibaigong_xuaxiong_dizhi_data()
    notxuaxiongs = Xuexi_Ziwei.sibaigong_notxuaxiong_dizhi_data()
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.sibaigong_xuaxiong_dizhi_data()
    dizhis = @@Sibaigong
    a = []
    dizhis.each do |dizhi|
      a.push(Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi}"))
    end
    return a
  end
  def Xuexi_Ziwei.sibaigong_notxuaxiong_dizhi_data()
    dizhis = (1..12).to_a - @@Sibaigong
    a = []
    dizhis.each do |dizhi|
      a.push(Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi}"))
    end
    return a
  end
  # right wrong
  def Xuexi_Ziwei.sibaigong_xuaxiong2(oStar,nPanType)
    q1 = Pm.t("xuexi_ziwei.gong.sibaigong.taitou")
    xuaxiongs = Xuexi_Ziwei.sibaigong_xuaxiong2_strs(oStar,nPanType)
    notxuaxiongs = Xuexi_Ziwei.sibaigong_notxuaxiong2_strs(oStar,nPanType)
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.sibaigong_xuaxiong2_strs(oStar,nPanType)
    a = []
    a.push(Xuexi_Ziwei.jieguo_sibaigong_right_str())

    return a
  end
  def Xuexi_Ziwei.sibaigong_notxuaxiong2_strs(oStar,nPanType)
    dizhiss = Xuexi_Ziwei.rand_not_sibaigong_dizhis(10)
    a = Xuexi_Ziwei.jieguo_sibaigong_normal_strs(dizhiss,oStar,nPanType)
    return a
  end
  def Xuexi_Ziwei.jieguo_sibaigong_normal_strs(dizhiss,oStar,nPanType)
    a = []
    dizhiss.each do |dizhis|
      a.push(Xuexi_Ziwei.jieguo_sibaigong_normal_str(dizhis))
    end
    return a
  end
  def Xuexi_Ziwei.rand_not_sibaigong_dizhis(n)
    a = []
    while (a.length < n)
      dizhis = Xuexi_Ziwei.rand_dizhis(4)
      if (!a.include?(dizhis) && dizhis.sort != @@Sibaigong) then
        a.push(dizhis.sort)
      end
    end
    return a
  end
  def Xuexi_Ziwei.sibaigong_xuaxiong3(oStar,nPanType)
    q1 = Pm.t("xuexi_ziwei.gong.sibaigong.taitou")
    xuaxiongs = Xuexi_Ziwei.sibaigong_xuaxiong3_strs(oStar,nPanType)
    notxuaxiongs = Xuexi_Ziwei.sibaigong_notxuaxiong3_strs(oStar,nPanType)
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.sibaigong_xuaxiong3_strs(oStar,nPanType)
    a = []
    @@Sibaigong.each do |dizhi|
      gong = oStar.g_House_GetHouseId(nPanType,dizhi)
      a.push(Xuexi_Ziwei.jieguo_sibaigong_pan_str(gong))
    end
    return a
  end
  def Xuexi_Ziwei.jieguo_sibaigong_pan_str(gong)
    q1 = Xuexi_Ziwei.gong_str_with_gong(gong)
    return Pm.t("xuexi_ziwei.gong.sibaigong.question.pan", :q1 => q1)
  end
  def Xuexi_Ziwei.sibaigong_notxuaxiong3_strs(oStar,nPanType)
    a = []
    dizhis = (1..12).to_a - @@Sibaigong
    dizhis.each do |dizhi|
      gong = oStar.g_House_GetHouseId(nPanType,dizhi)
      a.push(Xuexi_Ziwei.jieguo_sibaigong_pan_str(gong))
    end
    return a
  end

  # 天羅地網
  # 辰、戍
  @@TianLuoDiWang = [5,11]
  def Xuexi_Ziwei.tianluodiwang_dizhis()
    return @@TianLuoDiWang
  end
  def Xuexi_Ziwei.jieguo_tianluodiwang_right_str()
    return Xuexi_Ziwei.jieguo_tianluodiwang_normal_str(@@TianLuoDiWang)
  end
  def Xuexi_Ziwei.jieguo_tianluodiwang_normal_str(dizhis)
    q1 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[0]}")
    q2 = Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhis[1]}")
    return Pm.t("xuexi_ziwei.gong.tianluodiwang.question.normal", :q1 => q1, :q2 => q2)
  end
  # is
  def Xuexi_Ziwei.tianluodiwang_xuaxiong()
    q1 = Pm.t("xuexi_ziwei.gong.tianluodiwang.taitou")
    xuaxiongs = Xuexi_Ziwei.tianluodiwang_xuaxiong_dizhi_data()
    notxuaxiongs = Xuexi_Ziwei.tianluodiwang_notxuaxiong_dizhi_data()
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.tianluodiwang_xuaxiong_dizhi_data()
    dizhis = @@TianLuoDiWang
    a = []
    dizhis.each do |dizhi|
      a.push(Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi}"))
    end
    return a
  end
  def Xuexi_Ziwei.tianluodiwang_notxuaxiong_dizhi_data()
    dizhis = (1..12).to_a - @@TianLuoDiWang
    a = []
    dizhis.each do |dizhi|
      a.push(Pm.t("xuexi_ziwei.dizhi.dizhi_#{dizhi}"))
    end
    return a
  end
  # right
  def Xuexi_Ziwei.tianluodiwang_xuaxiong2(oStar,nPanType)
    q1 = Pm.t("xuexi_ziwei.gong.tianluodiwang.taitou")
    xuaxiongs = Xuexi_Ziwei.tianluodiwang_xuaxiong2_strs(oStar,nPanType)
    notxuaxiongs = Xuexi_Ziwei.tianluodiwang_notxuaxiong2_strs(oStar,nPanType)
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.tianluodiwang_xuaxiong2_strs(oStar,nPanType)
    a = []
    a.push(Xuexi_Ziwei.jieguo_tianluodiwang_right_str())

    return a
  end
  def Xuexi_Ziwei.tianluodiwang_notxuaxiong2_strs(oStar,nPanType)
    dizhiss = Xuexi_Ziwei.rand_not_tianluodiwang_dizhis(10)
    a = Xuexi_Ziwei.jieguo_tianluodiwang_normal_strs(dizhiss,oStar,nPanType)
    return a
  end
  def Xuexi_Ziwei.jieguo_tianluodiwang_normal_strs(dizhiss,oStar,nPanType)
    a = []
    dizhiss.each do |dizhis|
      a.push(Xuexi_Ziwei.jieguo_tianluodiwang_normal_str(dizhis))
    end
    return a
  end
  def Xuexi_Ziwei.rand_not_tianluodiwang_dizhis(n)
    a = []
    while (a.length < n)
      dizhis = Xuexi_Ziwei.rand_dizhis(4)
      if (!a.include?(dizhis) && dizhis.sort != @@TianLuoDiWang) then
        a.push(dizhis.sort)
      end
    end
    return a
  end
  def Xuexi_Ziwei.tianluodiwang_xuaxiong3(oStar,nPanType)
    q1 = Pm.t("xuexi_ziwei.gong.tianluodiwang.taitou")
    if (rand(2) == 1) then
      xuaxiongs = Xuexi_Ziwei.tianluodiwang_xuaxiong3_strs(oStar,nPanType)
      notxuaxiongs = Xuexi_Ziwei.tianluodiwang_notxuaxiong3_strs(oStar,nPanType)
    else
      xuaxiongs = Xuexi_Ziwei.tianluodiwang_xuaxiong3_xings_strs(oStar,nPanType)
      notxuaxiongs = Xuexi_Ziwei.tianluodiwang_notxuaxiong3_xings_strs(oStar,nPanType)
    end
    return q1,xuaxiongs,notxuaxiongs
  end
  def Xuexi_Ziwei.tianluodiwang_xuaxiong3_strs(oStar,nPanType)
    a = []
    @@TianLuoDiWang.each do |dizhi|
      gong = oStar.g_House_GetHouseId(nPanType,dizhi)
      a.push(Xuexi_Ziwei.jieguo_tianluodiwang_pan_str(gong))
    end
    return a
  end
  def Xuexi_Ziwei.jieguo_tianluodiwang_pan_str(gong)
    q1 = Xuexi_Ziwei.gong_str_with_gong(gong)
    return Pm.t("xuexi_ziwei.gong.tianluodiwang.question.pan", :q1 => q1)
  end
  def Xuexi_Ziwei.tianluodiwang_notxuaxiong3_strs(oStar,nPanType)
    a = []
    dizhis = (1..12).to_a - @@TianLuoDiWang
    dizhis.each do |dizhi|
      gong = oStar.g_House_GetHouseId(nPanType,dizhi)
      a.push(Xuexi_Ziwei.jieguo_tianluodiwang_pan_str(gong))
    end
    return a
  end
  def Xuexi_Ziwei.tianluodiwang_xuaxiong3_xings_strs(oStar,nPanType)
    a = []
    bOnlyMain = true
    bOppIfNoMain = false
    @@TianLuoDiWang.each do |dizhi|
      gong = oStar.g_House_GetHouseId(nPanType,dizhi)
      xings,bUseOpp = oStar.gHouse_GetAStars(nPanType,dizhi,bOnlyMain,bOppIfNoMain)
      a += Xuexi_Ziwei.jieguo_tianluodiwang_xings_str(xings)
    end
    return a
  end
  def Xuexi_Ziwei.jieguo_tianluodiwang_xings_str(xings)
    a = []
    xings.each do |xing|
      q1 = Xuexi_Ziwei.astar_str(xing)
      a.push(Pm.t("xuexi_ziwei.gong.tianluodiwang.question.xing", :q1 => q1))
    end
    return a
  end
  def Xuexi_Ziwei.tianluodiwang_notxuaxiong3_xings_strs(oStar,nPanType)
    a = []
    bOnlyMain = true
    bOppIfNoMain = false
    dizhis = (1..12).to_a - @@TianLuoDiWang
    dizhis.each do |dizhi|
      xings,bUseOpp = oStar.gHouse_GetAStars(nPanType,dizhi,bOnlyMain,bOppIfNoMain)
      a += Xuexi_Ziwei.jieguo_tianluodiwang_xings_str(xings)
    end
    return a
  end

  def Xuexi_Ziwei.kecheng_strs(ap_name)
    c = Xuexi_Ziwei.all_class_count(ap_name)
    i = Xuexi_Ziwei.itemcount_in_class(c)
    h = {}
    (1..i).each do |item|
      h[Pm.t("#{ap_name}.kecheng.kecheng_#{c}_#{item}")] = [c,item]
    end
    return h
  end

end
