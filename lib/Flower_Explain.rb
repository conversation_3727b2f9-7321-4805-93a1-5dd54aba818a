# coding: UTF-8

class Flower

	# 問事項目:  愛情：有對象  無對象
	# 		  婚姻：已婚  未婚
	# 		  事業：經營者  就業者
	# 		  財運：有投資  無投資
	# 		  股市：有持股  無持股
	# 		  學業：功課  考試
	Love_0 = 1 # 愛情：有對象
	Love_1 = 2 # 愛情：無對象
	Marriage_0 = 3 # 婚姻：已婚
	Marriage_1 = 4 # 婚姻：未婚
	Business_0 = 5 # 事業：經營者
	Business_1 = 6 # 事業：就業者
	Money_0 = 7 # 財運：有投資
	Money_1 = 8 # 財運：無投資
	Stock_0 = 9 # 股市：有持股
	Stock_1 = 10 # 股市：無持股
	Study_0 = 11 # 學業：功課
	Study_1 = 12 # 學業：考試

	# Love_0 愛情：有對象  
	@@Love_0_Explain_zhTW = {
		"11" => "相逢自是有緣，伊人對我很好；",
		"12" => "相逢自是有緣，彼此相互尊重；",
		"13" => "相逢自是有緣，伊人對我有點冷；",
		"21" => "起初交往甜甜蜜蜜，相親相愛，",
		"22" => "起初交往有說有笑，靦腆有趣，",
		"23" => "起初交往障礙重重，意見不合，",
		"31" => "經過一段了解後，撥雲見日，感情加深。",
		"32" => "經過一段了解後，相互了解，感情微增。",
		"33" => "經過一段了解後，尚有疑慮，感情降溫。",
		"41" => "最後，您們的戀愛會成功。",
		"42" => "最後，您們的戀愛尚須加強。",
		"43" => "最後，您們的戀愛會因故而失敗。"
	}
	@@Love_0_Explain_zhCN = {
		"11" => "相逢自是有缘，伊人对我很好；",
		"12" => "相逢自是有缘，彼此相互尊重；",
		"13" => "相逢自是有缘，伊人对我有点冷；",
		"21" => "起初交往甜甜蜜蜜，相亲相爱，",
		"22" => "起初交往有说有笑，腼腆有趣，",
		"23" => "起初交往障碍重重，意见不合，",
		"31" => "经过一段了解后，拨云见日，感情加深。",
		"32" => "经过一段了解后，相互了解，感情微增。",
		"33" => "经过一段了解后，尚有疑虑，感情降温。",
		"41" => "最后，您们的恋爱会成功。",
		"42" => "最后，您们的恋爱尚须加强。",
		"43" => "最后，您们的恋爱会因故而失败。"
	}
	# Love_1 愛情：無對象
	@@Love_1_Explain_zhTW = {
		"11" => "您的人際關係頗佳，但相識滿天下，知音有幾人？",
		"12" => "抱著有緣無緣這一切隨緣，凡事皆順其自然的態度。",
		"13" => "因本身不善交際，緣份總是猜擦身而過。",
		"21" => "曾經有過交往，感覺也算不錯。",
		"22" => "認識是有，不過只是因事連絡不算戀愛。",
		"23" => "沒有交往，也沒有戀愛的感覺。",
		"31" => "戀愛對象近期會明朗化。",
		"32" => "戀愛對象近期仍模糊中。",
		"33" => "戀愛對象近期會仍未明朗化。",
		"41" => "最後，只要鎖定對象努力聯絡密集交往，戀愛會成功。",
		"42" => "最後，仍然平平淡淡，算是朋友不算愛人。",
		"43" => "最後，因緣份未到，急也沒用，只有再等。"
	}
	@@Love_1_Explain_zhCN = {
		"11" => "您的人际关系颇佳，但相识满天下，知音有几人？",
		"12" => "抱著有缘无缘这一切随缘，凡事皆顺其自然的态度。",
		"13" => "因本身不善交际，缘份总是猜擦身而过。",
		"21" => "曾经有过交往，感觉也算不错。",
		"22" => "认识是有，不过只是因事连络不算恋爱。",
		"23" => "没有交往，也没有恋爱的感觉。",
		"31" => "恋爱对象近期会明朗化。",
		"32" => "恋爱对象近期仍模糊中。",
		"33" => "恋爱对象近期会仍未明朗化。",
		"41" => "最后，只要锁定对象努力联络密集交往，恋爱会成功。",
		"42" => "最后，仍然平平淡淡，算是朋友不算爱人。",
		"43" => "最后，因缘份未到，急也没用，只有再等。"
	}
  def Flower.find_Explain_Love_0(lang)
    return @@Love_0_Explain_zhTW if lang == "zh-TW"
    return @@Love_0_Explain_zhCN if lang == "zh-CN"
    return @@Love_0_Explain_zhCN
  end
  def Flower.find_Explain_Love_1(lang)
    return @@Love_1_Explain_zhTW if lang == "zh-TW"
    return @@Love_1_Explain_zhCN if lang == "zh-CN"
    return @@Love_1_Explain_zhCN
  end
  def Flower.find_Explain_Love(item,lang)
  	return Flower.find_Explain_Love_0(lang) if item == Flower::Love_0 
  	return Flower.find_Explain_Love_1(lang) if item == Flower::Love_1 
    return Flower.find_Explain_Love_0(lang)
  end

	# Marriage_0 婚姻：已婚  
	@@Marriage_0_Explain_zhTW = {
		"11" => "婚姻初期心心相印琴瑟和鳴，是天賜良緣。",
		"12" => "婚姻初期平平淡淡各自忙碌，回歸真實生活。",
		"13" => "婚姻初期意見相左態度忽冷忽熱，感情起伏不定。",
		"21" => "彼此相處愉快，家和萬事興，共同為家庭打拚。",
		"22" => "彼此相處習慣有落差，尚待溝通與協調。",
		"23" => "彼此相處各有抱怨，不良習慣顯露無遺。",
		"31" => "在感情處理上，絕對信任無怨無悔，猶如神仙美眷。",
		"32" => "在感情處理上，雖有小人從中作梗，但誤解隨即可澄清。",
		"33" => "在感情處理上，恐有第三者介入破壞糾纏不清，有待化解。",
		"41" => "最後結果，彼此抱著互敬互愛體諒的心，相互扶持，白頭偕老共渡美滿人生。",
		"42" => "最後結果 ，相互協調家庭分工各司所職，雖不滿意但可接受的婚姻。",
		"43" => "最後結果，親家變冤家，面臨解除婚約危機，應先做好心理準備，考慮過著分居或獨居的生活方式。"
	}
	@@Marriage_0_Explain_zhCN = {
		"11" => "婚姻初期心心相印琴瑟和鸣，是天赐良缘。",
		"12" => "婚姻初期平平淡淡各自忙碌，回归真实生活。",
		"13" => "婚姻初期意见相左态度忽冷忽热，感情起伏不定。",
		"21" => "彼此相处愉快，家和万事兴，共同为家庭打拚。",
		"22" => "彼此相处习惯有落差，尚待沟通与协调。",
		"23" => "彼此相处各有抱怨，不良习惯显露无遗。",
		"31" => "在感情处理上，绝对信任无怨无悔，犹如神仙美眷。",
		"32" => "在感情处理上，虽有小人从中作梗，但误解随即可澄清。",
		"33" => "在感情处理上，恐有第三者介入破坏纠缠不清，有待化解。",
		"41" => "最后结果，彼此抱著互敬互爱体谅的心，相互扶持，白头偕老共渡美满人生。",
		"42" => "最后结果 ，相互协调家庭分工各司所职，虽不满意但可接受的婚姻。",
		"43" => "最后结果，亲家变冤家，面临解除婚约危机，应先做好心理准备，考虑过著分居或独居的生活方式。"
	}
	# Marriage_1 婚姻：未婚
	@@Marriage_1_Explain_zhTW = {
		"11" => "起初雙方有意愛，情投意合相互溝通，彼此甜蜜交往。",
		"12" => "起初雙方觀念、性格有落差，心意不定，暫以朋友方式交往。",
		"13" => "起初雙方印象不好，聚少離多緣份淺，口舌是非不斷。",
		"21" => "交往過程中，建立默契蹦出愛情火花，感情直線上升。",
		"22" => "交往過程中，意見相左感情起伏不 定，反覆無常似有若無。",
		"23" => "交往過程中，因意見衝突或懷疑感情不專一，而爭吵失和。",
		"31" => "後來經過親友介入兩人世界，良性地推波助瀾，使交往情況堅定不移歷久彌新。",
		"32" => "後來經過親友介入兩人世界而恢復平靜，交往情況暫沒有如膠似漆的感覺。",
		"33" => "後來經過親友介入兩人世界，無意中地破壞或質疑，使交往情況搖搖欲墜。",
		"41" => "若論及婚嫁之事，秉持相互尊重與體諒心態，誠心誠意要共渡人生，最後有情人終成眷屬，圓滿成婚結成夫妻。",
		"42" => "若論及婚嫁之事，須各自反省遷就對方，或相互退讓與信任，或職業固定後，才有可能美夢成真結成夫妻，否則事有變卦婚姻難成。",
		"43" => "若論及婚嫁之事，會借故吵鬧或因性格不合而反目成仇，或受長輩親友阻攔，有緣無份感情破裂，只有另覓對象一途。"
	}
	@@Marriage_1_Explain_zhCN = {
		"11" => "起初双方有意爱，情投意合相互沟通，彼此甜蜜交往。",
		"12" => "起初双方观念、性格有落差，心意不定，暂以朋友方式交往。",
		"13" => "起初双方印象不好，聚少离多缘份浅，口舌是非不断。",
		"21" => "交往过程中，建立默契蹦出爱情火花，感情直线上升。",
		"22" => "交往过程中，意见相左感情起伏不 定，反复无常似有若无。",
		"23" => "交往过程中，因意见冲突或怀疑感情不专一，而争吵失和。",
		"31" => "后来经过亲友介入两人世界，良性地推波助澜，使交往情况坚定不移历久弥新。",
		"32" => "后来经过亲友介入两人世界而恢复平静，交往情况暂没有如胶似漆的感觉。",
		"33" => "后来经过亲友介入两人世界，无意中地破坏或质疑，使交往情况摇摇欲坠。",
		"41" => "若论及婚嫁之事，秉持相互尊重与体谅心态，诚心诚意要共渡人生，最后有情人终成眷属，圆满成婚结成夫妻。",
		"42" => "若论及婚嫁之事，须各自反省迁就对方，或相互退让与信任，或职业固定后，才有可能美梦成真结成夫妻，否则事有变卦婚姻难成。",
		"43" => "若论及婚嫁之事，会借故吵闹或因性格不合而反目成仇，或受长辈亲友阻拦，有缘无份感情破裂，只有另觅对象一途。"
	}
  def Flower.find_Explain_Marriage_0(lang)
    return @@Marriage_0_Explain_zhTW if lang == "zh-TW"
    return @@Marriage_0_Explain_zhCN if lang == "zh-CN"
    return @@Marriage_0_Explain_zhCN
  end
  def Flower.find_Explain_Marriage_1(lang)
    return @@Marriage_1_Explain_zhTW if lang == "zh-TW"
    return @@Marriage_1_Explain_zhCN if lang == "zh-CN"
    return @@Marriage_1_Explain_zhCN
  end
  def Flower.find_Explain_Marriage(item,lang)
  	return Flower.find_Explain_Marriage_0(lang) if item == Flower::Marriage_0 
  	return Flower.find_Explain_Marriage_1(lang) if item == Flower::Marriage_1 
    return Flower.find_Explain_Marriage_0(lang)
  end

	# Business_0 事業：經營者
	# G_B	Explain
	@@Business_0_Explain_zhTW = {
		"11" => "事業初期，前景一片看好，營運一炮而紅，喜上眉稍。",
		"12" => "事業初期，遠景擬似不錯，營運平淡，收支略為平衡。",
		"13" => "事業初期，前景搖擺不定，慘淡經營，支出大於收入，處於赤字狀況。",
		"21" => "內部管理，漸上軌道，人事和諧，同心協力，共創前程。",
		"22" => "內部管理，有好有壞，人事稍有缺失，有待協調。",
		"23" => "內部管理，雜亂無章，人事一踏糊塗，應整頓一下。",
		"31" => "事業發展，蒸蒸日上，蓬勃發展，如日中天。",
		"32" => "事業發展，漸有起色，惟進度稍嫌慢，有待加強。",
		"33" => "事業發展，落沒寡歡，憂慮煩腦，點滴在心頭。",
		"41" => "最後結果，擴大經營，鴻圖大展，再創事業顛峰。",
		"42" => "最後結果，保守經營，枕戈待旦，待機而動，營運平平。",
		"43" => "最後結果，結束經營，轉換行業，或暫時休業。"
	}
	@@Business_0_Explain_zhCN = {
		"11" => "事业初期，前景一片看好，营运一炮而红，喜上眉稍。",
		"12" => "事业初期，远景拟似不错，营运平淡，收支略为平衡。",
		"13" => "事业初期，前景摇摆不定，惨淡经营，支出大于收入，处于赤字状况。",
		"21" => "内部管理，渐上轨道，人事和谐，同心协力，共创前程。",
		"22" => "内部管理，有好有坏，人事稍有缺失，有待协调。",
		"23" => "内部管理，杂乱无章，人事一踏糊涂，应整顿一下。",
		"31" => "事业发展，蒸蒸日上，蓬勃发展，如日中天。",
		"32" => "事业发展，渐有起色，惟进度稍嫌慢，有待加强。",
		"33" => "事业发展，落没寡欢，忧虑烦脑，点滴在心头。",
		"41" => "最后结果，扩大经营，鸿图大展，再创事业颠峰。",
		"42" => "最后结果，保守经营，枕戈待旦，待机而动，营运平平。",
		"43" => "最后结果，结束经营，转换行业，或暂时休业。"
	}
	# Business_1 事業：就業者
	# G_B	Explain
	@@Business_1_Explain_zhTW = {
		"11" => "工作初期，極獲重視，發揮專長，適得其所。",
		"12" => "工作初期，尚待認同，表現平平，立於儲備狀態。",
		"13" => "工作初期，有待考驗，沒有表現，搖擺不定。",
		"21" => "工作環境，非常喜歡，可以安居樂業。",
		"22" => "工作環境，還可接受，習慣就好。",
		"23" => "工作環境，不太滿意，有點不適應。",
		"31" => "同事相處，配合無間，順順利利，歡歡喜喜。",
		"32" => "同事相處，平平淡淡，中規中矩，照章處理。",
		"33" => "同事相處，有所抱怨，略有突衡，忍耐再忍耐。",
		"41" => "最後結果，升職發財，恭喜恭喜，祝您步步高陞。",
		"42" => "最後結果，依然故我，升遷緩慢，安於現狀。",
		"43" => "最後結果，準備跳槽，更換工作，祝您好運。"
	}
	@@Business_1_Explain_zhCN = {
		"11" => "工作初期，极获重视，发挥专长，适得其所。",
		"12" => "工作初期，尚待认同，表现平平，立于储备状态。",
		"13" => "工作初期，有待考验，没有表现，摇摆不定。",
		"21" => "工作环境，非常喜欢，可以安居乐业。",
		"22" => "工作环境，还可接受，习惯就好。",
		"23" => "工作环境，不太满意，有点不适应。",
		"31" => "同事相处，配合无间，顺顺利利，欢欢喜喜。",
		"32" => "同事相处，平平淡淡，中规中矩，照章处理。",
		"33" => "同事相处，有所抱怨，略有突衡，忍耐再忍耐。",
		"41" => "最后结果，升职发财，恭喜恭喜，祝您步步高升。",
		"42" => "最后结果，依然故我，升迁缓慢，安于现状。",
		"43" => "最后结果，准备跳槽，更换工作，祝您好运。"
	}
  def Flower.find_Explain_Business_0(lang)
    return @@Business_0_Explain_zhTW if lang == "zh-TW"
    return @@Business_0_Explain_zhCN if lang == "zh-CN"
    return @@Business_0_Explain_zhCN
  end
  def Flower.find_Explain_Business_1(lang)
    return @@Business_1_Explain_zhTW if lang == "zh-TW"
    return @@Business_1_Explain_zhCN if lang == "zh-CN"
    return @@Business_1_Explain_zhCN
  end
  def Flower.find_Explain_Business(item,lang)
  	return Flower.find_Explain_Business_0(lang) if item == Flower::Business_0 
  	return Flower.find_Explain_Business_1(lang) if item == Flower::Business_1 
    return Flower.find_Explain_Business_0(lang)
  end

	# Money_0 = 0 # 財運：有投資
	@@Money_0_Explain_zhTW = {
		"11" => "投資初期，利潤可觀，暗爽在心內。",
		"12" => "投資初期，有賺有賠，觀望狀態。",
		"13" => "投資初期，覺無利潤，至少賠掉利息。",
		"21" => "投資中期，累積盈餘，錢滾錢。",
		"22" => "投資中期，尚無起色，繼續觀望。",
		"23" => "投資中期，開始虧損，力圖振作。",
		"31" => "投資末期，擴大投資，利潤豐厚。",
		"32" => "投資末期，財來財去，紙上富貴。",
		"33" => "投資末期，嚴重虧損，週轉困難。",
		"41" => "最後結果，奇蹟式發大財，累積財富。",
		"42" => "最後結果，回歸原點，只賺利息或只賠利息。",
		"43" => "最後結果，膨漲過速反而虧損累累，債台高築。"
	}
	@@Money_0_Explain_zhCN = {
		"11" => "投资初期，利润可观，暗爽在心内。",
		"12" => "投资初期，有赚有赔，观望状态。",
		"13" => "投资初期，觉无利润，至少赔掉利息。",
		"21" => "投资中期，累积盈余，钱滚钱。",
		"22" => "投资中期，尚无起色，继续观望。",
		"23" => "投资中期，开始亏损，力图振作。",
		"31" => "投资末期，扩大投资，利润丰厚。",
		"32" => "投资末期，财来财去，纸上富贵。",
		"33" => "投资末期，严重亏损，周转困难。",
		"41" => "最后结果，奇迹式发大财，累积财富。",
		"42" => "最后结果，回归原点，只赚利息或只赔利息。",
		"43" => "最后结果，膨涨过速反而亏损累累，债台高筑。"
	}
	# Money_1 = 1 # 財運：無投資
	@@Money_1_Explain_zhTW = {
		"11" => "財運初期，好運連連，頻頻得獎。",
		"12" => "財運初期，平平淡淡，沒有得獎。",
		"13" => "財運初期，常常破財，消災解運。",
		"21" => "財運中期，時來運轉，財源滾滾。",
		"22" => "財運中期，時有時無，沒有額外財源。",
		"23" => "財運中期，頻頻繳款，財源短缺。",
		"31" => "財運末期，一路發跡，金玉滿堂。",
		"32" => "財運末期，趨向平穩，維持現狀。",
		"33" => "財運末期，轉變成經濟困難，舉債度日。",
		"41" => "最後結果，財源廣進，恭喜發大財。",
		"42" => "最後結果，有進有出，收支大約平衡。",
		"43" => "最後結果，判斷錯誤急轉直下，演變債台高築，應提早防範。"
	}
	@@Money_1_Explain_zhCN = {
		"11" => "财运初期，好运连连，频频得奖。",
		"12" => "财运初期，平平淡淡，没有得奖。",
		"13" => "财运初期，常常破财，消灾解运。",
		"21" => "财运中期，时来运转，财源滚滚。",
		"22" => "财运中期，时有时无，没有额外财源。",
		"23" => "财运中期，频频缴款，财源短缺。",
		"31" => "财运末期，一路发迹，金玉满堂。",
		"32" => "财运末期，趋向平稳，维持现状。",
		"33" => "财运末期，转变成经济困难，举债度日。",
		"41" => "最后结果，财源广进，恭喜发大财。",
		"42" => "最后结果，有进有出，收支大约平衡。",
		"43" => "最后结果，判断错误急转直下，演变债台高筑，应提早防范。"
	}
  def Flower.find_Explain_Money_0(lang)
    return @@Money_0_Explain_zhTW if lang == "zh-TW"
    return @@Money_0_Explain_zhCN if lang == "zh-CN"
    return @@Money_0_Explain_zhCN
  end
  def Flower.find_Explain_Money_1(lang)
    return @@Money_1_Explain_zhTW if lang == "zh-TW"
    return @@Money_1_Explain_zhCN if lang == "zh-CN"
    return @@Money_1_Explain_zhCN
  end
  def Flower.find_Explain_Money(item,lang)
  	return Flower.find_Explain_Money_0(lang) if item == Flower::Money_0 
  	return Flower.find_Explain_Money_1(lang) if item == Flower::Money_1 
    return Flower.find_Explain_Money_0(lang)
  end

	# Stock_0 = 0 # 股市：有持股
	@@Stock_0_Explain_zhTW = {
		"11" => "持股初期，處於穫利狀態，抱牢勿放。",
		"12" => "持股初期，處於下單價上下狀態，觀望之中。",
		"13" => "持股初期，處於虧損狀態，考慮停損。",
		"21" => "持股中期，正在穫利中，繼續持有。",
		"22" => "持股中期，仍然處於下單價上下狀態，繼續觀望。",
		"23" => "持股中期，會有虧損狀況產生，可以停損。",
		"31" => "持股末期，已在穫利中，可以穫利了解。",
		"32" => "持股末期，處於下單價上下浮動狀態，等待再等待。",
		"33" => "持股末期，已在虧損狀況，猶豫不決。",
		"41" => "最後結果，順利出脫持股穫利，恭喜發財。",
		"42" => "最後結果，賺賠有限，再接再勵。",
		"43" => "最後結果，虧損出脫持股，記起教訓。"
	}
	@@Stock_0_Explain_zhCN = {
		"11" => "持股初期，处于获利状态，抱牢勿放。",
		"12" => "持股初期，处于下单价上下状态，观望之中。",
		"13" => "持股初期，处于亏损状态，考虑停损。",
		"21" => "持股中期，正在获利中，继续持有。",
		"22" => "持股中期，仍然处于下单价上下状态，继续观望。",
		"23" => "持股中期，会有亏损状况产生，可以停损。",
		"31" => "持股末期，已在获利中，可以获利了解。",
		"32" => "持股末期，处于下单价上下浮动状态，等待再等待。",
		"33" => "持股末期，已在亏损状况，犹豫不决。",
		"41" => "最后结果，顺利出脱持股获利，恭喜发财。",
		"42" => "最后结果，赚赔有限，再接再励。",
		"43" => "最后结果，亏损出脱持股，记起教训。"
	}
	# Stock_1 = 1 # 股市：無持股
	@@Stock_1_Explain_zhTW = {
		"11" => "下單初期，可在有利狀態投入，立即穫利。",
		"12" => "下單初期，會在有震盪狀態投入，賺賠有限。",
		"13" => "下單初期，會在不佳狀態投入，立即虧損。",
		"21" => "持股中期，正在穫利中，繼續持有。",
		"22" => "持股中期，仍然處於下單價上下狀態，繼續觀望。",
		"23" => "持股中期，會有虧損狀況產生，可以停損。",
		"31" => "持股末期，已在穫利中，可以穫利了解。",
		"32" => "持股末期，處於下單價上下浮動狀態，等待再等待。",
		"33" => "持股末期，已在虧損狀況，猶豫不決。",
		"41" => "最後結果，順利出脫持股穫利，恭喜發財。",
		"42" => "最後結果，賺賠有限，再接再勵。",
		"43" => "最後結果，虧損出脫持股，記起教訓。"
	}
	@@Stock_1_Explain_zhCN = {
		"11" => "下单初期，可在有利状态投入，立即获利。",
		"12" => "下单初期，会在有震荡状态投入，赚赔有限。",
		"13" => "下单初期，会在不佳状态投入，立即亏损。",
		"21" => "持股中期，正在获利中，继续持有。",
		"22" => "持股中期，仍然处于下单价上下状态，继续观望。",
		"23" => "持股中期，会有亏损状况产生，可以停损。",
		"31" => "持股末期，已在获利中，可以获利了解。",
		"32" => "持股末期，处于下单价上下浮动状态，等待再等待。",
		"33" => "持股末期，已在亏损状况，犹豫不决。",
		"41" => "最后结果，顺利出脱持股获利，恭喜发财。",
		"42" => "最后结果，赚赔有限，再接再励。",
		"43" => "最后结果，亏损出脱持股，记起教训。"
	}
  def Flower.find_Explain_Stock_0(lang)
    return @@Stock_0_Explain_zhTW if lang == "zh-TW"
    return @@Stock_0_Explain_zhCN if lang == "zh-CN"
    return @@Stock_0_Explain_zhCN
  end
  def Flower.find_Explain_Stock_1(lang)
    return @@Stock_1_Explain_zhTW if lang == "zh-TW"
    return @@Stock_1_Explain_zhCN if lang == "zh-CN"
    return @@Stock_1_Explain_zhCN
  end
  def Flower.find_Explain_Stock(item,lang)
  	return Flower.find_Explain_Stock_0(lang) if item == Flower::Stock_0 
  	return Flower.find_Explain_Stock_1(lang) if item == Flower::Stock_1 
    return Flower.find_Explain_Stock_0(lang)
  end

	# Study_0 = 0 # 學業：功課
	@@Study_0_Explain_zhTW = {
		"11" => "學習基礎紮實，程度不錯，良師益友扶持，功課有進展。",
		"12" => "學習基礎待加強，程度平平，良師益友扶持有限，功課進展慢。",
		"13" => "學習基礎鬆動，程度趕不上，少有良師益友扶持，功課進展落後。",
		"21" => "學習前段過程，事先有準備，課堂上可全部吸收。",
		"22" => "學習前段過程，事先稍有準備，課堂上可大部分吸收。",
		"23" => "學習前段過程，事先無準備，課堂上可少部分吸收，請專心一點。",
		"31" => "學習後段過程，課後有複習，可尋求良師益友討論解惑，功課突飛猛進。",
		"32" => "學習後段過程，課後略有複習，尚可尋求良師益友討論解惑，功課有進步。",
		"33" => "學習後段過程，課後少有複習，難尋求良師益友討論解惑，功課略有退步。",
		"41" => "最後結果，成績名列前茅，值得鼓勵，好好唸書，會出人頭地。",
		"42" => "最後結果，成績名列中段，加油加油！好好唸書，再接再勵，下次會更好。",
		"43" => "最後結果，成績名列後段，反省反省！改善唸書態度與方式，不灰心會有轉變的啦！"
	}
	@@Study_0_Explain_zhCN = {
		"11" => "学习基础扎实，程度不错，良师益友扶持，功课有进展。",
		"12" => "学习基础待加强，程度平平，良师益友扶持有限，功课进展慢。",
		"13" => "学习基础松动，程度赶不上，少有良师益友扶持，功课进展落后。",
		"21" => "学习前段过程，事先有准备，课堂上可全部吸收。",
		"22" => "学习前段过程，事先稍有准备，课堂上可大部分吸收。",
		"23" => "学习前段过程，事先无准备，课堂上可少部分吸收，请专心一点。",
		"31" => "学习后段过程，课后有复习，可寻求良师益友讨论解惑，功课突飞猛进。",
		"32" => "学习后段过程，课后略有复习，尚可寻求良师益友讨论解惑，功课有进步。",
		"33" => "学习后段过程，课后少有复习，难寻求良师益友讨论解惑，功课略有退步。",
		"41" => "最后结果，成绩名列前茅，值得鼓励，好好念书，会出人头地。",
		"42" => "最后结果，成绩名列中段，加油加油！好好念书，再接再励，下次会更好。",
		"43" => "最后结果，成绩名列后段，反省反省！改善念书态度与方式，不灰心会有转变的啦！"
	}
	# Study_1 = 1 # 學業：考試
	@@Study_1_Explain_zhTW = {
		"11" => "您有應付考試的程度與實力，應是信心滿滿，勇往直前不畏懼。",
		"12" => "您在應付考試的程度與實力尚需加強，只要有信心，應可參加考試。",
		"13" => "您在應付考試的程度與實力沒有把握，只要有信心，考慮參加考試也無妨。",
		"21" => "準備階段，資料齊全並有良師益友扶持，實力增添不少。",
		"22" => "準備階段，資料齊全良師益友扶持有限，實力略為增加。",
		"23" => "準備階段，資料不全少有良師益友扶持，實力原地大踏步。",
		"31" => "考試時候異常順利，考題是您有準備部份，只要不粗心，會有好成績。",
		"32" => "考試時候略有不順，考題大多是您有準備部份，只要不粗心，成績應可接受。",
		"33" => "考試時候略因故而有不順，考題有些不會，成績只有聽天由命。",
		"41" => "考試結果，理想成績金榜題名，可喜可賀。",
		"42" => "考試結果，成績尚可，是否金榜題名？是依錄取名額而定。",
		"43" => "考試結果，成績不好，名落孫山？勝敗乃兵家常事，履敗履戰是不二法門。"
	}
	@@Study_1_Explain_zhCN = {
		"11" => "您有应付考试的程度与实力，应是信心满满，勇往直前不畏惧。",
		"12" => "您在应付考试的程度与实力尚需加强，只要有信心，应可参加考试。",
		"13" => "您在应付考试的程度与实力没有把握，只要有信心，考虑参加考试也无妨。",
		"21" => "准备阶段，资料齐全并有良师益友扶持，实力增添不少。",
		"22" => "准备阶段，资料齐全良师益友扶持有限，实力略为增加。",
		"23" => "准备阶段，资料不全少有良师益友扶持，实力原地大踏步。",
		"31" => "考试时候异常顺利，考题是您有准备部份，只要不粗心，会有好成绩。",
		"32" => "考试时候略有不顺，考题大多是您有准备部份，只要不粗心，成绩应可接受。",
		"33" => "考试时候略因故而有不顺，考题有些不会，成绩只有听天由命。",
		"41" => "考试结果，理想成绩金榜题名，可喜可贺。",
		"42" => "考试结果，成绩尚可，是否金榜题名？是依录取名额而定。",
		"43" => "考试结果，成绩不好，名落孙山？胜败乃兵家常事，履败履战是不二法门。"
	}
  def Flower.find_Explain_Study_0(lang)
    return @@Study_0_Explain_zhTW if lang == "zh-TW"
    return @@Study_0_Explain_zhCN if lang == "zh-CN"
    return @@Study_0_Explain_zhCN
  end
  def Flower.find_Explain_Study_1(lang)
    return @@Study_1_Explain_zhTW if lang == "zh-TW"
    return @@Study_1_Explain_zhCN if lang == "zh-CN"
    return @@Study_1_Explain_zhCN
  end
  def Flower.find_Explain_Study(item,lang)
  	return Flower.find_Explain_Study_0(lang) if item == Flower::Study_0 
  	return Flower.find_Explain_Study_1(lang) if item == Flower::Study_1 
    return Flower.find_Explain_Study_0(lang)
  end

	@@Explain_empty = {
		"11" => "",
		"12" => "",
		"13" => "",
		"21" => "",
		"22" => "",
		"23" => "",
		"31" => "",
		"32" => "",
		"33" => "",
		"41" => "",
		"42" => "",
		"43" => ""
	}
	# Love_0 = 1 # 愛情：有對象
	# Love_1 = 2 # 愛情：無對象
	# Marriage_0 = 3 # 婚姻：已婚
	# Marriage_1 = 4 # 婚姻：未婚
	# Business_0 = 5 # 事業：經營者
	# Business_1 = 6 # 事業：就業者
	# Money_0 = 7 # 財運：有投資
	# Money_1 = 8 # 財運：無投資
	# Stock_0 = 9 # 股市：有持股
	# Stock_1 = 10 # 股市：無持股
	# Study_0 = 11 # 學業：功課
	# Study_1 = 12 # 學業：考試
  def Flower.find_Explain(ask_no,lang)
  	return Flower.find_Explain_Love_0(lang) if ask_no == Flower::Love_0 
  	return Flower.find_Explain_Love_1(lang) if ask_no == Flower::Love_1 
  	return Flower.find_Explain_Marriage_0(lang) if ask_no == Flower::Marriage_0 
  	return Flower.find_Explain_Marriage_1(lang) if ask_no == Flower::Marriage_1 
  	return Flower.find_Explain_Business_0(lang) if ask_no == Flower::Business_0 
  	return Flower.find_Explain_Business_1(lang) if ask_no == Flower::Business_1 
  	return Flower.find_Explain_Money_0(lang) if ask_no == Flower::Money_0 
  	return Flower.find_Explain_Money_1(lang) if ask_no == Flower::Money_1 
  	return Flower.find_Explain_Stock_0(lang) if ask_no == Flower::Stock_0 
  	return Flower.find_Explain_Stock_1(lang) if ask_no == Flower::Stock_1 
  	return Flower.find_Explain_Study_0(lang) if ask_no == Flower::Study_0 
  	return Flower.find_Explain_Study_1(lang) if ask_no == Flower::Study_1 

  	return @@Explain_empty 
  end
  def Flower.find_Process_Explain(hExplain,aProcess)
  	h = {}
  	aProcess.each_with_index do |n,i|
  		h["process_#{i}"] = hExplain["#{n}"]
  	end
  	return h
  end
  def Flower.find_Process_JiXiong(a_JiXiong)
  	h = {}
  	a_JiXiong.each_with_index do |n,i|
  		h["process_#{i}"] = [n,Pm.t("flower.process.p_#{n}")]
  	end
  	return h
  end

end