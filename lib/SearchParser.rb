require("Xdate.rb")

class SearchParser
  YEAR = 0
  MONTH = 1
  DAY = 2
  HOUR = 3
  SEX = 4
  NAME = 5
  SP_DEFAULT = [nil,nil,nil,nil,nil,nil]
  def SearchParser.ParseSearchData(sSearchData,par_YearDisplay)
    nWYear = nWMonth = nWDate = nWHour = nSex = sName = nil
    sSearchDataRet = sSearchData

    sData = sSearchData.to_i.to_s
    aDate = SearchParser.make_default_ret_array()
    #19720223041 yyyymmddhhs  s: sex
    if (sData.length == 11) then
      aDate,sSearchDataRet = SearchParser.parseSimpleDateTimeSexStr(sData)
    # elsif (sData == "0" && sSearchData != "0") then
    #   sName = sSearchData
    #   aDate[0][NAME] = sName
    elsif (sSearchData.length >= 1 && sSearchData.length <= 10) then
      aDate = SearchParser.parse_str(sSearchData,par_YearDisplay)
    else
      sSearchDataRet = ""
    end
    # 所有值都找名字看看，包含數字
    sName = sSearchData
    aDate1 = SearchParser.make_default_ret_array()
    aDate1[0][NAME] = sName
    aDate += aDate1

    return aDate, sSearchDataRet
  end

  # def SearchParser.parseSimpleDateTimeSexStr(sData)
  #   nWYear ,nWMonth ,nWDate ,nWHour = SearchParser.parseDateTimeStr(sData)
  #   nSex = sData[10].to_i
  #   sSearchData = sData
  #   if (!Xdate.IsWDateLegal?(nWYear ,nWMonth ,nWDate ,nWHour) || (nSex !=0 && nSex != 1)) then
  #     nWYear = nWMonth = nWDate = nWHour = nSex = nil
  #     sSearchData = ""
  #   else
  #     nWYear = [nWYear]
  #     nWMonth = [nWMonth]
  #     nWDate = [nWDate]
  #   end
  #   return [[]]
  #   return nWYear ,nWMonth ,nWDate ,nWHour ,nSex ,sSearchData
  # end
  def SearchParser.parseSimpleDateTimeSexStr(sData)
    nWYear ,nWMonth ,nWDate ,nWHour = SearchParser.parseDateTimeStr(sData)
    nSex = sData[10].to_i
    sSearchData = sData
    if (!Xdate.IsWDateLegal?(nWYear ,nWMonth ,nWDate ,nWHour) || (nSex !=0 && nSex != 1)) then
      nWYear = nWMonth = nWDate = nWHour = nSex = nil
      sSearchData = ""
    end
    return [[nWYear ,nWMonth ,nWDate,nWHour ,nSex]],sSearchData
  end


  # def SearchParser.parse1WordsStr(sSearchData)
  #   nSex = sSearchData[0].to_i
  #   # sName = nil
  #   if (!((nSex == 0) || (nSex == 1))) then
  #     nSex = nil
  #     sName = sSearchData
  #   end
  #   return nSex,sName
  # end
  # def SearchParser.parse4WordsStr(sSearchData)
  #   nWYear = sSearchData.slice(0..3).to_i
  #   nWMonth = nWDate = nil
  #   sName = nil
  #   if (!Xdate.IsYearLegal?(nWYear)) then
  #     nWYear = nil
  #     nWMonth = sSearchData.slice(0..1).to_i
  #     nWDate = sSearchData.slice(2..3).to_i
  #     if (!Xdate.IsWDateLegal?(2000,nWMonth,nWDate)) then
  #       nWMonth = nWDate = nil
  #       sName = sSearchData
  #     # else
  #     #   nWMonth = [nWMonth]
  #     #   nWDate = [nWDate]
  #     end
  #   # else
  #   #   nWYear = [nWYear]
  #   end
  #   nType = 1
  #   return nWYear,nWMonth,nWDate,sName,nType
  # end

  # def SearchParser.parseless4WordsStr(sSearchData,par_YearDisplay)
  #   if (sSearchData.length == 3) then
  #     return SearchParser.parse3WordsStr(sSearchData,par_YearDisplay)
  #   elsif (sSearchData.length == 2) then
  #     return SearchParser.parse2WordsStr(sSearchData,par_YearDisplay)
  #   end
  # end

  # def SearchParser.parse3WordsStr(sSearchData,par_YearDisplay)
  #   nWYear = Array.new
  #   if (par_YearDisplay == Cfate::PAN_DISPLAY_CHINA) then
  #     nYear = sSearchData.slice(0..2).to_i
  #     nWYear.push(nYear + 1911)
  #   else
  #     nWYear = nil
  #   end

  #   nWMonth = Array.new
  #   nWDate = Array.new
  #   m,d = SearchParser.parse3WordsStr_1Month(sSearchData)
  #   nWMonth.push(m) if (m != nil)
  #   nWDate.push(d) if (d != nil)
  #   m,d = SearchParser.parse3WordsStr_2Month(sSearchData)
  #   nWMonth.push(m) if (m != nil)
  #   nWDate.push(d) if (d != nil)

  #   sName = ""
  #   nType = 2
  #   return nWYear,nWMonth,nWDate,sName,nType
  # end

  # def SearchParser.parse3WordsStr_1Month(sSearchData)
  #   nWMonth = sSearchData[0].to_i
  #   nWDate = sSearchData.slice(1..2).to_i
  #   if (!SearchParser.isMonthLegal?(nWMonth) || !SearchParser.isDateLegal?(nWDate)) then
  #     nWMonth = nil
  #     nWDate = nil
  #   end
  #   return nWMonth,nWDate
  # end
  # def SearchParser.parse3WordsStr_2Month(sSearchData)
  #   nWMonth = sSearchData.slice(0..1).to_i
  #   nWDate = sSearchData.slice(2..2).to_i
  #   if (!SearchParser.isMonthLegal?(nWMonth) || !SearchParser.isDateLegal?(nWDate)) then
  #     nWMonth = nil
  #     nWDate = nil
  #   end
  #   return nWMonth,nWDate
  # end
  def SearchParser.isMonthLegal?(nWMonth)
    return nWMonth.between?(1,12)
  end
  def SearchParser.isDateLegal?(nWDate)
    return nWDate.between?(1,31)
  end
  def SearchParser.isHourLegal?(nHour)
    return nHour.between?(0,23)
  end

  # def SearchParser.parse2WordsStr(sSearchData,par_YearDisplay)
  #   nType = 3
  #   nWYear = Array.new
  #   nYear = sSearchData.slice(0..1).to_i
  #   if (par_YearDisplay == Cfate::PAN_DISPLAY_CHINA) then
  #     nWYear.push(nYear + 1911)
  #     nWYear.push(nYear + 2011)
  #   else
  #     nWYear.push(nYear + 1900)
  #     nWYear.push(nYear + 2000)
  #   end
  #   nWMonth = Array.new
  #   nWDate = Array.new
  #   # nWMonth.push(sSearchData.slice(0..1).to_i) if (SearchParser.isMonthLegal?(sSearchData.slice(0..1).to_i))
  #   # nWDate.push(sSearchData.slice(0..1).to_i) if (SearchParser.isDateLegal?(sSearchData.slice(0..1).to_i))
  #   nWMonth.push(sSearchData[0].to_i)
  #   nWDate.push(sSearchData[1].to_i)
  #   sName = ""
  #   if (nWMonth.length == 1 && nWDate.length == 1) then
  #     nType = 2
  #   end
  #   return nWYear,nWMonth,nWDate,sName,nType
  # end

  def SearchParser.parseDateTimeStr(sSearchData)
    nWYear = nWMonth = nWDate = nWHour = nil
    if (sSearchData.length >= 10) then
      nWYear = sSearchData.slice(0..3).to_i
      nWMonth = sSearchData.slice(4..5).to_i
      nWDate = sSearchData.slice(6..7).to_i
      nWHour = sSearchData.slice(8..9).to_i
    end
    return nWYear ,nWMonth ,nWDate ,nWHour
  end

  # new for user_list_100
  def SearchParser.make_sp_array(y=nil,m=nil,d=nil,h=nil,s=nil,n=nil)
    return [y,m,d,h,s,n]
  end
  def SearchParser.make_default_ret_array()
    return SearchParser.make_ret_array()
  end
  def SearchParser.make_ret_array(y=nil,m=nil,d=nil,h=nil,s=nil,n=nil)
    sp = SearchParser.make_sp_array(y,m,d,h,s,n)
    return [sp]
  end
  def SearchParser.parse_str_china_year(s)
    if (s == nil) then
      return SearchParser.make_default_ret_array()
    end
    aDate = Array.new
    n = s.to_i
    nWYear = n + 1911
    if (Xdate.IsYearLegal?(nWYear)) then
      aDate.push(SearchParser.make_sp_array(nWYear))
    end
    nWYear = n + 2011
    if (Xdate.IsYearLegal?(nWYear)) then
      aDate.push(SearchParser.make_sp_array(nWYear))
    end
    if (aDate.length == 0) then
      return SearchParser.make_default_ret_array()
    else
      return aDate
    end
  end
  def SearchParser.parse_str_west_year(s)
    if (s == nil) then
      return SearchParser.make_default_ret_array()
    end
    aDate = Array.new
    if (s.length == 2) then
      n = s.to_i
      nWYear = n + 1900
      if (Xdate.IsYearLegal?(nWYear)) then
        sp = SearchParser.make_sp_array(nWYear)
        aDate.push(sp)
      end
      nWYear = n + 2000
      if (Xdate.IsYearLegal?(nWYear)) then
        sp = SearchParser.make_sp_array(nWYear)
        aDate.push(sp)
      end
    end
    if (s.length == 4) then
      n = s.to_i
      nWYear = n
      if (Xdate.IsYearLegal?(nWYear)) then
        sp = SearchParser.make_sp_array(nWYear)
        aDate.push(sp)
      end
    end
    if (aDate.length == 0) then
      return SearchParser.make_default_ret_array()
    else
      return aDate
    end
  end
  def SearchParser.parse_str_china_year_month_day_hour(sy,sm,sd,sh=nil)
    aDate = Array.new
    y = SearchParser.parse_str_china_year(sy)
    if (sy != nil && y == SearchParser.make_default_ret_array()) then
      return SearchParser.make_default_ret_array()
    end
    m = SearchParser.parse_str_month(sm)
    if (sm != nil && m == SearchParser.make_default_ret_array()) then
      return SearchParser.make_default_ret_array()
    end
    d = SearchParser.parse_str_day(sd)
    if (sd != nil && d == SearchParser.make_default_ret_array()) then
      return SearchParser.make_default_ret_array()
    end
    h = SearchParser.parse_str_hour(sh)
    if (sh != nil && h == SearchParser.make_default_ret_array()) then
      return SearchParser.make_default_ret_array()
    end
    y.each_index do |i|
      h.each_index do |j|
        sp = SearchParser.make_sp_array(y[i][YEAR],m[0][MONTH],d[0][DAY],h[j][HOUR])
        aDate.push(sp)
      end
    end
    return aDate
  end
  def SearchParser.parse_str_west_year_month_day_hour(sy,sm,sd,sh=nil)
    aDate = Array.new
    y = SearchParser.parse_str_west_year(sy)
    if (sy != nil && y == SearchParser.make_default_ret_array()) then
      return SearchParser.make_default_ret_array()
    end

    m = SearchParser.parse_str_month(sm)
    if (sm != nil && m == SearchParser.make_default_ret_array()) then
      return SearchParser.make_default_ret_array()
    end
    d = SearchParser.parse_str_day(sd)
    if (sd != nil && d == SearchParser.make_default_ret_array()) then
      return SearchParser.make_default_ret_array()
    end
    h = SearchParser.parse_str_hour(sh)
    if (sh != nil && h == SearchParser.make_default_ret_array()) then
      return SearchParser.make_default_ret_array()
    end
    y.each_index do |i|
      h.each_index do |j|
        sp = SearchParser.make_sp_array(y[i][YEAR],m[0][MONTH],d[0][DAY],h[j][HOUR])
        aDate.push(sp)
      end
    end
    return aDate
  end
  def SearchParser.parse_str_month(s)
    if (s == nil) then
      return SearchParser.make_default_ret_array()
    end
    n = s.to_i
    if (SearchParser.isMonthLegal?(n)) then
      return SearchParser.make_ret_array(nil,n)
    end
    return SearchParser.make_default_ret_array()
  end
  def SearchParser.parse_str_day(s)
    if (s == nil) then
      return SearchParser.make_default_ret_array()
    end
    n = s.to_i
    if (SearchParser.isDateLegal?(n)) then
      return SearchParser.make_ret_array(nil,nil,n)
    end
    return SearchParser.make_default_ret_array()
  end
  def SearchParser.parse_str_month_day(sm,sd)
    d = SearchParser.make_default_ret_array()
    a = SearchParser.parse_str_month(sm)
    b = SearchParser.parse_str_day(sd)
    if (a == d || b == d) then
      return d
    end
    return SearchParser.make_ret_array(nil,a[0][MONTH],b[0][DAY])
  end
  def SearchParser.parse_str_hour(s)
    if (s == nil) then
      return SearchParser.make_default_ret_array()
    end
    ns = SearchParser.check_hour(s)
    aDate = SearchParser.make_default_ret_array()
    ns.each do |n|
      if (SearchParser.isHourLegal?(n)) then
        if (aDate == SearchParser.make_default_ret_array()) then
          aDate = SearchParser.make_ret_array(nil,nil,nil,n)
        else
          aDate += SearchParser.make_ret_array(nil,nil,nil,n)
        end
      end
    end
    return aDate
  end
  def SearchParser.check_hour(s)
    if (s.to_i.to_s != s) then # 非數字
      if (s.length == 1) then
        dizhis = Pm.GetStr("customer.search.dizhis")
        n = dizhis.index(s)
        if (n != nil) then
          n = n * 2
          m = n % 2 == 1 ? (n == 23 ? 0 : n + 1) : (n == 0 ? 23 : n - 1)
          return [m,n]
        end
      end
      return [-1]
    end
    return [s.to_i]
  end
  def SearchParser.parse_str_sex(s)
    nSex = s.to_i
    if ((nSex == 0) || (nSex == 1)) then
      return SearchParser.make_ret_array(nil,nil,nil,nil,nSex)
    end
    return SearchParser.make_default_ret_array()
  end
  # PAN_DISPLAY_CHINA
  def SearchParser.parse_str(sSearchData,par_YearDisplay)
    aDate = Array.new
    if (par_YearDisplay == Cfate::PAN_DISPLAY_CHINA) then
      aDate = SearchParser.parse_str_china(sSearchData)
      aDate += SearchParser.parse_str_west(sSearchData)
    else
      aDate = SearchParser.parse_str_west(sSearchData)
    end

    aDate.uniq!
    aDate.delete(SP_DEFAULT)

    return aDate
  end
  # nType:1 完整 訊息 11碼
  # nType：2 性別
  # nType：3 姓名
  # nType：4 array of hash with YEAR,MONTH,DAY content
  # aDate : array of array with YEAR index 0,MONTH index 1,DAY index 2 content like
  # [[1971,2,3],[nil,2,3],[2001,nil,nil]]
  def SearchParser.parse_str_china(sSearchData)
    aDate = SearchParser.make_default_ret_array()
    sData = sSearchData.to_i.to_s
    if (SearchParser.check_colon(sSearchData[-1,1])) then
      aDate = SearchParser.parse_str_hour(sSearchData[0,sSearchData.length - 1])
    elsif (sData == "0" && sSearchData != "0") then
      aDate = SearchParser.parse_str_hour(sSearchData)
    elsif (sSearchData.length == 1) then
      aDate = SearchParser.parse_str_china_1words(sSearchData)
    elsif (sSearchData.length == 2) then
      aDate = SearchParser.parse_str_china_2words(sSearchData)
    elsif (sSearchData.length == 3) then
      aDate = SearchParser.parse_str_china_3words(sSearchData)
    elsif (sSearchData.length == 4) then
      aDate = SearchParser.parse_str_china_4words(sSearchData)
    elsif (sSearchData.length == 5) then
      aDate = SearchParser.parse_str_china_5words(sSearchData)
    elsif (sSearchData.length == 6) then
      aDate = SearchParser.parse_str_china_6words(sSearchData)
    elsif (sSearchData.length == 7) then
      aDate = SearchParser.parse_str_china_7words(sSearchData)
    elsif (sSearchData.length == 8) then
      aDate = SearchParser.parse_str_china_8words(sSearchData)
    elsif (sSearchData.length == 9) then
      aDate = SearchParser.parse_str_china_9words(sSearchData)
    end

    return aDate
  end
  # china 1 words
  def SearchParser.parse_str_china_1words(sSearchData)
    aDate = SearchParser.parse_str_china_1words_sex(sSearchData)
    aDate += SearchParser.parse_str_china_1words_month(sSearchData)
    aDate += SearchParser.parse_str_china_1words_day(sSearchData)
    aDate += SearchParser.parse_str_china_1words_hour(sSearchData)
    return aDate
  end
  def SearchParser.parse_str_china_1words_month(sSearchData)
    return SearchParser.parse_str_month(sSearchData.slice(0..0))
  end
  def SearchParser.parse_str_china_1words_day(sSearchData)
    return SearchParser.parse_str_day(sSearchData.slice(0..0))
  end
  def SearchParser.parse_str_china_1words_sex(sSearchData)
    return SearchParser.parse_str_sex(sSearchData.slice(0..0))
  end
  def SearchParser.parse_str_china_1words_hour(sSearchData)
    return SearchParser.parse_str_hour(sSearchData.slice(0..0))
  end

  # china 2 words
  def SearchParser.parse_str_china_2words(sSearchData)
    aDate = SearchParser.parse_str_china_2words_year(sSearchData)
    aDate += SearchParser.parse_str_china_2words_month(sSearchData)
    aDate += SearchParser.parse_str_china_2words_day(sSearchData)
    aDate += SearchParser.parse_str_china_2words_month_day(sSearchData)
    aDate += SearchParser.parse_str_china_2words_hour(sSearchData)
    return aDate
  end
  def SearchParser.parse_str_china_2words_year(sSearchData)
    return SearchParser.parse_str_china_year(sSearchData.slice(0..1))
  end
  def SearchParser.parse_str_china_2words_month(sSearchData)
    return SearchParser.parse_str_month(sSearchData.slice(0..1))
  end
  def SearchParser.parse_str_china_2words_day(sSearchData)
    return SearchParser.parse_str_day(sSearchData.slice(0..1))
  end
  def SearchParser.parse_str_china_2words_month_day(sSearchData)
    sm = sSearchData.slice(0..0)
    sd = sSearchData.slice(1..1)
    return SearchParser.parse_str_month_day(sm,sd)
  end
  def SearchParser.parse_str_china_2words_hour(sSearchData)
    return SearchParser.parse_str_hour(sSearchData.slice(0..1))
  end

  # china 3 words
  def SearchParser.parse_str_china_3words(sSearchData)
    aDate = SearchParser.parse_str_china_3words_year(sSearchData)
    aDate += SearchParser.parse_str_china_3words_year_month(sSearchData)
    aDate += SearchParser.parse_str_china_3words_month_day(sSearchData)
    return aDate
  end
  def SearchParser.parse_str_china_3words_year(sSearchData)
    return SearchParser.parse_str_china_year(sSearchData.slice(0..2))
  end
  def SearchParser.parse_str_china_3words_year_month(sSearchData)
    return SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),nil)
  end
  def SearchParser.parse_str_china_3words_month_day(sSearchData)
    aDate = SearchParser.parse_str_china_year_month_day_hour(nil,sSearchData.slice(0..1),sSearchData.slice(2..2))
    aDate += SearchParser.parse_str_china_year_month_day_hour(nil,sSearchData.slice(0..0),sSearchData.slice(1..2))
    return aDate
  end

  # china 4 words
  def SearchParser.parse_str_china_4words(sSearchData)
    aDate = SearchParser.parse_str_china_4words_year_month(sSearchData)
    aDate += SearchParser.parse_str_china_4words_year_month_day(sSearchData)
    aDate += SearchParser.parse_str_china_4words_month_day(sSearchData)

    return aDate
  end
  def SearchParser.parse_str_china_4words_year_month(sSearchData)
    aDate = SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..3),nil)
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),nil)
    return aDate
  end
  def SearchParser.parse_str_china_4words_year_month_day(sSearchData)
    aDate = SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..3))
    return aDate
  end
  def SearchParser.parse_str_china_4words_month_day(sSearchData)
    aDate = SearchParser.parse_str_china_year_month_day_hour(nil,sSearchData.slice(0..1),sSearchData.slice(2..3))
    return aDate
  end

  # china 5 words
  def SearchParser.parse_str_china_5words(sSearchData)
    aDate = SearchParser.parse_str_china_5words_year_month(sSearchData)
    aDate += SearchParser.parse_str_china_5words_year_month_day(sSearchData)
    return aDate
  end
  def SearchParser.parse_str_china_5words_year_month(sSearchData)
    aDate = SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..4),nil)
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..3),sSearchData.slice(4..4))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..3),sSearchData.slice(4..4))
    return aDate
  end
  def SearchParser.parse_str_china_5words_year_month_day(sSearchData)
    aDate = SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..4))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..4))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..3),sSearchData.slice(4..4))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..3),sSearchData.slice(4..4))
    return aDate
  end

  # china 6 words
  def SearchParser.parse_str_china_6words(sSearchData)
    aDate = SearchParser.parse_str_china_6words_year_month_day(sSearchData)
    return aDate
  end
  def SearchParser.parse_str_china_6words_year_month_day(sSearchData)
    aDate = SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..5))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..4),sSearchData.slice(5..5))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..4),sSearchData.slice(5..5))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..3),sSearchData.slice(4..5))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..3),sSearchData.slice(4..5))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..4),sSearchData.slice(5..5))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..3),sSearchData.slice(4..4),sSearchData.slice(5..5))
    return aDate
  end

  # china 7 words
  def SearchParser.parse_str_china_7words(sSearchData)
    aDate = SearchParser.parse_str_china_7words_year_month_day(sSearchData)
    return aDate
  end
  def SearchParser.parse_str_china_7words_year_month_day(sSearchData)
    aDate = SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..4),sSearchData.slice(5..6))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..3),sSearchData.slice(4..5),sSearchData.slice(6..6))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..4),sSearchData.slice(5..5),sSearchData.slice(6..6))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..5),sSearchData.slice(6..6))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..4),sSearchData.slice(5..6))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..4),sSearchData.slice(5..6))
    return aDate
  end
  def SearchParser.parse_str_china_8words(sSearchData)
    return SearchParser.parse_str_china_8words_year_month_day_hour(sSearchData)
  end
  def SearchParser.parse_str_china_8words_year_month_day_hour(sSearchData)
    aDate = SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..5),sSearchData.slice(6..7))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..3),sSearchData.slice(4..5),sSearchData.slice(6..7))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..4),sSearchData.slice(5..5),sSearchData.slice(6..7))
    aDate += SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..4),sSearchData.slice(5..6),sSearchData.slice(7..7))
    return aDate
  end
  def SearchParser.parse_str_china_9words(sSearchData)
    return SearchParser.parse_str_china_9words_year_month_day_hour(sSearchData)
  end
  def SearchParser.parse_str_china_9words_year_month_day_hour(sSearchData)
    aDate = SearchParser.parse_str_china_year_month_day_hour(sSearchData.slice(0..2),sSearchData.slice(3..4),sSearchData.slice(5..6),sSearchData.slice(7..8))
    return aDate
  end
  def SearchParser.check_colon(s)
    if (s == ":") then
      return true
    end
    if (s == Pm.GetStr("IDS_S_COLON")) then
      return true
    end
    return false
  end

  # PAN_DISPLAY_WEST
  def SearchParser.parse_str_west(sSearchData)
    aDate = SearchParser.make_default_ret_array()
    sData = sSearchData.to_i.to_s
    if (SearchParser.check_colon(sSearchData[-1,1])) then
      aDate = SearchParser.parse_str_hour(sSearchData[0,sSearchData.length - 1])
    elsif (sData == "0" && sSearchData != "0") then
      # 輸入非數字
      aDate = SearchParser.parse_str_hour(sSearchData)
    elsif (sSearchData.length == 1) then
      aDate = SearchParser.parse_str_west_1words(sSearchData)
    elsif (sSearchData.length == 2) then
      aDate = SearchParser.parse_str_west_2words(sSearchData)
    elsif (sSearchData.length == 3) then
      aDate = SearchParser.parse_str_west_3words(sSearchData)
    elsif (sSearchData.length == 4) then
      aDate = SearchParser.parse_str_west_4words(sSearchData)
    elsif (sSearchData.length == 5) then
      aDate = SearchParser.parse_str_west_5words(sSearchData)
    elsif (sSearchData.length == 6) then
      aDate = SearchParser.parse_str_west_6words(sSearchData)
    elsif (sSearchData.length == 7) then
      aDate = SearchParser.parse_str_west_7words(sSearchData)
    elsif (sSearchData.length == 8) then
      aDate = SearchParser.parse_str_west_8words(sSearchData)
    elsif (sSearchData.length == 9) then
      aDate = SearchParser.parse_str_west_9words(sSearchData)
    elsif (sSearchData.length == 10) then
      aDate = SearchParser.parse_str_west_10words(sSearchData)
    end

    return aDate
  end
  # west 1 words
  def SearchParser.parse_str_west_1words(sSearchData)
    aDate = SearchParser.parse_str_west_1words_sex(sSearchData)
    aDate += SearchParser.parse_str_west_1words_month(sSearchData)
    aDate += SearchParser.parse_str_west_1words_day(sSearchData)
    aDate += SearchParser.parse_str_west_1words_hour(sSearchData)
    return aDate
  end
  def SearchParser.parse_str_west_1words_month(sSearchData)
    return SearchParser.parse_str_month(sSearchData.slice(0..0))
  end
  def SearchParser.parse_str_west_1words_day(sSearchData)
    return SearchParser.parse_str_day(sSearchData.slice(0..0))
  end
  def SearchParser.parse_str_west_1words_hour(sSearchData)
    return SearchParser.parse_str_hour(sSearchData.slice(0..0))
  end
  def SearchParser.parse_str_west_1words_sex(sSearchData)
    return SearchParser.parse_str_sex(sSearchData.slice(0..0))
  end
  # west 2 words
  def SearchParser.parse_str_west_2words(sSearchData)
    aDate = Array.new
    aDate += SearchParser.parse_str_west_2words_year(sSearchData)
    aDate += SearchParser.parse_str_west_2words_month(sSearchData)
    aDate += SearchParser.parse_str_west_2words_day(sSearchData)
    aDate += SearchParser.parse_str_west_2words_month_day(sSearchData)
    aDate += SearchParser.parse_str_west_2words_hour(sSearchData)
    return aDate
  end
  def SearchParser.parse_str_west_2words_year(sSearchData)
    return SearchParser.parse_str_west_year(sSearchData.slice(0..1))
  end
  def SearchParser.parse_str_west_2words_month(sSearchData)
    return SearchParser.parse_str_month(sSearchData.slice(0..1))
  end
  def SearchParser.parse_str_west_2words_day(sSearchData)
    return SearchParser.parse_str_day(sSearchData.slice(0..1))
  end
  def SearchParser.parse_str_west_2words_month_day(sSearchData)
    sm = sSearchData.slice(0..0)
    sd = sSearchData.slice(1..1)
    return SearchParser.parse_str_month_day(sm,sd)
  end
  def SearchParser.parse_str_west_2words_hour(sSearchData)
    return SearchParser.parse_str_hour(sSearchData.slice(0..1))
  end

  # west 3 words
  def SearchParser.parse_str_west_3words(sSearchData)
    aDate = SearchParser.parse_str_west_3words_year_month(sSearchData)
    aDate += SearchParser.parse_str_west_3words_month_day(sSearchData)
    return aDate
  end
  def SearchParser.parse_str_west_3words_year_month(sSearchData)
    return SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),nil)
  end
  def SearchParser.parse_str_west_3words_month_day(sSearchData)
    aDate = SearchParser.parse_str_west_year_month_day_hour(nil,sSearchData.slice(0..1),sSearchData.slice(2..2))
    aDate += SearchParser.parse_str_west_year_month_day_hour(nil,sSearchData.slice(0..0),sSearchData.slice(1..2))
    return aDate
  end

  # west 4 words
  def SearchParser.parse_str_west_4words(sSearchData)
    aDate = SearchParser.parse_str_west_4words_year(sSearchData)
    aDate += SearchParser.parse_str_west_4words_year_month(sSearchData)
    aDate += SearchParser.parse_str_west_4words_year_month_day(sSearchData)
    aDate += SearchParser.parse_str_west_4words_month_day(sSearchData)

    return aDate
  end
  def SearchParser.parse_str_west_4words_year(sSearchData)
    return SearchParser.parse_str_west_year(sSearchData.slice(0..3))
  end
  def SearchParser.parse_str_west_4words_year_month(sSearchData)
    return SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),nil)
  end
  def SearchParser.parse_str_west_4words_year_month_day(sSearchData)
    return SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..3))
  end
  def SearchParser.parse_str_west_4words_month_day(sSearchData)
    return SearchParser.parse_str_west_year_month_day_hour(nil,sSearchData.slice(0..1),sSearchData.slice(2..3))
  end

  # west 5 words
  def SearchParser.parse_str_west_5words(sSearchData)
    aDate = SearchParser.parse_str_west_5words_year_month(sSearchData)
    aDate += SearchParser.parse_str_west_5words_year_month_day(sSearchData)
    return aDate
  end
  def SearchParser.parse_str_west_5words_year_month(sSearchData)
    return SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..4),nil)
  end
  def SearchParser.parse_str_west_5words_year_month_day(sSearchData)
    aDate = SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..4))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..4))
    return aDate
  end

  # west 6 words
  def SearchParser.parse_str_west_6words(sSearchData)
    aDate = SearchParser.parse_str_west_6words_year_month(sSearchData)
    aDate += SearchParser.parse_str_west_6words_year_month_day(sSearchData)
    return aDate
  end
  def SearchParser.parse_str_west_6words_year_month(sSearchData)
    return SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..5),nil)
  end
  def SearchParser.parse_str_west_6words_year_month_day(sSearchData)
    aDate = SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..5))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..3),sSearchData.slice(4..5))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..4),sSearchData.slice(5..5))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..4),sSearchData.slice(5..5))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..4),sSearchData.slice(5..5))
    return aDate
  end
  # west 7 words
  def SearchParser.parse_str_west_7words(sSearchData)
    return SearchParser.parse_str_west_7words_year_month_day(sSearchData)
  end
  def SearchParser.parse_str_west_7words_year_month_day(sSearchData)
    aDate = SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..5),sSearchData.slice(6..6))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..4),sSearchData.slice(5..6))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..4),sSearchData.slice(5..5),sSearchData.slice(6..6))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..5),sSearchData.slice(6..6))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..4),sSearchData.slice(5..6))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..2),sSearchData.slice(3..4),sSearchData.slice(5..6))
    return aDate
  end
  # west 8 words
  def SearchParser.parse_str_west_8words(sSearchData)
    return SearchParser.parse_str_west_8words_year_month_day(sSearchData)
  end
  def SearchParser.parse_str_west_8words_year_month_day(sSearchData)
    aDate = SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..5),sSearchData.slice(6..7))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..5),sSearchData.slice(6..6),sSearchData.slice(7..7))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..4),sSearchData.slice(5..6),sSearchData.slice(7..7))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..4),sSearchData.slice(5..5),sSearchData.slice(6..7))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..1),sSearchData.slice(2..3),sSearchData.slice(4..5),sSearchData.slice(6..7))
    return aDate
  end
  def SearchParser.parse_str_west_9words(sSearchData)
    return SearchParser.parse_str_west_9words_year_month_day_hour(sSearchData)
  end
  def SearchParser.parse_str_west_9words_year_month_day_hour(sSearchData)
    aDate = SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..5),sSearchData.slice(6..7),sSearchData.slice(8..8))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..5),sSearchData.slice(6..6),sSearchData.slice(7..8))
    aDate += SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..4),sSearchData.slice(5..6),sSearchData.slice(7..8))
    return aDate
  end
  def SearchParser.parse_str_west_10words(sSearchData)
    return SearchParser.parse_str_west_10words_year_month_day_hour(sSearchData)
  end
  def SearchParser.parse_str_west_10words_year_month_day_hour(sSearchData)
    aDate = SearchParser.parse_str_west_year_month_day_hour(sSearchData.slice(0..3),sSearchData.slice(4..5),sSearchData.slice(6..7),sSearchData.slice(8..9))
    return aDate
  end

end
