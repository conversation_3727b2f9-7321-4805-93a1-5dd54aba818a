require("Star.rb")

class StarInfo
	# 由Star物件取得
	def StarInfo.GetDoctorName(oStar,nPanType,nEarth)
		return oStar.g_GetDoctorName(nPanType,nEarth)
	end

	def StarInfo.GetGodName(oStar,nPanType,nEarth)
		return oStar.g_GetGodName(nPanType,nEarth)
	end

	def StarInfo.GetYearGodName(oStar,nPanType,nEarth)
		return oStar.gHouse_GetYearGodName(nPanType,nEarth)
	end

	def StarInfo.GetYearStarName(oStar,nPanType,nEarth)
		return oStar.g_GetYearStarName(nPanType,nEarth)
	end

	def StarInfo.Modify2TwoWords(sIn)
		sOut = sIn
		if (sIn.length == 1) then
			sOut = sIn + StarInfo.GetSpace(1)
		end
		return sOut
	end
	
	def StarInfo.GetSpace(nCnt)
		sBuf = ""
		nCnt.times do |i|
			sBuf += Pm.GetStr("IDS_A_KEY_SPACE")
		end
		return sBuf
	end

	def StarInfo.GetAStarName(oStar,nPanType,nEarth,nMax)
		return oStar.g_GetAStarName(nPanType,nEarth,nMax)
	end

	def StarInfo.GetMioWong(oStar,nPanType,nEarth,nMax)
		return oStar.g_GetMioWong(nPanType,nEarth,nMax)
	end

	def StarInfo.GetBStarName(oStar,nPanType,nEarth,nMax)
		return oStar.g_GetBStarName(nPanType,nEarth,nMax)
	end
	
	#	74 --- 83
	def StarInfo.GetLarge(oStar,nEarth)
		return oStar.g_GetLarge(nEarth)
	end

	# "<FONT COLOR=GRAY SIZE=1>" + "5 17 29 41 53 65 77 89" + "<BR>"
	def StarInfo.GetSmall(oStar,nEarth)
		return oStar.g_GetSmall(nEarth)
	end

	# 宮名 <FONT COLOR=MIDNIGHTBLUE FACE=細明體 SIZE=2>　　　【朋友】
	def StarInfo.GetHouseName(oStar,nPanType,nEarth)
		return oStar.g_GetHouseName(nPanType,nEarth)
	end

	# 宮干支 <FONT COLOR=BLACK>丁巳
	def StarInfo.GetHouseSkyEarthName(oStar,nPanType,nEarth)
		return oStar.g_GetHouseSkyEarthName(nPanType,nEarth)
	end


	# 資料結構取得
	def StarInfo.GetDoctorName2(houseInfo,nEarth)
		houseInfo[Earth.GetKeyName(nEarth)][Star::DOCTOR][0][0]
	end

	def StarInfo.GetGodName2(houseInfo,nEarth)
		sOut = houseInfo[Earth.GetKeyName(nEarth)][Star::GOD][0][0]
		sOut = StarInfo.Modify2TwoWords(sOut)
	end

	def StarInfo.GetYearGodName2(houseInfo,nEarth)
		sOut = houseInfo[Earth.GetKeyName(nEarth)][Star::YEARGOD][0][0]
		sOut = StarInfo.Modify2TwoWords(sOut)
	end

	def StarInfo.GetYearStarName2(houseInfo,nEarth)
		sOut = houseInfo[Earth.GetKeyName(nEarth)][Star::YEARSTAR][0][0]
		sOut = StarInfo.Modify2TwoWords(sOut)
	end

	def StarInfo.Modify2TwoWords(sIn)
		sOut = sIn
		if (sIn.length == 1) then
			sOut = sIn + StarInfo.GetSpace(1)
		end
		return sOut
	end
	
	def StarInfo.GetSpace(nCnt)
		sBuf = ""
		nCnt.times do |i|
			sBuf += Pm.GetStr("IDS_A_KEY_SPACE")
		end
		return sBuf
	end

	def StarInfo.GetAStarName2(houseInfo,nEarth,nMax)
		sStar = Array.new(2,"")
		starArray = houseInfo[Earth.GetKeyName(nEarth)][Star::A_STAR]

		sOne = ""
		sTwo = ""
		starArray.each do |oStar|
			sTmp = StarInfo.Modify2TwoWords(oStar[0])
			sOne += sTmp[0]
			sTwo += sTmp[1]
		end
		sOne += StarInfo.GetSpace(nMax - starArray.length)
		sTwo += StarInfo.GetSpace(nMax - starArray.length)
		sStar[0] = sOne.reverse
		sStar[1] = sTwo.reverse
		return sStar
	end

	def StarInfo.GetBStarName2(houseInfo,nEarth,nMax)
		sStar = Array.new(2,"")
		starArray = houseInfo[Earth.GetKeyName(nEarth)][Star::B_STAR]
		sOne = ""
		sTwo = ""
		starArray.each do |oStar|
			sTmp = StarInfo.Modify2TwoWords(oStar[0])
			sOne += sTmp[0]
			sTwo += sTmp[1]
		end
		sOne += StarInfo.GetSpace(nMax - starArray.length)
		sTwo += StarInfo.GetSpace(nMax - starArray.length)
		sStar[0] = sOne.reverse
		sStar[1] = sTwo.reverse
		return sStar
	end
	
	#	74 --- 83
	def StarInfo.GetLarge2(starInfo,nEarth)
		nStarYear = starInfo[Star::U_LARGE][Earth.Earth2EarthIndex(nEarth)]
		sOut = "#{nStarYear} --- #{nStarYear+9}"
		return sOut
	end

	# "<FONT COLOR=GRAY SIZE=1>" + "5 17 29 41 53 65 77 89" + "<BR>"
	def StarInfo.GetSmall2(starInfo,nEarth)
		nStarYear = starInfo[Star::U_SMALL][Earth.Earth2EarthIndex(nEarth)]
		sOut = nStarYear.to_s
		7.times do |i|
			nStarYear += 12
			sOut += " "
			sOut += nStarYear.to_s
		end
		
		return sOut
	end

	# 宮名 <FONT COLOR=MIDNIGHTBLUE FACE=細明體 SIZE=2>　　　【朋友】
	def StarInfo.GetHouseName2(houseInfo,nEarth)
		sOut = Pm.GetStr("IDS_A_LEFT_QUOTE")
		sOut += houseInfo[Earth.GetKeyName(nEarth)][Star::HOUSE_NAME][0]
		sOut += Pm.GetStr("IDS_A_RIGHT_QUOTE")
		return sOut
	end

	# 宮干支 <FONT COLOR=BLACK>丁巳
	def StarInfo.GetHouseSkyEarthName2(houseInfo,nEarth)
		sOut = Sky.GetName(Sky.SkyIndex2Sky(houseInfo[Earth.GetKeyName(nEarth)][Star::HOUSE_SKY]))
		sOut += Earth.GetName(nEarth)
	end
		
end
