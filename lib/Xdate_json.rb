require("Xdate_Constant.rb")
require("Xdate_Variable.rb")
require("Pm.rb")
require("SkyEarthFive_Function.rb")
require("Cfate.rb")

class Xdate
  # 萬年曆
  def Xdate.MakeXdateInfo_Star(hAppData,nPanType,hApFunc,hParAll)
    @hXdate = Hash.new
    @hXdate.merge!(hAppData)

    @ShowXdate = Array.new

    i = 1
    if (Xdate.NeedXdate?(Cfate::PAN_NORMAL,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Normal(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_NORMAL)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_TENYEAR,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Tenyear(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_TENYEAR)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_FLOWYEAR,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Flowyear(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_FLOWYEAR)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_FLOWMONTH,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Flowmonth(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_FLOWMONTH)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_FLOWDATE,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Flowdate(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_FLOWDATE)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_FLOWTIME,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Flowtime(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_FLOWTIME)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_FLOWMIN,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Flowmin(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_FLOWMIN)
    end

    @hXdate["ShowXdate"] = @ShowXdate
    return @hXdate
  end

  def Xdate.AddShowXdate(nPanType)
    @ShowXdate.push(nPanType)
  end

  def Xdate.NeedXdate?(nDisPanType,nCurPanType,hApFunc)
    if (hApFunc == nil || hApFunc["showXdate"] == false) then  # 免費版
      return false
    end
    if (nDisPanType > hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      return false
    end
    # if (Cfate::PAN_NORMAL == nCurPanType) then
    #   return false
    # end
    # if (Cfate::PAN_TENYEAR == nCurPanType) then
    #   return false
    # end
    # if (Cfate::PAN_FLOWTIME == nCurPanType) then
    #   return false
    # end
    # if (Cfate::PAN_FLOWMIN == nCurPanType) then
    #   return false
    # end
    if (nDisPanType <= nCurPanType) then
      return true
    end
    if (nDisPanType == nCurPanType) then
      return true
    end
    if (nDisPanType == (nCurPanType - 1)) then
      return true
    end
    return false
  end

  def Xdate.MakeXdateInfo_Normal(hApFunc,hParAll)
    nSky = @hXdate["nSky"]
    nEarth = @hXdate["nEarth"]

    # 萬年曆
    i = 0
    if (Cfate::PAN_NORMAL <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_normal"] = Array.new(11) {Array.new(8) {Array.new(5)}}
      (1..11).each do |nRow|
          nRowIndex = nRow - 1
        (1..8).each do |nCol|
          nColIndex = nCol - 1
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][0] = @hXdate["Ui_EY"] + i
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][1] = Sky.GetName(nSky)
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][2] = Earth.GetName(nEarth)
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][3] = i + 1
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][4] = ((@hXdate["Ui_EY"] + i) == @hXdate["ddg_EY"] ? true : false)
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][5] = Xdate.YearDisplayOnlyNumber(@hXdate["Ui_EY"] + i,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY]) # 顯示年

          i += 1
          nSky = Sky.ModifySky(nSky + 1)
          nEarth = Earth.ModifyEarth(nEarth + 1)
        end
      end
    end
  end

  def Xdate.MakeXdateInfo_Tenyear(hApFunc,hParAll)
    nSky = @hXdate["nSky"]
    nEarth = @hXdate["nEarth"]
    if (Cfate::PAN_TENYEAR <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_tenyear"] = Array.new(30) {Array.new(5)}

      #取得十年大限開始歲數
      nEarthIndex = @hXdate["LargeSan"]
      nETenYearStartYearOld = @hXdate["Large"][nEarthIndex]

      # 十年大限開始年
      @hXdate["nHighLightStartEYear"] = @hXdate["Ui_EY"] + nETenYearStartYearOld - 1

      # 萬年曆十年盤起始年
      if (nETenYearStartYearOld > 10) then
        nDisStartEYear = @hXdate["nHighLightStartEYear"] - 10
      else
        nDisStartEYear = @hXdate["Ui_EY"]
      end

      # 萬年曆十年盤起始年紀
      nStartEYearOld = Xdate.GetYearOld(@hXdate["Ui_EY"],nDisStartEYear)

      # 起始干支
      nSky = Sky.ModifySky(nSky + nStartEYearOld - 1)
      nEarth = Earth.ModifyEarth(nEarth + nStartEYearOld - 1)
      (1..30).each do |nRow|
        nRowIndex = nRow - 1
        @hXdate["pan_xdate_tenyear"][nRowIndex][0] = nDisStartEYear + nRowIndex
        @hXdate["pan_xdate_tenyear"][nRowIndex][1] = Xdate.WestYearToChina(@hXdate["pan_xdate_tenyear"][nRowIndex][0])
        @hXdate["pan_xdate_tenyear"][nRowIndex][2] = Sky.GetName(nSky) + Earth.GetName(nEarth)
        @hXdate["pan_xdate_tenyear"][nRowIndex][3] = Xdate.GetAnimal(nEarth)
        @hXdate["pan_xdate_tenyear"][nRowIndex][4] = Xdate.GetYearOldStr(nStartEYearOld + nRowIndex)

        nSky = Sky.ModifySky(nSky + 1)
        nEarth = Earth.ModifyEarth(nEarth + 1)
      end
    end
  end

  def Xdate.MakeXdateInfo_Flowyear(hApFunc,hParAll)
    nEYear = @hXdate["FlowEYear"]
    nEMonth = @hXdate["FlowEMonth"]
    if (Cfate::PAN_FLOWYEAR <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_flowyear_title"] = Array.new(2) {Array.new(5)}
      @hXdate["pan_xdate_flowyear_data"] = Array.new(2) {Array.new}

      (0..1).each do |index|
        nEYear = @hXdate["FlowEYear"] + index

        # 存放流年農曆年
        @hXdate["pan_xdate_flowyear_title"][index][0] = nEYear
        @hXdate["pan_xdate_flowyear_title"][index][1] = Xdate.GetYearStr_WC(nEYear,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY])
        @hXdate["pan_xdate_flowyear_title"][index][2] = Xdate.GetLunarYearGanZhiStr(nEYear)
        @hXdate["pan_xdate_flowyear_title"][index][3] = Xdate.GetEYearAnimal(nEYear)
        @hXdate["pan_xdate_flowyear_title"][index][4] = Xdate.GetYearOldStr(Xdate.GetYearOld(@hXdate["Ui_EY"],nEYear))

        # 取得該年月資訊
        aaMonth = Xdate.GetEastMonthArray(nEYear)
        aaMonth.each do |aMonth|
          aData = Array.new

          nEMonth = aMonth[0]
          nEDays = aMonth[1]
          bLeapMonth = aMonth[2]
          nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,1, bLeapMonth)

          aData.push(nEMonth) # 0
          aData.push(bLeapMonth) # 1

          # 農曆月,含潤月
          aData.push(Xdate.GetEMonthStr(nEMonth,bLeapMonth)) # 2

          # 國曆日期 2/1
          aData.push(Xdate.GetWMonthDateStr(nWMonth,nWDate)) # 3

          # 星期幾
          aData.push(Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)) # 4

          # 月干支
            aData.push(Xdate.GetLunarMonthGanZhiStr(nEYear, nEMonth)) # 5

            # 大小月
            aData.push(Xdate.GetEMonthBigSmall(nEDays)) # 6

            @hXdate["pan_xdate_flowyear_data"][index].push(aData)
        end
      end
    end
  end

  def Xdate.MakeXdateInfo_Flowmonth(hApFunc,hParAll)
    nEYear = @hXdate["FlowEYear"]
    nEMonth = @hXdate["FlowEMonth"]
    bLeapMonth = @hXdate["FlowELeap"]
    if (Cfate::PAN_FLOWMONTH <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_flowmonth_title"] = Array.new(6)

      nEYear = @hXdate["pan_xdate_flowmonth_title"][0] = @hXdate["FlowEYear"]
      nEMonth = @hXdate["pan_xdate_flowmonth_title"][1] = @hXdate["FlowEMonth"]
      bLeapMonth = @hXdate["pan_xdate_flowmonth_title"][2] = @hXdate["FlowELeap"]
      @hXdate["pan_xdate_flowmonth_title"][3] = Xdate.GetYearStr_WC(nEYear,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY]) + Xdate.GetMonthStr(nEMonth,bLeapMonth)
      @hXdate["pan_xdate_flowmonth_title"][4] = Xdate.GetLunarYearGanZhiStr(nEYear) + Xdate.GetLunarMonthGanZhiStr(nEYear, nEMonth)
      @hXdate["pan_xdate_flowmonth_title"][5] = Xdate.GetYearOldStr(Xdate.GetYearOld(@hXdate["Ui_EY"],nEYear))

      # 取得該年月資訊
      aaMonth = Xdate.GetEastMonthArray(nEYear)
      nMonthIndex = Xdate.GetEMonthIndex(nEMonth,bLeapMonth,aaMonth)
      @hXdate["pan_xdate_flowmonth_data"] = Array.new

      (1..aaMonth[nMonthIndex][1]).each do |nEDate|
        aData = Array.new
        aData.push(nEDate) # 0
        # 月/日
        aData.push(Xdate.GetMonthDateStr(nEMonth,nEDate,bLeapMonth)) # 1
        # 干支日
        aData.push(Xdate.GetLunarDateGanZhiStr(nEYear,nEMonth,nEDate,1,bLeapMonth,hParAll[Cfate::PAR_FIRST_TIME_TYPE])) # 2

        nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
        # 國曆日期 2/1
        aData.push(Xdate.GetWMonthDateStr(nWMonth,nWDate)) # 3

        # 節氣 時21分59參考A3紫微程式
          aData.push(Xdate.GetSegmentStrFromWest(nWYear, nWMonth, nWDate, 23, 59)) #4

        # 星期幾
        aData.push(Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)) # 5

        @hXdate["pan_xdate_flowmonth_data"].push(aData)
      end
    end
  end

  def Xdate.MakeXdateInfo_Flowdate(hApFunc,hParAll)
    nEYear = @hXdate["FlowEYear"]
    nEMonth = @hXdate["FlowEMonth"]
    bLeapMonth = @hXdate["FlowELeap"]
    nEDate = @hXdate["FlowEDate"]
    if (Cfate::PAN_FLOWDATE <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_flowdate_title"] = Array.new(6)
      @hXdate["pan_xdate_flowdate_data_title"] = Array.new(2)
      @hXdate["pan_xdate_flowdate_data"] = Array.new(2) {Array.new}

      # 年月表頭
      nEYear = @hXdate["pan_xdate_flowdate_title"][0] = @hXdate["FlowEYear"]
      nEMonth = @hXdate["pan_xdate_flowdate_title"][1] = @hXdate["FlowEMonth"]
      bLeapMonth = @hXdate["pan_xdate_flowdate_title"][2] = @hXdate["FlowELeap"]
      @hXdate["pan_xdate_flowdate_title"][3] = Xdate.GetYearStr_WC(nEYear,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY]) + Xdate.GetMonthStr(nEMonth,bLeapMonth)
      @hXdate["pan_xdate_flowdate_title"][4] = Xdate.GetLunarYearGanZhiStr(nEYear) + Xdate.GetLunarMonthGanZhiStr(nEYear, nEMonth)
      @hXdate["pan_xdate_flowdate_title"][5] = Xdate.GetYearOldStr(Xdate.GetYearOld(@hXdate["Ui_EY"],nEYear))

      # 日的訊息
      nEDate = @hXdate["FlowEDate"]
      (0..1).each do |index|
        # 日表頭
        aDataTitle = Array.new
        aDataTitle.push(nEDate) # 0
        aDataTitle.push(Xdate.GetMonthDateStr(nEMonth,nEDate,bLeapMonth)) # 1
        aDataTitle.push(Xdate.GetLunarDateGanZhiStr(nEYear,nEMonth,nEDate,1,bLeapMonth,hParAll[Cfate::PAR_FIRST_TIME_TYPE])) # 2
        nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
        aDataTitle.push(Xdate.GetWMonthDateStr(nWMonth,nWDate)) # 3
        aDataTitle.push(Xdate.GetSegmentStrFromWest(nWYear, nWMonth, nWDate, 23, 59)) # 4
        aDataTitle.push(Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)) # 5
        @hXdate["pan_xdate_flowdate_data_title"][index] = aDataTitle

        (1..12).each do |nEarth|
          nETime = nEarth - 1
          nWHour = Xdate.ETime2Hour(nETime)
          aData = Array.new
          aData.push(nETime) # 0
          aData.push(nWHour) # 1
          aData.push(Xdate.GetLunarTimeGanZhiStr(nEYear,nEMonth,nEDate,nETime,bLeapMonth,hParAll[Cfate::PAR_FIRST_TIME_TYPE])) # 2
          sETime = Xdate.GetETimeRangeStr(nEarth)
          aData.push(sETime) # 3
          aData.push(Xdate.GetSegmentStrFromWest(nWYear, nWMonth, nWDate, nWHour, 59)) # 4

          @hXdate["pan_xdate_flowdate_data"][index].push(aData)
        end
        # 換到下一天
        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      end
    end
  end

  def Xdate.MakeXdateInfo_Flowtime(hApFunc,hParAll)
    nEYear = @hXdate["FlowEYear"]
    nEMonth = @hXdate["FlowEMonth"]
    bLeapMonth = @hXdate["FlowELeap"]
    nEDate = @hXdate["FlowEDate"]
    nETime = @hXdate["FlowETime"]
    nWHour = @hXdate["FlowWHour"]
    if (Cfate::PAN_FLOWTIME <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_flowtime_data_title"] = Array.new(2) {Array.new(5)}
      @hXdate["pan_xdate_flowtime_data"] = Array.new(2) {Array.new(12) {Array.new(9)}}

      aTitle = Array.new
      aTitle.push(Xdate.GetYearStr_WC(nEYear,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY]) + Xdate.GetMonthStr(nEMonth,bLeapMonth) + Xdate.GetDateStr(nEDate)) #0
      nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
      aTitle.push(Xdate.GetWYearMonthDateStr_WC(nWYear,nWMonth,nWDate,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY])) # 1
      aTitle.push(Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)) # 2
      # 3
      aTitle.push(Xdate.GetLunarYearGanZhiStr(nEYear) + Xdate.GetLunarMonthGanZhiStr(nEYear, nEMonth) + Xdate.GetLunarDateGanZhiStr(nEYear,nEMonth,nEDate,1,bLeapMonth,hParAll[Cfate::PAR_FIRST_TIME_TYPE]))
      aTitle.push(Xdate.GetSegmentStrFromWest(nWYear, nWMonth, nWDate, 22, 59)) # 4
      aTitle.push(Xdate.GetYearOldStr(Xdate.GetYearOld(@hXdate["Ui_EY"],nEYear))) # 5
      @hXdate["pan_xdate_flowtime_title"] = aTitle

      (0..1).each do |nIndex|
        # GetSegmentStrFromWest 用沒有調整過的時間 2012/03/12/23/59 不要換成 2012/03/13/23/59
        # nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
        nETime = @hXdate["FlowETime"] + nIndex
          nEYearNext, nEMonthNext, nEDateNext, bLeapMonthNext, nETimeNext = Xdate.NextEDateFromTime(nEYear, nEMonth, nEDate, bLeapMonth, nETime)
        nWHour = Xdate.ETime2Hour(nETimeNext) - 1
        if (nWHour < 0) then
          nWHour += 24
        end
        nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = nEYearNext, nEMonthNext, nEDateNext, bLeapMonthNext
        if ((nWHour == 23)) then
          nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = Xdate.PreEDate(nEYearNext,nEMonthNext,nEDateNext,bLeapMonthNext)
        end
        nWYearPre,nWMonthPre,nWDatePre = Xdate.East2West(nEYearPre, nEMonthPre,nEDatePre, bLeapMonthPre)
        nWYearNext,nWMonthNext,nWDateNext = Xdate.East2West(nEYearNext, nEMonthNext,nEDateNext, bLeapMonthNext)

        nSegHour = (nWHour == 23) ? 0 : nWHour + 1
        @hXdate["pan_xdate_flowtime_data_title"][nIndex][0] = nETimeNext
        @hXdate["pan_xdate_flowtime_data_title"][nIndex][1] = nWHour
        @hXdate["pan_xdate_flowtime_data_title"][nIndex][2] = Xdate.GetLunarTimeGanZhiStr(nEYearNext,nEMonthNext,nEDateNext,nETimeNext,bLeapMonthNext,hParAll[Cfate::PAR_FIRST_TIME_TYPE])
        nEarth = Earth.EarthIndex2Earth(nETime)
        @hXdate["pan_xdate_flowtime_data_title"][nIndex][3] = Xdate.GetETimeRangeStr(nEarth)
        @hXdate["pan_xdate_flowtime_data_title"][nIndex][4] = Xdate.GetSegmentStrFromWest(nWYearPre, nWMonthPre, nWDatePre, nSegHour, 59)

        (1..12).each do |nEarth|
          nEarthIndex = Earth.Earth2EarthIndex(nEarth)
          nWHourStart,nWHourStop = Xdate.GetEMinHour(nETime,nEarthIndex)
          nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = nEYearNext,nEMonthNext,nEDateNext,bLeapMonthNext
          if (nWHourStart == 23) then
            nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = Xdate.PreEDate(nEYearNext,nEMonthNext,nEDateNext,bLeapMonthNext)
          end
          #if (nWHourStart == 0) then
          #nEYear, nEMonth, nEDate, bLeapMonth, nlETime = Xdate.NextEDateFromTime(nEYear,nEMonth,nEDate,bLeapMonth,Xdate.Hour2ETime(nWHourStart))
            nWYearPre,nWMonthPre,nWDatePre = Xdate.East2West(nEYearPre, nEMonthPre,nEDatePre, bLeapMonthPre)
          #end

          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][0] = nEYearPre
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][1] = nEMonthPre
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][2] = bLeapMonthPre
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][3] = nEDatePre

          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][4] = nEarthIndex
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][5] = nWHourStart
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][6] = Xdate.GetEMin(nEarthIndex)
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][7] = Xdate.GetEMinRangeStr(nETimeNext,nEarthIndex)
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][8] = Xdate.GetSegmentStrFromWest(nWYearPre, nWMonthPre, nWDatePre, nWHourStart, Xdate.GetEMin(nEarthIndex)+10-1)
        end
      end
    end
  end

  def Xdate.MakeXdateInfo_Flowmin(hApFunc,hParAll)
    nEYear = @hXdate["FlowEYear"]
    nEMonth = @hXdate["FlowEMonth"]
    bLeapMonth = @hXdate["FlowELeap"]
    nEDate = @hXdate["FlowEDate"]
    nETime = @hXdate["FlowETime"]
    nWHour = @hXdate["FlowWHour"]
    if (Cfate::PAN_FLOWMIN <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_flowmin_data_title"] = Array.new(2) {Array.new(5)}
      @hXdate["pan_xdate_flowmin_data"] = Array.new(2) {Array.new(12) {Array.new(9)}}

      aTitle = Array.new
      aTitle.push(Xdate.GetYearStr_WC(nEYear,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY]) + Xdate.GetMonthStr(nEMonth,bLeapMonth) + Xdate.GetDateStr(nEDate)) #0
      nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
      aTitle.push(Xdate.GetWYearMonthDateStr_WC(nWYear,nWMonth,nWDate,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY])) # 1
      aTitle.push(Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)) # 2
      # 3
      aTitle.push(Xdate.GetLunarYearGanZhiStr(nEYear) + Xdate.GetLunarMonthGanZhiStr(nEYear, nEMonth) + Xdate.GetLunarDateGanZhiStr(nEYear,nEMonth,nEDate,1,bLeapMonth,hParAll[Cfate::PAR_FIRST_TIME_TYPE]))
      aTitle.push(Xdate.GetSegmentStrFromWest(nWYear, nWMonth, nWDate, 22, 59)) # 4
      aTitle.push(Xdate.GetYearOldStr(Xdate.GetYearOld(@hXdate["Ui_EY"],nEYear))) # 5
      @hXdate["pan_xdate_flowmin_title"] = aTitle

      (0..1).each do |nIndex|
        # GetSegmentStrFromWest 用沒有調整過的時間 2012/03/12/23/59 不要換成 2012/03/13/23/59
        nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
        nETime = @hXdate["FlowETime"] + nIndex
          nEYearNext, nEMonthNext, nEDateNext, bLeapMonthNext, nETimeNext = Xdate.NextEDateFromTime(nEYear, nEMonth, nEDate, bLeapMonth, nETime)
        nWHour = Xdate.ETime2Hour(nETimeNext) - 1
        if (nWHour < 0) then
          nWHour += 24
        end
        nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = nEYearNext, nEMonthNext, nEDateNext, bLeapMonthNext
        if ((nWHour == 23)) then
          nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = Xdate.PreEDate(nEYearNext,nEMonthNext,nEDateNext,bLeapMonthNext)
        end
        nWYearPre,nWMonthPre,nWDatePre = Xdate.East2West(nEYearPre, nEMonthPre,nEDatePre, bLeapMonthPre)
        nWYearNext,nWMonthNext,nWDateNext = Xdate.East2West(nEYearNext, nEMonthNext,nEDateNext, bLeapMonthNext)

        nSegHour = (nWHour == 23) ? 0 : nWHour + 1

        @hXdate["pan_xdate_flowmin_data_title"][nIndex][0] = nETimeNext
        @hXdate["pan_xdate_flowmin_data_title"][nIndex][1] = nWHour
        @hXdate["pan_xdate_flowmin_data_title"][nIndex][2] = Xdate.GetLunarTimeGanZhiStr(nEYearNext,nEMonthNext,nEDateNext,nETimeNext,bLeapMonthNext,hParAll[Cfate::PAR_FIRST_TIME_TYPE])
        nEarth = Earth.EarthIndex2Earth(nETime)
        @hXdate["pan_xdate_flowmin_data_title"][nIndex][3] = Xdate.GetETimeRangeStr(nEarth)
        @hXdate["pan_xdate_flowmin_data_title"][nIndex][4] = Xdate.GetSegmentStrFromWest(nWYearPre, nWMonthPre, nWDatePre, nSegHour, 59)

        (1..12).each do |nEarth|
          nEarthIndex = Earth.Earth2EarthIndex(nEarth)
          nWHourStart,nWHourStop = Xdate.GetEMinHour(nETime,nEarthIndex)
          nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = nEYearNext,nEMonthNext,nEDateNext,bLeapMonthNext
          if (nWHourStart == 23) then
            nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = Xdate.PreEDate(nEYearNext,nEMonthNext,nEDateNext,bLeapMonthNext)
          end
          #nEYear, nEMonth, nEDate, bLeapMonth, nlETime = Xdate.NextEDateFromTime(nEYear,nEMonth,nEDate,bLeapMonth,Xdate.Hour2ETime(nWHourStart))
            nWYearPre,nWMonthPre,nWDatePre = Xdate.East2West(nEYearPre, nEMonthPre,nEDatePre, bLeapMonthPre)
          #end
          @hXdate["pan_xdate_flowmin_data"][nIndex][nEarthIndex][0] = nEYearPre
          @hXdate["pan_xdate_flowmin_data"][nIndex][nEarthIndex][1] = nEMonthPre
          @hXdate["pan_xdate_flowmin_data"][nIndex][nEarthIndex][2] = bLeapMonthPre
          @hXdate["pan_xdate_flowmin_data"][nIndex][nEarthIndex][3] = nEDatePre

          @hXdate["pan_xdate_flowmin_data"][nIndex][nEarthIndex][4] = nEarthIndex
          @hXdate["pan_xdate_flowmin_data"][nIndex][nEarthIndex][5] = nWHourStart
          @hXdate["pan_xdate_flowmin_data"][nIndex][nEarthIndex][6] = Xdate.GetEMin(nEarthIndex)
          @hXdate["pan_xdate_flowmin_data"][nIndex][nEarthIndex][7] = Xdate.GetEMinRangeStr(nETimeNext,nEarthIndex)
          @hXdate["pan_xdate_flowmin_data"][nIndex][nEarthIndex][8] = Xdate.GetSegmentStrFromWest(nWYearPre, nWMonthPre, nWDatePre, nWHourStart, Xdate.GetEMin(nEarthIndex)+10-1)
        end
      end
    end
  end

  def Xdate.MakeXdateInfo_Eightword(hAppData,nPanType,hApFunc,hParAll)
    @hXdate = Hash.new
    @hXdate.merge!(hAppData)

    @ShowXdate = Array.new

    i = 1
    if (Xdate.NeedXdate?(Cfate::PAN_NORMAL,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Normal_Eightword(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_NORMAL)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_TENYEAR,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Tenyear_Eightword(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_TENYEAR)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_FLOWYEAR,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Flowyear_Eightword(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_FLOWYEAR)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_FLOWMONTH,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Flowmonth_Eightword(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_FLOWMONTH)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_FLOWDATE,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Flowdate_Eightword(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_FLOWDATE)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_FLOWTIME,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Flowtime_Eightword(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_FLOWTIME)
    end

    @hXdate["ShowXdate"] = @ShowXdate
    return @hXdate
  end
  def Xdate.MakeXdateInfo_Normal_Eightword(hApFunc,hParAll)
    nSky = @hXdate["nSky"]
    nEarth = @hXdate["nEarth"]

    # 萬年曆
    i = 0
    if (Cfate::PAN_NORMAL <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_normal"] = Array.new(11) {Array.new(8) {Array.new(5)}}
      (1..11).each do |nRow|
          nRowIndex = nRow - 1
        (1..8).each do |nCol|
          nColIndex = nCol - 1
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][0] = @hXdate["Ui_SY"] + i
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][1] = Sky.GetName(nSky)
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][2] = Earth.GetName(nEarth)
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][3] = i + 1
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][4] = ((@hXdate["Ui_SY"] + i) == @hXdate["ddg_EY"] ? true : false)
          @hXdate["pan_xdate_normal"][nRowIndex][nColIndex][5] = Xdate.YearDisplayOnlyNumber(@hXdate["Ui_SY"] + i,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY]) # 顯示年

          i += 1
          nSky = Sky.ModifySky(nSky + 1)
          nEarth = Earth.ModifyEarth(nEarth + 1)
        end
      end
    end
  end

  def Xdate.MakeXdateInfo_Tenyear_Eightword(hApFunc,hParAll)
    nSky = @hXdate["nSky"]
    nEarth = @hXdate["nEarth"]
    if (Cfate::PAN_TENYEAR <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_tenyear"] = Array.new(30) {Array.new(5)}

      #取得十年大限開始歲數
      nEarthIndex = @hXdate["LargeSan"]
      nETenYearStartYearOld = @hXdate["Large"][nEarthIndex]

      # 十年大限開始年
      @hXdate["nHighLightStartEYear"] = @hXdate["Ui_SY"] + nETenYearStartYearOld - 1

      # 萬年曆十年盤起始年
      if (nETenYearStartYearOld > 10) then
        nDisStartEYear = @hXdate["nHighLightStartEYear"] - 10
      else
        nDisStartEYear = @hXdate["Ui_SY"]
      end

      # 萬年曆十年盤起始年紀
      nStartEYearOld = Xdate.GetYearOld(@hXdate["Ui_SY"],nDisStartEYear)

      # 起始干支
      nSky = Sky.ModifySky(nSky + nStartEYearOld - 1)
      nEarth = Earth.ModifyEarth(nEarth + nStartEYearOld - 1)
      (1..30).each do |nRow|
        nRowIndex = nRow - 1
        @hXdate["pan_xdate_tenyear"][nRowIndex][0] = nDisStartEYear + nRowIndex
        @hXdate["pan_xdate_tenyear"][nRowIndex][1] = Xdate.WestYearToChina(@hXdate["pan_xdate_tenyear"][nRowIndex][0])
        @hXdate["pan_xdate_tenyear"][nRowIndex][2] = Sky.GetName(nSky) + Earth.GetName(nEarth)
        @hXdate["pan_xdate_tenyear"][nRowIndex][3] = Xdate.GetAnimal(nEarth)
        @hXdate["pan_xdate_tenyear"][nRowIndex][4] = Xdate.GetYearOldStr(nStartEYearOld + nRowIndex)

        nSky = Sky.ModifySky(nSky + 1)
        nEarth = Earth.ModifyEarth(nEarth + 1)
      end
    end
  end

  def Xdate.MakeXdateInfo_Flowyear_Eightword(hApFunc,hParAll)
    nEYear = @hXdate["FlowEYear"]
    nEMonth = @hXdate["FlowEMonth"]
    nWYear = @hXdate["FlowWYear"]
    nWMonth = @hXdate["FlowWMonth"]
    nWDate = @hXdate["FlowWDate"]
      nWHour = @hXdate["FlowWHour"]
      nWMinute = @hXdate["FlowWMin"]

      nSYear,nSMonth,nSDate,nWHour,nWMinute,nSSegmentIndex = Xdate.Wdate2SegmentDate(nWYear,nWMonth,nWDate,nWHour,nWMinute,hParAll[Cfate::PAR_FIRST_SEGMENT])
    if (Cfate::PAN_FLOWYEAR <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_flowyear_title"] = Array.new(2) {Array.new(4)}
      @hXdate["pan_xdate_flowyear_data"] = Array.new(2) {Array.new}

      (0..1).each do |index|
        nSYear += index

        # 存放流年農曆年
        @hXdate["pan_xdate_flowyear_title"][index][0] = Xdate.GetYearStr_WC(nSYear,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY])
        @hXdate["pan_xdate_flowyear_title"][index][1] = Xdate.GetLunarYearGanZhiStr(nSYear)
        @hXdate["pan_xdate_flowyear_title"][index][2] = Xdate.GetEYearAnimal(nSYear)
        @hXdate["pan_xdate_flowyear_title"][index][3] = Xdate.GetYearOldStr(Xdate.GetYearOld(@hXdate["Ui_SY"],nSYear))

        # 取得該年月資訊
        (0..11).each do |nSegMonth|
          aData = Array.new

          nWYear,nWMonth,nWDate,nWHour,nWMinute,nSegmentIndex = Xdate.SegmentDate2Wdate(nSYear,nSegMonth + 1,1,hParAll[Cfate::PAR_FIRST_SEGMENT])
          nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)

          aData.push(nEYear) # 0
          aData.push(nEMonth) # 1
          aData.push(nEDate) # 2
          aData.push(bLeapMonth) # 3
          aData.push(nSegmentIndex) # 4

          # 農曆月,含潤月
          aData.push(Xdate.GetSegMonthStr(nSegMonth + 1)) # 5

          # 月干支
            aData.push(Xdate.GetSegMonthGanZhiStr(nWYear, nWMonth, nWDate,nWHour, nWMinute,hParAll[Cfate::PAR_FIRST_SEGMENT])) # 6

            # 節氣
            aData.push(Xdate.GetSegmentStr(nSegmentIndex)) # 7

          # 國曆日期 2/1
          aData.push(Xdate.GetWMonthDateStr(nWMonth,nWDate)) # 8

          # 星期幾
          aData.push(Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)) # 9

          # 農曆月/日
          aData.push(Xdate.GetMonthDateStr(nEMonth,nEDate,bLeapMonth)) # 10

            @hXdate["pan_xdate_flowyear_data"][index].push(aData)
        end
      end
    end
  end

  def Xdate.MakeXdateInfo_Flowmonth_Eightword(hApFunc,hParAll)
    if (Cfate::PAN_FLOWMONTH <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      nEYear = @hXdate["FlowEYear"]
      nEMonth = @hXdate["FlowEMonth"]
      nEDate = @hXdate["FlowEDate"]
      bLeapMonth = @hXdate["FlowELeap"]
      nWYear = @hXdate["FlowWYear"]
      nWMonth = @hXdate["FlowWMonth"]
      nWDate = @hXdate["FlowWDate"]
        nWHour = @hXdate["FlowWHour"]
        nWMinute = @hXdate["FlowWMin"]

      nWYear, nWMonth, nWDate,nHour,nMinute,nSegmentIndex = Xdate.GetSegMonthFirstDate(nWYear, nWMonth, nWDate,23,59)
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)

      nDays = Xdate.GetSegMonthDaysByWestDate(nWYear, nWMonth, nWDate,nHour,nMinute)

        nSYear,nSMonth,nSDate,nWHour,nWMinute,nSSegmentIndex = Xdate.Wdate2SegmentDate(nWYear,nWMonth,nWDate,23,59,hParAll[Cfate::PAR_FIRST_SEGMENT])

      @hXdate["pan_xdate_flowmonth_title"] = Array.new(3)

      @hXdate["pan_xdate_flowmonth_title"][0] = Xdate.GetYearStr_WC(nSYear,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY]) + Xdate.GetSegMonthStr(nSMonth)
      sGanZhi = Xdate.GetLunarYearGanZhiStr(nSYear) + Xdate.GetLunarMonthGanZhiStr(nSYear, nSMonth)
      @hXdate["pan_xdate_flowmonth_title"][1] = sGanZhi
      @hXdate["pan_xdate_flowmonth_title"][2] = Xdate.GetYearOldStr(Xdate.GetYearOld(@hXdate["Ui_SY"],nSYear))

      @hXdate["pan_xdate_flowmonth_data"] = Array.new

      (0...nDays).each do |i|
        aData = Array.new
        aData.push(nEYear) # 0
        aData.push(nEMonth) # 1
        aData.push(nEDate) # 2
        aData.push(bLeapMonth) # 3

        # 國曆日期 2/1
        aData.push(Xdate.GetWMonthDateStr(nWMonth,nWDate)) # 4

        # 干支日
        sGanZhi = Xdate.GetLunarDateGanZhiStr(nEYear,nEMonth,nEDate,1,bLeapMonth,hParAll[Cfate::PAR_FIRST_TIME_TYPE])
        aData.push(sGanZhi) # 5

        # 星期幾
        aData.push(Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)) # 6

        # 節氣 時21分59參考A3紫微程式
          aData.push(Xdate.GetSegmentStrFromWest(nWYear, nWMonth, nWDate, 23, 59)) # 7

        # 農曆月/日
        aData.push(Xdate.GetMonthDateStr(nEMonth,nEDate,bLeapMonth)) # 8

        @hXdate["pan_xdate_flowmonth_data"].push(aData)

        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextNEDate(nEYear,nEMonth,nEDate,bLeapMonth,1)
        nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
      end
    end
  end

  def Xdate.MakeXdateInfo_Flowdate_Eightword(hApFunc,hParAll)
    nEYear = @hXdate["FlowEYear"]
    nEMonth = @hXdate["FlowEMonth"]
    bLeapMonth = @hXdate["FlowELeap"]
    nEDate = @hXdate["FlowEDate"]

    nWYear = @hXdate["FlowWYear"]
    nWMonth = @hXdate["FlowWMonth"]
    nWDate = @hXdate["FlowWDate"]
      nWHour = @hXdate["FlowWHour"]
      nWMinute = @hXdate["FlowWMin"]
    nSYear,nSMonth,nSDate,nWHour,nWMinute,nSSegmentIndex = Xdate.Wdate2SegmentDate(nWYear,nWMonth,nWDate,23,59,hParAll[Cfate::PAR_FIRST_SEGMENT])
    if (Cfate::PAN_FLOWDATE <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_flowdate_title"] = Array.new(6)
      @hXdate["pan_xdate_flowdate_data_title"] = Array.new(2)
      @hXdate["pan_xdate_flowdate_data"] = Array.new(2) {Array.new}

      # 年月表頭
      nEYear = @hXdate["pan_xdate_flowdate_title"][0] = @hXdate["FlowEYear"]
      nEMonth = @hXdate["pan_xdate_flowdate_title"][1] = @hXdate["FlowEMonth"]
      bLeapMonth = @hXdate["pan_xdate_flowdate_title"][2] = @hXdate["FlowELeap"]
      @hXdate["pan_xdate_flowdate_title"][3] = Xdate.GetYearStr_WC(nSYear,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY]) + Xdate.GetSegMonthStr(nSMonth)
      @hXdate["pan_xdate_flowdate_title"][4] = Xdate.GetLunarYearGanZhiStr(nSYear) + Xdate.GetLunarMonthGanZhiStr(nSYear, nSMonth)
      @hXdate["pan_xdate_flowdate_title"][5] = Xdate.GetYearOldStr(Xdate.GetYearOld(@hXdate["Ui_SY"],nSYear))

      # 日的訊息
      nEDate = @hXdate["FlowEDate"]
      (0..1).each do |index|
        nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
        # 日表頭
        aDataTitle = Array.new
        aDataTitle.push(nEDate) # 0
        aDataTitle.push(Xdate.GetWMonthDateStr(nWMonth,nWDate)) # 3
        aDataTitle.push(Xdate.GetLunarDateGanZhiStr(nEYear,nEMonth,nEDate,1,bLeapMonth,hParAll[Cfate::PAR_FIRST_TIME_TYPE])) # 2
        aDataTitle.push(Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)) # 5
        aDataTitle.push(Xdate.GetSegmentStrFromWest(nWYear, nWMonth, nWDate, 23, 59)) # 4
        aDataTitle.push(Xdate.GetMonthDateStr(nEMonth,nEDate,bLeapMonth)) # 1
        @hXdate["pan_xdate_flowdate_data_title"][index] = aDataTitle

        (1..12).each do |nEarth|
          nETime = nEarth - 1
          nWHour = Xdate.ETime2Hour(nETime)
          aData = Array.new
          aData.push(nETime) # 0
          aData.push(nWHour) # 1
          aData.push(Xdate.GetLunarTimeGanZhiStr(nEYear,nEMonth,nEDate,nETime,bLeapMonth,hParAll[Cfate::PAR_FIRST_TIME_TYPE])) # 2
          sETime = Xdate.GetETimeRangeStr(nEarth)
          aData.push(sETime) # 3
          aData.push(Xdate.GetSegmentStrFromWest(nWYear, nWMonth, nWDate, nWHour, 59)) # 4

          @hXdate["pan_xdate_flowdate_data"][index].push(aData)
        end
        # 換到下一天
        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      end
    end
  end

  def Xdate.MakeXdateInfo_Flowtime_Eightword(hApFunc,hParAll)
    nEYear = @hXdate["FlowEYear"]
    nEMonth = @hXdate["FlowEMonth"]
    bLeapMonth = @hXdate["FlowELeap"]
    nEDate = @hXdate["FlowEDate"]
    nETime = @hXdate["FlowETime"]
    nWHour = @hXdate["FlowWHour"]

    # nWYear = @hXdate["FlowWYear"]
    # nWMonth = @hXdate["FlowWMonth"]
    # nWDate = @hXdate["FlowWDate"]
   #    nWHour = @hXdate["FlowWHour"]
   #    nWMinute = @hXdate["FlowWMin"]
    # nSYear,nSMonth,nSDate,nWHour,nWMinute,nSSegmentIndex = Xdate.Wdate2SegmentDate(nWYear,nWMonth,nWDate,23,59,hParAll[Cfate::PAR_FIRST_SEGMENT])
    if (Cfate::PAN_FLOWTIME <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_flowtime_data_title"] = Array.new(2) {Array.new(5)}
      @hXdate["pan_xdate_flowtime_data"] = Array.new(2) {Array.new(12) {Array.new(9)}}

      aTitle = Array.new
      aTitle.push(Xdate.GetYearStr_WC(nEYear,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY]) + Xdate.GetMonthStr(nEMonth,bLeapMonth) + Xdate.GetDateStr(nEDate)) #0
      nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
      aTitle.push(Xdate.GetWYearMonthDateStr_WC(nWYear,nWMonth,nWDate,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY])) # 1
      aTitle.push(Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)) # 2
      # 3
      aTitle.push(Xdate.GetLunarYearGanZhiStr(nEYear) + Xdate.GetLunarMonthGanZhiStr(nEYear, nEMonth) + Xdate.GetLunarDateGanZhiStr(nEYear,nEMonth,nEDate,1,bLeapMonth,hParAll[Cfate::PAR_FIRST_TIME_TYPE]))
      aTitle.push(Xdate.GetSegmentStrFromWest(nWYear, nWMonth, nWDate, 22, 59)) # 4
      aTitle.push(Xdate.GetYearOldStr(Xdate.GetYearOld(@hXdate["Ui_SY"],nEYear))) # 5
      @hXdate["pan_xdate_flowtime_title"] = aTitle

      (0..1).each do |nIndex|
        # GetSegmentStrFromWest 用沒有調整過的時間 2012/03/12/23/59 不要換成 2012/03/13/23/59
        # nWYear,nWMonth,nWDate = Xdate.East2West(nEYear, nEMonth,nEDate, bLeapMonth)
        nETime = @hXdate["FlowETime"] + nIndex
          nEYearNext, nEMonthNext, nEDateNext, bLeapMonthNext, nETimeNext = Xdate.NextEDateFromTime(nEYear, nEMonth, nEDate, bLeapMonth, nETime)
        nWHour = Xdate.ETime2Hour(nETimeNext) - 1
        if (nWHour < 0) then
          nWHour += 24
        end
        nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = nEYearNext, nEMonthNext, nEDateNext, bLeapMonthNext
        if ((nWHour == 23)) then
          nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = Xdate.PreEDate(nEYearNext,nEMonthNext,nEDateNext,bLeapMonthNext)
        end
        nWYearPre,nWMonthPre,nWDatePre = Xdate.East2West(nEYearPre, nEMonthPre,nEDatePre, bLeapMonthPre)
        nWYearNext,nWMonthNext,nWDateNext = Xdate.East2West(nEYearNext, nEMonthNext,nEDateNext, bLeapMonthNext)

        nSegHour = (nWHour == 23) ? 0 : nWHour + 1
        @hXdate["pan_xdate_flowtime_data_title"][nIndex][0] = nETimeNext
        @hXdate["pan_xdate_flowtime_data_title"][nIndex][1] = nWHour
        @hXdate["pan_xdate_flowtime_data_title"][nIndex][2] = Xdate.GetLunarTimeGanZhiStr(nEYearNext,nEMonthNext,nEDateNext,nETimeNext,bLeapMonthNext,hParAll[Cfate::PAR_FIRST_TIME_TYPE])
        nEarth = Earth.EarthIndex2Earth(nETime)
        @hXdate["pan_xdate_flowtime_data_title"][nIndex][3] = Xdate.GetETimeRangeStr(nEarth)
        @hXdate["pan_xdate_flowtime_data_title"][nIndex][4] = Xdate.GetSegmentStrFromWest(nWYearPre, nWMonthPre, nWDatePre, nSegHour, 59)

        (1..12).each do |nEarth|
          nEarthIndex = Earth.Earth2EarthIndex(nEarth)
          nWHourStart,nWHourStop = Xdate.GetEMinHour(nETime,nEarthIndex)
          nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = nEYearNext,nEMonthNext,nEDateNext,bLeapMonthNext
          if (nWHourStart == 23) then
            nEYearPre,nEMonthPre,nEDatePre,bLeapMonthPre = Xdate.PreEDate(nEYearNext,nEMonthNext,nEDateNext,bLeapMonthNext)
          end
          #if (nWHourStart == 0) then
          #nEYear, nEMonth, nEDate, bLeapMonth, nlETime = Xdate.NextEDateFromTime(nEYear,nEMonth,nEDate,bLeapMonth,Xdate.Hour2ETime(nWHourStart))
            nWYearPre,nWMonthPre,nWDatePre = Xdate.East2West(nEYearPre, nEMonthPre,nEDatePre, bLeapMonthPre)
          #end

          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][0] = nEYearPre
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][1] = nEMonthPre
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][2] = bLeapMonthPre
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][3] = nEDatePre

          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][4] = nEarthIndex
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][5] = nWHourStart
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][6] = Xdate.GetEMin(nEarthIndex)
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][7] = Xdate.GetEMinRangeStr(nETimeNext,nEarthIndex)
          @hXdate["pan_xdate_flowtime_data"][nIndex][nEarthIndex][8] = Xdate.GetSegmentStrFromWest(nWYearPre, nWMonthPre, nWDatePre, nWHourStart, Xdate.GetEMin(nEarthIndex)+10-1)
        end
      end
    end
  end

  def Xdate.date_input_prepare(params)
    nWMinute =  Xdate.date_s2int("00")
    nWSec = Xdate.date_s2int("00")

    hDate = Hash.new
    if (params == nil || params["timestamp"] == nil) then
      if (params == nil || params["calType"] == nil) then
        # now
        nWYear,nWMonth,nWDate,nWHour,sex,nWMinute,mb = Xdate.parse_timestamp()
        calType = Xdate::CT_SOLAR
        hDate["sex"] = sex
      else
        hDate["pan"] = params["pan"]
        hDate["name"] = params["name"]
        hDate["sex"] = params["sex"]
        calType = params["calType"].to_i
        if (calType == Xdate::CT_SOLAR) then
          nWYear = Xdate.date_s2int(params["year"])
          nWMonth = Xdate.date_s2int(params["month"])
          nWDate = Xdate.date_s2int(params["day"])
          nWHour = Xdate.date_s2int(params["hour"])
          nWMinute = Xdate.date_s2int(params["minute"])
        else
          nEYear = Xdate.date_s2int(params["year"])
          nEMonth,bLeapMonth = Xdate.monthValue2month(Xdate.date_s2int(params["month"]))
          nEDate = Xdate.date_s2int(params["day"])
          nWHour = Xdate.date_s2int(params["hour"])
          nWMinute = Xdate.date_s2int(params["minute"])
        end
        nWHour = Xdate.GetLegalHour(nWHour)
      end
      # sBirthday = "#{nWYear}-#{nWMonth}-#{nWDate} #{nWHour}:#{nWMinute}:#{nWSec}"
    else
      timestamp = params["timestamp"]
      calType = Xdate.caltype_timestamp(timestamp)
      y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp) # 回來全部都是CT_SOLAR
      hDate["sex"] = sex

      nWHour = h
      if (calType == Xdate::CT_SOLAR) then
        nWYear = y.to_i
        nWMonth = m.to_i
        nWDate = d.to_i
      else # lunar
        l = 0
        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(y,m,d)
      end
      nWMinute = min
    end
    if (calType == Xdate::CT_SOLAR) then
      nWYear,nWMonth,nWDate = Xdate.GetLegalWDate(nWYear,nWMonth,nWDate)
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
    else # lunar
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.GetLegalEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,nEMonth,nEDate,bLeapMonth)
    end

    hDate["calType"] = calType
    hDate["wyear"] = nWYear
    hDate["wmonth"] = nWMonth
    hDate["wday"] = nWDate
    hDate["hour"] = nWHour
    hDate["minute"] = nWMinute
    hDate["eyear"] = nEYear
    hDate["eleap"] = bLeapMonth
    hDate["emonth"] = nEMonth
    hDate["eday"] = nEDate
    hDate["birthday"] = DateTime.new(nWYear,nWMonth,nWDate,nWHour,nWMinute,nWSec,"+8")

    return hDate
  end
  def Xdate.date_input_prepare_with_timestamp(params,timestamp_str)
    hDate = Xdate.date_input_prepare(params)
    nWYear,nWMonth,nWDate,nWHour,nWMinute = hDate["wyear"],hDate["wmonth"],hDate["wday"],hDate["hour"],hDate["minute"]
    nWHour = 0 if nWHour < 0
    nWMinute = 0 if nWMinute < 0
    hDate[timestamp_str] = Xdate.make_timestamp2(nWYear,nWMonth,nWDate,nWHour,nWMinute)

    return hDate
  end

  def Xdate.udt_date_input_prepare(params)
    nWMinute =  Xdate.date_s2int("00")
    nWSec = Xdate.date_s2int("00")

    hDate = Hash.new
    if (params == nil || params["timestamp_udt"] == nil) then
      if (params == nil || params["calType"] == nil) then
        # now
        nWYear,nWMonth,nWDate,nWHour,sex,nWMinute,mb = Xdate.parse_timestamp()
        calType = Xdate::CT_SOLAR
        hDate["sex"] = sex
      else
        hDate["pan"] = params["pan"]
        hDate["name"] = params["name"]
        hDate["sex"] = params["sex"]
        calType = params["calType"].to_i
        if (calType == Xdate::CT_SOLAR) then
          nWYear = Xdate.date_s2int(params["year"])
          nWMonth = Xdate.date_s2int(params["month"])
          nWDate = Xdate.date_s2int(params["day"])
          nWHour = Xdate.date_s2int(params["hour"])
          nWMinute = Xdate.date_s2int(params["minute"])
        else
          nEYear = Xdate.date_s2int(params["year"])
          nEMonth,bLeapMonth = Xdate.monthValue2month(Xdate.date_s2int(params["month"]))
          nEDate = Xdate.date_s2int(params["day"])
          nWHour = Xdate.date_s2int(params["hour"])
          nWMinute = Xdate.date_s2int(params["minute"])
        end
        nWHour = Xdate.GetLegalHour(nWHour)
      end
      # sBirthday = "#{nWYear}-#{nWMonth}-#{nWDate} #{nWHour}:#{nWMinute}:#{nWSec}"
    else
      timestamp = params["timestamp_udt"]
      calType = Xdate.caltype_timestamp(timestamp)
      y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp) # 回來全部都是CT_SOLAR
      hDate["sex"] = sex

      nWHour = h
      if (calType == Xdate::CT_SOLAR) then
        nWYear = y.to_i
        nWMonth = m.to_i
        nWDate = d.to_i
      else # lunar
        l = 0
        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(y,m,d)
      end
      nWMinute = min
    end
    if (calType == Xdate::CT_SOLAR) then
      nWYear,nWMonth,nWDate = Xdate.GetLegalWDate(nWYear,nWMonth,nWDate)
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
    else # lunar
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.GetLegalEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,nEMonth,nEDate,bLeapMonth)
    end

    hDate["calType"] = calType
    hDate["wyear"] = nWYear
    hDate["wmonth"] = nWMonth
    hDate["wday"] = nWDate
    hDate["hour"] = nWHour
    hDate["minute"] = nWMinute
    hDate["eyear"] = nEYear
    hDate["eleap"] = bLeapMonth
    hDate["emonth"] = nEMonth
    hDate["eday"] = nEDate
    hDate["birthday"] = DateTime.new(nWYear,nWMonth,nWDate,nWHour,nWMinute,nWSec,"+8")

    return hDate
  end

  def Xdate.MakeXdateInfo_Wannianli(hAppData,nPanType,hApFunc,hParAll)
    @hXdate = Hash.new
    @hXdate.merge!(hAppData)

    @ShowXdate = Array.new

    i = 1

    if (Xdate.NeedXdate?(Cfate::PAN_TENYEAR,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Wannianli_Tenyear(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_TENYEAR)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_FLOWYEAR,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Wannianli_Flowyear(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_FLOWYEAR)
    end
    if (Xdate.NeedXdate?(Cfate::PAN_FLOWMONTH,nPanType,hApFunc)) then
      Xdate.MakeXdateInfo_Wannianli_Flowmonth(hApFunc,hParAll)
      Xdate.AddShowXdate(Cfate::PAN_FLOWMONTH)
    end

    @hXdate["ShowXdate"] = @ShowXdate
    return @hXdate
  end

  Wannianli_TenYear_Row = 28
  def Xdate.MakeXdateInfo_Wannianli_Tenyear(hApFunc,hParAll)
    flow_year = @hXdate["FlowWYear"]
    if (flow_year < Cfate::PAN_FIRST_YEAR) then
      flow_year = Cfate::PAN_FIRST_YEAR
    elsif (flow_year > Cfate::PAN_LAST_YEAR) then
      flow_year = Cfate::PAN_LAST_YEAR
    end
    nStarYear = flow_year - 14
    if (nStarYear < Cfate::PAN_FIRST_YEAR) then
      nStarYear = Cfate::PAN_FIRST_YEAR
    elsif (nStarYear + Wannianli_TenYear_Row > Cfate::PAN_LAST_YEAR) then
      nStarYear = Cfate::PAN_LAST_YEAR - Wannianli_TenYear_Row + 1
    end

    ganzhi_year = Xdate.GetWYearGanZhi(nStarYear)
    nSky,nEarth = Xdate.GetGanZhiSkyEarth(ganzhi_year)

    if (Cfate::PAN_TENYEAR <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_tenyear"] = Array.new(Wannianli_TenYear_Row) {Array.new(5)}

      # 起始干支
      (1..Wannianli_TenYear_Row).each do |nRow|
        nRowIndex = nRow - 1
        @hXdate["pan_xdate_tenyear"][nRowIndex][0] = nStarYear + nRowIndex
        @hXdate["pan_xdate_tenyear"][nRowIndex][1] = Xdate.WestYearToChina(@hXdate["pan_xdate_tenyear"][nRowIndex][0])
        @hXdate["pan_xdate_tenyear"][nRowIndex][2] = Sky.GetName(nSky) + Earth.GetName(nEarth) + Pm.GetStr("IDS_X_YEAR")
        @hXdate["pan_xdate_tenyear"][nRowIndex][3] = Xdate.GetAnimal(nEarth)
        @hXdate["pan_xdate_tenyear"][nRowIndex][4] = Xdate.tai_sui(ganzhi_year)

        nSky = Sky.ModifySky(nSky + 1)
        nEarth = Earth.ModifyEarth(nEarth + 1)
        ganzhi_year = Pm.TestNo60(ganzhi_year + 1)
      end
    end
  end
  def Xdate.tai_sui(ganzhi_year)
    tai_sui = Xdate.wan_nian_li_str("tai_sui")
    tai_sui += Xdate.wan_nian_li_tai_sui_str(ganzhi_year)
    return tai_sui
  end
  def Xdate.MakeXdateInfo_Wannianli_Flowyear(hApFunc,hParAll)
    nWMonth = @hXdate["FlowWMonth"]
    if (Cfate::PAN_FLOWYEAR <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_flowyear_title"] = Array.new(2) {Array.new(5)}
      @hXdate["pan_xdate_flowyear_data"] = Array.new(2) {Array.new}

      (0..1).each do |index|
        nWYear = @hXdate["FlowWYear"] + index
        ganzhi_year = Xdate.GetWYearGanZhi(nWYear)

        # 存放流年農曆年
        @hXdate["pan_xdate_flowyear_title"][index][0] = nWYear
        @hXdate["pan_xdate_flowyear_title"][index][1] = nWYear
        @hXdate["pan_xdate_flowyear_title"][index][2] = Xdate.WestYearToChina(nWYear)
        @hXdate["pan_xdate_flowyear_title"][index][3] = Xdate.GetLunarYearGanZhiStr(nWYear)
        @hXdate["pan_xdate_flowyear_title"][index][4] = Xdate.GetEYearAnimal(nWYear)
        @hXdate["pan_xdate_flowyear_title"][index][5] = Xdate.tai_sui(ganzhi_year)

        # 取得該年月資訊
        months = (1..12)
        months.each do |nWMonth|
          aData = Array.new

          aData.push(nWMonth) # 0 cur month
          # 國曆月
          aData.push(Xdate.GetWMonthStr(nWMonth)) # 1

          nWDate = 1
          nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
          # 農曆月日,含潤月
          aData.push(Xdate.GetEMonthDateStr(nEMonth,nEDate,bLeapMonth)) # 2

          # 節氣
          nWYear,nWMonth,nWDate, nHour, nMinute,segment_str = Xdate.GetSegMonthFirstDateWithSegmentStr(nWYear, nWMonth, 10,22,59)
          aData.push(segment_str) # 3

          # 節氣開始之國曆日期 2/1
          month_date = "#{nWMonth}/#{nWDate}"
          aData.push(month_date) # 4

          # 節氣月干支
          nSYear,nSMonth,nSDate,nWHour,nWMinute,nSegmentIndex = Xdate.Wdate2SegmentDate(nWYear,nWMonth,nWDate,nHour,nMinute,Xdate::SEGMENT_SPRING)
          nMonthSky,nMonthEarth = Xdate.GetMonthSkyEarth(nSYear,nSMonth)
          nGanZhiMonth = Xdate.SkyEarthIndex2GanZhi(Sky.Sky2SkyIndex(nMonthSky),Earth.Earth2EarthIndex(nMonthEarth))
          aData.push(Xdate.GetGanZhiMonth_Str(nGanZhiMonth)) # 5

          jieqi_month_desc,jieqi_yue_sha = Xdate.fc_segment_month_desc_only(nGanZhiMonth,nMonthEarth)
          # 臘月等
          aData.push(jieqi_month_desc) # 6
          # 月煞何方
          aData.push(jieqi_yue_sha) # 7

          @hXdate["pan_xdate_flowyear_data"][index].push(aData)
        end
      end
    end
  end

  def Xdate.MakeXdateInfo_Wannianli_Flowmonth(hApFunc,hParAll)
    nWYear = @hXdate["FlowWYear"]
    nWMonth = @hXdate["FlowWMonth"]
    nWDate = @hXdate["FlowWDate"]
    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
    ganzhi_year = Xdate.GetWYearGanZhi(nWYear)
    nSky,nEarth = Xdate.GetGanZhiSkyEarth(ganzhi_year)
    if (Cfate::PAN_FLOWMONTH <= hApFunc[Cfate::LAST_XDATE_PAN_TYPE]) then
      @hXdate["pan_xdate_flowmonth_title"] = Array.new(12)

      nWYear = @hXdate["pan_xdate_flowmonth_title"][0] = @hXdate["FlowWYear"]
      nWMonth = @hXdate["pan_xdate_flowmonth_title"][1] = @hXdate["FlowWMonth"]
      @hXdate["pan_xdate_flowmonth_title"][2] = Xdate.GetYearStr_WC(nWYear,hParAll[Cfate::PAR_PAN_YEAR_DISPLAY]) + Xdate.GetWMonthStr(nWMonth)
      # @hXdate["pan_xdate_flowmonth_title"][3] = Xdate.GetLunarYearGanZhiStr(nWYear) + Xdate.GetLunarMonthGanZhiStr(nWYear, nWMonth)
      @hXdate["pan_xdate_flowmonth_title"][3] = Xdate.GetLunarYearGanZhiStr(nEYear,nEMonth,nEDate,bLeapMonth) + Xdate.GetLunarMonthGanZhiStr(nEYear,nEMonth)
      @hXdate["pan_xdate_flowmonth_title"][4] = Xdate.GetAnimal(nEarth)
      @hXdate["pan_xdate_flowmonth_title"][5] = Xdate.tai_sui(ganzhi_year)
      nSYear,nSMonth,nSDate, nSHour, nSMinute,segment_str = Xdate.GetSegMonthFirstDateWithJieqiStr(nWYear, nWMonth, 1,23,59)
      @hXdate["pan_xdate_flowmonth_title"][6] = [segment_str,nSYear,nSMonth,nSDate]
      nSYear1,nSMonth1,nSDate1, nSHour1, nSMinute1,segment_str1 = Xdate.GetSegMonthFirstDateWithJieqiStr(nWYear, nWMonth, 10,23,59)
      jieqi_shichen1 = "#{nSMonth1}/#{nSDate1} %02d:%02d" % [nSHour1, nSMinute1]
      @hXdate["pan_xdate_flowmonth_title"][7] = segment_str1
      @hXdate["pan_xdate_flowmonth_title"][8] = jieqi_shichen1
      nSYear2,nSMonth2,nSDate2, nSHour2, nSMinute2,segment_str2 = Xdate.GetSegMonthFirstDateWithJieqiStr(nWYear, nWMonth, 25,23,59)
      jieqi_shichen2 = "#{nSMonth2}/#{nSDate2} %02d:%02d" % [nSHour2, nSMinute2]
      @hXdate["pan_xdate_flowmonth_title"][9] = segment_str2
      @hXdate["pan_xdate_flowmonth_title"][10] = jieqi_shichen2
      nWYear3, nWMonth3 = Xdate.NextWMonth(nWYear,nWMonth)
      nSYear3,nSMonth3,nSDate3, nSHour3, nSMinute3,segment_str3 = Xdate.GetSegMonthFirstDateWithJieqiStr(nWYear3, nWMonth3, 10,23,59)
      @hXdate["pan_xdate_flowmonth_title"][11] = [segment_str3,nSYear3,nSMonth3,nSDate3]

      # 取得該年月資訊
      nDays = Xdate.GetWMonthDays(nWYear,nWMonth)
      @hXdate["pan_xdate_flowmonth_data"] = Array.new
      nWHour = 22
      nWMinute = 59
      jieqi_jie = (nSDate1..nSDate2-1)

      (1..nDays).each do |nWDate|
        aData = Array.new
        # 是否在節氣的節裡面
        aData.push(jieqi_jie.include?(nWDate)) # 0

        # 日
        aData.push(nWDate) # 1

        # 星期幾
        aData.push(Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)) # 2

        # 農曆年 月 日
        nFirstTimeType = Xdate::FIRST_NEXT
        nongmingli_str = Xdate.GetLunar8Words_Str3(nWYear, nWMonth,nWDate, nWHour,nFirstTimeType)
        aData.push(nongmingli_str) # 3

        # 干支日
        nFirstTimeType = Xdate::FIRST_NEXT
        nFirstSegment = Xdate::SEGMENT_SPRING
        jieqili_str = Xdate.GetSeg8Words_Str3(nWYear, nWMonth,nWDate, nWHour,nWMinute,nFirstTimeType,nFirstSegment,true,Xdate::SegmentNow)
        aData.push(jieqili_str) # 4

        # 奇門局
        aData.push(Xdate.api_nong_min_li_qi_men_ri_ju(nWYear,nWMonth,nWDate)) # 5


        @hXdate["pan_xdate_flowmonth_data"].push(aData)
      end
    end
  end
end
