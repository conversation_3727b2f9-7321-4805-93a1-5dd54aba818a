class Zeri
  Max_chucun_count = 20
  def Zeri.max_chucun_count()
    count = Pm.getSystemValue("zeri_max_chucun_count")
    if (count == nil || count == "") then
      return Max_chucun_count
    end
    return count.to_i
  end
  def Zeri.zeri_pan_data_constant_data(ap_name,zeri_simple)
    if (ap_name == "star") then
      return Star.zeri_pan_data_constant_data(zeri_simple)
    elsif (ap_name == "eightword") then
      return Eightword.zeri_pan_data_constant_data(zeri_simple)
    elsif (ap_name == "wannianli") then
      return Wannianli.zeri_pan_data_constant_data(zeri_simple)
    else
      return {}
    end
  end
  def Zeri.zeri_pan_data_default(ap_name,zeri_simple)
    if (ap_name == "star") then
      return Star.zeri_pan_data_default(zeri_simple)
    elsif (ap_name == "eightword") then
      return Eightword.zeri_pan_data_default(zeri_simple)
    elsif (ap_name == "wannianli") then
      return Wannianli.zeri_pan_data_default(zeri_simple)
    else
      return {}
    end
  end
  def Zeri.zeri_pan_data_parsing(ap_name,zeri_simple,hData)
    if (ap_name == "star") then
      return Star.zeri_pan_data_parsing(zeri_simple,hData)
    elsif (ap_name == "eightword") then
      return Eightword.zeri_pan_data_parsing(zeri_simple,hData)
    elsif (ap_name == "wannianli") then
      return Wannianli.zeri_pan_data_parsing(zeri_simple,hData)
    else
      return {}
    end
    if (zeri_simple == 2) then
    end
  end
  def Zeri.zeri_simple(can_zeri,ap_name)
    # if (ap_name == "eightword") then
    #   return 1
    # else
      return can_zeri ? 2 : 1
    # end
  end
  def Zeri.zeri_need_hour(ap_name,free=false)
    if (["star","eightword"].include?(ap_name)) then
      return false
    elsif (["wannianli"].include?(ap_name)) then
      if (free) then
        return true
      else
        return false
      end
    else
      return false
    end
  end
  def Zeri.need_seven_days(zeri_simple,ap_name)
    if (["star","eightword"].include?(ap_name)) then
      return (zeri_simple == 2)
    elsif (["wannianli"].include?(ap_name)) then
      return false
    else
      return false
    end
  end

  def ziwei_pan_answer(ap_name,hUserInfo_org,hUserDefData_org,hParAll_org)
    hUserInfo = hUserInfo_org.clone
    hUserDefData = hUserDefData_org.clone
    hParAll = hParAll_org.clone
    nPanType = Cfate::PAN_NORMAL
    if (ap_name == "star") then
      return zeri_pan_answer_bazhiziwei(ap_name,nPanType,hUserInfo,hUserDefData,hParAll)
    elsif (ap_name == "eightword") then
      return zeri_pan_answer_bazhiziwei(ap_name,nPanType,hUserInfo,hUserDefData,hParAll)
    elsif (ap_name == "wannianli") then
      return zeri_pan_answer_wannianli(ap_name,nPanType,hUserInfo,hUserDefData,hParAll)
    else
      return false,{}
    end
  end
  def zeri_pan_answer_bazhiziwei(ap_name,nPanType,hUserInfo,hUserDefData,hParAll)
    a = Array.new

    zeri_pars = hUserDefData["zeri_pars"]
    days = zeri_pars["days"]
    nWYear,nWMonth,nWDate = hUserInfo[Cfate::WYear],hUserInfo[Cfate::WMonth],hUserInfo[Cfate::WDate]
    (0...days).each do |n_day|
      (0...12).each do |nTime|
        hUserInfo[Cfate::WYear] = nWYear
        hUserInfo[Cfate::WMonth] = nWMonth
        hUserInfo[Cfate::WDate] = nWDate
        hUserInfo[Cfate::WHour] = Xdate.ETime2Hour(nTime)
        bFind,answer = find_zeri_pan_answer_bazhiziwei(ap_name,nPanType,hUserInfo,hUserDefData,hParAll)
        if (bFind) then
          a.push(answer)
        end
      end
      nWYear,nWMonth,nWDate = Xdate.NextWDate(nWYear,nWMonth,nWDate)
    end

    return a
  end
  def find_zeri_pan_answer_bazhiziwei(ap_name,nPanType,hUserInfo,hUserDefData,hParAll)
    if (ap_name == "star") then
      return find_ziwei_pan_answer_ziwei(nPanType,hUserInfo,hUserDefData,hParAll)
    elsif (ap_name == "eightword") then
      return find_ziwei_pan_answer_bazhi(nPanType,hUserInfo,hUserDefData,hParAll)
    else
      return false,{}
    end
  end
  def find_ziwei_pan_answer_ziwei(nPanType,hUserInfo,hUserDefData,hParAll)
    zeri_pars = hUserDefData["zeri_pars"]
    oStar = Star.new
    oStar.g_GetPanInfo(nPanType,hUserInfo,hUserDefData,hParAll)
    hOut = Hash.new
    bFind = false
    if (hUserDefData["zeri_pars"] == nil || oStar.match_zeri_pars(zeri_pars)) then
      if (hUserInfo[Cfate::CalType] == Xdate::CT_SOLAR) then
        hOut["title"] = "#{oStar.uig_W_BirthDate_ganzhishi_Str()} #{oStar.uig_bS_str()}"
      else
        hOut["title"] = "#{Pm.GetStr("ifate.wannianli.nong")}#{oStar.uig_E_BirthDate_ganzhi_Str()} #{oStar.uig_bS_str()}"
      end
      hOut["timestamp"] = "#{oStar.uig_W_timestamp()}#{oStar.uig_bS_Ui_Val()}"
      hOut["sex"] = oStar.uig_bS_Ui_Val()
      hOut["calType"] = Xdate::CT_SOLAR
      hOut["customer_timestamp"] = "#{oStar.uig_W_timestamp()}#{oStar.uig_bS_Ui_Val()}"
      bFind = true
    end

    return bFind,hOut
  end
  def find_ziwei_pan_answer_bazhi(nPanType,hUserInfo,hUserDefData,hParAll)
    zeri_pars = hUserDefData["zeri_pars"]
    oEightword = Eightword.new
    oEightword.g_GetPanInfo(nPanType,hUserInfo,hUserDefData,hParAll)
    hOut = Hash.new
    bFind = false
    if (hUserDefData["zeri_pars"] == nil || oEightword.match_zeri_pars(zeri_pars)) then
      if (hUserInfo[Cfate::CalType] == Xdate::CT_SOLAR) then
        hOut["title"] = "#{oEightword.uig_W_BirthDate_ganzhishi_Str()} #{oEightword.uig_bS_str()}"
      else
        hOut["title"] = "#{Pm.GetStr("ifate.wannianli.nong")}#{oEightword.uig_E_BirthDate_ganzhi_Str()} #{oEightword.uig_bS_str()}"
      end
      hOut["timestamp"] = "#{oEightword.uig_W_timestamp()}#{oEightword.uig_bS_Ui_Val()}"
      hOut["sex"] = oEightword.uig_bS_Ui_Val()
      hOut["calType"] = Xdate::CT_SOLAR
      hOut["customer_timestamp"] = "#{oEightword.uig_W_timestamp()}#{oEightword.uig_bS_Ui_Val()}"
      bFind = true
    end

    return bFind,hOut
  end
  def zeri_pan_answer_wannianli(ap_name,nPanType,hUserInfo,hUserDefData,hParAll)
    zeri_pars = hUserDefData["zeri_pars"]
    oWannianli = Wannianli.new
    oWannianli.g_GetPanInfo(nPanType,hUserInfo,hUserDefData,hParAll)
    max_answer = Wannianli.max_answer(zeri_pars)
    # 先找到60天內符合條件的
    aUserInfo = oWannianli.find_first_match_userinfos(zeri_pars,hUserInfo)
    if (aUserInfo.length == 0) then
      return aUserInfo
    end

    aOut = Array.new
    first_length = aUserInfo.length
    aLastOut = assign_zeri_wannianli_answer(oWannianli,aUserInfo,zeri_pars)
    aOut += aLastOut
    (0...((max_answer-1)/first_length).to_i).each do |i|
      aUserInfo = oWannianli.find_next_match_userinfos(zeri_pars,aUserInfo)
      aOut += assign_zeri_wannianli_answer(oWannianli,aUserInfo,zeri_pars)
    end

    return aOut[0,10]
  end
  def assign_zeri_wannianli_answer(oWannianli,aUserInfo,zeri_par)
    aOut = Array.new
    aUserInfo.each do |hUserInfo|
      hOut = Hash.new
      hOut["title"] = make_zeri_wannianli_title(oWannianli,hUserInfo)
      hOut["timestamp"] = oWannianli.uig_W_timestamp(hUserInfo)
      hOut["calType"] = Xdate::CT_SOLAR
      hOut["lifa"] = zeri_par["lifa"]
      aOut.push(hOut)
    end
    return aOut
  end
  def make_zeri_wannianli_title(oWannianli,hUserInfo)
    s = oWannianli.uig_W_YearMonthDateStr(hUserInfo)
    s += "("
    s += oWannianli.uig_W_WeekDayStr(hUserInfo)
    s += ")"
    s += Cfate.GetSpace(1)
    s += oWannianli.uig_W_Lunar8Words_Str(hUserInfo)
    s += Cfate.GetSpace(1)
    s += oWannianli.uig_W_Seg8Words(hUserInfo)
    return s
  end
  def zeri_pan_userinfo_make_timestamp()
  end
  def Zeri.get_zeri_str(key)
    return Pm.GetStr("zeri.#{key}")
  end
  def Zeri.get_ziwei_str(key)
    return Zeri.get_zeri_str("ziwei.#{key}")
  end
  def Zeri.get_ziwei_taitou_str(key)
    return Zeri.get_ziwei_str("taitou.#{key}")
  end
  def Zeri.get_ziwei_gongwei_str(key)
    return Zeri.get_ziwei_str("gongwei.#{key}")
  end
  def Zeri.get_ziwei_gongwei_id_str(id)
    return Zeri.get_ziwei_str("gongwei.gongwei_#{id}")
  end
  def Zeri.get_ziwei_sihua_str(key)
    return Zeri.get_ziwei_str("sihua.#{key}")
  end
  def Zeri.get_ziwei_sihua_id_str(id)
    return Zeri.get_ziwei_str("sihua.sihua_#{id}")
  end
  def Zeri.get_ziwei_dizhi_str(key)
    return Zeri.get_ziwei_str("dizhi.#{key}")
  end
  def Zeri.get_ziwei_dizhi_id_str(id)
    return Zeri.get_ziwei_str("dizhi.dizhi_#{id}")
  end

  def Zeri.find_answers(ap_name,user_id)
    tdb_key = "#{ap_name}_zeri_pan_answers_#{user_id}"
    tdb = Testdb.check_key(tdb_key).last
    if (tdb == nil) then
      tdb = Testdb.new
      tdb.key = tdb_key
      tdb.save!
    end
    return tdb.hValue
  end
  def Zeri.save_answers(ap_name,user_id,answers)
    tdb_key = "#{ap_name}_zeri_pan_answers_#{user_id}"
    tdb = Testdb.check_key(tdb_key).last
    if (tdb == nil) then
      tdb = Testdb.new
      tdb.key = tdb_key
    end
    tdb.hValue = answers
    tdb.save!
    return tdb.id
  end
  def Zeri.check_in_answers(ap_name,user_id,timestamp)
    answers = Zeri.find_answers(ap_name,user_id)
    answers.each do |answer|
      if (answer["timestamp"] == timestamp) then
        return answer
      end
    end
    return nil
  end
  def Zeri.find_from_ifatepar(user_id,ap_name)
    zeri_ap_name = Zeri.zeri_ap_name(ap_name)
    ifatepar = IfatePar.check_userid(user_id).check_apname(zeri_ap_name).last
    if (ifatepar == nil) then
      ifatepar = IfatePar.new
      ifatepar.user_id = user_id
      ifatepar.ap_name = zeri_ap_name
      ifatepar.hPars = []
    end
    return ifatepar.hPars
  end
  def Zeri.save_to_ifatepar(ifatepar_zeri_index,user_id,ap_name,hParAll)
    zeri_ap_name = Zeri.zeri_ap_name(ap_name)
    ifatepar = IfatePar.check_userid(user_id).check_apname(zeri_ap_name).last
    if (ifatepar == nil) then
      ifatepar = IfatePar.new
      ifatepar.user_id = user_id
      ifatepar.ap_name = zeri_ap_name
      ifatepar.hPars = []
    end
    ifatepar_par = ifatepar.hPars
    if (ifatepar_zeri_index == -1) then
      # index,same = check_ifatepar_same(ifatepar_par,hParAll["chaxuncanshuchucunmingcheng"])
      # if (same) then
      #   # 可能會重復輸入所以覆蓋
      #   ifatepar_par[index] = hParAll
      # else
        ifatepar_par.push(hParAll)
        ifatepar_zeri_index = ifatepar_par.length - 1
      # end
    else
      ifatepar_par[ifatepar_zeri_index] = hParAll
    end

    ifatepar.hPars = ifatepar_par
    ifatepar.save!
    return ifatepar_par,ifatepar_zeri_index
  end
  def Zeri.delete_from_ifatepar(ifatepar_zeri_index,user_id,ap_name,hParAll)
    zeri_ap_name = Zeri.zeri_ap_name(ap_name)
    ifatepar = IfatePar.check_userid(user_id).check_apname(zeri_ap_name).last
    if (ifatepar == nil) then
      return [],-1
    end
    ifatepar_par = ifatepar.hPars
    if (ifatepar_zeri_index >= 0) then
      ifatepar_par.delete_at(ifatepar_zeri_index)
    end

    ifatepar.hPars = ifatepar_par
    ifatepar.save!
    new_idx = ifatepar_par.length > 0 ? 0 : -1
    return ifatepar_par,new_idx
  end
  def Zeri.check_ifatepar_same(ifatepar_par,chaxuncanshuchucunmingcheng)
    if (ifatepar_par.length == 0) then
      return nil,false
    end
    index = ifatepar_par.length - 1
    return index,(ifatepar_par[index]["chaxuncanshuchucunmingcheng"] == chaxuncanshuchucunmingcheng)
  end
  def Zeri.zeri_ap_name(ap_name)
    zeri_ap_name = "#{ap_name}_zeri"
    return zeri_ap_name
  end

  # for xuexi
  def Zeri.set_xuexi_zeri_pars(pars,sex,y=nil,m=nil,d=nil,h=nil,min=nil)
    y = rand(20) + 2000 if y == nil
    m = rand(12) + 1 if m == nil
    d = rand(27) + 1 if d == nil
    ey,em,ed,el = Xdate.West2East(y,m,d)
    h = rand(60) if h == nil
    min = rand(60) if min == nil
    zeri_pars = {"need_seven_days"=>true,
                "UserInfo"=>{"user_id"=>0, "Sex"=>sex, "CalType"=>Xdate::CT_SOLAR, "WYear"=>y, "WMonth"=>m, "WDate"=>d, "EYear"=>ey, "ELeapMonth"=>el, "EMonth"=>em, "EDate"=>ed, "WHour"=>h, "Name"=>"", "WMinute"=>min, "remote_ip"=>"127.0.0.1"},
                "pars"=>pars,
                "chaxuncanshuchucunmingcheng"=>"noname3",
                "days"=>7,"need_count" => 1}
    return zeri_pars
  end
  def Zeri.make_xuexi_ziwei_par(gongwei,xingyao1_jiaxing,xingyao1_yixing,xingyao2_jiaxing,xingyao2_yixing,sihua_shengnian,sihua_zhihua)
    par = {
                "seven_days"=>true, "gongweitiaojian_xingyao1"=>0, "gongweitiaojian_xingyao2"=>0,
                "gongweitiaojian_sihua_shengnian"=>0, "gongweitiaojian_sihua_zhihua"=>0,
                "gongwei"=> gongwei,
                "sihua_shengnian"=> sihua_shengnian, "sihua_zhihua"=> sihua_zhihua,
                "xingyao1_jiaxing"=> xingyao1_jiaxing, "xingyao1_yixing"=>xingyao1_yixing,
                "xingyao2_jiaxing"=>xingyao2_jiaxing, "xingyao2_yixing"=>xingyao2_yixing,
                "minggongweizhi"=>0
            }
    return par
  end

end
