require("Star.rb")

# For Star
class PanWeb
  @StarInfo
  @PanInfo
  @m_nPanType
  @AllPanInfo

  #House var
  @AStar   #include star name,miowong,four hua,house self four hua
  @BStar   #include star name,miowong if any
  @Doctor  #doctor name
  @God     #God name
  @YearGod #Year God name
  @YearStar #Year Star name
  @LargeSam
  @SmallSam
  @NormalHouseName
  @FlowHouseName
  @HouseEarth
  @HouseSky
  @HouseFive

  MAX_STAR = 8


  def pw_Star_getPan(nPanType,hUserInfo,hUserAskData,hUserType)
    @m_nPanType = nPanType
    @Star = Star.new
    @StarInfo = @Star.g_GetStarPanInfo(nPanType,hUserInfo,hUserAskData,hUserType)
    @AllPanInfo = @StarInfo[Star::U_PAN_INFO]
    @PanInfo = @AllPanInfo[nPanType]
    return pw_Star_createWebPan(nPanType)
  end

  def pw_Star_createWebPan(nPanType)

    # 宮表格開始
    sWebPan = pw_Star_GetPan_table()

    #表格置中
    sWebPan += PanWeb.pw_GetCenter()

    nTrFlag = [1,0,0,-1,1,-1,1,-1,1,0,0,-1]
    nMiddle = [0,0,0,0,1,0,0,0,0,0,0,0]
    (0..11).each do |nHouseDisplayIndex|
      # 宮表格 TR 開始
      if (nTrFlag[nHouseDisplayIndex] == 1) then
        sWebPan += PanWeb.pw_GetPan_table_tr()
      end
      sWebPan += pw_Star_createWebPan_House(nHouseDisplayIndex)
      if (nMiddle[nHouseDisplayIndex] == 1) then
        sWebPan += pw_Star_createWebPan_Middle()
      end
      if (nTrFlag[nHouseDisplayIndex] == -1) then
        sWebPan += PanWeb.pw_GetPan_table_tr_end()
      end
    end
    # 宮表格結束
    sWebPan += PanWeb.pw_GetPan_table_end()

    return sWebPan
  end

  def pw_Star_createWebPan_Middle()
    sBuf = PanWeb.pw_GetPan_table_td_2(2,2)
    sBuf += "<FONT size=3 FACE=Arial line-height=20>"
    sBuf += PanWeb.pw_GetPan_br()
    sBuf += pw_Star_PersonalInfo()
    sBuf += PanWeb.pw_Get_Font_end()
    sBuf += PanWeb.pw_GetPan_br()
    sBuf += PanWeb.pw_GetCenter()
    sBuf += pw_GetCompanyInfo()
    sBuf += PanWeb.pw_GetPan_table_td_end()
  end

  def pw_Star_PersonalInfo()
    nSpaceCnt = 2
    sBuf = PanWeb.pw_Star_GetSpan(14,2,20)
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_Name()
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_IYMF()
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_Animal()
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_SolarBirth()
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_LunarBirth()
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_GanZhiBirth()
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_FiveType()
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_LifeHouse()
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_BodyHouse()
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_Life()
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_Body()
    sBuf += Cfate.GetSpace(nSpaceCnt) + pw_Star_PI_God()
    sBuf += PanWeb.pw_Star_GetSpanEnd()
  end

  def PanWeb.pw_Star_GetSpan(fSize,letterSpace,lineHeight)
    "<span style='font-family:Arial;font-size:#{fSize}px;font-style:normal;font-weight:normal;text-decoration:none;text-transform:none;letter-spacing:#{letterSpace}px;line-height:#{lineHeight}px;color:000000;'>"
  end

  def PanWeb.pw_Star_GetSpanEnd()
    "</span>"
  end
  # 姓名
  def pw_Star_PI_Name()
    sBuf = Pm.GetStrWithColon("IDS_S_UI_NAME")

    if (@Star.gPI_GetName() != nil) then
      sBuf += @Star.gPI_GetName()
    end

    sBuf += PanWeb.pw_GetPan_br()
  end

  # 命造(陰男,陽男,陰女,陽女)
  def pw_Star_PI_IYMF()
    sBuf = Pm.GetStrWithColon("IDS_S_UI_IYMF")
    sBuf += @Star.gPI_GetIYMF()
    sBuf += Cfate.GetSpace(1)
  end

  #　生肖:兔<BR>
  def pw_Star_PI_Animal()
    sBuf = Pm.GetStrWithColon("IDS_S_UI_ANIMAL")
    sBuf += @Star.gPI_GetAnimal()
    sBuf += PanWeb.pw_GetPan_br()
  end

  #　　陽曆　1963年　2月　23日　4時<BR>
  def pw_Star_PI_SolarBirth()
    sBuf = Pm.GetStr("IDS_S_UI_SOLAR_CAL")
    sBuf += @Star.gPI_GetWBirthDateStr()
    sBuf += PanWeb.pw_GetPan_br()
  end

  #     農曆　1963年　1月　30日　寅時<BR>
  def pw_Star_PI_LunarBirth()
    sBuf = Pm.GetStr("IDS_S_UI_LUNAR_CAL")
    sBuf += @Star.gPI_GetEBirthDateStr()
    sBuf += PanWeb.pw_GetPan_br()
  end

  #     　癸卯年 甲寅月 丁酉日 壬寅時<BR>
  def pw_Star_PI_GanZhiBirth()
    sBuf = @Star.gPI_GetGanZhiBirthDateStr()
    sBuf += PanWeb.pw_GetPan_br()
  end

  #     　　命局: 金四局<BR>
  def pw_Star_PI_FiveType()
    sBuf = Pm.GetStrWithColon("IDS_S_FIVE_TYPE")
    sBuf += @Star.gPI_GetFiveType()
    sBuf += PanWeb.pw_GetPan_br()
  end

  #     　　　命宮: 子<BR>
  def pw_Star_PI_LifeHouse()
    sBuf = Pm.GetStrWithColon("IDS_S_LIFE_HOUSE")
    sBuf += @Star.gPI_GetLifeHouse()
    sBuf += PanWeb.pw_GetPan_br()
  end

  #     　身宮: 辰<BR>
  def pw_Star_PI_BodyHouse()
    sBuf = Pm.GetStrWithColon("IDS_S_BODY_HOUSE")
    sBuf += @Star.gPI_GetBodyHouse()
    sBuf += PanWeb.pw_GetPan_br()
  end

  #   　命主: 貪狼<BR>
  def pw_Star_PI_Life()
    sBuf = Pm.GetStrWithColon("IDS_S_LIFE")
    sBuf += @Star.gPI_GetLife()
    sBuf += PanWeb.pw_GetPan_br()
  end

  #   　身主: 天同<BR>
  def pw_Star_PI_Body()
    sBuf = Pm.GetStrWithColon("IDS_S_BODY")
    sBuf += @Star.gPI_GetBody()
    sBuf += PanWeb.pw_GetPan_br()
  end

  #   　子年斗君: 寅
  def pw_Star_PI_God()
    sBuf = Pm.GetStrWithColon("IDS_S_GOD")
    sBuf += @Star.gPI_GetGod()
  end

  #     <font size=5 color=#DAA520 face=Arial><B><i> 公司 </i><font color=RED face=標楷體>  命理網</B><BR>
  def pw_GetCompanyInfo()
    sBuf = pw_CI_Company()
    sBuf += pw_CI_Web()
  end

  def pw_CI_Company()
    sBuf = "<FONT size=5 color=#DAA520 face=Arial>"
    sBuf += PanWeb.pw_GetB()
      sBuf += PanWeb.pw_Get_i()
        sBuf += Pm.GetStr("IDS_S_COMPANY_NAME")
      sBuf += PanWeb.pw_Get_i_end()
      sBuf += PanWeb.pw_Get_Font_end()

      sFace = Pm.GetStr("IDS_A_KEY_FONT_BIAO")
      sBuf += "<FONT color=RED face=#{sFace}>"
      sBuf += Pm.GetStr("IDS_S_PROFATE")
      sBuf += PanWeb.pw_Get_Font_end()
    sBuf += PanWeb.pw_GetB_end()
    sBuf += PanWeb.pw_GetPan_br()
  end

  def pw_CI_Web()
    sBuf = "<FONT size=4 color=#0000ff FACE=arial>"
    sBuf += "<a href='" + Pm.getSystemValue("MEEN_URL") + "' target='_blank'>" + Pm.getSystemValue("MEEN_URL") + "</a>"
    sBuf += PanWeb.pw_GetPan_br()
    sBuf += "<a href='" + Pm.getSvUrl("FREE_STAR_DESC_URL") + "' target='_blank'>" + Pm.GetStr("IDS_S_CI_FREE_WEB_INFO") + "</a>"

    sBuf += PanWeb.pw_Get_Font_end()
    sBuf += PanWeb.pw_Get_a_end()
  end

  def pw_Star_createWebPan_House(nHouseDisplayIndex)
    nHousePanIndex = [6,7,8,9,5,10,4,11,3,2,1,12]
    nEarth = nHousePanIndex[nHouseDisplayIndex]
    # 宮表格td開始
    sBuf = PanWeb.pw_GetPan_table_td()

    # 星名拆成2個上下兩個字來顯示
    (0..1).each do |nWord|
      #博士星
      sBuf += pw_Star_GetDoctor(nEarth,nWord)
      #長生
      sBuf += pw_Star_GetGod(nEarth,nWord)
      #甲級星
      sBuf += pw_Star_GetAStar(nEarth,nWord)
    end

    #廟旺落陷
    sBuf += pw_Star_GetMioWong(nEarth)

    #祿權科忌
    sBuf += pw_Star_GetFourHua(nEarth)

    # 乙級星星名拆成2個上下兩個字來顯示
    (0..1).each do |nWord|
      #將星
      sBuf += pw_Star_GetYearStar(nEarth,nWord)
      #歲建星
      sBuf += pw_Star_GetYearGod(nEarth,nWord)
      #乙級星
      sBuf += pw_Star_GetBStar(nEarth,nWord)
    end

    # 接下來的要置中
    sBuf += PanWeb.pw_GetCenter()
    sBuf += pw_Star_GetBottomInfo(@m_nPanType,nEarth)

    #宮結束
    sBuf += PanWeb.pw_GetPan_table_td_end()

    return sBuf
  end

  def pw_Star_GetBottomInfo(nPanType,nEarth)
    case (nPanType)
    when Cfate::PAN_NORMAL then
      sBuf = pw_Star_GetBottomInfo_Normal(nEarth,nPanType)
    when Cfate::PAN_TENYEAR then
      sBuf = pw_Star_GetBottomInfo_TenYear(nEarth,nPanType)
    when Cfate::PAN_FLOWYEAR then
      sBuf = pw_Star_GetBottomInfo_FlowYear(nEarth,nPanType)
    when Cfate::PAN_FLOWMONTH then
      sBuf = pw_Star_GetBottomInfo_FlowMonth(nEarth,nPanType)
    when Cfate::PAN_FLOWDATE then
      sBuf = pw_Star_GetBottomInfo_FlowDate(nEarth,nPanType)
    when Cfate::PAN_FLOWTIME then
      sBuf = pw_Star_GetBottomInfo_FlowTime(nEarth,nPanType)
    when Cfate::PAN_FLOWMIN then
      sBuf = pw_Star_GetBottomInfo_FlowMin(nEarth,nPanType)
    else
      sBuf =  ""
    end
    return sBuf
  end

  def pw_Star_GetBottomInfo_Normal(nEarth,nPanType=nil)
    sBuf = PanWeb.pw_GetPan_br()
    sBuf += pw_Star_GetSmallSan(nEarth)

    sBuf += pw_Star_GetLast2Line_Normal(nEarth)

    sBuf += pw_Star_GetLastLine_Normal(nEarth)
    return sBuf
  end

  def pw_Star_GetLast2Line_Normal(nEarth)
    sBuf = Cfate.GetSpace(3)
    sBuf += PanWeb.GetSmallSpace(1)
    sLargeSan = pw_Star_GetLargeSan(nEarth)
    sBuf += sLargeSan
    sBuf += Cfate.GetSpace(2)
    sBuf += pw_Star_GetHouseFive(nEarth)
    sBuf += PanWeb.GetSmallSpace(1)
    sBuf += PanWeb.pw_GetPan_br()
  end

  # 大限 <FONT COLOR=BLACK FACE=Arial>74 --- 83<BR>
  # "<FONT COLOR=BLACK FACE=Arial>" + "74 --- 83" + "<BR>"
  def pw_Star_GetLargeSan(nEarth)
    sBuf = "<FONT COLOR=BLACK FACE=Arial>"
#   sBuf += PanWeb.GetLarge2(@StarInfo,nEarth)
    sBuf += PanWeb.GetLarge(@Star,nEarth)
  end

  # 小限 <FONT COLOR=GRAY SIZE=1>5 17 29 41 53 65 77 89<BR>
  def pw_Star_GetSmallSan(nEarth)
    sBuf = "<FONT COLOR=GRAY SIZE=1>"
    sBuf += PanWeb.GetSmall(@Star,nEarth)
    sBuf += PanWeb.pw_GetPan_br()
  end

  def pw_Star_GetLastLine_Normal(nEarth)
    if (Star.HouseIndex2Earth(@Star.cp_getBody()) == nEarth) then
      sBuf = pw_Star_GetBody()
    else
      sBuf = Cfate.GetSpace(3)
    end
    sBuf += pw_Star_GetHouseName(nEarth)
    sBuf += Cfate.GetSpace(1)
    sBuf += pw_Star_GetHouseSkyName(nEarth)
    sBuf += pw_Star_GetHouseEarthName(nEarth)
  end

  def pw_Star_GetHouseFive(nEarth)
    #sBuf = PanWeb.pw_GetPan_table_td_2(1,2)
    sBuf = "<FONT COLOR=BLACK FACE=Arial>"
    sBuf += PanWeb.GetHouseFive(@Star,@m_nPanType,nEarth)
    #sBuf += PanWeb.pw_GetPan_table_td_end()
  end

  def pw_Star_GetBottomInfo_TenYear(nEarth,nPanType=nil)
    sBuf = PanWeb.pw_GetPan_br()
    sBuf += pw_Star_GetSmallSan(nEarth)

    sBuf += pw_Star_GetLast2Line_TenYear(nEarth)

    sBuf += pw_Star_GetLastLine_Others(nEarth)
    return sBuf
  end

  def pw_Star_GetLast2Line_TenYear(nEarth)
    sBuf = Cfate.GetSpace(3)
    sBuf += PanWeb.GetSmallSpace(1)
    sLast2 = pw_Star_GetLargeSan(nEarth)
    sBuf += sLast2
    sBuf += Cfate.GetSpace(2)
    sBuf += pw_Star_GetHouseFive(nEarth)
    sBuf += PanWeb.GetSmallSpace(1)
    sBuf += PanWeb.pw_GetPan_br()
  end

  def pw_Star_GetLastLine_Others(nEarth)
    #sBuf = Cfate.GetSpace(3)
    sBuf = pw_Star_GetHouseName(nEarth)
    sBuf += pw_Star_GetNormalHouseName(nEarth)
    #sBuf += Cfate.GetSpace(1)
    sBuf += pw_Star_GetHouseSkyName(nEarth)
    sBuf += pw_Star_GetHouseEarthName(nEarth)
    return sBuf
  end

  def pw_Star_GetBottomInfo_FlowYear(nEarth,nPanType=nil)
    sBuf = PanWeb.pw_GetPan_br()
    sBuf += pw_Star_GetFlowYear_SmallSan(nEarth,nPanType)
    sBuf += pw_Star_GetLast2Line_FlowYear(nEarth,nPanType)
    sBuf += pw_Star_GetLastLine_Others(nEarth)
    return sBuf
  end

  def pw_Star_GetFlowYear_SmallSan(nEarth,nPanType=nil)
    sSmallSan = PanWeb.GetFlowYear_SmallSan(@Star,nEarth,nPanType)

    sBuf = ""
    if (sSmallSan != nil)
      sBuf = "<FONT COLOR=RED FACE=Arial>"
      sBuf += sSmallSan
    end

    sBuf += PanWeb.pw_GetPan_br()
  end

  def pw_Star_GetLast2Line_FlowYear(nEarth,nPanType=nil)
    sBuf = Cfate.GetSpace(3)
    sBuf += PanWeb.GetSmallSpace(1)
    sLast2 = pw_Star_GetFlowYear(nEarth,nPanType)
    sBuf += sLast2
    sBuf += Cfate.GetSpace(2)
    sBuf += pw_Star_GetHouseFive(nEarth)
    sBuf += PanWeb.GetSmallSpace(1)
    sBuf += PanWeb.pw_GetPan_br()
  end

  def pw_Star_GetFlowYear(nEarth,nPanType=nil)
    bFlowHouse,sInfo = PanWeb.GetFlowYear(@Star,nEarth,nPanType)

    if (bFlowHouse)
      sBuf = "<FONT COLOR=RED FACE=Arial>"
    else
      sBuf = "<FONT COLOR=BLACK FACE=Arial>"
    end

    sBuf += sInfo

    return sBuf
  end

  def pw_Star_GetBottomInfo_FlowMonth(nEarth,nPanType=nil)
    sBuf = PanWeb.pw_GetPan_br()
    sBuf += pw_Star_GetLast2Line_FlowMonth(nEarth)
    sBuf += pw_Star_GetLastLine_Others(nEarth)
    return sBuf
  end

  def pw_Star_GetLast2Line_FlowMonth(nEarth)
    sBuf = Cfate.GetSpace(3)
    sBuf += PanWeb.GetSmallSpace(1)
    sLast2 = pw_Star_GetFlowMonth(nEarth)
    sBuf += sLast2
    sBuf += Cfate.GetSpace(2)
    sBuf += pw_Star_GetHouseFive(nEarth)
    sBuf += PanWeb.GetSmallSpace(1)
    sBuf += PanWeb.pw_GetPan_br()
  end

  def pw_Star_GetFlowMonth(nEarth)
    bFlowHouse,sInfo = PanWeb.GetFlowMonth(@Star,nEarth)

    if (bFlowHouse)
      sBuf = "<FONT COLOR=RED FACE=Arial>"
    else
      sBuf = "<FONT COLOR=BLACK FACE=Arial>"
    end

    sBuf += sInfo

    return sBuf
  end

  def pw_Star_GetBottomInfo_FlowDate(nEarth,nPanType=nil)
    sBuf = PanWeb.pw_GetPan_br()
    sBuf += pw_Star_GetLast2Line_FlowDate(nEarth)
    sBuf += pw_Star_GetLastLine_Others(nEarth)
    return sBuf
  end

  def pw_Star_GetLast2Line_FlowDate(nEarth)
    sLast2,bFlow = pw_Star_GetFlowDate(nEarth)
    if (bFlow) then
      sBuf = Cfate.GetSpace(2)
    else
      sBuf = Cfate.GetSpace(3)
    end
    sBuf += PanWeb.GetSmallSpace(1)
    sBuf += sLast2
    if (bFlow) then
      sBuf += Cfate.GetSpace(1)
    else
      sBuf += Cfate.GetSpace(2)
    end
    sBuf += pw_Star_GetHouseFive(nEarth)
    sBuf += PanWeb.GetSmallSpace(1)
    sBuf += PanWeb.pw_GetPan_br()
  end

  def pw_Star_GetFlowDate(nEarth)
    bFlowHouse,flowDate,aInfo = PanWeb.GetFlowDate(@Star,nEarth)

    if (bFlowHouse)
      sBuf = "<FONT size=1 COLOR=BLACK FACE=Arial>"
    else
      sBuf = "<FONT size=1 COLOR=BLACK FACE=Arial>"
    end
    if (bFlowHouse) then
      sBuf += Pm.GetStr("IDS_S_FLOW_DATE")
    end

    aInfo.each do |nNum|
      if (nNum != nil) then
        if (flowDate == nNum) then
          sBuf += "<FONT size=2 COLOR=RED FACE=Arial>"
          sBuf += nNum.to_s
          sBuf += PanWeb.pw_Get_Font_end()
        else
          sBuf += nNum.to_s
        end
        sBuf += PanWeb.GetSmallSpace(1)
      end
    end
    sBuf += Pm.GetStr("IDS_X_DAY")

    return sBuf,bFlowHouse
  end

  def pw_Star_GetBottomInfo_FlowTime(nEarth,nPanType=nil)
    sBuf = PanWeb.pw_GetPan_br()
    sBuf += pw_Star_GetLast2Line_FlowTime(nEarth)
    sBuf += pw_Star_GetLastLine_Others(nEarth)
    return sBuf
  end

  def pw_Star_GetLast2Line_FlowTime(nEarth)
    sBuf = Cfate.GetSpace(3)
    sBuf += PanWeb.GetSmallSpace(1)
    sLast2 = pw_Star_GetFlowTime(nEarth)
    sBuf += sLast2
    sBuf += Cfate.GetSpace(2)
    sBuf += pw_Star_GetHouseFive(nEarth)
    sBuf += PanWeb.GetSmallSpace(1)
    sBuf += PanWeb.pw_GetPan_br()
  end

  def pw_Star_GetFlowTime(nEarth)
    bFlowHouse,sInfo = PanWeb.GetFlowTime(@Star,nEarth)

    if (bFlowHouse)
      sBuf = "<FONT COLOR=RED FACE=Arial>"
    else
      sBuf = "<FONT COLOR=BLACK FACE=Arial>"
    end

    sBuf += sInfo

    return sBuf
  end

  def pw_Star_GetBottomInfo_FlowMin(nEarth,nPanType=nil)
    sBuf = PanWeb.pw_GetPan_br()
    sBuf += pw_Star_GetLast2Line_FlowMin(nEarth)
    sBuf += pw_Star_GetLastLine_Others(nEarth)
    return sBuf
  end

  def pw_Star_GetLast2Line_FlowMin(nEarth)
    sLast2,bFlowHouse = pw_Star_GetFlowMin(nEarth)
    if (bFlowHouse)
      sBuf = PanWeb.GetSmallSpace(1)
    else
      sBuf = Cfate.GetSpace(1)
    end
    #sBuf += PanWeb.GetSmallSpace(1)
    sBuf += sLast2
    if (bFlowHouse)
      sBuf += PanWeb.GetSmallSpace(1)
    else
      sBuf += Cfate.GetSpace(1)
    end
    sBuf += pw_Star_GetHouseFive(nEarth)
    sBuf += PanWeb.GetSmallSpace(1)
    sBuf += PanWeb.pw_GetPan_br()
  end

  def pw_Star_GetFlowMin(nEarth)
    bFlowHouse,sInfo = PanWeb.GetFlowMin(@Star,nEarth)

    if (bFlowHouse)
      sBuf = "<FONT COLOR=RED FACE=Arial>"
    else
      sBuf = "<FONT COLOR=BLACK FACE=Arial>"
    end

    sBuf += sInfo

    return sBuf,bFlowHouse
  end

  def pw_Star_GetPan_table()
    sBuf = "<TABLE ALIGN=CENTER BORDER=1 name=TblStar id=TblStar>"
  end


  # Line 1 : <FONT FACE=細明體 SIZE=2><FONT COLOR=DARKGREEN>喜長<FONT COLOR=black>　　　　　天天天<BR>
  # Line 2: <FONT COLOR=DARKGREEN>神生<FONT COLOR=black>　　　　　鉞馬府<BR>　　　　　　　
  #   sValue = ["喜","神"]
  #   sBuf = "<FONT FACE=細明體 SIZE=2><FONT COLOR=DARKGREEN>" + sValue[nWord]
  def pw_Star_GetDoctor(nEarth,nWord)
#   sValue = PanWeb.GetDoctorName2(@PanInfo,nEarth)
    sValue = PanWeb.GetDoctorName(@Star,@m_nPanType,nEarth)

    faceName = Pm.GetStr("IDS_A_KEY_FONT_MIN")
    sBuf = "<FONT  FACE=#{faceName} SIZE=2><FONT COLOR=DARKGREEN>" + sValue[nWord]
  end

  #   sValue = ["長","生"]
  #   sBuf = "<FONT FACE=細明體 SIZE=2><FONT COLOR=DARKGREEN>" +  sValue[nWord]
  def pw_Star_GetGod(nEarth,nWord)
#   sValue = PanWeb.GetGodName2(@PanInfo,nEarth)
    sValue = PanWeb.GetGodName(@Star,@m_nPanType,nEarth)
    faceName = Pm.GetStr("IDS_A_KEY_FONT_MIN")
#   sBuf = "<FONT FACE=#{faceName} SIZE=2><FONT COLOR=DARKGREEN>" +  sValue[nWord]
    sBuf = "<FONT COLOR=DARKGREEN>" +  sValue[nWord]
  end

  #   sValue = ["　　　　　天天天","　　　　　鉞馬府"]
  def pw_Star_GetAStar(nEarth,nWord)
#   sValue = PanWeb.GetAStarName2(@PanInfo,nEarth,PanWeb::MAX_STAR)
    sValue = PanWeb.GetAStarName(@Star,@m_nPanType,nEarth,PanWeb::MAX_STAR)
    sBuf = "<FONT COLOR=black>" + sValue[nWord]
    sBuf += PanWeb.pw_GetPan_br()
  end

  def pw_Star_MakeMioWongStr_Steven(sStr,nValArray)
    sBuf = ""
    (1..nValArray.length).each do |i|
      case nValArray[i - 1]
      when 1 then
        sBuf += "<FONT COLOR=RED>"
      when 2 then
        sBuf += "<FONT COLOR=RED>"
      when 3 then
        sBuf += "<FONT COLOR=DARKGREEN>"
      when 4 then
        sBuf += "<FONT COLOR=DARKGREEN>"
      when 5 then
        sBuf += "<FONT COLOR=BLUE>"
      else
      end
      sBuf += sStr[i - 1]
    end

    return sBuf
  end

  def pw_Star_MakeMioWongStr_Lin(sStr,nValArray)
  end

  # 博士及長生2個空格，後面接8個全形中文字，沒有就用全形空白，一排10個中文字
  #　　　　　　　<FONT COLOR=RED>旺<FONT COLOR=DARKGREEN>平<FONT COLOR=DARKGREEN>平
  def pw_Star_GetMioWong(nEarth)
    sStr,nValArray,nMioWongStyle = PanWeb.GetMioWong(@Star,@m_nPanType,nEarth,PanWeb::MAX_STAR)

    if (nMioWongStyle == Star::INT_MW_CHAR) then
      sBuf = pw_Star_MakeMioWongStr_Steven(sStr,nValArray)
    else
      # 內湖 林信銘老師
      sBuf = pw_Star_MakeMioWongStr_Lin(sStr,nValArray)
    end
    sBuf = Cfate.GetSpace(2) + sBuf
    sBuf += PanWeb.pw_GetPan_br()
    return sBuf
  end

  def pw_Star_MakeFourHuaStr_Steven(aStr,aaValArray)
#   sBuf = Cfate.GetSpace(2)
#   sBuf += Cfate.GetSpace(PanWeb::MAX_STAR - 4)  # 8 - 甲級星的數目
#   sBuf += "<FONT COLOR=BLUE>" + "L" # "忌"
#   sBuf += "<FONT COLOR=RED>" + "C" # "祿"
#   sBuf += "<FONT COLOR=RED>" + "K" # "權"
#   sBuf += "<FONT COLOR=DARKGREEN>" + "G" # "科"
#   sBuf += PanWeb.pw_GetPan_br()

    sBuf = ""
    (1..aaValArray[0].length).each do |i|
      case aaValArray[0][i - 1]
      when 1 then
        sBuf += "<FONT COLOR=RED>"
      when 2 then
        sBuf += "<FONT COLOR=RED>"
      when 3 then
        sBuf += "<FONT COLOR=DARKGREEN>"
      when 4 then
        sBuf += "<FONT COLOR=BLUE>"
      else
      end
      sBuf += aStr[0][i - 1]
    end

    return sBuf

  end

  def pw_Star_MakeFourHuaStr_Lin(aStr,aaValArray)
    sBuf = ""
    (1..aaValArray.length).each do |i|
      (1..aaValArray[i].length).each do |j|
      case aaValArray[i - 1][j - 1]
      when 1 then
        sBuf += "<FONT COLOR=RED>"
      when 2 then
        sBuf += "<FONT COLOR=RED>"
      when 3 then
        sBuf += "<FONT COLOR=DARKGREEN>"
      when 4 then
        sBuf += "<FONT COLOR=BLUE>"
      else
      end
      sBuf += aStr[i - 1][j - 1]
      end
    end
    return sBuf
  end

  # 博士及長生2個空格，後面接8個全形中文字，沒有就用全形空白，一排10個中文字
  #<FONT COLOR=RED>祿<FONT COLOR=RED>權<FONT COLOR=DARKGREEN>科<FONT COLOR=BLUE>忌
  def pw_Star_GetFourHua(nEarth)
    aStr,aaValArray,nStyle = PanWeb.GetAStarFourHua(@Star,@m_nPanType,nEarth,PanWeb::MAX_STAR)

    if (nStyle != Star::PAN_YEAR_HUA_STACK) then
      sBuf = pw_Star_MakeFourHuaStr_Steven(aStr,aaValArray)
    else
      # 內湖 林信銘老師
      sBuf = pw_Star_MakeFourHuaStr_Lin(aStr,aaValArray)
    end
    sBuf = Cfate.GetSpace(2) + sBuf
    sBuf += PanWeb.pw_GetPan_br()
    return sBuf
  end

  # <FONT COLOR=DARKGREEN>歲喪<FONT COLOR=MAROON>天旬破蜚孤天八天<BR>
  # <FONT COLOR=DARKGREEN>驛門<FONT COLOR=MAROON>傷中碎廉辰福座巫<BR>
  # 歲建星 YEARGOD
  #   sValue = ["歲","建"]
  def pw_Star_GetYearGod(nEarth,nWord)
#   sValue = PanWeb.GetYearGodName2(@PanInfo,nEarth)
    sValue = PanWeb.GetYearGodName(@Star,@m_nPanType,nEarth)
    sBuf = "<FONT COLOR=DARKGREEN>" + sValue[nWord]
  end

  # 將星 YEARSTAR
  #   sValue = ["將","星"]
  def pw_Star_GetYearStar(nEarth,nWord)
#   sValue = PanWeb.GetYearStarName2(@PanInfo,nEarth)
    sValue = PanWeb.GetYearStarName(@Star,@m_nPanType,nEarth)
    sBuf = "<FONT COLOR=DARKGREEN>" +  sValue[nWord]
  end

  # 乙級星
  #   sValue = ["天旬破蜚孤天八天","傷中碎廉辰福座巫"]
  def pw_Star_GetBStar(nEarth,nWord)
#   sValue = PanWeb.GetBStarName2(@PanInfo,nEarth,PanWeb::MAX_STAR)
    sValue = PanWeb.GetBStarName(@Star,@m_nPanType,nEarth,PanWeb::MAX_STAR)
    sBuf = "<FONT COLOR=MAROON>" + sValue[nWord] + "<BR>"
  end

  # 宮名 <FONT COLOR=MIDNIGHTBLUE FACE=細明體 SIZE=2>　　　【朋友】
  def pw_Star_GetHouseName(nEarth)
    faceName = Pm.GetStr("IDS_A_KEY_FONT_MIN")
#   sBuf = "<FONT COLOR=MIDNIGHTBLUE FACE=#{faceName} SIZE=2>"
    sBuf = "<FONT COLOR=DARKRED FACE=#{faceName} SIZE=2>"
#   sBuf += PanWeb.GetHouseName2(@PanInfo,nEarth)
    sBuf += PanWeb.GetHouseName(@Star,@m_nPanType,nEarth)
  end

  def pw_Star_GetNormalHouseName(nEarth)
    faceName = Pm.GetStr("IDS_A_KEY_FONT_MIN")
    sBuf = "<FONT COLOR=DARKGREEN FACE=#{faceName} SIZE=2>"
#   sBuf += PanWeb.GetHouseName2(@PanInfo,nEarth)
    sBuf += PanWeb.GetHouseName(@Star,Cfate::PAN_NORMAL,nEarth)
  end

  # 宮干支 <FONT COLOR=BLACK>丁巳
  def pw_Star_GetHouseSkyEarthName(nEarth)
    sBuf = "<FONT COLOR=BLACK>"
#   sBuf += PanWeb.GetHouseSkyEarthName2(@PanInfo,nEarth)
    sBuf += PanWeb.GetHouseSkyEarthName(@Star,@m_nPanType,nEarth)
  end
  def pw_Star_GetHouseSkyName(nEarth)
    #sBuf = PanWeb.pw_GetPan_table_td()
    sBuf = "<FONT COLOR=BLACK>"
    sBuf += PanWeb.GetHouseSkyName(@Star,@m_nPanType,nEarth)
    #sBuf += PanWeb.pw_GetPan_table_td_end()
  end
  def pw_Star_GetHouseEarthName(nEarth)
    sBuf = "<FONT COLOR=BLACK>"
    sBuf += PanWeb.GetHouseEarthName(@Star,nEarth)
  end


  # <FONT COLOR=DEEPPINK> 【身】
  def pw_Star_GetBody()
    # <FONT COLOR=MIDNIGHTBLUE FACE=細明體 SIZE=2>
    sBuf = "<FONT COLOR=DEEPPINK>"
    sBuf += Pm.GetStrWithQuote("IDS_S_BODY_STRING")
    sBuf += PanWeb.pw_Get_Font_end()
  end

  # class function
  # 由Star物件取得
  def PanWeb.GetDoctorName(oStar,nPanType,nEarth)
    return oStar.gPanHouse_GetDoctorName(nPanType,nEarth)
  end

  def PanWeb.GetGodName(oStar,nPanType,nEarth)
    return oStar.gPanHouse_GetGodName(nPanType,nEarth)
  end

  def PanWeb.GetYearGodName(oStar,nPanType,nEarth)
    return oStar.gPanHouse_GetYearGodName(nPanType,nEarth)
  end

  def PanWeb.GetYearStarName(oStar,nPanType,nEarth)
    return oStar.gPanHouse_GetYearStarName(nPanType,nEarth)
  end

  def PanWeb.GetAStarName(oStar,nPanType,nEarth,nMax)
    return oStar.gPanHouse_GetAStarName(nPanType,nEarth,nMax)
  end

  def PanWeb.GetAStarFourHua(oStar,nPanType,nEarth,nMax)
    return oStar.gPanHouse_GetAStarFourHua(nPanType,nEarth,nMax)
  end

  def PanWeb.GetMioWong(oStar,nPanType,nEarth,nMax)
    return oStar.gPanHouse_GetMioWong(nPanType,nEarth,nMax)
  end

  def PanWeb.GetBStarName(oStar,nPanType,nEarth,nMax)
    return oStar.gPanHouse_GetBStarName(nPanType,nEarth,nMax)
  end

  # 74 --- 83
  def PanWeb.GetLarge(oStar,nEarth)
    return oStar.gPanHouse_GetLarge(nEarth)
  end

  # "<FONT COLOR=GRAY SIZE=1>" + "5 17 29 41 53 65 77 89" + "<BR>"
  def PanWeb.GetSmall(oStar,nEarth)
    return oStar.gPanHouse_GetSmall(nEarth)
  end

  # 宮名 <FONT COLOR=MIDNIGHTBLUE FACE=細明體 SIZE=2>　　　【朋友】
  def PanWeb.GetHouseName(oStar,nPanType,nEarth)
    return oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
  end

  # 宮干支 <FONT COLOR=BLACK>丁巳
  def PanWeb.GetHouseSkyEarthName(oStar,nPanType,nEarth)
    return oStar.gHouse_GetHouseSkyEarthName(nPanType,nEarth)
  end
  def PanWeb.GetHouseSkyName(oStar,nPanType,nEarth)
    return oStar.gHouse_GetHouseSkyName(nPanType,nEarth)
  end
  def PanWeb.GetHouseEarthName(oStar,nEarth)
    return oStar.gHouse_GetHouseEarthName(nEarth)
  end


  # 資料結構取得
  def PanWeb.GetDoctorName2(panInfo,nEarth)
    panInfo[Earth.GetKeyName(nEarth)][Star::DOCTOR][0][0]
  end

  def PanWeb.GetGodName2(panInfo,nEarth)
    sOut = panInfo[Earth.GetKeyName(nEarth)][Star::GOD][0][0]
    sOut = PanWeb.Modify2TwoWords(sOut)
  end

  def PanWeb.GetYearGodName2(panInfo,nEarth)
    sOut = panInfo[Earth.GetKeyName(nEarth)][Star::YEARGOD][0][0]
    sOut = PanWeb.Modify2TwoWords(sOut)
  end

  def PanWeb.GetYearStarName2(panInfo,nEarth)
    sOut = panInfo[Earth.GetKeyName(nEarth)][Star::YEARSTAR][0][0]
    sOut = PanWeb.Modify2TwoWords(sOut)
  end

  def PanWeb.GetAStarName2(panInfo,nEarth,nMax)
    sStar = Array.new(2,"")
    starArray = panInfo[Earth.GetKeyName(nEarth)][Star::A_STAR]

    sOne = ""
    sTwo = ""
    starArray.each do |oStar|
      sTmp = PanWeb.Modify2TwoWords(oStar[0])
      sOne += sTmp[0]
      sTwo += sTmp[1]
    end
    sOne += Cfate.GetSpace(nMax - starArray.length)
    sTwo += Cfate.GetSpace(nMax - starArray.length)
    sStar[0] = sOne.reverse
    sStar[1] = sTwo.reverse
    return sStar
  end

  def PanWeb.GetBStarName2(panInfo,nEarth,nMax)
    sStar = Array.new(2,"")
    starArray = panInfo[Earth.GetKeyName(nEarth)][Star::B_STAR]
    sOne = ""
    sTwo = ""
    starArray.each do |oStar|
      sTmp = PanWeb.Modify2TwoWords(oStar[0])
      sOne += sTmp[0]
      sTwo += sTmp[1]
    end
    sOne += Cfate.GetSpace(nMax - starArray.length)
    sTwo += Cfate.GetSpace(nMax - starArray.length)
    sStar[0] = sOne.reverse
    sStar[1] = sTwo.reverse
    return sStar
  end

  # 74 --- 83
  def PanWeb.GetLarge2(starInfo,nEarth)
    nStarYear = starInfo[Star::U_LARGE][Earth.Earth2EarthIndex(nEarth)]
    sOut = "#{nStarYear} --- #{nStarYear+9}"
    return sOut
  end

  # "<FONT COLOR=GRAY SIZE=1>" + "5 17 29 41 53 65 77 89" + "<BR>"
  def PanWeb.GetSmall2(starInfo,nEarth)
    nStarYear = starInfo[Star::U_SMALL][Earth.Earth2EarthIndex(nEarth)]
    sOut = nStarYear.to_s
    7.times do |i|
      nStarYear += 12
      sOut += " "
      sOut += nStarYear.to_s
    end

    return sOut
  end

  # 宮名 <FONT COLOR=MIDNIGHTBLUE FACE=細明體 SIZE=2>　　　【朋友】
  def PanWeb.GetHouseName2(panInfo,nEarth)
    sOut = Pm.GetStr("IDS_A_LEFT_QUOTE")
    sOut += panInfo[Earth.GetKeyName(nEarth)][Star::HOUSE_NAME][0]
    sOut += Pm.GetStr("IDS_A_RIGHT_QUOTE")
    return sOut
  end

  # 宮干支 <FONT COLOR=BLACK>丁巳
  def PanWeb.GetHouseSkyEarthName2(panInfo,nEarth)
    sOut = Sky.GetName(Sky.SkyIndex2Sky(panInfo[Earth.GetKeyName(nEarth)][Star::HOUSE_SKY]))
    sOut += Earth.GetName(nEarth)
  end

  def PanWeb.GetFlowYear(oStar,nEarth,nPanType=nil)
    bFlowHouse,sBuf,nHouseYear = oStar.gPanHouse_FlowYear(nEarth,nPanType)
    return bFlowHouse,sBuf
  end

  def PanWeb.GetFlowMonth(oStar,nEarth)
    bFlowHouse,sBuf,nHouse = oStar.gPanHouse_FlowMonth(nEarth)
    return bFlowHouse,sBuf
  end

  def PanWeb.GetFlowDate(oStar,nEarth)
    return oStar.gPanHouse_FlowDate(nEarth)
  end

  def PanWeb.GetFlowTime(oStar,nEarth)
    bFlowHouse,sBuf,nHouseTimeIndex = oStar.gPanHouse_FlowTime(nEarth)
    return bFlowHouse
  end

  def PanWeb.GetFlowMin(oStar,nEarth)
    return oStar.gPanHouse_FlowMin(nEarth)
  end


  def PanWeb.GetFlowYear_SmallSan(oStar,nEarth,nPanType=nil)
    return oStar.gPanHouse_FlowYear_SmallSan(nEarth,nPanType)
  end

  def PanWeb.GetHouseFive(oStar,nPanType,nEarth)
    return oStar.gPanHouse_FiveStr(nPanType,nEarth)
  end
end
