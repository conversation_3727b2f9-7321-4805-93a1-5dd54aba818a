class Xdate
  def Xdate.nongminli_str(key)
    return Pm.GetStr("nongminli.#{key}")
  end

  # lifa 參數不用，只有日 跟 時 有差； 節氣干支曆 跟 陰曆干支曆 的干支年及干支月 有可能會不同
  def Xdate.api_ganzhi_riqi_str(nWYear, nWMonth,nWDate, nWHour,gan_zhis,duixiang,count,lifa)
    a = Array.new

    a_ganzhi = Xdate.MakeGanZhiValues(gan_zhis)

    (0..count).each do |n|
      if (duixiang.upcase == "Y") then
        nWYear,a1 = Xdate.api_ganzhi_riqi_str_nian(nWYear, a_ganzhi,lifa)
        a = a + a1
      elsif (duixiang.upcase == "M") then
        nWYear,nWMonth,a1 = Xdate.api_ganzhi_riqi_str_yue(nWYear, nWMonth,a_ganzhi,lifa)
        a = a + a1
      elsif (duixiang.upcase == "D") then
        nWYear,nWMonth,nWDate,a1 = Xdate.api_ganzhi_riqi_str_ri(nWYear, nWMonth,nWDate, a_ganzhi,lifa)
        a = a + a1
      elsif (duixiang.upcase == "H") then
        nWYear,nWMonth,nWDate,nWHour,a1 = Xdate.api_ganzhi_riqi_str_shi(nWYear, nWMonth,nWDate, nWHour,a_ganzhi,lifa)
        a = a + a1
      end
      if (a.length >= count) then
        return a[0..count - 1]
      end
    end

    return a
  end
  # 重新排列 a_ganzhi；讓大於等於 ganzhi的開始
  # 例：a_ganzhi =[0,1,5,7] ganzhi=6
  # 結果：[7,0,1,5]
  def Xdate.ganzhi_re_org(a_ganzhi,ganzhi)
    n = a_ganzhi.bsearch {|x| x >=   ganzhi }
    if (n == nil) then
      # a_ganzhi 都比 ganzhi 小，照原來順序
      return a_ganzhi
    else
      index = a_ganzhi.index(n)
      a = a_ganzhi[index..a_ganzhi.length - 1]
      if (a.length != a_ganzhi.length) then
        a = a + a_ganzhi[0..index - 1]
      end
      return a
    end
  end
  def Xdate.api_ganzhi_riqi_str_nian(nWYear,a_ganzhi,lifa)
    a1 = Array.new
    nGanZhiYear = Xdate.GetYearGanZhi(nWYear)
    new_ganzhi = Xdate.ganzhi_re_org(a_ganzhi,nGanZhiYear)

    new_ganzhi.each_index do |i|
      ganzhi = new_ganzhi[i]
      nWYear,nGanZhiYear = Xdate.GetGanZhiYear(ganzhi,nWYear)

      if (nWYear <= Xdate::LAST_YEAR) then
        h = Hash.new
        a = Xdate.ganzhi_date_str_array2(nGanZhiYear,1,1,1)
        h["ganzhi_nian"] = a[0]
        h["yangli_nian"] = nWYear.to_s
        h["timestamp"] = "%04d" % [nWYear]

        nSYear,nSMonth,nSDate,nSHour,nSMinute,nSSegmentIndex = Xdate.Wdate2SegmentDate(nWYear,6,1,0,59,Xdate::SEGMENT_SPRING)
        h["jieqili_nian"] = nSYear.to_s

        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,6,1)
        h["yinli_nian"] = nEYear.to_s

        a1.push(h)
      end

      nWYear += 1
    end

    return nWYear,a1
  end
  def Xdate.api_ganzhi_riqi_str_yue(nWYear, nWMonth,a_ganzhi,lifa)
    a1 = Array.new
    nGanZhiMonth = Xdate.GetMonthGanZhi(nWYear,nWMonth)
    new_ganzhi = Xdate.ganzhi_re_org(a_ganzhi,nGanZhiMonth)

    new_ganzhi.each_index do |i|
      ganzhi = new_ganzhi[i]
      nWYear,nWMonth,nGanZhiYear,nGanZhiMonth = Xdate.GetGanZhiMonth(ganzhi,nWYear,nWMonth)

      if (nWYear <= Xdate::LAST_YEAR) then
        h = Hash.new
        a = Xdate.ganzhi_date_str_array2(nGanZhiYear,nGanZhiMonth,1,1)
        h["ganzhi_nian"] = a[0]
        h["ganzhi_yue"] = a[1]

        h["yangli_nian"] = nWYear.to_s
        h["yangli_yue"] = nWMonth.to_s
        h["timestamp"] = "%04d%02d" % [nWYear,nWMonth]

        nSYear,nSMonth,nSDate,nSHour,nSMinute,nSSegmentIndex = Xdate.Wdate2SegmentDate(nWYear,nWMonth,1,0,59,Xdate::SEGMENT_SPRING)
        h["jieqili_nian"] = nSYear.to_s
        h["jieqili_yue"] = nSMonth.to_s
        h["jieqi"] = Xdate.GetSegmentStrFromWest(nWYear, nWMonth, 1,23, 59)

        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,1)
        h["yinli_nian"] = nEYear.to_s
        h["yinli_yue"] = nEMonth.to_s

        a1.push(h)
      end

      nWYear,nWMonth = Xdate.NextWMonths(nWYear,nWMonth,1)
    end

    return nWYear,nWMonth,a1
  end
  def Xdate.api_ganzhi_riqi_str_ri(nWYear, nWMonth,nWDate,a_ganzhi,lifa)
    a1 = Array.new

    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, 0,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    new_ganzhi = Xdate.ganzhi_re_org(a_ganzhi,nGanZhiDay)
    new_ganzhi.each_index do |i|
      ganzhi = new_ganzhi[i]
      nWYear,nWMonth,nWDate,nGanZhiYear,nGanZhiMonth,nGanZhiDay = Xdate.GetSegGanZhiDate(ganzhi,nWYear, nWMonth, nWDate,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
      if (nWYear <= Xdate::LAST_YEAR) then
        a = Xdate.ganzhi_date_str_array2(nGanZhiYear,nGanZhiMonth,nGanZhiDay,1)
        h = Hash.new
        h["jieqi_ganzhi_nian"] = a[0]
        h["jieqi_ganzhi_yue"] = a[1]
        h["jieqi_ganzhi_ri"] = a[2]

        h["yangli_nian"] = nWYear.to_s
        h["yangli_yue"] = nWMonth.to_s
        h["yangli_ri"] = nWDate.to_s
        h["timestamp"] = "%04d%02d%02d" % [nWYear,nWMonth,nWDate]

        nSYear,nSMonth,nSDate,nSHour,nSMinute,nSSegmentIndex = Xdate.Wdate2SegmentDate(nWYear,nWMonth,nWDate,23,59,Xdate::SEGMENT_SPRING)
        h["jieqili_nian"] = nSYear.to_s
        h["jieqili_yue"] = nSMonth.to_s
        h["jieqili_ri"] = nSDate.to_s
        h["jieqi"] = Xdate.GetSegmentStrFromWest(nWYear, nWMonth, nWDate,23, 59)

        nWYear1,nWMonth1,nWDate1,nGanZhiYear,nGanZhiMonth,nGanZhiDay = Xdate.GetLunarGanZhiDate(ganzhi,nWYear, nWMonth, nWDate,nil)
        a = Xdate.ganzhi_date_str_array2(nGanZhiYear,nGanZhiMonth,nGanZhiDay,1)
        h["yinli_ganzhi_nian"] = a[0]
        h["yinli_ganzhi_yue"] = a[1]
        h["yinli_ganzhi_ri"] = a[2]

        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
        h["yinli_nian"] = nEYear.to_s
        h["yinli_yue"] = nEMonth.to_s
        h["yinli_runyue"] = bLeapMonth
        h["yinli_ri"] = nEDate.to_s

        h["is_holiday"] = Xdate.is_holiday?(nWYear, nWMonth, nWDate)
        h["week_day"] = Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)

        a1.push(h)
      end

      nWYear,nWMonth,nWDate = Xdate.NextNWDate(nWYear,nWMonth,nWDate,1)
    end

    return nWYear,nWMonth,nWDate,a1
  end
  def Xdate.api_ganzhi_riqi_str_shi(nWYear, nWMonth,nWDate, nWHour,a_ganzhi,lifa)
    a1 = Array.new

    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, nWHour,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    new_ganzhi = Xdate.ganzhi_re_org(a_ganzhi,nGanZhiHour)

    new_ganzhi.each_index do |i|
      ganzhi = new_ganzhi[i]

      nWYear,nWMonth,nWDate,nWHour,nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetSegGanZhiHour(ganzhi,nWYear, nWMonth, nWDate, nWHour,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
      if (nWYear <= Xdate::LAST_YEAR) then
        nWYear1,nWMonth1,nWDate1,nWHour1 = Xdate.NextWDateFromTime(nWYear,nWMonth,nWDate,nWHour)

        a = Xdate.ganzhi_date_str_array2(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour)
        h = Hash.new
        h["jieqi_ganzhi_nian"] = a[0]
        h["jieqi_ganzhi_yue"] = a[1]
        h["jieqi_ganzhi_ri"] = a[2]
        h["jieqi_ganzhi_shi"] = a[3]

        nSYear,nSMonth,nSDate,nSHour,nSMinute,nSSegmentIndex = Xdate.Wdate2SegmentDate(nWYear1,nWMonth1,nWDate1,23,59,Xdate::SEGMENT_SPRING)
        h["jieqili_nian"] = nSYear.to_s
        h["jieqili_yue"] = nSMonth.to_s
        h["jieqili_ri"] = nSDate.to_s
        h["jieqi"] = Xdate.GetSegmentStrFromWest(nWYear1, nWMonth1, nWDate1,nWHour1, 59)

        h["yangli_nian"] = nWYear1.to_s
        h["yangli_yue"] = nWMonth1.to_s
        h["yangli_ri"] = nWDate1.to_s
        h["shi"] = Xdate.GetETimeRangeStr2(Xdate.Hour2Earth(nWHour1))
        h["timestamp"] = "%04d%02d%02d%02d" % [nWYear1,nWMonth1,nWDate1,nWHour1]

        nWYear2,nWMonth2,nWDate2,nWHour2,nGanZhiYear2,nGanZhiMonth2,nGanZhiDay2,nGanZhiHour2 = Xdate.GetLunarGanZhiHour(ganzhi,nWYear, nWMonth, nWDate, nWHour,nil)
        a = Xdate.ganzhi_date_str_array2(nGanZhiYear2,nGanZhiMonth2,nGanZhiDay2,nGanZhiHour2)
        h["yinli_ganzhi_nian"] = a[0]
        h["yinli_ganzhi_yue"] = a[1]
        h["yinli_ganzhi_ri"] = a[2]
        h["yinli_ganzhi_shi"] = a[3]
        nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear1,nWMonth1,nWDate1)
        h["yinli_nian"] = nEYear.to_s
        h["yinli_yue"] = nEMonth.to_s
        h["yinli_runyue"] = bLeapMonth
        h["yinli_ri"] = nEDate.to_s

        h["is_holiday"] = Xdate.is_holiday?(nWYear1, nWMonth1, nWDate1)
        h["week_day"] = Xdate.GetWWeekDayStr(nWYear1, nWMonth1, nWDate1)

        a1.push(h)
      end

      nWYear,nWMonth,nWDate,nWHour = Xdate.NextNHour(nWYear,nWMonth,nWDate,nWHour,2)
    end

    return nWYear,nWMonth,nWDate,nWHour,a1
  end

  def Xdate.api_ganzhi_str(nWYear, nWMonth,nWDate, nWHour)
    h = Hash.new

    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, nWHour,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    a = Xdate.ganzhi_date_str_array2(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour)
    h["jieqi_ganzhi_year"] = a[0]
    h["jieqi_ganzhi_month"] = a[1]
    h["jieqi_ganzhi_day"] = a[2]
    h["jieqi_ganzhi_hour"] = a[3]

    nETime = Xdate.Hour2ETime(nWHour)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, nETime,nil)
    a = Xdate.ganzhi_date_str_array2(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour)
    h["yinli_ganzhi_year"] = a[0]
    h["yinli_ganzhi_month"] = a[1]
    h["yinli_ganzhi_day"] = a[2]
    h["yinli_ganzhi_hour"] = a[3]

    h["timestamp"] = "%04d%02d%02d%02d" % [nWYear,nWMonth,nWDate,nWHour]

    return h
  end
  def Xdate.farmercal(nWYear,nWMonth,nWDate)
  	h = Hash.new
  	nGanZhiYear = Xdate.GetSegYearGanZhi(nWYear, nWMonth, nWDate,23,59,Xdate::SEGMENT_SPRING)
  	nSYear,nSMonth,nSDate,nWHour,nWMinute,nSegmentIndex = Xdate.Wdate2SegmentDate(nWYear,nWMonth,nWDate,23,59,Xdate::SEGMENT_SPRING)
  	nSegmentIndex,nSegmentDays,nSegmentTime,bExactSegment = Xdate.GetSegmentFromWest_farmercal(nWYear,nWMonth,nWDate,23,59)
  	nMonthSky,nMonthEarth = Xdate.GetMonthSkyEarth(nSYear,nSMonth)
  	nGanZhiMonth = Xdate.SkyEarthIndex2GanZhi(Sky.Sky2SkyIndex(nMonthSky),Earth.Earth2EarthIndex(nMonthEarth))
  	nGanZhiDay,nDaySky,nDayEarth = Xdate.GetWDateGanZhiSkyEarth(nWYear,nWMonth,nWDate)
    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)

  	h["xili_year"] = nWYear
  	h["xili_month"] = nWMonth
  	h["xili_day"] = nWDate
    h["timestamp"] = "%04d%02d%02d" % [nWYear,nWMonth,nWDate]

    h["xili_date"] = "#{Xdate.GetFullWDateStr2(nWYear,nWMonth,nWDate,Cfate::PAN_DISPLAY_WEST)}"
    h["yinli_date"] = Xdate.fc_GetMoonDateStr(nEYear,nEMonth,nEDate,bLeapMonth)
    h["jieqili_date"] = Xdate.GetGanZhiYearMonthDay_Str(nGanZhiYear,nGanZhiMonth,nGanZhiDay)
    h["suici"] = Xdate.GetLunarYearGanZhiStr(nEYear) # 歲次
    
    nong = Xdate.nongminli_str("nong")
  	h["yinli_daxiaoyue"] = nong + "#{Xdate.GetEMonthBigSmall(Xdate.GetEastMonthDays(nEYear, nEMonth, bLeapMonth))}"

  	h["yinli_month_day"] = Xdate.GetEMonthStr(nEMonth,bLeapMonth) + Xdate.GetEDayStr(nEDate)
    h["is_holiday"] = Xdate.is_holiday?(nWYear, nWMonth, nWDate)
    xing_qi = Xdate.nongminli_str("xing_qi")
  	h["week_day"] = xing_qi + "#{Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)}"
  	h["jieqi_ganzhi_year"] = Xdate.GetGanZhiYear_Str2(nGanZhiYear)
  	h["jieqi_ganzhi_month"] = Xdate.GetGanZhiMonth_Str2(nGanZhiMonth)
  	h["jieqi_ganzhi_day"] = Xdate.GetGanZhiDate_Str2(nGanZhiDay)

  	h["xili_date_simple"] = Xdate.GetWYearMonthDateStr(nWYear,nWMonth,nWDate)  # 西曆日期簡易版
  	h["jieqili_date_simple"] = Xdate.GetWYearMonthDateStr(nSYear,nSMonth,nSDate)
  	h["yinli_date_simple"] = Xdate.GetWYearMonthDateStr(nEYear,nEMonth,nEDate)
  	h["jieqi"] = Xdate.GetSegmentStr(nSegmentIndex)
  	h["month_ganzhi"] = Xdate.GetGanZhiMonth_Str2(nGanZhiMonth) # Xdate.GetGanZhiDate_Str(nGanZhiDay)

  	h["tiande_yuede"] = Xdate.fc_skymonth_de(nMonthEarth,nDaySky,nDayEarth)   # 天德月德
    h["jiu_xing"] = Xdate.fc_9star(nGanZhiDay,nSegmentIndex)  # 九星
    h["shi_er_shen"] = Xdate.fc_12god(nMonthEarth,nDayEarth)  # 十二神 十二建星

    h["ba_gua"] = Xdate.fc_8trigrams(nWYear, nWMonth, nWDate,nSegmentIndex) # 八卦
    h["chong_shi_er_sheng_xiao"] = Xdate.fc_rushanimal(nGanZhiDay) # 沖十二生肖
    h["chong_nian_ji"] = Xdate.fc_rushyearold(nGanZhiDay,nEYear)  # 沖幾歲
    h["chong_zuo_xiang"] = Xdate.fc_rushdirection(nGanZhiDay)  # 沖坐向
    h["day_wuxing"] = Xdate.fc_dayfive(nGanZhiDay)
  	h["day_ganzhi"] = Xdate.fc_dayganzhi(nGanZhiDay)
  	h["gi_shi"],h["gi_shi_count"],h["gi_shi_desc"] = Xdate.fc_goodhour(nGanZhiDay)  # 吉時
  	h["xiong_shi"],h["xiong_shi_count"],h["xiong_shi_desc"] = Xdate.fc_badhour(nGanZhiDay)

    h["ning_he_day"] = Xdate.fc_ning_he_ri(nEMonth,nDayEarth,nGanZhiDay)

    h["cai_shen"] = Xdate.fc_richgod(nGanZhiDay)  # 財神
    h["xi_shen"] = Xdate.fc_gladgod(nGanZhiDay)   # 喜神
    h["tai_shen"] = Xdate.fc_birthgod(nGanZhiDay)   # 胎神

    h["xili_holiday"] = Xdate.fc_westholiday(nWYear,nWMonth,nWDate,nSegmentIndex)
    h["yinli_holiday"] = Xdate.fc_lunarholiday(nEYear,nEMonth,nEDate,bLeapMonth)

    h["wu_tan_bing_day"] = Xdate.fc_donotvisitthepatient(nGanZhiDay)  # 勿探病日

    h["tian_she_day_yi_ji"] = Xdate.fc_god_amnesty_suitable_scruple(nMonthEarth,nGanZhiDay)  # 天赦日
    h["ershibaxingxiu_dai_hao"],h["ershibaxingxiu"],h["ershibaxingxiu_xi"],h["ershibaxingxiu_ji"] = Xdate.fc_28alphard_suitable_scruple(nWYear,nWMonth,nWDate) # 二十八星宿喜忌
    h["shiershen_dai_hao"],h["shiershen"],h["shiershen_yi"],h["shiershen_ji"] = Xdate.fc_12god_suitable_scruple(nMonthEarth,nDayEarth) # 十二神宜忌
    h["tian_yue_de_he_yi"] = Xdate.fc_skymonth_des_suitable(nMonthEarth,nDaySky,nDayEarth)
    if (h["tian_she_day_yi_ji"].length > 0) then # 天赦日諸事皆宜，沒有忌事
      h["yi"] = h["tian_she_day_yi_ji"]
      h["ji"] = ""
    else
      # puts h["28alphardscruple"]
      h["yi"] = Xdate.fc_array_uniq(h["tian_yue_de_he_yi"] + h["ershibaxingxiu_xi"] + h["shiershen_yi"]).join("、")
      h["ji"] = Xdate.fc_array_uniq(h["ershibaxingxiu_ji"] + h["shiershen_ji"] - h["tian_yue_de_he_yi"]).join("、")
    end

    h["exact_jieqi"] = bExactSegment
    h["jieqi_food"] = Xdate.fc_segmentfood(nSegmentIndex)
    h["jieqi_fish"] = Xdate.fc_segmentfish(nSegmentIndex)
    h["jieqi_month_desc"],h["jieqi_yue_sha"] = Xdate.fc_segment_month_desc(nGanZhiMonth,nMonthEarth)

    h["tian_she_day"] = Xdate.fc_god_amnesty(nMonthEarth,nGanZhiDay)  # 天赦日
    h["dao_zhen_day"] = Xdate.fc_knife_anvil(nMonthEarth,nDayEarth)  # 刀砧日
    h["pengzu_bai_ji"] = Xdate.fc_peng_100avoid(nDaySky,nDayEarth)  # 彭祖百忌

    h["wang_wang"] = Xdate.fc_gotdead(nSMonth,nDayEarth)  # 往亡
    h["shou_si"] = Xdate.fc_go_to_dead(nSMonth,nDayEarth)  # 受死

    alphard = Xdate.fc_28alphard(nWYear,nWMonth,nWDate)
    h["n28alphard"] = alphard[0]
    h["qilin_day"] = Xdate.fc_kirin(nMonthEarth,h["n28alphard"])   # 麒麟日
    h["feng_huang_day"] = Xdate.fc_phoenix(nMonthEarth,h["n28alphard"])   # 鳳凰日

    hTemp = Xdate.api_wan_nian_li_str_yue3(nWYear, nWMonth, nWDate)
    h["yue_jie"] = hTemp["yue_jie"]
    h["yue_qi"] = hTemp["yue_qi"]
    h["yue_jie_time"] = hTemp["yue_jie_time"]
    h["yue_qi_time"] = hTemp["yue_qi_time"]
    h["tai_sui"] = hTemp["tai_sui"]

    return h
  end
  def Xdate.api_wan_nian_li_str_yue3(nWYear, nWMonth, nWDate)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, 23,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    h = Hash.new
    # 2002年 1月
    h["xi_li_nian_yue"] = Xdate.GetFullWDateStr2(nWYear,nWMonth,0,Cfate::PAN_DISPLAY_WEST)
    # 辛巳年
    h["ganzhi_nian"] = Xdate.GetGanZhiYear_Str(nGanZhiYear)
    # # （陰 白蠟金）
    s = "(#{Xdate.wan_nian_li_yin_yang_str(nGanZhiYear)} #{Xdate.wan_nian_li_liu_shi_na_yin_str(nGanZhiYear)})"
    h["yin_yang_liu_shi_na_yin"] = s
    # # 生肖（蛇）
    h["sheng_xiao"] = Xdate.wan_nian_li_sheng_xiao_str(nGanZhiYear)
    # # 太歲(太歲鄭祖 )
    s = Xdate.wan_nian_li_str("tai_sui")
    s += Xdate.wan_nian_li_tai_sui_str(nGanZhiYear)
    h["tai_sui"] = s
    # 冬至
    nGanZhiYear1,nGanZhiMonth1,nGanZhiDay1,nGanZhiHour1,nCurSegment1 = Xdate.GetSeg8Words(nWYear, nWMonth,1, 0,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    h["yue_qian_jie_qi"] = Xdate.wan_nian_li_er_shi_si_jie_qi_str(nCurSegment1)
    # 小寒1 / 5  20:44
    jieqiIndex = Xdate.Get_Nian_Yue_Jieqi_Index(nWYear,nWMonth)
    yue,ri,shi,fen = Xdate.GetJieqiShiJianByIndex(jieqiIndex)
    h["yue_jie"] = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}"
    h["yue_jie_time"] = Time.new(nWYear, yue, ri, shi, fen, 2, "+08:00")
    # 大寒1 / 20  14:03
    jieqiIndex = Pm.TestNo24(jieqiIndex + 1)
    yue,ri,shi,fen = Xdate.GetJieqiShiJianByIndex(jieqiIndex)
    h["yue_qi"] = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}"
    h["yue_qi_time"] = Time.new(nWYear, yue, ri, shi, fen, 2, "+08:00")
    return h
  end
  def Xdate.api_wan_nian_li_str_yue2(nWYear, nWMonth, nWDate)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, 23,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    h = Hash.new
    # 2002年 1月
    h["xi_li_nian_yue"] = Xdate.GetFullWDateStr2(nWYear,nWMonth,0,Cfate::PAN_DISPLAY_WEST)
    # 辛巳年
    h["ganzhi_nian"] = Xdate.GetGanZhiYear_Str(nGanZhiYear)
    # # （陰 白蠟金）
    s = "(#{Xdate.wan_nian_li_yin_yang_str(nGanZhiYear)} #{Xdate.wan_nian_li_liu_shi_na_yin_str(nGanZhiYear)})"
    h["yin_yang_liu_shi_na_yin"] = s
    # # 生肖（蛇）
    h["sheng_xiao"] = Xdate.wan_nian_li_sheng_xiao_str(nGanZhiYear)
    # # 太歲(太歲鄭祖 )
    s = Xdate.wan_nian_li_str("tai_sui")
    s += Xdate.wan_nian_li_tai_sui_str(nGanZhiYear)
    h["tai_sui"] = s
    # 冬至
    nGanZhiYear1,nGanZhiMonth1,nGanZhiDay1,nGanZhiHour1,nCurSegment1 = Xdate.GetSeg8Words(nWYear, nWMonth,1, 0,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    h["yue_qian_jie_qi"] = Xdate.wan_nian_li_er_shi_si_jie_qi_str(nCurSegment1)
    # 小寒1 / 5  20:44
    jieqiIndex = Xdate.Get_Nian_Yue_Jieqi_Index(nWYear,nWMonth)
    yue,ri,shi,fen = Xdate.GetJieqiShiJianByIndex(jieqiIndex)
    s = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}#{yue}/#{ri} #{shi}:#{fen}"
    h["yue_jie"] = s
    # 大寒1 / 20  14:03
    jieqiIndex = Pm.TestNo24(jieqiIndex + 1)
    yue,ri,shi,fen = Xdate.GetJieqiShiJianByIndex(jieqiIndex)
    s = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}#{yue}/#{ri} #{shi}:#{fen}"
    h["yue_qi"] = s
    return h
  end

  def Xdate.fc_array_uniq(a)
    return a.flatten.uniq
  end
  def Xdate.fc_array_exclude(a,a2)
    return Xdate.fc_array_uniq(a - a2)
  end
  def Xdate.fc_GetMoonDateStr(nEYear,nEMonth,nEDate,bLeapMonth)
    sBuf = ""
    if (Xdate.IsYearLegal?(nEYear)) then
      sBuf += Xdate.GetLunarYearGanZhiStr(nEYear)
    end

    if (Xdate.IsEMonthLegal?(nEYear,nEMonth,bLeapMonth)) then
      sBuf += Xdate.GetEMonthStr(nEMonth,bLeapMonth)
    end

    if (Xdate.IsEDateLegal?(nEYear,nEMonth,nEDate,bLeapMonth)) then
      sBuf += Xdate.GetEDayStr(nEDate)
    end
    return sBuf
  end
# 1、求天德月德
# 說明:以地支月求之,在地支月中對應到天干或地支日。 例如:在丑月 天干有「乙」則可合為「天月德合」
  # @@fc_des = ["天德","天德合","月德","月德合","天月德合"]
  @@fc_des_key = ["tian_de","tian_de_he","yue_de","yue_de_he","tian_yue_de_he"]
  @@fc_skymonth_des = {                   #      天德 天德合 月德 月德合  天月德合
    "e1"  =>  ["  ","  ","s9","s4","  "], # 子 => 1     1     壬    丁
    "e2"  =>  ["s7","s2","e3","s2","s2"], # 丑 => 庚    乙     寅    乙    乙
    "e3"  =>  ["s4","s9","s3","s8","  "], # 寅 => 丁    壬     丙    辛
    "e4"  =>  ["  ","  ","s1","s6","  "], # 卯 => 1     1     甲    己
    "e5"  =>  ["s9","s4","s9","s4","s4"], # 辰 => 壬    丁     壬    丁    丁
    "e6"  =>  ["s8","s3","e3","s4","  "], # 巳 => 辛    丙     寅    丁
    "e7"  =>  ["  ","  ","s3","s8","  "], # 午 => 1     1     丙    辛
    "e8"  =>  ["s1","s6","s1","s6","s6"], # 未 => 甲    己     甲    己   己
    "e9" =>  ["s10","s5","s9","s4","  "], # 申 => 癸    戊     壬    丁
    "e10" =>  ["  ","  ","e3","s2","  "], # 酉 => 1     1     寅    乙
    "e11" =>  ["s3","s8","s3","s8","s8"], # 戌 => 丙    辛     丙    辛   辛
    "e12" =>  ["s2","s7","s1","s6","  "]  # 亥 => 乙    庚     甲    己
  }
  def Xdate.fc_tian_yue_de_he_str(index)
    key = Xdate.fc_tian_yue_de_he_key(index)
    return Xdate.nongminli_str(key)
  end
  def Xdate.fc_tian_yue_de_he_key(index)
    return @@fc_des_key[index]
  end
# nMonthEarth 是指節氣月的地支
  def Xdate.fc_skymonth_de(nMonthEarth,nDaySky,nDayEarth)
  	aOut = Array.new
    aIndex = Xdate.fc_skymonth_de_index(nMonthEarth,nDaySky,nDayEarth)
  	aIndex.each_index do |i|
      s = Xdate.fc_tian_yue_de_he_str(aIndex[i])
      aOut.push(s)
  	end
  	# return [aOut,nMonthEarth,nDaySky,nDayEarth]
  	return aOut
  end
  def Xdate.fc_skymonth_de_index(nMonthEarth,nDaySky,nDayEarth)
    aIndex = Array.new
    a = @@fc_skymonth_des["e#{nMonthEarth}"].clone
    asm = ["s#{nDaySky}","e#{nDayEarth}"]
    if (a[1] == a[4]) then # 天月德合 remove 天德合 月德合
      a[1] = "  "
    end
    if (a[3] == a[4]) then # 天月德合 remove 天德合 月德合
      a[3] = "  "
    end
    a.each_index do |i|
      if (asm.include?(a[i])) then
        aIndex.push(i)
      end
    end
    return aIndex
  end
  # 天德","天德合","月德","月德合","天月德合
  # @@fc_skymonth_des_suitable = [
  #   ["祭祀","祈福","求嗣","赴任","結婚姻","納釆問名","嫁娶","納婿","遷移","入宅","安香","興造","動土","豎柱","上樑","造門","安門向","種植","放牧","納畜","解除"],
  #   ["祭祀","祈福","求嗣","赴任","結婚姻","納釆問名","嫁娶","納婿","遷移","入宅","安香","解除","興造","動土","豎柱","上樑","造門","安門向","破土","安葬","啟攢","種植","放牧","納畜"],
  #   ["祭祀","祈福","求嗣","赴任","結婚姻","納釆問名","嫁娶","納婿","遷移","入宅","安香","解除","興造","動土","豎柱","上樑","造門","安門向","破土","安葬","啟攢","種植","放牧","納畜"],
  #   ["祭祀","祈福","求嗣","赴任","結婚姻","納釆問名","嫁娶","納婿","遷移","入宅","安香","解除","興造","動土","豎柱","上樑","造門","安門向","破土","安葬","啟攢","種植","放牧","納畜"],
  #   ["祭祀","祈福","求嗣","赴任","結婚姻","納釆問名","嫁娶","納婿","遷移","入宅","安香","興造","動土","豎柱","上樑","造門","安門向","種植","放牧","納畜","解除","破土","安葬","啟攢"]
  # ]
  def Xdate.fc_tian_yue_de_he_shi_he_array(index)
    tian_yue_de_he_key = Xdate.fc_tian_yue_de_he_key(index)
    key = tian_yue_de_he_key + "_shi_he"
    s = Xdate.nongminli_str(key)
    return s.split(",")
  end
  def Xdate.fc_skymonth_des_suitable(nMonthEarth,nDaySky,nDayEarth)
    aOut = Array.new
    aIndex = Xdate.fc_skymonth_de_index(nMonthEarth,nDaySky,nDayEarth)
    aIndex.each_index do |i|
      # aOut = aOut + @@fc_skymonth_des_suitable[aIndex[i]]
      aOut = aOut + Xdate.fc_tian_yue_de_he_shi_he_array(aIndex[i])
    end
    aOut.flatten!
    aOut.uniq!
    return aOut
  end

  # 2、九星求法
  # 節氣代碼,配合上表使用 節氣 代碼
  # 小寒(0開始) 1 大寒 1 立春 1 雨水 2 驚蟄 2 春分 2 清明 2 穀雨 3 立夏 3 小滿 3 芒種 3 夏至 4 小暑 4 大暑 4 立秋 4 處暑 5 白露 5 秋分 5 寒露 5 霜降 6 立冬 6 小雪 6 大雪 6 冬至 1
  @@fc_segment_no = [1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,6,6,6,6,1]
                                             # 九星展開查表(日用法) 123456
                                             # 日 冬至 雨水 穀雨 夏至 處暑 霜降
  # @@fc_9_star = [
  # 	["一白","七赤","四綠","九紫","三碧","六白"], # 甲子 一白 七赤 四綠 九紫 三碧 六白
  # 	["二黑","八白","五黃","八白","二黑","五黃"], # 乙丑 二黑 八白 五黃 八白 二黑 五黃
  # 	["三碧","九紫","六白","七赤","一白","四綠"], # 丙寅 三碧 九紫 六白 七赤 一白 四綠
  # 	["四綠","一白","七赤","六白","九紫","三碧"], # 丁卯 四綠 一白 七赤 六白 九紫 三碧
  # 	["五黃","二黑","八白","五黃","八白","二黑"], # 戊辰 五黃 二黑 八白 五黃 八白 二黑
  # 	["六白","三碧","九紫","四綠","七赤","一白"], # 己巳 六白 三碧 九紫 四綠 七赤 一白
  # 	["七赤","四綠","一白","三碧","六白","九紫"], # 庚午 七赤 四綠 一白 三碧 六白 九紫
  # 	["八白","五黃","二黑","二黑","五黃","八白"], # 辛未 八白 五黃 二黑 二黑 五黃 八白
  # 	["九紫","六白","三碧","一白","四綠","七赤"], # 壬申 九紫 六白 三碧 一白 四綠 七赤
  # 	["一白","七赤","四綠","九紫","三碧","六白"], # 癸酉 一白 七赤 四綠 九紫 三碧 六白
  # 	["二黑","八白","五黃","八白","二黑","五黃"], # 甲戌 二黑 八白 五黃 八白 二黑 五黃
  # 	["三碧","九紫","六白","七赤","一白","四綠"], # 乙亥 三碧 九紫 六白 七赤 一白 四綠
  # 	["四綠","一白","七赤","六白","九紫","三碧"], # 丙子 四綠 一白 七赤 六白 九紫 三碧
  # 	["五黃","二黑","八白","五黃","八白","二黑"], # 丁丑 五黃 二黑 八白 五黃 八白 二黑
  # 	["六白","三碧","九紫","四綠","七赤","一白"], # 戊寅 六白 三碧 九紫 四綠 七赤 一白
  # 	["七赤","四綠","一白","三碧","六白","九紫"], # 己卯 七赤 四綠 一白 三碧 六白 九紫
  # 	["八白","五黃","二黑","二黑","五黃","八白"], # 庚辰 八白 五黃 二黑 二黑 五黃 八白
  # 	["九紫","六白","三碧","一白","四綠","七赤"], # 辛巳 九紫 六白 三碧 一白 四綠 七赤
  # 	["一白","七赤","四綠","九紫","三碧","六白"], # 壬午 一白 七赤 四綠 九紫 三碧 六白
  # 	["二黑","八白","五黃","八白","二黑","五黃"], # 癸未 二黑 八白 五黃 八白 二黑 五黃
  # 	["三碧","九紫","六白","七赤","一白","四綠"], # 甲申 三碧 九紫 六白 七赤 一白 四綠
  # 	["四綠","一白","七赤","六白","九紫","三碧"], # 乙酉 四綠 一白 七赤 六白 九紫 三碧
  # 	["五黃","二黑","八白","五黃","八白","二黑"], # 丙戌 五黃 二黑 八白 五黃 八白 二黑
  # 	["六白","三碧","九紫","四綠","七赤","一白"], # 丁亥 六白 三碧 九紫 四綠 七赤 一白
  # 	["七赤","四綠","一白","三碧","六白","九紫"], # 戊子 七赤 四綠 一白 三碧 六白 九紫
  # 	["八白","五黃","二黑","二黑","五黃","八白"], # 己丑 八白 五黃 二黑 二黑 五黃 八白
  # 	["九紫","六白","三碧","一白","四綠","七赤"], # 庚寅 九紫 六白 三碧 一白 四綠 七赤
  # 	["一白","七赤","四綠","九紫","三碧","六白"], # 辛卯 一白 七赤 四綠 九紫 三碧 六白
  # 	["二黑","八白","五黃","八白","二黑","五黃"], # 壬辰 二黑 八白 五黃 八白 二黑 五黃
  # 	["三碧","九紫","六白","七赤","一白","四綠"], # 癸巳 三碧 九紫 六白 七赤 一白 四綠
  # 	["四綠","一白","七赤","六白","九紫","三碧"], # 甲午 四綠 一白 七赤 六白 九紫 三碧
  # 	["五黃","二黑","八白","五黃","八白","二黑"], # 乙未 五黃 二黑 八白 五黃 八白 二黑
  # 	["六白","三碧","九紫","四綠","七赤","一白"], # 丙申 六白 三碧 九紫 四綠 七赤 一白
  # 	["七赤","四綠","一白","三碧","六白","九紫"], # 丁酉 七赤 四綠 一白 三碧 六白 九紫
  # 	["八白","五黃","二黑","二黑","五黃","八白"], # 戊戌 八白 五黃 二黑 二黑 五黃 八白
  # 	["九紫","六白","三碧","一白","四綠","七赤"], # 己亥 九紫 六白 三碧 一白 四綠 七赤
  # 	["一白","七赤","四綠","九紫","三碧","六白"], # 庚子 一白 七赤 四綠 九紫 三碧 六白
  # 	["二黑","八白","五黃","八白","二黑","五黃"], # 辛丑 二黑 八白 五黃 八白 二黑 五黃
  # 	["三碧","九紫","六白","七赤","一白","四綠"], # 壬寅 三碧 九紫 六白 七赤 一白 四綠
  # 	["四綠","一白","七赤","六白","九紫","三碧"], # 癸卯 四綠 一白 七赤 六白 九紫 三碧
  # 	["五黃","二黑","八白","五黃","八白","二黑"], # 甲辰 五黃 二黑 八白 五黃 八白 二黑
  # 	["六白","三碧","九紫","四綠","七赤","一白"], # 乙巳 六白 三碧 九紫 四綠 七赤 一白
  # 	["七赤","四綠","一白","三碧","六白","九紫"], # 丙午 七赤 四綠 一白 三碧 六白 九紫
  # 	["八白","五黃","二黑","二黑","五黃","八白"], # 丁未 八白 五黃 二黑 二黑 五黃 八白
  # 	["九紫","六白","三碧","一白","四綠","七赤"], # 戊申 九紫 六白 三碧 一白 四綠 七赤
  # 	["一白","七赤","四綠","九紫","三碧","六白"], # 己酉 一白 七赤 四綠 九紫 三碧 六白
  # 	["二黑","八白","五黃","八白","二黑","五黃"], # 庚戌 二黑 八白 五黃 八白 二黑 五黃
  # 	["三碧","九紫","六白","七赤","一白","四綠"], # 辛亥 三碧 九紫 六白 七赤 一白 四綠
  # 	["四綠","一白","七赤","六白","九紫","三碧"], # 壬子 四綠 一白 七赤 六白 九紫 三碧
  # 	["五黃","二黑","八白","五黃","八白","二黑"], # 癸丑 五黃 二黑 八白 五黃 八白 二黑
  # 	["六白","三碧","九紫","四綠","七赤","一白"], # 甲寅 六白 三碧 九紫 四綠 七赤 一白
  # 	["七赤","四綠","一白","三碧","六白","九紫"], # 乙卯 七赤 四綠 一白 三碧 六白 九紫
  # 	["八白","五黃","二黑","二黑","五黃","八白"], # 丙辰 八白 五黃 二黑 二黑 五黃 八白
  # 	["九紫","六白","三碧","一白","四綠","七赤"], # 丁巳 九紫 六白 三碧 一白 四綠 七赤
  # 	["一白","七赤","四綠","九紫","三碧","六白"], # 戊午 一白 七赤 四綠 九紫 三碧 六白
  # 	["二黑","八白","五黃","八白","二黑","五黃"], # 己未 二黑 八白 五黃 八白 二黑 五黃
  # 	["三碧","九紫","六白","七赤","一白","四綠"], # 庚申 三碧 九紫 六白 七赤 一白 四綠
  # 	["四綠","一白","七赤","六白","九紫","三碧"], # 辛酉 四綠 一白 七赤 六白 九紫 三碧
  # 	["五黃","二黑","八白","五黃","八白","二黑"], # 壬戌 五黃 二黑 八白 五黃 八白 二黑
  # 	["六白","三碧","九紫","四綠","七赤","一白"]  # 癸亥 六白 三碧 九紫 四綠 七赤 一白
  # ]

  def Xdate.fc_jiu_xing_array(ganzhi_index)
    key = "jiu_xing.gan_zhi_#{ganzhi_index + 1}"
    s = Xdate.nongminli_str(key)
    return s.split(",")
  end
  def Xdate.fc_9star(nGanZhiDay,nSegmentIndex)
  	# a = @@fc_9_star[nGanZhiDay]
    a = Xdate.fc_jiu_xing_array(nGanZhiDay)
  	nSegno = @@fc_segment_no[nSegmentIndex]
  	return a[nSegno - 1]
  end

  # 3、十二神排法
# 說明:以地支月為排法,該月之地支日開始排。
# ￼￼￼￼ 日 日 日 日 日 日 日 日 日 日 日 日
#    A 子 丑 寅 卯 辰 巳 午 未 申 酉 戌 亥 ￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼
# 月 寅 開 閉 建 除 滿 平 定 執 破 危 成 收
# 月 卯 收 開 閉 建 除 滿 平 定 執 破 危 成
# 月 辰 成 收 開 閉 建 除 滿 平 定 執 破 危
# 月 巳 危 成 收 開 閉 建 除 滿 平 定 執 破
# 月 午 破 危 成 收 開 閉 建 除 滿 平 定 執
# 月 未 執 破 危 成 收 開 閉 建 除 滿 平 定
# 月 申 定 執 破 危 成 收 開 閉 建 除 滿 平
# 月 酉 平 定 執 破 危 成 收 開 閉 建 除 滿
# 月 戌 滿 平 定 執 破 危 成 收 開 閉 建 除
# 月 亥 除 滿 平 定 執 破 危 成 收 開 閉 建
# 月 子 建 除 滿 平 定 執 破 危 成 收 開 閉
# 月 丑 閉 建 除 滿 平 定 執 破 危 成 收 開
  @@fc_12_god = {
   # 月     日  子   丑   寅   卯   辰   巳   午   未   申   酉   戌   亥 ￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼￼
    "e3"  =>  [10,11,0,1,2,3,4,5,6,7,8,9],
    "e4"  =>  [9,10,11,0,1,2,3,4,5,6,7,8],
    "e5"  =>  [8,9,10,11,0,1,2,3,4,5,6,7],
    "e6"  =>  [7,8,9,10,11,0,1,2,3,4,5,6],
    "e7"  =>  [6,7,8,9,10,11,0,1,2,3,4,5],
    "e8"  =>  [5,6,7,8,9,10,11,0,1,2,3,4],
    "e9"  =>  [4,5,6,7,8,9,10,11,0,1,2,3],
    "e10" =>  [3,4,5,6,7,8,9,10,11,0,1,2],
    "e11" =>  [2,3,4,5,6,7,8,9,10,11,0,1],
    "e12" =>  [1,2,3,4,5,6,7,8,9,10,11,0],
    "e1"  =>  [0,1,2,3,4,5,6,7,8,9,10,11],
    "e2"  =>  [11,0,1,2,3,4,5,6,7,8,9,10]
  }
  def Xdate.fc_12god(nMonthEarth,nDayEarth)
  	a = @@fc_12_god["e#{nMonthEarth}"]
  	b = Xdate.fc_12god_ss_item(a[nDayEarth - 1])
  	return b[1]
  end
#    項目	宜	忌
  # @@fc_12_god_suitable_scruple = [
  #   [ 0,"建",["上樑","入學","結婚","動土","立柱","醫療","出行吉"],["掘井","乘船"]],
  #   [ 1,"除",["祭祀","藥之調合"],["婚姻","出行","掘井"]],
  #   [ 2,"滿",["移徙","裁衣","開店","祭祀事","出行","栽植"],["葬儀"]],
  #   [ 3,"平",["嫁娶","造屋","移徙","裁衣","相談"],["掘溝","栽植"]],
  #   [ 4,"定",["祭祀","嫁娶","移徙","造屋","庸人","買牛馬"],["訴訟","出行"]],
  #   [ 5,"執",["造屋","播種","嫁娶","掘井"],["移徙","出行","開車凶"]],
  #   [ 6,"破",["出臘吉。此日大凶"],["諸事不吉"]],
  #   [ 7,"危",["諸事不吉"],["登山","乘馬","乘船","出行凶"]],
  #   [ 8,"成",["萬事成就，造屋","嫁娶","入學","出行","開店","播種"],["訴訟","爭鬥"]],
  #   [ 9,"收",["入學","嫁娶","造屋","買賣","移徙","播種"],["葬儀","出行"]],
  #   [10,"開",["入學","嫁娶","造屋","移徙","開業，願望成就"],["葬儀不淨之事"]],
  #   [11,"閉",["萬事閉塞之日","築堤","埋池","埋穴"],["開店","出行","其他凶"]]
  # ]
  def Xdate.fc_12god_suitable_scruple(nMonthEarth,nDayEarth)
  	a = @@fc_12_god["e#{nMonthEarth}"]
  	b = Xdate.fc_12god_ss_item(a[nDayEarth - 1])
  	return b[0],b[1],b[2],b[3]
  end
  def Xdate.fc_shi_er_shen_str(key)
    key = "shi_er_shen.#{key}"
    return Xdate.nongminli_str(key)
  end
  def Xdate.fc_shi_er_shen_yi_array(key)
    s = Xdate.fc_shi_er_shen_str(key)
    return s.split(",")
  end
  def Xdate.fc_shi_er_shen_ji_array(key)
    s = Xdate.fc_shi_er_shen_str(key)
    return s.split(",")
  end
  def Xdate.fc_shi_er_shen_yi_ji(n12godIndex)
    shi_er_shen = Xdate.fc_shi_er_shen_str("id_#{n12godIndex}")
    yi = Xdate.fc_shi_er_shen_yi_array("yi_#{n12godIndex}")
    ji = Xdate.fc_shi_er_shen_ji_array("ji_#{n12godIndex}")
    return [n12godIndex,shi_er_shen,yi,ji]
  end
  def Xdate.fc_12god_ss_item(n12godIndex)
    return Xdate.fc_shi_er_shen_yi_ji(n12godIndex)
  	# return @@fc_12_god_suitable_scruple[n12godIndex]
  end

  # 4、八卦排法
#   冬至順排 代碼 夏至逆排
#      乾    0     坤
#      兌    1     艮
#      離    2 ￼   坎
#      震    3     巽
#      巽    4 ￼   震
#      坎    5     離
#      艮    6 ￼   兌
#      坤    7 ￼   乾
# ￼ 以冬至日及夏至日排法轉換
#   例如:冬至日則以乾卦開始。
  # @@fc_8trigrams_winter = ["乾","兌","離","震","巽","坎","艮","坤"]
  @@fc_8trigrams_winter_id = ["1","2","3","4","5","6","7","8"]

  # @@fc_8trigrams_summer = ["坤","艮","坎","巽","震","離","兌","乾"]
  @@fc_8trigrams_summer_id = ["8","7","6","5","4","3","2","1"]
  def Xdate.fc_ba_gua_str(id)
    key = "ba_gua.id_#{id}"
    return Xdate.nongminli_str(key)
  end
  def Xdate.fc_8trigrams(nWYear, nWMonth, nWDate,nSegmentIndex)
  	if (nSegmentIndex.between?(Xdate::SEGMENT_SUMMER,Xdate::SEGMENT_WINTER - 1)) then
  	  nDays = Xdate.GetDaysAfterSegment(nWYear, nWMonth, nWDate,Xdate::SEGMENT_SUMMER)
  	  nIndex = (nDays - 1) % 8
  	  id = @@fc_8trigrams_summer_id[nIndex]
      return Xdate.fc_ba_gua_str(id)
  	else
  	  nDays = Xdate.GetDaysAfterSegment(nWYear, nWMonth, nWDate,Xdate::SEGMENT_WINTER)
  	  nIndex = (nDays - 1) % 8
      id = @@fc_8trigrams_winter_id[nIndex]
      return Xdate.fc_ba_gua_str(id)
    end
  end

  # 5、日沖煞
  # @@fc_day_ghost = [
  #                                        # 日    五行  日沖  沖生肖  沖方位
  #   ["甲子","金","戊午","沖馬","煞南",54],  # 甲子   金   戊午   沖馬   煞南  54
  #   ["乙丑","金","己未","沖羊","煞東",55],  # 乙丑￼  金￼  己未   沖羊   煞東￼ 55
  #   ["丙寅","火","庚申","沖猴","煞北",56],  # 丙寅￼  火   庚申   沖猴   煞北  56
  #   ["丁卯","火","辛酉","沖雞","煞西",57],  # 丁卯   火   辛酉   沖雞   煞西 ￼57
  #   ["戊辰","木","壬戌","沖狗","煞南",58],  # 戊辰   木   壬戌   沖狗   煞南 ￼58
  #   ["己巳","木","癸亥","沖猪","煞東",59],  # 己巳   木   癸亥   沖猪   煞東  59
  #   ["庚午","土","甲子","沖鼠","煞北", 0],  # 庚午   土   甲子   沖鼠   煞北   0
  #   ["辛未","土","乙丑","沖牛","煞西", 1],  # 辛未   土   乙丑   沖牛   煞西   1
  #   ["壬申","金","丙寅","沖虎","煞南", 2],  # 壬申   金   丙寅   沖虎   煞南   2
  #   ["癸酉","金","丁卯","沖兔","煞東", 3],  # 癸酉   金   丁卯   沖兔   煞東   3
  #   ["甲戌","火","戊辰","沖龍","煞北", 4],  # 甲戌   火   戊辰   沖龍   煞北   4
  #   ["乙亥","火","己巳","沖蛇","煞西", 5],  # 乙亥   火   己巳   沖蛇   煞西   5
  #   ["丙子","水","庚午","沖馬","煞南", 6],  # 丙子   水   庚午   沖馬   煞南   6
  #   ["丁丑","水","辛未","沖羊","煞東", 7],  # 丁丑   水   辛未   沖羊   煞東   7
  #   ["戊寅","土","壬申","沖猴","煞北", 8],  # 戊寅   土   壬申   沖猴   煞北   8
  #   ["己卯","土","癸酉","沖雞","煞西", 9],  # 己卯   土   癸酉   沖雞   煞西   9
  #   ["庚辰","金","甲戌","沖狗","煞南",10],  # 庚辰   金   甲戌   沖狗   煞南  10
  #   ["辛巳","金","乙亥","沖猪","煞東",11],  # 辛巳   金   乙亥   沖猪   煞東  11
  #   ["壬午","木","丙子","沖鼠","煞北",12],  # 壬午   木   丙子   沖鼠   煞北  12
  #   ["癸未","木","丁丑","沖牛","煞西",13],  # 癸未   木   丁丑   沖牛   煞西  13
  #   ["甲申","水","戊寅","沖虎","煞南",14],  # 甲申   水   戊寅   沖虎   煞南  14
  #   ["乙酉","水","己卯","沖兔","煞東",15],  # 乙酉   水   己卯   沖兔   煞東  15
  #   ["丙戌","土","庚辰","沖龍","煞北",16],  # 丙戌   土   庚辰   沖龍   煞北  16
  #   ["丁亥","土","辛巳","沖蛇","煞西",17],  # 丁亥   土   辛巳   沖蛇   煞西  17
  #   ["戊子","火","壬午","沖馬","煞南",18],  # 戊子   火   壬午   沖馬   煞南  18
  #   ["己丑","火","癸未","沖羊","煞東",19],  # 己丑   火   癸未   沖羊   煞東  19
  #   ["庚寅","木","甲申","沖猴","煞北",20],  # 庚寅   木   甲申   沖猴   煞北  20
  #   ["辛卯","木","乙酉","沖雞","煞西",21],  # 辛卯   木   乙酉   沖雞   煞西  21
  #   ["壬辰","水","丙戌","沖狗","煞南",22],  # 壬辰   水   丙戌   沖狗   煞南  22
  #   ["癸巳","水","丁亥","沖猪","煞東",23],  # 癸巳   水   丁亥   沖猪   煞東  23
  #   ["甲午","金","戊子","沖鼠","煞北",24],  # 甲午   金   戊子   沖鼠   煞北  24
  #   ["乙未","金","己丑","沖牛","煞西",25],  # 乙未   金   己丑   沖牛   煞西  25
  #   ["丙申","火","庚寅","沖虎","煞南",26],  # 丙申   火   庚寅   沖虎   煞南  26
  #   ["丁酉","火","辛卯","沖兔","煞東",27],  # 丁酉   火   辛卯   沖兔   煞東  27
  #   ["戊戌","木","壬辰","沖龍","煞北",28],  # 戊戌   木   壬辰   沖龍   煞北  28
  #   ["己亥","木","癸巳","沖蛇","煞西",29],  # 己亥   木   癸巳   沖蛇   煞西  29
  #   ["庚子","土","甲午","沖馬","煞南",30],  # 庚子   土   甲午   沖馬   煞南  30
  #   ["辛丑","土","乙未","沖羊","煞東",31],  # 辛丑   土   乙未   沖羊   煞東  31
  #   ["壬寅","金","丙申","沖猴","煞北",32],  # 壬寅   金   丙申   沖猴   煞北  32
  #   ["癸卯","金","丁酉","沖雞","煞西",33],  # 癸卯   金   丁酉   沖雞   煞西  33
  #   ["甲辰","火","戊戌","沖狗","煞南",34],  # 甲辰   火   戊戌   沖狗   煞南  34
  #   ["乙巳","火","己亥","沖猪","煞東",35],  # 乙巳   火   己亥   沖猪   煞東  35
  #   ["丙午","水","庚子","沖鼠","煞北",36],  # 丙午   水   庚子   沖鼠   煞北  36
  #   ["丁未","水","辛丑","沖牛","煞西",37],  # 丁未   水   辛丑   沖牛   煞西  37
  #   ["戊申","土","壬寅","沖虎","煞南",38],  # 戊申   土   壬寅   沖虎   煞南  38
  #   ["己酉","土","癸卯","沖兔","煞東",39],  # 己酉   土   癸卯   沖兔   煞東  39
  #   ["庚戌","金","甲辰","沖龍","煞北",40],  # 庚戌   金   甲辰   沖龍   煞北  40
  #   ["辛亥","金","乙巳","沖蛇","煞西",41],  # 辛亥   金   乙巳   沖蛇   煞西  41
  #   ["壬子","木","丙午","沖馬","煞南",42],  # 壬子   木   丙午   沖馬   煞南  42
  #   ["癸丑","木","丁未","沖羊","煞東",43],  # 癸丑   木   丁未   沖羊   煞東  43
  #   ["甲寅","水","戊申","沖猴","煞北",44],  # 甲寅   水   戊申   沖猴   煞北  44
  #   ["乙卯","水","己酉","沖雞","煞西",45],  # 乙卯   水   己酉   沖雞   煞西  45
  #   ["丙辰","土","庚戌","沖狗","煞南",46],  # 丙辰   土   庚戌   沖狗   煞南  46
  #   ["丁巳","土","辛亥","沖猪","煞東",47],  # 丁巳   土   辛亥   沖猪   煞東  47
  #   ["戊午","火","壬子","沖鼠","煞北",48],  # 戊午   火   壬子   沖鼠   煞北  48
  #   ["己未","火","癸丑","沖牛","煞西",49],  # 己未   火   癸丑   沖牛   煞西  49
  #   ["庚申","木","甲寅","沖虎","煞南",50],  # 庚申   木   甲寅   沖虎   煞南  50
  #   ["辛酉","木","乙卯","沖兔","煞東",51],  # 辛酉   木   乙卯   沖兔   煞東  51
  #   ["壬戌","水","丙辰","沖龍","煞北",52],  # 壬戌   水   丙辰   沖龍   煞北  52
  #   ["癸亥","水","丁巳","沖蛇","煞西",53]   # 癸亥   水   丁巳   沖蛇   煞西  53
  # ]
  @@fc_day_ghost_key = [
                                                                                        # 日    五行  日沖  沖生肖  沖方位
    ["gan_zhi_1","wu_xing_4","gan_zhi_55","chong_sheng_xiao_7","sha_fang_xiang_3",54],  # 甲子   金   戊午   沖馬   煞南  54
    ["gan_zhi_2","wu_xing_4","gan_zhi_56","chong_sheng_xiao_8","sha_fang_xiang_1",55],  # 乙丑￼  金￼  己未   沖羊   煞東￼ 55
    ["gan_zhi_3","wu_xing_2","gan_zhi_57","chong_sheng_xiao_9","sha_fang_xiang_4",56],  # 丙寅￼  火   庚申   沖猴   煞北  56
    ["gan_zhi_4","wu_xing_2","gan_zhi_58","chong_sheng_xiao_10","sha_fang_xiang_2",57],  # 丁卯   火   辛酉   沖雞   煞西 ￼57
    ["gan_zhi_5","wu_xing_1","gan_zhi_59","chong_sheng_xiao_11","sha_fang_xiang_3",58],  # 戊辰   木   壬戌   沖狗   煞南 ￼58
    ["gan_zhi_6","wu_xing_1","gan_zhi_60","chong_sheng_xiao_12","sha_fang_xiang_1",59],  # 己巳   木   癸亥   沖猪   煞東  59
    ["gan_zhi_7","wu_xing_3","gan_zhi_1","chong_sheng_xiao_1","sha_fang_xiang_4", 0],  # 庚午   土   甲子   沖鼠   煞北   0
    ["gan_zhi_8","wu_xing_3","gan_zhi_2","chong_sheng_xiao_2","sha_fang_xiang_2", 1],  # 辛未   土   乙丑   沖牛   煞西   1
    ["gan_zhi_9","wu_xing_4","gan_zhi_3","chong_sheng_xiao_3","sha_fang_xiang_3", 2],  # 壬申   金   丙寅   沖虎   煞南   2
    ["gan_zhi_10","wu_xing_4","gan_zhi_4","chong_sheng_xiao_4","sha_fang_xiang_1", 3],  # 癸酉   金   丁卯   沖兔   煞東   3
    ["gan_zhi_11","wu_xing_2","gan_zhi_5","chong_sheng_xiao_5","sha_fang_xiang_4", 4],  # 甲戌   火   戊辰   沖龍   煞北   4
    ["gan_zhi_12","wu_xing_2","gan_zhi_6","chong_sheng_xiao_6","sha_fang_xiang_2", 5],  # 乙亥   火   己巳   沖蛇   煞西   5
    ["gan_zhi_13","wu_xing_5","gan_zhi_7","chong_sheng_xiao_7","sha_fang_xiang_3", 6],  # 丙子   水   庚午   沖馬   煞南   6
    ["gan_zhi_14","wu_xing_5","gan_zhi_8","chong_sheng_xiao_8","sha_fang_xiang_1", 7],  # 丁丑   水   辛未   沖羊   煞東   7
    ["gan_zhi_15","wu_xing_3","gan_zhi_9","chong_sheng_xiao_9","sha_fang_xiang_4", 8],  # 戊寅   土   壬申   沖猴   煞北   8
    ["gan_zhi_16","wu_xing_3","gan_zhi_10","chong_sheng_xiao_10","sha_fang_xiang_2", 9],  # 己卯   土   癸酉   沖雞   煞西   9
    ["gan_zhi_17","wu_xing_4","gan_zhi_11","chong_sheng_xiao_11","sha_fang_xiang_3",10],  # 庚辰   金   甲戌   沖狗   煞南  10
    ["gan_zhi_18","wu_xing_4","gan_zhi_12","chong_sheng_xiao_12","sha_fang_xiang_1",11],  # 辛巳   金   乙亥   沖猪   煞東  11
    ["gan_zhi_19","wu_xing_1","gan_zhi_13","chong_sheng_xiao_1","sha_fang_xiang_4",12],  # 壬午   木   丙子   沖鼠   煞北  12
    ["gan_zhi_20","wu_xing_1","gan_zhi_14","chong_sheng_xiao_2","sha_fang_xiang_2",13],  # 癸未   木   丁丑   沖牛   煞西  13
    ["gan_zhi_21","wu_xing_5","gan_zhi_15","chong_sheng_xiao_3","sha_fang_xiang_3",14],  # 甲申   水   戊寅   沖虎   煞南  14
    ["gan_zhi_22","wu_xing_5","gan_zhi_16","chong_sheng_xiao_4","sha_fang_xiang_1",15],  # 乙酉   水   己卯   沖兔   煞東  15
    ["gan_zhi_23","wu_xing_3","gan_zhi_17","chong_sheng_xiao_5","sha_fang_xiang_4",16],  # 丙戌   土   庚辰   沖龍   煞北  16
    ["gan_zhi_24","wu_xing_3","gan_zhi_18","chong_sheng_xiao_6","sha_fang_xiang_2",17],  # 丁亥   土   辛巳   沖蛇   煞西  17
    ["gan_zhi_25","wu_xing_2","gan_zhi_19","chong_sheng_xiao_7","sha_fang_xiang_3",18],  # 戊子   火   壬午   沖馬   煞南  18
    ["gan_zhi_26","wu_xing_2","gan_zhi_20","chong_sheng_xiao_8","sha_fang_xiang_1",19],  # 己丑   火   癸未   沖羊   煞東  19
    ["gan_zhi_27","wu_xing_1","gan_zhi_21","chong_sheng_xiao_9","sha_fang_xiang_4",20],  # 庚寅   木   甲申   沖猴   煞北  20
    ["gan_zhi_28","wu_xing_1","gan_zhi_22","chong_sheng_xiao_10","sha_fang_xiang_2",21],  # 辛卯   木   乙酉   沖雞   煞西  21
    ["gan_zhi_29","wu_xing_5","gan_zhi_23","chong_sheng_xiao_11","sha_fang_xiang_3",22],  # 壬辰   水   丙戌   沖狗   煞南  22
    ["gan_zhi_30","wu_xing_5","gan_zhi_24","chong_sheng_xiao_12","sha_fang_xiang_1",23],  # 癸巳   水   丁亥   沖猪   煞東  23
    ["gan_zhi_31","wu_xing_4","gan_zhi_25","chong_sheng_xiao_1","sha_fang_xiang_4",24],  # 甲午   金   戊子   沖鼠   煞北  24
    ["gan_zhi_32","wu_xing_4","gan_zhi_26","chong_sheng_xiao_2","sha_fang_xiang_2",25],  # 乙未   金   己丑   沖牛   煞西  25
    ["gan_zhi_33","wu_xing_2","gan_zhi_27","chong_sheng_xiao_3","sha_fang_xiang_3",26],  # 丙申   火   庚寅   沖虎   煞南  26
    ["gan_zhi_34","wu_xing_2","gan_zhi_28","chong_sheng_xiao_4","sha_fang_xiang_1",27],  # 丁酉   火   辛卯   沖兔   煞東  27
    ["gan_zhi_35","wu_xing_1","gan_zhi_29","chong_sheng_xiao_5","sha_fang_xiang_4",28],  # 戊戌   木   壬辰   沖龍   煞北  28
    ["gan_zhi_36","wu_xing_1","gan_zhi_30","chong_sheng_xiao_6","sha_fang_xiang_2",29],  # 己亥   木   癸巳   沖蛇   煞西  29
    ["gan_zhi_37","wu_xing_3","gan_zhi_31","chong_sheng_xiao_7","sha_fang_xiang_3",30],  # 庚子   土   甲午   沖馬   煞南  30
    ["gan_zhi_38","wu_xing_3","gan_zhi_32","chong_sheng_xiao_8","sha_fang_xiang_1",31],  # 辛丑   土   乙未   沖羊   煞東  31
    ["gan_zhi_39","wu_xing_4","gan_zhi_33","chong_sheng_xiao_9","sha_fang_xiang_4",32],  # 壬寅   金   丙申   沖猴   煞北  32
    ["gan_zhi_40","wu_xing_4","gan_zhi_34","chong_sheng_xiao_10","sha_fang_xiang_2",33],  # 癸卯   金   丁酉   沖雞   煞西  33
    ["gan_zhi_41","wu_xing_2","gan_zhi_35","chong_sheng_xiao_11","sha_fang_xiang_3",34],  # 甲辰   火   戊戌   沖狗   煞南  34
    ["gan_zhi_42","wu_xing_2","gan_zhi_36","chong_sheng_xiao_12","sha_fang_xiang_1",35],  # 乙巳   火   己亥   沖猪   煞東  35
    ["gan_zhi_43","wu_xing_5","gan_zhi_37","chong_sheng_xiao_1","sha_fang_xiang_4",36],  # 丙午   水   庚子   沖鼠   煞北  36
    ["gan_zhi_44","wu_xing_5","gan_zhi_38","chong_sheng_xiao_2","sha_fang_xiang_2",37],  # 丁未   水   辛丑   沖牛   煞西  37
    ["gan_zhi_45","wu_xing_3","gan_zhi_39","chong_sheng_xiao_3","sha_fang_xiang_3",38],  # 戊申   土   壬寅   沖虎   煞南  38
    ["gan_zhi_46","wu_xing_3","gan_zhi_40","chong_sheng_xiao_4","sha_fang_xiang_1",39],  # 己酉   土   癸卯   沖兔   煞東  39
    ["gan_zhi_47","wu_xing_4","gan_zhi_41","chong_sheng_xiao_5","sha_fang_xiang_4",40],  # 庚戌   金   甲辰   沖龍   煞北  40
    ["gan_zhi_48","wu_xing_4","gan_zhi_42","chong_sheng_xiao_6","sha_fang_xiang_2",41],  # 辛亥   金   乙巳   沖蛇   煞西  41
    ["gan_zhi_49","wu_xing_1","gan_zhi_43","chong_sheng_xiao_7","sha_fang_xiang_3",42],  # 壬子   木   丙午   沖馬   煞南  42
    ["gan_zhi_50","wu_xing_1","gan_zhi_44","chong_sheng_xiao_8","sha_fang_xiang_1",43],  # 癸丑   木   丁未   沖羊   煞東  43
    ["gan_zhi_51","wu_xing_5","gan_zhi_45","chong_sheng_xiao_9","sha_fang_xiang_4",44],  # 甲寅   水   戊申   沖猴   煞北  44
    ["gan_zhi_52","wu_xing_5","gan_zhi_46","chong_sheng_xiao_10","sha_fang_xiang_2",45],  # 乙卯   水   己酉   沖雞   煞西  45
    ["gan_zhi_53","wu_xing_3","gan_zhi_47","chong_sheng_xiao_11","sha_fang_xiang_3",46],  # 丙辰   土   庚戌   沖狗   煞南  46
    ["gan_zhi_54","wu_xing_3","gan_zhi_48","chong_sheng_xiao_12","sha_fang_xiang_1",47],  # 丁巳   土   辛亥   沖猪   煞東  47
    ["gan_zhi_55","wu_xing_2","gan_zhi_49","chong_sheng_xiao_1","sha_fang_xiang_4",48],  # 戊午   火   壬子   沖鼠   煞北  48
    ["gan_zhi_56","wu_xing_2","gan_zhi_50","chong_sheng_xiao_2","sha_fang_xiang_2",49],  # 己未   火   癸丑   沖牛   煞西  49
    ["gan_zhi_57","wu_xing_1","gan_zhi_51","chong_sheng_xiao_3","sha_fang_xiang_3",50],  # 庚申   木   甲寅   沖虎   煞南  50
    ["gan_zhi_58","wu_xing_1","gan_zhi_52","chong_sheng_xiao_4","sha_fang_xiang_1",51],  # 辛酉   木   乙卯   沖兔   煞東  51
    ["gan_zhi_59","wu_xing_5","gan_zhi_53","chong_sheng_xiao_5","sha_fang_xiang_4",52],  # 壬戌   水   丙辰   沖龍   煞北  52
    ["gan_zhi_60","wu_xing_5","gan_zhi_54","chong_sheng_xiao_6","sha_fang_xiang_2",53]   # 癸亥   水   丁巳   沖蛇   煞西  53
  ]
  def Xdate.fc_ri_chong_sha_str(key)
    key = "ri_chong_sha.#{key}"
    return Xdate.nongminli_str(key)
  end
  def Xdate.fc_gan_zhi_str(key)
    return Xdate.nongminli_str(key)
  end
  def Xdate.fc_wu_xing_str(key)
    return Xdate.nongminli_str(key)
  end
  def Xdate.fc_rushanimal(nGanZhiDay)
  	# a = @@fc_day_ghost[nGanZhiDay]
    # return a[3]
    a = @@fc_day_ghost_key[nGanZhiDay]
  	return Xdate.fc_ri_chong_sha_str(a[3])
  end
  def Xdate.fc_rushdirection(nGanZhiDay)
  	# a = @@fc_day_ghost[nGanZhiDay]
  	# return a[4]
    a = @@fc_day_ghost_key[nGanZhiDay]
    return Xdate.fc_ri_chong_sha_str(a[4])
  end
  def Xdate.fc_dayfive(nGanZhiDay)
  	# a = @@fc_day_ghost[nGanZhiDay]
  	# return a[1]
    a = @@fc_day_ghost_key[nGanZhiDay]
    return Xdate.fc_wu_xing_str(a[1])
  end
  def Xdate.fc_dayganzhi(nGanZhiDay)
  	# a = @@fc_day_ghost[nGanZhiDay]
  	# return a[0]
    a = @@fc_day_ghost_key[nGanZhiDay]
    return Xdate.fc_gan_zhi_str(a[0])
  end
  # 1924年為甲子年
  # 幾歲的人犯沖
  def Xdate.fc_rushyearold(nGanZhiDay,nEYear)
    # a = @@fc_day_ghost[nGanZhiDay]
  	a = @@fc_day_ghost_key[nGanZhiDay]
  	nGanZhiYear = a[5]
    nYearOld = Xdate.GetYearOld(1924 + nGanZhiYear,nEYear)
    a_2 = Xdate.fc_gan_zhi_str(a[2])
    if (nYearOld > 60) then
      nYearOld -= 60
    end
    if (nYearOld > 20) then
      return "(#{a_2}" + Xdate.nongminli_str("nian") + "#{nYearOld}" + Xdate.nongminli_str("sui") + ")"
      # return "(#{a[2]}年#{nYearOld}歲)"
    else
      return "(#{a_2}" + Xdate.nongminli_str("nian") + "#{nYearOld}" + Xdate.nongminli_str("sui") + ",#{nYearOld + 60}" + Xdate.nongminli_str("sui") + ")"
  	  # return "(#{a[2]}年#{nYearOld}歲,#{nYearOld + 60}歲)"
    end
  end

  # 6、吉門吉時
  #                                                                 子   丑   寅   卯   辰   巳   午   未   申   酉   戌   亥
  #                                                       日干 日干 11-1 1-3 3-5  5-7  7-9  9-11 11-13 13-15 15-17 17-19 19-21 21-23
  # @@fc_day_goodhour_value = [
  #   ["甲子","甲午",27,26,25,24,23,22,27,26,25,24,23,22], # 甲子,甲午, 27,  26,  25,  24,  23,  22,  27,  26,  25,  24,  23,  22
  #   ["乙丑","乙未",25,24,23,22,21,20,25,24,23,22,21,20], # 乙丑,乙未, 25,  24,  23,  22,  21,  20,  25,  24,  23,  22,  21,  20
  #   ["丙寅","丙申",23,22,21,20,19,18,23,22,21,20,19,18], # 丙寅,丙申, 23,  22,  21,  20,  19,  18,  23,  22,  21,  20,  19,  18
  #   ["丁卯","丁酉",21,20,19,18,17,16,21,20,19,18,17,16], # 丁卯,丁酉, 21,  20,  19,  18,  17,  16,  21,  20,  19,  18,  17,  16
  #   ["戊辰","戊戌",19,18,17,16,15,14,19,18,17,16,15,14], # 戊辰,戊戌, 19,  18,  17,  16,  15,  14,  19,  18,  17,  16,  15,  14
  #   ["己巳","己亥",22,21,20,19,18,17,22,21,20,19,18,17], # 己巳,己亥, 22,  21,  20,  19,  18,  17,  22,  21,  20,  19,  18,  17
  #   ["庚午","庚子",26,25,24,23,22,21,26,25,24,23,22,21], # 庚午,庚子, 26,  25,  24,  23,  22,  21,  26,  25,  24,  23,  22,  21
  #   ["辛未","辛丑",24,23,22,21,20,19,24,23,22,21,20,19], # 辛未,辛丑, 24,  23,  22,  21,  20,  19,  24,  23,  22,  21,  20,  19
  #   ["壬申","壬寅",22,21,20,19,18,17,22,21,20,19,18,17], # 壬申,壬寅, 22,  21,  20,  19,  18,  17,  22,  21,  20,  19,  18,  17
  #   ["癸酉","癸卯",20,19,18,17,16,15,20,19,18,17,16,15], # 癸酉,癸卯, 20,  19,  18,  17,  16,  15,  20,  19,  18,  17,  16,  15
  #   ["甲戌","甲辰",23,22,21,20,19,18,23,22,21,20,19,18], # 甲戌,甲辰, 23,  22,  21,  20,  19,  18,  23,  22,  21,  20,  19,  18
  #   ["乙亥","乙巳",21,20,19,18,17,16,21,20,19,18,17,16], # 乙亥,乙巳, 21,  20,  19,  18,  17,  16,  21,  20,  19,  18,  17,  16
  #   ["丙子","丙午",25,24,23,22,21,20,25,24,23,22,21,20], # 丙子,丙午, 25,  24,  23,  22,  21,  20,  25,  24,  23,  22,  21,  20
  #   ["丁丑","丁未",23,22,21,20,19,18,23,22,21,20,19,18], # 丁丑,丁未, 23,  22,  21,  20,  19,  18,  23,  22,  21,  20,  19,  18
  #   ["戊寅","戊申",21,20,19,18,17,16,21,20,19,18,17,16], # 戊寅,戊申, 21,  20,  19,  18,  17,  16,  21,  20,  19,  18,  17,  16
  #   ["己卯","己酉",24,23,22,21,20,19,24,23,22,21,20,19], # 己卯,己酉, 24,  23,  22,  21,  20,  19,  24,  23,  22,  21,  20,  19
  #   ["庚辰","庚戌",22,21,20,19,18,17,22,21,20,19,18,17], # 庚辰,庚戌, 22,  21,  20,  19,  18,  17,  22,  21,  20,  19,  18,  17
  #   ["辛巳","辛亥",20,19,18,17,16,15,20,19,18,17,16,15], # 辛巳,辛亥, 20,  19,  18,  17,  16,  15,  20,  19,  18,  17,  16,  15
  #   ["壬午","壬子",24,23,22,21,20,19,24,23,22,21,20,19], # 壬午,壬子, 24,  23,  22,  21,  20,  19,  24,  23,  22,  21,  20,  19
  #   ["癸未","癸丑",22,21,20,19,18,17,22,21,20,19,18,17], # 癸未,癸丑, 22,  21,  20,  19,  18,  17,  22,  21,  20,  19,  18,  17
  #   ["甲申","甲寅",25,24,23,22,21,20,25,24,23,22,21,20], # 甲申,甲寅, 25,  24,  23,  22,  21,  20,  25,  24,  23,  22,  21,  20
  #   ["乙酉","乙卯",23,22,21,20,19,18,23,22,21,20,19,18], # 乙酉,乙卯, 23,  22,  21,  20,  19,  18,  23,  22,  21,  20,  19,  18
  #   ["丙戌","丙辰",21,20,19,18,17,16,21,20,19,18,17,16], # 丙戌,丙辰, 21,  20,  19,  18,  17,  16,  21,  20,  19,  18,  17,  16
  #   ["丁亥","丁巳",19,18,17,16,15,14,19,18,17,16,15,14], # 丁亥,丁巳, 19,  18,  17,  16,  15,  14,  19,  18,  17,  16,  15,  14
  #   ["戊子","戊午",23,22,21,20,19,18,23,22,21,20,19,18], # 戊子,戊午, 23,  22,  21,  20,  19,  18,  23,  22,  21,  20,  19,  18
  #   ["己丑","己未",26,25,24,23,22,21,26,25,24,23,22,21], # 己丑,己未, 26,  25,  24,  23,  22,  21,  26,  25,  24,  23,  22,  21
  #   ["庚寅","庚申",24,23,22,21,20,19,24,23,22,21,20,19], # 庚寅,庚申, 24,  23,  22,  21,  20,  19,  24,  23,  22,  21,  20,  19
  #   ["辛卯","辛酉",22,21,20,19,18,17,22,21,20,19,18,17], # 辛卯,辛酉, 22,  21,  20,  19,  18,  17,  22,  21,  20,  19,  18,  17
  #   ["壬辰","壬戌",20,19,18,17,16,15,20,19,18,17,16,15], # 壬辰,壬戌, 20,  19,  18,  17,  16,  15,  20,  19,  18,  17,  16,  15
  #   ["癸巳","癸亥",18,17,16,15,14,13,18,17,16,15,14,13]  # 癸巳,癸亥, 18,  17,  16,  15,  14,  13,  18,  17,  16,  15,  14,  13
  # ]
  @@fc_day_goodhour_value = [
    ["gan_zhi_1","gan_zhi_31",27,26,25,24,23,22,27,26,25,24,23,22],
    ["gan_zhi_2","gan_zhi_32",25,24,23,22,21,20,25,24,23,22,21,20],
    ["gan_zhi_3","gan_zhi_33",23,22,21,20,19,18,23,22,21,20,19,18],
    ["gan_zhi_4","gan_zhi_34",21,20,19,18,17,16,21,20,19,18,17,16],
    ["gan_zhi_5","gan_zhi_35",19,18,17,16,15,14,19,18,17,16,15,14],
    ["gan_zhi_6","gan_zhi_36",22,21,20,19,18,17,22,21,20,19,18,17],
    ["gan_zhi_7","gan_zhi_37",26,25,24,23,22,21,26,25,24,23,22,21],
    ["gan_zhi_8","gan_zhi_38",24,23,22,21,20,19,24,23,22,21,20,19],
    ["gan_zhi_9","gan_zhi_39",22,21,20,19,18,17,22,21,20,19,18,17],
    ["gan_zhi_10","gan_zhi_40",20,19,18,17,16,15,20,19,18,17,16,15],
    ["gan_zhi_11","gan_zhi_41",23,22,21,20,19,18,23,22,21,20,19,18],
    ["gan_zhi_12","gan_zhi_42",21,20,19,18,17,16,21,20,19,18,17,16],
    ["gan_zhi_13","gan_zhi_43",25,24,23,22,21,20,25,24,23,22,21,20],
    ["gan_zhi_14","gan_zhi_44",23,22,21,20,19,18,23,22,21,20,19,18],
    ["gan_zhi_15","gan_zhi_45",21,20,19,18,17,16,21,20,19,18,17,16],
    ["gan_zhi_16","gan_zhi_46",24,23,22,21,20,19,24,23,22,21,20,19],
    ["gan_zhi_17","gan_zhi_47",22,21,20,19,18,17,22,21,20,19,18,17],
    ["gan_zhi_18","gan_zhi_48",20,19,18,17,16,15,20,19,18,17,16,15],
    ["gan_zhi_19","gan_zhi_49",24,23,22,21,20,19,24,23,22,21,20,19],
    ["gan_zhi_20","gan_zhi_50",22,21,20,19,18,17,22,21,20,19,18,17],
    ["gan_zhi_21","gan_zhi_51",25,24,23,22,21,20,25,24,23,22,21,20],
    ["gan_zhi_22","gan_zhi_52",23,22,21,20,19,18,23,22,21,20,19,18],
    ["gan_zhi_23","gan_zhi_53",21,20,19,18,17,16,21,20,19,18,17,16],
    ["gan_zhi_24","gan_zhi_54",19,18,17,16,15,14,19,18,17,16,15,14],
    ["gan_zhi_25","gan_zhi_55",23,22,21,20,19,18,23,22,21,20,19,18],
    ["gan_zhi_26","gan_zhi_56",26,25,24,23,22,21,26,25,24,23,22,21],
    ["gan_zhi_27","gan_zhi_57",24,23,22,21,20,19,24,23,22,21,20,19],
    ["gan_zhi_28","gan_zhi_58",22,21,20,19,18,17,22,21,20,19,18,17],
    ["gan_zhi_29","gan_zhi_59",20,19,18,17,16,15,20,19,18,17,16,15],
    ["gan_zhi_30","gan_zhi_60",18,17,16,15,14,13,18,17,16,15,14,13]
  ]
  # 輸出紅色為吉時、黑色為凶時。
  # @@fc_day_goodhour_desc = {
  #   "d13" => [1,"吉","十三數吉","十三遁甲見陰陽 西方乾宮老婦娘 手執棉布將小女 此時有應主亨昌作事出門 推合陰陽 營謀嫁娶 起造營昌 子孫官貴 祿遇時良"],
  #   "d14" => [0,"凶","十四數凶","遁甲俱十四 東北有人至 白馬或青驢 弓箭身穿紫有人著紫 或是皂衣 前有大坑 後有白虎凶惡相及 不宜嫁娶 休去遠行 凡事難遇"],
  #   "d15" => [1,"吉","十五數吉","南方有少女 牽牛抱嬰孩 東方車與馬 更有男赶來 日月在前光明麗天 駟馬時從 千事皆好 非但殯葬 亦宜修造"],
  #   "d16" => [1,"吉","十六數吉","東北有禽蜚 西北白色衣 老人持杖橛 脫卻西黃衣 出遇財珍兼逢酒食 營謀起造 必遇貴人 交易婚姻 得宜獲福"],
  #   "d17" => [0,"凶","十七數凶","西北風雨雲 西方孝服子 兩箇駕車人 十七必逢是 死屍在前病符在後 營謀嫁娶 所求災咎 若遇此時 必獲良久"],
  #   "d18" => [1,"吉","十八數吉","南方有少婦 犬子或青驢 東方忽見行 剋應必無疑 不宜嫁娶萬事吉昌 若遇此時 升官未必"],
  #   "d19" => [0,"凶","十九數凶","鴉鵲並弓弩 銅鐵東方舞 更有著白衣 西方喚驢狗 俱是凶兆不是吉祥 故難動用"],
  #   "d20" => [0,"凶","二十數大凶","北方有人驢 乾坤僧道尼 屠赶豬羊去 諸凶必應之 前有津梁後有豬羊 若遇此時 萬事俱映"],
  #   "d21" => [1,"吉","二十一數吉","東方有孝子 更與婦人逢 老人持更板 西北應乾宮 此時所遊青雲上蓋 百事吉昌 不敢移改"],
  #   "d22" => [1,"吉","二十二數吉","求乞持杖子 西方鼓笛聲 坤兌應更奇 提籠有婦人 乘車駕馬攜酒相看 萬事皆吉 必遇平安"],
  #   "d23" => [0,"凶","二十三數中凶","男婦攜寶劍 南方執鐵瓶 文字並豬羊 毀罵病迎門 萬事重疊難於遠行 動則遇侵 靜則安寧"],
  #   "d24" => [1,"吉","二十四數吉","南方送福仙 少女手執錢 兌宮羊又至 物色黑青纏 飛鳥在前玉士在後 刑見長官 嫁娶旡咎"],
  #   "d25" => [1,"吉","二十五數吉","驢子共猿猻 南方有婦人 西方老人至 剋應最為靈 見四足物百事稱心 出逢長者 更遇知音"],
  #   "d26" => [1,"吉","二十六數吉","北方公文行 南方小兒啼以上諸時 此時最吉 但試其事 萬無一失"],
  #   "d27" => [0,"凶","二十七數凶","台馬共紫驢 西方一騎出以上諸數 此數最惡 先賢詳看 明應不錯"]
  # }
  @@fc_day_goodhour_desc = {
    "d13" => [1,"ji","shu.d13_ji","shuo_ming.d13_ji"],
    "d14" => [0,"xiong","shu.d14_xiong","shuo_ming.d14_xiong"],
    "d15" => [1,"ji","shu.d15_ji","shuo_ming.d15_ji"],
    "d16" => [1,"ji","shu.d16_ji","shuo_ming.d16_ji"],
    "d17" => [0,"xiong","shu.d17_xiong","shuo_ming.d17_xiong"],
    "d18" => [1,"ji","shu.d18_ji","shuo_ming.d18_ji"],
    "d19" => [0,"xiong","shu.d19_xiong","shuo_ming.d19_xiong"],
    "d20" => [0,"xiong","shu.d20_da_xiong","shuo_ming.d20_da_xiong"],
    "d21" => [1,"ji","shu.d21_ji","shuo_ming.d21_ji"],
    "d22" => [1,"ji","shu.d22_ji","shuo_ming.d22_ji"],
    "d23" => [0,"xiong","shu.d23_zhong_xiong","shuo_ming.d23_zhong_xiong"],
    "d24" => [1,"ji","shu.d24_ji","shuo_ming.d24_ji"],
    "d25" => [1,"ji","shu.d25_ji","shuo_ming.d25_ji"],
    "d26" => [1,"ji","shu.d26_ji","shuo_ming.d26_ji"],
    "d27" => [0,"xiong","shu.d27_xiong","shuo_ming.d27_xiong"]
  }
  def Xdate.fc_ji_xiong_str(key)
    return Xdate.nongminli_str("ji_xiong.#{key}")
  end
  def Xdate.fc_goodhour(nGanZhiDay)
  	goodhour = Array.new
  	good_count = Array.new
  	good_desc = Array.new
  	nGanZhiDayIndex = nGanZhiDay % 30
  	a = @@fc_day_goodhour_value[nGanZhiDayIndex]
  	(2..13).each do |i|
  	  desc = @@fc_day_goodhour_desc["d#{a[i]}"]
  	  if (desc[0] == 1) then
  	  	goodhour.push(Earth.GetName(i - 1))
  	  	good_count.push(a[i])
        desc_2 = Xdate.fc_ji_xiong_str(desc[2])
        desc_3 = Xdate.fc_ji_xiong_str(desc[3])
  	  	good_desc.push("#{desc_2}:#{desc_3}")
  	  end
  	end
  	return goodhour,good_count,good_desc
  end
  def Xdate.fc_badhour(nGanZhiDay)
  	badhour = Array.new
  	bad_count = Array.new
  	bad_desc = Array.new
  	nGanZhiDayIndex = nGanZhiDay % 30
  	arrayIndex = nGanZhiDay / 30
  	a = @@fc_day_goodhour_value[nGanZhiDayIndex]
  	(2..13).each do |i|
  	  desc = @@fc_day_goodhour_desc["d#{a[i]}"]
  	  if (desc[0] == 0) then
  	  	badhour.push(Earth.GetName(i - 1))
  	  	bad_count.push(a[i])
        desc_2 = Xdate.fc_ji_xiong_str(desc[2])
        desc_3 = Xdate.fc_ji_xiong_str(desc[3])
        bad_desc.push("#{desc_2}:#{desc_3}")
  	  end
  	end
  	return badhour,bad_count,bad_desc
  end


  # 7、計算寧合日
  # 以節氣月為主
              # 日    甲子 乙丑 甲寅 乙卯 甲辰 乙巳 甲午 乙未 甲申 乙酉 甲戌 乙亥
              #       丙子 丁丑 丙寅 丁卯 丙辰 丁巳 丙午 丁未 丙申 丁酉 丙戌 丁亥
              #       戊子 己丑 戊寅 己卯 戊辰 己巳 戊午 己未 戊申 己酉 戊戌 己亥
              #       庚子 辛丑 庚寅 辛卯 庚辰 辛巳 庚午 辛未 庚申 辛酉 庚戌 辛酉
              # ￼     壬子 癸丑 壬寅 癸卯 壬辰 癸巳 壬午 癸未 壬申 癸酉 壬戌 癸酉

  # @@fc_ning_he_ri = {
  #   "m1"  => ["正月"  ,"和","","寧","和","寧","","和","寧","","","",""],
  #   "m2"  => ["二月"  ,"","和","","","","和","","寧","和","","和","和"],
  #   "m3"  => ["三月"  ,"和","","寧","","","和","和","","和","寧","",""],
  #   "m4"  => ["四月"  ,"和","寧","","和","","","和","和","","和","寧",""],
  #   "m5"  => ["五月"  ,"","寧","和","","和","","","和","","","和",""],
  #   "m6"  => ["六月"  ,"和","","寧","寧","","寧","","","和","","",""],
  #   "m7"  => ["七月"  ,"和","","","和","寧","","和","","寧","和","寧",""],
  #   "m8"  => ["八月"  ,"","和","和","","和","和","和","","","","和","和"],
  #   "m9"  => ["九月"  ,"和","","","和","","寧","","寧","","","",""],
  #   "m10" => ["十月"  ,"和","寧","","和","寧","","和","寧","","和","",""],
  #   "m11" => ["十一月","","寧","和","","和","和","","和","","","和","和"],
  #   "m12" => ["十二月","和","","寧","寧","","寧","和","","寧","","","寧"]
  # }
  @@fc_ning_he_ri = {
    "m1"  => ["yue_1"  ,"he","","ning","he","ning","","he","ning","","","",""],
    "m2"  => ["yue_2"  ,"","he","","","","he","","ning","he","","he","he"],
    "m3"  => ["yue_3"  ,"he","","ning","","","he","he","","he","ning","",""],
    "m4"  => ["yue_4"  ,"he","ning","","he","","","he","he","","he","ning",""],
    "m5"  => ["yue_5"  ,"","ning","he","","he","","","he","","","he",""],
    "m6"  => ["yue_6"  ,"he","","ning","ning","","ning","","","he","","",""],
    "m7"  => ["yue_7"  ,"he","","","he","ning","","he","","ning","he","ning",""],
    "m8"  => ["yue_8"  ,"","he","he","","he","he","he","","","","he","he"],
    "m9"  => ["yue_9"  ,"he","","","he","","ning","","ning","","","",""],
    "m10" => ["yue_10"  ,"he","ning","","he","ning","","he","ning","","he","",""],
    "m11" => ["yue_11","","ning","he","","he","he","","he","","","he","he"],
    "m12" => ["yue_12","he","","ning","ning","","ning","he","","ning","","","ning"]
  }
  def Xdate.fc_ning_he_ri_str(key)
    if (key == "") then
      return ""
    end
    return Xdate.nongminli_str("ning_he_ri.#{key}")
  end
  def Xdate.fc_ning_he_ri(nSMonth,nDayEarth,nGanZhiDay)
  	if ([47,59].include?(nGanZhiDay)) then # 辛亥，癸亥
  		return ""
  	end
  	a = @@fc_ning_he_ri["m#{nSMonth}"] # 辛酉，癸酉
  	if ([9,57].include?(nGanZhiDay)) then
      s = Xdate.fc_ning_he_ri_str(a[nDayEarth])
      s += Xdate.fc_ning_he_ri_str(a[12])
  		return s
  	else
      return Xdate.fc_ning_he_ri_str(a[nDayEarth])
  	end
  end


  # 8、喜神財神等排法
  # 以日之干支找出

  # 六十甲子 喜神 貴神 財神 吉門 生門 開門 噩神 五鬼 死門 胎神
  # @@fc_god_door = [
  #   ["甲子","東北","東北","東南","正北","東北","西北","東南","東南","西南","占門碓外東南"],
  #   ["乙丑","西北","正北","東南","正北","東北","西北","東南","東北","西南","碓磨廁外東南"],
  #   ["丙寅","西南","正西","正西","正北","東北","西北","正南","西南","西南","廚灶爐外正南"],
  #   ["丁卯","正南","西北","正西","西南","正西","正南","正南","正東","正東","倉庫門外正南"],
  #   ["戊辰","東南","東北","正北","西南","正西","正南","正南","正南","正東","房床棲外正南"],
  #   ["己巳","東北","西南","正北","西南","正西","正南","正南","正北","正東","占門床外正南"],
  #   ["庚午","西北","西南","正東","正東","東南","東北","正南","正西","西北","占碓磨外正南"],
  #   ["辛未","西南","正南","正東","正東","東南","東北","西南","西北","西北","廚灶廁外西南"],
  #   ["壬申","正南","正東","正南","正東","東南","東北","西南","東南","西北","倉庫爐外西南"],
  #   ["癸酉","東南","東南","正南","東南","正南","正東","西南","東北","正北","房床門外西南"],
  #   ["甲戌","東北","東北","東南","東南","正南","正東","西南","東南","正北","門雞棲外西南"],
  #   ["乙亥","西北","西南","東南","東南","正南","正東","西南","東北","正北","碓磨床外西南"],
  #   ["丙子","西南","正西","正西","西北","正北","正西","西南","西南","正南","廚灶碓外西南"],
  #   ["丁丑","正南","西北","正西","西北","正北","正西","正西","正東","正南","倉庫廁外西南"],
  #   ["戊寅","東南","東北","正北","西北","正北","正西","正西","正南","正南","房床廁外正南"],
  #   ["己卯","東北","西南","正北","正西","西北","西南","正西","正北","東南","占門廁外正南"],
  #   ["庚辰","西北","東北","正東","正西","西北","西南","正西","正西","東南","碓磨棲外正西"],
  #   ["辛巳","西南","東北","正東","正西","西北","西南","正西","西北","東南","廚灶床外正西"],
  #   ["壬午","正南","正東","正南","東北","正東","正北","西北","東南","正西","倉庫碓外西北"],
  #   ["癸未","東南","正東","正南","東北","正東","正北","西北","東北","正西","房床廁外西北"],
  #   ["甲申","東北","西南","東南","東北","正東","正北","西北","東南","正西","占門爐外西北"],
  #   ["乙酉","西北","西南","東南","正南","西南","東南","西北","東北","東北","碓磨門外西北"],
  #   ["丙戌","西南","正西","正西","正南","西南","東南","西北","西南","東北","廚灶棲外西北"],
  #   ["丁亥","正南","正西","正西","正南","西南","東南","西北","正東","東北","倉庫床外西北"],
  #   ["戊子","東南","東北","正北","正北","東北","西北","正北","正南","西南","房床碓外正北"],
  #   ["己丑","東北","正北","正北","正北","東北","西北","正北","正北","西南","占門廁外正北"],
  #   ["庚寅","西北","東北","正東","正北","東北","西北","正北","正西","西南","碓磨爐外正北"],
  #   ["辛卯","西南","東北","正東","西南","正西","正南","正北","西北","正東","廚灶門外正北"],
  #   ["壬辰","正南","正東","正南","西南","正西","正南","正北","東南","正東","倉庫棲外正北"],
  #   ["癸巳","東南","東南","正南","西南","正西","正南","在天","東北","正東","占房床房內北"],
  #   ["甲午","東北","西南","東南","正東","東南","東北","在天","東南","西北","占門碓房內北"],
  #   ["乙未","西北","西南","東南","正東","東南","東北","在天","東北","西北","碓磨廁房內北"],
  #   ["丙申","西南","正西","正西","正東","東南","東北","在天","西南","西北","廚灶爐房內北"],
  #   ["丁酉","正南","西北","正西","東南","正南","正東","在天","正東","正北","倉庫門房內北"],
  #   ["戊戌","東南","東北","正北","東南","正南","正東","在天","正南","正北","房床棲房內南"],
  #   ["己亥","東北","西南","正北","東南","正南","正東","在天","正北","正北","占門床房內南"],
  #   ["庚子","西北","東北","正東","西北","正北","正西","在天","正西","正南","占碓磨房內南"],
  #   ["辛丑","西南","東北","正東","西北","正北","正西","在天","西北","正南","廚灶廁房內南"],
  #   ["壬寅","正南","正東","正南","西北","正北","正西","在天","東南","正南","倉庫爐房內南"],
  #   ["癸卯","東南","正東","正南","正西","西北","西南","在天","東北","東南","房床門房內南"],
  #   ["甲辰","東北","西南","東南","正西","西北","西南","在天","東南","東南","門雞棲房內東"],
  #   ["乙巳","西北","正北","東南","正西","西北","西南","在天","東北","東南","碓磨床房內東"],
  #   ["丙午","西南","西北","正西","東北","正東","正北","在天","西南","正西","廚灶碓房內東"],
  #   ["丁未","正南","西北","正西","東北","正東","正北","在天","正東","正西","倉庫廁房內東"],
  #   ["戊申","東南","西南","正北","東北","正東","正北","在天","正南","正西","房床爐房內東"],
  #   ["己酉","東北","西南","正北","正南","西南","東南","東北","正北","東北","佔大門外東北"],
  #   ["庚戌","西北","西南","正東","正南","西南","東南","東北","正西","東北","碓磨棲外東北"],
  #   ["辛亥","西南","正南","正東","正南","西南","東南","東北","西北","東北","廚灶床外東北"],
  #   ["壬子","正南","正東","正南","正北","東北","西北","東北","東南","西南","倉庫碓外東北"],
  #   ["癸丑","東南","正東","正南","正北","東北","西北","東北","東北","西南","房床廁外東北"],
  #   ["甲寅","東北","東北","東南","正北","東北","西北","東北","東南","西南","占門爐外東北"],
  #   ["乙卯","西北","西南","東南","西南","正西","正南","正東","東北","正東","碓磨門外正東"],
  #   ["丙辰","西南","正西","正西","西南","正西","正南","正東","西南","正東","廚灶棲外正東"],
  #   ["丁巳","正南","正西","正西","西南","正西","正南","正東","正東","正東","倉庫床外正東"],
  #   ["戊午","東南","西南","正北","正東","東南","東北","正東","正南","西北","房床碓外正東"],
  #   ["己未","東北","西南","正北","正東","東南","東北","正東","正北","西北","占門廁外正東"],
  #   ["庚申","西北","西南","正東","正東","東南","東北","東南","正西","西北","碓磨爐外東南"],
  #   ["辛酉","西南","東北","正東","東南","正南","正東","東南","西北","正北","廚灶門外東南"],
  #   ["壬戌","正南","正東","正南","東南","正南","正東","東南","東南","正北","倉庫棲外東南"],
  #   ["癸亥","東南","正東","正南","東南","正南","正東","東南","東北","正北","占房床外東南"]
  # ]
  @@fc_god_door = [
    ["gan_zhi_1","dong_bei","dong_bei","dong_nan","zheng_bei","dong_bei","xi_bei","dong_nan","dong_nan","xi_nan","tai_shen.gan_zhi_1"],
    ["gan_zhi_2","xi_bei","zheng_bei","dong_nan","zheng_bei","dong_bei","xi_bei","dong_nan","dong_bei","xi_nan","tai_shen.gan_zhi_2"],
    ["gan_zhi_3","xi_nan","zheng_xi","zheng_xi","zheng_bei","dong_bei","xi_bei","zheng_nan","xi_nan","xi_nan","tai_shen.gan_zhi_3"],
    ["gan_zhi_4","zheng_nan","xi_bei","zheng_xi","xi_nan","zheng_xi","zheng_nan","zheng_nan","zheng_dong","zheng_dong","tai_shen.gan_zhi_4"],
    ["gan_zhi_5","dong_nan","dong_bei","zheng_bei","xi_nan","zheng_xi","zheng_nan","zheng_nan","zheng_nan","zheng_dong","tai_shen.gan_zhi_5"],
    ["gan_zhi_6","dong_bei","xi_nan","zheng_bei","xi_nan","zheng_xi","zheng_nan","zheng_nan","zheng_bei","zheng_dong","tai_shen.gan_zhi_6"],
    ["gan_zhi_7","xi_bei","xi_nan","zheng_dong","zheng_dong","dong_nan","dong_bei","zheng_nan","zheng_xi","xi_bei","tai_shen.gan_zhi_7"],
    ["gan_zhi_8","xi_nan","zheng_nan","zheng_dong","zheng_dong","dong_nan","dong_bei","xi_nan","xi_bei","xi_bei","tai_shen.gan_zhi_8"],
    ["gan_zhi_9","zheng_nan","zheng_dong","zheng_nan","zheng_dong","dong_nan","dong_bei","xi_nan","dong_nan","xi_bei","tai_shen.gan_zhi_9"],
    ["gan_zhi_10","dong_nan","dong_nan","zheng_nan","dong_nan","zheng_nan","zheng_dong","xi_nan","dong_bei","zheng_bei","tai_shen.gan_zhi_10"],
    ["gan_zhi_11","dong_bei","dong_bei","dong_nan","dong_nan","zheng_nan","zheng_dong","xi_nan","dong_nan","zheng_bei","tai_shen.gan_zhi_11"],
    ["gan_zhi_12","xi_bei","xi_nan","dong_nan","dong_nan","zheng_nan","zheng_dong","xi_nan","dong_bei","zheng_bei","tai_shen.gan_zhi_12"],
    ["gan_zhi_13","xi_nan","zheng_xi","zheng_xi","xi_bei","zheng_bei","zheng_xi","xi_nan","xi_nan","zheng_nan","tai_shen.gan_zhi_13"],
    ["gan_zhi_14","zheng_nan","xi_bei","zheng_xi","xi_bei","zheng_bei","zheng_xi","zheng_xi","zheng_dong","zheng_nan","tai_shen.gan_zhi_14"],
    ["gan_zhi_15","dong_nan","dong_bei","zheng_bei","xi_bei","zheng_bei","zheng_xi","zheng_xi","zheng_nan","zheng_nan","tai_shen.gan_zhi_15"],
    ["gan_zhi_16","dong_bei","xi_nan","zheng_bei","zheng_xi","xi_bei","xi_nan","zheng_xi","zheng_bei","dong_nan","tai_shen.gan_zhi_16"],
    ["gan_zhi_17","xi_bei","dong_bei","zheng_dong","zheng_xi","xi_bei","xi_nan","zheng_xi","zheng_xi","dong_nan","tai_shen.gan_zhi_17"],
    ["gan_zhi_18","xi_nan","dong_bei","zheng_dong","zheng_xi","xi_bei","xi_nan","zheng_xi","xi_bei","dong_nan","tai_shen.gan_zhi_18"],
    ["gan_zhi_19","zheng_nan","zheng_dong","zheng_nan","dong_bei","zheng_dong","zheng_bei","xi_bei","dong_nan","zheng_xi","tai_shen.gan_zhi_19"],
    ["gan_zhi_20","dong_nan","zheng_dong","zheng_nan","dong_bei","zheng_dong","zheng_bei","xi_bei","dong_bei","zheng_xi","tai_shen.gan_zhi_20"],
    ["gan_zhi_21","dong_bei","xi_nan","dong_nan","dong_bei","zheng_dong","zheng_bei","xi_bei","dong_nan","zheng_xi","tai_shen.gan_zhi_21"],
    ["gan_zhi_22","xi_bei","xi_nan","dong_nan","zheng_nan","xi_nan","dong_nan","xi_bei","dong_bei","dong_bei","tai_shen.gan_zhi_22"],
    ["gan_zhi_23","xi_nan","zheng_xi","zheng_xi","zheng_nan","xi_nan","dong_nan","xi_bei","xi_nan","dong_bei","tai_shen.gan_zhi_23"],
    ["gan_zhi_24","zheng_nan","zheng_xi","zheng_xi","zheng_nan","xi_nan","dong_nan","xi_bei","zheng_dong","dong_bei","tai_shen.gan_zhi_24"],
    ["gan_zhi_25","dong_nan","dong_bei","zheng_bei","zheng_bei","dong_bei","xi_bei","zheng_bei","zheng_nan","xi_nan","tai_shen.gan_zhi_25"],
    ["gan_zhi_26","dong_bei","zheng_bei","zheng_bei","zheng_bei","dong_bei","xi_bei","zheng_bei","zheng_bei","xi_nan","tai_shen.gan_zhi_26"],
    ["gan_zhi_27","xi_bei","dong_bei","zheng_dong","zheng_bei","dong_bei","xi_bei","zheng_bei","zheng_xi","xi_nan","tai_shen.gan_zhi_27"],
    ["gan_zhi_28","xi_nan","dong_bei","zheng_dong","xi_nan","zheng_xi","zheng_nan","zheng_bei","xi_bei","zheng_dong","tai_shen.gan_zhi_28"],
    ["gan_zhi_29","zheng_nan","zheng_dong","zheng_nan","xi_nan","zheng_xi","zheng_nan","zheng_bei","dong_nan","zheng_dong","tai_shen.gan_zhi_29"],
    ["gan_zhi_30","dong_nan","dong_nan","zheng_nan","xi_nan","zheng_xi","zheng_nan","在天","dong_bei","zheng_dong","tai_shen.gan_zhi_30"],
    ["gan_zhi_31","dong_bei","xi_nan","dong_nan","zheng_dong","dong_nan","dong_bei","在天","dong_nan","xi_bei","tai_shen.gan_zhi_31"],
    ["gan_zhi_32","xi_bei","xi_nan","dong_nan","zheng_dong","dong_nan","dong_bei","在天","dong_bei","xi_bei","tai_shen.gan_zhi_32"],
    ["gan_zhi_33","xi_nan","zheng_xi","zheng_xi","zheng_dong","dong_nan","dong_bei","在天","xi_nan","xi_bei","tai_shen.gan_zhi_33"],
    ["gan_zhi_34","zheng_nan","xi_bei","zheng_xi","dong_nan","zheng_nan","zheng_dong","在天","zheng_dong","zheng_bei","tai_shen.gan_zhi_34"],
    ["gan_zhi_35","dong_nan","dong_bei","zheng_bei","dong_nan","zheng_nan","zheng_dong","在天","zheng_nan","zheng_bei","tai_shen.gan_zhi_35"],
    ["gan_zhi_36","dong_bei","xi_nan","zheng_bei","dong_nan","zheng_nan","zheng_dong","在天","zheng_bei","zheng_bei","tai_shen.gan_zhi_36"],
    ["gan_zhi_37","xi_bei","dong_bei","zheng_dong","xi_bei","zheng_bei","zheng_xi","在天","zheng_xi","zheng_nan","tai_shen.gan_zhi_37"],
    ["gan_zhi_38","xi_nan","dong_bei","zheng_dong","xi_bei","zheng_bei","zheng_xi","在天","xi_bei","zheng_nan","tai_shen.gan_zhi_38"],
    ["gan_zhi_39","zheng_nan","zheng_dong","zheng_nan","xi_bei","zheng_bei","zheng_xi","在天","dong_nan","zheng_nan","tai_shen.gan_zhi_39"],
    ["gan_zhi_40","dong_nan","zheng_dong","zheng_nan","zheng_xi","xi_bei","xi_nan","在天","dong_bei","dong_nan","tai_shen.gan_zhi_40"],
    ["gan_zhi_41","dong_bei","xi_nan","dong_nan","zheng_xi","xi_bei","xi_nan","在天","dong_nan","dong_nan","tai_shen.gan_zhi_41"],
    ["gan_zhi_42","xi_bei","zheng_bei","dong_nan","zheng_xi","xi_bei","xi_nan","在天","dong_bei","dong_nan","tai_shen.gan_zhi_42"],
    ["gan_zhi_43","xi_nan","xi_bei","zheng_xi","dong_bei","zheng_dong","zheng_bei","在天","xi_nan","zheng_xi","tai_shen.gan_zhi_43"],
    ["gan_zhi_44","zheng_nan","xi_bei","zheng_xi","dong_bei","zheng_dong","zheng_bei","在天","zheng_dong","zheng_xi","tai_shen.gan_zhi_44"],
    ["gan_zhi_45","dong_nan","xi_nan","zheng_bei","dong_bei","zheng_dong","zheng_bei","在天","zheng_nan","zheng_xi","tai_shen.gan_zhi_45"],
    ["gan_zhi_46","dong_bei","xi_nan","zheng_bei","zheng_nan","xi_nan","dong_nan","dong_bei","zheng_bei","dong_bei","tai_shen.gan_zhi_46"],
    ["gan_zhi_47","xi_bei","xi_nan","zheng_dong","zheng_nan","xi_nan","dong_nan","dong_bei","zheng_xi","dong_bei","tai_shen.gan_zhi_47"],
    ["gan_zhi_48","xi_nan","zheng_nan","zheng_dong","zheng_nan","xi_nan","dong_nan","dong_bei","xi_bei","dong_bei","tai_shen.gan_zhi_48"],
    ["gan_zhi_49","zheng_nan","zheng_dong","zheng_nan","zheng_bei","dong_bei","xi_bei","dong_bei","dong_nan","xi_nan","tai_shen.gan_zhi_49"],
    ["gan_zhi_50","dong_nan","zheng_dong","zheng_nan","zheng_bei","dong_bei","xi_bei","dong_bei","dong_bei","xi_nan","tai_shen.gan_zhi_50"],
    ["gan_zhi_51","dong_bei","dong_bei","dong_nan","zheng_bei","dong_bei","xi_bei","dong_bei","dong_nan","xi_nan","tai_shen.gan_zhi_51"],
    ["gan_zhi_52","xi_bei","xi_nan","dong_nan","xi_nan","zheng_xi","zheng_nan","zheng_dong","dong_bei","zheng_dong","tai_shen.gan_zhi_52"],
    ["gan_zhi_53","xi_nan","zheng_xi","zheng_xi","xi_nan","zheng_xi","zheng_nan","zheng_dong","xi_nan","zheng_dong","tai_shen.gan_zhi_53"],
    ["gan_zhi_54","zheng_nan","zheng_xi","zheng_xi","xi_nan","zheng_xi","zheng_nan","zheng_dong","zheng_dong","zheng_dong","tai_shen.gan_zhi_54"],
    ["gan_zhi_55","dong_nan","xi_nan","zheng_bei","zheng_dong","dong_nan","dong_bei","zheng_dong","zheng_nan","xi_bei","tai_shen.gan_zhi_55"],
    ["gan_zhi_56","dong_bei","xi_nan","zheng_bei","zheng_dong","dong_nan","dong_bei","zheng_dong","zheng_bei","xi_bei","tai_shen.gan_zhi_56"],
    ["gan_zhi_57","xi_bei","xi_nan","zheng_dong","zheng_dong","dong_nan","dong_bei","dong_nan","zheng_xi","xi_bei","tai_shen.gan_zhi_57"],
    ["gan_zhi_58","xi_nan","dong_bei","zheng_dong","dong_nan","zheng_nan","zheng_dong","dong_nan","xi_bei","zheng_bei","tai_shen.gan_zhi_58"],
    ["gan_zhi_59","zheng_nan","zheng_dong","zheng_nan","dong_nan","zheng_nan","zheng_dong","dong_nan","dong_nan","zheng_bei","tai_shen.gan_zhi_59"],
    ["gan_zhi_60","dong_nan","zheng_dong","zheng_nan","dong_nan","zheng_nan","zheng_dong","dong_nan","dong_bei","zheng_bei","tai_shen.gan_zhi_60"]
  ]

# 說明:六十甲子所對應之日期所沖煞之方向。
# 本項只輸出財神、喜神、胎神三項。
  def Xdate.fc_liu_shi_jia_zi_men_str(key)
    return Xdate.nongminli_str(key)
  end
  def Xdate.fc_richgod(nGanZhiDay)
  	a = @@fc_god_door[nGanZhiDay]
  	key = a[3]
    return Xdate.fc_liu_shi_jia_zi_men_str(key)
  end
  def Xdate.fc_gladgod(nGanZhiDay)
  	a = @@fc_god_door[nGanZhiDay]
  	key = a[1]
    return Xdate.fc_liu_shi_jia_zi_men_str(key)
  end
  def Xdate.fc_birthgod(nGanZhiDay)
  	a = @@fc_god_door[nGanZhiDay]
  	key = a[10]
    return Xdate.fc_liu_shi_jia_zi_men_str(key)
  end


  # 9、國曆假日表
  # @@fc_westholiday = {
  #   "m1d1" => "中華民國 元旦",
  #   "m1d11" => "司法節",
  #   "m2d28" => "和平紀念日",
  #   "m3d8" => "婦女節",
  #   "m3d12" => "國父逝世紀念日 （植樹節）",
  #   "m3d14" => "反侵略日",
  #   "m3d29" => "革命先烈紀念日",
  #   "m4d4" => "兒童節",
  #   "m5d1" => "勞動節",
  #   "m5d4" => "文藝節",
  #   "m6d3" => "禁煙節",
  #   "m6d9" => "鐵路節",
  #   "m8d8" => "父親節",
  #   "m9d1" => "記者節",
  #   "m9d3" => "軍人節",
  #   "m9d21" => "國家防災日",
  #   "m9d28" => "孔子誕辰紀念日 （教師節）",
  #   "m10d10" => "國慶日",
  #   "m10d21" => "華僑節",
  #   "m10d24" => "臺灣聯合國日",
  #   "m10d25" => "臺灣光復節",
  #   "m10d31" => "蔣公誕辰紀念日",
  #   "m11d12" => "國父誕辰紀念日",
  #   "m12d25" => "行憲紀念日"
  # }
  @@fc_guo_li_jia_ri = [
                  "m1d1",
                  "m1d11",
                  "m2d28",
                  "m3d8",
                  "m3d12",
                  "m3d14",
                  "m3d29",
                  "m4d4",
                  "m5d1",
                  "m5d4",
                  "m6d3",
                  "m6d9",
                  "m8d8",
                  "m9d1",
                  "m9d3",
                  "m9d21",
                  "m9d28",
                  "m10d10",
                  "m10d21",
                  "m10d24",
                  "m10d25",
                  "m10d31",
                  "m11d12",
                  "m12d25"
                ]
  # @@fc_motherday = "母親節（五月第二週）"
  # @@fc_ancestor = "民族掃墓節（節氣－%{segment}）"
  def Xdate.fc_guo_li_jia_ri_str(key)
    return Xdate.nongminli_str("guo_li_jia_ri.#{key}")
  end
  def Xdate.fc_westholiday(nWYear,nWMonth,nWDate,nSegmentIndex)
  	sw = "m#{nWMonth}d#{nWDate}"
  	# s = @@fc_westholiday[sw]
  	if (@@fc_guo_li_jia_ri.index(sw) == nil) then
  		s = ""
  		if ([4,5] == [nWMonth,nWDate]) then
  		  # 民族掃墓節
        min_zu_sao_mu_jie = Xdate.fc_guo_li_jia_ri_str("min_zu_sao_mu_jie")
        s = min_zu_sao_mu_jie % { :segment => Xdate.GetSegmentStr(nSegmentIndex) }
      else
        # 母親節（五月第二週）
        n51Week = Xdate.GetWWeekDay(nWYear, 5, 1)
        nWYear1,nWMonth1,nWDate1 = Xdate.NextNWDate(nWYear,5,1,14 - n51Week)
        if ([nWMonth1, nWDate1] == [nWMonth, nWDate]) then
          s = Xdate.fc_guo_li_jia_ri_str("mu_qin_jie")
        	# s = @@fc_motherday
        end
      end
    else
      s = Xdate.fc_guo_li_jia_ri_str(sw)
  	end
  	return s
  end

  # 10、農曆節日表
  # 以下以太陰月為計算時間。
  # 月	日	農 曆 節 日
  # @@fc_lunarholiday = {
    # "m1d1" => [ 1, 1,"春節、元始天尊聖誕、彌勒尊佛佛辰"],
    # "m1d12" => [ 1,12,"羅千歲千秋"],
    # "m1d13" => [ 1,13,"關聖帝君飛昇"],
    # "m1d15" => [ 1,15,"元宵節、上元天官大地聖誕、門神戶尉千秋、臨水夫人陳靖姑千秋"],
    # "m1d20" => [ 1,20,"天穿日"],
    # "m1d22" => [ 1,22,"武德尊侯沈祖公聖誕"],
    # "m1d24" => [ 1,24,"雷都光耀大帝聖誕"],
    # "m1d4" => [ 1, 4,"孫天醫真人千秋"],
    # "m1d6" => [ 1, 6,"清水祖師佛辰"],
    # "m1d8" => [ 1, 8,"五殿閻羅王聖誕"],
    # "m1d9" => [ 1, 9,"玉皇大帝萬壽"],
    # "m2d1" => [ 2, 1,"一殿秦廣王千秋"],
    # "m2d10" => [ 2,10,"南無慈勝王佛聖誕"],
    # "m2d15" => [ 2,15,"九天玄女娘娘聖誕、太上老君萬壽、精忠岳王千秋"],
    # "m2d16" => [ 2,16,"開漳聖王千秋"],
    # "m2d18" => [ 2,18,"四殿五官王千秋"],
    # "m2d19" => [ 2,19,"觀世音菩薩佛辰"],
    # "m2d2" => [ 2, 2,"福德正神千秋、濟公活佛聖誕、土穀尊神聖誕"],
    # "m2d21" => [ 2,21,"普賢菩薩佛辰"],
    # "m2d25" => [ 2,25,"三山國王千秋"],
    # "m2d26" => [ 2,26,"南宮趙真君聖誕"],
    # "m2d3" => [ 2, 3,"文昌帝君聖誕"],
    # "m2d6" => [ 2, 6,"東華大帝聖誕"],
    # "m2d8" => [ 2, 8,"三殿宋帝王千秋、馬神爺千秋"],
    # "m3d1" => [ 3, 1,"二殿楚江王千秋"],
    # "m3d15" => [ 3,15,"無極老母娘聖誕、保生大帝吳真人千秋、中路財神趙元帥千秋"],
    # "m3d16" => [ 3,16,"準提菩薩佛辰"],
    # "m3d17" => [ 3,17,"南無慈力王佛聖誕"],
    # "m3d18" => [ 3,18,"南天廖將軍聖誕、后土皇神聖誕"],
    # "m3d19" => [ 3,19,"太陽星君聖誕"],
    # "m3d20" => [ 3,20,"註生娘娘千秋"],
    # # "m3d23" => [ 3,23,"媽祖生"],
    # # "m3d23" => [ 3,23,"天上聖母聖誕"],
    # "m3d23" => [ 3,23,"媽祖生、天上聖母聖誕"],
    # "m3d26" => [ 3,26,"鬼谷先師千秋"],
    # "m3d27" => [ 3,27,"七殿泰山王千秋"],
    # # "m3d28" => [ 3,28,"蒼頡先師聖誕"],
    # # "m3d28" => [ 3,28,"東嶽大帝聖誕"],
    # "m3d28" => [ 3,28,"蒼頡先師聖誕、東嶽大帝聖誕"],
    # # "m3d3" => [ 3, 3,"上巳節"],
    # # "m3d3" => [ 3, 3,"玄天上帝萬壽、吳千歲千秋"],
    # "m3d3" => [ 3, 3,"上巳節、玄天上帝萬壽、吳千歲千秋"],
    # "m3d4" => [ 3, 4,"張千歲千秋、五年千歲"],
    # "m3d6" => [ 3, 6,"濟公活佛成道"],
    # "m3d7" => [ 3, 7,"三天主考聖誕"],
    # # "m3d8" => [ 3, 8,"六殿卞城王千秋"],
    # # "m3d8" => [ 3, 8,"趙千歲千秋"],
    # "m3d8" => [ 3, 8,"六殿卞城王千秋、趙千歲千秋"],
    # "m4d1" => [ 4, 1,"八殿都市王千秋"],
    # "m4d14" => [ 4,14,"呂純陽祖師聖誕、孚佑帝君聖誕"],
    # "m4d15" => [ 4,15,"釋迦文佛得道"],
    # "m4d17" => [ 4,17,"十殿轉輪王千秋"],
    # "m4d18" => [ 4,18,"北極紫微帝君千秋"],
    # "m4d21" => [ 4,21,"李托塔天王聖誕、先天朱將軍聖誕"],
    # "m4d24" => [ 4,24,"金光祖師聖誕"],
    # "m4d25" => [ 4,25,"武安尊王千秋"],
    # "m4d26" => [ 4,26,"五榖先帝千秋(神農大帝)、南鯤身李大王爺千秋"],
    # "m4d27" => [ 4,27,"南鯤身范五王爺千秋"],
    # "m4d28" => [ 4,28,"神農先帝萬壽"],
    # "m4d4" => [ 4, 4,"文殊菩薩佛辰"],
    # # "m4d8" => [ 4, 8,"釋迦佛祖萬壽(陽曆)"],
    # # "m4d8" => [ 4, 8,"九殿平等王千秋"],
    # "m4d8" => [ 4, 8,"釋迦佛祖萬壽(陽曆)、九殿平等王千秋"],
    # # "m5d1" => [ 5, 1,"南極長生帝君千秋"],
    # # "m5d1" => [ 5, 1,"封千歲千秋"],
    # "m5d1" => [ 5, 1,"南極長生帝君千秋、封千歲千秋"],
    # "m5d11" => [ 5,11,"天下都城隍爺千秋"],
    # "m5d12" => [ 5,12,"盧千歲千秋"],
    # "m5d13" => [ 5,13,"霞海城隍爺千秋、關平太子千秋"],
    # "m5d17" => [ 5,17,"蕭府王爺千秋"],
    # "m5d18" => [ 5,18,"西王金母聖誕、張府天師聖誕"],
    # "m5d19" => [ 5,19,"九天馬恩師聖誕"],
    # # "m5d5" => [ 5, 5,"端午節"],
    # # "m5d5" => [ 5, 5,"侯千歲千秋"],
    # "m5d5" => [ 5, 5,"端午節、侯千歲千秋"],
    # "m5d6" => [ 5, 6,"清水祖師成道、薛千歲千秋"],
    # "m5d7" => [ 5, 7,"巧聖先師千秋、耿千歲千秋"],
    # "m6d11" => [ 6,11,"田都元帥千秋"],
    # "m6d15" => [ 6,15,"王靈天君聖誕、無極老申老娘壽誕"],
    # "m6d16" => [ 6,16,"先天王靈官聖誕"],
    # "m6d18" => [ 6,18,"南鯤身池二王爺千秋"],
    # "m6d19" => [ 6,19,"觀世音菩薩得道紀念"],
    # "m6d24" => [ 6,24,"關聖帝君聖誕、雷祖大帝聖誕、西秦王爺千秋、南極大帝聖誕"],
    # "m6d3" => [ 6, 3,"韋馱尊佛辰"],
    # "m6d6" => [ 6, 6,"虎爺誕辰千秋"],
    # "m6d8" => [ 6, 8,"四女仙祖壽誕"],
    # "m7d13" => [ 7,13,"大勢至菩薩聖誕"],
    # "m7d14" => [ 7,14,"開基恩主千秋"],
    # # "m7d15" => [ 7,15,"中元節"],
    # # "m7d15" => [ 7,15,"中元地官大帝聖誕"],
    # "m7d15" => [ 7,15,"中元節、中元地官大帝聖誕"],
    # "m7d18" => [ 7,18,"瑤池王母娘娘聖誕"],
    # # "m7d19" => [ 7,19,"天然古佛聖誕"],
    # # "m7d19" => [ 7,19,"值年太歲星君千秋"],
    # "m7d19" => [ 7,19,"天然古佛聖誕、值年太歲星君千秋"],
    # "m7d21" => [ 7,21,"普庵菩薩佛辰"],
    # # "m7d23" => [ 7,23,"南宮柳星君千秋"],
    # # "m7d23" => [ 7,23,"諸葛武侯千秋"],
    # # "m7d23" => [ 7,23,"法主聖君千秋"],
    # "m7d23" => [ 7,23,"南宮柳星君千秋、諸葛武侯千秋、法主聖君千秋"],
    # "m7d24" => [ 7,24,"鄭延平郡王千秋"],
    # "m7d25" => [ 7,25,"武德侯沈祖公千秋"],
    # "m7d30" => [ 7,30,"地藏王菩薩佛辰"],
    # # "m7d7" => [ 7, 7,"七夕"],
    # # "m7d7" => [ 7, 7,"七星娘娘千秋、大成魁星聖誕、衛房聖母聖誕"],
    # "m7d7" => [ 7, 7,"七夕、七星娘娘千秋、大成魁星聖誕、衛房聖母聖誕"],
    # "m8d10" => [ 8,10,"臨水夫人李姑千秋"],
    # "m8d12" => [ 8,12,"何千歲千秋"],
    # # "m8d15" => [ 8,15,"中秋節"],
    # # "m8d15" => [ 8,15,"太陰星君聖誕、陳靖姑成道紀念、南鯤身朱四王爺千秋"],
    # "m8d15" => [ 8,15,"中秋節、太陰星君聖誕、陳靖姑成道紀念、南鯤身朱四王爺千秋"],
    # "m8d18" => [ 8,18,"九天玄女娘娘千秋、開台灣鄭國姓聖誕"],
    # "m8d22" => [ 8,22,"燃燈古佛萬壽、廣澤尊王千秋"],
    # "m8d23" => [ 8,23,"邢天王爺千秋"],
    # "m8d24" => [ 8,24,"南鯤身萬善爺千秋"],
    # "m8d27" => [ 8,27,"至聖孔子先師聖誕"],
    # "m8d29" => [ 8,29,"中華聖母聖誕"],
    # "m8d3" => [ 8, 3,"北斗星君聖誕、九天司命灶君千秋、九天朱恩師聖誕、姜相子牙千秋、徐千歲千秋"],
    # "m8d5" => [ 8, 5,"雷聲普化天尊聖誕"],
    # "m8d8" => [ 8, 8,"瑤池大會"],
    # "m9d1" => [ 9, 1,"南斗星君聖誕"],
    # "m9d13" => [ 9,13,"孟婆尊神千秋"],
    # "m9d15" => [ 9,15,"無極老申娘聖誕、朱聖夫子聖誕、南鯤身吳三王爺千秋、"],
    # "m9d18" => [ 9,18,"倉聖先師千秋"],
    # "m9d19" => [ 9,19,"觀世音菩薩出家紀念"],
    # # "m9d9" => [ 9, 9,"重陽節"],
    # # "m9d9" => [ 9, 9,"斗母星君聖誕、中壇元帥千秋、豐都大帝聖誕、九皇大帝聖誕、天上聖母飛昇"],
    # "m9d9" => [ 9, 9,"重陽節、斗母星君聖誕、中壇元帥千秋、豐都大帝聖誕、九皇大帝聖誕、天上聖母飛昇"],
    # "m10d10" => [10,10,"水僊尊王千秋"],
    # "m10d12" => [10,12,"齊天大聖佛聖"],
    # "m10d15" => [10,15,"下元水官大帝聖誕"],
    # "m10d18" => [10,18,"地母娘娘聖誕"],
    # "m10d22" => [10,22,"青山靈安尊王千秋"],
    # "m10d23" => [10,23,"周倉將軍爺千秋"],
    # "m10d25" => [10,25,"感天大帝許真人千秋"],
    # "m10d27" => [10,27,"紫微星君聖誕"],
    # "m10d3" => [10, 3,"臨水夫人林姑千秋"],
    # "m10d5" => [10, 5,"達摩祖師佛辰"],
    # "m11d11" => [11,11,"太乙救苦天尊聖誕"],
    # "m11d15" => [11,15,"無極老母娘聖誕"],
    # "m11d17" => [11,17,"阿彌陀佛佛誕"],
    # "m11d19" => [11,19,"九蓮菩薩佛誕"],
    # "m11d23" => [11,23,"張仙大帝聖誕"],
    # "m11d4" => [11, 4,"安南尊王千秋"],
    # "m12d1" => [12, 1,"譚千歲千秋"],
    # "m12d16" => [12,16,"福德正神千秋"],
    # "m12d24" => [12,24,"送神"],
    # "m12d25" => [12,25,"天神下降"],
    # # "m12d29" => [12,29,"農曆除夕"],
    # "m12d29" => [12,29,"南斗北斗星君下降、華嚴菩薩佛辰"],
    # # "m12d30" => [12,30,"農曆除夕（有時會在12/29）"],
    # "m12d6" => [12, 6,"普庵祖師聖誕"],
    # "m12d8" => [12, 8,"釋迦文佛成道"]
  # }
  @@fc_nong_li_jia_ri = [
                           "m1d1",
                           "m1d12",
                           "m1d13",
                           "m1d15",
                           "m1d20",
                           "m1d22",
                           "m1d24",
                           "m1d4",
                           "m1d6",
                           "m1d8",
                           "m1d9",
                           "m2d1",
                           "m2d10",
                           "m2d15",
                           "m2d16",
                           "m2d18",
                           "m2d19",
                           "m2d2",
                           "m2d21",
                           "m2d25",
                           "m2d26",
                           "m2d3",
                           "m2d6",
                           "m2d8",
                           "m3d1",
                           "m3d15",
                           "m3d16",
                           "m3d17",
                           "m3d18",
                           "m3d19",
                           "m3d20",
                           "m3d23",
                           "m3d26",
                           "m3d27",
                           "m3d28",
                           "m3d3",
                           "m3d4",
                           "m3d6",
                           "m3d7",
                           "m3d8",
                           "m4d1",
                           "m4d14",
                           "m4d15",
                           "m4d17",
                           "m4d18",
                           "m4d21",
                           "m4d24",
                           "m4d25",
                           "m4d26",
                           "m4d27",
                           "m4d28",
                           "m4d4",
                           "m4d8",
                           "m5d1",
                           "m5d11",
                           "m5d12",
                           "m5d13",
                           "m5d17",
                           "m5d18",
                           "m5d19",
                           "m5d5",
                           "m5d6",
                           "m5d7",
                           "m6d11",
                           "m6d15",
                           "m6d16",
                           "m6d18",
                           "m6d19",
                           "m6d24",
                           "m6d3",
                           "m6d6",
                           "m6d8",
                           "m7d13",
                           "m7d14",
                           "m7d15",
                           "m7d18",
                           "m7d19",
                           "m7d21",
                           "m7d23",
                           "m7d24",
                           "m7d25",
                           "m7d30",
                           "m7d7",
                           "m8d10",
                           "m8d12",
                           "m8d15",
                           "m8d18",
                           "m8d22",
                           "m8d23",
                           "m8d24",
                           "m8d27",
                           "m8d29",
                           "m8d3",
                           "m8d5",
                           "m8d8",
                           "m9d1",
                           "m9d13",
                           "m9d15",
                           "m9d18",
                           "m9d19",
                           "m9d9",
                           "m10d10",
                           "m10d12",
                           "m10d15",
                           "m10d18",
                           "m10d22",
                           "m10d23",
                           "m10d25",
                           "m10d27",
                           "m10d3",
                           "m10d5",
                           "m11d11",
                           "m11d15",
                           "m11d17",
                           "m11d19",
                           "m11d23",
                           "m11d4",
                           "m12d1",
                           "m12d16",
                           "m12d24",
                           "m12d25",
                           "m12d29",
                           "m12d6",
                           "m12d8"
                         ]
  # @@fc_thedaybeforelunarnewyear = "農曆除夕"
  def Xdate.fc_nong_li_jia_ri_str(key)
    return Xdate.nongminli_str("nong_li_jia_ri.#{key}")
  end
  def Xdate.fc_lunarholiday(nEYear,nEMonth,nEDate,bLeapMonth)
  	a = Array.new

  	nEYear1,nEMonth1,nEDate1,bLeapMonth1 = Xdate.PreEDate(nEYear+1,1,1,false)
  	if ([nEYear1,nEMonth1,nEDate1,bLeapMonth1] == [nEYear,nEMonth,nEDate,bLeapMonth]) then
      s = Xdate.fc_nong_li_jia_ri_str("nong_li_chu_xi")
      a.push(s)
  	  # a.push(@@fc_thedaybeforelunarnewyear)
  	end

  	sw = "m#{nEMonth}d#{nEDate}"
  	# s = @@fc_lunarholiday[sw]
    # if (s != nil) then
      # a.push(s[2])
    if (@@fc_nong_li_jia_ri.index(sw) != nil) then
      s = Xdate.fc_nong_li_jia_ri_str(sw)
  	  a.push(s)
  	end

  	return a.join("、")
  end

  # 11、勿探病日
  # "壬寅"、"甲寅"、"壬午"、"庚午"、"乙卯"、"己卯"
  # 說明：日「干支」遇到上述之六日，則為勿探病日，不論在何月份上。
  # @@fc_donotvisitthepatient = "勿探病日"
  @@fc_donotvisitthepatient_ganzhi = [38,50,18,6,51,15]  # "壬寅","甲寅","壬午","庚午","乙卯","己卯"
  def Xdate.fc_wu_tan_bing_ri_str()
    return Xdate.nongminli_str("wu_tan_bing_ri")
  end
  def Xdate.fc_donotvisitthepatient(nGanZhiDay)
  	i = @@fc_donotvisitthepatient_ganzhi.index(nGanZhiDay)
  	if (i == nil) then
  	  return ""
  	end
  	# return @@fc_donotvisitthepatient
    return Xdate.fc_wu_tan_bing_ri_str()
  end

  # 12、喜忌
         # 項目	      宜	                   忌
  # @@fc_28alphard = [
  #   [ 0,"虛",["不論何事，退守則吉"],["開門放水，不論何事，小心退守則吉。"]],
  #   [ 1,"危",["出行","納財"],["起造","埋葬","開門","放水，其它要戒慎"]],
  #   [ 2,"室",["婚禮","祝壽","移徙","建造","祭祀","掘井"],["喪儀"]],
  #   [ 3,"壁",["婚禮","建造","埋葬"],["往南方凶"]],
  #   [ 4,"奎",["出行","裁衣","修屋"],["開張","新築"]],
  #   [ 5,"婁",["婚禮","修屋","造庭"],["往南方凶"]],
  #   [ 6,"胃",["嫁娶","下葬","公事吉"],["裁衣","私事凶"]],
  #   [ 7,"昂",["萬事大吉"],["裁衣凶"]],
  #   [ 8,"畢",["造屋","葬儀","嫁娶","造橋","掘井"],["裁衣凶"]],
  #   [ 9,"觜",["百事皆凶"],["大惡日，建造","下葬","百事皆凶"]],
  #   [10,"參",["求財","旅行","立門","建造皆吉"],["埋葬"]],
  #   [11,"井",["祭祀","播種","建造"],["裁衣"]],
  #   [12,"鬼",["可下葬"],["婚禮","往西方亦凶"]],
  #   [13,"柳",["造作","婚禮"],["喪儀"]],
  #   [14,"星",["婚禮","播種"],["喪儀","裁衣凶"]],
  #   [15,"張",["婚禮","開市","祭祀","祝壽"],[""]],
  #   [16,"翼",["百事皆不利"],["百事皆不宜"]],
  #   [17,"軫",["買田園","入學","建造","婚禮","裁衣","置業"],["向北方旅行兇"]],
  #   [18,"角",["婚禮","旅行","穿新衣","動土","立柱","立門","裁衣","搬遷"],["喪儀"]],
  #   [19,"亢",["婚禮","播種","買賣"],["建屋","下葬"]],
  #   [20,"氐",["買田園","播種","婚禮"],["葬儀","嫁娶"]],
  #   [21,"房",["祭祀","婚姻","上樑","移徙","嫁娶","修造","搬遷"],["買田園","裁衣"]],
  #   [22,"心",["祭祀","移徙","旅行"],["裁衣","祭祀","搬遷","遠行"]],
  #   [23,"尾",["婚禮","造作","嫁娶","建造"],["裁衣"]],
  #   [24,"箕",["建造","開池","開門","造屋","收財"],["婚禮","葬儀","裁衣"]],
  #   [25,"斗",["裁衣","建造","開門","放水"],[""]],
  #   [26,"牛",["建造","開門","放水"],["嫁娶","建造"]],
  #   [27,"女",["學藝"],["喪儀","爭訟","裁衣","嫁娶","建造"]],
  #   [28,"",[""],[""]]  # empty
  # ]
  # @@fc_28alphard_start = {
  #   "2015_1_1" => [25,"斗"],
  #   "2016_1_1" => [26,"牛"],
  #   "2017_1_1" => [ 0,"虛"],
  #   "2018_1_1" => [ 1,"危"],
  #   "2019_1_1" => [ 2,"室"],
  #   "2020_1_1" => [ 3,"壁"],
  #   "2021_1_1" => [ 5,"婁"],
  #   "2022_1_1" => [ 6,"胃"],
  #   "2023_1_1" => [ 7,"昂"],
  #   "2024_1_1" => [ 8,"畢"],
  #   "2025_1_1" => [10,"參"],
  #   "2026_1_1" => [11,"井"],
  #   "2027_1_1" => [12,"鬼"],
  #   "2028_1_1" => [13,"柳"],
  #   "2029_1_1" => [15,"張"],
  #   "2030_1_1" => [16,"翼"],
  #   "2031_1_1" => [17,"軫"],
  #   "2032_1_1" => [18,"角"],
  #   "2033_1_1" => [20,"氐"],
  #   "2034_1_1" => [21,"房"],
  #   "2035_1_1" => [22,"心"],
  #   "2036_1_1" => [23,"尾"],
  #   "2037_1_1" => [25,"斗"],
  #   "2038_1_1" => [26,"牛"],
  #   "2039_1_1" => [27,"女"],
  #   "2040_1_1" => [ 0,"虛"],
  #   "2041_1_1" => [ 2,"室"],
  #   "2042_1_1" => [ 3,"壁"],
  #   "2043_1_1" => [ 4,"奎"],
  #   "2044_1_1" => [ 5,"婁"],
  #   "2045_1_1" => [ 7,"昂"],
  #   "2046_1_1" => [ 8,"畢"],
  #   "2047_1_1" => [ 9,"觜"],
  #   "2048_1_1" => [10,"參"],
  #   "2049_1_1" => [12,"鬼"],
  #   "2050_1_1" => [13,"柳"],
  #   "2051_1_1" => [14,"星"],
  #   "2052_1_1" => [15,"張"],
  #   "2053_1_1" => [17,"軫"],
  #   "2054_1_1" => [18,"角"],
  #   "2055_1_1" => [19,"亢"],
  #   "2056_1_1" => [20,"氐"],
  #   "2057_1_1" => [22,"心"],
  #   "2058_1_1" => [23,"尾"],
  #   "2059_1_1" => [24,"箕"],
  #   "2060_1_1" => [25,"斗"],
  #   "2061_1_1" => [27,"女"],
  #   "2062_1_1" => [ 0,"虛"],
  #   "2063_1_1" => [ 1,"危"],
  #   "2064_1_1" => [ 2,"室"],
  #   "2065_1_1" => [ 4,"奎"],
  #   "2066_1_1" => [18,"角"],
  #   "2067_1_1" => [ 5,"婁"],
  #   "2068_1_1" => [19,"亢"],
  #   "2069_1_1" => [ 7,"昂"],
  #   "2070_1_1" => [21,"房"],
  #   "2071_1_1" => [ 8,"畢"],
  #   "2072_1_1" => [22,"心"],
  #   "2073_1_1" => [10,"參"],
  #   "2074_1_1" => [24,"箕"],
  #   "2075_1_1" => [11,"井"],
  #   "2076_1_1" => [25,"斗"],
  #   "2077_1_1" => [13,"柳"],
  #   "2078_1_1" => [27,"女"],
  #   "2079_1_1" => [14,"星"],
  #   "2080_1_1" => [ 0,"虛"],
  #   "2081_1_1" => [16,"翼"],
  #   "2082_1_1" => [ 2,"室"],
  #   "2083_1_1" => [17,"軫"],
  #   "2084_1_1" => [ 3,"壁"],
  #   "2085_1_1" => [19,"亢"],
  #   "2086_1_1" => [ 6,"胃"],
  #   "2087_1_1" => [20,"氐"],
  #   "2088_1_1" => [ 7,"昂"],
  #   "2089_1_1" => [22,"心"],
  #   "2090_1_1" => [ 9,"觜"],
  #   "2091_1_1" => [23,"尾"],
  #   "2092_1_1" => [10,"參"],
  #   "2093_1_1" => [25,"斗"],
  #   "2094_1_1" => [12,"鬼"],
  #   "2095_1_1" => [26,"牛"],
  #   "2096_1_1" => [13,"柳"],
  #   "2097_1_1" => [ 0,"虛"],
  #   "2098_1_1" => [15,"張"],
  #   "2099_1_1" => [ 1,"危"],
  #   "2100_1_1" => [16,"翼"],
  #   "2101_1_1" => [ 2,"室"],
  #   "2102_1_1" => [17,"軫"],
  #   "2103_1_1" => [ 3,"壁"],
  #   "2104_1_1" => [18,"角"],
  #   "2105_1_1" => [ 6,"胃"],
  #   "2106_1_1" => [20,"氐"],
  #   "2107_1_1" => [ 7,"昂"],
  #   "2108_1_1" => [21,"房"],
  #   "2109_1_1" => [ 9,"觜"],
  #   "2110_1_1" => [23,"尾"],
  #   "2111_1_1" => [10,"參"],
  #   "2112_1_1" => [24,"箕"],
  #   "2113_1_1" => [12,"鬼"],
  #   "2114_1_1" => [26,"牛"],
  #   "2115_1_1" => [13,"柳"],
  #   "2116_1_1" => [27,"女"],
  #   "2117_1_1" => [15,"張"],
  #   "2118_1_1" => [ 1,"危"],
  #   "2119_1_1" => [16,"翼"],
  #   "2120_1_1" => [ 2,"室"],
  #   "2121_1_1" => [18,"角"],
  #   "2122_1_1" => [ 5,"婁"],
  #   "2123_1_1" => [19,"亢"],
  #   "2124_1_1" => [ 6,"胃"],
  #   "2125_1_1" => [21,"房"],
  #   "2126_1_1" => [ 8,"畢"],
  #   "2127_1_1" => [22,"心"],
  #   "2128_1_1" => [ 9,"觜"],
  #   "2129_1_1" => [24,"箕"],
  #   "2130_1_1" => [11,"井"],
  #   "2131_1_1" => [25,"斗"],
  #   "2132_1_1" => [12,"鬼"],
  #   "2133_1_1" => [27,"女"],
  #   "2134_1_1" => [14,"星"],
  #   "2135_1_1" => [ 0,"虛"],
  #   "2136_1_1" => [15,"張"],
  #   "2137_1_1" => [ 2,"室"],
  #   "2138_1_1" => [17,"軫"],
  #   "2139_1_1" => [ 3,"壁"],
  #   "2140_1_1" => [18,"角"]
  # }
  @@fc_28alphard = [
    [ 0,"id_0" ,"xi_0" ,"ji_0"], #[0,"虛",["不論何事，退守則吉"],["開門放水，不論何事，小心退守則吉。"]],
    [ 1,"id_1" ,"xi_1" ,"ji_1"], #[1,"危",["出行","納財"],["起造","埋葬","開門","放水，其它要戒慎"]],
    [ 2,"id_2" ,"xi_2" ,"ji_2"], #[2,"室",["婚禮","祝壽","移徙","建造","祭祀","掘井"],["喪儀"]],
    [ 3,"id_3" ,"xi_3" ,"ji_3"], #[3,"壁",["婚禮","建造","埋葬"],["往南方凶"]],
    [ 4,"id_4" ,"xi_4" ,"ji_4"], #[4,"奎",["出行","裁衣","修屋"],["開張","新築"]],
    [ 5,"id_5" ,"xi_5" ,"ji_5"], #[5,"婁",["婚禮","修屋","造庭"],["往南方凶"]],
    [ 6,"id_6" ,"xi_6" ,"ji_6"], #[6,"胃",["嫁娶","下葬","公事吉"],["裁衣","私事凶"]],
    [ 7,"id_7" ,"xi_7" ,"ji_7"], #[7,"昂",["萬事大吉"],["裁衣凶"]],
    [ 8,"id_8" ,"xi_8" ,"ji_8"], #[8,"畢",["造屋","葬儀","嫁娶","造橋","掘井"],["裁衣凶"]],
    [ 9,"id_9" ,"xi_9" ,"ji_9"], #[9,"觜",["百事皆凶"],["大惡日，建造","下葬","百事皆凶"]],
    [10,"id_10","xi_10","ji_10"], #[10,"參",["求財","旅行","立門","建造皆吉"],["埋葬"]],
    [11,"id_11","xi_11","ji_11"], #[11,"井",["祭祀","播種","建造"],["裁衣"]],
    [12,"id_12","xi_12","ji_12"], #[12,"鬼",["可下葬"],["婚禮","往西方亦凶"]],
    [13,"id_13","xi_13","ji_13"], #[13,"柳",["造作","婚禮"],["喪儀"]],
    [14,"id_14","xi_14","ji_14"], #[14,"星",["婚禮","播種"],["喪儀","裁衣凶"]],
    [15,"id_15","xi_15","ji_15"], #[15,"張",["婚禮","開市","祭祀","祝壽"],[""]],
    [16,"id_16","xi_16","ji_16"], #[16,"翼",["百事皆不利"],["百事皆不宜"]],
    [17,"id_17","xi_17","ji_17"], #[17,"軫",["買田園","入學","建造","婚禮","裁衣","置業"],["向北方旅行兇"]],
    [18,"id_18","xi_18","ji_18"], #[18,"角",["婚禮","旅行","穿新衣","動土","立柱","立門","裁衣","搬遷"],["喪儀"]],
    [19,"id_19","xi_19","ji_19"], #[19,"亢",["婚禮","播種","買賣"],["建屋","下葬"]],
    [20,"id_20","xi_20","ji_20"], #[20,"氐",["買田園","播種","婚禮"],["葬儀","嫁娶"]],
    [21,"id_21","xi_21","ji_21"], #[21,"房",["祭祀","婚姻","上樑","移徙","嫁娶","修造","搬遷"],["買田園","裁衣"]],
    [22,"id_22","xi_22","ji_22"], #[22,"心",["祭祀","移徙","旅行"],["裁衣","祭祀","搬遷","遠行"]],
    [23,"id_23","xi_23","ji_23"], #[23,"尾",["婚禮","造作","嫁娶","建造"],["裁衣"]],
    [24,"id_24","xi_24","ji_24"], #[24,"箕",["建造","開池","開門","造屋","收財"],["婚禮","葬儀","裁衣"]],
    [25,"id_25","xi_25","ji_25"], #[25,"斗",["裁衣","建造","開門","放水"],[""]],
    [26,"id_26","xi_26","ji_26"], #[26,"牛",["建造","開門","放水"],["嫁娶","建造"]],
    [27,"id_27","xi_27","ji_27"], #[27,"女",["學藝"],["喪儀","爭訟","裁衣","嫁娶","建造"]],
    [28,"",[""],[""]]  # empty
  ]
  @@fc_28alphard_start = {
    "2015_1_1" => [25], #,"斗"],
    "2016_1_1" => [26], #,"牛"],
    "2017_1_1" => [ 0], #,"虛"],
    "2018_1_1" => [ 1], #,"危"],
    "2019_1_1" => [ 2], #,"室"],
    "2020_1_1" => [ 3], #,"壁"],
    "2021_1_1" => [ 5], #,"婁"],
    "2022_1_1" => [ 6], #,"胃"],
    "2023_1_1" => [ 7], #,"昂"],
    "2024_1_1" => [ 8], #,"畢"],
    "2025_1_1" => [10], #,"參"],
    "2026_1_1" => [11], #,"井"],
    "2027_1_1" => [12], #,"鬼"],
    "2028_1_1" => [13], #,"柳"],
    "2029_1_1" => [15], #,"張"],
    "2030_1_1" => [16], #,"翼"],
    "2031_1_1" => [17], #,"軫"],
    "2032_1_1" => [18], #,"角"],
    "2033_1_1" => [20], #,"氐"],
    "2034_1_1" => [21], #,"房"],
    "2035_1_1" => [22], #,"心"],
    "2036_1_1" => [23], #,"尾"],
    "2037_1_1" => [25], #,"斗"],
    "2038_1_1" => [26], #,"牛"],
    "2039_1_1" => [27], #,"女"],
    "2040_1_1" => [ 0], #,"虛"],
    "2041_1_1" => [ 2], #,"室"],
    "2042_1_1" => [ 3], #,"壁"],
    "2043_1_1" => [ 4], #,"奎"],
    "2044_1_1" => [ 5], #,"婁"],
    "2045_1_1" => [ 7], #,"昂"],
    "2046_1_1" => [ 8], #,"畢"],
    "2047_1_1" => [ 9], #,"觜"],
    "2048_1_1" => [10], #,"參"],
    "2049_1_1" => [12], #,"鬼"],
    "2050_1_1" => [13], #,"柳"],
    "2051_1_1" => [14], #,"星"],
    "2052_1_1" => [15], #,"張"],
    "2053_1_1" => [17], #,"軫"],
    "2054_1_1" => [18], #,"角"],
    "2055_1_1" => [19], #,"亢"],
    "2056_1_1" => [20], #,"氐"],
    "2057_1_1" => [22], #,"心"],
    "2058_1_1" => [23], #,"尾"],
    "2059_1_1" => [24], #,"箕"],
    "2060_1_1" => [25], #,"斗"],
    "2061_1_1" => [27], #,"女"],
    "2062_1_1" => [ 0], #,"虛"],
    "2063_1_1" => [ 1], #,"危"],
    "2064_1_1" => [ 2], #,"室"],
    "2065_1_1" => [ 4], #,"奎"],
    "2066_1_1" => [18], #,"角"],
    "2067_1_1" => [ 5], #,"婁"],
    "2068_1_1" => [19], #,"亢"],
    "2069_1_1" => [ 7], #,"昂"],
    "2070_1_1" => [21], #,"房"],
    "2071_1_1" => [ 8], #,"畢"],
    "2072_1_1" => [22], #,"心"],
    "2073_1_1" => [10], #,"參"],
    "2074_1_1" => [24], #,"箕"],
    "2075_1_1" => [11], #,"井"],
    "2076_1_1" => [25], #,"斗"],
    "2077_1_1" => [13], #,"柳"],
    "2078_1_1" => [27], #,"女"],
    "2079_1_1" => [14], #,"星"],
    "2080_1_1" => [ 0], #,"虛"],
    "2081_1_1" => [16], #,"翼"],
    "2082_1_1" => [ 2], #,"室"],
    "2083_1_1" => [17], #,"軫"],
    "2084_1_1" => [ 3], #,"壁"],
    "2085_1_1" => [19], #,"亢"],
    "2086_1_1" => [ 6], #,"胃"],
    "2087_1_1" => [20], #,"氐"],
    "2088_1_1" => [ 7], #,"昂"],
    "2089_1_1" => [22], #,"心"],
    "2090_1_1" => [ 9], #,"觜"],
    "2091_1_1" => [23], #,"尾"],
    "2092_1_1" => [10], #,"參"],
    "2093_1_1" => [25], #,"斗"],
    "2094_1_1" => [12], #,"鬼"],
    "2095_1_1" => [26], #,"牛"],
    "2096_1_1" => [13], #,"柳"],
    "2097_1_1" => [ 0], #,"虛"],
    "2098_1_1" => [15], #,"張"],
    "2099_1_1" => [ 1], #,"危"],
    "2100_1_1" => [16], #,"翼"],
    "2101_1_1" => [ 2], #,"室"],
    "2102_1_1" => [17], #,"軫"],
    "2103_1_1" => [ 3], #,"壁"],
    "2104_1_1" => [18], #,"角"],
    "2105_1_1" => [ 6], #,"胃"],
    "2106_1_1" => [20], #,"氐"],
    "2107_1_1" => [ 7], #,"昂"],
    "2108_1_1" => [21], #,"房"],
    "2109_1_1" => [ 9], #,"觜"],
    "2110_1_1" => [23], #,"尾"],
    "2111_1_1" => [10], #,"參"],
    "2112_1_1" => [24], #,"箕"],
    "2113_1_1" => [12], #,"鬼"],
    "2114_1_1" => [26], #,"牛"],
    "2115_1_1" => [13], #,"柳"],
    "2116_1_1" => [27], #,"女"],
    "2117_1_1" => [15], #,"張"],
    "2118_1_1" => [ 1], #,"危"],
    "2119_1_1" => [16], #,"翼"],
    "2120_1_1" => [ 2], #,"室"],
    "2121_1_1" => [18], #,"角"],
    "2122_1_1" => [ 5], #,"婁"],
    "2123_1_1" => [19], #,"亢"],
    "2124_1_1" => [ 6], #,"胃"],
    "2125_1_1" => [21], #,"房"],
    "2126_1_1" => [ 8], #,"畢"],
    "2127_1_1" => [22], #,"心"],
    "2128_1_1" => [ 9], #,"觜"],
    "2129_1_1" => [24], #,"箕"],
    "2130_1_1" => [11], #,"井"],
    "2131_1_1" => [25], #,"斗"],
    "2132_1_1" => [12], #,"鬼"],
    "2133_1_1" => [27], #,"女"],
    "2134_1_1" => [14], #,"星"],
    "2135_1_1" => [ 0], #,"虛"],
    "2136_1_1" => [15], #,"張"],
    "2137_1_1" => [ 2], #,"室"],
    "2138_1_1" => [17], #,"軫"],
    "2139_1_1" => [ 3], #,"壁"],
    "2140_1_1" => [18] #,"角"]
  }
  def Xdate.fc_er_shi_ba_xing_xiu_str(key)
    if (key == "") then
      return ""
    end
    return Xdate.nongminli_str("er_shi_ba_xing_xiu.#{key}")
  end
  def Xdate.fc_er_shi_ba_xing_xiu_xi_ji_str(key)
    if (key == "") then
      return ""
    end
    s = Xdate.nongminli_str("er_shi_ba_xing_xiu.#{key}")
    return s.split(",")
  end
  def Xdate.fc_28alphard_suitable_scruple(nWYear,nWMonth,nWDate)
  	a = Xdate.fc_28alphard(nWYear,nWMonth,nWDate)
    a_0 = a[0]
    a_1 = Xdate.fc_er_shi_ba_xing_xiu_str(a[1])
    a_2 = Xdate.fc_er_shi_ba_xing_xiu_xi_ji_str(a[2])
    a_3 = Xdate.fc_er_shi_ba_xing_xiu_xi_ji_str(a[3])
  	return a_0,a_1,a_2,a_3
  end
  def Xdate.fc_28alphard(nWYear,nWMonth,nWDate)
  	key = "#{nWYear}_1_1"
  	a = @@fc_28alphard_start[key]
  	alphardIndex = 28
  	if (a == nil) then
  	  if (nWYear.between?(1901,2014)) then
  	    key = "2015_1_1"
  	    a = @@fc_28alphard_start[key]
  	    alphardIndex = a[0]
        nDays = Xdate.GetDaysBetweenWDates(nWYear,nWMonth,nWDate,2015,1,1)
        alphardIndex1 = Pm.TestNo28(nDays - 1)
        alphardIndex = Pm.TestNo28(alphardIndex + 28 - alphardIndex1)
      end
    else
   	  alphardIndex = a[0]
      nDays = Xdate.GetDaysBetweenWDates(nWYear,1,1,nWYear,nWMonth,nWDate)
      alphardIndex = Pm.TestNo28(nDays - 1 + alphardIndex)
    end
  	return @@fc_28alphard[alphardIndex]
  end


  # 14、台灣節氣農產
  # 節氣   北部                         中部                     南部
  # @@fc_taiwan_area = ["節氣","北部","中部","南部"]
  # @@fc_segment_food = [
  #   ["小寒","菜頭、蘿蔔、金瓜、皇帝豆","冬瓜、香瓜、絲瓜、茄子菜","冬瓜、西瓜、南瓜、金瓜"],
  #   ["大寒","菜瓜、紅菜頭、胡瓜、菠菜","菜瓜、胡蘿蔔、水芋、菠菜","蓮藕、西瓜、南瓜、絲瓜、白芋"],
  #   ["立春","蕃茄、牛蒡、胡蘿蔔、大蒽、茄子","西瓜、胡瓜、白芋、甜菜、香瓜、芋","薑、甜瓜、肉豆、白芋、萵苣、蔥"],
  #   ["雨水","鍋仔菜、絲瓜、韭菜、白豆、紫蘇","苦瓜、烏豆、蕃茄、落花生、菠菜","蘿蔔、紫蘇、菜瓜、蓮藕、落花生"],
  #   ["驚蟄","西瓜、胡瓜、白芋、甜瓜、香瓜","落花生、薑、菜豆、茭白筍","落花生、菜豆、西瓜、茭白筍"],
  #   ["春分","山藥、西瓜、苦瓜、韮菜、甕菜","田薯、蓮藕、肉豆、幸菜、落花生","莧菜、肉豆、幸菜、莿菜、落花生"],
  #   ["清明","甘薯、莧菜、茭白筍、大豆、萵苣","蕃薯、白菜、茭白筍、大豆、萵苣","芥菜、小白菜、皇帝豆、茭白筍、白豆"],
  #   ["穀雨","西瓜、胡瓜、蕃茄、菜瓜、白芋、大蔥","大蔥、甕菜、大豆、菜豆、菜瓜、西瓜","大蔥、甕菜、菜豆、白芋、薯菜、蔥仔"],
  #   ["立夏","蕃茄、紅豆、胡瓜、芥菜、莧","大豆、菜瓜、甘薯、胡瓜、大蔥","烏豆、白豆、香瓜、蘿蔔"],
  #   ["小滿","胡瓜、茄子、小白菜、大蔥、韮菜、蘿蔔","白豆、越瓜、土白菜、韮菜、小胡瓜","水稻、越瓜、香瓜、大豆、小白菜"],
  #   ["芒種","胡瓜、莧菜、甘薯、分蔥、蘿蔔","小白菜、甕菜、越瓜、韮菜、蕃薯","小白菜、越瓜、大豆、蔥、莧菜"],
  #   ["夏至","小白菜、金針菜、蘿蔔、恭菜、櫻桃","土白菜、金針菜、胡瓜、水芹菜","越瓜、金針菜、胡瓜、水芹菜"],
  #   ["小暑","芹菜、越瓜、甘薯、小白菜、大芹菜","胡瓜、芥菜、茄、芥藍菜","蕃茄、韮菜、夏燕、大芥菜、蕃椒"],
  #   ["大暑","甘藍、土白菜、冬瓜、韮菜、甘薯","小白菜、冬瓜、甘薯、芥藍菜、甘薯","苘萵、玉蜀黍、甘薯、菜豆、冬瓜"],
  #   ["立秋","甘藍、蘿蔔、大豆、芹菜、烏豆、大蔥","蕃茄、牛蒡、蘿蔔、甘薯、茄子、芹菜","芹菜、越瓜、甘藍、牛蒡、甘薯、芥菜"],
  #   ["處暑","甘藍、甘薯、花椰菜、蕃茄、八月豆","花椰菜、大豆、落花生、芹菜、蕃茄","萵苣、甘藍、甘薯、蘿蔔、花椰菜"],
  #   ["白露","花椰菜、白菜、胡瓜、大蔥、高麗菜","花椰菜、白菜、菠菜、恭菜、芥菜、苘萵","白菜、高麗菜、大豆、芥菜、苘萵"],
  #   ["秋分","苦瓜、萵苣、蒜、韮菜、蘿蔔、芹菜","牛蒡、大蔥、紅蘿蔔","牛蒡、大蔥、紅蘿蔔、苦瓜"],
  #   ["寒露","蘿蔔、豌豆、茄子、百合、菜頭、燕青","豌豆、芋頭、馬鈴薯、菠菜、牛蒡、白菜","馬鈴薯、苘萵、花椰菜、甘薯、西瓜、苦瓜"],
  #   ["霜降","皇帝豆、牛蒡、白菜、角菜、馬鈴薯","牛蒡、蕃茄、燕青、蒜、火焰菜、萵苣","牛蒡、蕃茄、燕青、蒜、火焰菜、芹菜"],
  #   ["立冬","皇帝豆、牛蒡、菜豆、馬鈴薯","胡蘿蔔、玉蜀黍、石刁柏、百合、南瓜","西瓜、苦瓜、玉蜀黍、甘藍、大小麥、蘿蔔"],
  #   ["小雪","芹菜、恭菜、萵苣、莞荌、胡椒、白菜","牛蒡、馬鈴薯、蘿蔔、玉蜀黍、大蔥","玉蜀黍、胡瓜、西瓜、蘿蔔、刀豆、大蔥"],
  #   ["大雪","南瓜、冬瓜、金瓜、白菜、蘿蔔、扁蒲","韮菜、玉蜀黍、南瓜、白菜、蘿蔔、扁蒲","南瓜、冬瓜、苦瓜、西瓜、蘿蔔、扁蒲、甜瓜、款冬"],
  #   ["冬至","蘿蔔、石刁柏、菜豆、芥菜、燕青","蘿蔔、石刁柏、皇帝豆、菠菜","茄子、冬瓜、蘿蔔、韮菜、冬瓜、香瓜"]
  # ]
  def Xdate.fc_jie_qi_nong_chan_array(index)
    s = Xdate.nongminli_str("tai_wan_jie_qi.nong_chan.jie_qi_#{index}")
    return s.split(",")
  end
  def Xdate.fc_jie_qi_qu_yu_array()
    s = Xdate.nongminli_str("tai_wan_jie_qi.qu_yu")
    return s.split(",")
  end

  def Xdate.fc_segmentfood(nSegmentIndex)
  	# a = @@fc_segment_food[nSegmentIndex]
    a = Xdate.fc_jie_qi_nong_chan_array(nSegmentIndex)
    fc_taiwan_area = Xdate.fc_jie_qi_qu_yu_array()
  	s = "#{fc_taiwan_area[1]} : #{a[1]}"
  	s += ";  #{fc_taiwan_area[2]} : #{a[2]}"
  	s += ";  #{fc_taiwan_area[3]} : #{a[3]}"
  	return s
  end
  # @@fc_segment_fish = [
  #   ["小寒","蘇澳：釘鮸、梳齒","基隆：釘鮸","澎湖：沙魚、狗母、龍蝦"],
  #   ["大寒","新港：釘鮸","高雄：釘鮸、硼串","安平：沙魚、馬鮫、烏魚","東港：狗母、過仔魚、烏魚"],
  #   ["立春","淡水：釘鮸、沙魚、加蚋魚、錩魚","基隆：梳齒、沙魚、加蚋魚","澎湖：梳齒、沙魚、加蚋魚","蘇澳：梳齒、沙魚、狗母、龍蝦"],
  #   ["雨水","新港：釘鮸","高雄：烏魚、鰛魚、沙魚、石卿","安平：烏魚、龍蝦、沙魚、石卿","東港：白帶魚"],
  #   ["驚蟄","蘇澳：釘鮸、沙魚","基隆：加蚋魚、釘鮸、沙魚","淡水：鮸魚、沙魚、釘鮸"],
  #   ["春分","澎湖：加蚋魚、釘鮸、沙魚、闊北","東港：虱目魚、錩魚、白帶魚","高雄：虱目魚苗、沙魚、釘鮸"],
  #   ["清明","基隆：加蚋魚、梳齒、沙魚、鰛魚","澎湖：沙魚、加蚋魚、鰛魚、煙仔魚","蘇澳：目吼、飛鳥、沙魚、煙仔魚"],
  #   ["穀雨","安平：加蚋魚、虱目魚苗、鮸魚北","高雄：虱目魚苗、沙魚、白帶魚","東港：目吼、沙魚、虱目魚苗、白帶魚"],
  #   ["立夏","淡水：沙魚、鰛魚","基隆：鰛魚、沙魚、加蚋魚","澎湖：沙魚、龍尖、白錩、煙仔魚","蘇澳：飛鳥、煙仔魚"],
  #   ["小滿","安平：虱目魚苗、鮸魚","高雄：虱目魚苗、飛鳥、加蚋魚","東港：龍蝦、沙魚、鳥鯸"],
  #   ["芒種","基隆：鰛魚、卓鯤、棘鬣魚","淡水：龍尖、鰛魚","高雄：飛鳥、棘鬣魚","東港：龍蝦、沙魚、飛魚","安平：鮸魚、煙仔魚"],
  #   ["夏至","基隆：飛魚","東港：龍蝦、飛魚、虱目魚苗","安平：烏魚、鮸魚、虱目魚苗","澎湖：虱目魚苗"],
  #   ["小暑","基隆：飛魚、煙仔魚","高雄：龍蝦、鰛魚、龍尖、煙仔魚","澎湖：虱目魚苗"],
  #   ["大暑","東港：龍蝦、鮸魚、虱目魚苗","安平：烏鮸、虱目魚苗、鮸魚","澎湖：龍蝦、鰛魚、龍尖、煙仔魚、虱目魚苗","高雄：烏魚、鮸魚、虱目魚苗"],
  #   ["立秋","基隆：鰛魚、卓鯤、目吼、棘鬣魚","淡水：鰛魚","澎湖：鰛魚、龍尖、沙魚"],
  #   ["處暑","安平：烏魚、虱目魚苗","高雄：虱目魚苗","東港：虱目魚苗"],
  #   ["白露","蘇澳：硼串","基隆：鰛魚、目吼、加蚋魚、卓鯤","淡水：鬣魚、梳齒"],
  #   ["秋分","淡水：鰛魚","澎湖：鰛魚","安平：烏格魚"],
  #   ["寒露","蘇澳：硼串、目吼、加蚋魚、沙魚","基隆：鰛魚、目賊、棘鬣魚"],
  #   ["霜降","淡水：鰛魚、加蚋魚、棘鬣魚、龍蝦","高雄：卓鯤、硼串","東港：目賊"],
  #   ["立冬","高雄：沙魚、卓鯤","東港：沙魚、鰛仔、馬鮫"],
  #   ["小雪","基隆：棘鬣魚、目吼、梳齒","淡水：棘鬣魚、旗魚","澎湖：棘鬣魚、加蚋魚"],
  #   ["大雪","基隆：棘鬣魚、加蚋魚、梳齒","蘇澳：旗魚","淡水：棘鬣魚、旗魚","澎湖：加蚋魚、梳齒"],
  #   ["冬至","安平：烏魚、馬駮","高雄：沙魚、過仔魚、硼串","東港：沙魚、過仔魚、烏魚、馬鮫"]
  # ]
  def Xdate.fc_jie_qi_yu_chan_array(index)
    s = Xdate.nongminli_str("tai_wan_jie_qi.yu_chan.jie_qi_#{index}")
    return s.split(",")
  end
  def Xdate.fc_segmentfish(nSegmentIndex)
  	# a = @@fc_segment_fish[nSegmentIndex]
    a = Xdate.fc_jie_qi_yu_chan_array(nSegmentIndex)
  	a.delete_at(0)
  	return a.join("; ")
  end

  # 15、月名稱
   # 月份 地支月	煞	顯示樣式	      月煞
  # @@fc_segment_month_desc = [
  #   ["子","葭","南","%{segment}月葭月","月煞在南方"],
  #   ["丑","臘","東","%{segment}月臘月","月煞在東方"],
  #   ["寅","端","北","%{segment}月端月","月煞在北方"],
  #   ["卯","花","西","%{segment}月花月","月煞在西方"],
  #   ["辰","桐","南","%{segment}月桐月","月煞在南方"],
  #   ["巳","梅","東","%{segment}月梅月","月煞在東方"],
  #   ["午","蒲","北","%{segment}月蒲月","月煞在北方"],
  #   ["未","荔","西","%{segment}月荔月","月煞在西方"],
  #   ["申","瓜","南","%{segment}月瓜月","月煞在南方"],
  #   ["酉","桂","東","%{segment}月桂月","月煞在東方"],
  #   ["戌","菊","北","%{segment}月菊月","月煞在北方"],
  #   ["亥","陽","西","%{segment}月陽月","月煞在西方"]
  # ]
  # 月份以節氣月為主，以流年之天干佈流月之天干。
  def Xdate.fc_yue_sha_array(yue)
    s = Xdate.nongminli_str("yue_sha.yue_#{yue}")
    return s.split(",")
  end
  def Xdate.fc_segment_month_desc(nGanZhiMonth,nMonthEarth)
  	# a = @@fc_segment_month_desc[nMonthEarth - 1]
    a = Xdate.fc_yue_sha_array(nMonthEarth)
  	return a[3] % { :segment => Xdate.GetGanZhi_Str(nGanZhiMonth)},a[4]
  end
  def Xdate.fc_segment_month_desc_only(nGanZhiMonth,nMonthEarth)
    # a = @@fc_segment_month_desc[nMonthEarth - 1]
    a = Xdate.fc_yue_sha_array(nMonthEarth)
    s = a[3] % { :segment => Xdate.GetGanZhi_Str(nGanZhiMonth)}
    s3 = a[4]
    s2 = s3[0,2] + s3[3,2]
    return s[3,2],s2
  end


  # 16、天赦日
  # 天赦日：各季在干支日時。
  # 春天（寅、卯、辰月）：戊寅日。  14
  # 夏天（巳、午、未月）：甲午日。  30
  # 秋天（申、酉、戌月）：戊申日。  44
  # 冬天（亥、子、丑月）：甲子日    0
  # 各季之特定之干支日為天郝日。
  # @@fc_god_amnesty_str = "天赦日"
  @@fc_god_amnesty = [0,0,14,14,14,30,30,30,44,44,44,0]
  def Xdate.fc_tian_she_ri_str()
    return Xdate.nongminli_str("tian_she_ri")
  end
  def Xdate.fc_god_amnesty(nMonthEarth,nGanZhiDay)
  	return Xdate.fc_is_god_amnesty?(nMonthEarth,nGanZhiDay) ? Xdate.fc_tian_she_ri_str() : ""
  end
  def Xdate.fc_is_god_amnesty?(nMonthEarth,nGanZhiDay)
    n = @@fc_god_amnesty[nMonthEarth - 1]
    return (n == nGanZhiDay)
  end
  # @@fc_god_amnesty_suitable_str = "本日行事皆宜"
  def Xdate.fc_ben_ri_xing_shi_jie_yi_str()
    return Xdate.nongminli_str("ben_ri_xing_shi_jie_yi")
  end
  def Xdate.fc_god_amnesty_suitable_scruple(nMonthEarth,nGanZhiDay)
    return Xdate.fc_is_god_amnesty?(nMonthEarth,nGanZhiDay) ? Xdate.fc_ben_ri_xing_shi_jie_yi_str() : ""
  end

  # 17、刀砧日
  # 刀砧日：各季在干支日時。
  # 春天（寅、卯、辰月）：亥、子日。 12 1
  # 夏天（巳、午、未月）：寅、卯日。 3 4
  # 秋天（申、酉、戌月）：巳、午日。 6 7
  # 冬天（亥、子、丑月）：申、酉日   9 10

  # 各季之特定之地支日為刀砧日。
  # @@fc_knife_anvil_str = "刀砧日"
  @@fc_knife_anvil = [[9,10],[9,10],[12,1],[12,1],[12,1],[3,4],[3,4],[3,4],[6,7],[6,7],[6,7],[9,10]]
  def Xdate.fc_dao_zhen_ri_str()
    return Xdate.nongminli_str("dao_zhen_ri")
  end
  def Xdate.fc_knife_anvil(nMonthEarth,nDayEarth)
  	a = @@fc_knife_anvil[nMonthEarth - 1]
  	return a.include?(nDayEarth) ? Xdate.fc_dao_zhen_ri_str() : ""
  end

  # 18、彭祖百忌
  # @@fc_peng_100avoid_sky = [
  #   "甲不開倉，財物耗散","乙不栽植，千株不長",
  #   "丙不修灶，必見災殃","丁不剃頭，頭必生瘡",
  #   "戊不受田，田主不祥","己不破券，二比並亡",
  #   "庚不經絡，織機虛張","辛不合醬，主人不嘗",
  #   "壬不汲水，更難提防","癸不詞訟，理弱敵強"
  # ]
  # @@fc_peng_100avoid_earth = [
  #   "子不問卜，自惹禍殃","丑不冠帶，主不還鄉",
  #   "寅不祭祀，神鬼不嘗","卯不穿井，水泉不香",
  #   "辰不哭泣，必主重喪","已不遠行，財物伏藏",
  #   "午不苫蓋，屋主更張","未不服藥，毒氣入腸",
  #   "申不安床，鬼祟入房","酉不宴客，醉坐顛狂",
  #   "戍不吃犬，作怪上床","亥不嫁娶，不利新郎"
  # ]
  # 註： 以日之天干及地支組合，例如「甲子日」甲不開倉；財物耗散、子不問卜；自惹禍殃。
  def Xdate.fc_peng_zu_bai_ji_str(key,index)
    s = Xdate.nongminli_str("peng_zu_bai_ji.#{key}")
    a = s.split(",")
    return a[index]
  end
  def Xdate.fc_peng_zu_bai_ji_tian_gan_str(index)
    Xdate.fc_peng_zu_bai_ji_str("tian_gan",index)
  end
  def Xdate.fc_peng_zu_bai_ji_di_zhi_str(index)
    Xdate.fc_peng_zu_bai_ji_str("di_zhi",index)
  end
  def Xdate.fc_peng_100avoid(nDaySky,nDayEarth)
  	# s = @@fc_peng_100avoid_sky[nDaySky - 1]
  	# e = @@fc_peng_100avoid_earth[nDayEarth - 1]
    s = Xdate.fc_peng_zu_bai_ji_tian_gan_str(nDaySky - 1)
    e = Xdate.fc_peng_zu_bai_ji_di_zhi_str(nDayEarth - 1)
  	return "#{s}、#{e}"
  end

  # 19、往亡、受死
  #       1月  2月  3月  4月  5月  6月  7月  8月  9月  10月  11月  12月
  # 歸忌   丑   寅   子   丑   寅   子   丑   寅   子   丑   寅   子
  # 月厭殺 戌   酉   申   未   午   巳   辰   卯   寅   丑   子   亥
  # 厭對殺 辰   卯   寅   丑   子   亥   戌   酉   申   未   午   巳
  # 天賊   辰   酉   寅   未   子   巳   戌   卯   申   丑   午   亥
  # 月破   申   酉   戌   亥   子   丑   寅   卯   辰   巳   午   未
  # 受死   戌   辰   亥   巳   子   午   丑   未   寅   申   卯   酉
  # 天寡   卯   卯   卯   午   午   午   酉   酉   酉   子   子   子
  # 地寡   酉   酉   酉   子   子   子   卯   卯   卯   午   午   午
  # 紅紗   酉   酉   酉   酉   巳   巳   巳   巳   丑   丑   丑   丑
  # 天雄   戌   亥   子   丑   寅   卯   辰   巳   午   未   申   酉
  # 地雄   辰   巳   午   未   申   酉   戌   亥   子   丑   寅   卯
  # 披麻殺 子   酉   午   卯   子   酉   午   卯   子   酉   午   卯
  # 天罡   巳   子   未   寅   酉   辰   亥   午   丑   申   卯   戌
  # 河魁   亥   午   丑   申   卯   戌   巳   子   未   寅   酉   辰
  # 吟呻   酉   巳   丑   酉   巳   丑   酉   巳   丑   酉   巳   丑
  # 往亡   寅   巳   申   亥   卯   午   酉   子   辰   午   戌   丑
  # 無翅   亥   戌   酉   申   未   午   巳   辰   卯   寅   丑   子
  # 陰錯   庚   辛   庚   丁   丙   丁   乙   甲   乙   癸   壬   癸
  # 陰錯   戌   酉   申   未   午   巳   辰   卯   寅   丑   子   亥
  # 陽錯   甲   乙   甲   丁   丙   丁   庚   辛   庚   癸   壬   癸
  # 陽錯   寅   卯   辰   巳   午   未   申   酉   戌   亥   子   丑
  # 荒蕪   巳   酉   丑   申   子   辰   亥   卯   未   寅   午   戌
  # 伏斷   子   丑   寅   卯   辰   巳   午   未   申   酉   戌   亥
  # 伏斷   虛   斗   室   女   箕   房   角   張   鬼   觜   胃   壁
  # @@fc_godead_etc = {
  #   "backgi" => ["歸忌","e2","e3","e1","e2","e3","e1","e2","e3","e1","e2","e3","e1"],  # 地支
  #   "monthyk" => ["月厭殺","e11","e10","e9","e8","e7","e6","e5","e4","e3","e2","e1","e12"],
  #   "ydkill" => ["厭對殺","e5","e4","e3","e2","e1","e12","e11","e10","e9","e8","e7","e6"],
  #   "skythief" => ["天賊","e5","e10","e3","e8","e1","e6","e11","e4","e9","e2","e7","e12"],
  #   "monthbreak" => ["月破","e9","e10","e11","e12","e1","e2","e3","e4","e5","e6","e7","e8"],
  #   "gotdead" => ["受死","e11","e5","e12","e6","e1","e7","e2","e8","e3","e9","e4","e10"],
  #   "skyalong" => ["天寡","e4","e4","e4","e7","e7","e7","e10","e10","e10","e1","e1","e1"],
  #   "earthalong" => ["地寡","e10","e10","e10","e1","e1","e1","e4","e4","e4","e7","e7","e7"],
  #   "redyarn" => ["紅紗","e10","e10","e10","e10","e6","e6","e6","e6","e2","e2","e2","e2"],
  #   "skygreat" => ["天雄","e11","e12","e1","e2","e3","e4","e5","e6","e7","e8","e9","e10"],
  #   "earthgreat" => ["地雄","e5","e6","e7","e8","e9","e10","e11","e12","e1","e2","e3","e4"],
  #   "pimakill" => ["披麻殺","e1","e10","e7","e4","e1","e10","e7","e4","e1","e10","e7","e4"],
  #   "skygang" => ["天罡","e6","e1","e8","e3","e10","e5","e12","e7","e2","e9","e4","e11"],
  #   "riverchief" => ["河魁","e12","e7","e2","e9","e4","e11","e6","e1","e8","e3","e10","e5"],
  #   "yings" => ["吟呻","e10","e6","e2","e10","e6","e2","e10","e6","e2","e10","e6","e2"],
  #   "go_to_dead" => ["往亡","e3","e6","e9","e12","e4","e7","e10","e1","e5","e7","e11","e2"],
  #   "nowing" => ["無翅","e12","e11","e10","e9","e8","e7","e6","e5","e4","e3","e2","e1"],
  #   "yinwrong" => ["陰錯","s7","s8","s7","s4","s3","s4","s2","s1","s2","s10","s9","s10"], # 天干
  #   "yinwrong2" => ["陰錯","e11","e10","e9","e8","e7","e6","e5","e4","e3","e2","e1","e12"],
  #   "yangwrong" => ["陽錯","s1","s2","s1","s4","s3","s4","s7","s8","s7","s10","s9","s10"], # 天干
  #   "yangwrong2" => ["陽錯","e3","e4","e5","e6","e7","e8","e9","e10","e11","e12","e1","e2"],
  #   "huangwu" => ["荒蕪","e6","e10","e2","e9","e1","e5","e12","e4","e8","e3","e7","e11"],
  #   "fuduan" => ["伏斷","e1","e2","e3","e4","e5","e6","e7","e8","e9","e10","e11","e12"],
  #   "fuduan2" => ["伏斷","a0","a25","a2","a27","a24","a21","a18","a15","a12","a9","a6","a3"] # 28 星宿 index
  # }
  @@fc_godead_etc = {
    "backgi" => ["backgi","e2","e3","e1","e2","e3","e1","e2","e3","e1","e2","e3","e1"],  # 地支
    "monthyk" => ["monthyk","e11","e10","e9","e8","e7","e6","e5","e4","e3","e2","e1","e12"],
    "ydkill" => ["ydkill","e5","e4","e3","e2","e1","e12","e11","e10","e9","e8","e7","e6"],
    "skythief" => ["skythief","e5","e10","e3","e8","e1","e6","e11","e4","e9","e2","e7","e12"],
    "monthbreak" => ["monthbreak","e9","e10","e11","e12","e1","e2","e3","e4","e5","e6","e7","e8"],
    "gotdead" => ["gotdead","e11","e5","e12","e6","e1","e7","e2","e8","e3","e9","e4","e10"],
    "skyalong" => ["skyalong","e4","e4","e4","e7","e7","e7","e10","e10","e10","e1","e1","e1"],
    "earthalong" => ["earthalong","e10","e10","e10","e1","e1","e1","e4","e4","e4","e7","e7","e7"],
    "redyarn" => ["redyarn","e10","e10","e10","e10","e6","e6","e6","e6","e2","e2","e2","e2"],
    "skygreat" => ["skygreat","e11","e12","e1","e2","e3","e4","e5","e6","e7","e8","e9","e10"],
    "earthgreat" => ["earthgreat","e5","e6","e7","e8","e9","e10","e11","e12","e1","e2","e3","e4"],
    "pimakill" => ["pimakill","e1","e10","e7","e4","e1","e10","e7","e4","e1","e10","e7","e4"],
    "skygang" => ["skygang","e6","e1","e8","e3","e10","e5","e12","e7","e2","e9","e4","e11"],
    "riverchief" => ["riverchief","e12","e7","e2","e9","e4","e11","e6","e1","e8","e3","e10","e5"],
    "yings" => ["yings","e10","e6","e2","e10","e6","e2","e10","e6","e2","e10","e6","e2"],
    "go_to_dead" => ["go_to_dead","e3","e6","e9","e12","e4","e7","e10","e1","e5","e7","e11","e2"],
    "nowing" => ["nowing","e12","e11","e10","e9","e8","e7","e6","e5","e4","e3","e2","e1"],
    "yinwrong" => ["yinwrong","s7","s8","s7","s4","s3","s4","s2","s1","s2","s10","s9","s10"], # 天干
    "yinwrong2" => ["yinwrong2","e11","e10","e9","e8","e7","e6","e5","e4","e3","e2","e1","e12"],
    "yangwrong" => ["yangwrong","s1","s2","s1","s4","s3","s4","s7","s8","s7","s10","s9","s10"], # 天干
    "yangwrong2" => ["yangwrong2","e3","e4","e5","e6","e7","e8","e9","e10","e11","e12","e1","e2"],
    "huangwu" => ["huangwu","e6","e10","e2","e9","e1","e5","e12","e4","e8","e3","e7","e11"],
    "fuduan" => ["fuduan","e1","e2","e3","e4","e5","e6","e7","e8","e9","e10","e11","e12"],
    "fuduan2" => ["fuduan2","a0","a25","a2","a27","a24","a21","a18","a15","a12","a9","a6","a3"] # 28 星宿 index
  }
  def Xdate.fc_wang_wang_str(key)
    return Xdate.nongminli_str("wang_wang.#{key}")
  end
  def Xdate.fc_godead_etc_array(hk)
    a = @@fc_godead_etc[hk].clone
    s = Xdate.fc_wang_wang_str(a[0])
    a[0] = s
    return a
  end
  def Xdate.fc_gotdead(nSMonth,nDayEarth)
  	return Xdate.fc_godead_etc_earth(nSMonth,"gotdead",nDayEarth)
  end
  def Xdate.fc_go_to_dead(nSMonth,nDayEarth)
  	return Xdate.fc_godead_etc_earth(nSMonth,"go_to_dead",nDayEarth)
  end
  def Xdate.fc_godead_etc_sky(nSMonth,hk,nDaySky)
    a = Xdate.fc_godead_etc_array(hk)
  	return Xdate.fc_godead_etc(nSMonth,a,"s#{nDaySky}")
  end
  def Xdate.fc_godead_etc_earth(nSMonth,hk,nDayEarth)
    a = Xdate.fc_godead_etc_array(hk)
  	return Xdate.fc_godead_etc(nSMonth,a,"e#{nDayEarth}")
  end
  def Xdate.fc_godead_etc_alphard(nSMonth,hk,nWYear,nWMonth,nWDate)
    a = Xdate.fc_godead_etc_array(hk)
  	alphard = Xdate.fc_28alphard(nWYear,nWMonth,nWDate)
  	return Xdate.fc_godead_etc(nSMonth,a,"a#{alphard[0]}")
  end
  def Xdate.fc_godead_etc(nSMonth,a,k)
  	if (a[nSMonth] == k) then
  	  return a[0]
  	else
  	  return ""
  	end
  end

  # 20、麒麟日、鳳凰日

  # 麒麟日：各季在二十八星宿。
  # 春天（寅、卯、辰月）：井日。 11
  # 夏天（巳、午、未月）：尾日。 23
  # 秋天（申、酉、戌月）：牛日。 26
  # 冬天（亥、子、丑月）：𤩹日。 3
  # 各季之特定之地支日為麒麟日。
  # @@fc_kirin_str = "麒麟日"
  @@fc_kirin = [3,3,11,11,11,23,23,23,26,26,26,3]
  def Xdate.fc_ki_rin_ri_str()
    return Xdate.nongminli_str("ki_rin_ri")
  end
  def Xdate.fc_kirin(nMonthEarth,n28alphard)
    a = @@fc_kirin[nMonthEarth - 1]
    return a == n28alphard ? Xdate.fc_ki_rin_ri_str() : ""
  end


  # 鳳凰日：各季在二十八星宿。
  # 春天（寅、卯、辰月）：危日。 1
  # 夏天（巳、午、未月）：昴日。 7
  # 秋天（申、酉、戌月）：胃日。 6
  # 冬天（亥、子、丑月）：畢日。 8

  # 以節氣月為基準。
  # @@fc_phoenix_str = "鳳凰日"
  @@fc_phoenix = [8,8,1,1,1,7,7,7,6,6,6,8]
  def Xdate.fc_feng_huang_ri_str()
    return Xdate.nongminli_str("feng_huang_ri")
  end
  def Xdate.fc_phoenix(nMonthEarth,n28alphard)
    a = @@fc_phoenix[nMonthEarth - 1]
    return a == n28alphard ? Xdate.fc_feng_huang_ri_str() : ""
  end

  def Xdate.GetSegmentFromWest_farmercal(nWYear,nWMonth,nWDate,nHour,nMinute)
    if (!Xdate.IsWDateLegal?(nWYear, nWMonth, nWDate,nHour, nMinute)) then
      return -1
    end
    bFrontYear = false
    bExactSegment = false

    Xdate.Init()
    nSegment = -1
    nSegmentTime = 0
    nSegCountPerYear = Xdate::SEGMENT_COUNT_PER_YEAR
    nYearCount = nWYear - Xdate::BASE_YEAR
    nSegMonthCount = nYearCount * 12

    # nFirstSeg = 大雪 (國曆年前一個節)
    nFrontSeg = nYearCount * nSegCountPerYear - 2
    aFrontSeg = @@SegTimeArray[nFrontSeg]
    nFrontSegDays = Xdate.GetWDaysThisYear(nWYear - 1, aFrontSeg[Xdate::ST_MONTH], aFrontSeg[Xdate::ST_DAY])
    nFrontMinutes = aFrontSeg[Xdate::ST_HOUR] * 60 + aFrontSeg[Xdate::ST_MINUTE]

    nDays = Xdate.GetWDaysThisYear(nWYear - 1, aFrontSeg[Xdate::ST_MONTH], 31)
    nFrontSegOffset = nDays - nFrontSegDays

    # nFirstSeg = 小寒 (國曆年第一個節氣)
    nFirstSeg = nYearCount * nSegCountPerYear
    aCurSeg = @@SegTimeArray[nFirstSeg]
    nDays = Xdate.GetWDaysThisYear(nWYear, nWMonth, nWDate)
    if (nHour > 0) then
        nAllMinutes = nHour * 60 + nMinute
    else
        nAllMinutes = nMinute
    end
    nSegmentDays = 0
    (0..nSegCountPerYear).each do |i|
      if (nSegment == -1) then
        nSegDays = Xdate.GetWDaysThisYear(nWYear, aCurSeg[Xdate::ST_MONTH], aCurSeg[Xdate::ST_DAY])
        if (nDays == nSegDays) then
            nCurMinutes = aCurSeg[Xdate::ST_HOUR] * 60 + aCurSeg[Xdate::ST_MINUTE]
            if (nAllMinutes < nCurMinutes) then
              if (i == 0) then
                nSegment = nSegCountPerYear - 1 # 屬去年冬至
                nWYear -= 1
                bFrontYear = true
                nSegmentDays = nFrontSegOffset + nDays
              else
                nSegment = i - 1
                nSegmentDays = nDays - nFrontSegOffset
              end
              if (nAllMinutes < nFrontMinutes) then
                nSegmentDays -= 1
                nSegmentTime = 24 * 60 + nAllMinutes - nFrontMinutes
              else
                nSegmentTime = nAllMinutes - nFrontMinutes
              end
            else
              bExactSegment = true

              nSegment = i
              if (i % 2 == 0) then
                nSegmentDays = 0
                nSegmentTime = nAllMinutes - nCurMinutes
              else
                nSegmentDays = nDays - nFrontSegOffset
                if (nAllMinutes < nFrontMinutes) then
                  nSegmentDays -= 1
                  nSegmentTime = 24 * 60 + nAllMinutes - nFrontMinutes
                else
                  nSegmentTime = nAllMinutes - nFrontMinutes
                end
              end
            end
        elsif (nDays < nSegDays) then # The nWDate < 01/06 (小寒)
          if (i == 0) then
            nSegment = nSegCountPerYear - 1 # 屬去年冬至
            nWYear -= 1
            bFrontYear = true
            nSegmentDays = nFrontSegOffset + nDays
            if (nAllMinutes < nFrontMinutes) then
              nSegmentDays -= 1
              nSegmentTime = 24 * 60 + nAllMinutes - nFrontMinutes
            else
              nSegmentTime = nAllMinutes - nFrontMinutes
            end
          else
            nSegment = i - 1
            nSegmentDays = nDays - nFrontSegOffset
            if (nAllMinutes < nFrontMinutes) then
              nSegmentDays -= 1
              nSegmentTime = 24 * 60 + nAllMinutes - nFrontMinutes
            else
              nSegmentTime = nAllMinutes - nFrontMinutes
            end
          end
        else
          if (i % 2 == 0 && nDays < nSegDays + 60) then
            nFrontSegOffset = nSegDays
            nFrontMinutes = aCurSeg[Xdate::ST_HOUR] * 60 + aCurSeg[Xdate::ST_MINUTE]
          end
        end
        nFirstSeg += 1
        aCurSeg = @@SegTimeArray[nFirstSeg]
      end
    end
    if (nSegment == -1) then
      nSegment = nSegCountPerYear - 1     # 冬至
      nSegmentDays = nDays - nFrontSegOffset
      if (nAllMinutes < nFrontMinutes) then
        nSegmentDays -= 1
        nSegmentTime = 24 * 60 + nAllMinutes - nFrontMinutes
      else
        nSegmentTime = nAllMinutes - nFrontMinutes
      end
    end

    return nSegment,nSegmentDays,nSegmentTime,bExactSegment
  end

  # 該日前後各count 天之 節氣干支，陰香干支及月資訊－萬年曆
  # def Xdate.api_wan_nian_li_str_old(nWYear, nWMonth,nWDate,qian_ji_tian,hou_ji_tian,ji_tian_hou_kai_shi)
  #   if (ji_tian_hou_kai_shi >=0) then
  #     nWYear,nWMonth,nWDate = Xdate.NextNWDate(nWYear,nWMonth,nWDate,ji_tian_hou_kai_shi)
  #   else
  #     nWYear,nWMonth,nWDate = Xdate.PreWDates(nWYear,nWMonth,nWDate,ji_tian_hou_kai_shi.abs)
  #   end
  #   h = Hash.new
  #   # 當月資訊
  #   h["yue_#{nWYear}_#{nWMonth}"] = Xdate.api_wan_nian_li_str_yue(nWYear, nWMonth,nWDate)
  #   # 每日資訊
  #   h2,s = Xdate.api_wan_nian_li_str_ris(nWYear, nWMonth,nWDate,qian_ji_tian,hou_ji_tian)
  #   h.merge!(h2)
  #   h["mu_qian_ri_qi_key"] = s

  #   return h
  # end
  def Xdate.api_wan_nian_li_str(nWYear, nWMonth,nWDate,qian_ji_tian,hou_ji_tian,qian_hou_ji_tian)
    h = Hash.new
    a = Array.new

    # 每日資訊
    h_yue,h2,s = Xdate.api_wan_nian_li_str_ris(nWYear, nWMonth,nWDate,qian_ji_tian,hou_ji_tian)

    h2.keys.each do |key|
      h3 = h_yue[key].clone
      h3.merge!(h2[key])
      a.push(h3)
    end

    h["days"] = a

    nWYear1,nWMonth1,nWDate1 = Xdate.NextNWDate(nWYear,nWMonth,nWDate,qian_hou_ji_tian)
    timestamp = "%04d%02d%02d" % [nWYear1,nWMonth1,nWDate1]
    h["after"] = {"y" => "#{nWYear1}", "m" => "#{nWMonth1}", "d" => "#{nWDate1}", "dt" => "0", "ji_tian" => "#{qian_hou_ji_tian}", "timestamp" => timestamp}

    nWYear1,nWMonth1,nWDate1 = Xdate.PreWDates(nWYear,nWMonth,nWDate,qian_hou_ji_tian)
    timestamp = "%04d%02d%02d" % [nWYear1,nWMonth1,nWDate1]
    h["before"] = {"y" => "#{nWYear1}", "m" => "#{nWMonth1}", "d" => "#{nWDate1}", "dt" => "0", "ji_tian" => "#{qian_hou_ji_tian}", "timestamp" => timestamp}

    return h
  end
  # 當月資訊
  def Xdate.api_wan_nian_li_str_yue(nWYear, nWMonth, nWDate)
    # nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, 23,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    h = Hash.new
    # 2002年 1月
    h["xi_li_nian_yue"] = Xdate.GetFullWDateStr2(nWYear,nWMonth,0,Cfate::PAN_DISPLAY_WEST)
    # 辛巳年
    # h["ganzhi_nian"] = Xdate.GetGanZhiYear_Str(nGanZhiYear)
    # # （陰 白蠟金）
    # s = "(#{Xdate.wan_nian_li_yin_yang_str(nGanZhiYear)} #{Xdate.wan_nian_li_liu_shi_na_yin_str(nGanZhiYear)})"
    # h["yin_yang_liu_shi_na_yin"] = s
    # # 生肖（蛇）
    # h["sheng_xiao"] = Xdate.wan_nian_li_sheng_xiao_str(nGanZhiYear)
    # # 太歲(太歲鄭祖 )
    # s = Xdate.wan_nian_li_str("tai_sui")
    # s += Xdate.wan_nian_li_tai_sui_str(nGanZhiYear)
    # h["tai_sui"] = s
    # 冬至
    nGanZhiYear1,nGanZhiMonth1,nGanZhiDay1,nGanZhiHour1,nCurSegment1 = Xdate.GetSeg8Words(nWYear, nWMonth,1, 0,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    h["yue_qian_jie_qi"] = Xdate.wan_nian_li_er_shi_si_jie_qi_str(nCurSegment1)
    # 小寒1 / 5  20:44
    jieqiIndex = Xdate.Get_Nian_Yue_Jieqi_Index(nWYear,nWMonth)
    yue,ri,shi,fen = Xdate.GetJieqiShiJianByIndex(jieqiIndex)
    # s = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}#{yue}/#{ri} #{shi}:#{fen}"
    # h["yue_jie"] = s
    h["yue_jie"] = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}"
    h["yue_jie_time"] = Time.new(nWYear, yue, ri, shi, fen, 2, "+08:00")
    # 大寒1 / 20  14:03
    jieqiIndex = Pm.TestNo24(jieqiIndex + 1)
    yue,ri,shi,fen = Xdate.GetJieqiShiJianByIndex(jieqiIndex)
    # s = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}#{yue}/#{ri} #{shi}:#{fen}"
    # h["yue_qi"] = s
    h["yue_qi"] = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}"
    h["yue_qi_time"] = Time.new(nWYear, yue, ri, shi, fen, 2, "+08:00")
    return h
  end
  # def Xdate.api_wan_nian_li_str_yue_old(nWYear, nWMonth, nWDate)
  #   # nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, 23,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
  #   h = Hash.new
  #   # 2002年 1月
  #   h["xi_li_nian_yue"] = Xdate.GetFullWDateStr2(nWYear,nWMonth,0,Cfate::PAN_DISPLAY_WEST)
  #   # 辛巳年
  #   # h["ganzhi_nian"] = Xdate.GetGanZhiYear_Str(nGanZhiYear)
  #   # # （陰 白蠟金）
  #   # s = "(#{Xdate.wan_nian_li_yin_yang_str(nGanZhiYear)} #{Xdate.wan_nian_li_liu_shi_na_yin_str(nGanZhiYear)})"
  #   # h["yin_yang_liu_shi_na_yin"] = s
  #   # # 生肖（蛇）
  #   # h["sheng_xiao"] = Xdate.wan_nian_li_sheng_xiao_str(nGanZhiYear)
  #   # # 太歲(太歲鄭祖 )
  #   # s = Xdate.wan_nian_li_str("tai_sui")
  #   # s += Xdate.wan_nian_li_tai_sui_str(nGanZhiYear)
  #   # h["tai_sui"] = s
  #   # 冬至
  #   nGanZhiYear1,nGanZhiMonth1,nGanZhiDay1,nGanZhiHour1,nCurSegment1 = Xdate.GetSeg8Words(nWYear, nWMonth,1, 0,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
  #   h["yue_qian_jie_qi"] = Xdate.wan_nian_li_er_shi_si_jie_qi_str(nCurSegment1)
  #   # 小寒1 / 5  20:44
  #   jieqiIndex = Xdate.Get_Nian_Yue_Jieqi_Index(nWYear,nWMonth)
  #   yue,ri,shi,fen = Xdate.GetJieqiShiJianByIndex(jieqiIndex)
  #   s = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}#{yue}/#{ri} #{shi}:#{fen}"
  #   h["yue_jie"] = s
  #   # 大寒1 / 20  14:03
  #   jieqiIndex = Pm.TestNo24(jieqiIndex + 1)
  #   yue,ri,shi,fen = Xdate.GetJieqiShiJianByIndex(jieqiIndex)
  #   s = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}#{yue}/#{ri} #{shi}:#{fen}"
  #   h["yue_qi"] = s
  #   return h
  # end
  # 每日資訊
  def Xdate.api_wan_nian_li_str_ris(nWYear, nWMonth,nWDate,qian_ji_tian,hou_ji_tian)
    nWYear, nWMonth,nWDate = Xdate.PreWDates(nWYear,nWMonth,nWDate,qian_ji_tian)
    h = Hash.new
    h_yue = Hash.new
    count = qian_ji_tian + hou_ji_tian + 1
    s = ""
    (0...count).each do |i|
      # 當月資訊
      h_yue["ri_#{nWYear}_#{nWMonth}_#{nWDate}"] = Xdate.api_wan_nian_li_str_yue(nWYear, nWMonth, nWDate)
      # days = Xdate.GetTotalWDaysFrom180111(nWYear, nWMonth, nWDate)
      # h["ri_#{days}"] = Xdate.api_wan_nian_li_str_ri(nWYear, nWMonth,nWDate))
      h["ri_#{nWYear}_#{nWMonth}_#{nWDate}"] = Xdate.api_wan_nian_li_str_ri(nWYear, nWMonth,nWDate)
      if (qian_ji_tian == i) then
        # s = "ri_#{days}"
        s = "ri_#{nWYear}_#{nWMonth}_#{nWDate}"
      end
      nWYear,nWMonth,nWDate = Xdate.NextWDate(nWYear,nWMonth,nWDate)
    end
    return h_yue,h,s
  end
  def Xdate.api_wan_nian_li_str_ri(nWYear, nWMonth,nWDate)
    h = Hash.new
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, 0,Xdate::FIRST_NEXT)
    # （陰 白蠟金）
    s = "(#{Xdate.wan_nian_li_yin_yang_str(nGanZhiYear)} #{Xdate.wan_nian_li_liu_shi_na_yin_str(nGanZhiYear)})"
    h["yin_yang_liu_shi_na_yin"] = s
    # 生肖（蛇）
    h["sheng_xiao"] = Xdate.wan_nian_li_sheng_xiao_str(nGanZhiYear)
    # 太歲(太歲鄭祖 )
    s = Xdate.wan_nian_li_str("tai_sui")
    s += Xdate.wan_nian_li_tai_sui_str(nGanZhiYear)
    h["tai_sui"] = s

    # 國曆
    h["guo_li"] = Xdate.bignos(nWDate)
    # 星期
    h["week_day"] = Xdate.GetWWeekDayStr(nWYear, nWMonth, nWDate)
    # 農曆年月日
    # 干支年月日
    h2 = Xdate.wan_nian_li_ri_ganzhi_str(nWYear, nWMonth,nWDate)
    h.merge!(h2)
    # 奇門日局
    h["qi_men_ri_ju"] = Xdate.api_nong_min_li_qi_men_ri_ju(nWYear,nWMonth,nWDate)

    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, 23,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    h["ri_jie_qi"] = Xdate.wan_nian_li_er_shi_si_jie_qi_str(nCurSegment)

    h["timestamp"] = "%04d%02d%02d" % [nWYear,nWMonth,nWDate]

    return h
  end
  def Xdate.wan_nian_li_str(key)
    return Xdate.nongminli_str("wan_nian_li.#{key}")
  end
  # 六十納音
  def Xdate.wan_nian_li_liu_shi_na_yin_str(nGanZhi)
    s = Xdate.wan_nian_li_str("liu_shi_na_yin")
    a = s.split(",")
    index = nGanZhi / 2
    return a[index]
  end
  # 陰陽
  def Xdate.wan_nian_li_yin_yang_str(nGanZhi)
    s = Xdate.wan_nian_li_str("yin_yang")
    a = s.split(",")
    index = nGanZhi % 2
    return a[index]
  end
  # 生肖
  def Xdate.wan_nian_li_sheng_xiao_str(nGanZhi)
    s = Xdate.wan_nian_li_str("sheng_xiao")
    a = s.split(",")
    index = nGanZhi % 12
    return a[index]
  end
  # 太歲
  def Xdate.wan_nian_li_tai_sui_str(nGanZhi)
    s = Xdate.wan_nian_li_str("liu_shi_tai_sui")
    a = s.split(",")
    return a[nGanZhi]
  end
  def Xdate.wan_nian_li_er_shi_si_jie_qi_str(nCurSegment)
    index = nCurSegment % 24
    s = Xdate.wan_nian_li_str("er_shi_si_jie_qi")
    a = s.split(",")
    return a[index]
  end
  def Xdate.wan_nian_li_ri_ganzhi_str(nWYear, nWMonth,nWDate)
    h = Hash.new

    h["yangli_nian"] = nWYear.to_s
    h["yangli_yue"] = nWMonth.to_s
    h["yangli_ri"] = nWDate.to_s
    h["timestamp"] = "%04d%02d%02d" % [nWYear,nWMonth,nWDate]

    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, 0,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    a = Xdate.ganzhi_date_str_array2(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour)
    h["jieqi_ganzhi_nian"] = a[0]
    h["jieqi_ganzhi_yue"] = a[1]
    h["jieqi_ganzhi_ri"] = a[2]

    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(nWYear, nWMonth,nWDate, 0,nil)
    a = Xdate.ganzhi_date_str_array2(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour)
    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
    h["yinli_ganzhi_nian"] = a[0]
    h["yinli_ganzhi_run_yue"] = bLeapMonth ? Xdate.wan_nian_li_str("run") : ""
    h["yinli_ganzhi_yue"] = nEMonth.to_s
    h["yinli_ganzhi_ri"] = nEDate.to_s

    # shiju 2017/02/17新增
    # 小寒1 / 5  20:44
    jieqiIndex = Xdate.Get_Nian_Yue_Jieqi_Index(nWYear,nWMonth)
    yue,ri,shi,fen = Xdate.GetJieqiShiJianByIndex(jieqiIndex)
    # s = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}#{yue}/#{ri} #{shi}:#{fen}"
    # h["yue_jie"] = s
    h["yue_jie"] = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}"
    h["yue_jie_time"] = Time.new(nWYear, yue, ri, shi, fen, 2, "+08:00")
    # 大寒1 / 20  14:03
    jieqiIndex = Pm.TestNo24(jieqiIndex + 1)
    yue,ri,shi,fen = Xdate.GetJieqiShiJianByIndex(jieqiIndex)
    # s = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}#{yue}/#{ri} #{shi}:#{fen}"
    # h["yue_qi"] = s
    h["yue_qi"] = "#{Xdate.wan_nian_li_er_shi_si_jie_qi_str(jieqiIndex)}"
    h["yue_qi_time"] = Time.new(nWYear, yue, ri, shi, fen, 2, "+08:00")

    return h
  end
  def Xdate.api_nong_min_li_shi_ju_chong_sha(nWYear, nWMonth, nWDate, nWHour)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, nWHour,59,Xdate::FIRST_NEXT,Xdate::SEGMENT_SPRING,Xdate::SegmentNow)
    a1 = Xdate.api_nong_min_li_shi_ju_chong(nGanZhiHour)
    a2 = Xdate.api_nong_min_li_shi_ju_hao_huai(nGanZhiDay,nGanZhiHour)
    a3 = Xdate.api_nong_min_li_shi_ju_sha(nGanZhiHour)
    return a1 + a2 + a3
  end
  def Xdate.api_ri_shiju(nWYear, nWMonth, nWDate)
    nFirstTimeType = nil
    a = Array.new
    (0..11).each do |nETime|
      nWHour = Xdate.ETime2Hour(nETime)
      nGanZhiDay = Xdate.GetWDateLunarGanZhi(nWYear,nWMonth,nWDate,nWHour,nFirstTimeType)
      nGanZhiHour = Xdate.GetWHourLunarGanZhi(nWYear,nWMonth,nWDate,nWHour,nFirstTimeType)
      a1 = Xdate.api_nong_min_li_shi_ju_hao_huai(nGanZhiDay,nGanZhiHour)
      a.push([Xdate.GetGanZhiTime_Str(nGanZhiHour)] + a1)
    end
    return a
  end
  def Xdate.api_nong_min_li_shi_ju_hao_huai(nGanZhiDay,nGanZhiHour)
    di_zhi_ri = Xdate.GetGanZhiEarth(nGanZhiDay)
    tian_gan_ri = Xdate.GetGanZhiSky(nGanZhiDay)
    di_zhi_shi = Xdate.GetGanZhiEarth(nGanZhiHour)
    tian_gan_shi = Xdate.GetGanZhiSky(nGanZhiHour)

    ri_key = "#{Xdate.tiangan_key(tian_gan_ri)}_#{Xdate.dizhi_key(di_zhi_ri)}"
    shi_key = "#{Xdate.tiangan_key(tian_gan_shi)}_#{Xdate.dizhi_key(di_zhi_shi)}"

    s = Xdate.gan_zhi_shi_ju_hao_huai_str("#{ri_key}.#{shi_key}")
    a = s.split(",")
    return a
  end
  def Xdate.gan_zhi_shi_ju_str(key)
    return Xdate.nongminli_str("gan_zhi_shi_ju.#{key}")
  end
  def Xdate.gan_zhi_shi_ju_hao_huai_str(key)
    return Xdate.gan_zhi_shi_ju_str("hao_huai.#{key}")
  end
  def Xdate.tiangan_key(tiangan)
    return "s#{tiangan}"
  end
  def Xdate.dizhi_key(dizhi)
    return "e#{dizhi}"
  end
  def Xdate.nongminli_bigspace()
    return Xdate.nongminli_str("big_space")
  end
  def Xdate.gan_zhi_shi_ju_chong_sha_array(key)
    s = Xdate.gan_zhi_shi_ju_str("chong_sha.#{key}")
    return s.split(",")
  end
  def Xdate.gan_zhi_shi_ju_chong_str(key)
    a = Xdate.gan_zhi_shi_ju_chong_sha_array(key)
    chong = Xdate.gan_zhi_shi_ju_str("chong")
    return chong + a[1]
  end
  def Xdate.api_nong_min_li_shi_ju_chong(nGanZhiHour)
    di_zhi_shi = Xdate.GetGanZhiEarth(nGanZhiHour)
    tian_gan_shi = Xdate.GetGanZhiSky(nGanZhiHour)
    shi_key = "#{Xdate.tiangan_key(tian_gan_shi)}_#{Xdate.dizhi_key(di_zhi_shi)}"
    s = Xdate.gan_zhi_shi_ju_chong_str("#{shi_key}")
    return [s]
  end
  def Xdate.gan_zhi_shi_ju_sha_str(key)
    a = Xdate.gan_zhi_shi_ju_chong_sha_array(key)
    return a[3]
  end
  def Xdate.api_nong_min_li_shi_ju_sha(nGanZhiHour)
    di_zhi_shi = Xdate.GetGanZhiEarth(nGanZhiHour)
    tian_gan_shi = Xdate.GetGanZhiSky(nGanZhiHour)
    shi_key = "#{Xdate.tiangan_key(tian_gan_shi)}_#{Xdate.dizhi_key(di_zhi_shi)}"
    s = Xdate.gan_zhi_shi_ju_sha_str("#{shi_key}")
    s = Xdate.nongminli_bigspace() + s
    return [s]
  end

  # 奇門日局
  def Xdate.api_nong_min_li_qi_men_ri_ju(nWYear,nWMonth,nWDate)
    if (nWYear.between?(1991,2100)) then
      return Xdate.nongminli_str("qi_men_ri_ju.m_#{nWYear}_#{nWMonth}.d_#{nWDate}")
    else
      return ""
    end
  end
  # 時辰吉凶數
  def Xdate.api_nong_min_li_shi_gixiong(nWYear,nWMonth,nWDate,nWHour)
    if (nWYear.between?(1991,2100)) then
      s = Xdate.nongminli_str("shi_gi_xiong.m_#{nWYear}_#{nWMonth}.d_#{nWDate}")
      a = s.split(",")
      nWTime = Xdate.Hour2WTime(nWHour)
      return a[nWTime]
    else
      return ""
    end
  end
  # 上昇星座
  def Xdate.api_nong_min_li_shang_sheng_xing_zuo(nWYear,nWMonth,nWDate,nWHour)
    if (nWYear.between?(1801,2100)) then
      s = Xdate.nongminli_str("shang_sheng_xing_zuo.m_#{nWYear}_#{nWMonth}.d_#{nWDate}")
      a = s.split(",")
      nWTime = Xdate.Hour2WTime(nWHour)
      return a[nWTime]
    else
      return ""
    end
  end
end
