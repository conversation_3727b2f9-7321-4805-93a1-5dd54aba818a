require("SkyEarthFive_Function.rb")

class Eightword
  def GetCurTime(remote_ip)
    y,m,d,h,mi,s = Xdate.GetCurTime(remote_ip)

    cds_W_YMDH(y,m,d,h)
    cds_WMI(mi)
    cds_ET(Xdate.Hour2ETime(cdg_WH()))
    cds_EMI(Eightword.GetPanMinute(cdg_WH(),mi))

      y,m,d,l = Xdate.West2East(cdg_WY(),cdg_WM(),cdg_WD())
      cds_E_YMDL(y,m,d,l)
    y,m,d,l,t = Xdate.NextEDateFromTime(cdg_EY(), cdg_EM(), cdg_ED(), cdg_EL(), cdg_ET())
    cds_E_YMDLT(y,m,d,l,t)
  end

  def CheckUserAskDate()
#puts("1ddg_EY():#{ddg_EY()}")
    if (Xdate.IsYearLegal?(ddg_EY())) then
        y,m,d,l,t = ddg_E_YMDLT()
      y,m,d,l = Xdate.GetLegalEDate(y,m,d,l)
      dds_E_YMDLT(y,m,d,l,t)
        y,m,d = Xdate.East2West(y,m,d,l)
        dds_WY(y)
        dds_WM(m)
        dds_WD(d)
      # dds_WH(Xdate.ETime2Hour(ddg_ET()))
      dds_ET(Xdate.Hour2ETime(ddg_WH()))
      dds_EMI(Eightword.GetPanMinute(ddg_WH(),ddg_WMI()))
      #dds_WMI(Eightword.GetPanWMinute(ddg_EMI()))
      else
        y,m,d,l = Xdate.West2East(ddg_WY(),ddg_WM(),ddg_WD())
        dds_EY(y)
        dds_EM(m)
        dds_ED(d)
        dds_EL(d)
      dds_ET(Xdate.Hour2ETime(ddg_WH()))
      dds_EMI(Eightword.GetPanMinute(ddg_WH(),ddg_WMI()))
      end
      y,m,d,l,t = ddg_E_YMDLT()
    y,m,d,l,t = Xdate.NextEDateFromTime(y,m,d,l,t)
    dds_E_YMDLT(y,m,d,l,t)
#puts("2ddg_EY():#{ddg_EY()}")
  end

  def CheckSkyEarth()
    nSkyEarth = ddg_SkyEarth()
    bStop = false
    if (nSkyEarth == nil) then
      nSkyEarth = Sky::SKY
      if (@m_nPanType == Cfate::PAN_TENYEAR) then
        (0..11).each do |nIndex|
          if (!bStop) then
            nStarYear = cp_fp_LargeWin_Start_Year(nIndex)
            nYear = ddg_WY()
            if (nYear.between?(nStarYear + 5,nStarYear + 9)) then
              nSkyEarth = Earth::EARTH
              bStop = true
            end
          end
        end
      elsif (@m_nPanType == Cfate::PAN_FLOWYEAR) then
        nMonth = fdg_SM()
        if (nMonth > 6) then
          nSkyEarth = Earth::EARTH
        end
      elsif (@m_nPanType == Cfate::PAN_FLOWMONTH) then
        nDate = fdg_SD()
        if (nDate > 15) then
          nSkyEarth = Earth::EARTH
        end
      elsif (@m_nPanType == Cfate::PAN_FLOWDATE) then
        nHour = ddg_WH()
        if (nHour > 12) then
          nSkyEarth = Earth::EARTH
        end
      elsif (@m_nPanType == Cfate::PAN_FLOWTIME) then
        nHour = ddg_WH()
        if (nHour % 2 == 0) then
          nSkyEarth = Earth::EARTH
        end
      end
    end
    dds_SkyEarth(nSkyEarth)
  end

  def setFlowTimeInfo(nPanType)
    if (nPanType < Cfate::PAN_TENYEAR) then
      y,m,d,l,t = uig_E_YMDLT()
      fds_E_YMDLT(y,m,d,l,t)

      y,m,d,h = uig_W_YMDH()
      fds_W_YMD(y,m,d)
      fds_WH(h)
      fds_EY((ddg_EY() == nil) ? cdg_EY() : ddg_EY())
    else
      if (@par_PanTimeBase == Eightword::PAN_TIME_BASE_NOW) then
        nEYear,nEMonth,nEDate,bLeapMonth,nETime = cdg_E_YMDLT()
        nEMin = cdg_EMI()
        nWYear,nWMonth,nWDate,nWHour = cdg_W_YMDH()
        nWMin = cdg_WMI()
      elsif (@par_PanTimeBase == Eightword::PAN_TIME_BASE_BIRTH) then
        nEYear,nEMonth,nEDate,bLeapMonth,nETime = uig_E_YMDLT()
        nEMin = Eightword.GetPanMinute(uig_WH(),0) #uig_WMI())
        nWYear,nWMonth,nWDate,nWHour = uig_W_YMDH()
        nWMin = uig_WMI()
      else  #PAN_TIME_BASE_DEFINE
        nEYear,nEMonth,nEDate,bLeapMonth,nETime = pdg_E_YMDLT()
        nEMin = Eightword.GetPanMinute(pdg_WH(),pdg_WMI())
        nWYear,nWMonth,nWDate,nWHour = pdg_W_YMDH()
        nWMin = pdg_WMI()
      end
#puts("3fdg_EY():#{fdg_EY()},ddg_EY():#{ddg_EY()},nEYear:#{nEYear}")

      fds_EY((ddg_EY() == nil) ? nEYear : ddg_EY())
      fds_EM((ddg_EM() == nil) ? nEMonth : ddg_EM())
      fds_EL((ddg_EL() == nil) ? bLeapMonth : ddg_EL())
      fds_ED((ddg_ED() == nil) ? nEDate : ddg_ED())
      fds_ET((ddg_ET() == nil) ? nETime : ddg_ET())
      fds_WH((ddg_WH() == nil) ? nWHour : ddg_WH())
      fds_EMI((ddg_EMI() == nil) ? nEMin : ddg_EMI())
      fds_WMI((ddg_WMI() == nil) ? nWMin : ddg_WMI())
#puts("4fdg_EY():#{fdg_EY()},ddg_EY():#{ddg_EY()},nEYear:#{nEYear}")

      if (nPanType == Cfate::PAN_TENYEAR) then
        fds_E_YMDLT(fdg_EY(),fdg_EM(),fdg_ED(),fdg_EL(),fdg_ET())
      elsif (nPanType == Cfate::PAN_FLOWYEAR) then
        fds_E_YMDLT(fdg_EY(),fdg_EM(),fdg_ED(),fdg_EL(),fdg_ET())
        elsif (nPanType == Cfate::PAN_FLOWMONTH) then
        fds_E_YMDLT(fdg_EY(),fdg_EM(),fdg_ED(),fdg_EL(),fdg_ET())
        elsif (nPanType == Cfate::PAN_FLOWDATE) then
            fds_ET(fdg_ET())
          fds_EMI(nEMin)
        elsif (nPanType == Cfate::PAN_FLOWTIME) then
          nWHour = fdg_WH()
          fds_EMI(nEMin)
        elsif (nPanType >= Cfate::PAN_FLOWMIN) then
          nWHour = fdg_WH()
          nWMin = fdg_WMI()
        end

        # 日期有可能錯誤,要確認一下
        y,m,d,l = fdg_E_YMDL()
#puts("1y:#{y},m:#{m},d:#{d}")

        y,m,d,l = Xdate.GetLegalEDate(y,m,d,l)
#puts("2y:#{y},m:#{m},d:#{d}")
        #if (nPanType >= Cfate::PAN_FLOWDATE) then
          fds_E_YMDL(y,m,d,l)
      #end

#puts("1y:#{y},m:#{m},d:#{d}")
      # y,m,d,l = fdg_E_YMDL()
      y,m,d = Xdate.East2West(y,m,d,l)
#puts("2y:#{y},m:#{m},d:#{d}")
      fds_W_YMD(y,m,d)
      fds_WH(nWHour)
      fds_WMI(nWMin)
    end
    getFlowtimeSegmentInfo()
  end
  def getFlowtimeSegmentInfo()
    nWYear,nWMonth,nWDate,nWHour,nWMinute = fdg_W_YMDHm()
    nSYear,nSMonth,nSDate,nWHour,nWMinute,nSegmentIndex = Xdate.Wdate2SegmentDate(nWYear,nWMonth,nWDate,23,59,@par_FirstSegment)
    fds_s_YMDHmS(nSYear,nSMonth,nSDate,nWHour,nWMinute,nSegmentIndex)
    CheckSkyEarth()
  end

  #計算本命天干
  def cp_getSkyIndex()
     return Sky.GetYearSkyIndex(uig_EY())
  end

  #---------------------------------------------------------------------
  #計算本命地支
  def cp_getEarthIndex()
    return Earth.GetYearEarthIndex(uig_EY())
  end

  def cp_getAnimalEarthIndex()
    if (@par_Animal_Start == Xdate::ANIMAL_FIRST_SEGMENT) then
      return Earth.GetYearEarthIndex(uig_SY())
    else
      return Earth.GetYearEarthIndex(uig_EY())
    end
  end

  def cp_GetnSex()
    uis_nSIY(cp_getSkyIndex() % 2)
  end

  #計算本命五行局
  def cp_getFive()
    i = cp_getLifeEarthIndex() + 2
    i = Earth.Modify(i)
    i = i >> 1

    nSkyIndex = cp_getSkyIndex()
    j = nSkyIndex % 5

    return @@ByFive[i * 5 + j]
  end

  def cp_GetSmallPos()
    # 起小限規則
    if (@par_SmallSanType == Eightword::PAN_SSAN_TRAD) then
      # 傳統排法(預設值)
      n = cp_getEarthIndex()
      if (n == 0 || n == 4 || n == 8) then
        k = 8
      elsif (n == 1 || n == 5 || n == 9) then
        k = 5
      elsif (n == 2 || n == 6 || n == 10) then
        k = 2
      elsif (n == 3 || n == 7 || n == 11) then
        k = 11
      end
    else
      # 生年地支起小限
      k = cp_getEarthIndex()
      k = Earth.Modify(k - 2)
    end
  end

  #*** 大運起始位置之起始值
  def cp_GetLargePos()
    return cp_getFive()
  end

  def cp_GetLarge()
    if (@Large[0] != nil) then
      return @Large
    end

    m = cp_mp_getByAfterYear() + 1

    (0..11).each do |i|
      @Large[i] = m
      m += 10
    end
    return @Large
  end

  def LargeSanExist?()
    return (Earth.Exist?(@m_nLargeSan))
  end

  def g_getLargeSan()
    return cp_GetLargeSan(0)
  end

  def cp_GetLargeSan(nFlowAge)
    if (LargeSanExist?()) then
      return @m_nLargeSan
    end
    cp_GetLarge()

    @m_nLargeSan = -1
    nAge = nFlowAge
    if (nFlowAge == 0) then
        nAge = cp_GetFlowYearOld()
      end
#puts("cp_GetLargeSan nAge:#{nAge}, fdg_EY:#{fdg_EY()}")

    i = 0
    n = 200
    smallest = 0
    while ((@m_nLargeSan == -1) && (i < 12))
      m = @Large[i]
      if (n > m) then
        n = m
        smallest = i
      end
      if ((nAge >= m && nAge < m + 10) ||
        (nAge >= 120 + m && nAge < m + 130)) then
        @m_nLargeSan = i
      end
      i += 1
    end
    if (@m_nLargeSan == -1) then
      @m_nLargeSan = smallest
    end
    return @m_nLargeSan
  end
  def cp_GetFlowYearOld()
      return Xdate.GetYearOld(uig_EY(),fdg_EY())
  end
  def g_GetLarge(nEarth=nil)
    if (nEarth == nil) then
      aLarge = Array.new(12)
      (1..12).each do |nEarth|
        nEarthIndex = Earth.Earth2EarthIndex(nEarth)
        aLarge[nEarthIndex] = cp_getHouseLarge(nEarth)
      end
      return aLarge
    else
      return cp_getHouseLarge(nEarth)
    end
  end

  def gNormal_GetSky()
    nSkyIndex = cp_getSkyIndex()
    nSky = Sky.SkyIndex2Sky(nSkyIndex)
    return nSky
  end

  def gNormal_GetEarth()
    nEarthIndex = cp_getEarthIndex()
    nEarth = Earth.EarthIndex2Earth(nEarthIndex)
    return nEarth
  end

  def cp_CheckDirectionClock()
      #陽男,陰女順行
      bSex = uig_bS()
      nSex = uig_nSIY()
    if ((nSex == 0 && bSex == false) ||    #陽男
        (nSex == 1 && bSex == true)) then  #陰女
        return true
    else #陰男,陽女逆行
      return false
    end
  end

  def cp_get8Words(mainKey,sFunc,byWhatIdx=nil,nIndex=nil)
    if (nIndex == nil) then
      if (byWhatIdx == nil) then
        if (@EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][Eightword::UPI_GANZHI_8WORDS][Eightword::ByYearIdx] == nil) then
          eval(sFunc)
        end
        @EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][Eightword::UPI_GANZHI_8WORDS]
      else
        if (@EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][Eightword::UPI_GANZHI_8WORDS][byWhatIdx] == nil) then
          eval(sFunc)
        end
        @EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][Eightword::UPI_GANZHI_8WORDS][byWhatIdx]
      end
    else
      if (byWhatIdx == nil) then
        if (@EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][Eightword::UPI_GANZHI_8WORDS][Eightword::ByYearIdx] == nil) then
          eval(sFunc)
        end
        @EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][Eightword::UPI_GANZHI_8WORDS]
      else
        if (@EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][Eightword::UPI_GANZHI_8WORDS][byWhatIdx] == nil) then
          eval(sFunc)
        end
        @EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][Eightword::UPI_GANZHI_8WORDS][byWhatIdx]
      end
    end
  end
  def cp_set8Words(mainKey,byValue,byWhatIdx=nil,nIndex=nil)
    byValue = cloneValue(byValue)
    if (nIndex == nil) then
      if (byWhatIdx == nil) then
          @EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][Eightword::UPI_GANZHI_8WORDS] = byValue
      else
        @EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][Eightword::UPI_GANZHI_8WORDS][byWhatIdx] = byValue
      end
    else
      if (byWhatIdx == nil) then
        @EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][Eightword::UPI_GANZHI_8WORDS] = byValue
      else
        @EightwordInfo[mainKey][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][Eightword::UPI_GANZHI_8WORDS][byWhatIdx] = byValue
      end
    end
  end

  def cloneValue(byValue)
    if (byValue.class == Integer) then
      return byValue
    elsif (byValue.class == Float) then
      return byValue
    elsif (byValue.class == TrueClass) then
      return byValue
    elsif (byValue.class == FalseClass) then
      return byValue
    elsif (byValue == nil) then
      return byValue
    else
      return byValue.clone
    end
  end

  def cp_mp_get8Words(byWhatIdx=nil)
    mainKey = Eightword::MAIN_PAN
    sFunc = "cp_mp_GanZhi8Words()"
    cp_get8Words(mainKey,sFunc,byWhatIdx)
  end
  def cp_mp_set8Words(byValue,byWhatIdx=nil)
    mainKey = Eightword::MAIN_PAN
    cp_set8Words(mainKey,byValue,byWhatIdx)
  end
  def cp_mp_GanZhi8Words()
    nWYear,nWMonth,nWDate,nHour,nMinute = uig_W_YMDHm()
    h8Words = ew_get8Words(nWYear,nWMonth,nWDate,nHour,nMinute)
    cp_mp_set8Words(h8Words)
  end
  def ew_get8Words(nWYear,nWMonth,nWDate,nHour,nMinute)
    h8Words = Hash.new

      nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, nHour, nMinute,@par_FirstTimeType,@par_FirstSegment,@par_SegmentMove)
      h8Words[Eightword::ByYearIdx]  = nGanZhiYear
      h8Words[Eightword::ByMonthIdx]  = nGanZhiMonth
      h8Words[Eightword::ByDayIdx]  = nGanZhiDay
      h8Words[Eightword::ByHourIdx]  = nGanZhiHour
      h8Words[Eightword::Segment]  = nCurSegment

      return h8Words
  end

  # 四柱共用函式
  def cp_mp_getPanInfo(key,sFunc,byWhatIdx=nil,arrayIndex=nil)
    # if (key == Eightword::ByEarthScore_4) then
    #   Pm.saveTestDb("cp_mp_getPanInfo",@EightwordInfo)
    # end
    if (byWhatIdx != nil) then
      if (arrayIndex == nil) then
        if (@EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx] == nil) then
          eval(sFunc)
        end
        byValue = @EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx]
      else
        if (@EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx][arrayIndex] == nil) then
          eval(sFunc)
        end
        byValue = @EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx][arrayIndex]
      end
    else
      if (arrayIndex == nil) then
        if (@EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key][Eightword::ByYearIdx] == nil) then
          eval(sFunc)
        end
        byValue = @EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key]
      else
        if (@EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key][arrayIndex] == nil) then
          eval(sFunc)
        end
        byValue = @EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key][arrayIndex]
      end
    end
    return cloneValue(byValue)
  end
  def cp_mp_setPanInfo(key,byWhatIdx,byValue,arrayIndex=nil)
    byValue = cloneValue(byValue)
    if (byWhatIdx != nil) then
      if (arrayIndex == nil) then
        @EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx] = byValue
      else
        @EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key][byWhatIdx][arrayIndex] = byValue
      end
    else
      if (arrayIndex == nil) then
        @EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key] = byValue
      else
        @EightwordInfo[Eightword::MAIN_PAN][Eightword::PAN_FOUR_COL][key][arrayIndex] = byValue
      end
    end
  end

  # 天干
  def cp_BySkyIndex(n8Words)
    return n8Words % 10
  end

  def cp_mp_getBySkyIndex(byWhatIdx=nil)
    return cp_mp_getPanInfo(Eightword::BySky_4,"cp_mp_BySkyIndex_4()",byWhatIdx)
  end
  def cp_mp_setBySkyIndex(byValue,byWhatIdx=nil)
    cp_mp_setPanInfo(Eightword::BySky_4,byWhatIdx,byValue)
  end
  def cp_mp_BySkyIndex_4()
    h8Words = cp_mp_get8Words()
    hSkyIndex = cp_BySkyIndex_4(h8Words)
    cp_mp_setBySkyIndex(hSkyIndex)
  end
  def cp_BySkyIndex_4(h8Words)
    hSkyIndex = initGanzhi_8words(nil)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      n8Words = h8Words[byWhatIdx]
      hSkyIndex[byWhatIdx] = cp_BySkyIndex(n8Words)
    end
    return hSkyIndex
  end

  # 地支
  def cp_ByEarthIndex(n8Words)
    return n8Words % 12
  end
  def cp_mp_getByEarthIndex(byWhatIdx=nil)
    return cp_mp_getPanInfo(Eightword::ByEarth_4,"cp_mp_ByEarthIndex_4()",byWhatIdx)
  end
  def cp_mp_setByEarthIndex(byValue,byWhatIdx=nil)
    cp_mp_setPanInfo(Eightword::ByEarth_4,byWhatIdx,byValue)
  end
  def cp_mp_ByEarthIndex_4()
    h8Words = cp_mp_get8Words()
    hEarthIndex = cp_ByEarthIndex_4(h8Words)
    cp_mp_setByEarthIndex(hEarthIndex)
# nil[1]
  end
  def cp_ByEarthIndex_4(h8Words)
    hEarthIndex = initGanzhi_8words(nil)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      n8Words = h8Words[byWhatIdx]
      hEarthIndex[byWhatIdx] = cp_ByEarthIndex(n8Words)
    end
    return hEarthIndex
  end

  # 變異天干
  def cp_mp_getByChungSky(byWhatIdx=nil,arrayIndex=nil)
    return cp_mp_getPanInfo(Eightword::ByChungSky_4_3,"cp_mp_ByChungSky_4_3()",byWhatIdx,arrayIndex)
  end
  def cp_mp_setByChungSky(byValue,byWhatIdx=nil,arrayIndex=nil)
    cp_mp_setPanInfo(Eightword::ByChungSky_4_3,byWhatIdx,byValue,arrayIndex)
  end
  def cp_mp_ByChungSky_4_3()
    hEarthIndex = cp_mp_getByEarthIndex()
    hChungSky = cp_ByChungSky_4_3(hEarthIndex)
    cp_mp_setByChungSky(hChungSky)
  end
  def cp_ByChungSky_4_3(hEarthIndex)
    hChungSky = initGanzhi_8words_array(3,nil)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      (0...3).each do |j|
        if (@par_ChungSkyType == Eightword::PAN_SKY_1 && j > 0) then
          if ([0,3,9].include?(m)) then
            k = 0
          else
            k = @@ByChungSky[m][j]
          end
        else
          k = @@ByChungSky[m][j]
        end
        hChungSky[byWhatIdx][j] = k
      end
    end
    return hChungSky
  end

  # 天干之五行
  def cp_mp_getBySkyFive(byWhatIdx=nil)
    return cp_mp_getPanInfo(Eightword::BySkyFive_4,"cp_mp_BySkyFive_4()",byWhatIdx)
  end
  def cp_mp_setBySkyFive(byValue,byWhatIdx=nil)
    cp_mp_setPanInfo(Eightword::BySkyFive_4,byWhatIdx,byValue)
  end
  def cp_mp_BySkyFive_4()
    hSkyIndex = cp_mp_getBySkyIndex()
    aSkyFive = cp_BySkyFive_4(hSkyIndex)
    cp_mp_setBySkyFive(aSkyFive)
  end
  def cp_BySkyFive_4(hSkyIndex)
    aSkyFive = initGanzhi_8words(nil)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hSkyIndex[byWhatIdx]
      m >>= 1
      n = Five.Modify(m + 1)
      aSkyFive[byWhatIdx] = n
      end
      return aSkyFive
  end

  # 地支五行
  def cp_mp_getByEarthFive(byWhatIdx=nil)
    return cp_mp_getPanInfo(Eightword::ByEarthFive_4,"cp_mp_ByEarthFive_4()",byWhatIdx)
  end
  def cp_mp_setByEarthFive(byValue,byWhatIdx=nil)
    cp_mp_setPanInfo(Eightword::ByEarthFive_4,byWhatIdx,byValue)
  end
  def cp_mp_ByEarthFive_4()
      hEarthIndex = cp_mp_getByEarthIndex()
      aEarthFive = cp_ByEarthFive_4(hEarthIndex)
      cp_mp_setByEarthFive(aEarthFive)
  end
  def cp_ByEarthFive_4(hEarthIndex)
    aEarthFive = initGanzhi_8words(nil)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      n = @@EarthFiveTable[m]
      aEarthFive[byWhatIdx] = n
      end
      return aEarthFive
  end

  # 變異五行
  def cp_mp_getByChungFive(byWhatIdx=nil,arrayIndex=nil)
    return cp_mp_getPanInfo(Eightword::ByChungFive_4_3,"cp_mp_ByChungFive_4_3()",byWhatIdx,arrayIndex)
  end
  def cp_mp_setByChungFive(byValue,byWhatIdx=nil,arrayIndex=nil)
    cp_mp_setPanInfo(Eightword::ByChungFive_4_3,byWhatIdx,byValue,arrayIndex)
  end
  def cp_mp_ByChungFive_4_3()
    hEarthIndex = cp_mp_getByEarthIndex()
    aChungFive = cp_ByChungFive_4_3(hEarthIndex)
    cp_mp_setByChungFive(aChungFive)
  end
  def cp_ByChungFive_4_3(hEarthIndex)
    aChungFive = initGanzhi_8words_array(3,nil)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      (0...3).each do |j|
        l = cp_getByChungFive_4_3(m,j)
        aChungFive[byWhatIdx][j] = l
      end
    end
    return aChungFive
  end
  def cp_getByChungFive_4_3(nEarthIndex,nChungFiveIndex)
      m = nEarthIndex
      j = nChungFiveIndex
      if (@par_ChungSkyType == Eightword::PAN_SKY_1 && j > 0) then
        if ([0,3,9].include?(m)) then
          k = 0
        else
          k = @@ByChungSky[m][j]
        end
      else
        k = @@ByChungSky[m][j]
      end
      # 月支
      if (k > 0) then
        l = (k - 1) >> 1
        l = Five.Modify(l + 1)
      else
        l = 0xFF
      end
      return l
  end

  # 四柱的主星
  def cp_mp_getByMainStar(byWhatIdx=nil)
    return cp_mp_getPanInfo(Eightword::ByMainStar_4,"cp_mp_ByMainStar_4()",byWhatIdx)
  end
  def cp_mp_setByMainStar(byValue,byWhatIdx=nil)
    cp_mp_setPanInfo(Eightword::ByMainStar_4,byWhatIdx,byValue)
  end
  # 所有的天干與日天干比較。甲為陽，乙為陰，以日天干為基準，
  # 我同者，陰陽相同者為比肩，不同者為劫財；
  # 生我者，陰陽相同者為偏印；不同者為正印
  # 剋我者，陰陽相同者為七殺；不同者為正官；
  # 我剋者，陰陽相同者為偏財；不同者為正財；
  # 我生者，陰陽相同者為食神；不同者為傷官；
  def cp_mp_ByMainStar_4()
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    hSkyIndex = cp_mp_getBySkyIndex()
    hMainStars = cp_ByMainStar_4(nDaySkyIndex,hSkyIndex,false)
    cp_mp_setByMainStar(hMainStars)
  end
  def cp_ByMainStar_4(nDaySkyIndex,hSkyIndex,bNeedDay=false)
    hMainStars = Hash.new
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      nSkyIndex = hSkyIndex[byWhatIdx]
      l = Eightword.MainStar(nDaySkyIndex,nSkyIndex)
      if (!bNeedDay && (byWhatIdx == Eightword::ByDayIdx)) then
        hMainStars[byWhatIdx] = nil
      else
        hMainStars[byWhatIdx] = l
      end
    end
    return hMainStars
  end



  # 四柱的副星
  def cp_mp_getBySubStar(byWhatIdx=nil,arrayIndex=nil)
    return cp_mp_getPanInfo(Eightword::BySubStar_4_3,"cp_mp_BySubStar_4_3()",byWhatIdx,arrayIndex)
  end
  def cp_mp_setBySubStar(byValue,byWhatIdx=nil,arrayIndex=nil)
    cp_mp_setPanInfo(Eightword::BySubStar_4_3,byWhatIdx,byValue,arrayIndex)
  end
  def cp_mp_BySubStar_4_3()
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    aSubStar = cp_BySubStar_4_3(nDaySkyIndex,hEarthIndex)
    cp_mp_setBySubStar(aSubStar)
  end
  def cp_BySubStar_4_3(nDaySkyIndex,hEarthIndex)
    aSubStar = initGanzhi_8words_array(3,nil)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      (0...3).each do |j|
        l = cp_getBySubStar_4_3(nDaySkyIndex,m,j)
        aSubStar[byWhatIdx][j] = l
      end
    end
    return aSubStar
  end

  def cp_getBySubStar_4_3(nDaySkyIndex,nEarthIndex,nSubStarIndex)
    m = nEarthIndex
    j = nSubStarIndex
    if (@par_ChungSkyType == Eightword::PAN_SKY_1 && j > 0) then
      if ([0,3,9].include?(m)) then
        k = 0
      else
        k = @@ByChungSky[m][j]
      end
    else
      k = @@ByChungSky[m][j]
    end

    if (k > 0) then
            nSkyIndex = Sky.Modify(k - 1)
      l = Eightword.MainStar(nDaySkyIndex,nSkyIndex)
    else
      l = 0xFF
    end
    return l
  end


  # 四柱的十二運
  def cp_mp_getBy12Win(byWhatIdx=nil)
    return cp_mp_getPanInfo(Eightword::By12Win_4,"cp_mp_By12Win_4()",byWhatIdx)
  end
  def cp_mp_setBy12Win(byValue,byWhatIdx=nil)
    cp_mp_setPanInfo(Eightword::By12Win_4,byWhatIdx,byValue)
  end
  def cp_mp_By12Win_4()
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    a12Win = cp_By12Win_4(nDaySkyIndex,hEarthIndex)
    cp_mp_setBy12Win(a12Win)
  end
  def cp_By12Win_4(nDaySkyIndex,hEarthIndex)
    a12Win = initGanzhi_8words(nil)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      k = cp_getBy12Win_4(nDaySkyIndex,m)
      a12Win[byWhatIdx] = k
    end
    return a12Win
  end

  def cp_getBy12Win_4(nDaySkyIndex,nEarthIndex)
    m = nEarthIndex
    k = @@By12Win[nDaySkyIndex]
    n = nDaySkyIndex % 2
    if (n == 0) then
      k = Earth.Modify(k + m)
    else
      k = Earth.Modify(k - m)
    end
    return k
  end


  # 四柱中神煞之神
  def cp_init8words_array(arraySize,value)
    h = Hash.new
    if (arraySize > 0) then
        h[Eightword::ByYearIdx]  = Array.new(arraySize,value)
        h[Eightword::ByMonthIdx]  = Array.new(arraySize,value)
        h[Eightword::ByDayIdx]  = Array.new(arraySize,value)
        h[Eightword::ByHourIdx]  = Array.new(arraySize,value)
    else
        h[Eightword::ByYearIdx]  = Array.new
        h[Eightword::ByMonthIdx]  = Array.new
        h[Eightword::ByDayIdx]  = Array.new
        h[Eightword::ByHourIdx]  = Array.new
    end
      return h
  end
  def cp_mp_getDwStarType(byWhatIdx=nil)
    return cp_mp_getPanInfo(Eightword::DwStarType_4,"cp_mp_DwStarType_4()",byWhatIdx)
  end
  def cp_mp_setDwStarType(byWhatIdx,byValue)
    cp_mp_setPanInfo(Eightword::DwStarType_4,byWhatIdx,byValue)
  end
  def cp_mp_DwStarType_4()
    dwStarType = cp_init8words_array(0,nil)
    (1..22).each do |nStar|
      sFunc = "cp_mp_DwStarType_God_#{nStar}(dwStarType)"
      dwStarType = eval(sFunc)
    end
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      cp_mp_setDwStarType(byWhatIdx,dwStarType[byWhatIdx].uniq)
    end
  end
  # 三奇貴人
  def cp_mp_DwStarType_God_1(dwStarType)
    nYearSkyIndex = cp_mp_getBySkyIndex(Eightword::ByYearIdx)
    nMonthSkyIndex = cp_mp_getBySkyIndex(Eightword::ByMonthIdx)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    return cp_DwStarType_God_1(dwStarType,nYearSkyIndex,nMonthSkyIndex,nDaySkyIndex)
  end
  def cp_DwStarType_God_1(dwStarType,nYearSkyIndex,nMonthSkyIndex,nDaySkyIndex)
    if ([0,1,8].include?(nDaySkyIndex)) then
      if (nDaySkyIndex == 0) then # 甲戊庚
        if (nMonthSkyIndex == 4 && nYearSkyIndex == 6) then
          dwStarType[Eightword::ByDayIdx].push(Eightword::GOD_1)
        end
      elsif (nDaySkyIndex == 1) then # 乙丙丁
        if (nMonthSkyIndex == 2 && nYearSkyIndex == 3) then
          dwStarType[Eightword::ByDayIdx].push(Eightword::GOD_1)
        end
      elsif (nDaySkyIndex == 8) then # 壬癸辛
        if (nMonthSkyIndex == 9 && nYearSkyIndex == 7)
          dwStarType[Eightword::ByDayIdx].push(Eightword::GOD_1)
        end
      end
    end
    return dwStarType
  end
  # 魁罡貴人
  def cp_mp_DwStarType_God_2(dwStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    return cp_DwStarType_God_2(dwStarType,nDaySkyIndex,nDayEarthIndex)
  end
  def cp_DwStarType_God_2(dwStarType,nDaySkyIndex,nDayEarthIndex)
    if (nDaySkyIndex == 8 && nDayEarthIndex == 4) then
      dwStarType[Eightword::ByDayIdx].push(Eightword::GOD_2)
    elsif (nDaySkyIndex == 4 && nDayEarthIndex == 10) then
      dwStarType[Eightword::ByDayIdx].push(Eightword::GOD_2)
    elsif (nDaySkyIndex == 6 && nDayEarthIndex == 10) then
      dwStarType[Eightword::ByDayIdx].push(Eightword::GOD_2)
    elsif (nDaySkyIndex == 6 && nDayEarthIndex == 4) then
      dwStarType[Eightword::ByDayIdx].push(Eightword::GOD_2)
    end
    return dwStarType
  end
  # 金神
  def cp_mp_DwStarType_God_3(dwStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    nHourSkyIndex = cp_mp_getBySkyIndex(Eightword::ByHourIdx)
    nHourEarthIndex = cp_mp_getByEarthIndex(Eightword::ByHourIdx)
    return cp_DwStarType_God_3(dwStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex,nHourSkyIndex,nHourEarthIndex)
  end
  def cp_DwStarType_God_3(dwStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex,nHourSkyIndex,nHourEarthIndex)
    if (nMonthEarthIndex >= 5 && nMonthEarthIndex <= 7) then
      if ([[1,1],[5,5],[9,9]].include?([nDaySkyIndex,nDayEarthIndex])) then
        dwStarType[Eightword::ByDayIdx].push(Eightword::GOD_3)
      end
      if (nHourSkyIndex == nHourEarthIndex &&
        [1,5,9].include?(nHourSkyIndex) &&
        [0,5].include?(nDaySkyIndex)) then
        dwStarType[Eightword::ByHourIdx].push(Eightword::GOD_3)
      end
    end
    return dwStarType
  end
  # 福星
  def cp_mp_DwStarType_God_4(dwStarType)
    nMonthSkyIndex = cp_mp_getBySkyIndex(Eightword::ByMonthIdx)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    return cp_DwStarType_God_4(dwStarType,nMonthSkyIndex,nMonthEarthIndex)
  end
  def cp_DwStarType_God_4(dwStarType,nMonthSkyIndex,nMonthEarthIndex)
    if (nMonthSkyIndex == @@ByFu[nMonthEarthIndex]) then
      dwStarType[Eightword::ByMonthIdx].push(Eightword::GOD_4)
    end
    return dwStarType
  end
  # 天乙貴人
  def cp_mp_DwStarType_God_5(dwStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwStarType_God_5(dwStarType,nDaySkyIndex,hEarthIndex)
  end
  def cp_DwStarType_God_5(dwStarType,nDaySkyIndex,hEarthIndex)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (m == @@ByTenYiGod[nDaySkyIndex][0] ||
        m == @@ByTenYiGod[nDaySkyIndex][1]) then
        dwStarType[byWhatIdx].push(Eightword::GOD_5)
      end
    end
    return dwStarType
  end
  # 文昌
  def cp_mp_DwStarType_God_6(dwStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwStarType_God_6(dwStarType,nDaySkyIndex,hEarthIndex)
  end
  def cp_DwStarType_God_6(dwStarType,nDaySkyIndex,hEarthIndex)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (m == @@ByWenChung[nDaySkyIndex]) then
        dwStarType[byWhatIdx].push(Eightword::GOD_6)
      end
    end
    return dwStarType
  end
  # 天德
  def cp_mp_DwStarType_God_7(dwStarType)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    hSkyIndex = cp_mp_getBySkyIndex()
    return cp_DwStarType_God_7(dwStarType,nMonthEarthIndex,hSkyIndex)
  end
  def cp_DwStarType_God_7(dwStarType,nMonthEarthIndex,hSkyIndex)
    nMonth = Earth.ModifyEarth(nMonthEarthIndex - 1)
    if (@@BySkyDe[nMonth - 1] >= 0) then
      m = @@BySkyDe[nMonth - 1]
      (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
        j = hSkyIndex[byWhatIdx]
        if (m == j) then
          dwStarType[byWhatIdx].push(Eightword::GOD_7)
        end
      end
    end
    return dwStarType
  end
  # 月德
  def cp_mp_DwStarType_God_8(dwStarType)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    hSkyIndex = cp_mp_getBySkyIndex()
    return cp_DwStarType_God_8(dwStarType,nMonthEarthIndex,hSkyIndex)
  end
  def cp_DwStarType_God_8(dwStarType,nMonthEarthIndex,hSkyIndex)
    nMonth = Earth.ModifyEarth(nMonthEarthIndex - 1)
    j = (nMonth - 1) % 4
    m  = @@ByMonthDe[j]
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      j = hSkyIndex[byWhatIdx]
      if (m == j) then
        dwStarType[byWhatIdx].push(Eightword::GOD_8)
      end
    end
    return dwStarType
  end
  # 月將
  def cp_mp_DwStarType_God_9(dwStarType)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    nCurSegment = cp_mp_get8Words(Eightword::Segment)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwStarType_God_9(dwStarType,nMonthEarthIndex,nCurSegment,hEarthIndex)
  end
  def cp_DwStarType_God_9(dwStarType,nMonthEarthIndex,nCurSegment,hEarthIndex)
    nMonth = Earth.ModifyEarth(nMonthEarthIndex - 1)
    n = nMonth
    m = n << 1
    m += 1
    if (m >= 24) then
      m -= 24
    end
    if (m == nCurSegment) then
      (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
        nEarthIndex = hEarthIndex[byWhatIdx]
        if (nEarthIndex == (12 - n)) then
          dwStarType[byWhatIdx].push(Eightword::GOD_9)
        end
      end
    end
    return dwStarType
  end
  # 將星 日柱地支 年柱地支
  def cp_mp_DwStarType_God_10(dwStarType)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    nYearEarthIndex = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwStarType_God_10(dwStarType,nDayEarthIndex,nYearEarthIndex,hEarthIndex)
  end
  def cp_DwStarType_God_10(dwStarType,nDayEarthIndex,nYearEarthIndex,hEarthIndex)
    # 日柱地支
    n = nDayEarthIndex % 4
    m = nYearEarthIndex
    if (m == @@ByJohnStar[n]) then
      dwStarType[Eightword::ByYearIdx].push(Eightword::GOD_10)
    end

    # 年柱地支
    n = nYearEarthIndex % 4
    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (m == @@ByJohnStar[n]) then
        dwStarType[byWhatIdx].push(Eightword::GOD_10)
      end
    end
    return dwStarType
  end
  # 華蓋 日柱地支 年柱地支
  def cp_mp_DwStarType_God_11(dwStarType)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    nYearEarthIndex = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    return cp_DwStarType_God_11(dwStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
  end
  def cp_DwStarType_God_11(dwStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
    # 日柱地支
    if (@par_PanGodBase == Eightword::PAN_GOD_DATE) then
      n = nDayEarthIndex % 4
      [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |byWhatIdx|
        m = hEarthIndex[byWhatIdx]
        if (m == @@ByHuaGai[n]) then
          dwStarType[byWhatIdx].push(Eightword::GOD_11)
        end
      end
    end
    # 年柱地支
    if (@par_PanGodBase == Eightword::PAN_GOD_YEAR) then
      n = nYearEarthIndex % 4
      (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |byWhatIdx|
        m = hEarthIndex[byWhatIdx]
        if (m == @@ByHuaGai[n]) then
          dwStarType[byWhatIdx].push(Eightword::GOD_11)
        end
      end
    end
    return dwStarType
  end
  # 學堂
  def cp_mp_DwStarType_God_12(dwStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    hSkyIndex = cp_mp_getBySkyIndex()
    return cp_DwStarType_God_12(dwStarType,nDaySkyIndex,hSkyIndex)
  end
  def cp_DwStarType_God_12(dwStarType,nDaySkyIndex,hSkyIndex)
    [Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |byWhatIdx|
      m = hSkyIndex[byWhatIdx]
      n = nDaySkyIndex >> 1
      if (m == @@BySchool[n]) then
        dwStarType[byWhatIdx].push(Eightword::GOD_12)
      end
    end
    return dwStarType
  end
  # 詞館
  def cp_mp_DwStarType_God_13(dwStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwStarType_God_13(dwStarType,nDaySkyIndex,hEarthIndex)
  end
  def cp_DwStarType_God_13(dwStarType,nDaySkyIndex,hEarthIndex)
    [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |byWhatIdx|
      n = nDaySkyIndex >> 1
      m = hEarthIndex[byWhatIdx]
      if (byWhatIdx != Eightword::ByYearIdx && m == @@BySchool1[n]) then
        dwStarType[byWhatIdx].push(Eightword::GOD_13)
      end
    end
    return dwStarType
  end
  # 驛馬 日柱地支 年柱地支
  def cp_mp_DwStarType_God_14(dwStarType)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    nYearEarthIndex = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    return cp_DwStarType_God_14(dwStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
  end
  def cp_DwStarType_God_14(dwStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
    # 日柱地支
    if (@par_PanGodBase == Eightword::PAN_GOD_DATE) then
      n = nDayEarthIndex % 4
      [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |byWhatIdx|
        m = hEarthIndex[byWhatIdx]
        if (m == @@ByHorse[n]) then
          dwStarType[byWhatIdx].push(GOD_14)
        end
      end
    end
    # 年柱地支
    if (@par_PanGodBase == Eightword::PAN_GOD_YEAR) then
      n = nYearEarthIndex % 4
      (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |byWhatIdx|
        m = hEarthIndex[byWhatIdx]
        if (m == @@ByHorse[n]) then
          dwStarType[byWhatIdx].push(Eightword::GOD_14)
        end
      end
    end
    return dwStarType
  end
  # 紅鸞 年柱地支
  def cp_mp_DwStarType_God_15(dwStarType)
    nYearEarthIndex = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwStarType_God_15(dwStarType,nYearEarthIndex,hEarthIndex)
  end
  def cp_DwStarType_God_15(dwStarType,nYearEarthIndex,hEarthIndex)
    # 年柱地支
    k = nYearEarthIndex
    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (k <= 3) then
        j = 3 - k
      else
        j = 15 - k
      end
      if (m == j) then
        dwStarType[byWhatIdx].push(Eightword::GOD_15)
      end
    end
    return dwStarType
  end
  # 天喜 年柱地支
  def cp_mp_DwStarType_God_16(dwStarType)
    nYearEarthIndex = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwStarType_God_16(dwStarType,nYearEarthIndex,hEarthIndex)
  end
  def cp_DwStarType_God_16(dwStarType,nYearEarthIndex,hEarthIndex)
    # 年柱地支
    k = nYearEarthIndex
    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (k <= 9) then
        j = 9 - k
      else
        j = 21 - k
      end
      if (m == j) then
        dwStarType[byWhatIdx].push(Eightword::GOD_16)
      end
    end
    return dwStarType
  end
  # 六秀
  def cp_mp_DwStarType_God_17(dwStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    return cp_DwStarType_God_17(dwStarType,nDaySkyIndex,nDayEarthIndex)
  end
  def cp_DwStarType_God_17(dwStarType,nDaySkyIndex,nDayEarthIndex)
    if (nDayEarthIndex == 0 && (nDaySkyIndex == 2 || nDaySkyIndex == 4))
      dwStarType[Eightword::ByDayIdx].push(Eightword::GOD_17)
    end
    return dwStarType
  end
  # 十靈
  def cp_mp_DwStarType_God_18(dwStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    return cp_DwStarType_God_18(dwStarType,nDaySkyIndex,nDayEarthIndex)
  end
  def cp_DwStarType_God_18(dwStarType,nDaySkyIndex,nDayEarthIndex)
    if (nDaySkyIndex != 5) then
      m = @@ByTenLin[nDaySkyIndex]
      if (m == nDayEarthIndex || (nDaySkyIndex == 6 && nDayEarthIndex == 10)) then
        dwStarType[Eightword::ByDayIdx].push(Eightword::GOD_18)
      end
    end
    return dwStarType
  end
  # 天醫 月柱地支
  def cp_mp_DwStarType_God_19(dwStarType)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwStarType_God_19(dwStarType,nMonthEarthIndex,hEarthIndex)
  end
  def cp_DwStarType_God_19(dwStarType,nMonthEarthIndex,hEarthIndex)
    nMonth = Earth.ModifyEarth(nMonthEarthIndex - 1)
    n = Earth.Modify(nMonth)
    [Eightword::ByYearIdx,Eightword::ByDayIdx,Eightword::ByHourIdx].each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (m == n) then
        dwStarType[byWhatIdx].push(Eightword::GOD_19)
      end
    end
    return dwStarType
  end

  # 祿神
  def cp_mp_DwStarType_God_20(dwStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwStarType_God_20(dwStarType,nDaySkyIndex,hEarthIndex)
  end
  def cp_DwStarType_God_20(dwStarType,nDaySkyIndex,hEarthIndex)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (m == @@ByLu[nDaySkyIndex]) then
        dwStarType[byWhatIdx].push(Eightword::GOD_20)
      end
    end
    return dwStarType
  end
  # 天德合
  def cp_mp_DwStarType_God_21(dwStarType)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    hSkyIndex = cp_mp_getBySkyIndex()
    return cp_DwStarType_God_21(dwStarType,nMonthEarthIndex,hSkyIndex)
  end
  def cp_DwStarType_God_21(dwStarType,nMonthEarthIndex,hSkyIndex)
    nMonth = Earth.ModifyEarth(nMonthEarthIndex - 1)
    if (@@BySkyDe[nMonth - 1] >= 0) then
      m = @@BySkyDe[nMonth - 1]
      if (m <= 4) then
        k = m + 5
      else
        k = m - 5
      end
      (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
        j = hSkyIndex[byWhatIdx]
        if (k == j) then
          dwStarType[byWhatIdx].push(Eightword::GOD_21)
        end
      end
    end
    return dwStarType
  end
  # 月德合 月柱地支
  def cp_mp_DwStarType_God_22(dwStarType)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    hSkyIndex = cp_mp_getBySkyIndex()
    return cp_DwStarType_God_22(dwStarType,nMonthEarthIndex,hSkyIndex)
  end
  def cp_DwStarType_God_22(dwStarType,nMonthEarthIndex,hSkyIndex)
    nMonth = Earth.ModifyEarth(nMonthEarthIndex - 1)
    j = (nMonth - 1) % 4
    m  = @@ByMonthDe[j]
    if (m <= 4) then
      k = m + 5
    else
      k = m - 5
    end
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      j = hSkyIndex[byWhatIdx]
      if (k == j) then
        dwStarType[byWhatIdx].push(Eightword::GOD_22)
      end
    end
    return dwStarType
  end

  # 旺相休囚死
  def cp_mp_getByFiveLevel(arrayIndex=nil)
    return cp_mp_getPanInfo(Eightword::ByFiveLevel_5,"cp_mp_ByFiveLevel_5()",nil,arrayIndex)
  end
  def cp_mp_setByFiveLevel(byValue,arrayIndex=nil)
    cp_mp_setPanInfo(Eightword::ByFiveLevel_5,nil,byValue,arrayIndex)
  end
  def cp_mp_ByFiveLevel_5()
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    byValue = cp_ByFiveLevel_5(nMonthEarthIndex)
    cp_mp_setByFiveLevel(byValue)
  end

    def cp_ByFiveLevel_5(nMonthEarthIndex)
    aFiveLevel = Array.new(5,nil)
    n = Earth.ModifyEarth(nMonthEarthIndex - 1) # 地支變成月(寅月為1月)
    nMonth = n
    if ([1,2].include?(n)) then # 木
      aFiveLevel[0] = 1 # 木
      aFiveLevel[1] = 2 # 木生火木相生
      aFiveLevel[2] = 0 # 水生木水休
      aFiveLevel[3] = 4 # 金剋木囚
      aFiveLevel[4] = 3 # 木剋土死
    elsif ([3,6,9,12].include?(n)) then # 土
      aFiveLevel[0] = 3 # 土
      aFiveLevel[1] = 4 # 土生金土相生
      aFiveLevel[2] = 2 # 火生土火休
      aFiveLevel[3] = 1 # 木剋土囚
      aFiveLevel[4] = 0 # 土剋水土
    elsif ([4,5].include?(n)) then # 火
      aFiveLevel[0] = 2 # 火
      aFiveLevel[1] = 3 # 火生土火相生
      aFiveLevel[2] = 1 # 木生火木休
      aFiveLevel[3] = 0 # 水剋火囚
      aFiveLevel[4] = 4 # 火剋金死
    elsif ([7,8].include?(n)) then # 金
      aFiveLevel[0] = 4 # 金
      aFiveLevel[1] = 0 # 金生水金相生
      aFiveLevel[2] = 3 # 土生金土休
      aFiveLevel[3] = 2 # 火剋金囚
      aFiveLevel[4] = 1 # 金剋木死
    else # 水 10,11
      aFiveLevel[0] = 0 # 水
      aFiveLevel[1] = 1 # 水生木水相生
      aFiveLevel[2] = 4 # 金生水金休
      aFiveLevel[3] = 3 # 土剋水囚
      aFiveLevel[4] = 2 # 水剋火死
    end
    return aFiveLevel
  end


  # 四柱中神煞之煞
  def cp_mp_getDwBStarType(byWhatIdx=nil)
    return cp_mp_getPanInfo(Eightword::DwBStarType_4,"cp_mp_DwBStarType_4()",byWhatIdx)
  end
  def cp_mp_setDwBStarType(byWhatIdx,byValue)
    cp_mp_setPanInfo(Eightword::DwBStarType_4,byWhatIdx,byValue)
  end
  def cp_mp_DwBStarType_4()
    dwBStarType = cp_init8words_array(0,nil)
    (1..18).each do |nStar|
      sFunc = "cp_mp_DwBStarType_Kill_#{nStar}(dwBStarType)"
      dwBStarType = eval(sFunc)
    end
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      cp_mp_setDwBStarType(byWhatIdx,dwBStarType[byWhatIdx].uniq)
    end
  end
  # 羊刃
  def cp_mp_DwBStarType_Kill_1(dwBStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwBStarType_Kill_1(dwBStarType,nDaySkyIndex,hEarthIndex)
  end
  def cp_DwBStarType_Kill_1(dwBStarType,nDaySkyIndex,hEarthIndex)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (m == @@BySheep[nDaySkyIndex]) then
        dwBStarType[byWhatIdx].push(Eightword::KILL_1)
      end
    end
    return dwBStarType
  end
  # 飛刃
  def cp_mp_DwBStarType_Kill_2(dwBStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwBStarType_Kill_2(dwBStarType,nDaySkyIndex,hEarthIndex)
  end
  def cp_DwBStarType_Kill_2(dwBStarType,nDaySkyIndex,hEarthIndex)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (m == @@ByFly[nDaySkyIndex]) then
        dwBStarType[byWhatIdx].push(Eightword::KILL_2)
      end
    end
    return dwBStarType
  end
  # 劫煞 日柱地支 年柱地支
  def cp_mp_DwBStarType_Kill_3(dwBStarType)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    nYearEarthIndex = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    return cp_DwBStarType_Kill_3(dwBStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
  end
  def cp_DwBStarType_Kill_3(dwBStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
    # 日柱地支
    if (@par_PanGodBase == Eightword::PAN_GOD_DATE) then
      n = nDayEarthIndex % 4
      [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |byWhatIdx|
        m = hEarthIndex[byWhatIdx]
        if (m == @@ByGieKill[n]) then
          dwBStarType[byWhatIdx].push(Eightword::KILL_3)
        end
      end
    end
    # 年柱地支
    if (@par_PanGodBase == Eightword::PAN_GOD_YEAR) then
      k = nYearEarthIndex
      n = k % 4
      (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |byWhatIdx|
        m = hEarthIndex[byWhatIdx]
        if (m == @@ByGieKill[n]) then
          dwBStarType[byWhatIdx].push(Eightword::KILL_3)
        end
      end
    end
    return dwBStarType
  end
  # 亡神 日柱地支 年柱地支
  def cp_mp_DwBStarType_Kill_4(dwBStarType)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    nYearEarthIndex = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    return cp_DwBStarType_Kill_4(dwBStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
  end
  def cp_DwBStarType_Kill_4(dwBStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
    # 日柱地支
    if (@par_PanGodBase == Eightword::PAN_GOD_DATE) then
      n = nDayEarthIndex % 4
      [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |byWhatIdx|
        m = hEarthIndex[byWhatIdx]
        if (m == @@ByDieGod[n]) then
          dwBStarType[byWhatIdx].push(Eightword::KILL_4)
        end
      end
    end
    # 年柱地支
    if (@par_PanGodBase == Eightword::PAN_GOD_YEAR) then
      k = nYearEarthIndex
      n = k % 4
      (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |byWhatIdx|
        m = hEarthIndex[byWhatIdx]
        if (m == @@ByDieGod[n]) then
          dwBStarType[byWhatIdx].push(Eightword::KILL_4)
        end
      end
    end
    return dwBStarType
  end
  # 咸池 年柱地支 月柱地支
  def cp_mp_DwBStarType_Kill_5(dwBStarType)
    nYearEarthIndex = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    return cp_DwBStarType_Kill_5(dwBStarType,nYearEarthIndex,hEarthIndex,nMonthEarthIndex)
  end
  def cp_DwBStarType_Kill_5(dwBStarType,nYearEarthIndex,hEarthIndex,nMonthEarthIndex)
    # 年柱地支
    if (@par_CenPoolType == Eightword::CEN_POOL_YEAR) then
      n = nYearEarthIndex % 4
      (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |byWhatIdx|
        m = hEarthIndex[byWhatIdx]
        if (m == @@ByFlower[n]) then
          dwBStarType[byWhatIdx].push(Eightword::KILL_5)
        end
      end
    end
    # 月柱地支
    if (@par_CenPoolType == Eightword::CEN_POOL_MONTH) then
      n = nMonthEarthIndex % 4
      [Eightword::ByYearIdx,Eightword::ByDayIdx,Eightword::ByHourIdx].each do |byWhatIdx|
        m = hEarthIndex[byWhatIdx]
        if (m == @@ByFlower[n]) then
          dwBStarType[byWhatIdx].push(Eightword::KILL_5)
        end
      end
    end

    return dwBStarType
  end
  # 紅艷
  def cp_mp_DwBStarType_Kill_6(dwBStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwBStarType_Kill_6(dwBStarType,nDaySkyIndex,hEarthIndex)
  end
  def cp_DwBStarType_Kill_6(dwBStarType,nDaySkyIndex,hEarthIndex)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (m == @@ByRed[nDaySkyIndex]) then
        dwBStarType[byWhatIdx].push(Eightword::KILL_6)
      end
    end
    return dwBStarType
  end
  # 外桃 日柱地支
  def cp_mp_DwBStarType_Kill_7(dwBStarType)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    nHourEarthIndex = cp_mp_getByEarthIndex(Eightword::ByHourIdx)
    return cp_DwBStarType_Kill_7(dwBStarType,nDayEarthIndex,nHourEarthIndex)
  end
  def cp_DwBStarType_Kill_7(dwBStarType,nDayEarthIndex,nHourEarthIndex)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      if (nDayEarthIndex == @@ByFlower[byWhatIdx]) then
        m = nHourEarthIndex
        (0...4).each do |j|
          if (m == @@ByFlower[j]) then
            dwBStarType[Eightword::ByHourIdx].push(Eightword::KILL_7)
          end
        end
      end
    end
    return dwBStarType
  end
  # 六甲空亡 日柱天干
  def cp_mp_DwBStarType_Kill_8(dwBStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwBStarType_Kill_8(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
  end
  def cp_DwBStarType_Kill_8(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
    m = nDayEarthIndex
    if (nDaySkyIndex > 0) then
      m = Earth.Modify(m - nDaySkyIndex)
    end
    m >>= 1
    n = @@BySkyDie[m]
    [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (m == n || m == n + 1) then
        dwBStarType[byWhatIdx].push(Eightword::KILL_8)
      end
    end
    return dwBStarType
  end
  # 孤虛 日柱天干
  def cp_mp_DwBStarType_Kill_9(dwBStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwBStarType_Kill_9(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
  end
  def cp_DwBStarType_Kill_9(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
    m = nDayEarthIndex
    if (nDaySkyIndex > 0) then
      m = Earth.Modify(m - nDaySkyIndex)
    end
    m >>= 1
    n = @@BySkyDie[m]
    if (n <= 4) then
      k = n + 6
    else
      k = n - 6
    end
    [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (m == k || m == k + 1) then
        dwBStarType[byWhatIdx].push(Eightword::KILL_9)
      end
    end
    return dwBStarType
  end
  # 四大空亡 日柱天干
  def cp_mp_DwBStarType_Kill_10(dwBStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwBStarType_Kill_10(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
  end
  def cp_DwBStarType_Kill_10(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
    m = nDayEarthIndex
    if (nDaySkyIndex > 0) then
      m = Earth.Modify(m - nDaySkyIndex)
    end
    m >>= 1
    l = 0
    if (m == 0 || m == 3) then
      l = 2
    elsif (m == 1 || m == 4) then
      l = 8
    end
    [Eightword::ByYearIdx,Eightword::ByMonthIdx,Eightword::ByHourIdx].each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (l > 0 && (m == l || m == l + 1)) then
        dwBStarType[byWhatIdx].push(Eightword::KILL_10)
      end
    end
    return dwBStarType
  end
  # 孤辰 年柱地支
  def cp_mp_DwBStarType_Kill_11(dwBStarType)
    nYearEarthIndex = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwBStarType_Kill_11(dwBStarType,nYearEarthIndex,hEarthIndex)
  end
  def cp_DwBStarType_Kill_11(dwBStarType,nYearEarthIndex,hEarthIndex)
    l = Earth.Modify(nYearEarthIndex + 1)
    l /= 3
    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (@@ByGuChen[l] == m) then
        dwBStarType[byWhatIdx].push(Eightword::KILL_11)
      end
    end
    return dwBStarType
  end
  # 寡宿 年柱地支
  def cp_mp_DwBStarType_Kill_12(dwBStarType)
    nYearEarthIndex = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwBStarType_Kill_12(dwBStarType,nYearEarthIndex,hEarthIndex)
  end
  def cp_DwBStarType_Kill_12(dwBStarType,nYearEarthIndex,hEarthIndex)
    l = Earth.Modify(nYearEarthIndex + 1)
    l /= 3
    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hEarthIndex[byWhatIdx]
      if (@@ByGuaSu[l] == m) then
        dwBStarType[byWhatIdx].push(Eightword::KILL_12)
      end
    end
    return dwBStarType
  end
  # 天轉
  def cp_mp_DwBStarType_Kill_13(dwBStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    return cp_DwBStarType_Kill_13(dwBStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex)
  end
  def cp_DwBStarType_Kill_13(dwBStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex)
    nMonth = Earth.ModifyEarth(nMonthEarthIndex - 1)

    if (nMonth <= 3) then # 春
      if (nDayEarthIndex == 3) then
        if (nDaySkyIndex == 1) then
          dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_13)
        end
      end
    elsif (nMonth <= 6) then  # 夏
      if (nDayEarthIndex == 6) then
        if (nDaySkyIndex == 2) then
          dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_13)
        end
      end
    elsif (nMonth <= 9) then # 秋
      if (nDayEarthIndex == 9) then
        if (nDaySkyIndex == 7) then
          dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_13)
        end
      end
    elsif (nMonth <= 12) then # 冬
      if (nDayEarthIndex == 0) then
        if (nDaySkyIndex == 8) then
          dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_13)
        end
      end
    end
    return dwBStarType
  end
  # 地轉 月柱地支
  def cp_mp_DwBStarType_Kill_14(dwBStarType)
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    return cp_DwBStarType_Kill_14(dwBStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex)
  end
  def cp_DwBStarType_Kill_14(dwBStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex)
    nMonth = Earth.ModifyEarth(nMonthEarthIndex - 1)

    if (nMonth <= 3) then # 春
      if (nDayEarthIndex == 3) then
        if (nDaySkyIndex == 7) then
          dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_14)
        end
      end
    elsif (nMonth <= 6) then # 夏
      if (nDayEarthIndex == 6) then
        if (nDaySkyIndex == 4) then
          dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_14)
        end
      end
    elsif (nMonth <= 9) then # 秋
      if (nDayEarthIndex == 9)
        if (nDaySkyIndex == 9) then
          dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_14)
        end
      end
    elsif (nMonth <= 12) then # 冬
      if (nDayEarthIndex == 0) then
        if (nDaySkyIndex == 2) then
          dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_14)
        end
      end
    end
    return dwBStarType
  end
  # 天羅
  def cp_mp_DwBStarType_Kill_15(dwBStarType)
    hNaIn = cp_mp_getNaIn()
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwBStarType_Kill_15(dwBStarType,hNaIn,hEarthIndex)
  end
  def cp_DwBStarType_Kill_15(dwBStarType,hNaIn,hEarthIndex)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hNaIn[byWhatIdx]
      m %= 15
      m = @@ByFive[m]
      n = hEarthIndex[byWhatIdx]
      if (m == 2 && (n == 10 || n == 11)) then
        dwBStarType[byWhatIdx].push(Eightword::KILL_15)
      end
    end
    return dwBStarType
  end
  # 地網
  def cp_mp_DwBStarType_Kill_16(dwBStarType)
    hNaIn = cp_mp_getNaIn()
    hEarthIndex = cp_mp_getByEarthIndex()
    return cp_DwBStarType_Kill_16(dwBStarType,hNaIn,hEarthIndex)
  end
  def cp_DwBStarType_Kill_16(dwBStarType,hNaIn,hEarthIndex)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = hNaIn[byWhatIdx]
      m %= 15
      m = @@ByFive[m]
      n = hEarthIndex[byWhatIdx]
      if ((m == 0 || m == 3) && (n == 4 || n == 5)) then
        dwBStarType[byWhatIdx].push(Eightword::KILL_16)
      end
    end
    return dwBStarType
  end
  # 十惡大敗
  def cp_mp_DwBStarType_Kill_17(dwBStarType)
    h8Words = cp_mp_get8Words()
    return cp_DwBStarType_Kill_17(dwBStarType,h8Words)
  end
  def cp_DwBStarType_Kill_17(dwBStarType,h8Words)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      n8Words = h8Words[byWhatIdx]
      if (n8Words == 40 || n8Words == 41 || n8Words == 8 ||
        n8Words == 32 || n8Words == 23 || n8Words == 16 ||
        n8Words == 34 || n8Words == 59 || n8Words == 17 ||
        n8Words == 25) then
        dwBStarType[byWhatIdx].push(Eightword::KILL_17)
      end
    end
    return dwBStarType
  end
  # 四廢
  def cp_mp_DwBStarType_Kill_18(dwBStarType)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    h8Words = cp_mp_get8Words()
    return cp_DwBStarType_Kill_18(dwBStarType,nMonthEarthIndex,h8Words)
  end
  def cp_DwBStarType_Kill_18(dwBStarType,nMonthEarthIndex,h8Words)
    nMonth = Earth.ModifyEarth(nMonthEarthIndex - 1)
    byDay = h8Words[Eightword::ByDayIdx]

    if (nMonth <= 3) then # 春
      if (byDay == 56 || byDay == 57) then
        dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_18)
      end
    elsif (nMonth <= 6) then # 夏
      if (byDay == 48 || byDay == 59) then
        dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_18)
      end
    elsif (nMonth <= 9) then # 秋
      if (byDay == 50 || byDay == 51) then
        dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_18)
      end
    elsif (nMonth <= 12) then # 冬
      if (byDay == 42 || byDay == 53) then
        dwBStarType[Eightword::ByDayIdx].push(Eightword::KILL_18)
      end
    end
    return dwBStarType
  end


  # 納音
  def cp_NaIn(n8Words)
    return n8Words >> 1
  end

  def cp_mp_getNaIn(byWhatIdx=nil)
    return cp_mp_getPanInfo(Eightword::ByNaIn_4,"cp_mp_ByNaIn_4()",byWhatIdx)
  end
  def cp_mp_setNaIn(byValue,byWhatIdx=nil)
    cp_mp_setPanInfo(Eightword::ByNaIn_4,byWhatIdx,byValue)
  end
  def cp_mp_ByNaIn_4()
    h8Words = cp_mp_get8Words()
    hNaIn = cp_ByNaIn_4(h8Words)
    cp_mp_setNaIn(hNaIn)
  end
  def cp_ByNaIn_4(h8Words)
    hNaIn = initGanzhi_8words(nil)
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      n8Words = h8Words[byWhatIdx]
      hNaIn[byWhatIdx] = cp_NaIn(n8Words)
    end
    return hNaIn
  end

   # 提供給八字排盤之文字資料

  # 四柱的主星
  def cp_mp_4c_getByMainStars_Name()
    hMainStars = Hash.new
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      # byWhat = byWhatIdx
      hMainStars[byWhatIdx] = cp_mp_4c_getByMainStar_Name(byWhatIdx)
    end
    return hMainStars
  end
  def cp_mp_4c_getByMainStar_Name(byWhatIdx)
    if (byWhatIdx == Eightword::ByDayIdx) then
      sOut = Pm.GetStr("IDS_E_MS_SELF")
    else
      nMainStar = cp_mp_getByMainStar(byWhatIdx)
      sOut = cp_mp_4c_getMainStarName(nMainStar)
    end
    return sOut
  end
  def cp_mp_4c_getMainStarName(nMainStar)
    sOut = pan_par_getMainStarStr(nMainStar)
    return sOut
  end

  # 主星中的旺相休囚死
  def cp_mp_4c_get4CFiveLevels_Name()
    hOut = Hash.new
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      # byWhat = byWhatIdx
      hOut[byWhatIdx] = g_mp_get4CFiveLevel_Name(byWhatIdx)
    end
    return hOut
  end
  def cp_mp_4c_get4CFiveLevel_Name(byWhatIdx)
    sOut = ""
    nSkyFive = cp_mp_getBySkyFive(byWhatIdx)
    (0..4).each do |five|
      n4ColumnFive = cp_mp_getByFiveLevel(five)
      if (nSkyFive == n4ColumnFive) then
        sOut = Pm.GetStr("IDS_E_FIVE_LEVEL_#{five}")
      end
    end
    return sOut
  end

  # 天干
  def cp_mp_4c_getSky_Name(byWhatIdx)
    nSkyIndex = cp_mp_getBySkyIndex(byWhatIdx)
    return Sky.GetName(Sky.SkyIndex2Sky(nSkyIndex))
  end
  def cp_mp_4c_getSkyFive_Name(byWhatIdx)
    if (@dis_Five == Eightword::DIS_FIVE_SHOW) then
      nFive = cp_mp_getBySkyFive(byWhatIdx)
      return Eightword.getFiveName(nFive)
    else
      return ""
    end
  end
  # 地支
  def cp_mp_4c_getEarth_Name(byWhatIdx)
    nEarthIndex = cp_mp_getByEarthIndex(byWhatIdx)
    return Earth.GetName(Earth.EarthIndex2Earth(nEarthIndex))
  end
  def cp_mp_4c_getEarthFive_Name(byWhatIdx)
    if (@dis_Five == Eightword::DIS_FIVE_SHOW) then
      nFive = cp_mp_getByEarthFive(byWhatIdx)
      return Eightword.getFiveName(nFive)
    else
      return ""
    end
  end

  # 藏干
  def cp_mp_4c_getChungSky_Name(byWhatIdx,arrayIndex)
    nSky = cp_mp_getByChungSky(byWhatIdx,arrayIndex)
    if (nSky > 0) then
      return Sky.GetName(nSky)
    else
      return ""
    end
  end
  def cp_mp_4c_getChungSkyFive_Name(byWhatIdx,arrayIndex)
    nFive = cp_mp_getByChungFive(byWhatIdx,arrayIndex)
    if (nFive == 0xFF) then
      return ""
    else
      return Eightword.getFiveName(nFive)
    end
  end

  # 副星
  def cp_mp_4c_getBySubStar_Name(byWhatIdx,arrayIndex)
    nStar = cp_mp_getBySubStar(byWhatIdx,arrayIndex)
    if (nStar == 0xFF) then
      sOut = ""
    else
      sOut = pan_par_getSubStarStr(nStar)
    end
    return sOut
  end

  # 12運
  def cp_mp_4c_getBy12Win_Name(byWhatIdx)
    n12Win = cp_mp_getBy12Win(byWhatIdx)
    return Pm.GetStr("IDS_S_GOD_#{n12Win + 1}")
  end

  # 神煞
  def cp_mp_4c_getGodKill_NameArray(byWhatIdx)
    aStartTypeName = Array.new
    sStarType = cp_mp_4c_getDwStarType_NameArray(byWhatIdx)
    sBStarType = cp_mp_4c_getDwBStarType_NameArray(byWhatIdx)
    if (!sStarType.empty?) then
      aStartTypeName = aStartTypeName + sStarType
    end
    if (!sBStarType.empty?) then
      aStartTypeName = aStartTypeName + sBStarType
    end
    return aStartTypeName
  end
  def cp_mp_4c_getDwStarType_NameArray(byWhatIdx)
    dwStarType = cp_mp_getDwStarType(byWhatIdx)
    aStartTypeName = Array.new
    dwStarType.each do |nStarType|
      if (par_ShowGod?(nStarType)) then
        aStartTypeName.push(Eightword.getGodStr(nStarType))
      end
    end
    return aStartTypeName
  end
  def cp_mp_4c_getDwBStarType_NameArray(byWhatIdx)
    dwBStarType = cp_mp_getDwBStarType(byWhatIdx)
    aStartTypeName = Array.new
    dwBStarType.each do |nStarType|
      if (par_ShowKill?(nStarType)) then
        aStartTypeName.push(Eightword.getKillStr(nStarType))
      end
    end
    return aStartTypeName
  end
  def cp_mp_4c_getGodKill_Name(byWhatIdx)
    aStartTypeName = Array.new
    sStarType = cp_mp_4c_getDwStarType_Name(byWhatIdx)
    sBStarType = cp_mp_4c_getDwBStarType_Name(byWhatIdx)
    if (!sStarType.empty?) then
      aStartTypeName.push(sStarType)
    end
    if (!sBStarType.empty?) then
      aStartTypeName.push(sBStarType)
    end
    return aStartTypeName.join(Pm.GetStr("IDS_E_CHI_DOT"))
  end
  def cp_mp_4c_getDwStarType_Name(byWhatIdx)
    dwStarType = cp_mp_getDwStarType(byWhatIdx)
    aStartTypeName = Array.new
    dwStarType.each do |nStarType|
      if (par_ShowGod?(nStarType)) then
        aStartTypeName.push(Eightword.getGodStr(nStarType))
      end
    end
    return aStartTypeName.join(Pm.GetStr("IDS_E_CHI_DOT"))
  end
  def cp_mp_4c_getDwBStarType_Name(byWhatIdx)
    dwBStarType = cp_mp_getDwBStarType(byWhatIdx)
    aStartTypeName = Array.new
    dwBStarType.each do |nStarType|
      if (par_ShowKill?(nStarType)) then
        aStartTypeName.push(Eightword.getKillStr(nStarType))
      end
    end
    return aStartTypeName.join(Pm.GetStr("IDS_E_CHI_DOT"))
  end

  # 納音
  def cp_mp_4c_getNaIn_Name(byWhatIdx)
    nNaIn = cp_mp_getNaIn(byWhatIdx)
    return Pm.GetStr("IDS_E_NAING_#{nNaIn + 1}")
  end


  # 四柱的五行等級
  def cp_mp_getByDateFiveLevel(arrayIndex=nil)
    return cp_mp_getPanInfo(Eightword::ByDateFiveLevel_5,"cp_mp_ByDateFiveLevel_5()",nil,arrayIndex)
  end
  def cp_mp_setByDateFiveLevel(byValue,arrayIndex=nil)
    cp_mp_setPanInfo(Eightword::ByDateFiveLevel_5,nil,byValue,arrayIndex)
  end
  def cp_mp_ByDateFiveLevel_5()
    n = cp_mp_getBySkyFive(Eightword::ByDayIdx)
    if (n == 0) then # 水
      cp_mp_setByDateFiveLevel(0,0) # 水
      cp_mp_setByDateFiveLevel(1,1) # 水生木水相生
      cp_mp_setByDateFiveLevel(4,2) # 金生水金休
      cp_mp_setByDateFiveLevel(3,3) # 土剋水囚
      cp_mp_setByDateFiveLevel(2,4) # 水剋火死
    elsif (n == 1) then # 木
      cp_mp_setByDateFiveLevel(1,0) # 木
      cp_mp_setByDateFiveLevel(2,1) # 木生火木相生
      cp_mp_setByDateFiveLevel(0,2) # 水生木水休
      cp_mp_setByDateFiveLevel(4,3) # 金剋木囚
      cp_mp_setByDateFiveLevel(3,4) # 木剋土死
    elsif (n == 2) then # 火
      cp_mp_setByDateFiveLevel(2,0) # 火
      cp_mp_setByDateFiveLevel(3,1) # 火生土火相生
      cp_mp_setByDateFiveLevel(1,2) # 木生火木休
      cp_mp_setByDateFiveLevel(0,3) # 水剋火囚
      cp_mp_setByDateFiveLevel(4,4) # 火剋金死
    elsif (n == 3) then # 土
      cp_mp_setByDateFiveLevel(3,0) # 土
      cp_mp_setByDateFiveLevel(4,1) # 土生金土相生
      cp_mp_setByDateFiveLevel(2,2) # 火生土火休
      cp_mp_setByDateFiveLevel(1,3) # 木剋土囚
      cp_mp_setByDateFiveLevel(0,4) # 土剋水土
    elsif (n == 4) then # 金
      cp_mp_setByDateFiveLevel(4,0) # 金
      cp_mp_setByDateFiveLevel(0,1) # 金生水金相生
      cp_mp_setByDateFiveLevel(3,2) # 土生金土休
      cp_mp_setByDateFiveLevel(2,3) # 火剋金囚
      cp_mp_setByDateFiveLevel(1,4) # 金剋木死
    end
  end


  # Main Pan User Info
  # return 0(小寒) ~ 23（冬至）
   # 各戶資訊
  def cp_mp_getUserInfo(key,sFunc,arrayIndex=nil)
    if (arrayIndex == nil) then
      if (@EightwordInfo[Eightword::MAIN_PAN][Eightword::USER_INFO][key] == nil) then
        eval(sFunc)
      end
      return @EightwordInfo[Eightword::MAIN_PAN][Eightword::USER_INFO][key]
    else
      if (@EightwordInfo[Eightword::MAIN_PAN][Eightword::USER_INFO][key][arrayIndex] == nil) then
        eval(sFunc)
      end
      return @EightwordInfo[Eightword::MAIN_PAN][Eightword::USER_INFO][key][arrayIndex]
    end
  end
  def cp_mp_setUserInfo(key,byValue,arrayIndex=nil)
    byValue = cloneValue(byValue)
    if (arrayIndex == nil) then
      @EightwordInfo[Eightword::MAIN_PAN][Eightword::USER_INFO][key] = byValue
    else
      @EightwordInfo[Eightword::MAIN_PAN][Eightword::USER_INFO][key][arrayIndex] = byValue
    end
  end
  def cp_mp_getBySegmentOffsetTime()
    return cp_mp_getBySegment(),cp_mp_getBySegOffset(),cp_mp_getBySegTime()
  end
  def cp_mp_getBySegment()
    return cp_mp_getUserInfo(Eightword::BySegment,"cp_mp_BySegment()")
  end
  def cp_mp_setBySegment(byValue)
    cp_mp_setUserInfo(Eightword::BySegment,byValue)
  end
  def cp_mp_getBySegOffset()
    return cp_mp_getUserInfo(Eightword::BySegOffset,"cp_mp_BySegment()")
  end
  def cp_mp_setBySegOffset(byValue)
    cp_mp_setUserInfo(Eightword::BySegOffset,byValue)
  end
  def cp_mp_getBySegTime()
    return cp_mp_getUserInfo(Eightword::BySegTime,"cp_mp_BySegment()")
  end
  def cp_mp_setBySegTime(byValue)
    cp_mp_setUserInfo(Eightword::BySegTime,byValue)
  end
  def cp_mp_BySegment()
    nWYear,nWMonth,nWDate,nHour,nMinute = uig_W_YMDHm()
      nSegment,nSegmentDays,nSegmentTime = Xdate.GetSegmentFromWest_Eightwords(nWYear,nWMonth,nWDate,nHour,nMinute)
      cp_mp_setBySegment(nSegment)
      cp_mp_setBySegOffset(nSegmentDays)
      cp_mp_setBySegTime(nSegmentTime)
  end

  def cp_mp_getByWeekend()
    return cp_mp_getUserInfo(Eightword::ByWeekend,"cp_mp_ByWeekend()")
  end
  def cp_mp_setByWeekend(byValue)
    cp_mp_setUserInfo(Eightword::ByWeekend,byValue)
  end
  def cp_mp_ByWeekend()
    nWYear,nWMonth,nWDate,nHour,nMinute = uig_W_YMDHm()

    snWeekend = Xdate.GetWWeekDay(nWYear, nWMonth, nWDate)
      cp_mp_setByWeekend(snWeekend)
  end


    def cp_mp_getByLargeYear_2(arrayIndex=nil)
    return cp_mp_getUserInfo(Eightword::ByLargeYear_2,"cp_mp_UserInfos()",arrayIndex)
    end
  def cp_mp_setByLargeYear_2(byValue,arrayIndex=nil)
    cp_mp_setUserInfo(Eightword::ByLargeYear_2,byValue,arrayIndex)
  end
    # 交大運
    def cp_mp_getByMatchYear_2(arrayIndex)
    return cp_mp_getUserInfo(Eightword::ByMatchYear_2,"cp_mp_UserInfos()",arrayIndex)
    end
  def cp_mp_setByMatchYear_2(byValue,arrayIndex=nil)
    cp_mp_setUserInfo(Eightword::ByMatchYear_2,byValue,arrayIndex)
  end
    def cp_mp_getBySegment()
    return cp_mp_getUserInfo(Eightword::BySegment,"cp_mp_UserInfos()")
    end
  def cp_mp_setBySegment(byValue)
    cp_mp_setUserInfo(Eightword::BySegment,byValue)
  end
    def cp_mp_getByMatchDay()
    return cp_mp_getUserInfo(Eightword::ByMatchDay,"cp_mp_UserInfos()")
    end
  def cp_mp_setByMatchDay(byValue)
    cp_mp_setUserInfo(Eightword::ByMatchDay,byValue)
  end

    def cp_mp_getByAfterYear()
    return cp_mp_getUserInfo(Eightword::ByAfterYear,"cp_mp_UserInfos()")
    end
  def cp_mp_setByAfterYear(byValue)
    cp_mp_setUserInfo(Eightword::ByAfterYear,byValue)
  end
    def cp_mp_getByAfterMonth()
    return cp_mp_getUserInfo(Eightword::ByAfterMonth,"cp_mp_UserInfos()")
    end
  def cp_mp_setByAfterMonth(byValue)
    cp_mp_setUserInfo(Eightword::ByAfterMonth,byValue)
  end
    def cp_mp_getByAfterDate()
    return cp_mp_getUserInfo(Eightword::ByAfterDate,"cp_mp_UserInfos()")
    end
  def cp_mp_setByAfterDate(byValue)
    cp_mp_setUserInfo(Eightword::ByAfterDate,byValue)
  end
    def cp_mp_UserInfos()
      bNextSeg = cp_CheckDirectionClock()
      nWYear,nWMonth,nWDate,nHour,nMinute = uig_W_YMDHm()
      hSegDiff = Xdate.GetNextSegmentDays(nWYear,nWMonth,nWDate,nHour,nMinute,bNextSeg)
    m = hSegDiff[Xdate::Sd_Days]
    m = m * 24 + hSegDiff[Xdate::Sd_Hours]
    nHours = 2 * hSegDiff[Xdate::Sd_Mins]
    nSum = (1.01456165 * (5.0 * m + nHours / 24.0)).floor
    nYears = (nSum / 360).floor
    m = nSum - (nYears * 360 + 0.5).floor
    nMonthes = m / 30
    nDays = m % 30

    cp_mp_setByAfterYear(nYears)
    cp_mp_setByAfterMonth(nMonthes)
    cp_mp_setByAfterDate(nDays)

    nWYear,nWMonth,nWDate = uig_W_YMD()
    nNewWYear,nNewWMonth,nNewWDate = Xdate.GetWestDate(nWYear,nWMonth,nWDate,nSum)

      hSegDiff1 = Xdate.GetNextSegmentDays(nNewWYear,nNewWMonth,nNewWDate,0,0,false,false)
    cp_mp_setBySegment(hSegDiff1[Xdate::Sd_Segment])
    cp_mp_setByMatchDay(hSegDiff1[Xdate::Sd_Days])

    n8Words = cp_mp_get8Words(Eightword::ByYearIdx)
    nSkyIndex = cp_mp_getBySkyIndex(Eightword::ByYearIdx)
    nEarthIndex = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    m = Sky.Modify(nSkyIndex + nYears)
    cp_mp_setByMatchYear_2(m,0)
    if (m >= 5) then
      m -= 5
    else
      m += 5
    end
    cp_mp_setByMatchYear_2(m,1)
    if (nMonthes >= 6) then
      nYears += 1
    end
    cp_mp_setByLargeYear_2(nYears + 1,0)
    cp_mp_setByLargeYear_2(nYears + 6,1)
    end


    # 命宮
  def cp_mp_LifeHouse()
    nSegmentIndex = cp_mp_get8Words(Eightword::Segment)
  # Pm.saveTestDb("LifeHouse",nSegmentIndex)
    nHourEarthIndex = cp_mp_getByEarthIndex(Eightword::ByHourIdx)
  # Pm.saveTestDb("nHourEarthIndex",nHourEarthIndex)
    nLifeHouseEarthIndex = Eightword.LifeHouse_EarthIndex(nSegmentIndex,nHourEarthIndex)
  # Pm.saveTestDb("nLifeHouseEarthIndex",nLifeHouseEarthIndex)
    # nYearSkyIndex = cp_getSkyIndex()
    nYearSkyIndex = cp_mp_getBySkyIndex(Eightword::ByYearIdx)
    nSkyIndex = Xdate.FiveTigerOut(nYearSkyIndex,nLifeHouseEarthIndex)
  # Pm.saveTestDb("nSkyIndex",nSkyIndex)
    h = Hash.new
    h[Sky::SKY] = nSkyIndex
    h[Earth::EARTH] = nLifeHouseEarthIndex

    cp_mp_setLifeHouse(h)
  end
  def cp_mp_getLifeHouse()
    return cp_mp_getUserInfo(Eightword::LifeHouse,"cp_mp_LifeHouse()")
  end
  def cp_mp_setLifeHouse(byValue)
    return cp_mp_setUserInfo(Eightword::LifeHouse,byValue)
  end
    # 命格 定格局訣
  def cp_mp_LifeStyle()
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    nLifeStyle = Eightword.LifeStyleRule(nDaySkyIndex,nMonthEarthIndex)
    cp_mp_setLifeStyle(nLifeStyle)
  end
  def cp_mp_getLifeStyle()
    return cp_mp_getUserInfo(Eightword::LifeStyle,"cp_mp_LifeStyle()")
  end
  def cp_mp_setLifeStyle(byValue)
    return cp_mp_setUserInfo(Eightword::LifeStyle,byValue)
  end
    # 胎元
  def cp_mp_TaiUan()
    if (@m_nPanType == Cfate::PAN_NORMAL) then
      nMonthSkyIndex = cp_mp_getBySkyIndex(Eightword::ByMonthIdx)
      nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    else
      nMonthSkyIndex = cp_sfi_getBySkyIndex(Eightword::ByMonthIdx)
      nMonthEarthIndex = cp_sfi_getByEarthIndex(Eightword::ByMonthIdx)
    end
    h = Hash.new
    h[Sky::SKY] = Eightword.TaiUanSkyIndex(nMonthSkyIndex)
    h[Earth::EARTH] = Eightword.TaiUanEarthIndex(nMonthEarthIndex)
# nil[1]
    cp_mp_setTaiUan(h)
  end
  def cp_mp_getTaiUan()
    return cp_mp_getUserInfo(Eightword::TaiUan,"cp_mp_TaiUan()")
  end
  def cp_mp_setTaiUan(byValue)
    return cp_mp_setUserInfo(Eightword::TaiUan,byValue)
  end
  # 胎息
    def cp_mp_TaiHsi()
    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    nDayEarthIndex = cp_mp_getByEarthIndex(Eightword::ByDayIdx)
    h = Hash.new
    h[Sky::SKY] = Eightword.TaiHsiSkyIndex(nDaySkyIndex)
    h[Earth::EARTH] = Eightword.TaiHsiEarthIndex(nDayEarthIndex)

    cp_mp_setTaiHsi(h)
    end
  def cp_mp_getTaiHsi()
    return cp_mp_getUserInfo(Eightword::TaiHsi,"cp_mp_TaiHsi()")
  end
  def cp_mp_setTaiHsi(byValue)
    return cp_mp_setUserInfo(Eightword::TaiHsi,byValue)
  end

    # 年空
  def cp_mp_YearEmpty()
    aEmpty = cp_mp_EmptyEarthIndex(Eightword::ByYearIdx)
    cp_mp_setYearEmpty(aEmpty)
  end
  def cp_mp_getYearEmpty()
    return cp_mp_getUserInfo(Eightword::YearEmpty,"cp_mp_YearEmpty()")
  end
  def cp_mp_setYearEmpty(byValue)
    return cp_mp_setUserInfo(Eightword::YearEmpty,byValue)
  end
    # 日空
  def cp_mp_DayEmpty()
    aEmpty = cp_mp_EmptyEarthIndex(Eightword::ByDayIdx)
    cp_mp_setDayEmpty(aEmpty)
  end
  def cp_mp_getDayEmpty()
    return cp_mp_getUserInfo(Eightword::DayEmpty,"cp_mp_DayEmpty()")
  end
  def cp_mp_setDayEmpty(byValue)
    return cp_mp_setUserInfo(Eightword::DayEmpty,byValue)
  end
  # 旬中，旬空
    def cp_mp_EmptyEarthIndex(byWhatIdx)
    nSkyIndex = cp_mp_getBySkyIndex(byWhatIdx)
    nEarthIndex = cp_mp_getByEarthIndex(byWhatIdx)

    aEmpty = Eightword.EmptyEarthIndex(nSkyIndex,nEarthIndex)

      return aEmpty
    end


  # 人元用事
  def cp_mp_UsePeople()
    nMonthEarthIndex = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    # nMonthEarthIndex = Earth.Modify(uig_EM() + 1)
    nDay = uig_ED()
    nSkyIndex = Eightword.UsePeople(nMonthEarthIndex,nDay)
    cp_mp_setUsePeople(nSkyIndex)
  end
  def cp_mp_getUsePeople()
    return cp_mp_getUserInfo(Eightword::UsePeople,"cp_mp_UsePeople()")
  end
  def cp_mp_setUsePeople(byValue)
    return cp_mp_setUserInfo(Eightword::UsePeople,byValue)
  end

  def cp_mp_getGodStyleInfo2(key,sFunc,byWhatIdx=nil,arrayIndex=nil)
    byValue = cp_mp_getGodStyleInfo(key,sFunc,byWhatIdx)
    if (byWhatIdx == nil) then
      return byValue
    else
      if (arrayIndex == nil) then
        return byValue
      else
        return byValue[arrayIndex]
      end
    end
  end
  def cp_mp_getGodStyleInfo(key,sFunc,arrayIndex=nil)
  # if (key == Eightword::ByUseGod_5) then
  #   Pm.saveTestDb("cp_mp_getGodStyleInfo","#{@EightwordInfo[Eightword::MAIN_PAN][Eightword::GOD_STYLE]}")
  # end
    if (arrayIndex == nil) then
      if ((@EightwordInfo[Eightword::MAIN_PAN][Eightword::GOD_STYLE][key] == nil)) then
        eval(sFunc)
      end
      return @EightwordInfo[Eightword::MAIN_PAN][Eightword::GOD_STYLE][key]
    else
      if ((@EightwordInfo[Eightword::MAIN_PAN][Eightword::GOD_STYLE][key] == nil) ||
        (@EightwordInfo[Eightword::MAIN_PAN][Eightword::GOD_STYLE][key][arrayIndex] == nil)) then
        eval(sFunc)
      end
      return @EightwordInfo[Eightword::MAIN_PAN][Eightword::GOD_STYLE][key][arrayIndex]
    end
  end
  def cp_mp_setGodStyleInfo(key,byValue,arrayIndex=nil)
  # if (key == Eightword::ByUseGod_5) then
  #   Pm.saveTestDb("cp_mp_setGodStyleInfo","#{@EightwordInfo[Eightword::MAIN_PAN][Eightword::GOD_STYLE]}")
  # end
    byValue = cloneValue(byValue)
    if (arrayIndex == nil) then
      @EightwordInfo[Eightword::MAIN_PAN][Eightword::GOD_STYLE][key] = byValue
    else
      @EightwordInfo[Eightword::MAIN_PAN][Eightword::GOD_STYLE][key][arrayIndex] = byValue
    end
  end

  # 日主旺度 A90 > 30B
  def cp_mp_NA()
      m = cp_mp_getBySkyFive(Eightword::ByDayIdx)
    n = Five.Modify(m - 1)

    nFiveSum = cp_mp_getFiveSum()
    nA = nFiveSum[m] + nFiveSum[n]

    cp_mp_setNA(nA)
  end
  def cp_mp_getNA()
    return cp_mp_getGodStyleInfo(Eightword::NA,"cp_mp_NA()")
  end
  def cp_mp_setNA(byValue)
      cp_mp_setGodStyleInfo(Eightword::NA,byValue)
  end

  def cp_mp_NB()
    nB = 0
      m = cp_mp_getBySkyFive(Eightword::ByDayIdx)
    n = Five.Modify(m - 1)
      n1 = Five.Modify(m + 1)

    nFiveSum = cp_mp_getFiveSum()

    a = Array(0..4)
    b = [m,n,n1]
    c = a - b
    c.each do |i|
      nB += nFiveSum[i]
    end
  # nil[1]
    cp_mp_setNB(nB)
  end
  def cp_mp_getNB()
    return cp_mp_getGodStyleInfo(Eightword::NB,"cp_mp_NB()")
  end
  def cp_mp_setNB(byValue)
      cp_mp_setGodStyleInfo(Eightword::NB,byValue)
  end

    def cp_mp_ByUseGod_5()
      byUseGod = Array.new(5,0)
      nUseGodStyle = cp_mp_getUseGodStyle()
    if (nUseGodStyle == 2) then
          bSpecial,byUseGod_0 = cp_mp_DecideSmallGod()
          n = byUseGod_0
      else
          n = cp_mp_getBySkyFive(Eightword::ByDayIdx)
      end
    if (n == 0) then
      byUseGod[0] = 0  # 用神 水
      byUseGod[1] = 4  # 喜神 金生水
      byUseGod[2] = 1  # 閒神 木
      byUseGod[3] = 3  # 忌神 土剋水
      byUseGod[4] = 2  # 仇神 火生土剋水
    elsif (n == 1) then
      byUseGod[0] = 1 # 用神 木
      byUseGod[1] = 0 # 喜神 水生木
      byUseGod[2] = 2 # 閒神 火
      byUseGod[3] = 4 # 忌神 金剋木
      byUseGod[4] = 3 # 仇神 土生金剋木
    elsif (n == 2) then
      byUseGod[0] = 2  # 用神 火
      byUseGod[1] = 1  # 喜神 木生火
      byUseGod[2] = 3  # 閒神 土
      byUseGod[3] = 0  # 忌神 水剋火
      byUseGod[4] = 4  # 仇神 金生水剋火
    elsif (n == 3) then
      byUseGod[0] = 3  # 用神 土
      byUseGod[1] = 2  # 喜神 火生土
      byUseGod[2] = 4  # 閒神 金
      byUseGod[3] = 1  # 忌神 木剋土
      byUseGod[4] = 0  # 仇神 水生木剋土
    elsif (n == 4) then
      byUseGod[0] = 4  # 用神 金
      byUseGod[1] = 3  # 喜神 土生金
      byUseGod[2] = 0  # 閒神 水
      byUseGod[3] = 2  # 忌神 火剋金
      byUseGod[4] = 1  # 仇神 木生火剋金
    end
    nA = cp_mp_getNA()
      nB = cp_mp_getNB()
      if (nUseGodStyle < 3 && nUseGodStyle != 1) then
          if (nUseGodStyle == 0) then
              if (nA < nB) then
              byUseGod[3],byUseGod[0] = byUseGod[0],byUseGod[3]
              byUseGod[1],byUseGod[4] = byUseGod[4],byUseGod[1]
              end
          end
    elsif (nA >= nB) then
      byUseGod[3],byUseGod[0] = byUseGod[0],byUseGod[3]
      byUseGod[1],byUseGod[4] = byUseGod[4],byUseGod[1]
    end

    cp_mp_setByUseGod(byUseGod)
    end
    def cp_mp_DecideSmallGod()
    byFiveList = Array.new(5,0)

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      n = cp_mp_getBySkyFive(byWhatIdx)
      byFiveList[n] = byFiveList[n] + 1
    end
      m = 0
      (0...5).each do |i|
          if (byFiveList[i] > 0) then
              m += 1
            end
      end
      if (m == 2) then
          j = k = -1
        (0...5).each do |i|
              if (byFiveList[i] > 0) then
                  if (j == -1) then
                      j = i
                  else
                      k = i
                end
              end
          end
          if (k == j + 2 || (k == 3 && j == 0) || (k == 4 && j == 1)) then
              bSpecial = true
              if (k == j + 2) then
                  byUseGod_0 = j + 1
              elsif (k == 3 && j == 0) then
                  byUseGod_0 = 4
              else
                  byUseGod_0 = 0
              end
          end
      end

      return bSpecial,byUseGod_0
    end
    def cp_mp_getByUseGod(arrayIndex=nil)
    return cp_mp_getGodStyleInfo(Eightword::ByUseGod_5,"cp_mp_ByUseGod_5()",arrayIndex)
    end
    def cp_mp_setByUseGod(byValue,arrayIndex=nil)
    return cp_mp_setGodStyleInfo(Eightword::ByUseGod_5,byValue,arrayIndex)
    end

     def cp_mp_StrongLife()
    nA = cp_mp_getNA()
      nB = cp_mp_getNB()
      nUseGodStyle = cp_mp_getUseGodStyle()
        bStrongLife = false
      if (nUseGodStyle < 3 && nUseGodStyle != 1) then
        # 怪怪的，多此一舉
          if (nUseGodStyle == 0) then
              if (nA < nB) then
                  bStrongLife = false
              end
          end
    elsif (nA >= nB) then
            bStrongLife = true
    else
            bStrongLife = false
    end
  # nil[1]
    cp_mp_setStrongLife(bStrongLife)
    end
  def cp_mp_getStrongLife()
    return cp_mp_getGodStyleInfo(Eightword::BStrongLife,"cp_mp_StrongLife()")
  end
  def cp_mp_setStrongLife(byValue)
    return cp_mp_setGodStyleInfo(Eightword::BStrongLife,byValue)
  end

  def cp_mp_NSpecialType()
    byEarthList = Array.new(12,0)
    bySkyFive = Array.new(5,0)
    byEarthFive = Array.new(5,0)
    nSpecialType = -1

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      n = cp_mp_getBySkyFive(byWhatIdx)
      bySkyFive[n] = bySkyFive[n] + 1
    end

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      n = cp_mp_getByEarthIndex(byWhatIdx)
      byEarthList[n] = byEarthList[n] + 1
      n = cp_mp_getByEarthFive(byWhatIdx)
      byEarthFive[n] = byEarthFive[n] + 1
    end

    nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    if (nDaySkyIndex <= 1 && bySkyFive[4] == 0 && byEarthFive[4] == 0) then
      if (byEarthList[3] >= 1) then
        if (byEarthList[2] >= 1 && byEarthList[4] >= 1) then
          bSpecial = true
        elsif (byEarthList[7] >= 1 && byEarthList[11] >= 1) then
          bSpecial = true
        end
              if (bSpecial) then
                  nSpecialType = 4
              end
      end
    elsif ((nDaySkyIndex == 2 || nDaySkyIndex == 3) && bySkyFive[0] == 0 && byEarthFive[0] == 0) then
      if (byEarthList[6] >= 1) then
        if (byEarthList[5] >= 1 && byEarthList[7] >= 1) then
          bSpecial = true
        elsif (byEarthList[2] >= 1 && byEarthList[10] >= 1) then
              bSpecial = true
          end
              if (bSpecial) then
                  nSpecialType = 0
              end
      end
    elsif ((nDaySkyIndex == 4 || nDaySkyIndex == 5) && byEarthFive[3] == 4) then
          if (byEarthList[1] == 1 && byEarthList[4] == 1 &&
        byEarthList[7] == 1 && byEarthList[10] == 1) then
        bSpecial = true
      end
          if (bSpecial) then
              nSpecialType = 3
          end
    elsif ((nDaySkyIndex == 6 || nDaySkyIndex == 7) && bySkyFive[2] == 0 && byEarthFive[2] == 0) then
      if (byEarthList[9] >= 1) then
        if (byEarthList[8] >= 1 && byEarthList[10] >= 1) then
          bSpecial = true
        elsif (byEarthList[1] >= 1 && byEarthList[5] >= 1) then
          bSpecial = true
        end
              if (bSpecial) then
                  nSpecialType = 2
              end
      end
    elsif (nDaySkyIndex >= 8 && bySkyFive[3] == 0 && byEarthFive[3] == 0) then
      if (byEarthList[0] >= 1) then
        if (byEarthList[1] >= 1 && byEarthList[11] >= 1) then
          bSpecial = true
        elsif (byEarthList[4] >= 1 && byEarthList[8] >= 1) then
          bSpecial = true
        end
              if (bSpecial) then
                  nSpecialType = 1
              end
      end
    end

    if (!bSpecial) then
          n = 0
          nMonthMainStar = cp_mp_getByMainStar(Eightword::ByMonthIdx)
          nA = cp_mp_getNA()
          nB = cp_mp_getNB()
          # 棄命從財格
          if (nMonthMainStar == 6 || nMonthMainStar == 7) then
              if (nA < nB) then
                bStop = false
          (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
            if (!bStop) then
                  j = cp_mp_getByMainStar(byWhatIdx)
                  nSkyFive = cp_mp_getBySkyFive(byWhatIdx)
                  nEarthFive = cp_mp_getByEarthFive(byWhatIdx)
                        if (j == 6 || j == 7) then
                            n += 1
                        elsif (nSkyFive == nEarthFive) then # ||lpPan1->bySkyFive[i] == lpPan1->byEarthFive[i])
                            n = 0
                            bStop = true
                        end
                    end
                  end
              end
              if (n >= 2) then
                  bSpecial = true
                  nSpecialType = 5
              end
          elsif (nMonthMainStar == 4 || nMonthMainStar == 5) then
          # 棄命從殺格
              if (nA < nB) then
                bStop = false
          (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
            if (!bStop) then
                  j = cp_mp_getByMainStar(byWhatIdx)
              j = 255 if j == nil
                        if (j >= 4 && j <= 7) then
                            n += 1
                        elsif (j < 4) then
                          n = 0
                            bStop = true
                        end
                    end
                  end
              end
              if (n >= 2) then
                  bSpecial = true
                  nSpecialType = 6
              end
          end
      end
# nil[1]
      cp_mp_setNSpecialType(nSpecialType)
  end
  def cp_mp_getNSpecialType()
    return cp_mp_getGodStyleInfo(Eightword::NSpecialType,"cp_mp_NSpecialType()")
  end
  def cp_mp_setNSpecialType(byValue)
    cp_mp_setGodStyleInfo(Eightword::NSpecialType,byValue)
  end
  def cp_mp_DecideSpecialType()
    return (cp_mp_getNSpecialType() >= 0)
  end

  def cp_mp_UseGodStyle()
    # 判斷特殊格局
    if (cp_mp_DecideSpecialType()) then
      nUseGodStyle = 0
    else
      if (cp_mp_DecideMidGod())
            nUseGodStyle = 1
          else
            bSpecial,byUseGod_0 = cp_mp_DecideSmallGod()
        if (bSpecial) then
          nUseGodStyle = 2
        else
          nUseGodStyle = 3
        end
      end
    end
    cp_mp_setUseGodStyle(nUseGodStyle)
    end
    def cp_mp_getUseGodStyle()
    return cp_mp_getGodStyleInfo(Eightword::NUseGodStyle,"cp_mp_UseGodStyle()")
    end
    def cp_mp_setUseGodStyle(byValue)
    cp_mp_setGodStyleInfo(Eightword::NUseGodStyle,byValue)
    end

  def cp_mp_DecideMidGod()
    bSpecial = false
    n = cp_mp_getNInYangValue()
      nValue = @par_MidParm
      nValue1 = 0 - nValue
      if (n >= nValue || n <= nValue1) then
          bSpecial = true
      end
      return (bSpecial)
  end

   def cp_mp_ByUseGodSky()
      byUseGodSky = 0
      m = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
      m = Earth.Modify(m - 2)
      nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
      if cp_mp_DecideMidGod() then
          byUseGodSky = @@ByMidUse[nDaySkyIndex][m]
      end
      cp_mp_setByUseGodSky(byUseGodSky)
    end
    def cp_mp_getByUseGodSky()
    return cp_mp_getGodStyleInfo(Eightword::ByUseGodSky,"cp_mp_ByUseGodSky()")
    end
    def cp_mp_setByUseGodSky(byValue)
    cp_mp_setGodStyleInfo(Eightword::ByUseGodSky,byValue)
    end

    def cp_mp_ByLikeGodSky_4()
      byLikeGodSky = Array.new(4,0)
      m = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
      m = Earth.Modify(m - 2)
      nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
      if cp_mp_DecideMidGod() then
        (0..3).each do |k|
              byLikeGodSky[k] = @@ByMidLike[nDaySkyIndex][m][k]
          end
      end
      cp_mp_setByLikeGodSky(byLikeGodSky)
    end
    def cp_mp_getByLikeGodSky()
    return cp_mp_getGodStyleInfo(Eightword::ByLikeGodSky_4,"cp_mp_ByLikeGodSky_4()")
    end
    def cp_mp_setByLikeGodSky(byValue)
    cp_mp_setGodStyleInfo(Eightword::ByLikeGodSky_4,byValue)
    end

  # 陰陽基本分
  def cp_mp_getNInYangValue()
    return cp_mp_getGodStyleInfo(Eightword::NInYangValue,"cp_mp_NInYangValue()")
  end
  def cp_mp_setNInYangValue(byValue)
    cp_mp_setGodStyleInfo(Eightword::NInYangValue,byValue)
  end
  # 陰陽氣含
  def cp_mp_NInYangValue()
    k = cp_mp_getBySkyIndex(Eightword::ByMonthIdx)
    k /= 2
    k = Five.Modify(k + 1)
    n = cp_mp_getByEarthIndex(Eightword::ByMonthIdx)
    fValue = 0.0
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = cp_mp_getBySkyIndex(byWhatIdx)
      if ((k == 0 && n >= 2 && n <= 4 && m <= 1) ||
        (k == 1 && n >= 5 && n <= 7 && (m == 2 || m == 3)) ||
        (k == 2 && n >= 8 && n <= 10 && (m == 6 || m == 7)) ||
        (k == 3 && (n == 11 || n <= 1) && (m == 8 || m == 9))) then
        fValue += (1.2 * @@SkyValue[m])
      else
        fValue += @@SkyValue[m]
      end
    end
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      m = cp_mp_getByEarthIndex(byWhatIdx)
      if (k == 0 && (n >= 2 && n <= 4 && m >= 2 && m <= 4) ||
        (k == 1 && n >= 5 && n <= 7 && m >= 5 && m <= 7) ||
        (k == 2 && n >= 8 && n <= 10 && m >= 8 && m <= 10) ||
        (k == 3 && (n == 11 || n <= 1) && (m == 11 || m <= 1)))
        fValue += (1.2 * @@EarthValue[m])
      else
        fValue += @@EarthValue[m]
      end
    end
    fValue *= 10
    if (fValue >= 0) then
      cp_mp_setNInYangValue((fValue + 0.5).to_i)
    else
      cp_mp_setNInYangValue((fValue - 0.5).to_i)
    end
  end

  def cp_mp_nSick()
    nSick = Array.new(10,0)
    nSky = Array.new(10,0)

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      m = cp_mp_getBySkyIndex(i)
      nSky[m] += 16
    end

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      (0..2).each do |j|
        m = cp_mp_getByChungSky(i,j)
        n = cp_mp_getByChungScore(i,j)
        if (m > 0) then
          nSky[m - 1] += n
        end
      end
    end

    (0..9).each do |i|
      nSick[i] = nSky[i]
    end
    cp_mp_setnSick(nSick)
  end
  def cp_mp_getnSick(arrayIndex=nil)
    return cp_mp_getGodStyleInfo(Eightword::NSick_10,"cp_mp_nSick()",arrayIndex)
  end
  def cp_mp_setnSick(byValue,arrayIndex=nil)
    cp_mp_setGodStyleInfo(Eightword::NSick_10,byValue,arrayIndex)
  end

  def cp_mp_GodValue()
    nGodValue = Array.new(10,0)

    nSick = cp_mp_nSick()

    m = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
    n = m % 2
    if (n == 1) then
      (0..9).each do |i|
        nGodValue[i] = nSick[m]
        m = Sky.Modify(m - 1)
      end
    else
      j = 0
      (0..4).each do |i|
        nGodValue[j] = nSick[m]
        nGodValue[j + 1] = nSick[m + 1]
        m = Sky.Modify(m - 2)
        j += 2
      end
    end
    cp_mp_setnGodValue(nGodValue)
  end
  def cp_mp_getnGodValue(arrayIndex=nil)
    return cp_mp_getGodStyleInfo(Eightword::NGodValue_10,"cp_mp_GodValue()",arrayIndex)
  end
  def cp_mp_setnGodValue(byValue,arrayIndex=nil)
    cp_mp_setGodStyleInfo(Eightword::NGodValue_10,byValue,arrayIndex)
  end

  def cp_mp_FiveValue()
    nFive = Array.new(5,0)
    nFiveValue = Array.new(5,0)

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      m = cp_mp_getBySkyFive(i)
      nFive[m] += 16
    end

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      (0..2).each do |j|
        m = cp_mp_getByChungSky(i,j)
        n = cp_mp_getByChungScore(i,j)
        if (m > 0) then
          m = cp_mp_getByChungFive(i,j)
          nFive[m] += n
        end
      end
    end

    (0..4).each do |i|
      nFiveValue[i] = nFive[i]
    end

    cp_mp_setnFiveValue(nFiveValue)
  end
  def cp_mp_getnFiveValue(arrayIndex=nil)
    return cp_mp_getGodStyleInfo(Eightword::NFiveValue_5,"cp_mp_FiveValue()",arrayIndex)
  end
  def cp_mp_setnFiveValue(byValue,arrayIndex=nil)
    cp_mp_setGodStyleInfo(Eightword::NFiveValue_5,byValue,arrayIndex)
  end

  def cp_mp_getByChungScore(byWhatIdx=nil,arrayIndex=nil)
    return cp_mp_getGodStyleInfo2(Eightword::ByChungScore_4_3,"cp_mp_ChungSkyScore()",byWhatIdx,arrayIndex)
  end
  def cp_mp_setByChungScore(byValue,byWhatIdx=nil)
    cp_mp_setGodStyleInfo(Eightword::ByChungScore_4_3,byValue,byWhatIdx)
  end

  def cp_mp_ChungSkyScore()
    byChungScore = Array.new(4) {Array.new(3,0)}

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      m = cp_mp_getByEarthIndex(i)

      if (m == 0 || m == 3 || m == 6 || m == 9) then
        if (@par_ChungSkyType == PAN_SKY_1) then
          if (i == 1) then
            byChungScore[i][0] = 24
          else
            byChungScore[i][0] = 16
          end
        else
          if (i == 1) then
            byChungScore[i][0] = 17
            byChungScore[i][1] = 7
          else
            byChungScore[i][0] = 11
            byChungScore[i][1] = 5
          end
        end
      elsif (m == 6 || m == 11) then
        if (i == 1) then
          byChungScore[i][0] = 17
          byChungScore[i][1] = 7
        else
          byChungScore[i][0] = 11
          byChungScore[i][1] = 5
        end
      else
        if (i == 1) then
          byChungScore[i][0] = 12
          byChungScore[i][1] = 7
          byChungScore[i][2] = 5
        else
          byChungScore[i][0] = 8
          byChungScore[i][1] = 5
          byChungScore[i][2] = 3
        end
      end
    end
    cp_mp_setByChungScore(byChungScore)
  end

  # 四柱的天干分數
  def cp_mp_getBySkyScore(byWhatIdx=nil)
    return cp_mp_getPanInfo(Eightword::BySkyScore_4,"cp_mp_BySkyScore_4()",byWhatIdx)
  end
  def cp_mp_setBySkyScore(byValue,byWhatIdx=nil)
    cp_mp_setPanInfo(Eightword::BySkyScore_4,byWhatIdx,byValue)
  end

  def cp_mp_CheckCombineSuccess(m,n,k,l)
    nType = ""
    if (m > n) then
      m,n = n,m
      k,l = l,k
    end
    if (m == 0 && (k == 10 || l == 5)) then
      if ((l == 5 && (k == 4 || k == 6 || k == 10)) ||
        (k == 10 && (l == 5 || l == 7))) then
        bSuccess = true
        nType = "m0_k10_l5"
      end
    elsif (m == 1 && (k == 9 || l == 4)) then
      if ((l == 4 && (k == 1 || k == 7 || k == 9)) ||
        (k == 9 && (l == 4 || l == 8 || l == 10))) then
        bSuccess = true
        nType = "m1_k9_l4"
      end
    elsif (m == 2 && (k == 8 || l == 1)) then
      if ((l == 1 && (k == 0 || k == 4 || k == 8)) ||
        (k == 8 && (l == 1 || l == 5 || l == 11))) then
        bSuccess = true
        nType = "m2_k8_l1"
      end
    elsif (m == 3 && (k == 7 || l == 2)) then
      if ((l == 2 && (k == 3 || k == 7 || k == 11)) ||
        (k == 7 && (l == 0 || l == 2 || l == 4))) then
        bSuccess = true
        nType = "m3_k7_l2"
      end
    elsif (m == 4 && (k == 6 || l == 5)) then
      if ((l == 5 && (k == 2 || k == 6 || k == 10)) ||
        (k == 6 && (l == 3 || l == 5 || l == 7))) then
        bSuccess = true
        nType = "m4_k6_l5"
      end
    end
    return bSuccess,m,n,k,l,nType
  end
  def cp_mp_BySkyScore_4()
    bSuccess = false
    bySkyScore = Array.new(4,9)

    m = cp_mp_getBySkyIndex(Eightword::ByYearIdx)
    k = cp_mp_getByEarthIndex(Eightword::ByYearIdx)
    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |byWhatIdx|
      n = cp_mp_getBySkyIndex(byWhatIdx)
      l = cp_mp_getByEarthIndex(byWhatIdx)
      if (m == n + 5 || n == m + 5) then
        bSuccess,m,n,k,l,nType = cp_mp_CheckCombineSuccess(m,n,k,l)

              k = 0
              if (!bSuccess && byWhatIdx < Eightword::ByHourIdx) then
                a = cp_mp_getBySkyIndex(byWhatIdx + 1)
                b = cp_mp_getBySkyIndex(byWhatIdx - 1)
                  if (a == b) then
                       k = -1
                  end
              end
              if (k != -1) then
            if (!bSuccess) then
              bySkyScore[byWhatIdx] = bySkyScore[byWhatIdx] - 4
              bySkyScore[byWhatIdx - 1] = bySkyScore[byWhatIdx - 1] - 4
                  end
                  n = -1
            l = -1
              end
      end
      m = n
      k = l
    end
    cp_mp_setBySkyScore(bySkyScore)
  end

  def cp_mp_getByEarthScore(byWhatIdx=nil)
    return cp_mp_getPanInfo(Eightword::ByEarthScore_4,"cp_mp_ByEarthScore_4()",byWhatIdx)
  end
  def cp_mp_setByEarthScore(byValue,byWhatIdx=nil)
    cp_mp_setPanInfo(Eightword::ByEarthScore_4,byWhatIdx,byValue)
  end
  def cp_mp_ByEarthScore_4()
    byEarthScore = Array.new(4,16)
    byEarthScore[1] = 24
    cp_mp_setByEarthScore(byEarthScore)
  end

  def cp_mp_getnOrgCombine()
    return cp_mp_getGodStyleInfo(Eightword::NOrgCombine,"cp_mp_nOrgCombine()")
  end
  def cp_mp_setnOrgCombine(byValue)
      cp_mp_setGodStyleInfo(Eightword::NOrgCombine,byValue)
  end
  def cp_mp_nOrgCombine()
    nOrgCombine = 0

    bySkyIndex = cp_mp_getBySkyIndex()
    byEarthIndex = cp_mp_getByEarthIndex()

    m = bySkyIndex[Eightword::ByYearIdx]
    k = byEarthIndex[Eightword::ByYearIdx]

    (Eightword::ByMonthIdx..Eightword::ByHourIdx).each do |i|
      n = bySkyIndex[i]
      l = byEarthIndex[i]
      if (m == n + 5 || n == m + 5) then
        bSuccess,m,n,k,l,nType = cp_mp_CheckCombineSuccess(m,n,k,l)

              k = 0
              if (!bSuccess && i < 3) then
                  if (bySkyIndex[i + 1] == bySkyIndex[i - 1]) then
                      k = -1
                  end
              end
              if (k != -1) then
            if (bSuccess) then
                      nOrgCombine = nOrgCombine + 1
                  end
                  n = -1
            l = -1
              end
      end
      m = n
      k = l
    end
    cp_mp_setnOrgCombine(nOrgCombine)
  end

  def cp_mp_isSkyMouseOverEvent()
    return @func_4cSmm_Event && (par_isAncillaryTrue?(Eightword::ADP_SKYHER))
  end
  def cp_mp_event_SkyHerHua(nSkyIndex)
    Eightword.SkyHerHua(nSkyIndex,cp_mp_isSkyMouseOverEvent())
  end

  def cp_mp_EarthMouseOverEvent()
    return cp_mp_EarthMouseOverEvent_Display(@func_4cEmm_Event)
  end

  def cp_mp_EarthMouseOverEvent_Display(nEvent)
    (Eightword::ADP_EARTHHER3..Eightword::ADP_EARTHHAI).each do |nId|
      bEnable = par_isAncillaryTrue?(nId)
      nEventValue = Eightword.GetEarthEventValue(nId)
      if (!bEnable) then
        nEvent = nEvent & ~nEventValue
      end
    end
    return nEvent
  end

  def Eightword.cp_isEarthMouseOverEvent(nValue,nEvent)
    return (nEvent & nValue) > 0
  end

  def cp_mp_GetOrgFive()
    nOFive = Array.new(5,0)

    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      n = cp_mp_getBySkyFive(i)
      nOFive[n] += 16
    end
    (Eightword::ByYearIdx..Eightword::ByHourIdx).each do |i|
      (0..2).each do |j|
        n = cp_mp_getByChungSky(i,j)
        if (n > 0) then
          n = cp_mp_getByChungFive(i,j)
        else
          n = cp_mp_getByChungFive(i,0)
        end

        if (j == 0) then
          nOFive[n] += 8
        elsif (j == 1) then
          nOFive[n] += 5
        else
          nOFive[n] += 3
        end
      end
    end

    return nOFive
  end

    def cp_mp_getBBase()
      if (@par_BType == Eightword::B_TYPE_MONTH) then
        nMonthSkyFive = cp_mp_getBySkyFive(Eightword::ByMonthIdx)
        nMonthEarthFive = cp_mp_getByEarthFive(Eightword::ByMonthIdx)
      if (nMonthSkyFive == nMonthEarthFive) then
        b = @par_BBase / 100.0
      else
        b = 0
      end
    else
        nDaySkyFive = cp_mp_getBySkyFive(Eightword::ByDayIdx)
        nDayEarthFive = cp_mp_getByEarthFive(Eightword::ByDayIdx)
      if (nDaySkyFive == nDayEarthFive) then
        b = @par_BBase / 100.0
      else
        b = 0
      end
    end
    return b
    end
    def cp_mp_getBType()
    nbType = -1
      if (@par_BType == Eightword::B_TYPE_MONTH) then
        nMonthSkyFive = cp_mp_getBySkyFive(Eightword::ByMonthIdx)
        nMonthEarthFive = cp_mp_getByEarthFive(Eightword::ByMonthIdx)
      if (nMonthSkyFive == nMonthEarthFive) then
        nbType = nMonthSkyFive
      end
    else
        nDaySkyFive = cp_mp_getBySkyFive(Eightword::ByDayIdx)
        nDayEarthFive = cp_mp_getByEarthFive(Eightword::ByDayIdx)
      if (nDaySkyFive == nDayEarthFive) then
        nbType = nDaySkyFive
      end
    end
    return nbType
    end

end
