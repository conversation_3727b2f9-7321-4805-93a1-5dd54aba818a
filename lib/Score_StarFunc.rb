# Score of Star
class Score
	@m_nPanType
	@Star
	@StarInfo
	@UserAskData
	@ScoreHouse
	@UserInfo
	@Score

	def ps_Star_getScore(nPanType,hUserInfo,hUserAskData,bDetail=true)
    # 先不存資料庫，以免資料過大，速度過慢 2020/6/26
   #  timestamp_user = Score.get_user_info_timestamp(hUserInfo)
   #  timestamp_udt = Score.make_timestamp_udt2(nPanType,hUserAskData)
   #  ts = find_ts_org_score(timestamp_user,nPanType,timestamp_udt,0)
   #  org_score = ts.hScores
	  
   #  # 之前計算出來的結果存起來，直接取用即可
   #  if !(org_score == nil || org_score == {}) && (ts.status == 0) then
   #    @Score = org_score.clone
   #    return @Score
	  # end

	  # 資料庫中沒有，重新計算
	  @m_nPanType = nPanType
	  @Star = Star.new(hUserInfo,hUserAskData)
    @Star.g_GetAllPanInfo(nPanType,hUserInfo,hUserAskData,nil)
      # @Star.Score_GetAllScore(nPanType,hUserInfo,hUserAskData)
      # @Star.Score_CalcScore(nPanType,hUserInfo,hUserAskData)
      @UserAskData = hUserAskData.clone
      @UserAskData[Cfate::ETime] = @Star.ddg_ET()
      @UserInfo = hUserInfo
      @ScoreHouse = @UserAskData[Star::HOUSE_NAME]
      # if (@m_nPanType == Cfate::PAN_NORMAL) then
      #   @ScoreHouse = Star::EX_HOUSE_ALL
      # else
      #   if (@ScoreHouse == Star::EX_HOUSE_ALL) then
      #     # @ScoreHouse = 1
      #   end
      # end
      @Score = Hash.new
      if (@ScoreHouse == nil) then
        @ScoreHouse = Star::EX_HOUSE_ALL
      end
      ps_Star_createScore(bDetail)

      # 將計算出來的結果存起來，下次直接取用即可
      # ts.status = 0
      # ts.hScores = @Score
      # ts.save!

      return @Score
	end
    def Score.get_user_info_timestamp(hUI)
        y,m,d = hUI[Cfate::WYear],hUI[Cfate::WMonth],hUI[Cfate::WDate]
        h,min,bSex = hUI[Cfate::WHour],hUI[Cfate::WMinute],hUI[Cfate::Sex]
        timestamp_user = Xdate.make_timestamp_solar(y,m,d,h,bSex ? 0 : 1)
        return timestamp_user
    end
    def Score.make_timestamp_udt2(pan_type,hUserDefData)
        timestamp_udt = Score.get_user_def_data_timestamp(hUserDefData)
        return Score.make_timestamp_udt(pan_type,timestamp_udt)
    end
    def Score.make_timestamp_udt(pan_type,timestamp_udt)
        y,m,d,h,sex,min,mb = Xdate.parse_timestamp(timestamp_udt)
        case pan_type
        when Cfate::PAN_NORMAL
            timestamp_udt = Xdate.make_timestamp2(0)
        when Cfate::PAN_TENYEAR
            timestamp_udt = Xdate.make_timestamp2(y)
        when Cfate::PAN_FLOWYEAR
            timestamp_udt = Xdate.make_timestamp2(y)
        when Cfate::PAN_FLOWMONTH
            timestamp_udt = Xdate.make_timestamp2(y,m)
        when Cfate::PAN_FLOWDATE
            timestamp_udt = Xdate.make_timestamp2(y,m,d)
        when Cfate::PAN_FLOWTIME
            timestamp_udt = Xdate.make_timestamp2(y,m,d,h)
        when Cfate::PAN_FLOWMIN
            timestamp_udt = Xdate.make_timestamp2(y,m,d,h,min)
        end
        return timestamp_udt
    end
    def Score.get_user_def_data_timestamp(hUD)
        ey,em,ed = hUD[Cfate::EYear],hUD[Cfate::EMonth],hUD[Cfate::EDate]
        bLeapMonth,h,min = hUD[Cfate::LeapMonth],hUD[Cfate::WHour],hUD[Cfate::WMinute]
        y,m,d = Xdate.East2West(ey,em,ed,bLeapMonth)
        timestamp_udt = Xdate.make_timestamp2(y,m,d,h,min)
        return timestamp_udt
    end
    def find_ts_org_score(timestamp_user,pan_type,timestamp_udt,status)
        ts = TalentScore.check_talent_key("org_score")
        ts = ts.check_timestamp_user(timestamp_user)
        ts = ts.check_pan_type(pan_type)
        ts = ts.check_timestamp_udt(timestamp_udt).last

        if (ts == nil) then
           ts = TalentScore.new
           ts.talent_key = "org_score"
           ts.timestamp_user = timestamp_user
           ts.pan_type = pan_type
           ts.timestamp_udt = timestamp_udt
        end

        return ts
    end

# 改為Hash,之後再to_json
	def ps_Star_createScore(bDetail)
		sBuf = ""
		if (@ScoreHouse == Star::EX_HOUSE_ALL) then
			@Score["AllHouse"] = ps_Star_Create_AllHouse(@m_nPanType)
		else
			@Score["OneHouse"] = ps_Star_Create_OneHouse(@m_nPanType,@ScoreHouse)
		end
		if (bDetail) then
      @Score["Details"] = ps_Star_createScore_Details()
		end
	end

	def ps_Star_Create_AllHouse(nPanType)
      oStar = @Star
	  oStar.Score_CalcHouseEarthScore(nPanType,@UserInfo,@UserAskData)

	  nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType)

      # 各宮分數總表
      hHouseTotal = Hash.new

	  # 1..12 表示子宮到亥宮
	  (1..12).each do |i|
		  sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)
      sHouseName = sHouseName

		  # 指數顯示部份
		  nHouseScore = oStar.Score_GetHouseScore(nPanType,nEarth).round(1)
		  hHouseTotal[sHouseName] = nHouseScore.round(1)

		  # 尋找下一個宮名
		  nEarth = oStar.gHouse_GetNextHouseEarth(nEarth)
	  end

	  return hHouseTotal
	end

	def ps_Star_Create_OneHouse(nPanType,nScoreHouse)
      hHouse = Hash.new
      oStar = @Star

  	  nEarth = oStar.g_House_GetEarth(nPanType,nScoreHouse)
  	  sHouseName = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
  	  hHouse["house_name"] = sHouseName
  	  case (nPanType)
        when (Cfate::PAN_NORMAL) then
          hHouse["score_info"],flow_title = ps_Star_Create_OneHouse_Normal(nPanType,nScoreHouse)  # PAN_TENYEAR
  	  	when (Cfate::PAN_TENYEAR) then
  	  		hHouse["score_info"],flow_title = ps_Star_Create_OneHouse_TenYear(nPanType,nScoreHouse)  # PAN_TENYEAR
  	  	when (Cfate::PAN_FLOWYEAR) then
  	  		hHouse["score_info"],flow_title = ps_Star_Create_OneHouse_FlowYear(nPanType,nScoreHouse)  # PAN_FLOWYEAR
  	  	when (Cfate::PAN_FLOWMONTH) then
  	  		hHouse["score_info"],flow_title = ps_Star_Create_OneHouse_FlowMonth(nPanType,nScoreHouse)  # PAN_FLOWMONTH
  	  	when (Cfate::PAN_FLOWDATE) then
  	  	 	hHouse["score_info"],flow_title = ps_Star_Create_OneHouse_FlowDate(nPanType,nScoreHouse)   # PAN_FLOWDATE
  	  	when (Cfate::PAN_FLOWTIME) then
  	  		hHouse["score_info"],flow_title = ps_Star_Create_OneHouse_FlowTime(nPanType,nScoreHouse)  # PAN_FLOWTIME
  	  	when (Cfate::PAN_FLOWMIN) then
  	  		hHouse["score_info"],flow_title = "",""    # PAN_FLOWMIN
  	  end
      hHouse["flow_title"] = flow_title
	  return hHouse
	end

  def ps_Star_Create_OneHouse_Normal(nPanType,nScoreHouse)
    hFlow = Hash.new
    oStar = @Star

    nNextPanType = nPanType + 1
    # 本命開始年
    nStarYear = oStar.gEx_GetLargeYear(0)

    # oStar = Star.new
    hAskData = Hash.new
    (0..11).each do |i|
      hAskData[Cfate::EYear] = nStarYear
      oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
      nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

      # 指數顯示部份
      nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)

      hFlow["#{nStarYear}~#{nStarYear+9}#{Pm.GetStr("IDS_X_YEAR")}"] = nHouseScore.round(1)

      nStarYear += 10
      # break if !Xdate.IsYearLegal?(nStarYear)
    end
      return hFlow,"Normal"
  end

	def ps_Star_Create_OneHouse_TenYear(nPanType,nScoreHouse)
      hFlow = Hash.new
      oStar = @Star

	  nNextPanType = nPanType + 1
	  # 2、再顯示該十年流運之十年之事業宮資料。
	  nStarYear = oStar.g_getLargeSanStartYear()

	  # oStar = Star.new
	  hAskData = Hash.new
	  (0..9).each do |i|
		hAskData[Cfate::EYear] = nStarYear + i
		oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
		nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

  		# 指數顯示部份
		nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)

        hFlow[hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")] = nHouseScore.round(1)
	  end
      return hFlow,"#{nStarYear}~#{nStarYear + 9}"
	end

	def ps_Star_Create_OneHouse_FlowYear(nPanType,nScoreHouse)
      hFlow = Hash.new
      oStar = @Star

	  nNextPanType = nPanType + 1

	  # oStar = Star.new
	  hAskData = @UserAskData.clone
      hAskData[Cfate::WHour] = 0

	  mArray = Xdate.GetEastMonthArray(hAskData[Cfate::EYear])
	  mArray.each do |aMonth|
		hAskData[Cfate::EMonth] = aMonth[0]
		hAskData[Cfate::LeapMonth] = aMonth[2]

		oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
		nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

		sName = ""
		if (aMonth[2] == true) then
			sName += Pm.GetStr("IDS_X_LEAP")
		end
		sName += hAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")

		# 指數顯示部份
		nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
	    hFlow[sName] = nHouseScore.round(1)
	  end
	  return hFlow,hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
	end


	def ps_Star_Create_OneHouse_FlowMonth(nPanType,nScoreHouse)
      hFlow = Hash.new
      oStar = @Star

	  nNextPanType = nPanType + 1

	  # oStar = Star.new
	  hAskData = @UserAskData.clone
      hAskData[Cfate::WHour] = 0

	  nEYear = hAskData[Cfate::EYear]
	  nEMonth = hAskData[Cfate::EMonth]
	  bLeapMonth = hAskData[Cfate::LeapMonth]
	  nDays = Xdate.GetEastMonthDays(nEYear, nEMonth, bLeapMonth)
	  flow_title = hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
      sLeap = hAskData[Cfate::LeapMonth] ? Pm.GetStr("IDS_X_LEAP") : ""
	  flow_title += sLeap + hAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")

    nDays = hAskData["CurrentDate"] ? 1 : nDays

	  (1..nDays).each do |i|
      hAskData[Cfate::EDate] = i if nDays != 1

		  oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
		  nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

		  sName = hAskData[Cfate::EDate].to_s + Pm.GetStr("IDS_X_DAY")

		  # 指數顯示部份
		  nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
      hFlow[sName] = nHouseScore.round(1)
	  end
	  return hFlow,flow_title
	end

	def ps_Star_Create_OneHouse_FlowDate(nPanType,nScoreHouse)
      hFlow = Hash.new
      oStar = @Star

	  nNextPanType = nPanType + 1

		# oStar = Star.new
	  hAskData = @UserAskData.clone

	  flow_title = hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
      sLeap = hAskData[Cfate::LeapMonth] ? Pm.GetStr("IDS_X_LEAP") : ""
      flow_title += sLeap + hAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")
	  flow_title += hAskData[Cfate::EDate].to_s + Pm.GetStr("IDS_X_DAY")

	  (1..12).each do |i|
		hAskData[Cfate::ETime] = i - 1
        nWHour = Xdate.ETime2PanHour(i - 1)
        hAskData[Cfate::WHour] = nWHour

		oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
		nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

		sName = Earth.GetName(Earth.EarthIndex2Earth(hAskData[Cfate::ETime])) + Pm.GetStr("IDS_X_TIME")

		# 指數顯示部份
		nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
        hFlow[sName] = nHouseScore.round(1)
	  end

      return hFlow,flow_title
	end

	def ps_Star_Create_OneHouse_FlowTime(nPanType,nScoreHouse)
      hFlow = Hash.new
      oStar = @Star

	  nNextPanType = nPanType + 1

	  # oStar = Star.new
	  hAskData = @UserAskData.clone

	  flow_title = hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
      sLeap = hAskData[Cfate::LeapMonth] ? Pm.GetStr("IDS_X_LEAP") : ""
      flow_title += sLeap + hAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")
	  flow_title += hAskData[Cfate::EDate].to_s + Pm.GetStr("IDS_X_DAY")
	  flow_title += Earth.GetName(Earth.EarthIndex2Earth(hAskData[Cfate::ETime])) + Pm.GetStr("IDS_X_TIME")

	  nETime = hAskData[Cfate::ETime]
      hAskData[Cfate::WHour] = Xdate.ETimeFirstHour(nETime)
      # nEYear,nEMonth,nEDate,bLeapMonth = hAskData[Cfate::EYear],hAskData[Cfate::EMonth],hAskData[Cfate::EDate],hAskData[Cfate::LeapMonth]
      # if (hAskData[Cfate::WHour] == 23) then
      # 	nEYear,nEMonth,nEDate,bLeapMonth = Xdate.PreEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      # end
      # hAskData[Cfate::EYear],hAskData[Cfate::EMonth],hAskData[Cfate::EDate],hAskData[Cfate::LeapMonth] = nEYear,nEMonth,nEDate,bLeapMonth

	  (1..12).each do |i|
	    hAskData[Cfate::EMinute] = i - 1
        hAskData[Cfate::WMinute] = ((i - 1) * 10) % 60
        hAskData[Cfate::WHour] += 1 if i == 7

        nEYear,nEMonth,nEDate,bLeapMonth = hAskData[Cfate::EYear],hAskData[Cfate::EMonth],hAskData[Cfate::EDate],hAskData[Cfate::LeapMonth]
        if (hAskData[Cfate::WHour] == 24) then
          hAskData[Cfate::WHour] = 0
      	  nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
        end
        hAskData[Cfate::EYear],hAskData[Cfate::EMonth],hAskData[Cfate::EDate],hAskData[Cfate::LeapMonth] = nEYear,nEMonth,nEDate,bLeapMonth

	    oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
	    nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

	    # sName = hAskData[Cfate::EMinute].to_s + Pm.GetStr("IDS_X_MINUTE")
        sName = Xdate.GetEMinRangeStr(oStar.fdg_ET(nNextPanType),hAskData[Cfate::EMinute])
	    # 指數顯示部份
		nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)

        hFlow[sName] = nHouseScore.round(1)
      end

      return hFlow,flow_title
	end

	def ps_Star_createScore_Details()
	  if (@ScoreHouse == Star::EX_HOUSE_ALL) then
		  return ps_Star_Create_AllHouse_Details(@m_nPanType)
	  else
		  return ps_Star_Create_OneHouse_Details(@m_nPanType,@ScoreHouse)
	  end
	end

	def ps_Star_Create_AllHouse_Details(nPanType)
      hDetail = Hash.new
      oStar = @Star

	  nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType)
	  # 1..12 表示子宮到亥宮
	  (1..12).each do |i|
      hHouse = Hash.new
      hHouse["house_id"] = i

		  # sHouseName = @Star.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
		  # hHouse["house_name"] = sHouseName
      hHouse["house_name"] = Star.GetHouseName(i)

		  # 指數顯示部份
		  nHouseScore = oStar.Score_GetHouseScore(nPanType,nEarth).round(1)
		  hHouse["house_score"] = nHouseScore.to_s

      hHouse["house_detail"] = ps_Details(oStar,nPanType,nEarth)

      hDetail["house_#{i}"] = hHouse
		  # 尋找下一個宮名
		  nEarth = oStar.gHouse_GetNextHouseEarth(nEarth)
	  end
	  return hDetail
	end

	def ps_Details(oStar,nPanType,nEarth)
    hHouseDetail = Hash.new
	  hHouseDetail["four_hua"] = ps_Details_FourHua(oStar,nPanType,nEarth)

	  hHouseDetail["star"] = ps_Details_Star(oStar,nPanType,nEarth)

	  # nHousePower = oStar.Score_GetHousePower(nPanType,nEarth).round(1)
	  # sBuf += "God Power == > GodPower:#{nHousePower}"
	  return hHouseDetail
	end

	def ps_Details_Star(oStar,nPanType,nEarth)
	  # sBuf = ""
	  # nStarScoreFive = oStar.Score_Star_GetHouseScoreFive(nPanType,nEarth).round(1)
	  # nStarScoreNoFive = oStar.Score_Star_GetHouseScoreNoFive(nPanType,nEarth).round(1)
	  # sBuf += "Star == >\n 	 ScoreFive:#{nStarScoreFive},\n 	ScoreNoFive:#{nStarScoreNoFive}"
	  # sBuf += PanWeb.pw_GetPan_br()

	  return ps_Details_Star_Details(oStar,nPanType,nEarth)

	end

	def ps_Details_Star_Details(oStar,nPanType,nEarth)
	  hStars = Hash.new
	  hStars[Star::A_STAR] = Hash.new
	  hStars[Star::B_STAR] = Hash.new
	  hStars[Star::DOCTOR] = Hash.new
	  hStars[Star::YEARSTAR] = Hash.new
	  hStars[Star::YEARGOD] = Hash.new
	  hStars[Star::GOD] = Hash.new
	  hStars["score"] = Hash.new
	  # sBuf = ""
	  # sBuf += "HouseScoreRate:  Five: #{oStar.Score_GetScoreTypePower_Five(nPanType)}   ,  NoFive: #{oStar.Score_GetScoreTypePower_NoFive(nPanType)}"
	  # sBuf += PanWeb.pw_GetPan_br()
	  aStarInfo = oStar.Score_Star_GetHouseInfo(nPanType,nEarth)
	  aStarInfo.each do |sInfo|
      hStar = Hash.new
      if (sInfo[0] == Star::GOD) then
        hStar[Star.GetStarName(sInfo[0],sInfo[1])] = oStar.Score_GetHousePower(nPanType,nEarth)
      else
        hStar[Star.GetStarName(sInfo[0],sInfo[1])] = sInfo[2] + sInfo[3]
      end
      nStar = sInfo[1]
      # 甲級星中從對宮借來的不顯示
      if (sInfo[0] == Star::A_STAR) then
        if (oStar.gHouse_GetAStar_HouseEarth(nPanType,nStar) == nEarth) then
          hStars[sInfo[0]][sInfo[1]] = hStar
      			hStars["score"]["#{sInfo[0]}_#{sInfo[1]}"] = hStar[Star.GetStarName(sInfo[0],sInfo[1])]
        end
      else
        hStars[sInfo[0]][sInfo[1]] = hStar
      		hStars["score"]["#{sInfo[0]}_#{sInfo[1]}"] = hStar[Star.GetStarName(sInfo[0],sInfo[1])]
      end
# 			sBuf += sInfo[0]
# 			sBuf += "#{Star.GetStarName(sInfo[0],sInfo[1])} Five: S #{Five.GetFiveStr(sInfo[11])} , H : #{Five.GetFiveStr(sInfo[12])}"
# 			sBuf += "	StarScoreFive : #{sInfo[2]},	StarScoreNoFive : #{sInfo[3]}"
# #			sBuf += "	ScoreOrgFive : #{sInfo[4]},	ScoreOrgNoFive : #{sInfo[5]},    OtherPower : #{sInfo[6]},"
# 			sBuf += "	OtherPower : #{sInfo[6]},"
# 			sBuf += "	StarPower : #{sInfo[7]},	OrgPower : #{sInfo[8]},	SihPower : #{sInfo[9]},	Sign : #{sInfo[10]}"
# 			sBuf += PanWeb.pw_GetPan_br()
		end
		# sBuf += PanWeb.pw_GetPan_br()
      return hStars
	end

	def ps_Details_FourHua(oStar,nPanType,nEarth)
    hFourHua = Hash.new
	  nFourHuaScore = oStar.Score_FourHua_GetHouseScore(nPanType,nEarth).round(1)
    hFourHua["summary"] = nFourHuaScore

	  (0..3).each do |nFourHua|
		  nScoreOrg = oStar.Score_FourHua_GetScore_Original(nPanType,nEarth,nFourHua).round(1)
		  nScoreSelf = oStar.Score_FourHua_GetScore_Self(nPanType,nEarth,nFourHua).round(1)
      hFourHua[nFourHua] = (nScoreOrg + nScoreSelf).round(1)
	  end

	  # aInfo = oStar.Score_FourHua_GetHouseInfo(nPanType,nEarth)
	  # aInfo.each do |sInfo|
	  # 	sBuf += sInfo
	  # 	sBuf += PanWeb.pw_GetPan_br()
	  # end

	  return hFourHua
	end

	def ps_Star_Create_OneHouse_Details(nPanType,nScoreHouse)
      oStar = @Star
	  nEarth = oStar.g_House_GetEarth(nPanType,nScoreHouse)
	  sHouseName = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
	  case (nPanType)
      when (Cfate::PAN_NORMAL) then
       return ps_Star_Create_OneHouse_Details_Normal(nPanType,nScoreHouse)  # PAN_NORMAL
		  when (Cfate::PAN_TENYEAR) then
			 return ps_Star_Create_OneHouse_Details_TenYear(nPanType,nScoreHouse)  # PAN_TENYEAR
		  when (Cfate::PAN_FLOWYEAR) then
			 return ps_Star_Create_OneHouse_Details_FlowYear(nPanType,nScoreHouse)  # PAN_FLOWYEAR
		  when (Cfate::PAN_FLOWMONTH) then
			 return ps_Star_Create_OneHouse_Details_FlowMonth(nPanType,nScoreHouse)  # PAN_FLOWMONTH
		  when (Cfate::PAN_FLOWDATE) then
		 	  return ps_Star_Create_OneHouse_Details_FlowDate(nPanType,nScoreHouse)   # PAN_FLOWDATE
		  when (Cfate::PAN_FLOWTIME) then
			 return ps_Star_Create_OneHouse_Details_FlowTime(nPanType,nScoreHouse)  # PAN_FLOWTIME
		  when (Cfate::PAN_FLOWMIN) then
			 return Hash.new    # PAN_FLOWMIN
		  else return Hash.new
	  end
	end

  def ps_Star_Create_OneHouse_Details_Normal(nPanType,nScoreHouse)
    hDetail = Hash.new
    oStar = @Star

    nNextPanType = nPanType + 1
    # 本命開始年
    nStarYear = oStar.gEx_GetLargeYear(0)

    hDetail["flow_start"] = 0
    hDetail["flow_stop"] = 11 
    # oStar = Star.new
    hAskData = Hash.new
    (0..11).each do |i|
      hHouse = Hash.new

      hAskData[Cfate::EYear] = nStarYear
      oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
      nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

      hHouse["flow_title"] = "#{hAskData[Cfate::EYear]}~#{hAskData[Cfate::EYear]+9}#{Pm.GetStr("IDS_X_YEAR")}"

      # 指數顯示部份
      nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
      hHouse["flow_score"] = nHouseScore.to_s

      hHouse["flow_detail"] = ps_Details(oStar,nNextPanType,nEarth)

      hDetail["flow_#{i}"] = hHouse

      nStarYear += 10
    end
    return hDetail
  end

	def ps_Star_Create_OneHouse_Details_TenYear(nPanType,nScoreHouse)
      hDetail = Hash.new
      oStar = @Star

	  nNextPanType = nPanType + 1
	  # 2、再顯示該十年流運之十年之事業宮資料。
	  nStarYear = oStar.g_getLargeSanStartYear()

      hDetail["flow_start"] = 0
      hDetail["flow_stop"] = 9
	  # oStar = Star.new
	  hAskData = Hash.new
	  (0..9).each do |i|
      hHouse = Hash.new

		  hAskData[Cfate::EYear] = nStarYear + i
		  oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
		  nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

		  hHouse["flow_title"] = hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")

		  # 指數顯示部份
		  nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
      hHouse["flow_score"] = nHouseScore.to_s

		  hHouse["flow_detail"] = ps_Details(oStar,nNextPanType,nEarth)

      hDetail["flow_#{i}"] = hHouse

	  end
	  return hDetail
	end

	def ps_Star_Create_OneHouse_Details_FlowYear(nPanType,nScoreHouse)
      hDetail = Hash.new
      oStar = @Star

	  nNextPanType = nPanType + 1

	  # oStar = Star.new
	  hAskData = @UserAskData.clone
	  hAskData[Cfate::WHour] = 0

      hDetail["flow_start"] = 1
      hDetail["flow_stop"] = 12
	  (1..12).each do |i|
        hHouse = Hash.new

		hAskData[Cfate::EMonth] = i
		oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
		nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)
		flow_title = hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
        sLeap = hAskData[Cfate::LeapMonth] ? Pm.GetStr("IDS_X_LEAP") : ""
		flow_title += sLeap + hAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")
		hHouse["flow_title"] = flow_title

		# 指數顯示部份
		nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
        hHouse["flow_score"] = nHouseScore.to_s

		hHouse["flow_detail"] = ps_Details(oStar,nNextPanType,nEarth)

        hDetail["flow_#{i}"] = hHouse
	  end
	  return hDetail
	end


	def ps_Star_Create_OneHouse_Details_FlowMonth(nPanType,nScoreHouse)
      hDetail = Hash.new
      oStar = @Star

	  nNextPanType = nPanType + 1

	  # oStar = Star.new
	  hAskData = @UserAskData.clone
	  hAskData[Cfate::WHour] = 0

	  nEYear = hAskData[Cfate::EYear]
	  nEMonth = hAskData[Cfate::EMonth]
	  bLeapMonth = hAskData[Cfate::LeapMonth]
	  nDays = Xdate.GetEastMonthDays(nEYear, nEMonth, bLeapMonth)
      hDetail["flow_start"] = 1
      hDetail["flow_stop"] = nDays
	  (1..nDays).each do |i|
        hHouse = Hash.new

		hAskData[Cfate::EDate] = i
        hAskData[Cfate::ETime] = 0
		oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
		nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

		flow_title = hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
        sLeap = hAskData[Cfate::LeapMonth] ? Pm.GetStr("IDS_X_LEAP") : ""
		flow_title += sLeap + hAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")
		flow_title += hAskData[Cfate::EDate].to_s + Pm.GetStr("IDS_X_DAY")
        hHouse["flow_title"] = flow_title

		# 指數顯示部份
		nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
        hHouse["flow_score"] = nHouseScore.to_s

		hHouse["flow_detail"] = ps_Details(oStar,nNextPanType,nEarth)

        hDetail["flow_#{i}"] = hHouse
	  end
	  return hDetail
	end

	def ps_Star_Create_OneHouse_Details_FlowDate(nPanType,nScoreHouse)
      hDetail = Hash.new
      oStar = @Star

	  nNextPanType = nPanType + 1

	  # oStar = Star.new
	  hAskData = @UserAskData.clone
      hDetail["flow_start"] = 1
      hDetail["flow_stop"] = 12
	  (1..12).each do |i|
        hHouse = Hash.new

		hAskData[Cfate::ETime] = i - 1
        nWHour = Xdate.ETime2PanHour(i - 1)
        hAskData[Cfate::WHour] = nWHour
		oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
		nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

		flow_title = hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
        sLeap = hAskData[Cfate::LeapMonth] ? Pm.GetStr("IDS_X_LEAP") : ""
		flow_title += sLeap + hAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")
		flow_title += hAskData[Cfate::EDate].to_s + Pm.GetStr("IDS_X_DAY")
		flow_title += Earth.GetName(Earth.EarthIndex2Earth(hAskData[Cfate::ETime])) + Pm.GetStr("IDS_X_TIME")
        hHouse["flow_title"] = flow_title

		# 指數顯示部份
		nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
        hHouse["flow_score"] = nHouseScore.to_s

        hHouse["flow_detail"] = ps_Details(oStar,nNextPanType,nEarth)

        hDetail["flow_#{i}"] = hHouse
	  end
	  return hDetail
	end

	def ps_Star_Create_OneHouse_Details_FlowTime(nPanType,nScoreHouse)
      hDetail = Hash.new
      oStar = @Star

	  nNextPanType = nPanType + 1

	  # oStar = Star.new
	  hAskData = @UserAskData.clone
	  nETime = hAskData[Cfate::ETime]
      hAskData[Cfate::WHour] = Xdate.ETimeFirstHour(nETime)
      # nEYear,nEMonth,nEDate,bLeapMonth = hAskData[Cfate::EYear],hAskData[Cfate::EMonth],hAskData[Cfate::EDate],hAskData[Cfate::LeapMonth]
      # if (hAskData[Cfate::WHour] == 23) then
      # 	nEYear,nEMonth,nEDate,bLeapMonth = Xdate.PreEDate(nEYear,nEMonth,nEDate,bLeapMonth)
      # end
      # hAskData[Cfate::EYear],hAskData[Cfate::EMonth],hAskData[Cfate::EDate],hAskData[Cfate::LeapMonth] = nEYear,nEMonth,nEDate,bLeapMonth
      hDetail["flow_start"] = 1
      hDetail["flow_stop"] = 12
	  (1..12).each do |i|
        hHouse = Hash.new

		hAskData[Cfate::EMinute] = i - 1
        hAskData[Cfate::WMinute] = ((i - 1) * 10) % 60
        hAskData[Cfate::WHour] += 1 if i == 7
        nEYear,nEMonth,nEDate,bLeapMonth = hAskData[Cfate::EYear],hAskData[Cfate::EMonth],hAskData[Cfate::EDate],hAskData[Cfate::LeapMonth]
        if (hAskData[Cfate::WHour] == 24) then
          hAskData[Cfate::WHour] = 0
      	  nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextEDate(nEYear,nEMonth,nEDate,bLeapMonth)
        end
        hAskData[Cfate::EYear],hAskData[Cfate::EMonth],hAskData[Cfate::EDate],hAskData[Cfate::LeapMonth] = nEYear,nEMonth,nEDate,bLeapMonth
		oStar.Score_CalcScore(nNextPanType,@UserInfo,hAskData,nScoreHouse)
		nEarth = oStar.g_House_GetEarth(nNextPanType,nScoreHouse)

		flow_title = hAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
        sLeap = hAskData[Cfate::LeapMonth] ? Pm.GetStr("IDS_X_LEAP") : ""
		flow_title += sLeap + hAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")
		flow_title += hAskData[Cfate::EDate].to_s + Pm.GetStr("IDS_X_DAY")
		flow_title += Earth.GetName(Earth.EarthIndex2Earth(hAskData[Cfate::ETime])) + Pm.GetStr("IDS_X_TIME")
		flow_title += hAskData[Cfate::WMinute].to_s + Pm.GetStr("IDS_X_MINUTE")
        hHouse["flow_title"] = flow_title

		# 指數顯示部份
		nHouseScore = oStar.Score_GetHouseScore(nNextPanType,nEarth).round(1)
        hHouse["flow_score"] = nHouseScore.to_s

        hHouse["flow_detail"] = ps_Details(oStar,nNextPanType,nEarth)

        hDetail["flow_#{i}"] = hHouse
	  end
	  return hDetail
	end

  # def ps_Star_getScore_20180606(nPanType,hUserInfo,hUserAskData,bDetail=true)
  #   score = ps_Star_getScore(nPanType,hUserInfo,hUserAskData,bDetail=true)
  #   out = {}
  #   out["AllHouse"] = score["AllHouse"]
  #   return score
  # end
  def ps_Star_getScore_20180606(score,pan,oStar,nPanType)
    out = {}
    out["AllHouse"] = score["AllHouse"]
    dizhi = oStar.gHouse_GetFirstHouseEarth(nPanType)

    (1..12).each do |house|
      dizhi_index = Earth.Earth2EarthIndex(dizhi)
      out["house_#{house}"] = make_house_detail(score["Details"]["house_#{house}"],pan["house"]["#{dizhi}"],pan["LuYangTuos"][dizhi_index],dizhi,nPanType,oStar,pan["Fas"][dizhi_index])
      dizhi = oStar.gHouse_GetNextHouseEarth(dizhi)
    end

    out["personal_info"] = pan["middle"]["personal_info"]

    return out
  end

  def make_house_detail(d,pan_house,lyts,dizhi,nPanType,oStar,fas)
    o = {}
    o["house_id"] = d["house_id"]
    o["house_name"] = d["house_name"]
    o["house_score"] = d["house_score"]

    s = pan_house["housename_normal"]
    s.sub!(Pm.GetStr("IDS_A_LEFT_QUOTE"),"")
    s.sub!(Pm.GetStr("IDS_A_RIGHT_QUOTE"),"")
    o["housename_normal"] = s
    o["house_wuxing_str"] = pan_house["housefivestr"]
    o["house_tiangan_str"] = pan_house["houseskystr"]
    o["house_tiangan"] = pan_house["housesky"]
    o["house_dizhi_str"] = pan_house["houseearthstr"]
    o["house_dizhi"] = dizhi
    o["small"] = pan_house["small"]
    o["large"] = [pan_house["large_1"],pan_house["large_2"]]

    hd = d["house_detail"]
    o["sihua"] = {}
    o["sihua"]["sihua_0"] = hd["four_hua"][0]
    o["sihua"]["sihua_1"] = hd["four_hua"][1]
    o["sihua"]["sihua_2"] = hd["four_hua"][2]
    o["sihua"]["sihua_3"] = hd["four_hua"][3]
    o["sihua"]["summary"] = hd["four_hua"]["summary"]

    o["tierA"] = make_house_astars(hd["star"]["A_STAR"],pan_house,lyts,nPanType,oStar,fas)
    o["tierB"] = make_house_bstars(hd["star"]["B_STAR"],pan_house,oStar,fas)
    o["DOCTOR"] = make_house_stars_one(hd["star"]["DOCTOR"])
    o["YEARSTAR"] = make_house_stars_one(hd["star"]["YEARSTAR"])
    o["YEARGOD"] = make_house_stars_one(hd["star"]["YEARGOD"])
    o["GOD"] = make_house_stars_one(hd["star"]["GOD"])
    
    return o
  end

  def make_house_stars_one(d)
    h = make_house_stars(d)
    if (h["stars"].length > 0) then
      return h["stars"][0]
    else
      return {}
    end
  end

  def make_house_astars(d,pan_house,lyts,nPanType,oStar,fas)
    astars = pan_house["astar"]
    a = []
    b = []
    # 先取得panscore的所有的星
    d.each_pair do |k,v|
      b.push(k)
    end
    b.sort!
    b.each do |k|
      a.push(make_house_star(k,d[k]))
    end
    # 更改羊刄 成為 大羊 等 陀 祿
    a = modify_astar_for_lyt(a,nPanType,oStar)

    # 移除對宮借來的主星
    c = []
    a.each_index do |i|
      if (!astars.include?(a[i]["name"])) then
        c.push(i)
      end
    end
    c.reverse!
    c.each do |i|
      a.delete_at(i)
      b.delete_at(i)
    end

    # 加入 廟旺 先天四化 宮干自化 
    a.each_index do |i|
      a[i]["miowong"] = pan_house["astarmiowong"][i][1]
      a[i]["org_sihua"] = pan_house["astarorgfourhua"][i][1]
      a[i]["zi_hua"] = pan_house["astarselffourhua"][i][1]
    end

    # 加入流盤的 流昌等
    fas.each_index do |i|
      s = {}
      starInfo = fas[i]
      if (Star.Get_AStarInfo_StarType(starInfo) == "fa") then
        s["id"] = Star.Get_AStarInfo_Star(starInfo) + 100
        s["name"] = Star.Get_AStarInfo_Name(starInfo)
        s["score"] = 0
        s["miowong"] = oStar.get_AStarInfoMioWong_Str(starInfo)
  
        hFourHua = Star.Get_AStarInfo_OrgFourHua(starInfo)
        s["org_sihua"] = oStar.getFourHuaStr(hFourHua[hFourHua.keys[0]])
  
        nFourHua = Star.Get_AStarInfo_SelfFourHua(starInfo)
        s["zi_hua"] = oStar.getFourHuaStr(nFourHua)
        a.push(s)
      end
    end

    # 加入流盤的 羊 陀 祿
    lyts.each_index do |i|
      s = {}
      starInfo = lyts[i]
      org_sihua = oStar.get_AStarInfoOrgFourHua(starInfo)
      xing_pantype = Cfate.gFindPanTypeByHashKey(org_sihua.keys[0])
      s["id"] = Star.Get_AStarInfo_Star(starInfo) * 10 + xing_pantype
      s["name"] = Star.Get_AStarInfo_Name(starInfo)
      s["score"] = 0
      s["miowong"] = oStar.get_AStarInfoMioWong_Str(starInfo)

      hFourHua = Star.Get_AStarInfo_OrgFourHua(starInfo)
      s["org_sihua"] = oStar.getFourHuaStr(hFourHua[hFourHua.keys[0]])

      nFourHua = Star.Get_AStarInfo_SelfFourHua(starInfo)
      s["zi_hua"] = oStar.getFourHuaStr(nFourHua)
      a.push(s)
    end

    # 重新取得index
    b = []
    a.each_index do |i|
      b.push(a[i]["id"])
    end
    o = {}
    o["index"] = b
    o["stars"] = a

    return o
  end
  def modify_astar_for_lyt(astars,nPanType,oStar)
    a = []
    astars.each do |astar|
      if ([19,20,21].include?(astar["id"])) then
        astar["name"] = Pm.t("star.xing.astar_#{astar["id"]}.pt_#{nPanType}")
        astar["id"] = astar["id"] * 10 + nPanType
      end
      a.push(astar)
    end
    return a
  end

  def make_house_bstars(d,pan_house,oStar,fas)
    a = []
    b = []
    d.each_pair do |k,v|
      b.push(k)
    end
    b.sort!
    b.each do |k|
      a.push(make_house_star(k,d[k]))
    end

    a.each_index do |i|
      a[i]["miowong"] = pan_house["bstarmiowong"][i][1]
    end

    # 加入流盤的 流鸞 流馬等
    fas.each_index do |i|
      starInfo = fas[i]
      if (Star.Get_BStarInfo_StarType(starInfo) == "fb") then
        s = {}
        s["id"] = Star.Get_BStarInfo_Star(starInfo) + 100
        s["name"] = Star.Get_BStarInfo_Name(starInfo)
        s["score"] = 0
        s["miowong"] = oStar.get_BStarInfoMioWong_Str(starInfo)
  
        a.push(s)
      end
    end
    o = {}
    o["index"] = b
    o["stars"] = a

    return o
  end

  def make_house_stars(d)
    a = []
    b = []
    d.each_pair do |k,v|
      b.push(k)
    end
    b.sort!
    b.each do |k|
      a.push(make_house_star(k,d[k]))
    end

    o = {}
    o["index"] = b
    o["stars"] = a

    return o
  end

  def make_house_star(k,v)
    o = {}
    o["id"] = k
    o["name"] = v.keys[0]
    o["score"] = v[o["name"]]
    return o
  end

end
