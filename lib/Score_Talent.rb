class Score
    def get_org_score()
      return @Score
    end
    def set_org_score(org_score)
      @Score = org_score
    end
    def get_star_score(star_type,nStar,nHouseId)
      return 0 if nHouseId == nil
      
    	hDetail = @Score["Details"]
      hHouseDetail = hDetail["house_#{nHouseId}"]["house_detail"]
      hStars = hHouseDetail["star"]

      return hStars["score"]["#{star_type}_#{nStar}"].to_i
  	end
    def get_house_sihua_score(nHouseId)
      org_house_sihua_score = [0,0,0,0]

      hDetail = @Score["Details"]
      hHouseDetail = hDetail["house_#{nHouseId}"]["house_detail"]
      hFourHua = hHouseDetail["four_hua"]
      [Star::FH_LU,Star::FH_CHUAN,Star::FH_KER,Star::FH_GI].each_with_index  do |sihua,i|
        # 如果是從資料庫的Jason回傳的，祿權科忌的key，會變成 "1"，如果是算出來的，會是 1 ，
        # 所以用文字"1"或用數字1當做key，只有一個會有值，另一個為0，因此相加會是最後正常的結果。
        org_house_sihua_score[i] = hFourHua["#{sihua}"].to_i + hFourHua[sihua].to_i
      end

      return org_house_sihua_score
    end
    def get_house_score(nHouseId)
      hDetail = @Score["Details"]
      house_score = hDetail["house_#{nHouseId}"]["house_score"]
      # hAllHouse = @Score["Details"]
      # house_score = hAllHouse[hAllHouse.keys[nHouseId - 1]]["house_score"]

      return house_score.to_i
    end
end
