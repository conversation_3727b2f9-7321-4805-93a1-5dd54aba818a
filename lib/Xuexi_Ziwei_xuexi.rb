
class Xuexi_Ziwei
    # oStar = Star.new(@m_nPanType,@UserInfo,@UserDefData,@ParAll)
  def Xuexi_Ziwei.xuexi_pan_event(hPars,oStar,nPanType)
    xuexi_class = hPars["xuexi_class"]
    xuexi_item = hPars["xuexi_item"]
    s = "Xuexi_Ziwei.xuexi_pan_event_#{xuexi_class}_#{xuexi_item}(hPars,oStar,nPanType)"
    return eval(s)
  end
  def Xuexi_Ziwei.xuexi_shuoming(hPars,oStar,nPanType)
    xuexi_class = hPars["xuexi_class"]
    xuexi_item = hPars["xuexi_item"]
    s = "Xuexi_Ziwei.xuexi_shuoming_#{xuexi_class}_#{xuexi_item}(hPars,oStar,nPanType)"
    return eval(s)
  end

  # 盤別：本命盤
  # 級別1：基本術語及專有名詞介紹
  # xuexi_class 1 event
  # 天干：（學習效果：了解十天干在命盤位置，須背記。）
  def Xuexi_Ziwei.xuexi_shuoming_1_1(hPars,oStar,nPanType)
    tiangan = hPars["tiangan"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.tiangan.taitou") + Xuexi_Ziwei.tiangan_str(tiangan),
              "value" => Xuexi_Ziwei.tiangan_shuoming(tiangan)
              }
  end
  # 地支：（學習效果：了解十二地支在命盤位置，須背記。）
  def Xuexi_Ziwei.xuexi_shuoming_1_2(hPars,oStar,nPanType)
    dizhi = hPars["dizhi"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.dizhi.taitou") + Xuexi_Ziwei.dizhi_str(dizhi),
              "value" => Xuexi_Ziwei.dizhi_shuoming(dizhi)
              }
  end

  # 五行生剋：（學習效果：了解五行生剋，有助於星曜之在各宮位之強弱判斷依據，須背記。）
  def Xuexi_Ziwei.xuexi_shuoming_1_3(hPars,oStar,nPanType)
    wuxing = hPars["wuxing"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.wuxing.taitou") + Pm.GetStr("xuexi_ziwei.wuxing.wuxing_#{wuxing}"),
              "value" => Xuexi_Ziwei.wuxing_sheng_ke_strs(wuxing)
              }
  end
  # 五行旺相休囚絕：（學習效果：了解五行之在各春夏秋冬時強弱。）
  def Xuexi_Ziwei.xuexi_shuoming_1_4(hPars,oStar,nPanType)
    wuxing = hPars["wuxing"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.wuxing.taitou") + Pm.GetStr("xuexi_ziwei.wuxing.wuxing_#{wuxing}"),
              "value" => Xuexi_Ziwei.wuxing_wang_xiang_xiu_qiu_si_strs(wuxing)
              }
  end
  # 六十花甲納音歌：（學習效果：了解天干與地支組合成六十花納甲之五行，須背記。）
  def Xuexi_Ziwei.xuexi_shuoming_1_5(hPars,oStar,nPanType)
    ganzhi = hPars["ganzhi"].to_i
    ganzhi_str,liu_shi_na_yin_str,wuxing = Xuexi_Ziwei.liu_shi_na_yin_str(ganzhi)
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.liu_shi_na_yin.taitou") + ganzhi_str,
              "value" => [liu_shi_na_yin_str,Pm.t("xuexi_ziwei.liu_shi_na_yin.wuxing_su", :q1 => wuxing)]
              }
  end
  # 主星：（學習效果：了解基本定義或說明。）
  # 主星：紫微斗數主星共有十四顆，紫微星系六顆，依序為紫微、天機、太陽、武曲、天同、廉貞；天府星系則有八顆，依序為天府、太陰、貪狼、巨門、天相、天梁、七殺和破軍，主星也包含六吉星及六凶星。
  # 紫微星系：紫微、天機、太陽、武曲、天同、廉貞共有六顆。
  # 天府星系：天府、太陰、貪狼、巨門、天相、天梁、七殺、破軍共有八顆。
  # 六煞星或煞星：羊刃（擎羊星）、陀羅、火星、鈴星、天空、地劫，對凶星的威力有加強的作用，對吉星有減弱吉星效果。
  # 六吉星或吉星：文昌、文曲、天魁、天鉞、左輔、右弼，對凶星的威力有減弱的作用，對吉星有增強吉星效果。
  def Xuexi_Ziwei.xuexi_shuoming_1_6(hPars,oStar,nPanType)
    zhuxingxi = hPars["zhuxingxi"]
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.xing.zhuxing") + Pm.t("xuexi_ziwei.xing.#{zhuxingxi}"),
              "value" => Xuexi_Ziwei.zhuxingxi_xings_str(zhuxingxi)
              }
  end

  # 星、曜：（學習效果：了解基本定義或說明。）
  # 北斗主星：（學習效果：了解基本定義或說明。）
  # 北斗助星：（學習效果：了解基本定義或說明。）
  # 南斗主星：（學習效果：了解基本定義或說明。）
  # 南斗助星：（學習效果：了解基本定義或說明。）
  # 中天主星：（學習效果：了解基本定義或說明。）
  # 中天助星：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_7(hPars,oStar,nPanType)
    xingyao = hPars["xingyao"]
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.xing.xingyao") + Pm.t("xuexi_ziwei.xing.#{xingyao}"),
              "value" => Xuexi_Ziwei.xingyao_xings_str(xingyao)
              }
  end

  # 四化：（學習效果：了解四化星有那些，天干對應各四化星曜，須背記、熟記。）
  def Xuexi_Ziwei.xuexi_shuoming_1_8(hPars,oStar,nPanType)
    tiangan = hPars["tiangan"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.sihua.taitou") + Pm.t("xuexi_ziwei.tiangan.taitou") + Pm.t("xuexi_ziwei.tiangan.tiangan_#{tiangan}"),
              "value" => Xuexi_Ziwei.tiangan_sihua_xings_str(tiangan)
              }
  end


  # 十二宮：（學習效果：了解基本定義或說明。）
  # 本宮：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_9(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    whichgong = hPars["whichgong"]
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.shiergong_strs(gong,whichgong)
              }
  end
  # 對宮：（學習效果：了解基本定義或說明，須背記。）
  def Xuexi_Ziwei.xuexi_shuoming_1_10(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    whichgong = hPars["whichgong"]
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.shiergong_strs(gong,whichgong)
              }
  end
  # 鄰宮、兩鄰宮：（學習效：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_11(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    whichgong = hPars["whichgong"]
    return {
              # "name" => Pm.GetStrWithColon("xuexi_ziwei.liu_shi_na_yin.taitou") + Xdate.GetGanZhi_Str(ganzhi) ,
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.shiergong_strs(gong,whichgong)
              }
  end

  # 三合：（學習效果：了解基本定義或說明，須背記。）
  def Xuexi_Ziwei.xuexi_shuoming_1_12(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    whichgong = hPars["whichgong"]
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.shiergong3634_str(gong,whichgong,oStar,nPanType)
              }
  end
  # 六合：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_13(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    whichgong = hPars["whichgong"]
    liuhe_gong = Xuexi_Ziwei.shiergong_find_gong_liuhe(gong,oStar,nPanType)
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.shiergong_gong_liuhe_str(liuhe_gong)
              }
  end
  # 三方四正：（學習效果：了解基本定義或說明，須背記。）
  def Xuexi_Ziwei.xuexi_shuoming_1_14(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    whichgong = hPars["whichgong"]
    sanfangsizheng_gong = Xuexi_Ziwei.find_gong_sanfangsizheng(gong,oStar,nPanType)
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.shiergong_gong_sanfangsizheng_str(sanfangsizheng_gong)
              }
  end

  # 宮位天干：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_15(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    tiangan = oStar.gHouse_GetSky(nPanType,dizhi)
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.gong_tiangan_str(gong,tiangan)
              }
  end
  # 宮位地支
  def Xuexi_Ziwei.xuexi_shuoming_1_16(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.gong_dizhi_str(gong,dizhi)
              }
  end
  # 宮氣五行
  def Xuexi_Ziwei.xuexi_shuoming_1_17(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    dizhi = oStar.g_House_GetEarth(nPanType,gong)
    wuxing = oStar.gHouse_Five(nPanType,dizhi)
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.gong_wuxing_str(gong,wuxing)
              }
  end
  # 坐、守：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_18(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.pan_gong_zuoshou_str(gong,oStar,nPanType)
              }
  end
  # 會、
  def Xuexi_Ziwei.xuexi_shuoming_1_19(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.pan_gong_hui_str(gong,oStar,nPanType)
              }
  end
  # 照：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_20(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.pan_gong_zhao_str(gong,oStar,nPanType)
              }
  end
  # 沖：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_21(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.pan_gong_chong_str(gong,oStar,nPanType)
              }
  end

  # 同宮、同度、共守、同守：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_22_no_use(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.pan_gong_gongshou_str(gong,oStar,nPanType)
              }
  end

  # 夾：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_22(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.gong_jia_xings_for_select(gong,oStar,nPanType)
              # "value" => Xuexi_Ziwei.jieguo_gong_xing_jia_str(gong,oStar,nPanType)
              }
  end
  # 挾：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_23(hPars,oStar,nPanType)
    gong = hPars["gong"].to_i
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.taitou") + Pm.t("xuexi_ziwei.gong.gong_#{gong}"),
              "value" => Xuexi_Ziwei.jieguo_gong_xing_xia_str(gong,oStar,nPanType)
              }
  end
  # 四生宮：（學習效果：了解基本定義或說明。）
  # 寅、申、巳、亥四宮稱為「四生宮」
  def Xuexi_Ziwei.xuexi_shuoming_1_24(hPars,oStar,nPanType)
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.sishenggong.taitou"),
              "value" => Xuexi_Ziwei.jieguo_sishenggong_right_str()
              }
  end
  # 四墓宮：（學習效果：了解基本定義或說明。）
  # 辰、戍、丑、未四宮稱為「四墓宮」
  def Xuexi_Ziwei.xuexi_shuoming_1_25(hPars,oStar,nPanType)
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.simugong.taitou"),
              "value" => Xuexi_Ziwei.jieguo_simugong_right_str()
              }
  end
  # 四敗宮：（學習效果：了解基本定義或說明。）
  # 子、午、卯、酉四宮稱為「四敗宮」
  def Xuexi_Ziwei.xuexi_shuoming_1_26(hPars,oStar,nPanType)
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.sibaigong.taitou"),
              "value" => Xuexi_Ziwei.jieguo_sibaigong_right_str()
              }
  end
  # 天羅地網：（學習效果：了解基本定義或說明。）
  def Xuexi_Ziwei.xuexi_shuoming_1_27(hPars,oStar,nPanType)
    return {
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.gong.tianluodiwang.taitou"),
              "value" => Xuexi_Ziwei.jieguo_tianluodiwang_right_str()
              }
  end
  # 虛歲：（學習效果：了解基本定義或說明）
  # 太歲：（學習效果：了解基本定義或說明。）

  def Xuexi_Ziwei.xuexi_pan_event_1_1(hPars,oStar,nPanType)
  end

  def Xuexi_Ziwei.xuexi_pan_event_11(hPars,oStar,nPanType)
    if (hPars["event_type"] == "gong") then
      # 宮位的定義
      house_id = hPars["house_id"]
      return {"taitou" => Pm.GetStrWithQuote("xuexi_ziwei.gong.gong_#{house_id}"),
              "jieguo" => Pm.GetStrWithQuote("xuexi_ziwei.gong.dingyi_#{house_id}")
            }
    end
  end
  def Xuexi_Ziwei.xuexi_pan_event_12(hPars,oStar,nPanType)
    if (hPars["event_type"] == "gong") then
      # 宮位的定義
      house_id = hPars["house_id"].to_i
      oStar = Star.new(hUserInfo,hUserDefData,hParAll)

      e = Explain.new
      hUserDefData[Star::HOUSE_NAME] = house_id
      hUserDefData[Star::HOUSE_EARTH] = oStar.g_House_GetEarth(nPanType,house_id)
      hExplain = e.ex_Star_getHouseExplain(hPars["sLang"],nPanType,hUserInfo,hUserDefData,hApFunc,hParAll)
      return {"taitou" => Pm.GetStrWithQuote("xuexi_ziwei.gong.gong_#{house_id}"),
              "jieguo" => hExplain
            }
    end
  end
  def Xuexi_Ziwei.xuexi_pan_event_1_1(hPars,oStar,nPanType)
    if (hPars["event_type"] == "xing") then
      # 宮位的定義
      xing = hPars["xing"].to_i
      xingming = hPars["xingming"]
      xingxing = hPars["xingxing"]

      oStar = Star.new(hUserInfo,hUserDefData,hParAll)

      e = Explain.new
      # hUserDefData[Star::HOUSE_NAME] = house_id
      # hUserDefData[Star::HOUSE_EARTH] = nEarth
      if (xingxing == Star::DIS_ASTAR) then
        # xing = Star.Get_AStarInfo_Star(starinfo)
        # sStarName = Star.Get_AStarInfo_Name(starinfo)
        nEarth = oStar.gHouse_GetAStar_HouseEarth(nPanType,xing)
        sSanKur = oStar.gExHouse_GetAStarSanKurName(nPanType,nEarth,xing)
      elsif (xingxing == Star::DIS_BSTAR) then
        # xing = Star.Get_BStarInfo_Star(starinfo)
        # sStarName = Star.Get_BStarInfo_Name(starinfo)
        nEarth = oStar.gHouse_GetBStar_HouseEarth(nPanType,xing)
        sSanKur = oStar.gExHouse_GetBStarSanKurName(nPanType,nEarth,xing)
      end
      hExplain = Xuexi_Ziwei.xuexi_xing_shuoming(oStar,e,sSanKur,xingming,nPanType,nEarth)

      return {"taitou" => Pm.GetStrWithQuote("xuexi_ziwei.xing.#{xingxing}.star_#{xing}"),
              "jieguo" => hExplain
            }
    end
  end
  def Xuexi_Ziwei.xuexi_shuoming_n_1(hPars,oStar,nPanType)
    tiangan = hPars["tiangan"].to_i
    gongs = oStar.gHouse_GetHouseNameFromTiangan(nPanType,tiangan)
    return {
              "gong" => gongs,
              "name" => Pm.GetStrWithQuote("xuexi_ziwei.tiangan.taitou"),
              "value" => Zeri.get_zeri_str("wannianli.tiangan.tiangan_#{tiangan}")
              }
  end

  def Xuexi_Ziwei.xuexi_xing_shuoming(oStar,e,sSanKur,sStarName,nPanType,nEarth)
    # house_id = oStar.g_House_GetHouseId(nPanType,nEarth)
    sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)
  puts "#{sSanKur} #{sHouseName} #{sStarName}  #{nPanType}  #{nEarth}"
    hExplain = e.we_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseName,sStarName,sSanKur)
    return hExplain
  end
  def Xuexi_Ziwei.xuexi_data(hPars,xuexi_class,xuexi_item,*args)
    s = "Xuexi_Ziwei.xuexi_data_#{xuexi_class}_#{xuexi_item}(*args)"
    return eval(s)
  end
  def Xuexi_Ziwei.xuexi_data_1_1(*args)
    # tiangan = args[0]
    return {
            "tiangan" => 1,
            "dizhi" => 1
           }
  end
  def Xuexi_Ziwei.xuexi_data_1_2(*args)
    # tiangan = args[0]
    return {
            "tiangan" => 1,
            "dizhi" => 1
           }
  end
  def Xuexi_Ziwei.xuexi_data_1_3(*args)
    return {
            "wuxing" => 1
           }
  end
  def Xuexi_Ziwei.xuexi_const_data(xuexi_class,xuexi_item,oStar,nPanType)
    s = "Xuexi_Ziwei.xuexi_const_data_#{xuexi_class}(oStar,nPanType)"
    return eval(s)
  end
  def Xuexi_Ziwei.xuexi_const_data_1(oStar,nPanType)
    # tiangan = 0
    return {
            "tiangan_select" => Xuexi_Ziwei.tiangan_for_select(),
            "dizhi_select" => Xuexi_Ziwei.dizhi_for_select(),
            "wuxing_select" => Xuexi_Ziwei.wuxing_for_select(),
            "ganzhi_select" => Xuexi_Ziwei.ganzhi_for_select(),
            "zhuxingxi_select" => Xuexi_Ziwei.zhuxing_for_select(),
            "xingyao_select" => Xuexi_Ziwei.xingyao_for_select(),
            "sihua_select" => Xuexi_Ziwei.sihua_for_select(),
            "sihua_xings" => Xuexi_Ziwei.sihua_xings(),
            "gong_bdl_select" => Xuexi_Ziwei.gong_bdl_for_select(oStar,nPanType),
            "shiergong_bdl" => ["bengong","duigong","lingong"],
            "gong_3634_select" => Xuexi_Ziwei.gong_3634_for_select(oStar,nPanType),
            "shiergong_3634" => ["sanhe","liuhe","sanfangsizheng"],
            "gong_gdw_select" => Xuexi_Ziwei.gong_gdw_for_select(oStar,nPanType),
            "shiergong_gdw" => ["tiangan","dizhi","wuxing"],
            "gong_xing_select" => Xuexi_Ziwei.gong_xing_for_select(oStar,nPanType),
            # "shiergong_xing" => ["zuoshou","hui","zhao","chong","gongshou"],
            "shiergong_xing" => ["zuoshou","hui","zhao","chong","jia","xia"],
            "sixgong_select" => Xuexi_Ziwei.sixgong_for_select(),
            "sixgong" => ["sishenggong","simugong","sibaigong","tianluodiwang"],
            "end" => "end"
           }
  end
  def Xuexi_Ziwei.wuxing_for_select()
    h = Hash.new
    (1..5).each do |i|
      h[Pm.GetStr("xuexi_ziwei.wuxing.wuxing_#{i}")] = i
    end
    return h
  end

  def Xuexi_Ziwei.tiangan_for_select()
    h = Hash.new
    (1..10).each do |i|
      h[Pm.t("xuexi_ziwei.tiangan.tiangan_#{i}")] = i
    end
    return h
  end
  def Xuexi_Ziwei.dizhi_for_select(tiangan=0)
    h = Hash.new
    (1..12).each do |i|
      if (i == 0 ) then
        h[Pm.t("xuexi_ziwei.dizhi.dizhi_#{i}")] = i
      else
        if (tiangan == 0 ) then
          h[Pm.t("xuexi_ziwei.dizhi.dizhi_#{i}")] = i
        else
          n_tiangan = tiangan % 2
          n_dizhi = i % 2
          if (n_tiangan == 1) then
            if (n_dizhi == 1) then
              h[Pm.t("xuexi_ziwei.dizhi.dizhi_#{i}")] = i
            end
          else
            if (n_dizhi == 0) then
              h[Pm.t("xuexi_ziwei.dizhi.dizhi_#{i}")] = i
            end
          end
        end
      end
    end
    return h
  end
  def Xuexi_Ziwei.ganzhi_for_select()
    h = Hash.new
    (0...60).each do |ganzhi|
      ganzhi_str = Xuexi_Ziwei.ganzhi_str(ganzhi)
      h[ganzhi_str] = ganzhi
    end
    return h
  end
  def Xuexi_Ziwei.zhuxing_for_select()
    a = ["ziweixingxi","tianfuxingxi","liujixing","liushaxing"]
    h = Hash.new
    (0...a.length).each do |i|
      xings = Xuexi_Ziwei.zhuxingxi_xings(a[i])
      h[Pm.t("xuexi_ziwei.xing.#{a[i]}")] = [a[i],xings]
    end
    return h
  end
  def Xuexi_Ziwei.xingyao_for_select()
    a = ["beidouzhu3xing","beidouzhu4xing","nandouzhu3xing","nandouzhu4xing","zhongtianzhu3xing","zhongtianzhu4xing"]
    h = Hash.new
    (0...a.length).each do |i|
      xings = Xuexi_Ziwei.xingyao_xings(a[i])
      h[Pm.t("xuexi_ziwei.xing.#{a[i]}")] = [a[i],xings]
    end
    return h
  end
  def Xuexi_Ziwei.sihua_for_select()
    h = Hash.new
    (0...4).each do |i|
      h[Pm.t("xuexi_ziwei.sihua.sihua_#{i}")] = i
    end
    return h
  end
  def Xuexi_Ziwei.gong_bdl_for_select(oStar,nPanType)
    h = Hash.new
    (1..12).each do |gong|
      dizhi = oStar.g_House_GetEarth(nPanType,gong)
      bengongdizhi = dizhi
      duigongdizhi = Xuexi_Ziwei.dui_gong(dizhi)
      lingongdizhi1 = Xuexi_Ziwei.lin_gong(dizhi,1)
      lingongdizhi2 = Xuexi_Ziwei.lin_gong(dizhi,-1)
      a = [[],[duigongdizhi],[lingongdizhi1,lingongdizhi2]]
      h[Xuexi_Ziwei.gong_str(gong)] = [gong,dizhi,a]
    end
    return h
  end
  def Xuexi_Ziwei.gong_3634_for_select(oStar,nPanType)
    h = Hash.new
    (1..12).each do |gong|
      dizhi = oStar.g_House_GetEarth(nPanType,gong)
      sanhe = Xuexi_Ziwei.find_dizhi_sanhe(dizhi)
      sanhe -= [dizhi]
      liuhe = Xuexi_Ziwei.find_dizhi_liuhe(dizhi)
      liuhe -= [dizhi]
      sanfangsizheng = Xuexi_Ziwei.find_dizhi_sanfangsizheng(dizhi,oStar,nPanType)
      sanfangsizheng -= [dizhi]
      a = [sanhe,liuhe,sanfangsizheng]
      h[Xuexi_Ziwei.gong_str(gong)] = [gong,dizhi,a]
    end
    return h
  end
  def Xuexi_Ziwei.gong_gdw_for_select(oStar,nPanType)
    h = Hash.new
    (1..12).each do |gong|
      dizhi = oStar.g_House_GetEarth(nPanType,gong)
      tiangan = oStar.gHouse_GetSky(nPanType,dizhi)
      wuxing = oStar.gHouse_Five(nPanType,dizhi)
      a = [tiangan,dizhi,wuxing]
      h[Xuexi_Ziwei.gong_str(gong)] = [gong,dizhi,a]
    end
    return h
  end

  def Xuexi_Ziwei.gong_xing_for_select(oStar,nPanType)
    h = Hash.new
    bOnlyMain = true
    bOppIfNoMain = false
    (1..12).each do |gong|
      dizhi = oStar.g_House_GetEarth(nPanType,gong)
      zuoshou_xings = Xuexi_Ziwei.pan_gong_zuoshou_xings_pan_id(gong,oStar,nPanType)
      hui_xings = Xuexi_Ziwei.pan_gong_hui_xings_pan_id(gong,oStar,nPanType)
      zhao_xings = Xuexi_Ziwei.pan_gong_zhao_xings_pan_id(gong,oStar,nPanType)
      chong_xings = Xuexi_Ziwei.pan_gong_chong_xings_pan_id(gong,oStar,nPanType)
      # gongshou_xings = Xuexi_Ziwei.pan_gong_gongshou_xings_pan_id(gong,oStar,nPanType)
      gong_jia_xing = Xuexi_Ziwei.pan_gong_jia_xings_pan_id(gong,oStar,nPanType)
      gong_xia_xing = Xuexi_Ziwei.pan_gong_xia_xings_pan_id(gong,oStar,nPanType)
      a = [zuoshou_xings,hui_xings,zhao_xings,chong_xings,gong_jia_xing,gong_xia_xing]
      h[Xuexi_Ziwei.gong_str(gong)] = [gong,dizhi,a]
    end
    return h
  end
  # 寅、申、巳、亥四宮稱為「四生宮」
  def Xuexi_Ziwei.sishenggong_for_select()
    return {Pm.t("xuexi_ziwei.gong.sishenggong.taitou") => Xuexi_Ziwei.sishenggong_dizhis()}
  end
  # 辰、戍、丑、未四宮稱為「四墓宮」
  def Xuexi_Ziwei.simugong_for_select()
    return {Pm.t("xuexi_ziwei.gong.simugong.taitou") => Xuexi_Ziwei.simugong_dizhis()}
  end
  def Xuexi_Ziwei.sibaigong_for_select()
    return {Pm.t("xuexi_ziwei.gong.sibaigong.taitou") => Xuexi_Ziwei.sibaigong_dizhis()}
  end
  def Xuexi_Ziwei.tianluodiwang_for_select()
    return {Pm.t("xuexi_ziwei.gong.tianluodiwang.taitou") => Xuexi_Ziwei.tianluodiwang_dizhis()}
  end
  def Xuexi_Ziwei.sixgong_for_select()
    h = {
      "sishenggong" => Xuexi_Ziwei.sishenggong_for_select(),
      "simugong" => Xuexi_Ziwei.simugong_for_select(),
      "sibaigong" => Xuexi_Ziwei.sibaigong_for_select(),
      "tianluodiwang" => Xuexi_Ziwei.tianluodiwang_for_select()
    }
    return h
  end

end
