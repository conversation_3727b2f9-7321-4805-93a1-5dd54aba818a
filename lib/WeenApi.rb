require("Pm.rb")
require("SkyEarthFive_Function.rb")
require("Cfate.rb")
require("Xdate.rb")
require("Star.rb")
require("PanGiSuong.rb")
require("Score.rb")
require("Explain.rb")
require("Name.rb")
require("Divination.rb")
require("GNum.rb")
require("Eightword.rb")

class WeenApi

  # 臨時改出來給HRT用的
  def g_GetFourHuaInfo(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    hOut = Hash.new

    # oStar = Star.new
    # hPan = oStar.wa_Pan(Cfate::PAN_NORMAL,hUserInfo,hUserDefData,hUserType)
    # # hPan = oStar.wa_Pan(nPanType,hUserInfo,hUserDefData,hUserType)
    # hOut["pan"] = hPan

    # oScore = Score.new
    # hScore = oScore.wa_Result(nPanType,hUserInfo,hUserDefData,hUserType)
    # hOut["score"] = hScore

    oExplain = Explain.new
    hExplain = oExplain.wa_Result(Cfate::DEFAULT_LANG,nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    hOut["explain"] = hExplain

    new_hash = {}

    # new_hash.merge!(flat_hash(hOut["pan"],[]))
    # new_hash.merge!(hOut["score"])
    new_hash.merge!(flat_hash(hOut["explain"],[]))
    new_hash["timestamp"] = make_timestampsex(hUserInfo)

    return new_hash
  end

  def g_GetInfo(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    hOut = Hash.new

    oStar = Star.new
    hPan = oStar.wa_Pan(Cfate::PAN_NORMAL,hUserInfo,hUserDefData,hUserType)
    # hPan = oStar.wa_Pan(nPanType,hUserInfo,hUserDefData,hUserType)
    hOut["pan"] = hPan

    oScore = Score.new
    hScore = oScore.wa_Result(nPanType,hUserInfo,hUserDefData,hUserType)
    hOut["score"] = hScore

    oExplain = Explain.new
    hExplain = oExplain.wa_Result(Cfate::DEFAULT_LANG,nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    hOut["explain"] = hExplain

    new_hash = {}

    new_hash.merge!(flat_hash(hOut["pan"],[]))
    new_hash.merge!(hOut["score"])
    new_hash.merge!(flat_hash(hOut["explain"],[]))
    new_hash["timestamp"] = make_timestampsex(hUserInfo)

    return new_hash
  end
  def g_meen_lunduan(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    nGiSuongHouseId = hUserDefData[Star::HOUSE_NAME]
    hOut = g_profate_lunduan(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)

    oExplain = Explain.new
    my_locale = hUserDefData["my_current_lang"]
    my_locale = Cfate::DEFAULT_LANG if my_locale == nil
    # hExplain = oExplain.wa_Result("zh-TW",nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    hExplain = oExplain.wa_Result(my_locale,nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    hOut["lunduan"] = parse_meen_lunduan_to_profate(hOut["lunduan"],nPanType,hExplain,nGiSuongHouseId)

    return hOut
  end
  def parse_meen_lunduan_to_profate(h_profate_lunduan,nPanType,hExplain,nGiSuongHouseId)
    a = Array.new
    # if (nPanType == Cfate::PAN_NORMAL || nGiSuongHouseId == Star::EX_HOUSE_ALL) then
    if (nGiSuongHouseId == Star::EX_HOUSE_ALL) then
      (1..12).each do |nHouse|
        gong = Ziwei.gong_nick(nHouse)
        h_profate_lunduan[nHouse - 1] = parse_meen_lunduan_to_profate_onehouse(h_profate_lunduan[nHouse - 1],nPanType,hExplain["house_#{nHouse}"],gong)
      end
    else
      gong = Ziwei.gong_nick(nGiSuongHouseId)
      h_profate_lunduan[0] = parse_meen_lunduan_to_profate_onehouse(h_profate_lunduan[0],nPanType,hExplain["house_#{nGiSuongHouseId}"],gong)
    end
    return h_profate_lunduan
  end
  def parse_meen_lunduan_to_profate_onehouse(h_profate_lunduan_onehouse,nPanType,hExplain,gong)
      # 說明部分
      h_profate_lunduan_onehouse["xing"] = parse_meen_lunduan_to_profate_onehouse_xing(h_profate_lunduan_onehouse["xing"],nPanType,hExplain,gong)
      h_profate_lunduan_onehouse["sihua"] = parse_meen_lunduan_to_profate_onehouse_sihua(h_profate_lunduan_onehouse["sihua"],nPanType,hExplain)

      return h_profate_lunduan_onehouse
  end
# first line
  def parse_meen_lunduan_to_profate_onehouse_xing(h_profate_lunduan_onehouse_xing,nPanType,hExplain,gong)
  # puts " explain : #{hExplain["explain"]}"
    if (nPanType <= Cfate::PAN_FLOWYEAR) then
      h_profate_lunduan_onehouse_xing["zhuyao"] = parse_meen_lunduan_to_profate_onehouse_xing_data(hExplain["explain"]["stars"]["astars"])
      # h["zhuyao"]["taitou"] = Ziwei.shuoming_zhuyao_taitou()
      h_profate_lunduan_onehouse_xing["ciyao"] = parse_meen_lunduan_to_profate_onehouse_xing_data(hExplain["explain"]["stars"]["bstars"])
      # h["ciyao"]["taitou"] = Ziwei.shuoming_ciyao_taitou()
    else
      h_profate_lunduan_onehouse_xing["zhuyao"] = parse_meen_lunduan_to_profate_onehouse_xing_data(hExplain["explain"]["stars"]["bstars"])
      # h["zhuyao"]["taitou"] = Ziwei.shuoming_zhuyao_taitou()
      h_profate_lunduan_onehouse_xing["ciyao"] = parse_meen_lunduan_to_profate_onehouse_xing_data(hExplain["explain"]["stars"]["astars"])
      # h["ciyao"]["taitou"] = Ziwei.shuoming_ciyao_taitou()
    end
    h_profate_lunduan_onehouse_xing["huanjingtezheng"] = parse_meen_lunduan_to_profate_onehouse_xing_data(hExplain["explain"]["stars"]["cstars"])
    # h["huanjingtezheng"]["taitou"] = Ziwei.shuoming_huanjingtezheng_taitou_gong(gong)
    return h_profate_lunduan_onehouse_xing
  end
  def parse_meen_lunduan_to_profate_onehouse_xing_data(hIn)
    h = Hash.new
    a_name_explain = Array.new
    hInData = hIn["data"]
    (1..hInData["count"]).each do |i|
      h2 = Hash.new
      h2["name"] = hInData[i]["name"]
      h2["explain"] = hInData[i]["explain"]
      a_name_explain.push(h2)
    end
    # h["data"] = {"name" => a_name, "explain" => a_explain}
    h["data"] = a_name_explain
    return h
  end
  def parse_meen_lunduan_to_profate_onehouse_sihua(h_profate_lunduan_onehouse_sihua,nPanType,hExplain)
    (0..3).each do |nFourHuaIndex|
      h_profate_lunduan_onehouse_sihua[Ziwei.sihua_nick(nFourHuaIndex)] = parse_meen_lunduan_to_profate_onehouse_sihua_each(h_profate_lunduan_onehouse_sihua[Ziwei.sihua_nick(nFourHuaIndex)],nPanType,hExplain,nFourHuaIndex)
    end
    return h_profate_lunduan_onehouse_sihua
  end
  def parse_meen_lunduan_to_profate_onehouse_sihua_each(h_profate_lunduan_onehouse_sihua_each,nPanType,hExplain,nFourHuaIndex)
    # h = Hash.new
    # h["taitou"] = Ziwei.shuoming_sihua_taitou(Ziwei.sihua_nick(nFourHuaIndex))
    h_profate_lunduan_onehouse_sihua_each["xianxiang"] = parse_meen_lunduan_to_profate_onehouse_sihua_each_xianxiang(h_profate_lunduan_onehouse_sihua_each["xianxiang"],nFourHuaIndex,hExplain)
    # h_profate_lunduan_onehouse_sihua_each["shijian"] = parse_meen_lunduan_to_profate_onehouse_sihua_each_shijian(h_profate_lunduan_onehouse_sihua_each["shijian"],nPanType,nFourHuaIndex,nGiSuongHouseId)
    h_profate_lunduan_onehouse_sihua_each["jilu"] = parse_meen_lunduan_to_profate_onehouse_sihua_each_jilu(h_profate_lunduan_onehouse_sihua_each["jilu"],nFourHuaIndex,hExplain)
    return h_profate_lunduan_onehouse_sihua_each
  end
#   # 預測時間點之現象說明：
  def parse_meen_lunduan_to_profate_onehouse_sihua_each_xianxiang(h_profate_lunduan_onehouse_sihua_each_xianxiang,nFourHuaIndex,hExplain)
    h_profate_lunduan_onehouse_sihua_each_xianxiang["data"]["diegong"] = hExplain["explain"]["fourhua"][nFourHuaIndex + 1]["data"]
    h_profate_lunduan_onehouse_sihua_each_xianxiang["data"]["tiangan"] = ""

    return h_profate_lunduan_onehouse_sihua_each_xianxiang
  end
#   # 預測發生時間點： (應期)：
  # def parse_meen_lunduan_to_profate_onehouse_sihua_each_shijian(h_profate_lunduan_onehouse_sihua_each_shijian,nPanType,nFourHuaIndex,nGiSuongHouseId)
  #   h = Hash.new
  #   h["taitou"] = Ziwei.shuoming_sihua_taitou("shijian")
  #   h["gi"] = parse_meen_lunduan_to_profate_onehouse_sihua_each_shijian_gixiong(nPanType,hPanGiSuong["gi"],"gi",gong,hexplode)
  #   h["xiong"] = parse_meen_lunduan_to_profate_onehouse_sihua_each_shijian_gixiong(nPanType,hPanGiSuong["xiong"],"xiong",gong,hexplode)
  #   return h
  # end
#   def parse_meen_lunduan_to_profate_onehouse_sihua_each_shijian_gixiong(nPanType,hIn,gixiong,gong,hexplode)
#     if (hexplode == nil) then
#       return ""
#     end
#     hgx = hexplode[gixiong]
#     keys = hIn.keys
#     s = Ziwei.shuoming_yingqi_pt_gixiong_gong(nPanType,gixiong,gong)
#     s += Ziwei.maohao()
#     a = Array.new
#     keys.each do |key|
#       if (hIn[key].abs > 0) then
#         a.push("#{key}（#{Ziwei.shuoming_yingqi("zhishu")}#{hgx[key]}）")
#       end
#     end
#     if (a.length == 0) then
#       return ""
#     end
#     s += a.join(Ziwei.dunhao())
#     return s
#   end
#   # 發生機率或說明：（自化說明）
  def parse_meen_lunduan_to_profate_onehouse_sihua_each_jilu(h_profate_lunduan_onehouse_sihua_each_jilu,nFourHuaIndex,hExplain)
    # h = Hash.new
    # 2021/1/18 保留感受說明
    # h_profate_lunduan_onehouse_sihua_each_jilu["taitou"] = ""
    # h_profate_lunduan_onehouse_sihua_each_jilu["data"]["zihua"] = ["",[],""][0]
    return h_profate_lunduan_onehouse_sihua_each_jilu
  end
  def gw_star_pan(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    hOut = Hash.new

    oStar = Star.new
    hPan = oStar.wa_Pan(nPanType,hUserInfo,hUserDefData,hUserType)
    # hPan = oStar.wa_Pan(nPanType,hUserInfo,hUserDefData,hUserType)
    hOut["pan"] = hPan

    new_hash = {}

    new_hash.merge!(flat_hash(hOut["pan"],[]))
    new_hash["timestamp"] = make_timestampsex(hUserInfo)

    # result.pan.middle_personal_info_pi_ganzhibirth_2 輸出內容的前面加入 八字：
    # result.pan.middle_personal_info_ci_data2 輸出內容本來是 "雲端命盤"，改成"ween.tw"
    # result.pan.middle_personal_info_ci_data3 輸出內容本來是"http://www.profate.com.tw" 改成 "<EMAIL>"
    new_hash["middle_personal_info_pi_solarbirth_2"] = ppi_only_data(new_hash["middle_personal_info_pi_solarbirth_2"])
    new_hash["middle_personal_info_pi_lunarbirth_2"] = ppi_only_data(new_hash["middle_personal_info_pi_lunarbirth_2"])
    new_hash["middle_personal_info_pi_ganzhibirth_1"] = "#{Ziwei.pan_zhongjian_bazhi_biaotou()}#{Ziwei.maohao()}"
    # new_hash["middle_personal_info_pi_ganzhibirth_2"] = "#{Ziwei.pan_zhongjian_bazhi_biaotou()}#{Ziwei.maohao()}#{new_hash["middle_personal_info_pi_ganzhibirth_2"]}"
    new_hash["middle_personal_info_ci_data2"] = "#{Cfate::SERVER_URL}"
    new_hash["middle_personal_info_ci_data3"] = "support@#{Cfate::SERVER_URL}"

    return new_hash
  end

# last line
  def ziwei_pan(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    hOut = Hash.new

    oStar = Star.new
    hPan = oStar.wa_Pan(Cfate::PAN_NORMAL,hUserInfo,hUserDefData,hUserType)
    # hPan = oStar.wa_Pan(nPanType,hUserInfo,hUserDefData,hUserType)
    hOut["pan"] = hPan

    new_hash = {}

    new_hash.merge!(flat_hash(hOut["pan"],[]))
    new_hash["timestamp"] = make_timestampsex(hUserInfo)

    # result.pan.middle_personal_info_pi_ganzhibirth_2 輸出內容的前面加入 八字：
    # result.pan.middle_personal_info_ci_data2 輸出內容本來是 "雲端命盤"，改成"ween.tw"
    # result.pan.middle_personal_info_ci_data3 輸出內容本來是"http://www.profate.com.tw" 改成 "<EMAIL>"
    new_hash["middle_personal_info_pi_solarbirth_2"] = ppi_only_data(new_hash["middle_personal_info_pi_solarbirth_2"])
    new_hash["middle_personal_info_pi_lunarbirth_2"] = ppi_only_data(new_hash["middle_personal_info_pi_lunarbirth_2"])
    new_hash["middle_personal_info_pi_ganzhibirth_1"] = "#{Ziwei.pan_zhongjian_bazhi_biaotou()}#{Ziwei.maohao()}"
    # new_hash["middle_personal_info_pi_ganzhibirth_2"] = "#{Ziwei.pan_zhongjian_bazhi_biaotou()}#{Ziwei.maohao()}#{new_hash["middle_personal_info_pi_ganzhibirth_2"]}"
    new_hash["middle_personal_info_ci_data2"] = "#{Cfate::SERVER_URL}"
    new_hash["middle_personal_info_ci_data3"] = "support@#{Cfate::SERVER_URL}"

    return new_hash
  end
  def ppi_only_data(s)
    s.strip!
    return s.sub(Cfate.GetSpace(1),"")
  end
  def g_fe_score(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    # if (nPanType == Cfate::PAN_NORMAL || hUserDefData[Star::HOUSE_NAME] == Star::EX_HOUSE_ALL) then
    if (hUserDefData[Star::HOUSE_NAME] == Star::EX_HOUSE_ALL) then
      return g_fe_score_all(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    end
    h = Hash.new
    hfeeling = g_feeling(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType,false)
    hexplode = g_explode(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    h["housename"] = hexplode["base8_housename"]
    # h["flow_title"] = hexplode["flow_title"]
    h["title"] = hexplode["flow_title"]
    h["timestamp"] = make_timestampsex(hUserInfo)
    h["score_title"] = hexplode["score_title"]
    h["feeling"] = hfeeling["OneHouse"]["score_info"]
    h["explode"] = hexplode["explode"]

    h2 = Hash.new
    h2["categories"] = hexplode["score_title"]
    h2["guoli"] = hexplode["guoli"]
    h["xAxis"] = h2

    a = Array.new
    h3 = Hash.new
    h3["name"] = Pm.GetStr("score.star.title.feeling")
    h3["data"] = hfeeling["OneHouse"]["score_info"].values
    a.push(h3)

    h4 = Hash.new
    h4["name"] = Pm.GetStr("score.star.title.gi")
    h4["data"] = hexplode["explode"]["gi"].values
    a.push(h4)

    h5 = Hash.new
    h5["name"] = Pm.GetStr("score.star.title.xiong")
    h5["data"] = hexplode["explode"]["xiong"].values
    a.push(h5)

    h["series"] = a
    return h
  end
  def g_fe_score_all(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    h = Hash.new
    hfeeling = g_feeling(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType,false)
    h["timestamp"] = make_timestampsex(hUserInfo)
    hfeeling_allhouse = hfeeling["AllHouse"]
    h2 = Hash.new
    h2["categories"] = hfeeling_allhouse.keys
    h["xAxis"] = h2

    a = Array.new
    h3 = Hash.new
    h3["name"] = Pm.GetStr("score.star.title.feeling")
    h3["data"] = hfeeling_allhouse.values
    a.push(h3)
    h["series"] = a
    return h
  end

  def g_feeling(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType,bDetail=true)
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    hOut = Hash.new

    oScore = Score.new
    hScore = oScore.ps_getScore(Pm::AP_STAR,nPanType,hUserInfo,hUserDefData,bDetail)
    hOut["score"] = hScore

    new_hash = {}

    new_hash.merge!(hOut["score"])
    new_hash["timestamp"] = make_timestampsex(hUserInfo)

    return new_hash
  end
  def g_explode(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    hOut = Hash.new

    oPanGiSuong = PanGiSuong.new
    if (nPanType != Cfate::PAN_NORMAL) then
      hPanGiSuong,flow_title = oPanGiSuong.wa_explode(nPanType,hUserInfo,hUserDefData,hUserType)
      hOut["gixiong_score"] = hPanGiSuong
    else
      hOut["gixiong_score"] = {}
      hPanGiSuong = {}
      hPanGiSuong["gixiong_scores"] = {"gi" => {},"xiong" => {}}
    end

    oScore = Score.new
    hScore = oScore.wa_explode(nPanType,hUserInfo,hUserDefData,hUserType)
    hOut["base8_score"] = hScore

    new_hash = {}

    h = hPanGiSuong["gixiong_scores"].clone
    new_hash["flow_title"] = flow_title
    new_hash["explode"] = g_final_explode(h["gi"].clone,h["xiong"].clone,hScore["base8"].clone)
    new_hash.merge!(hOut["gixiong_score"])
    new_hash.merge!(hOut["base8_score"])
    new_hash["timestamp"] = make_timestampsex(hUserInfo)

    return new_hash
  end

  def g_final_explode(gi,xiong,base8)
    gi.each_pair do |key,value|
      gi[key] = gi[key] + base8[key] if gi[key].abs > 0
      xiong[key] = xiong[key] + base8[key] if xiong[key].abs > 0
      gi[key] = 49 if gi[key] > 49
      xiong[key] = -49 if xiong[key].abs > 49

      # 吉凶的值也乘以10,變成整數 2018/12/31 Peter
      gi[key] *= 10
      xiong[key] *= 10
    end
    return { "gi" => gi, "xiong" => xiong}
  end
  def g_explode_text(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType,bTest=false)
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    new_hash = Hash.new

    oPanGiSuong = PanGiSuong.new
    hPanGiSuong,flow_title = oPanGiSuong.wa_explode(nPanType,hUserInfo,hUserDefData,hUserType)
    h = hPanGiSuong["gixiong_scores"].clone
    new_hash["explode_text"] = g_final_explode_text(h["gi"].clone,h["xiong"].clone,bTest)
    new_hash["timestamp"] = make_timestampsex(hUserInfo)

    return new_hash
  end
  def g_final_explode_text(gi,xiong,bTest)
    if (bTest) then
      hgi = Hash.new(" ")
      hxiong = Hash.new(" ")
      gi.each_pair do |key,value|
        hgi[key] = " "
        hgi[key] = Pm.GetStr("IDS_GISUONG_GI") if gi[key].abs > 0
        hxiong[key] = " "
        hxiong[key] = Pm.GetStr("IDS_GISUONG_SUONG") if xiong[key].abs > 0
      end
      return { "gi" => hgi, "xiong" => hxiong}
    else
      agi = Array.new
      axiong = Array.new
      gi.each_pair do |key,value|
        if gi[key].abs > 0 then
          agi.push(Pm.GetStr("IDS_GISUONG_GI"))
        else
          agi.push(" ")
        end
        if xiong[key].abs > 0 then
          axiong.push(Pm.GetStr("IDS_GISUONG_SUONG"))
        else
          axiong.push(" ")
        end
      end
      return { "gi" => agi, "xiong" => axiong}
    end
  end

  def flat_hash(h, k = [])
    new_hash = {}
    h.each_pair do |key, val|
      if val.is_a?(Hash)
        new_hash.merge!(flat_hash(val, k + [key]))
      elsif val.is_a?(Array) then
        new_hash.merge!(flat_array(val, k + [key]))
      else
        new_hash[(k + [key]).join("_")] = val
      end
    end
    return new_hash
  end
  def flat_array(a, k = [])
    new_hash = {}
    a.each_index {|index|
      if a[index].is_a?(Hash)
        new_hash.merge!(flat_hash(a[index], k + [index + 1]))
      elsif a[index].is_a?(Array) then
        new_hash.merge!(flat_array(a[index], k + [index + 1]))
      else
        new_hash[(k + [index + 1]).join("_")] = a[index]
      end
    }
    return new_hash
  end
  def flat_hash2(hash, k = "")
    return {k => hash} unless hash.is_a?(Hash)
    hash.inject({}){ |h, v| h.merge! flat_hash(v[-1], k + [v[0]]) }
  end
  def g_gnum(word,land,minute,thing)
    h = GNum.create(word,land,minute)
    h["thing"] = thing
    return h
  end
  def g_gnum_simple(word,land,minute,thing)
    a = GNum.create_simple(word,land,minute)
    h = {}
    h["result"] = a
    h["thing"] = thing

    h["final_success"] = a != []

    return h
  end
  def g_name(surname,txtname,sex,s=nil,t=nil,lang=Cfate::DEFAULT_LANG)
    return Name.name(surname,txtname,sex,s,t,lang)
  end
  def g_namepair(surname,txtname,sex,pairfirstn,pairlastn,s=nil,t=nil,ps=nil,pt=nil,lang=Cfate::DEFAULT_LANG)
    return Name.pair(surname,txtname,sex,pairfirstn,pairlastn,s,t,ps,pt,lang)
  end
  def g_farmercal(hData)
    y = hData[Cfate::WYear]
    m = hData[Cfate::WMonth]
    d = hData[Cfate::WDate]
    return Xdate.farmercal(y,m,d)
  end
  def g_farmercal_jianggong(hData)
    h,h2 = g_farmercal_jianggong2(hData)
    # 十二神宜忌
    # h["shiershen_yi"] = h2["shiershen_yi"].join(Pm.GetStr("ifate.nongminli.dunhao")) # 宜
    # h["shiershen_ji"] = h2["shiershen_ji"].join(Pm.GetStr("ifate.nongminli.dunhao")) # 忌
    h["shiershen_yi"] = h2["yi"] # 宜
    h["shiershen_ji"] = h2["ji"] # 忌
    return h
  end
  def g_farmercal_jianggong2(hData)
    y = hData[Cfate::WYear]
    m = hData[Cfate::WMonth]
    d = hData[Cfate::WDate]
    h2 = Xdate.farmercal(y,m,d)
    h = {}
    nWYear, nWMonth,nWDate = h2["xili_year"],h2["xili_month"],h2["xili_day"]
    h[Cfate::WYear],h[Cfate::WMonth],h[Cfate::WDate] = nWYear, nWMonth,nWDate
    h["xili_date"] = h2["xili_date"]
    h["xili_date_simple"] = h2["xili_date_simple"]
    h["yinli_date"] = h2["yinli_date"]
    h["yinli_date_simple"] = h2["yinli_date_simple"]
    h["jieqili_date"] = h2["jieqili_date"]
    h["jieqi_ganzhi_year"] = h2["jieqi_ganzhi_year"]
    h["jieqi_ganzhi_month"] = h2["jieqi_ganzhi_month"]
    h["jieqi_ganzhi_day"] = h2["jieqi_ganzhi_day"]
    h["jieqi"] = h2["jieqi"]
    h["week_day"] = h2["week_day"]
    h["chong_sha"] = "#{h2["chong_shi_er_sheng_xiao"]}#{h2["chong_nian_ji"]}#{h2["chong_zuo_xiang"]}" # 沖煞
    h["tai_shen"] = h2["tai_shen"] # 胎神
    h["cai_shen"] = h2["cai_shen"] #財神
    h["xi_shen"] = h2["xi_shen"] # 喜神

    h["suici"] = h2["suici"] # 農曆歲次

    return h,h2
  end
  def g_farmercal_gpc(hData)
    h,h2 = g_farmercal_jianggong2(hData)
    a = []
    a.push(h2["tiande_yuede"].join(" ")) if h2["tiande_yuede"].join(" ") != ""
    a.push(h2["wu_tan_bing_day"]) if h2["wu_tan_bing_day"] != ""
    h["ji_shi"] = a.join(" ") # 紀事
    h["pengzu_bai_ji"] = h2["pengzu_bai_ji"] # 彭祖百忌
    h["jiu_xing"] = h2["jiu_xing"] # 九星
    h["ba_gua"] = h2["ba_gua"] # 八卦
    h["shi_er_shen"] = h2["shi_er_shen"] # 十二神 十二建星
    h["ershibaxingxiu"] = h2["ershibaxingxiu"] # 二十八星宿
    h["yi"] = h2["yi"] # 宜
    h["ji"] = h2["ji"] # 忌
    h["gi_shi"] = h2["gi_shi"].join(Pm.GetStr("ifate.nongminli.dunhao")) # 每日吉時
    h["xiong_shi"] = h2["xiong_shi"].join(Pm.GetStr("ifate.nongminli.dunhao")) # 每日凶時

    return h
  end
  def g_ganzhi(hData)
    y = hData[Cfate::WYear]
    m = hData[Cfate::WMonth]
    d = hData[Cfate::WDate]
    h = hData[Cfate::WHour]
    return Xdate.api_ganzhi_str(y,m,d,h)
  end

  def g_pick_days(hUserInfo,hUserDefData,hApFunc,hUserType,bDetail=false)
    n_pick_days = hUserDefData[Cfate::PickDays].to_i
    if (n_pick_days == 0) then
      n_pick_days = 1
    end
    n_pick_days = 1 if (n_pick_days.to_i < 1)
    n_pre_days = (n_pick_days / 2).to_i
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    @bClockWise = oStar.cp_CheckDirectionClock()
    @oScore = Score.new

    hUI = hUserInfo.clone
    hUI[Cfate::WHour] = 0
    hUI[Cfate::WMinute] = 0
    new_hash = Hash.new

    nWYear,nWMonth,nWDate,nWHour = hUI[Cfate::WYear],hUI[Cfate::WMonth],hUI[Cfate::WDate],hUI[Cfate::WHour]

    # 從輸入的日期之後幾天開始,沒輸入就是當天
    after_days = hUserDefData["after_days"].to_i
    nWYear,nWMonth,nWDate = Xdate.NextNWDate(nWYear,nWMonth,nWDate,after_days)
    # pd_start = hUserDefData["pd_start"].to_i

    hUI[Cfate::WYear],hUI[Cfate::WMonth],hUI[Cfate::WDate] = Xdate.PreWDates(nWYear,nWMonth,nWDate,n_pre_days)

    new_hash["title"] = g_pick_day_title(hUI,hUserDefData,hApFunc,hUserType,bDetail)
    new_hash["score"] = []
    (0...n_pick_days * 12).each do |i|
      new_hash["score"].push(g_pick_day_score(hUI,hUserDefData,hApFunc,hUserType,bDetail))

      nWYear,nWMonth,nWDate,nWHour = hUI[Cfate::WYear],hUI[Cfate::WMonth],hUI[Cfate::WDate],hUI[Cfate::WHour]
      nWYear,nWMonth,nWDate,nWHour = Xdate.NextNHour(nWYear,nWMonth,nWDate,nWHour,2)

      hUI[Cfate::WYear],hUI[Cfate::WMonth],hUI[Cfate::WDate],hUI[Cfate::WHour] = nWYear,nWMonth,nWDate,nWHour
    end
    return new_hash
  end
  def g_pick_day_title(hUserInfo,hUserDefData,hApFunc,hUserType,bDetail=false)
    new_hash = Hash.new
    h = g_pick_day(hUserInfo,hUserDefData,hApFunc,hUserType,bDetail)
    # return h["keys"]
    return h["names"]
  end
  def g_pick_day_score(hUserInfo,hUserDefData,hApFunc,hUserType,bDetail=false)
    new_hash = Hash.new
    h = g_pick_day(hUserInfo,hUserDefData,hApFunc,hUserType,bDetail)
    at = h["keys"]
    av = h["values"]
    at.each_index do |i|
      new_hash[at[i]] = av[i]
    end
    # new_hash["score"] = h["values"]
    new_hash["timestamp"] = make_timestampsex(hUserInfo)
    # new_hash = av
    return new_hash
  end
  def g_pick_day(hUserInfo,hUserDefData,hApFunc,hUserType,bDetail=false)
    nWYear = hUserInfo[Cfate::WYear]
    nWMonth = hUserInfo[Cfate::WMonth]
    nWDate = hUserInfo[Cfate::WDate]
    nWHour = hUserInfo[Cfate::WHour]
    nSex = hUserInfo[Cfate::Sex]
    sSex = nSex == false ? "male" : "female" #性別 true 是女性,false是男性

    # 看資料庫是否已經有此筆資料
    new_hash = gpd_db_find(nWYear,nWMonth,nWDate,nWHour,sSex)
    if (new_hash != nil) then
      return new_hash
    end

    new_hash = Hash.new

    # oScore = Score.new
    hScore = @oScore.ps_getScore(Pm::AP_STAR,Cfate::PAN_NORMAL,hUserInfo,hUserDefData,bDetail)
    at = Array.new
    ak = Array.new
    av = Array.new

    at.push(Pm.GetStr("pickdays.birthday.sun"))
    at.push(Pm.GetStr("pickdays.birthday.lunar"))
    # at.push(Pm.GetStr("pickdays.sex"))
    at.push(Pm.GetStr("pickdays.fortune"))

    av.push(Xdate.makeSunDateStr(nWYear,nWMonth,nWDate,nWHour,-1))
    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
    av.push(Xdate.makeLunarDateStr(nEYear,nEMonth,nEDate,false,nWHour))
    # av.push(Star.Sex(nSex))
    as_all = hScore["AllHouse"].values
    av.push(gpd_get_fortune_score(as_all,hUserInfo,hUserDefData,hUserType))

    at += hScore["AllHouse"].keys
    av += hScore["AllHouse"].values

    at.push("timestamp")
    ts = Xdate.make_timestamp_hash2(hUserInfo)
    av.push(ts)

    new_hash["keys"] = ["yan","yin","fortune","house1","house2","house3","house4","house5","house6","house7","house8","house9","house10","house11","house12","timestamp"]
    new_hash["names"] = at
    new_hash["values"] = av

    # 存入資料庫
    gpd_db_save(nWYear,nWMonth,nWDate,nWHour,sSex,new_hash)

    return new_hash
  end
  # 運勢：根據排盤時間之順逆，從命宮到遷移宮（7宮），有的命－＞兄⋯⋯；有的命─＞父⋯⋯
  # 1.算7宮換算之總分
  def gpd_get_fortune_score(as_all,hUserInfo,hUserDefData,hUserType)
    if (@bClockWise) then
      # +1
      as = as_all[0,1] + as_all[6,6].reverse
    else
      # -1
      as = as_all[0,7]
    end

    return gpd_calc_fortune(as)
  end
  def gpd_calc_fortune(as)
    s = 0
    as.each do |x| s = s + gpd_check_score(x) end
    return s
  end
  # 5分以上優，0-5分普通，負分弱
  # 依3個等級判斷，優5,普3,弱1分；
  def gpd_check_score(s)
    if (s >= 5) then
      return 5
    elsif (s >= 0 && s < 5) then
      return 3
    else
      return 1
    end
  end

  # 卜卦
  def g_pugua(up,down,change,year=nil,month=nil,day=nil,hour=nil,sname=nil,question=nil,gua_bie=nil,hParAll=nil,hApFunc=nil)
    new_hash = Hash.new
    oDivination = Divination.new(up,down,change,year,month,day,hour,sname,question,gua_bie,hParAll,hApFunc)
    new_hash["pugua_pan"] = oDivination.result()
    return new_hash
  end

  # 卜卦只有盤
  def g_pugua_pan(up,down,change,year=nil,month=nil,day=nil,hour=nil,sname=nil,question=nil,gua_bie=nil,hParAll=nil,hApFunc=nil)
    new_hash = Hash.new
    oDivination = Divination.new(up,down,change,year,month,day,hour,sname,question,gua_bie,hParAll,hApFunc)
    new_hash["pugua_pan"] = oDivination.result_pan()
    return new_hash
  end

  def gpd_db_find(nWYear,nWMonth,nWDate,nWHour,sSex)
    pick_day = PickDays.c_y(nWYear).c_m(nWMonth).c_d(nWDate).c_h(nWHour).c_s(sSex).first
    if (pick_day == nil) then
      return nil
    end
    h = pick_day.hAll
    if (h["keys"].index("timestamp") == nil) then
      h["keys"].push("timestamp")
      h["names"].push("timestamp")
      sex = Xdate.parse_sex(sSex)
      h["values"].push(make_timestampsex2(nWYear,nWMonth,nWDate,nWHour,!sex))
    end
    return h
  end

  def gpd_db_save(nWYear,nWMonth,nWDate,nWHour,sSex,new_hash)
    pick_day = PickDays.new
    pick_day.wyear = nWYear
    pick_day.wmonth = nWMonth
    pick_day.wday = nWDate
    pick_day.hour = nWHour
    pick_day.sex = sSex
    pick_day.hAll = new_hash
    pick_day.save!
  end

  def g_ganzhi_riqi(hData)
    y = hData[Cfate::WYear]
    m = hData[Cfate::WMonth]
    d = hData[Cfate::WDate]
    h = hData[Cfate::WHour]
    gan_zhis = hData["gan_zhis"]
    if (gan_zhis == nil) then
      gan_zhis = []
    end
    duixiang = hData["duixiang"]
    if (duixiang == nil) then
      duixiang = 'd'
    end
    count = hData["count"]
    if (count == nil) then
      count = 10
    end
    lifa = hData["lifa"]
    if (lifa == nil) then
      lifa = "jieqi"
    end

    return Xdate.api_ganzhi_riqi_str(y,m,d,h,gan_zhis,duixiang,count,lifa)
  end

  def g_wan_nian_li(hData)
    y = hData[Cfate::WYear]
    m = hData[Cfate::WMonth]
    d = hData[Cfate::WDate]
    # qian_ji_tian = hData["qian_ji_tian"]
    # hou_ji_tian = hData["hou_ji_tian"]
    # ji_tian_hou_kai_shi = hData["ji_tian_hou_kai_shi"]
    qian_hou_ji_tian = hData["qian_hou_ji_tian"]
    if (qian_hou_ji_tian == nil) then
      qian_hou_ji_tian = 7
    end
    qian_ji_tian = hou_ji_tian = qian_hou_ji_tian / 2

    return Xdate.api_wan_nian_li_str(y,m,d,qian_ji_tian,hou_ji_tian,qian_hou_ji_tian)
  end
  # 當天的時局 紫微局命紫，八字時順逆
  # def g_zi_wei_ju_ming_zi_ba_zi_shi_shun_ni(hData)
  def g_shi_ju(hData,hUserDefData)
    h = Hash.new
    # h3 = Hash.new []
    arr = Array.new

    nWYear = hData[Cfate::WYear]
    nWMonth = hData[Cfate::WMonth]
    nWDate = hData[Cfate::WDate]
    # h["shiju"] = Xdate.wan_nian_li_ri_ganzhi_str(nWYear, nWMonth,nWDate)
    h = Xdate.wan_nian_li_ri_ganzhi_str(nWYear, nWMonth,nWDate)
    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)

    (0...12).each do |shi|
      h2 = Hash.new
      hData[Cfate::WHour] = shi * 2
      hData[Cfate::Sex] = false # 男性
      h2["zi_wei_ju_ming_zi"] = g_shi_ju_zi_wei_ju_ming_zi(hData,hUserDefData)

      h2["ba_zi_shi_shun_ni"] = g_shi_ju_ba_zi_shi_shun_ni(hData,hUserDefData)

      h2["nong_min_li_chong_sha"] = g_shi_ju_nong_min_li_chong_sha(hData,hUserDefData)

      # 時辰吉凶數
      h2["shi_gixiong"] = g_nong_min_li_shi_gixiong(hData,hUserDefData)

      # 上昇星座
      h2["shang_sheng_xing_zuo"] = g_nong_min_li_shang_sheng_xing_zuo(hData,hUserDefData)

      h2["timestamp"] = make_timestampsex2(nWYear,nWMonth,nWDate,hData[Cfate::WHour],hData[Cfate::Sex])

      h2["ganzhi"] = Xdate.GetLunarTimeGanZhiStr2(nEYear,nEMonth,nEDate,shi,bLeapMonth,nil)

      # h["dizhi_#{shi + 1}"] = h2

      arr[shi] = h2
    end

    nWYear1,nWMonth1,nWDate1 = Xdate.PreWDates(nWYear,nWMonth,nWDate,1)
    timestamp = "%04d%02d%02d" % [nWYear1,nWMonth1,nWDate1]
    h["before"] = {"timestamp" => timestamp}
    nWYear1,nWMonth1,nWDate1 = Xdate.NextNWDate(nWYear,nWMonth,nWDate,1)
    timestamp = "%04d%02d%02d" % [nWYear1,nWMonth1,nWDate1]
    h["after"] = {"timestamp" => timestamp}

    h["dizhi"] = arr
    return h
  end
  def g_shi_ju_zi_wei_ju_ming_zi(hData,hUserDefData)
    ziwei = Star.new(hData,hUserDefData,nil)
    a = Array.new
    a[0] = ziwei.gPI_GetFiveType()[0]  # 五行局
    a[1] = ziwei.gPI_GetLifeHouse()
    a[2] = ziwei.gHouse_GetAStar_HouseEarthName(Cfate::PAN_NORMAL,1)
    return a
  end
  def g_shi_ju_ba_zi_shi_shun_ni(hData,hUserDefData)
    b = Array.new
    bazi = Eightword.new
    hData[Cfate::Sex] = false # 男性
    bazi.g_GetPanInfo(Cfate::PAN_NORMAL,hData,hUserDefData,nil,nil)
    bazi2 = Eightword.new
    hData[Cfate::Sex] = true # 女性
    bazi2.g_GetPanInfo(Cfate::PAN_NORMAL,hData,hUserDefData,nil,nil)
    b[0] = bazi.cp_mp_4c_getSky_Name(Eightword::ByHourIdx)  # 時
    if (bazi.cp_CheckDirectionClock()) then
      b1 = bazi.cp_fp_LargeWin_Start_YearOld(0) # 順
      b2 = bazi2.cp_fp_LargeWin_Start_YearOld(0) # 逆
    else
      b1 = bazi2.cp_fp_LargeWin_Start_YearOld(0) # 順
      b2 = bazi.cp_fp_LargeWin_Start_YearOld(0) # 逆
    end
    b[1] = WeenApi.get_guo_zhi_su_zhi(b1)
    b[2] = WeenApi.get_guo_zhi_su_zhi(b2)

    return b
  end
  def WeenApi.get_guo_zhi_su_zhi(n)
    s = Xdate.wan_nian_li_str("guo_zhi_su_zhi")  # "一,二,三,四,五,六,七,八,九,十,土"
    a = s.split(",")
    return a[n - 1]
  end
  def g_shi_ju_nong_min_li_chong_sha(hData,hUserDefData)
    nWYear = hData[Cfate::WYear]
    nWMonth = hData[Cfate::WMonth]
    nWDate = hData[Cfate::WDate]
    nWHour = hData[Cfate::WHour]
    return Xdate.api_nong_min_li_shi_ju_chong_sha(nWYear, nWMonth, nWDate, nWHour)
  end
  # 時辰吉凶數
  def g_nong_min_li_shi_gixiong(hData,hUserDefData)
    nWYear = hData[Cfate::WYear]
    nWMonth = hData[Cfate::WMonth]
    nWDate = hData[Cfate::WDate]
    nWHour = hData[Cfate::WHour]
    return Xdate.api_nong_min_li_shi_gixiong(nWYear, nWMonth, nWDate, nWHour)
  end
  # 上昇星座
  def g_nong_min_li_shang_sheng_xing_zuo(hData,hUserDefData)
    nWYear = hData[Cfate::WYear]
    nWMonth = hData[Cfate::WMonth]
    nWDate = hData[Cfate::WDate]
    nWHour = hData[Cfate::WHour]
    return Xdate.api_nong_min_li_shang_sheng_xing_zuo(nWYear,nWMonth,nWDate,nWHour)
  end
  def g_bazhi_info(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    hOut = Hash.new

    oEightword = Eightword.new
    hPan = oEightword.wa_Pan(Cfate::PAN_NORMAL,hUserInfo,hUserDefData,hUserType)
    hOut["pan"] = hPan

    new_hash = {}

    new_hash.merge!(flat_hash(hOut["pan"],[]))
    new_hash["timestamp"] = make_timestampsex(hUserInfo)

    return new_hash
  end

  # profate 使用各宮紫微論斷結果，包含文字，數值及刻應
  def g_profate_lunduan(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    hOut = Hash.new
    my_locale = hUserDefData["my_current_lang"]
    my_locale = Cfate::DEFAULT_LANG if my_locale == nil
    # 論斷
    oExplain = Explain.new
    # hExplain = oExplain.zwld_result("zh-TW",nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    hExplain = oExplain.zwld_result(my_locale,nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)

    # 提供趨勢圖用
    hfeeling = g_fe_score(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)

    # 依不同的四化提供吉凶分析
    oPanGiSuong = PanGiSuong.new
    hPanGiSuong,flow_title,gong = oPanGiSuong.zwld_sihua_explode(nPanType,hUserInfo,hUserDefData,hUserType)

    new_hash = {}
    nGiSuongHouseId = hUserDefData[Star::HOUSE_NAME]

    new_hash["pan"] = ziwei_pan(Cfate::PAN_NORMAL,hUserInfo,hUserDefData,hApFunc,hUserType)
    # 論斷說明
    new_hash["lunduan"] = g_profate_lunduan_final(nPanType,hPanGiSuong.clone,gong,hfeeling["explode"],hExplain,nGiSuongHouseId)
    # 趨勢圖
    new_hash["qushitu"] = g_profate_explode_final(nPanType,gong,hfeeling,hUserDefData)
    # new_hash["explain"] = hExplain
    # 警語
    new_hash["jingyu"] = Ziwei.shuoming_jingyu()

    new_hash["timestamp"] = make_timestampsex(hUserInfo)
    new_hash["name"] = hUserInfo[Cfate::Name]

    return new_hash
  end
  def userinfo_sex(h)
    return userinfo_sex2(h[Cfate::Sex])
  end
  def userinfo_sex2(s)
    return s ? "F" : "M"
  end
  def make_timestampsex(h)
    return Xdate.make_timesex_timestamp(h[Cfate::WYear],h[Cfate::WMonth],h[Cfate::WDate],h[Cfate::WHour],userinfo_sex(h))
  end
  def make_timestampsex2(y,m,d,h,s)
    return Xdate.make_timesex_timestamp(y,m,d,h,userinfo_sex2(s))
  end
  def g_profate_lunduan_gixiong_scores(hPanGiSuong)
    h = Hash.new
    h["gi"] = g_profate_lunduan_gixiong_scores_each(hPanGiSuong,"gi")
    h["xiong"] = g_profate_lunduan_gixiong_scores_each(hPanGiSuong,"xiong")
    return h
  end
  def g_profate_lunduan_gixiong_scores_each(hPanGiSuong,gixiong)
    h = Hash.new(0)
    hPanGiSuong[0][gixiong].keys.each do |key|
      (0..3).each do |nFourHuaIndex|
        h[key] += hPanGiSuong[nFourHuaIndex][gixiong][key]
      end
    end
    return h
  end
  def g_profate_lunduan_final(nPanType,hPanGiSuong,gong,hexplode,hExplain,nGiSuongHouseId)
    a = Array.new
    # if (nPanType == Cfate::PAN_NORMAL || nGiSuongHouseId == Star::EX_HOUSE_ALL) then
    if (nGiSuongHouseId == Star::EX_HOUSE_ALL) then
      (1..hExplain["count"]).each do |nHouse|
        h2 = Hash.new
        # "title": "2007～2016年 or  2015年 or 12月 or 7日""
        # h2["title"] = hExplain["date_title"]
        h2["gongming"] = Ziwei.id_name_gong(nHouse)
        h2["title"] = h2["gongming"]
        h2["taitou"] = hExplain["date_title"]
        h2["xing"] = g_profate_lunduan_final_xing(nPanType,hPanGiSuong,gong,hExplain["house_#{nHouse}"])
        h2["sihua"] = g_profate_lunduan_final_sihua(nPanType,hPanGiSuong,gong,hexplode,hExplain["house_#{nHouse}"],nGiSuongHouseId)
        a.push(h2)
      end
    else
      h = Hash.new
      h["title"] = hExplain["date_title"]
      h["gongming"] = Ziwei.nick_name_gong(gong)
      h["xing"] = g_profate_lunduan_final_xing(nPanType,hPanGiSuong,gong,hExplain)
      h["sihua"] = g_profate_lunduan_final_sihua(nPanType,hPanGiSuong,gong,hexplode,hExplain,nGiSuongHouseId)
      a.push(h)
    end
    return a
  end
  def g_profate_lunduan_final_xing(nPanType,hPanGiSuong,gong,hExplain)
    h = Hash.new
    if (nPanType <= Cfate::PAN_FLOWYEAR) then
      h["zhuyao"] = hExplain["explain"]["xing"]["axing"]
      h["zhuyao"]["taitou"] = Ziwei.shuoming_zhuyao_taitou()
      h["ciyao"] = hExplain["explain"]["xing"]["bxing"]
      h["ciyao"]["taitou"] = Ziwei.shuoming_ciyao_taitou()
      h["huanjingtezheng"] = hExplain["explain"]["xing"]["cxing"]
      h["huanjingtezheng"]["taitou"] = Ziwei.shuoming_huanjingtezheng_taitou_gong(gong)
    else
      h["zhuyao"] = hExplain["explain"]["xing"]["bxing"]
      h["zhuyao"]["taitou"] = Ziwei.shuoming_zhuyao_taitou()
      h["ciyao"] = hExplain["explain"]["xing"]["axing"]
      h["ciyao"]["taitou"] = Ziwei.shuoming_ciyao_taitou()
      h["huanjingtezheng"] = hExplain["explain"]["xing"]["cxing"]
      h["huanjingtezheng"]["taitou"] = Ziwei.shuoming_huanjingtezheng_taitou_gong(gong)
    end
    h["zhuyao"].delete("title")
    h["ciyao"].delete("title")
    h["huanjingtezheng"].delete("title")
    return h
  end
  def g_profate_lunduan_final_sihua(nPanType,hPanGiSuong,gong,hexplode,hExplain,nGiSuongHouseId)
    h = Hash.new
    (0..3).each do |nFourHuaIndex|
      h[Ziwei.sihua_nick(nFourHuaIndex)] = g_profate_lunduan_final_sihua_each(nPanType,hPanGiSuong[nFourHuaIndex],nFourHuaIndex,gong,hexplode,hExplain,nGiSuongHouseId)
    end
    return h
  end
  def g_profate_lunduan_final_sihua_each(nPanType,hPanGiSuong,nFourHuaIndex,gong,hexplode,hExplain,nGiSuongHouseId)
    h = Hash.new
    h["taitou"] = Ziwei.shuoming_sihua_taitou(Ziwei.sihua_nick(nFourHuaIndex))
    h["xianxiang"] = g_profate_lunduan_final_sihua_each_xianxiang(hPanGiSuong,nFourHuaIndex,hExplain,nGiSuongHouseId)
    h["shijian"] = g_profate_lunduan_final_sihua_each_shijian(nPanType,hPanGiSuong,nFourHuaIndex,gong,hexplode,nGiSuongHouseId)
    h["jilu"] = g_profate_lunduan_final_sihua_each_jilu(hPanGiSuong,nFourHuaIndex,hExplain)
    return h
  end
  # 預測時間點之現象說明：
  def g_profate_lunduan_final_sihua_each_xianxiang(hPanGiSuong,nFourHuaIndex,hExplain,nGiSuongHouseId)
    h = Hash.new
    if (nGiSuongHouseId == Star::EX_HOUSE_ALL) then
      h["taitou"] = Ziwei.shuoming_sihua_taitou("xianxiang_meiyougixiong")
    else
      h["taitou"] = Ziwei.shuoming_sihua_taitou("xianxiang")
    end
    h3 = hExplain["explain"]["sihua"][nFourHuaIndex + 1]["data"]
    h2 = Hash.new
    h2["diegong"] = h3["diegong"]
    h2["tiangan"] = h3["tiangan"]
    h["data"] = h2
    return h
  end
  # 預測發生時間點： (應期)：
  def g_profate_lunduan_final_sihua_each_shijian(nPanType,hPanGiSuong,nFourHuaIndex,gong,hexplode,nGiSuongHouseId)
    h = Hash.new
    if (nGiSuongHouseId == Star::EX_HOUSE_ALL) then
      h["taitou"] = ""
      h["gi"] = ""
      h["xiong"] = ""
    else
      h["taitou"] = Ziwei.shuoming_sihua_taitou("shijian")
      h["gi"] = g_profate_lunduan_final_sihua_each_shijian_gixiong(nPanType,hPanGiSuong["gi"],"gi",gong,hexplode)
      h["xiong"] = g_profate_lunduan_final_sihua_each_shijian_gixiong(nPanType,hPanGiSuong["xiong"],"xiong",gong,hexplode)
    end
    return h
  end
  def g_profate_lunduan_final_sihua_each_shijian_gixiong(nPanType,hIn,gixiong,gong,hexplode)
    h = {}
    if (hexplode == nil) then
      return h
    end
    hgx = hexplode[gixiong]
    keys = hIn.keys
    s = Ziwei.shuoming_yingqi_pt_gixiong_gong(nPanType,gixiong,gong)
    s += Ziwei.maohao()
    h["taitou"] = s

    a = Array.new
    keys.each do |key|
      if (hIn[key].abs > 0) then
        if (nPanType >= Cfate::PAN_FLOWDATE) then
          a.push({"time" => key, "scale" => hgx[key]})
        else
          # a.push({"time" => key.to_i, "scale" => hgx[key]})
          a.push({"time" => key, "scale" => hgx[key]})
        end
        # a.push("#{key}（#{Ziwei.shuoming_yingqi("zhishu")}#{hgx[key]}）")
      end
    end
    h["data"] = a
    return h
  end
  # 發生機率或說明：（自化說明）
  def g_profate_lunduan_final_sihua_each_jilu(hPanGiSuong,nFourHuaIndex,hExplain)
    h = Hash.new
    h["taitou"] = Ziwei.shuoming_sihua_taitou("jilu")
    h3 = hExplain["explain"]["sihua"][nFourHuaIndex + 1]["data"]
    h2 = Hash.new
    h2["zihua"] = h3["zihua"][0]
    h["data"] = h2
    return h
  end

  def g_profate_explode_final(nPanType,gong,hfeeling,hUserDefData)
    # if (nPanType == Cfate::PAN_NORMAL || hUserDefData[Star::HOUSE_NAME] == Star::EX_HOUSE_ALL) then
    if (hUserDefData[Star::HOUSE_NAME] == Star::EX_HOUSE_ALL) then
      return g_profate_explode_final_house_all(nPanType,gong,hfeeling)
    end
    if (hfeeling == {}) then
      return {}
    end
    h = Hash.new
    h.merge!(hfeeling)
    h2 = Hash.new
    h2["taitou"] = Ziwei.shuoming_sihua_taitou("shijian")
    h2["gi"] = g_profate_lunduan_final_sihua_each_shijian_gixiong(nPanType,hfeeling["explode"]["gi"],"gi",gong,hfeeling["explode"])
    h2["xiong"] = g_profate_lunduan_final_sihua_each_shijian_gixiong(nPanType,hfeeling["explode"]["xiong"],"xiong",gong,hfeeling["explode"])
    h["shijian"] = h2
    return h
  end
  def g_profate_explode_final_house_all(nPanType,gong,hfeeling)
    if (hfeeling == {}) then
      return {}
    end
    h = Hash.new
    h.merge!(hfeeling)
    h2 = Hash.new
    h2["taitou"] = ""
    h2["gi"] = ""
    h2["xiong"] = ""
    h["shijian"] = h2
    return h
  end
  def ziwei_api_result(hPars)
    neirong = hPars["neirong"] == nil ? "xing_lunduan" : hPars["neirong"]
    nPanType = hPars["pt"] == nil ? Cfate::PAN_NORMAL : hPars["pt"].to_i
    apFunc = nil
    parAll = nil

    if (neirong == "xing_lunduan") then
      return ziwei_api_result_xing_lunduan(nPanType,hPars)
    end
  end

  def ziwei_api_result_xing_lunduan(nPanType,hPars)
    xingstr = hPars["xing"] == nil ? "astar1" : hPars["xing"]
    gong = hPars["gong"] == nil ? 1 : hPars["gong"].to_i
    shengke = hPars["shengke"] == nil ? "sheng" : hPars["shengke"]
    hUserDefData = Hash.new
    hApFunc = Hash.new
    hUserType = Hash.new
    hParAll = nil

    new_hash = {}
    xings = xingstr.split(",")
    xings.each do |xing|
      ts = Xuexi_Ziwei.api_xl_find_timestamp(nPanType,xing,gong,shengke)
      hUserInfo = Xuexi_Ziwei.set_pan_user_info(ts)
      oStar = Star.new(hUserInfo,hUserDefData,hParAll)

      h = Hash.new
      h["pan"] = ziwei_pan(Cfate::PAN_NORMAL,hUserInfo,hUserDefData,hApFunc,hUserType)
      # 論斷說明
      h["lunduan"] = api_xing_in_gong_lunduan(oStar,nPanType,xing,gong)
      # 警語
      h["jingyu"] = Xuexi_Ziwei.api_xing_jingyu()
  
      h["timestamp"] = make_timestampsex(hUserInfo)
      h["name"] = hUserInfo[Cfate::Name]
  
      new_hash[xing] = h
    end
    new_hash["params"] = hPars

    return new_hash
  end

  # 星曜：天梁
  # 宮位：命宮
  # 宮氣生剋：甲午五行屬「金」，星曜生宮氣。
  # 基本特性：
  # 屬土，陽，化蔭，南斗第二星，化氣為「蔭」，主壽星，司壽之星、為福壽、主吉祥，能有解厄制化功能。
  # 運用特徵：
  # 「長壽」、「逢凶化吉」、「清高」、「專制」、「不受約束」、「醫藥」、「耿直」、「權謀」。
  # 
  # 演繹說明：
  # 您心性耿直開朗，自律性高，頗有老大的作風，可為人師表，喜歡用長者的口氣訓人，給人的感覺是沈著穩重有時會有專制或霸道的現象，為人清高，在思想上比同年齡的人來的成熟，不喜歡勞力，遇事都能逢凶化吉，您也有長壽之象。
  def api_xing_in_gong_lunduan(oStar,nPanType,xing,gong)
    h = {}
    # 星曜：天梁
    h["xingyao"] = Xuexi_Ziwei.api_xing_mingzi(oStar,nPanType,xing,gong)
    # 宮位：命宮
    h["gongwei"] = Xuexi_Ziwei.api_gongwei_mingzi(gong)
    # 宮氣生剋：甲午五行屬「金」，星曜生宮氣。
    h["gongqishengke"] = Xuexi_Ziwei.api_xing_gongqishenke(oStar,nPanType,xing,gong)
    # 基本特性：
    # 屬土，陽，化蔭，南斗第二星，化氣為「蔭」，主壽星，司壽之星、為福壽、主吉祥，能有解厄制化功能。
    h["jibentexing"] = Xuexi_Ziwei.api_xing_jibentexing(oStar,nPanType,xing,gong)
    # 運用特徵：
    h["yunyongtezheng"] = Xuexi_Ziwei.api_xing_yunyongtezheng(oStar,nPanType,xing,gong)
    # 演繹說明
    h["yanyishuoming"] = Xuexi_Ziwei.api_xing_in_gong_shuoming(oStar,nPanType,xing,gong)

    return h
  end

  # 火錘 排盤
  def huochui_ziwei_pan(nPanType,hUserInfo,hUserDefData,hApFunc,hUserType)
    if (hApFunc == nil) then
      hApFunc = Hash.new
    end
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    hOut = Hash.new

    oStar = Star.new
    hPan = oStar.wa_Pan_for_huochui(nPanType,hUserInfo,hUserDefData,hUserType)

    hOut["pan"] = hPan

    new_hash = {}

    new_hash.merge!(flat_hash(hOut["pan"],[]))
    new_hash["timestamp"] = make_timestampsex(hUserInfo)

    # result.pan.middle_personal_info_pi_ganzhibirth_2 輸出內容的前面加入 八字：
    # result.pan.middle_personal_info_ci_data2 輸出內容本來是 "雲端命盤"，改成"ween.tw"
    # result.pan.middle_personal_info_ci_data3 輸出內容本來是"http://www.profate.com.tw" 改成 "<EMAIL>"
    new_hash["middle_personal_info_pi_solarbirth_2"] = ppi_only_data(new_hash["middle_personal_info_pi_solarbirth_2"])
    new_hash["middle_personal_info_pi_lunarbirth_2"] = ppi_only_data(new_hash["middle_personal_info_pi_lunarbirth_2"])
    new_hash["middle_personal_info_pi_ganzhibirth_1"] = "#{Ziwei.pan_zhongjian_bazhi_biaotou()}#{Ziwei.maohao()}"
    # new_hash["middle_personal_info_pi_ganzhibirth_2"] = "#{Ziwei.pan_zhongjian_bazhi_biaotou()}#{Ziwei.maohao()}#{new_hash["middle_personal_info_pi_ganzhibirth_2"]}"
    new_hash["middle_personal_info_ci_data2"] = "#{Cfate::SERVER_URL}"
    new_hash["middle_personal_info_ci_data3"] = "support@#{Cfate::SERVER_URL}"

    return new_hash
  end

  # gpc
  def gpc_name(surname,txtname,sex,s=[],t=[],lang=Cfate::DEFAULT_LANG)
    result = Name.gpc_name(surname,txtname,sex,s,t,lang)
    result["final_success"] = !result["nameerr"]
    return result    
  end
  def gpc_namepair(surname,txtname,sex,pairfirstn,pairlastn,s=[],t=[],ps=[],pt=[],lang=Cfate::DEFAULT_LANG)
    result = Name.gpc_pair(surname,txtname,sex,pairfirstn,pairlastn,s,t,ps,pt,lang)
    result["final_success"] = !result["nameerr"]
    return result    
  end

end
