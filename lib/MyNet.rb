class MyNet
  def MyNet.my_post(sHttp,data)
    if (sHttp.length > 0) then
      uri = URI.parse(sHttp)
      if (sHttp.include?("https://")) then
        res = MyNet.post_https(uri,data)
      else
        res = MyNet.post_http(uri,data)
      end
    else
      # render :layout => false
      res = nil
    end
    return res
  end
  def MyNet.post_http(uri,data)
    res = Net::HTTP.post_form(uri,data)
    # puts "res.header => #{res.header}"
    # puts "res.body => #{res.body}"
    return res
  end
  def MyNet.post_https(uri,data)
    # uri = URI.parse("https://secure.com/")
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    http.verify_mode = OpenSSL::SSL::VERIFY_NONE
    # http.verify_mode = OpenSSL::SSL::VERIFY_PEER
    
    # request = Net::HTTP::Get.new(uri.request_uri)
    request = Net::HTTP::Post.new(uri.request_uri)
    request.set_form_data(data)

    response = http.request(request)
    # response.body
    # response.status
    # response["header-here"] # All headers are lowercase
    return response
  end
end
