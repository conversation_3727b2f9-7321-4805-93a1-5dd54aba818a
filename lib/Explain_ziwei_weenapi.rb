require("Star.rb")
require("Ziwei.rb")

class Explain
  def zwld_result(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut = Hash.new
    @lang = sLang
    @m_nPanType = nPanType
    @UserInfo = hUserInfo.clone
    @ApFunc = hApFunc.clone
    @ParAll_sihua = hParAll.clone
    # 小限起命宮 四化
    # @ParAll_sihua[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN
    @ParAll_sihua[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN_FLOW_SKY
    # 四化依本命
    @ParAll_sihua[Star::PAR_FLOWYEAR_HUA] = Star::PAN_YEAR_HUA_FLOW
    @Star_sihua = Star.new(hUserInfo,hUserAskData,@ParAll_sihua)
    # 官祿宮流年之星曜解釋，要依地支年為主，目前是以小限年為主，四化則為正確。
    # 地支年起命宮 星性說明
    @ParAll_xing = hParAll.clone
    @ParAll_xing[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_BIRTH
    # 四化依本命
    @ParAll_xing[Star::PAR_FLOWYEAR_HUA] = Star::PAN_YEAR_HUA_FLOW
    @Star = Star.new(hUserInfo,hUserAskData,@ParAll_xing)

    # @StarInfo = @Star.g_GetStarPanInfo(nPanType,hUserInfo,hUserAskData,@ParAll)
    @UserAskData = hUserAskData.clone
    @ExplainHouse = @UserAskData[Star::HOUSE_NAME]
    if (@ExplainHouse == nil) then
      @ExplainHouse = Star::EX_HOUSE_ALL
    end
    # @FourK = FourKData.new
    # @FourOut = FourOutData.new
    # @TzMain = TzMainData.new

    # if (nPanType == Cfate::PAN_NORMAL) then
    #   @hOut = zwld_result_normal(@Star,@Star_sihua,nPanType)
    # else
      if (@ExplainHouse == Star::EX_HOUSE_ALL) then
        @hOut = zwld_allhouse(@Star,@Star_sihua,nPanType)
      else
        if (nPanType == Cfate::PAN_NORMAL) then
          @hOut = zwld_result_normal_onehouse(@Star,@Star_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,@ParAll)
        elsif (nPanType == Cfate::PAN_TENYEAR) then
          @hOut = zwld_result_tenyear(@Star,@Star_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,@ParAll)
        elsif (nPanType == Cfate::PAN_FLOWYEAR) then
          @hOut = zwld_result_flowyear(@Star,@Star_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,@ParAll)
        elsif (nPanType == Cfate::PAN_FLOWMONTH) then
          @hOut = zwld_result_flowmonth(@Star,@Star_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,@ParAll)
        elsif (nPanType == Cfate::PAN_FLOWDATE) then
          @hOut = zwld_result_flowdate(@Star,@Star_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,@ParAll)
        elsif (nPanType == Cfate::PAN_FLOWTIME) then
          @hOut = zwld_result_flowtime(@Star,@Star_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,@ParAll)
        elsif (nPanType == Cfate::PAN_FLOWMIN) then
          @hOut = zwld_result_flowmin(@Star,@Star_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,@ParAll)
        end
      end
    # end
    # @hOut["date_title"] = zwld_result_date_title(@Star,nPanType)
    @hOut["date_title"] = zwld_result_taitou(nPanType,@Star,@ExplainHouse)
    return @hOut
  end
  def zwld_result_date_title(oStar,nPanType)
    return oStar.gWeen_GetFlowTimeStr(nPanType)
  end

  def zwld_result_normal(oStar,oStar_sihua,nPanType)
    @ExplainHouse = Star::EX_HOUSE_ALL

    @hOut = zwld_allhouse(oStar,oStar_sihua,nPanType)

    return @hOut
  end
  # 輸出論斷增加內容，消費者清楚了解問的項目。
  # 姓名： 王大明 性別：男
  # ＝》出生日期：國曆 1971 年 12月 29 日14 時10 分 農曆 辛亥 年 11 月 12 日 未 時
  # ＝》財帛命盤分析時間：44 -- 53歲（農曆：2006 -- 2015年）
  # ＝》財帛命盤分析時間：46歲 丙申年 2016年
  # ＝》財帛命盤分析時間：46歲 丙申年一月 2016年2月8日～3月8日
  # ＝》財帛命盤分析時間：46歲 丙申年1/6（乙丑日） 2016年2月13日

  # 柏翰要這樣才好寫
  # "name": "AAA",
  # "gender": "女",
  # "birth_s": "國曆1999年9月9日9時9分",
  # "birth_l": /"農曆己卯年7月30日巳時",
  # "target": "16 -- 25歲(農曆：2014 -- 2023年)"
  def zwld_result_taitou(nPanType,oStar,nExplainHouse)
    h = {}
    # 姓名： 王大明 性別：男
    # h["name"] = zwld_result_taitou_xingming(oStar)
    # ＝》出生日期：國曆 1971 年 12月 29 日14 時10 分 農曆 辛亥 年 11 月 12 日 未 時
    # h["birth"] = zwld_result_taitou_chusheng(nPanType,oStar)
    # ＝》財帛命盤分析時間：44 -- 53歲（農曆：2006 -- 2015年）
    # ＝》各宮命盤分析時間：44 -- 53歲（農曆：2006 -- 2015年）
    # h["target"] = zwld_result_taitou_shijian(nPanType,oStar,nExplainHouse)

    h["name"] = oStar.uig_Name()
    h["gender"] = oStar.uig_bS_str()
    h["birth_s"],h["birth_l"] = zwld_result_taitou_chusheng(nPanType,oStar)
    h["target"] = zwld_result_taitou_shijian(nPanType,oStar,nExplainHouse)

    return h
  end
  # 姓名： 王大明 性別：男
  def zwld_result_taitou_xingming(oStar)
    s1 = Pm.GetStr("ziwei.shuoming.taitou.xingming") + oStar.uig_Name()
    s2 = Pm.GetStr("ziwei.shuoming.taitou.xingbie") + oStar.uig_bS_str()
    return s1 + Cfate.GetSpace(2) + s2
  end
  # ＝》出生日期：國曆 1971 年 12月 29 日14 時10 分 農曆 辛亥 年 11 月 12 日 未 時
  def zwld_result_taitou_chusheng(nPanType,oStar)
    nWYear, nWMonth,nWDate, nWHour = oStar.uig_W_YMDH()
    nETime = oStar.uig_ET()
    nWMinute = oStar.uig_WMI()
    s1 = Xdate.GetFullWDateTimeStr2(nWYear,nWMonth,nWDate,nWHour,nWMinute,Cfate::PAN_DISPLAY_WEST)
    # s1 += Cfate.GetSpace(1)
    s2 = Xdate.GetLunar8Words_Str4(nWYear, nWMonth,nWDate, nETime)
    return s1,s2
  end
  # ＝》財帛命盤分析時間：44 -- 53歲（農曆：2006 -- 2015年）
  # ＝》財帛命盤分析時間：46歲 丙申年 2016年
  # ＝》財帛命盤分析時間：46歲 丙申年一月 2016年2月8日～3月8日
  # ＝》財帛命盤分析時間：46歲 丙申年1/6（乙丑日） 2016年2月13日
  def zwld_result_taitou_shijian(nPanType,oStar,nExplainHouse)
    if (nPanType == Cfate::PAN_NORMAL) then
        return Pm.GetStr("ziwei.shuoming.taitou.shijian.benming")
    else
      # ＝》各宮命盤分析時間：44 -- 53歲（農曆：2006 -- 2015年）
      if (nExplainHouse == Star::EX_HOUSE_ALL) then
        # ＝》各宮命盤分析時間：
        s1 = Pm.GetStr("ziwei.shuoming.taitou.shijian.all")
        # 44 -- 53歲（農曆：2006 -- 2015年）
        s2 = oStar.gWeen_GetLunduanTaitou(nPanType)
      else
        # ＝》財帛命盤分析時間：
        gong = Ziwei.gong_nick(nExplainHouse)
        s1 = Pm.GetStr("ziwei.shuoming.taitou.shijian.#{gong}")
        # 44 -- 53歲（農曆：2006 -- 2015年）
        s2 = oStar.gWeen_GetLunduanTaitou(nPanType)
      end
      return s1 + s2
    end
  end
  # 本命盤：則各宮顯示說明(十二宮分析)
  def zwld_allhouse(oStar,oStar_sihua,nPanType)
    hExplain = Hash.new
    # nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType)
    hExplain["count"] = 12
    # 1..12 表示命宮到父母宮
    (1..12).each do |nHouseId|
      hData = Hash.new
      # hData["housename"] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
      hData["housename"] = Star.GetHouseNameWithQuota(nHouseId)

      # 文字定義
      hData["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
      hData["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{nHouseId}")
      # 說明部分
      # hData["explain"] = zwld_gong_xing_onehouse(oStar,nPanType,nEarth)
      hData["explain"] = zwld_gong_xing_onehouse(oStar,nPanType,oStar_sihua,nHouseId)

      hExplain["house_#{nHouseId}"] = hData
      # 尋找下一個宮名
      # nEarth = oStar.gHouse_GetNextHouseEarth(nEarth)
    end

    return hExplain
  end

  def zwld_gong_xing_onehouse(oStar,nPanType,oStar_sihua,nHouseId)
    hExplain = Hash.new
    # 星性顯示部份：
    hExplain["xing"] = zwld_gong_xing(oStar,nPanType,nHouseId)
    # hExplain["xing"] = zwld_gong_xing(oStar,nPanType,nEarth)
    # 四化部份：
    hExplain["sihua"] = zwld_gong_xing_sihua(oStar_sihua,nPanType,nHouseId)

    return hExplain
  end

  def zwld_gong_xing_sihua(oStar_sihua,nPanType,nHouseId)
    nEarth = oStar_sihua.cp_getHouseIdEarth(nPanType,nHouseId)
    hExplain = Hash.new
    (Star::FH_LU..Star::FH_GI).each do |nFourHuaIndex|
      hData = Hash.new
      hData["title"] = Pm.GetStr("explain.star.title.fourhua_#{nFourHuaIndex}")

      hData["data"] = zwld_gong_sihua_lunduan(oStar_sihua,nPanType,nEarth,nFourHuaIndex)

      hExplain[nFourHuaIndex + 1] = hData
    end
    hExplain["count"] = 4
    return hExplain
  end

  def zwld_gong_sihua_lunduan(oStar_sihua,nPanType,nEarth,sihua_idx)
    hData = Hash.new
    wenshigong_id = oStar_sihua.g_House_GetHouseId(nPanType,nEarth)
    huarugong_id = oStar_sihua.gHouse_GetFourOutHouseId(nPanType,nEarth,sihua_idx)
    benmin_gong_id = oStar_sihua.gHouse_GetFourOutNormalHouseId(nPanType,nEarth,sihua_idx)

    # diegong + tiangan = xianxiang
    # 財帛#,,遷移,,夫妻,,祿,, 看起來是從 ziwei_sihua_caibo.zh-TW.yml 取得
    # sihua diegong(四化疊宮)
    hData["diegong"] = Ziwei.gong_sihua_diegong_lunduan(nPanType,wenshigong_id,huarugong_id,benmin_gong_id,sihua_idx)

    # 1,,丙,,夫妻,,祿,, ？？？
    # sihua_tiangan(四化天干)
    sihua_gong_gan_id = oStar_sihua.gHouse_GetSky(nPanType,nEarth)
    hData["tiangan"] = Ziwei.gong_sihua_tiangan_lunduan(nPanType,wenshigong_id,sihua_gong_gan_id,huarugong_id,sihua_idx)

    # 發生機率或說明：（自化說明）（#夫妻#,,祿,,權,,科,,）？？？
      # 2016/1/18 jieshou_sihua_gong_earth 改為 問事宮地支 nEarth
      # 在飛入之宮裡找先天四化及自化
    jieshou_sihua_gong_earth = oStar_sihua.gHouse_GetFourOutHouseEarth(nPanType,nEarth,sihua_idx)
    xiantiansihua = oStar_sihua.gExHouse_Find_OrgSky_FourHuaHouse_inHouse(nPanType,jieshou_sihua_gong_earth)
    aaFourHua,nFourHuaCount = oStar_sihua.gHouse_GetAStarsSelfFourHua(nPanType,jieshou_sihua_gong_earth)
    jieshou_sihua_gong_zihua = zwld_zihua_check(aaFourHua,nFourHuaCount)
    # 2016/1/18 jieshou_sihua_gong_id 改為 wenshigong_id
    # jieshou_sihua_gong_id = huarugong_id
    # hData["zihua"] = Ziwei.gong_sihua_zihua_lunduan(nPanType,jieshou_sihua_gong_id,sihua_idx,xiantiansihua,jieshou_sihua_gong_zihua)
    # 感受現象 zihua
    hData["zihua"] = Ziwei.gong_sihua_zihua_lunduan(nPanType,wenshigong_id,sihua_idx,xiantiansihua,jieshou_sihua_gong_zihua)
    return hData
  end
  def zwld_zihua_check(aaFourHua,nFourHuaCount)
    aOut = Array.new
    aaFourHua.each do |aFourHua|
      if (aFourHua[0] != 99) then
        aOut.push(aFourHua[0])
      end
    end
    return aOut
  end
  # 星性顯示部份：
  # def zwld_gong_xing(oStar,nPanType,nEarth)
  def zwld_gong_xing(oStar,nPanType,nHouseId)
    nEarth = oStar.cp_getHouseIdEarth(nPanType,nHouseId)
    hExplain = Hash.new
    hExplainA = Hash.new
    hExplainB = Hash.new

    shuangxingOut,astarOut,bstarOut,dstarOut,sstarOut,ystarOut,gstarOut,house_id,house_wu_xing = zwld_gong_shuangxing_check(oStar,nPanType,nEarth)
    if (nPanType < Cfate::PAN_FLOWMONTH) then
      # 主要特徵現象
      hExplainA["title"] = Pm.GetStr("explain.star.title.main")
      # 主星解釋
      hExplainA["data"] = zwld_gong_xing_main(shuangxingOut,astarOut,nPanType,house_id,house_wu_xing)
      hExplain["axing"] = hExplainA
      # 次要特徵現象
      hExplainB["title"] = Pm.GetStr("explain.star.title.sub")
      # 其他星解釋
      hExplainB["data"] = zwld_gong_xing_sub(shuangxingOut,bstarOut,nPanType,house_id,house_wu_xing)
      hExplain["bxing"] = hExplainB

      hExplainC = Hash.new
      # 環境特徵
      hExplainC["title"] = Pm.GetStr("explain.star.title.env")
      # 十二長生解釋
      # 博士十二星解釋
      # 將前星解釋
      # 歲前星解釋
      hExplainC["data"] = zwld_gong_xing_c(shuangxingOut,dstarOut,sstarOut,ystarOut,gstarOut,nPanType,house_id,house_wu_xing)
      hExplain["cxing"] = hExplainC
    elsif (nPanType >= Cfate::PAN_FLOWMONTH) then
      # 主要特徵現象
      hExplainA["title"] = Pm.GetStr("explain.star.title.main")
      # 主星解釋
      hExplainA["data"] = zwld_gong_xing_sub(shuangxingOut,bstarOut,nPanType,house_id,house_wu_xing)
      hExplain["axing"] = hExplainA
      # 次要特徵現象
      hExplainB["title"] = Pm.GetStr("explain.star.title.sub")
      # 其他星解釋
      hExplainB["data"] = zwld_gong_xing_main(shuangxingOut,astarOut,nPanType,house_id,house_wu_xing)
      hExplain["bxing"] = hExplainB

      hExplainC = Hash.new
      # 環境特徵
      hExplainC["title"] = Pm.GetStr("explain.star.title.env")
      # 十二長生解釋
      # 博士十二星解釋
      # 將前星解釋
      # 歲前星解釋
      hExplainC["data"] = zwld_gong_xing_c(shuangxingOut,dstarOut,sstarOut,ystarOut,gstarOut,nPanType,house_id,house_wu_xing)
      hExplain["cxing"] = hExplainC
    end
    return hExplain
  end
  # 雙星判斷
  def zwld_gong_shuangxing_check(oStar,nPanType,nEarth)
    aStars,bUseOpp = oStar.gHouse_GetAStars(nPanType,nEarth)
    bStars = oStar.gHouse_GetBStars(nPanType,nEarth)
    dStar = oStar.gHouse_GetCStar(nPanType,nEarth,Star::DOCTOR)
    sStar = oStar.gHouse_GetCStar(nPanType,nEarth,Star::YEARSTAR)
    yStar = oStar.gHouse_GetCStar(nPanType,nEarth,Star::YEARGOD)
    gStar = oStar.gHouse_GetCStar(nPanType,nEarth,Star::GOD)

    astar_nicks = Ziwei.danxing_a_nicks(aStars)
    bstar_nicks = Ziwei.danxing_b_nicks(bStars)
    dstar_nick = Ziwei.danxing_d_nick(dStar)
    sstar_nick = Ziwei.danxing_s_nick(sStar)
    ystar_nick = Ziwei.danxing_y_nick(yStar)
    gstar_nick = Ziwei.danxing_g_nick(gStar)

    star_nicks = astar_nicks
    star_nicks += bstar_nicks
    star_nicks.push(dstar_nick)
    star_nicks.push(sstar_nick)
    star_nicks.push(ystar_nick)
    star_nicks.push(gstar_nick)

    house_id = oStar.g_House_GetHouseId(nPanType,nEarth)
    house_wu_xing = oStar.gHouse_Five(nPanType,nEarth)
    shuangxingOut,danxingOut = Ziwei.shuangxing_check(star_nicks,nPanType,house_id,house_wu_xing)

    astarOut = astar_nicks & danxingOut
    bstarOut = bstar_nicks & danxingOut
    dstarOut = danxingOut.include?(dstar_nick) ? dstar_nick : ""
    sstarOut = danxingOut.include?(sstar_nick) ? sstar_nick : ""
    ystarOut = danxingOut.include?(ystar_nick) ? ystar_nick : ""
    gstarOut = danxingOut.include?(gstar_nick) ? gstar_nick : ""

    return shuangxingOut,astarOut,bstarOut,dstarOut,sstarOut,ystarOut,gstarOut,house_id,house_wu_xing
  end
  def zwld_gong_xing_main(shuangxingOut,astarOut,nPanType,house_id,house_wu_xing)
    ashuangxing = zwld_gong_xing_main_shuangxing(shuangxingOut,nPanType,house_id,house_wu_xing)
    adanxing = zwld_gong_xing_main_danxing_a(astarOut,nPanType,house_id,house_wu_xing)
    ashuangxing += adanxing
    return ashuangxing
  end
  def zwld_gong_xing_main_shuangxing(shuangxingOut,nPanType,house_id,house_wu_xing)
    aOut = Array.new
    shuangxingOut.each do |a|
      b = Ziwei.shuangxing_dengji(a)
      if ([Ziwei::ShuangXing_MA,Ziwei::ShuangXing_A].include?(b[0])) then
        h = Hash.new
        h["name"] = Ziwei.nick_name_shuangxing(a)
        h["explain"] = Ziwei.gong_shuangxing_lunduan(nPanType,house_id,a,house_wu_xing)
        aOut.push(h)
      end
    end

    return aOut
  end
  def zwld_gong_xing_main_danxing_a(astarOut,nPanType,house_id,house_wu_xing)
    aOut = Array.new
    astarOut.each do |a|
      h = Hash.new
      h["name"] = Ziwei.nick_name_danxing_a(a)
      h["explain"] = Ziwei.gong_danxing_lunduan(nPanType,house_id,a,house_wu_xing)
      aOut.push(h)
    end
    return aOut
  end
  def zwld_gong_xing_sub(shuangxingOut,bstarOut,nPanType,house_id,house_wu_xing)
    ashuangxing = zwld_gong_xing_sub_shuangxing(shuangxingOut,nPanType,house_id,house_wu_xing)
    adanxing = zwld_gong_xing_sub_danxing_b(bstarOut,nPanType,house_id,house_wu_xing)
    ashuangxing += adanxing
    return ashuangxing
  end
  def zwld_gong_xing_sub_shuangxing(shuangxingOut,nPanType,house_id,house_wu_xing)
    aOut = Array.new
    shuangxingOut.each do |a|
      b = Ziwei.shuangxing_dengji(a)
      if ([Ziwei::ShuangXing_B].include?(b[0])) then
        h = Hash.new
        h["name"] = Ziwei.nick_name_shuangxing(a)
        h["explain"] = Ziwei.gong_shuangxing_lunduan(nPanType,house_id,a,house_wu_xing)
        aOut.push(h)
      end
    end
    return aOut
  end
  def zwld_gong_xing_sub_danxing_b(bstarOut,nPanType,house_id,house_wu_xing)
    aOut = Array.new
    bstarOut.each do |a|
      h = Hash.new
      h["name"] = Ziwei.nick_name_danxing_b(a)
      h["explain"] = Ziwei.gong_danxing_lunduan(nPanType,house_id,a,house_wu_xing)
      aOut.push(h)
    end
    return aOut
  end

  # 在環境特徵：星曜排列順序調整，1為十二長生g，2為博士十二星d，3為歲前星y，4為將前星s
  def zwld_gong_xing_c(shuangxingOut,dstarOut,sstarOut,ystarOut,gstarOut,nPanType,house_id,house_wu_xing)
    aOut = Array.new

    shuangxingOut.each do |a|
      b = Ziwei.shuangxing_dengji(a)
      if ([Ziwei::ShuangXing_DSYG].include?(b[0])) then
        h = Hash.new
        h["name"] = Ziwei.nick_name_shuangxing(a)
        h["explain"] = Ziwei.gong_shuangxing_lunduan(nPanType,house_id,a,house_wu_xing)
        aOut.push(h)
      end
    end

    if (dstarOut != "") then
      h = Hash.new
      h["name"] = Ziwei.nick_name_danxing_d(dstarOut)
      h["explain"] = Ziwei.gong_danxing_lunduan(nPanType,house_id,dstarOut,house_wu_xing)
      aOut.push(h)
    end

    if (sstarOut != "") then
      h = Hash.new
      h["name"] = Ziwei.nick_name_danxing_s(sstarOut)
      h["explain"] = Ziwei.gong_danxing_lunduan(nPanType,house_id,sstarOut,house_wu_xing)
      aOut.push(h)
    end

    if (ystarOut != "") then
      h = Hash.new
      h["name"] = Ziwei.nick_name_danxing_y(ystarOut)
      h["explain"] = Ziwei.gong_danxing_lunduan(nPanType,house_id,ystarOut,house_wu_xing)
      aOut.push(h)
    end

    if (gstarOut != "") then
      h = Hash.new
      h["name"] = Ziwei.nick_name_danxing_g(gstarOut)
      h["explain"] = Ziwei.gong_danxing_lunduan(nPanType,house_id,gstarOut,house_wu_xing)
      aOut.push(h)
    end

    return aOut
  end

  def zwld_gong_xing_main_old(shuangxingOut,astarOut,nPanType,house_id,house_wu_xing)
    ashuangxing = zwld_gong_xing_main_shuangxing(shuangxingOut,nPanType,house_id,house_wu_xing)
    adanxing = zwld_gong_xing_main_danxing_a(astarOut,nPanType,house_id,house_wu_xing)
    ashuangxing["name"] += adanxing["name"]
    ashuangxing["explain"] += adanxing["explain"]
    return ashuangxing
  end
  def zwld_gong_xing_main_shuangxing_old(shuangxingOut,nPanType,house_id,house_wu_xing)
    h = Hash.new
    aname = Array.new
    aexplain = Array.new
    shuangxingOut.each do |a|
      b = Ziwei.shuangxing_dengji(a)
      if ([Ziwei::ShuangXing_MA,Ziwei::ShuangXing_A].include?(b[0])) then
        aname.push(Ziwei.nick_name_shuangxing(a))
        aexplain.push(Ziwei.gong_shuangxing_lunduan(nPanType,house_id,a,house_wu_xing))
      end
    end
    h["name"] = aname
    h["explain"] = aexplain
    return h
  end
  def zwld_gong_xing_main_danxing_a_old(astarOut,nPanType,house_id,house_wu_xing)
    h = Hash.new
    aname = Array.new
    aexplain = Array.new
    astarOut.each do |a|
      aname.push(Ziwei.nick_name_danxing_a(a))
      aexplain.push(Ziwei.gong_danxing_lunduan(nPanType,house_id,a,house_wu_xing))
    end
    h["name"] = aname
    h["explain"] = aexplain
    return h
  end

  def zwld_gong_xing_sub_old(shuangxingOut,bstarOut,nPanType,house_id,house_wu_xing)
    ashuangxing = zwld_gong_xing_sub_shuangxing(shuangxingOut,nPanType,house_id,house_wu_xing)
    adanxing = zwld_gong_xing_sub_danxing_b(bstarOut,nPanType,house_id,house_wu_xing)
    ashuangxing["name"] += adanxing["name"]
    ashuangxing["explain"] += adanxing["explain"]
    return ashuangxing
  end
  def zwld_gong_xing_sub_shuangxing_old(shuangxingOut,nPanType,house_id,house_wu_xing)
    h = Hash.new
    aname = Array.new
    aexplain = Array.new
    shuangxingOut.each do |a|
      b = Ziwei.shuangxing_dengji(a)
      if ([Ziwei::ShuangXing_B].include?(b[0])) then
        aname.push(Ziwei.nick_name_shuangxing(a))
        aexplain.push(Ziwei.gong_shuangxing_lunduan(nPanType,house_id,a,house_wu_xing))
      end
    end
    h["name"] = aname
    h["explain"] = aexplain
    return h
  end
  def zwld_gong_xing_sub_danxing_b_old(bstarOut,nPanType,house_id,house_wu_xing)
    h = Hash.new
    aname = Array.new
    aexplain = Array.new
    bstarOut.each do |a|
      aname.push(Ziwei.nick_name_danxing_b(a))
      aexplain.push(Ziwei.gong_danxing_lunduan(nPanType,house_id,a,house_wu_xing))
    end
    h["name"] = aname
    h["explain"] = aexplain
    return h
  end

  def zwld_gong_xing_c_old(shuangxingOut,dstarOut,sstarOut,ystarOut,gstarOut,nPanType,house_id,house_wu_xing)
    h = Hash.new
    aname = Array.new
    aexplain = Array.new

    if (gstarOut != "") then
      aname.push(Ziwei.nick_name_danxing_g(gstarOut))
      aexplain.push(Ziwei.gong_danxing_lunduan(nPanType,house_id,gstarOut,house_wu_xing))
    end

    if (dstarOut != "") then
      aname.push(Ziwei.nick_name_danxing_d(dstarOut))
      aexplain.push(Ziwei.gong_danxing_lunduan(nPanType,house_id,dstarOut,house_wu_xing))
    end

    if (ystarOut != "") then
      aname.push(Ziwei.nick_name_danxing_y(ystarOut))
      aexplain.push(Ziwei.gong_danxing_lunduan(nPanType,house_id,ystarOut,house_wu_xing))
    end

    if (sstarOut != "") then
      aname.push(Ziwei.nick_name_danxing_s(sstarOut))
      aexplain.push(Ziwei.gong_danxing_lunduan(nPanType,house_id,sstarOut,house_wu_xing))
    end

    shuangxingOut.each do |a|
      b = Ziwei.shuangxing_dengji(a)
      if ([Ziwei::ShuangXing_DSYG].include?(b[0])) then
        aname.push(Ziwei.nick_name_shuangxing(a))
        aexplain.push(Ziwei.gong_shuangxing_lunduan(nPanType,house_id,a,house_wu_xing))
      end
    end

    h["name"] = aname
    h["explain"] = aexplain
    return h
  end
  def zwld_result_normal_onehouse(oStar,oStar_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut["count"] = 12
    # 文字定義
    @hOut["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
    @hOut["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{@ExplainHouse}")
    sHouseName = Star.GetHouseName(@ExplainHouse.to_s)
    nEarth = oStar.g_House_FindEarth(nPanType,sHouseName)
    @hOut["housename"] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)

    nHouseId = oStar.g_House_GetHouseId(nPanType,nEarth)

    # 1、顯示該流運十年之事業宮之資料顯示，如「頁面顯示說明」。
    @hOut["explain"] = zwld_gong_xing_onehouse(oStar,nPanType,oStar_sihua,nHouseId)

    # nNextPanType = nPanType + 1

    # # 2、再顯示該十年流運之十年之事業宮資料。
    # #取得十年大限開始歲數
    # nStarYear = oStar.g_getLargeSanStartYear()
    # nYearOld = oStar.cp_getLargeSanStartYearOld()
    # (0..9).each do |i|
    #   hExplain = Hash.new
    #   hUserAskData[Cfate::EYear] = nStarYear + i
    #   hExplain["explain"] = zwld_result_nextpantype_onehouse(sLang,nNextPanType,hUserInfo,hUserAskData,hApFunc,hParAll,@ExplainHouse)
    #   hExplain["yearold"] = nYearOld + i
    #   hExplain["eyear"] = nStarYear + i
    #   hExplain["description_title"] = Xdate.GetYearStr(hUserAskData[Cfate::EYear])
    #   @hOut["period_#{i + 1}"] = hExplain
    # end

    return @hOut
  end

  def zwld_result_tenyear(oStar,oStar_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut["count"] = 10
    # 文字定義
    @hOut["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
    @hOut["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{@ExplainHouse}")
    sHouseName = Star.GetHouseName(@ExplainHouse.to_s)
    nEarth = oStar.g_House_FindEarth(nPanType,sHouseName)
    @hOut["housename"] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)

    nHouseId = oStar.g_House_GetHouseId(nPanType,nEarth)

    # 1、顯示該流運十年之事業宮之資料顯示，如「頁面顯示說明」。
    @hOut["explain"] = zwld_gong_xing_onehouse(oStar,nPanType,oStar_sihua,nHouseId)

    # nNextPanType = nPanType + 1

    # # 2、再顯示該十年流運之十年之事業宮資料。
    # #取得十年大限開始歲數
    # nStarYear = oStar.g_getLargeSanStartYear()
    # nYearOld = oStar.cp_getLargeSanStartYearOld()
    # (0..9).each do |i|
    #   hExplain = Hash.new
    #   hUserAskData[Cfate::EYear] = nStarYear + i
    #   hExplain["explain"] = zwld_result_nextpantype_onehouse(sLang,nNextPanType,hUserInfo,hUserAskData,hApFunc,hParAll,@ExplainHouse)
    #   hExplain["yearold"] = nYearOld + i
    #   hExplain["eyear"] = nStarYear + i
    #   hExplain["description_title"] = Xdate.GetYearStr(hUserAskData[Cfate::EYear])
    #   @hOut["period_#{i + 1}"] = hExplain
    # end

    return @hOut
  end
  def zwld_result_nextpantype_onehouse(sLang,nNextPanType,hUserInfo,hUserAskData,hApFunc,hParAll,nExplainHouse)
    oStar_sihua = Star.new(hUserInfo,hUserAskData,@ParAll_sihua)
    oStar = Star.new(hUserInfo,hUserAskData,@ParAll_xing)

    return zwld_create_onehouse(oStar,oStar_sihua,nNextPanType,nExplainHouse)
  end
  # 各宮顯示說明(單一宮分析)
  def zwld_create_onehouse(oStar,oStar_sihua,nPanType,nExplainHouse)
    sHouseName = Star.GetHouseName(nExplainHouse.to_s)
    nEarth = oStar.g_House_FindEarth(nPanType,sHouseName)
    nHouseId = oStar.g_House_GetHouseId(nPanType,nEarth)
    # 說明部分
    return zwld_gong_xing_onehouse(oStar,nPanType,oStar_sihua,nHouseId)
  end

  def zwld_result_flowyear(oStar,oStar_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut["count"] = 12
    # 文字定義
    @hOut["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
    @hOut["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{@ExplainHouse}")
    sHouseName = Star.GetHouseName(@ExplainHouse.to_s)
    nEarth = oStar.g_House_FindEarth(nPanType,sHouseName)
    @hOut["housename"] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)

    nHouseId = oStar.g_House_GetHouseId(nPanType,nEarth)

    # 1、顯示該流運年之事業宮之資料顯示，如「頁面顯示說明」。
    @hOut["explain"] = zwld_gong_xing_onehouse(oStar,nPanType,oStar_sihua,nHouseId)

    # nNextPanType = nPanType + 1
    # # 2、再顯示該年流運之十二月之事業宮資料。
    # mArray = Xdate.GetEastMonthArray(hUserAskData[Cfate::EYear])
    # mArray.each do |aMonth|
    #   hUserAskData[Cfate::EMonth] = aMonth[0]
    #   hUserAskData[Cfate::LeapMonth] = aMonth[2]
    #   hExplain = Hash.new
    #   hExplain["explain"] = zwld_result_nextpantype_onehouse(sLang,nNextPanType,hUserInfo,hUserAskData,hApFunc,hParAll,@ExplainHouse)
    #   hExplain["description_title"] = Xdate.GetYearStr(hUserAskData[Cfate::EYear])
    #   hExplain["description_title"] += Xdate.GetMonthStr(hUserAskData[Cfate::EMonth],hUserAskData[Cfate::LeapMonth])
    #   @hOut[aMonth[0]] = hExplain
    # end

    return @hOut
  end

  def zwld_result_flowmonth(oStar,oStar_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut["count"] = Xdate.GetEastMonthDays(hUserAskData[Cfate::EYear], hUserAskData[Cfate::EMonth], hUserAskData[Cfate::LeapMonth])
    # 文字定義
    @hOut["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
    @hOut["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{@ExplainHouse}")
    sHouseName = Star.GetHouseName(@ExplainHouse.to_s)
    nEarth = oStar.g_House_FindEarth(nPanType,sHouseName)
    @hOut["housename"] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)

    nHouseId = oStar.g_House_GetHouseId(nPanType,nEarth)

    # 1、顯示該流運月之事業宮之資料顯示，如「頁面顯示說明」。
    @hOut["explain"] = zwld_gong_xing_onehouse(oStar,nPanType,oStar_sihua,nHouseId)

    # nNextPanType = nPanType + 1
    # # 2、再顯示該月流運之日之事業宮資料。
    # (1..@hOut["count"].to_i).each do |i|
    #   hExplain = Hash.new
    #   hUserAskData[Cfate::EDate] = i
    # hExplain["explain"] = zwld_result_nextpantype_onehouse(sLang,nNextPanType,hUserInfo,hUserAskData,hApFunc,hParAll,@ExplainHouse)
    # hExplain["description_title"] = hUserAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
    #   hExplain["description_title"] += hUserAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")
    #   hExplain["description_title"] += hUserAskData[Cfate::EDate].to_s + Pm.GetStr("IDS_X_DAY")
    # @hOut[i] = hExplain
    # end

    return @hOut
  end

  def zwld_result_flowdate(oStar,oStar_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut["count"] = 12
    # 文字定義
    @hOut["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
    @hOut["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{@ExplainHouse}")
    sHouseName = Star.GetHouseName(@ExplainHouse.to_s)
    nEarth = oStar.g_House_FindEarth(nPanType,sHouseName)
    @hOut["housename"] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)

    nHouseId = oStar.g_House_GetHouseId(nPanType,nEarth)

    # 1、顯示該流運日之事業宮之資料顯示，如「頁面顯示說明」。
    @hOut["explain"] = zwld_gong_xing_onehouse(oStar,nPanType,oStar_sihua,nHouseId)

    # nNextPanType = nPanType + 1
    # # 2、再顯示該年日運之十二時辰之事業宮資料。
    # (1..@hOut["count"].to_i).each do |i|
    #   hExplain = Hash.new
    #   hUserAskData[Cfate::WHour] = Xdate.ETime2Hour(i - 1)
    # hExplain["explain"] = zwld_result_nextpantype_onehouse(sLang,nNextPanType,hUserInfo,hUserAskData,hApFunc,hParAll,@ExplainHouse)
    # hExplain["description_title"] = hUserAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
    #   hExplain["description_title"] += hUserAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")
    #   hExplain["description_title"] += hUserAskData[Cfate::EDate].to_s + Pm.GetStr("IDS_X_DAY")
    #   hExplain["description_title"] += Earth.GetName(i) + Pm.GetStr("IDS_X_HOUR")
    # @hOut[i] = hExplain
    # end

    return @hOut
  end

  def zwld_result_flowtime(oStar,oStar_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut["count"] = 12
    # 文字定義
    @hOut["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
    @hOut["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{@ExplainHouse}")
    sHouseName = Star.GetHouseName(@ExplainHouse.to_s)
    nEarth = oStar.g_House_FindEarth(nPanType,sHouseName)
    @hOut["housename"] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)

    nHouseId = oStar.g_House_GetHouseId(nPanType,nEarth)

    # 1、顯示該流運日之事業宮之資料顯示，如「頁面顯示說明」。
    @hOut["explain"] = zwld_gong_xing_onehouse(oStar,nPanType,oStar_sihua,nHouseId)

    return @hOut
  end

  def zwld_result_flowmin(oStar,oStar_sihua,sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut["count"] = 12
    # 文字定義
    @hOut["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
    @hOut["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{@ExplainHouse}")
    sHouseName = Star.GetHouseName(@ExplainHouse.to_s)
    nEarth = oStar.g_House_FindEarth(nPanType,sHouseName)
    @hOut["housename"] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)

    nHouseId = oStar.g_House_GetHouseId(nPanType,nEarth)

    # 1、顯示該流運日之事業宮之資料顯示，如「頁面顯示說明」。
    @hOut["explain"] = zwld_gong_xing_onehouse(oStar,nPanType,oStar_sihua,nHouseId)
    return @hOut
  end


end
