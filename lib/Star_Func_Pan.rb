require("SkyEarthFive_Function.rb")

class Star
  def GetCurTime(remote_ip)
    y,m,d,h,mi,s = Xdate.GetCurTime(remote_ip)

    cds_W_YMDH(y,m,d,h)
    cds_WMI(mi)
    cds_ET(Xdate.Hour2ETime(cdg_WH()))
    cds_EMI(Xdate.GetPanMinute(cdg_WH(),mi))

    y,m,d,l = Xdate.West2East(cdg_WY(),cdg_WM(),cdg_WD())
    cds_E_YMDL(y,m,d,l)
    y,m,d,l,t = Xdate.NextEDateFromTime(cdg_EY(), cdg_EM(), cdg_ED(), cdg_EL(), cdg_ET())
    cds_E_YMDLT(y,m,d,l,t)
  end

  def CheckUserAskDate()
#puts("1ddg_EY():#{ddg_EY()}")
    if ddg_EY() != nil then
      y = Xdate.getLegalYear(ddg_EY())
      dds_EY(y)
    elsif ddg_WY() != nil then
      y = Xdate.getLegalYear(ddg_WY())
      dds_WY(y)
    end
    if (Xdate.IsYearLegal?(ddg_EY())) then
      y,m,d,l,t = ddg_E_YMDLT()
      y,m,d,l = Xdate.GetLegalEDate(y,m,d,l)
      dds_E_YMDLT(y,m,d,l,t)
      y,m,d = Xdate.East2West(y,m,d,l)
      dds_WY(y)
      dds_WM(m)
      dds_WD(d)
      # dds_WH(Xdate.ETime2Hour(ddg_ET()))
      dds_ET(Xdate.Hour2ETime(ddg_WH()))
      dds_EMI(Xdate.GetPanMinute(ddg_WH(),ddg_WMI()))
      #dds_WMI(Xdate.GetPanWMinute(ddg_EMI()))
    elsif (Xdate.IsYearLegal?(ddg_WY())) then
      y,m,d,l = Xdate.West2East(ddg_WY(),ddg_WM(),ddg_WD())
      dds_EY(y)
      dds_EM(m)
      dds_ED(d)
      dds_EL(d)
      dds_ET(Xdate.Hour2ETime(ddg_WH()))
      dds_EMI(Xdate.GetPanMinute(ddg_WH(),ddg_WMI()))
    end
    y,m,d,l,t = ddg_E_YMDLT()
    y,m,d,l,t = Xdate.NextEDateFromTime(y,m,d,l,t)
    dds_E_YMDLT(y,m,d,l,t)
#puts("2ddg_EY():#{ddg_EY()}")
  end
# 改成Array of PAN_CNT
  def setFlowTimeInfo(nPanType)
    if (nPanType < Cfate::PAN_TENYEAR) then
      y,m,d,l,t = uig_E_YMDLT()
      fds_E_YMDLT(nPanType,y,m,d,l,t)
      nELeapMonth = cp_GetBirthMonth()
      fds_ELM(nPanType,nELeapMonth)

      y,m,d,h = uig_W_YMDH()
      fds_W_YMD(nPanType,y,m,d)
      fds_WH(nPanType,h)
      fds_EY(nPanType,(ddg_EY() == nil) ? cdg_EY() : ddg_EY())
    else
      if (@par_PanTimeBase == Star::PAN_TIME_BASE_NOW) then
        nEYear,nEMonth,nEDate,bLeapMonth,nETime = cdg_E_YMDLT()
        nELeapMonth = nEMonth
        nEMin = cdg_EMI()
        nWYear,nWMonth,nWDate,nWHour = cdg_W_YMDH()
      elsif (@par_PanTimeBase == Star::PAN_TIME_BASE_BIRTH) then
        nEYear,nEMonth,nEDate,bLeapMonth,nETime = uig_E_YMDLT()
        nELeapMonth = cp_GetBirthMonth()
        nEMin = Xdate.GetPanMinute(uig_WH(),0) #uig_WMI())
        nWYear,nWMonth,nWDate,nWHour = uig_W_YMDH()
      else  #PAN_TIME_BASE_DEFINE
        nEYear,nEMonth,nEDate,bLeapMonth,nETime = pdg_E_YMDLT()
        nELeapMonth = nEMonth
        nEMin = Xdate.GetPanMinute(pdg_WH(),pdg_WMI())
        nWYear,nWMonth,nWDate,nWHour = pdg_W_YMDH()
      end
#puts("3fdg_EY():#{fdg_EY(nPanType)},ddg_EY():#{ddg_EY()},nEYear:#{nEYear}")

      fds_EY(nPanType,(ddg_EY() == nil) ? nEYear : ddg_EY())
      fds_EM(nPanType,(ddg_EM() == nil) ? nEMonth : ddg_EM())
      fds_EL(nPanType,(ddg_EL() == nil) ? bLeapMonth : ddg_EL())
      fds_ED(nPanType,(ddg_ED() == nil) ? nEDate : ddg_ED())
      fds_ET(nPanType,(ddg_ET() == nil) ? nETime : ddg_ET())
      fds_WH(nPanType,(ddg_WH() == nil) ? nWHour : ddg_WH())
      fds_EMI(nPanType,(ddg_EMI() == nil) ? nEMin : ddg_EMI())
#puts("4fdg_EY():#{fdg_EY()},ddg_EY():#{ddg_EY()},nEYear:#{nEYear}")

      if (nPanType == Cfate::PAN_TENYEAR) then
        # fds_E_YMDLT(nPanType,fdg_EY2(nPanType),nEMonth,nEDate,bLeapMonth,nETime)
        fds_E_YMDLT(nPanType,fdg_EY2(nPanType),fdg_EM2(nPanType),fdg_ED2(nPanType),fdg_EL2(nPanType),nETime)
        fds_ELM(nPanType,nELeapMonth)
      elsif (nPanType == Cfate::PAN_FLOWYEAR) then
        # fds_E_YMDLT(nPanType,fdg_EY2(nPanType),nEMonth,nEDate,bLeapMonth,nETime)
        fds_E_YMDLT(nPanType,fdg_EY2(nPanType),fdg_EM2(nPanType),fdg_ED2(nPanType),fdg_EL2(nPanType),nETime)
        fds_ELM(nPanType,nELeapMonth) # 流年以上受䦌月影響
      elsif (nPanType == Cfate::PAN_FLOWMONTH) then
        # fds_E_YMDLT(nPanType,fdg_EY2(nPanType),fdg_EM2(nPanType),nEDate,fdg_EL2(nPanType),nETime)
        # 2020/9/28修改
        fds_E_YMDLT(nPanType,fdg_EY2(nPanType),fdg_EM2(nPanType),fdg_ED2(nPanType),fdg_EL2(nPanType),nETime)
        fds_ELM(nPanType,fdg_EM2(nPanType))  # 流月以下不受䦌月影響
      elsif (nPanType == Cfate::PAN_FLOWDATE) then
        fds_ELM(nPanType,fdg_EM2(nPanType))
        fds_ET(nPanType,nETime)
        fds_EMI(nPanType,nEMin)
      elsif (nPanType == Cfate::PAN_FLOWTIME) then
        fds_ELM(nPanType,fdg_EM2(nPanType))
        nWHour = fdg_WH2(nPanType)
        fds_EMI(nPanType,nEMin)
      elsif (nPanType >= Cfate::PAN_FLOWMIN) then
        fds_ELM(nPanType,fdg_EM2(nPanType))
        nWHour = fdg_WH2(nPanType)
      end

      # 日期有可能錯誤,要確認一下
      y,m,d,l = fdg_E_YMDL2(nPanType)
#puts("1y:#{y},m:#{m},d:#{d}")

      y,m,d,l = Xdate.GetLegalEDate(y,m,d,l)
#puts("2y:#{y},m:#{m},d:#{d}")
      #if (nPanType >= Cfate::PAN_FLOWDATE) then
      fds_E_YMDL(nPanType,y,m,d,l)
      #end

#puts("1y:#{y},m:#{m},d:#{d}")
      # y,m,d,l = fdg_E_YMDL(nPanType)
      y,m,d = Xdate.East2West(y,m,d,l)
#puts("2y:#{y},m:#{m},d:#{d}")
      fds_W_YMD(nPanType,y,m,d)
      fds_WH(nPanType,nWHour)
    end
  end

  # 計算個人中宮(本命)資訊
  #---------------------------------------------------------------------
  #計算本命命宮位置(以地支表示,0表示寅宮)
  def cp_getLife()
    if (Earth.Exist?(@StarInfo[U_LIFE])) then
      return @StarInfo[U_LIFE]
    end


      if (par_check_LeapType()) then
          @StarInfo[U_LIFE] = uig_EM() - uig_ET()
      else
          @StarInfo[U_LIFE] = uig_EM() - uig_ET() - 1
      end

      @StarInfo[U_LIFE] = Earth.Modify(@StarInfo[U_LIFE])

      return @StarInfo[U_LIFE]
  end

  #---------------------------------------------------------------------
  #計算本命子年斗君位置
  def cp_getGod()
    if (Earth.Exist?(@StarInfo[U_GOD])) then
      return @StarInfo[U_GOD]
    end
    nBirthMonth = cp_GetBirthMonth() # uig_EM()

    @StarInfo[U_GOD] = uig_ET() - nBirthMonth + 1
    @StarInfo[U_GOD] = Earth.Modify(@StarInfo[U_GOD])

    return @StarInfo[U_GOD]
  end

  #---------------------------------------------------------------------
  #計算本命身宮所在地支位置,0表示寅宮
  def cp_getBody()
    if (Earth.Exist?(@StarInfo[U_BODY])) then
      return @StarInfo[U_BODY]
    end

    if (par_check_LeapType()) then
        @StarInfo[U_BODY] = uig_EM() + uig_ET()
    else
        @StarInfo[U_BODY] = uig_EM() + uig_ET() - 1
    end

    @StarInfo[U_BODY] = Earth.Modify(@StarInfo[U_BODY])

    return @StarInfo[U_BODY]
  end

  def cp_getLifeEarthIndex()
    if (@par_PanBase == Star::PAN_BASE_LIFE) then
      n = cp_getLife()  # 本命命宮所在地支位置
    else
      n = cp_getBody()  # 本命身宮所在地支位置
    end
    return n
  end

  #---------------------------------------------------------------------
  #計算本命天干
  def cp_getSkyIndex()
    if (Sky.Exist?(@StarInfo[U_SKY])) then
      return @StarInfo[U_SKY]
    end

     @StarInfo[U_SKY] = Sky.GetYearSkyIndex(uig_EY())

     return @StarInfo[U_SKY]
  end

  #---------------------------------------------------------------------
  #計算本命地支
  def cp_getEarthIndex()
    if (Earth.Exist?(@StarInfo[U_EARTH])) then
      return @StarInfo[U_EARTH]
    end

     @StarInfo[U_EARTH] = Earth.GetYearEarthIndex(uig_EY())

     return @StarInfo[U_EARTH]
  end

  def cp_GetnSex()
    uis_nSIY(cp_getSkyIndex() % 2)
  end

  #---------------------------------------------------------------------
  #計算本命五行局
  def cp_getFive()
    if (Five.Exist?(@StarInfo[U_FIVE] - 2)) then
      return @StarInfo[U_FIVE]
    end

    i = cp_getLifeEarthIndex() + 2
    i = Earth.Modify(i)
    i = i >> 1

    nSkyIndex = cp_getSkyIndex()
    j = nSkyIndex % 5

    @StarInfo[U_FIVE] = @@ByFive[i * 5 + j]

    return @StarInfo[U_FIVE]
  end

  def cp_GetSmallPos()
    # 起小限規則
    if (@par_SmallSanType == Star::PAN_SSAN_TRAD) then
      # 傳統排法(預設值)
      n = cp_getEarthIndex()
      if (n == 0 || n == 4 || n == 8) then
        k = 8
      elsif (n == 1 || n == 5 || n == 9) then
        k = 5
      elsif (n == 2 || n == 6 || n == 10) then
        k = 2
      elsif (n == 3 || n == 7 || n == 11) then
        k = 11
      end
    else
      # 生年地支起小限
      k = cp_getEarthIndex()
      k = Earth.Modify(k - 2)
    end
  end

  #*** 大運起始位置之起始值
  def cp_GetLargePos()
    return cp_getFive()
  end

  def cp_CheckDirectionClock()
    #陽男,陰女順行
    bSex = uig_bS()
    nSex = uig_nSIY()
    if ((nSex == 0 && bSex == false) ||    #陽男
        (nSex == 1 && bSex == true)) then  #陰女
        return true
    else #陰男,陽女逆行
      return false
    end
  end

  # 「傳統排法」男順女逆
  # 施老師排盤「生年生年地支起小限」是施老師用法，男女不順逆排，全部只有順排。
  def cp_CheckSmallDirectionClock()
    bRet = ((uig_bS() == false) || (@par_SmallSanType == Star::PAN_SSAN_BIRTH_EARTH))
    return bRet
  end

  #取得大小限
  def cp_GetLargeSmall()
    j = cp_getLifeEarthIndex()
    m = cp_GetLargePos()
    k = cp_GetSmallPos()

    (1..12).each do |i|
      @StarInfo[Star::U_LARGE][j] = m
      j = Earth.Modify(cp_CheckDirectionClock() ? j + 1 : j - 1)
      m += 10

      @StarInfo[U_SMALL][k] = i
      k = Earth.Modify((cp_CheckSmallDirectionClock())? k + 1 : k - 1)
    end
  end

  def SmallSanExist?()
    return (@StarInfo[U_SMALL][0] != nil)
  end

  def LargeSanExist?()
    return (Earth.Exist?(@m_nLargeSan))
  end

  def cp_GetLargeSan(nFlowAge,nPanType=Cfate::PAN_TENYEAR)
    if (LargeSanExist?()) then
      return @m_nLargeSan
    end

    @m_nLargeSan = -1
    nAge = nFlowAge
    if (nFlowAge == 0) then
      nAge = cp_GetFlowYearOld(nPanType)
    end
#puts("cp_GetLargeSan nAge:#{nAge}, fdg_EY:#{fdg_EY()}")

    i = 0
    n = 200
    smallest = 0
    while ((@m_nLargeSan == -1) && (i < 12))
      # m = @StarInfo[Star::U_LARGE][i]
      m = cp_getHouseIndexLarge(i)
      if (n > m) then
        n = m
        smallest = i
      end
      if ((nAge >= m && nAge < m + 10) ||
        (nAge >= 120 + m && nAge < m + 130)) then
        @m_nLargeSan = i
      end
      i += 1
    end
    if (@m_nLargeSan == -1) then
      @m_nLargeSan = smallest
    end
    return @m_nLargeSan
  end

  # 各宮資訊

  # 三方四正
  def g_Check34House(nEarth,nFlyOutEarth)
    if (nEarth == nFlyOutEarth) then
      return HOUSE_34_SELF
    elsif (nFlyOutEarth == Earth.ModifyEarth(nEarth + 6)) then
      return HOUSE_34_OPP
    elsif (nFlyOutEarth == Earth.ModifyEarth(nEarth + 4)) then
      return HOUSE_34_1
    elsif (nFlyOutEarth == Earth.ModifyEarth(nEarth + 8)) then
      return HOUSE_34_2
    end
    return HOUSE_34_NONE
  end

  def cp_getLargeSanStartYear()
    nFlowAge = cp_getLargeSanStartYearOld()
    return cp_YearOld2FlowYear(nFlowAge)
  end
  def cp_getLargeSanStartYearOld()
    nLargeSan = cp_GetLargeSan(0)
    nFlowAge = cp_getHouseIndexLarge(nLargeSan)
    return nFlowAge
  end

  def cp_YearOld2FlowYear(nFlowAge)
      return (nFlowAge + uig_EY() - 1)

  end

  # 大限起始年
  def cp_getHouseLarge(nEarth=nil)
    if (nEarth == nil) then
      return cp_getHouseIndexLarge()
    else
      return cp_getHouseIndexLarge(Star.Earth2HouseIndex(nEarth))
    end
  end

  def cp_getHouseIndexLarge(nHouseIndex=nil)
#puts("@StarInfo[Star::U_LARGE]:#{@StarInfo[Star::U_LARGE]}")
    if (@StarInfo[Star::U_LARGE][0] == nil) then
      cp_GetLargeSmall()  #取得大小限
    end

    if (nHouseIndex == nil) then
      return @StarInfo[Star::U_LARGE]
    else
      return @StarInfo[Star::U_LARGE][nHouseIndex]
    end
  end

  # 小限起始年
  def cp_getHouseSmall(nEarth)
    return @StarInfo[Star::U_SMALL][Star.Earth2HouseIndex(nEarth)]
  end

  # 宮名
  def cp_getHouseName(nPanType,nEarth)
    # if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::HOUSE_NAME] == nil) then
    #   cp_HouseName(nPanType)
    # end
    # a = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::HOUSE_NAME]
    # return a[0]
    nHouseId = cp_getHouse(nPanType,nEarth)
    return Star.GetHouseName(nHouseId)
  end

  def cp_GetBodyEarth()
    nHouseIndex = cp_getBody()
    nEarth = Star.HouseIndex2Earth(nHouseIndex)
    return nEarth
  end
  def cp_getHouse(nPanType,nEarth)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::HOUSE_NAME] == nil) then
      cp_HouseName(nPanType)
    end
    a = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::HOUSE_NAME]
    nHouseId = a[2]

    # 2021/5/20 加入多胞胎宮位切換功能，單胞胎沒變
    nEarth2 = cp_GetBodyEarth()
    b = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth2)][Star::HOUSE_NAME]
    nBody_HouseId = b[2]
    nHouseId3 = uig_mb_change_houseid(nHouseId,nBody_HouseId)
    return nHouseId3
  end

  def cp_test(nPanType)
      # Pm.saveTestDb("allpaninfo","#{@AllPanInfo[nPanType]}")
  end
  def cp_getHouseEarth(nPanType,sHouseName)
    (1..12).each do |nEarth|
      if (sHouseName == cp_getHouseName(nPanType,nEarth)) then
        return nEarth
      end
    end
    return nil
  end

  def cp_getFirstHouseEarth(nPanType)
    (1..12).each do |nEarth|
      if (1 == cp_getHouse(nPanType,nEarth)) then
        return nEarth
      end
    end
    return nil
  end
  def cp_getHouseIdEarth(nPanType,nHouseId)
    (1..12).each do |nEarth|
      if (nHouseId == cp_getHouse(nPanType,nEarth)) then
        return nEarth
      end
    end
    return nil
  end

  def cp_GetHouseSmallSan(nHouseIndex)
    if (!SmallSanExist?()) then
      cp_GetLargeSmall()
    end
    return @StarInfo[U_SMALL][nHouseIndex]
  end

  def cp_getHouseSky(nPanType,nEarth)
    nDisPanType = Star.Get3ParPanType(@par_FlowSkyType,nPanType)
    if (@AllPanInfo[nDisPanType][Earth.GetKeyName(nEarth)][Star::HOUSE_SKY] == nil) then
      cp_HouseSky(nDisPanType)
    end
    nSky = @AllPanInfo[nDisPanType][Earth.GetKeyName(nEarth)][Star::HOUSE_SKY]
    return nSky
  end

  def cp_getHouseSkyIndex(nPanType,nEarth)
    # nHouseIndex = Star.Earth2HouseIndex(nEarth)
    # return cp_get_Pan_HouseSkyIndex(nPanType,nHouseIndex)
    nSky = cp_getHouseSky(nPanType,nEarth)
    return Sky.Sky2SkyIndex(nSky)
    end

  def cp_getHouseFive(nPanType,nEarth)
    # if (@par_FixSky) then
      # n = cp_getHouseSkyIndex(Cfate::PAN_NORMAL,nEarth)
    # else
      n = cp_getHouseSkyIndex(nPanType,nEarth)
    # end
    nHouseIndex = Star.Earth2HouseIndex(nEarth)
    m = Earth.Modify(nHouseIndex + 2)

        n >>= 1
        m >>= 1
        if (m >= 3) then
            m -= 3
        end
        nFive = GetBy60Five(n,m) + 1
        return nFive
    end

    #  失敗的函式
  def cp_getHouseFive_Fail(nPanType,nEarth)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)
    i = nEarthIndex >> 1

    nSkyIndex = cp_getHouseSkyIndex(nPanType,nEarth)
    j = nSkyIndex % 5

    return (@@ByFive[i * 5 + j] - 1)
  end

  def g_IsMainAStar(nStar)
    return nStar.between?(1,14)
  end

  def get_HouseAStarNames(nPanType,nEarth,bOnlyMain=false)
    nLength = get_HouseAStarLength(nPanType,nEarth)
    aStar = Array.new
    (0...nLength).each do |nIndex|
      if (bOnlyMain) then
        if (g_IsMainAStar(get_HouseAStar(nPanType,nEarth,nIndex))) then
          aStar.push(get_HouseAStarName(nPanType,nEarth,nIndex))
        end
      else
        aStar.push(get_HouseAStarName(nPanType,nEarth,nIndex))
      end
    end
    return aStar
  end

  def get_HouseAStarLength(nPanType,nEarth)
    starInfos = get_HouseFullAStarInfos(nPanType,nEarth)
    return starInfos.length
  end

  def get_HouseAStarName(nPanType,nEarth,nIndex)
    starInfo = get_HouseAStarInfo(nPanType,nEarth,nIndex)
    return starInfo[0]
  end

  def get_HouseAStars(nPanType,nEarth,bOnlyMain=false)
    nLength = get_HouseAStarLength(nPanType,nEarth)
    aStar = Array.new
    (0...nLength).each do |nIndex|
      if (bOnlyMain) then
        if (g_IsMainAStar(get_HouseAStar(nPanType,nEarth,nIndex))) then
          aStar.push(get_HouseAStar(nPanType,nEarth,nIndex))
        end
      else
        aStar.push(get_HouseAStar(nPanType,nEarth,nIndex))
      end
    end
    return aStar
  end

  def get_HouseAStarsMioWong(nPanType,nEarth,bOnlyMain=false)
    nLength = get_HouseAStarLength(nPanType,nEarth)
    aStarMioWong = Array.new
    (0...nLength).each do |nIndex|
      if (bOnlyMain) then
        if (g_IsMainAStar(get_HouseAStar(nPanType,nEarth,nIndex))) then
          aStarMioWong.push(get_HouseAStarMioWong(nPanType,nEarth,nIndex))
        end
      else
        aStarMioWong.push(get_HouseAStarMioWong(nPanType,nEarth,nIndex))
      end
    end
    return aStarMioWong
  end

  def get_HouseAStarsMioWongStr(nPanType,nEarth,bOnlyMain=false)
    aStarMioWong = get_HouseAStarsMioWong(nPanType,nEarth,bOnlyMain)
    aMioWongStr = Array.new
    aStarMioWong.each do |nMioWong|
      aMioWong = Array.new
      aMioWong.push(getMioWongStr(nMioWong))
      aMioWong.push(isMioWongNeedOutline?(nMioWong))
      aMioWongStr.push(aMioWong)
    end
    return aMioWongStr
  end


  def get_HouseAStarMioWong(nPanType,nEarth,nIndex)
    starInfo = get_HouseAStarInfo(nPanType,nEarth,nIndex)
    return starInfo[3]
  end

  def get_HouseAStar(nPanType,nEarth,nIndex)
    starInfo = get_HouseAStarInfo(nPanType,nEarth,nIndex)
    return starInfo[2]
  end

  def get_HouseAStarOrgFourHua(nPanType,nEarth,nIndex)
    starInfo = get_HouseAStarInfo(nPanType,nEarth,nIndex)
    return get_AStarInfoOrgFourHua(starInfo)
  end

  def get_AStarInfoOrgFourHua(starInfo)
    return starInfo[4]
  end

  def get_HouseAStarInfos(nPanType,nEarth,bOnlyMain=false)
    nLength = get_HouseAStarLength(nPanType,nEarth)
    aStar = Array.new
    (0...nLength).each do |nIndex|
      if (bOnlyMain) then
        if (g_IsMainAStar(get_HouseAStar(nPanType,nEarth,nIndex))) then
          aStar.push(get_HouseAStarInfo(nPanType,nEarth,nIndex))
        end
      else
        aStar.push(get_HouseAStarInfo(nPanType,nEarth,nIndex))
      end
    end
    return aStar
  end

  def get_HouseFullAStarInfos(nPanType,nEarth)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::A_STAR] == nil) then
      cp_A_Stars(nPanType)
    end
    return @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::A_STAR]
  end
  def get_HouseAStarInfo(nPanType,nEarth,nIndex)
    starInfos = get_HouseFullAStarInfos(nPanType,nEarth)
    starInfo = starInfos[nIndex]
    return starInfo
  end

  def Star.Get_AStarInfo_Name(starInfo)
    return starInfo[0]
  end
  def Star.Set_AStarInfo_Name(starInfo,s)
    starInfo[0] = s
    return starInfo
  end

  def Star.Get_AStarInfo_Star(starInfo)
    return starInfo[2]
  end
  def Star.AStarInfos_2_Stars(starInfos)
    a = Array.new
    starInfos.each do |starInfo|
      a.push(Star.Get_AStarInfo_Star(starInfo))
    end
    return a
  end

  def Star.Get_AStarInfo_MioWong(starInfo)
    return starInfo[3]
  end
  def get_AStarInfoMioWong_Str(starInfo)
    nMioWong = Star.Get_AStarInfo_MioWong(starInfo)

    return getMioWongStr(nMioWong)
  end

  def Star.Get_AStarInfo_OrgFourHua(starInfo)
    return starInfo[4]
  end
  def get_AStarInfoOrgFourHua_Str(starInfo,nPanType)
    hFourHua = Star.Get_AStarInfo_OrgFourHua(starInfo)
    nFourHua = getFourHuaHashValue(hFourHua,nPanType)

    return getFourHuaStr(nFourHua)
  end

  def Star.Get_AStarInfo_SelfFourHua(starInfo)
    return starInfo[5]
  end
  def get_AStarInfoSelfFourHua_Str(starInfo)
    nFourHua = Star.Get_AStarInfo_SelfFourHua(starInfo)

    return getFourHuaStr(nFourHua)
  end

  def Star.Get_AStarInfo_StarType(starInfo)
    return starInfo[6]
  end
  def Star.Check_AStarInfo_StarType_A(starInfo)
    starType = Star.Get_AStarInfo_StarType(starInfo)
    return (starType == "a")
  end
  # B star name in house
  def get_HouseBStarNames(nPanType,nEarth)
    nLength = get_HouseBStarLength(nPanType,nEarth)
    aStar = Array.new
    (0...nLength).each do |nIndex|
      aStar.push(get_HouseBStarName(nPanType,nEarth,nIndex))
    end
    return aStar
  end

  def get_HouseBStarLength(nPanType,nEarth)
    starInfos = get_HouseFullBStarInfos(nPanType,nEarth)
    return starInfos.length
  end

  def get_HouseBStarName(nPanType,nEarth,nIndex)
    starInfo = get_HouseBStarInfo(nPanType,nEarth,nIndex)
    return starInfo[0]
  end

  def Star.Get_BStarInfo_Name(starInfo)
    return starInfo[0]
  end

  def Star.Get_BStarInfo_Star(starInfo)
    return starInfo[2]
  end

  def Star.Get_BStarInfo_MioWong(starInfo)
    return starInfo[3]
  end
  def get_BStarInfoMioWong_Str(starInfo)
    nMioWong = Star.Get_BStarInfo_MioWong(starInfo)

    return getMioWongStr(nMioWong)
  end

  def Star.Get_BStarInfo_StarType(starInfo)
    return starInfo[6]
  end

  def get_HouseBStarsMioWong(nPanType,nEarth)
    nLength = get_HouseBStarLength(nPanType,nEarth)
    aStarMioWong = Array.new
    (0...nLength).each do |nIndex|
      aStarMioWong.push(get_HouseBStarMioWong(nPanType,nEarth,nIndex))
    end
    return aStarMioWong
  end

  def get_HouseBStarMioWong(nPanType,nEarth,nIndex)
    starInfo = get_HouseBStarInfo(nPanType,nEarth,nIndex)
    return Star.Get_BStarInfo_MioWong(starInfo)
  end

  def get_HouseBStars(nPanType,nEarth)
    nLength = get_HouseBStarLength(nPanType,nEarth)
    aStar = Array.new
    (0...nLength).each do |nIndex|
      aStar.push(get_HouseBStar(nPanType,nEarth,nIndex))
    end
    return aStar
  end

  def get_HouseBStar(nPanType,nEarth,nIndex)
    starInfo = get_HouseBStarInfo(nPanType,nEarth,nIndex)
    return starInfo[2]
  end

  def get_HouseBStarInfos(nPanType,nEarth,bOnlyMain=false)
    nLength = get_HouseBStarLength(nPanType,nEarth)

    aStar = Array.new
    (0...nLength).each do |nIndex|
      aStar.push(get_HouseBStarInfo(nPanType,nEarth,nIndex))
    end
    return aStar
  end

  def get_HouseFullBStarInfos(nPanType,nEarth)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::B_STAR] == nil) then
      cp_B_Stars(nPanType)
    end
    return @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::B_STAR]
  end
  def get_HouseBStarInfo(nPanType,nEarth,nIndex)
    starInfos = get_HouseFullBStarInfos(nPanType,nEarth)
    starInfo = starInfos[nIndex]
    return starInfo
  end

  #---------------------------------------------------------------------
  #設定宮天干，輸入宮位置(寅宮為0)及天干；將宮位置轉為地支再存入@PanInfo == @AllPanInfo[@m_nPanType]中
  def cp_setEach_HouseSky(nPanType,nHouseIndex,nSkyIndex)
    @AllPanInfo[nPanType][Earth.GetKeyName(Star.HouseIndex2Earth(nHouseIndex))][Star::HOUSE_SKY] = Sky.SkyIndex2Sky(nSkyIndex)  #用1~10來存天干
  end

  def cp_set_HouseSky(nPanType,nHouseIndex,nSkyIndex)
    m = Earth.Modify(nHouseIndex)
    n = Sky.Modify(nSkyIndex)
    12.times do |i|
      cp_setEach_HouseSky(nPanType,m,n)
      m = Earth.Modify(m + 1)
      n = Sky.Modify(n + 1)
    end
  end

  # def cp_get_HouseSkyIndex(nHouseIndex)
  #   return Sky.Sky2SkyIndex(@AllPanInfo[@m_nPanType][Earth.GetKeyName(Star.HouseIndex2Earth(nHouseIndex))][Star::HOUSE_SKY])   #用1~10來存天干
  # end

  # def eval_cp_get_HouseSkyIndex(nHouseIndex)
  #   if (!Sky.Exist?(cp_get_HouseSkyIndex(nHouseIndex))) then
  #     cp_HouseSky()
  #   end
  #   return cp_get_HouseSkyIndex(nHouseIndex)
  # end

  def cp_get_Pan_HouseSkyIndex(nPanType,nHouseIndex)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(Star.HouseIndex2Earth(nHouseIndex))][Star::HOUSE_SKY] == nil) then
      cp_HouseSky(nPanType)
    end
    return Sky.Sky2SkyIndex(@AllPanInfo[nPanType][Earth.GetKeyName(Star.HouseIndex2Earth(nHouseIndex))][Star::HOUSE_SKY])   #用1~10來存天干
  end

  #---------------------------------------------------------------------
  #設定宮天干，輸入地支位置(子宮為0)及天干；存入@PanInfo == @AllPanInfo[@m_nPanType]中
  def cp_set_HouseSky_byEarthIndex(nPanType,nEarthIndex,nSkyIndex)
    nEIdx = Earth.Modify(nEarthIndex)
    @AllPanInfo[nPanType][Earth.GetKeyName(Star.EarthIndex2Earth(nEIdx))][Star::HOUSE_SKY] = nSkyIndex
  end

  #---------------------------------------------------------------------
  #計算PAN_NORMAL的宮天干
  def cp_HouseSky_0(nPanType)
    nSkyIndex = cp_getSkyIndex()
    n = @@FiveWord[nSkyIndex]
    cp_set_HouseSky(nPanType,0,n)
  end

  #計算PAN_TENYEAR的宮天干
  def cp_HouseSky_1(nPanType)
    cp_HouseSky_0(nPanType)
  end

  def getFlowYearHouseSkyPos(nPanType)
    nFlowEarthIndex = getFlowEarthIndex(nPanType)
    # 流年位置
    return Earth.Modify(nFlowEarthIndex - 2)
  end

  def getFlowEarthIndex(nPanType)
    if (nPanType == Cfate::PAN_NORMAL || nPanType == Cfate::PAN_TENYEAR) then
      return cp_getEarthIndex()
    else
      return getFlowYearEarthIndex(nPanType)
    end
  end
  def cp_getFlowEarthIndex_3_PanType(nPanType)
    nEarthIndex = 0
    if (nPanType == Cfate::PAN_NORMAL) then
      nEarthIndex = cp_getEarthIndex()
    elsif (nPanType == Cfate::PAN_FLOWYEAR) then
      nEarthIndex = getFlowYearEarthIndex(nPanType)
    elsif (nPanType == Cfate::PAN_FLOWDATE) then
      nSkyIndex,nEarthIndex = getFlowDateSkyEarthIndex(nPanType)
    end
    return nEarthIndex
  end

  def getFlowSkyIndex(nPanType)
    if (nPanType == Cfate::PAN_NORMAL || nPanType == Cfate::PAN_TENYEAR) then
      return cp_getSkyIndex()
    else
      return getFlowYearSkyIndex(nPanType)
    end
  end

  def getFlowYearSkyIndex(nPanType)
    if (@AllPanInfo[Cfate::PAN_FLOWYEAR][Star::SKY_EARTH_INDEX][nPanType] == nil) then
      nSkyIndex = Sky.GetYearSkyIndex(fdg_EY(nPanType))
      nEarthIndex = Earth.GetYearEarthIndex(fdg_EY(nPanType))
      @AllPanInfo[Cfate::PAN_FLOWYEAR][Star::SKY_EARTH_INDEX][nPanType] = [nSkyIndex,nEarthIndex]
    else
      a = @AllPanInfo[Cfate::PAN_FLOWYEAR][Star::SKY_EARTH_INDEX][nPanType]
      nSkyIndex,nEarthIndex = a[0],a[1]
    end
    return nSkyIndex
  end
  def getFlowYearEarthIndex(nPanType)
    if (@AllPanInfo[Cfate::PAN_FLOWYEAR][Star::SKY_EARTH_INDEX][nPanType] == nil) then
      nSkyIndex = Sky.GetYearSkyIndex(fdg_EY(nPanType))
      nEarthIndex = Earth.GetYearEarthIndex(fdg_EY(nPanType))
      @AllPanInfo[Cfate::PAN_FLOWYEAR][Star::SKY_EARTH_INDEX][nPanType] = [nSkyIndex,nEarthIndex]
    else
      a = @AllPanInfo[Cfate::PAN_FLOWYEAR][Star::SKY_EARTH_INDEX][nPanType]
      nSkyIndex,nEarthIndex = a[0],a[1]
    end
    return nEarthIndex
  end

  # 2021/5/5改回依生肖年為命宮起算天干
  def cp_HouseSky_2(nPanType)
    if (@par_FlowSkyType_Cald_Flowyear == Star::PAN_FS_CALD_FLOWYEAR_NIANXUN) then
      # 年旬排法
      cp_HouseSky_2_nianxun(nPanType)
    else 
      # Star::PAN_FS_CALD_FLOWYEAR_TAISUI 太歲排法
      cp_HouseSky_2_taisui(nPanType)
    end
  end
      # Star::PAN_FS_CALD_FLOWYEAR_TAISUI 太歲排法
  def cp_HouseSky_2_taisui(nPanType)
    nFlowSkyIndex = getFlowYearSkyIndex(nPanType)
    nFlowYearHouseSkyPos = getFlowYearHouseSkyPos(nPanType)
    m = nFlowYearHouseSkyPos
    n = nFlowSkyIndex
    cp_set_HouseSky(nPanType,m,n)
  end
  #計算PAN_FLOWYEAR的宮天干,只有甲乙重覆復,根據旬中空亡判斷，紫微築基篇158頁
  # 年旬排法
  def cp_HouseSky_2_nianxun(nPanType)
    nFlowSkyIndex = getFlowYearSkyIndex(nPanType)
    nFlowEarthIndex = getFlowYearEarthIndex(nPanType)
    m = nFlowEarthIndex - nFlowSkyIndex - 2
    n = 0
    cp_set_HouseSky(nPanType,m,n)
  end
  def cp_HouseSky_2_old_failed(nPanType)
    # PAN_FLH_BIRTH          =        0  #生肖年為命宮
    if (@par_FlowLifeHouseType == Star::PAN_FLH_BIRTH) then
      nFlowYearHouseSkyPos = getFlowYearHouseSkyPos(nPanType)
      # nFlowSkyIndex = getFlowSkyIndex(nPanType)
      nFlowSkyIndex = getFlowYearSkyIndex(nPanType)
      # 流年會前兩宮位會放之前2年
      m = nFlowYearHouseSkyPos - 2
      n = nFlowSkyIndex - 2
      cp_set_HouseSky(nPanType,m,n)
    # else  # PAN_FLH_SMALL_SAN      =        1  #小限為命宮  以該年農曆一月天干為寅宮天干
    elsif (@par_FlowLifeHouseType == Star::PAN_FLH_SMALL_SAN) then
      cp_HouseSky_3(nPanType)
    else # PAN_FLH_SMALL_SAN_FLOW_SKY      =        2  小限為命宮流年天干
      nFlowYearHouseSkyPos = getFlowYearHouseSkyPos(nPanType)
      m = nFlowYearHouseSkyPos - 2
      nFlowSkyIndex = getFlowYearSkyIndex(nPanType)
      n = nFlowSkyIndex - 2
      cp_set_HouseSky(nPanType,m,n)
    end
  end

  #計算PAN_FLOWMONTH的宮天干
  def cp_HouseSky_3(nPanType)
    m = 0
    nFlowSkyIndex = getFlowYearSkyIndex(nPanType)
    n = @@FiveWord[nFlowSkyIndex]
    cp_set_HouseSky(nPanType,m,n)
  end

  def getFlowMonthSkyEarthIndex(nPanType)
    if (@AllPanInfo[Cfate::PAN_FLOWMONTH][Star::SKY_EARTH_INDEX][nPanType] == nil) then
      if (nPanType >= Cfate::PAN_FLOWTIME) then
        nETime = fdg_ET(nPanType) # Xdate.Hour2ETime(fdg_WH(nPanType)) #fdg_ET(nPanType)
      else
        nETime = 0
      end
      y,m,d = fdg_W_YMD(nPanType)
      nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(y,m,d, nETime)
      nSkyIndex = Sky.GetSkyIndex(nGanZhiMonth)
      nEarthIndex = Earth.GetEarthIndex(nGanZhiMonth)
      @AllPanInfo[Cfate::PAN_FLOWMONTH][Star::SKY_EARTH_INDEX][nPanType] = [nSkyIndex,nEarthIndex]
    else
      a = @AllPanInfo[Cfate::PAN_FLOWMONTH][Star::SKY_EARTH_INDEX][nPanType]
      nSkyIndex,nEarthIndex = a[0],a[1]
    end

    return nSkyIndex,nEarthIndex
  end

  # 算完存起來用，不要每次都算
  def getFlowDateSkyEarthIndex(nPanType)
    if (@AllPanInfo[Cfate::PAN_FLOWDATE][Star::SKY_EARTH_INDEX][nPanType] == nil) then
      if (nPanType >= Cfate::PAN_FLOWTIME) then
        nETime = fdg_ET(nPanType) # Xdate.Hour2ETime(fdg_WH(nPanType)) #fdg_ET(nPanType)
      else
        nETime = 0
      end
      y,m,d = fdg_W_YMD(nPanType)
      nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(y,m,d, nETime)
      nSkyIndex = Sky.GetSkyIndex(nGanZhiDay)
      nEarthIndex = Earth.GetEarthIndex(nGanZhiDay)
      @AllPanInfo[Cfate::PAN_FLOWDATE][Star::SKY_EARTH_INDEX][nPanType] = [nSkyIndex,nEarthIndex]
    else
      a = @AllPanInfo[Cfate::PAN_FLOWDATE][Star::SKY_EARTH_INDEX][nPanType]
      nSkyIndex,nEarthIndex = a[0],a[1]
    end
    return nSkyIndex,nEarthIndex
  end

  #計算PAN_FLOWDATE的宮天干,根據旬中空亡判斷，紫微築基篇158頁
  def cp_HouseSky_4(nPanType)
    nFlowSkyIndex,nFlowEarthIndex = getFlowDateSkyEarthIndex(nPanType)
    m = nFlowEarthIndex - nFlowSkyIndex - 2
    n = 0
    cp_set_HouseSky(nPanType,m,n)
  end
  def cp_HouseSky_4_old_failed(nPanType)
    nFlowSkyIndex,nFlowEarthIndex = getFlowDateSkyEarthIndex(nPanType)
    m = nFlowEarthIndex - 2
    n = nFlowSkyIndex
    cp_set_HouseSky(nPanType,m,n)
  end

  #計算PAN_FLOWTIME的宮天干
  def cp_HouseSky_5(nPanType)
    nFlowSkyIndex,nFlowEarthIndex = getFlowDateSkyEarthIndex(nPanType)
    m = -2
    n = @@FiveMouse[nFlowSkyIndex]
    cp_set_HouseSky(nPanType,m,n)
  end

  def getFlowTimeSkyEarthIndex(nPanType)
    if (nPanType >= Cfate::PAN_FLOWTIME) then
      nETime = fdg_ET(nPanType) # Xdate.Hour2ETime(fdg_WH(nPanType)) #fdg_ET(nPanType)
    else
      nETime = 0
    end
    y,m,d = fdg_W_YMD(nPanType)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(y,m,d, nETime)
    nSkyIndex = Sky.GetSkyIndex(nGanZhiHour)
    nEarthIndex = nGanZhiHour % 12

#puts("y:#{y},m:#{m},d:#{d},nETime:#{nETime},nWHour:#{fdg_WH()},nSkyIndex:#{nSkyIndex}")
    return nSkyIndex,nEarthIndex
  end

  #計算PAN_FLOWMIN的宮天干
  def cp_HouseSky_6(nPanType)
    # 改為流時的干支，用流日的天干五鼠盾
    nFlowSkyIndex,nFlowEarthIndex = getFlowDateSkyEarthIndex(nPanType)
    # 原先為流時的干支五鼠盾
    # nFlowSkyIndex,nFlowEarthIndex = getFlowTimeSkyEarthIndex(nPanType)
    m = -2
    n = @@FiveMouse[nFlowSkyIndex]
    cp_set_HouseSky(nPanType,m,n)
  end


  #---------------------------------------------------------------------
  #計算每個宮位之天干
  def cp_HouseSky(nPanType)
    if (!Star.IsPanType?(nPanType)) then return false end

    funcName = "cp_HouseSky_#{nPanType}(nPanType)"
    eval(funcName)
    return true
  end

  #---------------------------------------------------------------------
  #設定宮天干，輸入宮位置(寅宮為0)及天干；將宮位置轉為地支再存入@PanInfo == @AllPanInfo[@m_nPanType]中
  def cp_setEach_HouseName(nPanType,nHouseIndex,nHouse)
    a = Array.new
    a.push(Star.GetHouseName(nHouse))
    a.push(Star.GetHouseNameKey(nHouse))
    a.push(nHouse)
    @AllPanInfo[nPanType][Earth.GetKeyName(Star.HouseIndex2Earth(nHouseIndex))][Star::HOUSE_NAME] = a
  end

  def cp_set_HouseName(nPanType,nHouseIndex,nHouse)
    set_FlowLifeHouseIndex(nPanType,nHouseIndex) # 儲存命宮起始宮位
    m = Earth.Modify(nHouseIndex)
    n = nHouse
    12.times do |i|
      cp_setEach_HouseName(nPanType,m,n)
      m = Earth.Modify(m - 1)
      n = Earth.ModifyEarth(n + 1)
    end
  end

  def cp_GetFlowYearOld(nPanType=nil)
      return Xdate.GetYearOld(uig_EY(),fdg_EY(nPanType))
  end

  #計算PAN_NORMAL的宮名
  def cp_HouseName_0(nPanType)
    # 計算流年位置
    n = cp_getLifeEarthIndex()
    cp_set_HouseName(nPanType,n,1)
  end

  #計算PAN_TENYEAR的宮名
  def cp_HouseName_1(nPanType)
    n = cp_GetLargeSan(0)
    cp_set_HouseName(nPanType,n,1)
  end

  # 直接算出地支
  def cp_FindSmallDo(nAge12,nBirthEMonth,nBirthETime)
    if (nAge12 == 0) then
      nAge12 = 12
    end
    # 先算出小限宮位
    nSmallDo = -1
    i = 0
    while ((nSmallDo == -1) && (i < 12))
      if (nAge12 == @StarInfo[U_SMALL][i]) then
        nSmallDo = i
      end
      i += 1
    end

    # 再算出斗君宮位
    nSmallDo = nSmallDo - nBirthEMonth + nBirthETime + 1

    # 再算出斗君所在地支
    nSmallDo = Earth.Modify(nSmallDo + 2)

    return nSmallDo
  end

  # 命宮起始宮位
  def set_FlowLifeHouseIndex(nPanType,nValue)
    @AllPanInfo[nPanType][Star::FLOW_LIFE_HEAD] = nValue
  end

  def get_FlowLifeHouseIndex(nPanType)
    if (@AllPanInfo[nPanType][Star::FLOW_LIFE_HEAD] == nil) then
      cp_HouseName(nPanType)
    end
    return @AllPanInfo[nPanType][Star::FLOW_LIFE_HEAD]
  end

  # 月,日，時,分等起始宮位之HouseIndex
  def set_FlowHouseHeadIndex(nPanType,nValue)
    @AllPanInfo[nPanType][Star::FLOW_HOUSE_HEAD] = nValue
  end

  def get_FlowHouseHeadIndex(nPanType)
    if (@AllPanInfo[nPanType][Star::FLOW_HOUSE_HEAD] == nil) then
      cp_FlowHouseHeadIndex(nPanType)
    end
    return @AllPanInfo[nPanType][Star::FLOW_HOUSE_HEAD]
  end
  def cp_FlowHouseHeadIndex(nPanType)
    case (nPanType)
      when (Cfate::PAN_NORMAL) then
        nFlowLife = cp_getLifeEarthIndex()  # PAN_NORMAL,PAN_TENYEAR
      when (Cfate::PAN_TENYEAR) then
        nFlowLife = cp_GetLargeSan(0)  # PAN_NORMAL,PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        nFlowLife = cp_GetFlowYearHouseHead()  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        nFlowLife = cp_GetFlowMonthLife(nPanType)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        nFlowLife = cp_GetFlowDateLife(nPanType)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        nFlowLife = cp_GetFlowTimeLife(nPanType)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        nFlowLife = cp_GetFlowMinLife(nPanType)    # PAN_FLOWMIN
      else nFlowLife = -1
    end
    return nFlowLife
  end

  #流年命宮排法
  def cp_GetFlowYearHouseHead()
    # if (Earth.Exist?(get_FlowHouseHeadIndex(Cfate::PAN_FLOWYEAR))) then
    #   return get_FlowHouseHeadIndex(Cfate::PAN_FLOWYEAR)
    # end

    nFlowEarthIndex = getFlowYearEarthIndex(Cfate::PAN_FLOWYEAR)
    nFlowAge = cp_GetFlowYearOld(Cfate::PAN_FLOWYEAR)
    if (@par_FlowLifeHouseType == Star::PAN_FLH_BIRTH) then
      # 生肖年為命盤，先算出地支，子是0，再算出宮位，0-2+12=10
      nHouseHead = nFlowEarthIndex - 2
    elsif (@par_FlowLifeHouseType == Star::PAN_FLH_SMALL_SAN) then
      # 小限為命盤
      nHouseHead = cp_FindSmallDo(nFlowAge % 12,1,0)   # 小限的地支
      nHouseHead -= 2
    else # PAN_FLH_SMALL_SAN_FLOW_SKY
      # 小限為命盤
      nHouseHead = cp_FindSmallDo(nFlowAge % 12,1,0)   # 小限的地支
      nHouseHead -= 2
    end
    nHouseHead = Earth.Modify(nHouseHead)
    set_FlowHouseHeadIndex(Cfate::PAN_FLOWYEAR,nHouseHead)

    # return get_FlowHouseHeadIndex(Cfate::PAN_FLOWYEAR)
  end

  def cp_GetBirthTime()
    nBirthTime = uig_ET()
    if (nBirthTime == 12) then
      nBirthTime = 0
    end

    return nBirthTime
  end

  def cp_GetBirthMonth()
    nBirthMonth = uig_EM()
    if (par_check_LeapType()) then
      nBirthMonth += 1
    end

    return nBirthMonth
  end

  def cp_GetFlowYearHouse(nPanType)
    nFlowEarthIndex = Earth.GetEarthIndex(fdg_EY(nPanType) - 1900)

    # 流年位置
    nFlowYearHouse = Earth.Modify(nFlowEarthIndex - 2)

    return nFlowYearHouse
  end

  def cp_GetFlowLife(nPanType)
    case (nPanType)
      when (Cfate::PAN_NORMAL..Cfate::PAN_TENYEAR) then
        nFlowLife = cp_getLifeEarthIndex()  # PAN_NORMAL,PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        nFlowLife = cp_GetFlowYearLife(nPanType)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        nFlowLife = cp_GetFlowMonthLife(nPanType)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        nFlowLife = cp_GetFlowDateLife(nPanType)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        nFlowLife = cp_GetFlowTimeLife(nPanType)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        nFlowLife = cp_GetFlowMinLife(nPanType)    # PAN_FLOWMIN
      else nFlowLife = -1
    end
    return nFlowLife
  end

  #斗君排法
  def cp_GetFlowYearLife(nPanType=nil)
    nBirthMonth = cp_GetBirthMonth()
    nBirthTime = cp_GetBirthTime()
    nFlowAge = cp_GetFlowYearOld(nPanType)
    nFlowYearLife = 0

    # 斗君排法
    if (@par_GodType == Star::PAN_GOD_YEAR) then  #流年斗君
      nFlowYearLife = getFlowYearEarthIndex(Cfate::PAN_FLOWYEAR) - 2
      nFlowYearLife = Earth.Modify(nFlowYearLife - nBirthMonth + nBirthTime + 1)
    elsif (@par_GodType == Star::PAN_GOD_LIFE) then   #子年斗君
      nFlowYearLife = Earth.Modify(cp_getGod() - 2)
    else #PAN_GOD_SMALL  m_oPanLife.nSmall[j] 小限斗君
      @m_nSmallDo = cp_FindSmallDo(nFlowAge % 12,nBirthMonth,nBirthTime)
      nFlowYearLife = Earth.Modify(@m_nSmallDo - 2)   # 斗君的地支轉換為宮位
    end
    return nFlowYearLife
  end

  #計算PAN_FLOWYEAR的宮名
  def cp_HouseName_2(nPanType)
    # n = cp_GetFlowYearHouseHead()
    n = get_FlowHouseHeadIndex(nPanType)
    cp_set_HouseName(nPanType,n,1)
  end

  def cp_GetFlowMonthLife(nPanType=nil)
    nFlowMonth = fdg_EM(nPanType) #以本月看待，不受䦌月排法參數影響
    nFlowLife = cp_GetFlowYearLife(nPanType)
    set_FlowHouseHeadIndex(Cfate::PAN_FLOWMONTH,nFlowLife)
    nFlowLife = Earth.Modify(nFlowLife + nFlowMonth - 1)

    return nFlowLife
  end

  #計算PAN_FLOWMONTH的宮名
  def cp_HouseName_3(nPanType)
    nFlowLife = cp_GetFlowMonthLife(nPanType)
    cp_set_HouseName(nPanType,nFlowLife,1)
  end

  def cp_GetFlowDateLife(nPanType)
    nFlowLife = cp_GetFlowMonthLife(nPanType)
    set_FlowHouseHeadIndex(Cfate::PAN_FLOWDATE,nFlowLife)
    nFlowLife = Earth.Modify(nFlowLife + (fdg_ED(nPanType) % 12) - 1)

    return nFlowLife
  end

  #計算PAN_FLOWDAY的宮名
  def cp_HouseName_4(nPanType)
    nFlowLife = cp_GetFlowDateLife(nPanType)
    cp_set_HouseName(nPanType,nFlowLife,1)
  end

  def cp_GetFlowTimeLife(nPanType)
    nETime = fdg_ET(nPanType)
    # nETime = Xdate.Hour2ETime(fdg_WH())
    y,m,d = fdg_W_YMD(nPanType)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(y,m,d, nETime)
    nFlowTimeEarthIndex = Earth.GetEarthIndex(nGanZhiHour)
    if (@par_liushi_minggong == Star::PAN_LIUSHI_MINGGONG_1) then
      # 直接根據子時在子宮 計算流時的宮位
      nFlowLife = Earth.Modify(nFlowTimeEarthIndex - 2)
      # 設定子時的位置 固定在子宮 set_FlowHouseHeadIndex
      set_FlowHouseHeadIndex(Cfate::PAN_FLOWTIME,10)
    else
      # 子時的宮位 
      nFlowLife = cp_GetFlowDateLife(Cfate::PAN_FLOWDATE)
      # 設定子時的位置 set_FlowHouseHeadIndex
      set_FlowHouseHeadIndex(Cfate::PAN_FLOWTIME,nFlowLife)
      # 根據子時的宮位計算流時的宮位
      nFlowLife = Earth.Modify(nFlowTimeEarthIndex + nFlowLife)
    end

    # 表示流時的位置 nFlowLife
    return nFlowLife
  end

  #計算PAN_FLOWTIME的宮名
  def cp_HouseName_5(nPanType)
    nFlowLife = cp_GetFlowTimeLife(nPanType)
    cp_set_HouseName(nPanType,nFlowLife,1)
  end

  def cp_GetFlowMinLife_FlowTimeLife(nPanType)
    nETime = fdg_ET(nPanType)
    # nETime = Xdate.Hour2ETime(fdg_WH())
    y,m,d = fdg_W_YMD(nPanType)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour = Xdate.GetLunar8Words(y,m,d, nETime)
    nFlowTimeEarthIndex = Earth.GetEarthIndex(nGanZhiHour)
    nFlowLife = Earth.Modify(nFlowTimeEarthIndex - 2)

    return nFlowLife
  end
  def cp_GetFlowMinLife(nPanType)
    nFlowLife = cp_GetFlowTimeLife(nPanType)
    set_FlowHouseHeadIndex(Cfate::PAN_FLOWMIN,nFlowLife)
    nFlowMin = fdg_EMI(nPanType)
    nFlowLife = Earth.Modify(nFlowLife + nFlowMin)

    return nFlowLife
  end

  #計算PAN_FLOWMIN的宮名
  def cp_HouseName_6(nPanType)
    nFlowLife = cp_GetFlowMinLife(nPanType)
    cp_set_HouseName(nPanType,nFlowLife,1)
  end

  #計算每個宮位之名稱
  def cp_HouseName(nPanType)
    if (!Star.IsPanType?(nPanType)) then return end

    funcName = "cp_HouseName_" + nPanType.to_s + "(#{nPanType})"

    eval(funcName)
  end

  #---------------------------------------------------------------------
  #-----------------底下開始計算各星位置------------------------------
  #---------------------------------------------------------------------
  def exec_funcs(preFuncName,nStart,nStop)
    sFuncName = ""
    (nStart..nStop).each do |i|
      sFuncName = preFuncName + i.to_s + "()"
      eval(sFuncName)
    end
  end

  def assign_var(preVarName,nStart,nStop,nValue)
    sVarName = ""
    (nStart..nStop).each do |i|
      sVarName = preVarName + i.to_s + " = " + nValue.to_s
      eval(sVarName)
    end
  end

  #甲級星
  def cp_A_Stars(nPanType)
    (1..12).each do |i|
      @AllPanInfo[nPanType][Earth.GetKeyName(i)][Star::A_STAR] = Array.new
    end
    (1..Star::A_STAR_COUNT).each do |i|
      eval_cp_A_Star(nPanType,i)
    end
  end

  # index 0 : name,1 : name key, 2 : id, 3 : miowong, 4 : four hua
  def make_A_StarInfo(nPanType,nStar)
    a = Array.new
    # a.push(Star.GetAStarName(nStar))  # 0
    a.push(get_A_Star_Name(nPanType,nStar))  # 0
    a.push(Star.GetAStarNameKey(nStar))  # 1
    a.push(nStar) # 2
    nEarth = Star.HouseIndex2Earth(get_A_Star_HouseIndex(nPanType,nStar))
    nMioWong = findMioWong(A_STAR,nEarth,nStar)
    a.push(nMioWong) # 3

    # 先天四化
    hFourHua = findFourHua(nPanType,nStar)
    a.push(hFourHua)  # 4
    cp_set_OriginalFourHua(nPanType,nEarth,nStar,getFourHuaIndex(hFourHua,nPanType))

    # 宮干自化
    nFourHua = findSelfFourHua(nPanType,nEarth,nStar)
    a.push(nFourHua) # 5
    cp_set_HouseSelfFourHua(nPanType,nEarth,nStar,nFourHua)

    a.push("a") # 6 AStar

    return a.clone
  end
  def get_A_Star_Name(nPanType,nStar)
    # if (nPanType <= Cfate::PAN_FLOWDATE && [19,20,21].include?(nStar) && (@dis_FlowLYT == Star::STAR_DIS_FLOW_DIE)) then
    if ([19,20,21].include?(nStar) && (@dis_FlowLYT == Star::STAR_DIS_FLOW_DIE)) then
      return Star.ziwei_astar_name(nPanType,nStar,@par_LuType)
    else
      return Star.GetAStarName(nStar)
    end
  end
  # return nHouseIndex
  # Purple on House start from 寅(0)
  def get_A_Star_HouseIndex(nPanType,nStar)
    if (@AllPanInfo[nPanType][Star::A_STAR_POS][Star.Star2StarIndex(nStar)] == nil) then
      eval_cp_A_Star(nPanType,nStar)
    end
    return @AllPanInfo[nPanType][Star::A_STAR_POS][Star.Star2StarIndex(nStar)]
  end

  def set_A_Star_HouseIndex(nPanType,nStar,nValue)
    @AllPanInfo[nPanType][Star::A_STAR_POS][Star.Star2StarIndex(nStar)] = Earth.Modify(nValue)
  end

  def get_A_Star_Info(nPanType,nStar,nEarth=nil)
    return get_A_Star_Info_by_star(nPanType,nStar,nEarth)
  end
  def get_A_Star_HouseEarth(nPanType,nStar)
    nIndex = get_A_Star_HouseIndex(nPanType,nStar)
    return Star.HouseIndex2Earth(nIndex)
  end
  def get_A_Star_Info_by_star(nPanType,nStar,nEarth=nil)
    # nEarth是nil，表示要抓出此nStar所在宮的starInfo
    # 若nEarth有值，則是判斷此nStar是否在此宮中，若有則回starInfo，沒有則回nil
    if (nEarth == nil) then
      nEarth = get_A_Star_HouseEarth(nPanType,nStar)
    end
    nLength = get_HouseAStarLength(nPanType,nEarth)
    (0...nLength).each do |nIndex|
      starInfo = get_HouseAStarInfo(nPanType,nEarth,nIndex)
      if (Star.Get_AStarInfo_Star(starInfo) == nStar) then
        return starInfo
      end
    end
    return nil
  end
  def set_A_Star_Info(nPanType,nStar,nEarth,starInfo)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::A_STAR] == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::A_STAR] = Array.new
    end
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::A_STAR].find_index(starInfo) == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::A_STAR].push(starInfo)
    end
  end

  def eval_Set_A_Star_Info(nPanType,nStar)
    expName = "set_A_Star_Info(#{nPanType},#{nStar},Star.HouseIndex2Earth(#{get_A_Star_HouseIndex(nPanType,nStar)}),make_A_StarInfo(#{nPanType},#{nStar}))"
    eval(expName)
  end

  def IsAStarExist?(nPanType,nStar)
    return (Earth.Exist?(get_A_Star_HouseIndex(nPanType,nStar)))
  end

  def eval_cp_A_Star(nPanType,nStar)
    sFuncName = "cp_get_A_Star_" + nStar.to_s + "(#{nPanType},#{nStar})"
    eval(sFuncName)

    if (gHouse_GetAStarDisplay(nPanType,nStar)) then
      eval_Set_A_Star_Info(nPanType,nStar)
    end
  end


  #steven 紫微逆去宿天機 ， dwStarType[n]中的 n 是以地支來看待。 寅宮為0
  def cp_get_A_Star_1(nPanType,nStar)    #紫微
    nFive = cp_getFive()
    i = nFive - 2
    n = @@ByPurple[i][uig_ED() - 1]
    n -= 2
    set_A_Star_HouseIndex(nPanType,nStar,n)
  end

  def cp_get_A_Star_2(nPanType,nStar)    #天機
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,1) - 1)
  end

  def cp_get_A_Star_3(nPanType,nStar)    #太陽
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,2) - 2)
  end

  def cp_get_A_Star_4(nPanType,nStar)    #武曲
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,3) - 1)
  end

  def cp_get_A_Star_5(nPanType,nStar)    #天同
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,4) - 1)
  end

  def cp_get_A_Star_6(nPanType,nStar)    #廉貞
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,5) - 3)
  end

  def cp_get_A_Star_7(nPanType,nStar)    #天府
    set_A_Star_HouseIndex(nPanType,nStar,12 - get_A_Star_HouseIndex(nPanType,1))
  end

  def cp_get_A_Star_8(nPanType,nStar)    #太陰
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,7) + 1)
  end

  def cp_get_A_Star_9(nPanType,nStar)    #貪狼
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,8) + 1)
  end

  def cp_get_A_Star_10(nPanType,nStar)   #巨門
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,9) + 1)
  end

  def cp_get_A_Star_11(nPanType,nStar)   #天相
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,10) + 1)
  end

  def cp_get_A_Star_12(nPanType,nStar)    #天梁
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,11) + 1)
  end

  def cp_get_A_Star_13(nPanType,nStar)   #七殺
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,12) + 1)
  end

  def cp_get_A_Star_14(nPanType,nStar)   #破軍
    set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,13) + 4)
  end

  def cp_get_A_Star_15(nPanType,nStar)   #文昌
    set_A_Star_HouseIndex(nPanType,nStar,8 - fdg_ET(nPanType))
  end

  def cp_get_A_Star_16(nPanType,nStar)   #文曲
    set_A_Star_HouseIndex(nPanType,nStar,2 + fdg_ET(nPanType))
  end

  def cp_get_A_Star_17(nPanType,nStar)   #左輔
    set_A_Star_HouseIndex(nPanType,nStar,1 + fdg_ELM(nPanType))
  end

  def cp_get_A_Star_18(nPanType,nStar)   #右弼
    set_A_Star_HouseIndex(nPanType,nStar,9 - fdg_ELM(nPanType))
  end

  def cp_getFlowSkyIndex_PanType(nPanType)
    nSkyIndex = 0
    if (nPanType == Cfate::PAN_NORMAL) then
      nSkyIndex = cp_getSkyIndex()
    elsif (nPanType == Cfate::PAN_TENYEAR) then
      nSkyIndex = cp_get_Pan_HouseSkyIndex(nPanType,cp_GetLargeSan(0))  # 宮天干
    elsif (nPanType == Cfate::PAN_FLOWYEAR) then
      nSkyIndex = getFlowYearSkyIndex(nPanType)
    elsif (nPanType == Cfate::PAN_FLOWMONTH) then
      nSkyIndex,nEarthIndex = getFlowMonthSkyEarthIndex(nPanType)
    elsif (nPanType == Cfate::PAN_FLOWDATE) then
      nSkyIndex,nEarthIndex = getFlowDateSkyEarthIndex(nPanType)
    elsif (nPanType >= Cfate::PAN_FLOWTIME) then
      nSkyIndex,nEarthIndex = getFlowTimeSkyEarthIndex(nPanType)
    end
    return nSkyIndex
  end

  def cp_getFlowEarthIndex_PanType(nPanType)
    nEarthIndex = 0
    if (nPanType == Cfate::PAN_NORMAL) then
      nEarthIndex = cp_getEarthIndex()
    elsif (nPanType == Cfate::PAN_TENYEAR) then
      nHouseIndex = cp_GetLargeSan(0)
      nEarthIndex = Star.HouseIndex2EarthIndex(nHouseIndex)
    elsif (nPanType == Cfate::PAN_FLOWYEAR) then
      nEarthIndex = getFlowYearEarthIndex(nPanType)
    elsif (nPanType == Cfate::PAN_FLOWMONTH) then
      nSkyIndex,nEarthIndex = getFlowMonthSkyEarthIndex(nPanType)
    elsif (nPanType == Cfate::PAN_FLOWDATE) then
      nSkyIndex,nEarthIndex = getFlowDateSkyEarthIndex(nPanType)
    elsif (nPanType >= Cfate::PAN_FLOWTIME) then
      nSkyIndex,nEarthIndex = getFlowTimeSkyEarthIndex(nPanType)
    end
    return nEarthIndex
  end

  def cp_getFlowSkyEarthIndex_PanType(nPanType)
    nSkyIndex = cp_getFlowSkyIndex_PanType(nPanType)
    nEarthIndex = cp_getFlowEarthIndex_PanType(nPanType)
    return nSkyIndex,nEarthIndex
  end

  def get_A_Star_19_Sky(nLuPanType)
    nSkyIndex = cp_getFlowSkyIndex_PanType(nLuPanType)
    return nSkyIndex
  end

  def cp_get_A_Star_19(nPanType,nStar)   #祿存
    nLuPanType = Star.Get3ParPanType(@par_LuType,nPanType)
    nLuSky = get_A_Star_19_Sky(nLuPanType)
    if ((nPanType >= Cfate::PAN_TENYEAR) && (@dis_FlowLYT == Star::STAR_DIS_FLOW_FLOW)) then
      # 流祿，流羊，流陀 時，各盤都顯示本命盤的祿存等。流祿等由FS 1，2，3來顯示
      nLuSky = cp_getSkyIndex()
    end
    m = @@BuLu[nLuSky]

    set_A_Star_HouseIndex(nPanType,nStar,m)
  end

  def cp_get_A_Star_20(nPanType,nStar)   #擎羊
      set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,19) + 1)
    end

  def cp_get_A_Star_21(nPanType,nStar)   #陀羅
      set_A_Star_HouseIndex(nPanType,nStar,get_A_Star_HouseIndex(nPanType,19) - 1)
    end

  def cp_get_A_Star_22(nPanType,nStar)   #火星
    if (nPanType >= Cfate::PAN_FLOWTIME) then
      nEarthIndex = getFlowYearEarthIndex(nPanType)
      nTime = fdg_ET(nPanType)
    else
      nEarthIndex = cp_getEarthIndex()
      nTime = uig_ET()
    end
    set_A_Star_HouseIndex(nPanType,nStar,@@ByFire[nEarthIndex] + nTime - 2)
  end

  def cp_get_A_Star_23(nPanType,nStar)   #鈴星
    if (nPanType >= Cfate::PAN_FLOWTIME) then
      nEarthIndex = getFlowYearEarthIndex(nPanType)
      nTime = fdg_ET(nPanType)
    else
      nEarthIndex = cp_getEarthIndex()
      nTime = uig_ET()
    end
      set_A_Star_HouseIndex(nPanType,nStar,@@ByLin[nEarthIndex] + nTime - 2)
  end

  def cp_get_A_Star_24(nPanType,nStar)   #天魁
    nDisPanType = Star.Get3ParPanType(@par_b2425Type,nPanType)
    nFlowSkyIndex,nFlowEarthIndex = cp_getFlowSkyEarthIndex_PanType(nDisPanType)
    if (@par_yuekui == Star::PAN_YUEKUI_NORMAL) then
      # nFlowSkyIndex = getFlowSkyIndex(nPanType)
      set_A_Star_HouseIndex(nPanType,nStar,@@ByKue[nFlowSkyIndex])
    else
      # 勸學齋主排法
      # 年 干 甲 乙 丙 丁 戊 己 庚 辛 壬 癸
      # 天魁  未 申 酉 亥 丑 子 丑 寅 卯 巳
      # nFlowSkyIndex = getFlowSkyIndex(nPanType)
      set_A_Star_HouseIndex(nPanType,nStar,@@ByKue_Su[nFlowSkyIndex])
    end
  end

  def cp_get_A_Star_25(nPanType,nStar)   #天鉞
    nDisPanType = Star.Get3ParPanType(@par_b2425Type,nPanType)
    nFlowSkyIndex,nFlowEarthIndex = cp_getFlowSkyEarthIndex_PanType(nDisPanType)
    if (@par_yuekui == Star::PAN_YUEKUI_NORMAL) then
      # nFlowSkyIndex = getFlowSkyIndex(nPanType)
      set_A_Star_HouseIndex(nPanType,nStar,@@ByUei[nFlowSkyIndex])
    else
      # 勸學齋主排法
      # 年 干 甲 乙 丙 丁 戊 己 庚 辛 壬 癸
      # 天鉞  丑 子 亥 酉 未 申 未 午 巳 卯
      # nFlowSkyIndex = getFlowSkyIndex(nPanType)
      set_A_Star_HouseIndex(nPanType,nStar,@@ByUei_Su[nFlowSkyIndex])
    end
  end
  #乙級星
  def cp_B_Stars(nPanType)
    (1..12).each do |i|
      @AllPanInfo[nPanType][Earth.GetKeyName(i)][Star::B_STAR] = Array.new
    end
    (1..Star::B_STAR_COUNT).each do |i|
      if (gHouse_GetBStarDisplay(i))
        eval_cp_B_Star(nPanType,i)
      end
    end
  end

  def get_B_Star(nPanType,nStar)
    if (@AllPanInfo[nPanType][Star::B_STAR_POS][Star.Star2StarIndex(nStar)] == nil) then
      eval_cp_B_Star(nPanType,nStar)
    end
    return @AllPanInfo[nPanType][Star::B_STAR_POS][Star.Star2StarIndex(nStar)]
  end
  def get_B_Star_HouseEarth(nPanType,nStar)
    nIndex = get_B_Star(nPanType,nStar)
    return Star.HouseIndex2Earth(nIndex)
  end

  def set_B_Star(nPanType,nStar,nValue)
    @AllPanInfo[nPanType][Star::B_STAR_POS][Star.Star2StarIndex(nStar)] = Earth.Modify(nValue)
  end

  def set_B_Star_Info(nPanType,nStar,nEarth,starInfo)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::B_STAR] == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::B_STAR] = Array.new
    end
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::B_STAR].find_index(starInfo) == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::B_STAR].push(starInfo)
    end
  end

  # index 0 : name,1 : name key, 2 : id, 3 : miowong
  def make_B_StarInfo(nPanType,nStar)
    a = Array.new
    a.push(Star.GetBStarName(nStar,@par_FourHuaType)) # 0
    a.push(Star.GetBStarNameKey(nStar,@par_FourHuaType)) # 1
    a.push(nStar) # 2
    nEarth = Star.HouseIndex2Earth(get_B_Star(nPanType,nStar))
    a.push(findMioWong(B_STAR,nEarth,nStar))  # 3
    a.push(Hash.new)  # 4  # 先天四化
    a.push(nil) # 5     宮干自化
    a.push("b") # 6 BStar
    return a.clone
  end

  def IsBStarExist?(nPanType,nStar)
    return (Earth.Exist?(get_B_Star(nPanType,nStar)))
  end

  def eval_Set_B_Star_Info(nPanType,nStar)
      expName = "set_B_Star_Info(nPanType,#{nStar},Star.HouseIndex2Earth(#{get_B_Star(nPanType,nStar)}),make_B_StarInfo(nPanType,#{nStar}))"
      eval(expName)
  end

  def eval_cp_B_Star(nPanType,nStar)
    sFuncName = "cp_B_Star_" + nStar.to_s + "(nPanType,nStar)"
    bSet = eval(sFuncName)

    if (bSet && gHouse_GetBStarDisplay(nStar)) then
      eval_Set_B_Star_Info(nPanType,nStar)
    end
  end

  def GetBStar1House(nFlowTime)
    # nFlowTime: 是指從子(0)到亥(11)的值
    # 順序  :0 ,1 ,2 ,3 ,4 ,5 ,6 ,7 ,8 ,9 ,10,11
    # 地支  :子,丑,寅,卯,辰,巳,午,未,申,酉,戌,亥
    # 地劫  :亥,子,丑,寅,卯,辰,巳,午,未,申,酉,戌
    bStart1 = [11,0,1,2,3,4,5,6,7,8,9,10]
    return Star.EarthIndex2HouseIndex(bStart1[nFlowTime])
  end

  #on House start from 寅(0)
  def cp_B_Star_1(nPanType,nStar)       #地劫
    set_B_Star(nPanType,nStar,GetBStar1House(fdg_WT(nPanType)))
    return true
  end

  #計算天空之宮位,原算式,不易改公式,占驗門不易用
  # 天空搜尋表
  # 不同派別之天空,只要填的出安時系星表的.  就可以修改bStart2來修改
  def GetBStar2House(nFlowTime)
      # nFlowTime: 是指從子(0)到亥(11)的值
      # 順序  :0 ,1 ,2 ,3 ,4 ,5 ,6 ,7 ,8 ,9 ,10,11
      # 地支  :子,丑,寅,卯,辰,巳,午,未,申,酉,戌,亥
      # 天空  :亥,戌,酉,申,未,午,巳,辰,卯,寅,丑,子
    bStart2 = [11,10,9,8,7,6,5,4,3,2,1,0]
    return Star.EarthIndex2HouseIndex(bStart2[nFlowTime])
      # 天空-2:酉,申,未,午,巳,辰,卯,寅,丑,子,亥,戌
      # short bStart2[]={9,8,7,6,5,4,3,2,1,0,11,10}
      # return bStart2[nFlowTime]
  end

  def cp_B_Star_2(nPanType,nStar)       #天空
    set_B_Star(nPanType,nStar,GetBStar2House(fdg_WT(nPanType)))
    return true
  end

  def cp_B_Star_3(nPanType,nStar)       #天刑
    set_B_Star(nPanType,nStar,7 + fdg_ELM(nPanType) - 1)
    return true
  end

  def cp_B_Star_4(nPanType,nStar)       #天姚
    set_B_Star(nPanType,nStar,fdg_ELM(nPanType) - 2)
    return true
  end

  def get_B_Star_5_OrgHouseIndex()
    if (@par_HorseType == PAN_HORSE_YEAR) then  #以年排法
      # 2016/1/20 文華修改，天馬都依本命出生日期
      nFlowEarthIndex = getFlowEarthIndex(Cfate::PAN_NORMAL)
      m = nFlowEarthIndex - 2
    else  #以月排法
      # m = (fdg_ELM(nPanType) - 1)
      m = (uig_ELM() - 1) # 2016/1/20 文華修改，天馬都依本命出生日期
    end

    return  (Earth.Modify(m) % 4)
  end

  def NeedBStar5?(nPanType)
    if ((nPanType >= Cfate::PAN_FLOWYEAR) && (@par_FourHuaType != Star::PAN_FH_HSU) && (@dis_FlowMa == Star::STAR_DIS_FLOW_FLOW)) then
      # 其餘門派的流年以下只顯示天馬或流馬，因位置相同
      return true # 2016/1/20 文華修改，天馬通通顯示
    else
      # 占驗門在流年以下天馬跟流馬位置不同，但都要顯示
      return true
    end
  end

  # 流年天馬 稱流馬，由甲年起寅位逆行
  #  十八飛星
  #  欽天派
  def GetBStar5HouseIndex(nPanType)
    if (@par_FourHuaType == Star::PAN_FH_HSU) then
      if ((nPanType >= Cfate::PAN_FLOWYEAR) && (@dis_FlowMa == Star::STAR_DIS_FLOW_ORG)) then
        bBStar5_Hsu = [2,3,0,1,2,3,0,1,2,3]
        nFlowSkyIndex = getFlowSkyIndex(nPanType)
        return @@ByHorse[bBStar5_Hsu[nFlowSkyIndex]]
      else
        return @@ByHorse[get_B_Star_5_OrgHouseIndex()]
      end
    else
      # 2016/1/20 文華修改，天馬都依本命出生日期
      # return @@ByHorse[get_B_Star_5_OrgHouseIndex(nPanType)]
      return @@ByHorse[get_B_Star_5_OrgHouseIndex()]
    end
  end

  def cp_B_Star_5(nPanType,nStar)       #天馬
    if (NeedBStar5?(nPanType)) then
      set_B_Star(nPanType,nStar,GetBStar5HouseIndex(nPanType))
    end
    return NeedBStar5?(nPanType)
  end

  def cp_B_Star_6(nPanType,nStar)       #解神
    m = (fdg_ELM(nPanType) - 1) / 2
    m = 6 + (m << 1)
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_7(nPanType,nStar)       #陰煞
    m = (fdg_ELM(nPanType) - 1) % 6
    m = 12 - (m << 1)
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_8(nPanType,nStar)       #天喜
    m = GetFStar5HouseIndex(nPanType)   # 天喜跟著流鸞走
    m = (m <= 5) ? (m + 6) : (m - 6)
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_9(nPanType,nStar)       #天官
    nFlowSkyIndex = getFlowSkyIndex(nPanType)
    m = @@ByTGuan[nFlowSkyIndex]
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_10(nPanType,nStar)      #天福
    nFlowSkyIndex = getFlowSkyIndex(nPanType)
    m = @@ByTFu2[nFlowSkyIndex]
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_11(nPanType,nStar)      #天哭
    nFlowEarthIndex = getFlowEarthIndex(nPanType)
    m = Earth.Modify(4 - nFlowEarthIndex)
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_12(nPanType,nStar)      #天虛
    nFlowEarthIndex = getFlowEarthIndex(nPanType)
    m = 4 + nFlowEarthIndex
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_13(nPanType,nStar)      #龍池
    nFlowEarthIndex = getFlowEarthIndex(nPanType)
    m = 2 + nFlowEarthIndex
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_14(nPanType,nStar)      #鳳閣
    nFlowEarthIndex = getFlowEarthIndex(nPanType)
    m = 8 - nFlowEarthIndex
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def NeedBStar15?(nPanType)
    if ((nPanType >= Cfate::PAN_FLOWYEAR) && (@par_FourHuaType != Star::PAN_FH_HSU) && (@dis_FlowLuan == Star::STAR_DIS_FLOW_FLOW)) then
      # 其餘門派的流年以下只顯示紅鸞或流鸞，因位置相同
      return false
    else
      # 占驗門在流年以下紅鸞跟流鸞位置不同，但都要顯示
      return true
    end
  end

  def get_B_Star_15_OrgHouseIndex(nPanType)
    if (@par_FourHuaType == Star::PAN_FH_HSU) then
      m = 1 - cp_getEarthIndex()
    else
      m = 1 - getFlowEarthIndex(nPanType)
    end
    return Earth.Modify(m)
  end

  # 紅鸞
  def GetBStar15HouseIndex(nPanType)
    if ((nPanType >= Cfate::PAN_FLOWYEAR) && (@par_FourHuaType == Star::PAN_FH_HSU) && (@dis_FlowLuan == Star::STAR_DIS_FLOW_ORG)) then
      return GetFStar5HouseIndex(nPanType)  # 流鸞
    else
      return get_B_Star_15_OrgHouseIndex(nPanType)
    end
  end

  def cp_B_Star_15(nPanType,nStar)      #紅鸞
    if (NeedBStar15?(nPanType)) then
      set_B_Star(nPanType,nStar,GetBStar15HouseIndex(nPanType))
    end
    return NeedBStar15?(nPanType)
  end

  def get_B_Star_16_17_Base(nPanType)
    nFlowEarthIndex = getFlowEarthIndex(nPanType)
    n = Earth.Modify(nFlowEarthIndex - 2)

    return (n / 3) * 3
  end

  def cp_B_Star_16(nPanType,nStar)      #孤辰
    m = Earth.Modify(3 + get_B_Star_16_17_Base(nPanType))
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_17(nPanType,nStar)      #寡宿
    m = Earth.Modify(11 + get_B_Star_16_17_Base(nPanType))
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def get_B_Star_18_19_Base(nPanType)
  nFlowEarthIndex = getFlowEarthIndex(nPanType)
    m = nFlowEarthIndex / 3
    n = nFlowEarthIndex % 3

    return m,n
  end

  def cp_B_Star_18(nPanType,nStar)      #蜚廉
    nFlowEarthIndex = getFlowEarthIndex(nPanType)
    m,n = get_B_Star_18_19_Base(nPanType)
    if (m == 0) then
      m = 6 + n
    elsif (m == 1 || m == 3) then
      m = nFlowEarthIndex
    else
      m = n
    end
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_19(nPanType,nStar)      #破碎
    m,n = get_B_Star_18_19_Base(nPanType)
    if (n == 0) then
      m = 3
    elsif (n == 1) then
      m = 11
    else
      m = 7
    end
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_20(nPanType,nStar)      #台輔
    m = 4 + fdg_WT(nPanType)
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_21(nPanType,nStar)      #封誥
    m = fdg_WT(nPanType)
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_22(nPanType,nStar)      #天巫
    m = @@ByWu[(fdg_ELM(nPanType) - 1) % 4]
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_23(nPanType,nStar)      #天月
    m =@@ByTMoon[fdg_ELM(nPanType) - 1]
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_24(nPanType,nStar)      #三台
    m = get_A_Star_HouseIndex(nPanType,17) + fdg_ED(nPanType) - 1  #參考左輔
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_25(nPanType,nStar)      #八座
    m = get_A_Star_HouseIndex(nPanType,18) - fdg_ED(nPanType) + 1   #參考右弼
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_26(nPanType,nStar)      #恩光
    m = get_A_Star_HouseIndex(nPanType,15) + fdg_ED(nPanType) - 2   #參考文昌
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_27(nPanType,nStar)      #天貴
    m = get_A_Star_HouseIndex(nPanType,16) + fdg_ED(nPanType) - 2   #參考文曲
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_28(nPanType,nStar)      #天才
    nFlowEarthIndex = getFlowEarthIndex(nPanType)
    nFlowLife = cp_GetFlowLife(nPanType)

    m = nFlowLife + nFlowEarthIndex
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_29(nPanType,nStar)      #天壽
    if (nPanType == Cfate::PAN_NORMAL || nPanType == Cfate::PAN_TENYEAR) then
      m = cp_getBody() + cp_getEarthIndex()
      set_B_Star(nPanType,nStar,m)
      return true
    end
    return false
  end

  # 截路空亡
  # 占驗門
  #   生年干
  #            甲,己,乙,庚,丙,辛,丁,壬,戊,癸
  #              0 ,5 ,1 ,6 ,2 ,7 ,3 ,8 ,4 ,9
  #        申,酉,午,未,辰,巳,寅,卯,戌-,亥-
  #   重排順序==>
  #            甲,乙,丙,丁,戊,己,庚,辛,壬,癸
  #              0 ,1 ,2 ,3 ,4 ,5 ,6 ,7 ,8 ,9
  #        申,午,辰,寅,戌-,酉,未,巳,卯,亥-
  #
  # 十八飛星
  #   生年干
  #            甲,己,乙,庚,丙,辛,丁,壬,戊,癸
  #              0 ,5 ,1 ,6 ,2 ,7 ,3 ,8 ,4 ,9
  #        申,酉,午,未,辰,巳,寅,卯,子,丑
  #   重排順序==>
  #            甲,乙,丙,丁,戊,己,庚,辛,壬,癸
  #              0 ,1 ,2 ,3 ,4 ,5 ,6 ,7 ,8 ,9
  #        申,午,辰,寅,子,酉,未,巳,卯,丑
  # 欽天派
  #   生年干
  #            甲,己,乙,庚,丙,辛,丁,壬,戊,癸
  #              0 ,5 ,1 ,6 ,2 ,7 ,3 ,8 ,4 ,9
  #        申,酉,午,未,辰,巳,寅,卯,子,丑
  #   重排順序==>
  #            甲,乙,丙,丁,戊,己,庚,辛,壬,癸
  #              0 ,1 ,2 ,3 ,4 ,5 ,6 ,7 ,8 ,9
  #        申,午,辰,寅,子,酉,未,巳,卯,丑
  def GetBStar30HouseIndex(nFlowSkyIndex)
    bStart30_Hsu = [8,6,4,2,10,9,7,5,3,11]
    bStart30_18FlyStar = [8,6,4,2,0,9,7,5,3,1]
    bStart30_Steven = [8,6,4,2,0,9,7,5,3,1]

    if (@par_GiaKungType == PAN_GK_TRAD) then
      return Star.EarthIndex2HouseIndex(bStart30_Steven[nFlowSkyIndex])
    elsif (@par_GiaKungType == PAN_GK_DIFF) then
      return Star.EarthIndex2HouseIndex(bStart30_Hsu[nFlowSkyIndex])
    else
      return Star.EarthIndex2HouseIndex(bStart30_18FlyStar[nFlowSkyIndex])
    end
  end

  def cp_B_Star_30(nPanType,nStar)      #截空
    #nFlowSkyIndex = getFlowSkyIndex(nPanType)
    nDisPanType = Star.Get3ParPanType(@par_b30Type,nPanType)
    nFlowSkyIndex,nFlowEarthIndex = cp_getFlowSkyEarthIndex_PanType(nDisPanType)
    m = GetBStar30HouseIndex(nFlowSkyIndex)
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_31(nPanType,nStar)      #旬中
    nDisPanType = Star.Get3ParPanType(@par_b3132Type,nPanType)
    nFlowSkyIndex,nFlowEarthIndex = cp_getFlowSkyEarthIndex_PanType(nDisPanType)
    #nFlowEarthIndex = getFlowEarthIndex(nPanType)
    #nFlowSkyIndex = getFlowSkyIndex(nPanType)
    m = Earth.Modify(nFlowEarthIndex - nFlowSkyIndex - 2)
    m = Earth.Modify(m - 2)
#puts("nSkyIndex:#{nFlowSkyIndex},nFlowEarthIndex:#{nFlowEarthIndex},m:#{m}")
    set_B_Star(nPanType,nStar,m)
    return true
  end

  def cp_B_Star_32(nPanType,nStar)      #旬空
    m = get_B_Star(nPanType,31) + 1
    set_B_Star(nPanType,nStar,m)
    return true
  end

 # 天傷永遠在朋友宮
  def cp_B_Star_33(nPanType,nStar)      #天傷
#   if (cp_CheckDirectionClock()) then
#     m = 8 - 1
#   else
      m = 6 - 1
#   end
    nFlowLifeHead = get_FlowLifeHouseIndex(nPanType)
    m = nFlowLifeHead + m

    set_B_Star(nPanType,nStar,m)
    return true
  end

  # 天使永遠在疾厄宮
  def cp_B_Star_34(nPanType,nStar)      #天使
#   if (cp_CheckDirectionClock()) then
#     m = 6 - 1
#   else
      m = 8 - 1
#   end
    nFlowLifeHead = get_FlowLifeHouseIndex(nPanType)
    m = nFlowLifeHead + m

    set_B_Star(nPanType,nStar,m)
    return true
  end

  # 加天廚星排法：(加在A3版中，其他版本不加)
  # 生 年 干 甲 乙 丙 丁 戊 己 庚 辛 壬 癸
  # 丙 天廚  巳 午 子 巳 午 申 寅 午 酉 亥
  #           3  4 10  3  4  6 0   4  7  9
  #
  # 子 丑寅卯辰巳午未申酉戌亥
  # 10 11 0 1 2 3 4 5 6 7 8 9
  def GetTianChuHouseIndex(nSky)
    nHouseIndex = [3,4,10,3,4,6,0,4,7,9]
    return nHouseIndex[nSky]
  end

  def cp_B_Star_35(nPanType,nStar)      #天廚
    if (gHouse_GetSkyCookDisplay()) then
      nSky = cp_getSkyIndex()
      m = GetTianChuHouseIndex(nSky)
      set_B_Star(nPanType,nStar,m)
    end
    return gHouse_GetSkyCookDisplay()
  end

  #博士星
  # 這個HouseIndex是以寅宮為0開始計算
  def get_Doctor_HouseIndex(nPanType,nDoctorIndex)
    if (!Earth.Exist?(@AllPanInfo[nPanType][Star::DOCTOR_POS][nDoctorIndex])) then
      cp_Doctors(nPanType)
    end
    return @AllPanInfo[nPanType][Star::DOCTOR_POS][nDoctorIndex]
  end
  # 這個HouseId 是指從命宮開始為1,父母宮為12
  def get_Doctor_HouseId(nPanType,nDoctor)
    nDoctorIndex = Star.Star2StarIndex(nDoctor)
    nHouseIndex = get_Doctor_HouseIndex(nPanType,nDoctorIndex)
    nHouseId = g_House_HouseIndex2HouseId(nPanType,nHouseIndex)
    return nHouseId
  end

  def set_Doctor_HouseIndex(nPanType,nDoctorIndex,nValue)
    @AllPanInfo[nPanType][Star::DOCTOR_POS][nDoctorIndex] = Earth.Modify(nValue)
  end

  def set_Doctor_Info(nPanType,nDoctorIndex,nValue,doctorInfo)
    set_Doctor_HouseIndex(nPanType,nDoctorIndex,nValue)

    nEarth = Star.HouseIndex2Earth(nValue)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::DOCTOR] == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::DOCTOR] = Array.new
    end
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::DOCTOR].find_index(doctorInfo) == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::DOCTOR].push(doctorInfo)
    end
  end

  def get_Doctor_Info(nPanType,nEarth)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::DOCTOR] == nil) then
      cp_Doctors(nPanType)
    end
    dInfo = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::DOCTOR][0]
    return dInfo
  end

  def get_Doctor_Name(nPanType,nEarth)
    dInfo = get_Doctor_Info(nPanType,nEarth)
    return dInfo[0]
  end

  def get_Doctor(nPanType,nEarth)
    dInfo = get_Doctor_Info(nPanType,nEarth)
    return dInfo[2]
  end

  def make_Doctor_Info(nDoctorIndex)
    nDoctor = Star.StarIndex2Star(nDoctorIndex)
    a = Array.new
    a.push(Star.GetDoctorName(nDoctor))
    a.push(Star.GetDoctorNameKey(nDoctor))
    a.push(nDoctor)
    return a.clone
  end

  def IsDoctorExist?(nPanType,nDoctorIndex)
    return (Earth.Exist?(get_Doctor_HouseIndex(nPanType,nDoctorIndex)))
  end

  def cp_Doctors(nPanType)
    (1..12).each do |i|
      @AllPanInfo[nPanType][Earth.GetKeyName(i)][Star::DOCTOR] = Array.new
    end

    nDocPanType = Star.Get3ParPanType(@par_DocType,nPanType)
    nSkyIndex = cp_getFlowSkyIndex_PanType(nDocPanType)
    nHouseIndex = @@BuLu[nSkyIndex]
    (0..11).each do |nDoctorIndex|
      set_Doctor_Info(nPanType,nDoctorIndex,nHouseIndex,make_Doctor_Info(nDoctorIndex))
      nHouseIndex = Earth.Modify(cp_CheckDirectionClock() ? nHouseIndex + 1 : nHouseIndex - 1)
    end
  end


  #歲建星
  def get_YearGod_HouseIndex(nPanType,nYGIndex)
    if (!Earth.Exist?(@AllPanInfo[nPanType][Star::YEARGOD_POS][nYGIndex])) then
      cp_YearGods(nPanType)
    end
    return @AllPanInfo[nPanType][Star::YEARGOD_POS][nYGIndex]
  end
  def get_YearGod_HouseId(nPanType,nYearGod)
    nYearGodIndex = Star.Star2StarIndex(nYearGod)
    nHouseIndex = get_YearGod_HouseIndex(nPanType,nYearGodIndex)
    nHouseId = g_House_HouseIndex2HouseId(nPanType,nHouseIndex)
    return nHouseId
  end

  def set_YearGod_HouseIndex(nPanType,nYGIndex,nHouseIndex)
    @AllPanInfo[nPanType][Star::YEARGOD_POS][nYGIndex] = Earth.Modify(nHouseIndex)
  end

  def set_YearGod_Info(nPanType,nYGIndex,nHouseIndex,ygInfo)
    set_YearGod_HouseIndex(nPanType,nYGIndex,nHouseIndex)

    nEarth = Star.HouseIndex2Earth(nHouseIndex)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARGOD] == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARGOD] = Array.new
    end
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARGOD].find_index(ygInfo) == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARGOD].push(ygInfo)
    end
  end

  def get_YearGod_Info(nPanType,nEarth)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARGOD] == nil) then
      cp_YearGods(nPanType)
    end
    ygInfo = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARGOD][0]
    return ygInfo
  end

  def get_YearGod_Name(nPanType,nEarth)
    ygInfo = get_YearGod_Info(nPanType,nEarth)
    return ygInfo[0]
  end

  def get_YearGod(nPanType,nEarth)
    ygInfo = get_YearGod_Info(nPanType,nEarth)
    return ygInfo[2]
  end

  def make_YearGod_Info(nYGIndex)
    nYearGod = Star.StarIndex2Star(nYGIndex)
    a = Array.new
    a.push(GetYearGodName(nYearGod))
    a.push(GetYearGodNameKey(nYearGod))
    a.push(nYearGod)
    return a.clone
  end

  def IsYearGodExist?(nPanType,nYGIndex)
    return (Earth.Exist?(get_YearGod_HouseIndex(nPanType,nYGIndex)))
  end

  def cp_YearGods(nPanType)
    (1..12).each do |i|
      @AllPanInfo[nPanType][Earth.GetKeyName(i)][Star::YEARGOD] = Array.new
    end

    # 歲前 各星排法
    nDisPanType = Star.Get2ParPanType(@par_GSType,nPanType)
    m = cp_getFlowEarthIndex_3_PanType(nDisPanType)
    # if (@par_GSType == PAN_GS_LIFE) then
    #   # 本命盤固定顯示
    #   m = cp_getEarthIndex()
    # else
    #   # 本命、流年及流日變化顯示
    #   m = getFlowEarthIndex(nPanType)
    # end

    nHouseIndex = Earth.Modify(m - 2)
    (0..11).each do |nYGIndex|
      set_YearGod_Info(nPanType,nYGIndex,nHouseIndex,make_YearGod_Info(nYGIndex))
      nHouseIndex = Earth.Modify(nHouseIndex + 1)
    end
  end


  #將星
  def get_YearStar_HouseIndex(nPanType,nYSIndex)
    if (!Earth.Exist?(@AllPanInfo[nPanType][Star::YEARSTAR_POS][nYSIndex])) then
      cp_YearStars(nPanType)
    end
    return @AllPanInfo[nPanType][Star::YEARSTAR_POS][nYSIndex]
  end
  def get_YearStar_HouseId(nPanType,nYearStar)
    nYearStarIndex = Star.Star2StarIndex(nYearStar)
    nHouseIndex = get_YearStar_HouseIndex(nPanType,nYearStarIndex)
    nHouseId = g_House_HouseIndex2HouseId(nPanType,nHouseIndex)
    return nHouseId
  end

  def set_YearStar_HouseIndex(nPanType,nYSIndex,nHouseIndex)
    @AllPanInfo[nPanType][Star::YEARSTAR_POS][nYSIndex] = Earth.Modify(nHouseIndex)
  end

  def set_YearStar_Info(nPanType,nYSIndex,nHouseIndex,ysInfo)
    set_YearStar_HouseIndex(nPanType,nYSIndex,nHouseIndex)

    nEarth = Star.HouseIndex2Earth(nHouseIndex)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARSTAR] == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARSTAR] = Array.new
    end
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARSTAR].find_index(ysInfo) == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARSTAR].push(ysInfo)
    end
  end

  def get_YearStar_Info(nPanType,nEarth)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARSTAR] == nil) then
      cp_YearStars(nPanType)
    end
    ysInfo = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARSTAR][0]
    return ysInfo
  end

  def get_YearStar_Name(nPanType,nEarth)
    ysInfo = get_YearStar_Info(nPanType,nEarth)
    return ysInfo[0]
  end

  def get_YearStar(nPanType,nEarth)
    ysInfo = get_YearStar_Info(nPanType,nEarth)
    return ysInfo[2]
  end

  def make_YearStar_Info(nYSIndex)
    nYearStar = Star.StarIndex2Star(nYSIndex)
    a = Array.new
    a.push(Star.GetYearStarName(nYearStar))
    a.push(Star.GetYearStarNameKey(nYearStar))
    a.push(nYearStar)
    return a.clone
  end

  def IsYearStarExist?(nPanType,nYSIndex)
    return (Earth.Exist?(get_YearStar_HouseIndex(nPanType,nYSIndex)))
  end

  def cp_YearStars(nPanType)
    (1..12).each do |i|
      @AllPanInfo[nPanType][Earth.GetKeyName(i)][Star::YEARSTAR] = Array.new
    end

    nDisPanType = Star.Get2ParPanType(@par_GSType,nPanType)
    n = cp_getFlowEarthIndex_3_PanType(nDisPanType) % 4
    # if (@par_GSType == PAN_GS_LIFE) then
    #   # 本命盤固定顯示
    #   # n = cp_getEarthIndex() #以前是這個版本 2016/4/27 以前
    #   n = cp_getEarthIndex() % 4
    # else
    #   # 本命、流年及流日變化顯示
    #   n = getFlowEarthIndex(nPanType) % 4
    # end
    if (n == 0) then
      m = 10
    elsif (n == 1) then
      m = 7
    elsif (n == 2) then
      m = 4
    else
      m = 1
    end

    nHouseIndex = Earth.Modify(m)
    (0..11).each do |nYSIndex|
      set_YearStar_Info(nPanType,nYSIndex,nHouseIndex,make_YearStar_Info(nYSIndex))
      nHouseIndex = Earth.Modify(nHouseIndex + 1)
    end
  end



  #十二長生
  def GetBy60Five(nSky,nEarth)
    return @@By60Five[nSky][nEarth]
  end

  def Get12GodStartHouseIndex_Fail(n12GodPanType,nSkyIndex,nEarthIndex)
    if (n12GodPanType == Cfate::PAN_NORMAL || n12GodPanType == Cfate::PAN_TENYEAR) then
      nEarthIndex = Earth.Modify(nEarthIndex + 2)
      nEarthIndex = nEarthIndex >> 1
      i = nSkyIndex % 5   # 算六十納音
      i = @@ByFive[nEarthIndex * 5 + i] - 2
    else
      nSkyIndex >>= 1
      nEarthIndex >>= 1
      if (nEarthIndex >= 3) then
        nEarthIndex -= 3
      end
      i = GetBy60Five(nSkyIndex,nEarthIndex)  # 掌上訣
    end

    # m = 0 寅 ,m = 3 巳, m=6 申, m=9 亥
    if (i == 0) then
          m = 6
      elsif (i == 1) then
          m = 9
      elsif (i == 2) then
          m = 3
      elsif (i == 3) then
          m = 6
      else
          m = 0
      end

    return m
  end

  def Get12GodStartHouseIndex(n12GodPanType)
    nEarth = gHouse_GetFirstHouseEarth(n12GodPanType)
    nFive = cp_getHouseFive(n12GodPanType,nEarth)
    # nHouseIndex = 0 寅 ,nHouseIndex = 3 巳, nHouseIndex=6 申, nHouseIndex=9 亥
    if (nFive == 1) then # 水二局
          nHouseIndex = 6
      elsif (nFive == 2) then # 木三局
          nHouseIndex = 9
      elsif (nFive == 3) then # 金四局
          nHouseIndex = 3
      elsif (nFive == 4) then # 土五局
          nHouseIndex = 6
      else # 火六局
          nHouseIndex = 0
      end
# puts("n12GodPanType:#{n12GodPanType},nEarth:#{nEarth},nFive:#{nFive},nHouseIndex:#{nHouseIndex}")
    return nHouseIndex
  end

  def get_GodHouseIndex(nPanType,nGodIndex)
    if (!Earth.Exist?(@AllPanInfo[nPanType][Star::GOD_POS][nGodIndex])) then
      cp_Gods(nPanType)
    end
    return @AllPanInfo[nPanType][Star::GOD_POS][nGodIndex]
  end
  def get_God_HouseId(nPanType,nGod)
    nGodIndex = Star.Star2StarIndex(nGod)
    nHouseIndex = get_GodHouseIndex(nPanType,nGodIndex)
    nHouseId = g_House_HouseIndex2HouseId(nPanType,nHouseIndex)
    return nHouseId
  end

  def set_GodHouseIndex(nPanType,nGodIndex,nHouseIndex)
    @AllPanInfo[nPanType][Star::GOD_POS][nGodIndex] = Earth.Modify(nHouseIndex)
  end

  def set_God_Info(nPanType,nGodIndex,nHouseIndex,ysInfo)
    set_GodHouseIndex(nPanType,nGodIndex,nHouseIndex)

    nEarth = Star.HouseIndex2Earth(nHouseIndex)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::GOD] == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::GOD] = Array.new
    end
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::GOD].find_index(ysInfo) == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::GOD].push(ysInfo)
    end
  end

  def get_God_Info(nPanType,nEarth)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::GOD] == nil) then
      cp_Gods(nPanType)
    end
    gInfo = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::GOD][0]
    return gInfo
  end

  def get_God_Name(nPanType,nEarth)
    gInfo = get_God_Info(nPanType,nEarth)
    return gInfo[0]
  end

  def get_God(nPanType,nEarth)
    gInfo = get_God_Info(nPanType,nEarth)
    return gInfo[2]
  end

  def make_God_Info(nGodIndex)
    nGod = Star.StarIndex2Star(nGodIndex)
    a = Array.new
    a.push(Star.GetGodName(nGod))
    a.push(Star.GetGodNameKey(nGod))
    a.push(nGod)
    return a.clone
  end

  def IsGodExist?(nPanType,nGodIndex)
    return (Earth.Exist?(get_GodHouseIndex(nPanType,nGodIndex)))
  end

  def cp_Gods(nPanType)
    (1..12).each do |i|
      @AllPanInfo[nPanType][Earth.GetKeyName(i)][Star::GOD] = Array.new
    end

    n12GodPanType = Star.Get3ParPanType(@par_12GodType,nPanType)
    #nSkyIndex,nEarthIndex = cp_getFlowSkyEarthIndex_PanType(n12GodPanType)
    nHouseIndex = Earth.Modify(Get12GodStartHouseIndex(n12GodPanType))
    (0..11).each do |nGodIndex|
      set_God_Info(nPanType,nGodIndex,nHouseIndex,make_God_Info(nGodIndex))
      nHouseIndex = Earth.Modify(cp_CheckDirectionClock() ? nHouseIndex + 1 : nHouseIndex - 1)
    end
  end


  #流年星
  def cp_F_Stars(nPanType)
    (1..12).each do |i|
      @AllPanInfo[nPanType][Earth.GetKeyName(i)][Star::F_STAR] = Array.new
      @AllPanInfo[nPanType][Earth.GetKeyName(i)][Star::FA_STAR] = Array.new
      @AllPanInfo[nPanType][Earth.GetKeyName(i)][Star::FB_STAR] = Array.new
    end
    (1..Star::F_STAR_COUNT).each do |i|
      eval_cp_F_Star(nPanType,i)
    end
  end

  def get_F_Star(nPanType,nStar)
    if (!Earth.Exist?(@AllPanInfo[nPanType][Star::F_STAR_POS][Star.Star2StarIndex(nStar)])) then
      eval_cp_F_Star(nPanType,nStar)
    end
    return @AllPanInfo[nPanType][Star::F_STAR_POS][Star.Star2StarIndex(nStar)]
  end

  def set_F_Star(nPanType,nStar,nValue)
    @AllPanInfo[nPanType][Star::F_STAR_POS][Star.Star2StarIndex(nStar)] = Earth.Modify(nValue)
  end

  def set_F_Star_Info(nPanType,nStar,nEarth,starInfo)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::F_STAR] == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::F_STAR] = Array.new
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::FA_STAR] = Array.new
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::FB_STAR] = Array.new
    end
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::F_STAR].find_index(starInfo) == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::F_STAR].push(starInfo)
    end
    if (get_F_StarType(nStar) == "fa") then
      if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::FA_STAR].find_index(starInfo) == nil) then
        @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::FA_STAR].push(starInfo)
      end
    end
    if (get_F_StarType(nStar) == "fb") then
      if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::FB_STAR].find_index(starInfo) == nil) then
        @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::FB_STAR].push(starInfo)
      end
    end
  end

  def get_HouseFullFStarInfo_Key(nPanType,nEarth,sFKey)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][sFKey] == nil) then
      cp_F_Stars(nPanType)
    end
    return @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][sFKey]
  end
  def get_HouseFStarInfo_Key(nPanType,nEarth,nIndex,sFKey)
    starInfos = get_HouseFullFStarInfo_Key(nPanType,nEarth,sFKey)
    starInfo = starInfos[nIndex]
    return starInfo
  end

  def get_HouseFStarLength_Key(nPanType,nEarth,sFKey)
    starInfos = get_HouseFullFStarInfo_Key(nPanType,nEarth,sFKey)
    return starInfos.length
  end

  def get_HouseFStarInfo(nPanType,nEarth,nIndex)
    return get_HouseFStarInfo_Key(nPanType,nEarth,nIndex,Star::F_STAR)
  end

  def get_HouseFStarLength(nPanType,nEarth)
    return get_HouseFStarLength_Key(nPanType,nEarth,Star::F_STAR)
  end

  def get_HouseFStarInfos(nPanType,nEarth)
    nLength = get_HouseFStarLength(nPanType,nEarth)
    aStar = Array.new
    (0...nLength).each do |nIndex|
      aStar.push(get_HouseFStarInfo(nPanType,nEarth,nIndex))
    end
    return aStar
  end

  def get_HouseFAStarInfo(nPanType,nEarth,nIndex)
    return get_HouseFStarInfo_Key(nPanType,nEarth,nIndex,Star::FA_STAR)
  end
  def get_HouseFAStarLength(nPanType,nEarth)
    return get_HouseFStarLength_Key(nPanType,nEarth,Star::FA_STAR)
  end

  def get_HouseFBStarInfo(nPanType,nEarth,nIndex)
    return get_HouseFStarInfo_Key(nPanType,nEarth,nIndex,Star::FB_STAR)
  end
  def get_HouseFBStarLength(nPanType,nEarth)
    return get_HouseFStarLength_Key(nPanType,nEarth,Star::FB_STAR)
  end

  def get_HouseFAStarInfos(nPanType,nEarth)
    nLength = get_HouseFAStarLength(nPanType,nEarth)
    aStar = Array.new
    (0...nLength).each do |nIndex|
      aStar.push(get_HouseFAStarInfo(nPanType,nEarth,nIndex))
    end
    return aStar
  end

  def get_HouseFBStarInfos(nPanType,nEarth)
    nLength = get_HouseFBStarLength(nPanType,nEarth)
    aStar = Array.new
    (0...nLength).each do |nIndex|
      aStar.push(get_HouseFBStarInfo(nPanType,nEarth,nIndex))
    end
    return aStar
  end

  def Star.Get_FStarInfo_Name(starInfo)
    return starInfo[0]
  end
  def Star.Get_FStarInfo_NameKey(starInfo)
    return starInfo[1]
  end
  def Star.Get_FStarInfo_Star(starInfo)
    return starInfo[2]
  end

  def make_F_StarInfo(nStar)
    a = Array.new
    a.push(Star.GetFStarName(nStar))
    a.push(Star.GetFStarNameKey(nStar))
    a.push(nStar)
    a.push(nil) # 3 廟旺
    a.push(Hash.new)  # 4  # 先天四化
    a.push(nil) # 5     宮干自化
    a.push(get_F_StarType(nStar)) # 6
    return a.clone
  end

  def get_F_StarType(nStar)
    if (nStar.between?(1,3) || nStar == 6) then
      return "fa"
    end
    return "fb"
  end

  def IsFStarExist?(nPanType,nStar)
    return (Earth.Exist?(get_F_Star(nPanType,nStar)))
  end

  def eval_Set_F_Star_Info(nPanType,nStar)
      expName = "set_F_Star_Info(#{nPanType},#{nStar},Star.HouseIndex2Earth(#{get_F_Star(nPanType,nStar)}),make_F_StarInfo(#{nStar}))"
      eval(expName)
  end

  def eval_cp_F_Star(nPanType,nStar)
    sFuncName = "cp_F_Star_" + nStar.to_s + "(#{nPanType},#{nStar})"
    bSet = eval(sFuncName)

    if (bSet) then
      eval_Set_F_Star_Info(nPanType,nStar)
    end
  end

  def NeedFStar1_2_3?(nPanType)
    return ((nPanType >= Cfate::PAN_TENYEAR) && (@dis_FlowLYT == Star::STAR_DIS_FLOW_FLOW))
  end

  def GetFStar1HouseIndex(nPanType)
    if (NeedFStar1_2_3?(nPanType)) then
      nLuSky = get_A_Star_19_Sky(nPanType)
      return @@BuLu[nLuSky]
    end
  end

  def cp_F_Star_1(nPanType,nStar)       # 流祿
    if (NeedFStar1_2_3?(nPanType)) then
      set_F_Star(nPanType,nStar,GetFStar1HouseIndex(nPanType))
    end
    return NeedFStar1_2_3?(nPanType)
  end

  def cp_F_Star_2(nPanType,nStar)       #流羊
    if (NeedFStar1_2_3?(nPanType)) then
      set_F_Star(nPanType,nStar,get_F_Star(nPanType,1) + 1)
    end
    return NeedFStar1_2_3?(nPanType)
  end

  def cp_F_Star_3(nPanType,nStar)       #流陀
    if (NeedFStar1_2_3?(nPanType)) then
      set_F_Star(nPanType,nStar,get_F_Star(nPanType,1) - 1)
    end
    return NeedFStar1_2_3?(nPanType)
  end

  def NeedFStar4?(nPanType)
    return ((nPanType >= Cfate::PAN_FLOWYEAR) && (@dis_FlowMa == Star::STAR_DIS_FLOW_FLOW))
  end

  # 流年天馬 稱流馬，由甲年起寅位逆行
  #  十八飛星
  #  欽天派
  def GetFStar4HouseIndex(nPanType)
    if (@par_FourHuaType == Star::PAN_FH_HSU) then
      bBStar5_Hsu = [2,3,0,1,2,3,0,1,2,3]
      flowSky = getFlowSkyIndex(nPanType)
      return @@ByHorse[bBStar5_Hsu[flowSky]]
    else
      return @@ByHorse[get_B_Star_5_OrgHouseIndex_liuma(nPanType)]
    end
  end
  def get_B_Star_5_OrgHouseIndex_liuma(nPanType)
    # 流馬不管 以年排法跟以月排法 ， 通通以流年排法
    nFlowEarthIndex = getFlowEarthIndex(nPanType)
    m = nFlowEarthIndex - 2

    return  (Earth.Modify(m) % 4)
  end

  def cp_F_Star_4(nPanType,nStar)       #流馬
    if (NeedFStar4?(nPanType)) then
      set_F_Star(nPanType,nStar,GetFStar4HouseIndex(nPanType))
    end
    return NeedFStar4?(nPanType)
  end

  def NeedFStar5?(nPanType)
    return ((nPanType >= Cfate::PAN_FLOWYEAR) && (@dis_FlowLuan == Star::STAR_DIS_FLOW_FLOW))
  end

  # 流鸞，由子年起卯位逆行
  def GetFStar5HouseIndex(nPanType)
    if (@par_FourHuaType == Star::PAN_FH_HSU) then
      bBStart15_Hsu = [1,4,7,6,9,8,11,10,1,0,3,2]
      nFlowEarthIndex = getFlowEarthIndex(nPanType)
      return bBStart15_Hsu[nFlowEarthIndex]
    else
      return get_B_Star_15_OrgHouseIndex(nPanType)
    end
  end

  def cp_F_Star_5(nPanType,nStar)       # 紅鸞
    if (NeedFStar5?(nPanType)) then
      set_F_Star(nPanType,nStar,GetFStar5HouseIndex(nPanType))
    end
    return NeedFStar5?(nPanType)
  end

  # 流年文昌 稱流昌，由流年太歲擎羊前二位
  # 甲 乙 丙 丁 戊 己 庚 辛 壬 癸
  # 巳 午 申 酉 申 酉 亥 子 寅 卯
  def GetFStar6HouseIndex(nPanType,nFlowSkyIndex)
      if (!NeedFStar6?(nPanType)) then
        return -1
      end

    bAStart27_Hsu = [3,4,6,7,6,7,9,10,0,1]
    # 流年干
    # 甲 乙 丙 丁 戊 己 庚 辛 壬 癸
    #  0  1 2 3 4 5 6 7 8 9
    # 巳 午 申 酉 申 酉 亥 子 寅 卯
    #  3  4 6 7 6 7 9 10  0 1
    #
    return bAStart27_Hsu[nFlowSkyIndex]
  end

  def NeedFStar6?(nPanType)
    return ((nPanType >= Cfate::PAN_FLOWYEAR) && (gHouse_GetFlowChanDisplay()))
  end

  def cp_F_Star_6(nPanType,nStar)       # 流昌
    if (NeedFStar6?(nPanType)) then
      nFlowSkyIndex = getFlowSkyIndex(nPanType)
      set_F_Star(nPanType,nStar,GetFStar6HouseIndex(nPanType,nFlowSkyIndex))
    end
    return NeedFStar6?(nPanType)
  end

  # 特殊星,會重覆出現
  def cp_S_Stars(nPanType)
    (1..12).each do |i|
      @AllPanInfo[nPanType][Earth.GetKeyName(i)][Star::S_STAR] = Array.new
    end
    (1..Star::S_STAR_COUNT).each do |i|
      eval_cp_S_Star(nPanType,i)
    end
  end

  def get_S_Star(nPanType,nStar)
    if (@AllPanInfo[nPanType][Star::S_STAR_POS][Star.Star2StarIndex(nStar)] == nil) then
      cp_S_Stars(nPanType)
    end
    return @AllPanInfo[nPanType][Star::S_STAR_POS][Star.Star2StarIndex(nStar)]
  end

  def get_S_Star_ByIndex(nPanType,nStar,nIndex)
    if (@AllPanInfo[nPanType][Star::S_STAR_POS][Star.Star2StarIndex(nStar)] == nil) then
      cp_S_Stars(nPanType)
    end
    return @AllPanInfo[nPanType][Star::S_STAR_POS][Star.Star2StarIndex(nStar)][nIndex]
  end

  def set_S_Star(nPanType,nStar,houseArray)
    @AllPanInfo[nPanType][Star::S_STAR_POS][Star.Star2StarIndex(nStar)] = houseArray
  end

  def set_S_Star_Info(nPanType,nStar,nIndex,nEarth,starInfo)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::S_STAR] == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::S_STAR] = Array.new
    end
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::S_STAR].find_index(starInfo) == nil) then
      @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::S_STAR].push(starInfo)
    end
  end

  def make_S_StarInfo(nStar)
    a = Array.new
    a.push(Star.GetSStarName(nStar))
    a.push(Star.GetSStarNameKey(nStar))
    a.push(nStar)
    a.push(nil) # 3 廟旺
    a.push(Hash.new)  # 4  # 先天四化
    a.push(nil) # 5     宮干自化
    a.push("s") # 6
    return a.clone
  end

  def get_HouseFullSStarInfos(nPanType,nEarth)
    if (@AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::S_STAR] == nil) then
      cp_S_Stars(nPanType)
    end
    return @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::S_STAR]
  end
  def get_HouseSStarInfo(nPanType,nEarth,nIndex)
    starInfos = get_HouseFullSStarInfos(nPanType,nEarth)
    starInfo = starInfos[nIndex]
    return starInfo
  end

  def get_HouseSStarLength(nPanType,nEarth)
    starInfos = get_HouseFullSStarInfos(nPanType,nEarth)
    return starInfos.length
  end

  def get_HouseSStarInfos(nPanType,nEarth)
    nLength = get_HouseSStarLength(nPanType,nEarth)
    aStar = Array.new
    (0...nLength).each do |nIndex|
      aStar.push(get_HouseSStarInfo(nPanType,nEarth,nIndex))
    end
    return aStar
  end

  def Star.Get_SStarInfo_Name(starInfo)
    return starInfo[0]
  end
  def Star.Get_SStarInfo_NameKey(starInfo)
    return starInfo[1]
  end
  def Star.Get_SStarInfo_Star(starInfo)
    return starInfo[2]
  end

  def IsSStarExist?(nStar)
    return (get_S_Star(nStar) != nil)
  end

  def eval_Set_S_Star_Info(nPanType,nStar)
    sArray = get_S_Star(nPanType,nStar)
    (0...sArray.length).each do |nIndex|
      expName = "set_S_Star_Info(#{nPanType},#{nStar},#{nIndex},Star.HouseIndex2Earth(#{get_S_Star_ByIndex(nPanType,nStar,nIndex)}),make_S_StarInfo(#{nStar}))"
      eval(expName)
    end
  end

  def eval_cp_S_Star(nPanType,nStar)
    sFuncName = "cp_S_Star_" + nStar.to_s + "(#{nPanType},#{nStar})"
    eval(sFuncName)

    eval_Set_S_Star_Info(nPanType,nStar)
  end

  def NeedSStar1_2?()
    return true
    # return (gHouse_GetLyygDisplay())
  end

  # 找來因 找出生天干，再找命盤之天干在何位，那一個位置則是來因宮位，若有二個則二個都是
  def GetSStar1HouseIndex()
    nSkyIndex = cp_getSkyIndex()
    sArray = Array.new
    (0..11).each do |nHouseIndex|
      if (nSkyIndex == cp_get_Pan_HouseSkyIndex(Cfate::PAN_NORMAL,nHouseIndex)) then
        sArray.push(nHouseIndex)
      end
    end
    return sArray
  end

  # 來因   ,來因,業障依本命決定位置
  def cp_S_Star_1(nPanType,nStar)
    if (NeedSStar1_2?()) then
      set_S_Star(nPanType,nStar,GetSStar1HouseIndex())
    end
  end

  # 找業障 固定在丑宮位置(10)找出宮干為何，該宮干之化忌飛出之所在位置即為業障宮位
  def GetSStar2HouseIndex(nPanType)
    nSkyIndex = cp_get_Pan_HouseSkyIndex(Cfate::PAN_NORMAL,11)
    n11HuaGiStar = getFourHuaStarValue(nSkyIndex,Star::FH_GI)
    sArray = Array.new
    nHouseIndex = get_A_Star_HouseIndex(nPanType,n11HuaGiStar)
    sArray.push(nHouseIndex)

    return sArray
  end

  # 業障
  def cp_S_Star_2(nPanType,nStar)
      if (NeedSStar1_2?()) then
        set_S_Star(nPanType,nStar,GetSStar2HouseIndex(nPanType))
      end
  end

  # 四化資訊
  # 客戶自訂四化，預設用欽天派
  def Star.GetDefaultFourHua(nFourHuaType)
    if (nFourHuaType == Star::PAN_FH_HSU) then
      return @@m_dwHua_Hsu
    elsif (nFourHuaType == Star::PAN_FH_18FLY_STAR) then
      return @@m_dwHua_18FlyStar
    else
      return @@m_dwHua_Steven
    end
  end

  def Star.GetDefaultFourHuaStarValue(nFourHuaType,nSky,nFourHuaIndex)
    nSkyIndex = Sky.Sky2SkyIndex(nSky)
    dwHua = Star.GetDefaultFourHua(nFourHuaType)
    return dwHua[nSkyIndex][nFourHuaIndex]
  end

  def FindUserFourHua()  # 客戶自訂四化,預設用欽天派,以後有增加客戶設定的功能再來這裡改
    return @@m_dwHua_Steven
  end

  def GetFourHua(nFourHuaType)
    if (nFourHuaType == Star::PAN_FH_HSU) then
      return @@m_dwHua_Hsu
    elsif (nFourHuaType == Star::PAN_FH_18FLY_STAR) then
      return @@m_dwHua_18FlyStar
    elsif (nFourHuaType == Star::PAN_FH_USER) then
      return @m_dwHua_User
    else
      return @@m_dwHua_Steven
    end
  end

  def getFourHuaStarValue(nSkyIndex,nFourHuaIndex)
    dwHua = GetFourHua(@par_FourHuaType)
    return dwHua[nSkyIndex][nFourHuaIndex]
  end


  def get_FlowFourHua_SkyIndex(nPanType)
    nSkyIndex = 0
    nSkyIndex = cp_getFlowSkyIndex_PanType(nPanType)
    return nSkyIndex
  end

  def findFourHuaIndex(nSkyIndex,nStar)
    (0..3).each do |nFourHuaIndex|
      nFHStar = getFourHuaStarValue(nSkyIndex,nFourHuaIndex)
      if (nStar == nFHStar) then
        return nFourHuaIndex
      end
    end
    return nil
  end

  def gNormal_FindFourHuaSky(nStar)
    aaFourHuaSky = Array.new
    (Sky::SKY_FIRST..Sky::SKY_LAST).each do |nSky|
      nSkyIndex = Sky.Sky2SkyIndex(nSky)
      (Star::FH_LU..Star::FH_GI).each do |nFourHuaIndex|
        nFHStar = getFourHuaStarValue(nSkyIndex,nFourHuaIndex)
        if (nStar == nFHStar) then
          aFourHuaSky = Array.new
          aFourHuaSky.push(nSky)
          aFourHuaSky.push(nFourHuaIndex)
          aaFourHuaSky.push(aFourHuaSky)
        end
      end
    end
    return aaFourHuaSky
  end

  def Star.GetFourHuaSky_Sky(hFourHuaSky)
    return hFourHuaSky[Sky::SKY_KEY]
  end

  def Star.GetFourHuaSky_AStar(hFourHuaSky)
    return hFourHuaSky[Star::A_STAR]
  end

  def Star.GetFourHuaSky_FourHua(hFourHuaSky)
    return hFourHuaSky[Star::FOUR_HUA]
  end

  def getFourHuaHashValue(hFourHua,nPanType)
    return hFourHua[Cfate.gGetPanTypeHashKey(nPanType)]
  end

  def getFourHuaIndex(hFourHua,nPanType)
    nFourHua = getFourHuaHashValue(hFourHua,nPanType)
    return nFourHua
  end

  def getFourHuaStr(nFourHuaIndex)
    if (nFourHuaIndex == nil) then
      return Cfate.GetSpace(1)
    else
      return Star.GetFourHuaStr(nFourHuaIndex)
    end
  end

  # 原先的四化版本
  def findFourHua_Steven(nPanType,nStar)
    hFourHua = Hash.new
    nPanType2 = Star.Get3ParPanType(@par_FlowYearHua,nPanType)
    nSkyIndex = get_FlowFourHua_SkyIndex(nPanType2)
    hFourHua[Cfate.gGetPanTypeHashKey(nPanType)] = findFourHuaIndex(nSkyIndex,nStar)
    return hFourHua
  end

  # 內湖 林信銘 老師的四化
  def findFourHua_Lin_old(nPanType,nStar)
    hFourHua = Hash.new
    # 生年四化
    nSkyIndex = get_FlowFourHua_SkyIndex(Cfate::PAN_NORMAL)
    hFourHua[Cfate.gGetPanTypeHashKey(Cfate::PAN_NORMAL)] = findFourHuaIndex(nSkyIndex,nStar)
#   hFourHua[Cfate.gGetPanTypeHashKey(nPanType)] = findFourHuaIndex(nSkyIndex,nStar)

    # 十年四化
    if (nPanType >= Cfate::PAN_TENYEAR) then
      nSkyIndex = get_FlowFourHua_SkyIndex(Cfate::PAN_TENYEAR)
      hFourHua[Cfate.gGetPanTypeHashKey(Cfate::PAN_TENYEAR)] = findFourHuaIndex(nSkyIndex,nStar)
#     hFourHua[Cfate.gGetPanTypeHashKey(nPanType + 1)] = findFourHuaIndex(nSkyIndex,nStar)
    end

    # 流年四化
    if (nPanType >= Cfate::PAN_FLOWYEAR) then
#     nSkyIndex = get_FlowFourHua_SkyIndex(Cfate::PAN_FLOWYEAR)
#     hFourHua[Cfate.gGetPanTypeHashKey(Cfate::PAN_FLOWYEAR)] = findFourHuaIndex(nSkyIndex,nStar)
      nSkyIndex = get_FlowFourHua_SkyIndex(nPanType)
      hFourHua[Cfate.gGetPanTypeHashKey(nPanType)] = findFourHuaIndex(nSkyIndex,nStar)
    end

    return hFourHua
  end

  def Star.IsPanTypeLegal?(nPanType)
    return nPanType.between?(Cfate::PAN_NORMAL,Cfate::PAN_FLOWMIN)
  end

  # 往前兩個PanType
  def findFourHua_Lin(nPanTypeOrg,nStar)
    hFourHua = Hash.new
    [2,1,0].each do |nDiff|
      nPanType = nPanTypeOrg - nDiff
      if (Star.IsPanTypeLegal?(nPanType)) then
        nSkyIndex = get_FlowFourHua_SkyIndex(nPanType)
        hFourHua[Cfate.gGetPanTypeHashKey(nPanType)] = findFourHuaIndex(nSkyIndex,nStar)
      end
    end

    return hFourHua
  end

  def findFourHua(nPanType,nStar)
    if (@par_FlowYearHua != Star::PAN_YEAR_HUA_STACK) then
      return findFourHua_Steven(nPanType,nStar)
    else
      # 內湖 林信銘老師
      return findFourHua_Lin(nPanType,nStar)
    end
  end



  # 廟旺利平陷
  def IsShowMioWong?()
    return (@int_MioWongStyle != Star::INT_MW_NO_DISPLAY)
  end

  def findMioWong_Steven(nStarLevel,nEarth,nStar)
    if (nStarLevel != Star::A_STAR) then
      return nil
    end

    sArray = @@MioWong[Star.Earth2HouseIndex(nEarth)]

    (1..sArray.length).each do |i|
      if (sArray[i-1].find_index(nStar) != nil) then
        return i
      end
    end
    return nil
  end

  def findMioWong_Lin_StarIndex(nStarLevel,nStar)
    #a = Array.new(2)
    #a[0] = nStarLevel
    #a[1] = nStar
    return (@@MioWongStar.find_index([nStarLevel,nStar]))

#   (1..@@MioWongStar.length).each do |mwStar|
#     if (mwStar[0] == nStarLevel) then
#       if (mwStar[1] == nStar) then
#         return i
#       end
#     end
#   end
#   return nil
  end

  def findMioWong_Lin(nStarLevel,nEarth,nStar)
    nMioWongStarIndex = findMioWong_Lin_StarIndex(nStarLevel,nStar)
    if (nMioWongStarIndex == nil) then
      return nil
    end

    nEarthIndex = Earth.Earth2EarthIndex(nEarth)
    return @@MioWongLin[nMioWongStarIndex][nEarthIndex]
  end

  def findMioWong(nStarLevel,nEarth,nStar)
    if (!IsShowMioWong?()) then
      return nil
    end

    if (@int_MioWongStyle == Star::INT_MW_CHAR) then
      return findMioWong_Steven(nStarLevel,nEarth,nStar)
    else
      # 內湖 林信銘老師
      return findMioWong_Lin(nStarLevel,nEarth,nStar)
    end
  end

  def getMioWongStr_Steven(nMioWong)
    return Pm.GetStr("IDS_S_TYPE_LEVEL_#{nMioWong}")
  end

  def getMioWongStr_Lin(nMioWong)
    return Pm.GetStr("IDS_S_TYPE_LEVEL_LIN_#{nMioWong & 0x0F}")
  end

  def getMioWongStr(nMioWong)
    if (nMioWong == nil) then
      return Cfate.GetSpace(1)
    end
    if (@int_MioWongStyle == Star::INT_MW_CHAR) then
      return getMioWongStr_Steven(nMioWong)
    else
      # 內湖 林信銘老師
      return getMioWongStr_Lin(nMioWong)
    end
  end

  def isMioWongNeedOutline?(nMioWong)
    if (@int_MioWongStyle == Star::INT_MW_NUM) then
      return isMioWongNeedOutline_Lin?(nMioWong)
    end
    return false
  end

  def isMioWongNeedOutline_Lin?(nMioWong)
    return ((nMioWong & 0x10) == 0x10)
  end


  # 四化
  #先天四化
  def cp_set_OriginalFourHua(nPanType,nEarth,nStar,nFourHua)
    if (nFourHua != nil) then
      hFHHouseStar = Hash.new
      hFHHouseStar[Star::HOUSE_EARTH] = nEarth
      hFHHouseStar[Star::A_STAR] = nStar
      @OriginalFourHua[nPanType][nFourHua] = hFHHouseStar
    end
  end

  def cp_get_OriginalFourHua(nPanType,nFourHua)
    if (@OriginalFourHua[nPanType][nFourHua] == nil) then
      cp_A_Stars(nPanType)
    end
    hFHHouseStar = @OriginalFourHua[nPanType][nFourHua]
  end

  def cp_get_OriginalFourHua_HouseEarth(nPanType,nFourHua)
    hFHHouseStar = cp_get_OriginalFourHua(nPanType,nFourHua)
    return hFHHouseStar[Star::HOUSE_EARTH]
  end

  def cp_get_OriginalFourHua_Star(nPanType,nFourHua)
    hFHHouseStar = cp_get_OriginalFourHua(nPanType,nFourHua)
    return hFHHouseStar[Star::A_STAR]
  end

  #宮干自化
  def findSelfFourHua(nPanType,nEarth,nStar)
    nSkyIndex = cp_getHouseSkyIndex(nPanType,nEarth)
    nFourHua = findFourHuaIndex(nSkyIndex,nStar)
    return nFourHua
  end
  def cp_set_HouseSelfFourHua(nPanType,nEarth,nStar,nFourHua)
    if (nFourHua != nil) then
      hFHHouseStar = Hash.new
      hFHHouseStar[Star::A_STAR] = nStar
      hFHHouseStar[Star::FOUR_HUA] = nFourHua
      nEarthIndex = Earth.Earth2EarthIndex(nEarth)
      @SelfFourHua[nPanType][nEarthIndex].push(hFHHouseStar)
    end
  end

  # return Array of Hash
  def cp_get_HouseSelfFourHua(nPanType,nEarth)
    nEarthIndex = Earth.Earth2EarthIndex(nEarth)
    ahFourHua = @SelfFourHua[nPanType][nEarthIndex]
    return ahFourHua
  end

  def g_get_SelfFourHuaHash_Star(hFHHouseStar)
    return hFHHouseStar[Star::A_STAR]
  end

  def g_get_SelfFourHuaHash_FourHua(hFHHouseStar)
    return hFHHouseStar[Star::FOUR_HUA]
  end

  def cp_get_SelfFourHua_Star(nPanType,nEarth)
    ahFHHouseStar = cp_get_HouseSelfFourHua(nPanType,nEarth)
    aFhStar = Array.new

    ahFHHouseStar.each do |hFHHouseStar|
      aFhStar.push(g_get_SelfFourHuaHash_Star(hFHHouseStar))
    end
    return aFhStar
  end

  def cp_get_SelfFourHua_FourHua(nPanType,nEarth)
    ahFHHouseStar = cp_get_HouseSelfFourHua(nPanType,nEarth)
    aFhStar = Array.new

    ahFHHouseStar.each do |hFHHouseStar|
      aFhStar.push(hFHHouseStar[Star::FOUR_HUA])
    end
    return aFhStar
  end

end
