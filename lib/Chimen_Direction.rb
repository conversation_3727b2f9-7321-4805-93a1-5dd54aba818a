# coding: UTF-8

class Chimen


  # Direction	Description
  @@Direction_zhTW = {
	"0" =>	東,
	"1" =>	東北,
	"2" =>	北,
	"3" =>	西北,
	"4" =>	西,
	"5" =>	西南,
	"6" =>	南,
	"7" =>	東南
	}

  @@Direction_zhCN = {
	"0" =>	东,
	"1" =>	东北,
	"2" =>	北,
	"3" =>	西北,
	"4" =>	西,
	"5" =>	西南,
	"6" =>	南,
	"7" =>	东南
	}

  def Chimen.find_Direction(no,lang=Cfate::DEFAULT_LANG)
    g_out = Chimen.find_Directions(lang)
    return g_out["#{no}"]
  end
  def Chimen.find_Directions(lang)
    return @@Direction_zhTW if lang == "zh-TW"
    return @@Direction_zhCN if lang == "zh-CN"
    return @@Direction_zhCN
  end

  @@Find = ["AA1122","BB","CC","DD","EE","FF","GG","HH"]
end