require("SkyEarthFive_Function.rb")

class Eightword
	# Flow pan info
	def cp_fp_getInfo(nIndex,key,sFunc,byWhatIdx=nil,arrayIndex=nil)
		if (byWhatIdx != nil) then
			if (arrayIndex == nil) then
				if (@EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key][byWhatIdx] == nil) then
					eval(sFunc)
				end
				return @EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key][byWhatIdx]
			else
				if (@EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key][byWhatIdx][arrayIndex] == nil) then
					eval(sFunc)
				end
				return @EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key][byWhatIdx][arrayIndex]
			end
		else
			if (arrayIndex == nil) then
				if (@EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key][Eightword::ByYearIdx] == nil) then
					eval(sFunc)
				end
				return @EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key]
			else
				if (@EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key][arrayIndex] == nil) then
					eval(sFunc)
				end
				return @EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key][arrayIndex]
			end
		end
	end
	def cp_fp_setInfo(nIndex,key,byWhatIdx,byValue,arrayIndex=nil)
		byValue = cloneValue(byValue)
		if (byWhatIdx != nil) then
			if (arrayIndex == nil) then
				@EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key][byWhatIdx] = byValue
			else
				@EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key][byWhatIdx][arrayIndex] = byValue
			end
		else
			if (arrayIndex == nil) then
				@EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key] = byValue
			else
				@EightwordInfo[Eightword::FLOW_PAN][Eightword::PAN_FOUR_COL][fp_get4ColKey(nIndex)][key][arrayIndex] = byValue
			end
		end
	end
	def cp_fp_get8Words(nIndex,byWhatIdx=nil)
		mainKey = Eightword::FLOW_PAN
		sFunc = "cp_fp_GanZhi8Words(nIndex,#{@m_nPanType})"
		v = cp_get8Words(mainKey,sFunc,byWhatIdx,nIndex)
		# if (nIndex == 4) then
		# 	Pm.saveTestDb("Get8Words","#{nIndex},#{v}")
		# end
		return v
	end
	def cp_fp_set8Words(nIndex,byValue,byWhatIdx=nil)
		mainKey = Eightword::FLOW_PAN
	# Pm.saveTestDb("#{nIndex}","#{byValue}")
		cp_set8Words(mainKey,byValue,byWhatIdx,nIndex)
	end
	# 每個盤都設為流盤時之值，大運部分再加減換算
	# 本命存出生干支八字
	# 十年存本命之月柱下一個干支開始(有分順行逆行)
	# 流年以下存該流盤之干支，及保留本命未被更動之干支(例流年僅換年柱)
	def cp_fp_GanZhi8Words(nIndex,nPanType)
		if (nPanType == Cfate::PAN_NORMAL) then
			cp_fp_GanZhi8Words_TenYear(nIndex,nPanType)
		elsif (nPanType == Cfate::PAN_TENYEAR) then
			cp_fp_GanZhi8Words_TenYear(nIndex,nPanType)
		elsif (nPanType == Cfate::PAN_FLOWYEAR) then
			cp_fp_GanZhi8Words_FlowYear(nIndex,nPanType)
		elsif (nPanType == Cfate::PAN_FLOWMONTH) then
			cp_fp_GanZhi8Words_FlowMonth(nIndex,nPanType)
		elsif (nPanType == Cfate::PAN_FLOWDATE) then
			cp_fp_GanZhi8Words_FlowDate(nIndex,nPanType)
		elsif (nPanType == Cfate::PAN_FLOWTIME) then
			cp_fp_GanZhi8Words_FlowHour(nIndex,nPanType)
		end
	end

	def cp_fp_GanZhi8Words_Normal(nIndex,nPanType)
		nWYear,nWMonth,nWDate,nHour,nMinute = uig_W_YMDHm()
		h8WordsNormal = ew_get8Words(nWYear,nWMonth,nWDate,nHour,nMinute)
		cp_fp_set8Words(nIndex,h8WordsNormal)
	end

	# 十年存的是第一個大運的干支
	def cp_fp_GanZhi8Words_TenYear(nIndex,nPanType)
		# nWYear,nWMonth,nWDate,nHour,nMinute = fdg_WY(),fdg_WM(),fdg_WD(),fdg_WH(),fdg_WMI()
		# h8Words = ew_get8Words(nWYear,nWMonth,nWDate,nHour,nMinute)
		h8WordsNormal = cp_mp_get8Words()
		k = cp_CheckDirectionClock() ? (nIndex + 1) : (-1 - nIndex)
		nGz = Pm.TestNo60(h8WordsNormal[Eightword::ByMonthIdx] + k)
		h8WordsNormal[Eightword::ByYearIdx] = nGz
		cp_fp_set8Words(nIndex,h8WordsNormal)
	end
	# 流年存的是該年的干支
	def cp_fp_GanZhi8Words_FlowYear(nIndex,nPanType)
		nWYear,nWMonth,nWDate,nHour,nMinute = cp_fp_getByDateValue_FlowYear(nIndex),6,1,fdg_WH(),fdg_WMI()
		h8Words = ew_get8Words(nWYear,nWMonth,nWDate,nHour,nMinute)

		h8WordsNormal = cp_mp_get8Words()
		h8WordsNormal[Eightword::ByYearIdx] = h8Words[Eightword::ByYearIdx]
		cp_fp_set8Words(nIndex,h8WordsNormal)
	end
	# 流月存的是該年一月的干支
	def cp_fp_GanZhi8Words_FlowMonth(nIndex,nPanType)
		nWYear,nWMonth,nWDate,nWHour,nWMinute = Xdate.GetSegWDateBySegYear(fdg_SY(),nIndex + 1,@par_FirstSegment)
		# nWYear,nWMonth,nWDate = Xdate.GetWDateForFirstEDay(fdg_WY())
		# nWYear,nWMonth,nWDate = Xdate.GetWDateForFirstEDay(fdg_SY())
		# nWMonth += nIndex
		# if (nWMonth > 12) then
		# 	nWMonth -= 12
		# 	nWYear += 1
		# end
		# nHour,nMinute = fdg_WH(),fdg_WMI()
		h8Words = ew_get8Words(nWYear,nWMonth,nWDate,nWHour,nWMinute)

		h8WordsNormal = cp_mp_get8Words()
		h8WordsNormal[Eightword::ByYearIdx] = h8Words[Eightword::ByYearIdx]
		h8WordsNormal[Eightword::ByMonthIdx] = h8Words[Eightword::ByMonthIdx]
		cp_fp_set8Words(nIndex,h8WordsNormal)
	end
	# 流日存的是該月1日的干支
	def cp_fp_GanZhi8Words_FlowDate(nIndex,nPanType)
		nSYear = fdg_SY()
		nSMonth = fdg_SM()
		nSDate = cp_fp_getStartDate_FlowDate()
		nWYear,nWMonth,nWDate,nHour,nMinute,nSegmentIndex = Xdate.SegmentDate2Wdate(nSYear,nSMonth,nSDate,@par_FirstSegment)
		nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)

		nEYear,nEMonth,nEDate,bLeapMonth = Xdate.NextNEDate(nEYear,nEMonth,nEDate,bLeapMonth,nIndex)
		nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,nEMonth,nEDate,bLeapMonth)
		# nHour,nMinute = fdg_WH(),fdg_WMI()
		nHour,nMinute = 12,0
		h8Words = ew_get8Words(nWYear,nWMonth,nWDate,nHour,nMinute)

		h8WordsNormal = cp_mp_get8Words()
		h8WordsNormal[Eightword::ByYearIdx] = h8Words[Eightword::ByYearIdx]
		h8WordsNormal[Eightword::ByMonthIdx] = h8Words[Eightword::ByMonthIdx]
		h8WordsNormal[Eightword::ByDayIdx] = h8Words[Eightword::ByDayIdx]
		cp_fp_set8Words(nIndex,h8WordsNormal)
	end
	def cp_fp_getStartDate_FlowDate_static()
		nSDate = fdg_SD() - 1
		nSDate /= 12
		nSDate *= 12
		nSDate += 1
		return nSDate
	end
	def cp_fp_getStartDate_FlowDate()
		nSDate = fdg_SD()
		nSDates = cp_fp_getMax_FlowDate()
		if (nSDate <= 11) then
			return 1
		elsif (nSDate >= 12 && nSDate < 22) then
			return 11
		elsif (nSDate >= 22) then
		# 	return 21
		# elsif (nSDate == nSDates) then
			return nSDates - 11
		else
			nSDate = fdg_SD() - 1
			nSDate /= 12
			nSDate *= 12
			nSDate += 1
			return nSDate
		end
	end
	# 流時存的是該天的子時(是存早子還是晚子，目前是存晚子)
	def cp_fp_GanZhi8Words_FlowHour(nIndex,nPanType)
		nWYear,nWMonth,nWDate,nHour,nMinute = fdg_WY(),fdg_WM(),fdg_WD(),fdg_WH(),fdg_WMI()
		nHour = Pm.TestNo24(nIndex * 2)
		nMinute = 0
		h8Words = ew_get8Words(nWYear,nWMonth,nWDate,nHour,nMinute)

		h8WordsNormal = cp_mp_get8Words()
		h8WordsNormal[Eightword::ByYearIdx] = h8Words[Eightword::ByYearIdx]
		h8WordsNormal[Eightword::ByMonthIdx] = h8Words[Eightword::ByMonthIdx]
		h8WordsNormal[Eightword::ByDayIdx] = h8Words[Eightword::ByDayIdx]
		h8WordsNormal[Eightword::ByHourIdx] = h8Words[Eightword::ByHourIdx]
		cp_fp_set8Words(nIndex,h8WordsNormal)
	end

	def cp_fp_CurrentGanZhi8Words(nPanType)
		if (nPanType == Cfate::PAN_NORMAL) then
			return cp_mp_get8Words()
		else
			nIndex = cp_fp_CurrentIndex(nPanType)
			return cp_fp_get8Words(nIndex)
		end
	end
	def cp_fp_CurrentIndex(nPanType)
		if (nPanType == Cfate::PAN_TENYEAR) then
			nIndex = cp_fp_CurrentIndex_TenYear(nPanType)
		elsif (nPanType == Cfate::PAN_FLOWYEAR) then
			nIndex = cp_fp_CurrentIndex_FlowYear(nPanType)
		elsif (nPanType == Cfate::PAN_FLOWMONTH) then
			nIndex = cp_fp_CurrentIndex_FlowMonth(nPanType)
		elsif (nPanType == Cfate::PAN_FLOWDATE) then
			nIndex = cp_fp_CurrentIndex_FlowDate(nPanType)
		elsif (nPanType == Cfate::PAN_FLOWTIME) then
			nIndex = cp_fp_CurrentIndex_FlowHour(nPanType)
		end
		return nIndex
	end
	def cp_fp_CurrentIndex_TenYear(nPanType)
		nEYear = fdg_EY()
		(0..11).each do |nIndex|
			nDate = cp_fp_getByDateValue(nIndex,nPanType)
			if ((nEYear - nDate) < 10) then
				return nIndex
			end
		end
		return 0
	end
	def cp_fp_CurrentIndex_FlowYear(nPanType)
		nEYear = fdg_EY()
		(0..11).each do |nIndex|
			nDate = cp_fp_getByDateValue(nIndex,nPanType)
			if (nEYear == nDate) then
				return nIndex
			end
		end
		return 0
	end
	def cp_fp_CurrentIndex_FlowMonth(nPanType)
		# nEMonth = fdg_EM()
		# return nEMonth - 1
		return fdg_SM() - 1
	end
	def cp_fp_CurrentIndex_FlowDate(nPanType)
		nSDate = fdg_SD()
		(0..11).each do |nIndex|
			nDate = cp_fp_getByDateValue(nIndex,nPanType)
			if (nSDate == nDate) then
				return nIndex
			end
		end
		return 0
	end
	def cp_fp_CurrentIndex_FlowHour(nPanType)
		nWHour = fdg_WH()
		return Pm.TestNo24(nWHour + 1) / 2
	end

	def cp_fp_getByDateValue(nIndex,nPanType)
		if (nPanType == Cfate::PAN_NORMAL) then
			cp_fp_getByDateValue_Normal(nIndex)
		elsif (nPanType == Cfate::PAN_TENYEAR) then
			cp_fp_getByDateValue_TenYear(nIndex)
		elsif (nPanType == Cfate::PAN_FLOWYEAR) then
			cp_fp_getByDateValue_FlowYear(nIndex)
		elsif (nPanType == Cfate::PAN_FLOWMONTH) then
			cp_fp_getByDateValue_FlowMonth(nIndex)
		elsif (nPanType == Cfate::PAN_FLOWDATE) then
			cp_fp_getByDateValue_FlowDate(nIndex)
		elsif (nPanType == Cfate::PAN_FLOWTIME) then
			cp_fp_getByDateValue_FlowHour(nIndex)
		end
	end
	# 可以取得前後之干支
# 方法 ( 一 ) 直接查萬年曆
# 1、陽男陰女順行，由出生日時算起，順算至下月之「節」為止，共有幾日幾時。
# 2、陰男陽女逆行，由出生日時算起，逆算至上月之「節」為止，共有幾日幾時。
# 3、將總計之日時，以三日折算一歲，一日折算四個月，一時辰折算十天，將其加總就知道是幾歲起大運了。
# 4、所謂算到「節」，就是以立春、驚蟄、清明、立夏、芒種、小暑、立秋、白露、寒露、立冬、大雪、小寒為結束。

# 計算公式說明如下：
# 例一：男命出生年月日是：陰曆 54 年 11 月 2 日 20 時生 ( 國曆 54 年 11 月 24日 20 時 )，查萬年曆 2 日壬午日，陰男陽女逆推從 2 日逆算到上個月的 ( 節立冬 15 號子時 ) 共 17 日，以三日為 1 歲，17 除 3 = 5 餘 2 天加一歲就以 6 歲起大運

# 例二：女命出生年月日是：陰曆 54 年 11 月 2 日 20 時生 ( 國曆 54 年 11 月 24 日 20 時 )，查萬年曆 2 日壬午日，陽男陰女順數從 2 日順數到本月的 ( 節大雪 15 號申時) 共 11 日又 13 小時，以三日為 1 歲，11 除 3 = 3 歲餘 2 日又 13 小時就算加一歲，所以該女為 4 歲起大運。

# 方法 ( 二 )
# 計算公式說明如下：
# 1、應特別留意，各月份有二十九天，也有三十天，因為一日折算四個月影響很大。
# 2、接下來用日時相減，若日小需向月借位時，特別注意是借「二十九日」或「三十日」來減。
# 3、如果陽男陰女用「節」減出生日 ( 請查萬年曆 )；陰男陽女用出生日減「節」。
# 4、一律以大數目減小數目，能整除或餘數為 2，都要加 1 歲。

# 例一：男命出生年月日是：54 年 11 月 2 日 20 時生 ( 國曆 54 年 11 月 24 日 20 時 ) 陰男陽女用出生日減「節」，經查 54、11、2 該月 = 30 天，因 2 日不夠減 - 15 ( 上月節 )，所以要借 30，2 日加 30 = 32 - 15 = 17 除 3 = 5 餘 2 要加 1，5 歲加 1 = 6，所以，該員的第一大運從 6 歲起算

# 例二：女命出生年月日是：54 年 11 月 2 日 20 時生 ( 國曆 54 年 11 月 24 日 20 時 ) 陽男陰女用「節」減出生日，經查 54、11、2 節氣 = 15 日 ( 下月節 ) - 出生 2 日 = 13 日再除 3 = 4 餘 1 = 4，所以，該員的第一大運從 4 歲起算
	def cp_fp_LargeWin0_Start_Year(uig_BigWinStart)
		if (uig_BigWinStart != nil) then
			return uig_BigWinStart + uig_SY() - 1
		else
			if (@par_LargeWin == Eightword::LARGE_WIN_REAL) then
			# 實歲算法
				if (cp_mp_getByAfterYear() == 0) then
					return uig_SY()
				else
					return cp_mp_getByAfterYear() + uig_SY() - 1
				end
			elsif (@par_LargeWin == Eightword::LARGE_WIN_VIRTUAL) then
			# 虛歲算法
				nAddDay = 0
				if ((cp_mp_getByAfterMonth() > 0) || (cp_mp_getByAfterDate() > 0)) then
					nAddDay = 1
				else
					if (cp_mp_getByAfterYear() == 0) then
						nAddDay = 1
					end
				end
				return cp_mp_getByAfterYear() + uig_SY() + nAddDay - 1
			else
			# 槪略算法
				# LARGE_WIN_ABOUT
				nAddDay = 0
				if ((cp_mp_getByAfterMonth() >= 6)) then
					nAddDay = 1
				else
					if (cp_mp_getByAfterYear() == 0) then
						nAddDay = 1
					end
				end
				return cp_mp_getByAfterYear() + uig_SY() + nAddDay - 1
			end
		end
	end
	def cp_fp_LargeWin0_Start_Year_Org()
		return cp_fp_LargeWin0_Start_Year(nil)
	end
	def cp_fp_LargeWin0_Start_YearOld_Org()
		nYear = cp_fp_LargeWin0_Start_Year_Org()
		return Xdate.GetYearOld(uig_SY(),nYear)
	end

	def cp_fp_LargeWin_Start_Year(nIndex)
		nBigWinStart = cp_fp_LargeWin0_Start_Year(uig_BigWinStart())
		nBigWinStart += (nIndex * 10)
		return nBigWinStart
	end
	def cp_fp_LargeWin_Start_YearOld(nIndex)
		nYear = cp_fp_LargeWin_Start_Year(nIndex)
		return Xdate.GetYearOld(uig_SY(),nYear)
	end
	def cp_fp_getByDateValue_Normal(nIndex)
		return cp_fp_LargeWin_Start_Year(nIndex)
	end
	def cp_fp_getByDateValue_TenYear(nIndex)
		return cp_fp_LargeWin_Start_Year(nIndex)
	end
	def cp_fp_getByDateValue_FlowYear(nIndex)
		nIndexTy = cp_fp_CurrentIndex(Cfate::PAN_TENYEAR)
		nTyStart = cp_fp_getByDateValue_TenYear(nIndexTy)
		if (nTyStart > 1) then
			nTyStart -= 1
		end
		return nTyStart + nIndex
	end
	def cp_fp_getByDateValue_FlowMonth(nIndex)
		return nIndex + 1
	end
	def cp_fp_getMax_FlowDate()
		# nEDates = Xdate.GetEastMonthDays(fdg_EY(), fdg_EM(), false)
		# return nEDates
		nSDates = Xdate.GetSegMonthDaysBySegDate(fdg_SY(),fdg_SM(),@par_FirstSegment)
		return nSDates
	end
	def cp_fp_getMaxCol_FlowDate_static()
		nSDates = cp_fp_getMax_FlowDate()
		nSDate = cp_fp_getStartDate_FlowDate()
		if (nSDate == 25) then
			nCols = nSDates - nSDate + 1
		else
			nCols = 12
		end
		return nCols
	end
	def cp_fp_getMaxCol_FlowDate()
		nCols = 12
		return nCols
	end
	def cp_fp_getByDateValue_FlowDate(nIndex)
		nSDate = cp_fp_getStartDate_FlowDate()
		return nIndex + nSDate
	end
	def cp_fp_getByDateValue_FlowHour(nIndex)
		return Pm.TestNo24((nIndex * 2) - 1)
	end


	def cp_fp_getBySkyIndex(nIndex,byWhatIdx=nil)
		return cp_fp_getInfo(nIndex,Eightword::BySky_4,"cp_fp_BySkyIndex_4(nIndex)",byWhatIdx)
# 		h = cp_fp_getInfo(nIndex,Eightword::BySky_4,"cp_fp_BySkyIndex_4(nIndex)",byWhatIdx)
# 	if (nIndex == 4) then
# 	Pm.saveTestDb("cp_fp_getBySkyIndex","#{h}")
# end
# 		return h
	end
	def cp_fp_setBySkyIndex(nIndex,byValue,byWhatIdx=nil)
		cp_fp_setInfo(nIndex,Eightword::BySky_4,byWhatIdx,byValue)
	end
	def cp_fp_BySkyIndex_4(nIndex)
		h8Words = cp_fp_get8Words(nIndex)
		hSkyIndex = cp_BySkyIndex_4(h8Words)
		cp_fp_setBySkyIndex(nIndex,hSkyIndex)
	end

	def cp_fp_getByEarthIndex(nIndex,byWhatIdx=nil)
		return cp_fp_getInfo(nIndex,Eightword::ByEarth_4,"cp_fp_ByEarthIndex_4(#{nIndex})",byWhatIdx)
	end
	def cp_fp_setByEarthIndex(nIndex,byValue,byWhatIdx=nil)
		cp_fp_setInfo(nIndex,Eightword::ByEarth_4,byWhatIdx,byValue)
	end
	def cp_fp_ByEarthIndex_4(nIndex)
		h8Words = cp_fp_get8Words(nIndex)
		hEarthIndex = cp_ByEarthIndex_4(h8Words)
		cp_fp_setByEarthIndex(nIndex,hEarthIndex)
	end

	# 變異天干
	def cp_fp_getByChungSky(nIndex,byWhatIdx=nil,arrayIndex=nil)
		return cp_fp_getInfo(nIndex,Eightword::ByChungSky_4_3,"cp_fp_ByChungSky_4_3(#{nIndex})",byWhatIdx,arrayIndex)
	end
	def cp_fp_setByChungSky(nIndex,byValue,byWhatIdx=nil,arrayIndex=nil)
		cp_fp_setInfo(nIndex,Eightword::ByChungSky_4_3,byWhatIdx,byValue,arrayIndex)
	end
	def cp_fp_ByChungSky_4_3(nIndex)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		hChungSky = cp_ByChungSky_4_3(hEarthIndex)
		cp_fp_setByChungSky(nIndex,hChungSky)
	end

	# 天干之五行
	def cp_fp_getBySkyFive(nIndex,byWhatIdx=nil)
		return cp_fp_getInfo(nIndex,Eightword::BySkyFive_4,"cp_fp_BySkyFive_4(#{nIndex})",byWhatIdx)
	end
	def cp_fp_setBySkyFive(nIndex,byValue,byWhatIdx=nil)
		cp_fp_setInfo(nIndex,Eightword::BySkyFive_4,byWhatIdx,byValue)
	end
	def cp_fp_BySkyFive_4(nIndex)
		hSkyIndex = cp_fp_getBySkyIndex(nIndex)
		aSkyFive = cp_BySkyFive_4(hSkyIndex)
		cp_fp_setBySkyFive(nIndex,aSkyFive)
	end

	# 地支五行
	def cp_fp_getByEarthFive(nIndex,byWhatIdx=nil)
		return cp_fp_getInfo(nIndex,Eightword::ByEarthFive_4,"cp_fp_ByEarthFive_4(#{nIndex})",byWhatIdx)
	end
	def cp_fp_setByEarthFive(nIndex,byValue,byWhatIdx=nil)
		cp_fp_setInfo(nIndex,Eightword::ByEarthFive_4,byWhatIdx,byValue)
	end
	def cp_fp_ByEarthFive_4(nIndex)
	    hEarthIndex = cp_fp_getByEarthIndex(nIndex)
	    aEarthFive = cp_ByEarthFive_4(hEarthIndex)
	    cp_fp_setByEarthFive(nIndex,aEarthFive)
	end

	# 變異五行
	def cp_fp_getByChungFive(nIndex,byWhatIdx=nil,arrayIndex=nil)
		return cp_fp_getInfo(nIndex,Eightword::ByChungFive_4_3,"cp_fp_ByChungFive_4_3(#{nIndex})",byWhatIdx,arrayIndex)
	end
	def cp_fp_setByChungFive(nIndex,byValue,byWhatIdx=nil,arrayIndex=nil)
		cp_fp_setInfo(nIndex,Eightword::ByChungFive_4_3,byWhatIdx,byValue,arrayIndex)
	end
	def cp_fp_ByChungFive_4_3(nIndex)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		aChungFive = cp_ByChungFive_4_3(hEarthIndex)
		cp_fp_setByChungFive(nIndex,aChungFive)
	end

	# 四柱的主星
	def cp_fp_getByMainStar(nIndex,byWhatIdx=nil)
		return cp_fp_getInfo(nIndex,Eightword::ByMainStar_4,"cp_fp_ByMainStar_4(#{nIndex})",byWhatIdx)
	end
	def cp_fp_setByMainStar(nIndex,byValue,byWhatIdx=nil)
		cp_fp_setInfo(nIndex,Eightword::ByMainStar_4,byWhatIdx,byValue)
	end
	# 所有的天干與日天干比較。甲為陽，乙為陰，以日天干為基準，
	# 我同者，陰陽相同者為比肩，不同者為劫財；
	# 生我者，陰陽相同者為偏印；不同者為正印
	# 剋我者，陰陽相同者為七殺；不同者為正官；
	# 我剋者，陰陽相同者為偏財；不同者為正財；
	# 我生者，陰陽相同者為食神；不同者為傷官；
	def cp_fp_ByMainStar_4(nIndex)
		# nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		# 拿本命的日天干來比較
		nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
		hSkyIndex = cp_fp_getBySkyIndex(nIndex)
		hMainStars = cp_ByMainStar_4(nDaySkyIndex,hSkyIndex,true)
		cp_fp_setByMainStar(nIndex,hMainStars)
	end

	# 四柱的副星
	def cp_fp_getBySubStar(nIndex,byWhatIdx=nil,arrayIndex=nil)
		return cp_fp_getInfo(nIndex,Eightword::BySubStar_4_3,"cp_fp_BySubStar_4_3(#{nIndex})",byWhatIdx,arrayIndex)
	end
	def cp_fp_setBySubStar(nIndex,byValue,byWhatIdx=nil,arrayIndex=nil)
		cp_fp_setInfo(nIndex,Eightword::BySubStar_4_3,byWhatIdx,byValue,arrayIndex)
	end
	def cp_fp_BySubStar_4_3(nIndex)
		nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		aSubStar = cp_BySubStar_4_3(nDaySkyIndex,hEarthIndex)
		cp_fp_setBySubStar(nIndex,aSubStar)
	end


	# 四柱的十二運
	def cp_fp_getBy12Win(nIndex,byWhatIdx=nil)
		return cp_fp_getInfo(nIndex,Eightword::By12Win_4,"cp_fp_By12Win_4(#{nIndex})",byWhatIdx)
	end
	def cp_fp_setBy12Win(nIndex,byValue,byWhatIdx=nil)
		cp_fp_setInfo(nIndex,Eightword::By12Win_4,byWhatIdx,byValue)
	end
	def cp_fp_By12Win_4(nIndex)
		nDaySkyIndex = cp_mp_getBySkyIndex(Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		a12Win = cp_By12Win_4(nDaySkyIndex,hEarthIndex)
		cp_fp_setBy12Win(nIndex,a12Win)
	end

	def cp_fp_getDwStarType(nIndex,byWhatIdx=nil)
		return cp_fp_getInfo(nIndex,Eightword::DwStarType_4,"cp_fp_DwStarType_4(#{nIndex})",byWhatIdx)
	end
	def cp_fp_setDwStarType(nIndex,byWhatIdx,byValue)
		cp_fp_setInfo(nIndex,Eightword::DwStarType_4,byWhatIdx,byValue)
	end
	def cp_fp_DwStarType_4(nIndex)
		dwStarType = cp_init8words_array(0,nil)
		(1..22).each do |nStar|
			sFunc = "cp_fp_DwStarType_God_#{nStar}(nIndex,dwStarType)"
			dwStarType = eval(sFunc)
		end
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
			cp_fp_setDwStarType(nIndex,byWhatIdx,dwStarType[byWhatIdx].uniq)
		end
	end
	# 三奇貴人
	def cp_fp_DwStarType_God_1(nIndex,dwStarType)
		nYearSkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByYearIdx)
		nMonthSkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByMonthIdx)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		return cp_DwStarType_God_1(dwStarType,nYearSkyIndex,nMonthSkyIndex,nDaySkyIndex)
	end

	# 魁罡貴人
	def cp_fp_DwStarType_God_2(nIndex,dwStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		return cp_DwStarType_God_2(dwStarType,nDaySkyIndex,nDayEarthIndex)
	end

	# 金神
	def cp_fp_DwStarType_God_3(nIndex,dwStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		nHourSkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByHourIdx)
		nHourEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByHourIdx)
		return cp_DwStarType_God_3(dwStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex,nHourSkyIndex,nHourEarthIndex)
	end

	# 福星
	def cp_fp_DwStarType_God_4(nIndex,dwStarType)
		nMonthSkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByMonthIdx)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		return cp_DwStarType_God_4(dwStarType,nMonthSkyIndex,nMonthEarthIndex)
	end
	# 天乙貴人
	def cp_fp_DwStarType_God_5(nIndex,dwStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwStarType_God_5(dwStarType,nDaySkyIndex,hEarthIndex)
	end

	# 文昌
	def cp_fp_DwStarType_God_6(nIndex,dwStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwStarType_God_6(dwStarType,nDaySkyIndex,hEarthIndex)
	end

	# 天德
	def cp_fp_DwStarType_God_7(nIndex,dwStarType)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		hSkyIndex = cp_fp_getBySkyIndex(nIndex)
		return cp_DwStarType_God_7(dwStarType,nMonthEarthIndex,hSkyIndex)
	end

	# 月德
	def cp_fp_DwStarType_God_8(nIndex,dwStarType)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		hSkyIndex = cp_fp_getBySkyIndex(nIndex)
		return cp_DwStarType_God_8(dwStarType,nMonthEarthIndex,hSkyIndex)
	end

	# 月將
	def cp_fp_DwStarType_God_9(nIndex,dwStarType)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		nCurSegment = cp_fp_get8Words(nIndex,Eightword::Segment)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwStarType_God_9(dwStarType,nMonthEarthIndex,nCurSegment,hEarthIndex)
	end

	# 將星 日柱地支 年柱地支
	def cp_fp_DwStarType_God_10(nIndex,dwStarType)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		nYearEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByYearIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwStarType_God_10(dwStarType,nDayEarthIndex,nYearEarthIndex,hEarthIndex)
	end

	# 華蓋 日柱地支 年柱地支
	def cp_fp_DwStarType_God_11(nIndex,dwStarType)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		nYearEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByYearIdx)
		return cp_DwStarType_God_11(dwStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
	end

	# 學堂
	def cp_fp_DwStarType_God_12(nIndex,dwStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		hSkyIndex = cp_fp_getBySkyIndex(nIndex)
		return cp_DwStarType_God_12(dwStarType,nDaySkyIndex,hSkyIndex)
	end

	# 詞館
	def cp_fp_DwStarType_God_13(nIndex,dwStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwStarType_God_13(dwStarType,nDaySkyIndex,hEarthIndex)
	end

	# 驛馬 日柱地支 年柱地支
	def cp_fp_DwStarType_God_14(nIndex,dwStarType)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		nYearEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByYearIdx)
		return cp_DwStarType_God_14(dwStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
	end

	# 紅鸞 年柱地支
	def cp_fp_DwStarType_God_15(nIndex,dwStarType)
		nYearEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByYearIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwStarType_God_15(dwStarType,nYearEarthIndex,hEarthIndex)
	end

	# 天喜 年柱地支
	def cp_fp_DwStarType_God_16(nIndex,dwStarType)
		nYearEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByYearIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwStarType_God_16(dwStarType,nYearEarthIndex,hEarthIndex)
	end

	# 六秀
	def cp_fp_DwStarType_God_17(nIndex,dwStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		return cp_DwStarType_God_17(dwStarType,nDaySkyIndex,nDayEarthIndex)
	end

	# 十靈
	def cp_fp_DwStarType_God_18(nIndex,dwStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		return cp_DwStarType_God_18(dwStarType,nDaySkyIndex,nDayEarthIndex)
	end

	# 天醫 月柱地支
	def cp_fp_DwStarType_God_19(nIndex,dwStarType)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwStarType_God_19(dwStarType,nMonthEarthIndex,hEarthIndex)
	end

	# 祿神
	def cp_fp_DwStarType_God_20(nIndex,dwStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwStarType_God_20(dwStarType,nDaySkyIndex,hEarthIndex)
	end

	# 天德合
	def cp_fp_DwStarType_God_21(nIndex,dwStarType)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		hSkyIndex = cp_fp_getBySkyIndex(nIndex)
		return cp_DwStarType_God_21(dwStarType,nMonthEarthIndex,hSkyIndex)
	end

	# 月德合 月柱地支
	def cp_fp_DwStarType_God_22(nIndex,dwStarType)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		hSkyIndex = cp_fp_getBySkyIndex(nIndex)
		return cp_DwStarType_God_22(dwStarType,nMonthEarthIndex,hSkyIndex)
	end

	# 四柱中神煞之煞
	def cp_fp_getDwBStarType(nIndex,byWhatIdx=nil)
		return cp_fp_getInfo(nIndex,Eightword::DwBStarType_4,"cp_fp_DwBStarType_4(#{nIndex})",byWhatIdx)
	end
	def cp_fp_setDwBStarType(nIndex,byWhatIdx,byValue)
		cp_fp_setInfo(nIndex,Eightword::DwBStarType_4,byWhatIdx,byValue)
	end
	def cp_fp_DwBStarType_4(nIndex)
		dwBStarType = cp_init8words_array(0,nil)
		(1..18).each do |nStar|
			sFunc = "cp_fp_DwBStarType_Kill_#{nStar}(nIndex,dwBStarType)"
			dwBStarType = eval(sFunc)
		end
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
			cp_fp_setDwBStarType(nIndex,byWhatIdx,dwBStarType[byWhatIdx].uniq)
		end
	end

	# 羊刃
	def cp_fp_DwBStarType_Kill_1(nIndex,dwBStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwBStarType_Kill_1(dwBStarType,nDaySkyIndex,hEarthIndex)
	end

	# 飛刃
	def cp_fp_DwBStarType_Kill_2(nIndex,dwBStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwBStarType_Kill_2(dwBStarType,nDaySkyIndex,hEarthIndex)
	end

	# 劫煞 日柱地支 年柱地支
	def cp_fp_DwBStarType_Kill_3(nIndex,dwBStarType)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		nYearEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByYearIdx)
		return cp_DwBStarType_Kill_3(dwBStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
	end

	# 亡神 日柱地支 年柱地支
	def cp_fp_DwBStarType_Kill_4(nIndex,dwBStarType)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		nYearEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByYearIdx)
		return cp_DwBStarType_Kill_4(dwBStarType,nDayEarthIndex,hEarthIndex,nYearEarthIndex)
	end

	# 咸池 年柱地支 月柱地支
	def cp_fp_DwBStarType_Kill_5(nIndex,dwBStarType)
		nYearEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByYearIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		return cp_DwBStarType_Kill_5(dwBStarType,nYearEarthIndex,hEarthIndex,nMonthEarthIndex)
	end

	# 紅艷
	def cp_fp_DwBStarType_Kill_6(nIndex,dwBStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwBStarType_Kill_6(dwBStarType,nDaySkyIndex,hEarthIndex)
	end

	# 外桃 日柱地支
	def cp_fp_DwBStarType_Kill_7(nIndex,dwBStarType)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		nHourEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByHourIdx)
		return cp_DwBStarType_Kill_7(dwBStarType,nDayEarthIndex,nHourEarthIndex)
	end

	# 六甲空亡 日柱天干
	def cp_fp_DwBStarType_Kill_8(nIndex,dwBStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwBStarType_Kill_8(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
	end

	# 孤虛 日柱天干
	def cp_fp_DwBStarType_Kill_9(nIndex,dwBStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwBStarType_Kill_9(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
	end

	# 四大空亡 日柱天干
	def cp_fp_DwBStarType_Kill_10(nIndex,dwBStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwBStarType_Kill_10(dwBStarType,nDaySkyIndex,nDayEarthIndex,hEarthIndex)
	end

	# 孤辰 年柱地支
	def cp_fp_DwBStarType_Kill_11(nIndex,dwBStarType)
		nYearEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByYearIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwBStarType_Kill_11(dwBStarType,nYearEarthIndex,hEarthIndex)
	end

	# 寡宿 年柱地支
	def cp_fp_DwBStarType_Kill_12(nIndex,dwBStarType)
		nYearEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByYearIdx)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwBStarType_Kill_12(dwBStarType,nYearEarthIndex,hEarthIndex)
	end

	# 天轉
	def cp_fp_DwBStarType_Kill_13(nIndex,dwBStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		return cp_DwBStarType_Kill_13(dwBStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex)
	end

	# 地轉 月柱地支
	def cp_fp_DwBStarType_Kill_14(nIndex,dwBStarType)
		nDaySkyIndex = cp_fp_getBySkyIndex(nIndex,Eightword::ByDayIdx)
		nDayEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByDayIdx)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		return cp_DwBStarType_Kill_14(dwBStarType,nDaySkyIndex,nDayEarthIndex,nMonthEarthIndex)
	end

	# 天羅
	def cp_fp_DwBStarType_Kill_15(nIndex,dwBStarType)
		hNaIn = cp_fp_getNaIn(nIndex)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwBStarType_Kill_15(dwBStarType,hNaIn,hEarthIndex)
	end

	# 地網
	def cp_fp_DwBStarType_Kill_16(nIndex,dwBStarType)
		hNaIn = cp_fp_getNaIn(nIndex)
		hEarthIndex = cp_fp_getByEarthIndex(nIndex)
		return cp_DwBStarType_Kill_16(dwBStarType,hNaIn,hEarthIndex)
	end

	# 十惡大敗
	def cp_fp_DwBStarType_Kill_17(nIndex,dwBStarType)
		h8Words = cp_fp_get8Words(nIndex)
		return cp_DwBStarType_Kill_17(dwBStarType,h8Words)
	end

	# 四廢
	def cp_fp_DwBStarType_Kill_18(nIndex,dwBStarType)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		h8Words = cp_fp_get8Words(nIndex)
		return cp_DwBStarType_Kill_18(dwBStarType,nMonthEarthIndex,h8Words)
	end

	def cp_fp_getNaIn(nIndex,byWhatIdx=nil)
		return cp_fp_getInfo(nIndex,Eightword::ByNaIn_4,"cp_fp_ByNaIn_4(#{nIndex})",byWhatIdx)
	end
	def cp_fp_setNaIn(nIndex,byValue,byWhatIdx=nil)
		cp_fp_setInfo(nIndex,Eightword::ByNaIn_4,byWhatIdx,byValue)
	end
	def cp_fp_ByNaIn_4(nIndex)
		h8Words = cp_fp_get8Words(nIndex)
		hNaIn = cp_ByNaIn_4(h8Words)
		cp_fp_setNaIn(nIndex,hNaIn)
	end

	# 旺相休囚死
	def cp_fp_getByFiveLevel(nIndex,arrayIndex=nil)
		return cp_fp_getInfo(nIndex,Eightword::ByFiveLevel_5,"cp_fp_ByFiveLevel_5(#{nIndex})",nil,arrayIndex)
	end
	def cp_fp_setByFiveLevel(nIndex,byValue,arrayIndex=nil)
		cp_fp_setInfo(nIndex,Eightword::ByFiveLevel_5,nil,byValue,arrayIndex)
	end
	def cp_fp_ByFiveLevel_5(nIndex)
		nMonthEarthIndex = cp_fp_getByEarthIndex(nIndex,Eightword::ByMonthIdx)
		byValue = cp_ByFiveLevel_5(nMonthEarthIndex)
		cp_fp_setByFiveLevel(nIndex,byValue)
	end

	# 流盤排盤資訊
	def cp_fp_4c_getEDates(nIndex,nPanType)
		if (nPanType <= Cfate::PAN_FLOWYEAR) then
			nEYear = cp_fp_getByDateValue(nIndex,nPanType)
			return nEYear,-1,-1,-1,false
		elsif (nPanType == Cfate::PAN_FLOWMONTH) then
			nSYear = fdg_SY()
			nSMonth = nIndex + 1
			nSDate = 1
			nHour = -1
		elsif (nPanType == Cfate::PAN_FLOWDATE) then
			nSDate = cp_fp_getByDateValue_FlowDate(nIndex)
			nSYear = fdg_SY()
			nSMonth = fdg_SM()
			nHour = -1
		else
			nSYear = fdg_SY()
			nSMonth = fdg_SM()
			nSDate = fdg_SD()
			nHour = cp_fp_getByDateValue(nIndex,nPanType)
		end
		nWYear,nWMonth,nWDate,nWHour,nWMinute,nSegmentIndex = Xdate.SegmentDate2Wdate(nSYear,nSMonth,nSDate,@par_FirstSegment)
		nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
		return nEYear,nEMonth,nEDate,nHour,bLeapMonth
	end

	def cp_fp_4c_getByDateValue(nIndex,nPanType)
		if (nPanType == Cfate::PAN_FLOWMONTH) then
			return cp_fp_4c_getByDateValue_FlowMonth(nIndex)
		elsif (nPanType == Cfate::PAN_FLOWDATE) then
			return cp_fp_4c_getByDateValue_FlowDate(nIndex)
		else
			nDate = cp_fp_getByDateValue(nIndex,nPanType)
			if (nPanType <= Cfate::PAN_FLOWYEAR) then
				nDate = Xdate.GetYearOld(uig_EY(),nDate)
				if (nDate == 0) then
					nDate = ""
				end
			end
			return nDate
		end
	end
	def cp_fp_4c_getByDateValue_FlowMonth(nIndex)
		if (nIndex == 0) then
			str = Xdate.GetSegMonthStr(nIndex + 1)
			return str[0]
		else
			return nIndex + 1
		end
	end
	def cp_fp_4c_getByDateValue_FlowDate(nIndex)
		nSDate = cp_fp_getByDateValue_FlowDate(nIndex)
		nSYear = fdg_SY()
		nSMonth = fdg_SM()

		nWYear,nWMonth,nWDate,nWHour,nWMinute,nSegmentIndex = Xdate.SegmentDate2Wdate(nSYear,nSMonth,nSDate,@par_FirstSegment)
		return Xdate.GetWMonthDateStr(nWMonth,nWDate)
	end

	def cp_fp_4c_getByDateValue2(nIndex,nPanType)
		if (nPanType == Cfate::PAN_FLOWMONTH) then
			cp_fp_4c_getByDateValue2_FlowMonth(nIndex)
		elsif (nPanType == Cfate::PAN_FLOWDATE) then
			return cp_fp_4c_getByDateValue2_FlowDate(nIndex)
		else
			nDate = cp_fp_getByDateValue(nIndex,nPanType)
			if (nPanType == Cfate::PAN_TENYEAR) then
				nDate = Xdate.GetYearOld(uig_EY(),nDate) + 5
			elsif (nPanType == Cfate::PAN_NORMAL) then
				nDate = yearDisplayOnlyNumber(nDate)
			elsif (nPanType == Cfate::PAN_FLOWYEAR) then
				nDate = yearDisplayOnlyNumber(nDate)
			elsif (nPanType == Cfate::PAN_FLOWTIME) then
				nDate = Pm.TestNo24(nDate + 1)
			end
			return nDate
		end
	end
	def cp_fp_4c_getByDateValue2_FlowMonth(nIndex)
		nSYear = fdg_SY()
		nWYear,nWMonth,nWDate,nWHour,nWMinute,nSegmentIndex = Xdate.SegmentDate2Wdate(nSYear,nIndex + 1,1,@par_FirstSegment)
		return Xdate.GetWMonthDateStr(nWMonth,nWDate)
	end
	def cp_fp_4c_getByDateValue2_FlowDate(nIndex)
		nSDate = cp_fp_getByDateValue_FlowDate(nIndex)
		nSYear = fdg_SY()
		nSMonth = fdg_SM()
	# Pm.saveTestDb("cp_fp_getByDateValue","#{nSYear},#{nSMonth},#{nSDate}")

		nWYear,nWMonth,nWDate,nWHour,nWMinute,nSegmentIndex = Xdate.SegmentDate2Wdate(nSYear,nSMonth,nSDate,@par_FirstSegment)
		nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
		return Xdate.GetMonthDateStr(nEMonth,nEDate,bLeapMonth)
	end
	def cp_fp_4c_getByMainStars_Name(nIndex)
		hMainStars = Hash.new
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
			# byWhat = byWhatIdx
			hMainStars[byWhatIdx] = cp_fp_4c_getByMainStar_Name(nIndex,byWhatIdx)
		end
		return hMainStars
	end
	def cp_fp_4c_getByMainStar_Name(nIndex,nPanType)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		# if (byWhatIdx == Eightword::ByDayIdx) then
		# 	sOut = Pm.GetStr("IDS_E_MS_SELF")
		# else
			nMainStar = cp_fp_getByMainStar(nIndex,byWhatIdx)
			sOut = cp_fp_4c_getMainStarName(nMainStar)
		# end
		return sOut
	end
	def cp_fp_4c_getDateByWhatIdx(nPanType)
		if (nPanType <= Cfate::PAN_FLOWYEAR) then
			return Eightword::ByYearIdx
		elsif (nPanType == Cfate::PAN_FLOWMONTH) then
			return Eightword::ByMonthIdx
		elsif (nPanType == Cfate::PAN_FLOWDATE) then
			return Eightword::ByDayIdx
		else
			return Eightword::ByHourIdx
		end
	end
	def cp_fp_4c_getMainStarName(nMainStar)
		sOut = pan_par_getMainStarStr(nMainStar)
		return sOut
	end

	# 主星中的旺相休囚死
	def cp_fp_4c_get4CFiveLevels_Name(nIndex)
		hOut = Hash.new
		(Eightword::ByYearIdx..Eightword::ByHourIdx).each do |byWhatIdx|
			# byWhat = byWhatIdx
			hOut[byWhatIdx] = g_fp_get4CFiveLevel_Name(nIndex,byWhatIdx)
		end
		return hOut
	end
	def cp_fp_4c_get4CFiveLevel_Name(nIndex,byWhatIdx)
		sOut = ""
		nSkyFive = cp_fp_getBySkyFive(nIndex,byWhatIdx)
		(0..4).each do |five|
			n4ColumnFive = cp_fp_getByFiveLevel(nIndex,five)
			if (nSkyFive == n4ColumnFive) then
				sOut = Pm.GetStr("IDS_E_FIVE_LEVEL_#{five}")
			end
		end
		return sOut
	end

	# 天干
	def cp_fp_4c_getSky_Name(nIndex,nPanType)
		nSkyIndex = cp_fp_4c_getSkyIndex(nIndex,nPanType)
		return Sky.GetName(Sky.SkyIndex2Sky(nSkyIndex))
	end
	def cp_fp_4c_getSkyIndex(nIndex,nPanType)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		nSkyIndex = cp_fp_getBySkyIndex(nIndex,byWhatIdx)
		return nSkyIndex
	end
	def cp_fp_4c_getSkyFive_Name(nIndex,nPanType)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		nFive = cp_fp_getBySkyFive(nIndex,byWhatIdx)
		return Eightword.getFiveName(nFive)
	end
	# 地支
	def cp_fp_4c_getEarth_Name(nIndex,nPanType)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		nEarthIndex = cp_fp_getByEarthIndex(nIndex,byWhatIdx)
		return Earth.GetName(Earth.EarthIndex2Earth(nEarthIndex))
	end
	def cp_fp_4c_getEarthIndex(nIndex,nPanType)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		nEarthIndex = cp_fp_getByEarthIndex(nIndex,byWhatIdx)
		return nEarthIndex
	end
	def cp_fp_4c_getEarthFive_Name(nIndex,nPanType)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		nFive = cp_fp_getByEarthFive(nIndex,byWhatIdx)
		return Eightword.getFiveName(nFive)
	end

	# 藏干
	def cp_fp_4c_getChungSky_Name(nIndex,nPanType,arrayIndex)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		nSky = cp_fp_getByChungSky(nIndex,byWhatIdx,arrayIndex)
		if (nSky > 0) then
			return Sky.GetName(nSky)
		else
			return ""
		end
	end
	def cp_fp_4c_getChungSkyFive_Name(nIndex,nPanType,arrayIndex)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		nFive = cp_fp_getByChungFive(nIndex,byWhatIdx,arrayIndex)
		if (nFive == 0xFF) then
			return ""
		else
			return Eightword.getFiveName(nFive)
		end
	end

	# 副星
	def cp_fp_4c_getBySubStar_Name(nIndex,nPanType,arrayIndex)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		nStar = cp_fp_getBySubStar(nIndex,byWhatIdx,arrayIndex)
		if (nStar == 0xFF) then
			sOut = ""
		else
			sOut = pan_par_getSubStarStr(nStar)
		end
		return sOut
	end

	# 12運
	def cp_fp_4c_getBy12Win_Name(nIndex,nPanType)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		n12Win = cp_fp_getBy12Win(nIndex,byWhatIdx)
		return Pm.GetStr("IDS_S_GOD_#{n12Win + 1}")
	end

	# 神煞
	def cp_fp_4c_getGodKill_Name(nIndex,nPanType)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		aStartTypeName = Array.new
		sStarType = cp_fp_4c_getDwStarType_Name(nIndex,byWhatIdx)
		sBStarType = cp_fp_4c_getDwBStarType_Name(nIndex,byWhatIdx)
		if (!sStarType.efpty?) then
			aStartTypeName.push(sStarType)
		end
		if (!sBStarType.efpty?) then
			aStartTypeName.push(sBStarType)
		end
		return aStartTypeName.join(Pm.GetStr("IDS_E_CHI_DOT"))
	end
	def cp_fp_4c_getDwStarType_Name(nIndex,byWhatIdx)
		dwStarType = cp_fp_getDwStarType(nIndex,byWhatIdx)
		aStartTypeName = Array.new
		dwStarType.each do |nStarType|
			if (par_ShowGod?(nStarType)) then
				aStartTypeName.push(Eightword.getGodStr(nStarType))
			end
		end
		return aStartTypeName.join(Pm.GetStr("IDS_E_CHI_DOT"))
	end
	def cp_fp_4c_getDwBStarType_Name(nIndex,byWhatIdx)
		dwBStarType = cp_fp_getDwBStarType(nIndex,byWhatIdx)
		aStartTypeName = Array.new
		dwBStarType.each do |nStarType|
			if (par_ShowKill?(nStarType)) then
				aStartTypeName.push(Eightword.getKillStr(nStarType))
			end
		end
		return aStartTypeName.join(Pm.GetStr("IDS_E_CHI_DOT"))
	end

	# 納音
	def cp_fp_4c_getNaIn_Name(nIndex,nPanType)
		byWhatIdx = cp_fp_4c_getDateByWhatIdx(nPanType)
		nNaIn = cp_fp_getNaIn(nIndex,byWhatIdx)
		return Pm.GetStr("IDS_E_NAING_#{nNaIn + 1}")
	end

	# 大運資訊
	def cp_fp_ty_getSEWin(nPanType)
		h8Words = cp_fp_CurrentGanZhi8Words(nPanType)
		n8Words = h8Words[Eightword::ByYearIdx]
		nSkyIndex = cp_BySkyIndex(n8Words)
		nEarthIndex = cp_ByEarthIndex(n8Words)
		return Sky.GetIndexName(nSkyIndex) + Earth.GetIndexName(nEarthIndex) + Pm.GetStr("IDS_E_FW_3")
	end
	def cp_fp_ty_getDate1(nPanType)
		nIndex = cp_fp_CurrentIndex(nPanType)
		nYearOld = cp_fp_4c_getByDateValue(nIndex,nPanType)
		return nYearOld
	end
	def cp_fp_ty_getByDateValue(nIndex,nPanType)
		nDate = cp_fp_getByDateValue(nIndex,nPanType)
		return nDate
	end
	def cp_fp_ty_getDate1Year(nPanType)
		nIndex = cp_fp_CurrentIndex(nPanType)
		nYear = cp_fp_ty_getByDateValue(nIndex,nPanType)
		return nYear
	end
	def cp_fp_ty_getDate1Year_Str(nPanType)
		nYear = cp_fp_ty_getDate1Year(nPanType)
		return Xdate.GetYearStr_WC(nYear,@par_YearDisplay)
	end
	def cp_fp_ty_getDate2(nPanType)
		nYearOld = cp_fp_ty_getDate1(nPanType)
		# if (nYearOld == "") then
			# return ""
		# end
		nYearOld = nYearOld + 10 - 1
		return nYearOld
	end
	def cp_fp_ty_getDate2Year(nPanType)
		nYear = cp_fp_ty_getDate1Year(nPanType) + 10 - 1
		return nYear
	end
	def cp_fp_ty_getDate2Year_Str(nPanType)
		nYear = cp_fp_ty_getDate2Year(nPanType)
		return Xdate.GetYearStr_WC(nYear,@par_YearDisplay)
	end

	# 流年
	def cp_fp_fy_getSEWin(nPanType)
		# h8Words = cp_fp_CurrentGanZhi8Words(nPanType)
		# n8Words = h8Words[Eightword::ByYearIdx]
		# nSkyIndex = cp_BySkyIndex(n8Words)
		# nEarthIndex = cp_ByEarthIndex(n8Words)
		# return Sky.GetIndexName(nSkyIndex) + Earth.GetIndexName(nEarthIndex) + Pm.GetStr("IDS_X_YEAR")
		nGanZhi = Xdate.GetYearGanZhi(fdg_SY())
		return Xdate.GetGanZhiYear_Str(nGanZhi)
	end
	def cp_fp_fy_getDate1(nPanType)
		nIndex = cp_fp_CurrentIndex(nPanType)
		nYearOld = cp_fp_4c_getByDateValue(nIndex,nPanType)
		return nYearOld
	end
	def cp_fp_fy_getDate1Year(nPanType)
		nIndex = cp_fp_CurrentIndex(nPanType)
		nYear = cp_fp_getByDateValue(nIndex,nPanType)
		return Xdate.GetYearStr_WC(nYear,@par_YearDisplay)
	end

	# 流月
	def cp_fp_fm_getSEWinYear(nPanType)
		# h8Words = cp_fp_CurrentGanZhi8Words(nPanType)
		# n8Words = h8Words[Eightword::ByYearIdx]
		# nSkyIndex = cp_BySkyIndex(n8Words)
		# nEarthIndex = cp_ByEarthIndex(n8Words)
		# return Sky.GetIndexName(nSkyIndex) + Earth.GetIndexName(nEarthIndex) + Pm.GetStr("IDS_X_YEAR")
		nGanZhi = Xdate.GetYearGanZhi(fdg_SY())
		return Xdate.GetGanZhiYear_Str(nGanZhi)
	end
	def cp_fp_fm_getSEWinMonth(nPanType)
		# h8Words = cp_fp_CurrentGanZhi8Words(nPanType)
		# n8Words = h8Words[Eightword::ByMonthIdx]
		# nSkyIndex = cp_BySkyIndex(n8Words)
		# nEarthIndex = cp_ByEarthIndex(n8Words)
		# return Sky.GetIndexName(nSkyIndex) + Earth.GetIndexName(nEarthIndex) + Pm.GetStr("IDS_X_MONTH")

		nGanZhi = Xdate.GetMonthGanZhi(fdg_SY(), fdg_SM())
		return Xdate.GetGanZhiMonth_Str(nGanZhi)
	end
	def cp_fp_fm_getDate1(nPanType)
		# nYear = fdg_WY()
		# nIndex = cp_fp_CurrentIndex(nPanType)
		# nMonth = cp_fp_4c_getByDateValue(nIndex,nPanType)
		# if (nMonth == 12) then
		# 	nMonth = 1
		# 	nYear += 1
		# else
		# 	nMonth += 1
		# end
		nSYear = fdg_SY()
		return Xdate.GetYearStr_WC(nSYear,@par_YearDisplay)
	end
	def cp_fp_fm_getDate2(nPanType)
		# nIndex = cp_fp_CurrentIndex(nPanType)
		# nMonth = cp_fp_4c_getByDateValue(nIndex,nPanType)
		# nMonth = Pm.TestNo12(nMonth)
		# nMonth += 1
		# return Xdate.GetMonthStr(nMonth,false)
		nSYear = fdg_SY()
		nSMonth = fdg_SM()
		nWYear, nWMonth, nWDate,nWHour,nWMinute = Xdate.GetSegWDateBySegYear(nSYear,nSMonth,@par_FirstSegment)
		return Xdate.GetMonthStr(nWMonth,false)
	end

	# 流日
	def cp_fp_fd_getSEWinYear(nPanType)
		h8Words = cp_fp_CurrentGanZhi8Words(nPanType)
		n8Words = h8Words[Eightword::ByYearIdx]
		nSkyIndex = cp_BySkyIndex(n8Words)
		nEarthIndex = cp_ByEarthIndex(n8Words)
		return Sky.GetIndexName(nSkyIndex) + Earth.GetIndexName(nEarthIndex) + Pm.GetStr("IDS_X_YEAR")
	end
	def cp_fp_fd_getSEWinMonth(nPanType)
		h8Words = cp_fp_CurrentGanZhi8Words(nPanType)
		n8Words = h8Words[Eightword::ByMonthIdx]
		nSkyIndex = cp_BySkyIndex(n8Words)
		nEarthIndex = cp_ByEarthIndex(n8Words)
		return Sky.GetIndexName(nSkyIndex) + Earth.GetIndexName(nEarthIndex) + Pm.GetStr("IDS_X_MONTH")
	end
	def cp_fp_fd_getSEWinDate(nPanType)
		h8Words = cp_fp_CurrentGanZhi8Words(nPanType)
		n8Words = h8Words[Eightword::ByDayIdx]
		nSkyIndex = cp_BySkyIndex(n8Words)
		nEarthIndex = cp_ByEarthIndex(n8Words)
		return Sky.GetIndexName(nSkyIndex) + Earth.GetIndexName(nEarthIndex) + Pm.GetStr("IDS_X_DAY")
	end
	def cp_fp_fd_getDate1(nPanType)
		nYear = fdg_WY()
		return Xdate.GetYearStr_WC(nYear,@par_YearDisplay)
	end
	def cp_fp_fd_getDate2(nPanType)
		nMonth = fdg_WM()
		return Xdate.GetMonthStr(nMonth,false)
	end
	def cp_fp_fd_getDate3(nPanType)
		nDate = fdg_WD()
		return Xdate.GetDateStr(nDate)
	end

	# 流時
	def cp_fp_ft_getSEWinYear(nPanType)
		h8Words = cp_fp_CurrentGanZhi8Words(nPanType)
		n8Words = h8Words[Eightword::ByYearIdx]
		nSkyIndex = cp_BySkyIndex(n8Words)
		nEarthIndex = cp_ByEarthIndex(n8Words)
		return Sky.GetIndexName(nSkyIndex) + Earth.GetIndexName(nEarthIndex) + Pm.GetStr("IDS_X_YEAR")
	end
	def cp_fp_ft_getSEWinMonth(nPanType)
		h8Words = cp_fp_CurrentGanZhi8Words(nPanType)
		n8Words = h8Words[Eightword::ByMonthIdx]
		nSkyIndex = cp_BySkyIndex(n8Words)
		nEarthIndex = cp_ByEarthIndex(n8Words)
		return Sky.GetIndexName(nSkyIndex) + Earth.GetIndexName(nEarthIndex) + Pm.GetStr("IDS_X_MONTH")
	end
	def cp_fp_ft_getSEWinDate(nPanType)
		h8Words = cp_fp_CurrentGanZhi8Words(nPanType)
		n8Words = h8Words[Eightword::ByDayIdx]
		nSkyIndex = cp_BySkyIndex(n8Words)
		nEarthIndex = cp_ByEarthIndex(n8Words)
		return Sky.GetIndexName(nSkyIndex) + Earth.GetIndexName(nEarthIndex) + Pm.GetStr("IDS_X_DAY")
	end
	def cp_fp_ft_getDate1(nPanType)
		nYear = fdg_WY()
		return Xdate.GetYearStr_WC(nYear,@par_YearDisplay)
	end
	def cp_fp_ft_getDate2(nPanType)
		nMonth = fdg_WM()
		return Xdate.GetMonthStr(nMonth,false)
	end
	def cp_fp_ft_getDate3(nPanType)
		nDate = fdg_WD()
		return Xdate.GetDateStr(nDate)
	end

	def cp_fp_isHighlight(nPanType,nIndex,sSkyEarth)
		if (ddg_SkyEarth() == nil) then
			return false
		end

		if (ddg_SkyEarth() != sSkyEarth) then
			return false
		end

		return (cp_fp_CurrentIndex(nPanType) == nIndex)
	end

	def cp_fp_isSkyMouseOverEvent()
		return @func_FwSmm_Event && (par_isAncillaryTrue?(Eightword::ADP_SKYHER))
	end
	def cp_fp_event_SkyHerHua(nSkyIndex)
		Eightword.SkyHerHua(nSkyIndex,cp_fp_isSkyMouseOverEvent())
	end

	def cp_fp_EarthMouseOverEvent()
		return cp_mp_EarthMouseOverEvent_Display(@func_FwEmm_Event)
	end

end
