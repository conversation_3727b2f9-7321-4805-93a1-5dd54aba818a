class PanGiSuong
  def wa_Result(nPanType,hUserInfo,hUserDefDate,hUserType)
  end
  # 爆發性explode
  def wa_explode(nPanType,hUserInfo,hUserDefData,hUserType)
    @m_bShowGiSuong = true
    @UserInfo = hUserInfo.clone
    @UserDefData = hUserDefData.clone
    @UserType = Hash.new
    # 小限起命宮
    # @UserType[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN
    @UserType[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN_FLOW_SKY
    # 四化依本命
    @UserType[Star::PAR_FLOWYEAR_HUA] = Star::PAN_YEAR_HUA_FLOW
    @m_nShowGiSuongHouseId = nGiSuongHouseId = hUserDefData[Star::HOUSE_NAME]

    wae_Star_Reset_Result()
    case (nPanType)
      when (Cfate::PAN_TENYEAR) then
        h,a,flow_title = wae_Star_GetGiSuong_TenYear(nPanType,hUserInfo,hUserDefData,@UserType,nGiSuongHouseId)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        h,a,flow_title = wae_Star_GetGiSuong_FlowYear(nPanType,hUserInfo,hUserDefData,@UserType,nGiSuongHouseId)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        h,a,flow_title = wae_Star_GetGiSuong_FlowMonth(nPanType,hUserInfo,hUserDefData,@UserType,nGiSuongHouseId)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        h,a,flow_title = wae_Star_GetGiSuong_FlowDate(nPanType,hUserInfo,hUserDefData,@UserType,nGiSuongHouseId)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        h,a,flow_title = wae_Star_GetGiSuong_FlowTime(nPanType,hUserInfo,hUserDefData,@UserType,nGiSuongHouseId)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        return Hash.new    # PAN_FLOWMIN
    end
    h2 = Hash.new
    gi = Hash.new
    xiong = Hash.new
    h["rule1"].keys.each do |key|
      gi[key] = h["rule1"][key] + h["rule2"]["gi"][key]
      xiong[key] = h["rule3"][key] + h["rule2"]["xiong"][key]
    end
    h2["score_title"] = h["rule1"].keys
    h2["gixiong_scores"] = { "gi" => gi, "xiong" => xiong}
    h2["rule_scores"] = h
    h2["guoli"] = a
    return h2,flow_title
  end
  def zwld_guoli(nPanType)
  end
  # 四化個別吉凶
  def zwld_sihua_explode(nPanType,hUserInfo,hUserDefData,hUserType)
    @m_bShowGiSuong = true
    @UserInfo = hUserInfo.clone
    @UserDefData = hUserDefData.clone
    @UserType = Hash.new
    # 小限起命宮
    # @UserType[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN
    @UserType[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN_FLOW_SKY
    # 四化依本命
    @UserType[Star::PAR_FLOWYEAR_HUA] = Star::PAN_YEAR_HUA_FLOW
    @m_nShowGiSuongHouseId = nGiSuongHouseId = hUserDefData[Star::HOUSE_NAME] == 99 ? 1 : hUserDefData[Star::HOUSE_NAME]

    wae_Star_Reset_Result()
    case (nPanType)
      when (Cfate::PAN_NORMAL) then
        h,flow_title = Hash.new,""
      when (Cfate::PAN_TENYEAR) then
        h,flow_title = zwld_Star_GetGiSuong_TenYear(nPanType,hUserInfo,hUserDefData,@UserType,nGiSuongHouseId)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        h,flow_title = zwld_Star_GetGiSuong_FlowYear(nPanType,hUserInfo,hUserDefData,@UserType,nGiSuongHouseId)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        h,flow_title = zwld_Star_GetGiSuong_FlowMonth(nPanType,hUserInfo,hUserDefData,@UserType,nGiSuongHouseId)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        h,flow_title = zwld_Star_GetGiSuong_FlowDate(nPanType,hUserInfo,hUserDefData,@UserType,nGiSuongHouseId)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        h,flow_title = zwld_Star_GetGiSuong_FlowTime(nPanType,hUserInfo,hUserDefData,@UserType,nGiSuongHouseId)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        h,flow_title = Hash.new,""    # PAN_FLOWMIN
    end

    aOut = Array.new(4,nil)
    (0..3).each do |nFourHuaIndex|
      h2 = Hash.new
      gi = Hash.new
      xiong = Hash.new
      if (h["rule1"] != nil) then
        h["rule1"][nFourHuaIndex].keys.each do |key|
          gi[key] = h["rule1"][nFourHuaIndex][key] + h["rule2"][nFourHuaIndex]["gi"][key]
          xiong[key] = h["rule3"][nFourHuaIndex][key] + h["rule2"][nFourHuaIndex]["xiong"][key]
        end
      end

      aOut[nFourHuaIndex] = { "gi" => gi, "xiong" => xiong}
    end
    gong = Ziwei.gong_nick(@m_nShowGiSuongHouseId)

    return aOut,flow_title,gong
  end
end
