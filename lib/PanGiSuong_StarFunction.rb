require("Star.rb")
require("Pm.rb")

# For Star
class PanGiSuong

  TEN_YEAR_DIFF = "TEN_YEAR_DIFF"
  FLOW_YEAR = "FLOW_YEAR"
  FLOW_MONTH = "FLOW_MONTH"
  FLOW_BLEAP = "FLOW_BLEAP"
  FLOW_DATE = "FLOW_DATE"
  FLOW_LEAP = "FLOW_LEAP"

  def ap_Star_Init(oStar,nPanType,hUserInfo,hUserDefData,nGiSuongHouseId)
    @m_bShowGiSuong = true
    @UserInfo = hUserInfo.clone
    @UserDefData = hUserDefData.clone
    @UserType = Hash.new
    # 小限起命宮
    # @UserType[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN
    @UserType[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN_FLOW_SKY
    # 四化依本命
    @UserType[Star::PAR_FLOWYEAR_HUA] = Star::PAN_YEAR_HUA_FLOW
    @m_nShowGiSuongHouseId = nGiSuongHouseId
    ap_Star_Reset_Result()

    @Star = oStar
  end

  def ap_Star_Deinit()
    @UserDefData = nil
  end

  def ap_Star_Reset_Result()
    aRule1 = aRule2 = aRule3 = nil
    @FlowEYear = 0
    @FlowEMonth = 0
    @FlowLeap = false
    @FlowEDate = 0
  end

  def ap_Star_GetGiSuong_TenYear(nSky,nEarth,nAge,i,nStartNum)

    bNeedToFindGiSuong,nDiff = ap_Star_NeedToFindGiSuong_TenYear(@Star,i,nStartNum)

    if (bNeedToFindGiSuong) then
      nLargeSan = Earth.Modify(@Star.gHouse_getLargeSan() + nDiff)
#puts("nDiff:#{nDiff}")
#puts("nLargeSan:#{nLargeSan}")
      #nAge = oStar.gGiSuong_getLargeSanStartYearOld(nLargeSan)
      nStartEYear = @Star.gGiSuong_getLargeSanStartYear(nLargeSan)
#puts("nStartEYear:#{nStartEYear}")
      if (nStartEYear > Cfate::PAN_LAST_YEAR) then
        return "",""
      end

      oStar = Star.new
      hUserDefData = @UserDefData
      hUserDefData[Cfate::EYear] = nStartEYear
      hStarInfo = oStar.g_GetStarPanInfo(Cfate::PAN_TENYEAR,@UserInfo,hUserDefData,@UserType)


      @aRule1,@aRule2,@aRule3 = xGetGiSuong_Rule(oStar,Cfate::PAN_TENYEAR,oStar,Cfate::PAN_TENYEAR)
    end
    #return aRule1,aRule2,aRule3
    return ap_Star_GetGiSuongText_Rule(Cfate::PAN_TENYEAR,nSky,nEarth,nAge)
  end

  def ap_Star_NeedToFindGiSuong_TenYear(oStar,i,nStartNum)
    nDiff = 0
    bNeedToFindGiSuong = false

    if (i == 0) then
      nDiff = oStar.gGiSuong_CheckClockWise() ? -1 : 1
      bNeedToFindGiSuong = true
    elsif (i == nStartNum) then
      nDiff = 0
      bNeedToFindGiSuong = true
    elsif (i == nStartNum + 10) then
      nDiff = oStar.gGiSuong_CheckClockWise() ? 1 : -1
      bNeedToFindGiSuong = true
    elsif (i == nStartNum + 20) then
      nDiff = oStar.gGiSuong_CheckClockWise() ? 2 : -2
      bNeedToFindGiSuong = true
    end

    return bNeedToFindGiSuong,nDiff
  end


  def ap_Star_GetGiSuong_FlowYear(nSky,nEarth,nAge,nFlowEYear)
    if (@FlowEYear != nFlowEYear) then
      @FlowEYear = nFlowEYear
      oStar = Star.new
      hUserDefData = @UserDefData
      hUserDefData[Cfate::EYear] = nFlowEYear
      hUserDefData[Cfate::EMonth] = 6
      hUserDefData[Cfate::LeapMonth] = false
      hStarInfo = oStar.g_GetStarPanInfo(Cfate::PAN_FLOWMONTH,@UserInfo,hUserDefData,@UserType)

      @aRule1,@aRule2,@aRule3 = xGetGiSuong_Rule(oStar,Cfate::PAN_FLOWYEAR,oStar,Cfate::PAN_FLOWMONTH)
    end

    #return aRule1,aRule2,aRule3
    return ap_Star_GetGiSuongText_Rule(Cfate::PAN_FLOWYEAR,nSky,nEarth,nAge)
  end


  def ap_Star_GetGiSuong_FlowMonth(nFlowEYear,nFlowEMonth,bFlowLeap,nSky,nEarth,nAge)
    if ((@FlowEYear != nFlowEYear) || (@FlowEMonth != nFlowEMonth) || (@FlowLeap != bFlowLeap)) then
      @FlowEYear = nFlowEYear
      @FlowEMonth = nFlowEMonth
      @FlowLeap = bFlowLeap
      oStar = Star.new
      hUserDefData = @UserDefData
      hUserDefData[Cfate::EYear] = nFlowEYear
      hUserDefData[Cfate::EMonth] = nFlowEMonth
      hUserDefData[Cfate::LeapMonth] = bFlowLeap
      hStarInfo = oStar.g_GetStarPanInfo(Cfate::PAN_FLOWMONTH,@UserInfo,hUserDefData,@UserType)

      @aRule1,@aRule2,@aRule3 = xGetGiSuong_Rule(oStar,Cfate::PAN_FLOWMONTH,oStar,Cfate::PAN_FLOWMONTH)
    end

    #return aRule1,aRule2,aRule3
    return ap_Star_GetGiSuongText_Rule(Cfate::PAN_FLOWMONTH,nSky,nEarth,nAge)
  end

  def ap_Star_GetGiSuong_FlowDate(nFlowEYear,nFlowEMonth,bFlowLeap,nFlowEDate,nSky,nEarth,nAge)
    return ap_Star_GetGiSuong_FlowMonth(nFlowEYear, nFlowEMonth, bFlowLeap,nSky,nEarth,nAge)
  end

  def ap_Star_GetGiSuongText_Rule(nPanType,nSky,nEarth,nAge)
    szGi = szSuong = ""

    if (!@m_bShowGiSuong) then
      return szGi,szSuong
    end
    szGi = xGetGiSuongText_Rule1(nSky)

    nRule2Par = xGetGiSuong_Rule2_Par(nPanType,nSky,nEarth,nAge)
    szGi1,szSuong = xGetGiSuongText_Rule2(nRule2Par)
    if (szGi.length == 0) then szGi = szGi1 end

    szSuong1 = xGetGiSuongText_Rule3(nEarth)
    if (szSuong.length == 0) then szSuong = szSuong1 end

    return szGi,szSuong
  end

  protected
  def xGetGiSuongText_Rule1(nSky)
    szGi = ""
    if (@aRule1 == nil) then
      return szGi
    end

    @aRule1.each do |nRuleSky|
      if (nRuleSky == nSky) then
        szGi = Pm.GetStr("IDS_GISUONG_GI")
      end
    end
    return szGi
  end

  def xGetGiSuongText_Rule2(nAge)
    szGi = szSuong = ""
    if (@aRule2 == nil) then
      return szGi,szSuong
    end

    @aRule2[0].each do |nRuleAge|
      if (nRuleAge == nAge) then
        szGi = Pm.GetStr("IDS_GISUONG_GI")
      end
    end

    @aRule2[1].each do |nRuleAge|
      if (nRuleAge == nAge) then
        szSuong = Pm.GetStr("IDS_GISUONG_SUONG")
      end
    end

    return szGi,szSuong
  end

  def xGetGiSuongText_Rule3(nEarth)
    szSuong = ""
    if (@aRule3 == nil) then
      return szSuong
    end

    @aRule3.each do |nRuleEarth|
      if (nRuleEarth == nEarth) then
        szSuong = Pm.GetStr("IDS_GISUONG_SUONG")
      end
    end

    return szSuong
  end

  def xGetGiSuong_Rule2_Par(nPanType,nSky,nEarth,nAge)
    nRule2Par = nEarth
    if (nPanType == Cfate::PAN_TENYEAR) then
      nRule2Par = nAge
    end
    return nRule2Par
  end

  def xGetGiSuong_Rule(oStar1,nPanType1,oStar2,nPanType2)
    aRule1 = aRule2 = aRule3 = nil
    aRule1 = xGetGiSuong_Rule1(oStar1,nPanType1,oStar2,nPanType2)
    aRule2 = xGetGiSuong_Rule2(oStar1,nPanType1,oStar2,nPanType2)
    aRule3 = xGetGiSuong_Rule3(oStar1,nPanType1,oStar2,nPanType2)
    return aRule1,aRule2,aRule3
  end


  # 吉年找法
  def xGetGiSuong_Rule1(oStar1,nPanType1,oStar2,nPanType2)
    nHouseEarth = oStar1.gGiSuong_GetHouseEarth(nPanType1,@m_nShowGiSuongHouseId)
#puts("@m_nShowGiSuongHouseId:#{@m_nShowGiSuongHouseId}")
#puts("nHouseEarth:#{nHouseEarth}")
    # 尋找天干
    nSky = oStar2.gHouse_GetSky(nPanType2,nHouseEarth)
#puts("nSky:#{nSky}")
    # find flow sky
    nFlowSky = oStar1.gGiSuong_GetGiSuong_Rule1_OrgSky(nPanType1)
#puts("nFlowSky:#{nFlowSky}")

    # 尋找四化
    aFourHuaHouse = oStar1.gGiSuong_FindFourHuaHouse(nPanType1,nSky)  # 所選天干四化
    aFlowFourHuaHouse = oStar1.gGiSuong_FindFourHuaHouse(nPanType1,nFlowSky) # 先天四化
#puts("aFourHuaHouse:#{aFourHuaHouse}")
#puts("aFlowFourHuaHouse:#{aFlowFourHuaHouse}")

    # 吉凶宮位判別
    # 判斷四化飛出有沒有跟先天四化遭遇，若完全沒有遇到就結束，表示沒有變化
    aFocusHouse = xCheckGiSuongHouse_Rule1(aFourHuaHouse,aFlowFourHuaHouse)
    if (aFocusHouse.length == 0) then
      return nil
    end
#puts("aFocusHouse:#{aFocusHouse}")

    # 尋找刻應的天干
    aRule = xFindGiSuongSky_Rule1(oStar1,nPanType1,aFocusHouse)
#puts("aRule:#{aRule}")
    return aRule
  end

  # 吉凶宮位判別
  def xCheckGiSuongHouse_Rule1(aFourHuaHouse,aFlowFourHuaHouse)
    #aFocusHouse = aFourHuaHouse & aFlowFourHuaHouse
    aFocusHouse = Array.new(4,nil)

    (0..3).each do |nFourHuaIndex|
      if (aFlowFourHuaHouse.index(aFourHuaHouse[nFourHuaIndex]) != nil) then
        aFocusHouse[nFourHuaIndex] = aFourHuaHouse[nFourHuaIndex]
      end
    end
    return aFocusHouse
  end

  # 尋找刻應的天干
  def xFindGiSuongSky_Rule1(oStar1,nPanType1,aFocusHouse)
    aFourHuaToFind = [3,2,1,0] # 流年與十年同

    # if (nPanType1 >= Cfate::PAN_FLOWMONTH) then
    #   aFourHuaToFind = [0,1,2,3]
    # end
    aRule = Array.new
    (0..3).each do |nFourHuaIndex|
      aRule += xFindGiSuong_Rule1_FourHua(oStar1,nPanType1,aFourHuaToFind[nFourHuaIndex],aFocusHouse[nFourHuaIndex])
    end
    return aRule
  end

  # 尋找四化之一刻應的天干
  def xFindGiSuong_Rule1_FourHua(oStar1,nPanType1,nFourHuaToFind,nFocusHouseEarth)
    aRule = Array.new
    (1..10).each do |nSky|
      nStar = oStar1.gGiSuong_GetFourHuaStarValue(nSky,nFourHuaToFind)
      nHouseEarth = oStar1.gHouse_GetAStar_HouseEarth(nPanType1,nStar)
      if (nHouseEarth == nFocusHouseEarth) then
        aRule.push(nSky)
      end
    end
    return aRule
  end

  # rule 2
  def xGetGiSuong_Rule2(oStar1,nPanType1,oStar2,nPanType2)
    nHouseEarth = oStar1.gGiSuong_GetHouseEarth(nPanType1,@m_nShowGiSuongHouseId)
#puts("@m_nShowGiSuongHouseId:#{@m_nShowGiSuongHouseId}")

    # 尋找天干
    nSky = oStar2.gHouse_GetSky(nPanType2,nHouseEarth)

    # 尋找四化
    aFourHuaHouse = oStar1.gGiSuong_FindFourHuaHouse(nPanType1,nSky)  # 所選天干四化

    # 尋找刻應的小限應期，自化祿權科為吉年，忌為凶年
    aRule = xFindGiSuong_Rule2(oStar1,nPanType1,aFourHuaHouse)
    return aRule
  end

  # 尋找刻應的小限應期，自化祿權科為吉年，忌為凶年
  def xFindGiSuong_Rule2(oStar1,nPanType1,aFourHuaHouse)
    aRule2 = Array.new(2) { Array.new }
    (0..3).each do |nFourHuaIndex|
      aRule = xFindGiSuong_Rule2_FourHua(oStar1,nPanType1,aFourHuaHouse[nFourHuaIndex])
      if (aRule[0] != nil) then aRule2[0] += aRule[0] end
      if (aRule[1] != nil) then aRule2[1] += aRule[1] end
    end
    return aRule2
  end

  # 尋找四化之一刻應的小限應期，宮自化祿權科為吉年，忌為凶年
  def xFindGiSuong_Rule2_FourHua(oStar1,nPanType1,nFocusHouseEarth)
    aRule2 = Array.new(2) { Array.new }

    if (nFocusHouseEarth == nil) then
      return aRule2
    end

    # 找出宮天干
    #nHouseSky = oStar1.gHouse_GetSky(nPanType1,nFocusHouseEarth)

    # 判斷有無在此宮內,即宮自化
    aaFourHua,nFourHuaCount = oStar1.gHouse_GetAStarsSelfFourHua(nPanType1,nFocusHouseEarth)
    aaFourHua.each do |aFourHua|
      aRule = xFindGiSuong_Rule2_Result(oStar1,nPanType1,nFocusHouseEarth,aFourHua[0])
      if (aRule[0] != nil) then aRule2[0] += aRule[0] end
      if (aRule[1] != nil) then aRule2[1] += aRule[1] end
    end
    return aRule2
  end

  def xFindGiSuong_Rule2_Result(oStar1,nPanType1,nFocusHouseEarth,nFourHuaIndex)
    aRule = Array.new(2)
    if (nFourHuaIndex != 99) then
      if (nFourHuaIndex < 3) then
        # 祿權科
        nGiSuongIndex = 0
      else
        # 忌
        nGiSuongIndex = 1
      end
      if (nPanType1 == Cfate::PAN_TENYEAR) then
        aRule[nGiSuongIndex] = xFindGiSuong_Rule2_Result_TenYear(oStar1,nPanType1,nFocusHouseEarth)
      elsif (nPanType1 >= Cfate::PAN_FLOWYEAR) then
        aRule[nGiSuongIndex] = xFindGiSuong_Rule2_Result_FlowYear(oStar1,nPanType1,nFocusHouseEarth)
      end
    end
    return aRule
  end

  def xFindGiSuong_Rule2_Result_TenYear(oStar1,nPanType1,nFocusHouseEarth)
    nLargeSan = oStar1.gHouse_getLargeSan()
    nStartAge = oStar1.gGiSuong_getLargeSanStartYearOld(nLargeSan)
    nStopAge = nStartAge + 9

    aRule = Array.new
    m = oStar1.gGiSuong_GetSmallSan(nFocusHouseEarth)
    (0..7).each do |i|
      if ((m >= nStartAge) && (m <= nStopAge)) then
        aRule.push(m)
      end

      m += 12
    end
    return aRule

  end

  def xFindGiSuong_Rule2_Result_FlowYear(oStar1,nPanType1,nFocusHouseEarth)
    aRule = Array.new
    aRule.push(nFocusHouseEarth)
    return aRule
  end


  def xGetGiSuong_Rule3(oStar1,nPanType1,oStar2,nPanType2)
    nHouseEarth = oStar1.gGiSuong_GetHouseEarth(nPanType1,@m_nShowGiSuongHouseId)
#puts("nHouseEarth3:#{nHouseEarth}")

    # 尋找天干
    nSky = oStar2.gHouse_GetSky(nPanType2,nHouseEarth)
#puts("nSky3:#{nSky}")

    # 尋找四化
    aFourHuaHouse = oStar1.gGiSuong_FindFourHuaHouse(nPanType1,nSky)  # 所選天干四化
#puts("aFourHuaHouse3:#{aFourHuaHouse}")

    # 化忌之星所在宮位及其對宮之地支為凶年
    aRule = xFindGiSuong_Rule3(aFourHuaHouse[3],oStar1,nPanType1)
#puts("aRule3:#{aRule}")
    return aRule
  end

  def xFindGiSuong_Rule3(nHuaGiHouseEarth,oStar1,nPanType1)
    aRule = Array.new
    aRule.push(nHuaGiHouseEarth)
    aRule.push(Earth.ModifyEarth(nHuaGiHouseEarth+6))
    return aRule
  end

end
