class Eightword

	PAN_GOD_YEAR      =              0
	PAN_GOD_DATE      =              1

	PAN_SKY_1         =                     0
	PAN_SKY_2         =                      1

	CEN_POOL_YEAR     =              0
	CEN_POOL_MONTH    =              1

	LARGE_WIN_REAL = 0
	LARGE_WIN_VIRTUAL = 1
	LARGE_WIN_ABOUT = 2

	FIVE_TYPE_ORG     =      0
	FIVE_TYPE_CHANGE  =      1

	B_TYPE_MONTH      =      0
	B_TYPE_DATE       =              1

	TEN_GOD_DEFAULT = 0
	TEN_GOD_SELF = 1

	COMBINE_1   =    1       #*** 三方會局 ***#
	COMBINE_2   =    2       #*** 三合局 ***#
	COMBINE_3   =    3       #*** 半方會局 ***#
	COMBINE_4   =    4       #*** 半合會局 ***#
	COMBINE_5   =    5       #*** 地支六合 ***#
	COMBINE_6   =    6       #*** 六沖 ***#
	COMBINE_7   =    7       #*** 三刑 ***#
	COMBINE_8   =    8       #*** 自刑 ***#
	COMBINE_9   =    9       #*** 相刑 - 無恩之刑 ***#
	COMBINE_10  =    10      #*** 相刑 - 恃勢之刑 ***#
	COMBINE_11  =    11      #*** 相刑 - 無禮之刑 ***#
	COMBINE_12  =    12      #*** 六害 ***#

	STAR_LEVEL_1  =  0 # *Standard Free *
	STAR_LEVEL_2  =  1 # * Cheaper *
	STAR_LEVEL_3  =  2
	STAR_LEVEL_4  =  3
	STAR_LEVEL_5  =  4
	STAR_LEVEL_6  =  5
	STAR_LEVEL_7  =  6

	ENGY_ROW   =     0
	ENGY_COL   =     1
	ENGY_MID_COL  =  2
	ENGY_LINE     =  3
	ENGY_LINE_1   =  4
	ENGY_HLINE    =  5
	ENGY_HLINE_1  =  6
	ENGY_RADA     =  7

	MAIN_PAN  = "MAIN_PAN"
	CHANGE_PAN  = "CHANGE_PAN"
	PAN_FOUR_COL = "PAN_FOUR_COL"
	GOD_STYLE = "GOD_STYLE"
	FLOW_INFO = "FLOW_INFO"

	FLOW_PAN  = "FLOW_PAN"
	WIN_INFO = "WIN_INFO"
	WIN_FOUR_COL = "WIN_FOUR_COL"

	SCORE_INFO = "SCORE_INFO"
	U_LIFE            =    "U_LIFE"
	U_GOD	            =    "U_GOD"
	U_BODY	          =    "U_BODY"
	U_SKY	            =    "U_SKY"
	U_EARTH	          =    "U_EARTH"
	U_FIVE	          =    "U_FIVE"
	U_SMALL	          =    "U_SMALL"
	U_LARGE	          =    "U_LARGE"
	USER_INFO		=   "USER_INFO"
	UPI_GANZHI_8WORDS = "UPI_GANZHI_8WORDS"
	U_PAN_INFO	      =    "U_PAN_INFO"
		UPI_PAN_INFO = "UPI_PAN_INFO"
		UPI_FLOWPAN_INFO = "UPI_FLOWPAN_INFO"
		UPI_LARGE_INFO = "UPI_LARGE_INFO"
		UPI_PAN_LIFE = "UPI_PAN_LIFE"

	# GANZHI_8WORDS
    ByYear = "byYear"
    ByMonth = "byMonth"
    ByDay = "byDay"
    ByHour = "byHour"
    ByYearIdx = 0
    ByMonthIdx = 1
    ByDayIdx = 2
    ByHourIdx = 3

    BySegOffset = "BySegOffset"
    BySegTime = "BySegTime"
    ByWeekend = "ByWeekend"

	# tagEightPan  PAN_LIFE
    # 客戶資訊
    	# 交大運，例:每逢癸或戊年寒露後05天交大運
    ByMatchYear_2	 = "byMatchYear_2"		# 癸或戊 byMatchYear[2]
    Segment	 = "segment"		# segment
    BySegment	 = "bySegment"		# 寒露 bySegment
    ByMatchDay	 = "byMatchDay"		# 05天 byMatchDay
    	# 交大運，例:出生後02年11月13日交大運
    ByAfterYear		 = "byAfterYear"	# 02年 byAfterYear
    ByAfterMonth	 = "byAfterMonth"		# 11月 byAfterMonth
    ByAfterDate	 = "byAfterDate"		# 13日 byAfterDate

    # 四柱
    ByDateValue1 = "byDateValue1"
    ByDateValue2 = "byDateValue2"
    ByDateValue = "byDateValue"
    BySky_4 = "bySky_4"	#  四柱的天干 bySky[4]
    ByEarth_4 = "byEarth_4" # 四柱的地支 byEarth[4]
    ByChungSky_4_3 = "byChungSky_4_3"  # 四柱的藏干 byChungSky[4][3]
    BySkyFive_4	 = "bySkyFive_4"		# 四柱的天干之五行 bySkyFive[4]
    ByEarthFive_4	 = "byEarthFive_4"		# 四柱的地支之五行 byEarthFive[4]
    EmptyDead        = "EmptyDead"	# 空亡所在地支
    ByChungFive_4_3	 = "byChungFive_4_3"		# 四柱的藏干之五行 byChungFive[4][3]
    ByDateFiveLevel_5	 = "byDateFiveLevel_5"	# 根據BySkyFive_4而有不同值ChangeMainStarLevel byDateFiveLevel[5]
    ByMainStar_4 = "byMainStar_4"  # 四柱的主星 byMainStar[4]
    BySubStar_4_3	 = "bySubStar_4_3"	# 四柱的副星 bySubStar[4][3]
    By12Win_4		 = "by12Win_4"	# 四柱的12運 by12Win[4]
    DwStarType_4	 = "dwStarType_4"		# 四柱中神煞之神 dwStarType[4]
		GOD_1       =       1  #   0x80000000      #三奇貴人
		GOD_2       =       2  #   0x40000000      #魁罡貴人
		GOD_3       =       3  #   0x20000000      #金神
		GOD_4       =       4  #   0x10000000      #福星
		GOD_5       =       5  #   0x08000000      #天乙貴人
		GOD_6       =       6  #   0x04000000      #文昌
		GOD_7       =       7  #   0x02000000      #天德
		GOD_8       =       8  #   0x01000000      #月德
		GOD_9       =       9  #   0x00800000      #月將
		GOD_10      =       10 #   0x00400000      #將星
		GOD_11      =       11 #    0x00200000      #華蓋
		GOD_12      =       12 #    0x00100000      #學堂
		GOD_13      =       13 #   0x00080000      #詞館
		GOD_14      =       14 #    0x00040000      #驛馬
		GOD_15      =       15 #    0x00020000      #紅鸞
		GOD_16      =       16 #    0x00010000      #天喜
		GOD_17      =       17 #    0x00008000      #六秀
		GOD_18      =       18 #    0x00004000      #十靈
		GOD_19      =       19 #    0x00002000      #天醫
		GOD_20      =       20 #    0x00001000      #祿神
		GOD_21      =       21 #    0x00000800      #天德合
		GOD_22      =       22 #    0x00000400      #月德合
	DwBStarType_4	 = "dwBStarType_4"		# 四柱中神煞之煞dwBStarType[4]
		KILL_1      =      1  #    0x80000000      #羊刃
		KILL_2      =      2   #   0x40000000      #飛刃
		KILL_3      =      3  #    0x20000000      #劫煞
		KILL_4      =      4   #   0x10000000      #亡神
		KILL_5      =      5  #    0x08000000      #咸池
		KILL_6      =      6  #    0x04000000      #紅艷
		KILL_7      =      7  #    0x02000000      #外桃
		KILL_8      =      8  #    0x01000000      #六甲空亡
		KILL_9      =      9  #   0x00800000      #孤虛
		KILL_10     =      10 #     0x00400000      #四大空亡
		KILL_11     =      11 #     0x00200000      #孤辰
		KILL_12     =      12 #     0x00100000      #寡宿
		KILL_13     =      13 #     0x00080000      #天轉
		KILL_14     =      14 #    0x00040000      #地轉
		KILL_15     =      15 #     0x00020000      #天羅
		KILL_16     =      16 #     0x00010000      #地網
		KILL_17     =      17 #     0x00008000      #十惡大敗
		KILL_18     =      18 #     0x00004000      #四廢
    ByNaIn_4	 = "byNaIn_4"		# 四柱的納音 byNaIn[4]
    Scores = "Scores"
    FSkyArray = "FSkyArray"
    BySkyScore_4	 = "bySkyScore_4"		# 用來算出NFiveSum_5 bySkyScore[4]
    ByEarthScore_4	 = "byEarthScore_4"		# 用來算出NFiveSum_5 byEarthScore[4]
    ByChungScore_4_3	 = "byChungScore_4_3"	# 用來算出NSick_10及NFiveValue_5 byChungScore[4][3]
    NSkyScore_10	 = "nSkyScore_10"		# 天干基本分數，用來計算NGodScore_10 nSkyScore[10]
    NGodScore_10	 = "nGodScore_10"		# 十神基本分 nGodScore[10]
    FHRScore_10	 = "FHRScore_10"		# 十神基本分 nGodScore[10]
    NFiveScore_5	 = "nFiveScore_5"		# 五行基本分 nFiveScore[5]
    NInYangValue	 = "nInYangValue"		# 陰陽基本分 nInYangValue
    NSkyType_5	 = "nSkyType_5"		# nSkyType[5]
    NEarthType_5	 = "nEarthType_5"		# nEarthType[5]
    BChangePan = "BChangePan"

    # 判斷特殊格局
    # 格局用神
    NSpecialType  = "nSpecialType" # 格局用神
    ByUseGod_5	 = "byUseGod_5"		# 喜用神之五行 byUseGod[5]
    ByFiveLevel_5	 = "byFiveLevel_5"		# 旺相休囚死之五行 byFiveLevel[5]
    # 中用神
    NUseGodStyle	= "nUseGodStyle"  # 中用神
    ByUseGodSky	 = "byUseGodSky"		# 用神天干 byUseGodSky
    ByLikeGodSky_4	 = "byLikeGodSky_4"		# 喜神天干 byLikeGodSky[4]

    # 基本命宮命格等
	LifeHouse     =   "LifeHouse"  # 命宮 干支
	LifeStyle     =   "LifeStyle"  # 命格 食神...
	TaiUan     =   "TaiUan"  # 胎元 乙巳
	TaiHsi     =   "TaiHsi"  # 胎息 辛亥
	YearEmpty     =   "YearEmpty"  # 年空 戌亥
	DayEmpty     =   "DayEmpty"  # 日空 戌亥
	UsePeople    =   "UsePeople" # 人元用事

    # 流盤資訊
    ByLargeYear_2	 = "byLargeYear_2"		# 十年大運之年紀，十年拆成2個5年 byLargeYear[2]
    ByWinSky_12	 = "byWinSky_12"		# 大運天干 byWinSky[12]
    ByWinEarth_12	 = "byWinEarth_12"		# 大運地支 byWinEarth[12]
    ByLargeGod_12	 = "byLargeGod_12"		# 大運十神上 byLargeGod[12]
    BySmallGod_12	 = "bySmallGod_12"		# 大運十神下 bySmallGod[12]
	    # 日主旺度 A90 > 30B
	    NA	 = "nA"		# A90 nA
	    NB	 = "nB"		# 30B nB
    	BStrongLife	 = "bStrongLife"		# nA>=nB為true，調整用神 bStrongLife
	    # 疾病檢核表
	    NSick_10	 = "nSick_10"		# nSick[10]
	    # 十神分數
    	NGodValue_10	 = "nGodValue_10"		# nGodValue[10]
    	# 五行分數
    	NFiveSum_5	 = "nFiveSum_5"		# nFiveSum[5]
    	NOEarthFive_5 = "nOEarthFive_5"
    	NFiveValue_5	 = "nFiveValue_5"		# nFiveValue[5]
    NWinValue	 = "nWinValue"		# nWinValue
	NOrgCombine  = "nOrgCombine"

    @@ByWhat = [Eightword::ByYear,Eightword::ByMonth,Eightword::ByDay,Eightword::ByHour]
	def Eightword.byWhat2(byWhat)
		if (byWhat.class == Integer) then
			return @@ByWhat[byWhat]
		else
			return byWhat
		end
	end
	def Eightword.byWhat(byWhat)
		if (byWhat.class == Integer) then
			return byWhat
		else
			return @@ByWhat[byWhat].index
		end
	end

	#流盤取時間的方法
	PAN_TIME_BASE_BIRTH     =        0
	PAN_TIME_BASE_NOW       =        1
	PAN_TIME_BASE_DEFINE		=				 2

	#起小限規則
	PAN_SSAN_TRAD          =        0  #傳統排法
	PAN_SSAN_BIRTH_EARTH   =        1  #生年地支起小限

	def Eightword.getFiveName(nFive)
		nFive += 1
		return Pm.GetStr("IDS_E_FIVE_#{nFive}")
	end

	HER6 = "her"
	CHUON = "chuon"
	HSIN = "hsin"
	HAI = "hai"

	#參數設定
  PanPar_Copy = 0
	PanPar_Type = 1
	PanPar_Interface = 2
	PanPar_Display = 3
	PanPar_TenGod = 4
	PanPar_HallNumber = 5
	PanPar_Print = 6


	DIS_E0HD_LATE = 0
	DIS_E0HD_ORIGINAL = 1

	DIS_FIVE_HIDE = 0
	DIS_FIVE_SHOW = 1

	DIS_YEAROLD_SPRING = 0
	DIS_YEAROLD_WINTER = 1
	DIS_YEAROLD_LUNAR = 2

	DIS_SICK_SHOW = 0
	DIS_SICK_HIDE = 1
	DIS_SICK_SELF = 2

	def Eightword.getGodStr(nId)
		return Pm.GetStr("IDS_E_ST_GOD_#{nId}")
	end
	def Eightword.getKillStr(nId)
		return Pm.GetStr("IDS_E_BST_KILL_#{nId}")
	end

	ADP_SKYHER = 1 # IDS_E_ADP_SKYHER : 天干合化
	ADP_EARTHHER3 = 2 # IDS_E_ADP_EARTHHER3 : 地支三合
	ADP_EARTHHER6  = 3 # IDS_E_ADP_EARTHHER6 : 地支六合
	ADP_EARTHCHUN6 = 4 # IDS_E_ADP_EARTHCHUN6 : 地支六沖
	ADP_EARTHHSIN = 5 # IDS_E_ADP_EARTHHSIN : 地支相刑
	ADP_EARTHHAI = 6 # IDS_E_ADP_EARTHHAI : 地支相害

	def Eightword.getAncillaryStrKey(nId)
		  sKey = ["IDS_E_ADP_SKYHER","IDS_E_ADP_EARTHHER3","IDS_E_ADP_EARTHHER6","IDS_E_ADP_EARTHCHUN6",
		  		  "IDS_E_ADP_EARTHHSIN","IDS_E_ADP_EARTHHAI"]
		  return sKey[nId - 1]
	end
	def Eightword.getAncillaryStr(nId)
		return Pm.GetStr(Eightword.getAncillaryStrKey(nId))
	end
	def Eightword.GodCount()
		return 22
	end
	def Eightword.KillCount()
		return 18
	end

  	U_BigWinStart = "ew_bigWinStart"
  	U_UseGodFive = "ew_UseGod_five"

end
