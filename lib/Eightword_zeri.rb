class Eightword
  def Eightword.zeri_pan_data_constant_data(zeri_simple)
    const_data = Hash.new
    return const_data
  end
  def Eightword.zeri_pan_data_default(zeri_simple)
    zeri = Hash.new
    if (zeri_simple == 1) then
      return zeri
    end
    zeri["chaxuncanshuchucunmingcheng"] = ""
    return zeri
  end
  def Eightword.zeri_pan_data_parsing(zeri_simple,hData)
    zeri = Hash.new
    if (zeri_simple == 1) then
      return zeri
    end
    zeri["chaxuncanshuchucunmingcheng"] = hData["chaxuncanshuchucunmingcheng"]
    return zeri
  end
  def match_zeri_pars(zeri_pars)
    return match_zeri_par(Cfate::PAN_NORMAL,zeri_pars)
  end
  def match_zeri_par(pantype,zeri_par)
    return true
  end
end
