require("Star.rb")
require("Pm.rb")

# ziwei 吉凶 數值 應期計算 依四化各別產出
# For Star
# rule 1: 四化飛出的宮位，有碰到先天四化或宮干自化者，即為吉
# rule 2: 四化飛出的宮位，若碰到宮干自化祿權科者為吉，碰到忌者為凶
# rule 3: 四化之化忌飛出的宮位及其對宮即為凶
class PanGiSuong

  def zwld_Star_Reset_Result()
    aRule1 = aRule2 = aRule3 = nil
    @FlowEYear = 0
    @FlowEMonth = 0
    @FlowLeap = false
    @FlowEDate = 0
  end

  def zwld_Star_GetGiSuong_TenYear(nPanType,hUserInfo,hUserDefData,hUserType,nGiSuongHouseId)
    oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    # hStarInfo = oStar.g_GetStarPanInfo(nPanType,hUserInfo,hUserDefData,@UserType)
    # flow_title = "#{oStar.g_getLargeSanStartYear()}~#{oStar.g_getLargeSanStartYear() + 9}"
    flow_title = oStar.gWeen_GetFlowTimeStr(nPanType)

    aRule1,aRule2,aRule3 = wae_GetGiSuong_Rule(oStar,Cfate::PAN_TENYEAR)

    vRule1,vRule2,vRule3 = zwld_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)

    return zwld_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3),flow_title
  end
  def zwld_Star_GetGiSuong_FlowYear(nPanType,hUserInfo,hUserDefData,hUserType,nGiSuongHouseId)
    # oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    hUserDefData[Cfate::EMonth] = 6
    hUserDefData[Cfate::LeapMonth] = false
    # hUserType[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN
    hUserType[Star::PAR_FLOWLIFE_HOUSETYPE] = Star::PAN_FLH_SMALL_SAN_FLOW_SKY
    oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    # hStarInfo = oStar.g_GetStarPanInfo(nPanType,hUserInfo,hUserDefData,hUserType)
    # flow_title = Xdate.GetYearStr(oStar.fdg_EY(nPanType))
    flow_title = oStar.gWeen_GetFlowTimeStr(nPanType)

    aRule1,aRule2,aRule3 = wae_GetGiSuong_Rule(oStar,Cfate::PAN_FLOWYEAR)

    vRule1,vRule2,vRule3 = zwld_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)
    return zwld_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3),flow_title
  end
  def zwld_Star_GetGiSuong_FlowMonth(nPanType,hUserInfo,hUserDefData,hUserType,nGiSuongHouseId)
    oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    # hStarInfo = oStar.g_GetStarPanInfo(nPanType,hUserInfo,hUserDefData,hUserType)
    # flow_title = Xdate.GetYearStr(oStar.fdg_EY(nPanType)) + Xdate.GetMonthStr(oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType))
    flow_title = oStar.gWeen_GetFlowTimeStr(nPanType)

    aRule1,aRule2,aRule3 = wae_GetGiSuong_Rule(oStar,Cfate::PAN_FLOWMONTH)

    vRule1,vRule2,vRule3 = zwld_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)

    return zwld_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3),flow_title
  end
  def zwld_Star_GetGiSuong_FlowDate(nPanType,hUserInfo,hUserDefData,hUserType,nGiSuongHouseId)
    oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    # hStarInfo = oStar.g_GetStarPanInfo(nPanType,hUserInfo,hUserDefData,hUserType)
    # flow_title = Xdate.GetYearStr(oStar.fdg_EY(nPanType)) + Xdate.GetMonthStr(oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType))
    # flow_title += Xdate.GetDateStr(oStar.fdg_ED(nPanType))
    flow_title = oStar.gWeen_GetFlowTimeStr(nPanType)

    aRule1,aRule2,aRule3 = wae_GetGiSuong_Rule(oStar,nPanType)

    vRule1,vRule2,vRule3 = zwld_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)
    return zwld_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3),flow_title
  end
  def zwld_Star_GetGiSuong_FlowTime(nPanType,hUserInfo,hUserDefData,hUserType,nGiSuongHouseId)
    oStar = Star.new(hUserInfo,hUserDefData,hUserType)
    # hStarInfo = oStar.g_GetStarPanInfo(nPanType,hUserInfo,hUserDefData,hUserType)
    # flow_title = Xdate.GetYearStr(oStar.fdg_EY(nPanType)) + Xdate.GetMonthStr(oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType))
    # flow_title += Xdate.GetDateStr(oStar.fdg_ED(nPanType))
    # flow_title += Xdate.GetETimeStr(oStar.fdg_ET(nPanType))
    flow_title = oStar.gWeen_GetFlowTimeStr(nPanType)

    aRule1,aRule2,aRule3 = wae_GetGiSuong_Rule(oStar,nPanType)

    vRule1,vRule2,vRule3 = zwld_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)

    return zwld_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3),flow_title
  end


  def zwld_GetGiSuong_Value(nPanType,oStar,aRule1,aRule2,aRule3)
    vRule1 = zwld_GetGiSuong_Value_Rule1(nPanType,oStar,aRule1)
    vRule2 = zwld_GetGiSuong_Value_Rule2(nPanType,oStar,aRule2)
    vRule3 = zwld_GetGiSuong_Value_Rule3(nPanType,oStar,aRule3)
    return vRule1,vRule2,vRule3
  end
  def zwld_GetGiSuong_Value_Rule1(nPanType,oStar,aRule1)
    vRule1 = [Array.new(10,0),Array.new(10,0),Array.new(10,0),Array.new(10,0)]
    if (aRule1 == nil) then
      return vRule1
    end
    aRule1.each do |r|
      nSkyIndex = Sky.Sky2SkyIndex(r[1])
      if (vRule1[r[3]][nSkyIndex] > 0) then
        vRule1[r[3]][nSkyIndex] += 23
      else
        vRule1[r[3]][nSkyIndex] += 30
      end
    end

    return vRule1
  end

  def zwld_GetGiSuong_Value_Rule2(nPanType,oStar,aRule2)
    case (nPanType)
      when (Cfate::PAN_TENYEAR) then
        return zwld_GetGiSuong_Value_Rule2_TenYear(nPanType,oStar,aRule2)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        return zwld_GetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,aRule2)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        return zwld_GetGiSuong_Value_Rule2_FlowMonth(nPanType,oStar,aRule2)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        return zwld_GetGiSuong_Value_Rule2_FlowDate(nPanType,oStar,aRule2)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        return zwld_GetGiSuong_Value_Rule2_FlowTime(nPanType,oStar,aRule2)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        return Hash.new    # PAN_FLOWMIN
    end
  end
  def zwld_GetGiSuong_Value_Rule2_TenYear(nPanType,oStar,aRule2)
    vRule2 = [[Array.new(10,0),Array.new(10,0)],[Array.new(10,0),Array.new(10,0)],[Array.new(10,0),Array.new(10,0)],[Array.new(10,0),Array.new(10,0)]]
    if (aRule2 == nil) then
      return vRule2
    end
    v = [18,-18]
    nLargeSan = oStar.gHouse_getLargeSan()
    nStartAge = oStar.gGiSuong_getLargeSanStartYearOld(nLargeSan)
    # 吉 0,凶 1
    aRule2.each_index do |i|
      aRule2[i].each do |r|
        nAge = r[1]
        vRule2[r[2]][i][nAge - nStartAge] += v[i]
      end
    end
    return vRule2
  end
  def zwld_GetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,aRule2)
    vRule2 = [[Array.new(12,0),Array.new(12,0)],[Array.new(12,0),Array.new(12,0)],[Array.new(12,0),Array.new(12,0)],[Array.new(12,0),Array.new(12,0)]]
    if (aRule2 == nil) then
      return vRule2
    end
    v = [18,-18]
    # 吉 0,凶 1
    aRule2.each_index do |i|
      aRule2[i].each do |r|
        nFocusHouseEarthIndex = Earth.Earth2EarthIndex(r[0])
        vRule2[r[2]][i][nFocusHouseEarthIndex] += v[i]
      end
    end
    return vRule2
  end
  def zwld_GetGiSuong_Value_Rule2_FlowMonth(nPanType,oStar,aRule2)
    return zwld_GetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,aRule2)
  end
  def zwld_GetGiSuong_Value_Rule2_FlowDate(nPanType,oStar,aRule2)
    return zwld_GetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,aRule2)
  end
  def zwld_GetGiSuong_Value_Rule2_FlowTime(nPanType,oStar,aRule2)
    return zwld_GetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,aRule2)
  end
  def zwld_GetGiSuong_Value_Rule3(nPanType,oStar,aRule3)
    vRule3 = [Array.new(12,0),Array.new(12,0),Array.new(12,0),Array.new(12,0)]
    if (aRule3 == nil) then
      return vRule3
    end
    aRule3.each do |nHouseEarth|
      nEarthIndex = Earth.Earth2EarthIndex(nHouseEarth)
      vRule3[3][nEarthIndex] += -30
    end
    return vRule3
  end

  def zwld_SetGiSuong_Value(nPanType,oStar,vRule1,vRule2,vRule3)
    h = Hash.new
    h["rule1"] = zwld_SetGiSuong_Value_Rule1(nPanType,oStar,vRule1)
    h["rule2"] = zwld_SetGiSuong_Value_Rule2(nPanType,oStar,vRule2)
    h["rule3"] = zwld_SetGiSuong_Value_Rule3(nPanType,oStar,vRule3)
    return h
  end
  def zwld_SetGiSuong_Value_Rule1(nPanType,oStar,vRule1)
    case (nPanType)
      when (Cfate::PAN_TENYEAR) then
        return zwld_SetGiSuong_Value_Rule1_TenYear(nPanType,oStar,vRule1)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        return zwld_SetGiSuong_Value_Rule1_FlowYear(nPanType,oStar,vRule1)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        return zwld_SetGiSuong_Value_Rule1_FlowMonth(nPanType,oStar,vRule1)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        return zwld_SetGiSuong_Value_Rule1_FlowDate(nPanType,oStar,vRule1)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        return zwld_SetGiSuong_Value_Rule1_FlowTime(nPanType,oStar,vRule1)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        return Hash.new    # PAN_FLOWMIN
    end
  end
  def zwld_SetGiSuong_Value_Rule1_TenYear(nPanType,oStar,vRule1)
  # puts "zwld_SetGiSuong_Value_Rule1_TenYear => vRule1 : #{vRule1}"
    a = Array.new(4,nil)
    nLargeSan = Earth.Modify(oStar.gHouse_getLargeSan())
    nStartEYear = oStar.gGiSuong_getLargeSanStartYear(nLargeSan)
    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      (0..9).each do |i|
        nEYear = nStartEYear + i
        nSky,nEarth = Xdate.GetLunarYearGanZhiSkyEarth(nEYear)
        h["#{Xdate.GetYearStr(nEYear)}"] = vRule1[nFourHuaIndex][Sky.Sky2SkyIndex(nSky)]
      end
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule1_FlowYear(nPanType,oStar,vRule1)
    a = Array.new(4,nil)
    nEYear = oStar.fdg_EY(nPanType)
    mArray = Xdate.GetEastMonthArray(nEYear)
    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      (0...mArray.length).each do |nMonthIndex|
        nEMonth = mArray[nMonthIndex][0]
        bLeapMonth = mArray[nMonthIndex][2]
        nSky,nEarth = Xdate.GetLunarMonthGanZhiSkyEarth(nEYear,nEMonth)
        h["#{Xdate.GetMonthStr(nEMonth,bLeapMonth)}"] = vRule1[nFourHuaIndex][Sky.Sky2SkyIndex(nSky)]
      end
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule1_FlowMonth(nPanType,oStar,vRule1)
    a = Array.new(4,nil)
    nEYear,nEMonth,bLeapMonth = oStar.fdg_EY(nPanType),oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType)
    nDays = Xdate.GetEMonthDays(nEYear,nEMonth,bLeapMonth)
    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      (1..nDays).each do |nEDate|
        nSky,nEarth = Xdate.GetLunarDateGanZhiSkyEarth(nEYear,nEMonth,nEDate,1,bLeapMonth)
        h["#{Xdate.GetDateStr(nEDate)}"] = vRule1[nFourHuaIndex][Sky.Sky2SkyIndex(nSky)]
      end
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule1_FlowDate(nPanType,oStar,vRule1)
    a = Array.new(4,nil)
    nEYear,nEMonth,bLeapMonth,nEDate = oStar.fdg_EY(nPanType),oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType),oStar.fdg_ED(nPanType)
    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      (1..12).each do |nETime|
        nSky,nEarth = Xdate.GetLunarTimeGanZhiSkyEarth(nEYear,nEMonth,nEDate,nETime - 1,bLeapMonth)
        h["#{Xdate.GetETimeStr(nETime - 1)}"] = vRule1[nFourHuaIndex][Sky.Sky2SkyIndex(nSky)]
      end
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule1_FlowTime(nPanType,oStar,vRule1)
    a = Array.new(4,nil)
    nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType)
    nSky = oStar.gHouse_GetSky(nPanType,nEarth)
    nGanZhi = Xdate.SkyEarth2GanZhi(nSky,nEarth)
    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      (1..12).each do |nEMinute|
        nGanZhiMin = nGanZhi + nEMinute - 1
        nSky,nEarth = Xdate.GetGanZhiSkyEarth(nGanZhiMin)
        h["#{Xdate.GetEMinRangeStr(oStar.fdg_ET(nPanType),nEMinute - 1)}"] = vRule1[nFourHuaIndex][Sky.Sky2SkyIndex(nSky)]
      end
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule2(nPanType,oStar,vRule2)
    case (nPanType)
      when (Cfate::PAN_TENYEAR) then
        return zwld_SetGiSuong_Value_Rule2_TenYear(nPanType,oStar,vRule2)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        return zwld_SetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,vRule2)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        return zwld_SetGiSuong_Value_Rule2_FlowMonth(nPanType,oStar,vRule2)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        return zwld_SetGiSuong_Value_Rule2_FlowDate(nPanType,oStar,vRule2)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        return zwld_SetGiSuong_Value_Rule2_FlowTime(nPanType,oStar,vRule2)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        return Hash.new    # PAN_FLOWMIN
    end
  end
  def zwld_SetGiSuong_Value_Rule2_TenYear(nPanType,oStar,vRule2)
    a = Array.new(4,nil)
    nLargeSan = Earth.Modify(oStar.gHouse_getLargeSan())
    nStartEYear = oStar.gGiSuong_getLargeSanStartYear(nLargeSan)
    nStartEYearOld = oStar.gGiSuong_getLargeSanStartYearOld(nLargeSan)

    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      b = Hash.new
      c = Hash.new
      (0..9).each do |i|
        nEYear = nStartEYear + i
        # a[i] = "#{nEYear}:#{nStartEYearOld + i}"
        b["#{Xdate.GetYearStr(nEYear)}"] = vRule2[nFourHuaIndex][0][i]
        c["#{Xdate.GetYearStr(nEYear)}"] = vRule2[nFourHuaIndex][1][i]
      end
      # h["title"] = a
      h["gi"] = b
      h["xiong"] = c
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule2_FlowYear(nPanType,oStar,vRule2)
    a = Array.new(4,nil)
    mArray = Xdate.GetEastMonthArray(oStar.fdg_EY(nPanType))
    nEarth = zwld_GetGod(oStar) # 子年斗君所在的宮位為1月

    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      b = Hash.new
      c = Hash.new
      (0...mArray.length).each do |nMonthIndex|
        nEMonth = mArray[nMonthIndex][0]
        bLeapMonth = mArray[nMonthIndex][2]
        nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nEMonth - 1 + nEarth)
        # a[nMonthIndex] = "#{bLeapMonth}#{nEMonth}"
        # b[nMonthIndex] = vRule2[0][nFocusHouseEarthIndex]
        # c[nMonthIndex] = vRule2[1][nFocusHouseEarthIndex]
        b["#{Xdate.GetMonthStr(nEMonth,bLeapMonth)}"] = vRule2[nFourHuaIndex][0][nFocusHouseEarthIndex]
        c["#{Xdate.GetMonthStr(nEMonth,bLeapMonth)}"] = vRule2[nFourHuaIndex][1][nFocusHouseEarthIndex]
      end
      # h["title"] = a
      h["gi"] = b
      h["xiong"] = c
      a[nFourHuaIndex] = h
    end
    return a
  end
  # 子年斗君
  def zwld_GetGod(oStar)
    nVal = oStar.cp_getGod()
    nVal = Earth.EarthIndex2Earth(nVal)
    return nVal
  end
  def zwld_SetGiSuong_Value_Rule2_FlowMonth(nPanType,oStar,vRule2)
    a = Array.new(4,nil)
    nEYear,nEMonth,bLeapMonth = oStar.fdg_EY(nPanType),oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType)
    nDays = Xdate.GetEMonthDays(nEYear,nEMonth,bLeapMonth)
    nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType) # 命宮所在宮位起1日

    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      b = Hash.new
      c = Hash.new
      (1..nDays).each do |nEDate|
        nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nEDate - 1 + nEarth)
        # a[nEDate - 1] = "#{nEDate}:#{nFocusHouseEarthIndex}:#{nEarth}"
        # b[nEDate - 1] = vRule2[0][nFocusHouseEarthIndex]
        # c[nEDate - 1] = vRule2[1][nFocusHouseEarthIndex]
        b["#{Xdate.GetDateStr(nEDate)}"] = vRule2[nFourHuaIndex][0][nFocusHouseEarthIndex]
        c["#{Xdate.GetDateStr(nEDate)}"] = vRule2[nFourHuaIndex][1][nFocusHouseEarthIndex]
      end
      # h["title"] = a
      h["gi"] = b
      h["xiong"] = c
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule2_FlowDate(nPanType,oStar,vRule2)
    a = Array.new(4,nil)

    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      b = Hash.new
      c = Hash.new
      (1..12).each do |nETime|
        nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nETime)
        b["#{Xdate.GetETimeStr(nETime - 1)}"] = vRule2[nFourHuaIndex][0][nFocusHouseEarthIndex]
        c["#{Xdate.GetETimeStr(nETime - 1)}"] = vRule2[nFourHuaIndex][1][nFocusHouseEarthIndex]
      end
      # h["title"] = a
      h["gi"] = b
      h["xiong"] = c
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule2_FlowTime(nPanType,oStar,vRule2)
    a = Array.new(4,nil)
    nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType) # 命宮所在宮位起1日

    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      b = Hash.new
      c = Hash.new
      (1..12).each do |nEMinute|
        nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nEMinute - 1 + nEarth)
        b["#{Xdate.GetEMinRangeStr(oStar.fdg_ET(nPanType),nEMinute - 1)}"] = vRule2[nFourHuaIndex][0][nFocusHouseEarthIndex]
        c["#{Xdate.GetEMinRangeStr(oStar.fdg_ET(nPanType),nEMinute - 1)}"] = vRule2[nFourHuaIndex][1][nFocusHouseEarthIndex]
      end
      # h["title"] = a
      h["gi"] = b
      h["xiong"] = c
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule3(nPanType,oStar,vRule3)
    case (nPanType)
      when (Cfate::PAN_TENYEAR) then
        return zwld_SetGiSuong_Value_Rule3_TenYear(nPanType,oStar,vRule3)  # PAN_TENYEAR
      when (Cfate::PAN_FLOWYEAR) then
        return zwld_SetGiSuong_Value_Rule3_FlowYear(nPanType,oStar,vRule3)  # PAN_FLOWYEAR
      when (Cfate::PAN_FLOWMONTH) then
        return zwld_SetGiSuong_Value_Rule3_FlowMonth(nPanType,oStar,vRule3)  # PAN_FLOWMONTH
      when (Cfate::PAN_FLOWDATE) then
        return zwld_SetGiSuong_Value_Rule3_FlowDate(nPanType,oStar,vRule3)   # PAN_FLOWDATE
      when (Cfate::PAN_FLOWTIME) then
        return zwld_SetGiSuong_Value_Rule3_FlowTime(nPanType,oStar,vRule3)  # PAN_FLOWTIME
      when (Cfate::PAN_FLOWMIN) then
        return Hash.new    # PAN_FLOWMIN
    end
  end
  def zwld_SetGiSuong_Value_Rule3_TenYear(nPanType,oStar,vRule3)
    a = Array.new(4,nil)

    nLargeSan = Earth.Modify(oStar.gHouse_getLargeSan())
    nStartEYear = oStar.gGiSuong_getLargeSanStartYear(nLargeSan)
    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      (0..9).each do |i|
        nEYear = nStartEYear + i
        nSky,nEarth = Xdate.GetLunarYearGanZhiSkyEarth(nEYear)
        h["#{Xdate.GetYearStr(nEYear)}"] = vRule3[nFourHuaIndex][Earth.Earth2EarthIndex(nEarth)]
      end
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule3_FlowYear(nPanType,oStar,vRule3)
    a = Array.new(4,nil)

    nEYear = oStar.fdg_EY(nPanType)
    mArray = Xdate.GetEastMonthArray(nEYear)
    nEarth = zwld_GetGod(oStar) # 子年斗君所在的宮位為1月
    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      (0...mArray.length).each do |nMonthIndex|
        nEMonth = mArray[nMonthIndex][0]
        bLeapMonth = mArray[nMonthIndex][2]
        nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nEMonth - 1 + nEarth)
        h["#{Xdate.GetMonthStr(nEMonth,bLeapMonth)}"] = vRule3[nFourHuaIndex][nFocusHouseEarthIndex]
      end
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule3_FlowMonth(nPanType,oStar,vRule3)
    a = Array.new(4,nil)

    nEYear,nEMonth,bLeapMonth = oStar.fdg_EY(nPanType),oStar.fdg_EM(nPanType),oStar.fdg_EL(nPanType)
    nDays = Xdate.GetEMonthDays(nEYear,nEMonth,bLeapMonth)
    nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType)
    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      (1..nDays).each do |nEDate|
        nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nEDate - 1 + nEarth)
        h["#{Xdate.GetDateStr(nEDate)}"] = vRule3[nFourHuaIndex][nFocusHouseEarthIndex]
      end
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule3_FlowDate(nPanType,oStar,vRule3)
    a = Array.new(4,nil)
    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      (1..12).each do |nETime|
        nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nETime)
        h["#{Xdate.GetETimeStr(nETime - 1)}"] = vRule3[nFourHuaIndex][nFocusHouseEarthIndex]
      end
      a[nFourHuaIndex] = h
    end
    return a
  end
  def zwld_SetGiSuong_Value_Rule3_FlowTime(nPanType,oStar,vRule3)
    a = Array.new(4,nil)
    nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType)
    (0..3).each do |nFourHuaIndex|
      h = Hash.new
      (1..12).each do |nEMinute|
        nFocusHouseEarthIndex = Earth.Earth2EarthIndex(nEMinute - 1 + nEarth)
        h["#{Xdate.GetEMinRangeStr(oStar.fdg_ET(nPanType),nEMinute - 1)}"] = vRule3[nFourHuaIndex][nFocusHouseEarthIndex]
      end
      a[nFourHuaIndex] = h
    end
    return a
  end

  def zwld_GetGiSuongText_Rule1(nSky)
    szGi = ""
    if (@aRule1 == nil) then
      return szGi
    end

    @aRule1.each do |nRuleSky|
      if (nRuleSky == nSky) then
        szGi = Pm.GetStr("IDS_GISUONG_GI")
      end
    end
    return szGi
  end

  def zwld_GetGiSuongText_Rule2(nAge)
    szGi = szSuong = ""
    if (@aRule2 == nil) then
      return szGi,szSuong
    end

    @aRule2[0].each do |nRuleAge|
      if (nRuleAge == nAge) then
        szGi = Pm.GetStr("IDS_GISUONG_GI")
      end
    end

    @aRule2[1].each do |nRuleAge|
      if (nRuleAge == nAge) then
        szSuong = Pm.GetStr("IDS_GISUONG_SUONG")
      end
    end

    return szGi,szSuong
  end

  def zwld_GetGiSuongText_Rule3(nEarth)
    szSuong = ""
    if (@aRule3 == nil) then
      return szSuong
    end

    @aRule3.each do |nRuleEarth|
      if (nRuleEarth == nEarth) then
        szSuong = Pm.GetStr("IDS_GISUONG_SUONG")
      end
    end

    return szSuong
  end

end
