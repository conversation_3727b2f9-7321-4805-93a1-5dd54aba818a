# coding: UTF-8

class Cfate

  DEFAULT_LANG = "zh-TW"
  SERVER_URL = "meen.tw"

  @@num_objects = 0
  #constructor initialize
  def initialize()
    @@num_objects += 1
    #instance variable
  end

  PAN_FIRST_YEAR = 1901
  PAN_LAST_YEAR = 2099

  #年代顯示
  PAN_DISPLAY_WEST      =    0  #西元年
  PAN_DISPLAY_CHINA     =    1  #民國年
  PAN_DISPLAY_GANZHI      =    2  #干支年


  #排盤類別
  PAN_NONE      =    -1
  PAN_NORMAL    =     0
  PAN_TENYEAR   =     1
  PAN_FLOWYEAR  =     2
  PAN_FLOWMONTH =     3
  PAN_FLOWDATE  =     4
  PAN_FLOWTIME  =     5
  PAN_FLOWMIN   =     6
  PAN_CNT = 7
  PAN_CHANGE = 9

  #各盤的名字
  PAN_TYPE_KEY_0          =   "b_PAN_NORMAL"
  PAN_TYPE_KEY_1         =   "b_PAN_TENYEAR"
  PAN_TYPE_KEY_2       =   "b_PAN_FLOWYEAR"
  PAN_TYPE_KEY_3       =   "b_PAN_FLOWMONTH"
  PAN_TYPE_KEY_4       =   "b_PAN_FLOWDATE"
  PAN_TYPE_KEY_5       =   "b_PAN_FLOWTIME"
  PAN_TYPE_KEY_6         =   "b_PAN_FLOWMIN"
  def Cfate.gGetPanTypeHashKey(nPanType)
    return eval("Cfate::PAN_TYPE_KEY_#{nPanType}")
  end
  def Cfate.gFindPanTypeByHashKey(hashKey)
    (0..6).each do |pt|
      if (hashKey == Cfate.gGetPanTypeHashKey(pt)) then
        return pt
      end
    end
    return 0
  end


  #個人資訊索引值
  Name     =    "Name"        #姓名
  EYear     =    "EYear"        #出生年(農曆)
  EMonth    =    "EMonth"       #出生月(農曆)
  ELeapMonth    =    "ELeapMonth"       #䦌月依䦌月排法調整之月份，影響右鳀，左輔之值
  EDate    =     "EDate"        #出生日(農曆)
  ETime     =    "ETime"        #出生時(地支時，十二個時辰)
  EMinute     =    "EMinute"        #出生分
  LeapMonth =    "LeapMonth"    #出生月是否為潤月
  WYear     =    "WYear"        #出生年(西元曆)
  WMonth    =    "WMonth"       #出生月(西元曆)
  WDate    =     "WDate"        #出生日(西元曆)
  WHour      =   "WHour"         #出生時(24小時制)
  WMinute     =    "WMinute"        #出生分(西元曆)
  Sex       =    "Sex"          #性別 true 是女性,false是男性
  SexInYang  = "SexInYang"       #0為陽,1為陰(陰男,陽男,陰女,陽女用)
  PickDays  = "pickdays"       #剖腹擇日前後幾天
  Question = "question"
  CalType     =    "CalType"        #出生年(西元曆)

  # 原輸入之出生日
  WYear_B     =    "WYear_B"        #出生年(西元曆)
  WMonth_B    =    "WMonth_B"       #出生月(西元曆)
  WDate_B    =     "WDate_B"        #出生日(西元曆)
  WHour_B      =   "WHour_B"         #出生時(24小時制)
  WMinute_B     =    "WMinute_B"        #出生分(西元曆)
  EYear_B     =    "EYear_B"        #出生年(農曆)
  EMonth_B    =    "EMonth_B"       #出生月(農曆)
  ELeapMonth_B    =    "ELeapMonth_B"       #䦌月依䦌月排法調整之月份，影響右鳀，左輔之值
  EDate_B    =     "EDate_B"        #出生日(農曆)
  ETime_B     =    "ETime_B"        #出生時(地支時，十二個時辰)
  EMinute_B     =    "EMinute_B"        #出生分
  LeapMonth_B =    "LeapMonth_B"    #出生月是否為潤月
  Multiple_births = "Multiple_births"  #多胞胎，兩碼 第一碼：S 表示同卵 D表示異卵  第二碼 數字，表示第幾胎
                                      # S1 表示同卵第一胎，或是單卵單胎（一般人的狀況）
                                      # D2 表示異卵的第二胎

  SkyEarth = "SkyEarth"

  PAR_TRUE = 1
  PAR_FALSE = 0

  # 堂號
  HALL_NUMBER1 = "hall_number1"  # 中宮堂號一
  HALL_NUMBER2 = "hall_number2"  # 中宮堂號二
  HALL_NUMBER3 = "hall_number3"  # 中宮堂號三

  # 堂號介紹
  ADV_HALL_NUMBER_1 = "s_adv_hall_number1"
  ADV_HALL_NUMBER_2 = "s_adv_hall_number2"
  ADV_HALL_NUMBER_3 = "s_adv_hall_number3"

  # 列印參數
  PRINT_TP_DEFAULT =  Cfate::PAR_FALSE   # 列印時間頁
  PRINT_TP_INONE      =    0  #同一頁
  PRINT_TP_THREE      =    1  #分三頁
  PRINT_TP_THREE_1     =    1  #第一頁時間：１～２７歲
  PRINT_TP_THREE_2     =    2  #第二頁時間：２８～５４歲
  PRINT_TP_THREE_3     =    3  #第三頁時間：５５～８１歲

  PAR_PRINT_TIME_PAGE = "par_print_time_page"
  PAR_PRINT_TIME_PAGE_TYPE = "par_print_time_page_type"
  PAR_PRINT_TIME_PAGE_INONE = "par_print_time_page_inone"
  PAR_PRINT_TIME_PAGE_THREE = "par_print_time_page_three"
  PAR_PRINT_TIME_PAGE_THREE_PRE = "par_print_time_page_three_"
  PAR_PRINT_TIME_PAGE_THREE_1 = "par_print_time_page_three_1"
  PAR_PRINT_TIME_PAGE_THREE_2 = "par_print_time_page_three_2"
  PAR_PRINT_TIME_PAGE_THREE_3 = "par_print_time_page_three_3"
  PAR_PRINT_PAN_HEADER = "par_print_pan_header"  # 排盤表頭
  PAR_PRINT_SET_FOOTER = "par_print_set_footer"  # 時間頁頁尾說明

  def Cfate.pan_par_dbfield2hash(sdbColumn)
    if (sdbColumn == nil) then
      return Hash.new
    end
    a = Cfate.pan_par_split(sdbColumn)
    h = Hash[*a]
  end

  def Cfate.pan_par_hash2db(h)
    a = h.to_a.flatten
    return pan_par_join(a)
  end

  def Cfate.pan_par_split(s)
    if (s == nil) then
      return nil
    end
    a = s.split(/\s*,\s*/)
    a.map! {|item| Cfate.showComma(item)}
    return a
  end

  def Cfate.pan_par_join(a)
    a.map! {|item| Cfate.hideComma(item)}
    s = a.join(",")
    return s
  end

  def Cfate.hideComma(sIn)
    if (sIn == nil) then
      return sIn
    end
    if (sIn.class == Array) then
      return sIn.join("[}")
    elsif (sIn.class == String) then
      return sIn.gsub(/,/,"___")
    else
      return sIn
    end
  end
  def Cfate.showComma(sIn)
    if (sIn == nil) then
      return sIn
    end
    if (sIn.index("[}") != nil) then
      return sIn.split("[}")
    elsif (sIn.index("___") != nil) then
      return sIn.gsub(/___/,",");
    else
      return sIn
    end
  end

  def Cfate.GetPanName(nPanType)
    Pm.GetStr("IDS_S_PAN_NAME#{nPanType}")
  end
  def Cfate.GetPanFullName(nPanType)
    sName = Pm.GetStr("IDS_S_PAN_NAME#{nPanType}") + Pm.GetStr("IDS_S_PAN_PAN")
  end

  def Cfate.GetPanFullNameWithColon(nPanType)
    sName = Pm.GetStr("IDS_S_PAN_NAME#{nPanType}") + Pm.GetStrWithColon("IDS_S_PAN_PAN")
  end


    PAR_PAN_YEAR_DISPLAY = "n_par_pan_year_display"
    LAST_XDATE_PAN_TYPE = "n_LastXdatePanType"
    PAR_FIRST_TIME_TYPE = "n_par_first_time_type "
    PAR_FIRST_SEGMENT = "n_par_first_segment"

  def Cfate.ParInHashValueCheck(hInput,key,hDefault)
    defaultVal = hDefault[key]

    if (hInput == nil) then
      value = nil
    else
      value = hInput[key]
    end
    return Cfate.ParValueCheck(key,value,defaultVal)
  end

  def Cfate.ParValueCheck(key,value,defaultVal)
    parType = Cfate.parType(key)
    if (parType == Array) then
      return Cfate.ArrayCheck(value,defaultVal)
    elsif (parType == String) then
      return Cfate.StringCheck(value,defaultVal)
    elsif (parType == Integer) then
      return Cfate.IntCheck(value,defaultVal)
    elsif (parType == TrueClass) then
      return Cfate.BoolCheck(value,defaultVal)
    else
      return Cfate.StringCheck(value,defaultVal)
    end
  end

  def Cfate.parType(parName)
    sType = parName[0,2]
    if (sType == "a_") then
      return Array
    elsif (sType == "s_") then
      return String
    elsif (sType == "n_") then
      return Integer
    elsif (sType == "b_") then
      return TrueClass
    else
      return TrueClass  # 沒有前置定義預設為 true false
    end
  end

  def Cfate.ValueBoolCheck(value)
    if (value == nil) then
      return false
    end
    if (value == true || value == "true" || value == "t" || value == "1" || value == 1) then
      return true
    elsif (value == "TRUE" || value == "T" || value == "True") then
      return true
    elsif (value == false || value == "false" || value == "f" || value == "0" || value == 0) then
      return false
    elsif (value == "FALSE" || value == "F" || value == "False") then
      return false
    else
      return (value.to_i == 1)
    end
  end

  def Cfate.BoolCheck(value,defaultVal)
    if (value == nil) then
      return Cfate.ValueBoolCheck(defaultVal)
    else
      return Cfate.ValueBoolCheck(value)
    end
  end

  def Cfate.Int2Bool(nIn)
    if (nIn == nil) then
      return false
    elsif (nIn == 0) then
      return false
    else
      return true
    end
  end

  def Cfate.Bool2Int(bInS)
    bIn = Cfate.ValueBoolCheck(bInS)
    if (bIn == true) then
      return 1
    elsif (bIn == false) then
      return 0
    else
      return nil
    end
  end

  def Cfate.StringCheck(value,defaultVal)
    if (value == nil) then
      return defaultVal
    else
      return value
    end
  end

  def Cfate.IntCheck(value,defaultVal)
    if (value == nil) then
      return defaultVal.to_i
    else
      return value.to_i
    end
  end

  def Cfate.ArrayCheck(value,defaultVal)
    if (defaultVal == nil) then
      defaultVal = Array.new
    end
    if (value == nil) then
      return defaultVal
    else
      if (value.class == String) then
        return value.split("[}")
      else
        return value
      end
    end
  end

  def Cfate.HashOrDefault(hInput,sKey,nDefault)
    if (hInput == nil) then
      return nDefault.to_i
    else
      sHashData = hInput[sKey]
      if (sHashData == nil) then
        return nDefault.to_i
      end
      return sHashData.to_i
    end
  end
  def Cfate.HashOrDefault2(hInput,sKey,nDefault)
    if (hInput == nil) then
      return nDefault.to_i
    else
      sHashData = hInput[sKey]
      if (sHashData == nil) then
        return 0
      end
      return sHashData.to_i
    end
  end

  def Cfate.HashBoolCheck(hInput,sKey,nDefault)
    if (hInput == nil) then
      return Cfate.ValueBoolCheck(nDefault)
    else
      sHashData = hInput[sKey]
      if (sHashData == nil) then
        return Cfate.ValueBoolCheck(nDefault)
      else
        return Cfate.ValueBoolCheck(sHashData)
      end
    end
  end
  def Cfate.HashBoolCheck2(hInput,sKey,nDefault)
    if (hInput == nil) then
      return Cfate.ValueBoolCheck(nDefault)
    else
      sHashData = hInput[sKey]
      if (sHashData == nil) then
        return Cfate.ValueBoolCheck(0)
      else
        return Cfate.ValueBoolCheck(sHashData)
      end
    end
  end

  def Cfate.Hash2Str(hInput,sKey,nDefault)
    if (hInput == nil) then
      return nDefault
    else
      sHashData = hInput[sKey]
      if (sHashData == nil) then
        return nDefault
      end
      return sHashData.gsub(/[,]/,' ')
    end
  end


  FUNC_SET_PARS  = "b_SetPars"
  FUNC_HALL_NUMBER = "b_HallNumber"

  def Cfate.GetSpace(nCnt)
    sBuf = ""
    nCnt.times do |i|
      sBuf += Pm.GetStr("IDS_A_KEY_SPACE")
    end
    return sBuf
  end

  def Cfate.Sex(sex)
    if !(sex == 0 || sex == 1) then
      sex = Cfate.Bool2Int(sex)
    end
    return Pm.GetStr("IDS_S_CUST_MF_#{sex}")
  end

  # 最大公因數
  def Cfate.gong_yin_su(num1,num2)
    return num1.gcd(num2)
    # gcd = 1
    # (1..[num1,num2].min).each do |i|
    #   if (num1 % i == 0) then
    #     if (num2 % i == 0) then
    #       gcd=i
    #     end
    #   end
    # end
    # return gcd
  end
  def Cfate.gong_bei_su(num1,num2)
    return num1.lcm(num2)
    # return num1 * num2 / Cfate.gong_yin_su(num1,num2)
  end
  def Cfate.gong_bei_sus(nums)
    num2 = 1
    nums.each do |num|
      num2 = Cfate.gong_bei_su(num,num2)
    end
    return num2
  end
  def Cfate.arr_gong_bei_sus(arrs)
    arr2 = Cfate.zips(arrs)
    arr3 = []
    arr2.each do |arr|
      arr3.push(Cfate.gong_bei_sus(arr))
    end
    return arr3
  end
  def Cfate.zip(arrs1,arrs2)
    if (arrs1 == []) then
      return arrs2
    end
    if (arrs2 == []) then
      return arrs1
    end
    arrs = Array.new
    arrs1.each do |arr1|
      arrs2.each do |arr2|
        arrs.push([arr1,arr2].flatten)
      end
    end
    return arrs
  end
  def Cfate.zips(arrs)
    arr2 = []
    arrs.each do |arr|
      arr2 = Cfate.zip(arr,arr2)
    end
    return arr2
  end
  def Cfate.array_and(arrs)
    if (arrs == []) then
      return arrs
    end
    arr2 = []
    arrs.each do |arr|
      if (arr != []) then
        if (arr2 == []) then
          arr2 = arr
        else
          arr2 = arr2 & arr
        end
      end
    end
    return arr2
  end
  def Cfate.array_or(arrs)
    if (arrs == []) then
      return arrs
    end
    arr2 = arrs[0]
    arrs.each do |arr|
      arr2 = arr2 | arr
    end
    return arr2
  end

  def Cfate.canDemo?(user_id,ap_name,oUserApNow)
    oUserAp = UserAp.check_userid(user_id).check_demo().check_product_name(ap_name).last
    if (oUserAp == nil) then
      return !oUserApNow.main
    else
      return false
    end
  end
end
