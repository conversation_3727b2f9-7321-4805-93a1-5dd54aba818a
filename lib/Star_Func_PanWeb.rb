
# Star for PanWeb
class Star
  def gPI_GetName()
    return uig_Name()
  end

  # 命造(陰陽男女)
  def gPI_GetIYMF()
    sBuf = Pm.GetStr("IDS_S_UI_IY_#{uig_nSIY()}")
    sBuf += Pm.GetStr("IDS_S_UI_MF_#{uig_bS_Val()}")
    return sBuf
  end

  # 生肖
  def gPI_GetAnimal()
    nEarth = Earth.EarthIndex2Earth(cp_getEarthIndex())
    sBuf = Xdate.GetAnimal(nEarth)
    return sBuf
  end

  # 陽曆
  def gPI_GetWBirthDateStr()
    return uig_W_BirthDate_Str()
  end

  def gPI_GetWBirthDateStr2()
    return uig_W_BirthDate_Str2()
  end

  # 農民曆
  def gPI_GetEBirthDateStr()
    return uig_E_BirthDate_Str()
  end

  def gPI_GetEBirthDateStr2()
    return uig_E_BirthDate_Str2()
  end

  # 農曆干支曆
  def gPI_GetGanZhiBirthDateStr()
    return uig_GanZhi_Birth_Str()
  end
  # 節氣干支曆
  def gPI_GetSegmentGanZhiBirthDateStr(bSegment=false,nFirstTimeType=Xdate::FIRST_NEXT,nFirstSegment=Xdate::SEGMENT_SPRING,bSpace=true,nSegmentMove=Xdate::SegmentNow)
    return uig_Seg8Words_Str(bSegment,nFirstTimeType,nFirstSegment,bSpace,nSegmentMove)
  end

  # 五行局或命局
  def gPI_GetFiveType()
    nVal = cp_getFive() - 1
    return Pm.GetStr("IDS_S_FIVE_#{nVal}")
  end

  # 命宮
  def gPI_GetLifeHouse()
    nVal = cp_getLife()
    nVal = Star.HouseIndex2Earth(nVal)
    return Pm.GetStr("IDS_S_EARTH_#{nVal}")
  end

  # 身宮
  def gPI_GetBodyHouse()
    nVal = cp_getBody()
    nVal = Star.HouseIndex2Earth(nVal)
    return Pm.GetStr("IDS_S_EARTH_#{nVal}")
  end
  def gPI_GetBodyHouseEarth()
    nVal = cp_getBody()
    nVal = Star.HouseIndex2Earth(nVal)
    return nVal
  end

  # 命主
  def gPI_GetLife()
    nVal = cp_getLife()
    nVal = @@LifeStar[nVal]
    nVal = Earth.EarthIndex2Earth(nVal)
    return Pm.GetStr("IDS_S_LIFE_STAR_#{nVal}")
  end

  # 身主
  def gPI_GetBody()
    nVal = cp_getEarthIndex() % 6
    nVal = Earth.EarthIndex2Earth(nVal)
    return Pm.GetStr("IDS_S_BODY_STAR_#{nVal}")
  end

  # 子年斗君
  def gPI_GetGod()
    nVal = cp_getGod()
    nVal = Earth.EarthIndex2Earth(nVal)
    return Pm.GetStr("IDS_S_EARTH_#{nVal}")
  end

  def gPanHouse_GetDoctorName(nPanType,nEarth)
    @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::DOCTOR][0][0]
  end

  def gPanHouse_GetGodName(nPanType,nEarth)
    sOut = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::GOD][0][0]
    sOut = Modify2TwoWords(sOut)
  end

  def gPanHouse_GetYearGodName(nPanType,nEarth)
    sOut = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARGOD][0][0]
    sOut = Modify2TwoWords(sOut)
  end

  def gPanHouse_GetYearStarName(nPanType,nEarth)
    sOut = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::YEARSTAR][0][0]
    sOut = Modify2TwoWords(sOut)
  end

  def gPanHouse_GetAStarName(nPanType,nEarth,nMax)
    sStar = Array.new(2,"")
    starArray = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::A_STAR]

    sOne = ""
    sTwo = ""
    starArray.each do |oStar|
      sTmp = Modify2TwoWords(oStar[0])
      sOne += sTmp[0]
      sTwo += sTmp[1]
    end
    sOne += Cfate.GetSpace(nMax - starArray.length)
    sTwo += Cfate.GetSpace(nMax - starArray.length)
    sStar[0] = sOne.reverse
    sStar[1] = sTwo.reverse
    return sStar
  end

  def gHouse_GetAStarFourHua_Each(nPanType,nEarth,nMax,nDisPanType)
    starArray = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::A_STAR]

    sOut = ""
    aValue = Array.new(nMax,nil)
    i = nMax - 1
    starArray.each do |oStar|
      sOut += getFourHuaStr(getFourHuaHashValue(oStar[4],nDisPanType))
      aValue[i] = getFourHuaHashValue(oStar[4],nDisPanType)
      i -= 1
    end
    sOut += Cfate.GetSpace(nMax - starArray.length)

    sBuf = sOut.reverse

    return sBuf,aValue,@par_FlowYearHua
  end

  def gPanHouse_GetAStarFourHua(nPanType,nEarth,nMax)
    aBuf = Array.new
    aaValue = Array.new
    if (@par_FlowYearHua == Star::PAN_YEAR_HUA_STACK) then
      sBuf,aValue,nStyle = gHouse_GetAStarFourHua_Each(nPanType,nEarth,nMax,Cfate::PAN_NORMAL)
      aBuf.push(sBuf)
      aaValue.push(aValue)
      if (nPanType >= Cfate::PAN_TENYEAR) then
        sBuf,aValue,nStyle = gHouse_GetAStarFourHua_Each(nPanType,nEarth,nMax,Cfate::PAN_TENYEAR)
        aBuf.push(sBuf)
        aaValue.push(aValue)
      end
      if (nPanType >= Cfate::PAN_FLOWYEAR) then
        sBuf,aValue,nStyle = gHouse_GetAStarFourHua_Each(nPanType,nEarth,nMax,Cfate::PAN_FLOWYEAR)
        aBuf.push(sBuf)
        aaValue.push(aValue)
      end
    else
      sBuf,aValue,nStyle = gHouse_GetAStarFourHua_Each(nPanType,nEarth,nMax,nPanType)
      aBuf.push(sBuf)
      aaValue.push(aValue)
    end
    return aBuf,aaValue,nStyle
  end

  def gPanHouse_GetMioWong(nPanType,nEarth,nMax)
    starArray = @AllPanInfo[nPanType][Earth.GetKeyName(nEarth)][Star::A_STAR]

    sOut = ""
    aMioWong = Array.new(nMax,nil)
    i = nMax - 1
    starArray.each do |oStar|
      sOut += getMioWongStr(oStar[3])
      aMioWong[i] = oStar[3]
      i -= 1
    end
    sOut += Cfate.GetSpace(nMax - starArray.length)

    sBuf = sOut.reverse

    return sBuf,aMioWong,@int_MioWongStyle
  end

  def gPanHouse_GetBStarName(nPanType,nEarth,nMax)
    asStar = Array.new(2,"")
    starArray = get_HouseBStarNames(nPanType,nEarth)
    sOne = ""
    sTwo = ""
    starArray.each do |sStar|
      sTmp = PanWeb.Modify2TwoWords(sStar)
      sOne += sTmp[0]
      sTwo += sTmp[1]
    end
    sOne += Cfate.GetSpace(nMax - starArray.length)
    sTwo += Cfate.GetSpace(nMax - starArray.length)
    asStar[0] = sOne.reverse
    asStar[1] = sTwo.reverse
    return asStar
  end

  def gPanHouse_GetLarge(nEarth)
    nStarYear = cp_getHouseLarge(nEarth)
    sOut = "#{nStarYear} --- #{nStarYear+9}"
    return sOut
  end

  def gPanHouse_GetSmall(nEarth)
    nStarYear = cp_getHouseSmall(nEarth)
    sOut = nStarYear.to_s
    7.times do |i|
      nStarYear += 12
      sOut += " "
      sOut += nStarYear.to_s
    end

    return sOut
  end

  def gPanHouse_FlowYear_SmallSan(nEarth,nPanType=nil)
    nFlowAge = fdg_EY(nPanType) - uig_EY() + 1
    nAge = nFlowAge % 12
    if (nAge == 0) then
      nAge = 12
    end
    sBuf = ""
    nHouseIndex = Star.Earth2HouseIndex(nEarth)
    if (nAge == cp_GetHouseSmallSan(nHouseIndex)) then
      sBuf = Pm.GetStr("IDS_S_SMALL_SAN") + uig_FlowAge(nPanType).to_s
    end
    return sBuf
  end

  def gPanHouse_FiveStr(nPanType,nEarth)
    nFive = cp_getHouseFive(nPanType,nEarth)
    return Five.GetFiveStr(nFive)
  end

  def gPanHouse_FlowYear(nEarth,nPanType=nil)
    nHouseIndex = Star.Earth2HouseIndex(nEarth)
    nFlowAge = uig_FlowAge(nPanType)
    # nFlowYearHouseHead = cp_GetFlowYearHouseHead() - 2
    nFlowYearHouse = cp_GetFlowYearHouse(nPanType) - 2

    nHouseAge = (nFlowAge + Earth.Modify(nHouseIndex - nFlowYearHouse) - 2)
    if (nHouseAge <= 0) then
      nHouseAge += 12
    end
    sBuf = ""
    bFlowHouse = false
    if (nHouseAge == nFlowAge) then
      bFlowHouse = true
      sBuf += Pm.GetStr("IDS_S_FLOW_YEAR")
    end
    sBuf += nHouseAge.to_s + Pm.GetStr("IDS_S_YEAR_OLD")
    nHouseYear = nHouseAge + uig_EY() - 1
    return bFlowHouse,sBuf,nHouseYear
  end

  def gPanHouse_FlowMonth(nEarth)
    sBuf = ""
    bFlowHouse = false
    nFirstHosueEarth = gHouse_GetFirstHouseEarth(Cfate::PAN_FLOWMONTH)
    nMonth = Earth.ModifyEarth(nEarth - nFirstHosueEarth + fdg_EM(Cfate::PAN_FLOWMONTH))
    if (nFirstHosueEarth == nEarth) then
      bFlowHouse = true
      sBuf += Pm.GetStr("IDS_S_FLOW_MONTH")
    end

    sBuf += nMonth.to_s + Pm.GetStr("IDS_X_MONTH")
    return bFlowHouse,sBuf,nMonth
  end

  def gPanHouse_FlowMonth_old(nEarth)
    nHouse = Star.Earth2House(nEarth)
    sBuf = ""
    bFlowHouse = false
    if (fdg_EM(Cfate::PAN_FLOWMONTH) == nHouse) then
      bFlowHouse = true
      sBuf += Pm.GetStr("IDS_S_FLOW_MONTH")
    end

    sBuf += nHouse.to_s + Pm.GetStr("IDS_X_MONTH")
    return bFlowHouse,sBuf,nHouse
  end


  def gPanHouse_FlowDate(nEarth)
    nDays = Xdate.GetEMonthDays(fdg_EY(Cfate::PAN_FLOWDATE), fdg_EM(Cfate::PAN_FLOWDATE), fdg_EL(Cfate::PAN_FLOWDATE))
    nHouseIndex = Star.Earth2HouseIndex(nEarth)
    m = nHouseIndex - get_FlowHouseHeadIndex(Cfate::PAN_FLOWDATE)
    if (m < 0) then
       m += 12
    end

    aInfo = Array.new(3,0)
    aInfo[1] = m + 1
    if (aInfo[1] > 12) then
      aInfo[0] = aInfo[1] - 12
      aInfo[2] = aInfo[1] + 12
    else
      aInfo[0] = aInfo[1]
      aInfo[1] = aInfo[0] + 12
      aInfo[2] = aInfo[1] + 12
    end
    if (aInfo[2] > nDays) then
      aInfo[2] = nil
    end

    flowDate = fdg_ED(Cfate::PAN_FLOWDATE)
    bFlowHouse = ((flowDate - aInfo[0]).abs % 12) == 0 ? true : false

    return bFlowHouse,flowDate,aInfo
  end

  def gPanHouse_FlowTime(nEarth)
    nHouseIndex = Star.Earth2HouseIndex(nEarth)
    nHouseTimeIndex = Earth.Modify(nHouseIndex - get_FlowHouseHeadIndex(Cfate::PAN_FLOWTIME))

    flowTime = fdg_WT(Cfate::PAN_FLOWTIME)
    bFlowHouse = (flowTime == nHouseTimeIndex) ? true : false

    sBuf = ""
    if (bFlowHouse) then
      sBuf += Pm.GetStr("IDS_S_FLOW_TIME")
    end

    nHouseTimeEarth = Earth.EarthIndex2Earth(nHouseTimeIndex)
    sBuf += Earth.GetName(nHouseTimeEarth) + Pm.GetStr("IDS_X_TIME")

    return bFlowHouse,sBuf,nHouseTimeIndex
  end

  def gPanHouse_FlowMin(nEarth)
    bFlowHouse,sBuf,nHead = gPanHouse_FlowMin2(nEarth,true)
    return bFlowHouse,sBuf
  end

  def gPanHouse_FlowMin2(nEarth,bWithFlowHouse)
    nHour = fdg_ET(Cfate::PAN_FLOWMIN)
    nHour = nHour << 1
    nHour = nHour - 1
    if (nHour < 0) then
      nHour += 24
    end
    nHouseIndex = Star.Earth2HouseIndex(nEarth)
    nHead = Earth.Modify(nHouseIndex - get_FlowHouseHeadIndex(Cfate::PAN_FLOWMIN))

    m = nHead
    m = m * 10
    k = nHour
    if (m >= 60) then
      k += 1
      if (k >= 24) then
        k -= 24
      end
      m -= 60
    end
    m1 = m + 10
    k1 = k
    if (m1 >= 60) then
      k1 += 1
      m1 -= 60
    end
    sBuf = ""
    bFlowHouse = (fdg_EMI(Cfate::PAN_FLOWMIN) == nHead) ? true : false
    if (bWithFlowHouse && bFlowHouse) then
      sBuf += Pm.GetStr("IDS_S_FLOW_MIN")
    end
    sk = "%02d" % k
    sm = "%02d" % m
    sk1 = "%02d" % k1
    sm1 = "%02d" % m1
    sBuf += "#{sk}:#{sm} ~ #{sk1}:#{sm1}"
    return bFlowHouse,sBuf,nHead
  end

  def gPanHouse_GetBodyHouse()
    return Star.HouseIndex2Earth(cp_getBody())
  end

  # 姓名
  def gPan_PI_Name()
    sCaption = Pm.GetStrWithColon("IDS_S_UI_NAME")
    sData = gPI_GetName()
    if (sData == nil) then
      sData = ""
    end
    return sCaption,sData
  end

  # 虛歲
  def gPan_PI_LunarYearOld()
    sCaption = Pm.GetStr("IDS_X_NOW") + Pm.GetStrWithColon("IDS_S_YEAR_OLD_LUNAR")
    #sData = Xdate.GetYearOldStr(gNormal_GetYearOld(uig_EY(),fdg_EY()))
    sData = gNormal_GetYearOld(uig_EY(),cdg_EY()).to_s
    return sCaption,sData
  end

  # 命造(陰男,陽男,陰女,陽女)
  def gPan_PI_IYMF()
    sCaption = Pm.GetStrWithColon("IDS_S_UI_IYMF")
    sData = gPI_GetIYMF()
    return sCaption,sData
  end

  #　生肖:兔<BR>
  def gPan_PI_Animal()
    sCaption = Pm.GetStrWithColon("IDS_S_UI_ANIMAL")
    sData = gPI_GetAnimal()
    return sCaption,sData
  end

  #　　陽曆　1963年　2月　23日　4時<BR>
  def gPan_PI_SolarBirth()
    sCaption = Pm.GetStr("IDS_S_UI_SOLAR_CAL")
    sData = gPI_GetWBirthDateStr()
    return sCaption,sData
  end

  def gPan_PI_SolarBirth2()
    sCaption = Pm.GetStr("IDS_S_UI_SOLAR_CAL")
    sData = gPI_GetWBirthDateStr2()
    return sCaption,sData
  end

  #     農曆　1963年　1月　30日　寅時<BR>
  def gPan_PI_LunarBirth()
    sCaption = Pm.GetStr("IDS_S_UI_LUNAR_CAL")
    sData = gPI_GetEBirthDateStr()
    return sCaption,sData
  end

  def gPan_PI_LunarBirth2()
    sCaption = Pm.GetStr("IDS_S_UI_LUNAR_CAL")
    sData = gPI_GetEBirthDateStr2()
    if (par_check_LeapType()) then
      sData += Pm.t("IDS_NEXT_MONTH")
    end
    return sCaption,sData
  end

  #   農曆四柱  　癸卯年 甲寅月 丁酉日 壬寅時<BR>
  def gPan_PI_GanZhiBirth()
    # sCaption = ""
    sCaption = Pm.GetStr("IDS_S_UI_LUNAR_FOUR")
    sData = gPI_GetGanZhiBirthDateStr()
    return sCaption,sData
  end
  #   節氣四柱  　癸卯年 甲寅月 丁酉日 壬寅時<BR>
  def gPan_PI_SegmentGanZhiBirth(bSegment=false,nFirstTimeType=Xdate::FIRST_NEXT,nFirstSegment=Xdate::SEGMENT_SPRING,bSpace=true,nSegmentMove=Xdate::SegmentNow)
    # sCaption = ""
    sCaption = Pm.GetStr("IDS_S_UI_SEGMENT_FOUR")
    sData = gPI_GetSegmentGanZhiBirthDateStr(bSegment,nFirstTimeType,nFirstSegment,bSpace,nSegmentMove)
    return sCaption,sData
  end

  #     　　命局: 金四局<BR>
  def gPan_PI_FiveType()
    sCaption = Pm.GetStrWithColon("IDS_S_FIVE_TYPE")
    sData = gPI_GetFiveType()
    return sCaption,sData
  end

  #     　　　命宮: 子<BR>
  def gPan_PI_LifeHouse()
    sCaption = Pm.GetStrWithColon("IDS_S_LIFE_HOUSE")
    sData = gPI_GetLifeHouse()
    return sCaption,sData
  end

  #     　身宮: 辰<BR>
  def gPan_PI_BodyHouse()
    sCaption = Pm.GetStrWithColon("IDS_S_BODY_HOUSE")
    sData = gPI_GetBodyHouse()
    return sCaption,sData
  end

  #   　命主: 貪狼<BR>
  def gPan_PI_Life()
    sCaption = Pm.GetStrWithColon("IDS_S_LIFE")
    sData = gPI_GetLife()
    return sCaption,sData
  end

  #   　身主: 天同<BR>
  def gPan_PI_Body()
    sCaption = Pm.GetStrWithColon("IDS_S_BODY")
    sData = gPI_GetBody()
    return sCaption,sData
  end

  #   　子年斗君: 寅
  def gPan_PI_God()
    sCaption = Pm.GetStrWithColon("IDS_S_GOD")
    sData = gPI_GetGod()
    return sCaption,sData
  end

  def gPan_CI_Company()
    sData = Pm.GetStr("IDS_S_COMPANY_NAME")
    sData2 = Pm.GetStr("IDS_S_PROFATE")
    return sData,sData2
  end

  def gPan_CI_Web()
    return ""
  end

  def Star.gPan_CI_Data1(hApFunc=nil,hParAll=nil)
    return Star.gGetCIData(hApFunc,hParAll,Cfate::HALL_NUMBER1,Pm.GetStr("IDS_S_COMPANY_NAME"))
  end

  def Star.gPan_CI_Data2(hApFunc=nil,hParAll=nil)
    return Star.gGetCIData(hApFunc,hParAll,Cfate::HALL_NUMBER2,Pm.GetStr("IDS_S_PROFATE"))
  end

  def Star.gPan_CI_Data3(hApFunc=nil,hParAll=nil)
    return Star.gGetCIData(hApFunc,hParAll,Cfate::HALL_NUMBER3,Pm.GetStr("IDS_S_CONTACT_INFO"))
    # return Star.gGetCIData(hApFunc,hParAll,Cfate::HALL_NUMBER3,Pm.getSystemValue("MEEN_URL"))
  end

  def Star.gGetCIData(hApFunc,hParAll,sParAllKey,defValue)
    if (hApFunc == nil) then
      return defValue
    end
    if (hParAll == nil) then
      return defValue
    end
    if (hParAll["full_show"]) then
      return defValue
    end
    if (hApFunc[Cfate::FUNC_HALL_NUMBER] == true) then
      if (hParAll[sParAllKey] == nil) then
        return ""
      else
        return hParAll[sParAllKey]
      end
    else
      return defValue
    end
  end
end
