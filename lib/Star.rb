# coding: UTF-8

require("Cfate.rb")
require("Xdate.rb")
require("Pm.rb")
require("Star_Constant.rb")
require("Star_ClassVariable.rb")
require("Star_InstanceVariable.rb")
require("Star_Func_Pan.rb")
require("Star_Func_Explain.rb")
require("Star_Func_Score.rb")
require("Star_Func_PanWeb.rb")
require("Star_Func_PanXml.rb")
require("Star_Func_GiSuong.rb")
require("Star_ParFunction.rb")
require("SkyEarthFive_Function.rb")
require("Constant.rb")
require("Star_WeenApi.rb")
require("Star_lucun.rb")
require("Star_zeri.rb")

class Star
  #---------------------------------------------------------------------
  #constructor initialize
  def initialize(hUserInfo=nil,hUserDefData=nil,hUserType=nil)
    initAll(Cfate::PAN_NONE,hUserInfo,hUserDefData,hUserType)
  end

  def g_GetPanInfo(nPanType,hUserInfo,hUserDefData,*args)
    # if ((@m_UserInfo != hUserInfo) || (@m_nUserDefinedDate != hUserDefData) || !LargeSanExist?()) then
    #   CreateAllPan(nPanType,hUserInfo,hUserDefData,args[0])
    # end
    initAll(Cfate::PAN_NONE,hUserInfo,hUserDefData,args[0])

    return @AllPanInfo[nPanType]
  end
  def g_GetAllPanInfo(nPanType,hUserInfo,hUserDefData,*args)
    g_GetPanInfo(nPanType,hUserInfo,hUserDefData,*args)
    (1..12).each do |nEarth|
      starInfos = get_HouseFullAStarInfos(nPanType,nEarth)
      starInfos = get_HouseFullBStarInfos(nPanType,nEarth)
      starInfos = get_HouseFullFStarInfo_Key(nPanType,nEarth,Star::F_STAR)
      starInfos = get_HouseFullSStarInfos(nPanType,nEarth)
    end
  end

  def g_GetStarPanInfo(nPanType,hUserInfo,hUserDefData,*args)
    # if ((@m_UserInfo != hUserInfo) || (@m_nUserDefinedDate != hUserDefData) || !LargeSanExist?()) then
    #   CreateAllPan(nPanType,hUserInfo,hUserDefData,args[0])
    # end
    initAll(Cfate::PAN_NONE,hUserInfo,hUserDefData,args[0])

    return @StarInfo
  end

#   def CreateAllPan(nPanType,hUserInfo,*args)
#     #初始化物件變數
#     initAll(nPanType,args[1])

#     #設定客戶資訊
#     setUserInfo(hUserInfo)

#     #取得目前系統時間
#     GetCurTime(hUserInfo["remote_ip"])

#     #取得中宮資訊
#     cp_UserInfo()

#     if (args[0] != nil) then
#       @m_nUserDefinedDate = args[0].clone
# #puts("@m_nUserDefinedDate:#{@m_nUserDefinedDate}")
#       CheckUserAskDate()
#     end

#     createPan(nPanType,hUserInfo)
#     # (Cfate::PAN_NORMAL..Cfate::PAN_FLOWMIN).each do |panType|
#     # [Cfate::PAN_NORMAL,@m_nLastPanType].each do |panType|
#     #   createPan(panType,hUserInfo)
#     # end
#   end

  #---------------------------------------------------------------------
  def initAll(nPanType,hUserInfo,hUserDefData,hUserType=nil)
    a = check_userdefdata(nPanType,hUserInfo,hUserDefData,@orgUserInfo == hUserInfo)
    initUserType(hUserType)
    initPanInfo(nPanType,a)
    initUserInfo(hUserInfo) # after initPanInfo
    #取得目前系統時間
    if (hUserInfo != nil) then
      GetCurTime(hUserInfo["remote_ip"])
    end
    initUserDefData(hUserDefData,a)
  end
  def check_userdefdata(nPanType,hUserInfo,hUserDefData,same_user)
    a = Array.new(Cfate::PAN_CNT,false)
    if (hUserDefData == nil) then return a end
    if (@m_nUserDefinedDate == nil) then return a end
    a[Cfate::PAN_NORMAL] = same_user
    a[Cfate::PAN_TENYEAR] = Cfate::PAN_TENYEAR > nPanType || (a[Cfate::PAN_NORMAL] && @m_nUserDefinedDate[Cfate::EYear] == hUserDefData[Cfate::EYear])
    a[Cfate::PAN_FLOWYEAR] = Cfate::PAN_FLOWYEAR > nPanType || (a[Cfate::PAN_TENYEAR])
    a[Cfate::PAN_FLOWMONTH] = Cfate::PAN_FLOWMONTH > nPanType || (a[Cfate::PAN_FLOWYEAR] && @m_nUserDefinedDate[Cfate::EMonth] == hUserDefData[Cfate::EMonth] && @m_nUserDefinedDate[Cfate::ELeapMonth] == hUserDefData[Cfate::ELeapMonth])
    a[Cfate::PAN_FLOWDATE] = Cfate::PAN_FLOWDATE > nPanType || (a[Cfate::PAN_FLOWMONTH] && @m_nUserDefinedDate[Cfate::EDate] == hUserDefData[Cfate::EDate])
    a[Cfate::PAN_FLOWTIME] = Cfate::PAN_FLOWTIME > nPanType || (a[Cfate::PAN_FLOWDATE] && @m_nUserDefinedDate[Cfate::WHour] == hUserDefData[Cfate::WHour])
    a[Cfate::PAN_FLOWMIN] = Cfate::PAN_FLOWMIN > nPanType || (a[Cfate::PAN_FLOWTIME] && @m_nUserDefinedDate[Cfate::WMinute] == hUserDefData[Cfate::WMinute])
    return a
  end
  def get_ParAll()
    return @UserType.clone
  end
  def initUserType(hUserType)
    if (hUserType == nil) then
      hUserType = Hash.new
    end
    if (@UserType != hUserType) then
      pan_par_hash2var_all(hUserType) # 設定所有參數值
      @UserType = hUserType.clone
    end
  end
  def initPanInfo(nPanType,a)
    b = Array.new(Cfate::PAN_CNT){ |index| index }
    if (nPanType == nil || !b.include?(nPanType)) then
      initAllPanInfo()
    else
      initPanInfos(a)
    end
  end
  def initAllPanInfo()
    @m_nPanType = Cfate::PAN_NONE  #排盤類別

    @m_nUserDefinedDate = Hash.new
    @m_nFlowDate = [Hash.new,Hash.new,Hash.new,Hash.new,Hash.new,Hash.new,Hash.new]
    @m_nCurDate = Hash.new
    @m_nPanDate = Hash.new

    @AllPanInfo = Array.new(Cfate::PAN_CNT)
    @OriginalFourHua = Array.new(Cfate::PAN_CNT)
    @SelfFourHua = Array.new(Cfate::PAN_CNT)

    a = Array.new(Cfate::PAN_CNT,false)
    initPanInfos(a)

    @StarInfo = Hash.new   # 包含 以下及@AllPanInfo
    @StarInfo[U_LIFE] = -1  #h[Star::U_LIFE]    @m_nLife    #本命命宮所在地支位置(以地支表示)
    @StarInfo[U_GOD] = -1  #h[Star::U_GOD]    @m_nGod     #本命子年斗君所在地支位置(以地支表示)
    @StarInfo[U_BODY] = -1  #h[Star::U_BODY]    @m_nBody    #本命身宮所在地支位置(以地支表示)
    @StarInfo[U_SKY] = -1  #h[Star::U_SKY]    @m_nSky     #本命(出生年)天干
    @StarInfo[U_EARTH] = -1  #h[Star::U_EARTH]    @m_nEarth   #本命(出生年)地支
    @StarInfo[U_FIVE] = -1  #h[Star::U_FIVE]   @m_nFive    #本命五行局(2~6)
    @StarInfo[U_SMALL] = Array.new(12,nil)  #h[Star::U_SMALL]  @m_nSmall   #小限Array
    @StarInfo[U_LARGE] = Array.new(12,nil)  #h[Star::U_LARGE]       @m_nLarge   #大限Array
    @StarInfo[U_PAN_INFO] = @AllPanInfo  #h[Star::U_PAN_INFO]       @AllPanInfo   #各盤資訊
    @StarInfo[Star::ORIGINAL_FOUR_HUA] = @OriginalFourHua  # 先天四化
    @StarInfo[Star::SELF_FOUR_HUA] = @SelfFourHua  # 宮干自化
  end
  def initPanInfos(a)
    a.each_index do |panType|
      if (!a[panType]) then
        initEachPanInfo(panType) #設定流盤資訊
      end
    end
  end
  #---------------------------------------------------------------------
  def initEachPanInfo(nPanType)
    @m_nPanType = nPanType  #排盤類別

    if (nPanType <= Cfate::PAN_TENYEAR) then
      @m_nLargeSan = -1       #目前大限
    end

    #初始化各宮資訊
    hHouseInfo = Hash.new
    (1..12).each do |i|
      h = Hash.new
      h[Star::HOUSE_NAME] = nil
      h[Star::A_STAR] = nil #Array.new
      h[Star::B_STAR] = nil #Array.new
      h[Star::F_STAR] = nil #Array.new
      h[Star::FA_STAR] = nil #Array.new
      h[Star::FB_STAR] = nil #Array.new
      h[Star::S_STAR] = nil #Array.new
      h[Star::DOCTOR] = nil #Array.new
      h[Star::YEARGOD] = nil #Array.new
      h[Star::YEARSTAR] = nil #Array.new
      h[Star::GOD] = nil #Array.new
      h[Star::HOUSE_SKY] = nil #Array.new
      hHouseInfo[Earth.GetKeyName(i)] = h
    end
    hHouseInfo[Star::A_STAR_POS] = Array.new(Star::A_STAR_COUNT,nil)
    hHouseInfo[Star::B_STAR_POS] = Array.new(Star::B_STAR_COUNT,nil)
    hHouseInfo[Star::F_STAR_POS] = Array.new(Star::F_STAR_COUNT,-1)
    hHouseInfo[Star::S_STAR_POS] = Array.new(Star::S_STAR_COUNT,nil)
    hHouseInfo[Star::DOCTOR_POS] = Array.new(Star::DOCTOR_COUNT,-1)
    hHouseInfo[Star::YEARGOD_POS] = Array.new(Star::YEARGOD_COUNT,-1)
    hHouseInfo[Star::YEARSTAR_POS] = Array.new(Star::YEARSTAR_COUNT,-1)
    hHouseInfo[Star::GOD_POS] = Array.new(Star::GOD_COUNT,-1)
    hHouseInfo[Star::FLOW_HOUSE_HEAD] = nil
    hHouseInfo[Star::FLOW_LIFE_HEAD] = nil
    hHouseInfo[Star::SKY_EARTH_INDEX] = Array.new(Cfate::PAN_CNT,nil)
    @AllPanInfo[nPanType] = hHouseInfo
    aFourHua = Array.new(4,nil)
    @OriginalFourHua[nPanType] = aFourHua   # 先天四化
    aSelFourHua = Array.new(12) {Array.new}
    @SelfFourHua[nPanType] =  aSelFourHua # 宮干自化
  end
  def get_UserDefData()
    return @m_nUserDefinedDate.clone
  end
  def initUserDefData(hUserDefData,a)
    if (hUserDefData == nil) then return end
    if (@m_nUserDefinedDate != hUserDefData) then
      @m_nUserDefinedDate = hUserDefData.clone
      CheckUserAskDate()

      a.each_index do |panType|
        if (!a[panType]) then
          setFlowTimeInfo(panType) #設定流盤資訊
        end
      end
    end
  end
  #---------------------------------------------------------------------
  def initUserInfo(hUserInfo=nil)
    if (hUserInfo != nil) then
      #設定客戶資訊
      setUserInfo(hUserInfo)
    else
      @m_UserInfo = Hash.new()
      nEYear = 1900
      nEMonth = 1
      nEDate = 1
      nETime = 0
      bLeap = false
      # 真太陽時
      uis_EY(nEYear)     #出生年(農曆)
      uis_EM(nEMonth)    #出生月(農曆)
      uis_ED(nEDate)      #出生日(農曆)
      uis_ET(nETime)     #出生時(地支時，十二個時辰)
      uis_EL(bLeap)    #出生月是否為潤月
      uis_EMI(0)    #出生分

      nWYear,nWMonth,nWDate = Xdate.East2West(nEYear,nEMonth,nEDate,bLeap)
      uis_WY(nWYear)
      uis_WM(nWMonth)
      uis_WD(nWDate)
      uis_WH(Xdate.ETime2Hour(nETime))   #西元時24小時
      uis_WMI(0)          #西元分鐘(60分鐘)
      uis_bS(true)     #性別 true 是女性,false是男性
      uis_nSIY(0)       #0為陽,1為陰(陰男,陽男,陰女,陽女用)

      # 出生時
      uis_EY_B(nEYear)     #出生年(農曆)
      uis_EM_B(nEMonth)    #出生月(農曆)
      uis_ED_B(nEDate)      #出生日(農曆)
      uis_ET_B(nETime)     #出生時(地支時，十二個時辰)
      uis_EL_B(bLeap)    #出生月是否為潤月
      uis_EMI_B(0)    #出生分
      uis_WY_B(nWYear)
      uis_WM_B(nWMonth)
      uis_WD_B(nWDate)
      uis_WH_B(Xdate.ETime2Hour(nETime))   #西元時24小時
      uis_WMI_B(0)          #西元分鐘(60分鐘)

      #取得中宮資訊
      cp_UserInfo()
    end
  end

  #---------------------------------------------------------------------
  def Star.GetName(aStar)
    return Pm.GetStr("IDS_S_" + aStar)
  end
  def Star.GetNameKey(aStar)
    return "IDS_S_#{aStar}"
  end
  def Star.GetNameWithQuota(aStar)
    return Pm.GetStrWithQuote("IDS_S_" + aStar)
  end

  def resetUserInfo(nPanType,hUserInfo)
    initAll(Cfate::PAN_NONE,hUserInfo,@m_nUserDefinedDate,@UserType)
  end
  #input : Hash of UserInfo
  def setUserInfo(hUserInfo)
    if (@orgUserInfo == hUserInfo) then
      return
    end
    # 真太陽時修正 2020/8/18
    hUserInfo = realsun_time_check(hUserInfo)

    @m_UserInfo = hUserInfo.clone
    if (Xdate.IsWDateLegal?(uig_WY(),uig_WM(),uig_WD(),uig_WH())) then
      y,m,d,l = Xdate.West2East(uig_WY(),uig_WM(),uig_WD())

      # 一般子時
      # uis_ET(Xdate.Hour2ETime(uig_WH()))
      # y,m,d,l,t = Xdate.NextEDateFromTime(y,m,d,l, uig_ET())
      # 子時排盤 直接改農曆的客戶資訊，排盤跟著改
      y,m,d,l,t = par_check_ZiShi_Type(y,m,d,l,uig_WH())
      uis_E_YMDLT(y,m,d,l,t)
    elsif (Xdate.IsEDateLegal?(uig_EY(),uig_EM(),uig_ED(),uig_EL(),uig_WH())) then
      y,m,d = Xdate.East2West(uig_EY(),uig_EM(),uig_ED(),uig_EL())
      uis_W_YMD(y,m,d)

      # uis_ET(Xdate.Hour2ETime(uig_WH()))
      # y,m,d,l,t = Xdate.NextEDateFromTime(uig_EY(), uig_EM(), uig_ED(), uig_EL(), uig_ET())
      # 子時排盤
      y,m,d,l,t = par_check_ZiShi_Type(uig_EY(), uig_EM(), uig_ED(), uig_EL(),uig_WH())
      uis_E_YMDLT(y,m,d,l,t)
    end

    cp_GetnSex()

    #取得中宮資訊
    cp_UserInfo()

    @orgUserInfo = hUserInfo.clone
  end
  def realsun_time_check(hUserInfo)
    h = hUserInfo.clone
    nWYear, nWMonth,nWDate = h[Cfate::WYear],h[Cfate::WMonth],h[Cfate::WDate]
    h[Cfate::WYear_B],h[Cfate::WMonth_B],h[Cfate::WDate_B] = h[Cfate::WYear],h[Cfate::WMonth],h[Cfate::WDate]
    nWHour,nWMin = h[Cfate::WHour],h[Cfate::WMinute]
    h[Cfate::WHour_B],h[Cfate::WMinute_B] = h[Cfate::WHour],h[Cfate::WMinute]

    nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
    y,m,d,l,t = par_check_ZiShi_Type(nEYear,nEMonth,nEDate, bLeapMonth,nWHour)
    h[Cfate::EYear_B],h[Cfate::EMonth_B],h[Cfate::EDate_B],h[Cfate::ELeapMonth_B] = y,m,d,l
    h[Cfate::ETime_B] = t

    longitude,latitude = h["longitude"],h["latitude"]
    nWsYear,nWsMonth,nWsDate,nWsHour,nWsMin = Xdate.api_rst_localtime_to_localsuntime(nWYear, nWMonth,nWDate, nWHour,nWMin,longitude,latitude)
    h[Cfate::WYear] = nWsYear
    h[Cfate::WMonth] = nWsMonth
    h[Cfate::WDate] = nWsDate
    h[Cfate::WHour] = nWsHour
    h[Cfate::WMinute] = nWsMin
    nEsYear,nEsMonth,nEsDate,bsLeapMonth = Xdate.West2East(nWsYear,nWsMonth,nWsDate)
    h[Cfate::EYear] = nEsYear
    h[Cfate::EMonth] = nEsMonth
    h[Cfate::EDate] = nEsDate
    h[Cfate::ELeapMonth] = bsLeapMonth

    return h
  end

  #---------------------------------------------------------------------
  # def createPan(nPanType,hUserInfo)
  #   #取得各宮資訊
  #   cp_HouseInfo(nPanType)
  # end


  #---------------------------------------------------------------------
  #取得各宮資訊
  # def cp_House(nPanType)
  #   #取得各宮之天干
  #   cp_HouseSky(nPanType)

  #   #取得各宮之宮名
  #   cp_HouseName(nPanType)
  # end


  #---------------------------------------------------------------------
  #計算各宮資訊
  # def cp_HouseInfo(nPanType)

  #   cp_House(nPanType)    #宮位位置

  #   cp_A_Stars(nPanType)   #甲級星

  #   # cp_B_Stars(nPanType)   #乙級星

  #   # cp_Gods(nPanType)    #長生十二神

  #   # cp_YearStars(nPanType) #將前十二星

  #   # cp_YearGods(nPanType)  #歲前十二星

  #   # cp_Doctors(nPanType)   #博士十二星

  #   # cp_F_Stars(nPanType)   #流年星

  #   # cp_S_Stars(nPanType)   #特殊星
  # end

  def p_GetAStar(nPanType,nStar)
    return get_A_Star_HouseIndex(nPanType,nStar)
  end

  def p_GetBStar(nPanType,nStar)
    return get_B_Star(nPanType,nStar)
  end

  #---------------------------------------------------------------------
  #計算中宮相關資訊
  def cp_UserInfo()
    cp_getLife()    #計算命宮位置(以地支表示)
    cp_getGod()     #計算子年斗君位置
    cp_getBody()    #計算本命身宮所在地支位置

    cp_getSkyIndex()     #本命(出生年)天干
    cp_getEarthIndex()   #本命(出生年)地支
    cp_getFive()    #本命五行局(2~6)

    cp_GetLargeSmall()  #取得大小限
  end

  def Modify2TwoWords(sIn)
    sOut = sIn
    if (sIn.length == 1) then
      sOut = sIn + Cfate.GetSpace(1)
    end
    return sOut
  end

  def gHouse_GetHouseName(nPanType,nEarth)
    sOut = cp_getHouseName(nPanType,nEarth)
    return sOut
  end

  def gHouse_GetHouseNameWithQuota(nPanType,nEarth)
    sOut = Pm.GetStr("IDS_A_LEFT_QUOTE")
    sOut += cp_getHouseName(nPanType,nEarth)
    sOut += Pm.GetStr("IDS_A_RIGHT_QUOTE")
    return sOut
  end

  # 宮干支 <FONT COLOR=BLACK>丁巳
  def gHouse_GetHouseSkyEarthName(nPanType,nEarth)
    sOut = Sky.GetName(cp_getHouseSky(nPanType,nEarth))
    sOut += Earth.GetName(nEarth)
  end
  def gHouse_GetHouseSkyName(nPanType,nEarth)
    sOut = Sky.GetName(cp_getHouseSky(nPanType,nEarth))
  end

  def gHouse_GetSkyName(nPanType,nEarth)
    sOut = Sky.GetName(gHouse_GetSky(nPanType,nEarth))
    return sOut
  end

  def gHouse_GetSky(nPanType,nEarth)
    return cp_getHouseSky(nPanType,nEarth)
  end
  def gHouse_GetSky_ByHouseId(nPanType,nHouseId)
    nEarth = g_House_GetEarth(nPanType,nHouseId)
    return gHouse_GetSky(nPanType,nEarth)
  end
  def gHouse_GetHouseEarthFromTiangan(nPanType,tiangan)
    a = []
    (1..12).each do |nEarth|
      if (gHouse_GetSky(nPanType,nEarth) == tiangan) then
        a.push(nEarth)
      end
    end
    return a
  end
  def gHouse_GetHouseIdFromTiangan(nPanType,tiangan)
    aEarth = gHouse_GetHouseEarthFromTiangan(nPanType,tiangan)
    return g_House_GetHouseIds(nPanType,aEarth)
  end
  def gHouse_GetHouseNameFromTiangan(nPanType,tiangan)
    a = gHouse_GetHouseEarthFromTiangan(nPanType,tiangan)
    a2 = []
    a.each do |nEarth|
      a2.push(gHouse_GetHouseName(nPanType,nEarth))
    end
    return a2
  end
  def gHouse_GetHouseEarthName(nEarth)
    sOut = Earth.GetName(nEarth)
  end

  def gHouse_GetEarthName(nEarth)
    sOut = Earth.GetName(nEarth)
  end

  def gHouse_Five(nPanType,nEarth)
    nFive = cp_getHouseFive(nPanType,nEarth)
    return nFive
  end

  def g_House_FindEarth(nPanType,sHouseName)
    return cp_getHouseEarth(nPanType,sHouseName)
  end

  def gHouse_GetFirstHouseEarth(nPanType)
    return cp_getFirstHouseEarth(nPanType)
  end

  def gHouse_GetNextHouseEarth(nEarth)
    # if (cp_CheckDirectionClock()) then
    #   nEarth = nEarth + 1
    # else
      nEarth = nEarth - 1
    # end
    nEarth = Earth.ModifyEarth(nEarth)
    return nEarth
  end

  def Star.g_House_FindId(sHouseName)
    (1..12).each do |nHouseId|
      if (sHouseName == Star.GetHouseName(nHouseId)) then
        return nHouseId
      end
    end
    return nil
  end

  def g_House_GetHouseId(nPanType,nEarth)
    return cp_getHouse(nPanType,nEarth)
  end
  def g_House_GetHouseIds(nPanType,aEarth)
    a = []
    aEarth.each do |nEarth|
      a.push(g_House_GetHouseId(nPanType,nEarth))
    end
    return a
  end

  def g_House_HouseIndex2HouseIdIndex(nPanType,nHouseIndex)
    nHouseId = g_House_HouseIndex2HouseId(nPanType,nHouseIndex)
    return (Earth.Earth2EarthIndex(nHouseId))
  end
  def g_House_HouseIndex2HouseId(nPanType,nHouseIndex)
    nEarth = Star.HouseIndex2Earth(nHouseIndex)
    nHouseId = g_House_GetHouseId(nPanType,nEarth)
    return nHouseId
  end
  # nHouseIndex 是寅宮為0，nHouseId 是命宮為 1
  def g_getBodyHouseId(nPanType)
    nHouseIndex = cp_getBody()
    nHouseId = g_House_HouseIndex2HouseId(nPanType,nHouseIndex)
    return nHouseId
  end

  def g_House_GetEarth(nPanType,nHouse)
    (1..12).each do |nEarth|
      if (nHouse == g_House_GetHouseId(nPanType,nEarth)) then
        return nEarth
      end
    end
    return 0
  end

  def g_House_GetHouseIndex(nPanType,nEarth)
    nHouseId = g_House_GetHouseId(nPanType,nEarth)
    nHouseIndex = Earth.Earth2EarthIndex(nHouseId)
    return nHouseIndex
  end

  def gHouse_GetFourOutHouseName(nPanType,nEarth,nFourHua)
    aFourOutEarth = gHouse_GetFourOutHouse(nPanType,nEarth,nFourHua)
    sHouseName = cp_getHouseName(nPanType,aFourOutEarth[0])
    return sHouseName
  end
  def gHouse_GetFourOutHouseId(nPanType,nEarth,nFourHua)
    aFourOutEarth = gHouse_GetFourOutHouse(nPanType,nEarth,nFourHua)
    return g_House_GetHouseId(nPanType,aFourOutEarth[0])
  end
  def gHouse_GetFourOutHouseEarth(nPanType,nEarth,nFourHua)
    aFourOutEarth = gHouse_GetFourOutHouse(nPanType,nEarth,nFourHua)
    return aFourOutEarth[0]
  end
  def gHouse_GetFourOutNormalHouseId(nPanType,nEarth,nFourHua)
    aFourOutEarth = gHouse_GetFourOutHouse(nPanType,nEarth,nFourHua)
    return g_House_GetHouseId(Cfate::PAN_NORMAL,aFourOutEarth[0])
  end

  def gHouse_GetFourOutHouse(nPanType,nEarth,nFourHua)
    nStar = gHouse_GetFourOutStar(nPanType,nEarth,nFourHua)
    nHouseIndex = gHouse_GetAStar_HouseIndex(nPanType,nStar)
    # 若飛出到對宮，且本宮無主星，則本宮及對宮皆要判斷
    nFourHuaEarth = Star.HouseIndex2Earth(nHouseIndex)
    aFourOutEarth = Array.new
    aFourOutEarth.push(nFourHuaEarth)

    # 若四化之宮的對宮沒有主星，則一起傳回
    nFourOutOppEarth = Earth.ModifyEarth(nFourHuaEarth + 6)
    if (gHouse_WithoutMainAStar(nPanType,nFourOutOppEarth)) then
      aFourOutEarth.push(nFourOutOppEarth)
    end

    return aFourOutEarth
  end

  def gHouse_GetFourOutStar(nPanType,nEarth,nFourHua)
    nSkyIndex = cp_getHouseSkyIndex(nPanType,nEarth)
    nStar = getFourHuaStarValue(nSkyIndex,nFourHua)
    return nStar
  end

  def gHouse_GetAStar_HouseIndex(nPanType,nStar)
    nHouseIndex = get_A_Star_HouseIndex(nPanType,nStar)
    return nHouseIndex
  end
  def gHouse_GetAStar_HouseEarth(nPanType,nStar)
    nHouseIndex = gHouse_GetAStar_HouseIndex(nPanType,nStar)
    nEarth = Star.HouseIndex2Earth(nHouseIndex)
    return nEarth
  end
  def gHouse_GetAStar_HouseEarthName(nPanType,nStar)
    nEarth = gHouse_GetAStar_HouseEarth(nPanType,nStar)
    return Pm.GetStr("IDS_S_EARTH_#{nEarth}")
  end

  def gHouse_GetAStar_HouseId(nPanType,nStar)
    nEarth = gHouse_GetAStar_HouseEarth(nPanType,nStar)
    return g_House_GetHouseId(nPanType,nEarth)
  end

  def gHouse_GetBStar_HouseIndex(nPanType,nStar)
    nHouseIndex = get_B_Star(nPanType,nStar)
    return nHouseIndex
  end
  def gHouse_GetBStar_HouseEarth(nPanType,nStar)
    nHouseIndex = gHouse_GetBStar_HouseIndex(nPanType,nStar)
    return nil if nHouseIndex == nil
    return Star.HouseIndex2Earth(nHouseIndex)
  end
  def gHouse_GetBStar_HouseId(nPanType,nStar)
    nEarth = gHouse_GetBStar_HouseEarth(nPanType,nStar)
    return nil if nEarth == nil
    return g_House_GetHouseId(nPanType,nEarth)
  end

  def gHouse_GetYearStar_HouseIndex(nPanType,nStar)
    nStarIndex = nStar - 1
    nHouseIndex = get_YearStar_HouseIndex(nPanType,nStarIndex)
    return nHouseIndex
  end

  def gHouse_GetYearGod_HouseIndex(nPanType,nStar)
    nStarIndex = nStar - 1
    nHouseIndex = get_YearGod_HouseIndex(nPanType,nStarIndex)
    return nHouseIndex
  end

  def gHouse_GetAStarNames(nPanType,nEarth,bOnlyMain=false,bOppIfNoMain=true)
    aStar = get_HouseAStars(nPanType,nEarth,bOnlyMain)
    aStarName = get_HouseAStarNames(nPanType,nEarth,bOnlyMain)
    #若無主星,則拿對宮主星
    if (bOppIfNoMain) then
      if (gHouse_IfNoMainStar?(aStar)) then
        aStarName = get_HouseAStarNames(nPanType,Earth.ModifyEarth(nEarth + 6),true) + aStarName
      end
    end
    return aStarName
  end

  def gHouse_GetAStarsMioWong(nPanType,nEarth,bOnlyMain=false,bOppIfNoMain=true)
    aStar = get_HouseAStars(nPanType,nEarth,bOnlyMain)
    aStarMioWong = get_HouseAStarsMioWong(nPanType,nEarth,bOnlyMain)
    #若無主星,則拿對宮主星
    if (bOppIfNoMain) then
      if (gHouse_IfNoMainStar?(aStar)) then
        aStarMioWong = get_HouseAStarsMioWong(nPanType,Earth.ModifyEarth(nEarth + 6),true) + aStarMioWong
      end
    end
    return aStarMioWong

  end

  def gHouse_GetAStarsMioWongStr(nPanType,nEarth,bOnlyMain=false,bOppIfNoMain=true)
     aStarMioWong = gHouse_GetAStarsMioWong(nPanType,nEarth,bOnlyMain,bOppIfNoMain)
     aMioWongStr = Array.new
     aStarMioWong.each do |nMioWong|
      aMioWong = Array.new
      aMioWong.push(getMioWongStr(nMioWong))
      aMioWong.push(isMioWongNeedOutline?(nMioWong))
      aMioWongStr.push(aMioWong)
     end
     return aMioWongStr
  end

  def gHouse_GetAStarsMioWongInfo(nPanType,nEarth,bOnlyMain=false,bOppIfNoMain=true)
     aStarMioWong = gHouse_GetAStarsMioWong(nPanType,nEarth,bOnlyMain,bOppIfNoMain)
     aMioWongInfo = Array.new
     aStarMioWong.each do |nMioWong|
      aMioWong = Array.new
      aMioWong.push(nMioWong == nil ? 99 : nMioWong)
      aMioWong.push(getMioWongStr(nMioWong))
      aMioWong.push(isMioWongNeedOutline?(nMioWong))

      aMioWongInfo.push(aMioWong)
     end
     return aMioWongInfo
  end

  def gHouse_GetBStarsMioWong(nPanType,nEarth)
    aStarMioWong = get_HouseBStarsMioWong(nPanType,nEarth)
    return aStarMioWong

  end
  def gHouse_GetBStarsMioWongInfo(nPanType,nEarth)
     aStarMioWong = gHouse_GetBStarsMioWong(nPanType,nEarth)
     aMioWongInfo = Array.new
     aStarMioWong.each do |nMioWong|
      aMioWong = Array.new
      aMioWong.push(nMioWong == nil ? 99 : nMioWong)
      aMioWong.push(getMioWongStr(nMioWong))
      aMioWong.push(isMioWongNeedOutline?(nMioWong))

      aMioWongInfo.push(aMioWong)
     end
     return aMioWongInfo
  end

  def gHouse_GetAStarsFourHua(nPanType,nEarth)
    if (@par_FlowYearHua != Star::PAN_YEAR_HUA_STACK) then
      return gHouse_GetAStarsFourHua_Steven(nPanType,nEarth)
    else
      # 內湖 林信銘老師
      return gHouse_GetAStarsFourHua_Lin(nPanType,nEarth)
    end
  end
  def gHouse_GetOrgFourHua_FourHua(nPanType,nEarth)
    aFourHua = gHouse_GetAStarsFourHua(nPanType,nEarth)
    a = []
    aFourHua.each do |aFH|
      a.push(aFH[0]) if aFH[0] != 99
    end
    return a
  end

  def gHouse_GetAStarsFourHua_Steven(nPanType,nEarth)
    aStarInfos = get_HouseAStarInfos(nPanType,nEarth,false)
    aFourHua = Array.new
    aStarInfos.each do |starInfo|
      hFourHua = get_AStarInfoOrgFourHua(starInfo)
      nFourHua = getFourHuaHashValue(hFourHua,nPanType)

      aFH = Array.new
      aFH.push(nFourHua == nil ? 99 : nFourHua)
      aFH.push(getFourHuaStr(nFourHua))

      aFourHua.push(aFH)
    end

    return aFourHua
  end


  def gHouse_GetAStarsFourHua_Lin(nPanType,nEarth)
    aStarInfos = get_HouseAStarInfos(nPanType,nEarth,false)
    if (nPanType == Cfate::PAN_NORMAL) then
      aPanType = [Cfate::PAN_NORMAL]
    elsif (nPanType == Cfate::PAN_TENYEAR) then
      aPanType = [Cfate::PAN_NORMAL,Cfate::PAN_TENYEAR]
    else
      aPanType = [nPanType-2,nPanType-1,nPanType]
    end

    aFourHua = Array.new
    aStarInfos.each do |starInfo|
      hFourHua = get_AStarInfoOrgFourHua(starInfo)
      aAllFH = Array.new
      aPanType.each do |nDisPanType|
        nFourHua = getFourHuaHashValue(hFourHua,nDisPanType)

        aFH = Array.new
        aFH.push(nFourHua == nil ? 99 : nFourHua)
        aFH.push(getFourHuaStr(nFourHua))

        aAllFH.push(aFH)
      end

      aFourHua.push(aAllFH)
    end

    return aFourHua
  end

  def gHouse_IfNoMainStar?(aStar)
    return ((aStar & @@AStar_Main).empty?)
  end

  def gHouse_GetAStars_Sep(nPanType,nEarth,bOnlyMain=false,bOppIfNoMain=true)
    aOrgStar = get_HouseAStars(nPanType,nEarth,bOnlyMain)
    bUseOpp = false
    aOppStar = Array.new
    #若無主星,則拿對宮主星
    if (bOppIfNoMain) then
      if (gHouse_IfNoMainStar?(aOrgStar)) then
        aOppStar = get_HouseAStars(nPanType,Earth.ModifyEarth(nEarth + 6),true)
        if (aOppStar.length > 0) then
          bUseOpp = true
        end
      end
    end
    return aOrgStar,aOppStar,bUseOpp
  end

  def gHouse_GetAStars(nPanType,nEarth,bOnlyMain=false,bOppIfNoMain=true)
    aOrgStar,aOppStar,bUseOpp = gHouse_GetAStars_Sep(nPanType,nEarth,bOnlyMain,bOppIfNoMain)
    aStar = aOrgStar + aOppStar
    return aStar,bUseOpp
  end


  def gHouse_WithoutMainAStar(nPanType,nEarth)
    aStar,bUseOpp = gHouse_GetAStars(nPanType,nEarth,true,false)
    return (gHouse_IfNoMainStar?(aStar))
  end

  def gHouse_GetAStarInfos(nPanType,nEarth,bOnlyMain=false)
    return get_HouseAStarInfos(nPanType,nEarth,bOnlyMain)
  end

  def gHouse_GetYearGod(nPanType,nEarth)
    return get_YearGod(nPanType,nEarth)
  end

  def gHouse_GetYearGodName(nPanType,nEarth)
    return get_YearGod_Name(nPanType,nEarth)
  end

  def gHouse_GetYearGodInfo(nPanType,nEarth)
    return get_YearGod_Info(nPanType,nEarth)
  end

  def gHouse_GetGod(nPanType,nEarth)
    return get_God(nPanType,nEarth)
  end

  def gHouse_GetGodIndex(nPanType,nEarth)
    nGod = gHouse_GetGod(nPanType,nEarth)
    nGodIndex = Earth.Earth2EarthIndex(nGod)
    return nGodIndex
  end

  def gHouse_GetGodName(nPanType,nEarth)
    return get_God_Name(nPanType,nEarth)
  end

  def gHouse_GetGodInfo(nPanType,nEarth)
    return get_God_Info(nPanType,nEarth)
  end


  def gHouse_GetYearStar(nPanType,nEarth)
    return get_YearStar(nPanType,nEarth)
  end

  def gHouse_GetYearStarName(nPanType,nEarth)
    return get_YearStar_Name(nPanType,nEarth)
  end

  def gHouse_GetYearStarInfo(nPanType,nEarth)
    return get_YearStar_Info(nPanType,nEarth)
  end

  def gHouse_GetDoctor(nPanType,nEarth)
    return get_Doctor(nPanType,nEarth)
  end

  def gHouse_GetDoctorName(nPanType,nEarth)
    return get_Doctor_Name(nPanType,nEarth)
  end

  def gHouse_GetDoctorInfo(nPanType,nEarth)
    return get_Doctor_Info(nPanType,nEarth)
  end

  # 01234
  # 水木金土火
  # 回1是相生
  # 回3是相剋

  # 改成
  # 水木火土金  為  01234 來比
  # 差異值小於等於1的為相生,差異值為2或3的為相剋
  def g_GetFiveSanKur(nFive1,nFive2)
    cmpFive1 = p_GetCmpFive(nFive1)
    cmpFive2 = p_GetCmpFive(nFive2)
    nFiveDiff = p_GetFiveDiff(cmpFive1,cmpFive2)

    if (nFiveDiff == -1) then
      return 5
    elsif (nFiveDiff == 0) then
      return 1
    elsif (nFiveDiff == 1) then
      return 1
    elsif (nFiveDiff == 4) then
      return 1
    end
    return 3
  end
  def g_GetFiveSanKur2(nFive1,nFive2)
    cmpFive1 = p_GetCmpFive(nFive1)
    cmpFive2 = p_GetCmpFive(nFive2)
    nFiveDiff = p_GetFiveDiff2(cmpFive1,cmpFive2)

    return nFiveDiff
  end

  # 傳入
  # 0 表示沒有五行
  # 12345  表示  水木金土火
  # 傳回
  # -1 表示沒有五行
  # 01234  表示  水木火土金
  def p_GetCmpFive(nFive)
    nCmpFive = [0,1,4,3,2]
    if (nFive == 0) then  # 0 表示沒有五行
      return -1
    end
    nFiveIndex = nFive - 1
    return nCmpFive[nFiveIndex]
  end

  def p_GetFiveDiff(nFive1,nFive2)
    if (nFive1 == -1) then return -1 end
    if (nFive2 == -1) then return -1 end

    if (nFive1 > nFive2) then
      return (nFive1 - nFive2)
    end
    return (nFive2 - nFive1)
  end
  def p_GetFiveDiff2(nFive1,nFive2)
    if (nFive1 == -1) then return -10 end
    if (nFive2 == -1) then return -10 end

    return (nFive1 - nFive2)
  end

  def gHouse_GetAStarFive(nPanType,nEarth,nStar)
    nStarFive = p_GetA2StarFive(nPanType,nEarth,nStar)
    if (nStarFive == 0) then
      nStarIndex = Star.Star2StarIndex(nStar)
          nStarFive = @@AStarFive[nStarIndex] + 1
      end
      return (nStarFive)
  end

  def p_GetA2StarFive(nPanType,nEarth,nStar)
    if (@@A2Star.include?(nStar) == false) then
      return 0
    end
    aStar,bUseOpp = gHouse_GetAStars(nPanType,nEarth)
    # 本宮無主星，取對宮當主星時，不算雙星
    if (bUseOpp) then
      return 0
    end

    nDStarIndex = @@A2Star1.index(nStar)
    # 廉貞,貪狼,巨門,七殺
    if (nDStarIndex != nil) then
      if ((aStar & @@DoubleAStar_One[nDStarIndex]).length > 0) then
        return p_ChangeAStarFive(nStar)
      end
    else # 擎羊，本宮中有雙星組合就改變五行屬性
      if (p_IsFindDoubleStar?(aStar)) then
        return p_ChangeAStarFive(nStar)
      end
    end
    return 0
  end

  def p_ChangeAStarFive(nStar)
    nDStarIndex = @@A2Star.index(nStar)
    if (nDStarIndex == nil) then
      return 0
    end
    return @@ByChangeFive[nDStarIndex] + 1
  end

  def p_FindDoubleStar(aStar)
    aStar.sort!
    aDouble = aStar.repeated_combination(2).to_a
    return (aDouble & @@DoubleAStar_All)
  end

  def p_IsFindDoubleStar?(aStar)
    aDouble = p_FindDoubleStar(aStar)
    return (aDouble.length > 0)
  end

  def gHouse_GetBStarFive(nPanType,nEarth,nStar)
      nHouse = cp_getHouse(nPanType,nEarth)
      nStarIndex = Star.Star2StarIndex(nStar)
      nFive = -1
      if (nHouse == 6 && nStar == 33) then  # 天傷
          nFive = @@BStarFive[nStarIndex]
      elsif (nHouse == 8 && nStar == 34) then # 天使
          nFive = @@BStarFive[nStarIndex]
      elsif (nStar < 32) then
          aStar = gHouse_GetBStars(nPanType,nEarth)
          if (aStar.include?(nStar)) then
              nFive = @@BStarFive[nStarIndex]
          end
      end
      return (nFive + 1)
  end

  def gHouse_GetBStarNames(nPanType,nEarth)
    return get_HouseBStarNames(nPanType,nEarth)
  end

  def gHouse_GetBStars(nPanType,nEarth)
    return get_HouseBStars(nPanType,nEarth)
  end

  def gHouse_GetBStarInfos(nPanType,nEarth)
    return get_HouseBStarInfos(nPanType,nEarth)
  end

  def g_getLargeSanStartYear()
    return cp_getLargeSanStartYear()
  end

  def gHouse_getLargeSan()
    return cp_GetLargeSan(0)
  end

  def gHouse_GetStarFive(nPanType,nEarth,nStar,sStarType)
    nFive = -1
    if (sStarType == Star::A_STAR) then
      nFive = gHouse_GetAStarFive(nPanType,nEarth,nStar)
    elsif (sStarType == Star::B_STAR) then
      nFive = gHouse_GetBStarFive(nPanType,nEarth,nStar)
    elsif (sStarType == Star::DOCTOR) then
      nFive = gHouse_GetDoctorFive(nStar)
    elsif (sStarType == Star::YEARSTAR) then
      nFive = gHouse_GetYearStarFive(nStar)
    elsif (sStarType == Star::YEARGOD) then
      nFive = gHouse_GetYearGodFive(nStar)
    elsif (sStarType == Star::GOD) then
      nFive = gHouse_GetGodFive(nStar)
    end
    return nFive
  end

  def gHouse_GetCStar(nPanType,nEarth,sStarType)
    nStar = -1
    if (sStarType == Star::DOCTOR) then
      nStar = gHouse_GetDoctor(nPanType,nEarth)
    elsif (sStarType == Star::YEARSTAR) then
      nStar = gHouse_GetYearStar(nPanType,nEarth)
    elsif (sStarType == Star::YEARGOD) then
      nStar = gHouse_GetYearGod(nPanType,nEarth)
    elsif (sStarType == Star::GOD) then
      nStar = gHouse_GetGod(nPanType,nEarth)
    end
    return nStar
  end

  def gHouse_GetCStarName(nPanType,nEarth,sStarType)
    sStarName = ""
    if (sStarType == Star::DOCTOR) then
      sStarName = gHouse_GetDoctorName(nPanType,nEarth)
    elsif (sStarType == Star::YEARSTAR) then
      sStarName = gHouse_GetYearStarName(nPanType,nEarth)
    elsif (sStarType == Star::YEARGOD) then
      sStarName = gHouse_GetYearGodName(nPanType,nEarth)
    elsif (sStarType == Star::GOD) then
      sStarName = gHouse_GetGodName(nPanType,nEarth)
    end
    return sStarName
  end

  # 水=0 木=1 金=2 土=3 火=4
  def gHouse_GetDoctorFive(nStar)
    nStarIndex = nStar - 1
    return @@DoctorFive[nStarIndex] + 1
  end

  def gHouse_GetGodFive(nStar)
    nStarIndex = nStar - 1
    return @@GodFive[nStarIndex] + 1
  end

  def gHouse_GetYearStarFive(nStar)
    nStarIndex = nStar - 1
    return @@YearStarFive[nStarIndex] + 1
  end

  def gHouse_GetYearGodFive(nStar)
    nStarIndex = nStar - 1
    return @@YearGodFive[nStarIndex] + 1
  end

  def gHouse_GetSmall(nEarth)
    aSmall = Array.new
    nStarYear = cp_getHouseSmall(nEarth)
    aSmall.push(nStarYear)
    7.times do |i|
      nStarYear += 12
      aSmall.push(nStarYear)
    end

    return aSmall
  end

  def gHouse_FindSelfFourHua(nPanType,nEarth)
    aStar,bUseOpp = gHouse_GetAStars(nPanType,nEarth,true,true)
    ahFourHua = Array.new
    aStar.each do |nStar|
      nFourHua = findSelfFourHua(nPanType,nEarth,nStar)

      if (nFourHua != nil) then
        hFourHua = Hash.new
        hFourHua[Star::A_STAR] = nStar
        hFourHua[Star::FOUR_HUA] = nFourHua
        ahFourHua.push(hFourHua)
      end
    end
    return ahFourHua
  end

  def gHouse_FindSelfFourHua_Star(nPanType,nEarth)
    aFourHuaInfo = gHouse_FindSelfFourHua(nPanType,nEarth)
    aFourHuaStar = Array.new
    aFourHuaInfo.each do |hFourHua|
      aFourHuaStar.push(hFourHua[Star::A_STAR])
    end

    return aFourHuaStar
  end

  def gHouse_FindSelfFourHua_FourHua(nPanType,nEarth)
    aFourHuaInfo = gHouse_FindSelfFourHua(nPanType,nEarth)
    aFourHua = Array.new
    aFourHuaInfo.each do |hFourHua|
      aFourHua.push(hFourHua[Star::FOUR_HUA])
    end

    return aFourHua
  end

  def gNormal_FindAStarsFourHuaSky(aStar)
    aaaFourHuaSky = Array.new
    aStar.each do |nStar|
      aaFourHuaSky = gNormal_FindFourHuaSky(nStar)
      aaaFourHuaSky.push(aaFourHuaSky)
    end
    return aaaFourHuaSky
  end

  def gNormal_FindSkyFourHuaStar(nSky)
    nSkyIndex = Sky.Sky2SkyIndex(nSky)
    aSkyFourHuaStar = Array.new
    (Star::FH_LU..Star::FH_GI).each do |nFourHuaIndex|
      nStar = getFourHuaStarValue(nSkyIndex,nFourHuaIndex)
      aFourHuaStar = Array.new
      aFourHuaStar.push(nStar)
      aFourHuaStar.push(nFourHuaIndex)
      aSkyFourHuaStar.push(aFourHuaStar)
    end
    return aSkyFourHuaStar
  end

  def gNormal_GetSky()
    nSkyIndex = cp_getSkyIndex()
    nSky = Sky.SkyIndex2Sky(nSkyIndex)
    return nSky
  end

  def gNormal_GetEarth()
    nEarthIndex = cp_getEarthIndex()
    nEarth = Earth.EarthIndex2Earth(nEarthIndex)
    return nEarth
  end

  def gNormal_GetFlowYearOld(nPanType)
    return cp_GetFlowYearOld(nPanType)
  end

  def gNormal_GetYearOld(nBirthEYear,nFlowEYear)
      return Xdate.GetYearOld(nBirthEYear,nFlowEYear)
  end

end

