require("Xdate_Constant.rb")
require("Xdate_Variable.rb")
require("Pm.rb")
require("SkyEarthFive_Function.rb")
require("Cfate.rb")

class Xdate
  def Xdate.GetSegYearGanZhi(nWYear, nWMonth, nWDate,nHour,nMinute,nFirstSegment=nil)
    nYear = Xdate.GetSegYear(nWYear, nWMonth, nWDate,nHour,nMinute,nFirstSegment)

    return Xdate.GetYearGanZhi(nYear)
  end
  def Xdate.GetSegYearGanZhiStr(nWYear, nWMonth, nWDate,nHour, nMinute,nFirstSegment=nil)
      nGanZhi = Xdate.GetSegYearGanZhi(nWYear, nWMonth, nWDate,nHour, nMinute,nFirstSegment)
    sBuf = Xdate.GetGanZhiYear_Str(nGanZhi)
    return sBuf
  end
  def Xdate.GetSegYear(nWYear, nWMonth, nWDate,nHour,nMinute,nFirstSegment)
    if (nFirstSegment == nil) then
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
      nYear = nEYear
    else
      nYear = nWYear
      nWYear1,nSegment = Xdate.GetSegmentFromWest(nWYear, nWMonth, nWDate, nHour, nMinute)
      if (nFirstSegment == Xdate::SEGMENT_SPRING) then
        if (nSegment < nFirstSegment) then
          nYear = nWYear - 1
        else
          nYear = nWYear1
        end
      elsif (nFirstSegment == Xdate::SEGMENT_WINTER) then
        if (nSegment >= nFirstSegment) then
          nYear = nWYear + 1
        end
      end
    end
# Pm.saveTestDb("GetSegYear","#{nWYear}, #{nWMonth}, #{nWDate}, #{nHour}, #{nMinute},#{nWYear1},#{nSegment},#{nYear}")
    return nYear
  end
  # 求出 nWYear, nWMonth, nWDate 之後的第一個 ganzhi 的日
  def Xdate.GetSegGanZhiDate(ganzhi,nWYear, nWMonth, nWDate,nFirstSegment=nil,nSegmentMove=Xdate::SegmentNow)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, 0,59,Xdate::FIRST_NEXT,nFirstSegment,nSegmentMove)

    n = ganzhi - nGanZhiDay
    if (n < 0) then
      n += 60
    end
    nWYear,nWMonth,nWDate = Xdate.NextNWDate(nWYear,nWMonth,nWDate,n)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, 0,59,Xdate::FIRST_NEXT,nFirstSegment,nSegmentMove)
    return nWYear,nWMonth,nWDate,nGanZhiYear,nGanZhiMonth,nGanZhiDay
  end
  def Xdate.GetSegGanZhiHour(ganzhi,nWYear, nWMonth, nWDate,nHour,nFirstSegment=nil,nSegmentMove=Xdate::SegmentNow)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, nHour,59,Xdate::FIRST_NEXT,nFirstSegment,nSegmentMove)

    n = ganzhi - nGanZhiHour
    if (n < 0) then
      n += 60
    end
    n *= 2

    nWYear,nWMonth,nWDate,nHour = Xdate.NextNHour(nWYear,nWMonth,nWDate,nHour,n)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, nHour,59,Xdate::FIRST_NEXT,nFirstSegment,nSegmentMove)
    return nWYear,nWMonth,nWDate,nHour,nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour
  end

  def Xdate.GetSegYearForMonthGanZhi(nWYear, nWMonth, nWDate,nHour,nMinute,nFirstSegment)
    if (nFirstSegment == Xdate::SEGMENT_SPRING) then
      nYear = nWYear
      nWYear1,nSegment = Xdate.GetSegmentFromWest(nWYear, nWMonth, nWDate, nHour, nMinute)
      if (nSegment < nFirstSegment) then
        nYear = nWYear - 1
      else
        nYear = nWYear1
      end
    else
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
      nYear = nEYear
    end

    return nYear
  end

  #---------------------------------------------------------------------
  # Input the west "Year" and "Month", and then return the GanZhi of month.
  # nHour is from "0" to "24".
  def Xdate.GetSegMonthGanZhi(nWYear, nWMonth, nWDate,nHour, nMinute,nFirstSegment=nil)
    nYear = Xdate.GetSegYearForMonthGanZhi(nWYear, nWMonth, nWDate,nHour,nMinute,nFirstSegment)
    # nYear = Xdate.GetSegYearForMonthGanZhi(nWYear, nWMonth, nWDate,22,59,nFirstSegment)

    nMonth = Xdate.GetSegMonthForMonthGanZhi(nWYear, nWMonth, nWDate,nHour,nMinute,nFirstSegment)
    # nMonth = Xdate.GetSegMonthForMonthGanZhi(nWYear, nWMonth, nWDate,22,59,nFirstSegment)

    return Xdate.GetMonthGanZhi(nYear, nMonth)
  end
  def Xdate.GetSegMonthGanZhiStr(nWYear, nWMonth, nWDate,nHour, nMinute,nFirstSegment=nil)
      nGanZhiMonth = Xdate.GetSegMonthGanZhi(nWYear, nWMonth, nWDate,nHour, nMinute,nFirstSegment)
    sBuf = Xdate.GetGanZhiMonth_Str(nGanZhiMonth)
    return sBuf
  end

  def Xdate.GetSegMonth(nWYear, nWMonth, nWDate,nHour,nMinute,nFirstSegment=nil)
    if (nFirstSegment == nil) then
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
      nMonth = nEMonth
    else
      nWYear,nSegment = Xdate.GetSegmentFromWest(nWYear, nWMonth, nWDate, nHour, nMinute)
      nSegmentDiff = Pm.TestNo24(nSegment - nFirstSegment)
      nMonth = nSegmentDiff / 2 + 1
    end

    return nMonth
  end
  def Xdate.GetSegMonthStr(nSMonth)
    sBuf = ""
    sBuf += Pm.GetStr("IDS_S_CHINESE_MONTH_#{nSMonth}")
    sBuf += Pm.GetStr("IDS_X_MONTH")
    return sBuf
  end

  def Xdate.SegMonth2SegmentIndex(nSYear,nSMonth,nFirstSegment=nil)
    if (nFirstSegment == Xdate::SEGMENT_SPRING) then
      if (((nSMonth - 1) * 2 + nFirstSegment) >= Xdate::SEGMENT_COUNT_PER_YEAR) then
        nSYear += 1
      end
      nSegmentIndex = Pm.TestNo24((nSMonth - 1) * 2 + nFirstSegment)
      return nSYear,nSegmentIndex
    elsif (nFirstSegment == Xdate::SEGMENT_WINTER) then
      if (((nSMonth - 1) * 2 + nFirstSegment) < Xdate::SEGMENT_COUNT_PER_YEAR) then
        nSYear -= 1
      end
      nSegmentIndex = Pm.TestNo24((nSMonth - 1) * 2 + nFirstSegment)
      return nSYear,nSegmentIndex
    else
      return nSYear,(nSMonth - 1) * 2
    end
  end

  def Xdate.GetSegMonthForMonthGanZhi(nWYear, nWMonth, nWDate,nHour,nMinute,nFirstSegment=nil)
    if (nFirstSegment == Xdate::SEGMENT_SPRING) then
      nWYear,nSegment = Xdate.GetSegmentFromWest(nWYear, nWMonth, nWDate, nHour, nMinute)
      nSegmentDiff = Pm.TestNo24(nSegment - nFirstSegment)
      nMonth = nSegmentDiff / 2 + 1
    else
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
      nMonth = nEMonth
    end

    return nMonth
  end
  def Xdate.GetSegMonthFirstDateWithSegmentStr(nWYear, nWMonth, nWDate,nHour,nMinute)
    nWYear, nWMonth, nWDate, nHour, nMinute,nSegmentIndex = Xdate.GetSegMonthFirstDate(nWYear, nWMonth, nWDate,nHour,nMinute)
    segment_str = Xdate.GetSegmentStr(nSegmentIndex)
    return nWYear,nWMonth,nWDate, nHour, nMinute,segment_str
  end
  def Xdate.GetSegMonthFirstDateTimeWithSegmentStr(nWYear, nWMonth, nWDate,nHour,nMinute)
    nWYear, nWMonth, nWDate, nHour, nMinute,nSegmentIndex = Xdate.GetSegMonthFirstDate(nWYear, nWMonth, nWDate,nHour,nMinute)
    segment_str = Xdate.GetSegmentStr(nSegmentIndex)
    dt = "#{nWMonth}/#{nWDate} %02d:%02d" % [nHour, nMinute]
    return dt,segment_str
  end
  def Xdate.GetSegMonthFirstDateWithJieqiStr(nWYear, nWMonth, nWDate,nHour,nMinute)
    nWYear, nWMonth, nWDate, nHour, nMinute,nSegmentIndex = Xdate.GetSegFirstDate(nWYear, nWMonth, nWDate,nHour,nMinute)
    segment_str = Xdate.GetSegmentStr(nSegmentIndex)
    return nWYear,nWMonth,nWDate, nHour, nMinute,segment_str
  end
  def Xdate.GetSegMonthFirstDateTimeWithJieqiStr(nWYear, nWMonth, nWDate,nHour,nMinute)
    nWYear, nWMonth, nWDate, nHour, nMinute,nSegmentIndex = Xdate.GetSegFirstDate(nWYear, nWMonth, nWDate,nHour,nMinute)
    segment_str = Xdate.GetSegmentStr(nSegmentIndex)
    dt = "#{nWMonth}/#{nWDate} %02d:%02d" % [nHour, nMinute]
    return dt,segment_str
  end

  def Xdate.GetSegMonthFirstDate(nWYear, nWMonth, nWDate,nHour,nMinute)
    nWYear, nWMonth, nWDate, nHour, nMinute,nSegmentIndex = Xdate.GetSegFirstDate(nWYear, nWMonth, nWDate,nHour,nMinute)
    if (nSegmentIndex % 2 == 1) then
      nWYear,nWMonth,nWDate = Xdate.PreWDate(nWYear,nWMonth,nWDate)
      nWYear, nWMonth, nWDate, nHour, nMinute,nSegmentIndex = Xdate.GetSegFirstDate(nWYear, nWMonth, nWDate,nHour,nMinute)
    end
    return nWYear, nWMonth, nWDate, nHour, nMinute,nSegmentIndex
  end
  def Xdate.GetSegFirstDate(nWYear, nWMonth, nWDate,nHour,nMinute)
    nWYear,nSegmentIndex = Xdate.GetSegmentFromWest(nWYear, nWMonth, nWDate, nHour, nMinute)
    segTime = Xdate.GetSegDateThisYear(nWYear,nSegmentIndex)
    return nWYear,segTime[Xdate::ST_MONTH],segTime[Xdate::ST_DAY],segTime[Xdate::ST_HOUR],segTime[Xdate::ST_MINUTE],nSegmentIndex
  end

  def Xdate.GetSegDate(nWYear, nWMonth, nWDate,nHour,nMinute,nFirstSegment=nil)
    if (nFirstSegment == nil) then # 指太陰曆
      nEYear,nEMonth,nEDate,bLeapMonth = Xdate.West2East(nWYear,nWMonth,nWDate)
      nDate = nEDate
    else
      nWYear1, nWMonth1, nWDate1, nHour1, nMinute1,nSegmentIndex1 = Xdate.GetSegMonthFirstDate(nWYear, nWMonth, nWDate,nHour,nMinute)
      nDate = Xdate.GetDaysBetweenWDates(nWYear1,nWMonth1,nWDate1,nWYear,nWMonth,nWDate)
    end

    return nDate
  end


  def Xdate.GetWDaysThisYear2WinterSeg(nWYear)
    if (!Xdate.IsYearLegal?(nWYear)) then
      return 0,[0,0,0,0]
    end
      segTime = Xdate.GetSegDateThisYear(nWYear,Xdate::SEGMENT_WINTER)
      nDays = Xdate.GetWDaysThisYear(nWYear, segTime[Xdate::ST_MONTH], segTime[Xdate::ST_DAY])

      return nDays,segTime
    end
  def Xdate.GetWDaysThisYear2SpringSeg(nWYear)
    if (!Xdate.IsYearLegal?(nWYear)) then
      return 0,[0,0,0,0]
    end
      segTime = Xdate.GetSegDateThisYear(nWYear,Xdate::SEGMENT_SPRING)
      nSpringDays = Xdate.GetWDaysThisYear(nWYear, segTime[Xdate::ST_MONTH], segTime[Xdate::ST_DAY])

      return nSpringDays,segTime
    end

  def Xdate.GetSegDateThisYear(nWYear,nSegment)
    if (!Xdate.IsYearLegal?(nWYear)) then
      return [0,0,0,0]
    end
    Xdate.Init()

      nSegCountPerYear = Xdate::SEGMENT_COUNT_PER_YEAR
      nYearCount = nWYear - Xdate::BASE_YEAR
      nSegmentIndex = nYearCount * nSegCountPerYear + nSegment;
      segTime = @@SegTimeArray[nSegmentIndex]

      return segTime
    end
  def Xdate.GetSegWDateBySegYear(nSYear,nSMonth,nFirstSegment=nil)
    nSYear,nSegmentIndex = Xdate.SegMonth2SegmentIndex(nSYear,nSMonth,nFirstSegment)
    segTime = Xdate.GetSegDateThisYear(nSYear,nSegmentIndex)
    return nSYear,segTime[Xdate::ST_MONTH],segTime[Xdate::ST_DAY],segTime[Xdate::ST_HOUR],segTime[Xdate::ST_MINUTE]
  end
  # 取得節氣干支曆的八字
  def Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, nHour, nMinute,nFirstTimeType=nil,nFirstSegment=nil,nSegmentMove=Xdate::SegmentNow)
    if (!Xdate.IsWDateLegal?(nWYear,nWMonth,nWDate,nHour)) then
      return 0,0,0,0,0
    end
    nHour1,nMinute1 = Xdate.getSegHourMinute(nHour,nMinute,nSegmentMove)
    nGanZhiYear = Xdate.GetSegYearGanZhi(nWYear, nWMonth, nWDate,nHour1,nMinute1,nFirstSegment)
    nGanZhiMonth = Xdate.GetSegMonthGanZhi(nWYear, nWMonth, nWDate,nHour1,nMinute1,nFirstSegment)
    # 因施老師需求，換節氣改為動態 2016/11/13
    # nGanZhiYear = Xdate.GetSegYearGanZhi(nWYear, nWMonth, nWDate,23,59,nFirstSegment)
    # nGanZhiMonth = Xdate.GetSegMonthGanZhi(nWYear, nWMonth, nWDate,23,59,nFirstSegment)

      nEYear, nEMonth, nEDate, bLeapMonth = Xdate.West2East(nWYear, nWMonth, nWDate)
    nETime = Xdate.Hour2ETime(nHour)

      nGanZhiDay = Xdate.GetLunarDateGanZhi(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType)
      nGanZhiHour = Xdate.GetLunarTimeGanZhi(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType)

    nWYear,nCurSegment = Xdate.GetSegmentFromWest(nWYear, nWMonth, nWDate, nHour, nMinute)
    return nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment
  end
  # 八字排盤參數 加一個[節氣換柱]，三個選項,1、當日換月柱。2、過節氣換月柱，3、隔日換月柱. 
  def Xdate.getSegHourMinute(nHour,nMinute,nSegmentMove=Xdate::SegmentNow)
    # 過節氣換月柱
    nHour1,nMinute1 = nHour,nMinute
    if (nSegmentMove == Xdate::SegmentToday) then
      # 當日換月柱
      nHour1,nMinute1 = 23,59
    elsif (nSegmentMove == Xdate::SegmentTomorrow) then
      # 隔日換月柱
      nHour1,nMinute1 = 0,0
    end
      
    return nHour1,nMinute1
  end

  def Xdate.Wdate2SegmentDate(nWYear,nWMonth=1,nWDate=1,nWHour=23,nWMinute=59,nFirstSegment=nil)
    nSYear = Xdate.GetSegYear(nWYear, nWMonth, nWDate,nWHour,nWMinute,nFirstSegment)
    nSMonth = Xdate.GetSegMonth(nWYear, nWMonth, nWDate,nWHour,nWMinute,nFirstSegment)
    nSDate = Xdate.GetSegDate(nWYear, nWMonth, nWDate,nWHour,nWMinute,nFirstSegment)
    nSegmentIndex = (nSMonth - 1) * 2
    return nSYear,nSMonth,nSDate,nWHour,nWMinute,nSegmentIndex
  end
  def Xdate.SegmentDate2Wdate(nSYear,nSMonth=1,nSDate=1,nFirstSegment=nil)
    nSYear1,nSegmentIndex = Xdate.SegMonth2SegmentIndex(nSYear,nSMonth,nFirstSegment)
    segTime = Xdate.GetSegDateThisYear(nSYear1,nSegmentIndex)
    nWYear,nWMonth,nWDate = Xdate.NextNWDate(nSYear1,segTime[Xdate::ST_MONTH],segTime[Xdate::ST_DAY],nSDate - 1)
    return nWYear,nWMonth,nWDate,segTime[Xdate::ST_HOUR],segTime[Xdate::ST_MINUTE],nSegmentIndex
  end
  def Xdate.GetSegMonthDaysByWestDate(nWYear, nWMonth, nWDate,nHour,nMinute)
    nSYear,nSegmentIndex = Xdate.GetSegmentFromWest(nWYear, nWMonth, nWDate, nHour, nMinute)
    segTime = Xdate.GetSegDateThisYear(nSYear,nSegmentIndex)
    nWYear,nWMonth,nWDate,hour = nSYear,segTime[Xdate::ST_MONTH],segTime[Xdate::ST_DAY],segTime[Xdate::ST_HOUR]

    nSYear1,nSegmentIndex1 = Xdate.GetNextSegments(nSYear,nSegmentIndex,2)
    segTime1 = Xdate.GetSegDateThisYear(nSYear1,nSegmentIndex1)
    nWYear1,nWMonth1,nWDate1,hour1 = nSYear1,segTime1[Xdate::ST_MONTH],segTime1[Xdate::ST_DAY],segTime1[Xdate::ST_HOUR]
    nWYear1,nWMonth1,nWDate1 = Xdate.PreWDate(nWYear1,nWMonth1,nWDate1)

    return Xdate.GetDaysBetweenWDates(nWYear,nWMonth,nWDate,nWYear1,nWMonth1,nWDate1)
  end
  def Xdate.GetSegMonthDaysBySegDate(nSYear,nSMonth,nFirstSegment=nil)
    nSYear,nSegmentIndex = Xdate.SegMonth2SegmentIndex(nSYear,nSMonth,nFirstSegment)
    segTime = Xdate.GetSegDateThisYear(nSYear,nSegmentIndex)
    nWYear,nWMonth,nWDate,hour = nSYear,segTime[Xdate::ST_MONTH],segTime[Xdate::ST_DAY],segTime[Xdate::ST_HOUR]

    nSYear1,nSegmentIndex1 = Xdate.GetNextSegments(nSYear,nSegmentIndex,2)
    segTime1 = Xdate.GetSegDateThisYear(nSYear1,nSegmentIndex1)
    nWYear1,nWMonth1,nWDate1,hour1 = nSYear1,segTime1[Xdate::ST_MONTH],segTime1[Xdate::ST_DAY],segTime1[Xdate::ST_HOUR]
    nWYear1,nWMonth1,nWDate1 = Xdate.PreWDate(nWYear1,nWMonth1,nWDate1)

    return Xdate.GetDaysBetweenWDates(nWYear,nWMonth,nWDate,nWYear1,nWMonth1,nWDate1)
  end

  def Xdate.GetNextSegment(nSYear,nCurSeg)
    nNewSeg = Pm.TestNo24(nCurSeg + 1)
    if (nNewSeg == 0) then
      return nSYear + 1,nNewSeg
    else
      return nSYear,nNewSeg
    end
  end
  def Xdate.GetPreSegment(nSYear,nCurSeg)
    nNewSeg = Pm.TestNo24(nCurSeg - 1)
    if (nCurSeg == 0) then
      return nSYear - 1,nNewSeg
    else
      return nSYear,nNewSeg
    end
  end
  def Xdate.GetNextSegments(nSYear,nCurSeg,nSegCount)
    nSign = (nSegCount / nSegCount.abs)
    nSYear1 = nSYear
    nNewSeg = nCurSeg
    if (nSign == 1) then
      (0...nSegCount).each do |nSegmentDiff|
        nSYear1,nNewSeg = Xdate.GetNextSegment(nSYear1,nNewSeg)
      end
    else
      (0...nSegCount).each do |nSegmentDiff|
        nSYear1,nNewSeg = Xdate.GetPreSegment(nSYear1,nNewSeg)
      end
    end
    return nSYear1,nNewSeg
  end

  def Xdate.GetSegmentStrFromWest(nWYear, nWMonth, nWDate,nHour, nMinute)
      nWYear,nSegmentIndex = Xdate.GetSegmentFromWest(nWYear, nWMonth, nWDate, nHour, nMinute)
      if (nSegmentIndex == -1) then
#puts("nWYear:#{nWYear}, nWMonth:#{nWMonth}, nWDate:#{nWDate}, nHour:#{nHour}, nMinute:#{nMinute}")
        return ""
      end
      return Xdate.GetSegmentStr(nSegmentIndex)
  end

  def Xdate.GetSegmentStr(nSegmentIndex)
    nSegment = nSegmentIndex + 1
    return Pm.GetStr("IDS_S_SEGMENT_#{nSegment}")
  end

  def Xdate.GetSeg8Words_Str(nWYear, nWMonth,nWDate, nWHour,nWMinute,bSegment,nFirstTimeType=nil,nFirstSegment=nil,bSpace=true,nSegmentMove=Xdate::SegmentNow)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, nWHour, nWMinute,nFirstTimeType,nFirstSegment,nSegmentMove)
    sBuf = Xdate.ganzhi_date_str(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,bSpace)

    if (bSegment) then
      sBuf += "("
      sBuf += Xdate.GetSegmentStr(nCurSegment)
      sBuf += ")"
    end
    return sBuf
  end
  def Xdate.GetSeg8Words_Str2(nWYear, nWMonth,nWDate, nWHour,nWMinute,bSegment,nFirstTimeType=Xdate::FIRST_NEXT,nFirstSegment=Xdate::SEGMENT_SPRING,bSpace=true,nSegmentMove=Xdate::SegmentNow)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, nWHour, nWMinute,nFirstTimeType,nFirstSegment,nSegmentMove)
    sBuf = Xdate.ganzhi_date_str2(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,bSpace)

    if (bSegment) then
      sBuf += "("
      sBuf += Xdate.GetSegmentStr(nCurSegment)
      sBuf += ")"
    end
    return sBuf
  end
  def Xdate.GetSeg8Words_Str3(nWYear, nWMonth,nWDate, nWHour,nWMinute,nFirstTimeType=Xdate::FIRST_NEXT,nFirstSegment=Xdate::SEGMENT_SPRING,bSpace=true,nSegmentMove=Xdate::SegmentNow)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, nWHour, nWMinute,nFirstTimeType,nFirstSegment,nSegmentMove)
    sBuf = Xdate.ganzhi_date_str3(nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,bSpace)

    return sBuf
  end
  def Xdate.Get8WordsSeg_Str(nWYear, nWMonth,nWDate, nWHour,nWMinute,nFirstTimeType=nil,nFirstSegment=nil,nSegmentMove=Xdate::SegmentNow)
    nGanZhiYear,nGanZhiMonth,nGanZhiDay,nGanZhiHour,nCurSegment = Xdate.GetSeg8Words(nWYear, nWMonth,nWDate, nWHour, nWMinute,nFirstTimeType,nFirstSegment,nSegmentMove)
    return Xdate.GetSegmentStr(nCurSegment)
  end

  def Xdate.makeSegmentGanZhiStr(nWYear,nWMonth,nWDate,nWHour,nWMinute,bNeedSegment,nFirstTimeType=nil,nFirstSegment=nil,nSegmentMove=Xdate::SegmentNow)
    # sOut = Pm.GetStrWithQuote("IDS_S_UI_GANZHI_CAL")
    sOut = Xdate.GetSeg8Words_Str(nWYear, nWMonth,nWDate, nWHour,nWMinute,bNeedSegment,nFirstTimeType,nFirstSegment,false,nSegmentMove)
    return sOut
  end

  def Xdate.GetDaysAfterSegment(nWYear, nWMonth, nWDate,nSegmentIndex)
      segTime = Xdate.GetSegDateThisYear(nWYear,nSegmentIndex)
    nWYear1,nWMonth1,nWDate1 = nWYear,segTime[Xdate::ST_MONTH],segTime[Xdate::ST_DAY]
    nDays = Xdate.GetDaysBetweenWDates(nWYear1,nWMonth1,nWDate1,nWYear,nWMonth,nWDate)
    if (nDays < 0) then
        segTime = Xdate.GetSegDateThisYear(nWYear - 1,nSegmentIndex)
      nWYear1,nWMonth1,nWDate1 = nWYear - 1,segTime[Xdate::ST_MONTH],segTime[Xdate::ST_DAY]
      nDays = Xdate.GetDaysBetweenWDates(nWYear1,nWMonth1,nWDate1,nWYear,nWMonth,nWDate)
    end
    return nDays
  end

  def Xdate.GetSegDateGanzhi(nWYear, nWMonth,nWDate, nHour,nFirstTimeType=Xdate::FIRST_NEXT)
    nEYear, nEMonth, nEDate, bLeapMonth = Xdate.West2East(nWYear, nWMonth, nWDate)
    nETime = Xdate.Hour2ETime(nHour)

    nGanZhiDay = Xdate.GetLunarDateGanZhi(nEYear,nEMonth,nEDate,nETime,bLeapMonth,nFirstTimeType)

    return nGanZhiDay
  end

  def Xdate.leap_date_before_segment_date_check(wy, wm,wd, h,min)
    ey, em, ed, el = Xdate.West2East(wy, wm, wd)
    if (!el) then
      return false
    end
    y,m,d = Xdate.East2West(ey,em,20,el)
    sy, sm, sd, sh, smin,nSegmentIndex = Xdate.GetSegFirstDate(y,m,d,0,0)
    seg_days = Xdate.GetTotalWDaysFrom180111(sy, sm, sd)
    birth_days = Xdate.GetTotalWDaysFrom180111(wy, wm, wd)
    if (birth_days < seg_days) then
      return false
    elsif (birth_days == seg_days) then
      if (h < sh) then
        return false
      elsif (h == sh) then
        if (min < smin) then
          return false
        else
          return true
        end
      else
        return true
      end
    else
      return true
    end
  end

  def Xdate.GetSeasonBySegment(nWYear, nWMonth, nWDate)
    nMonth = Xdate.GetSegMonth(nWYear, nWMonth, nWDate,23,59,Xdate::SEGMENT_SPRING)
    nSeason = (nMonth - 1) / 3
    return nSeason
  end
  def Xdate.GetSeasonBySegmentStr(nWYear, nWMonth, nWDate)
    nSeason = Xdate.GetSeasonBySegment(nWYear, nWMonth, nWDate)
    s = ["chun","xia","qiu","dong"]
    return Pm.t("ji_jie.#{s[nSeason]}")
  end

end
