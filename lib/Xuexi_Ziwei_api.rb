class <PERSON><PERSON><PERSON>_<PERSON>wei
  def <PERSON><PERSON><PERSON>_Ziwei.rand_timestampsex(y=nil,m=nil,d=nil,h=nil,sex=nil)
    return Xdate.rand_timestampsex(y,m,d,h,sex)
  end
  def Xu<PERSON><PERSON>_Ziwei.next_timestampsex(ts)
    return Xdate.next_timestamp(ts)
  end
  def Xuexi_Ziwei.api_check_xing(xing)
    xing_type = ""
    xing_id = 0
    if (xing.include?("astar")) then
        xing_type = "astar"
        xing_id = xing.sub("astar","").to_i
    elsif (xing.include?("bstar")) then
        xing_type = "bstar"
        xing_id = xing.sub("bstar","").to_i
    elsif (xing.include?("doctor")) then
        xing_type = "doctor"
        xing_id = xing.sub("doctor","").to_i
    elsif (xing.include?("yeargod")) then
        xing_type = "yeargod"
        xing_id = xing.sub("yeargod","").to_i
    elsif (xing.include?("yearstar")) then
        xing_type = "yearstar"
        xing_id = xing.sub("yearstar","").to_i
    elsif (xing.include?("god")) then
        xing_type = "god"
        xing_id = xing.sub("god","").to_i
    end

    return xing_type,xing_id
  end
  def Xuexi_Ziwei.api_xl_find_timestamp(nPanType,xing,gong,shengke)
    ts = Xuexi_Ziwei.rand_timestampsex()
    hUserDefData = Hash.new
    hParAll = nil
    hUserInfo = Xuexi_Ziwei.set_pan_user_info(ts)
    oStar = Star.new(hUserInfo,hUserDefData,hParAll)

    (0..1000).each do |i|
      hUserInfo = Xuexi_Ziwei.set_pan_user_info(ts)
      oStar.resetUserInfo(nPanType,hUserInfo)
      bfind = Xuexi_Ziwei.api_xing_in_gong(oStar,nPanType,xing,gong,shengke)
      if (bfind) then
        return ts
      end
      ts = Xuexi_Ziwei.next_timestampsex(ts)
    end
  end
  def Xuexi_Ziwei.api_xing_in_gong(oStar,nPanType,xing,gong,shengke)
    gong_dizhi = oStar.g_House_GetEarth(nPanType,gong)
    xing_type,xing_id = Xuexi_Ziwei.api_check_xing(xing)
    if (xing_type == "astar") then
      xing_gong_dizhi = oStar.gHouse_GetAStar_HouseEarth(nPanType,xing_id)
    elsif (xing_type == "bstar") then
      xing_gong_dizhi = oStar.gHouse_GetBStar_HouseEarth(nPanType,xing_id)
    end

    if (gong_dizhi != xing_gong_dizhi) then
        return false
    end
    if (xing_type == "astar") then
      nSanKur = oStar.gExHouse_GetAStarSanKur(nPanType,xing_gong_dizhi,xing_id)
    elsif (xing_type == "bstar") then
      nSanKur = gExHouse_GetBStarSanKur(nPanType,xing_gong_dizhi,xing_id)
    end
    n = nSanKur / 2
    shengke_all = ["sheng","ke","wu"]
    return (shengke_all[n] == shengke)
  end
  def Xuexi_Ziwei.api_xing_in_gong_shuoming(oStar,nPanType,xing,gong)
    xing_type,xing_id = Xuexi_Ziwei.api_check_xing(xing)
    gong_dizhi = oStar.g_House_GetEarth(nPanType,gong)
    if (xing_type == "astar") then
      xing_gong_dizhi = oStar.gHouse_GetAStar_HouseEarth(nPanType,xing_id)
      sSanKur = oStar.gExHouse_GetAStarSanKurName(nPanType,xing_gong_dizhi,xing_id)
      sStarName = Star.GetAStarName(xing_id)
    elsif (xing_type == "bstar") then
      xing_gong_dizhi = oStar.gHouse_GetBStar_HouseEarth(nPanType,xing_id)
      sSanKur = oStar.gExHouse_GetBStarSanKurName(nPanType,xing_gong_dizhi,xing_id)
      sStarName = Star.GetBStarName(xing_id)
    end
    e = Explain.new
    yanyishuoming = Xuexi_Ziwei.xuexi_xing_shuoming(oStar,e,sSanKur,sStarName,nPanType,xing_gong_dizhi)
    neirong = Xuexi_Ziwei.api_xing_neirong("yanyishuoming", :yanyishuoming => yanyishuoming)
    taitou = Xuexi_Ziwei.api_xing_taitou("yanyishuoming")
    return Xuexi_Ziwei.api_xing_taitou_neirong(taitou,neirong)
  end
  def Xuexi_Ziwei.api_xing_mingzi(oStar,nPanType,xing,gong)
    xing_type,xing_id = Xuexi_Ziwei.api_check_xing(xing)
    if (xing_type == "astar") then
      sStarName = Star.GetAStarName(xing_id)
    elsif (xing_type == "bstar") then
      sStarName = Star.GetBStarName(xing_id)
    end
    gong_dizhi = oStar.g_House_GetEarth(nPanType,gong)
    wuxing = Xuexi_Ziwei.api_xing_wuxing(oStar,nPanType,gong_dizhi,xing_id,xing_type)
    xing_wuxing = Five.GetFiveStr(wuxing)
    neirong = Xuexi_Ziwei.api_xing_neirong("xingyao", :xingyao => sStarName)
    taitou = Xuexi_Ziwei.api_xing_taitou("xingyao")
    return Xuexi_Ziwei.api_xing_taitou_neirong(taitou,neirong)
    # return sStarName,xing_wuxing
  end
  def Xuexi_Ziwei.api_gongwei_mingzi(gong)
    neirong = Xuexi_Ziwei.api_xing_neirong("gongwei", :gongwei => Star.GetHouseName(gong))
    taitou = Xuexi_Ziwei.api_xing_taitou("gongwei")
    return Xuexi_Ziwei.api_xing_taitou_neirong(taitou,neirong)
  end
  def Xuexi_Ziwei.api_xing_gongqishenke(oStar,nPanType,xing,gong)
    # 宮氣生剋：甲午五行屬「金」，星曜生宮氣。
    gong_dizhi = oStar.g_House_GetEarth(nPanType,gong)
    ganzhi = oStar.gHouse_GetHouseSkyEarthName(nPanType,gong_dizhi)
    gong_wuxing = oStar.gHouse_FiveStr(nPanType,gong_dizhi)
    xinggongshenke = Xuexi_Ziwei.api_xing_xinggongshenke(oStar,nPanType,xing,gong)
    # gongqishuoming: "%{ganzhi}五行屬「%{wuxing}」，%{xinggongshenke}。"
    neirong = Xuexi_Ziwei.api_xing_neirong("gongqishengke",:ganzhi => ganzhi, :wuxing => gong_wuxing, :xinggongshenke => xinggongshenke)
    taitou = Xuexi_Ziwei.api_xing_taitou("gongqishengke")
    return Xuexi_Ziwei.api_xing_taitou_neirong(taitou,neirong)
  end
  def Xuexi_Ziwei.api_xing_xinggongshenke(oStar,nPanType,xing,gong)
    xing_type,xing_id = Xuexi_Ziwei.api_check_xing(xing)
    gong_dizhi = oStar.g_House_GetEarth(nPanType,gong)
    gong_wuxing = oStar.cp_getHouseFive(nPanType,gong_dizhi)
    xing_wuxing = Xuexi_Ziwei.api_xing_wuxing(oStar,nPanType,gong_dizhi,xing_id,xing_type)
    return Xuexi_Ziwei.api_xing_gong_shengke(oStar,gong_wuxing,xing_wuxing)
  end
  def Xuexi_Ziwei.api_xing_wuxing(oStar,nPanType,gong_dizhi,xing_id,xing_type)
    wuxing = -1
    if (xing_type == "astar") then
      wuxing = oStar.gHouse_GetAStarFive(nPanType,gong_dizhi,xing_id)
    elsif (xing_type == "bstar") then
      wuxing = oStar.gHouse_GetBStarFive(nPanType,gong_dizhi,xing_id)
    elsif (xing_type == "doctor") then
      wuxing = oStar.gHouse_GetDoctorFive(xing_id)
    elsif (xing_type == "yearstar") then
      wuxing = oStar.gHouse_GetYearStarFive(xing_id)
    elsif (xing_type == "yeargod") then
      wuxing = oStar.gHouse_GetYearGodFive(xing_id)
    elsif (xing_type == "god") then
      wuxing = oStar.gHouse_GetGodFive(xing_id)
    end
    return wuxing
  end
  # A、宮氣生星曜，B、星曜生宮氣，C、宮氣與星曜相同，D、宮氣剋星曜，E、星曜剋宮氣
  def Xuexi_Ziwei.api_xing_gong_shengke(oStar,gong_wuxing,xing_wuxing)
    shengke = oStar.g_GetFiveSanKur2(gong_wuxing,xing_wuxing)
    a = ["A","B","C","D","E"]
    if (shengke == -10) then
      return ""
    elsif (shengke == 0) then
      return Xuexi_Ziwei.api_xing_neirong("xinggongshenke_C")
    elsif ([-1,-4].include?(shengke)) then
      return Xuexi_Ziwei.api_xing_neirong("xinggongshenke_A")
    elsif ([1,4].include?(shengke)) then
      return Xuexi_Ziwei.api_xing_neirong("xinggongshenke_B")
    elsif ([-2,3].include?(shengke)) then
      return Xuexi_Ziwei.api_xing_neirong("xinggongshenke_D")
    elsif ([2,-3].include?(shengke)) then
      return Xuexi_Ziwei.api_xing_neirong("xinggongshenke_E")
    end
    return ""
  end
  def Xuexi_Ziwei.api_xing_taitou(s,*arg)
    return Pm.t("ziwei.api.xing.taitou.#{s}",*arg)
  end
  def Xuexi_Ziwei.api_xing_neirong(s,*arg)
    return Pm.t("ziwei.api.xing.neirong.#{s}",*arg)
  end
  def Xuexi_Ziwei.api_xing_taitou_neirong(taitou,neirong)
    return Pm.t("ziwei.api.xing.taitouneirong", :taitou => taitou, :neirong => neirong)
    # return taitou,neirong
  end
  def Xuexi_Ziwei.api_xing_neirong_jibentexing(s,*arg)
    return Pm.t("ziwei.api.xing.neirong.jibentexings.#{s}",*arg)
  end
  def Xuexi_Ziwei.api_xing_neirong_yunyongtezheng(s,*arg)
    return Pm.t("ziwei.api.xing.neirong.yunyongtezhengs.#{s}",*arg)
  end
  def Xuexi_Ziwei.api_xing_jingyu()
    return Pm.t("ziwei.api.xing.jingyu")
  end

  def Xuexi_Ziwei.api_xing_jibentexing(oStar,nPanType,xing,gong)
    # 基本特性：
    # 屬土，陽，化蔭，南斗第二星，化氣為「蔭」，主壽星，司壽之星、為福壽、主吉祥，能有解厄制化功能。
    jibentexing = Xuexi_Ziwei.api_xing_neirong_jibentexing(xing)
    neirong = Xuexi_Ziwei.api_xing_neirong("jibentexing", :jibentexing => jibentexing)
    taitou = Xuexi_Ziwei.api_xing_taitou("jibentexing")
    return Xuexi_Ziwei.api_xing_taitou_neirong(taitou,neirong)
  end
  def Xuexi_Ziwei.api_xing_yunyongtezheng(oStar,nPanType,xing,gong)
    # 運用特徵：
    # 「長壽」、「逢凶化吉」、「清高」、「專制」、「不受約束」、「醫藥」、「耿直」、「權謀」。
    yunyongtezheng = Xuexi_Ziwei.api_xing_neirong_yunyongtezheng(xing)
    neirong = Xuexi_Ziwei.api_xing_neirong("yunyongtezheng", :yunyongtezheng => yunyongtezheng)
    taitou = Xuexi_Ziwei.api_xing_taitou("yunyongtezheng")
    return Xuexi_Ziwei.api_xing_taitou_neirong(taitou,neirong)
  end
end
