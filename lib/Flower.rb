require("GNum.rb")
require("Flower_Explain.rb")
require("Flower_GuaYao.rb")

class Flower
	# 問事項目、方位數值、時間數值、亂數值。
	# 問事項目:  愛情：有對象  無對象
	# 		  婚姻：已婚  未婚
	# 		  事業：經營者  就業者
	# 		  財運：有投資  無投資
	# 		  股市：有持股  無持股
	# 		  學業：功課  考試
	# 方位數值：在依上圖中按到何方位，傳回數值。數值如上表所示。例如按到西北方則數值為1 回傳值為1
	# 時間數值：以現在時間以十二地支回傳數值。子1、丑2、寅3、卯4、辰5、巳6、午7、未8、申9、酉10、戌11、亥12， 例如晚上 22:10 則亥時回傳數值12. 
	# 亂數值：取小數第二數值，例0.28487 第二位為回傳值8。

	def Flower.create(ask_no,any_val,dir_val,time_val,lang="zh-CN")
		ask_no = ask_no.to_i
		dir_val = dir_val.to_i
		time_val = time_val.to_i
		any_val = any_val.to_i

		up = Pm.TestNoN(any_val,8)
		down = Pm.TestNoN(dir_val,8)
		bian = Pm.TestNoN(dir_val + time_val + any_val,6)
		no = "#{up}#{down}#{bian}"

		hOut = {}
		# hOut["no"] = no
		hOut["Yao_Content"] = Flower.find_Yao_Content(no,lang)

		hExplain = Flower.find_Explain(ask_no,lang)
		a_JiXiong = GNum.find_JiXiong(no)
    	hOut["process_jixiong"] = Flower.find_Process_JiXiong(a_JiXiong)

		aProcess = GNum.JiXiong_to_Process(a_JiXiong)
    	hOut["process_explain"] = Flower.find_Process_Explain(hExplain,aProcess)
  		aMean = GNum.find_Mean(no)
    	luck = aMean[GNum::Mean_LUCK]
    	hOut["luck"] = luck
    	# hOut["luckId"] = GNum.getLuckId(luck)
    	hOut["ask_no"] = ask_no
    	hOut["any_val"] = any_val
    	hOut["dir_val"] = dir_val
    	hOut["time_val"] = time_val
    	hOut["ask"] = Pm.t("flower.ask.ask_#{ask_no}")

		return hOut
	end

	def Flower.gua(up,down,bian,lang="zh-CN")
		hOut = {}
		no = "#{up}#{down}#{bian}"
		hOut["Yao_Content"] = Flower.find_Yao_Content(no,lang)
		hOut["Gua"] = "#{Pm.t("flower.gua.gb_#{up}")}#{Pm.t("flower.gua.gb_#{down}")}#{hOut["Yao_Content"]["gua"]}"
		return hOut
	end	
end