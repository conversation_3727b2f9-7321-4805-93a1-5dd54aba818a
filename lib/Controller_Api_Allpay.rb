require("Xdate.rb")
require("WeenApi.rb")
require("AllpayConstant.rb")

module Controller_Api_Allpay
  # 歐付寶 資料 放在 資料庫 System_par中，若沒有就傳正式環境，方便在staging中測兩種環境

  # 測試環境
  # @@MerchantID_Allpay = "2000132"
  # @@Allpay_Server_AioCheckOut = "http://payment-stage.allpay.com.tw/Cashier/AioCheckOut"  # test server
  # @@Allpay_HashKey               = "5294y06JbISpM5x9"
  # @@Allpay_HashIV               = "v77hoKGq4kWxNNIS"

# ALL IN ONE 介接 HashKey 5294y06JbISpM5x9
# ALL IN ONE 介接 HashIV  v77hoKGq4kWxNNIS
# 一般金流 介接 HashKey ejCk326UnaZWKisg
# 一般金流 介接 HashIV  q9jcZX8Ib9LM8wYk
# 物流 介接 HashKey 5294y06JbISpM5x9
# 物流 介接 HashIV  v77hoKGq4kWxNNIS

  # # 正式環境(請注意:正式機為 https,測試機則為 http)
  # @@MerchantID_Allpay = "1118704"
  # @@Allpay_Server_AioCheckOut = "https://payment.allpay.com.tw/Cashier/AioCheckOut"
  # @@Allpay_HashKey               = "iB63SMrLc5lhtV1X"
  # @@Allpay_HashIV               = "1m1kmLvIv4EjZCFX"

# 20180719
# 正式環境：https://payment.opay.tw/Cashier/AioCheckOut/V5
# 測試環境：https://payment-stage.opay.tw/Cashier/AioCheckOut/V5

# 歐付寶
# Allpay_HashKey S1r3AL1twrke90Qj
# Allpay_Server_AioCheckOut https://payment.ecpay.com.tw/Cashier/AioCheckOut/V2
# Allpay_HashIV nOI9QNfC9Tr99VTJ
# MerchantID_Allpay 3003886


# 綠界
# Allpay_HashKey wtI3d8aDnCWn6w52
# Allpay_Server_AioCheckOut ：https://payment.ecpay.com.tw/Cashier/AioCheckOut/V5
# Allpay_HashIV hUdjwSz9TuHfo7is
# MerchantID_Allpay  3049793
  # # 正式環境(請注意:正式機為 https,測試機則為 http)
  # @@MerchantID_Allpay = "3049793"
  # @@Allpay_Server_AioCheckOut = "https://payment.ecpay.com.tw/Cashier/AioCheckOut/V5"
  # @@Allpay_HashKey               = "wtI3d8aDnCWn6w52"
  # @@Allpay_HashIV               = "hUdjwSz9TuHfo7is"
 
  # 王文華個人戶頭
  @@MerchantID_Allpay = "3003886"
  @@Allpay_Server_AioCheckOut = "https://payment.ecpay.com.tw/Cashier/AioCheckOut/V5"
  @@Allpay_HashKey               = "S1r3AL1twrke90Qj"
  @@Allpay_HashIV               = "nOI9QNfC9Tr99VTJ"

  def allpay_get_checkout_data(hPar)
    h = Hash.new
    h["allpay_aiocheckout_server"] = getAllpayServer_AioCheckOut()
    allpay = allpay_AioCheckOut_hash(hPar)
    allpay_keys = allpay.keys
    a = Array.new
    allpay_keys.each do |key|
      a.push({ "name" => key,"value" => allpay[key]})
    end
    h["allpay"] = a
    # res = send_to_allpay_formdata(h[:allpay_aiocheckout_server],h[:allpay])
    # h[:auto_post_html] = res
    return h
  end
  def getMerchantNumber()
    sData = Pm.getSystemValue("MerchantID_Allpay")
    if (sData != "") then
      return sData
    else
      return @@MerchantID_Allpay
    end
  end

  def getAllpayServer_AioCheckOut()
    sData = Pm.getSystemValue("Allpay_Server_AioCheckOut")
    if (sData != "") then
      return sData
    else
      return @@Allpay_Server_AioCheckOut
    end
  end

  def getAllpay_HashKey()
    sData = Pm.getSystemValue("Allpay_HashKey")
    if (sData != "") then
      return sData
    else
      return @@Allpay_HashKey
    end
  end

  def getAllpay_HashIV()
    sData = Pm.getSystemValue("Allpay_HashIV")
    if (sData != "") then
      return sData
    else
      return @@Allpay_HashIV
    end
  end
  # 特殊字元置換
  def replaceChar(value)
    # a = "kad%2dfde%5f"
    search_list = ['%2d', '%5f', '%2e', '%21', '%2a', '%28', '%29', '%20']
    replace_list = ['-', '_', '.', '!', '*', '(', ')', '+']

    # zipped = search_list.zip(replace_list)
    # # => [['%2d', '-'], ['%5f', '_'], ['%2e', '.'],...]
    # replace_hash = Hash[zipped]
    # value = value.gsub(/%\w\w/,replace_hash)
    # %22會被替換成空的，所以不能用

    search_list.each_index { |i| value.gsub!(search_list[i],replace_list[i])}

    return value
  end
  # 產生檢查碼
  def getMacValue(h,hash_key=nil, hash_iv=nil)
    hash_key = getAllpay_HashKey() if hash_key == nil
    hash_iv = getAllpay_HashIV() if hash_iv == nil

    encode_str = "HashKey=" + hash_key
    keys = h.keys.sort
    keys.each { |x| encode_str = "#{encode_str}&#{x}=#{h[x]}"}
    encode_str = encode_str + "&HashIV=" + hash_iv
    # Pm.saveTestDb("MacValue original:",encode_str.clone)

    encode_str = URI.encode(encode_str) # 先編碼中文
    encode_str = URI.encode(encode_str,"=&/:@?, ") # 特殊符號編碼

    encode_str = replaceChar(encode_str) # 幾個特別的不需要編碼，並將空白變成＋
    # Pm.saveTestDb("MacValue original url encode:",encode_str.clone)

    encode_str.downcase!
    # Pm.saveTestDb("MacValue original downcase:",encode_str.clone)

    s = Digest::MD5.hexdigest(encode_str)
    # s = Digest::SHA256.hexdigest encode_str
# puts "MacValue original replaceChar: #{encode_str}"
    s.upcase!

    return s
  end
  def allpay_unset(h,key)
    # h[key] = ''
    h.delete(key)
  end
  def allpay_AioCheckOut_datacheck(h)
    if (allpay_AioCheckOut_datacheck_default() == false) then
      return false
    end
    if (allpay_AioCheckOut_datacheck_eachpayment() == false) then
      return false
    end
    return true
  end
  def allpay_AioCheckOut_datacheck_default(hSend)
    if (hSend["ReturnURL"] == 0) then return false end
    if (hSend["ClientBackURL"].length > 200) then return false end
    if (hSend["OrderResultURL"].length > 200) then return false end
    if (hSend["MerchantTradeNo"].length == 0) then return false end
    if (hSend["MerchantTradeNo"].length > 20) then return false end
    if (hSend["MerchantTradeDate"].length == 0) then return false end
    if (hSend["TotalAmount"].length == 0) then return false end
    if (hSend["TradeDesc"].length == 0) then return false end
    if (hSend["TradeDesc"].length > 200) then return false end
    if (hSend["ChoosePayment"].length == 0) then return false end
    if (hSend["NeedExtraPaidInfo"].length == 0) then return false end
    if (hSend["DeviceSource"].length == 0) then return false end
    # if (sizeof(hSend["Items"]) == 0) then
    #     array_push($arErrors, 'Items is required.');
    # end
    return true
  end
  def allpay_AioCheckOut_datacheck_eachpayment(hSend,hSendExtend)
    # 檢查 Alipay 條件。
    if (hSend["ChoosePayment"] == Allpay_ChoosePayment::Alipay) then
      if (hSendExtend["Email"].length == 0) then return false end
      if (hSendExtend["Email"].length > 200) then return false end
      if (hSendExtend["PhoneNo"].length == 0) then return false end
      if (hSendExtend["PhoneNo"].length > 20) then return false end
      if (hSendExtend["UserName"].length == 0) then return false end
      if (hSendExtend["UserName"].length > 20) then return false end
    end
    # 檢查產品名稱。
    if (hSend["ItemName"].length == 0) then
      return false
    end
    return true
  end
  def allpay_AioCheckOut_hash(h)
    # ServiceURL
    hSend = allpay_AioCheckOut_hash_Send(h)
    hSendExtend = allpay_AioCheckOut_hash_SendExtend(h,hSend)

    hAll = Hash.new
    hAll.merge!(hSend)
    hAll.merge!(hSendExtend)

    # for test when mac value error
    # hAll["MerchantTradeNo"] = "ween2508"
    # hAll["MerchantTradeDate"] = "2016/02/08 00:18:29"
    # hAll["ClientBackURL"] = "http://staging.meen.tw/zh-TW/ifate/customer/index?ap_name=star"
    # hAll["ReturnURL"] = "http://staging.ween.tw//api/v1/allpay_return"

    # hAll["Remark"] = "http://staging.meen.tw/zh-TW/ifate/customer/index?ap_name=star"
    # hAll["TradeDesc"] = "紫微斗數專家版 排盤服務(1個月)"
    # hAll["ItemName"] = "紫微斗數專家版 排盤服務(1個月)"
    # hAll["TotalAmount"] = "200"
    # for test end

    # if (hSend["InvoiceMark"] == Allpay_InvoiceState::Yes) then
    # end
    puts "hAll : #{hAll}"
    macvalue = getMacValue(hAll)
    puts "macvalue : #{macvalue}"
    hAll["CheckMacValue"] = macvalue
    return hAll
  end
  def allpay_merchanttradeno_encode(order_id)
    pre = Pm.getSystemValue("pre_order_words")
    pre = "ween" if (pre == "")
    return "#{pre}#{order_id}"
  end
  def allpay_merchanttradeno_decode(merchanttradeno)
    pre = Pm.getSystemValue("pre_order_words")
    pre = "ween" if (pre == "")
    s = merchanttradeno.gsub(pre,"")
    return s.to_i
  end
  def allpay_merchanttradeno_decode_hash(hPars)
    return allpay_merchanttradeno_decode(hPars["MerchantTradeNo"])
  end
  def allpay_modify_for_item(s)
    # because varchar(200)
    a = s.split("：")
    itemName = ""
    if (a.length == 0) then
      itemName = "pro58 product"
    else
      itemName = a[0]
    end
    return itemName.slice(0,30)
  end
  def allpay_AioCheckOut_hash_Send(h)
    return_url = Pm.getWeenBillingServer("/api/v1/allpay_return")
    itemName = allpay_modify_for_item(h["ItemName"])
    choosepayment = Allpay_ChoosePayment.check_choosepayment(h["ChoosePayment"])
    merchanttradeno = allpay_merchanttradeno_encode(h["order_id"])
    merchanttradedate = Allpay_date.datetime_now()
    clientbackurl = h["receive_url"]

    # for test only
    # choosepayment = Allpay_ChoosePayment::BARCODE
    # Credit:信用卡  成功
    # WebATM:網路 ATM  成功
    # ATM:自動櫃員機 成功
    # CVS:超商代碼 成功
    # BARCODE:超商條碼 成功
    # Alipay:支付寶  需要 email,phone,username
    # Tenpay:財付通
    # TopUpUsed:儲值消費
    # merchanttradeno = "ween2286"
    # merchanttradedate = "2015/11/20 20:41:08"
    # clientbackurl = "/6941E52C284C43C8A44DE412696D78C3538245B57052473DA7BDE3D142D5B204"
    # for tet only
    choosesubpayment = Allpay_ChoosePayment.check_choosesubpayment(choosepayment,h["ChooseSubPayment"])
    hSend = {
            "MerchantID" => getMerchantNumber(),
            "MerchantTradeNo" => merchanttradeno,
            "MerchantTradeDate" => merchanttradedate, # yyyy/MM/dd HH:mm:ss
            "PaymentType" => 'aio',
            "TotalAmount" => h["TotalAmount"],
            "TradeDesc" => allpay_modify_for_item(h["TradeDesc"]), # allpay 商城購物
            "ItemName" => itemName, # h["ItemName"] 手機 20 元 X2#隨身碟 60 元 X1
            "ReturnURL" => return_url, # 當消費者付款完成後, 會將付款結果以 server 端幕後方式,回傳到該 網址 http://www.allpay.com.tw/ receive.php
            "ChoosePayment" => choosepayment, # Credit:信用卡 WebATM:網路 ATM ATM:自動櫃員機 CVS:超商代碼 BARCODE:超商條碼 Alipay:支付寶 Tenpay:財付通 TopUpUsed:儲值消費 ALL:不指定付款方式
            "ClientBackURL" => clientbackurl, # http://www.allpay.com.tw/ Shopping/Detail 先為空
            "ItemURL" => '',
            "Remark" => "#{clientbackurl}",
            "ChooseSubPayment" => choosesubpayment,
            "OrderResultURL" => '' # http://www.allpay.com.tw/ client.php 先為空
# 底下之前不送
            # "NeedExtraPaidInfo" => Allpay_ExtraPaymentInfo::No, # 先為空
            # "DeviceSource" => Allpay_DeviceSource.check(h["DeviceSource"]),
            # "IgnorePayment" => "",  # ATM#WebATM
            # "PlatformID" => '',  # 特約合作平台 商代號(由 allpay 提供)
            # "InvoiceMark" => Allpay_InvoiceState::No,
            # "HoldTradeAMT" => '0',  # 買方付款完成後,歐付 寶依合約約定之時間, 撥款給廠商(此為預設 值)。
            # "AllPayID" => '',
            # "AccountID" => '',
            # "EncryptType" => Allpay_EncryptType::MD5
    }

    return hSend
  end
  # 延伸參數。
  def allpay_AioCheckOut_hash_SendExtend(h,hSend)
    hSendExtend = Hash.new
    # if (hSend["ChoosePayment"] == Allpay_ChoosePayment::Alipay || hSend["ChoosePayment"] == "ALL") then
    if (hSend["ChoosePayment"] == Allpay_ChoosePayment::Alipay) then
      hSendExtend = allpay_AioCheckOut_hash_SendExtend_Alipay(h,hSend)
    elsif (hSend["ChoosePayment"] == Allpay_ChoosePayment::ATM) then
      hSendExtend = allpay_AioCheckOut_hash_SendExtend_ATM(h,hSend)
    elsif ([Allpay_ChoosePayment::CVS,Allpay_ChoosePayment::BARCODE].include?(hSend["ChoosePayment"])) then
      hSendExtend = allpay_AioCheckOut_hash_SendExtend_Cvs_Barcode(h,hSend)
    elsif (hSend["ChoosePayment"] == Allpay_ChoosePayment::Tenpay) then
      hSendExtend = allpay_AioCheckOut_hash_SendExtend_Tenpay(h,hSend)
    elsif (hSend["ChoosePayment"] == Allpay_ChoosePayment::Credit) then
      hSendExtend = allpay_AioCheckOut_hash_SendExtend_Credit(h,hSend)
    elsif (hSend["ChoosePayment"] == Allpay_ChoosePayment::Credit_Period) then
      hSendExtend = allpay_AioCheckOut_hash_SendExtend_Credit_Period(h,hSend)
    elsif (hSend["ChoosePayment"] == Allpay_ChoosePayment::ALL) then
      # hSendExtend = allpay_AioCheckOut_hash_SendExtend_Alipay(h,hSend)
      # hSendExtend = allpay_AioCheckOut_hash_SendExtend_ATM(h,hSend)
      # hSendExtend.merge!(allpay_AioCheckOut_hash_SendExtend_Alipay(h,hSend))
      # hSendExtend.merge!(allpay_AioCheckOut_hash_SendExtend_Cvs_Barcode(h,hSend))
      # hSendExtend.merge!(allpay_AioCheckOut_hash_SendExtend_Tenpay(h,hSend))
      # hSendExtend.merge!(allpay_AioCheckOut_hash_SendExtend_Credit(h,hSend))
      # hSendExtend.merge!(allpay_AioCheckOut_hash_SendExtend_Credit_Period(h,hSend))
    else
      hSendExtend = Hash.new
    end

    # 當 InvoiceMark 參數為 Y 付款完成後開立電子發票時代入下列參數
    if (hSend["InvoiceMark"] == Allpay_InvoiceState::Yes) then
      hSendExtend["RelateNumber"] = hSend["MerchantTradeNo"]
      hSendExtend["CustomerID"] = ""
      hSendExtend["CustomerIdentifier"] = ""  # 統一編號
      hSendExtend["CustomerName"] = ""
      hSendExtend["CustomerAddr"] = ""
      hSendExtend["CustomerPhone"] = ""
      hSendExtend["CustomerEmail"] = ""
      hSendExtend["ClearanceMark"] = ""
      hSendExtend["TaxType"] = ""
      hSendExtend["CarruerType"] = ""
      hSendExtend["CarruerNum"] = ""
      hSendExtend["Donation"] = ""
      hSendExtend["LoveCode"] = ""
      hSendExtend["Print"] = ""
      hSendExtend["InvoiceItemName"] = ""
      hSendExtend["InvoiceItemCount"] = ""
      hSendExtend["InvoiceItemWord"] = ""
      hSendExtend["InvoiceItemPrice"] = ""
      hSendExtend["InvoiceItemTaxType"] = ""
      hSendExtend["InvoiceRemark"] = ""
      hSendExtend["DelayDay"] = ""
      hSendExtend["InvType"] = ""
    end
    return hSendExtend
  end
  def allpay_AioCheckOut_hash_SendExtend_ATM(h,hSend)
    paymentinfo_url = Pm.getWeenBillingServer("/api/v1/allpay_paymentinfo")
    hSendExtend = {
      "ExpireDate" => 3,
      "PaymentInfoURL" =>  paymentinfo_url, # http://www.allpay.com.tw/ paymentinfo.php
      "ClientRedirectURL" => "",
    }

    return hSendExtend
  end

  def allpay_AioCheckOut_hash_SendExtend_Cvs_Barcode(h,hSend)
    paymentinfo_url = Pm.getWeenBillingServer("/api/v1/allpay_paymentinfo")
    hSendExtend = {
      "StoreExpireDate" => "",  # 超商繳費截止 時間 CVS:以分為計算單位 BARCODE:以天為計算 單位
      "Desc_1" => hSend["ItemName"].slice(0,200),
      "Desc_2" => "",
      "Desc_3" => "",
      "Desc_4" => "",
      "PaymentInfoURL" =>  paymentinfo_url, # http://www.allpay.com.tw/ paymentinfo.php
      "ClientRedirectURL" => ""
    }

    return hSendExtend
  end

  def allpay_AioCheckOut_hash_SendExtend_Alipay(h,hSend)
    hSendExtend = {
      "AlipayItemName" => hSend["ItemName"].slice(0,200),
      "AlipayItemCounts" => "1",
      "AlipayItemPrice" => hSend["TotalAmount"],
      "Email" => h["email"],
      "PhoneNo" => h["phoneno"],
      "UserName" => h["username"]
      # "Email" => "peter@gmail",
      # "PhoneNo" => "0911101991",
      # "UserName" => "peter"
    }

    return hSendExtend
  end
  def allpay_AioCheckOut_hash_SendExtend_Tenpay(h,hSend)
    hSendExtend = { "ExpireTime" => "" } # 格式為 yyyy/MM/dd HH:mm:ss。只能帶入送 出交易後的 72 小時(三 天)之內時間。不填則預 設為送出交易後的 72 小 時。

    return hSendExtend
  end

  def allpay_AioCheckOut_hash_SendExtend_Credit(h,hSend)
    # Credit 分期延伸參數。
    hSendExtend = {
      "CreditInstallment" => 0,
      "InstallmentAmount" => 0,
      "Redeem" => Allpay_Redeem::No,
      "UnionPay" => unionpay_check(h), # (銀聯卡交易必須跟歐付 寶提出申請,方可使用)
      "Language" => ""  # 預設語系為中文 若要變更語系為英文 時,該參數值請帶 ENG。
    }

    return hSendExtend
  end
  def unionpay_check(h)
    if (h["UnionPay"] == nil || h["UnionPay"] == "") then
      return Allpay_UnionPay::No
    end
    if (Cfate.ValueBoolCheck(h["UnionPay"])) then
      return Allpay_UnionPay::Yes
    else
      return Allpay_UnionPay::No
    end
  end

  def allpay_AioCheckOut_hash_SendExtend_Credit_Period(h,hSend)
    periodreturn_url = Pm.getWeenBillingServer("/api/v1/periodreturn")
    # Credit 定期定額延伸參數。
    hSendExtend = {
      "PeriodAmount" => "",
      "PeriodType" => "",
      "Frequency" => "",
      "ExecTimes" => "",
      "PeriodReturnURL" => periodreturn_url,
      "Language" => ""  # 預設語系為中文 若要變更語系為英文 時,該參數值請帶 ENG。
    }

    return hSendExtend
  end

  def allpay_paymentinfo_pars_parse(hPar)
    hdefault,is_valid = allpay_paymentinfo_pars_parse_default(hPar)
    if (!is_valid) then
      return hdefault,is_valid,false
    end
    paymenttype_valid,choosepayment,choosesubpayment = Allpay_PaymentType.check_paymenttype(hdefault["PaymentType"])
    if (choosepayment == Allpay_ChoosePayment::ATM) then
      hExtend,is_valid = allpay_paymentinfo_pars_parse_ATM(hdefault,hPar)
    elsif ([Allpay_ChoosePayment::CVS,Allpay_ChoosePayment::BARCODE].include?(choosepayment)) then
      hExtend,is_valid = allpay_paymentinfo_pars_parse_Cvs_Barcode(hdefault,hPar)
    else
      hExtend,is_valid = Hash.new,true
    end
    hdefault.merge!(hExtend)

    macvalue = getMacValue(hdefault)
    hdefault["CheckMacValue"] = hPar["CheckMacValue"]
    hdefault["ChoosePayment"] = choosepayment
    if (macvalue != hPar["CheckMacValue"]) then
      return hdefault,false,false
    end

    return hdefault,is_valid,true
  end

  def allpay_paymentinfo_pars_parse_default(hPar)
    hdefault = Hash.new
    # 廠商編號
    hdefault["MerchantID"] = hPar["MerchantID"]
    # 廠商交易編號
    hdefault["MerchantTradeNo"] = hPar["MerchantTradeNo"]
    # 交易狀態 ATM = 2:取號成功
    # CVS 或 BARCODE = ********: 取號成功
    # 其餘為失敗
    hdefault["RtnCode"] = hPar["RtnCode"]
    # 交易訊息
    hdefault["RtnMsg"] = hPar["RtnMsg"]
    # allpay 的交易 編號  請保􏰁 allpay 的交易編號與 MerchantTradeNo 的關連。
    hdefault["TradeNo"] = hPar["TradeNo"]
    # 交易金額
    hdefault["TradeAmt"] = hPar["TradeAmt"]
    # 會員選擇的付 款方式
    hdefault["PaymentType"] = hPar["PaymentType"]
    # 訂單成立時間
    hdefault["TradeDate"] = hPar["TradeDate"]

    # 綠界新加入的 2018/3/1
    if (isGreenPay()) then
      hdefault["CustomField1"] = hPar["CustomField1"]
      hdefault["CustomField2"] = hPar["CustomField2"]
      hdefault["CustomField3"] = hPar["CustomField3"]
      hdefault["CustomField4"] = hPar["CustomField4"]
      hdefault["StoreID"] = hPar["StoreID"]
    end
    # macvalue = getMacValue(h)
    # hdefault["CheckMacValue"] = hPar["CheckMacValue"]
    # if (macvalue != hPar["CheckMacValue"]) then
    #   return hdefault,false
    # end
    return hdefault,true
  end
  # 當 ChoosePayment 參數為使用 ATM 付款方式時:
  def allpay_paymentinfo_pars_parse_ATM(hdefault,hPar)
    hExtend = Hash.new
    # 繳費銀行代碼
    hExtend["BankCode"] = hPar["BankCode"]
    # 繳費虛擬帳號
    hExtend["vAccount"] = hPar["vAccount"]
    # 繳費期限
    hExtend["ExpireDate"] = hPar["ExpireDate"]

    is_valid = false
    # ATM = 2:取號成功
    if (hdefault["RtnCode"] == "2") then
      is_valid = true
    end
    return hExtend,is_valid
  end
  # 當 ChoosePayment 參數為使用 CVS 或 BARCODE 付款方式時:
  def allpay_paymentinfo_pars_parse_Cvs_Barcode(hdefault,hPar)
    hExtend = Hash.new
    # 繳費代碼
    hExtend["PaymentNo"] = hPar["PaymentNo"]
    # 繳費期限
    hExtend["ExpireDate"] = hPar["ExpireDate"]
    # 條碼第一段號 碼
    hExtend["Barcode1"] = hPar["Barcode1"]
    # 條碼第二段號 碼
    hExtend["Barcode2"] = hPar["Barcode2"]
    # 條碼第三段號 碼
    hExtend["Barcode3"] = hPar["Barcode3"]

    is_valid = false
    # CVS 或 BARCODE = ********: 取號成功
    if (hdefault["RtnCode"] == "********") then
      is_valid = true
    end
    return hExtend,is_valid
  end

  def allpay_returnurl_pars_parse(hPar)
    hdefault,is_RtnCode_valid = allpay_returnurl_pars_parse_default(hPar)
    if (!is_RtnCode_valid) then
      return hdefault,is_RtnCode_valid,false
    end
    paymenttype_valid,choosepayment,choosesubpayment = Allpay_PaymentType.check_paymenttype(hdefault["PaymentType"])
    hExtend = get_return_additional_pars(choosepayment,hdefault,hPar)
    hdefault.merge!(hExtend)

    macvalue = getMacValue(hdefault)
    # Pm.saveTestDb("macvalue",macvalue)
    # Pm.saveTestDb("CheckMacValue",hPar["CheckMacValue"])
    hdefault["CheckMacValue"] = hPar["CheckMacValue"]
  # puts "macvalue => #{macvalue}"
  # puts "hPar[CheckMacValue] => #{hPar["CheckMacValue"]}"
    if (macvalue != hPar["CheckMacValue"]) then
      return hdefault,is_RtnCode_valid,false
    end

    hdefault["ChoosePayment"] = choosepayment
    # hdefault["MerchantTradeNo"] = "ween2227"
    return hdefault,is_RtnCode_valid,true
  end
  def allpay_returnurl_pars_parse_default(hPar)
    hdefault = Hash.new
    # 廠商編號
    hdefault["MerchantID"] = hPar["MerchantID"]
    # 廠商交易編號
    hdefault["MerchantTradeNo"] = hPar["MerchantTradeNo"]
    # 交易狀態 1:付款成功。 800:貨到付款訂單建立成功。 其餘代碼為失敗。
    hdefault["RtnCode"] = hPar["RtnCode"]
    # 交易訊息
    hdefault["RtnMsg"] = hPar["RtnMsg"]
    # allpay 的交易 編號  請保􏰁 allpay 的交易編號與 MerchantTradeNo 的關連。
    hdefault["TradeNo"] = hPar["TradeNo"]
    # 交易金額
    hdefault["TradeAmt"] = hPar["TradeAmt"]
    # 付款時間
    hdefault["PaymentDate"] = hPar["PaymentDate"]
    # 會員選擇的付 款方式
    hdefault["PaymentType"] = hPar["PaymentType"] # Credit_CreditCard
    # 通路費
    hdefault["PaymentTypeChargeFee"] = hPar["PaymentTypeChargeFee"]
    # 訂單成立時間
    hdefault["TradeDate"] = hPar["TradeDate"]
    # 是否為模擬付 款
    if (hPar["SimulatePaid"] != nil) then
      hdefault["SimulatePaid"] = hPar["SimulatePaid"]
    end

    # 綠界新加入的 2018/3/1
    if (isGreenPay()) then
      hdefault["CustomField1"] = hPar["CustomField1"]
      hdefault["CustomField2"] = hPar["CustomField2"]
      hdefault["CustomField3"] = hPar["CustomField3"]
      hdefault["CustomField4"] = hPar["CustomField4"]
      hdefault["StoreID"] = hPar["StoreID"]
    end
    # macvalue = getMacValue(h)
    # hdefault["CheckMacValue"] = hPar["CheckMacValue"]
    # if (macvalue != hPar["CheckMacValue"]) then
    #   return hdefault,false
    # end
    is_valid = false
    # 交易狀態 1:付款成功。 800:貨到付款訂單建立成功。 其餘代碼為失敗。
    if (hdefault["RtnCode"] == "1" && hPar["MerchantID"] == getMerchantNumber()) then # || hdefault["RtnCode"] == "800") then
      is_valid = true
    end
    return hdefault,is_valid
  end
  def isGreenPay()
    sData = Pm.getSystemValue("Allpay_Company")
    if (sData == "GreenPay") then
      return true
    else
      return false
    end
  end
  def isAllPay()
    sData = Pm.getSystemValue("Allpay_Company")
    if (sData == "AllPay") then
      return true
    else
      return false
    end
  end
  def get_return_additional_pars(choosepayment,hdefault,hPar)
    if (choosepayment == Allpay_ChoosePayment::WebATM) then
      hExtend = get_return_additional_pars_WebATM(hdefault,hPar)
    elsif (choosepayment == Allpay_ChoosePayment::ATM) then
      hExtend = get_return_additional_pars_ATM(hdefault,hPar)
    elsif ([Allpay_ChoosePayment::CVS,Allpay_ChoosePayment::BARCODE].include?(choosepayment)) then
      hExtend = get_return_additional_pars_Cvs_Barcode(hdefault,hPar)
    elsif (choosepayment == Allpay_ChoosePayment::Alipay) then
      hExtend = get_return_additional_pars_Alipay(hdefault,hPar)
    elsif (choosepayment == Allpay_ChoosePayment::Tenpay) then
      hExtend = get_return_additional_pars_Tenpay(hdefault,hPar)
    elsif (choosepayment == Allpay_ChoosePayment::Credit) then
      hExtend = get_return_additional_pars_Credit(hdefault,hPar)
    elsif (choosepayment == Allpay_ChoosePayment::UnionPay) then
      hExtend = get_return_additional_pars_Credit(hdefault,hPar)
    elsif (choosepayment == Allpay_ChoosePayment::AndroidPay) then
      hExtend = get_return_additional_pars_AndroidPay(hdefault,hPar)
    else
      hExtend = Hash.new
    end
    return hExtend
  end
  # 當 ChoosePayment 參數為使用 ATM 付款方式時:
  def get_return_additional_pars_WebATM(hdefault,hPar)
    hExtend = Hash.new
    # 付款人銀行代碼
    hExtend["WebATMAccBank"] = hPar["WebATMAccBank"] if hPar["WebATMAccBank"] != nil
    # 付款人銀行帳號後五碼
    hExtend["WebATMAccNo"] = hPar["WebATMAccNo"] if hPar["WebATMAccNo"] != nil
    # 銀行名稱
    hExtend["WebATMBankName"] = hPar["WebATMBankName"] if hPar["WebATMBankName"] != nil

    return hExtend
  end
  def get_return_additional_pars_ATM(hdefault,hPar)
    hExtend = Hash.new
    # 付款人銀行代碼
    hExtend["ATMAccBank"] = hPar["ATMAccBank"] if hPar["ATMAccBank"] != nil
    # 付款人銀行帳號後五碼
    hExtend["ATMAccNo"] = hPar["ATMAccNo"] if hPar["ATMAccNo"] != nil

    return hExtend
  end
  def get_return_additional_pars_Cvs_Barcode(hdefault,hPar)
    hExtend = Hash.new
    # 繳費代碼
    hExtend["PaymentNo"] = hPar["PaymentNo"] if hPar["PaymentNo"] != nil
    # 繳費超商
    hExtend["PayFrom"] = hPar["PayFrom"] if hPar["PayFrom"] != nil

    return hExtend
  end
  def get_return_additional_pars_Alipay(hdefault,hPar)
    hExtend = Hash.new
    # 付款人在支付寶的系統編號
    hExtend["AlipayID"] = hPar["AlipayID"] if hPar["AlipayID"] != nil
    # 支付寶交易編號
    hExtend["AlipayTradeNo"] = hPar["AlipayTradeNo"] if hPar["AlipayTradeNo"] != nil

    return hExtend
  end
  def get_return_additional_pars_Tenpay(hdefault,hPar)
    hExtend = Hash.new
    # 財付通交易編號
    hExtend["TenpayTradeNo"] = hPar["TenpayTradeNo"] if hPar["TenpayTradeNo"] != nil

    return hExtend
  end
  def get_return_additional_pars_Credit(hdefault,hPar)
    hExtend = Hash.new
    # ￼ 授權交易單號
    hExtend["gwsr"] = hPar["gwsr"] if hPar["gwsr"] != nil
    # 處理時間
    hExtend["process_date"] = hPar["process_date"] if hPar["process_date"] != nil
    # ￼ 授權碼
    hExtend["auth_code"] = hPar["auth_code"] if hPar["auth_code"] != nil
    # 金額
    hExtend["amount"] = hPar["amount"] if hPar["amount"] != nil
    #  分期期數
    hExtend["stage"] = hPar["stage"] if hPar["stage"] != nil
    # 頭期金額
    hExtend["stast"] = hPar["stast"] if hPar["stast"] != nil
    # ￼ 各期金額
    hExtend["staed"] = hPar["staed"] if hPar["staed"] != nil
    # 3D(VBV) 回傳值(eci=5,6,2,1 代表該筆交易不可 否認)
    hExtend["eci"] = hPar["eci"] if hPar["eci"] != nil
    # ￼ 卡片的末 4 碼
    hExtend["card4no"] = hPar["card4no"] if hPar["card4no"] != nil
    # 卡片的前 6 碼
    hExtend["card6no"] = hPar["card6no"] if hPar["card6no"] != nil
    # ￼ 紅利扣點
    hExtend["red_dan"] = hPar["red_dan"] if hPar["red_dan"] != nil
    # 紅利折抵金額
    hExtend["red_de_amt"] = hPar["red_de_amt"] if hPar["red_de_amt"] != nil
    # 實際扣款金額
    hExtend["red_ok_amt"] = hPar["red_ok_amt"] if hPar["red_ok_amt"] != nil
    # 紅利剩餘點數
    hExtend["red_yet"] = hPar["red_yet"] if hPar["red_yet"] != nil
    # 訂單建立時的所設定的週期種類
    hExtend["PeriodType"] = hPar["PeriodType"] if hPar["PeriodType"] != nil
    # 訂單建立時的所設定的執行頻率
    hExtend["Frequency"] = hPar["Frequency"] if hPar["Frequency"] != nil
    # 訂單建立時的所設定的執行頻率
    hExtend["ExecTimes"] = hPar["ExecTimes"] if hPar["ExecTimes"] != nil
    # 訂單建立時的每次要授權金額
    hExtend["PeriodAmount"] = hPar["PeriodAmount"] if hPar["PeriodAmount"] != nil
    # 目前已成功授權的次數
    hExtend["TotalSuccessTimes"] = hPar["TotalSuccessTimes"] if hPar["TotalSuccessTimes"] != nil
    # 目前已成功授權的金額合計
    hExtend["TotalSuccessAmount"] = hPar["TotalSuccessAmount"] if hPar["TotalSuccessAmount"] != nil

    return hExtend
  end

  def get_return_additional_pars_AndroidPay(hdefault,hPar)
    hExtend = Hash.new
    return hExtend
  end
  def getperiodreturnpars(hPar)
    hdefault,is_valid = getperiodreturnpars_default(hPar)
    if (!is_valid) then
      return hdefault,is_valid
    end
    choosepayment = Allpay_ChooseSubPayment.check_choosepayment(hdefault["PaymentType"])
    hExtend = get_return_additional_pars(choosepayment,hdefault,hPar)
    hdefault.merge!(hExtend)

    macvalue = getMacValue(hdefault)
    hdefault["CheckMacValue"] = hPar["CheckMacValue"]
    if (macvalue != hPar["CheckMacValue"]) then
      return hdefault,false
    end

    hdefault["ChoosePayment"] = choosepayment

    return hdefault,is_valid
  end
  def getperiodreturnpars_default(hPar)
    hdefault = Hash.new
    # 廠商編號
    hdefault["MerchantID"] = hPar["MerchantID"]
    # 廠商交易編號
    hdefault["MerchantTradeNo"] = hPar["MerchantTradeNo"]
    # 交易狀態 1:授權成功,其餘為失敗
    hdefault["RtnCode"] = hPar["RtnCode"]
    # 交易訊息
    hdefault["RtnMsg"] = hPar["RtnMsg"]
    # 週期種類
    hdefault["PeriodType"] = hPar["PeriodType"]
    # 執行頻率
    hdefault["Frequency"] = hPar["Frequency"]
    # 執行次數
    hdefault["ExecTimes"] = hPar["ExecTimes"]
    # 本次授權金額
    hdefault["Amount"] = hPar["Amount"]
    # 授權交易單號
    hdefault["gwsr"] = hPar["gwsr"]
    # 處理時間
    hdefault["ProcessDate"] = hPar["ProcessDate"]
    # 授權碼
    hdefault["AuthCode"] = hPar["AuthCode"]
    # 初次授權金額
    hdefault["FirstAuthAmount"] = hPar["FirstAuthAmount"]
    # 已執行成功次 數
    hdefault["TotalSuccessTimes"] = hPar["TotalSuccessTimes"]
    # 是否為模擬付 款
    if (hPar["SimulatePaid"] != nil) then
      hdefault["SimulatePaid"] = hPar["SimulatePaid"]
    end

    # macvalue = getMacValue(h)
    # hdefault["CheckMacValue"] = hPar["CheckMacValue"]
    # if (macvalue != hPar["CheckMacValue"]) then
    #   return hdefault,false
    # end
    return hdefault,true
  end

  def send_to_allpay_formdata(sHttp,hSendAll)
    # return my_post(sHttp,hSendAll)
    return auto_post_order_html(sHttp,hSendAll,'_self')
  end
  def auto_post_order_html(sHttp,hAll,target='_self')
    szHtml = "<div style='text-align:center;' ><form id='__allpayForm' method='post' target='#{target}' action='#{sHttp}'>"
    hAll.each {|key, value|
        szHtml = szHtml + "<input type='hidden' name='#{key}' value='#{value}' />"
    }

     # 手動或自動送出表單。
    # if (!isset($paymentButton)) {
        szHtml = szHtml + "<script type='text/javascript'>document.getElementById('__allpayForm').submit();</script>"
    # } else {
    #     szHtml .= '<input type="submit" id="__paymentButton" value="' . $paymentButton . '" />';
    # }
    szHtml = szHtml + "</form></div>"
# puts "szHtml : => #{szHtml}"
    return szHtml
  end

  def Controller_Api_Allpay.choosepayment_desc(key)
    return Pm.GetStr("allpay.desc.choosepayment.#{key}")
  end
end
