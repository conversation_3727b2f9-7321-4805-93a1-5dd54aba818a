# coding: UTF-8

class GNum
  # num,numm
  @@turn = {
    "111" => "383",
    "113" => "385",
    "114" => "386",
    "115" => "371",
    "116" => "372",
    "117" => "373",
    "119" => "375",
    "120" => "376",
    "121" => "321",
    "122" => "322",
    "123" => "323",
    "125" => "325",
    "126" => "326",
    "127" => "311",
    "128" => "312",
    "130" => "314",
    "131" => "315",
    "132" => "316",
    "133" => "351",
    "134" => "352",
    "136" => "354",
    "137" => "355",
    "138" => "356",
    "139" => "361",
    "140" => "362",
    "142" => "364",
    "143" => "365",
    "144" => "366",
    "145" => "441",
    "147" => "443",
    "148" => "444",
    "149" => "445",
    "150" => "446",
    "151" => "481",
    "153" => "483",
    "154" => "484",
    "155" => "485",
    "156" => "486",
    "157" => "471",
    "159" => "473",
    "160" => "474",
    "161" => "475",
    "163" => "461",
    "166" => "464",
    "167" => "465",
    "169" => "431",
    "171" => "433",
    "172" => "434",
    "175" => "421",
    "177" => "424",
    "178" => "423",
    "180" => "426",
    "181" => "412",
    "185" => "415",
    "186" => "416",
    "188" => "452",
    "189" => "453",
    "191" => "",
    "193" => "551",
    "194" => "",
    "197" => "555",
    "198" => "556",
    "200" => "512",
    "202" => "514",
    "203" => "515",
    "206" => "522",
    "208" => "524",
    "209" => "525",
    "211" => "531",
    "212" => "532",
    "216" => "536",
    "217" => "561",
    "219" => "563",
    "220" => "564",
    "223" => "571",
    "225" => "573",
    "226" => "574",
    "228" => "576",
    "230" => "582",
    "233" => "585",
    "234" => "586",
    "236" => "542",
    "237" => "543",
    "239" => "545",
    "242" => "662",
    "244" => "664",
    "245" => "665",
    "247" => "651",
    "250" => "654",
    "251" => "251",
    "253" => "611",
    "254" => "612",
    "256" => "614",
    "259" => "622",
    "261" => "624",
    "262" => "625",
    "264" => "671",
    "265" => "672",
    "268" => "675",
    "270" => "681",
    "272" => "683",
    "273" => "684",
    "276" => "641",
    "278" => "643",
    "279" => "644",
    "281" => "646",
    "282" => "631",
    "286" => "635",
    "287" => "636",
    "289" => "772",
    "290" => "773",
    "292" => "775",
    "295" => "762",
    "296" => "763",
    "298" => "765",
    "300" => "751",
    "303" => "754",
    "304" => "755",
    "306" => "711",
    "307" => "712",
    "309" => "714",
    "312" => "781",
    "314" => "783",
    "315" => "784",
    "317" => "786",
    "318" => "741",
    "321" => "744",
    "323" => "746",
    "324" => "731",
    "326" => "733",
    "329" => "736",
    "331" => "722",
    "332" => "723",
    "334" => "725",
    "335" => "726",
    "338" => "883",
    "340" => "885",
    "342" => "871",
    "343" => "872",
    "345" => "874",
    "348" => "861",
    "349" => "862",
    "351" => "864",
    "352" => "865",
    "356" => "853",
    "357" => "854",
    "359" => "856",
    "360" => "841",
    "362" => "843",
    "365" => "846",
    "366" => "831",
    "368" => "833",
    "369" => "834",
    "370" => "835",
    "371" => "836",
    "372" => "821",
    "374" => "823",
    "375" => "824",
    "376" => "825",
    "377" => "826",
    "379" => "812",
    "380" => "813",
    "381" => "814",
    "382" => "815",
    "383" => "816",
    "385" => "111",
    "386" => "112",
    "387" => "113",
    "388" => "114",
    "389" => "115",
    "391" => "121",
    "392" => "122",
    "393" => "123",
    "394" => "124",
    "396" => "126",
    "397" => "131",
    "398" => "132",
    "399" => "133",
    "400" => "134",
    "402" => "136",
    "403" => "141",
    "404" => "142",
    "405" => "143",
    "406" => "144",
    "408" => "146",
    "409" => "151",
    "410" => "152",
    "411" => "153",
    "413" => "155",
    "414" => "156",
    "415" => "161",
    "416" => "162",
    "418" => "164",
    "421" => "171",
    "422" => "172",
    "424" => "174",
    "426" => "176",
    "427" => "181",
    "430" => "184",
    "432" => "186",
    "433" => "221",
    "435" => "223",
    "438" => "226",
    "440" => "232",
    "441" => "233",
    "443" => "235",
    "444" => "236",
    "447" => "243",
    "449" => "245",
    "450" => "246",
    "452" => "282",
    "454" => "284",
    "457" => "211",
    "458" => "212",
    "460" => "214",
    "461" => "215",
    "464" => "252",
    "466" => "254",
    "468" => "256",
    "469" => "261",
    "471" => "263",
    "474" => "266",
    "475" => "271",
    "477" => "273",
    "478" => "274",
    "480" => "276",
    "483" => "333",
    "485" => "335",
    "486" => "336",
    "488" => "342",
    "491" => "345",
    "492" => "346",
    "494" => "382",
    "496" => "384",
    "497" => "385",
    "500" => "372",
    "502" => "374",
    "503" => "375",
    "505" => "321",
    "506" => "322",
    "510" => "326",
    "511" => "311",
    "513" => "313",
    "514" => "314",
    "517" => "351",
    "519" => "353",
    "520" => "354",
    "522" => "356",
    "524" => "362",
    "527" => "365",
    "528" => "366",
    "530" => "442",
    "531" => "443",
    "533" => "445",
    "536" => "482",
    "538" => "484",
    "539" => "485",
    "541" => "471",
    "544" => "474",
    "545" => "475",
    "547" => "461",
    "548" => "462",
    "550" => "464",
    "553" => "431",
    "555" => "433",
    "556" => "434",
    "558" => "436",
    "559" => "421",
    "562" => "423",
    "564" => "426",
    "566" => "411",
    "567" => "413",
    "570" => "416",
    "572" => "452",
    "573" => "453",
    "575" => "455",
    "576" => "456",
    "580" => "554",
    "581" => "555",
    "583" => "511",
    "584" => "512",
    "586" => "514",
    "589" => "521",
    "590" => "522",
    "592" => "524",
    "594" => "526",
    "597" => "533",
    "598" => "534",
    "600" => "536",
    "601" => "561",
    "603" => "563",
    "606" => "566",
    "608" => "572",
    "609" => "573",
    "611" => "575",
    "612" => "576",
    "615" => "583",
    "617" => "585",
    "618" => "586",
    "620" => "542",
    "621" => "543",
    "623" => "545",
    "624" => "546",
    "625" => "661",
    "626" => "662",
    "627" => "663",
    "629" => "665",
    "630" => "666",
    "631" => "651",
    "632" => "652",
    "634" => "654",
    "635" => "655",
    "636" => "656",
    "637" => "611",
    "638" => "612",
    "640" => "614",
    "641" => "615",
    "642" => "621",
    "643" => "622",
    "644" => "623",
    "646" => "625",
    "647" => "626",
    "648" => "671",
    "649" => "672",
    "651" => "674",
    "652" => "675",
    "653" => "676",
    "654" => "681",
    "655" => "682",
    "657" => "684",
    "658" => "685",
    "659" => "686",
    "660" => "641",
    "661" => "642",
    "663" => "644",
    "664" => "645",
    "665" => "646",
    "666" => "631",
    "668" => "633",
    "669" => "634",
    "670" => "635",
    "671" => "636",
    "673" => "772",
    "676" => "775",
    "677" => "776",
    "679" => "762",
    "681" => "764",
    "682" => "765",
    "685" => "752",
    "687" => "754",
    "688" => "755",
    "690" => "711",
    "693" => "714",
    "695" => "716",
    "696" => "781",
    "698" => "783",
    "699" => "784",
    "702" => "741",
    "704" => "743",
    "705" => "744",
    "707" => "746",
    "709" => "732",
    "712" => "735",
    "713" => "736",
    "715" => "722",
    "716" => "723",
    "719" => "726",
    "721" => "882",
    "723" => "884",
    "724" => "885",
    "726" => "871",
    "729" => "874",
    "730" => "875",
    "732" => "861",
    "733" => "862",
    "735" => "864",
    "738" => "851",
    "740" => "853",
    "741" => "854",
    "743" => "856",
    "746" => "843",
    "747" => "844",
    "749" => "846",
    "751" => "832",
    "752" => "833",
    "755" => "836",
    "757" => "822",
    "758" => "823",
    "760" => "825",
    "761" => "826",
    "765" => "814",
    "766" => "815",
    "768" => "324",
    "769" => "111",
    "772" => "114",
    "774" => "116",
    "775" => "121",
    "777" => "123",
    "779" => "125",
    "782" => "132",
    "783" => "133",
    "785" => "135",
    "786" => "136",
    "788" => "142",
    "791" => "145",
    "793" => "151",
    "794" => "152",
    "796" => "154",
    "799" => "161",
    "800" => "162",
    "802" => "164",
    "803" => "165",
    "805" => "171",
    "808" => "174",
    "810" => "176",
    "811" => "181",
    "813" => "183",
    "814" => "184",
    "817" => "221",
    "819" => "223",
    "821" => "225",
    "822" => "226",
    "825" => "233",
    "827" => "235",
    "828" => "236",
    "830" => "242",
    "831" => "243",
    "835" => "281",
    "836" => "282",
    "838" => "284",
    "839" => "285",
    "841" => "211",
    "844" => "214",
    "845" => "215",
    "847" => "251",
    "849" => "253",
    "852" => "256",
    "853" => "261",
    "855" => "263",
    "856" => "264",
    "858" => "266",
    "861" => "273",
    "863" => "275",
    "864" => "276",
    "866" => "332",
    "867" => "333",
    "870" => "336",
    "872" => "342",
    "873" => "343",
    "875" => "345",
    "876" => "346",
    "877" => "381",
    "878" => "382",
    "879" => "383",
    "880" => "384",
    "881" => "385",
    "882" => "386",
    "883" => "371",
    "884" => "372",
    "885" => "373",
    "886" => "374",
    "887" => "375",
    "888" => "376",
    "889" => "321",
    "890" => "322",
    "891" => "323",
    "892" => "522",
    "893" => "325",
    "894" => "326",
    "895" => "311",
    "896" => "312",
    "897" => "313",
    "898" => "314",
    "899" => "315",
    "900" => "316",
    "901" => "351",
    "902" => "352",
    "903" => "353",
    "904" => "354",
    "905" => "355",
    "906" => "356",
    "907" => "361",
    "908" => "362",
    "909" => "363",
    "910" => "364",
    "911" => "365",
    "912" => "366",
    "913" => "441",
    "914" => "442",
    "915" => "443",
    "918" => "446",
    "920" => "482",
    "921" => "483",
    "923" => "485",
    "924" => "486",
    "928" => "474",
    "929" => "475",
    "931" => "461",
    "932" => "462",
    "934" => "464",
    "937" => "431",
    "938" => "432",
    "940" => "434",
    "942" => "436",
    "945" => "424",
    "946" => "423",
    "948" => "426",
    "949" => "412",
    "951" => "413",
    "954" => "416",
    "956" => "452",
    "957" => "453",
    "959" => "455",
    "960" => "456",
    "963" => "553",
    "965" => "555",
    "966" => "556",
    "968" => "512",
    "971" => "515",
    "973" => "521",
    "974" => "522",
    "976" => "524",
    "977" => "525",
    "980" => "532",
    "982" => "534",
    "984" => "536",
    "985" => "561",
    "987" => "563",
    "990" => "566",
    "991" => "571",
    "993" => "573",
    "994" => "574",
    "998" => "582",
    "999" => "583",
    "162" => "476",
    "165" => "463",
    "168" => "466",
    "170" => "432",
    "173" => "435",
    "176" => "422",
    "179" => "425",
    "182" => "411",
    "184" => "414",
    "187" => "451",
    "192" => "456",
    "196" => "554",
    "199" => "511",
    "201" => "513",
    "204" => "516",
    "207" => "523",
    "210" => "526",
    "213" => "533",
    "215" => "535",
    "218" => "562",
    "221" => "565",
    "224" => "572",
    "227" => "575",
    "229" => "581",
    "232" => "584",
    "235" => "541",
    "238" => "544",
    "241" => "661",
    "243" => "663",
    "246" => "666",
    "249" => "653",
    "252" => "656",
    "255" => "613",
    "257" => "615",
    "260" => "623",
    "263" => "626",
    "266" => "673",
    "269" => "676",
    "271" => "682",
    "274" => "685",
    "277" => "642",
    "280" => "645",
    "283" => "632",
    "285" => "634",
    "288" => "771",
    "291" => "774",
    "294" => "761",
    "297" => "764",
    "299" => "766",
    "302" => "753",
    "305" => "756",
    "308" => "713",
    "311" => "716",
    "313" => "782",
    "316" => "785",
    "319" => "742",
    "322" => "745",
    "325" => "732",
    "327" => "734",
    "330" => "721",
    "333" => "724",
    "336" => "881",
    "339" => "884",
    "341" => "886",
    "344" => "873",
    "347" => "876",
    "350" => "863",
    "353" => "866",
    "355" => "852",
    "358" => "855",
    "361" => "842",
    "112" => "384",
    "118" => "374",
    "124" => "522",
    "129" => "313",
    "135" => "353",
    "141" => "363",
    "146" => "442",
    "364" => "845",
    "417" => "163",
    "420" => "166",
    "423" => "173",
    "428" => "182",
    "431" => "185",
    "434" => "222",
    "437" => "225",
    "439" => "231",
    "442" => "234",
    "448" => "244",
    "451" => "281",
    "453" => "283",
    "456" => "286",
    "459" => "213",
    "462" => "216",
    "467" => "255",
    "470" => "262",
    "473" => "265",
    "476" => "272",
    "479" => "275",
    "481" => "331",
    "487" => "341",
    "490" => "344",
    "493" => "381",
    "495" => "383",
    "498" => "386",
    "501" => "373",
    "507" => "323",
    "509" => "325",
    "512" => "312",
    "515" => "315",
    "518" => "352",
    "521" => "355",
    "526" => "364",
    "529" => "441",
    "532" => "444",
    "535" => "481",
    "537" => "483",
    "540" => "486",
    "546" => "476",
    "549" => "463",
    "551" => "465",
    "554" => "432",
    "557" => "435",
    "560" => "422",
    "565" => "412",
    "568" => "414",
    "571" => "451",
    "574" => "454",
    "577" => "551",
    "579" => "553",
    "585" => "513",
    "588" => "516",
    "591" => "523",
    "593" => "525",
    "596" => "532",
    "599" => "535",
    "605" => "565",
    "607" => "571",
    "610" => "574",
    "613" => "581",
    "616" => "584",
    "619" => "541",
    "675" => "774",
    "678" => "761",
    "680" => "763",
    "683" => "766",
    "686" => "753",
    "689" => "756",
    "694" => "715",
    "697" => "782",
    "700" => "785",
    "703" => "742",
    "706" => "745",
    "708" => "731",
    "714" => "721",
    "717" => "724",
    "720" => "881",
    "722" => "883",
    "725" => "886",
    "728" => "873",
    "734" => "863",
    "736" => "865",
    "739" => "852",
    "742" => "855",
    "745" => "842",
    "748" => "845",
    "753" => "834",
    "756" => "821",
    "759" => "824",
    "762" => "811",
    "764" => "813",
    "767" => "816",
    "773" => "115",
    "776" => "122",
    "778" => "124",
    "781" => "131",
    "784" => "134",
    "787" => "141",
    "792" => "146",
    "795" => "153",
    "798" => "156",
    "801" => "163",
    "804" => "166",
    "806" => "172",
    "812" => "182",
    "815" => "185",
    "818" => "222",
    "820" => "224",
    "823" => "231",
    "826" => "234",
    "832" => "244",
    "834" => "246",
    "837" => "283",
    "840" => "286",
    "843" => "213",
    "846" => "216",
    "851" => "255",
    "854" => "262",
    "857" => "265",
    "860" => "272",
    "862" => "274",
    "865" => "331",
    "871" => "341",
    "874" => "344",
    "916" => "444",
    "919" => "481",
    "922" => "484",
    "925" => "471",
    "927" => "473",
    "930" => "476",
    "933" => "463",
    "936" => "466",
    "939" => "433",
    "941" => "435",
    "944" => "422",
    "947" => "425",
    "950" => "411",
    "953" => "415",
    "955" => "451",
    "958" => "454",
    "961" => "551",
    "964" => "554",
    "967" => "511",
    "969" => "513",
    "972" => "516",
    "975" => "523",
    "978" => "526",
    "981" => "533",
    "983" => "535",
    "986" => "562",
    "989" => "565",
    "995" => "575",
    "997" => "581",
    "152" => "482",
    "158" => "472",
    "164" => "462",
    "174" => "436",
    "183" => "413",
    "190" => "",
    "195" => "553",
    "205" => "521",
    "214" => "534",
    "222" => "566",
    "231" => "583",
    "917" => "445",
    "926" => "472",
    "935" => "465",
    "943" => "421",
    "952" => "414",
    "962" => "552",
    "970" => "514",
    "979" => "531",
    "240" => "546",
    "248" => "652",
    "258" => "621",
    "267" => "674",
    "275" => "686",
    "284" => "633",
    "293" => "776",
    "301" => "752",
    "310" => "715",
    "320" => "743",
    "328" => "735",
    "337" => "882",
    "346" => "875",
    "354" => "851",
    "363" => "844",
    "367" => "832",
    "373" => "822",
    "378" => "811",
    "384" => "324",
    "390" => "116",
    "395" => "125",
    "401" => "135",
    "407" => "145",
    "412" => "154",
    "419" => "165",
    "425" => "175",
    "429" => "183",
    "436" => "224",
    "445" => "241",
    "446" => "242",
    "455" => "285",
    "463" => "251",
    "465" => "253",
    "472" => "264",
    "482" => "332",
    "484" => "334",
    "489" => "343",
    "499" => "371",
    "504" => "376",
    "508" => "522",
    "516" => "316",
    "523" => "361",
    "525" => "363",
    "534" => "446",
    "542" => "472",
    "543" => "473",
    "552" => "466",
    "561" => "424",
    "563" => "425",
    "569" => "415",
    "578" => "552",
    "582" => "556",
    "587" => "515",
    "595" => "531",
    "602" => "562",
    "604" => "564",
    "614" => "582",
    "622" => "544",
    "628" => "664",
    "633" => "653",
    "639" => "613",
    "645" => "624",
    "650" => "673",
    "656" => "683",
    "662" => "643",
    "667" => "632",
    "672" => "771",
    "674" => "773",
    "684" => "751",
    "691" => "712",
    "692" => "713",
    "701" => "786",
    "710" => "733",
    "711" => "734",
    "718" => "725",
    "727" => "872",
    "731" => "876",
    "737" => "866",
    "744" => "841",
    "750" => "831",
    "754" => "835",
    "763" => "812",
    "770" => "112",
    "771" => "113",
    "780" => "126",
    "789" => "143",
    "790" => "144",
    "797" => "155",
    "807" => "173",
    "809" => "175",
    "816" => "186",
    "824" => "232",
    "829" => "241",
    "833" => "245",
    "842" => "212",
    "848" => "252",
    "850" => "254",
    "859" => "271",
    "868" => "334",
    "869" => "335",
    "988" => "564",
    "992" => "572",
    "996" => "576"
  }
  def GNum.find_Turn(num)
  	return @@turn[num]
  end

  # land 地數
  # sky 天數
  # people 人數
  def GNum.getTurnCount(sky,people,land)
  	landcount = GNum.landcount(land)
  	count = "#{sky}#{people}#{landcount}"
  	return count
  end

  def GNum.landcount(sland)
  	land = sland.to_i
  	if land == 1 or land == 2 or land == 3 then
    	landcount = "2"
  	elsif land == 4 or land == 5 or land == 6 then
		landcount = "7"
  	elsif land == 7 or land == 8 or land == 9 then
		landcount = "6"
  	elsif land == 10 or land == 11 or land == 12 then
		landcount = "1"
  	elsif land == 13 or land == 14 or land == 15 then
		landcount = "8"
  	elsif land == 16 or land == 17 or land == 18 then
		landcount = "3"
  	elsif land == 19 or land == 20 or land == 21 then
		landcount = "4"
  	elsif land == 22 or land == 23 or land == 24 then
  		landcount = "9"
  	elsif land == 0 then
		landcount = "5"
  	end
  	return landcount
  end

  # 地數
  def GNum.find_land(land_from_client=nil)
  	if (land_from_client == nil) then
  	  land = "#{rand(25)}" #取亂數「定數」
    else
      land = "#{land_from_client}"
    end
    return land
  end

  # 天數
  def GNum.find_sky(minute=nil)
    if (minute == nil) then
   	  t = DateTime.now
  	  m = t.min
  	else
  	  m = minute.to_i
  	end
  	sky = m % 10
  	if (sky == 0) then
  	  sky = 1 # 時間(天數)
  	end
  	return "#{sky}"
  end

end