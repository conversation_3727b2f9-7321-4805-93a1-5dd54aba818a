# coding: UTF-8


# Star for Explain
class Star
	# 取得大限資訊
	def gEx_FindFirstLargeIndex()
		nLarge = 10000
		nIndex = -1
		(0..11).each do |i|
			if (cp_getHouseIndexLarge(i) < nLarge) then
				nLarge = cp_getHouseIndexLarge(i)
				nIndex = i
			end
		end
		return nIndex,nLarge
	end

	def gEx_GetLarge(nIndex)
		nFirstIndex,nLarge = gEx_FindFirstLargeIndex()
		nNewIndex = Earth.Modify(nFirstIndex + nIndex)
		return cp_getHouseIndexLarge(nNewIndex)
	end
  def gEx_GetLargeYear(nIndex)
    nYearOld = gEx_GetLarge(nIndex)
    return  cp_YearOld2FlowYear(nYearOld)
  end
	def gExHouse_GetAStarSanKurName(nPanType,nEarth,nStar)
		nSanKur = gExHouse_GetAStarSanKur(nPanType,nEarth,nStar)
		return pEx_GetSanKurName(nSanKur)
	end

	def gExHouse_GetAStarSanKur(nPanType,nEarth,nStar)
 		nHouseFive = cp_getHouseFive(nPanType,nEarth)
  		nStarFive = gHouse_GetAStarFive(nPanType,nEarth,nStar)
  		return g_GetFiveSanKur(nHouseFive,nStarFive)
	end

	def pEx_GetSanKurName(nSanKur)
		sSanKur = ""
		if (nSanKur < 0) then return sSanKur end

		nSanKurIndex = nSanKur / 2
		sSanKur = Pm.GetStr("IDS_S_SANKUR_#{nSanKurIndex}")
		return sSanKur
	end

	def gExHouse_GetBStarSanKurName(nPanType,nEarth,nStar)
		nSanKur = gExHouse_GetBStarSanKur(nPanType,nEarth,nStar)
		return pEx_GetSanKurName(nSanKur)
	end

	def gExHouse_GetBStarSanKur(nPanType,nEarth,nStar)
 		nHouseFive = cp_getHouseFive(nPanType,nEarth)
  		nStarFive = gHouse_GetBStarFive(nPanType,nEarth,nStar)
  		return g_GetFiveSanKur(nHouseFive,nStarFive)
	end

	def gExHouse_GetCStarSanKurName(nPanType,nEarth,nStar,sStarType)
		nSanKur = gExHouse_GetCStarSanKur(nPanType,nEarth,nStar,sStarType)
		return pEx_GetSanKurName(nSanKur)
	end

	def gExHouse_GetCStarSanKur(nPanType,nEarth,nStar,sStarType)
 		nHouseFive = cp_getHouseFive(nPanType,nEarth)
  		nStarFive = gHouse_GetStarFive(nPanType,nEarth,nStar,sStarType)
  		return g_GetFiveSanKur(nHouseFive,nStarFive)
	end

  def gExHouse_Find_OrgSky_FourHuaHouse(nPanType)
    nFlowSky = gGiSuong_GetGiSuong_Rule1_OrgSky(nPanType)
    # 先天四化
    aFlowFourHuaHouse = gGiSuong_FindFourHuaHouse(nPanType,nFlowSky)
    return aFlowFourHuaHouse
  end
  def gExHouse_Find_OrgSky_FourHuaHouse_inHouse(nPanType,nEarth)
    aOut = Array.new
    aFlowFourHuaHouse = gExHouse_Find_OrgSky_FourHuaHouse(nPanType)
    aFlowFourHuaHouse.each_index do |i|
      if (aFlowFourHuaHouse[i] == nEarth) then
        aOut.push(i)
      end
    end
    return aOut
  end
end
