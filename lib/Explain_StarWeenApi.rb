class Explain
  def wa_Result(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
  	@hOut = Hash.new
  	@lang = sLang
  	@m_nPanType = nPanType
  	@UserInfo = hUserInfo.clone
  	@ApFunc = hApFunc.clone
  	@ParAll = hParAll.clone
  	@Star = Star.new(hUserInfo,hUserAskData,hParAll)
  	# @StarInfo = @Star.g_GetStarPanInfo(nPanType,hUserInfo,hUserAskData,hParAll)
  	@UserAskData = hUserAskData.clone
  	@ExplainHouse = @UserAskData[Star::HOUSE_NAME]
    if (@ExplainHouse == nil) then
      @ExplainHouse = Star::EX_HOUSE_ALL
    end
  	# @FourK = FourKData.new
  	# @FourOut = FourOutData.new
  	# @TzMain = TzMainData.new

  	if (nPanType == Cfate::PAN_NORMAL) then
  		return wa_Result_Normal(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
  	else
  	  if (@ExplainHouse == Star::EX_HOUSE_ALL) then
    	return we_Create_AllHouse(@Star,nPanType)
      else
    	if (nPanType == Cfate::PAN_TENYEAR) then
    		return wa_Result_TenYear(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    	elsif (nPanType == Cfate::PAN_FLOWYEAR) then
    		return wa_Result_FlowYear(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    	elsif (nPanType == Cfate::PAN_FLOWMONTH) then
    		return wa_Result_FlowMonth(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    	elsif (nPanType == Cfate::PAN_FLOWDATE) then
    		return wa_Result_FlowDate(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    	elsif (nPanType == Cfate::PAN_FLOWTIME) then
    		return wa_Result_FlowTime(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    	elsif (nPanType == Cfate::PAN_FLOWMIN) then
    		return wa_Result_FlowMin(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    	end
      end
    end
  end

  def wa_Result_Normal(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
  	@ExplainHouse = Star::EX_HOUSE_ALL

  	@hOut = we_Create_AllHouse(@Star,nPanType)

  	return @hOut
  end
  # 本命盤：則各宮顯示說明(十二宮分析)
  def we_Create_AllHouse(oStar,nPanType)
  	hExplain = Hash.new
  	nEarth = oStar.gHouse_GetFirstHouseEarth(nPanType)
    hExplain["count"] = 12
  	# 1..12 表示子宮到亥宮
  	(1..12).each do |nHouse|
  	  hData = Hash.new
   	  hData["housename"] = oStar.gHouse_GetHouseNameWithQuota(nPanType,nEarth)

      # 文字定義
  	  hData["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
  	  hData["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{nHouse}")
   	  # 說明部分
   	  hData["explain"] = we_Star_Create_HouseInfo_Explain_OneHouse(oStar,nPanType,nEarth)

      hExplain["house_#{nHouse}"] = hData
   	  # 尋找下一個宮名
   	  nEarth = oStar.gHouse_GetNextHouseEarth(nEarth)
  	end

  	return hExplain
  end

  def we_Star_Create_HouseInfo_Explain_OneHouse(oStar,nPanType,nEarth)
  	hExplain = Hash.new
  	# 星性顯示部份：
  	hExplain["stars"] = we_Star_Create_HouseInfo_Explain_Star(oStar,nPanType,nEarth)
  	# 四化部份：
  	hExplain["fourhua"] = we_Star_Create_HouseInfo_Explain_FourHua(oStar,nPanType,nEarth)

    return hExplain
  end

  def we_Star_Create_HouseInfo_Explain_FourHua(oStar,nPanType,nEarth)
  	hExplain = Hash.new
  	(Star::FH_LU..Star::FH_GI).each do |nFourHuaIndex|
  		hData = Hash.new
  		hData["title"] = Pm.GetStr("explain.star.title.fourhua_#{nFourHuaIndex}")
  		# FourOut(文字)
  		hData["data"] = we_Star_Create_HouseInfo_Explain_FourHua_FourOut(oStar,nPanType,nEarth,nFourHuaIndex)
  		# FourK(文字)
  		hData["data"] += we_Star_Create_HouseInfo_Explain_FourHua_FourK(oStar,nPanType,nEarth,nFourHuaIndex)

  		hExplain[nFourHuaIndex + 1] = hData
  	end
  	hExplain["count"] = 4
  	return hExplain
  end
  # FourOut(文字)
  def we_Star_Create_HouseInfo_Explain_FourHua_FourOut(oStar,nPanType,nEarth,nFourHuaIndex)
  	sFh = Star.GetFourHuaStr(nFourHuaIndex)
  	sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)
  	sFourOutHouseName = oStar.gHouse_GetFourOutHouseName(nPanType,nEarth,nFourHuaIndex)
  	return FourOutData.getDesc(nPanType,sHouseName,sFourOutHouseName,sFh)
  end
  def we_Star_Create_HouseInfo_Explain_FourHua_FourK(oStar,nPanType,nEarth,nFourHuaIndex)
  	sFh = Star.GetFourHuaStr(nFourHuaIndex)
  	sHouseSky = oStar.gHouse_GetHouseSkyName(nPanType,nEarth)
  	sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)
  	return FourKData.getDesc(nPanType,sHouseSky,sHouseName,sFh)
  end

  # 星性顯示部份：
  def we_Star_Create_HouseInfo_Explain_Star(oStar,nPanType,nEarth)
  	hExplain = Hash.new
  	hExplainA = Hash.new
  	hExplainB = Hash.new
  	# 主要特徵現象
  	hExplainA["title"] = Pm.GetStr("explain.star.title.main")
  	# 主星解釋
  	hExplainA["data"] = we_Star_Create_HouseInfo_Explain_Star_Main(oStar,nPanType,nEarth)
  	hExplain["astars"] = hExplainA
  	# 次要特徵現象
  	hExplainB["title"] = Pm.GetStr("explain.star.title.sub")
  	# 其他星解釋
  	hExplainB["data"] = we_Star_Create_HouseInfo_Explain_Star_Sub(oStar,nPanType,nEarth)
  	hExplain["bstars"] = hExplainB

  	hExplainC = Hash.new
  	# 環境特徵
  	hExplainC["title"] = Pm.GetStr("explain.star.title.env")
  	# 十二長生解釋
    # 博士十二星解釋
    # 將前星解釋
    # 歲前星解釋
  	hExplainC["data"] = we_Star_Create_HouseInfo_Explain_Star_C(oStar,nPanType,nEarth)
  	hExplain["cstars"] = hExplainC

  	return hExplain
  end
  def we_Star_Create_HouseInfo_Explain_Star_Main(oStar,nPanType,nEarth)
  	hExplain = Hash.new
  	sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)
  	aStar,bUseOpp = oStar.gHouse_GetAStars(nPanType,nEarth)
  	aStarName = oStar.gHouse_GetAStarNames(nPanType,nEarth)
  	hExplain["count"] = aStar.length
  	(0...aStar.length).each do |nIndex|
  		sSanKur = oStar.gExHouse_GetAStarSanKurName(nPanType,nEarth,aStar[nIndex])
  	    hStar = Hash.new
  	    hStar["name"] = aStarName[nIndex]
  		hStar["explain"] = we_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseName,aStarName[nIndex],sSanKur)
  		hExplain[nIndex + 1] = hStar
  	end
  	return hExplain
  end
  def we_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseStay,sStar,sSanKur)
  	return TzMainData.getDesc(nPanType,sHouseStay,sStar,sSanKur)
  end
  def we_Star_Create_HouseInfo_Explain_Star_Sub(oStar,nPanType,nEarth)
  	hExplain = Hash.new
  	sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)
  	aStar = oStar.gHouse_GetBStars(nPanType,nEarth)
  	aStarName = oStar.gHouse_GetBStarNames(nPanType,nEarth)
  	hExplain["count"] = aStar.length
  	(0...aStar.length).each do |nIndex|
  		sSanKur = oStar.gExHouse_GetBStarSanKurName(nPanType,nEarth,aStar[nIndex])
  	    hStar = Hash.new
  	    hStar["name"] = aStarName[nIndex]
  		hStar["explain"] = we_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseName,aStarName[nIndex],sSanKur)
  		hExplain[nIndex + 1] = hStar
  	end
  	return hExplain
  end
  def we_Star_Create_HouseInfo_Explain_Star_C(oStar,nPanType,nEarth)
  	hExplain = Hash.new
  	count = 0
  	starTypes = [Star::DOCTOR,Star::YEARSTAR,Star::YEARGOD,Star::GOD]
  	sHouseName = oStar.gHouse_GetHouseName(nPanType,nEarth)
  	(0...starTypes.length).each do |nIndex|
  		nStar = oStar.gHouse_GetCStar(nPanType,nEarth,starTypes[nIndex])
  		sStarName = oStar.gHouse_GetCStarName(nPanType,nEarth,starTypes[nIndex])
  		sSanKur = oStar.gExHouse_GetCStarSanKurName(nPanType,nEarth,nStar,starTypes[nIndex])
  		sExplain = we_Star_Create_HouseInfo_Explain_Star_GetDescript(nPanType,sHouseName,sStarName,sSanKur)
  		if (sExplain.length > 0) then
  		  count += 1
    	  hStar = Hash.new
    	  hStar["name"] = sStarName
    	  hStar["explain"] = sExplain
    	  hExplain[count] = hStar
  		end
  	end
  	hExplain["count"] = count

  	return hExplain
  end

  def wa_Result_TenYear(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut["count"] = 10
    # 文字定義
    @hOut["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
    @hOut["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{@ExplainHouse}")
    sHouseName = Star.GetHouseName(@ExplainHouse.to_s)
    nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
    @hOut["housename"] = @Star.gHouse_GetHouseNameWithQuota(nPanType,nEarth)

    # 1、顯示該流運十年之事業宮之資料顯示，如「頁面顯示說明」。
    @hOut["explain"] = we_Star_Create_HouseInfo_Explain_OneHouse(@Star,nPanType,nEarth)

    nNextPanType = nPanType + 1

    # 2、再顯示該十年流運之十年之事業宮資料。
    #取得十年大限開始歲數
    nStarYear = @Star.g_getLargeSanStartYear()
    nYearOld = @Star.cp_getLargeSanStartYearOld()
    (0..9).each do |i|
      hExplain = Hash.new
  	  hUserAskData[Cfate::EYear] = nStarYear + i
  	  hExplain["explain"] = wa_Result_NextPanType_OneHouse(sLang,nNextPanType,hUserInfo,hUserAskData,hApFunc,hParAll,@ExplainHouse)
  	  hExplain["yearold"] = nYearOld + i
  	  hExplain["eyear"] = nStarYear + i
  	  hExplain["description_title"] = Xdate.GetYearStr(hUserAskData[Cfate::EYear])
  	  @hOut["period_#{i + 1}"] = hExplain
    end

  	return @hOut
  end
  def wa_Result_NextPanType_OneHouse(sLang,nNextPanType,hUserInfo,hUserAskData,hApFunc,hParAll,nExplainHouse)
    oStar = Star.new
   	hStarInfo = oStar.g_GetStarPanInfo(nNextPanType,hUserInfo,hUserAskData,hParAll)
   	return we_Create_OneHouse(oStar,nNextPanType,nExplainHouse)
  end
  # 各宮顯示說明(單一宮分析)
  def we_Create_OneHouse(oStar,nPanType,nExplainHouse)
	sHouseName = Star.GetHouseName(nExplainHouse.to_s)
	nEarth = oStar.g_House_FindEarth(nPanType,sHouseName)
    # 說明部分
    return we_Star_Create_HouseInfo_Explain_OneHouse(oStar,nPanType,nEarth)
  end

  def wa_Result_FlowYear(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut["count"] = 12
    # 文字定義
    @hOut["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
    @hOut["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{@ExplainHouse}")
    sHouseName = Star.GetHouseName(@ExplainHouse.to_s)
    nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
    @hOut["housename"] = @Star.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
    # 1、顯示該流運年之事業宮之資料顯示，如「頁面顯示說明」。
    @hOut["explain"] = we_Star_Create_HouseInfo_Explain_OneHouse(@Star,nPanType,nEarth)

    nNextPanType = nPanType + 1
    # 2、再顯示該年流運之十二月之事業宮資料。
    mArray = Xdate.GetEastMonthArray(hUserAskData[Cfate::EYear])
    mArray.each do |aMonth|
      hUserAskData[Cfate::EMonth] = aMonth[0]
      hUserAskData[Cfate::LeapMonth] = aMonth[2]
      hExplain = Hash.new
 	    hExplain["explain"] = wa_Result_NextPanType_OneHouse(sLang,nNextPanType,hUserInfo,hUserAskData,hApFunc,hParAll,@ExplainHouse)
 	    hExplain["description_title"] = Xdate.GetYearStr(hUserAskData[Cfate::EYear])
      hExplain["description_title"] += Xdate.GetMonthStr(hUserAskData[Cfate::EMonth],hUserAskData[Cfate::LeapMonth])
 	    @hOut[aMonth[0]] = hExplain
    end

  	return @hOut
  end

  def wa_Result_FlowMonth(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut["count"] = Xdate.GetEastMonthDays(hUserAskData[Cfate::EYear], hUserAskData[Cfate::EMonth], hUserAskData[Cfate::LeapMonth])
    # 文字定義
    @hOut["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
    @hOut["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{@ExplainHouse}")
    sHouseName = Star.GetHouseName(@ExplainHouse.to_s)
    nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
    @hOut["housename"] = @Star.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
    # 1、顯示該流運月之事業宮之資料顯示，如「頁面顯示說明」。
    @hOut["explain"] = we_Star_Create_HouseInfo_Explain_OneHouse(@Star,nPanType,nEarth)

    # nNextPanType = nPanType + 1
    # # 2、再顯示該月流運之日之事業宮資料。
    # (1..@hOut["count"].to_i).each do |i|
    #   hExplain = Hash.new
    #   hUserAskData[Cfate::EDate] = i
 	  # hExplain["explain"] = wa_Result_NextPanType_OneHouse(sLang,nNextPanType,hUserInfo,hUserAskData,hApFunc,hParAll,@ExplainHouse)
 	  # hExplain["description_title"] = hUserAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
    #   hExplain["description_title"] += hUserAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")
    #   hExplain["description_title"] += hUserAskData[Cfate::EDate].to_s + Pm.GetStr("IDS_X_DAY")
 	  # @hOut[i] = hExplain
    # end

  	return @hOut
  end

  def wa_Result_FlowDate(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
    @hOut["count"] = 12
    # 文字定義
    @hOut["description_title"] = Pm.GetStrWithColon("explain.star.title.house")
    @hOut["description_data"] = Pm.GetStr("explain.star.content.house_#{nPanType}_#{@ExplainHouse}")
    sHouseName = Star.GetHouseName(@ExplainHouse.to_s)
    nEarth = @Star.g_House_FindEarth(nPanType,sHouseName)
    @hOut["housename"] = @Star.gHouse_GetHouseNameWithQuota(nPanType,nEarth)
    # 1、顯示該流運日之事業宮之資料顯示，如「頁面顯示說明」。
    @hOut["explain"] = we_Star_Create_HouseInfo_Explain_OneHouse(@Star,nPanType,nEarth)

    # nNextPanType = nPanType + 1
    # # 2、再顯示該年日運之十二時辰之事業宮資料。
    # (1..@hOut["count"].to_i).each do |i|
    #   hExplain = Hash.new
    #   hUserAskData[Cfate::WHour] = Xdate.ETime2Hour(i - 1)
 	  # hExplain["explain"] = wa_Result_NextPanType_OneHouse(sLang,nNextPanType,hUserInfo,hUserAskData,hApFunc,hParAll,@ExplainHouse)
 	  # hExplain["description_title"] = hUserAskData[Cfate::EYear].to_s + Pm.GetStr("IDS_X_YEAR")
    #   hExplain["description_title"] += hUserAskData[Cfate::EMonth].to_s + Pm.GetStr("IDS_X_MONTH")
    #   hExplain["description_title"] += hUserAskData[Cfate::EDate].to_s + Pm.GetStr("IDS_X_DAY")
    #   hExplain["description_title"] += Earth.GetName(i) + Pm.GetStr("IDS_X_HOUR")
 	  # @hOut[i] = hExplain
    # end

  	return @hOut
  end

  def wa_Result_FlowTime(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
  end

  def wa_Result_FlowMin(sLang,nPanType,hUserInfo,hUserAskData,hApFunc,hParAll)
  end


end
