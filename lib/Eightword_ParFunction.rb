class Eightword
    # 參數使用之常數
    PAR_FIRST_TIME_TYPE = "n_par_first_time_type"
    PAR_CHUNG_SKY_TYPE = "n_par_chung_sky_type"
    PAR_PAN_GOD_BASE = "n_par_pan_god_base"
    PAR_CEN_POOL_TYPE = "n_par_cen_pool_type"
    PAR_PAN_TIMEBASE  = "n_par_pan_timebase"
    PAR_FIRST_SEGMENT = "n_par_first_segment"
    PAR_FIVE_TYPE = "n_par_five_type"
    PAR_K1 = "n_par_k1"
    PAR_K2 = "n_par_k2"
    PAR_B_TYPE = "n_par_b_type"
    PAR_B_BASE = "n_par_b_base"
    PAR_B_ADD = "n_par_b_add"
    PAR_C_BASE = "n_par_c_base"
    PAR_MID_VALUE = "n_par_mid_value"
    PAR_ENGY_DISPLAY = "n_par_engy_display"
    PAR_ANIMAL_START = "n_par_animal_start"
    PAR_LARGE_WIN = "n_par_large_win"
    PAR_TEN_GOD = "n_par_ten_god"
    PAR_TG_0 = "s_par_tg_0"
    PAR_TG_1 = "s_par_tg_1"
    PAR_TG_2 = "s_par_tg_2"
    PAR_TG_3 = "s_par_tg_3"
    PAR_TG_4 = "s_par_tg_4"
    PAR_TG_5 = "s_par_tg_5"
    PAR_TG_6 = "s_par_tg_6"
    PAR_TG_7 = "s_par_tg_7"
    PAR_TG_8 = "s_par_tg_8"
    PAR_TG_9 = "s_par_tg_9"
    PAR_SEGMENT_MOVE = "n_par_segment_move"

    def Eightword.DefaultTenGodStr(nIndex)
      return Pm.GetStr("IDS_E_MS_#{nIndex}")
    end
    # 參數預設值
    @@hUserParDefault = { Cfate::PAR_PAN_YEAR_DISPLAY => Cfate::PAN_DISPLAY_WEST,  # 年代顯示
                Eightword::PAR_FIRST_TIME_TYPE => Xdate::FIRST_NEXT,  # 子時排法
                Eightword::PAR_CHUNG_SKY_TYPE => Eightword::PAN_SKY_1,  # 子卯酉藏干對照表
                Eightword::PAR_PAN_GOD_BASE => Eightword::PAN_GOD_YEAR,  # 華蓋、驛馬、劫煞、亡神排法
                Eightword::PAR_CEN_POOL_TYPE => Eightword::CEN_POOL_YEAR, # 咸池排法
                Eightword::PAR_MID_VALUE => 100,  # 中用神
                Eightword::PAR_FIRST_SEGMENT => Xdate::SEGMENT_SPRING,  # 年柱交接 立春 冬至
                Eightword::PAR_ANIMAL_START => Xdate::ANIMAL_FIRST_SEGMENT, # 生肖交接
                Eightword::PAR_LARGE_WIN => Eightword::LARGE_WIN_VIRTUAL, # 大運起算
                Eightword::PAR_TEN_GOD => Eightword::TEN_GOD_DEFAULT, # 十神名稱設定
                Eightword::PAR_FIVE_TYPE => Eightword::FIVE_TYPE_ORG,
                Eightword::PAR_K1 => 20,
                Eightword::PAR_K2 => 30,
                Eightword::PAR_B_TYPE => Eightword::B_TYPE_MONTH,
                Eightword::PAR_B_BASE => 70,
                Eightword::PAR_B_ADD => 70,
                Eightword::PAR_C_BASE => 20,
                Eightword::PAR_PAN_TIMEBASE => Eightword::PAN_TIME_BASE_BIRTH,
                Eightword::PAR_ENGY_DISPLAY => Eightword::ENGY_COL,
                Eightword::PAR_SEGMENT_MOVE => Xdate::SegmentNow
              }
# 華蓋、驛馬、劫煞、亡神排法
# 咸池排法
# 中用神
# 十神名稱設定 正印、比肩、劫煞、食神、傷官、偏財、正財、七殺、正官、偏印(以上為預設值)，可以讓其改名稱
    @@hTenGodDefault = {  Eightword::PAR_TG_0 => Eightword.DefaultTenGodStr(0), # 比肩
                Eightword::PAR_TG_1 => Eightword.DefaultTenGodStr(1), # 劫財
                Eightword::PAR_TG_2 => Eightword.DefaultTenGodStr(2), # 偏印
                Eightword::PAR_TG_3 => Eightword.DefaultTenGodStr(3), # 正印
                Eightword::PAR_TG_4 => Eightword.DefaultTenGodStr(4), # 七殺
                Eightword::PAR_TG_5 => Eightword.DefaultTenGodStr(5), # 正官
                Eightword::PAR_TG_6 => Eightword.DefaultTenGodStr(6), # 偏財
                Eightword::PAR_TG_7 => Eightword.DefaultTenGodStr(7), # 正財
                Eightword::PAR_TG_8 => Eightword.DefaultTenGodStr(8), # 食神
                Eightword::PAR_TG_9 => Eightword.DefaultTenGodStr(9) # 傷官
              }
# 生肖交接
# 大運起算


  def pan_par_hash2var_parameters(hInput)
    pan_par_hash2var_parameter(hInput)
    pan_par_hash2var_tengod(hInput)
    pan_par_hash2var_interface(hInput)
    pan_par_hash2var_display(hInput)
    pan_par_hash2var_hallnumber(hInput)
    pan_par_hash2var_print(hInput)
  end
  def pan_par_getTenGodStr(i)
    if (@par_TenGod == Eightword::TEN_GOD_DEFAULT) then
      return Eightword.DefaultTenGodStr(i)
    else
      return @par_TenGodStr[i]
    end
  end
  def pan_par_getMainStarStr(nStar)
    # sOut = Pm.GetStr("IDS_E_MS_#{nMainStar}")
    return pan_par_getTenGodStr(nStar)
  end
  def pan_par_getSubStarStr(nStar)
    # sOut = Pm.GetStr("IDS_E_MS_#{nStar}")
    return pan_par_getTenGodStr(nStar)
  end
  def pan_par_getLifeStyleStr(nLifeStyle)
    # return Pm.GetStr("IDS_E_MS_#{nLifeStyle}")
    return pan_par_getTenGodStr(nLifeStyle)
  end

  def get_par_YearDisplay()
    return @par_YearDisplay
  end
  def pan_par_hash2var_parameter(hInput)
    @par_YearDisplay = Cfate.ParInHashValueCheck(hInput,Cfate::PAR_PAN_YEAR_DISPLAY,@@hUserParDefault)
    @par_FirstTimeType = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_FIRST_TIME_TYPE,@@hUserParDefault)
    @par_ChungSkyType = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_CHUNG_SKY_TYPE,@@hUserParDefault)
    @par_PanGodBase = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_PAN_GOD_BASE,@@hUserParDefault)
    @par_CenPoolType = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_CEN_POOL_TYPE,@@hUserParDefault)
    @par_FirstSegment = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_FIRST_SEGMENT,@@hUserParDefault)
    @par_Animal_Start = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_ANIMAL_START,@@hUserParDefault)
    @par_LargeWin = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_LARGE_WIN,@@hUserParDefault)
    @par_TenGod = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_TEN_GOD,@@hUserParDefault)
    @par_SegmentMove = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_SEGMENT_MOVE,@@hUserParDefault)


    @par_FiveBase = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_FIVE_TYPE,@@hUserParDefault)
    @par_K1 = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_K1,@@hUserParDefault)
    @par_K2 = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_K2,@@hUserParDefault)
    @par_BType = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_B_TYPE,@@hUserParDefault)
    @par_BBase = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_B_BASE,@@hUserParDefault)
    @par_BAdd = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_B_ADD,@@hUserParDefault)
    @par_CBase = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_C_BASE,@@hUserParDefault)
    @par_MidParm = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_MID_VALUE,@@hUserParDefault)
    @par_EngyDisplay = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_ENGY_DISPLAY,@@hUserParDefault)

      @par_PanTimeBase  = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_PAN_TIMEBASE,@@hUserParDefault) #流盤取時間的方法
  end
  def pan_par_hash2var_tengod(hInput)
    @par_TenGodStr = Array.new(10)
    @par_TenGodStr[0] = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_TG_0,@@hTenGodDefault)
    @par_TenGodStr[1] = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_TG_1,@@hTenGodDefault)
    @par_TenGodStr[2] = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_TG_2,@@hTenGodDefault)
    @par_TenGodStr[3] = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_TG_3,@@hTenGodDefault)
    @par_TenGodStr[4] = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_TG_4,@@hTenGodDefault)
    @par_TenGodStr[5] = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_TG_5,@@hTenGodDefault)
    @par_TenGodStr[6] = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_TG_6,@@hTenGodDefault)
    @par_TenGodStr[7] = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_TG_7,@@hTenGodDefault)
    @par_TenGodStr[8] = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_TG_8,@@hTenGodDefault)
    @par_TenGodStr[9] = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_TG_9,@@hTenGodDefault)
  end

# 顯示設定：

# 1、年代顯示：
# ◎西元年
# ◎民國年

# 2、子時顯示(頁面顯示及列印顯示)
# ◎不轉換次日，顯示晚子時。
# ◎農曆顯示次日

# 3、顯示干支五行
# ◎不顯示
# ◎顯示(預設值)

# 4、神煞名稱顯示
# ◎全部顯示(預設值)
# ◎部份顯示
# □ 天乙貴人 □ 文昌 ………等

# 5、輔助功能顯示
# ◎全部顯示(預設值)
# ◎部份顯示
# □天干合化 □地支三合 □地支六合
# □地支六沖 □地支相刑 □地支相害

# 6、起年歲顯示                                  1020824增加
# ◎立春顯示(預設值)
# ◎立冬顯示
# ◎農曆轉換顯示
# 註:轉換年歲顯示，立春後才轉換加一歲，立冬則是以立冬後加一歲。農曆轉換則是太陰月初一轉換。

# 6、疾病檢核表
# ◎顯示提示(預設值)
# ◎不顯示
# ◎自訂顯示  (自訂分數)

# 說明：
# 預設值：甲乙一組丙丁一組…，各組以大數減小數大於35以，顯示提示，例如底色有一個淡黃色顯示
    @@hUserIntDefault = {
  }
  def pan_par_hash2var_interface(hInput)
  end


  DIS_EARTH0_HOUR = "n_dis_earth0_hour"
  DIS_FIVE = "n_dis_five"
  DIS_GK_ALL = "n_dis_gk_all"
  DIS_GK_GOD = "n_dis_gk_god"
  DIS_GK_GOD_1 = "n_dis_gk_god_1"
  DIS_GK_GOD_2 = "n_dis_gk_god_2"
  DIS_GK_GOD_3 = "n_dis_gk_god_3"
  DIS_GK_GOD_4 = "n_dis_gk_god_4"
  DIS_GK_GOD_5 = "n_dis_gk_god_5"
  DIS_GK_GOD_6 = "n_dis_gk_god_6"
  DIS_GK_GOD_7 = "n_dis_gk_god_7"
  DIS_GK_GOD_8 = "n_dis_gk_god_8"
  DIS_GK_GOD_9 = "n_dis_gk_god_9"
  DIS_GK_GOD_10 = "n_dis_gk_god_10"
  DIS_GK_GOD_11 = "n_dis_gk_god_11"
  DIS_GK_GOD_12 = "n_dis_gk_god_12"
  DIS_GK_GOD_13 = "n_dis_gk_god_13"
  DIS_GK_GOD_14 = "n_dis_gk_god_14"
  DIS_GK_GOD_15 = "n_dis_gk_god_15"
  DIS_GK_GOD_16 = "n_dis_gk_god_16"
  DIS_GK_GOD_17 = "n_dis_gk_god_17"
  DIS_GK_GOD_18 = "n_dis_gk_god_18"
  DIS_GK_GOD_19 = "n_dis_gk_god_19"
  DIS_GK_GOD_20 = "n_dis_gk_god_20"
  DIS_GK_GOD_21 = "n_dis_gk_god_21"
  DIS_GK_GOD_22 = "n_dis_gk_god_22"
  DIS_GK_KILL = "n_dis_gk_kill"
  DIS_GK_KILL_1 = "n_dis_gk_kill_1"
  DIS_GK_KILL_2 = "n_dis_gk_kill_2"
  DIS_GK_KILL_3 = "n_dis_gk_kill_3"
  DIS_GK_KILL_4 = "n_dis_gk_kill_4"
  DIS_GK_KILL_5 = "n_dis_gk_kill_5"
  DIS_GK_KILL_6 = "n_dis_gk_kill_6"
  DIS_GK_KILL_7 = "n_dis_gk_kill_7"
  DIS_GK_KILL_8 = "n_dis_gk_kill_8"
  DIS_GK_KILL_9 = "n_dis_gk_kill_9"
  DIS_GK_KILL_10 = "n_dis_gk_kill_10"
  DIS_GK_KILL_11 = "n_dis_gk_kill_11"
  DIS_GK_KILL_12 = "n_dis_gk_kill_12"
  DIS_GK_KILL_13 = "n_dis_gk_kill_13"
  DIS_GK_KILL_14 = "n_dis_gk_kill_14"
  DIS_GK_KILL_15 = "n_dis_gk_kill_15"
  DIS_GK_KILL_16 = "n_dis_gk_kill_16"
  DIS_GK_KILL_17 = "n_dis_gk_kill_17"
  DIS_GK_KILL_18 = "n_dis_gk_kill_18"
  DIS_ANCILLARY_ALL = "n_dis_ancillary_all"
  DIS_ANCILLARY = "n_dis_ancillary"
  DIS_ANCILLARY_1 = "n_dis_ancillary_1"
  DIS_ANCILLARY_2 = "n_dis_ancillary_2"
  DIS_ANCILLARY_3 = "n_dis_ancillary_3"
  DIS_ANCILLARY_4 = "n_dis_ancillary_4"
  DIS_ANCILLARY_5 = "n_dis_ancillary_5"
  DIS_ANCILLARY_6 = "n_dis_ancillary_6"
  DIS_SICK = "n_dis_sick"
  DIS_SICK_VALUE = "n_dis_sick_value"
    @@hUserDisDefault = {  Eightword::DIS_EARTH0_HOUR => Eightword::DIS_E0HD_LATE, # 子時顯示(頁面顯示及列印顯示)
                 Eightword::DIS_FIVE => Eightword::DIS_FIVE_SHOW, # 顯示干支五行
                 Eightword::DIS_GK_ALL => Eightword::DIS_GK_ALL, # 神煞名稱顯示
                 Eightword::DIS_GK_GOD_1 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_2 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_3 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_4 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_5 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_6 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_7 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_8 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_9 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_10 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_11 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_12 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_13 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_14 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_15 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_16 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_17 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_18 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_19 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_20 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_21 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_GOD_22 => Cfate::PAR_TRUE, # 神名稱顯示
                 Eightword::DIS_GK_KILL_1 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_2 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_3 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_4 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_5 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_6 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_7 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_8 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_9 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_10 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_11 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_12 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_13 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_14 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_15 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_16 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_17 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_GK_KILL_18 => Cfate::PAR_TRUE, # 煞名稱顯示
                 Eightword::DIS_ANCILLARY_ALL => Cfate::PAR_TRUE, # 輔助功能顯示
                 Eightword::DIS_ANCILLARY_1 => Cfate::PAR_TRUE, # IDS_E_ADP_SKYHER : 天干合化
                 Eightword::DIS_ANCILLARY_2 => Cfate::PAR_TRUE, # IDS_E_ADP_EARTHHER3 : 地支三合
                 Eightword::DIS_ANCILLARY_3 => Cfate::PAR_TRUE, # IDS_E_ADP_EARTHHER6 : 地支六合
                 Eightword::DIS_ANCILLARY_4 => Cfate::PAR_TRUE, # IDS_E_ADP_EARTHCHUN6 : 地支六沖
                 Eightword::DIS_ANCILLARY_5 => Cfate::PAR_TRUE, # IDS_E_ADP_EARTHHSIN : 地支相刑
                 Eightword::DIS_ANCILLARY_6 => Cfate::PAR_TRUE, # IDS_E_ADP_EARTHHAI : 地支相害
                 Eightword::DIS_SICK => Eightword::DIS_SICK_SHOW, # 疾病檢核表
                 Eightword::DIS_SICK_VALUE => 35 # 疾病檢核表自訂顯示分數
  }

  def pan_par_hash2var_display(hInput)
    @dis_Earth0_Hour = Cfate.ParInHashValueCheck(hInput,Eightword::DIS_EARTH0_HOUR,@@hUserDisDefault)
    @dis_Five = Cfate.ParInHashValueCheck(hInput,Eightword::DIS_FIVE,@@hUserDisDefault)
    @dis_Sick = Cfate.ParInHashValueCheck(hInput,Eightword::DIS_SICK,@@hUserDisDefault)
    if (@dis_Sick == Eightword::DIS_SICK_SELF) then
      @dis_SickValue = Cfate.ParInHashValueCheck(hInput,Eightword::DIS_SICK_VALUE,@@hUserDisDefault)
    else
      @dis_SickValue = @@hUserDisDefault[Eightword::DIS_SICK_VALUE]
    end

    @dis_Gk_All = Cfate.ParInHashValueCheck(hInput,Eightword::DIS_GK_ALL,@@hUserDisDefault)
    @dis_Gk_God = Hash.new
    (1..Eightword.GodCount()).each do |nId|
      sKey = Eightword.getGodKey(nId)
      @dis_Gk_God[sKey] = Cfate.ParInHashValueCheck(hInput,sKey,@@hUserDisDefault)
    end
    @dis_Gk_Kill = Hash.new
    (1..Eightword.KillCount()).each do |nId|
      sKey = Eightword.getKillKey(nId)
      @dis_Gk_Kill[sKey] = Cfate.ParInHashValueCheck(hInput,sKey,@@hUserDisDefault)
    end

    @dis_Ancillary_All = Cfate.ParInHashValueCheck(hInput,Eightword::DIS_ANCILLARY_ALL,@@hUserDisDefault)
    @dis_Ancillary = Hash.new
    (1..6).each do |nId|
      sKey = Eightword.getAncillaryKey(nId)
      @dis_Ancillary[sKey] = Cfate.ParInHashValueCheck(hInput,sKey,@@hUserDisDefault)
    end
  end

  def Eightword.getGodKey(nId)
    return "#{Eightword::DIS_GK_GOD}_#{nId}"
  end
  def Eightword.getKillKey(nId)
    return "#{Eightword::DIS_GK_KILL}_#{nId}"
  end
  def par_ShowGod?(nId)
    sKey = Eightword.getGodKey(nId)
    return (@dis_Gk_God[sKey] == Cfate::PAR_TRUE)
  end
  def par_ShowKill?(nId)
    sKey = Eightword.getKillKey(nId)
    return (@dis_Gk_Kill[sKey] == Cfate::PAR_TRUE)
  end

  def Eightword.getAncillaryKey(nId)
    return "#{Eightword::DIS_ANCILLARY}_#{nId}"
  end
  def par_isAncillaryTrue?(nId)
    sKey = Eightword.getAncillaryKey(nId)
    return (@dis_Ancillary[sKey] == Cfate::PAR_TRUE)
  end

  def Eightword.MultipleAssign(sKey,hUserType,hInput,nCount,nDefault)
    (1..nCount).each do |nId|
      key = "#{sKey}_#{nId}"
      if (hInput == nil) then
        value = nDefault
      else
        value = hInput[key]
      end
      sFunc = "hUserType['#{key}'] = Cfate.ParValueCheck(key,value,nDefault)"
      eval(sFunc)
    end
    return hUserType
  end

# 3、設定堂號
# 堂號修改只有三部份，分為網頁及列印之堂號修改。
  HN_HALL_NUMBER1 = "s_hn_hall_number1"
    @@hUserHnDefault = {   #
                 Eightword::HN_HALL_NUMBER1 => Pm.GetStr("IDS_E_APP_EIGHTWORD_OWNER"), # 堂號
                 Cfate::ADV_HALL_NUMBER_1 => "", # 老師堂號介紹1
                 Cfate::ADV_HALL_NUMBER_2 => "", # 老師堂號介紹2
                 Cfate::ADV_HALL_NUMBER_3 => "" # 老師堂號介紹3
  }
  def pan_par_hash2var_hallnumber(hInput)
    @hn_Hall_Number1 = Cfate.ParInHashValueCheck(hInput,Eightword::HN_HALL_NUMBER1,@@hUserHnDefault)
    @adv_Hall_Number1 = Cfate.ParInHashValueCheck(hInput,Cfate::ADV_HALL_NUMBER_1,@@hUserHnDefault)
    @adv_Hall_Number2 = Cfate.ParInHashValueCheck(hInput,Cfate::ADV_HALL_NUMBER_2,@@hUserHnDefault)
    @adv_Hall_Number3 = Cfate.ParInHashValueCheck(hInput,Cfate::ADV_HALL_NUMBER_3,@@hUserHnDefault)
  end

  def hn_GetHallNumber1()
    if (@func_ShowHallNumber) then
      return @hn_Hall_Number1
    else
      return Pm.GetStr("IDS_E_APP_EIGHTWORD_OWNER")
    end
  end
# 列印設定：(商務功能)

# 1、列印時間頁：
# ◎同一頁
# ◎分三頁
# □第一頁時間：１～２７歲
# □第二頁時間：２８～５４歲
# □第三頁時間：５５～８１歲
# 列印命書一覽最下方文字與命盤網頁相同。
  PAR_PRINT_TIME_PAGE = "b_par_print_time_page"
  PAR_PRINT_TIME_PAGE_TYPE = "n_par_print_time_page_type"
  PAR_PRINT_TIME_PAGE_INONE = "n_par_print_time_page_inone"
  PAR_PRINT_TIME_PAGE_THREE = "n_par_print_time_page_three"
  PAR_PRINT_TIME_PAGE_THREE_PRE = "b_par_print_time_page_three_"
  PAR_PRINT_TIME_PAGE_THREE_1 = "b_par_print_time_page_three_1"
  PAR_PRINT_TIME_PAGE_THREE_2 = "b_par_print_time_page_three_2"
  PAR_PRINT_TIME_PAGE_THREE_3 = "b_par_print_time_page_three_3"
  PAR_PRINT_PAN_HEADER = "s_par_print_pan_header"  # 排盤表頭
  PAR_PRINT_SET_FOOTER = "s_par_print_set_footer"  # 時間頁頁尾說明
    @@hUserPrnDefault = {   #
                 Eightword::PAR_PRINT_TIME_PAGE => false, #
                 Eightword::PAR_PRINT_TIME_PAGE_TYPE => Cfate::PRINT_TP_INONE, #
                 Eightword::PAR_PRINT_TIME_PAGE_THREE_1 => false, #
                 Eightword::PAR_PRINT_TIME_PAGE_THREE_2 => false, #
                 Eightword::PAR_PRINT_TIME_PAGE_THREE_3 => false, #
                 Eightword::PAR_PRINT_PAN_HEADER => Pm.GetStr("IDS_E_APP_EIGHTWORD_TITLE"), #
                 Eightword::PAR_PRINT_SET_FOOTER => Pm.GetStr("IDS_E_APP_EIGHTWORD_FOOTER") #
  }
  # 列印設定
  def pan_par_hash2var_print(hInput)
    @prn_print_time_page = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_PRINT_TIME_PAGE,@@hUserPrnDefault)
    @prn_print_time_page_type = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_PRINT_TIME_PAGE_TYPE,@@hUserPrnDefault)
    @prn_ptp_three_1 = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_PRINT_TIME_PAGE_THREE_1,@@hUserPrnDefault)
    @prn_ptp_three_2 = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_PRINT_TIME_PAGE_THREE_2,@@hUserPrnDefault)
    @prn_ptp_three_3 = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_PRINT_TIME_PAGE_THREE_3,@@hUserPrnDefault)
    @prn_print_header = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_PRINT_PAN_HEADER,@@hUserPrnDefault)
    @prn_print_footer = Cfate.ParInHashValueCheck(hInput,Eightword::PAR_PRINT_SET_FOOTER,@@hUserPrnDefault)
  end
  def prn_GetPrintHeader()
    if (@func_ShowHallNumber) then
      return @prn_print_header
    else
      return Pm.GetStr("IDS_E_APP_EIGHTWORD_TITLE")
    end
  end
  def prn_GetPrintFooter()
    if (@func_ShowHallNumber) then
      return @prn_print_footer
    else
      return Pm.GetStr("IDS_E_APP_EIGHTWORD_FOOTER")
    end
  end

  def Eightword.pan_par_assign_each(nPanPar,hParAll,hUserInput)
    if (nPanPar == Eightword::PanPar_Type) then
      hOutput = Eightword.pan_par_assign_parameter(hUserInput,hParAll,@@hUserParDefault)
    elsif (nPanPar == Eightword::PanPar_TenGod) then
      hOutput = Eightword.pan_par_assign_parameter(hUserInput,hParAll,@@hTenGodDefault)
    elsif (nPanPar == Eightword::PanPar_Interface) then
      hOutput = Eightword.pan_par_assign_parameter(hUserInput,hParAll,@@hUserIntDefault)
    elsif (nPanPar == Eightword::PanPar_Display) then
      hOutput = Eightword.pan_par_assign_parameter(hUserInput,hParAll,@@hUserDisDefault)
    elsif (nPanPar == Eightword::PanPar_HallNumber) then
      hOutput = Eightword.pan_par_assign_parameter(hUserInput,hParAll,@@hUserHnDefault)
    elsif (nPanPar == Eightword::PanPar_Print) then
      hOutput = Eightword.pan_par_assign_parameter(hUserInput,hParAll,@@hUserPrnDefault)
    else
      hOutput = hParAll.clone
    end
    return hOutput
  end

  def Eightword.pan_par_init_each(nPanPar,hParAll)
    hUserType = Eightword.pan_par_assign_each(nPanPar,hParAll,nil)

    return hUserType
  end
  # 參數設定
  def Eightword.pan_par_assign_parameter(hUserInput,hParAll,hDefault)
    hOutput = hParAll.clone
    bUi_nil = false
    if (hUserInput == nil) then
      bUi_nil = true
      hUserInput = Hash.new
    end

    hDefault.each {|key,value|
      v = hUserInput[key]
      parType = Cfate.parType(key)
      if !bUi_nil && (parType == Integer || parType == TrueClass) && v == nil then
        v = 0
      end
      hOutput[key] = Cfate.ParValueCheck(key,v,value)
    }

    return hOutput
  end


    FUNC_FIVE_WANG_DISPLAY = "b_func_five_wang_display"
    FUNC_PAR_DISPLAY = "b_func_par_display"
    FUNC_4C_SKY_MM_EVENT = "b_func_4c_sky_mm_event"
    FUNC_4C_EARTH_MM_EVENT = "n_func_4c_earth_mm_event"
    FUNC_FW_SKY_MM_EVENT = "b_func_fw_sky_mm_event"
    FUNC_FW_EARTH_MM_EVENT = "n_func_fw_earth_mm_event"

    EVENT_EARTH_HER3 = 1
    EVENT_EARTH_HER6 = 2
    EVENT_EARTH_CHUON = 4
    EVENT_EARTH_HSIN = 8
    EVENT_EARTH_HAI = 16
    FUNC_4C_EARTH_MM_EVENT_DEFAULT = Eightword::EVENT_EARTH_HER3
    FUNC_FW_EARTH_MM_EVENT_DEFAULT = Eightword::EVENT_EARTH_HER3
  def Eightword.GetEarthEventValue(nId)
    if (nId == Eightword::ADP_EARTHHER3) then
      return Eightword::EVENT_EARTH_HER3
    elsif (nId == Eightword::ADP_EARTHHER6) then
      return Eightword::EVENT_EARTH_HER6
    elsif (nId == Eightword::ADP_EARTHCHUN6) then
      return Eightword::EVENT_EARTH_CHUON
    elsif (nId == Eightword::ADP_EARTHHSIN) then
      return Eightword::EVENT_EARTH_HSIN
    elsif (nId == Eightword::ADP_EARTHHAI) then
      return Eightword::EVENT_EARTH_HAI
    end
    return 0
  end

    # 參數預設值
    @@hApFuncDefault = { Eightword::FUNC_FIVE_WANG_DISPLAY => false,
                Eightword::FUNC_PAR_DISPLAY => false,
                Eightword::FUNC_4C_SKY_MM_EVENT => false,
                Eightword::FUNC_4C_EARTH_MM_EVENT => Eightword::FUNC_4C_EARTH_MM_EVENT_DEFAULT,
                Eightword::FUNC_FW_SKY_MM_EVENT => false,
                Eightword::FUNC_FW_EARTH_MM_EVENT => Eightword::FUNC_FW_EARTH_MM_EVENT_DEFAULT,
                Cfate::FUNC_SET_PARS => false,
                Cfate::FUNC_HALL_NUMBER => false
              }
  def pan_func_hash2var_parameter(hInput)
    @func_FiveWang_Display = Cfate.ParInHashValueCheck(hInput,Eightword::FUNC_FIVE_WANG_DISPLAY,@@hApFuncDefault)
    @func_Par_Display = Cfate.ParInHashValueCheck(hInput,Eightword::FUNC_PAR_DISPLAY,@@hApFuncDefault)
    @func_4cSmm_Event = Cfate.ParInHashValueCheck(hInput,Eightword::FUNC_4C_SKY_MM_EVENT,@@hApFuncDefault)
    @func_4cEmm_Event = Cfate.ParInHashValueCheck(hInput,Eightword::FUNC_4C_EARTH_MM_EVENT,@@hApFuncDefault)
    @func_FwSmm_Event = Cfate.ParInHashValueCheck(hInput,Eightword::FUNC_FW_SKY_MM_EVENT,@@hApFuncDefault)
    @func_FwEmm_Event = Cfate.ParInHashValueCheck(hInput,Eightword::FUNC_FW_EARTH_MM_EVENT,@@hApFuncDefault)
    @func_SetPars =  Cfate.ParInHashValueCheck(hInput,Cfate::FUNC_SET_PARS,@@hApFuncDefault)
    @func_ShowHallNumber =  Cfate.ParInHashValueCheck(hInput,Cfate::FUNC_HALL_NUMBER,@@hApFuncDefault)
  end

  def isParDisplay?()
    return @func_Par_Display
  end

    # 客戶設定的排盤參數
  def Eightword.get_pan_pars(userId,bCanUseIfatePars=false)
    if (bCanUseIfatePars) then
      panPar = IfatePar.check_userid_eightword(userId).first
      if (panPar == nil) then
        hUserType = Eightword.pan_par_assign(nil,userId)
      else
        hUserType = Eightword.pan_par_assign(panPar.hPars,userId)
      end
    else
      hUserType = Eightword.pan_par_assign(nil,userId)
    end
    return hUserType
  end
  def Eightword.pan_par_assign(hInput,userId)
    hUserType = Hash.new
    if (hInput == nil) then
      hUserInput = nil #Hash.new
    else
      hUserInput = hInput
    end

    (Eightword::PanPar_Type..Eightword::PanPar_Print).each do |nPanPar|
      hUserType = Eightword.pan_par_assign_each(nPanPar,hUserType,hUserInput)
    end

    return hUserType
  end

  # 客戶購買的功能參數
  def Eightword.product_func_par_assign(hdbInput,hDefault)
    if (hdbInput == nil) then
      hdbInput = Hash.new
    end
    bhasDefault = true
    if (hDefault == nil) then
      bhasDefault = false
      hDefault = Hash.new
    end
    hApFunc = Hash.new
    hDemo = Eightword.getDemoApFuncHash()
    if (hDemo == nil) then
      if (bhasDefault) then
        hDefault.each {|key,value|
          hApFunc[key] = Cfate.ParValueCheck(key,hdbInput[key],hDefault[key])
        }
      else
        hdbInput.each {|key,value|
          hApFunc[key] = Cfate.ParValueCheck(key,hdbInput[key],hDefault[key])
        }
      end
    else
      hDemo.each {|key,value|
        hApFunc[key] = Cfate.ParValueCheck(key,hDemo[key],hDefault[key])
      }
    end
    return hApFunc
  end
  def Eightword.func_assign(hInput)
    hOutput = Hash.new
    hInput.each {|key,value|
      hOutput[key] = Cfate.ParValueCheck(key,hInput[key],hInput[key])
    }
    return hOutput
  end
  def Eightword.getDemoApFuncHash()
    oProduct = Product.check_demo.check_name("eightword").last
    if (oProduct == nil) then
      return nil
    end
    return Cfate.pan_par_dbfield2hash(oProduct.func_par)
  end

    def pan_par_hash2var_all(hInput)
    pan_par_hash2var_parameters(hInput)
    end
    def pan_func_hash2var_all(hInput)
    pan_func_hash2var_parameter(hInput)
    end

end
