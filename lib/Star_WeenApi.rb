class Star
  # return Pan Info
  def wa_Pan_for_score(nPanType,hUserInfo,hUserDefDate,hUserType)
    hOut = wa_Pan(nPanType,hUserInfo,hUserDefDate,hUserType)
    lyts = []
    fas = []
    (1..12).each do |nEarth|
      astarInfos = star_GetLuYangTuoInfos(nPanType,nEarth)
      lyts.push(astarInfos)
      fa_starInfos = get_HouseFAStarInfos(nPanType,nEarth)
      fas.push(fa_starInfos)
    end
    hOut["LuYangTuos"] = lyts
    hOut["Fas"] = fas
    return hOut
  end

  def wa_Pan(nPanType,hUserInfo,hUserDefDate,hUserType)
  	g_GetPanInfo(nPanType,hUserInfo,hUserDefDate,hUserType)

    hOut = Hash.new

  	# house info
    hHouse = wap_house_info(nPanType)

  	# middle info
    hMiddle = wap_middle_info(nPanType)

    hOut["fourhuastack"] = IsSelFourHuaStack?()
    hOut["house"] = hHouse
    hOut["middle"] = hMiddle
    hOut["body_house"] = gPI_GetBodyHouseEarth()
    hOut["body_house_name"] = Pm.GetStrWithColon("IDS_S_BODY_HOUSE")

    return hOut
  end
  def wa_Pan_for_huochui(nPanType,hUserInfo,hUserDefDate,hUserType)
    g_GetPanInfo(nPanType,hUserInfo,hUserDefDate,hUserType)

    hOut = Hash.new

    # house info
    hHouse = wap_house_info_huochui(nPanType)

    # middle info
    hMiddle = wap_middle_info_huochui(nPanType)

    # hOut["fourhuastack"] = IsSelFourHuaStack?()
    hOut["house"] = hHouse
    hOut["middle"] = hMiddle

    return hOut
  end

  def wap_house_info(nPanType)
  	hHouses = Hash.new
  	(1..12).each do |nEarth|
      hHouses["#{nEarth}"] = wap_house_info_each(nPanType,nEarth)
    end
    return hHouses
  end
  def wap_house_info_huochui(nPanType)
    hHouses = Hash.new
    (1..12).each do |nEarth|
      hHouses["#{nEarth}"] = wap_house_info_each_huochui(nPanType,nEarth)
    end
    return hHouses
  end
  def wap_house_info_each_old(nPanType,nEarth)
    hHouse = Hash.new
    
    aStarInfos = gHouse_GetAStarInfos(nPanType,nEarth,false) + get_HouseFAStarInfos(nPanType,nEarth)
    hHouse["astarinfos"] = Hash[aStarInfos.each_index.zip(aStarInfos)]  # hHouse["AStarInfos"] = Hash[[*aStarInfos.map.with_index]].invert
    hHouse["doctor"] = gHouse_GetDoctorName(nPanType,nEarth)
    hHouse["god"] = gHouse_GetGodName(nPanType,nEarth)
    hHouse["yeargod"] = gHouse_GetYearGodName(nPanType,nEarth)
    hHouse["yearstar"] = gHouse_GetYearStarName(nPanType,nEarth)
    hHouse["astarmiowong"] = gHouse_GetAStarsMioWongInfo(nPanType,nEarth,false,false)
    hHouse["astarorgfourhua"] = gHouse_GetAStarsFourHua(nPanType,nEarth)
    hHouse["astarselffourhua"],hHouse["astarselffourhuacount"] = gHouse_GetAStarsSelfFourHua(nPanType,nEarth)
    hHouse["astarselffourhuadisplay"] = gHouse_GetAStarsSelfFourHuaDisplay()

    # 甲級星的天干，干找星時使用
    aStar,bUseOpp = gHouse_GetAStars(nPanType,nEarth,false)
    hHouse["astarfourhuasky"] = gNormal_FindAStarsFourHuaSky(aStar)

    sstarinfos = get_HouseSStarInfos(nPanType,nEarth)
    hHouse["sstarinfos"] = sstarinfos

    bStarInfos = gHouse_GetBStarInfos(nPanType,nEarth) + get_HouseFBStarInfos(nPanType,nEarth)
    hHouse["bstarinfos"] = Hash[bStarInfos.each_index.zip(bStarInfos)]
    hHouse["bstarmiowong"] = gHouse_GetBStarsMioWongInfo(nPanType,nEarth)

    hHouse["houseid"] = g_House_GetHouseId(nPanType,nEarth)
    hHouse["housename"] = gHouse_GetHouseNameWithQuota(nPanType,nEarth)
    hHouse["housename_normal"] = gHouse_GetHouseNameWithQuota(Cfate::PAN_NORMAL,nEarth)
    hHouse["housefivestr"] = gHouse_FiveStr(nPanType,nEarth)
    hHouse["houseskystr"] = gHouse_GetSkyName(nPanType,nEarth)
    hHouse["housesky"] = gHouse_GetSky(nPanType,nEarth)

    # 星找干時用
    hHouse["skyastarfourhua"] = gNormal_FindSkyFourHuaStar(hHouse["housesky"])

    hHouse["houseearthstr"] = gHouse_GetEarthName(nEarth)

    hHouse["star7infos"] = gHouse_Get7Star(nEarth)

    return hHouse
  end

  def wap_house_info_each(nPanType,nEarth)
  	hHouse = Hash.new
    
    aStarInfos = gHouse_GetAStarInfos(nPanType,nEarth,false) + get_HouseFAStarInfos(nPanType,nEarth)
    astars = aStarInfos.map {|x| x.first}
    hHouse["astar"] = astars  # hHouse["AStarInfos"] = Hash[[*aStarInfos.map.with_index]].invert
    hHouse["doctor"] = gHouse_GetDoctorName(nPanType,nEarth)
    hHouse["god"] = gHouse_GetGodName(nPanType,nEarth)
    hHouse["yeargod"] = gHouse_GetYearGodName(nPanType,nEarth)
    hHouse["yearstar"] = gHouse_GetYearStarName(nPanType,nEarth)

    astarmiowongs = gHouse_GetAStarsMioWongInfo(nPanType,nEarth,false,false)
    # astarmiowong = astarmiowongs.map {|x| x[1]}
    hHouse["astarmiowong"] = astarmiowongs

    hHouse["astarorgfourhua"] = gHouse_GetAStarsFourHua(nPanType,nEarth)
    hHouse["astarselffourhua"],hHouse["astarselffourhuacount"] = gHouse_GetAStarsSelfFourHua(nPanType,nEarth)
    hHouse["astarselffourhuadisplay"] = gHouse_GetAStarsSelfFourHuaDisplay()

    # 甲級星的天干，干找星時使用
    aStar,bUseOpp = gHouse_GetAStars(nPanType,nEarth,false)
    hHouse["astarfourhuasky"] = gNormal_FindAStarsFourHuaSky(aStar)

    sstarinfos = get_HouseSStarInfos(nPanType,nEarth)
    sstars = sstarinfos.map {|x| x.first}
    hHouse["sstar"] = sstars

    bStarInfos = gHouse_GetBStarInfos(nPanType,nEarth) + get_HouseFBStarInfos(nPanType,nEarth)
    bstars = bStarInfos.map {|x| x.first}
    hHouse["bstar"] = bstars
    hHouse["bstarmiowong"] = gHouse_GetBStarsMioWongInfo(nPanType,nEarth)

    hHouse["houseid"] = g_House_GetHouseId(nPanType,nEarth)
    hHouse["housename"] = gHouse_GetHouseNameWithQuota(nPanType,nEarth)
    hHouse["housename_normal"] = gHouse_GetHouseNameWithQuota(Cfate::PAN_NORMAL,nEarth)
    hHouse["housefivestr"] = gHouse_FiveStr(nPanType,nEarth)
    hHouse["houseskystr"] = gHouse_GetSkyName(nPanType,nEarth)
    hHouse["housesky"] = gHouse_GetSky(nPanType,nEarth)

    # 星找干時用
    hHouse["skyastarfourhua"] = gNormal_FindSkyFourHuaStar(hHouse["housesky"])

    hHouse["houseearthstr"] = gHouse_GetEarthName(nEarth)

    star7infos = gHouse_Get7Star(nEarth)
    # star7s = star7infos.map {|x| x[1]} 這怪怪的 2018/12/28
    star7s = star7infos[1]
    hHouse["star7infos"] = star7s

    # normal and ten year
    hHouse["small"] = gHouse_GetSmall(nEarth)
    hHouse["large_1"] = gHouse_GetLarge(nEarth)
    hHouse["large_2"] = hHouse["large_1"] + 9

    return hHouse
  end

  def wap_middle_info(nPanType)
  	hMiddle = Hash.new

    nHouse1Earth = gHouse_GetFirstHouseEarth(nPanType)
    # 三方四正，用比例的方式
    ptEarth34 = [[75,100],[25,100],[0,100],[0,75],[0,25],[0,0],[25,0],[75,0],[100,0],[100,25],[100,75],[100,100]]
    # 各宮在中宮的資訊 三方四正及 四化飛出
    hMiddle["house_in_middle"] = pan_house_in_middle_info(nPanType,nHouse1Earth,ptEarth34)

    # personal info
    hMiddle["personal_info"] = pan_personal_info(nPanType)

    return hMiddle
  end

  def pan_house_in_middle_info(nPanType,nHouse1Earth,ptEarth34)
    hHim = Hash.new
    panBg34 = Array.new(12,Star::HOUSE_34_NONE)
    pan34Pt = Array.new(4,[0,0])
    houseOutFourHua = Array.new(12) {Array.new(4,"")}

    (1..12).each do |nEarth|
      nEarthIndex = Earth.Earth2EarthIndex(nEarth)
      # 三方四正色彩顯示
      panBg34[nEarthIndex] = gHouse_Check34House(nHouse1Earth,nEarth)

      # 標示命宮三方四正
      nMiddle34 = gHouse_CheckMiddle34House(nHouse1Earth,nEarth)
      if (nMiddle34 != Star::HOUSE_34_NONE) then
        pan34Pt[nMiddle34] = ptEarth34[nEarthIndex]
      end

      # four hua fly out
      houseOutFourHua[nEarthIndex] = gHouse_GetHouseOutFourHuaStr(nPanType,nEarth)
    end
    hHim["panbg34"] = panBg34
    hHim["pan34pt"] = pan34Pt
    hHim["houseoutfourhua"] = houseOutFourHua

    return hHim
  end

  # personal info
  def pan_personal_info(nPanType)
    hPi = Hash.new

    hPi["pi_name"] = gPan_PI_Name()
    hPi["pi_lunaryearold"] = gPan_PI_LunarYearOld()
    hPi["pi_iymf"] = gPan_PI_IYMF()
    hPi["pi_animal"] = gPan_PI_Animal()
    hPi["pi_solarbirth"] = gPan_PI_SolarBirth2()
    hPi["pi_lunarbirth"] = gPan_PI_LunarBirth2()
    hPi["pi_ganzhibirth"] = gPan_PI_GanZhiBirth()
    hPi["pi_segment_ganzhibirth"]  = gPan_PI_SegmentGanZhiBirth()

    hPi["pi_fivetype"] = gPan_PI_FiveType()
    hPi["pi_lifehouse"] = gPan_PI_LifeHouse()
    hPi["pi_bodyhouse"] = gPan_PI_BodyHouse()
    hPi["pi_life"] = gPan_PI_Life()
    hPi["pi_body"] = gPan_PI_Body()
    hPi["pi_god"] = gPan_PI_God()
    hPi["ci_data1"] = Star.gPan_CI_Data1(nil,nil)
    hPi["ci_data2"] = Star.gPan_CI_Data2(nil,nil)
    hPi["ci_data3"] = "http://www.profate.com.tw"

    hPi["di_info"] = Array.new(3,"")
    hPi["di_info"][0],hPi["di_info"][1],hPi["di_info"][2] = gHouse_GetFlowTimeStr(nPanType)

    return hPi
  end

  # 火錘公司
  def wap_house_info_each_huochui(nPanType,nEarth)
    hHouse = Hash.new
    
    aStarInfos = gHouse_GetAStarInfos(nPanType,nEarth,false) + get_HouseFAStarInfos(nPanType,nEarth)
    astars = aStarInfos.map {|x| x.first}
    hHouse["astar"] = astars  # hHouse["AStarInfos"] = Hash[[*aStarInfos.map.with_index]].invert
    hHouse["astar_count"] = astars.length
    hHouse["doctor"] = gHouse_GetDoctorName(nPanType,nEarth)
    hHouse["god"] = gHouse_GetGodName(nPanType,nEarth)
    hHouse["yeargod"] = gHouse_GetYearGodName(nPanType,nEarth)
    hHouse["yearstar"] = gHouse_GetYearStarName(nPanType,nEarth)

    astarmiowongs = gHouse_GetAStarsMioWongInfo(nPanType,nEarth,false,false)
    # astarmiowong = astarmiowongs.map {|x| x[1]}
    a = []
    if (astarmiowongs != nil) then
      astarmiowongs.each do |miowongs|
        a.push(miowongs[1])
      end
    end
    hHouse["astarmiowong"] = a.clone

    astarorgfourhuas = gHouse_GetAStarsFourHua(nPanType,nEarth)
    a = []
    if (astarorgfourhuas != nil) then
      astarorgfourhuas.each do |orgfourhuas|
        a.push(orgfourhuas[1])
      end
    end
    hHouse["astarorgfourhua"] = a.clone

    astarselffourhuas,astarselffourhuacount = gHouse_GetAStarsSelfFourHua(nPanType,nEarth)
    a = []
    if (astarselffourhuas != nil) then
      astarselffourhuas.each do |selffourhuas|
        a.push(selffourhuas[1])
      end
    end
    hHouse["astarselffourhua"] = a.clone
    # hHouse["astarselffourhuadisplay"] = gHouse_GetAStarsSelfFourHuaDisplay()

    # 甲級星的天干，干找星時使用
    # aStar,bUseOpp = gHouse_GetAStars(nPanType,nEarth,false)
    # hHouse["astarfourhuasky"] = gNormal_FindAStarsFourHuaSky(aStar)

    sstarinfos = get_HouseSStarInfos(nPanType,nEarth)
    sstars = sstarinfos.map {|x| x.first}
    hHouse["sstar"] = sstars
    hHouse["sstar_count"] = sstars.length

    bStarInfos = gHouse_GetBStarInfos(nPanType,nEarth) + get_HouseFBStarInfos(nPanType,nEarth)
    bstars = bStarInfos.map {|x| x.first}
    hHouse["bstar"] = bstars
    hHouse["bstar_count"] = bstars.length

    bstarmiowongs = gHouse_GetBStarsMioWongInfo(nPanType,nEarth)
    a = []
    if (bstarmiowongs != nil) then
      bstarmiowongs.each do |infos|
        a.push(infos[1])
      end
    end
    hHouse["bstarmiowong"] = a.clone

    # hHouse["houseid"] = g_House_GetHouseId(nPanType,nEarth)
    hHouse["housename"] = gHouse_GetHouseNameWithQuota(nPanType,nEarth)
    hHouse["housename_normal"] = gHouse_GetHouseNameWithQuota(Cfate::PAN_NORMAL,nEarth)
    hHouse["housefivestr"] = gHouse_FiveStr(nPanType,nEarth)
    hHouse["houseskystr"] = gHouse_GetSkyName(nPanType,nEarth)
    # hHouse["housesky"] = gHouse_GetSky(nPanType,nEarth)

    # 星找干時用
    # hHouse["skyastarfourhua"] = gNormal_FindSkyFourHuaStar(hHouse["housesky"])

    hHouse["houseearthstr"] = gHouse_GetEarthName(nEarth)

    star7infos = gHouse_Get7Star(nEarth)
    # star7s = star7infos.map {|x| x[1]} 這怪怪的 2018/12/28
    star7s = star7infos[1]
    # hHouse["star7infos"] = star7s

    # normal and ten year
    hHouse["small"] = gHouse_GetSmall(nEarth)
    hHouse["large_1"] = gHouse_GetLarge(nEarth)
    hHouse["large_2"] = hHouse["large_1"] + 9

    return hHouse
  end
  def wap_middle_info_huochui(nPanType)
    hMiddle = Hash.new

    nHouse1Earth = gHouse_GetFirstHouseEarth(nPanType)
    # 三方四正，用比例的方式
    ptEarth34 = [[75,100],[25,100],[0,100],[0,75],[0,25],[0,0],[25,0],[75,0],[100,0],[100,25],[100,75],[100,100]]
    # 各宮在中宮的資訊 三方四正及 四化飛出
    hMiddle["house_in_middle"] = pan_house_in_middle_info_huochui(nPanType,nHouse1Earth,ptEarth34)

    # personal info
    hMiddle["personal_info"] = pan_personal_info(nPanType)

    return hMiddle
  end

  def pan_house_in_middle_info_huochui(nPanType,nHouse1Earth,ptEarth34)
    hHim = Hash.new
    panBg34 = Array.new(12,Star::HOUSE_34_NONE)
    pan34Pt = Array.new(4,[0,0])
    houseOutFourHua = Array.new(12) {Array.new(4,"")}

    (1..12).each do |nEarth|
      nEarthIndex = Earth.Earth2EarthIndex(nEarth)
      # 三方四正色彩顯示
      panBg34[nEarthIndex] = gHouse_Check34House(nHouse1Earth,nEarth)

      # 標示命宮三方四正
      nMiddle34 = gHouse_CheckMiddle34House(nHouse1Earth,nEarth)
      if (nMiddle34 != Star::HOUSE_34_NONE) then
        pan34Pt[nMiddle34] = ptEarth34[nEarthIndex]
      end

      # four hua fly out
      houseOutFourHua[nEarthIndex] = gHouse_GetHouseOutFourHuaStr(nPanType,nEarth)
    end
    hHim["panbg34"] = panBg34
    hHim["pan34pt"] = pan34Pt
    # hHim["houseoutfourhua"] = houseOutFourHua

    return hHim
  end

  def gWeen_GetFlowTimeStr(nPanType)
    sFlowTime1 = ""
    if (nPanType == Cfate::PAN_NORMAL) then
    elsif (nPanType == Cfate::PAN_TENYEAR)
      sFlowTime1 = gWeen_GetFlowTimeStr_TenYear(nPanType)
    elsif (nPanType >= Cfate::PAN_FLOWYEAR)
      sFlowTime1 = gWeen_GetFlowTimeStr_FlowYear(nPanType)
    end

    return sFlowTime1
  end
  def gWeen_GetFlowTimeStr_TenYear(nPanType)
    nStartYear = g_getLargeSanStartYear()
    nStarYearOld = gNormal_GetYearOld(uig_EY(),nStartYear)

    sFlowTime1 = "#{nStarYearOld} -- #{nStarYearOld + 9}#{Pm.GetStr("IDS_S_YEAR_OLD")}"
    # sFlowTime1 = Cfate.GetPanFullNameWithColon(nPanType)
    # sFlowTime1 += "#{nStarYearOld} -- #{nStarYearOld + 9}#{Pm.GetStr("IDS_S_YEAR_OLD")}"

    sFlowTime2 = Pm.GetStrWithColon("IDS_S_UI_LUNAR_CAL")
    sFlowTime2 += "#{nStartYear} -- #{nStartYear + 9}#{Pm.GetStr("IDS_X_YEAR")}"

    return sFlowTime1,sFlowTime2
  end

  def gWeen_GetFlowTimeStr_FlowYear(nPanType)
    sFlowTime1 = gWeen_GetFlowTimeStr_PanInfo(nPanType)

    return sFlowTime1
  end
  def gWeen_GetFlowTimeStr_PanInfo(nPanType)
    sFlowTime1 = Pm.GetStrWithColon("IDS_S_UI_SOLAR_CAL")
    sFlowTime2 = Pm.GetStrWithColon("IDS_S_UI_LUNAR_CAL")
    if (nPanType == Cfate::PAN_FLOWYEAR) then
      sFlowTime1 += Xdate.YearDisplayOnlyNumber(fdg_WY(nPanType),@par_YearDisplay)
      sFlowTime2 += Xdate.GetFullEDateTimeNumStr(fdg_EY(nPanType),0,0,-1,false,@par_YearDisplay)
    elsif (nPanType == Cfate::PAN_FLOWMONTH) then
      sFlowTime1 += Xdate.GetFullWDateStr(fdg_WY(nPanType),fdg_WM(nPanType),0,@par_YearDisplay)
      sFlowTime2 += Xdate.GetFullEDateTimeNumStr(fdg_EY(nPanType),fdg_EM(nPanType),0,-1,fdg_EL(nPanType),@par_YearDisplay)
    elsif (nPanType == Cfate::PAN_FLOWDATE) then
      sFlowTime1 += Xdate.GetFullWDateStr(fdg_WY(nPanType),fdg_WM(nPanType),fdg_WD(nPanType),@par_YearDisplay)
      sFlowTime2 += Xdate.GetFullEDateTimeNumStr(fdg_EY(nPanType),fdg_EM(nPanType),fdg_ED(nPanType),-1,fdg_EL(nPanType),@par_YearDisplay)
    elsif (nPanType == Cfate::PAN_FLOWTIME) then
      sFlowTime1 += Xdate.GetFullWDateStr(fdg_WY(nPanType),fdg_WM(nPanType),fdg_WD(nPanType),@par_YearDisplay)
      sFlowTime2 += Xdate.GetFullEDateTimeNumStr(fdg_EY(nPanType),fdg_EM(nPanType),fdg_ED(nPanType),fdg_ET(nPanType),fdg_EL(nPanType),@par_YearDisplay)
    end

    # return sFlowTime1 + " " + sFlowTime2
    return sFlowTime1,sFlowTime2
  end


  # 輸出論斷增加內容，消費者清楚了解問的項目。
  # 姓名： 王大明 性別：男
  # ＝》出生日期：國曆 1971 年 12月 29 日14 時10 分 農曆 辛亥 年 11 月 12 日 未 時
  # ＝》財帛命盤分析時間：44 -- 53歲（農曆：2006 -- 2015年）
  # ＝》財帛命盤分析時間：46歲 丙申年 2016年
  # ＝》財帛命盤分析時間：46歲 丙申年一月 2016年2月8日～3月8日
  # ＝》財帛命盤分析時間：46歲 丙申年1/6（乙丑日） 2016年2月13日
  def gWeen_GetLunduanTaitou(nPanType)
    sFlowTime1 = ""
    if (nPanType == Cfate::PAN_TENYEAR)
      sFlowTime1 = gWeen_GetLunduanTaitou_TenYear(nPanType)
    elsif (nPanType == Cfate::PAN_FLOWYEAR)
      sFlowTime1 = gWeen_GetLunduanTaitou_FlowYear(nPanType)
    elsif (nPanType == Cfate::PAN_FLOWMONTH) then
      sFlowTime1 = gWeen_GetLunduanTaitou_FlowMonth(nPanType)
    elsif (nPanType == Cfate::PAN_FLOWDATE) then
      sFlowTime1 = gWeen_GetLunduanTaitou_FlowDate(nPanType)
    elsif (nPanType >= Cfate::PAN_FLOWTIME) then
      sFlowTime1 = gWeen_GetLunduanTaitou_FlowTime(nPanType)
    end

    return sFlowTime1
  end
  # 44 -- 53歲（農曆：2006 -- 2015年）
  def gWeen_GetLunduanTaitou_TenYear(nPanType)
    nStartYear = g_getLargeSanStartYear()
    nStarYearOld = gNormal_GetYearOld(uig_EY(),nStartYear)

    sFlowTime1 = "#{nStarYearOld} -- #{nStarYearOld + 9}#{Pm.GetStr("IDS_S_YEAR_OLD")}"
    # sFlowTime1 = Cfate.GetPanFullNameWithColon(nPanType)
    # sFlowTime1 += "#{nStarYearOld} -- #{nStarYearOld + 9}#{Pm.GetStr("IDS_S_YEAR_OLD")}"

    sFlowTime2 = "(#{Pm.GetStrWithColon("IDS_S_UI_LUNAR_CAL")}"
    sFlowTime2 += "#{nStartYear} -- #{nStartYear + 9}#{Pm.GetStr("IDS_X_YEAR")})"

    return sFlowTime1 + sFlowTime2
  end
  # 46歲 丙申年 2016年
  def gWeen_GetLunduanTaitou_FlowYear(nPanType)
    sFlowTime1 = gWeen_GetLunduanTaitou_YearOld(nPanType)
    sFlowTime1 += " " #Cfate.GetSpace(1)
    nEYear, nEMonth, nEDate, bLeapMonth, nETime = fdg_E_YMDLT(nPanType)
    sFlowTime1 += Xdate.GetLunarYearGanZhiStr(nEYear, nEMonth, nEDate, bLeapMonth, nETime)
    sFlowTime1 += " "
    sFlowTime1 += Xdate.YearDisplayStr(nEYear,Cfate::PAN_DISPLAY_WEST)
    return sFlowTime1
  end
  def gWeen_GetLunduanTaitou_YearOld(nPanType)
    return Xdate.GetYearOldStr(cp_GetFlowYearOld(nPanType))
  end
  # 46歲 丙申年一月 2016年2月8日～3月8日
  def gWeen_GetLunduanTaitou_FlowMonth(nPanType)
    sFlowTime1 = gWeen_GetLunduanTaitou_YearOld(nPanType)
    sFlowTime1 += " "
    nEYear, nEMonth, nEDate, bLeapMonth, nETime = fdg_E_YMDLT(nPanType)
    nWYear, nWMonth, nWDate = fdg_W_YMD(nPanType)
    sFlowTime1 += Xdate.GetLunarYearGanZhiStr(nEYear, nEMonth, nEDate, bLeapMonth, nETime)
    sFlowTime1 += Xdate.emonth_guo_zi(nEMonth,bLeapMonth)
    sFlowTime1 += " "

    y,m,d = Xdate.East2West(nEYear,nEMonth,1,bLeapMonth)
    nDays = Xdate.GetEMonthDays(nEYear,nEMonth,bLeapMonth)
    y2,m2,d2 = Xdate.NextWDates(y,m,d,nDays-1)
    sFlowTime1 += Xdate.GetFullWDateStr2(y,m,d,Cfate::PAN_DISPLAY_WEST)
    sFlowTime1 += Pm.GetStr("IDS_S_CAL_TODATE")
    sFlowTime1 += Xdate.GetFullWDateStr2(0,m2,d2,Cfate::PAN_DISPLAY_WEST)

    return sFlowTime1
  end
  # 46歲 丙申年1/6（乙丑日） 2016年2月13日
  def gWeen_GetLunduanTaitou_FlowDate(nPanType)
    nWYear, nWMonth, nWDate, nWHour = fdg_W_YMDH(nPanType)
    sFlowTime1 = gWeen_GetLunduanTaitou_YearOld(nPanType)
    sFlowTime1 += " "
    sFlowTime1 += Xdate.GetLunar8Words_Str5(nWYear, nWMonth,nWDate, nWHour)
    sFlowTime1 += " "
    sFlowTime1 += Xdate.GetFullWDateStr2(nWYear, nWMonth, nWDate,Cfate::PAN_DISPLAY_WEST)
    return sFlowTime1
  end
  def gWeen_GetLunduanTaitou_FlowTime(nPanType)
    nWYear, nWMonth, nWDate, nWHour = fdg_W_YMDH(nPanType)
    nETime = fdg_ET(nPanType)
    sFlowTime1 = gWeen_GetLunduanTaitou_FlowDate(nPanType)
    sFlowTime1 += " "
    sFlowTime1 += Xdate.GetETimeStr(nETime)
    return sFlowTime1
  end
end
