# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2017-05-05 02:19:13 +0800 using RuboCop version 0.48.1.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 24
Lint/AmbiguousBlockAssociation:
  Exclude:
    - 'app/models/application_record.rb'
    - 'app/models/location.rb'
    - 'app/models/node.rb'
    - 'app/models/reply.rb'
    - 'app/models/topic.rb'
    - 'app/models/user.rb'

# Offense count: 1
# Configuration parameters: AllowSafeAssignment.
Lint/AssignmentInCondition:
  Exclude:
    - 'lib/capistrano/template.rb'

# Offense count: 34
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleAlignWith, SupportedStylesAlignWith.
# SupportedStylesAlignWith: either, start_of_block, start_of_line
Lint/BlockAlignment:
  Exclude:
    - 'lib/Eightword_BigWin.rb'
    - 'lib/Eightword_Func_Pan.rb'
    - 'lib/Eightword_Func_Pan_Change.rb'
    - 'lib/Eightword_Pan_fi.rb'
    - 'lib/Eightword_WeenApi.rb'
    - 'lib/Score_StarFunc.rb'
    - 'lib/Star_WeenApi.rb'

# Offense count: 88
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleAlignWith, SupportedStylesAlignWith, AutoCorrect.
# SupportedStylesAlignWith: start_of_line, def
Lint/DefEndAlignment:
  Enabled: false

# Offense count: 8
Lint/DuplicateMethods:
  Exclude:
    - 'lib/Eightword_Func_Pan.rb'
    - 'lib/PanWeb_ClassFunction.rb'
    - 'lib/StarInfo_Function.rb'
    - 'lib/Wannianli.rb'
    - 'lib/Xuexi_Ziwei_timu.rb'
    - 'lib/Xuexi_Ziwei_xuexi.rb'

# Offense count: 187
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleAlignWith, SupportedStylesAlignWith, AutoCorrect.
# SupportedStylesAlignWith: keyword, variable, start_of_line
Lint/EndAlignment:
  Enabled: false

# Offense count: 10
# Cop supports --auto-correct.
Lint/LiteralInInterpolation:
  Exclude:
    - 'app/controllers/application_controller.rb'
    - 'app/helpers/ifate/eightword_helper.rb'
    - 'lib/Pm.rb'
    - 'lib/Xdate.rb'

# Offense count: 1
Lint/ParenthesesAsGroupedExpression:
  Exclude:
    - 'lib/Xdate.rb'

# Offense count: 1
Lint/RandOne:
  Exclude:
    - 'lib/Xdate.rb'

# Offense count: 1
Lint/RequireParentheses:
  Exclude:
    - 'app/controllers/ifate/function_controller.rb'

# Offense count: 14
Lint/ShadowingOuterLocalVariable:
  Exclude:
    - 'app/controllers/admin/accounting_controller.rb'
    - 'app/controllers/ifate/star_controller.rb'
    - 'lib/Divination_data.rb'
    - 'lib/Eightword_BigWin.rb'
    - 'lib/Eightword_Func_Pan.rb'
    - 'lib/Star_ParFunction.rb'
    - 'lib/Xdate.rb'
    - 'lib/Xdate_json.rb'
    - 'lib/Xuexi_Ziwei_timu.rb'

# Offense count: 6
# Cop supports --auto-correct.
Lint/UnifiedInteger:
  Exclude:
    - 'app/helpers/ifate/customer_helper.rb'
    - 'lib/Cfate.rb'
    - 'lib/Eightword_Constant.rb'
    - 'lib/Eightword_Func_Pan.rb'

# Offense count: 123
# Cop supports --auto-correct.
# Configuration parameters: IgnoreEmptyBlocks, AllowUnusedKeywordArguments.
Lint/UnusedBlockArgument:
  Enabled: false

# Offense count: 568
# Cop supports --auto-correct.
# Configuration parameters: AllowUnusedKeywordArguments, IgnoreEmptyMethods.
Lint/UnusedMethodArgument:
  Enabled: false

# Offense count: 32
# Configuration parameters: ContextCreatingMethods, MethodCreatingMethods.
Lint/UselessAccessModifier:
  Exclude:
    - 'app/controllers/admin/accounting_controller.rb'
    - 'app/controllers/api/v1/ween_billings_controller.rb'
    - 'app/controllers/ifate/customer_controller.rb'
    - 'app/controllers/ifate/eightword_controller.rb'
    - 'app/controllers/ifate/star_controller.rb'
    - 'app/controllers/ifate/xdate_controller.rb'
    - 'app/controllers/service/allpay_controller.rb'
    - 'app/controllers/service/paymentcenter_controller.rb'
    - 'app/controllers/service/products_controller.rb'
    - 'app/controllers/service/store_controller.rb'

# Offense count: 1121
Lint/UselessAssignment:
  Enabled: false

# Offense count: 74
Lint/Void:
  Exclude:
    - 'app/helpers/xuexi/ziwei_helper.rb'
    - 'lib/Eightword_BigWin.rb'
    - 'lib/Eightword_InstanceVariable.rb'
    - 'lib/Explain_StarFunction.rb'
    - 'lib/PanWeb_StarFunction.rb'
    - 'lib/PanXml_StarFunction.rb'
    - 'lib/Score_StarFunc.rb'
    - 'lib/Star_Func_Score.rb'
    - 'lib/Star_InstanceVariable.rb'

# Offense count: 83
# Configuration parameters: CountBlocks.
Metrics/BlockNesting:
  Max: 7

# Offense count: 13
# Configuration parameters: CountComments.
Metrics/ClassLength:
  Max: 13364

# Offense count: 119
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, IgnoredPatterns.
# URISchemes: http, https
Metrics/LineLength:
  Max: 6293

# Offense count: 169
# Configuration parameters: CountKeywordArgs.
Metrics/ParameterLists:
  Max: 15

# Offense count: 5
# Cop supports --auto-correct.
Performance/Casecmp:
  Exclude:
    - 'app/helpers/ifate/customer_helper.rb'
    - 'lib/Xdate_farmer.rb'

# Offense count: 3
# Cop supports --auto-correct.
Performance/RedundantMatch:
  Exclude:
    - 'lib/Health_bbbot.rb'

# Offense count: 2
# Cop supports --auto-correct.
Performance/RegexpMatch:
  Exclude:
    - 'app/models/reply.rb'
    - 'app/models/topic.rb'

# Offense count: 5
# Cop supports --auto-correct.
Performance/StringReplacement:
  Exclude:
    - 'lib/Controller_Api.rb'
    - 'lib/Health.rb'
    - 'lib/Xuexi_Ziwei_timu.rb'

# Offense count: 120
Security/Eval:
  Enabled: false

# Offense count: 9
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, IndentationWidth.
# SupportedStyles: outdent, indent
Style/AccessModifierIndentation:
  Exclude:
    - 'app/controllers/ifate/xdate_controller.rb'
    - 'app/controllers/service/store_controller.rb'

# Offense count: 31
Style/AccessorMethodName:
  Exclude:
    - 'app/controllers/application_controller.rb'
    - 'app/controllers/ifate/eightword_controller.rb'
    - 'app/controllers/ifate/function_controller.rb'
    - 'app/controllers/ifate/star_controller.rb'
    - 'app/controllers/ifate/wannianli_controller.rb'
    - 'app/controllers/xuexi/ziwei_controller.rb'
    - 'app/controllers/xuexi/ziweipan_controller.rb'
    - 'app/controllers/xuexi/ziweipars_controller.rb'
    - 'lib/Controller_Api.rb'
    - 'lib/Eightword_ParFunction.rb'
    - 'lib/Health.rb'
    - 'lib/Star.rb'
    - 'lib/Star_Func_Pan.rb'
    - 'lib/Xuexi_Ziwei_timu.rb'

# Offense count: 396
# Cop supports --auto-correct.
Style/AlignArray:
  Exclude:
    - 'app/helpers/ifate/eightword_helper.rb'
    - 'lib/AllpayConstant.rb'
    - 'lib/Divination_explain_ganqing.rb'
    - 'lib/Divination_explain_qiucai.rb'
    - 'lib/Eightword_ClassVariable.rb'
    - 'lib/Eightword_Constant.rb'
    - 'lib/Star_ClassVariable.rb'
    - 'lib/Star_Func_Score.rb'
    - 'lib/Xdate_Variable.rb'
    - 'lib/Xuexi_Ziwei_timu.rb'
    - 'lib/Ziwei.rb'
    - 'lib/Ziwei_Han.rb'

# Offense count: 236
# Cop supports --auto-correct.
# Configuration parameters: EnforcedHashRocketStyle, SupportedHashRocketStyles, EnforcedColonStyle, SupportedColonStyles, EnforcedLastArgumentHashStyle, SupportedLastArgumentHashStyles.
# SupportedHashRocketStyles: key, separator, table
# SupportedColonStyles: key, separator, table
# SupportedLastArgumentHashStyles: always_inspect, always_ignore, ignore_implicit, ignore_explicit
Style/AlignHash:
  Exclude:
    - 'app/controllers/application_controller.rb'
    - 'lib/Eightword_ParFunction.rb'
    - 'lib/Name_pair_land.rb'
    - 'lib/Name_pair_mk.rb'
    - 'lib/Name_pair_shen.rb'
    - 'lib/Name_pair_sky.rb'
    - 'lib/Name_word.rb'
    - 'lib/Star_ClassVariable.rb'
    - 'lib/Star_ParFunction.rb'
    - 'lib/Xuexi_Ziwei.rb'
    - 'lib/Zeri.rb'

# Offense count: 41
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: always, conditionals
Style/AndOr:
  Exclude:
    - 'app/models/user_ap.rb'
    - 'lib/GNum_mean.rb'
    - 'lib/GNum_turn.rb'
    - 'lib/Name.rb'
    - 'lib/Name_pair.rb'

# Offense count: 34
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, ProceduralMethods, FunctionalMethods, IgnoredMethods.
# SupportedStyles: line_count_based, semantic, braces_for_chaining
# ProceduralMethods: benchmark, bm, bmbm, create, each_with_object, measure, new, realtime, tap, with_object
# FunctionalMethods: let, let!, subject, watch
# IgnoredMethods: lambda, proc, it
Style/BlockDelimiters:
  Exclude:
    - 'app/controllers/service/store_controller.rb'
    - 'app/helpers/ifate/customer_helper.rb'
    - 'app/helpers/service/store_helper.rb'
    - 'app/views/topics/feed.builder'
    - 'lib/Controller_Api_Allpay.rb'
    - 'lib/Divination_parameter.rb'
    - 'lib/Eightword_ParFunction.rb'
    - 'lib/Health.rb'
    - 'lib/Pm.rb'
    - 'lib/Wannianli.rb'
    - 'lib/WeenApi.rb'
    - 'lib/Xdate.rb'
    - 'lib/Xuexi_Ziwei.rb'

# Offense count: 5
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: braces, no_braces, context_dependent
Style/BracesAroundHashParameters:
  Exclude:
    - 'app/controllers/api/v1/ween_billings_controller.rb'
    - 'lib/Controller_Api_Allpay.rb'
    - 'lib/Health_bbbot.rb'
    - 'lib/WeenApi.rb'

# Offense count: 141
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, IndentOneStep, IndentationWidth.
# SupportedStyles: case, end
Style/CaseIndentation:
  Exclude:
    - 'app/controllers/service/store_controller.rb'
    - 'app/helpers/ifate/eightword_helper.rb'
    - 'app/helpers/service/store_helper.rb'
    - 'lib/PanGiSuong_StarWeenApi.rb'
    - 'lib/PanGiSuong_WeenApi.rb'
    - 'lib/PanGiSuong_ziweiWeenApi.rb'
    - 'lib/Score_StarFunc.rb'
    - 'lib/Score_StarWeenApi.rb'
    - 'lib/Star_Func_Pan.rb'

# Offense count: 25
Style/ClassAndModuleCamelCase:
  Exclude:
    - 'lib/AllpayConstant.rb'
    - 'lib/Controller_Api.rb'
    - 'lib/Controller_Api_Allpay.rb'
    - 'lib/Controller_Api_Order.rb'
    - 'lib/Controller_Api_Profate_Order.rb'
    - 'lib/Pm_Constant_Big5.rb'
    - 'lib/Pm_Constant_Eng.rb'
    - 'lib/Xuexi_Ziwei.rb'
    - 'lib/Xuexi_Ziwei_api.rb'
    - 'lib/Xuexi_Ziwei_chuangguan.rb'
    - 'lib/Xuexi_Ziwei_lianxi.rb'
    - 'lib/Xuexi_Ziwei_timu.rb'
    - 'lib/Xuexi_Ziwei_xuexi.rb'
    - 'lib/Ziwei_Han.rb'

# Offense count: 53
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: nested, compact
Style/ClassAndModuleChildren:
  Enabled: false

# Offense count: 2410
# Cop supports --auto-correct.
Style/ClassMethods:
  Enabled: false

# Offense count: 329
Style/ClassVars:
  Enabled: false

# Offense count: 703
# Cop supports --auto-correct.
Style/CommentIndentation:
  Enabled: false

# Offense count: 253
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, SingleLineConditionsOnly, IncludeTernaryExpressions.
# SupportedStyles: assign_to_condition, assign_inside_condition
Style/ConditionalAssignment:
  Enabled: false

# Offense count: 380
Style/ConstantName:
  Enabled: false

# Offense count: 907
# Cop supports --auto-correct.
Style/DefWithParentheses:
  Enabled: false

# Offense count: 119
# Cop supports --auto-correct.
Style/ElseAlignment:
  Enabled: false

# Offense count: 7
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: empty, nil, both
Style/EmptyElse:
  Exclude:
    - 'lib/PanWeb_StarFunction.rb'
    - 'lib/Xdate_lunar.rb'
    - 'lib/Xuexi_Ziwei_timu.rb'

# Offense count: 6
# Cop supports --auto-correct.
Style/EmptyLineAfterMagicComment:
  Exclude:
    - 'lib/Customerparser.rb'
    - 'lib/FourKData.rb'
    - 'lib/FourOutData.rb'
    - 'lib/Health.rb'
    - 'lib/Health_bbbot.rb'
    - 'lib/TzMainData.rb'

# Offense count: 3561
# Cop supports --auto-correct.
# Configuration parameters: AllowAdjacentOneLineDefs, NumberOfEmptyLines.
Style/EmptyLineBetweenDefs:
  Enabled: false

# Offense count: 162
# Cop supports --auto-correct.
Style/EmptyLines:
  Enabled: false

# Offense count: 90
# Cop supports --auto-correct.
Style/EmptyLinesAroundAccessModifier:
  Enabled: false

# Offense count: 5
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: empty_lines, no_empty_lines
Style/EmptyLinesAroundBlockBody:
  Exclude:
    - 'app/controllers/service/store_controller.rb'
    - 'lib/Explain_StarFunction.rb'
    - 'lib/Score_StarFunc.rb'
    - 'lib/homeland/pipeline/profate_filter.rb'

# Offense count: 105
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: empty_lines, empty_lines_except_namespace, empty_lines_special, no_empty_lines
Style/EmptyLinesAroundClassBody:
  Enabled: false

# Offense count: 20
# Cop supports --auto-correct.
Style/EmptyLinesAroundMethodBody:
  Enabled: false

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: empty_lines, empty_lines_except_namespace, empty_lines_special, no_empty_lines
Style/EmptyLinesAroundModuleBody:
  Exclude:
    - 'app/helpers/ifate/eightword_helper.rb'
    - 'app/helpers/xuexi/ziweipan_helper.rb'
    - 'app/helpers/xuexi/ziweipars_helper.rb'

# Offense count: 982
# Cop supports --auto-correct.
Style/EmptyLiteral:
  Enabled: false

# Offense count: 9
# Cop supports --auto-correct.
Style/EvenOdd:
  Exclude:
    - 'lib/Eightword_Func_Pan.rb'
    - 'lib/Eightword_Func_Pan_Ui.rb'
    - 'lib/SearchParser.rb'
    - 'lib/Xdate.rb'
    - 'lib/Xdate_farmer.rb'
    - 'lib/Xdate_segment.rb'

# Offense count: 909
# Cop supports --auto-correct.
# Configuration parameters: AllowForAlignment, ForceEqualSignAlignment.
Style/ExtraSpacing:
  Enabled: false

# Offense count: 113
# Configuration parameters: ExpectMatchingDefinition, Regex, IgnoreExecutableScripts, AllowedAcronyms.
# AllowedAcronyms: CLI, DSL, ACL, API, ASCII, CPU, CSS, DNS, EOF, GUID, HTML, HTTP, HTTPS, ID, IP, JSON, LHS, QPS, RAM, RHS, RPC, SLA, SMTP, SQL, SSH, TCP, TLS, TTL, UDP, UI, UID, UUID, URI, URL, UTF8, VM, XML, XMPP, XSRF, XSS
Style/FileName:
  Enabled: false

# Offense count: 2
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: for, each
Style/For:
  Exclude:
    - 'app/views/topics/feed.builder'
    - 'app/views/topics/node_feed.builder'

# Offense count: 62
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: format, sprintf, percent
Style/FormatString:
  Enabled: false

# Offense count: 616
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, UseHashRocketsWithSymbolValues, PreferHashRocketsForNonAlnumEndingSymbols.
# SupportedStyles: ruby19, hash_rockets, no_mixed_keys, ruby19_no_mixed_keys
Style/HashSyntax:
  Enabled: false

# Offense count: 68
Style/IdenticalConditionalBranches:
  Enabled: false

# Offense count: 102
Style/IfInsideElse:
  Enabled: false

# Offense count: 95
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, IndentationWidth.
# SupportedStyles: special_inside_parentheses, consistent, align_brackets
Style/IndentArray:
  Enabled: false

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: IndentationWidth.
Style/IndentAssignment:
  Exclude:
    - 'lib/Xdate_Constant.rb'
    - 'lib/Xdate_Variable.rb'

# Offense count: 109
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, IndentationWidth.
# SupportedStyles: special_inside_parentheses, consistent, align_braces
Style/IndentHash:
  Enabled: false

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: auto_detection, squiggly, active_support, powerpack, unindent
Style/IndentHeredoc:
  Exclude:
    - 'lib/homeland/markdown.rb'

# Offense count: 2738
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: normal, rails
Style/IndentationConsistency:
  Enabled: false

# Offense count: 2312
# Cop supports --auto-correct.
# Configuration parameters: Width, IgnoredPatterns.
Style/IndentationWidth:
  Enabled: false

# Offense count: 1
# Cop supports --auto-correct.
Style/InitialIndentation:
  Exclude:
    - 'lib/Pm.rb'

# Offense count: 13
# Cop supports --auto-correct.
# Configuration parameters: InverseMethods, InverseBlocks.
Style/InverseMethods:
  Exclude:
    - 'app/jobs/push_job.rb'
    - 'lib/Divination_explain.rb'
    - 'lib/Divination_explain_ganqing.rb'
    - 'lib/Divination_explain_qiucai.rb'
    - 'lib/Divination_explain_zhichang.rb'
    - 'lib/Divination_parameter.rb'

# Offense count: 1101
# Cop supports --auto-correct.
Style/LeadingCommentSpace:
  Enabled: false

# Offense count: 7
# Cop supports --auto-correct.
Style/LineEndConcatenation:
  Exclude:
    - 'app/controllers/service/paymentcenter_controller.rb'

# Offense count: 2172
# Cop supports --auto-correct.
Style/MethodCallWithoutArgsParentheses:
  Enabled: false

# Offense count: 3410
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: snake_case, camelCase
Style/MethodName:
  Enabled: false

# Offense count: 14
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: symmetrical, new_line, same_line
Style/MultilineArrayBraceLayout:
  Exclude:
    - 'lib/Star_ClassVariable.rb'
    - 'lib/Xuexi_Ziwei_timu.rb'
    - 'lib/Ziwei.rb'
    - 'lib/Ziwei_Han.rb'

# Offense count: 12
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: symmetrical, new_line, same_line
Style/MultilineHashBraceLayout:
  Exclude:
    - 'app/controllers/application_controller.rb'
    - 'lib/Eightword_ParFunction.rb'
    - 'lib/Star_ParFunction.rb'
    - 'lib/Xuexi_Ziwei.rb'
    - 'lib/Xuexi_Ziwei_xuexi.rb'

# Offense count: 4159
# Cop supports --auto-correct.
Style/MultilineIfThen:
  Enabled: false

# Offense count: 154
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: both, prefix, postfix
Style/NegatedIf:
  Enabled: false

# Offense count: 2
Style/NestedTernaryOperator:
  Exclude:
    - 'lib/SearchParser.rb'

# Offense count: 92
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, MinBodyLength, SupportedStyles.
# SupportedStyles: skip_modifier_ifs, always
Style/Next:
  Enabled: false

# Offense count: 716
# Cop supports --auto-correct.
Style/NilComparison:
  Enabled: false

# Offense count: 466
# Cop supports --auto-correct.
# Configuration parameters: IncludeSemanticChanges.
Style/NonNilCheck:
  Enabled: false

# Offense count: 8
# Cop supports --auto-correct.
# Configuration parameters: Strict.
Style/NumericLiterals:
  MinDigits: 6

# Offense count: 108
# Cop supports --auto-correct.
Style/ParallelAssignment:
  Enabled: false

# Offense count: 4261
# Cop supports --auto-correct.
# Configuration parameters: AllowSafeAssignment.
Style/ParenthesesAroundCondition:
  Enabled: false

# Offense count: 38
# Cop supports --auto-correct.
# Configuration parameters: PreferredDelimiters.
Style/PercentLiteralDelimiters:
  Enabled: false

# Offense count: 1059
# Cop supports --auto-correct.
Style/RedundantParentheses:
  Enabled: false

# Offense count: 5244
# Cop supports --auto-correct.
# Configuration parameters: AllowMultipleReturnValues.
Style/RedundantReturn:
  Enabled: false

# Offense count: 37
# Cop supports --auto-correct.
Style/SelfAssignment:
  Enabled: false

# Offense count: 10
# Cop supports --auto-correct.
# Configuration parameters: AllowAsExpressionSeparator.
Style/Semicolon:
  Exclude:
    - 'app/controllers/ifate/personalevent_controller.rb'
    - 'lib/Cfate.rb'
    - 'lib/Eightword_Func_Pan_Change.rb'
    - 'lib/Xdate.rb'
    - 'lib/Xdate_lunar.rb'
    - 'lib/Xdate_segment.rb'
    - 'lib/Xdate_sun.rb'

# Offense count: 255
# Cop supports --auto-correct.
# Configuration parameters: AllowIfMethodIsEmpty.
Style/SingleLineMethods:
  Exclude:
    - 'lib/Eightword_InstanceVariable.rb'
    - 'lib/Star_InstanceVariable.rb'
    - 'lib/Wannianli.rb'

# Offense count: 1
# Cop supports --auto-correct.
Style/SpaceAfterColon:
  Exclude:
    - 'app/helpers/xuexi/ziwei_helper.rb'

# Offense count: 44042
# Cop supports --auto-correct.
Style/SpaceAfterComma:
  Enabled: false

# Offense count: 2
# Cop supports --auto-correct.
Style/SpaceAfterMethodName:
  Exclude:
    - 'lib/Pm.rb'
    - 'lib/Xdate.rb'

# Offense count: 652
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: space, no_space
Style/SpaceAroundEqualsInParameterDefault:
  Enabled: false

# Offense count: 4
# Cop supports --auto-correct.
Style/SpaceAroundKeyword:
  Exclude:
    - 'app/controllers/service/paymentcenter_controller.rb'

# Offense count: 589
# Cop supports --auto-correct.
# Configuration parameters: AllowForAlignment.
Style/SpaceAroundOperators:
  Enabled: false

# Offense count: 5
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: space, no_space
Style/SpaceBeforeBlockBraces:
  Exclude:
    - 'app/views/topics/feed.builder'
    - 'lib/Star.rb'
    - 'lib/WeenApi.rb'
    - 'lib/Xdate.rb'

# Offense count: 161
# Cop supports --auto-correct.
Style/SpaceBeforeComma:
  Enabled: false

# Offense count: 6
# Cop supports --auto-correct.
Style/SpaceBeforeComment:
  Exclude:
    - 'app/controllers/ifate/customer_controller.rb'
    - 'app/controllers/service/paymentcenter_controller.rb'
    - 'lib/Star_ParFunction.rb'
    - 'lib/Wannianli.rb'

# Offense count: 281
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, EnforcedStyleForEmptyBraces, SupportedStylesForEmptyBraces, SpaceBeforeBlockParameters.
# SupportedStyles: space, no_space
# SupportedStylesForEmptyBraces: space, no_space
Style/SpaceInsideBlockBraces:
  Enabled: false

# Offense count: 340
# Cop supports --auto-correct.
Style/SpaceInsideBrackets:
  Enabled: false

# Offense count: 419
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, EnforcedStyleForEmptyBraces, SupportedStylesForEmptyBraces.
# SupportedStyles: space, no_space, compact
# SupportedStylesForEmptyBraces: space, no_space
Style/SpaceInsideHashLiteralBraces:
  Enabled: false

# Offense count: 5
# Cop supports --auto-correct.
Style/SpaceInsideParens:
  Exclude:
    - 'app/views/topics/feed.builder'
    - 'lib/Wannianli.rb'
    - 'lib/Xuexi_Ziwei_xuexi.rb'

# Offense count: 64431
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, ConsistentQuotesInMultiline.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiterals:
  Enabled: false

# Offense count: 54
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiteralsInInterpolation:
  Enabled: false

# Offense count: 59
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: percent, brackets
Style/SymbolArray:
  Enabled: false

# Offense count: 11
# Cop supports --auto-correct.
# Configuration parameters: IgnoredMethods.
# IgnoredMethods: respond_to, define_method
Style/SymbolProc:
  Exclude:
    - 'lib/Eightword_Func_Pan_Ui.rb'
    - 'lib/Star_WeenApi.rb'
    - 'lib/Star_zeri.rb'
    - 'lib/Wannianli.rb'
    - 'lib/Xuexi_Ziwei_timu.rb'

# Offense count: 7240
# Cop supports --auto-correct.
Style/Tab:
  Enabled: false

# Offense count: 44
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, AllowSafeAssignment.
# SupportedStyles: require_parentheses, require_no_parentheses, require_parentheses_when_complex
Style/TernaryParentheses:
  Exclude:
    - 'app/controllers/application_controller.rb'
    - 'app/controllers/ifate/eightword_controller.rb'
    - 'app/controllers/ifate/wannianli_controller.rb'
    - 'app/controllers/xuexi/ziwei_controller.rb'
    - 'app/controllers/xuexi/ziweipan_controller.rb'
    - 'app/controllers/xuexi/ziweipars_controller.rb'
    - 'app/helpers/ifate/xdate_helper.rb'
    - 'lib/Eightword_Func_Pan.rb'
    - 'lib/Eightword_Func_Pan_Ui.rb'
    - 'lib/Star_Func_Pan.rb'
    - 'lib/Star_Func_PanWeb.rb'
    - 'lib/Xdate_json.rb'
    - 'lib/Xdate_lunar.rb'
    - 'lib/Xdate_sun.rb'

# Offense count: 23
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: final_newline, final_blank_line
Style/TrailingBlankLines:
  Enabled: false

# Offense count: 102
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleForMultiline, SupportedStylesForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, no_comma
Style/TrailingCommaInLiteral:
  Exclude:
    - 'lib/Controller_Api_Allpay.rb'
    - 'lib/Name_dk.rb'
    - 'lib/Name_mk.rb'

# Offense count: 353
# Cop supports --auto-correct.
Style/TrailingWhitespace:
  Enabled: false

# Offense count: 111
# Cop supports --auto-correct.
Style/UnneededInterpolation:
  Enabled: false

# Offense count: 17182
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: snake_case, camelCase
Style/VariableName:
  Enabled: false

# Offense count: 52
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: snake_case, normalcase, non_integer
Style/VariableNumber:
  Exclude:
    - 'app/controllers/ifate/wannianli_controller.rb'
    - 'lib/Controller_Api.rb'
    - 'lib/Divination_explain_ganqing.rb'
    - 'lib/Divination_explain_qiucai.rb'
    - 'lib/Divination_explain_zhichang.rb'
    - 'lib/Eightword_Func_Pan.rb'
    - 'lib/Eightword_ParFunction.rb'
    - 'lib/Score_StarWeenApi.rb'
    - 'lib/Star_ParFunction.rb'
    - 'lib/Star_zeri.rb'
    - 'lib/Wannianli.rb'
    - 'lib/Xdate_farmer.rb'

# Offense count: 1
# Cop supports --auto-correct.
Style/WhileUntilDo:
  Exclude:
    - 'lib/Xdate.rb'

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: MaxLineLength.
Style/WhileUntilModifier:
  Exclude:
    - 'lib/Pm.rb'
    - 'lib/Xuexi_Ziwei_timu.rb'

# Offense count: 555
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, MinSize, WordRegex.
# SupportedStyles: percent, brackets
Style/WordArray:
  Enabled: false

# Offense count: 93
# Cop supports --auto-correct.
Style/ZeroLengthPredicate:
  Enabled: false
