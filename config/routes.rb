Rails.application.routes.draw do

  namespace :ifate do
    get 'talent/main'
    get 'talent/setup'
    get 'talent/setting'
    post 'talent/setting'
    get 'talent/category'
    post 'talent/category'
    get 'talent/item'
    post 'talent/item'
    get 'talent/trait'
    post 'talent/trait'
    get 'talent/setup_star_rule'
    post 'talent/setup_star_rule'
    get 'talent/setup_rule'
    post 'talent/setup_rule'

    get 'talent/trait_score'
    post 'talent/trait_score'
    get 'talent/category_score'
    post 'talent/category_score'

    get 'talent/default_score_data'
    post 'talent/default_score_data'
    get 'talent/score_statistic'
    post 'talent/score_statistic'
    get 'talent/talent_scores'
    post 'talent/talent_scores'

    get 'talent/backup'
    post 'talent/backup'
    
    get 'talent/show_statistic'
    post 'talent/show_statistic'

    get 'talent/api_called_count'
    post 'talent/api_called_count'
  end
  
  match "/change_talent" => "ifate/talent#change_talent", via: [:get, :post]

  namespace :ifate do
    get 'liuyaogua/pan'
    post 'liuyaogua/pan'
    get 'liuyaogua/panprint'
    get 'liuyaogua/panparedit'
    post 'liuyaogua/panparupdate'
    get 'liuyaogua/pan2share'
    get 'liuyaogua/pan2'
    get 'liuyaogua/gualist'
    get 'liuyaogua/delete'
    post 'liuyaogua/destroy'
  end
  match "/liuyaogua_xdate_ajax" => "ifate/liuyaogua#xdate_ajax", via: [:get, :post]

  ####################### BELOW from meen.tw  ##########################
  ####################### BELOW from meen.tw  ##########################
  namespace :xuexi do
    get 'ziweipars/xuexi'
    get 'ziweipars/pan_ajax'
    get 'ziweipars/fuwu_shuoming'
    post 'ziweipars/fuwu_shuoming'
  end
  match "/xuexi_ziweipars_pan_ajax" => "xuexi/ziweipars#pan_ajax", via: [:get, :post]
  match "/xuexi_ziweipars_set_par_ajax" => "xuexi/ziweipars#set_par_ajax", via: [:get, :post]

  namespace :xuexi do
    get 'ziweipan/xuexi'
    get 'ziweipan/pan'
    get 'ziweipan/fuwu_shuoming'
    post 'ziweipan/fuwu_shuoming'
  end
  
  namespace :service do
  get 'allpay/dispatchpaymentconfirm_allpay'
  get 'allpay/dispatchfeedback_allpay'
  get 'allpay/dispatchreceive_allpay'
  get 'allpay/dispatchresult_allpay'
  end

  namespace :xuexi do
    get 'function/kecheng'
    get 'function/xuexi'
    get 'function/lianxi'
    get 'function/chuangguan'
    get 'function/fuwu_shuoming'
    post 'function/fuwu_shuoming'
  end

  namespace :xuexi do
    get 'ziwei/xpan'
    get 'ziwei/xparedit'
    post 'ziwei/xparedit'
    get 'ziwei/xuexi'
    get 'ziwei/lianxi'
    get 'ziwei/chuangguan'
    get 'ziwei/pan_ajax'
    post 'ziwei/pan_ajax'
    get 'ziwei/kecheng'
    post 'ziwei/kecheng'
    get 'ziwei/fuwu_shuoming'
    post 'ziwei/fuwu_shuoming'
  end
  match "/xuexi_ziwei_pan_ajax" => "xuexi/ziwei#pan_ajax", via: [:get, :post]
  match "/xuexi_ziwei_pan_event_ajax" => "xuexi/ziwei#pan_event_ajax", via: [:get, :post]
  match "/xuexi_ziwei_xuexi_ajax" => "xuexi/ziwei#xuexi_ajax", via: [:get, :post]
  match "/xuexi_ziwei_lianxi_ajax" => "xuexi/ziwei#lianxi_ajax", via: [:get, :post]
  match "/xuexi_ziwei_chuangguan_ajax" => "xuexi/ziwei#chuangguan_ajax", via: [:get, :post]

  match "/learning" => "xuexi/ziwei#xuexi", via: [:get, :post]

  namespace :ifate do
    get 'wannianli/pan'
    get 'wannianli/pan_free'
    get 'wannianli/zeri_pan_answer'
    get 'wannianli/zeri_pan_show_ajax'
    get 'wannianli/panparedit'
    post 'wannianli/panparedit'
    get "wannianli/management"
    get 'wannianli/fuwu_shuoming'
    post 'wannianli/fuwu_shuoming'
  end
  match "/wannianli_pan_ajax" => "ifate/wannianli#pan_ajax", via: [:get, :post]
  match "/wannianli_pan_ajax_no_sign_in" => "ifate/wannianli#pan_ajax_no_sign_in", via: [:get, :post]

  namespace :ifate do
    get 'function/zeri_pan'
    post 'function/zeri_pan'
    get 'function/show_pan'
    get 'function/xdate'
    post 'function/xdate'
    get 'function/xdate_free'
    post 'function/xdate_free'
    get 'function/fuwu_shuoming'
    post 'function/fuwu_shuoming'

    get 'star/zeri_pan_show_ajax'
    get 'star/zeri_pan_answer'

    get 'function/ji_xiong_chaxun_ajax'
    get 'star/ji_xiong_chaxun'
    get 'star/ji_xiong_chaxun_ajax'
    get 'star/ji_xiong_chaxun_ajax2'

    get 'eightword/zeri_pan_show_ajax'
    get 'eightword/zeri_pan_answer'
  end
  match "/zeri_pan_show_ajax" => "ifate/function#zeri_pan_show_ajax", via: [:get, :post]
  match "/zeri_pan_save_customer_ajax" => "ifate/customer#zeri_pan_save_customer_ajax", via: [:get, :post]
  match "/ganzhi_change" => "ifate/wannianli#ganzhi_change", via: [:get, :post]
  match "/ji_xiong_chaxun_ajax" => "ifate/function#ji_xiong_chaxun_ajax", via: [:get, :post]
  match "/ji_xiong_chaxun_ajax2" => "ifate/star#ji_xiong_chaxun_ajax2", via: [:get, :post]
  match "/liuyaogua_pan_show_ajax" => "ifate/liuyaogua#liuyaogua_pan_show_ajax", via: [:get, :post]
  match "/liuyaogua_save_ajax" => "ifate/liuyaogua#save_ajax", via: [:get, :post]
  match "/liuyaogua_get_luduan_ajax" => "ifate/liuyaogua#get_luduan_ajax", via: [:get, :post]

  get "ifate/eightword/pan"
  get "ifate/eightword/pan2"
  get "ifate/eightword/pan2share"
  # post "ifate/eightword/pan"
  get "ifate/eightword/panprint"
  # post "ifate/eightword/panprint"
  get "ifate/eightword/panparedit"
  # post "ifate/eightword/panparedit"
  post "ifate/eightword/panparupdate"
  # get "ifate/eightword/pansimple"
  post "ifate/eightword/pansimple"
  get "ifate/eightword/pansimpleinput"
  get "ifate/eightword/free"
  post "ifate/eightword/free"
  # post "ifate/eightword/pansimpleinput"
  # get "ifate/eightword/xml_gen"
  # post "ifate/eightword/xml_gen"
  get "ifate/eightword/management"
  # post "ifate/eightword/management"
  # get "ifate/eightword/free"
  # post "ifate/eightword/free"

  get "ifate/eightword/panparcopy"
  # get "ifate/eightword/panparcopy_action"
  post "ifate/eightword/panparcopy_action"
  post "ifate/eightword/panparsetname_action"
  post "ifate/eightword/panparcopyname_action"
  get "ifate/eightword/edit_customer"
  # get "ifate/eightword/update_customer"
  post "ifate/eightword/update_customer"
  get 'ifate/eightword/fuwu_shuoming'
  post 'ifate/eightword/fuwu_shuoming'
  match "/update_sp_eightword_pan_ajax" => "ifate/eightword#update_sp_eightword_pan_ajax", via: [:get, :post]

  match "/update_peitems" => "ifate/personalevent#update_peitems", via: [:get, :post]
  match "/pe_new_ajax" => "ifate/personalevent#pe_new_ajax", via: [:get, :post]
  get "ifate/personalevent/index"
  # post "ifate/personalevent/index"
  get "ifate/personalevent/edit"
  # post "ifate/personalevent/edit"
  get "ifate/personalevent/show"
  get "ifate/personalevent/new"
  # post "ifate/personalevent/new"
  get "ifate/personalevent/delete"
  # get "ifate/personalevent/update"
  post "ifate/personalevent/update"
  # get "ifate/personalevent/destroy"
  post "ifate/personalevent/destroy"

  get "demo" => "service/store#show"
  get "demo/eightword" => "ifate/eightword#show"
  get "demo/star" => "ifate/star#show"
  get "ifate/customer/agerange"
  get "ifate/customer/delete"
  get "ifate/customer/edit"
  get "ifate/customer/index"
  get "ifate/customer/indexwithnew"
  get "ifate/customer/new"
  get "ifate/customer/pan"
  post "ifate/customer/pan"
  get "ifate/customer/panparedit"
  get "ifate/customer/panscoreinput"
  get "ifate/customer/search"
  get "ifate/customer/setup"
  get "ifate/customer/show"
  get "ifate/customer/advance_search"
  get "ifate/customer/personalevent_search"
  get "ifate/star/user_customer_search"
  get "ifate/star/management"
  get "ifate/star/pan2"
  get "ifate/star/pan2share"
  get "ifate/star/pan"
  get "ifate/star/panparcopy"
  get "ifate/star/panparedit"
  get "ifate/star/panprint"
  get "ifate/star/panscore"
  get "ifate/star/xdate"
  get "ifate/star/setup_zeri_par"
  # get "ifate/star/xml_gen"
  get "service/allpay/dispatchfeedback"
  get "service/allpay/dispatchreceive"
  get "service/allpay/dispatchresult"
  get "service/order/index"
  get "service/order/show"
  get "service/payment/index"
  get "service/payment/show"
  get "service/paymentcenter/dispatchfeedback"
  get "service/paymentcenter/dispatchreceive"
  get "service/paymentcenter/dispatchresult"
  get "service/store/buied_products"
  get "service/store/buy_products"
  get "service/store/buy_products_ajax"
  get "service/store/buy_products_complete"
  get "service/store/demo_apply"
  get "service/store/management"
  get "service/store/show"
  get "service/store/list_products"
  get "service/store/updatedb"
  get 'ifate/star/fuwu_shuoming'
  match "/buy_products_ajax" => "service/store#buy_products_ajax", via: [:get, :post]
  match "/explain_ajax" => "ifate/star#explain_ajax", via: [:get, :post]
  match "/update_sp_star_pan_ajax" => "ifate/star#update_sp_star_pan_ajax", via: [:get, :post]
  match "/update_firstShowAdvertise_ajax" => "ifate/star#update_firstShowAdvertise_ajax", via: [:get, :post]
  match "/health" => "ifate/customer#health", via: [:get, :post]
  match "/health_key" => "ifate/customer#health_key", via: [:get, :post]
  match "/update_dates" => "ifate/customer#update_dates", via: [:get, :post]
  match "/update_dates_no_sign_in" => "ifate/customer#update_dates_no_sign_in", via: [:get, :post]
  match "/xdate_ajax" => "ifate/star#xdate_ajax", via: [:get, :post]
  match "dispatchpaymentconfirm" => "service/allpay#dispatchpaymentconfirm", via: [:get, :post]
  match "showaps" => "ifate/customer#user_loging", via: [:get, :post]
  post "ifate/customer/createcustomerfull"
  post "ifate/customer/createcustomersimple"
  post "ifate/customer/destroy"
  post "ifate/customer/setup"
  post "ifate/customer/update"
  post "ifate/customer/user_loging"
  get "ifate/customer/update_password"
  post "ifate/star/panparcopy_action"
  post "ifate/star/panparsetname_action"
  post "ifate/star/panparcopyname_action"
  post "ifate/star/panparupdate"
  post "ifate/star/panscore"
  post "service/allpay/dispatchfeedback"
  post "service/allpay/dispatchpaymentconfirm"
  post "service/allpay/dispatchreceive"
  post "service/allpay/dispatchresult"
  post "service/paymentcenter/dispatchfeedback"
  post "service/paymentcenter/dispatchpaymentconfirm"
  post "service/paymentcenter/dispatchreceive"
  post "service/paymentcenter/dispatchresult"
  # post "service/products/destroy"
  post "service/store/buy_products_complete"
  post "service/store/buy_products_confirm"
  post "service/store/demo_apply_complete"
  post 'ifate/star/fuwu_shuoming'
  get 'ifate/star/pansimpleinput'
  get 'ifate/star/pansimple'
  post 'ifate/star/pansimple'
  get "ifate/star/free"
  post "ifate/star/free"
  match "free" => "ifate/star#free", via: [:get, :post]

  get "ifate/xdate/free"

  ####################### ABOVE from meen.tw  ##########################
  ####################### ABOVE from meen.tw  ##########################


  # root to: 'home#index'
  # match "/" => "home#index", via: [:get, :post]

  get 'ifate/function/pansimpleinput'
  root to: 'ifate/function#pansimpleinput'
  match "/" => "ifate/function#pansimpleinput", via: [:get, :post]
  # match "/" => "ifate/customer#index", via: [:get, :post]
  # match "/" => "home#index", via: [:get, :post]

  namespace :home do
    get 'xuexi'
    get 'pan'
    get 'fuwu_shuoming'
    post 'fuwu_shuoming'
  end

  devise_for :users, path: 'account', controllers: {
    registrations: :account,
    sessions: :sessions,
    passwords: :passwords,
  }

  mount RuCaptcha::Engine => '/rucaptcha'
  mount StatusPage::Engine, at: '/'

  namespace :admin do
    root to: 'home#index', as: 'root'
    resources :site_configs
    resources :sections
    resources :users do
      member do
        delete :clean
      end
    end
    resources :exception_logs do
      collection do
        post :clean
      end
    end
    resources :applications
    resources :products

    # admin
    match "func" => "accounting#admin_func", via: [:get, :post]
    match "func_users" => "accounting#admin_func_users", via: [:get, :post]
    match "func_users_all" => "accounting#admin_func_users_all", via: [:get, :post]
    match "func_user_search" => "accounting#admin_func_user_search", via: [:get, :post]
    match "func_user_customer" => "accounting#admin_func_user_customer", via: [:get, :post]
    match "func_user_ap_users" => "accounting#admin_func_user_ap_users", via: [:get, :post]
    match "func_user_ap" => "accounting#admin_func_user_ap", via: [:get, :post]
    match "func_user_ap_edit" => "accounting#admin_func_user_ap_edit", via: [:get, :post]
    match "func_user_ap_update" => "accounting#admin_func_user_ap_update", via: [:get, :post]
    match "func_manual_order_users" => "accounting#admin_func_manual_order_users", via: [:get, :post]
    match "func_manual_order" => "accounting#admin_func_manual_order", via: [:get, :post]
    match "func_manual_order_update" => "accounting#admin_func_manual_order_update", via: [:get, :post]
    match "func_successfully_orders" => "accounting#admin_func_successfully_orders", via: [:get, :post]
    match "func_user_successfully_orders_users" => "accounting#admin_func_user_successfully_orders_users", via: [:get, :post]

    match "func_manual_order_product_get_additional" => "accounting#admin_func_manual_order_product_get_additional", via: [:get, :post]
    match "func_all_orders" => "accounting#admin_func_all_orders", via: [:get, :post]
    match "func_all_orders/:count" => "accounting#admin_func_all_orders", via: [:get, :post]

    # ween api
    match "func_ween_successfully_orders" => "accounting#admin_func_ween_successfully_orders", via: [:get, :post]
    match "func_ween_all_orders" => "accounting#admin_func_ween_all_orders", via: [:get, :post]
    match "func_ween_all_orders/:count" => "accounting#admin_func_ween_all_orders", via: [:get, :post]
  end



  # get 'api' => 'home#api', as: 'api'
  # get 'markdown' => 'home#markdown', as: 'markdown'

  namespace :api do

    # migrated from ween-api
    namespace :v1, defaults: { format: 'json' } do
      resources :profate_events, :only => [:show, :create, :edit]
      # resources :profate_orders
      match 'profate_finish_orders', :to => 'profate_orders#finish_orders', :via => [:get,:post]
      # match 'profate_event_name/:id', :to => 'profate_events#event_name', :via => [:get]
      resources :profate_user_events, :only => [:show, :create, :edit]
      resources :profate_event_points, :only => [:show, :index]
      resources :profate_coupons, :except => [:index, :delete]
      match 'profate_coupon_if_valid/:id', :to => 'profate_coupons#coupon_if_valid', :via => [:get]
      resources :profate_point_products, :only => [:show, :index]
      # match 'profate_point_products', :to => 'profate_point_products#index', :via => [:get]

      match 'supported_paymenttype', :to => 'ween_billings#supported_paymenttype', :via => [:get,:post]
      match 'allpay_checkout', :to => 'ween_billings#allpay_checkout', :via => [:post]
      match 'allpay_return', :to => 'ween_billings#allpay_return', :via => [:post]
      match 'allpay_paymentinfo', :to => 'ween_billings#allpay_paymentinfo', :via => [:post]
      match 'allpay_periodreturn', :to => 'ween_billings#allpay_periodreturn', :via => [:post]
      match 'buy', :to => 'profate_user_points#buy', :via => [:post]

      match 'dispatchpaymentconfirm', :to => 'payment_center#dispatchpaymentconfirm', :via => [:post]
      match 'dispatchreceive', :to => 'payment_center#dispatchreceive', :via => [:post]
      match 'dispatchfeedback', :to => 'payment_center#dispatchfeedback', :via => [:post]

      # resources :entries
      # resources :gnum, only: [:index, :create, :show]
      # match 'ween/answer', :to => 'ween#answer', :via => [:get]

      # free api
      match 'ziwei_shinian_dayun', :to => 'profate_free#ziwei_shinian_dayun', :via => [:get]

      # 陳明德 八字
      # match 'bazhi_1001/:timestamp', :to => 'profate_free#bazhi_1', :via => [:get]
      match 'bazhi_1001/:timestamp', :to => 'ween#bazhi_1', :via => [:get,:post]

      # 免費的流日盤api 夫妻宮，官祿宮，財帛宮
      match 'ziwei_liuri/:timestamp/:house/:name', :to => 'ween#ziwei_liuri', :via => [:get]
      match 'ziwei_liuri/:timestamp/:house', :to => 'ween#ziwei_liuri', :via => [:get]
      match 'timestamps/:timestamp', :to => 'ween#timestamps', :via => [:get]

      # sname : 姓
      # tname : 名
      # sex : "man","m","M","MAN","1" 表示男生，其餘女生
      match ':sname/:tname/:sex/name', :to => 'ween#name', :via => [:get]
      match 'name', :to => 'ween#name', :via => [:get]
      # sname : 姓
      # tname : 名
      # sex : "man","m","M","MAN","1" 表示男生，其餘女生
      # psname : 對方的姓
      # ptname : 對方的名
      match ':sname/:tname/:sex/:psname/:ptname/namepair', :to => 'ween#namepair', :via => [:get]
      match 'namepair', :to => 'ween#namepair', :via => [:get]
      # word : 所測的字，一個字
      # land : 地數,1 ~ 24
      # minute : 測字時的分鐘
      # thing : 問的事情，會原封不動回給呼叫者
      match ':word/:land/:minute/:thing/gnum', :to => 'ween#gnum', :via => [:get]
      match 'gnum', :to => 'ween#gnum', :via => [:get]
      # y,m,d,h,min : 客戶的出生日期；年(例1982)，月，日，時，分
      # l : 是否潤月，1表示潤月；0表示非潤月。陰曆時用(dt == 1)
      # dt : 日期格式，0是陽曆；1是陰曆
      # 陽曆，1表示陰曆
      # sex : 1表示男，0表示女
      # name : 姓名
      # uy,um,ud,uh,umin : 流盤的日期；年(例1982)，月，日，時，分
      # ul : 是否潤月，1表示潤月；0表示非潤月。陰曆時用(dt == 1)
      # udt : 日期格式，0是陽曆；1是陰曆
      # pt : pantype
      # house : 宮位,1 ~ 12 或 99 代表全部宮位

      # 臨時改出來給HRT用的
      match 'starFourHua', :to => 'ween#fourhua', :via => [:get]

      # match ':y/:m/:d/:h/:min/:l/:dt/:sex/:name/:uy/:um/:ud/:uh/:umin/:ul/:udt/:pt/:house/star/answer', :to => 'ween#answer', :via => [:get]
      match 'starAnswer', :to => 'ween#answer', :via => [:get]

      # match ':y/:m/:d/:h/:min/:l/:dt/:sex/:name/:uy/:um/:ud/:uh/:umin/:ul/:udt/:pt/:house/star/feeling', :to => 'ween#feeling', :via => [:get]
      match 'starFeeling', :to => 'ween#feeling', :via => [:get]

      # match ':y/:m/:d/:h/:min/:l/:dt/:sex/:name/:uy/:um/:ud/:uh/:umin/:ul/:udt/:pt/:house/star/explode', :to => 'ween#explode', :via => [:get]
      match 'starExplosion', :to => 'ween#explode', :via => [:get]
      match 'profate_lunduan', :to => 'ween#profate_lunduan', :via => [:get]

      # match ':y/:m/:d/:h/:min/:l/:dt/:sex/:name/:uy/:um/:ud/:uh/:umin/:ul/:udt/:pt/:house/star/gixiongscore', :to => 'ween#gixiongscore', :via => [:get]
      match 'starJixiongScore', :to => 'ween#gixiongscore', :via => [:get]

      # match ':y/:m/:d/:h/:min/:l/:dt/:sex/:name/:uy/:um/:ud/:uh/:umin/:ul/:udt/:pt/:house/star/gixiongtext', :to => 'ween#gixiongtext', :via => [:get]
      match 'starJixiongText', :to => 'ween#gixiongtext', :via => [:get]

      match 'panscore', :to => 'ween#panscore', :via => [:get, :post]

      # match ':y/:m/:d/:h/:l/:dt/ganzhi', :to => 'ween#ganzhi', :via => [:get]
      match 'ganzhi/:timestamp', :to => 'ween#ganzhi', :via => [:get]
      match 'ganzhi', :to => 'ween#ganzhi', :via => [:get]


      # match ':y/:m/:d/:l/:dt/farmercal', :to => 'ween#farmercal', :via => [:get]
      match 'nongli/:timestamp/:l/:dt', :to => 'ween#farmercal', :via => [:get]
      match 'nongli', :to => 'ween#farmercal', :via => [:get]

      # match ':y/:m/:d/:l/:dt/:sex/:pickdays/pickdays', :to => 'ween#pickdays', :via => [:get]
      match 'poufuzeri/:timestamp', :to => 'ween#pickdays', :via => [:get]
      match 'poufuzeri', :to => 'ween#pickdays', :via => [:get]

      # gua_bie: jiankang： 健康； qiucai：求財；
      # nuweihun：女未婚； nuyihun：女已婚； nanweihun： 男未婚； nanyihun： 男已婚
      # zhichang: 職場
      # match ':wai/:nei/:bian/:uy/:um/:ud/:uh/:name/:question/:gua_bie/pugua', :to => 'ween#pugua', :via => [:get]
      match 'liuyaogua', :to => 'ween#pugua', :via => [:get]
      match 'liuyaogua_jiankang', :to => 'ween#liuyaogua_jiankang', :via => [:get]
      match 'liuyaogua_qiucai', :to => 'ween#liuyaogua_qiucai', :via => [:get]
      match 'liuyaogua_nuweihun', :to => 'ween#liuyaogua_nuweihun', :via => [:get]
      match 'liuyaogua_nuyihun', :to => 'ween#liuyaogua_nuyihun', :via => [:get]
      match 'liuyaogua_nanweihun', :to => 'ween#liuyaogua_nanweihun', :via => [:get]
      match 'liuyaogua_nanyihun', :to => 'ween#liuyaogua_nanyihun', :via => [:get]
      match 'liuyaogua_zhichang', :to => 'ween#liuyaogua_zhichang', :via => [:get]

      match 'liuyaogua_pan', :to => 'ween#pugua_pan', :via => [:get]

      # y,m,d,h : 搜尋干支日期的起始點；年(例1982)，月，日，時
      # l : 是否潤月，1表示潤月；0表示非潤月。陰曆時用(dt == 1)
      # dt : 日期格式，0是陽曆；1是陰曆
      # gan_zhis : 天干跟地支的字串組合 => [[t1,d1],[t2,d2],...,[tn,dn]]
      # t is tiangan : 想要搜尋的天干，例 0 代表不限天干，1 代表甲，2 代表乙,...，10代表癸
      # d is dizhi : 地支，0 代表不限地支，1 代表 子， 2 代表 丑，...，12 代表 亥
      # 例：只要地支 子 寅 辰 午 時，傳入 [[0,1],[0,3],[0,5],[0,7]]
      # duixiang : 搜尋的對象， y:年,m:月,d:日,h:時
      # count : 需回傳的日期數量
      # 此參數放棄，全部回傳：lifa : 曆法(年，月時沒差，日，時有差）； jieqi => 節氣曆； yinli => 陰曆
      # match ':y/:m/:d/:h/:l/:dt/:gan_zhis/:duixiang/:count/ganzhi_riqi', :to => 'ween#ganzhi_riqi', :via => [:get]
      match 'ganzhi_riqi/:timestamp', :to => 'ween#ganzhi_riqi', :via => [:get]
      match 'ganzhi_riqi', :to => 'ween#ganzhi_riqi', :via => [:get]

      # qian_hou_ji_tian:前後幾天
      # 例：2014/1/5,qian_hou_ji_tian = 7;
      # 則取 2014/1/5 為目標日期，並取 前3天2014/1/2開始 到後3天 2014/1/8 結束 共取 7 天
      # match ':y/:m/:d/:h/:l/:dt/:qian_hou_ji_tian/wan_nian_li', :to => 'ween#wan_nian_li', :via => [:get]
      match 'wannianli/:timestamp', :to => 'ween#wan_nian_li', :via => [:get]
      match 'wannianli', :to => 'ween#wan_nian_li', :via => [:get]

      # match ':y/:m/:d/:h/:l/:dt/shi_ju', :to => 'ween#shi_ju', :via => [:get]
      match 'shiju/:timestamp', :to => 'ween#shi_ju', :via => [:get]
      match 'shiju', :to => 'ween#shi_ju', :via => [:get]

      # y,m,d,h,min : 客戶的出生日期；年(例1982)，月，日，時，分
      # l : 是否潤月，1表示潤月；0表示非潤月。陰曆時用(dt == 1)
      # dt : 日期格式，0是陽曆；1是陰曆
      # 陽曆，1表示陰曆
      # sex : 1表示男，0表示女
      # name : 姓名
      # uy,um,ud,uh,umin : 流盤的日期；年(例1982)，月，日，時，分
      # ul : 是否潤月，1表示潤月；0表示非潤月。陰曆時用(dt == 1)
      # udt : 日期格式，0是陽曆；1是陰曆
      # pt : pantype
      # match ':y/:m/:d/:h/:min/:l/:dt/:sex/:name/:uy/:um/:ud/:uh/:umin/:ul/:udt/:pt/bazhi_pan', :to => 'ween#bazhi_pan', :via => [:get]
      match 'bazhi_pan/:timestamp', :to => 'ween#bazhi_pan', :via => [:get]
      match 'bazhi_pan', :to => 'ween#bazhi_pan', :via => [:get, :post]

      # match ':y/:m/:d/:h/:min/:l/:dt/:sex/:name/:uy/:um/:ud/:uh/:umin/:ul/:udt/:pt/ziwei_pan', :to => 'ween#ziwei_pan', :via => [:get]
      match 'ziwei_pan/:timestamp', :to => 'ween#ziwei_pan', :via => [:get]
      match 'ziwei_pan', :to => 'ween#ziwei_pan', :via => [:get, :post]

      # 紫微論斷學習api
      match 'ziwei_lunduan_api', :to => 'ween#ziwei_lunduan_api', :via => [:get]

      match 'bbbot_health_neirong', :to => 'ween#bbbot_health_neirong', :via => [:get]

      # 火錘
      # timestamp: YYYYMMDDhhmms   (s:M male, F female) 客戶出生時間
      match 'huochui_api_ziwei_lunduan', :to => 'ween#huochui_api_ziwei_lunduan', :via => [:get]
      match 'huochui_api_ziwei_lunduan_12', :to => 'ween#huochui_api_ziwei_lunduan_12', :via => [:get]
      match 'huochui_api_ziwei_pan', :to => 'ween#huochui_api_ziwei_pan', :via => [:get]
      # word : 所測的字，一個字
      # land : 地數,1 ~ 24
      # minute : 測字時的分鐘
      # thing : 問的事情，會原封不動回給呼叫者
      match ':word/:land/:minute/:thing/huochui_api_gnum', :to => 'ween#huochui_api_gnum', :via => [:get]
      match 'huochui_api_gnum', :to => 'ween#huochui_api_gnum', :via => [:get]

      # 我們用來設定給火錘的參數
      # count 呼叫次數控制
      #  func : count
      #  cmd : add 增加 reset 設為0 minus 減少
      match ':func/:cmd/:ziwei_lunduan/:ziwei_lunduan_12/:ziwei_pan/:gnum/:key/huochui_api_par_set', :to => 'ween#huochui_api_par_set', :via => [:get]
      # ip address
      # func : ip
      #  cmd : add 加入 reset 清空 delete 刪除
      # get par : ip = 127.0.0.1 like this
      match ':func/:cmd/:key/huochui_api_par_set', :to => 'ween#huochui_api_par_set', :via => [:get]
      # 列出火錘所有設定參數
      match 'huochui_api_par_set', :to => 'ween#huochui_api_par_set', :via => [:get]
      match 'huochui_api_par_list', :to => 'ween#huochui_api_par_list', :via => [:get]
      # 顯示客戶呼叫api時輸入的參數
      # uniq : all 或者 huochui_api_ziwei_lunduan huochui_api_ziwei_lunduan_12 huochui_api_ziwei_pan huochui_api_gnum
      #        會整理輸入的參數 uniq and sort
      #        不輸入此參數，則不整理
      match 'huochui_api_show_par_input', :to => 'ween#huochui_api_show_par_input', :via => [:get]

      match 'hsieh_api_ziwei_lunduan', :to => 'ween#hsieh_api_ziwei_lunduan', :via => [:get]
      match 'hsieh_api_ziwei_pan', :to => 'ween#hsieh_api_ziwei_pan', :via => [:get]

      # 傳回pan2share的結果，對方使用iframe即可排出
      match 'pan_iframe', :to => 'ween#pan_iframe', :via => [:get]

      # 1.小趣科技 要呼叫吉凶分數api
      match 'sh_api_gixiongscore', :to => 'ween#sh_api_gixiongscore', :via => [:get]

      # 姜公獻寶 api
      match "jianggong" => "flyingstar#jianggong", via: [:get, :post]
      match "call" => "flyingstar#call", via: [:get, :post]

      # sti api
      match "sti" => "flyingstar#sti_api", via: [:get, :post]
      
      # 廣藥集團用的 api
      match "gpc" => "flyingstar#gpc_api", via: [:get, :post]
    end
  end

  require 'sidekiq/web'
  authenticate :user, ->(u) { u.admin? } do
    mount Sidekiq::Web => '/sidekiq'
  end

  mount JasmineRails::Engine => '/specs' if defined?(JasmineRails)

  match '*path', via: :all, to: 'home#error_404'
end
