upstream puma_<%= fetch(:nginx_config_name) %> { <%
  @backends = [fetch(:puma_bind)].flatten.map do |m|
  etype, address  = /(tcp|unix|ssl):\/{1,2}(.+)/.match(m).captures
  if etype == 'unix'
    "server #{etype}:#{address} #{fetch(:nginx_socket_flags)};"
  else
    "server #{address.gsub(/0\.0\.0\.0(.+)/, "127.0.0.1\\1")} #{fetch(:nginx_http_flags)};"
  end
end
%><% @backends.each do |server| %>
  <%= server %><% end %>
}
<% if fetch(:nginx_use_ssl) -%>
server {
  listen 80;
  server_name <%= fetch(:nginx_server_name) %>;
  return 301 https://$host$1$request_uri;
}
<% end -%>

server {
<% if fetch(:nginx_use_ssl) -%>
  listen 443;
  ssl on;
<% if fetch(:nginx_ssl_certificate) -%>
  ssl_certificate <%= fetch(:nginx_ssl_certificate) %>;
<% else -%>
  ssl_certificate /etc/ssl/certs/<%= fetch(:nginx_config_name) %>.crt;
<% end -%>
<% if fetch(:nginx_ssl_certificate_key) -%>
  ssl_certificate_key <%= fetch(:nginx_ssl_certificate_key) %>;
  include /etc/nginx/conf.d/letsencrypt.ssl.conf;
<% else -%>
  ssl_certificate_key /etc/ssl/private/<%= fetch(:nginx_config_name) %>.key;
<% end -%>
<% else -%>
  listen 80;
<% end -%>
  server_name <%= fetch(:nginx_server_name) %>;
  root <%= current_path %>/public;
  try_files $uri/index.html $uri @puma_<%= fetch(:nginx_config_name) %>;

  client_max_body_size 4G;
  keepalive_timeout 10;

  error_page 500 502 504 /500.html;
  error_page 503 @503;

  location @puma_<%= fetch(:nginx_config_name) %> {
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $host;
    proxy_redirect off;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
<% if fetch(:nginx_use_ssl) -%>
    proxy_set_header X-Forwarded-Proto https;
<% else -%>
    proxy_set_header X-Forwarded-Proto http;
<% end -%>
    proxy_pass http://puma_<%= fetch(:nginx_config_name) %>;
    # limit_req zone=one;
    access_log <%= shared_path %>/log/nginx.access.log;
    error_log <%= shared_path %>/log/nginx.error.log;
  }

  location ^~ /assets/ {
    gzip_static on;
    expires max;
    add_header Cache-Control public;
  }

  location = /50x.html {
    root html;
  }

  location = /404.html {
    root html;
  }

  location @503 {
    error_page 405 = /system/maintenance.html;
    if (-f $document_root/system/maintenance.html) {
      rewrite ^(.*)$ /system/maintenance.html break;
    }
    rewrite ^(.*)$ /503.html break;
  }

  if ($request_method !~ ^(GET|HEAD|PUT|PATCH|POST|DELETE|OPTIONS)$ ){
    return 405;
  }

  if (-f $document_root/system/maintenance.html) {
    return 503;
  }
}
