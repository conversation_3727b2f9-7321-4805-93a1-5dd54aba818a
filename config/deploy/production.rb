set :stage, :production
set :rails_env, :production
set :nginx_server_name, "meen.tw"

set :deploy_to, "/home/<USER>/#{fetch(:application)}"

set :nginx_ssl_certificate, "/etc/letsencrypt/live/profate.com.tw/fullchain.pem"
set :nginx_ssl_certificate_key, "/etc/letsencrypt/live/profate.com.tw/privkey.pem"
set :nginx_use_ssl, true

set :sidekiq_role, :app
set :sidekiq_config, "#{current_path}/config/sidekiq.yml"
# set :sidekiq_monit_templates_path, 'config/deploy/templates'
# set :sidekiq_monit_conf_file, ''
set :sidekiq_service_name, "sidekiq_meenforum_#{fetch(:sidekiq_env)}"
set :sidekiq_env, fetch(:rack_env, fetch(:rails_env, fetch(:stage)))
# set :sidekiq_processes, 2
set :sidekiq_roles, [:quadcore, :tricore, :dualcore, :unicore]
set :quadcore_processes, 4
set :tricore_processes, 3
set :dualcore_processes, 2
set :unicore_processes, 2

# staging 4gb 2cores
# server '*************', user: 'deployworker', roles: %w(app web unicore)
# forum 8gb 4cores
server '*************', user: 'deployworker', roles: %w(app web quadcore)
# forumdb 2gb 1cores
server '***********', user: 'deployworker', roles: %w(db)
# score 8gb 4cores
# server '************', user: 'deployworker', roles: %w(app quadcore)
# scoredb 8gb 4cores
# server '**************', user: 'deployworker', roles: %w(app db tricore)


# role-based syntax
# ==================

# Defines a role with one or multiple servers. The primary server in each
# group is considered to be the first unless any hosts have the primary
# property set. Specify the username and a domain or IP for the server.
# Don't use `:all`, it's a meta role.

# role :app, %w{<EMAIL>}, my_property: :my_value
# role :web, %w{<EMAIL> <EMAIL>}, other_property: :other_value
# role :db,  %w{<EMAIL>}



# Configuration
# =============
# You can set any configuration variable like in config/deploy.rb
# These variables are then only loaded and set in this stage.
# For available Capistrano configuration variables see the documentation page.
# http://capistranorb.com/documentation/getting-started/configuration/
# Feel free to add new variables to customise your setup.


# Custom SSH Options
# ==================
# You may pass any option but keep in mind that net/ssh understands a
# limited set of options, consult the Net::SSH documentation.
# http://net-ssh.github.io/net-ssh/classes/Net/SSH.html#method-c-start
#
# Global options
# --------------
#  set :ssh_options, {
#    keys: %w(/home/<USER>/.ssh/id_rsa),
#    forward_agent: false,
#    auth_methods: %w(password)
#  }
#
# The server-based syntax can be used to override options:
# ------------------------------------
# server "example.com",
#   user: "user_name",
#   roles: %w{web app},
#   ssh_options: {
#     user: "user_name", # overrides user setting above
#     keys: %w(/home/<USER>/.ssh/id_rsa),
#     forward_agent: false,
#     auth_methods: %w(publickey password)
#     # password: "please use keys"
#   }
