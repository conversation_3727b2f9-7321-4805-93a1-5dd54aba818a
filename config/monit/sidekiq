check process sidekiq
  with pidfile /home/<USER>/meen-forum/shared/tmp/pids/sidekiq-0.pid
  start program = "/bin/bash -l -c 'cd /home/<USER>/meen-forum/current && /home/<USER>/.rvm/bin/rvm default do bundle exec sidekiq --index 0 --pidfile /home/<USER>/meen-forum/shared/tmp/pids/sidekiq-0.pid --environment production --logfile /home/<USER>/meen-forum/shared/log/sidekiq.log --daemon'"
    as uid deployworker and gid deployworker
  stop program = "/bin/bash -l -c 'cd /home/<USER>/meen-forum/current && /home/<USER>/.rvm/bin/rvm default do bundle exec sidekiqctl stop /home/<USER>/meen-forum/shared/tmp/pids/sidekiq-0.pid 10'"
    as uid deployworker and gid deployworker

  if totalmem is greater than 1024 MB for 3 cycles then restart
  if 3 restarts within 5 cycles then timeout
