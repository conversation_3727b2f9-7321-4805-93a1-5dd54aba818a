check process puma
  with pidfile /home/<USER>/meen-forum/shared/tmp/pids/puma.pid
  start program = "/bin/bash -l -c 'cd /home/<USER>/meen-forum/current && /home/<USER>/.rvm/bin/rvm default do bundle exec puma -C /home/<USER>/meen-forum/current/config/puma-web.rb --daemon'"
    as uid deployworker and gid deployworker
  stop program = "/bin/bash -l -c 'cd /home/<USER>/meen-forum/current && /home/<USER>/.rvm/bin/rvm default do bundle exec pumactl -S /home/<USER>/meen-forum/shared/tmp/pids/puma.state stop'"
    as uid deployworker and gid deployworker

  if totalmem is greater than 2048 MB for 3 cycles then restart
  if 3 restarts within 5 cycles then timeout
