site_configs: &site_configs
  footer_html: |
    <p class="copyright">
     &copy; Powered by 郁宏數位© 2005 - <%= Time.now.year %>
    </p>
  custom_head_html: |
    <link rel="dns-prefetch" href="//meen.tw">

defaults: &defaults
  <<: *site_configs
  app_name: "MeenForum"
  domain: "meen.tw"
  mail_domain: "meen.tw"
  https: true
  # default_locale: 'zh-CN'
  auto_locale: false
  admin_emails: |
    <EMAIL>
    <EMAIL>
    <EMAIL>
    <EMAIL>
    <EMAIL>
  google_analytics_key: "UA-3247423-8"
  asset_host: ""
  mailer_provider: "smtp"
  mailer_sender: "Meen.tw系統訊息 <<EMAIL>>"
  mailer_options:
    address: "smtp.sparkpostmail.com"
    port: 25
    domain: "meen.tw"
    user_name: ""
    password: "db526516f0f26cc2b57b3e321a8a56e215264b65"
    authentication: "plain"
    enable_starttls_auto: true
  apns_pem: ""
  blacklist_ips: "*******"
  RECAPTCHA_SITE_KEY: 6LfpqFgUAAAAAABm9Y5AvbfJtVGjw-XTHiXanDmZ
  RECAPTCHA_SECRET_KEY: 6LfpqFgUAAAAAMyVUo8nZYbVbhvE1hZgFEn6L0b_

staging:
  <<: *defaults
  domain: "staging.meen.tw"
  mail_domain: "staging.meen.tw"

development:
  <<: *defaults
  domain: "localhost:3000"
  mail_domain: "localhost:3000"
  https: false

test:
  <<: *defaults
  admin_emails: >
    <EMAIL>

production:
  <<: *defaults
