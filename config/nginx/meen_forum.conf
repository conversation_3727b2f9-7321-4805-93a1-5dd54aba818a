upstream meen_forum_backend {
  server unix:/tmp/unicorn.meen-forum.sock fail_timeout=0;
  keepalive 3;
}

log_format timed_combined '$remote_addr - $remote_user [$time_local] '
                          '"$request" $status $body_bytes_sent '
                          '"$http_referer" "$http_user_agent" '
                          '$request_time $upstream_response_time $pipe';

server {
  listen 80 default_server;
  listen 443 ssl http2;
  server_name meen.tw;

  location /nginx_status {
    allow 127.0.0.1;
    deny all;
    stub_status on;
  }

  root /home/<USER>/meen-forum/current/public;

  access_log /home/<USER>/meen-forum/shared/log/meen-forum-access.log timed_combined buffer=1k;
  error_log /home/<USER>/meen-forum/shared/log/meen-forum-error.log;

  ssl_certificate     /etc/ssl/meen-forum.crt;
  ssl_certificate_key /etc/ssl/meen-forum.key;

  limit_conn  one  300;

  if (-f $document_root/system/maintenance.html) {
    rewrite ^(.*)$ /system/maintenance.html break;
  }

  location ~ (/assets|/system|/avatar.png|/favicon.ico|/*.txt) {
    access_log        off;
    expires           14d;
    gzip_static on;
    add_header  Cache-Control public;
  }

  location / {
    if ($host != 'meen.tw') {
      rewrite ^/(.*)$ https://meen.tw/$1 permanent;
    }
    proxy_redirect     off;
    proxy_set_header   Host $host;
    proxy_set_header   X-Forwarded-Host $host;
    proxy_set_header   X-Forwarded-Server $host;
    proxy_set_header   X-Real-IP        $remote_addr;
    proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
    proxy_buffering    on;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade $http_upgrade;
    proxy_set_header   Connection "Upgrade";
    proxy_set_header   X-Forwarded-Proto https;
    proxy_pass         http://meen_forum_backend;
    gzip on;
  }
}
