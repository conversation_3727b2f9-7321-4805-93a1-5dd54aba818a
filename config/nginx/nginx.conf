user ruby;
worker_processes 2;
pid /var/run/nginx.pid;

events {
  worker_connections 65535;
  multi_accept on;
  use epoll;
}

http {
    sendfile on;
    client_max_body_size 8m;

    tcp_nopush off;
    tcp_nodelay on;

    # limit_conn_zone $binary_remote_addr zone=one:100m;
    keepalive_timeout 15;
    types_hash_max_size 2048;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    gzip on;
    gzip_disable "msie6";

    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    include /etc/nginx/conf.d/*.conf;
}
