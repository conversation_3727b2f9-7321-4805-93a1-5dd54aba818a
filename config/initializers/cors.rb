# Be sure to restart your server when you modify this file.

# Avoid CORS issues when API is called from the frontend app.
# Handle Cross-Origin Resource Sharing (CORS) in order to accept cross-origin AJAX requests.

# Read more: https://github.com/cyu/rack-cors

Rails.application.config.middleware.insert_before 0, Rack::Cors do
  allow do
    origins 'www.profate.com.tw', 'profate.com.tw', 'staging.profate.com.tw', 'ween.tw', 'staging.ween.tw', 'localhost:3001', '************:3001'
    # resource '/api/v1/*', :headers => :any, :methods => [:get, :post, :put]
    resource '/api/v1/*', headers: :any, methods: [:get, :post, :put]
  end

  # allow do
  #   origins '*'
  #   resource '/ifate/star/xml_gen', headers: :any, methods: [:get, :post]
  # end

  allow do
    origins 'fate888.com.tw'
    resource '/api/v1/bazhi_1001', headers: :any, methods: [:get, :post]
  end
end

Rails.application.config.middleware.insert_before 0, Rack::UTF8Sanitizer
