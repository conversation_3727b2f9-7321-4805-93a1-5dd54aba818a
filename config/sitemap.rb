# Set the host name for URL creation
SitemapGenerator::Sitemap.default_host = "https://meen.tw"

SitemapGenerator::Sitemap.create do
  # Put links creation logic here.
  #
  # The root path '/' and sitemap index file are added automatically for you.
  # Links are added to the Sitemap in the order they are specified.
  #
  # Usage: add(path, options={})
  #        (default options are used if you don't specify)
  #
  # Defaults: :priority => 0.5, :changefreq => 'weekly',
  #           :lastmod => Time.now, :host => default_host
  #
  # Examples:
  #
  # Add '/articles'
  #
  #   add articles_path, :priority => 0.7, :changefreq => 'daily'
  #
  # Add all articles:
  #
  #   Article.find_each do |article|
  #     add article_path(article), :lastmod => article.updated_at
  #   end

  add topics_path, priority: 0.7, changefreq: 'daily'

  Topic.find_each do |t|
    add topic_path(t), priority: 0.7, changefreq: 'daily', :lastmod => t.updated_at
  end

  add recent_pages_path

  Page.find_each do |pa|
    add page_path(pa), :lastmod => pa.updated_at
  end

end
