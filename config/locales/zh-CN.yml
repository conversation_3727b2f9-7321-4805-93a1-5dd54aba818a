"zh-CN":
  omniauth:
    github: GitHub
    twitter: Twitter
  device_platform:
    ios: iOS
    android: Android
  activerecord:
    models:
      user: "用户"
      topic: "话题"
      post: 文章
      page: Wiki
      device: "设备"
    attributes:
      user:
        name: "名字"
        login: "用户名"
        email: "Email"
        email_public: "公开 Email"
        avatar: "头像"
        location: "城市"
        bio: "个人介绍"
        password: "密码"
        password_confirmation: "确认密码"
        website: "个人主页"
        last_logined_at: "上次登录时间"
        tagline: "签名"
        state: "状态"
        verified: "信任用户"
        hr: "招聘人员"
        company: "公司"
        github: "GitHub"
        remember_me: 记住登陆状态
        current_password: 旧密码
      team:
        name: '公司／组织名称'
        login: 'Slug'
        bio: '公司／组织介绍'
        website: '公司 / 项目网址'
      team_user:
        login: '用户名'
        role: '权限'
        user_id: '用户'
      topic:
        title: "标题"
        user_id: "用户"
        node_id: "节点"
        team_id: "关联到组织"
        body: "正文"
      reply:
        body: "回复内容"
      note:
        title: "标题"
      post:
        title: 标题
        body: 正文
        tag_list: 标签
        source: 来源
        source_url: 来源地址
      page:
        slug: Slug
        title: 页面标题
        body: 内容
        locked: 锁定开关
        change_desc: 变更描述
      site:
        name: 网站名称
        url: 地址
        desc: 简单介绍
        site_node_id: 分类
      exception_log:
        title: 摘要
        body: 详细内容
      device:
        platform: 类型
        token: Token
        alive: 是否有效
    errors:
      models:
        user: 用户
      messages:
        inclusion: "不包含于列表中"
        exclusion: "是保留关键字"
        invalid: "是无效的"
        confirmation: "与确认值不匹配"
        accepted: "必须是可被接受的"
        empty: "不能留空"
        blank: "不能为空字符"
        too_long: "过长（最长为 %{count} 个字符）"
        too_short: "过短（最短为 %{count} 个字符）"
        wrong_length: "长度非法（必须为 %{count} 个字符）"
        taken: "已经被使用"
        not_a_number: "不是数字"
        not_an_integer: "必须是整数"
        greater_than: "必须大于 %{count}"
        greater_than_or_equal_to: "必须大于或等于 %{count}"
        equal_to: "必须等于 %{count}"
        less_than: "必须小于 %{count}"
        less_than_or_equal_to: "必须小于或等于 %{count}"
        odd: "必须为单数"
        even: "必须为双数"
        record_invalid: "校验失败: %{errors}"
        taken:
          已占用
        document_not_found:
          没有发现类是 %{klass} ID 是 %{identifiers} 的文档
        invalid_database:
          数据库应该是Mongo::DB,而不是%{name}.
        invalid_type:
          在类%{klass}中定义了字段,实际值是%{value}的%{other}.
        unsupported_version:
          MongoDB %{version} 版本已过期，请升级到 %{mongo_version}.
        validations:
          校验失败 - %{errors}.
        invalid_collection:
          不允许直接访问嵌入式的集合%{klass} , 请从文档的根访问集合.
        invalid_field:
          字段的名字不允许为 %{name}. 你不应该定义跟Mongoid内部属性或者方法相同的名字，详细请看Use Document# instance_methods.
        too_many_nested_attribute_records:
          被关联的%{association} 嵌入式属性不能超过 %{limit}.
        embedded_in_must_have_inverse_of:
          embedded_in的关联属性必须包含inverse_of.
        dependent_only_references_one_or_many:
          dependent => destroy|delete 选项只有在references_one或者references_many时候有效.
        association_cant_have_inverse_of:
          在当前的关联中，不允许定义inverse_of去,其只有在embedded_in或者references_many是数组的情况下使用
        unsaved_document:
          You cannot call create or create! through a relational association
          relation (%{document}) who's parent (%{base}) is not already saved.
