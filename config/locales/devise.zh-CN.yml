"zh-CN":
  errors:
    messages:
      not_found: "没有找到。"
      already_confirmed: "已经确认。"
      not_locked: "没有锁定。"
  devise:
    omniauth_callbacks:
      user:
        failure: '三方登陆过程异常，请重试。'
    failure:
      already_authenticated: '登录成功。'
      unauthenticated: '继续操作前请注册或者登录。'
      unconfirmed: '请先啟用您的帐号。'
      locked: '由于多次密码错误，您的帐号已被暂时锁定，一小时后将自动解锁，或者你可以通过邮件手动解锁。'
      invalid: '帐号或密码错误。'
      invalid_token: '认证码无效。'
      timeout: '您已登录超时，请重新登录。'
      inactive: '您还没有啟用帐户。'
      user:
        not_found_in_database: '没有该用户，请您重新注册。'
        last_attempt: '密码错误多次，你还有最后一次机会，如果还是不对，帐号将被锁定一小时。'
    sessions:
      signed_in: '登录成功。'
      signed_out: '退出成功。'
      already_signed_out: "退出成功。"
    passwords:
      send_instructions: '稍后，您将收到重置密码的电子邮件。'
      updated: '您的密码已修改成功，请重新登录。'
    confirmations:
      send_instructions: '您将在几分钟后收到一封电子邮件，内有验证帐号的步骤说明。'
      confirmed: '您的帐号已经啟用，请登录。'
    registrations:
      user:
        signed_up: '您的帐号已注册成功，如无意外，您将收到一封确认邮件。'
        updated: '帐号资料更新成功。'
        destroyed: '再见！您的帐户已成功注销。我们希望很快可以再见到您。'
    unlocks:
      send_instructions: '稍后，您将收到一封帐号解锁的邮件。'
      unlocked: '您的帐号已成功解锁，请登录。'
    mailer:
      confirmation_instructions:
        subject: '确认信息'
      reset_password_instructions:
        subject: '重置密码信息'
      unlock_instructions:
        subject: '解锁信息'
