zh-CN:
  talent:
    big_nos: "一二三四五六七八九十"
    L1:
      setup: 基础指标设定_L1
      category: 类别
      item: 项目
      trait: 特质
      trait_def: 特质文字定义
      trait_def_taitou: "区间%{trait_def_area}竞争比在%{pr}以上："
      score_drop: 分数落点
      score_drop_1: 分数落点1
      score_drop_2: 分数落点2
      score_drop_3: 分数落点3
      score_drop_4: 分数落点4
      score_drop_up: 分数区间上标
      score_drop_down: 分数区间下标
      trait_content: 特质内容
      proposal: 建议方案
      star_rule: 星曜分数计算规则
      sihua_rule: 四化分数计算规则
      star_for_rule: 单星曜
      house_weight: 宫位权重
      weight_condition: 加权条件
      star_class: 星类别
      star: 星曜
      condition: 条件
      weight: 权重
      match_condition: 符合条件
      same_house: 同宫
      opposite_house: 对宫
      three_house: 三方
      # sihua_chong: 冲
      sihua_chong: 对宫(冲)
      sihua_san_fang:  三方
      jia: 夹
      lu: 禄 
      quan: 权
      ke: 科
      ji: 忌
      zihua_lu_quan_ke: 自禄、权、科
      zihua_lu: 自禄 
      zihua_quan: 自权
      zihua_ke: 自科
      zihua_ji: 自忌
      sheng_lu_quan_ke: 生禄、权、科
      sheng_lu: 生禄 
      sheng_quan: 生权
      sheng_ke: 生科
      sheng_ji: 生忌
      no_main_star: 无主星
      star_weight: 星曜比重
      sihua: 四化
      sihua_flyout: 四化飞出
      sihua_flyout_012: 禄权科飞出
      sihua_flyout_3: 化忌飞出
      sihua_flyin_OnlyStar_012: 禄权科飞入_本身
      sihua_flyin_OnlyStar_3: 化忌飞入_本身
      sihua_flyin_SameHouse_012: 禄权科飞入_同宫
      sihua_flyin_SameHouse_3: 化忌飞入_同宫
      sihua_house_weight: 四化加权比例
      hua_lu: 化禄 
      hua_quan: 化权
      hua_ke: 化科
      hua_ji: 化忌
      stars_weight: 星垣总比重
      sihuas_weight: 四化总比重
      trait_desc: 效标定义
      trait_score_desc: 效标分数说明
      select_sihua: 选择四化星
      sihua_star: 四化星
      sihua_star_self: 本身
      trait_score: 计算指标分数(L1)
      sihua_sign: 最后结果值的正负设定
      sihua_sign_original: 原值
      sihua_sign_positive: 绝对正值
      sihua_sign_negative: 绝对负值
      sihua_sign_exception_house: 固定原值宫位
      
      timestamp_user: 客户生日
      timestamp_start: 起始客户生日
      timestamp_stop: 结束客户生日
      timestamp_udt: 动盘时间
      trait_code: 名称代码

    L2:
      setup: 应用指标设定
      category: 应用类别
      item: 应用项目
      trait: 应用名称
      L1_trait: 项目名称
      value: 数值
      pr: 竞争比
      weight: 加权
      weight_value: 加权数值
      weight_pr: 加权竞争比
      trait_id: 应用名称ID
      total_weight_value: 总加权数值
      total_weight_pr: 总加权竞争比
      trait_def: 项目名称文字定义
      trait_def_taitou: "区间%{trait_def_area}竞争比在%{pr}以上："
      score_drop: 分数落点
      score_drop_1: 分数落点1
      score_drop_2: 分数落点2
      score_drop_3: 分数落点3
      score_drop_4: 分数落点4
      score_drop_up: 分数区间上标
      score_drop_down: 分数区间下标
      trait_content: 项目名称内容
      proposal: 建议方案
      trait_desc: 效标定义
      trait_score_desc: 效标分数说明
      rule_desc: 应用指标计算规则
      rule_L2: 应用指标_L2
      rl_use: "选取"
      rl_reverse_desc: "反向指标"
      rl_reverse: "反向"
      rl_weight: "加权(%)"
      trait_code: 名称代码
      trait_score: 计算指标分数(L2)

      timestamp_user: 客户生日
      timestamp_start: 起始客户生日
      timestamp_stop: 结束客户生日
      timestamp_udt: 动盘时间

    L3:
      setup: 应用指标设定_L3
      category: 应用类别
      item: 应用项目
      trait: 应用名称
      L1_trait: 项目名称
      value: 数值
      pr: 竞争比
      weight: 加权
      weight_value: 加权数值
      weight_pr: 加权竞争比
      trait_id: 应用名称ID
      total_weight_value: 总加权数值
      total_weight_pr: 总加权竞争比
      trait_def: 项目名称文字定义
      trait_def_taitou: "区间%{trait_def_area}竞争比在%{pr}以上："
      score_drop: 分数落点
      score_drop_1: 分数落点1
      score_drop_2: 分数落点2
      score_drop_3: 分数落点3
      score_drop_4: 分数落点4
      score_drop_up: 分数区间上标
      score_drop_down: 分数区间下标
      trait_content: 项目名称内容
      proposal: 建议方案
      trait_desc: 效标定义
      trait_score_desc: 效标分数说明
      rule_desc: 应用指标计算规则
      rule_L2: 应用指标_L3
      rl_use: "选取"
      rl_reverse_desc: "反向指标"
      rl_reverse: "反向"
      rl_weight: "加权(%)"
      trait_code: 名称代码
      trait_score: 计算指标分数(L3)

      timestamp_user: 客户生日
      timestamp_start: 起始客户生日
      timestamp_stop: 结束客户生日
      timestamp_udt: 动盘时间

    MP:
      setup: 商业指标设定_MP
      category: 应用类别
      item: 应用项目
      trait: 应用名称
      L1_trait: 项目名称
      value: 数值
      pr: 竞争比
      weight: 加权
      weight_value: 加权数值
      weight_pr: 加权竞争比
      trait_id: 应用名称ID
      total_weight_value: 总加权数值
      total_weight_pr: 总加权竞争比
      trait_def: 项目名称文字定义
      trait_def_taitou: "区间%{trait_def_area}竞争比在%{pr}以上："
      score_drop: 分数落点
      score_drop_1: 分数落点1
      score_drop_2: 分数落点2
      score_drop_3: 分数落点3
      score_drop_4: 分数落点4
      score_drop_up: 分数区间上标
      score_drop_down: 分数区间下标
      trait_content: 项目名称内容
      proposal: 建议方案
      trait_desc: 效标定义
      trait_score_desc: 效标分数说明
      rule_desc: 应用指标计算规则
      rule_L2: 应用指标_MP
      rl_use: "选取"
      rl_reverse_desc: "反向指标"
      rl_reverse: "反向"
      rl_weight: "加权(%)"
      trait_code: 名称代码
      trait_score: 计算指标分数(MP)

      timestamp_user: 客户生日
      timestamp_start: 起始客户生日
      timestamp_stop: 结束客户生日
      timestamp_udt: 动盘时间

    star_for_rule_desc:
      star: 星性分数
      weight_condition_score: 加权条件比重
      special_weight: 特殊加权
      stars_weight: 星垣总比重
      star_weight: 星曜比重
      star_house_weight: 宫位权重
      house_id_1: 命宫
      house_id_2: 兄弟宫
      house_id_3: 夫妻宫
      house_id_4: 子女宫
      house_id_5: 财帛宫
      house_id_6: 疾厄宫
      house_id_7: 迁移宫
      house_id_8: 朋友宫
      house_id_9: 官禄宫
      house_id_10: 田宅宫
      house_id_11: 福德宫
      house_id_12: 父母宫
      star_score: 星性分数
      star_score_sheng: 星性分数加身宫
      score: 分数
      org_score: 原始星性分数
      sihua: 四化分数
      org_house_sihua_score: 原始四化分数
      HouseId: 宫位
      sihua_one_house_weight: 四化单宫比重
      sihuas_weight: 四化总比重
      score_sihuas: 四化各分数
      score_sihua: 四化分数
      score_sihua_before_weight: 四化权重前分数
      sihua_star_weight_detail: 四化权重细项
      sihua_star_weight_detail_all: 四化权重全部细项
      sihua_star_weight: 四化权重
      sihua_signs: 四化星最后的正负
      sihua_sign_exception_house: 固定原值宫位

      # 身宫比重 
      shenggong_score: 身宫分数
      detail: 细节
      shenggong_weight: 身宫比重
      sw_star_sum: 星曜总分
      sw_sihua_0: 禄
      sw_sihua_1: 权
      sw_sihua_2: 科
      sw_sihua_3: 忌
      sw_gong_sum: 宫总分
      sw_sihua_sum: 四化总分
      
