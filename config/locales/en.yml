#  Files in the config/locales directory are used for internationalization
#  and are automatically loaded by Rails. If you want to use locales other
#  than English, add the necessary files in this directory.
# 
#  To use the locales, use `I18n.t`:
# 
#      I18n.t 'hello'
# 
#  In views, this is aliased to just `t`:
# 
#      <%= t('hello') %>
# 
#  To use a different locale, set it with `I18n.locale`:
# 
#      I18n.locale = :es
# 
#  This would use the information in config/locales/es.yml.
# 
#  To learn more, please read the Rails Internationalization guide
#  available at http://guides.rubyonrails.org/i18n.html.

en:
  omniauth:
    github: GitHub
    twitter: Twitter
  activerecord:
    models:
      user: "User"
      topic: "Topic"
      post: "Post"
      page: "Wiki"
    attributes:
      user:
        name: "Name"
        login: "Login name"
        email: "Email"
        email_public: "Public Email"
        avatar: "Avatar"
        location: "City"
        bio: "Bio"
        password: "Password"
        password_confirmation: "Password Confirmation"
        website: "Personal Website"
        last_logined_at: "Last signed in at"
        tagline: "Tagline"
        state: "Status"
        verified: "Verified"
        hr: "Recruiter"
        company: "Company"
        github: "GitHub"
        remember_me: "Remember me"
        current_password: "Current Password"
      topic:
        title: "Title"
        user_id: "User"
        node_id: "Node"
        body: "Content"
      reply:
        body: "Reply Content"
      note:
        title: "Title"
      post:
        title: "Title"
        body: "Content"
        tag_list: "Tags"
        source: "Source"
        source_url: "Source Url"
      page:
        slug: Slug
        title: "Title"
        body: "Content"
        locked: "Lock"
        change_desc: "Change Description"
      site:
        name: "Name"
        url: "URL"
        desc: "Description"
        site_node_id: "Category"
      exception_log:
        title: Abstract
        body: Details
    errors:
      models:
        user: "User"
      messages:
        inclusion: "Inclusion"
        exclusion: "Exclusion"
        invalid: "Invalid"
        confirmation: "Confirmation did not match"
        accepted: "acceptance required."
        empty: "Cannot be empty."
        blank: "Cannot be blank."
        too_long: "Too long (limited to %{count} characters)."
        too_short: "Too short (at least %{count} characters are required)."
        wrong_length: "Invvalid length (valid length: %{count} characters)."
        taken: "already taken"
        not_a_number: "Not a number"
        not_an_integer: "Not an integer"
        greater_than: "should be greater than %{count}"
        greater_than_or_equal_to: "should be greater than or equal to %{count}"
        equal_to: "should be equal to %{count}"
        less_than: "should be less than %{count}"
        less_than_or_equal_to: "should be less than or equal to %{count}"
        odd: "should be odd number."
        even: "should be even number."
