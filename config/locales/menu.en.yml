"en":
  common:
    justnow: 'Just Now'
    hour: 'Hour'
    minute: 'Minute'
    edit: "Edit"
    create: "Create"
    avatar: "Avatar"
    save: "Save"
    saving: "Saving"
    submitting: "Submitting"
    delete: "Delete"
    undelete: "Revert"
    place_top: "Stick Top"
    un_top: "Un-Stick Top"
    upload: "Upload"
    uploading: "Uploading"
    published_at: "Published At"
    register: "Sign Up"
    login: "Sign In"
    logining: "Sign In..."
    logout: "Sign Out"
    remember_me: "Remember me (2 months)"
    cancel: "Cancel"
    created_at: "Created at %{time}"
    until: "until"
    last_by: "Last by"
    floor: "Floor"
    reply_at: "replied at %{time}"
    create_item: "Create Item"
    read_times: "hits"
    confirm_delete: "Are you sure you want to delete?"
    search: "Search"
    public: "Public"
    create_success: "Created successfully."
    delete_success: "Deleted successfully."
    update_success: "Updated successfully."
    mark_all_as_read: "Mark all as read"
    last_modified_at: "Last modified at"
    index: "Index"
    you_may_like: "You may also like"
    read: "read"
    admin_user: "Admin"
    vip_user: "VIP"
    normal_user: "Member"
    newbie_user: "Newbie"
    blocked_user: "Blocked User"
    hr_user: "HR"
    title: "Title"
    reply_count: "Reply count"
    last_reply_time: "Last replied at"
    reply: "Reply"
    index_node_navigation: "Node Navigation"
    highlight_topic: "Sticky Topic"
    statics: "Statistics"
    comments: "Comments"
    no_search_result: "No search result"
    access_denied: "Access denied, Please sign in and make sure you have proper permission."
    editor_toolbar_edit: "Edit"
    editor_toolbar_preview: "Preview"
    by: "by"
    at: "on"
    hot_topics: "Hot topics"
    hot_locations: "Hot cities"
    has_deleted: "has deleted"
    auth_with_other_services: "Sign in with other services"
    captcha: 'Captcha'
    search_result: 'Search results'
  reply:
    edit_reply: "Edit Reply"
    delete_reply_success: 'Delete successfully.'
    topic_location: 'Replied to'
  photos:
    upload_and_insert_code: "Upload and insert code"
    uploading: "Uploading"
  sites:
    create: Submit a site
  menu:
    topics: "Discussion"
    wiki: "Wiki"
    gems: "Gems"
    users: "Users"
    admin: "Control Panel"
    notes: "Notes"
    nodes: "Nodes"
    sites: "Sites"
    jobs: "Jobs"
    my_home_page: "My home page"
    edit_account_path: "Account Profile"
    likes: "Favorites"
    teams: "Teams"
  comments:
    create_comment: "Comment"
    no_comment: "No Comment at the moment"
  mail:
    login_from: "Sign in from"
    this_is_a_notification_mail_please_do_not_reply: "This is an email notification. Please do not reply."
    welcome_subject: "Welcome to %{app_name} community"
    welcome_title: "Welcome to %{app_name}"
    you_have_successfully: "You have successfully registered an account in"
    registered_an_account: "."
  rss:
    recent_topics_description: "Recent Topics in %{name} Forum."
    recent_topics_title: "%{name} Forum"
    recent_node_topics_description: "Recent Topic in %{node_name} of %{name} Forum."
    recent_node_topics_title: "%{node_name} node of %{name} Forum"
