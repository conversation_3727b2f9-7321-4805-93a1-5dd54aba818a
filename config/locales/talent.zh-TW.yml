zh-TW:
  talent:
    big_nos: "一二三四五六七八九十"
    L1:
      setup: 基礎指標設定_L1
      category: 類別
      item: 項目
      trait: 特質
      trait_def: 特質文字定義
      trait_def_taitou: "區間%{trait_def_area}競爭比在%{pr}以上："
      score_drop: 分數落點
      score_drop_1: 分數落點1
      score_drop_2: 分數落點2
      score_drop_3: 分數落點3
      score_drop_4: 分數落點4
      score_drop_up: 分數區間上標
      score_drop_down: 分數區間下標
      trait_content: 特質內容
      proposal: 建議方案
      star_rule: 星曜分數計算規則
      sihua_rule: 四化分數計算規則
      star_for_rule: 單星曜
      house_weight: 宮位權重
      weight_condition: 加權條件
      star_class: 星類別
      star: 星曜
      condition: 條件
      weight: 權重
      match_condition: 符合條件
      same_house: 同宮
      opposite_house: 對宮
      three_house: 三方
      # sihua_chong: 沖
      sihua_chong: 對宮(沖)
      sihua_san_fang:  三方
      jia: 夾
      lu: 祿 
      quan: 權
      ke: 科
      ji: 忌
      zihua_lu_quan_ke: 自祿、權、科
      zihua_lu: 自祿 
      zihua_quan: 自權
      zihua_ke: 自科
      zihua_ji: 自忌
      sheng_lu_quan_ke: 生祿、權、科
      sheng_lu: 生祿 
      sheng_quan: 生權
      sheng_ke: 生科
      sheng_ji: 生忌
      no_main_star: 無主星
      star_weight: 星曜比重
      sihua: 四化
      sihua_flyout: 四化飛出
      sihua_flyout_012: 祿權科飛出
      sihua_flyout_3: 化忌飛出
      sihua_flyin_OnlyStar_012: 祿權科飛入_本身
      sihua_flyin_OnlyStar_3: 化忌飛入_本身
      sihua_flyin_SameHouse_012: 祿權科飛入_同宮
      sihua_flyin_SameHouse_3: 化忌飛入_同宮
      sihua_house_weight: 四化加權比例
      hua_lu: 化祿 
      hua_quan: 化權
      hua_ke: 化科
      hua_ji: 化忌
      stars_weight: 星垣總比重
      sihuas_weight: 四化總比重
      trait_desc: 效標定義
      trait_score_desc: 效標分數說明
      select_sihua: 選擇四化星
      sihua_star: 四化星
      sihua_star_self: 本身
      trait_score: 計算指標分數(L1)
      sihua_sign: 最後結果值的正負設定
      sihua_sign_original: 原值
      sihua_sign_positive: 絕對正值
      sihua_sign_negative: 絕對負值
      sihua_sign_exception_house: 固定原值宮位

      timestamp_user: 客戶生日
      timestamp_start: 起始客戶生日
      timestamp_stop: 結束客戶生日
      timestamp_udt: 動盤時間
      trait_code: 名稱代碼

    L2:
      setup: 應用指標設定_L2
      category: 應用類別
      item: 應用項目
      trait: 應用名稱
      L1_trait: 項目名稱
      value: 數值
      pr: 競爭比
      weight: 加權
      weight_value: 加權數值
      weight_pr: 加權競爭比
      trait_id: 應用名稱ID
      total_weight_value: 總加權數值
      total_weight_pr: 總加權競爭比
      trait_def: 項目名稱文字定義
      trait_def_taitou: "區間%{trait_def_area}競爭比在%{pr}以上："
      score_drop: 分數落點
      score_drop_1: 分數落點1
      score_drop_2: 分數落點2
      score_drop_3: 分數落點3
      score_drop_4: 分數落點4
      score_drop_up: 分數區間上標
      score_drop_down: 分數區間下標
      trait_content: 項目名稱內容
      proposal: 建議方案
      trait_desc: 效標定義
      trait_score_desc: 效標分數說明
      rule_desc: 應用指標計算規則
      rule_L2: 應用指標_L2
      rl_use: "選取"
      rl_reverse_desc: "反向指標"
      rl_reverse: "反向"
      rl_weight: "加權(%)"
      trait_code: 名稱代碼
      trait_score: 計算指標分數(L2)

      timestamp_user: 客戶生日
      timestamp_start: 起始客戶生日
      timestamp_stop: 結束客戶生日
      timestamp_udt: 動盤時間

    L3:
      setup: 應用指標設定_L3
      category: 應用類別
      item: 應用項目
      trait: 應用名稱
      L1_trait: 項目名稱
      value: 數值
      pr: 競爭比
      weight: 加權
      weight_value: 加權數值
      weight_pr: 加權競爭比
      trait_id: 應用名稱ID
      total_weight_value: 總加權數值
      total_weight_pr: 總加權競爭比
      trait_def: 項目名稱文字定義
      trait_def_taitou: "區間%{trait_def_area}競爭比在%{pr}以上："
      score_drop: 分數落點
      score_drop_1: 分數落點1
      score_drop_2: 分數落點2
      score_drop_3: 分數落點3
      score_drop_4: 分數落點4
      score_drop_up: 分數區間上標
      score_drop_down: 分數區間下標
      trait_content: 項目名稱內容
      proposal: 建議方案
      trait_desc: 效標定義
      trait_score_desc: 效標分數說明
      rule_desc: 應用指標計算規則
      rule_L2: 應用指標_L3
      rl_use: "選取"
      rl_reverse_desc: "反向指標"
      rl_reverse: "反向"
      rl_weight: "加權(%)"
      trait_code: 名稱代碼
      trait_score: 計算指標分數(L3)

      timestamp_user: 客戶生日
      timestamp_start: 起始客戶生日
      timestamp_stop: 結束客戶生日
      timestamp_udt: 動盤時間

    MP:
      setup: 商業指標設定_MP
      category: 應用類別
      item: 應用項目
      trait: 應用名稱
      L1_trait: 項目名稱
      value: 數值
      pr: 競爭比
      weight: 加權
      weight_value: 加權數值
      weight_pr: 加權競爭比
      trait_id: 應用名稱ID
      total_weight_value: 總加權數值
      total_weight_pr: 總加權競爭比
      trait_def: 項目名稱文字定義
      trait_def_taitou: "區間%{trait_def_area}競爭比在%{pr}以上："
      score_drop: 分數落點
      score_drop_1: 分數落點1
      score_drop_2: 分數落點2
      score_drop_3: 分數落點3
      score_drop_4: 分數落點4
      score_drop_up: 分數區間上標
      score_drop_down: 分數區間下標
      trait_content: 項目名稱內容
      proposal: 建議方案
      trait_desc: 效標定義
      trait_score_desc: 效標分數說明
      rule_desc: 應用指標計算規則
      rule_L2: 應用指標_MP
      rl_use: "選取"
      rl_reverse_desc: "反向指標"
      rl_reverse: "反向"
      rl_weight: "加權(%)"
      trait_code: 名稱代碼
      trait_score: 計算指標分數(MP)

      timestamp_user: 客戶生日
      timestamp_start: 起始客戶生日
      timestamp_stop: 結束客戶生日
      timestamp_udt: 動盤時間

    star_for_rule_desc:
      star: 星性分數
      weight_condition_score: 加權條件比重
      special_weight: 特殊加權
      stars_weight: 星垣總比重
      star_weight: 星曜比重
      star_house_weight: 宮位權重
      house_id_1: 命宮
      house_id_2: 兄弟宮
      house_id_3: 夫妻宮
      house_id_4: 子女宮
      house_id_5: 財帛宮
      house_id_6: 疾厄宮
      house_id_7: 遷移宮
      house_id_8: 朋友宮
      house_id_9: 官祿宮
      house_id_10: 田宅宮
      house_id_11: 福德宮
      house_id_12: 父母宮
      star_score: 星性分數
      star_score_sheng: 星性分數加身宮
      score: 分數
      org_score: 原始星性分數
      sihua: 四化分數
      org_house_sihua_score: 原始四化分數
      HouseId: 宮位
      sihua_one_house_weight: 四化單宮比重
      sihuas_weight: 四化總比重
      score_sihuas: 四化各分數
      score_sihua: 四化分數
      score_sihua_before_weight: 四化權重前分數
      sihua_star_weight_detail: 四化權重細項
      sihua_star_weight_detail_all: 四化權重全部細項
      sihua_star_weight: 四化權重
      sihua_signs: 四化星最後的正負
      sihua_sign_exception_house: 固定原值宮位
           
      # 身宮比重 
      shenggong_score: 身宮分數
      detail: 細節
      shenggong_weight: 身宮比重
      sw_star_sum: 星曜總分
      sw_sihua_0: 祿
      sw_sihua_1: 權
      sw_sihua_2: 科
      sw_sihua_3: 忌
      sw_gong_sum: 宮總分
      sw_sihua_sum: 四化總分
      
