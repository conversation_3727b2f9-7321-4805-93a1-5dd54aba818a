"zh-CN":
  common:
    justnow: '刚刚'
    hour: '小时'
    minute: '分钟'
    edit: "修改"
    create: "新建"
    avatar: "头像"
    save: "保存"
    saving: "正在保存"
    submitting: "正在送出"
    delete: "删除"
    undelete: "恢复"
    place_top: "置顶"
    un_top: "去置顶"
    upload: "上传"
    uploading: "正在上传"
    published_at: "发表于"
    register: "注册"
    login: "登录"
    logining: "正在登录"
    logout: "退出"
    remember_me: "记住登录状态（60 天）"
    cancel: "取消"
    created_at: "发布于 %{time}"
    until: "截止"
    last_by: "最后由"
    floor: "楼"
    reply_at: "回复于 %{time}"
    create_item: "中发起"
    read_times: "次阅读"
    confirm_delete: "确定要删除么？"
    search: "搜索"
    public: "公开"
    create_success: "创建成功."
    delete_success: "删除成功."
    update_success: "修改成功."
    clear: "清空"
    last_modified_at: "最后更新于"
    index: "目录"
    you_may_like: "你可能也喜欢"
    read: "阅读"
    admin_user: "管理员"
    vip_user: "大师"
    normal_user: "会员"
    newbie_user: "新手"
    blocked_user: "禁言用户"
    hr_user: "企业 HR"
    title: "标题"
    last_reply_time: "最后回复时间"
    reply: "回复"
    index_node_navigation: "讨论节点分类导航"
    highlight_topic: "置顶话题"
    statics: "统计信息"
    comments: "条评论"
    no_search_result: "没有搜索到相关内容。"
    access_denied: "访问被拒绝，你可能没有权限或未登录。"
    editor_toolbar_edit: "编辑"
    editor_toolbar_preview: "预览"
    node: "节点"
    by: "由"
    at: "在"
    hot_topics: "热门讨论"
    hot_locations: "热门城市"
    replies_count: "回复"
    likes_count: 赞
    has_deleted: 已删除
    auth_with_other_services: "用其他平台的帐号登录"
    captcha: '验证码'
    search_result: '搜索结果'
  reply:
    edit_reply: "修改回帖"
    delete_reply_success: '回帖删除成功.'
    topic_location: '回帖所在文章'
  photos:
    upload_and_insert_code: "上传并插入代码"
    uploading: "正在上传"
  sites:
    create: 提交网站
  menu:
    home: "首页"
    topics: "社区"
    wiki: "百科"
    gems: "Gems"
    users: "会员"
    admin: "后台"
    notes: "记事本"
    nodes: "节点"
    sites: "酷站"
    jobs: "招聘"
    my_home_page: "我的主页"
    edit_account_path: "个人资料设置"
    likes: "我的收藏"
    teams: '公司／组织'
  comments:
    create_comment: "发表评论"
    no_comment: "暂无评论"
  mail:
    login_from: "登录页面地址"
    this_is_a_notification_mail_please_do_not_reply: "这是一封提醒邮件，请勿直接通过邮件回复."
    welcome_subject: "欢迎加入 %{app_name} 社区"
    welcome_title: "欢迎加入 %{app_name}"
    you_have_successfully: "你已经成功在"
    registered_an_account: "注册了账号。"
  rss:
    recent_topics_description: "%{name}社区最新发帖."
    recent_topics_title: "%{name}社区"
    recent_node_topics_description: "%{name} 社区 %{node_name} 节点最新发帖."
    recent_node_topics_title: "%{name} 社区 %{node_name} 节点"
  setting:
    new_topic_dropdown_node_ids: "Sidebar 新建话题按钮，下拉显示的节点 (node_id)，逗号隔开如 (1,3,4)"
    node_ids_hide_in_topics_index: "要在论坛首页列表隐藏的节点,以逗号隔开"
    custom_head_html: "自定 HTML head 区域的内容"
    site_index_html: "酷站列表首页头的 HTML"
    topic_index_sidebar_html: "话题列表首页边栏 HTML"
    before_topic_html: "话题正文前面的 HTML"
    after_topic_html: "话题后面的 HTML"
    footer_html: "页脚 HTML"
    wiki_index_html: "WIKI 首页 HTML"
    wiki_sidebar_html: "WIKI 边栏 HTML"
    index_html: "网站首页 HTML"
    tips: "随机显示的小提示（一行一个）"
    ban_words_on_reply: "回帖禁止内容（一行一个）"
    apns_pem: "Apple Push 证书内容，保持为空将不会触发 Push 发送动作。"
    blacklist_ips: "黑名单 IP 列表，将会直接拦截请求，逗号隔开如 (************, *********)"
