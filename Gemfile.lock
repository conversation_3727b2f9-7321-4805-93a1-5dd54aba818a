GEM
  remote: https://rubygems.org/
  specs:
    actioncable (5.0.6)
      actionpack (= 5.0.6)
      nio4r (>= 1.2, < 3.0)
      websocket-driver (~> 0.6.1)
    actionmailer (5.0.6)
      actionpack (= 5.0.6)
      actionview (= 5.0.6)
      activejob (= 5.0.6)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (5.0.6)
      actionview (= 5.0.6)
      activesupport (= 5.0.6)
      rack (~> 2.0)
      rack-test (~> 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (5.0.6)
      activesupport (= 5.0.6)
      builder (~> 3.1)
      erubis (~> 2.7.0)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.3)
    activejob (5.0.6)
      activesupport (= 5.0.6)
      globalid (>= 0.3.6)
    activemodel (5.0.6)
      activesupport (= 5.0.6)
    activerecord (5.0.6)
      activemodel (= 5.0.6)
      activesupport (= 5.0.6)
      arel (~> 7.0)
    activesupport (5.0.6)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (~> 0.7)
      minitest (~> 5.1)
      tzinfo (~> 1.1)
    addressable (2.4.0)
    airbrussh (1.3.0)
      sshkit (>= 1.6.1, != 1.7.0)
    arel (7.1.4)
    ast (2.4.0)
    bcrypt (3.1.11)
    builder (3.2.3)
    bulk_insert (1.2.1)
      activerecord (>= 4.1.0)
    bundler-audit (*******)
      bundler (>= 1.2.0, < 3)
      thor (>= 0.18, < 2)
    callsite (0.0.11)
    cancancan (1.15.0)
    capistrano (3.11.0)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (1.3.0)
      capistrano (~> 3.1)
      sshkit (~> 1.2)
    capistrano-rails (1.4.0)
      capistrano (~> 3.1)
      capistrano-bundler (~> 1.1)
    capistrano-rvm (0.1.2)
      capistrano (~> 3.0)
      sshkit (~> 1.2)
    capistrano-sidekiq (1.0.2)
      capistrano (>= 3.9.0)
      sidekiq (>= 3.4)
    capistrano3-puma (3.1.1)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (~> 3.4)
    chronic (0.10.2)
    coffee-rails (4.2.1)
      coffee-script (>= 2.2.0)
      railties (>= 4.0.0, < 5.2.x)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.10.0)
    concurrent-ruby (1.0.5)
    connection_pool (2.2.2)
    crass (1.0.5)
    dalli (2.7.6)
    debase (0.2.2)
      debase-ruby_core_source (>= 0.10.2)
    debase-ruby_core_source (0.10.3)
    devise (4.4.3)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0, < 6.0)
      responders
      warden (~> 1.2.3)
    devise-encryptable (0.2.0)
      devise (>= 2.1.0)
    dotenv (2.5.0)
    dotenv-rails (2.5.0)
      dotenv (= 2.5.0)
      railties (>= 3.2, < 6.0)
    erubis (2.7.0)
    et-orbi (1.2.4)
      tzinfo
    exception_notification (4.2.1)
      actionmailer (>= 4.0, < 6)
      activesupport (>= 4.0, < 6)
    execjs (2.7.0)
    fast_jsonapi (1.1.1)
      activesupport (>= 4.2)
    ffi (1.12.2)
    font-awesome-rails (*******)
      railties (>= 3.2, < 5.1)
    fugit (1.5.0)
      et-orbi (~> 1.1, >= 1.1.8)
      raabro (~> 1.4)
    geocoder (1.4.0)
    geoip (1.6.2)
    globalid (0.4.1)
      activesupport (>= 4.2.0)
    hiredis (0.6.1)
    http_accept_language (2.0.5)
    i18n (0.9.5)
      concurrent-ruby (~> 1.0)
    iconv (1.0.5)
    jaro_winkler (1.5.1)
    jbuilder (2.6.0)
      activesupport (>= 3.0.0, < 5.1)
      multi_json (~> 1.2)
    jquery-rails (4.2.1)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    launchy (2.4.3)
      addressable (~> 2.3)
    letter_opener (1.4.1)
      launchy (~> 2.2)
    listen (3.1.5)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
      ruby_dep (~> 1.2)
    loofah (2.3.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.0)
      mini_mime (>= 0.1.1)
    meta_request (0.4.3)
      callsite (~> 0.0, >= 0.0.11)
      rack-contrib (>= 1.1, < 3)
      railties (>= 3.0.0, < 5.2.0)
    method_source (0.8.2)
    mini_mime (1.0.0)
    mini_portile2 (2.4.0)
    minitest (5.11.3)
    multi_json (1.12.1)
    net-scp (1.2.1)
      net-ssh (>= 2.6.5)
    net-ssh (5.0.2)
    nio4r (2.2.0)
    nokogiri (1.10.9)
      mini_portile2 (~> 2.4.0)
    orm_adapter (0.5.0)
    parallel (1.12.1)
    parser (*******)
      ast (~> 2.4.0)
    pg (0.21.0)
    powerpack (0.1.2)
    puma (3.12.6)
    raabro (1.4.0)
    rack (2.0.8)
    rack-contrib (1.2.0)
      rack (>= 0.9.1)
    rack-cors (1.0.5)
      rack (>= 1.6.0)
    rack-protection (2.0.3)
      rack
    rack-test (0.6.3)
      rack (>= 1.0)
    rack-utf8_sanitizer (1.3.2)
      rack (>= 1.0, < 3.0)
    rails (5.0.6)
      actioncable (= 5.0.6)
      actionmailer (= 5.0.6)
      actionpack (= 5.0.6)
      actionview (= 5.0.6)
      activejob (= 5.0.6)
      activemodel (= 5.0.6)
      activerecord (= 5.0.6)
      activesupport (= 5.0.6)
      bundler (>= 1.3.0)
      railties (= 5.0.6)
      sprockets-rails (>= 2.0.0)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.0.4)
      loofah (~> 2.2, >= 2.2.2)
    rails-i18n (5.0.1)
      i18n (~> 0.7)
      railties (~> 5.0)
    rails-settings-cached (0.6.5)
      rails (>= 4.2.0)
    railties (5.0.6)
      actionpack (= 5.0.6)
      activesupport (= 5.0.6)
      method_source
      rake (>= 0.8.7)
      thor (>= 0.18.1, < 2.0)
    rainbow (3.0.0)
    rake (13.0.1)
    rb-fsevent (0.9.7)
    rb-inotify (0.9.7)
      ffi (>= 0.5.0)
    redis (3.3.5)
    redis-namespace (1.5.2)
      redis (~> 3.0, >= 3.0.4)
    redis-objects (1.2.1)
      redis (>= 3.0.2)
    responders (2.4.0)
      actionpack (>= 4.2.0, < 5.3)
      railties (>= 4.2.0, < 5.3)
    rubocop (0.58.2)
      jaro_winkler (~> 1.5.1)
      parallel (~> 1.10)
      parser (>= 2.5, != 2.5.1.1)
      powerpack (~> 0.1)
      rainbow (>= 2.2.2, < 4.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (~> 1.0, >= 1.0.1)
    rubocop-rails_config (0.2.3)
      railties (>= 3.0)
      rubocop (~> 0.56)
    ruby-debug-ide (0.7.2)
      rake (>= 0.8.1)
    ruby-progressbar (1.10.0)
    ruby_dep (1.4.0)
    rucaptcha (1.0.0)
      railties (>= 3.2)
    sass (3.4.22)
    sass-rails (5.0.6)
      railties (>= 4.0.0, < 6)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    second_level_cache (2.2.3)
      activerecord (>= 5.0.0.beta3, < 5.1.0)
      activesupport (>= 5.0.0.beta3, < 5.1.0)
    sidekiq (5.2.1)
      connection_pool (~> 2.2, >= 2.2.2)
      rack-protection (>= 1.5.0)
      redis (>= 3.3.5, < 5)
    sidekiq-cron (1.2.0)
      fugit (~> 1.1)
      sidekiq (>= 4.2.1)
    simple_form (3.3.1)
      actionpack (> 4, < 5.1)
      activemodel (> 4, < 5.1)
    sitemap_generator (5.2.0)
      builder (~> 3.0)
    spring (1.7.2)
    spring-watcher-listen (2.0.1)
      listen (>= 2.7, < 4.0)
      spring (>= 1.2, < 3.0)
    sprockets (3.7.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.2.1)
      actionpack (>= 4.0)
      activesupport (>= 4.0)
      sprockets (>= 3.0.0)
    sshkit (1.17.0)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    status-page (0.1.3)
      rails (>= 4.2)
    thor (0.20.0)
    thread_safe (0.3.6)
    tilt (2.0.8)
    turbolinks (5.1.0)
      turbolinks-source (~> 5.1)
    turbolinks-source (5.1.0)
    tzinfo (1.2.5)
      thread_safe (~> 0.1)
    uglifier (3.0.2)
      execjs (>= 0.3.0, < 3)
    unicode-display_width (1.4.0)
    warden (1.2.7)
      rack (>= 1.0)
    websocket-driver (0.6.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.3)
    whenever (0.9.7)
      chronic (>= 0.6.3)
    will_paginate (3.1.3)

PLATFORMS
  ruby

DEPENDENCIES
  bulk_insert
  bundler-audit (>= 0.7.0)
  cancancan
  capistrano
  capistrano-bundler
  capistrano-rails
  capistrano-rvm
  capistrano-sidekiq
  capistrano3-puma
  coffee-rails
  dalli
  debase
  devise
  devise-encryptable
  dotenv-rails
  exception_notification
  fast_jsonapi
  font-awesome-rails
  geocoder
  geoip
  hiredis
  http_accept_language
  iconv
  jbuilder
  jquery-rails
  letter_opener
  listen (>= 3.0.5, < 3.2)
  meta_request
  pg (>= 0.18, < 2.0)
  puma
  rack-cors
  rack-utf8_sanitizer
  rails (~> 5.0.6)
  rails-i18n
  rails-settings-cached
  redis
  redis-namespace
  redis-objects
  rubocop (~> 0.58.2)
  rubocop-rails_config
  ruby-debug-ide (>= 0.7.0)
  rucaptcha
  sass-rails
  second_level_cache
  sidekiq
  sidekiq-cron
  simple_form
  sitemap_generator
  spring
  spring-watcher-listen (~> 2.0.0)
  sprockets
  status-page
  turbolinks
  uglifier
  whenever
  will_paginate

RUBY VERSION
   ruby 2.5.1p57

BUNDLED WITH
   1.17.3
