# meen-forum

This is the source code of [meen.tw](https://meen.tw) website.

|server|ssh簡寫|IPv4|IPv6|local|
|----------|--------|----------------|--------------------------------|---------------|
|db001     |d1      |**************  |2400:8901::f03c:91ff:fe84:b5e1  |***************|
|forum-01  |f01     |*************   |2400:8901::f03c:91ff:fe16:98ba  |***************|
|forum-02  |f02     |************    |2400:8901::f03c:91ff:fedb:8a31  |***************|
|forum-03  |f03     |*************   |2400:8901::f03c:91ff:fe33:f35e  |***************|
|forum-db  |fdb     |***********     |2400:8901::f03c:91ff:fe79:3e1f  |************** |



## Requirements

* Ruby 2.3.0 +
* PostgreSQL 9.4 +
* Redis 2.8 +
* Memcached 1.4 +
* ImageMagick 6.5 +
* Elasticsearch 2.0 +

## Install in development

### Mac OS X, use Homebrew

```bash
$ brew install memcached redis postgresql imagemagick gs elasticsearch
```

### Ubuntu

```bash
$ sudo apt-get install memcached postgresql-9.4 redis-server imagemagick ghostscript
```

Install Elasticsearch

```bash
curl -sSL https://git.io/vVHhm | bash
```

```bash
$ git clone https://github.com/ruby-china/ruby-china.git
$ cd ruby-china
$ ./bin/setup
Checking Package Dependencies...
--------------------------------------------------------------------------------
Redis 2.0+                                                                 [Yes]
Memcached 1.4+                                                             [Yes]
ImageMagick 6.5+                                                           [Yes]
--------------------------------------------------------------------------------

Installing dependencies
--------------------------------------------------------------------------------
The Gemfile's dependencies are satisfied
--------------------------------------------------------------------------------

Configure
--------------------------------------------------------------------------------
Your Redis host (default: 127.0.0.1:6379):
Your Elasticsearch host (default: 127.0.0.1:9200):
--------------------------------------------------------------------------------

Seed default data...                                                      [Done]

== Removing old logs and tempfiles ==

Ruby China Successfully Installed.

$ rails s
```

## Testing

```bash
bundle exec rake
```



## Reindex ElasticSearch

### DELETE all indexes
```bash
curl -XDELETE 'http://localhost:9200/_all'
```

### FLUSH
```bash
curl -X POST 'http://localhost:9200/User,Page/_flush'
curl -X POST 'http://localhost:9200/_flush'
```

### in console (RECOMMAND approach!!)
```bash
$ bundle exec rails c -e production
```

### 全新部署
```
Topic.__elasticsearch__.create_index!
User.__elasticsearch__.create_index!
Page.__elasticsearch__.create_index!
```

### 把資料庫裡原有的 record 送上 elasticsearch
```
RAILS_ENV=staging bundle exec rake environment elasticsearch:import:model CLASS='Topic' FORCE=y --trace
RAILS_ENV=staging bundle exec rake environment elasticsearch:import:model CLASS='Page' FORCE=y --trace
RAILS_ENV=staging bundle exec rake environment elasticsearch:import:model CLASS='User' FORCE=y --trace
```

```
# Create the index for User model on elasticsearch
> User.__elasticsearch__.create_index!
=> {"acknowledged"=>true}

# Import current User records into the index
> User.import
  User Load (207.3ms)  SELECT  "services".* FROM "services"  ORDER BY "services"."id" ASC LIMIT 1000

# Sample search returning total results
> User.__elasticsearch__.search("mykeyword").results.total
=> 123
```

## Deploy

```bash
cap staging deploy:symlink:shared
```
