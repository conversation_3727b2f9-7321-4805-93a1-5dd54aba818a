AllCops:
  Rails:
    Enabled: true
  Exclude:
    - 'lib/templates/**/*'
  TargetRubyVersion: 2.2

Style/Documentation:
  Enabled: false

Style/AsciiComments:
  Enabled: false

Style/IfUnlessModifier:
  Enabled: false

Style/RedundantSelf:
  Enabled: false

Style/RaiseArgs:
  EnforcedStyle: compact

Style/MutableConstant:
  Enabled: false

Metrics/LineLength:
  Max: 1200

Metrics/ClassLength:
  Max: 1200

Metrics/MethodLength:
  Max: 1200

Metrics/ModuleLength:
  Max: 1200

Metrics/CyclomaticComplexity:
  Enabled: false

Metrics/AbcSize:
  Enabled: false

Metrics/PerceivedComplexity:
  Enabled: false

Rails/TimeZone:
  Enabled: false

Style/GlobalVars:
  Enabled: false

Style/GuardClause:
  Enabled: false

Rails/FindBy:
  Enabled: false

Rails/HasAndBelongsToMany:
  Enabled: false
